/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { assertEqual, assertGreaterThan, assertGreaterThanOrEqual, throwError } from '../../util/assert';
import { assertTIcu, assertTNode } from '../assert';
import { createTNodeAtIndex } from '../instructions/shared';
import { assertTNodeType } from '../node_assert';
import { setI18nHandling } from '../node_manipulation';
import { getInsertInFrontOfRNodeWithI18n, processI18nInsertBefore } from '../node_manipulation_i18n';
import { addTNodeAndUpdateInsertBeforeIndex } from './i18n_insert_before_index';
/**
 * Retrieve `TIcu` at a given `index`.
 *
 * The `TIcu` can be stored either directly (if it is nested ICU) OR
 * it is stored inside tho `TIcuContainer` if it is top level ICU.
 *
 * The reason for this is that the top level ICU need a `TNode` so that they are part of the render
 * tree, but nested ICU's have no TNode, because we don't know ahead of time if the nested ICU is
 * expressed (parent ICU may have selected a case which does not contain it.)
 *
 * @param tView Current `TView`.
 * @param index Index where the value should be read from.
 */
export function getTIcu(tView, index) {
    const value = tView.data[index];
    if (value === null || typeof value === 'string')
        return null;
    if (ngDevMode &&
        !(value.hasOwnProperty('tView') || value.hasOwnProperty('currentCaseLViewIndex'))) {
        throwError('We expect to get \'null\'|\'TIcu\'|\'TIcuContainer\', but got: ' + value);
    }
    // Here the `value.hasOwnProperty('currentCaseLViewIndex')` is a polymorphic read as it can be
    // either TIcu or TIcuContainerNode. This is not ideal, but we still think it is OK because it
    // will be just two cases which fits into the browser inline cache (inline cache can take up to
    // 4)
    const tIcu = value.hasOwnProperty('currentCaseLViewIndex') ? value :
        value.value;
    ngDevMode && assertTIcu(tIcu);
    return tIcu;
}
/**
 * Store `TIcu` at a give `index`.
 *
 * The `TIcu` can be stored either directly (if it is nested ICU) OR
 * it is stored inside tho `TIcuContainer` if it is top level ICU.
 *
 * The reason for this is that the top level ICU need a `TNode` so that they are part of the render
 * tree, but nested ICU's have no TNode, because we don't know ahead of time if the nested ICU is
 * expressed (parent ICU may have selected a case which does not contain it.)
 *
 * @param tView Current `TView`.
 * @param index Index where the value should be stored at in `Tview.data`
 * @param tIcu The TIcu to store.
 */
export function setTIcu(tView, index, tIcu) {
    const tNode = tView.data[index];
    ngDevMode &&
        assertEqual(tNode === null || tNode.hasOwnProperty('tView'), true, 'We expect to get \'null\'|\'TIcuContainer\'');
    if (tNode === null) {
        tView.data[index] = tIcu;
    }
    else {
        ngDevMode && assertTNodeType(tNode, 32 /* TNodeType.Icu */);
        tNode.value = tIcu;
    }
}
/**
 * Set `TNode.insertBeforeIndex` taking the `Array` into account.
 *
 * See `TNode.insertBeforeIndex`
 */
export function setTNodeInsertBeforeIndex(tNode, index) {
    ngDevMode && assertTNode(tNode);
    let insertBeforeIndex = tNode.insertBeforeIndex;
    if (insertBeforeIndex === null) {
        setI18nHandling(getInsertInFrontOfRNodeWithI18n, processI18nInsertBefore);
        insertBeforeIndex = tNode.insertBeforeIndex =
            [null /* may be updated to number later */, index];
    }
    else {
        assertEqual(Array.isArray(insertBeforeIndex), true, 'Expecting array here');
        insertBeforeIndex.push(index);
    }
}
/**
 * Create `TNode.type=TNodeType.Placeholder` node.
 *
 * See `TNodeType.Placeholder` for more information.
 */
export function createTNodePlaceholder(tView, previousTNodes, index) {
    const tNode = createTNodeAtIndex(tView, index, 64 /* TNodeType.Placeholder */, null, null);
    addTNodeAndUpdateInsertBeforeIndex(previousTNodes, tNode);
    return tNode;
}
/**
 * Returns current ICU case.
 *
 * ICU cases are stored as index into the `TIcu.cases`.
 * At times it is necessary to communicate that the ICU case just switched and that next ICU update
 * should update all bindings regardless of the mask. In such a case the we store negative numbers
 * for cases which have just been switched. This function removes the negative flag.
 */
export function getCurrentICUCaseIndex(tIcu, lView) {
    const currentCase = lView[tIcu.currentCaseLViewIndex];
    return currentCase === null ? currentCase : (currentCase < 0 ? ~currentCase : currentCase);
}
export function getParentFromIcuCreateOpCode(mergedCode) {
    return mergedCode >>> 17 /* IcuCreateOpCode.SHIFT_PARENT */;
}
export function getRefFromIcuCreateOpCode(mergedCode) {
    return (mergedCode & 131070 /* IcuCreateOpCode.MASK_REF */) >>> 1 /* IcuCreateOpCode.SHIFT_REF */;
}
export function getInstructionFromIcuCreateOpCode(mergedCode) {
    return mergedCode & 1 /* IcuCreateOpCode.MASK_INSTRUCTION */;
}
export function icuCreateOpCode(opCode, parentIdx, refIdx) {
    ngDevMode && assertGreaterThanOrEqual(parentIdx, 0, 'Missing parent index');
    ngDevMode && assertGreaterThan(refIdx, 0, 'Missing ref index');
    return opCode | parentIdx << 17 /* IcuCreateOpCode.SHIFT_PARENT */ | refIdx << 1 /* IcuCreateOpCode.SHIFT_REF */;
}
//# sourceMappingURL=data:application/json;base64,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