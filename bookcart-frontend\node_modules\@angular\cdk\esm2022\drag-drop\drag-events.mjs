/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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