let jitOptions = null;
export function setJitOptions(options) {
    if (jitOptions !== null) {
        if (options.defaultEncapsulation !== jitOptions.defaultEncapsulation) {
            ngDevMode &&
                console.error('Provided value for `defaultEncapsulation` can not be changed once it has been set.');
            return;
        }
        if (options.preserveWhitespaces !== jitOptions.preserveWhitespaces) {
            ngDevMode &&
                console.error('Provided value for `preserveWhitespaces` can not be changed once it has been set.');
            return;
        }
    }
    jitOptions = options;
}
export function getJitOptions() {
    return jitOptions;
}
export function resetJitOptions() {
    jitOptions = null;
}
//# sourceMappingURL=data:application/json;base64,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