/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { isEnvironmentProviders } from '../di/interface/provider';
import { RuntimeError } from '../errors';
import { stringify } from '../util/stringify';
import { stringifyForError } from './util/stringify_utils';
/** Called when directives inject each other (creating a circular dependency) */
export function throwCyclicDependencyError(token, path) {
    const depPath = path ? `. Dependency path: ${path.join(' > ')} > ${token}` : '';
    throw new RuntimeError(-200 /* RuntimeErrorCode.CYCLIC_DI_DEPENDENCY */, ngDevMode ? `Circular dependency in DI detected for ${token}${depPath}` : token);
}
export function throwMixedMultiProviderError() {
    throw new Error(`Cannot mix multi providers and regular providers`);
}
export function throwInvalidProviderError(ngModuleType, providers, provider) {
    if (ngModuleType && providers) {
        const providerDetail = providers.map(v => v == provider ? '?' + provider + '?' : '...');
        throw new Error(`Invalid provider for the NgModule '${stringify(ngModuleType)}' - only instances of Provider and Type are allowed, got: [${providerDetail.join(', ')}]`);
    }
    else if (isEnvironmentProviders(provider)) {
        if (provider.ɵfromNgModule) {
            throw new RuntimeError(207 /* RuntimeErrorCode.PROVIDER_IN_WRONG_CONTEXT */, `Invalid providers from 'importProvidersFrom' present in a non-environment injector. 'importProvidersFrom' can't be used for component providers.`);
        }
        else {
            throw new RuntimeError(207 /* RuntimeErrorCode.PROVIDER_IN_WRONG_CONTEXT */, `Invalid providers present in a non-environment injector. 'EnvironmentProviders' can't be used for component providers.`);
        }
    }
    else {
        throw new Error('Invalid provider');
    }
}
/** Throws an error when a token is not found in DI. */
export function throwProviderNotFoundError(token, injectorName) {
    const errorMessage = ngDevMode &&
        `No provider for ${stringifyForError(token)} found${injectorName ? ` in ${injectorName}` : ''}`;
    throw new RuntimeError(-201 /* RuntimeErrorCode.PROVIDER_NOT_FOUND */, errorMessage);
}
//# sourceMappingURL=data:application/json;base64,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