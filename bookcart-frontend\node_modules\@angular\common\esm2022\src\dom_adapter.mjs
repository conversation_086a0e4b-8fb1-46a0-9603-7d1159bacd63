/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
let _DOM = null;
export function getDOM() {
    return _DOM;
}
export function setRootDomAdapter(adapter) {
    _DOM ??= adapter;
}
/* tslint:disable:requireParameterType */
/**
 * Provides DOM operations in an environment-agnostic way.
 *
 * @security Tread carefully! Interacting with the DOM directly is dangerous and
 * can introduce XSS risks.
 */
export class DomAdapter {
}
//# sourceMappingURL=data:application/json;base64,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