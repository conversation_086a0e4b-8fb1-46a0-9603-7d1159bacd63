{"version": 3, "file": "drag-drop.mjs", "sources": ["../../../../../../src/cdk/drag-drop/dom/styling.ts", "../../../../../../src/cdk/drag-drop/dom/dom-rect.ts", "../../../../../../src/cdk/drag-drop/dom/parent-position-tracker.ts", "../../../../../../src/cdk/drag-drop/dom/clone-node.ts", "../../../../../../src/cdk/drag-drop/dom/root-node.ts", "../../../../../../src/cdk/drag-drop/dom/transition-duration.ts", "../../../../../../src/cdk/drag-drop/preview-ref.ts", "../../../../../../src/cdk/drag-drop/drag-ref.ts", "../../../../../../src/cdk/drag-drop/drag-utils.ts", "../../../../../../src/cdk/drag-drop/sorting/single-axis-sort-strategy.ts", "../../../../../../src/cdk/drag-drop/drop-list-ref.ts", "../../../../../../src/cdk/drag-drop/drag-drop-registry.ts", "../../../../../../src/cdk/drag-drop/drag-drop.ts", "../../../../../../src/cdk/drag-drop/drag-parent.ts", "../../../../../../src/cdk/drag-drop/directives/assertions.ts", "../../../../../../src/cdk/drag-drop/directives/drag-handle.ts", "../../../../../../src/cdk/drag-drop/directives/config.ts", "../../../../../../src/cdk/drag-drop/directives/drag.ts", "../../../../../../src/cdk/drag-drop/directives/drop-list-group.ts", "../../../../../../src/cdk/drag-drop/directives/drop-list.ts", "../../../../../../src/cdk/drag-drop/directives/drag-preview.ts", "../../../../../../src/cdk/drag-drop/directives/drag-placeholder.ts", "../../../../../../src/cdk/drag-drop/drag-drop-module.ts", "../../../../../../src/cdk/drag-drop/drag-drop_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Extended CSSStyleDeclaration that includes a couple of drag-related\n * properties that aren't in the built-in TS typings.\n */\nexport interface DragCSSStyleDeclaration extends CSSStyleDeclaration {\n  msScrollSnapType: string;\n  scrollSnapType: string;\n  webkitTapHighlightColor: string;\n}\n\n/**\n * Shallow-extends a stylesheet object with another stylesheet-like object.\n * Note that the keys in `source` have to be dash-cased.\n * @docs-private\n */\nexport function extendStyles(\n  dest: CSSStyleDeclaration,\n  source: Record<string, string>,\n  importantProperties?: Set<string>,\n) {\n  for (let key in source) {\n    if (source.hasOwnProperty(key)) {\n      const value = source[key];\n\n      if (value) {\n        dest.setProperty(key, value, importantProperties?.has(key) ? 'important' : '');\n      } else {\n        dest.removeProperty(key);\n      }\n    }\n  }\n\n  return dest;\n}\n\n/**\n * Toggles whether the native drag interactions should be enabled for an element.\n * @param element Element on which to toggle the drag interactions.\n * @param enable Whether the drag interactions should be enabled.\n * @docs-private\n */\nexport function toggleNativeDragInteractions(element: HTMLElement, enable: boolean) {\n  const userSelect = enable ? '' : 'none';\n\n  extendStyles(element.style, {\n    'touch-action': enable ? '' : 'none',\n    '-webkit-user-drag': enable ? '' : 'none',\n    '-webkit-tap-highlight-color': enable ? '' : 'transparent',\n    'user-select': userSelect,\n    '-ms-user-select': userSelect,\n    '-webkit-user-select': userSelect,\n    '-moz-user-select': userSelect,\n  });\n}\n\n/**\n * Toggles whether an element is visible while preserving its dimensions.\n * @param element Element whose visibility to toggle\n * @param enable Whether the element should be visible.\n * @param importantProperties Properties to be set as `!important`.\n * @docs-private\n */\nexport function toggleVisibility(\n  element: HTMLElement,\n  enable: boolean,\n  importantProperties?: Set<string>,\n) {\n  extendStyles(\n    element.style,\n    {\n      position: enable ? '' : 'fixed',\n      top: enable ? '' : '0',\n      opacity: enable ? '' : '0',\n      left: enable ? '' : '-999em',\n    },\n    importantProperties,\n  );\n}\n\n/**\n * Combines a transform string with an optional other transform\n * that exited before the base transform was applied.\n */\nexport function combineTransforms(transform: string, initialTransform?: string): string {\n  return initialTransform && initialTransform != 'none'\n    ? transform + ' ' + initialTransform\n    : transform;\n}\n\n/**\n * Matches the target element's size to the source's size.\n * @param target Element that needs to be resized.\n * @param sourceRect Dimensions of the source element.\n */\nexport function matchElementSize(target: HTMLElement, sourceRect: DOMRect): void {\n  target.style.width = `${sourceRect.width}px`;\n  target.style.height = `${sourceRect.height}px`;\n  target.style.transform = getTransform(sourceRect.left, sourceRect.top);\n}\n\n/**\n * Gets a 3d `transform` that can be applied to an element.\n * @param x Desired position of the element along the X axis.\n * @param y Desired position of the element along the Y axis.\n */\nexport function getTransform(x: number, y: number): string {\n  // Round the transforms since some browsers will\n  // blur the elements for sub-pixel transforms.\n  return `translate3d(${Math.round(x)}px, ${Math.round(y)}px, 0)`;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Gets a mutable version of an element's bounding `DOMRect`. */\nexport function getMutableClientRect(element: Element): DOMRect {\n  const rect = element.getBoundingClientRect();\n\n  // We need to clone the `clientRect` here, because all the values on it are readonly\n  // and we need to be able to update them. Also we can't use a spread here, because\n  // the values on a `DOMRect` aren't own properties. See:\n  // https://developer.mozilla.org/en-US/docs/Web/API/Element/getBoundingClientRect#Notes\n  return {\n    top: rect.top,\n    right: rect.right,\n    bottom: rect.bottom,\n    left: rect.left,\n    width: rect.width,\n    height: rect.height,\n    x: rect.x,\n    y: rect.y,\n  } as DOMRect;\n}\n\n/**\n * Checks whether some coordinates are within a `DOMRect`.\n * @param clientRect DOMRect that is being checked.\n * @param x Coordinates along the X axis.\n * @param y Coordinates along the Y axis.\n */\nexport function isInsideClientRect(clientRect: DOMRect, x: number, y: number) {\n  const {top, bottom, left, right} = clientRect;\n  return y >= top && y <= bottom && x >= left && x <= right;\n}\n\n/**\n * Updates the top/left positions of a `DOMRect`, as well as their bottom/right counterparts.\n * @param domRect `DOMRect` that should be updated.\n * @param top Amount to add to the `top` position.\n * @param left Amount to add to the `left` position.\n */\nexport function adjustDomRect(\n  domRect: {\n    top: number;\n    bottom: number;\n    left: number;\n    right: number;\n    width: number;\n    height: number;\n  },\n  top: number,\n  left: number,\n) {\n  domRect.top += top;\n  domRect.bottom = domRect.top + domRect.height;\n\n  domRect.left += left;\n  domRect.right = domRect.left + domRect.width;\n}\n\n/**\n * Checks whether the pointer coordinates are close to a DOMRect.\n * @param rect DOMRect to check against.\n * @param threshold Threshold around the DOMRect.\n * @param pointerX Coordinates along the X axis.\n * @param pointerY Coordinates along the Y axis.\n */\nexport function isPointerNearDomRect(\n  rect: DOMRect,\n  threshold: number,\n  pointerX: number,\n  pointerY: number,\n): boolean {\n  const {top, right, bottom, left, width, height} = rect;\n  const xThreshold = width * threshold;\n  const yThreshold = height * threshold;\n\n  return (\n    pointerY > top - yThreshold &&\n    pointerY < bottom + yThreshold &&\n    pointerX > left - xThreshold &&\n    pointerX < right + xThreshold\n  );\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {_getEventTarget} from '@angular/cdk/platform';\nimport {getMutableClientRect, adjustDomRect} from './dom-rect';\n\n/** Object holding the scroll position of something. */\ninterface ScrollPosition {\n  top: number;\n  left: number;\n}\n\n/** Keeps track of the scroll position and dimensions of the parents of an element. */\nexport class ParentPositionTracker {\n  /** Cached positions of the scrollable parent elements. */\n  readonly positions = new Map<\n    Document | HTMLElement,\n    {\n      scrollPosition: ScrollPosition;\n      clientRect?: DOMRect;\n    }\n  >();\n\n  constructor(private _document: Document) {}\n\n  /** Clears the cached positions. */\n  clear() {\n    this.positions.clear();\n  }\n\n  /** Caches the positions. Should be called at the beginning of a drag sequence. */\n  cache(elements: readonly HTMLElement[]) {\n    this.clear();\n    this.positions.set(this._document, {\n      scrollPosition: this.getViewportScrollPosition(),\n    });\n\n    elements.forEach(element => {\n      this.positions.set(element, {\n        scrollPosition: {top: element.scrollTop, left: element.scrollLeft},\n        clientRect: getMutableClientRect(element),\n      });\n    });\n  }\n\n  /** Handles scrolling while a drag is taking place. */\n  handleScroll(event: Event): ScrollPosition | null {\n    const target = _getEventTarget<HTMLElement | Document>(event)!;\n    const cachedPosition = this.positions.get(target);\n\n    if (!cachedPosition) {\n      return null;\n    }\n\n    const scrollPosition = cachedPosition.scrollPosition;\n    let newTop: number;\n    let newLeft: number;\n\n    if (target === this._document) {\n      const viewportScrollPosition = this.getViewportScrollPosition();\n      newTop = viewportScrollPosition.top;\n      newLeft = viewportScrollPosition.left;\n    } else {\n      newTop = (target as HTMLElement).scrollTop;\n      newLeft = (target as HTMLElement).scrollLeft;\n    }\n\n    const topDifference = scrollPosition.top - newTop;\n    const leftDifference = scrollPosition.left - newLeft;\n\n    // Go through and update the cached positions of the scroll\n    // parents that are inside the element that was scrolled.\n    this.positions.forEach((position, node) => {\n      if (position.clientRect && target !== node && target.contains(node)) {\n        adjustDomRect(position.clientRect, topDifference, leftDifference);\n      }\n    });\n\n    scrollPosition.top = newTop;\n    scrollPosition.left = newLeft;\n\n    return {top: topDifference, left: leftDifference};\n  }\n\n  /**\n   * Gets the scroll position of the viewport. Note that we use the scrollX and scrollY directly,\n   * instead of going through the `ViewportRuler`, because the first value the ruler looks at is\n   * the top/left offset of the `document.documentElement` which works for most cases, but breaks\n   * if the element is offset by something like the `BlockScrollStrategy`.\n   */\n  getViewportScrollPosition() {\n    return {top: window.scrollY, left: window.scrollX};\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Creates a deep clone of an element. */\nexport function deepCloneNode(node: HTMLElement): HTMLElement {\n  const clone = node.cloneNode(true) as HTMLElement;\n  const descendantsWithId = clone.querySelectorAll('[id]');\n  const nodeName = node.nodeName.toLowerCase();\n\n  // Remove the `id` to avoid having multiple elements with the same id on the page.\n  clone.removeAttribute('id');\n\n  for (let i = 0; i < descendantsWithId.length; i++) {\n    descendantsWithId[i].removeAttribute('id');\n  }\n\n  if (nodeName === 'canvas') {\n    transferCanvasData(node as HTMLCanvasElement, clone as HTMLCanvasElement);\n  } else if (nodeName === 'input' || nodeName === 'select' || nodeName === 'textarea') {\n    transferInputData(node as HTMLInputElement, clone as HTMLInputElement);\n  }\n\n  transferData('canvas', node, clone, transferCanvasData);\n  transferData('input, textarea, select', node, clone, transferInputData);\n  return clone;\n}\n\n/** Matches elements between an element and its clone and allows for their data to be cloned. */\nfunction transferData<T extends Element>(\n  selector: string,\n  node: HTMLElement,\n  clone: HTMLElement,\n  callback: (source: T, clone: T) => void,\n) {\n  const descendantElements = node.querySelectorAll<T>(selector);\n\n  if (descendantElements.length) {\n    const cloneElements = clone.querySelectorAll<T>(selector);\n\n    for (let i = 0; i < descendantElements.length; i++) {\n      callback(descendantElements[i], cloneElements[i]);\n    }\n  }\n}\n\n// Counter for unique cloned radio button names.\nlet cloneUniqueId = 0;\n\n/** Transfers the data of one input element to another. */\nfunction transferInputData(\n  source: Element & {value: string},\n  clone: Element & {value: string; name: string; type: string},\n) {\n  // Browsers throw an error when assigning the value of a file input programmatically.\n  if (clone.type !== 'file') {\n    clone.value = source.value;\n  }\n\n  // Radio button `name` attributes must be unique for radio button groups\n  // otherwise original radio buttons can lose their checked state\n  // once the clone is inserted in the DOM.\n  if (clone.type === 'radio' && clone.name) {\n    clone.name = `mat-clone-${clone.name}-${cloneUniqueId++}`;\n  }\n}\n\n/** Transfers the data of one canvas element to another. */\nfunction transferCanvasData(source: HTMLCanvasElement, clone: HTMLCanvasElement) {\n  const context = clone.getContext('2d');\n\n  if (context) {\n    // In some cases `drawImage` can throw (e.g. if the canvas size is 0x0).\n    // We can't do much about it so just ignore the error.\n    try {\n      context.drawImage(source, 0, 0);\n    } catch {}\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {EmbeddedViewRef} from '@angular/core';\n\n/**\n * Gets the root HTML element of an embedded view.\n * If the root is not an HTML element it gets wrapped in one.\n */\nexport function getRootNode(viewRef: EmbeddedViewRef<any>, _document: Document): HTMLElement {\n  const rootNodes: Node[] = viewRef.rootNodes;\n\n  if (rootNodes.length === 1 && rootNodes[0].nodeType === _document.ELEMENT_NODE) {\n    return rootNodes[0] as HTMLElement;\n  }\n\n  const wrapper = _document.createElement('div');\n  rootNodes.forEach(node => wrapper.appendChild(node));\n  return wrapper;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Parses a CSS time value to milliseconds. */\nfunction parseCssTimeUnitsToMs(value: string): number {\n  // Some browsers will return it in seconds, whereas others will return milliseconds.\n  const multiplier = value.toLowerCase().indexOf('ms') > -1 ? 1 : 1000;\n  return parseFloat(value) * multiplier;\n}\n\n/** Gets the transform transition duration, including the delay, of an element in milliseconds. */\nexport function getTransformTransitionDurationInMs(element: HTMLElement): number {\n  const computedStyle = getComputedStyle(element);\n  const transitionedProperties = parseCssPropertyValue(computedStyle, 'transition-property');\n  const property = transitionedProperties.find(prop => prop === 'transform' || prop === 'all');\n\n  // If there's no transition for `all` or `transform`, we shouldn't do anything.\n  if (!property) {\n    return 0;\n  }\n\n  // Get the index of the property that we're interested in and match\n  // it up to the same index in `transition-delay` and `transition-duration`.\n  const propertyIndex = transitionedProperties.indexOf(property);\n  const rawDurations = parseCssPropertyValue(computedStyle, 'transition-duration');\n  const rawDelays = parseCssPropertyValue(computedStyle, 'transition-delay');\n\n  return (\n    parseCssTimeUnitsToMs(rawDurations[propertyIndex]) +\n    parseCssTimeUnitsToMs(rawDelays[propertyIndex])\n  );\n}\n\n/** Parses out multiple values from a computed style into an array. */\nfunction parseCssPropertyValue(computedStyle: CSSStyleDeclaration, name: string): string[] {\n  const value = computedStyle.getPropertyValue(name);\n  return value.split(',').map(part => part.trim());\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {EmbeddedViewRef, TemplateRef, ViewContainerRef} from '@angular/core';\nimport {Direction} from '@angular/cdk/bidi';\nimport {\n  extendStyles,\n  getTransform,\n  matchElementSize,\n  toggleNativeDragInteractions,\n} from './dom/styling';\nimport {deepCloneNode} from './dom/clone-node';\nimport {getRootNode} from './dom/root-node';\nimport {getTransformTransitionDurationInMs} from './dom/transition-duration';\n\n/** Template that can be used to create a drag preview element. */\nexport interface DragPreviewTemplate<T = any> {\n  matchSize?: boolean;\n  template: TemplateRef<T> | null;\n  viewContainer: ViewContainerRef;\n  context: T;\n}\n\n/** Inline styles to be set as `!important` while dragging. */\nconst importantProperties = new Set([\n  // Needs to be important, because some `mat-table` sets `position: sticky !important`. See #22781.\n  'position',\n]);\n\nexport class PreviewRef {\n  /** Reference to the view of the preview element. */\n  private _previewEmbeddedView: EmbeddedViewRef<any> | null;\n\n  /** Reference to the preview element. */\n  private _preview: HTMLElement;\n\n  constructor(\n    private _document: Document,\n    private _rootElement: HTMLElement,\n    private _direction: Direction,\n    private _initialDomRect: DOMRect,\n    private _previewTemplate: DragPreviewTemplate | null,\n    private _previewClass: string | string[] | null,\n    private _pickupPositionOnPage: {\n      x: number;\n      y: number;\n    },\n    private _initialTransform: string | null,\n    private _zIndex: number,\n  ) {}\n\n  attach(parent: HTMLElement): void {\n    this._preview = this._createPreview();\n    parent.appendChild(this._preview);\n\n    // The null check is necessary for browsers that don't support the popover API.\n    // Note that we use a string access for compatibility with Closure.\n    if ('showPopover' in this._preview) {\n      this._preview['showPopover']();\n    }\n  }\n\n  destroy(): void {\n    this._preview.remove();\n    this._previewEmbeddedView?.destroy();\n    this._preview = this._previewEmbeddedView = null!;\n  }\n\n  setTransform(value: string): void {\n    this._preview.style.transform = value;\n  }\n\n  getBoundingClientRect(): DOMRect {\n    return this._preview.getBoundingClientRect();\n  }\n\n  addClass(className: string): void {\n    this._preview.classList.add(className);\n  }\n\n  getTransitionDuration(): number {\n    return getTransformTransitionDurationInMs(this._preview);\n  }\n\n  addEventListener(name: string, handler: EventListenerOrEventListenerObject) {\n    this._preview.addEventListener(name, handler);\n  }\n\n  removeEventListener(name: string, handler: EventListenerOrEventListenerObject) {\n    this._preview.removeEventListener(name, handler);\n  }\n\n  private _createPreview(): HTMLElement {\n    const previewConfig = this._previewTemplate;\n    const previewClass = this._previewClass;\n    const previewTemplate = previewConfig ? previewConfig.template : null;\n    let preview: HTMLElement;\n\n    if (previewTemplate && previewConfig) {\n      // Measure the element before we've inserted the preview\n      // since the insertion could throw off the measurement.\n      const rootRect = previewConfig.matchSize ? this._initialDomRect : null;\n      const viewRef = previewConfig.viewContainer.createEmbeddedView(\n        previewTemplate,\n        previewConfig.context,\n      );\n      viewRef.detectChanges();\n      preview = getRootNode(viewRef, this._document);\n      this._previewEmbeddedView = viewRef;\n      if (previewConfig.matchSize) {\n        matchElementSize(preview, rootRect!);\n      } else {\n        preview.style.transform = getTransform(\n          this._pickupPositionOnPage.x,\n          this._pickupPositionOnPage.y,\n        );\n      }\n    } else {\n      preview = deepCloneNode(this._rootElement);\n      matchElementSize(preview, this._initialDomRect!);\n\n      if (this._initialTransform) {\n        preview.style.transform = this._initialTransform;\n      }\n    }\n\n    extendStyles(\n      preview.style,\n      {\n        // It's important that we disable the pointer events on the preview, because\n        // it can throw off the `document.elementFromPoint` calls in the `CdkDropList`.\n        'pointer-events': 'none',\n        // We have to reset the margin, because it can throw off positioning relative to the viewport.\n        'margin': '0',\n        'position': 'fixed',\n        'top': '0',\n        'left': '0',\n        'z-index': this._zIndex + '',\n      },\n      importantProperties,\n    );\n\n    toggleNativeDragInteractions(preview, false);\n    preview.classList.add('cdk-drag-preview');\n    preview.setAttribute('popover', 'manual');\n    preview.setAttribute('dir', this._direction);\n\n    if (previewClass) {\n      if (Array.isArray(previewClass)) {\n        previewClass.forEach(className => preview.classList.add(className));\n      } else {\n        preview.classList.add(previewClass);\n      }\n    }\n\n    return preview;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {EmbeddedViewRef, ElementRef, NgZone, ViewContainerRef, TemplateRef} from '@angular/core';\nimport {ViewportRuler} from '@angular/cdk/scrolling';\nimport {Direction} from '@angular/cdk/bidi';\nimport {\n  normalizePassiveListenerOptions,\n  _getEventTarget,\n  _getShadowRoot,\n} from '@angular/cdk/platform';\nimport {coerceElement} from '@angular/cdk/coercion';\nimport {isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader} from '@angular/cdk/a11y';\nimport {Subscription, Subject, Observable} from 'rxjs';\nimport type {DropListRef} from './drop-list-ref';\nimport {DragDropRegistry} from './drag-drop-registry';\nimport {\n  combineTransforms,\n  DragCSSStyleDeclaration,\n  getTransform,\n  toggleNativeDragInteractions,\n  toggleVisibility,\n} from './dom/styling';\nimport {getMutableClientRect, adjustDomRect} from './dom/dom-rect';\nimport {ParentPositionTracker} from './dom/parent-position-tracker';\nimport {deepCloneNode} from './dom/clone-node';\nimport {DragPreviewTemplate, PreviewRef} from './preview-ref';\nimport {getRootNode} from './dom/root-node';\n\n/** Object that can be used to configure the behavior of DragRef. */\nexport interface DragRefConfig {\n  /**\n   * Minimum amount of pixels that the user should\n   * drag, before the CDK initiates a drag sequence.\n   */\n  dragStartThreshold: number;\n\n  /**\n   * Amount the pixels the user should drag before the CDK\n   * considers them to have changed the drag direction.\n   */\n  pointerDirectionChangeThreshold: number;\n\n  /** `z-index` for the absolutely-positioned elements that are created by the drag item. */\n  zIndex?: number;\n\n  /** Ref that the current drag item is nested in. */\n  parentDragRef?: DragRef;\n}\n\n/** Options that can be used to bind a passive event listener. */\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({passive: true});\n\n/** Options that can be used to bind an active event listener. */\nconst activeEventListenerOptions = normalizePassiveListenerOptions({passive: false});\n\n/** Event options that can be used to bind an active, capturing event. */\nconst activeCapturingEventOptions = normalizePassiveListenerOptions({\n  passive: false,\n  capture: true,\n});\n\n/**\n * Time in milliseconds for which to ignore mouse events, after\n * receiving a touch event. Used to avoid doing double work for\n * touch devices where the browser fires fake mouse events, in\n * addition to touch events.\n */\nconst MOUSE_EVENT_IGNORE_TIME = 800;\n\n// TODO(crisbeto): add an API for moving a draggable up/down the\n// list programmatically. Useful for keyboard controls.\n\n/** Template that can be used to create a drag helper element (e.g. a preview or a placeholder). */\ninterface DragHelperTemplate<T = any> {\n  template: TemplateRef<T> | null;\n  viewContainer: ViewContainerRef;\n  context: T;\n}\n\n/** Point on the page or within an element. */\nexport interface Point {\n  x: number;\n  y: number;\n}\n\n/** Inline styles to be set as `!important` while dragging. */\nconst dragImportantProperties = new Set([\n  // Needs to be important, because some `mat-table` sets `position: sticky !important`. See #22781.\n  'position',\n]);\n\n/**\n * Possible places into which the preview of a drag item can be inserted.\n * - `global` - Preview will be inserted at the bottom of the `<body>`. The advantage is that\n * you don't have to worry about `overflow: hidden` or `z-index`, but the item won't retain\n * its inherited styles.\n * - `parent` - Preview will be inserted into the parent of the drag item. The advantage is that\n * inherited styles will be preserved, but it may be clipped by `overflow: hidden` or not be\n * visible due to `z-index`. Furthermore, the preview is going to have an effect over selectors\n * like `:nth-child` and some flexbox configurations.\n * - `ElementRef<HTMLElement> | HTMLElement` - Preview will be inserted into a specific element.\n * Same advantages and disadvantages as `parent`.\n */\nexport type PreviewContainer = 'global' | 'parent' | ElementRef<HTMLElement> | HTMLElement;\n\n/**\n * Reference to a draggable item. Used to manipulate or dispose of the item.\n */\nexport class DragRef<T = any> {\n  /** Element displayed next to the user's pointer while the element is dragged. */\n  private _preview: PreviewRef | null;\n\n  /** Container into which to insert the preview. */\n  private _previewContainer: PreviewContainer | undefined;\n\n  /** Reference to the view of the placeholder element. */\n  private _placeholderRef: EmbeddedViewRef<any> | null;\n\n  /** Element that is rendered instead of the draggable item while it is being sorted. */\n  private _placeholder: HTMLElement;\n\n  /** Coordinates within the element at which the user picked up the element. */\n  private _pickupPositionInElement: Point;\n\n  /** Coordinates on the page at which the user picked up the element. */\n  private _pickupPositionOnPage: Point;\n\n  /**\n   * Anchor node used to save the place in the DOM where the element was\n   * picked up so that it can be restored at the end of the drag sequence.\n   */\n  private _anchor: Comment;\n\n  /**\n   * CSS `transform` applied to the element when it isn't being dragged. We need a\n   * passive transform in order for the dragged element to retain its new position\n   * after the user has stopped dragging and because we need to know the relative\n   * position in case they start dragging again. This corresponds to `element.style.transform`.\n   */\n  private _passiveTransform: Point = {x: 0, y: 0};\n\n  /** CSS `transform` that is applied to the element while it's being dragged. */\n  private _activeTransform: Point = {x: 0, y: 0};\n\n  /** Inline `transform` value that the element had before the first dragging sequence. */\n  private _initialTransform?: string;\n\n  /**\n   * Whether the dragging sequence has been started. Doesn't\n   * necessarily mean that the element has been moved.\n   */\n  private _hasStartedDragging = false;\n\n  /** Whether the element has moved since the user started dragging it. */\n  private _hasMoved: boolean;\n\n  /** Drop container in which the DragRef resided when dragging began. */\n  private _initialContainer: DropListRef;\n\n  /** Index at which the item started in its initial container. */\n  private _initialIndex: number;\n\n  /** Cached positions of scrollable parent elements. */\n  private _parentPositions: ParentPositionTracker;\n\n  /** Emits when the item is being moved. */\n  private readonly _moveEvents = new Subject<{\n    source: DragRef;\n    pointerPosition: {x: number; y: number};\n    event: MouseEvent | TouchEvent;\n    distance: Point;\n    delta: {x: -1 | 0 | 1; y: -1 | 0 | 1};\n  }>();\n\n  /** Keeps track of the direction in which the user is dragging along each axis. */\n  private _pointerDirectionDelta: {x: -1 | 0 | 1; y: -1 | 0 | 1};\n\n  /** Pointer position at which the last change in the delta occurred. */\n  private _pointerPositionAtLastDirectionChange: Point;\n\n  /** Position of the pointer at the last pointer event. */\n  private _lastKnownPointerPosition: Point;\n\n  /**\n   * Root DOM node of the drag instance. This is the element that will\n   * be moved around as the user is dragging.\n   */\n  private _rootElement: HTMLElement;\n\n  /**\n   * Nearest ancestor SVG, relative to which coordinates are calculated if dragging SVGElement\n   */\n  private _ownerSVGElement: SVGSVGElement | null;\n\n  /**\n   * Inline style value of `-webkit-tap-highlight-color` at the time the\n   * dragging was started. Used to restore the value once we're done dragging.\n   */\n  private _rootElementTapHighlight: string;\n\n  /** Subscription to pointer movement events. */\n  private _pointerMoveSubscription = Subscription.EMPTY;\n\n  /** Subscription to the event that is dispatched when the user lifts their pointer. */\n  private _pointerUpSubscription = Subscription.EMPTY;\n\n  /** Subscription to the viewport being scrolled. */\n  private _scrollSubscription = Subscription.EMPTY;\n\n  /** Subscription to the viewport being resized. */\n  private _resizeSubscription = Subscription.EMPTY;\n\n  /**\n   * Time at which the last touch event occurred. Used to avoid firing the same\n   * events multiple times on touch devices where the browser will fire a fake\n   * mouse event for each touch event, after a certain time.\n   */\n  private _lastTouchEventTime: number;\n\n  /** Time at which the last dragging sequence was started. */\n  private _dragStartTime: number;\n\n  /** Cached reference to the boundary element. */\n  private _boundaryElement: HTMLElement | null = null;\n\n  /** Whether the native dragging interactions have been enabled on the root element. */\n  private _nativeInteractionsEnabled = true;\n\n  /** Client rect of the root element when the dragging sequence has started. */\n  private _initialDomRect?: DOMRect;\n\n  /** Cached dimensions of the preview element. Should be read via `_getPreviewRect`. */\n  private _previewRect?: DOMRect;\n\n  /** Cached dimensions of the boundary element. */\n  private _boundaryRect?: DOMRect;\n\n  /** Element that will be used as a template to create the draggable item's preview. */\n  private _previewTemplate?: DragPreviewTemplate | null;\n\n  /** Template for placeholder element rendered to show where a draggable would be dropped. */\n  private _placeholderTemplate?: DragHelperTemplate | null;\n\n  /** Elements that can be used to drag the draggable item. */\n  private _handles: HTMLElement[] = [];\n\n  /** Registered handles that are currently disabled. */\n  private _disabledHandles = new Set<HTMLElement>();\n\n  /** Droppable container that the draggable is a part of. */\n  private _dropContainer?: DropListRef;\n\n  /** Layout direction of the item. */\n  private _direction: Direction = 'ltr';\n\n  /** Ref that the current drag item is nested in. */\n  private _parentDragRef: DragRef<unknown> | null;\n\n  /**\n   * Cached shadow root that the element is placed in. `null` means that the element isn't in\n   * the shadow DOM and `undefined` means that it hasn't been resolved yet. Should be read via\n   * `_getShadowRoot`, not directly.\n   */\n  private _cachedShadowRoot: ShadowRoot | null | undefined;\n\n  /** Axis along which dragging is locked. */\n  lockAxis: 'x' | 'y';\n\n  /**\n   * Amount of milliseconds to wait after the user has put their\n   * pointer down before starting to drag the element.\n   */\n  dragStartDelay: number | {touch: number; mouse: number} = 0;\n\n  /** Class to be added to the preview element. */\n  previewClass: string | string[] | undefined;\n\n  /** Whether starting to drag this element is disabled. */\n  get disabled(): boolean {\n    return this._disabled || !!(this._dropContainer && this._dropContainer.disabled);\n  }\n  set disabled(value: boolean) {\n    if (value !== this._disabled) {\n      this._disabled = value;\n      this._toggleNativeDragInteractions();\n      this._handles.forEach(handle => toggleNativeDragInteractions(handle, value));\n    }\n  }\n  private _disabled = false;\n\n  /** Emits as the drag sequence is being prepared. */\n  readonly beforeStarted = new Subject<void>();\n\n  /** Emits when the user starts dragging the item. */\n  readonly started = new Subject<{source: DragRef; event: MouseEvent | TouchEvent}>();\n\n  /** Emits when the user has released a drag item, before any animations have started. */\n  readonly released = new Subject<{source: DragRef; event: MouseEvent | TouchEvent}>();\n\n  /** Emits when the user stops dragging an item in the container. */\n  readonly ended = new Subject<{\n    source: DragRef;\n    distance: Point;\n    dropPoint: Point;\n    event: MouseEvent | TouchEvent;\n  }>();\n\n  /** Emits when the user has moved the item into a new container. */\n  readonly entered = new Subject<{container: DropListRef; item: DragRef; currentIndex: number}>();\n\n  /** Emits when the user removes the item its container by dragging it into another container. */\n  readonly exited = new Subject<{container: DropListRef; item: DragRef}>();\n\n  /** Emits when the user drops the item inside a container. */\n  readonly dropped = new Subject<{\n    previousIndex: number;\n    currentIndex: number;\n    item: DragRef;\n    container: DropListRef;\n    previousContainer: DropListRef;\n    distance: Point;\n    dropPoint: Point;\n    isPointerOverContainer: boolean;\n    event: MouseEvent | TouchEvent;\n  }>();\n\n  /**\n   * Emits as the user is dragging the item. Use with caution,\n   * because this event will fire for every pixel that the user has dragged.\n   */\n  readonly moved: Observable<{\n    source: DragRef;\n    pointerPosition: {x: number; y: number};\n    event: MouseEvent | TouchEvent;\n    distance: Point;\n    delta: {x: -1 | 0 | 1; y: -1 | 0 | 1};\n  }> = this._moveEvents;\n\n  /** Arbitrary data that can be attached to the drag item. */\n  data: T;\n\n  /**\n   * Function that can be used to customize the logic of how the position of the drag item\n   * is limited while it's being dragged. Gets called with a point containing the current position\n   * of the user's pointer on the page, a reference to the item being dragged and its dimensions.\n   * Should return a point describing where the item should be rendered.\n   */\n  constrainPosition?: (\n    userPointerPosition: Point,\n    dragRef: DragRef,\n    dimensions: DOMRect,\n    pickupPositionInElement: Point,\n  ) => Point;\n\n  constructor(\n    element: ElementRef<HTMLElement> | HTMLElement,\n    private _config: DragRefConfig,\n    private _document: Document,\n    private _ngZone: NgZone,\n    private _viewportRuler: ViewportRuler,\n    private _dragDropRegistry: DragDropRegistry<DragRef, DropListRef>,\n  ) {\n    this.withRootElement(element).withParent(_config.parentDragRef || null);\n    this._parentPositions = new ParentPositionTracker(_document);\n    _dragDropRegistry.registerDragItem(this);\n  }\n\n  /**\n   * Returns the element that is being used as a placeholder\n   * while the current element is being dragged.\n   */\n  getPlaceholderElement(): HTMLElement {\n    return this._placeholder;\n  }\n\n  /** Returns the root draggable element. */\n  getRootElement(): HTMLElement {\n    return this._rootElement;\n  }\n\n  /**\n   * Gets the currently-visible element that represents the drag item.\n   * While dragging this is the placeholder, otherwise it's the root element.\n   */\n  getVisibleElement(): HTMLElement {\n    return this.isDragging() ? this.getPlaceholderElement() : this.getRootElement();\n  }\n\n  /** Registers the handles that can be used to drag the element. */\n  withHandles(handles: (HTMLElement | ElementRef<HTMLElement>)[]): this {\n    this._handles = handles.map(handle => coerceElement(handle));\n    this._handles.forEach(handle => toggleNativeDragInteractions(handle, this.disabled));\n    this._toggleNativeDragInteractions();\n\n    // Delete any lingering disabled handles that may have been destroyed. Note that we re-create\n    // the set, rather than iterate over it and filter out the destroyed handles, because while\n    // the ES spec allows for sets to be modified while they're being iterated over, some polyfills\n    // use an array internally which may throw an error.\n    const disabledHandles = new Set<HTMLElement>();\n    this._disabledHandles.forEach(handle => {\n      if (this._handles.indexOf(handle) > -1) {\n        disabledHandles.add(handle);\n      }\n    });\n    this._disabledHandles = disabledHandles;\n    return this;\n  }\n\n  /**\n   * Registers the template that should be used for the drag preview.\n   * @param template Template that from which to stamp out the preview.\n   */\n  withPreviewTemplate(template: DragPreviewTemplate | null): this {\n    this._previewTemplate = template;\n    return this;\n  }\n\n  /**\n   * Registers the template that should be used for the drag placeholder.\n   * @param template Template that from which to stamp out the placeholder.\n   */\n  withPlaceholderTemplate(template: DragHelperTemplate | null): this {\n    this._placeholderTemplate = template;\n    return this;\n  }\n\n  /**\n   * Sets an alternate drag root element. The root element is the element that will be moved as\n   * the user is dragging. Passing an alternate root element is useful when trying to enable\n   * dragging on an element that you might not have access to.\n   */\n  withRootElement(rootElement: ElementRef<HTMLElement> | HTMLElement): this {\n    const element = coerceElement(rootElement);\n\n    if (element !== this._rootElement) {\n      if (this._rootElement) {\n        this._removeRootElementListeners(this._rootElement);\n      }\n\n      this._ngZone.runOutsideAngular(() => {\n        element.addEventListener('mousedown', this._pointerDown, activeEventListenerOptions);\n        element.addEventListener('touchstart', this._pointerDown, passiveEventListenerOptions);\n        element.addEventListener('dragstart', this._nativeDragStart, activeEventListenerOptions);\n      });\n      this._initialTransform = undefined;\n      this._rootElement = element;\n    }\n\n    if (typeof SVGElement !== 'undefined' && this._rootElement instanceof SVGElement) {\n      this._ownerSVGElement = this._rootElement.ownerSVGElement;\n    }\n\n    return this;\n  }\n\n  /**\n   * Element to which the draggable's position will be constrained.\n   */\n  withBoundaryElement(boundaryElement: ElementRef<HTMLElement> | HTMLElement | null): this {\n    this._boundaryElement = boundaryElement ? coerceElement(boundaryElement) : null;\n    this._resizeSubscription.unsubscribe();\n    if (boundaryElement) {\n      this._resizeSubscription = this._viewportRuler\n        .change(10)\n        .subscribe(() => this._containInsideBoundaryOnResize());\n    }\n    return this;\n  }\n\n  /** Sets the parent ref that the ref is nested in.  */\n  withParent(parent: DragRef<unknown> | null): this {\n    this._parentDragRef = parent;\n    return this;\n  }\n\n  /** Removes the dragging functionality from the DOM element. */\n  dispose() {\n    this._removeRootElementListeners(this._rootElement);\n\n    // Do this check before removing from the registry since it'll\n    // stop being considered as dragged once it is removed.\n    if (this.isDragging()) {\n      // Since we move out the element to the end of the body while it's being\n      // dragged, we have to make sure that it's removed if it gets destroyed.\n      this._rootElement?.remove();\n    }\n\n    this._anchor?.remove();\n    this._destroyPreview();\n    this._destroyPlaceholder();\n    this._dragDropRegistry.removeDragItem(this);\n    this._removeListeners();\n    this.beforeStarted.complete();\n    this.started.complete();\n    this.released.complete();\n    this.ended.complete();\n    this.entered.complete();\n    this.exited.complete();\n    this.dropped.complete();\n    this._moveEvents.complete();\n    this._handles = [];\n    this._disabledHandles.clear();\n    this._dropContainer = undefined;\n    this._resizeSubscription.unsubscribe();\n    this._parentPositions.clear();\n    this._boundaryElement =\n      this._rootElement =\n      this._ownerSVGElement =\n      this._placeholderTemplate =\n      this._previewTemplate =\n      this._anchor =\n      this._parentDragRef =\n        null!;\n  }\n\n  /** Checks whether the element is currently being dragged. */\n  isDragging(): boolean {\n    return this._hasStartedDragging && this._dragDropRegistry.isDragging(this);\n  }\n\n  /** Resets a standalone drag item to its initial position. */\n  reset(): void {\n    this._rootElement.style.transform = this._initialTransform || '';\n    this._activeTransform = {x: 0, y: 0};\n    this._passiveTransform = {x: 0, y: 0};\n  }\n\n  /**\n   * Sets a handle as disabled. While a handle is disabled, it'll capture and interrupt dragging.\n   * @param handle Handle element that should be disabled.\n   */\n  disableHandle(handle: HTMLElement) {\n    if (!this._disabledHandles.has(handle) && this._handles.indexOf(handle) > -1) {\n      this._disabledHandles.add(handle);\n      toggleNativeDragInteractions(handle, true);\n    }\n  }\n\n  /**\n   * Enables a handle, if it has been disabled.\n   * @param handle Handle element to be enabled.\n   */\n  enableHandle(handle: HTMLElement) {\n    if (this._disabledHandles.has(handle)) {\n      this._disabledHandles.delete(handle);\n      toggleNativeDragInteractions(handle, this.disabled);\n    }\n  }\n\n  /** Sets the layout direction of the draggable item. */\n  withDirection(direction: Direction): this {\n    this._direction = direction;\n    return this;\n  }\n\n  /** Sets the container that the item is part of. */\n  _withDropContainer(container: DropListRef) {\n    this._dropContainer = container;\n  }\n\n  /**\n   * Gets the current position in pixels the draggable outside of a drop container.\n   */\n  getFreeDragPosition(): Readonly<Point> {\n    const position = this.isDragging() ? this._activeTransform : this._passiveTransform;\n    return {x: position.x, y: position.y};\n  }\n\n  /**\n   * Sets the current position in pixels the draggable outside of a drop container.\n   * @param value New position to be set.\n   */\n  setFreeDragPosition(value: Point): this {\n    this._activeTransform = {x: 0, y: 0};\n    this._passiveTransform.x = value.x;\n    this._passiveTransform.y = value.y;\n\n    if (!this._dropContainer) {\n      this._applyRootElementTransform(value.x, value.y);\n    }\n\n    return this;\n  }\n\n  /**\n   * Sets the container into which to insert the preview element.\n   * @param value Container into which to insert the preview.\n   */\n  withPreviewContainer(value: PreviewContainer): this {\n    this._previewContainer = value;\n    return this;\n  }\n\n  /** Updates the item's sort order based on the last-known pointer position. */\n  _sortFromLastPointerPosition() {\n    const position = this._lastKnownPointerPosition;\n\n    if (position && this._dropContainer) {\n      this._updateActiveDropContainer(this._getConstrainedPointerPosition(position), position);\n    }\n  }\n\n  /** Unsubscribes from the global subscriptions. */\n  private _removeListeners() {\n    this._pointerMoveSubscription.unsubscribe();\n    this._pointerUpSubscription.unsubscribe();\n    this._scrollSubscription.unsubscribe();\n    this._getShadowRoot()?.removeEventListener(\n      'selectstart',\n      shadowDomSelectStart,\n      activeCapturingEventOptions,\n    );\n  }\n\n  /** Destroys the preview element and its ViewRef. */\n  private _destroyPreview() {\n    this._preview?.destroy();\n    this._preview = null;\n  }\n\n  /** Destroys the placeholder element and its ViewRef. */\n  private _destroyPlaceholder() {\n    this._placeholder?.remove();\n    this._placeholderRef?.destroy();\n    this._placeholder = this._placeholderRef = null!;\n  }\n\n  /** Handler for the `mousedown`/`touchstart` events. */\n  private _pointerDown = (event: MouseEvent | TouchEvent) => {\n    this.beforeStarted.next();\n\n    // Delegate the event based on whether it started from a handle or the element itself.\n    if (this._handles.length) {\n      const targetHandle = this._getTargetHandle(event);\n\n      if (targetHandle && !this._disabledHandles.has(targetHandle) && !this.disabled) {\n        this._initializeDragSequence(targetHandle, event);\n      }\n    } else if (!this.disabled) {\n      this._initializeDragSequence(this._rootElement, event);\n    }\n  };\n\n  /** Handler that is invoked when the user moves their pointer after they've initiated a drag. */\n  private _pointerMove = (event: MouseEvent | TouchEvent) => {\n    const pointerPosition = this._getPointerPositionOnPage(event);\n\n    if (!this._hasStartedDragging) {\n      const distanceX = Math.abs(pointerPosition.x - this._pickupPositionOnPage.x);\n      const distanceY = Math.abs(pointerPosition.y - this._pickupPositionOnPage.y);\n      const isOverThreshold = distanceX + distanceY >= this._config.dragStartThreshold;\n\n      // Only start dragging after the user has moved more than the minimum distance in either\n      // direction. Note that this is preferable over doing something like `skip(minimumDistance)`\n      // in the `pointerMove` subscription, because we're not guaranteed to have one move event\n      // per pixel of movement (e.g. if the user moves their pointer quickly).\n      if (isOverThreshold) {\n        const isDelayElapsed = Date.now() >= this._dragStartTime + this._getDragStartDelay(event);\n        const container = this._dropContainer;\n\n        if (!isDelayElapsed) {\n          this._endDragSequence(event);\n          return;\n        }\n\n        // Prevent other drag sequences from starting while something in the container is still\n        // being dragged. This can happen while we're waiting for the drop animation to finish\n        // and can cause errors, because some elements might still be moving around.\n        if (!container || (!container.isDragging() && !container.isReceiving())) {\n          // Prevent the default action as soon as the dragging sequence is considered as\n          // \"started\" since waiting for the next event can allow the device to begin scrolling.\n          if (event.cancelable) {\n            event.preventDefault();\n          }\n          this._hasStartedDragging = true;\n          this._ngZone.run(() => this._startDragSequence(event));\n        }\n      }\n\n      return;\n    }\n\n    // We prevent the default action down here so that we know that dragging has started. This is\n    // important for touch devices where doing this too early can unnecessarily block scrolling,\n    // if there's a dragging delay.\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n\n    const constrainedPointerPosition = this._getConstrainedPointerPosition(pointerPosition);\n    this._hasMoved = true;\n    this._lastKnownPointerPosition = pointerPosition;\n    this._updatePointerDirectionDelta(constrainedPointerPosition);\n\n    if (this._dropContainer) {\n      this._updateActiveDropContainer(constrainedPointerPosition, pointerPosition);\n    } else {\n      // If there's a position constraint function, we want the element's top/left to be at the\n      // specific position on the page. Use the initial position as a reference if that's the case.\n      const offset = this.constrainPosition ? this._initialDomRect! : this._pickupPositionOnPage;\n      const activeTransform = this._activeTransform;\n      activeTransform.x = constrainedPointerPosition.x - offset.x + this._passiveTransform.x;\n      activeTransform.y = constrainedPointerPosition.y - offset.y + this._passiveTransform.y;\n      this._applyRootElementTransform(activeTransform.x, activeTransform.y);\n    }\n\n    // Since this event gets fired for every pixel while dragging, we only\n    // want to fire it if the consumer opted into it. Also we have to\n    // re-enter the zone because we run all of the events on the outside.\n    if (this._moveEvents.observers.length) {\n      this._ngZone.run(() => {\n        this._moveEvents.next({\n          source: this,\n          pointerPosition: constrainedPointerPosition,\n          event,\n          distance: this._getDragDistance(constrainedPointerPosition),\n          delta: this._pointerDirectionDelta,\n        });\n      });\n    }\n  };\n\n  /** Handler that is invoked when the user lifts their pointer up, after initiating a drag. */\n  private _pointerUp = (event: MouseEvent | TouchEvent) => {\n    this._endDragSequence(event);\n  };\n\n  /**\n   * Clears subscriptions and stops the dragging sequence.\n   * @param event Browser event object that ended the sequence.\n   */\n  private _endDragSequence(event: MouseEvent | TouchEvent) {\n    // Note that here we use `isDragging` from the service, rather than from `this`.\n    // The difference is that the one from the service reflects whether a dragging sequence\n    // has been initiated, whereas the one on `this` includes whether the user has passed\n    // the minimum dragging threshold.\n    if (!this._dragDropRegistry.isDragging(this)) {\n      return;\n    }\n\n    this._removeListeners();\n    this._dragDropRegistry.stopDragging(this);\n    this._toggleNativeDragInteractions();\n\n    if (this._handles) {\n      (this._rootElement.style as DragCSSStyleDeclaration).webkitTapHighlightColor =\n        this._rootElementTapHighlight;\n    }\n\n    if (!this._hasStartedDragging) {\n      return;\n    }\n\n    this.released.next({source: this, event});\n\n    if (this._dropContainer) {\n      // Stop scrolling immediately, instead of waiting for the animation to finish.\n      this._dropContainer._stopScrolling();\n      this._animatePreviewToPlaceholder().then(() => {\n        this._cleanupDragArtifacts(event);\n        this._cleanupCachedDimensions();\n        this._dragDropRegistry.stopDragging(this);\n      });\n    } else {\n      // Convert the active transform into a passive one. This means that next time\n      // the user starts dragging the item, its position will be calculated relatively\n      // to the new passive transform.\n      this._passiveTransform.x = this._activeTransform.x;\n      const pointerPosition = this._getPointerPositionOnPage(event);\n      this._passiveTransform.y = this._activeTransform.y;\n      this._ngZone.run(() => {\n        this.ended.next({\n          source: this,\n          distance: this._getDragDistance(pointerPosition),\n          dropPoint: pointerPosition,\n          event,\n        });\n      });\n      this._cleanupCachedDimensions();\n      this._dragDropRegistry.stopDragging(this);\n    }\n  }\n\n  /** Starts the dragging sequence. */\n  private _startDragSequence(event: MouseEvent | TouchEvent) {\n    if (isTouchEvent(event)) {\n      this._lastTouchEventTime = Date.now();\n    }\n\n    this._toggleNativeDragInteractions();\n\n    // Needs to happen before the root element is moved.\n    const shadowRoot = this._getShadowRoot();\n    const dropContainer = this._dropContainer;\n\n    if (shadowRoot) {\n      // In some browsers the global `selectstart` that we maintain in the `DragDropRegistry`\n      // doesn't cross the shadow boundary so we have to prevent it at the shadow root (see #28792).\n      this._ngZone.runOutsideAngular(() => {\n        shadowRoot.addEventListener(\n          'selectstart',\n          shadowDomSelectStart,\n          activeCapturingEventOptions,\n        );\n      });\n    }\n\n    if (dropContainer) {\n      const element = this._rootElement;\n      const parent = element.parentNode as HTMLElement;\n      const placeholder = (this._placeholder = this._createPlaceholderElement());\n      const anchor = (this._anchor = this._anchor || this._document.createComment(''));\n\n      // Insert an anchor node so that we can restore the element's position in the DOM.\n      parent.insertBefore(anchor, element);\n\n      // There's no risk of transforms stacking when inside a drop container so\n      // we can keep the initial transform up to date any time dragging starts.\n      this._initialTransform = element.style.transform || '';\n\n      // Create the preview after the initial transform has\n      // been cached, because it can be affected by the transform.\n      this._preview = new PreviewRef(\n        this._document,\n        this._rootElement,\n        this._direction,\n        this._initialDomRect!,\n        this._previewTemplate || null,\n        this.previewClass || null,\n        this._pickupPositionOnPage,\n        this._initialTransform,\n        this._config.zIndex || 1000,\n      );\n      this._preview.attach(this._getPreviewInsertionPoint(parent, shadowRoot));\n\n      // We move the element out at the end of the body and we make it hidden, because keeping it in\n      // place will throw off the consumer's `:last-child` selectors. We can't remove the element\n      // from the DOM completely, because iOS will stop firing all subsequent events in the chain.\n      toggleVisibility(element, false, dragImportantProperties);\n      this._document.body.appendChild(parent.replaceChild(placeholder, element));\n      this.started.next({source: this, event}); // Emit before notifying the container.\n      dropContainer.start();\n      this._initialContainer = dropContainer;\n      this._initialIndex = dropContainer.getItemIndex(this);\n    } else {\n      this.started.next({source: this, event});\n      this._initialContainer = this._initialIndex = undefined!;\n    }\n\n    // Important to run after we've called `start` on the parent container\n    // so that it has had time to resolve its scrollable parents.\n    this._parentPositions.cache(dropContainer ? dropContainer.getScrollableParents() : []);\n  }\n\n  /**\n   * Sets up the different variables and subscriptions\n   * that will be necessary for the dragging sequence.\n   * @param referenceElement Element that started the drag sequence.\n   * @param event Browser event object that started the sequence.\n   */\n  private _initializeDragSequence(referenceElement: HTMLElement, event: MouseEvent | TouchEvent) {\n    // Stop propagation if the item is inside another\n    // draggable so we don't start multiple drag sequences.\n    if (this._parentDragRef) {\n      event.stopPropagation();\n    }\n\n    const isDragging = this.isDragging();\n    const isTouchSequence = isTouchEvent(event);\n    const isAuxiliaryMouseButton = !isTouchSequence && (event as MouseEvent).button !== 0;\n    const rootElement = this._rootElement;\n    const target = _getEventTarget(event);\n    const isSyntheticEvent =\n      !isTouchSequence &&\n      this._lastTouchEventTime &&\n      this._lastTouchEventTime + MOUSE_EVENT_IGNORE_TIME > Date.now();\n    const isFakeEvent = isTouchSequence\n      ? isFakeTouchstartFromScreenReader(event as TouchEvent)\n      : isFakeMousedownFromScreenReader(event as MouseEvent);\n\n    // If the event started from an element with the native HTML drag&drop, it'll interfere\n    // with our own dragging (e.g. `img` tags do it by default). Prevent the default action\n    // to stop it from happening. Note that preventing on `dragstart` also seems to work, but\n    // it's flaky and it fails if the user drags it away quickly. Also note that we only want\n    // to do this for `mousedown` since doing the same for `touchstart` will stop any `click`\n    // events from firing on touch devices.\n    if (target && (target as HTMLElement).draggable && event.type === 'mousedown') {\n      event.preventDefault();\n    }\n\n    // Abort if the user is already dragging or is using a mouse button other than the primary one.\n    if (isDragging || isAuxiliaryMouseButton || isSyntheticEvent || isFakeEvent) {\n      return;\n    }\n\n    // If we've got handles, we need to disable the tap highlight on the entire root element,\n    // otherwise iOS will still add it, even though all the drag interactions on the handle\n    // are disabled.\n    if (this._handles.length) {\n      const rootStyles = rootElement.style as DragCSSStyleDeclaration;\n      this._rootElementTapHighlight = rootStyles.webkitTapHighlightColor || '';\n      rootStyles.webkitTapHighlightColor = 'transparent';\n    }\n\n    this._hasStartedDragging = this._hasMoved = false;\n\n    // Avoid multiple subscriptions and memory leaks when multi touch\n    // (isDragging check above isn't enough because of possible temporal and/or dimensional delays)\n    this._removeListeners();\n    this._initialDomRect = this._rootElement.getBoundingClientRect();\n    this._pointerMoveSubscription = this._dragDropRegistry.pointerMove.subscribe(this._pointerMove);\n    this._pointerUpSubscription = this._dragDropRegistry.pointerUp.subscribe(this._pointerUp);\n    this._scrollSubscription = this._dragDropRegistry\n      .scrolled(this._getShadowRoot())\n      .subscribe(scrollEvent => this._updateOnScroll(scrollEvent));\n\n    if (this._boundaryElement) {\n      this._boundaryRect = getMutableClientRect(this._boundaryElement);\n    }\n\n    // If we have a custom preview we can't know ahead of time how large it'll be so we position\n    // it next to the cursor. The exception is when the consumer has opted into making the preview\n    // the same size as the root element, in which case we do know the size.\n    const previewTemplate = this._previewTemplate;\n    this._pickupPositionInElement =\n      previewTemplate && previewTemplate.template && !previewTemplate.matchSize\n        ? {x: 0, y: 0}\n        : this._getPointerPositionInElement(this._initialDomRect, referenceElement, event);\n    const pointerPosition =\n      (this._pickupPositionOnPage =\n      this._lastKnownPointerPosition =\n        this._getPointerPositionOnPage(event));\n    this._pointerDirectionDelta = {x: 0, y: 0};\n    this._pointerPositionAtLastDirectionChange = {x: pointerPosition.x, y: pointerPosition.y};\n    this._dragStartTime = Date.now();\n    this._dragDropRegistry.startDragging(this, event);\n  }\n\n  /** Cleans up the DOM artifacts that were added to facilitate the element being dragged. */\n  private _cleanupDragArtifacts(event: MouseEvent | TouchEvent) {\n    // Restore the element's visibility and insert it at its old position in the DOM.\n    // It's important that we maintain the position, because moving the element around in the DOM\n    // can throw off `NgFor` which does smart diffing and re-creates elements only when necessary,\n    // while moving the existing elements in all other cases.\n    toggleVisibility(this._rootElement, true, dragImportantProperties);\n    this._anchor.parentNode!.replaceChild(this._rootElement, this._anchor);\n\n    this._destroyPreview();\n    this._destroyPlaceholder();\n    this._initialDomRect =\n      this._boundaryRect =\n      this._previewRect =\n      this._initialTransform =\n        undefined;\n\n    // Re-enter the NgZone since we bound `document` events on the outside.\n    this._ngZone.run(() => {\n      const container = this._dropContainer!;\n      const currentIndex = container.getItemIndex(this);\n      const pointerPosition = this._getPointerPositionOnPage(event);\n      const distance = this._getDragDistance(pointerPosition);\n      const isPointerOverContainer = container._isOverContainer(\n        pointerPosition.x,\n        pointerPosition.y,\n      );\n\n      this.ended.next({source: this, distance, dropPoint: pointerPosition, event});\n      this.dropped.next({\n        item: this,\n        currentIndex,\n        previousIndex: this._initialIndex,\n        container: container,\n        previousContainer: this._initialContainer,\n        isPointerOverContainer,\n        distance,\n        dropPoint: pointerPosition,\n        event,\n      });\n      container.drop(\n        this,\n        currentIndex,\n        this._initialIndex,\n        this._initialContainer,\n        isPointerOverContainer,\n        distance,\n        pointerPosition,\n        event,\n      );\n      this._dropContainer = this._initialContainer;\n    });\n  }\n\n  /**\n   * Updates the item's position in its drop container, or moves it\n   * into a new one, depending on its current drag position.\n   */\n  private _updateActiveDropContainer({x, y}: Point, {x: rawX, y: rawY}: Point) {\n    // Drop container that draggable has been moved into.\n    let newContainer = this._initialContainer._getSiblingContainerFromPosition(this, x, y);\n\n    // If we couldn't find a new container to move the item into, and the item has left its\n    // initial container, check whether the it's over the initial container. This handles the\n    // case where two containers are connected one way and the user tries to undo dragging an\n    // item into a new container.\n    if (\n      !newContainer &&\n      this._dropContainer !== this._initialContainer &&\n      this._initialContainer._isOverContainer(x, y)\n    ) {\n      newContainer = this._initialContainer;\n    }\n\n    if (newContainer && newContainer !== this._dropContainer) {\n      this._ngZone.run(() => {\n        // Notify the old container that the item has left.\n        this.exited.next({item: this, container: this._dropContainer!});\n        this._dropContainer!.exit(this);\n        // Notify the new container that the item has entered.\n        this._dropContainer = newContainer!;\n        this._dropContainer.enter(\n          this,\n          x,\n          y,\n          newContainer === this._initialContainer &&\n            // If we're re-entering the initial container and sorting is disabled,\n            // put item the into its starting index to begin with.\n            newContainer.sortingDisabled\n            ? this._initialIndex\n            : undefined,\n        );\n        this.entered.next({\n          item: this,\n          container: newContainer!,\n          currentIndex: newContainer!.getItemIndex(this),\n        });\n      });\n    }\n\n    // Dragging may have been interrupted as a result of the events above.\n    if (this.isDragging()) {\n      this._dropContainer!._startScrollingIfNecessary(rawX, rawY);\n      this._dropContainer!._sortItem(this, x, y, this._pointerDirectionDelta);\n\n      if (this.constrainPosition) {\n        this._applyPreviewTransform(x, y);\n      } else {\n        this._applyPreviewTransform(\n          x - this._pickupPositionInElement.x,\n          y - this._pickupPositionInElement.y,\n        );\n      }\n    }\n  }\n\n  /**\n   * Animates the preview element from its current position to the location of the drop placeholder.\n   * @returns Promise that resolves when the animation completes.\n   */\n  private _animatePreviewToPlaceholder(): Promise<void> {\n    // If the user hasn't moved yet, the transitionend event won't fire.\n    if (!this._hasMoved) {\n      return Promise.resolve();\n    }\n\n    const placeholderRect = this._placeholder.getBoundingClientRect();\n\n    // Apply the class that adds a transition to the preview.\n    this._preview!.addClass('cdk-drag-animating');\n\n    // Move the preview to the placeholder position.\n    this._applyPreviewTransform(placeholderRect.left, placeholderRect.top);\n\n    // If the element doesn't have a `transition`, the `transitionend` event won't fire. Since\n    // we need to trigger a style recalculation in order for the `cdk-drag-animating` class to\n    // apply its style, we take advantage of the available info to figure out whether we need to\n    // bind the event in the first place.\n    const duration = this._preview!.getTransitionDuration();\n\n    if (duration === 0) {\n      return Promise.resolve();\n    }\n\n    return this._ngZone.runOutsideAngular(() => {\n      return new Promise(resolve => {\n        const handler = ((event: TransitionEvent) => {\n          if (\n            !event ||\n            (_getEventTarget(event) === this._preview && event.propertyName === 'transform')\n          ) {\n            this._preview?.removeEventListener('transitionend', handler);\n            resolve();\n            clearTimeout(timeout);\n          }\n        }) as EventListenerOrEventListenerObject;\n\n        // If a transition is short enough, the browser might not fire the `transitionend` event.\n        // Since we know how long it's supposed to take, add a timeout with a 50% buffer that'll\n        // fire if the transition hasn't completed when it was supposed to.\n        const timeout = setTimeout(handler as Function, duration * 1.5);\n        this._preview!.addEventListener('transitionend', handler);\n      });\n    });\n  }\n\n  /** Creates an element that will be shown instead of the current element while dragging. */\n  private _createPlaceholderElement(): HTMLElement {\n    const placeholderConfig = this._placeholderTemplate;\n    const placeholderTemplate = placeholderConfig ? placeholderConfig.template : null;\n    let placeholder: HTMLElement;\n\n    if (placeholderTemplate) {\n      this._placeholderRef = placeholderConfig!.viewContainer.createEmbeddedView(\n        placeholderTemplate,\n        placeholderConfig!.context,\n      );\n      this._placeholderRef.detectChanges();\n      placeholder = getRootNode(this._placeholderRef, this._document);\n    } else {\n      placeholder = deepCloneNode(this._rootElement);\n    }\n\n    // Stop pointer events on the preview so the user can't\n    // interact with it while the preview is animating.\n    placeholder.style.pointerEvents = 'none';\n    placeholder.classList.add('cdk-drag-placeholder');\n    return placeholder;\n  }\n\n  /**\n   * Figures out the coordinates at which an element was picked up.\n   * @param referenceElement Element that initiated the dragging.\n   * @param event Event that initiated the dragging.\n   */\n  private _getPointerPositionInElement(\n    elementRect: DOMRect,\n    referenceElement: HTMLElement,\n    event: MouseEvent | TouchEvent,\n  ): Point {\n    const handleElement = referenceElement === this._rootElement ? null : referenceElement;\n    const referenceRect = handleElement ? handleElement.getBoundingClientRect() : elementRect;\n    const point = isTouchEvent(event) ? event.targetTouches[0] : event;\n    const scrollPosition = this._getViewportScrollPosition();\n    const x = point.pageX - referenceRect.left - scrollPosition.left;\n    const y = point.pageY - referenceRect.top - scrollPosition.top;\n\n    return {\n      x: referenceRect.left - elementRect.left + x,\n      y: referenceRect.top - elementRect.top + y,\n    };\n  }\n\n  /** Determines the point of the page that was touched by the user. */\n  private _getPointerPositionOnPage(event: MouseEvent | TouchEvent): Point {\n    const scrollPosition = this._getViewportScrollPosition();\n    const point = isTouchEvent(event)\n      ? // `touches` will be empty for start/end events so we have to fall back to `changedTouches`.\n        // Also note that on real devices we're guaranteed for either `touches` or `changedTouches`\n        // to have a value, but Firefox in device emulation mode has a bug where both can be empty\n        // for `touchstart` and `touchend` so we fall back to a dummy object in order to avoid\n        // throwing an error. The value returned here will be incorrect, but since this only\n        // breaks inside a developer tool and the value is only used for secondary information,\n        // we can get away with it. See https://bugzilla.mozilla.org/show_bug.cgi?id=1615824.\n        event.touches[0] || event.changedTouches[0] || {pageX: 0, pageY: 0}\n      : event;\n\n    const x = point.pageX - scrollPosition.left;\n    const y = point.pageY - scrollPosition.top;\n\n    // if dragging SVG element, try to convert from the screen coordinate system to the SVG\n    // coordinate system\n    if (this._ownerSVGElement) {\n      const svgMatrix = this._ownerSVGElement.getScreenCTM();\n      if (svgMatrix) {\n        const svgPoint = this._ownerSVGElement.createSVGPoint();\n        svgPoint.x = x;\n        svgPoint.y = y;\n        return svgPoint.matrixTransform(svgMatrix.inverse());\n      }\n    }\n\n    return {x, y};\n  }\n\n  /** Gets the pointer position on the page, accounting for any position constraints. */\n  private _getConstrainedPointerPosition(point: Point): Point {\n    const dropContainerLock = this._dropContainer ? this._dropContainer.lockAxis : null;\n    let {x, y} = this.constrainPosition\n      ? this.constrainPosition(point, this, this._initialDomRect!, this._pickupPositionInElement)\n      : point;\n\n    if (this.lockAxis === 'x' || dropContainerLock === 'x') {\n      y =\n        this._pickupPositionOnPage.y -\n        (this.constrainPosition ? this._pickupPositionInElement.y : 0);\n    } else if (this.lockAxis === 'y' || dropContainerLock === 'y') {\n      x =\n        this._pickupPositionOnPage.x -\n        (this.constrainPosition ? this._pickupPositionInElement.x : 0);\n    }\n\n    if (this._boundaryRect) {\n      // If not using a custom constrain we need to account for the pickup position in the element\n      // otherwise we do not need to do this, as it has already been accounted for\n      const {x: pickupX, y: pickupY} = !this.constrainPosition\n        ? this._pickupPositionInElement\n        : {x: 0, y: 0};\n\n      const boundaryRect = this._boundaryRect;\n      const {width: previewWidth, height: previewHeight} = this._getPreviewRect();\n      const minY = boundaryRect.top + pickupY;\n      const maxY = boundaryRect.bottom - (previewHeight - pickupY);\n      const minX = boundaryRect.left + pickupX;\n      const maxX = boundaryRect.right - (previewWidth - pickupX);\n\n      x = clamp(x, minX, maxX);\n      y = clamp(y, minY, maxY);\n    }\n\n    return {x, y};\n  }\n\n  /** Updates the current drag delta, based on the user's current pointer position on the page. */\n  private _updatePointerDirectionDelta(pointerPositionOnPage: Point) {\n    const {x, y} = pointerPositionOnPage;\n    const delta = this._pointerDirectionDelta;\n    const positionSinceLastChange = this._pointerPositionAtLastDirectionChange;\n\n    // Amount of pixels the user has dragged since the last time the direction changed.\n    const changeX = Math.abs(x - positionSinceLastChange.x);\n    const changeY = Math.abs(y - positionSinceLastChange.y);\n\n    // Because we handle pointer events on a per-pixel basis, we don't want the delta\n    // to change for every pixel, otherwise anything that depends on it can look erratic.\n    // To make the delta more consistent, we track how much the user has moved since the last\n    // delta change and we only update it after it has reached a certain threshold.\n    if (changeX > this._config.pointerDirectionChangeThreshold) {\n      delta.x = x > positionSinceLastChange.x ? 1 : -1;\n      positionSinceLastChange.x = x;\n    }\n\n    if (changeY > this._config.pointerDirectionChangeThreshold) {\n      delta.y = y > positionSinceLastChange.y ? 1 : -1;\n      positionSinceLastChange.y = y;\n    }\n\n    return delta;\n  }\n\n  /** Toggles the native drag interactions, based on how many handles are registered. */\n  private _toggleNativeDragInteractions() {\n    if (!this._rootElement || !this._handles) {\n      return;\n    }\n\n    const shouldEnable = this._handles.length > 0 || !this.isDragging();\n\n    if (shouldEnable !== this._nativeInteractionsEnabled) {\n      this._nativeInteractionsEnabled = shouldEnable;\n      toggleNativeDragInteractions(this._rootElement, shouldEnable);\n    }\n  }\n\n  /** Removes the manually-added event listeners from the root element. */\n  private _removeRootElementListeners(element: HTMLElement) {\n    element.removeEventListener('mousedown', this._pointerDown, activeEventListenerOptions);\n    element.removeEventListener('touchstart', this._pointerDown, passiveEventListenerOptions);\n    element.removeEventListener('dragstart', this._nativeDragStart, activeEventListenerOptions);\n  }\n\n  /**\n   * Applies a `transform` to the root element, taking into account any existing transforms on it.\n   * @param x New transform value along the X axis.\n   * @param y New transform value along the Y axis.\n   */\n  private _applyRootElementTransform(x: number, y: number) {\n    const transform = getTransform(x, y);\n    const styles = this._rootElement.style;\n\n    // Cache the previous transform amount only after the first drag sequence, because\n    // we don't want our own transforms to stack on top of each other.\n    // Should be excluded none because none + translate3d(x, y, x) is invalid css\n    if (this._initialTransform == null) {\n      this._initialTransform =\n        styles.transform && styles.transform != 'none' ? styles.transform : '';\n    }\n\n    // Preserve the previous `transform` value, if there was one. Note that we apply our own\n    // transform before the user's, because things like rotation can affect which direction\n    // the element will be translated towards.\n    styles.transform = combineTransforms(transform, this._initialTransform);\n  }\n\n  /**\n   * Applies a `transform` to the preview, taking into account any existing transforms on it.\n   * @param x New transform value along the X axis.\n   * @param y New transform value along the Y axis.\n   */\n  private _applyPreviewTransform(x: number, y: number) {\n    // Only apply the initial transform if the preview is a clone of the original element, otherwise\n    // it could be completely different and the transform might not make sense anymore.\n    const initialTransform = this._previewTemplate?.template ? undefined : this._initialTransform;\n    const transform = getTransform(x, y);\n    this._preview!.setTransform(combineTransforms(transform, initialTransform));\n  }\n\n  /**\n   * Gets the distance that the user has dragged during the current drag sequence.\n   * @param currentPosition Current position of the user's pointer.\n   */\n  private _getDragDistance(currentPosition: Point): Point {\n    const pickupPosition = this._pickupPositionOnPage;\n\n    if (pickupPosition) {\n      return {x: currentPosition.x - pickupPosition.x, y: currentPosition.y - pickupPosition.y};\n    }\n\n    return {x: 0, y: 0};\n  }\n\n  /** Cleans up any cached element dimensions that we don't need after dragging has stopped. */\n  private _cleanupCachedDimensions() {\n    this._boundaryRect = this._previewRect = undefined;\n    this._parentPositions.clear();\n  }\n\n  /**\n   * Checks whether the element is still inside its boundary after the viewport has been resized.\n   * If not, the position is adjusted so that the element fits again.\n   */\n  private _containInsideBoundaryOnResize() {\n    let {x, y} = this._passiveTransform;\n\n    if ((x === 0 && y === 0) || this.isDragging() || !this._boundaryElement) {\n      return;\n    }\n\n    // Note: don't use `_clientRectAtStart` here, because we want the latest position.\n    const elementRect = this._rootElement.getBoundingClientRect();\n    const boundaryRect = this._boundaryElement.getBoundingClientRect();\n\n    // It's possible that the element got hidden away after dragging (e.g. by switching to a\n    // different tab). Don't do anything in this case so we don't clear the user's position.\n    if (\n      (boundaryRect.width === 0 && boundaryRect.height === 0) ||\n      (elementRect.width === 0 && elementRect.height === 0)\n    ) {\n      return;\n    }\n\n    const leftOverflow = boundaryRect.left - elementRect.left;\n    const rightOverflow = elementRect.right - boundaryRect.right;\n    const topOverflow = boundaryRect.top - elementRect.top;\n    const bottomOverflow = elementRect.bottom - boundaryRect.bottom;\n\n    // If the element has become wider than the boundary, we can't\n    // do much to make it fit so we just anchor it to the left.\n    if (boundaryRect.width > elementRect.width) {\n      if (leftOverflow > 0) {\n        x += leftOverflow;\n      }\n\n      if (rightOverflow > 0) {\n        x -= rightOverflow;\n      }\n    } else {\n      x = 0;\n    }\n\n    // If the element has become taller than the boundary, we can't\n    // do much to make it fit so we just anchor it to the top.\n    if (boundaryRect.height > elementRect.height) {\n      if (topOverflow > 0) {\n        y += topOverflow;\n      }\n\n      if (bottomOverflow > 0) {\n        y -= bottomOverflow;\n      }\n    } else {\n      y = 0;\n    }\n\n    if (x !== this._passiveTransform.x || y !== this._passiveTransform.y) {\n      this.setFreeDragPosition({y, x});\n    }\n  }\n\n  /** Gets the drag start delay, based on the event type. */\n  private _getDragStartDelay(event: MouseEvent | TouchEvent): number {\n    const value = this.dragStartDelay;\n\n    if (typeof value === 'number') {\n      return value;\n    } else if (isTouchEvent(event)) {\n      return value.touch;\n    }\n\n    return value ? value.mouse : 0;\n  }\n\n  /** Updates the internal state of the draggable element when scrolling has occurred. */\n  private _updateOnScroll(event: Event) {\n    const scrollDifference = this._parentPositions.handleScroll(event);\n\n    if (scrollDifference) {\n      const target = _getEventTarget<HTMLElement | Document>(event)!;\n\n      // DOMRect dimensions are based on the scroll position of the page and its parent\n      // node so we have to update the cached boundary DOMRect if the user has scrolled.\n      if (\n        this._boundaryRect &&\n        target !== this._boundaryElement &&\n        target.contains(this._boundaryElement)\n      ) {\n        adjustDomRect(this._boundaryRect, scrollDifference.top, scrollDifference.left);\n      }\n\n      this._pickupPositionOnPage.x += scrollDifference.left;\n      this._pickupPositionOnPage.y += scrollDifference.top;\n\n      // If we're in free drag mode, we have to update the active transform, because\n      // it isn't relative to the viewport like the preview inside a drop list.\n      if (!this._dropContainer) {\n        this._activeTransform.x -= scrollDifference.left;\n        this._activeTransform.y -= scrollDifference.top;\n        this._applyRootElementTransform(this._activeTransform.x, this._activeTransform.y);\n      }\n    }\n  }\n\n  /** Gets the scroll position of the viewport. */\n  private _getViewportScrollPosition() {\n    return (\n      this._parentPositions.positions.get(this._document)?.scrollPosition ||\n      this._parentPositions.getViewportScrollPosition()\n    );\n  }\n\n  /**\n   * Lazily resolves and returns the shadow root of the element. We do this in a function, rather\n   * than saving it in property directly on init, because we want to resolve it as late as possible\n   * in order to ensure that the element has been moved into the shadow DOM. Doing it inside the\n   * constructor might be too early if the element is inside of something like `ngFor` or `ngIf`.\n   */\n  private _getShadowRoot(): ShadowRoot | null {\n    if (this._cachedShadowRoot === undefined) {\n      this._cachedShadowRoot = _getShadowRoot(this._rootElement);\n    }\n\n    return this._cachedShadowRoot;\n  }\n\n  /** Gets the element into which the drag preview should be inserted. */\n  private _getPreviewInsertionPoint(\n    initialParent: HTMLElement,\n    shadowRoot: ShadowRoot | null,\n  ): HTMLElement {\n    const previewContainer = this._previewContainer || 'global';\n\n    if (previewContainer === 'parent') {\n      return initialParent;\n    }\n\n    if (previewContainer === 'global') {\n      const documentRef = this._document;\n\n      // We can't use the body if the user is in fullscreen mode,\n      // because the preview will render under the fullscreen element.\n      // TODO(crisbeto): dedupe this with the `FullscreenOverlayContainer` eventually.\n      return (\n        shadowRoot ||\n        documentRef.fullscreenElement ||\n        (documentRef as any).webkitFullscreenElement ||\n        (documentRef as any).mozFullScreenElement ||\n        (documentRef as any).msFullscreenElement ||\n        documentRef.body\n      );\n    }\n\n    return coerceElement(previewContainer);\n  }\n\n  /** Lazily resolves and returns the dimensions of the preview. */\n  private _getPreviewRect(): DOMRect {\n    // Cache the preview element rect if we haven't cached it already or if\n    // we cached it too early before the element dimensions were computed.\n    if (!this._previewRect || (!this._previewRect.width && !this._previewRect.height)) {\n      this._previewRect = this._preview\n        ? this._preview!.getBoundingClientRect()\n        : this._initialDomRect!;\n    }\n\n    return this._previewRect;\n  }\n\n  /** Handles a native `dragstart` event. */\n  private _nativeDragStart = (event: DragEvent) => {\n    if (this._handles.length) {\n      const targetHandle = this._getTargetHandle(event);\n\n      if (targetHandle && !this._disabledHandles.has(targetHandle) && !this.disabled) {\n        event.preventDefault();\n      }\n    } else if (!this.disabled) {\n      // Usually this isn't necessary since the we prevent the default action in `pointerDown`,\n      // but some cases like dragging of links can slip through (see #24403).\n      event.preventDefault();\n    }\n  };\n\n  /** Gets a handle that is the target of an event. */\n  private _getTargetHandle(event: Event): HTMLElement | undefined {\n    return this._handles.find(handle => {\n      return event.target && (event.target === handle || handle.contains(event.target as Node));\n    });\n  }\n}\n\n/** Clamps a value between a minimum and a maximum. */\nfunction clamp(value: number, min: number, max: number) {\n  return Math.max(min, Math.min(max, value));\n}\n\n/** Determines whether an event is a touch event. */\nfunction isTouchEvent(event: MouseEvent | TouchEvent): event is TouchEvent {\n  // This function is called for every pixel that the user has dragged so we need it to be\n  // as fast as possible. Since we only bind mouse events and touch events, we can assume\n  // that if the event's name starts with `t`, it's a touch event.\n  return event.type[0] === 't';\n}\n\n/** Callback invoked for `selectstart` events inside the shadow DOM. */\nfunction shadowDomSelectStart(event: Event) {\n  event.preventDefault();\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Moves an item one index in an array to another.\n * @param array Array in which to move the item.\n * @param fromIndex Starting index of the item.\n * @param toIndex Index to which the item should be moved.\n */\nexport function moveItemInArray<T = any>(array: T[], fromIndex: number, toIndex: number): void {\n  const from = clamp(fromIndex, array.length - 1);\n  const to = clamp(toIndex, array.length - 1);\n\n  if (from === to) {\n    return;\n  }\n\n  const target = array[from];\n  const delta = to < from ? -1 : 1;\n\n  for (let i = from; i !== to; i += delta) {\n    array[i] = array[i + delta];\n  }\n\n  array[to] = target;\n}\n\n/**\n * Moves an item from one array to another.\n * @param currentArray Array from which to transfer the item.\n * @param targetArray Array into which to put the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n */\nexport function transferArrayItem<T = any>(\n  currentArray: T[],\n  targetArray: T[],\n  currentIndex: number,\n  targetIndex: number,\n): void {\n  const from = clamp(currentIndex, currentArray.length - 1);\n  const to = clamp(targetIndex, targetArray.length);\n\n  if (currentArray.length) {\n    targetArray.splice(to, 0, currentArray.splice(from, 1)[0]);\n  }\n}\n\n/**\n * Copies an item from one array to another, leaving it in its\n * original position in current array.\n * @param currentArray Array from which to copy the item.\n * @param targetArray Array into which is copy the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n *\n */\nexport function copyArrayItem<T = any>(\n  currentArray: T[],\n  targetArray: T[],\n  currentIndex: number,\n  targetIndex: number,\n): void {\n  const to = clamp(targetIndex, targetArray.length);\n\n  if (currentArray.length) {\n    targetArray.splice(to, 0, currentArray[currentIndex]);\n  }\n}\n\n/** Clamps a number between zero and a maximum. */\nfunction clamp(value: number, max: number): number {\n  return Math.max(0, Math.min(max, value));\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Direction} from '@angular/cdk/bidi';\nimport {ElementRef} from '@angular/core';\nimport {coerceElement} from '@angular/cdk/coercion';\nimport {DragDropRegistry} from '../drag-drop-registry';\nimport {moveItemInArray} from '../drag-utils';\nimport {combineTransforms} from '../dom/styling';\nimport {adjustDomRect, getMutableClientRect, isInsideClientRect} from '../dom/dom-rect';\nimport {\n  DropListSortStrategy,\n  DropListSortStrategyItem,\n  SortPredicate,\n} from './drop-list-sort-strategy';\n\n/**\n * Entry in the position cache for draggable items.\n * @docs-private\n */\ninterface CachedItemPosition<T> {\n  /** Instance of the drag item. */\n  drag: T;\n  /** Dimensions of the item. */\n  clientRect: DOMRect;\n  /** Amount by which the item has been moved since dragging started. */\n  offset: number;\n  /** Inline transform that the drag item had when dragging started. */\n  initialTransform: string;\n}\n\n/**\n * Strategy that only supports sorting along a single axis.\n * Items are reordered using CSS transforms which allows for sorting to be animated.\n * @docs-private\n */\nexport class SingleAxisSortStrategy<T extends DropListSortStrategyItem>\n  implements DropListSortStrategy<T>\n{\n  /** Function used to determine if an item can be sorted into a specific index. */\n  private _sortPredicate: SortPredicate<T>;\n\n  /** Cache of the dimensions of all the items inside the container. */\n  private _itemPositions: CachedItemPosition<T>[] = [];\n\n  /**\n   * Draggable items that are currently active inside the container. Includes the items\n   * that were there at the start of the sequence, as well as any items that have been dragged\n   * in, but haven't been dropped yet.\n   */\n  private _activeDraggables: T[];\n\n  /** Direction in which the list is oriented. */\n  orientation: 'vertical' | 'horizontal' = 'vertical';\n\n  /** Layout direction of the drop list. */\n  direction: Direction;\n\n  constructor(\n    private _element: HTMLElement | ElementRef<HTMLElement>,\n    private _dragDropRegistry: DragDropRegistry<T, unknown>,\n  ) {}\n\n  /**\n   * Keeps track of the item that was last swapped with the dragged item, as well as what direction\n   * the pointer was moving in when the swap occurred and whether the user's pointer continued to\n   * overlap with the swapped item after the swapping occurred.\n   */\n  private _previousSwap = {\n    drag: null as T | null,\n    delta: 0,\n    overlaps: false,\n  };\n\n  /**\n   * To be called when the drag sequence starts.\n   * @param items Items that are currently in the list.\n   */\n  start(items: readonly T[]) {\n    this.withItems(items);\n  }\n\n  /**\n   * To be called when an item is being sorted.\n   * @param item Item to be sorted.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param pointerDelta Direction in which the pointer is moving along each axis.\n   */\n  sort(item: T, pointerX: number, pointerY: number, pointerDelta: {x: number; y: number}) {\n    const siblings = this._itemPositions;\n    const newIndex = this._getItemIndexFromPointerPosition(item, pointerX, pointerY, pointerDelta);\n\n    if (newIndex === -1 && siblings.length > 0) {\n      return null;\n    }\n\n    const isHorizontal = this.orientation === 'horizontal';\n    const currentIndex = siblings.findIndex(currentItem => currentItem.drag === item);\n    const siblingAtNewPosition = siblings[newIndex];\n    const currentPosition = siblings[currentIndex].clientRect;\n    const newPosition = siblingAtNewPosition.clientRect;\n    const delta = currentIndex > newIndex ? 1 : -1;\n\n    // How many pixels the item's placeholder should be offset.\n    const itemOffset = this._getItemOffsetPx(currentPosition, newPosition, delta);\n\n    // How many pixels all the other items should be offset.\n    const siblingOffset = this._getSiblingOffsetPx(currentIndex, siblings, delta);\n\n    // Save the previous order of the items before moving the item to its new index.\n    // We use this to check whether an item has been moved as a result of the sorting.\n    const oldOrder = siblings.slice();\n\n    // Shuffle the array in place.\n    moveItemInArray(siblings, currentIndex, newIndex);\n\n    siblings.forEach((sibling, index) => {\n      // Don't do anything if the position hasn't changed.\n      if (oldOrder[index] === sibling) {\n        return;\n      }\n\n      const isDraggedItem = sibling.drag === item;\n      const offset = isDraggedItem ? itemOffset : siblingOffset;\n      const elementToOffset = isDraggedItem\n        ? item.getPlaceholderElement()\n        : sibling.drag.getRootElement();\n\n      // Update the offset to reflect the new position.\n      sibling.offset += offset;\n\n      // Since we're moving the items with a `transform`, we need to adjust their cached\n      // client rects to reflect their new position, as well as swap their positions in the cache.\n      // Note that we shouldn't use `getBoundingClientRect` here to update the cache, because the\n      // elements may be mid-animation which will give us a wrong result.\n      if (isHorizontal) {\n        // Round the transforms since some browsers will\n        // blur the elements, for sub-pixel transforms.\n        elementToOffset.style.transform = combineTransforms(\n          `translate3d(${Math.round(sibling.offset)}px, 0, 0)`,\n          sibling.initialTransform,\n        );\n        adjustDomRect(sibling.clientRect, 0, offset);\n      } else {\n        elementToOffset.style.transform = combineTransforms(\n          `translate3d(0, ${Math.round(sibling.offset)}px, 0)`,\n          sibling.initialTransform,\n        );\n        adjustDomRect(sibling.clientRect, offset, 0);\n      }\n    });\n\n    // Note that it's important that we do this after the client rects have been adjusted.\n    this._previousSwap.overlaps = isInsideClientRect(newPosition, pointerX, pointerY);\n    this._previousSwap.drag = siblingAtNewPosition.drag;\n    this._previousSwap.delta = isHorizontal ? pointerDelta.x : pointerDelta.y;\n\n    return {previousIndex: currentIndex, currentIndex: newIndex};\n  }\n\n  /**\n   * Called when an item is being moved into the container.\n   * @param item Item that was moved into the container.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param index Index at which the item entered. If omitted, the container will try to figure it\n   *   out automatically.\n   */\n  enter(item: T, pointerX: number, pointerY: number, index?: number): void {\n    const newIndex =\n      index == null || index < 0\n        ? // We use the coordinates of where the item entered the drop\n          // zone to figure out at which index it should be inserted.\n          this._getItemIndexFromPointerPosition(item, pointerX, pointerY)\n        : index;\n\n    const activeDraggables = this._activeDraggables;\n    const currentIndex = activeDraggables.indexOf(item);\n    const placeholder = item.getPlaceholderElement();\n    let newPositionReference: T | undefined = activeDraggables[newIndex];\n\n    // If the item at the new position is the same as the item that is being dragged,\n    // it means that we're trying to restore the item to its initial position. In this\n    // case we should use the next item from the list as the reference.\n    if (newPositionReference === item) {\n      newPositionReference = activeDraggables[newIndex + 1];\n    }\n\n    // If we didn't find a new position reference, it means that either the item didn't start off\n    // in this container, or that the item requested to be inserted at the end of the list.\n    if (\n      !newPositionReference &&\n      (newIndex == null || newIndex === -1 || newIndex < activeDraggables.length - 1) &&\n      this._shouldEnterAsFirstChild(pointerX, pointerY)\n    ) {\n      newPositionReference = activeDraggables[0];\n    }\n\n    // Since the item may be in the `activeDraggables` already (e.g. if the user dragged it\n    // into another container and back again), we have to ensure that it isn't duplicated.\n    if (currentIndex > -1) {\n      activeDraggables.splice(currentIndex, 1);\n    }\n\n    // Don't use items that are being dragged as a reference, because\n    // their element has been moved down to the bottom of the body.\n    if (newPositionReference && !this._dragDropRegistry.isDragging(newPositionReference)) {\n      const element = newPositionReference.getRootElement();\n      element.parentElement!.insertBefore(placeholder, element);\n      activeDraggables.splice(newIndex, 0, item);\n    } else {\n      coerceElement(this._element).appendChild(placeholder);\n      activeDraggables.push(item);\n    }\n\n    // The transform needs to be cleared so it doesn't throw off the measurements.\n    placeholder.style.transform = '';\n\n    // Note that usually `start` is called together with `enter` when an item goes into a new\n    // container. This will cache item positions, but we need to refresh them since the amount\n    // of items has changed.\n    this._cacheItemPositions();\n  }\n\n  /** Sets the items that are currently part of the list. */\n  withItems(items: readonly T[]): void {\n    this._activeDraggables = items.slice();\n    this._cacheItemPositions();\n  }\n\n  /** Assigns a sort predicate to the strategy. */\n  withSortPredicate(predicate: SortPredicate<T>): void {\n    this._sortPredicate = predicate;\n  }\n\n  /** Resets the strategy to its initial state before dragging was started. */\n  reset() {\n    // TODO(crisbeto): may have to wait for the animations to finish.\n    this._activeDraggables.forEach(item => {\n      const rootElement = item.getRootElement();\n\n      if (rootElement) {\n        const initialTransform = this._itemPositions.find(p => p.drag === item)?.initialTransform;\n        rootElement.style.transform = initialTransform || '';\n      }\n    });\n\n    this._itemPositions = [];\n    this._activeDraggables = [];\n    this._previousSwap.drag = null;\n    this._previousSwap.delta = 0;\n    this._previousSwap.overlaps = false;\n  }\n\n  /**\n   * Gets a snapshot of items currently in the list.\n   * Can include items that we dragged in from another list.\n   */\n  getActiveItemsSnapshot(): readonly T[] {\n    return this._activeDraggables;\n  }\n\n  /** Gets the index of a specific item. */\n  getItemIndex(item: T): number {\n    // Items are sorted always by top/left in the cache, however they flow differently in RTL.\n    // The rest of the logic still stands no matter what orientation we're in, however\n    // we need to invert the array when determining the index.\n    const items =\n      this.orientation === 'horizontal' && this.direction === 'rtl'\n        ? this._itemPositions.slice().reverse()\n        : this._itemPositions;\n\n    return items.findIndex(currentItem => currentItem.drag === item);\n  }\n\n  /** Used to notify the strategy that the scroll position has changed. */\n  updateOnScroll(topDifference: number, leftDifference: number) {\n    // Since we know the amount that the user has scrolled we can shift all of the\n    // client rectangles ourselves. This is cheaper than re-measuring everything and\n    // we can avoid inconsistent behavior where we might be measuring the element before\n    // its position has changed.\n    this._itemPositions.forEach(({clientRect}) => {\n      adjustDomRect(clientRect, topDifference, leftDifference);\n    });\n\n    // We need two loops for this, because we want all of the cached\n    // positions to be up-to-date before we re-sort the item.\n    this._itemPositions.forEach(({drag}) => {\n      if (this._dragDropRegistry.isDragging(drag)) {\n        // We need to re-sort the item manually, because the pointer move\n        // events won't be dispatched while the user is scrolling.\n        drag._sortFromLastPointerPosition();\n      }\n    });\n  }\n\n  /** Refreshes the position cache of the items and sibling containers. */\n  private _cacheItemPositions() {\n    const isHorizontal = this.orientation === 'horizontal';\n\n    this._itemPositions = this._activeDraggables\n      .map(drag => {\n        const elementToMeasure = drag.getVisibleElement();\n        return {\n          drag,\n          offset: 0,\n          initialTransform: elementToMeasure.style.transform || '',\n          clientRect: getMutableClientRect(elementToMeasure),\n        };\n      })\n      .sort((a, b) => {\n        return isHorizontal\n          ? a.clientRect.left - b.clientRect.left\n          : a.clientRect.top - b.clientRect.top;\n      });\n  }\n\n  /**\n   * Gets the offset in pixels by which the item that is being dragged should be moved.\n   * @param currentPosition Current position of the item.\n   * @param newPosition Position of the item where the current item should be moved.\n   * @param delta Direction in which the user is moving.\n   */\n  private _getItemOffsetPx(currentPosition: DOMRect, newPosition: DOMRect, delta: 1 | -1) {\n    const isHorizontal = this.orientation === 'horizontal';\n    let itemOffset = isHorizontal\n      ? newPosition.left - currentPosition.left\n      : newPosition.top - currentPosition.top;\n\n    // Account for differences in the item width/height.\n    if (delta === -1) {\n      itemOffset += isHorizontal\n        ? newPosition.width - currentPosition.width\n        : newPosition.height - currentPosition.height;\n    }\n\n    return itemOffset;\n  }\n\n  /**\n   * Gets the offset in pixels by which the items that aren't being dragged should be moved.\n   * @param currentIndex Index of the item currently being dragged.\n   * @param siblings All of the items in the list.\n   * @param delta Direction in which the user is moving.\n   */\n  private _getSiblingOffsetPx(\n    currentIndex: number,\n    siblings: CachedItemPosition<T>[],\n    delta: 1 | -1,\n  ) {\n    const isHorizontal = this.orientation === 'horizontal';\n    const currentPosition = siblings[currentIndex].clientRect;\n    const immediateSibling = siblings[currentIndex + delta * -1];\n    let siblingOffset = currentPosition[isHorizontal ? 'width' : 'height'] * delta;\n\n    if (immediateSibling) {\n      const start = isHorizontal ? 'left' : 'top';\n      const end = isHorizontal ? 'right' : 'bottom';\n\n      // Get the spacing between the start of the current item and the end of the one immediately\n      // after it in the direction in which the user is dragging, or vice versa. We add it to the\n      // offset in order to push the element to where it will be when it's inline and is influenced\n      // by the `margin` of its siblings.\n      if (delta === -1) {\n        siblingOffset -= immediateSibling.clientRect[start] - currentPosition[end];\n      } else {\n        siblingOffset += currentPosition[start] - immediateSibling.clientRect[end];\n      }\n    }\n\n    return siblingOffset;\n  }\n\n  /**\n   * Checks if pointer is entering in the first position\n   * @param pointerX Position of the user's pointer along the X axis.\n   * @param pointerY Position of the user's pointer along the Y axis.\n   */\n  private _shouldEnterAsFirstChild(pointerX: number, pointerY: number) {\n    if (!this._activeDraggables.length) {\n      return false;\n    }\n\n    const itemPositions = this._itemPositions;\n    const isHorizontal = this.orientation === 'horizontal';\n\n    // `itemPositions` are sorted by position while `activeDraggables` are sorted by child index\n    // check if container is using some sort of \"reverse\" ordering (eg: flex-direction: row-reverse)\n    const reversed = itemPositions[0].drag !== this._activeDraggables[0];\n    if (reversed) {\n      const lastItemRect = itemPositions[itemPositions.length - 1].clientRect;\n      return isHorizontal ? pointerX >= lastItemRect.right : pointerY >= lastItemRect.bottom;\n    } else {\n      const firstItemRect = itemPositions[0].clientRect;\n      return isHorizontal ? pointerX <= firstItemRect.left : pointerY <= firstItemRect.top;\n    }\n  }\n\n  /**\n   * Gets the index of an item in the drop container, based on the position of the user's pointer.\n   * @param item Item that is being sorted.\n   * @param pointerX Position of the user's pointer along the X axis.\n   * @param pointerY Position of the user's pointer along the Y axis.\n   * @param delta Direction in which the user is moving their pointer.\n   */\n  private _getItemIndexFromPointerPosition(\n    item: T,\n    pointerX: number,\n    pointerY: number,\n    delta?: {x: number; y: number},\n  ): number {\n    const isHorizontal = this.orientation === 'horizontal';\n    const index = this._itemPositions.findIndex(({drag, clientRect}) => {\n      // Skip the item itself.\n      if (drag === item) {\n        return false;\n      }\n\n      if (delta) {\n        const direction = isHorizontal ? delta.x : delta.y;\n\n        // If the user is still hovering over the same item as last time, their cursor hasn't left\n        // the item after we made the swap, and they didn't change the direction in which they're\n        // dragging, we don't consider it a direction swap.\n        if (\n          drag === this._previousSwap.drag &&\n          this._previousSwap.overlaps &&\n          direction === this._previousSwap.delta\n        ) {\n          return false;\n        }\n      }\n\n      return isHorizontal\n        ? // Round these down since most browsers report client rects with\n          // sub-pixel precision, whereas the pointer coordinates are rounded to pixels.\n          pointerX >= Math.floor(clientRect.left) && pointerX < Math.floor(clientRect.right)\n        : pointerY >= Math.floor(clientRect.top) && pointerY < Math.floor(clientRect.bottom);\n    });\n\n    return index === -1 || !this._sortPredicate(index, item) ? -1 : index;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ElementRef, NgZone} from '@angular/core';\nimport {Direction} from '@angular/cdk/bidi';\nimport {coerceElement} from '@angular/cdk/coercion';\nimport {ViewportRuler} from '@angular/cdk/scrolling';\nimport {_getShadowRoot} from '@angular/cdk/platform';\nimport {Subject, Subscription, interval, animationFrameScheduler} from 'rxjs';\nimport {takeUntil} from 'rxjs/operators';\nimport {DragDropRegistry} from './drag-drop-registry';\nimport type {DragRef, Point} from './drag-ref';\nimport {isPointerNearDomRect, isInsideClientRect} from './dom/dom-rect';\nimport {ParentPositionTracker} from './dom/parent-position-tracker';\nimport {DragCSSStyleDeclaration} from './dom/styling';\nimport {DropListSortStrategy} from './sorting/drop-list-sort-strategy';\nimport {SingleAxisSortStrategy} from './sorting/single-axis-sort-strategy';\n\n/**\n * Proximity, as a ratio to width/height, at which a\n * dragged item will affect the drop container.\n */\nconst DROP_PROXIMITY_THRESHOLD = 0.05;\n\n/**\n * Proximity, as a ratio to width/height at which to start auto-scrolling the drop list or the\n * viewport. The value comes from trying it out manually until it feels right.\n */\nconst SCROLL_PROXIMITY_THRESHOLD = 0.05;\n\n/** Vertical direction in which we can auto-scroll. */\nenum AutoScrollVerticalDirection {\n  NONE,\n  UP,\n  DOWN,\n}\n\n/** Horizontal direction in which we can auto-scroll. */\nenum AutoScrollHorizontalDirection {\n  NONE,\n  LEFT,\n  RIGHT,\n}\n\ntype RootNode = DocumentOrShadowRoot & {\n  // As of TS 4.4 the built in DOM typings don't include `elementFromPoint` on `ShadowRoot`,\n  // even though it exists (see https://developer.mozilla.org/en-US/docs/Web/API/ShadowRoot).\n  // This type is a utility to avoid having to add casts everywhere.\n  elementFromPoint(x: number, y: number): Element | null;\n};\n\n/**\n * Reference to a drop list. Used to manipulate or dispose of the container.\n */\nexport class DropListRef<T = any> {\n  /** Element that the drop list is attached to. */\n  element: HTMLElement | ElementRef<HTMLElement>;\n\n  /** Whether starting a dragging sequence from this container is disabled. */\n  disabled: boolean = false;\n\n  /** Whether sorting items within the list is disabled. */\n  sortingDisabled: boolean = false;\n\n  /** Locks the position of the draggable elements inside the container along the specified axis. */\n  lockAxis: 'x' | 'y';\n\n  /**\n   * Whether auto-scrolling the view when the user\n   * moves their pointer close to the edges is disabled.\n   */\n  autoScrollDisabled: boolean = false;\n\n  /** Number of pixels to scroll for each frame when auto-scrolling an element. */\n  autoScrollStep: number = 2;\n\n  /**\n   * Function that is used to determine whether an item\n   * is allowed to be moved into a drop container.\n   */\n  enterPredicate: (drag: DragRef, drop: DropListRef) => boolean = () => true;\n\n  /** Function that is used to determine whether an item can be sorted into a particular index. */\n  sortPredicate: (index: number, drag: DragRef, drop: DropListRef) => boolean = () => true;\n\n  /** Emits right before dragging has started. */\n  readonly beforeStarted = new Subject<void>();\n\n  /**\n   * Emits when the user has moved a new drag item into this container.\n   */\n  readonly entered = new Subject<{item: DragRef; container: DropListRef; currentIndex: number}>();\n\n  /**\n   * Emits when the user removes an item from the container\n   * by dragging it into another container.\n   */\n  readonly exited = new Subject<{item: DragRef; container: DropListRef}>();\n\n  /** Emits when the user drops an item inside the container. */\n  readonly dropped = new Subject<{\n    item: DragRef;\n    currentIndex: number;\n    previousIndex: number;\n    container: DropListRef;\n    previousContainer: DropListRef;\n    isPointerOverContainer: boolean;\n    distance: Point;\n    dropPoint: Point;\n    event: MouseEvent | TouchEvent;\n  }>();\n\n  /** Emits as the user is swapping items while actively dragging. */\n  readonly sorted = new Subject<{\n    previousIndex: number;\n    currentIndex: number;\n    container: DropListRef;\n    item: DragRef;\n  }>();\n\n  /** Emits when a dragging sequence is started in a list connected to the current one. */\n  readonly receivingStarted = new Subject<{\n    receiver: DropListRef;\n    initiator: DropListRef;\n    items: DragRef[];\n  }>();\n\n  /** Emits when a dragging sequence is stopped from a list connected to the current one. */\n  readonly receivingStopped = new Subject<{\n    receiver: DropListRef;\n    initiator: DropListRef;\n  }>();\n\n  /** Arbitrary data that can be attached to the drop list. */\n  data: T;\n\n  /** Whether an item in the list is being dragged. */\n  private _isDragging = false;\n\n  /** Keeps track of the positions of any parent scrollable elements. */\n  private _parentPositions: ParentPositionTracker;\n\n  /** Strategy being used to sort items within the list. */\n  private _sortStrategy: DropListSortStrategy<DragRef>;\n\n  /** Cached `DOMRect` of the drop list. */\n  private _domRect: DOMRect | undefined;\n\n  /** Draggable items in the container. */\n  private _draggables: readonly DragRef[] = [];\n\n  /** Drop lists that are connected to the current one. */\n  private _siblings: readonly DropListRef[] = [];\n\n  /** Connected siblings that currently have a dragged item. */\n  private _activeSiblings = new Set<DropListRef>();\n\n  /** Subscription to the window being scrolled. */\n  private _viewportScrollSubscription = Subscription.EMPTY;\n\n  /** Vertical direction in which the list is currently scrolling. */\n  private _verticalScrollDirection = AutoScrollVerticalDirection.NONE;\n\n  /** Horizontal direction in which the list is currently scrolling. */\n  private _horizontalScrollDirection = AutoScrollHorizontalDirection.NONE;\n\n  /** Node that is being auto-scrolled. */\n  private _scrollNode: HTMLElement | Window;\n\n  /** Used to signal to the current auto-scroll sequence when to stop. */\n  private readonly _stopScrollTimers = new Subject<void>();\n\n  /** Shadow root of the current element. Necessary for `elementFromPoint` to resolve correctly. */\n  private _cachedShadowRoot: RootNode | null = null;\n\n  /** Reference to the document. */\n  private _document: Document;\n\n  /** Elements that can be scrolled while the user is dragging. */\n  private _scrollableElements: HTMLElement[];\n\n  /** Initial value for the element's `scroll-snap-type` style. */\n  private _initialScrollSnap: string;\n\n  constructor(\n    element: ElementRef<HTMLElement> | HTMLElement,\n    private _dragDropRegistry: DragDropRegistry<DragRef, DropListRef>,\n    _document: any,\n    private _ngZone: NgZone,\n    private _viewportRuler: ViewportRuler,\n  ) {\n    this.element = coerceElement(element);\n    this._document = _document;\n    this.withScrollableParents([this.element]);\n    _dragDropRegistry.registerDropContainer(this);\n    this._parentPositions = new ParentPositionTracker(_document);\n    this._sortStrategy = new SingleAxisSortStrategy(this.element, _dragDropRegistry);\n    this._sortStrategy.withSortPredicate((index, item) => this.sortPredicate(index, item, this));\n  }\n\n  /** Removes the drop list functionality from the DOM element. */\n  dispose() {\n    this._stopScrolling();\n    this._stopScrollTimers.complete();\n    this._viewportScrollSubscription.unsubscribe();\n    this.beforeStarted.complete();\n    this.entered.complete();\n    this.exited.complete();\n    this.dropped.complete();\n    this.sorted.complete();\n    this.receivingStarted.complete();\n    this.receivingStopped.complete();\n    this._activeSiblings.clear();\n    this._scrollNode = null!;\n    this._parentPositions.clear();\n    this._dragDropRegistry.removeDropContainer(this);\n  }\n\n  /** Whether an item from this list is currently being dragged. */\n  isDragging() {\n    return this._isDragging;\n  }\n\n  /** Starts dragging an item. */\n  start(): void {\n    this._draggingStarted();\n    this._notifyReceivingSiblings();\n  }\n\n  /**\n   * Attempts to move an item into the container.\n   * @param item Item that was moved into the container.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param index Index at which the item entered. If omitted, the container will try to figure it\n   *   out automatically.\n   */\n  enter(item: DragRef, pointerX: number, pointerY: number, index?: number): void {\n    this._draggingStarted();\n\n    // If sorting is disabled, we want the item to return to its starting\n    // position if the user is returning it to its initial container.\n    if (index == null && this.sortingDisabled) {\n      index = this._draggables.indexOf(item);\n    }\n\n    this._sortStrategy.enter(item, pointerX, pointerY, index);\n\n    // Note that this usually happens inside `_draggingStarted` as well, but the dimensions\n    // can change when the sort strategy moves the item around inside `enter`.\n    this._cacheParentPositions();\n\n    // Notify siblings at the end so that the item has been inserted into the `activeDraggables`.\n    this._notifyReceivingSiblings();\n    this.entered.next({item, container: this, currentIndex: this.getItemIndex(item)});\n  }\n\n  /**\n   * Removes an item from the container after it was dragged into another container by the user.\n   * @param item Item that was dragged out.\n   */\n  exit(item: DragRef): void {\n    this._reset();\n    this.exited.next({item, container: this});\n  }\n\n  /**\n   * Drops an item into this container.\n   * @param item Item being dropped into the container.\n   * @param currentIndex Index at which the item should be inserted.\n   * @param previousIndex Index of the item when dragging started.\n   * @param previousContainer Container from which the item got dragged in.\n   * @param isPointerOverContainer Whether the user's pointer was over the\n   *    container when the item was dropped.\n   * @param distance Distance the user has dragged since the start of the dragging sequence.\n   * @param event Event that triggered the dropping sequence.\n   *\n   * @breaking-change 15.0.0 `previousIndex` and `event` parameters to become required.\n   */\n  drop(\n    item: DragRef,\n    currentIndex: number,\n    previousIndex: number,\n    previousContainer: DropListRef,\n    isPointerOverContainer: boolean,\n    distance: Point,\n    dropPoint: Point,\n    event: MouseEvent | TouchEvent = {} as any,\n  ): void {\n    this._reset();\n    this.dropped.next({\n      item,\n      currentIndex,\n      previousIndex,\n      container: this,\n      previousContainer,\n      isPointerOverContainer,\n      distance,\n      dropPoint,\n      event,\n    });\n  }\n\n  /**\n   * Sets the draggable items that are a part of this list.\n   * @param items Items that are a part of this list.\n   */\n  withItems(items: DragRef[]): this {\n    const previousItems = this._draggables;\n    this._draggables = items;\n    items.forEach(item => item._withDropContainer(this));\n\n    if (this.isDragging()) {\n      const draggedItems = previousItems.filter(item => item.isDragging());\n\n      // If all of the items being dragged were removed\n      // from the list, abort the current drag sequence.\n      if (draggedItems.every(item => items.indexOf(item) === -1)) {\n        this._reset();\n      } else {\n        this._sortStrategy.withItems(this._draggables);\n      }\n    }\n\n    return this;\n  }\n\n  /** Sets the layout direction of the drop list. */\n  withDirection(direction: Direction): this {\n    this._sortStrategy.direction = direction;\n    return this;\n  }\n\n  /**\n   * Sets the containers that are connected to this one. When two or more containers are\n   * connected, the user will be allowed to transfer items between them.\n   * @param connectedTo Other containers that the current containers should be connected to.\n   */\n  connectedTo(connectedTo: DropListRef[]): this {\n    this._siblings = connectedTo.slice();\n    return this;\n  }\n\n  /**\n   * Sets the orientation of the container.\n   * @param orientation New orientation for the container.\n   */\n  withOrientation(orientation: 'vertical' | 'horizontal'): this {\n    // TODO(crisbeto): eventually we should be constructing the new sort strategy here based on\n    // the new orientation. For now we can assume that it'll always be `SingleAxisSortStrategy`.\n    (this._sortStrategy as SingleAxisSortStrategy<DragRef>).orientation = orientation;\n    return this;\n  }\n\n  /**\n   * Sets which parent elements are can be scrolled while the user is dragging.\n   * @param elements Elements that can be scrolled.\n   */\n  withScrollableParents(elements: HTMLElement[]): this {\n    const element = coerceElement(this.element);\n\n    // We always allow the current element to be scrollable\n    // so we need to ensure that it's in the array.\n    this._scrollableElements =\n      elements.indexOf(element) === -1 ? [element, ...elements] : elements.slice();\n    return this;\n  }\n\n  /** Gets the scrollable parents that are registered with this drop container. */\n  getScrollableParents(): readonly HTMLElement[] {\n    return this._scrollableElements;\n  }\n\n  /**\n   * Figures out the index of an item in the container.\n   * @param item Item whose index should be determined.\n   */\n  getItemIndex(item: DragRef): number {\n    return this._isDragging\n      ? this._sortStrategy.getItemIndex(item)\n      : this._draggables.indexOf(item);\n  }\n\n  /**\n   * Whether the list is able to receive the item that\n   * is currently being dragged inside a connected drop list.\n   */\n  isReceiving(): boolean {\n    return this._activeSiblings.size > 0;\n  }\n\n  /**\n   * Sorts an item inside the container based on its position.\n   * @param item Item to be sorted.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param pointerDelta Direction in which the pointer is moving along each axis.\n   */\n  _sortItem(\n    item: DragRef,\n    pointerX: number,\n    pointerY: number,\n    pointerDelta: {x: number; y: number},\n  ): void {\n    // Don't sort the item if sorting is disabled or it's out of range.\n    if (\n      this.sortingDisabled ||\n      !this._domRect ||\n      !isPointerNearDomRect(this._domRect, DROP_PROXIMITY_THRESHOLD, pointerX, pointerY)\n    ) {\n      return;\n    }\n\n    const result = this._sortStrategy.sort(item, pointerX, pointerY, pointerDelta);\n\n    if (result) {\n      this.sorted.next({\n        previousIndex: result.previousIndex,\n        currentIndex: result.currentIndex,\n        container: this,\n        item,\n      });\n    }\n  }\n\n  /**\n   * Checks whether the user's pointer is close to the edges of either the\n   * viewport or the drop list and starts the auto-scroll sequence.\n   * @param pointerX User's pointer position along the x axis.\n   * @param pointerY User's pointer position along the y axis.\n   */\n  _startScrollingIfNecessary(pointerX: number, pointerY: number) {\n    if (this.autoScrollDisabled) {\n      return;\n    }\n\n    let scrollNode: HTMLElement | Window | undefined;\n    let verticalScrollDirection = AutoScrollVerticalDirection.NONE;\n    let horizontalScrollDirection = AutoScrollHorizontalDirection.NONE;\n\n    // Check whether we should start scrolling any of the parent containers.\n    this._parentPositions.positions.forEach((position, element) => {\n      // We have special handling for the `document` below. Also this would be\n      // nicer with a  for...of loop, but it requires changing a compiler flag.\n      if (element === this._document || !position.clientRect || scrollNode) {\n        return;\n      }\n\n      if (isPointerNearDomRect(position.clientRect, DROP_PROXIMITY_THRESHOLD, pointerX, pointerY)) {\n        [verticalScrollDirection, horizontalScrollDirection] = getElementScrollDirections(\n          element as HTMLElement,\n          position.clientRect,\n          this._sortStrategy.direction,\n          pointerX,\n          pointerY,\n        );\n\n        if (verticalScrollDirection || horizontalScrollDirection) {\n          scrollNode = element as HTMLElement;\n        }\n      }\n    });\n\n    // Otherwise check if we can start scrolling the viewport.\n    if (!verticalScrollDirection && !horizontalScrollDirection) {\n      const {width, height} = this._viewportRuler.getViewportSize();\n      const domRect = {\n        width,\n        height,\n        top: 0,\n        right: width,\n        bottom: height,\n        left: 0,\n      } as DOMRect;\n      verticalScrollDirection = getVerticalScrollDirection(domRect, pointerY);\n      horizontalScrollDirection = getHorizontalScrollDirection(domRect, pointerX);\n      scrollNode = window;\n    }\n\n    if (\n      scrollNode &&\n      (verticalScrollDirection !== this._verticalScrollDirection ||\n        horizontalScrollDirection !== this._horizontalScrollDirection ||\n        scrollNode !== this._scrollNode)\n    ) {\n      this._verticalScrollDirection = verticalScrollDirection;\n      this._horizontalScrollDirection = horizontalScrollDirection;\n      this._scrollNode = scrollNode;\n\n      if ((verticalScrollDirection || horizontalScrollDirection) && scrollNode) {\n        this._ngZone.runOutsideAngular(this._startScrollInterval);\n      } else {\n        this._stopScrolling();\n      }\n    }\n  }\n\n  /** Stops any currently-running auto-scroll sequences. */\n  _stopScrolling() {\n    this._stopScrollTimers.next();\n  }\n\n  /** Starts the dragging sequence within the list. */\n  private _draggingStarted() {\n    const styles = coerceElement(this.element).style as DragCSSStyleDeclaration;\n    this.beforeStarted.next();\n    this._isDragging = true;\n\n    // We need to disable scroll snapping while the user is dragging, because it breaks automatic\n    // scrolling. The browser seems to round the value based on the snapping points which means\n    // that we can't increment/decrement the scroll position.\n    this._initialScrollSnap = styles.msScrollSnapType || styles.scrollSnapType || '';\n    styles.scrollSnapType = styles.msScrollSnapType = 'none';\n    this._sortStrategy.start(this._draggables);\n    this._cacheParentPositions();\n    this._viewportScrollSubscription.unsubscribe();\n    this._listenToScrollEvents();\n  }\n\n  /** Caches the positions of the configured scrollable parents. */\n  private _cacheParentPositions() {\n    const element = coerceElement(this.element);\n    this._parentPositions.cache(this._scrollableElements);\n\n    // The list element is always in the `scrollableElements`\n    // so we can take advantage of the cached `DOMRect`.\n    this._domRect = this._parentPositions.positions.get(element)!.clientRect!;\n  }\n\n  /** Resets the container to its initial state. */\n  private _reset() {\n    this._isDragging = false;\n\n    const styles = coerceElement(this.element).style as DragCSSStyleDeclaration;\n    styles.scrollSnapType = styles.msScrollSnapType = this._initialScrollSnap;\n\n    this._siblings.forEach(sibling => sibling._stopReceiving(this));\n    this._sortStrategy.reset();\n    this._stopScrolling();\n    this._viewportScrollSubscription.unsubscribe();\n    this._parentPositions.clear();\n  }\n\n  /** Starts the interval that'll auto-scroll the element. */\n  private _startScrollInterval = () => {\n    this._stopScrolling();\n\n    interval(0, animationFrameScheduler)\n      .pipe(takeUntil(this._stopScrollTimers))\n      .subscribe(() => {\n        const node = this._scrollNode;\n        const scrollStep = this.autoScrollStep;\n\n        if (this._verticalScrollDirection === AutoScrollVerticalDirection.UP) {\n          node.scrollBy(0, -scrollStep);\n        } else if (this._verticalScrollDirection === AutoScrollVerticalDirection.DOWN) {\n          node.scrollBy(0, scrollStep);\n        }\n\n        if (this._horizontalScrollDirection === AutoScrollHorizontalDirection.LEFT) {\n          node.scrollBy(-scrollStep, 0);\n        } else if (this._horizontalScrollDirection === AutoScrollHorizontalDirection.RIGHT) {\n          node.scrollBy(scrollStep, 0);\n        }\n      });\n  };\n\n  /**\n   * Checks whether the user's pointer is positioned over the container.\n   * @param x Pointer position along the X axis.\n   * @param y Pointer position along the Y axis.\n   */\n  _isOverContainer(x: number, y: number): boolean {\n    return this._domRect != null && isInsideClientRect(this._domRect, x, y);\n  }\n\n  /**\n   * Figures out whether an item should be moved into a sibling\n   * drop container, based on its current position.\n   * @param item Drag item that is being moved.\n   * @param x Position of the item along the X axis.\n   * @param y Position of the item along the Y axis.\n   */\n  _getSiblingContainerFromPosition(item: DragRef, x: number, y: number): DropListRef | undefined {\n    return this._siblings.find(sibling => sibling._canReceive(item, x, y));\n  }\n\n  /**\n   * Checks whether the drop list can receive the passed-in item.\n   * @param item Item that is being dragged into the list.\n   * @param x Position of the item along the X axis.\n   * @param y Position of the item along the Y axis.\n   */\n  _canReceive(item: DragRef, x: number, y: number): boolean {\n    if (\n      !this._domRect ||\n      !isInsideClientRect(this._domRect, x, y) ||\n      !this.enterPredicate(item, this)\n    ) {\n      return false;\n    }\n\n    const elementFromPoint = this._getShadowRoot().elementFromPoint(x, y) as HTMLElement | null;\n\n    // If there's no element at the pointer position, then\n    // the client rect is probably scrolled out of the view.\n    if (!elementFromPoint) {\n      return false;\n    }\n\n    const nativeElement = coerceElement(this.element);\n\n    // The `DOMRect`, that we're using to find the container over which the user is\n    // hovering, doesn't give us any information on whether the element has been scrolled\n    // out of the view or whether it's overlapping with other containers. This means that\n    // we could end up transferring the item into a container that's invisible or is positioned\n    // below another one. We use the result from `elementFromPoint` to get the top-most element\n    // at the pointer position and to find whether it's one of the intersecting drop containers.\n    return elementFromPoint === nativeElement || nativeElement.contains(elementFromPoint);\n  }\n\n  /**\n   * Called by one of the connected drop lists when a dragging sequence has started.\n   * @param sibling Sibling in which dragging has started.\n   */\n  _startReceiving(sibling: DropListRef, items: DragRef[]) {\n    const activeSiblings = this._activeSiblings;\n\n    if (\n      !activeSiblings.has(sibling) &&\n      items.every(item => {\n        // Note that we have to add an exception to the `enterPredicate` for items that started off\n        // in this drop list. The drag ref has logic that allows an item to return to its initial\n        // container, if it has left the initial container and none of the connected containers\n        // allow it to enter. See `DragRef._updateActiveDropContainer` for more context.\n        return this.enterPredicate(item, this) || this._draggables.indexOf(item) > -1;\n      })\n    ) {\n      activeSiblings.add(sibling);\n      this._cacheParentPositions();\n      this._listenToScrollEvents();\n      this.receivingStarted.next({\n        initiator: sibling,\n        receiver: this,\n        items,\n      });\n    }\n  }\n\n  /**\n   * Called by a connected drop list when dragging has stopped.\n   * @param sibling Sibling whose dragging has stopped.\n   */\n  _stopReceiving(sibling: DropListRef) {\n    this._activeSiblings.delete(sibling);\n    this._viewportScrollSubscription.unsubscribe();\n    this.receivingStopped.next({initiator: sibling, receiver: this});\n  }\n\n  /**\n   * Starts listening to scroll events on the viewport.\n   * Used for updating the internal state of the list.\n   */\n  private _listenToScrollEvents() {\n    this._viewportScrollSubscription = this._dragDropRegistry\n      .scrolled(this._getShadowRoot())\n      .subscribe(event => {\n        if (this.isDragging()) {\n          const scrollDifference = this._parentPositions.handleScroll(event);\n\n          if (scrollDifference) {\n            this._sortStrategy.updateOnScroll(scrollDifference.top, scrollDifference.left);\n          }\n        } else if (this.isReceiving()) {\n          this._cacheParentPositions();\n        }\n      });\n  }\n\n  /**\n   * Lazily resolves and returns the shadow root of the element. We do this in a function, rather\n   * than saving it in property directly on init, because we want to resolve it as late as possible\n   * in order to ensure that the element has been moved into the shadow DOM. Doing it inside the\n   * constructor might be too early if the element is inside of something like `ngFor` or `ngIf`.\n   */\n  private _getShadowRoot(): RootNode {\n    if (!this._cachedShadowRoot) {\n      const shadowRoot = _getShadowRoot(coerceElement(this.element));\n      this._cachedShadowRoot = (shadowRoot || this._document) as RootNode;\n    }\n\n    return this._cachedShadowRoot;\n  }\n\n  /** Notifies any siblings that may potentially receive the item. */\n  private _notifyReceivingSiblings() {\n    const draggedItems = this._sortStrategy\n      .getActiveItemsSnapshot()\n      .filter(item => item.isDragging());\n    this._siblings.forEach(sibling => sibling._startReceiving(this, draggedItems));\n  }\n}\n\n/**\n * Gets whether the vertical auto-scroll direction of a node.\n * @param clientRect Dimensions of the node.\n * @param pointerY Position of the user's pointer along the y axis.\n */\nfunction getVerticalScrollDirection(clientRect: DOMRect, pointerY: number) {\n  const {top, bottom, height} = clientRect;\n  const yThreshold = height * SCROLL_PROXIMITY_THRESHOLD;\n\n  if (pointerY >= top - yThreshold && pointerY <= top + yThreshold) {\n    return AutoScrollVerticalDirection.UP;\n  } else if (pointerY >= bottom - yThreshold && pointerY <= bottom + yThreshold) {\n    return AutoScrollVerticalDirection.DOWN;\n  }\n\n  return AutoScrollVerticalDirection.NONE;\n}\n\n/**\n * Gets whether the horizontal auto-scroll direction of a node.\n * @param clientRect Dimensions of the node.\n * @param pointerX Position of the user's pointer along the x axis.\n */\nfunction getHorizontalScrollDirection(clientRect: DOMRect, pointerX: number) {\n  const {left, right, width} = clientRect;\n  const xThreshold = width * SCROLL_PROXIMITY_THRESHOLD;\n\n  if (pointerX >= left - xThreshold && pointerX <= left + xThreshold) {\n    return AutoScrollHorizontalDirection.LEFT;\n  } else if (pointerX >= right - xThreshold && pointerX <= right + xThreshold) {\n    return AutoScrollHorizontalDirection.RIGHT;\n  }\n\n  return AutoScrollHorizontalDirection.NONE;\n}\n\n/**\n * Gets the directions in which an element node should be scrolled,\n * assuming that the user's pointer is already within it scrollable region.\n * @param element Element for which we should calculate the scroll direction.\n * @param clientRect Bounding client rectangle of the element.\n * @param direction Layout direction of the drop list.\n * @param pointerX Position of the user's pointer along the x axis.\n * @param pointerY Position of the user's pointer along the y axis.\n */\nfunction getElementScrollDirections(\n  element: HTMLElement,\n  clientRect: DOMRect,\n  direction: Direction,\n  pointerX: number,\n  pointerY: number,\n): [AutoScrollVerticalDirection, AutoScrollHorizontalDirection] {\n  const computedVertical = getVerticalScrollDirection(clientRect, pointerY);\n  const computedHorizontal = getHorizontalScrollDirection(clientRect, pointerX);\n  let verticalScrollDirection = AutoScrollVerticalDirection.NONE;\n  let horizontalScrollDirection = AutoScrollHorizontalDirection.NONE;\n\n  // Note that we here we do some extra checks for whether the element is actually scrollable in\n  // a certain direction and we only assign the scroll direction if it is. We do this so that we\n  // can allow other elements to be scrolled, if the current element can't be scrolled anymore.\n  // This allows us to handle cases where the scroll regions of two scrollable elements overlap.\n  if (computedVertical) {\n    const scrollTop = element.scrollTop;\n\n    if (computedVertical === AutoScrollVerticalDirection.UP) {\n      if (scrollTop > 0) {\n        verticalScrollDirection = AutoScrollVerticalDirection.UP;\n      }\n    } else if (element.scrollHeight - scrollTop > element.clientHeight) {\n      verticalScrollDirection = AutoScrollVerticalDirection.DOWN;\n    }\n  }\n\n  if (computedHorizontal) {\n    const scrollLeft = element.scrollLeft;\n\n    if (direction === 'rtl') {\n      if (computedHorizontal === AutoScrollHorizontalDirection.RIGHT) {\n        // In RTL `scrollLeft` will be negative when scrolled.\n        if (scrollLeft < 0) {\n          horizontalScrollDirection = AutoScrollHorizontalDirection.RIGHT;\n        }\n      } else if (element.scrollWidth + scrollLeft > element.clientWidth) {\n        horizontalScrollDirection = AutoScrollHorizontalDirection.LEFT;\n      }\n    } else {\n      if (computedHorizontal === AutoScrollHorizontalDirection.LEFT) {\n        if (scrollLeft > 0) {\n          horizontalScrollDirection = AutoScrollHorizontalDirection.LEFT;\n        }\n      } else if (element.scrollWidth - scrollLeft > element.clientWidth) {\n        horizontalScrollDirection = AutoScrollHorizontalDirection.RIGHT;\n      }\n    }\n  }\n\n  return [verticalScrollDirection, horizontalScrollDirection];\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Injectable,\n  Ng<PERSON>one,\n  OnDestroy,\n  Inject,\n  inject,\n  ApplicationRef,\n  EnvironmentInjector,\n  Component,\n  ViewEncapsulation,\n  ChangeDetectionStrategy,\n  createComponent,\n} from '@angular/core';\nimport {DOCUMENT} from '@angular/common';\nimport {normalizePassiveListenerOptions} from '@angular/cdk/platform';\nimport {merge, Observable, Observer, Subject} from 'rxjs';\n\n/** Event options that can be used to bind an active, capturing event. */\nconst activeCapturingEventOptions = normalizePassiveListenerOptions({\n  passive: false,\n  capture: true,\n});\n\n/** Keeps track of the apps currently containing drag items. */\nconst activeApps = new Set<ApplicationRef>();\n\n/**\n * Component used to load the drag&drop reset styles.\n * @docs-private\n */\n@Component({\n  standalone: true,\n  styleUrl: 'resets.css',\n  encapsulation: ViewEncapsulation.None,\n  template: '',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  host: {'cdk-drag-resets-container': ''},\n})\nexport class _ResetsLoader {}\n\n/**\n * Service that keeps track of all the drag item and drop container\n * instances, and manages global event listeners on the `document`.\n * @docs-private\n */\n// Note: this class is generic, rather than referencing CdkDrag and CdkDropList directly, in order\n// to avoid circular imports. If we were to reference them here, importing the registry into the\n// classes that are registering themselves will introduce a circular import.\n@Injectable({providedIn: 'root'})\nexport class DragDropRegistry<I extends {isDragging(): boolean}, C> implements OnDestroy {\n  private _document: Document;\n  private _appRef = inject(ApplicationRef);\n  private _environmentInjector = inject(EnvironmentInjector);\n\n  /** Registered drop container instances. */\n  private _dropInstances = new Set<C>();\n\n  /** Registered drag item instances. */\n  private _dragInstances = new Set<I>();\n\n  /** Drag item instances that are currently being dragged. */\n  private _activeDragInstances: I[] = [];\n\n  /** Keeps track of the event listeners that we've bound to the `document`. */\n  private _globalListeners = new Map<\n    string,\n    {\n      handler: (event: Event) => void;\n      options?: AddEventListenerOptions | boolean;\n    }\n  >();\n\n  /**\n   * Predicate function to check if an item is being dragged.  Moved out into a property,\n   * because it'll be called a lot and we don't want to create a new function every time.\n   */\n  private _draggingPredicate = (item: I) => item.isDragging();\n\n  /**\n   * Emits the `touchmove` or `mousemove` events that are dispatched\n   * while the user is dragging a drag item instance.\n   */\n  readonly pointerMove: Subject<TouchEvent | MouseEvent> = new Subject<TouchEvent | MouseEvent>();\n\n  /**\n   * Emits the `touchend` or `mouseup` events that are dispatched\n   * while the user is dragging a drag item instance.\n   */\n  readonly pointerUp: Subject<TouchEvent | MouseEvent> = new Subject<TouchEvent | MouseEvent>();\n\n  /**\n   * Emits when the viewport has been scrolled while the user is dragging an item.\n   * @deprecated To be turned into a private member. Use the `scrolled` method instead.\n   * @breaking-change 13.0.0\n   */\n  readonly scroll: Subject<Event> = new Subject<Event>();\n\n  constructor(\n    private _ngZone: NgZone,\n    @Inject(DOCUMENT) _document: any,\n  ) {\n    this._document = _document;\n  }\n\n  /** Adds a drop container to the registry. */\n  registerDropContainer(drop: C) {\n    if (!this._dropInstances.has(drop)) {\n      this._dropInstances.add(drop);\n    }\n  }\n\n  /** Adds a drag item instance to the registry. */\n  registerDragItem(drag: I) {\n    this._dragInstances.add(drag);\n\n    // The `touchmove` event gets bound once, ahead of time, because WebKit\n    // won't preventDefault on a dynamically-added `touchmove` listener.\n    // See https://bugs.webkit.org/show_bug.cgi?id=184250.\n    if (this._dragInstances.size === 1) {\n      this._ngZone.runOutsideAngular(() => {\n        // The event handler has to be explicitly active,\n        // because newer browsers make it passive by default.\n        this._document.addEventListener(\n          'touchmove',\n          this._persistentTouchmoveListener,\n          activeCapturingEventOptions,\n        );\n      });\n    }\n  }\n\n  /** Removes a drop container from the registry. */\n  removeDropContainer(drop: C) {\n    this._dropInstances.delete(drop);\n  }\n\n  /** Removes a drag item instance from the registry. */\n  removeDragItem(drag: I) {\n    this._dragInstances.delete(drag);\n    this.stopDragging(drag);\n\n    if (this._dragInstances.size === 0) {\n      this._document.removeEventListener(\n        'touchmove',\n        this._persistentTouchmoveListener,\n        activeCapturingEventOptions,\n      );\n    }\n  }\n\n  /**\n   * Starts the dragging sequence for a drag instance.\n   * @param drag Drag instance which is being dragged.\n   * @param event Event that initiated the dragging.\n   */\n  startDragging(drag: I, event: TouchEvent | MouseEvent) {\n    // Do not process the same drag twice to avoid memory leaks and redundant listeners\n    if (this._activeDragInstances.indexOf(drag) > -1) {\n      return;\n    }\n\n    this._loadResets();\n    this._activeDragInstances.push(drag);\n\n    if (this._activeDragInstances.length === 1) {\n      const isTouchEvent = event.type.startsWith('touch');\n\n      // We explicitly bind __active__ listeners here, because newer browsers will default to\n      // passive ones for `mousemove` and `touchmove`. The events need to be active, because we\n      // use `preventDefault` to prevent the page from scrolling while the user is dragging.\n      this._globalListeners\n        .set(isTouchEvent ? 'touchend' : 'mouseup', {\n          handler: (e: Event) => this.pointerUp.next(e as TouchEvent | MouseEvent),\n          options: true,\n        })\n        .set('scroll', {\n          handler: (e: Event) => this.scroll.next(e),\n          // Use capturing so that we pick up scroll changes in any scrollable nodes that aren't\n          // the document. See https://github.com/angular/components/issues/17144.\n          options: true,\n        })\n        // Preventing the default action on `mousemove` isn't enough to disable text selection\n        // on Safari so we need to prevent the selection event as well. Alternatively this can\n        // be done by setting `user-select: none` on the `body`, however it has causes a style\n        // recalculation which can be expensive on pages with a lot of elements.\n        .set('selectstart', {\n          handler: this._preventDefaultWhileDragging,\n          options: activeCapturingEventOptions,\n        });\n\n      // We don't have to bind a move event for touch drag sequences, because\n      // we already have a persistent global one bound from `registerDragItem`.\n      if (!isTouchEvent) {\n        this._globalListeners.set('mousemove', {\n          handler: (e: Event) => this.pointerMove.next(e as MouseEvent),\n          options: activeCapturingEventOptions,\n        });\n      }\n\n      this._ngZone.runOutsideAngular(() => {\n        this._globalListeners.forEach((config, name) => {\n          this._document.addEventListener(name, config.handler, config.options);\n        });\n      });\n    }\n  }\n\n  /** Stops dragging a drag item instance. */\n  stopDragging(drag: I) {\n    const index = this._activeDragInstances.indexOf(drag);\n\n    if (index > -1) {\n      this._activeDragInstances.splice(index, 1);\n\n      if (this._activeDragInstances.length === 0) {\n        this._clearGlobalListeners();\n      }\n    }\n  }\n\n  /** Gets whether a drag item instance is currently being dragged. */\n  isDragging(drag: I) {\n    return this._activeDragInstances.indexOf(drag) > -1;\n  }\n\n  /**\n   * Gets a stream that will emit when any element on the page is scrolled while an item is being\n   * dragged.\n   * @param shadowRoot Optional shadow root that the current dragging sequence started from.\n   *   Top-level listeners won't pick up events coming from the shadow DOM so this parameter can\n   *   be used to include an additional top-level listener at the shadow root level.\n   */\n  scrolled(shadowRoot?: DocumentOrShadowRoot | null): Observable<Event> {\n    const streams: Observable<Event>[] = [this.scroll];\n\n    if (shadowRoot && shadowRoot !== this._document) {\n      // Note that this is basically the same as `fromEvent` from rxjs, but we do it ourselves,\n      // because we want to guarantee that the event is bound outside of the `NgZone`. With\n      // `fromEvent` it'll only happen if the subscription is outside the `NgZone`.\n      streams.push(\n        new Observable((observer: Observer<Event>) => {\n          return this._ngZone.runOutsideAngular(() => {\n            const eventOptions = true;\n            const callback = (event: Event) => {\n              if (this._activeDragInstances.length) {\n                observer.next(event);\n              }\n            };\n\n            (shadowRoot as ShadowRoot).addEventListener('scroll', callback, eventOptions);\n\n            return () => {\n              (shadowRoot as ShadowRoot).removeEventListener('scroll', callback, eventOptions);\n            };\n          });\n        }),\n      );\n    }\n\n    return merge(...streams);\n  }\n\n  ngOnDestroy() {\n    this._dragInstances.forEach(instance => this.removeDragItem(instance));\n    this._dropInstances.forEach(instance => this.removeDropContainer(instance));\n    this._clearGlobalListeners();\n    this.pointerMove.complete();\n    this.pointerUp.complete();\n  }\n\n  /**\n   * Event listener that will prevent the default browser action while the user is dragging.\n   * @param event Event whose default action should be prevented.\n   */\n  private _preventDefaultWhileDragging = (event: Event) => {\n    if (this._activeDragInstances.length > 0) {\n      event.preventDefault();\n    }\n  };\n\n  /** Event listener for `touchmove` that is bound even if no dragging is happening. */\n  private _persistentTouchmoveListener = (event: TouchEvent) => {\n    if (this._activeDragInstances.length > 0) {\n      // Note that we only want to prevent the default action after dragging has actually started.\n      // Usually this is the same time at which the item is added to the `_activeDragInstances`,\n      // but it could be pushed back if the user has set up a drag delay or threshold.\n      if (this._activeDragInstances.some(this._draggingPredicate)) {\n        event.preventDefault();\n      }\n\n      this.pointerMove.next(event);\n    }\n  };\n\n  /** Clears out the global event listeners from the `document`. */\n  private _clearGlobalListeners() {\n    this._globalListeners.forEach((config, name) => {\n      this._document.removeEventListener(name, config.handler, config.options);\n    });\n\n    this._globalListeners.clear();\n  }\n\n  // TODO(crisbeto): abstract this away into something reusable.\n  /** Loads the CSS resets needed for the module to work correctly. */\n  private _loadResets() {\n    if (!activeApps.has(this._appRef)) {\n      activeApps.add(this._appRef);\n\n      const componentRef = createComponent(_ResetsLoader, {\n        environmentInjector: this._environmentInjector,\n      });\n\n      this._appRef.onDestroy(() => {\n        activeApps.delete(this._appRef);\n        if (activeApps.size === 0) {\n          componentRef.destroy();\n        }\n      });\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injectable, Inject, NgZone, ElementRef} from '@angular/core';\nimport {DOCUMENT} from '@angular/common';\nimport {ViewportRuler} from '@angular/cdk/scrolling';\nimport {DragRef, DragRefConfig} from './drag-ref';\nimport {DropListRef} from './drop-list-ref';\nimport {DragDropRegistry} from './drag-drop-registry';\n\n/** Default configuration to be used when creating a `DragRef`. */\nconst DEFAULT_CONFIG = {\n  dragStartThreshold: 5,\n  pointerDirectionChangeThreshold: 5,\n};\n\n/**\n * Service that allows for drag-and-drop functionality to be attached to DOM elements.\n */\n@Injectable({providedIn: 'root'})\nexport class DragDrop {\n  constructor(\n    @Inject(DOCUMENT) private _document: any,\n    private _ngZone: NgZone,\n    private _viewportRuler: ViewportRuler,\n    private _dragDropRegistry: DragDropRegistry<DragRef, DropListRef>,\n  ) {}\n\n  /**\n   * Turns an element into a draggable item.\n   * @param element Element to which to attach the dragging functionality.\n   * @param config Object used to configure the dragging behavior.\n   */\n  createDrag<T = any>(\n    element: ElementRef<HTMLElement> | HTMLElement,\n    config: DragRefConfig = DEFAULT_CONFIG,\n  ): DragRef<T> {\n    return new DragRef<T>(\n      element,\n      config,\n      this._document,\n      this._ngZone,\n      this._viewportRuler,\n      this._dragDropRegistry,\n    );\n  }\n\n  /**\n   * Turns an element into a drop list.\n   * @param element Element to which to attach the drop list functionality.\n   */\n  createDropList<T = any>(element: ElementRef<HTMLElement> | HTMLElement): DropListRef<T> {\n    return new DropListRef<T>(\n      element,\n      this._dragDropRegistry,\n      this._document,\n      this._ngZone,\n      this._viewportRuler,\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {InjectionToken} from '@angular/core';\nimport type {CdkDrag} from './directives/drag';\n\n/**\n * Injection token that can be used for a `CdkDrag` to provide itself as a parent to the\n * drag-specific child directive (`CdkDragHandle`, `CdkDragPreview` etc.). Used primarily\n * to avoid circular imports.\n * @docs-private\n */\nexport const CDK_DRAG_PARENT = new InjectionToken<CdkDrag>('CDK_DRAG_PARENT');\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Asserts that a particular node is an element.\n * @param node Node to be checked.\n * @param name Name to attach to the error message.\n */\nexport function assertElementNode(node: Node, name: string): asserts node is HTMLElement {\n  if (node.nodeType !== 1) {\n    throw Error(\n      `${name} must be attached to an element node. ` + `Currently attached to \"${node.nodeName}\".`,\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Directive,\n  ElementRef,\n  Inject,\n  InjectionToken,\n  Input,\n  OnDestroy,\n  Optional,\n  SkipSelf,\n  booleanAttribute,\n} from '@angular/core';\nimport {Subject} from 'rxjs';\nimport type {CdkDrag} from './drag';\nimport {CDK_DRAG_PARENT} from '../drag-parent';\nimport {assertElementNode} from './assertions';\n\n/**\n * Injection token that can be used to reference instances of `CdkDragHandle`. It serves as\n * alternative token to the actual `CdkDragHandle` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nexport const CDK_DRAG_HANDLE = new InjectionToken<CdkDragHandle>('CdkDragHandle');\n\n/** Handle that can be used to drag a CdkDrag instance. */\n@Directive({\n  selector: '[cdkDragHandle]',\n  standalone: true,\n  host: {\n    'class': 'cdk-drag-handle',\n  },\n  providers: [{provide: CDK_DRAG_HANDLE, useExisting: CdkDragHandle}],\n})\nexport class CdkDragHandle implements OnDestroy {\n  /** Emits when the state of the handle has changed. */\n  readonly _stateChanges = new Subject<CdkDragHandle>();\n\n  /** Whether starting to drag through this handle is disabled. */\n  @Input({alias: 'cdkDragHandleDisabled', transform: booleanAttribute})\n  get disabled(): boolean {\n    return this._disabled;\n  }\n  set disabled(value: boolean) {\n    this._disabled = value;\n    this._stateChanges.next(this);\n  }\n  private _disabled = false;\n\n  constructor(\n    public element: ElementRef<HTMLElement>,\n    @Inject(CDK_DRAG_PARENT) @Optional() @SkipSelf() private _parentDrag?: CdkDrag,\n  ) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      assertElementNode(element.nativeElement, 'cdkDragHandle');\n    }\n\n    _parentDrag?._addHandle(this);\n  }\n\n  ngOnDestroy() {\n    this._parentDrag?._removeHandle(this);\n    this._stateChanges.complete();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {InjectionToken} from '@angular/core';\nimport {DragRefConfig, Point, DragRef} from '../drag-ref';\n\n/** Possible values that can be used to configure the drag start delay. */\nexport type DragStartDelay = number | {touch: number; mouse: number};\n\n/** Possible axis along which dragging can be locked. */\nexport type DragAxis = 'x' | 'y';\n\n/** Function that can be used to constrain the position of a dragged element. */\nexport type DragConstrainPosition = (point: Point, dragRef: DragRef) => Point;\n\n/** Possible orientations for a drop list. */\nexport type DropListOrientation = 'horizontal' | 'vertical';\n\n/**\n * Injection token that can be used to configure the\n * behavior of the drag&drop-related components.\n */\nexport const CDK_DRAG_CONFIG = new InjectionToken<DragDropConfig>('CDK_DRAG_CONFIG');\n\n/**\n * Object that can be used to configure the drag\n * items and drop lists within a module or a component.\n */\nexport interface DragDropConfig extends Partial<DragRefConfig> {\n  lockAxis?: DragAxis;\n  dragStartDelay?: DragStartDelay;\n  constrainPosition?: DragConstrainPosition;\n  previewClass?: string | string[];\n  boundaryElement?: string;\n  rootElementSelector?: string;\n  draggingDisabled?: boolean;\n  sortingDisabled?: boolean;\n  listAutoScrollDisabled?: boolean;\n  listOrientation?: DropListOrientation;\n  zIndex?: number;\n  previewContainer?: 'global' | 'parent';\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directionality} from '@angular/cdk/bidi';\nimport {DOCUMENT} from '@angular/common';\nimport {\n  AfterViewInit,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  Inject,\n  Input,\n  NgZone,\n  OnDestroy,\n  Optional,\n  Output,\n  SkipSelf,\n  ViewContainerRef,\n  OnChanges,\n  SimpleChanges,\n  ChangeDetectorRef,\n  Self,\n  InjectionToken,\n  booleanAttribute,\n} from '@angular/core';\nimport {coerceElement, coerceNumberProperty} from '@angular/cdk/coercion';\nimport {BehaviorSubject, Observable, Observer, Subject, merge} from 'rxjs';\nimport {startWith, take, map, takeUntil, switchMap, tap} from 'rxjs/operators';\nimport type {\n  CdkDragDrop,\n  CdkDragEnd,\n  CdkDragEnter,\n  CdkDragExit,\n  CdkDragMove,\n  CdkDragStart,\n  CdkDragRelease,\n} from '../drag-events';\nimport {CDK_DRAG_HANDLE, CdkDragHandle} from './drag-handle';\nimport {CdkDragPlaceholder} from './drag-placeholder';\nimport {CdkDragPreview} from './drag-preview';\nimport {CDK_DRAG_PARENT} from '../drag-parent';\nimport {DragRef, Point, PreviewContainer} from '../drag-ref';\nimport type {CdkDropList} from './drop-list';\nimport {DragDrop} from '../drag-drop';\nimport {CDK_DRAG_CONFIG, DragDropConfig, DragStartDelay, DragAxis} from './config';\nimport {assertElementNode} from './assertions';\n\nconst DRAG_HOST_CLASS = 'cdk-drag';\n\n/**\n * Injection token that can be used to reference instances of `CdkDropList`. It serves as\n * alternative token to the actual `CdkDropList` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nexport const CDK_DROP_LIST = new InjectionToken<CdkDropList>('CdkDropList');\n\n/** Element that can be moved inside a CdkDropList container. */\n@Directive({\n  selector: '[cdkDrag]',\n  exportAs: 'cdkDrag',\n  standalone: true,\n  host: {\n    'class': DRAG_HOST_CLASS,\n    '[class.cdk-drag-disabled]': 'disabled',\n    '[class.cdk-drag-dragging]': '_dragRef.isDragging()',\n  },\n  providers: [{provide: CDK_DRAG_PARENT, useExisting: CdkDrag}],\n})\nexport class CdkDrag<T = any> implements AfterViewInit, OnChanges, OnDestroy {\n  private readonly _destroyed = new Subject<void>();\n  private static _dragInstances: CdkDrag[] = [];\n  private _handles = new BehaviorSubject<CdkDragHandle[]>([]);\n  private _previewTemplate: CdkDragPreview | null;\n  private _placeholderTemplate: CdkDragPlaceholder | null;\n\n  /** Reference to the underlying drag instance. */\n  _dragRef: DragRef<CdkDrag<T>>;\n\n  /** Arbitrary data to attach to this drag instance. */\n  @Input('cdkDragData') data: T;\n\n  /** Locks the position of the dragged element along the specified axis. */\n  @Input('cdkDragLockAxis') lockAxis: DragAxis;\n\n  /**\n   * Selector that will be used to determine the root draggable element, starting from\n   * the `cdkDrag` element and going up the DOM. Passing an alternate root element is useful\n   * when trying to enable dragging on an element that you might not have access to.\n   */\n  @Input('cdkDragRootElement') rootElementSelector: string;\n\n  /**\n   * Node or selector that will be used to determine the element to which the draggable's\n   * position will be constrained. If a string is passed in, it'll be used as a selector that\n   * will be matched starting from the element's parent and going up the DOM until a match\n   * has been found.\n   */\n  @Input('cdkDragBoundary') boundaryElement: string | ElementRef<HTMLElement> | HTMLElement;\n\n  /**\n   * Amount of milliseconds to wait after the user has put their\n   * pointer down before starting to drag the element.\n   */\n  @Input('cdkDragStartDelay') dragStartDelay: DragStartDelay;\n\n  /**\n   * Sets the position of a `CdkDrag` that is outside of a drop container.\n   * Can be used to restore the element's position for a returning user.\n   */\n  @Input('cdkDragFreeDragPosition') freeDragPosition: Point;\n\n  /** Whether starting to drag this element is disabled. */\n  @Input({alias: 'cdkDragDisabled', transform: booleanAttribute})\n  get disabled(): boolean {\n    return this._disabled || (this.dropContainer && this.dropContainer.disabled);\n  }\n  set disabled(value: boolean) {\n    this._disabled = value;\n    this._dragRef.disabled = this._disabled;\n  }\n  private _disabled: boolean;\n\n  /**\n   * Function that can be used to customize the logic of how the position of the drag item\n   * is limited while it's being dragged. Gets called with a point containing the current position\n   * of the user's pointer on the page, a reference to the item being dragged and its dimensions.\n   * Should return a point describing where the item should be rendered.\n   */\n  @Input('cdkDragConstrainPosition') constrainPosition?: (\n    userPointerPosition: Point,\n    dragRef: DragRef,\n    dimensions: DOMRect,\n    pickupPositionInElement: Point,\n  ) => Point;\n\n  /** Class to be added to the preview element. */\n  @Input('cdkDragPreviewClass') previewClass: string | string[];\n\n  /**\n   * Configures the place into which the preview of the item will be inserted. Can be configured\n   * globally through `CDK_DROP_LIST`. Possible values:\n   * - `global` - Preview will be inserted at the bottom of the `<body>`. The advantage is that\n   * you don't have to worry about `overflow: hidden` or `z-index`, but the item won't retain\n   * its inherited styles.\n   * - `parent` - Preview will be inserted into the parent of the drag item. The advantage is that\n   * inherited styles will be preserved, but it may be clipped by `overflow: hidden` or not be\n   * visible due to `z-index`. Furthermore, the preview is going to have an effect over selectors\n   * like `:nth-child` and some flexbox configurations.\n   * - `ElementRef<HTMLElement> | HTMLElement` - Preview will be inserted into a specific element.\n   * Same advantages and disadvantages as `parent`.\n   */\n  @Input('cdkDragPreviewContainer') previewContainer: PreviewContainer;\n\n  /** Emits when the user starts dragging the item. */\n  @Output('cdkDragStarted') readonly started: EventEmitter<CdkDragStart> =\n    new EventEmitter<CdkDragStart>();\n\n  /** Emits when the user has released a drag item, before any animations have started. */\n  @Output('cdkDragReleased') readonly released: EventEmitter<CdkDragRelease> =\n    new EventEmitter<CdkDragRelease>();\n\n  /** Emits when the user stops dragging an item in the container. */\n  @Output('cdkDragEnded') readonly ended: EventEmitter<CdkDragEnd> = new EventEmitter<CdkDragEnd>();\n\n  /** Emits when the user has moved the item into a new container. */\n  @Output('cdkDragEntered') readonly entered: EventEmitter<CdkDragEnter<any>> = new EventEmitter<\n    CdkDragEnter<any>\n  >();\n\n  /** Emits when the user removes the item its container by dragging it into another container. */\n  @Output('cdkDragExited') readonly exited: EventEmitter<CdkDragExit<any>> = new EventEmitter<\n    CdkDragExit<any>\n  >();\n\n  /** Emits when the user drops the item inside a container. */\n  @Output('cdkDragDropped') readonly dropped: EventEmitter<CdkDragDrop<any>> = new EventEmitter<\n    CdkDragDrop<any>\n  >();\n\n  /**\n   * Emits as the user is dragging the item. Use with caution,\n   * because this event will fire for every pixel that the user has dragged.\n   */\n  @Output('cdkDragMoved')\n  readonly moved: Observable<CdkDragMove<T>> = new Observable(\n    (observer: Observer<CdkDragMove<T>>) => {\n      const subscription = this._dragRef.moved\n        .pipe(\n          map(movedEvent => ({\n            source: this,\n            pointerPosition: movedEvent.pointerPosition,\n            event: movedEvent.event,\n            delta: movedEvent.delta,\n            distance: movedEvent.distance,\n          })),\n        )\n        .subscribe(observer);\n\n      return () => {\n        subscription.unsubscribe();\n      };\n    },\n  );\n\n  constructor(\n    /** Element that the draggable is attached to. */\n    public element: ElementRef<HTMLElement>,\n    /** Droppable container that the draggable is a part of. */\n    @Inject(CDK_DROP_LIST) @Optional() @SkipSelf() public dropContainer: CdkDropList,\n    /**\n     * @deprecated `_document` parameter no longer being used and will be removed.\n     * @breaking-change 12.0.0\n     */\n    @Inject(DOCUMENT) _document: any,\n    private _ngZone: NgZone,\n    private _viewContainerRef: ViewContainerRef,\n    @Optional() @Inject(CDK_DRAG_CONFIG) config: DragDropConfig,\n    @Optional() private _dir: Directionality,\n    dragDrop: DragDrop,\n    private _changeDetectorRef: ChangeDetectorRef,\n    @Optional() @Self() @Inject(CDK_DRAG_HANDLE) private _selfHandle?: CdkDragHandle,\n    @Optional() @SkipSelf() @Inject(CDK_DRAG_PARENT) private _parentDrag?: CdkDrag,\n  ) {\n    this._dragRef = dragDrop.createDrag(element, {\n      dragStartThreshold:\n        config && config.dragStartThreshold != null ? config.dragStartThreshold : 5,\n      pointerDirectionChangeThreshold:\n        config && config.pointerDirectionChangeThreshold != null\n          ? config.pointerDirectionChangeThreshold\n          : 5,\n      zIndex: config?.zIndex,\n    });\n    this._dragRef.data = this;\n\n    // We have to keep track of the drag instances in order to be able to match an element to\n    // a drag instance. We can't go through the global registry of `DragRef`, because the root\n    // element could be different.\n    CdkDrag._dragInstances.push(this);\n\n    if (config) {\n      this._assignDefaults(config);\n    }\n\n    // Note that usually the container is assigned when the drop list is picks up the item, but in\n    // some cases (mainly transplanted views with OnPush, see #18341) we may end up in a situation\n    // where there are no items on the first change detection pass, but the items get picked up as\n    // soon as the user triggers another pass by dragging. This is a problem, because the item would\n    // have to switch from standalone mode to drag mode in the middle of the dragging sequence which\n    // is too late since the two modes save different kinds of information. We work around it by\n    // assigning the drop container both from here and the list.\n    if (dropContainer) {\n      this._dragRef._withDropContainer(dropContainer._dropListRef);\n      dropContainer.addItem(this);\n    }\n\n    this._syncInputs(this._dragRef);\n    this._handleEvents(this._dragRef);\n  }\n\n  /**\n   * Returns the element that is being used as a placeholder\n   * while the current element is being dragged.\n   */\n  getPlaceholderElement(): HTMLElement {\n    return this._dragRef.getPlaceholderElement();\n  }\n\n  /** Returns the root draggable element. */\n  getRootElement(): HTMLElement {\n    return this._dragRef.getRootElement();\n  }\n\n  /** Resets a standalone drag item to its initial position. */\n  reset(): void {\n    this._dragRef.reset();\n  }\n\n  /**\n   * Gets the pixel coordinates of the draggable outside of a drop container.\n   */\n  getFreeDragPosition(): Readonly<Point> {\n    return this._dragRef.getFreeDragPosition();\n  }\n\n  /**\n   * Sets the current position in pixels the draggable outside of a drop container.\n   * @param value New position to be set.\n   */\n  setFreeDragPosition(value: Point): void {\n    this._dragRef.setFreeDragPosition(value);\n  }\n\n  ngAfterViewInit() {\n    // Normally this isn't in the zone, but it can cause major performance regressions for apps\n    // using `zone-patch-rxjs` because it'll trigger a change detection when it unsubscribes.\n    this._ngZone.runOutsideAngular(() => {\n      // We need to wait for the zone to stabilize, in order for the reference\n      // element to be in the proper place in the DOM. This is mostly relevant\n      // for draggable elements inside portals since they get stamped out in\n      // their original DOM position and then they get transferred to the portal.\n      this._ngZone.onStable.pipe(take(1), takeUntil(this._destroyed)).subscribe(() => {\n        this._updateRootElement();\n        this._setupHandlesListener();\n\n        if (this.freeDragPosition) {\n          this._dragRef.setFreeDragPosition(this.freeDragPosition);\n        }\n      });\n    });\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    const rootSelectorChange = changes['rootElementSelector'];\n    const positionChange = changes['freeDragPosition'];\n\n    // We don't have to react to the first change since it's being\n    // handled in `ngAfterViewInit` where it needs to be deferred.\n    if (rootSelectorChange && !rootSelectorChange.firstChange) {\n      this._updateRootElement();\n    }\n\n    // Skip the first change since it's being handled in `ngAfterViewInit`.\n    if (positionChange && !positionChange.firstChange && this.freeDragPosition) {\n      this._dragRef.setFreeDragPosition(this.freeDragPosition);\n    }\n  }\n\n  ngOnDestroy() {\n    if (this.dropContainer) {\n      this.dropContainer.removeItem(this);\n    }\n\n    const index = CdkDrag._dragInstances.indexOf(this);\n    if (index > -1) {\n      CdkDrag._dragInstances.splice(index, 1);\n    }\n\n    // Unnecessary in most cases, but used to avoid extra change detections with `zone-paths-rxjs`.\n    this._ngZone.runOutsideAngular(() => {\n      this._handles.complete();\n      this._destroyed.next();\n      this._destroyed.complete();\n      this._dragRef.dispose();\n    });\n  }\n\n  _addHandle(handle: CdkDragHandle) {\n    const handles = this._handles.getValue();\n    handles.push(handle);\n    this._handles.next(handles);\n  }\n\n  _removeHandle(handle: CdkDragHandle) {\n    const handles = this._handles.getValue();\n    const index = handles.indexOf(handle);\n\n    if (index > -1) {\n      handles.splice(index, 1);\n      this._handles.next(handles);\n    }\n  }\n\n  _setPreviewTemplate(preview: CdkDragPreview) {\n    this._previewTemplate = preview;\n  }\n\n  _resetPreviewTemplate(preview: CdkDragPreview) {\n    if (preview === this._previewTemplate) {\n      this._previewTemplate = null;\n    }\n  }\n\n  _setPlaceholderTemplate(placeholder: CdkDragPlaceholder) {\n    this._placeholderTemplate = placeholder;\n  }\n\n  _resetPlaceholderTemplate(placeholder: CdkDragPlaceholder) {\n    if (placeholder === this._placeholderTemplate) {\n      this._placeholderTemplate = null;\n    }\n  }\n\n  /** Syncs the root element with the `DragRef`. */\n  private _updateRootElement() {\n    const element = this.element.nativeElement as HTMLElement;\n    let rootElement = element;\n    if (this.rootElementSelector) {\n      rootElement =\n        element.closest !== undefined\n          ? (element.closest(this.rootElementSelector) as HTMLElement)\n          : // Comment tag doesn't have closest method, so use parent's one.\n            (element.parentElement?.closest(this.rootElementSelector) as HTMLElement);\n    }\n\n    if (rootElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      assertElementNode(rootElement, 'cdkDrag');\n    }\n\n    this._dragRef.withRootElement(rootElement || element);\n  }\n\n  /** Gets the boundary element, based on the `boundaryElement` value. */\n  private _getBoundaryElement() {\n    const boundary = this.boundaryElement;\n\n    if (!boundary) {\n      return null;\n    }\n\n    if (typeof boundary === 'string') {\n      return this.element.nativeElement.closest<HTMLElement>(boundary);\n    }\n\n    return coerceElement(boundary);\n  }\n\n  /** Syncs the inputs of the CdkDrag with the options of the underlying DragRef. */\n  private _syncInputs(ref: DragRef<CdkDrag<T>>) {\n    ref.beforeStarted.subscribe(() => {\n      if (!ref.isDragging()) {\n        const dir = this._dir;\n        const dragStartDelay = this.dragStartDelay;\n        const placeholder = this._placeholderTemplate\n          ? {\n              template: this._placeholderTemplate.templateRef,\n              context: this._placeholderTemplate.data,\n              viewContainer: this._viewContainerRef,\n            }\n          : null;\n        const preview = this._previewTemplate\n          ? {\n              template: this._previewTemplate.templateRef,\n              context: this._previewTemplate.data,\n              matchSize: this._previewTemplate.matchSize,\n              viewContainer: this._viewContainerRef,\n            }\n          : null;\n\n        ref.disabled = this.disabled;\n        ref.lockAxis = this.lockAxis;\n        ref.dragStartDelay =\n          typeof dragStartDelay === 'object' && dragStartDelay\n            ? dragStartDelay\n            : coerceNumberProperty(dragStartDelay);\n        ref.constrainPosition = this.constrainPosition;\n        ref.previewClass = this.previewClass;\n        ref\n          .withBoundaryElement(this._getBoundaryElement())\n          .withPlaceholderTemplate(placeholder)\n          .withPreviewTemplate(preview)\n          .withPreviewContainer(this.previewContainer || 'global');\n\n        if (dir) {\n          ref.withDirection(dir.value);\n        }\n      }\n    });\n\n    // This only needs to be resolved once.\n    ref.beforeStarted.pipe(take(1)).subscribe(() => {\n      // If we managed to resolve a parent through DI, use it.\n      if (this._parentDrag) {\n        ref.withParent(this._parentDrag._dragRef);\n        return;\n      }\n\n      // Otherwise fall back to resolving the parent by looking up the DOM. This can happen if\n      // the item was projected into another item by something like `ngTemplateOutlet`.\n      let parent = this.element.nativeElement.parentElement;\n      while (parent) {\n        if (parent.classList.contains(DRAG_HOST_CLASS)) {\n          ref.withParent(\n            CdkDrag._dragInstances.find(drag => {\n              return drag.element.nativeElement === parent;\n            })?._dragRef || null,\n          );\n          break;\n        }\n        parent = parent.parentElement;\n      }\n    });\n  }\n\n  /** Handles the events from the underlying `DragRef`. */\n  private _handleEvents(ref: DragRef<CdkDrag<T>>) {\n    ref.started.subscribe(startEvent => {\n      this.started.emit({source: this, event: startEvent.event});\n\n      // Since all of these events run outside of change detection,\n      // we need to ensure that everything is marked correctly.\n      this._changeDetectorRef.markForCheck();\n    });\n\n    ref.released.subscribe(releaseEvent => {\n      this.released.emit({source: this, event: releaseEvent.event});\n    });\n\n    ref.ended.subscribe(endEvent => {\n      this.ended.emit({\n        source: this,\n        distance: endEvent.distance,\n        dropPoint: endEvent.dropPoint,\n        event: endEvent.event,\n      });\n\n      // Since all of these events run outside of change detection,\n      // we need to ensure that everything is marked correctly.\n      this._changeDetectorRef.markForCheck();\n    });\n\n    ref.entered.subscribe(enterEvent => {\n      this.entered.emit({\n        container: enterEvent.container.data,\n        item: this,\n        currentIndex: enterEvent.currentIndex,\n      });\n    });\n\n    ref.exited.subscribe(exitEvent => {\n      this.exited.emit({\n        container: exitEvent.container.data,\n        item: this,\n      });\n    });\n\n    ref.dropped.subscribe(dropEvent => {\n      this.dropped.emit({\n        previousIndex: dropEvent.previousIndex,\n        currentIndex: dropEvent.currentIndex,\n        previousContainer: dropEvent.previousContainer.data,\n        container: dropEvent.container.data,\n        isPointerOverContainer: dropEvent.isPointerOverContainer,\n        item: this,\n        distance: dropEvent.distance,\n        dropPoint: dropEvent.dropPoint,\n        event: dropEvent.event,\n      });\n    });\n  }\n\n  /** Assigns the default input values based on a provided config object. */\n  private _assignDefaults(config: DragDropConfig) {\n    const {\n      lockAxis,\n      dragStartDelay,\n      constrainPosition,\n      previewClass,\n      boundaryElement,\n      draggingDisabled,\n      rootElementSelector,\n      previewContainer,\n    } = config;\n\n    this.disabled = draggingDisabled == null ? false : draggingDisabled;\n    this.dragStartDelay = dragStartDelay || 0;\n\n    if (lockAxis) {\n      this.lockAxis = lockAxis;\n    }\n\n    if (constrainPosition) {\n      this.constrainPosition = constrainPosition;\n    }\n\n    if (previewClass) {\n      this.previewClass = previewClass;\n    }\n\n    if (boundaryElement) {\n      this.boundaryElement = boundaryElement;\n    }\n\n    if (rootElementSelector) {\n      this.rootElementSelector = rootElementSelector;\n    }\n\n    if (previewContainer) {\n      this.previewContainer = previewContainer;\n    }\n  }\n\n  /** Sets up the listener that syncs the handles with the drag ref. */\n  private _setupHandlesListener() {\n    // Listen for any newly-added handles.\n    this._handles\n      .pipe(\n        // Sync the new handles with the DragRef.\n        tap(handles => {\n          const handleElements = handles.map(handle => handle.element);\n\n          // Usually handles are only allowed to be a descendant of the drag element, but if\n          // the consumer defined a different drag root, we should allow the drag element\n          // itself to be a handle too.\n          if (this._selfHandle && this.rootElementSelector) {\n            handleElements.push(this.element);\n          }\n\n          this._dragRef.withHandles(handleElements);\n        }),\n        // Listen if the state of any of the handles changes.\n        switchMap((handles: CdkDragHandle[]) => {\n          return merge(\n            ...handles.map(item => item._stateChanges.pipe(startWith(item))),\n          ) as Observable<CdkDragHandle>;\n        }),\n        takeUntil(this._destroyed),\n      )\n      .subscribe(handleInstance => {\n        // Enabled/disable the handle that changed in the DragRef.\n        const dragRef = this._dragRef;\n        const handle = handleInstance.element.nativeElement;\n        handleInstance.disabled ? dragRef.disableHandle(handle) : dragRef.enableHandle(handle);\n      });\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, OnDestroy, Input, InjectionToken, booleanAttribute} from '@angular/core';\n\n/**\n * Injection token that can be used to reference instances of `CdkDropListGroup`. It serves as\n * alternative token to the actual `CdkDropListGroup` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nexport const CDK_DROP_LIST_GROUP = new InjectionToken<CdkDropListGroup<unknown>>(\n  'CdkDropListGroup',\n);\n\n/**\n * Declaratively connects sibling `cdkDropList` instances together. All of the `cdkDropList`\n * elements that are placed inside a `cdkDropListGroup` will be connected to each other\n * automatically. Can be used as an alternative to the `cdkDropListConnectedTo` input\n * from `cdkDropList`.\n */\n@Directive({\n  selector: '[cdkDropListGroup]',\n  exportAs: 'cdkDropListGroup',\n  standalone: true,\n  providers: [{provide: CDK_DROP_LIST_GROUP, useExisting: CdkDropListGroup}],\n})\nexport class CdkDropListGroup<T> implements OnDestroy {\n  /** Drop lists registered inside the group. */\n  readonly _items = new Set<T>();\n\n  /** Whether starting a dragging sequence from inside this group is disabled. */\n  @Input({alias: 'cdkDropListGroupDisabled', transform: booleanAttribute})\n  disabled: boolean = false;\n\n  ngOnDestroy() {\n    this._items.clear();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NumberInput, coerceArray, coerceNumberProperty} from '@angular/cdk/coercion';\nimport {\n  ElementRef,\n  EventEmitter,\n  Input,\n  OnDestroy,\n  Output,\n  Optional,\n  Directive,\n  ChangeDetectorRef,\n  SkipSelf,\n  Inject,\n  booleanAttribute,\n} from '@angular/core';\nimport {Directionality} from '@angular/cdk/bidi';\nimport {ScrollDispatcher} from '@angular/cdk/scrolling';\nimport {CDK_DROP_LIST, CdkDrag} from './drag';\nimport {CdkDragDrop, CdkDragEnter, CdkDragExit, CdkDragSortEvent} from '../drag-events';\nimport {CDK_DROP_LIST_GROUP, CdkDropListGroup} from './drop-list-group';\nimport {DropListRef} from '../drop-list-ref';\nimport {DragRef} from '../drag-ref';\nimport {DragDrop} from '../drag-drop';\nimport {DropListOrientation, DragAxis, DragDropConfig, CDK_DRAG_CONFIG} from './config';\nimport {merge, Subject} from 'rxjs';\nimport {startWith, takeUntil} from 'rxjs/operators';\nimport {assertElementNode} from './assertions';\n\n/** Counter used to generate unique ids for drop zones. */\nlet _uniqueIdCounter = 0;\n\n/** Container that wraps a set of draggable items. */\n@Directive({\n  selector: '[cdkDropList], cdk-drop-list',\n  exportAs: 'cdkDropList',\n  standalone: true,\n  providers: [\n    // Prevent child drop lists from picking up the same group as their parent.\n    {provide: CDK_DROP_LIST_GROUP, useValue: undefined},\n    {provide: CDK_DROP_LIST, useExisting: CdkDropList},\n  ],\n  host: {\n    'class': 'cdk-drop-list',\n    '[attr.id]': 'id',\n    '[class.cdk-drop-list-disabled]': 'disabled',\n    '[class.cdk-drop-list-dragging]': '_dropListRef.isDragging()',\n    '[class.cdk-drop-list-receiving]': '_dropListRef.isReceiving()',\n  },\n})\nexport class CdkDropList<T = any> implements OnDestroy {\n  /** Emits when the list has been destroyed. */\n  private readonly _destroyed = new Subject<void>();\n\n  /** Whether the element's scrollable parents have been resolved. */\n  private _scrollableParentsResolved: boolean;\n\n  /** Keeps track of the drop lists that are currently on the page. */\n  private static _dropLists: CdkDropList[] = [];\n\n  /** Reference to the underlying drop list instance. */\n  _dropListRef: DropListRef<CdkDropList<T>>;\n\n  /**\n   * Other draggable containers that this container is connected to and into which the\n   * container's items can be transferred. Can either be references to other drop containers,\n   * or their unique IDs.\n   */\n  @Input('cdkDropListConnectedTo')\n  connectedTo: (CdkDropList | string)[] | CdkDropList | string = [];\n\n  /** Arbitrary data to attach to this container. */\n  @Input('cdkDropListData') data: T;\n\n  /** Direction in which the list is oriented. */\n  @Input('cdkDropListOrientation') orientation: DropListOrientation;\n\n  /**\n   * Unique ID for the drop zone. Can be used as a reference\n   * in the `connectedTo` of another `CdkDropList`.\n   */\n  @Input() id: string = `cdk-drop-list-${_uniqueIdCounter++}`;\n\n  /** Locks the position of the draggable elements inside the container along the specified axis. */\n  @Input('cdkDropListLockAxis') lockAxis: DragAxis;\n\n  /** Whether starting a dragging sequence from this container is disabled. */\n  @Input({alias: 'cdkDropListDisabled', transform: booleanAttribute})\n  get disabled(): boolean {\n    return this._disabled || (!!this._group && this._group.disabled);\n  }\n  set disabled(value: boolean) {\n    // Usually we sync the directive and ref state right before dragging starts, in order to have\n    // a single point of failure and to avoid having to use setters for everything. `disabled` is\n    // a special case, because it can prevent the `beforeStarted` event from firing, which can lock\n    // the user in a disabled state, so we also need to sync it as it's being set.\n    this._dropListRef.disabled = this._disabled = value;\n  }\n  private _disabled: boolean;\n\n  /** Whether sorting within this drop list is disabled. */\n  @Input({alias: 'cdkDropListSortingDisabled', transform: booleanAttribute})\n  sortingDisabled: boolean;\n\n  /**\n   * Function that is used to determine whether an item\n   * is allowed to be moved into a drop container.\n   */\n  @Input('cdkDropListEnterPredicate')\n  enterPredicate: (drag: CdkDrag, drop: CdkDropList) => boolean = () => true;\n\n  /** Functions that is used to determine whether an item can be sorted into a particular index. */\n  @Input('cdkDropListSortPredicate')\n  sortPredicate: (index: number, drag: CdkDrag, drop: CdkDropList) => boolean = () => true;\n\n  /** Whether to auto-scroll the view when the user moves their pointer close to the edges. */\n  @Input({alias: 'cdkDropListAutoScrollDisabled', transform: booleanAttribute})\n  autoScrollDisabled: boolean;\n\n  /** Number of pixels to scroll for each frame when auto-scrolling an element. */\n  @Input('cdkDropListAutoScrollStep')\n  autoScrollStep: NumberInput;\n\n  /** Emits when the user drops an item inside the container. */\n  @Output('cdkDropListDropped')\n  readonly dropped: EventEmitter<CdkDragDrop<T, any>> = new EventEmitter<CdkDragDrop<T, any>>();\n\n  /**\n   * Emits when the user has moved a new drag item into this container.\n   */\n  @Output('cdkDropListEntered')\n  readonly entered: EventEmitter<CdkDragEnter<T>> = new EventEmitter<CdkDragEnter<T>>();\n\n  /**\n   * Emits when the user removes an item from the container\n   * by dragging it into another container.\n   */\n  @Output('cdkDropListExited')\n  readonly exited: EventEmitter<CdkDragExit<T>> = new EventEmitter<CdkDragExit<T>>();\n\n  /** Emits as the user is swapping items while actively dragging. */\n  @Output('cdkDropListSorted')\n  readonly sorted: EventEmitter<CdkDragSortEvent<T>> = new EventEmitter<CdkDragSortEvent<T>>();\n\n  /**\n   * Keeps track of the items that are registered with this container. Historically we used to\n   * do this with a `ContentChildren` query, however queries don't handle transplanted views very\n   * well which means that we can't handle cases like dragging the headers of a `mat-table`\n   * correctly. What we do instead is to have the items register themselves with the container\n   * and then we sort them based on their position in the DOM.\n   */\n  private _unsortedItems = new Set<CdkDrag>();\n\n  constructor(\n    /** Element that the drop list is attached to. */\n    public element: ElementRef<HTMLElement>,\n    dragDrop: DragDrop,\n    private _changeDetectorRef: ChangeDetectorRef,\n    private _scrollDispatcher: ScrollDispatcher,\n    @Optional() private _dir?: Directionality,\n    @Optional()\n    @Inject(CDK_DROP_LIST_GROUP)\n    @SkipSelf()\n    private _group?: CdkDropListGroup<CdkDropList>,\n    @Optional() @Inject(CDK_DRAG_CONFIG) config?: DragDropConfig,\n  ) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      assertElementNode(element.nativeElement, 'cdkDropList');\n    }\n\n    this._dropListRef = dragDrop.createDropList(element);\n    this._dropListRef.data = this;\n\n    if (config) {\n      this._assignDefaults(config);\n    }\n\n    this._dropListRef.enterPredicate = (drag: DragRef<CdkDrag>, drop: DropListRef<CdkDropList>) => {\n      return this.enterPredicate(drag.data, drop.data);\n    };\n\n    this._dropListRef.sortPredicate = (\n      index: number,\n      drag: DragRef<CdkDrag>,\n      drop: DropListRef<CdkDropList>,\n    ) => {\n      return this.sortPredicate(index, drag.data, drop.data);\n    };\n\n    this._setupInputSyncSubscription(this._dropListRef);\n    this._handleEvents(this._dropListRef);\n    CdkDropList._dropLists.push(this);\n\n    if (_group) {\n      _group._items.add(this);\n    }\n  }\n\n  /** Registers an items with the drop list. */\n  addItem(item: CdkDrag): void {\n    this._unsortedItems.add(item);\n\n    if (this._dropListRef.isDragging()) {\n      this._syncItemsWithRef();\n    }\n  }\n\n  /** Removes an item from the drop list. */\n  removeItem(item: CdkDrag): void {\n    this._unsortedItems.delete(item);\n\n    if (this._dropListRef.isDragging()) {\n      this._syncItemsWithRef();\n    }\n  }\n\n  /** Gets the registered items in the list, sorted by their position in the DOM. */\n  getSortedItems(): CdkDrag[] {\n    return Array.from(this._unsortedItems).sort((a: CdkDrag, b: CdkDrag) => {\n      const documentPosition = a._dragRef\n        .getVisibleElement()\n        .compareDocumentPosition(b._dragRef.getVisibleElement());\n\n      // `compareDocumentPosition` returns a bitmask so we have to use a bitwise operator.\n      // https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n      // tslint:disable-next-line:no-bitwise\n      return documentPosition & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;\n    });\n  }\n\n  ngOnDestroy() {\n    const index = CdkDropList._dropLists.indexOf(this);\n\n    if (index > -1) {\n      CdkDropList._dropLists.splice(index, 1);\n    }\n\n    if (this._group) {\n      this._group._items.delete(this);\n    }\n\n    this._unsortedItems.clear();\n    this._dropListRef.dispose();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n\n  /** Syncs the inputs of the CdkDropList with the options of the underlying DropListRef. */\n  private _setupInputSyncSubscription(ref: DropListRef<CdkDropList>) {\n    if (this._dir) {\n      this._dir.change\n        .pipe(startWith(this._dir.value), takeUntil(this._destroyed))\n        .subscribe(value => ref.withDirection(value));\n    }\n\n    ref.beforeStarted.subscribe(() => {\n      const siblings = coerceArray(this.connectedTo).map(drop => {\n        if (typeof drop === 'string') {\n          const correspondingDropList = CdkDropList._dropLists.find(list => list.id === drop);\n\n          if (!correspondingDropList && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            console.warn(`CdkDropList could not find connected drop list with id \"${drop}\"`);\n          }\n\n          return correspondingDropList!;\n        }\n\n        return drop;\n      });\n\n      if (this._group) {\n        this._group._items.forEach(drop => {\n          if (siblings.indexOf(drop) === -1) {\n            siblings.push(drop);\n          }\n        });\n      }\n\n      // Note that we resolve the scrollable parents here so that we delay the resolution\n      // as long as possible, ensuring that the element is in its final place in the DOM.\n      if (!this._scrollableParentsResolved) {\n        const scrollableParents = this._scrollDispatcher\n          .getAncestorScrollContainers(this.element)\n          .map(scrollable => scrollable.getElementRef().nativeElement);\n        this._dropListRef.withScrollableParents(scrollableParents);\n\n        // Only do this once since it involves traversing the DOM and the parents\n        // shouldn't be able to change without the drop list being destroyed.\n        this._scrollableParentsResolved = true;\n      }\n\n      ref.disabled = this.disabled;\n      ref.lockAxis = this.lockAxis;\n      ref.sortingDisabled = this.sortingDisabled;\n      ref.autoScrollDisabled = this.autoScrollDisabled;\n      ref.autoScrollStep = coerceNumberProperty(this.autoScrollStep, 2);\n      ref\n        .connectedTo(siblings.filter(drop => drop && drop !== this).map(list => list._dropListRef))\n        .withOrientation(this.orientation);\n    });\n  }\n\n  /** Handles events from the underlying DropListRef. */\n  private _handleEvents(ref: DropListRef<CdkDropList>) {\n    ref.beforeStarted.subscribe(() => {\n      this._syncItemsWithRef();\n      this._changeDetectorRef.markForCheck();\n    });\n\n    ref.entered.subscribe(event => {\n      this.entered.emit({\n        container: this,\n        item: event.item.data,\n        currentIndex: event.currentIndex,\n      });\n    });\n\n    ref.exited.subscribe(event => {\n      this.exited.emit({\n        container: this,\n        item: event.item.data,\n      });\n      this._changeDetectorRef.markForCheck();\n    });\n\n    ref.sorted.subscribe(event => {\n      this.sorted.emit({\n        previousIndex: event.previousIndex,\n        currentIndex: event.currentIndex,\n        container: this,\n        item: event.item.data,\n      });\n    });\n\n    ref.dropped.subscribe(dropEvent => {\n      this.dropped.emit({\n        previousIndex: dropEvent.previousIndex,\n        currentIndex: dropEvent.currentIndex,\n        previousContainer: dropEvent.previousContainer.data,\n        container: dropEvent.container.data,\n        item: dropEvent.item.data,\n        isPointerOverContainer: dropEvent.isPointerOverContainer,\n        distance: dropEvent.distance,\n        dropPoint: dropEvent.dropPoint,\n        event: dropEvent.event,\n      });\n\n      // Mark for check since all of these events run outside of change\n      // detection and we're not guaranteed for something else to have triggered it.\n      this._changeDetectorRef.markForCheck();\n    });\n\n    merge(ref.receivingStarted, ref.receivingStopped).subscribe(() =>\n      this._changeDetectorRef.markForCheck(),\n    );\n  }\n\n  /** Assigns the default input values based on a provided config object. */\n  private _assignDefaults(config: DragDropConfig) {\n    const {lockAxis, draggingDisabled, sortingDisabled, listAutoScrollDisabled, listOrientation} =\n      config;\n\n    this.disabled = draggingDisabled == null ? false : draggingDisabled;\n    this.sortingDisabled = sortingDisabled == null ? false : sortingDisabled;\n    this.autoScrollDisabled = listAutoScrollDisabled == null ? false : listAutoScrollDisabled;\n    this.orientation = listOrientation || 'vertical';\n\n    if (lockAxis) {\n      this.lockAxis = lockAxis;\n    }\n  }\n\n  /** Syncs up the registered drag items with underlying drop list ref. */\n  private _syncItemsWithRef() {\n    this._dropListRef.withItems(this.getSortedItems().map(item => item._dragRef));\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Directive,\n  InjectionToken,\n  Input,\n  OnDestroy,\n  TemplateRef,\n  booleanAttribute,\n  inject,\n} from '@angular/core';\nimport {CDK_DRAG_PARENT} from '../drag-parent';\n\n/**\n * Injection token that can be used to reference instances of `CdkDragPreview`. It serves as\n * alternative token to the actual `CdkDragPreview` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nexport const CDK_DRAG_PREVIEW = new InjectionToken<CdkDragPreview>('CdkDragPreview');\n\n/**\n * Element that will be used as a template for the preview\n * of a CdkDrag when it is being dragged.\n */\n@Directive({\n  selector: 'ng-template[cdkDragPreview]',\n  standalone: true,\n  providers: [{provide: CDK_DRAG_PREVIEW, useExisting: CdkDragPreview}],\n})\nexport class CdkDragPreview<T = any> implements OnD<PERSON>roy {\n  private _drag = inject(CDK_DRAG_PARENT, {optional: true});\n\n  /** Context data to be added to the preview template instance. */\n  @Input() data: T;\n\n  /** Whether the preview should preserve the same size as the item that is being dragged. */\n  @Input({transform: booleanAttribute}) matchSize: boolean = false;\n\n  constructor(public templateRef: TemplateRef<T>) {\n    this._drag?._setPreviewTemplate(this);\n  }\n\n  ngOnDestroy(): void {\n    this._drag?._resetPreviewTemplate(this);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, TemplateRef, Input, InjectionToken, inject, OnDestroy} from '@angular/core';\nimport {CDK_DRAG_PARENT} from '../drag-parent';\n\n/**\n * Injection token that can be used to reference instances of `CdkDragPlaceholder`. It serves as\n * alternative token to the actual `CdkDragPlaceholder` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nexport const CDK_DRAG_PLACEHOLDER = new InjectionToken<CdkDragPlaceholder>('CdkDragPlaceholder');\n\n/**\n * Element that will be used as a template for the placeholder of a CdkDrag when\n * it is being dragged. The placeholder is displayed in place of the element being dragged.\n */\n@Directive({\n  selector: 'ng-template[cdkDragPlaceholder]',\n  standalone: true,\n  providers: [{provide: CDK_DRAG_PLACEHOLDER, useExisting: CdkDragPlaceholder}],\n})\nexport class CdkDragPlaceholder<T = any> implements OnDestroy {\n  private _drag = inject(CDK_DRAG_PARENT, {optional: true});\n\n  /** Context data to be added to the placeholder template instance. */\n  @Input() data: T;\n\n  constructor(public templateRef: TemplateRef<T>) {\n    this._drag?._setPlaceholderTemplate(this);\n  }\n\n  ngOnDestroy(): void {\n    this._drag?._resetPlaceholderTemplate(this);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {CdkScrollableModule} from '@angular/cdk/scrolling';\nimport {CdkDropList} from './directives/drop-list';\nimport {CdkDropListGroup} from './directives/drop-list-group';\nimport {CdkDrag} from './directives/drag';\nimport {CdkDragHandle} from './directives/drag-handle';\nimport {CdkDragPreview} from './directives/drag-preview';\nimport {CdkDragPlaceholder} from './directives/drag-placeholder';\nimport {DragDrop} from './drag-drop';\n\nconst DRAG_DROP_DIRECTIVES = [\n  CdkDropList,\n  CdkDropListGroup,\n  CdkDrag,\n  CdkDragHandle,\n  CdkDragPreview,\n  CdkDragPlaceholder,\n];\n\n@NgModule({\n  imports: DRAG_DROP_DIRECTIVES,\n  exports: [CdkScrollableModule, ...DRAG_DROP_DIRECTIVES],\n  providers: [DragDrop],\n})\nexport class DragDropModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["activeCapturingEventOptions", "clamp", "i2.DragDropRegistry", "i1", "i2.DragDrop", "i1.DragDrop", "i2", "i3"], "mappings": ";;;;;;;;;;;;AAkBA;;;;AAIG;SACa,YAAY,CAC1B,IAAyB,EACzB,MAA8B,EAC9B,mBAAiC,EAAA;AAEjC,IAAA,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE;AACtB,QAAA,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;AAC9B,YAAA,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;YAE1B,IAAI,KAAK,EAAE;gBACT,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,EAAE,mBAAmB,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,WAAW,GAAG,EAAE,CAAC,CAAC;aAChF;iBAAM;AACL,gBAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;aAC1B;SACF;KACF;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;AAKG;AACa,SAAA,4BAA4B,CAAC,OAAoB,EAAE,MAAe,EAAA;IAChF,MAAM,UAAU,GAAG,MAAM,GAAG,EAAE,GAAG,MAAM,CAAC;AAExC,IAAA,YAAY,CAAC,OAAO,CAAC,KAAK,EAAE;QAC1B,cAAc,EAAE,MAAM,GAAG,EAAE,GAAG,MAAM;QACpC,mBAAmB,EAAE,MAAM,GAAG,EAAE,GAAG,MAAM;QACzC,6BAA6B,EAAE,MAAM,GAAG,EAAE,GAAG,aAAa;AAC1D,QAAA,aAAa,EAAE,UAAU;AACzB,QAAA,iBAAiB,EAAE,UAAU;AAC7B,QAAA,qBAAqB,EAAE,UAAU;AACjC,QAAA,kBAAkB,EAAE,UAAU;AAC/B,KAAA,CAAC,CAAC;AACL,CAAC;AAED;;;;;;AAMG;SACa,gBAAgB,CAC9B,OAAoB,EACpB,MAAe,EACf,mBAAiC,EAAA;AAEjC,IAAA,YAAY,CACV,OAAO,CAAC,KAAK,EACb;QACE,QAAQ,EAAE,MAAM,GAAG,EAAE,GAAG,OAAO;QAC/B,GAAG,EAAE,MAAM,GAAG,EAAE,GAAG,GAAG;QACtB,OAAO,EAAE,MAAM,GAAG,EAAE,GAAG,GAAG;QAC1B,IAAI,EAAE,MAAM,GAAG,EAAE,GAAG,QAAQ;KAC7B,EACD,mBAAmB,CACpB,CAAC;AACJ,CAAC;AAED;;;AAGG;AACa,SAAA,iBAAiB,CAAC,SAAiB,EAAE,gBAAyB,EAAA;AAC5E,IAAA,OAAO,gBAAgB,IAAI,gBAAgB,IAAI,MAAM;AACnD,UAAE,SAAS,GAAG,GAAG,GAAG,gBAAgB;UAClC,SAAS,CAAC;AAChB,CAAC;AAED;;;;AAIG;AACa,SAAA,gBAAgB,CAAC,MAAmB,EAAE,UAAmB,EAAA;IACvE,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,UAAU,CAAC,KAAK,CAAA,EAAA,CAAI,CAAC;IAC7C,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,UAAU,CAAC,MAAM,CAAA,EAAA,CAAI,CAAC;AAC/C,IAAA,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG,YAAY,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;AACzE,CAAC;AAED;;;;AAIG;AACa,SAAA,YAAY,CAAC,CAAS,EAAE,CAAS,EAAA;;;AAG/C,IAAA,OAAO,CAAe,YAAA,EAAA,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAO,IAAA,EAAA,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC;AAClE;;AC7GA;AACM,SAAU,oBAAoB,CAAC,OAAgB,EAAA;AACnD,IAAA,MAAM,IAAI,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;;;;;IAM7C,OAAO;QACL,GAAG,EAAE,IAAI,CAAC,GAAG;QACb,KAAK,EAAE,IAAI,CAAC,KAAK;QACjB,MAAM,EAAE,IAAI,CAAC,MAAM;QACnB,IAAI,EAAE,IAAI,CAAC,IAAI;QACf,KAAK,EAAE,IAAI,CAAC,KAAK;QACjB,MAAM,EAAE,IAAI,CAAC,MAAM;QACnB,CAAC,EAAE,IAAI,CAAC,CAAC;QACT,CAAC,EAAE,IAAI,CAAC,CAAC;KACC,CAAC;AACf,CAAC;AAED;;;;;AAKG;SACa,kBAAkB,CAAC,UAAmB,EAAE,CAAS,EAAE,CAAS,EAAA;IAC1E,MAAM,EAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAC,GAAG,UAAU,CAAC;AAC9C,IAAA,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC;AAC5D,CAAC;AAED;;;;;AAKG;SACa,aAAa,CAC3B,OAOC,EACD,GAAW,EACX,IAAY,EAAA;AAEZ,IAAA,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC;IACnB,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC;AAE9C,IAAA,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC;IACrB,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC;AAC/C,CAAC;AAED;;;;;;AAMG;AACG,SAAU,oBAAoB,CAClC,IAAa,EACb,SAAiB,EACjB,QAAgB,EAChB,QAAgB,EAAA;AAEhB,IAAA,MAAM,EAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAC,GAAG,IAAI,CAAC;AACvD,IAAA,MAAM,UAAU,GAAG,KAAK,GAAG,SAAS,CAAC;AACrC,IAAA,MAAM,UAAU,GAAG,MAAM,GAAG,SAAS,CAAC;AAEtC,IAAA,QACE,QAAQ,GAAG,GAAG,GAAG,UAAU;QAC3B,QAAQ,GAAG,MAAM,GAAG,UAAU;QAC9B,QAAQ,GAAG,IAAI,GAAG,UAAU;AAC5B,QAAA,QAAQ,GAAG,KAAK,GAAG,UAAU,EAC7B;AACJ;;ACtEA;MACa,qBAAqB,CAAA;AAUhC,IAAA,WAAA,CAAoB,SAAmB,EAAA;QAAnB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;;AAR9B,QAAA,IAAA,CAAA,SAAS,GAAG,IAAI,GAAG,EAMzB,CAAC;KAEuC;;IAG3C,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;KACxB;;AAGD,IAAA,KAAK,CAAC,QAAgC,EAAA;QACpC,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE;AACjC,YAAA,cAAc,EAAE,IAAI,CAAC,yBAAyB,EAAE;AACjD,SAAA,CAAC,CAAC;AAEH,QAAA,QAAQ,CAAC,OAAO,CAAC,OAAO,IAAG;AACzB,YAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE;AAC1B,gBAAA,cAAc,EAAE,EAAC,GAAG,EAAE,OAAO,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,CAAC,UAAU,EAAC;AAClE,gBAAA,UAAU,EAAE,oBAAoB,CAAC,OAAO,CAAC;AAC1C,aAAA,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;KACJ;;AAGD,IAAA,YAAY,CAAC,KAAY,EAAA;AACvB,QAAA,MAAM,MAAM,GAAG,eAAe,CAAyB,KAAK,CAAE,CAAC;QAC/D,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAElD,IAAI,CAAC,cAAc,EAAE;AACnB,YAAA,OAAO,IAAI,CAAC;SACb;AAED,QAAA,MAAM,cAAc,GAAG,cAAc,CAAC,cAAc,CAAC;AACrD,QAAA,IAAI,MAAc,CAAC;AACnB,QAAA,IAAI,OAAe,CAAC;AAEpB,QAAA,IAAI,MAAM,KAAK,IAAI,CAAC,SAAS,EAAE;AAC7B,YAAA,MAAM,sBAAsB,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;AAChE,YAAA,MAAM,GAAG,sBAAsB,CAAC,GAAG,CAAC;AACpC,YAAA,OAAO,GAAG,sBAAsB,CAAC,IAAI,CAAC;SACvC;aAAM;AACL,YAAA,MAAM,GAAI,MAAsB,CAAC,SAAS,CAAC;AAC3C,YAAA,OAAO,GAAI,MAAsB,CAAC,UAAU,CAAC;SAC9C;AAED,QAAA,MAAM,aAAa,GAAG,cAAc,CAAC,GAAG,GAAG,MAAM,CAAC;AAClD,QAAA,MAAM,cAAc,GAAG,cAAc,CAAC,IAAI,GAAG,OAAO,CAAC;;;QAIrD,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,IAAI,KAAI;AACxC,YAAA,IAAI,QAAQ,CAAC,UAAU,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;gBACnE,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;aACnE;AACH,SAAC,CAAC,CAAC;AAEH,QAAA,cAAc,CAAC,GAAG,GAAG,MAAM,CAAC;AAC5B,QAAA,cAAc,CAAC,IAAI,GAAG,OAAO,CAAC;QAE9B,OAAO,EAAC,GAAG,EAAE,aAAa,EAAE,IAAI,EAAE,cAAc,EAAC,CAAC;KACnD;AAED;;;;;AAKG;IACH,yBAAyB,GAAA;AACvB,QAAA,OAAO,EAAC,GAAG,EAAE,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,OAAO,EAAC,CAAC;KACpD;AACF;;AC1FD;AACM,SAAU,aAAa,CAAC,IAAiB,EAAA;IAC7C,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAgB,CAAC;IAClD,MAAM,iBAAiB,GAAG,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;IACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;;AAG7C,IAAA,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;AAE5B,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACjD,iBAAiB,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;KAC5C;AAED,IAAA,IAAI,QAAQ,KAAK,QAAQ,EAAE;AACzB,QAAA,kBAAkB,CAAC,IAAyB,EAAE,KAA0B,CAAC,CAAC;KAC3E;AAAM,SAAA,IAAI,QAAQ,KAAK,OAAO,IAAI,QAAQ,KAAK,QAAQ,IAAI,QAAQ,KAAK,UAAU,EAAE;AACnF,QAAA,iBAAiB,CAAC,IAAwB,EAAE,KAAyB,CAAC,CAAC;KACxE;IAED,YAAY,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,kBAAkB,CAAC,CAAC;IACxD,YAAY,CAAC,yBAAyB,EAAE,IAAI,EAAE,KAAK,EAAE,iBAAiB,CAAC,CAAC;AACxE,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED;AACA,SAAS,YAAY,CACnB,QAAgB,EAChB,IAAiB,EACjB,KAAkB,EAClB,QAAuC,EAAA;IAEvC,MAAM,kBAAkB,GAAG,IAAI,CAAC,gBAAgB,CAAI,QAAQ,CAAC,CAAC;AAE9D,IAAA,IAAI,kBAAkB,CAAC,MAAM,EAAE;QAC7B,MAAM,aAAa,GAAG,KAAK,CAAC,gBAAgB,CAAI,QAAQ,CAAC,CAAC;AAE1D,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,kBAAkB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClD,QAAQ,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;SACnD;KACF;AACH,CAAC;AAED;AACA,IAAI,aAAa,GAAG,CAAC,CAAC;AAEtB;AACA,SAAS,iBAAiB,CACxB,MAAiC,EACjC,KAA4D,EAAA;;AAG5D,IAAA,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE;AACzB,QAAA,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;KAC5B;;;;IAKD,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,IAAI,KAAK,CAAC,IAAI,EAAE;QACxC,KAAK,CAAC,IAAI,GAAG,CAAa,UAAA,EAAA,KAAK,CAAC,IAAI,CAAI,CAAA,EAAA,aAAa,EAAE,CAAA,CAAE,CAAC;KAC3D;AACH,CAAC;AAED;AACA,SAAS,kBAAkB,CAAC,MAAyB,EAAE,KAAwB,EAAA;IAC7E,MAAM,OAAO,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAEvC,IAAI,OAAO,EAAE;;;AAGX,QAAA,IAAI;YACF,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SACjC;QAAC,MAAM,GAAE;KACX;AACH;;ACxEA;;;AAGG;AACa,SAAA,WAAW,CAAC,OAA6B,EAAE,SAAmB,EAAA;AAC5E,IAAA,MAAM,SAAS,GAAW,OAAO,CAAC,SAAS,CAAC;AAE5C,IAAA,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,SAAS,CAAC,YAAY,EAAE;AAC9E,QAAA,OAAO,SAAS,CAAC,CAAC,CAAgB,CAAC;KACpC;IAED,MAAM,OAAO,GAAG,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC/C,IAAA,SAAS,CAAC,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;AACrD,IAAA,OAAO,OAAO,CAAC;AACjB;;AChBA;AACA,SAAS,qBAAqB,CAAC,KAAa,EAAA;;IAE1C,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AACrE,IAAA,OAAO,UAAU,CAAC,KAAK,CAAC,GAAG,UAAU,CAAC;AACxC,CAAC;AAED;AACM,SAAU,kCAAkC,CAAC,OAAoB,EAAA;AACrE,IAAA,MAAM,aAAa,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAChD,MAAM,sBAAsB,GAAG,qBAAqB,CAAC,aAAa,EAAE,qBAAqB,CAAC,CAAC;AAC3F,IAAA,MAAM,QAAQ,GAAG,sBAAsB,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,WAAW,IAAI,IAAI,KAAK,KAAK,CAAC,CAAC;;IAG7F,IAAI,CAAC,QAAQ,EAAE;AACb,QAAA,OAAO,CAAC,CAAC;KACV;;;IAID,MAAM,aAAa,GAAG,sBAAsB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC/D,MAAM,YAAY,GAAG,qBAAqB,CAAC,aAAa,EAAE,qBAAqB,CAAC,CAAC;IACjF,MAAM,SAAS,GAAG,qBAAqB,CAAC,aAAa,EAAE,kBAAkB,CAAC,CAAC;AAE3E,IAAA,QACE,qBAAqB,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;AAClD,QAAA,qBAAqB,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,EAC/C;AACJ,CAAC;AAED;AACA,SAAS,qBAAqB,CAAC,aAAkC,EAAE,IAAY,EAAA;IAC7E,MAAM,KAAK,GAAG,aAAa,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;AACnD,IAAA,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;AACnD;;ACdA;AACA,MAAM,mBAAmB,GAAG,IAAI,GAAG,CAAC;;IAElC,UAAU;AACX,CAAA,CAAC,CAAC;MAEU,UAAU,CAAA;AAOrB,IAAA,WAAA,CACU,SAAmB,EACnB,YAAyB,EACzB,UAAqB,EACrB,eAAwB,EACxB,gBAA4C,EAC5C,aAAuC,EACvC,qBAGP,EACO,iBAAgC,EAChC,OAAe,EAAA;QAXf,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QACnB,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAa;QACzB,IAAU,CAAA,UAAA,GAAV,UAAU,CAAW;QACrB,IAAe,CAAA,eAAA,GAAf,eAAe,CAAS;QACxB,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAA4B;QAC5C,IAAa,CAAA,aAAA,GAAb,aAAa,CAA0B;QACvC,IAAqB,CAAA,qBAAA,GAArB,qBAAqB,CAG5B;QACO,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAe;QAChC,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;KACrB;AAEJ,IAAA,MAAM,CAAC,MAAmB,EAAA;AACxB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;AACtC,QAAA,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;;;AAIlC,QAAA,IAAI,aAAa,IAAI,IAAI,CAAC,QAAQ,EAAE;AAClC,YAAA,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;SAChC;KACF;IAED,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,oBAAoB,EAAE,OAAO,EAAE,CAAC;QACrC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,oBAAoB,GAAG,IAAK,CAAC;KACnD;AAED,IAAA,YAAY,CAAC,KAAa,EAAA;QACxB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC;KACvC;IAED,qBAAqB,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,qBAAqB,EAAE,CAAC;KAC9C;AAED,IAAA,QAAQ,CAAC,SAAiB,EAAA;QACxB,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;KACxC;IAED,qBAAqB,GAAA;AACnB,QAAA,OAAO,kCAAkC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KAC1D;IAED,gBAAgB,CAAC,IAAY,EAAE,OAA2C,EAAA;QACxE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAC/C;IAED,mBAAmB,CAAC,IAAY,EAAE,OAA2C,EAAA;QAC3E,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAClD;IAEO,cAAc,GAAA;AACpB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC;AAC5C,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;AACxC,QAAA,MAAM,eAAe,GAAG,aAAa,GAAG,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC;AACtE,QAAA,IAAI,OAAoB,CAAC;AAEzB,QAAA,IAAI,eAAe,IAAI,aAAa,EAAE;;;AAGpC,YAAA,MAAM,QAAQ,GAAG,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AACvE,YAAA,MAAM,OAAO,GAAG,aAAa,CAAC,aAAa,CAAC,kBAAkB,CAC5D,eAAe,EACf,aAAa,CAAC,OAAO,CACtB,CAAC;YACF,OAAO,CAAC,aAAa,EAAE,CAAC;YACxB,OAAO,GAAG,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AAC/C,YAAA,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC;AACpC,YAAA,IAAI,aAAa,CAAC,SAAS,EAAE;AAC3B,gBAAA,gBAAgB,CAAC,OAAO,EAAE,QAAS,CAAC,CAAC;aACtC;iBAAM;AACL,gBAAA,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,YAAY,CACpC,IAAI,CAAC,qBAAqB,CAAC,CAAC,EAC5B,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAC7B,CAAC;aACH;SACF;aAAM;AACL,YAAA,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAC3C,YAAA,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,eAAgB,CAAC,CAAC;AAEjD,YAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBAC1B,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC;aAClD;SACF;AAED,QAAA,YAAY,CACV,OAAO,CAAC,KAAK,EACb;;;AAGE,YAAA,gBAAgB,EAAE,MAAM;;AAExB,YAAA,QAAQ,EAAE,GAAG;AACb,YAAA,UAAU,EAAE,OAAO;AACnB,YAAA,KAAK,EAAE,GAAG;AACV,YAAA,MAAM,EAAE,GAAG;AACX,YAAA,SAAS,EAAE,IAAI,CAAC,OAAO,GAAG,EAAE;SAC7B,EACD,mBAAmB,CACpB,CAAC;AAEF,QAAA,4BAA4B,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAC7C,QAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;AAC1C,QAAA,OAAO,CAAC,YAAY,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC1C,OAAO,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAE7C,IAAI,YAAY,EAAE;AAChB,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;AAC/B,gBAAA,YAAY,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;aACrE;iBAAM;AACL,gBAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;aACrC;SACF;AAED,QAAA,OAAO,OAAO,CAAC;KAChB;AACF;;AC3GD;AACA,MAAM,2BAA2B,GAAG,+BAA+B,CAAC,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC;AAErF;AACA,MAAM,0BAA0B,GAAG,+BAA+B,CAAC,EAAC,OAAO,EAAE,KAAK,EAAC,CAAC,CAAC;AAErF;AACA,MAAMA,6BAA2B,GAAG,+BAA+B,CAAC;AAClE,IAAA,OAAO,EAAE,KAAK;AACd,IAAA,OAAO,EAAE,IAAI;AACd,CAAA,CAAC,CAAC;AAEH;;;;;AAKG;AACH,MAAM,uBAAuB,GAAG,GAAG,CAAC;AAkBpC;AACA,MAAM,uBAAuB,GAAG,IAAI,GAAG,CAAC;;IAEtC,UAAU;AACX,CAAA,CAAC,CAAC;AAgBH;;AAEG;MACU,OAAO,CAAA;;AA0KlB,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,EAAE,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;KAClF;IACD,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,EAAE;AAC5B,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACvB,IAAI,CAAC,6BAA6B,EAAE,CAAC;AACrC,YAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,IAAI,4BAA4B,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;SAC9E;KACF;IAmED,WACE,CAAA,OAA8C,EACtC,OAAsB,EACtB,SAAmB,EACnB,OAAe,EACf,cAA6B,EAC7B,iBAAyD,EAAA;QAJzD,IAAO,CAAA,OAAA,GAAP,OAAO,CAAe;QACtB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QACnB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QACf,IAAc,CAAA,cAAA,GAAd,cAAc,CAAe;QAC7B,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAwC;AAnOnE;;;;;AAKG;QACK,IAAiB,CAAA,iBAAA,GAAU,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,CAAC;;QAGxC,IAAgB,CAAA,gBAAA,GAAU,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,CAAC;AAK/C;;;AAGG;QACK,IAAmB,CAAA,mBAAA,GAAG,KAAK,CAAC;;AAenB,QAAA,IAAA,CAAA,WAAW,GAAG,IAAI,OAAO,EAMtC,CAAC;;AA6BG,QAAA,IAAA,CAAA,wBAAwB,GAAG,YAAY,CAAC,KAAK,CAAC;;AAG9C,QAAA,IAAA,CAAA,sBAAsB,GAAG,YAAY,CAAC,KAAK,CAAC;;AAG5C,QAAA,IAAA,CAAA,mBAAmB,GAAG,YAAY,CAAC,KAAK,CAAC;;AAGzC,QAAA,IAAA,CAAA,mBAAmB,GAAG,YAAY,CAAC,KAAK,CAAC;;QAazC,IAAgB,CAAA,gBAAA,GAAuB,IAAI,CAAC;;QAG5C,IAA0B,CAAA,0BAAA,GAAG,IAAI,CAAC;;QAkBlC,IAAQ,CAAA,QAAA,GAAkB,EAAE,CAAC;;AAG7B,QAAA,IAAA,CAAA,gBAAgB,GAAG,IAAI,GAAG,EAAe,CAAC;;QAM1C,IAAU,CAAA,UAAA,GAAc,KAAK,CAAC;AAetC;;;AAGG;QACH,IAAc,CAAA,cAAA,GAA4C,CAAC,CAAC;QAgBpD,IAAS,CAAA,SAAA,GAAG,KAAK,CAAC;;AAGjB,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,OAAO,EAAQ,CAAC;;AAGpC,QAAA,IAAA,CAAA,OAAO,GAAG,IAAI,OAAO,EAAqD,CAAC;;AAG3E,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,OAAO,EAAqD,CAAC;;AAG5E,QAAA,IAAA,CAAA,KAAK,GAAG,IAAI,OAAO,EAKxB,CAAC;;AAGI,QAAA,IAAA,CAAA,OAAO,GAAG,IAAI,OAAO,EAAiE,CAAC;;AAGvF,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,OAAO,EAA2C,CAAC;;AAGhE,QAAA,IAAA,CAAA,OAAO,GAAG,IAAI,OAAO,EAU1B,CAAC;AAEL;;;AAGG;AACM,QAAA,IAAA,CAAA,KAAK,GAMT,IAAI,CAAC,WAAW,CAAC;;AAoSd,QAAA,IAAA,CAAA,YAAY,GAAG,CAAC,KAA8B,KAAI;AACxD,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;;AAG1B,YAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;gBACxB,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAElD,gBAAA,IAAI,YAAY,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAC9E,oBAAA,IAAI,CAAC,uBAAuB,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;iBACnD;aACF;AAAM,iBAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBACzB,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;aACxD;AACH,SAAC,CAAC;;AAGM,QAAA,IAAA,CAAA,YAAY,GAAG,CAAC,KAA8B,KAAI;YACxD,MAAM,eAAe,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC;AAE9D,YAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;AAC7B,gBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;AAC7E,gBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;gBAC7E,MAAM,eAAe,GAAG,SAAS,GAAG,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC;;;;;gBAMjF,IAAI,eAAe,EAAE;AACnB,oBAAA,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;AAC1F,oBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC;oBAEtC,IAAI,CAAC,cAAc,EAAE;AACnB,wBAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;wBAC7B,OAAO;qBACR;;;;AAKD,oBAAA,IAAI,CAAC,SAAS,KAAK,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,EAAE;;;AAGvE,wBAAA,IAAI,KAAK,CAAC,UAAU,EAAE;4BACpB,KAAK,CAAC,cAAc,EAAE,CAAC;yBACxB;AACD,wBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;AAChC,wBAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;qBACxD;iBACF;gBAED,OAAO;aACR;;;;AAKD,YAAA,IAAI,KAAK,CAAC,UAAU,EAAE;gBACpB,KAAK,CAAC,cAAc,EAAE,CAAC;aACxB;YAED,MAAM,0BAA0B,GAAG,IAAI,CAAC,8BAA8B,CAAC,eAAe,CAAC,CAAC;AACxF,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,YAAA,IAAI,CAAC,yBAAyB,GAAG,eAAe,CAAC;AACjD,YAAA,IAAI,CAAC,4BAA4B,CAAC,0BAA0B,CAAC,CAAC;AAE9D,YAAA,IAAI,IAAI,CAAC,cAAc,EAAE;AACvB,gBAAA,IAAI,CAAC,0BAA0B,CAAC,0BAA0B,EAAE,eAAe,CAAC,CAAC;aAC9E;iBAAM;;;AAGL,gBAAA,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,eAAgB,GAAG,IAAI,CAAC,qBAAqB,CAAC;AAC3F,gBAAA,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;AAC9C,gBAAA,eAAe,CAAC,CAAC,GAAG,0BAA0B,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;AACvF,gBAAA,eAAe,CAAC,CAAC,GAAG,0BAA0B,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;gBACvF,IAAI,CAAC,0BAA0B,CAAC,eAAe,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;aACvE;;;;YAKD,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,EAAE;AACrC,gBAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAK;AACpB,oBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;AACpB,wBAAA,MAAM,EAAE,IAAI;AACZ,wBAAA,eAAe,EAAE,0BAA0B;wBAC3C,KAAK;AACL,wBAAA,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,0BAA0B,CAAC;wBAC3D,KAAK,EAAE,IAAI,CAAC,sBAAsB;AACnC,qBAAA,CAAC,CAAC;AACL,iBAAC,CAAC,CAAC;aACJ;AACH,SAAC,CAAC;;AAGM,QAAA,IAAA,CAAA,UAAU,GAAG,CAAC,KAA8B,KAAI;AACtD,YAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAC/B,SAAC,CAAC;;AAmwBM,QAAA,IAAA,CAAA,gBAAgB,GAAG,CAAC,KAAgB,KAAI;AAC9C,YAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;gBACxB,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAElD,gBAAA,IAAI,YAAY,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;oBAC9E,KAAK,CAAC,cAAc,EAAE,CAAC;iBACxB;aACF;AAAM,iBAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;;;gBAGzB,KAAK,CAAC,cAAc,EAAE,CAAC;aACxB;AACH,SAAC,CAAC;AA1nCA,QAAA,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,CAAC;QACxE,IAAI,CAAC,gBAAgB,GAAG,IAAI,qBAAqB,CAAC,SAAS,CAAC,CAAC;AAC7D,QAAA,iBAAiB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;KAC1C;AAED;;;AAGG;IACH,qBAAqB,GAAA;QACnB,OAAO,IAAI,CAAC,YAAY,CAAC;KAC1B;;IAGD,cAAc,GAAA;QACZ,OAAO,IAAI,CAAC,YAAY,CAAC;KAC1B;AAED;;;AAGG;IACH,iBAAiB,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,UAAU,EAAE,GAAG,IAAI,CAAC,qBAAqB,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;KACjF;;AAGD,IAAA,WAAW,CAAC,OAAkD,EAAA;AAC5D,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;AAC7D,QAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,IAAI,4BAA4B,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QACrF,IAAI,CAAC,6BAA6B,EAAE,CAAC;;;;;AAMrC,QAAA,MAAM,eAAe,GAAG,IAAI,GAAG,EAAe,CAAC;AAC/C,QAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,MAAM,IAAG;AACrC,YAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;AACtC,gBAAA,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;aAC7B;AACH,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;AACxC,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;AAGG;AACH,IAAA,mBAAmB,CAAC,QAAoC,EAAA;AACtD,QAAA,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC;AACjC,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;AAGG;AACH,IAAA,uBAAuB,CAAC,QAAmC,EAAA;AACzD,QAAA,IAAI,CAAC,oBAAoB,GAAG,QAAQ,CAAC;AACrC,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;AAIG;AACH,IAAA,eAAe,CAAC,WAAkD,EAAA;AAChE,QAAA,MAAM,OAAO,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC;AAE3C,QAAA,IAAI,OAAO,KAAK,IAAI,CAAC,YAAY,EAAE;AACjC,YAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,gBAAA,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;aACrD;AAED,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;gBAClC,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,0BAA0B,CAAC,CAAC;gBACrF,OAAO,CAAC,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,2BAA2B,CAAC,CAAC;gBACvF,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,EAAE,0BAA0B,CAAC,CAAC;AAC3F,aAAC,CAAC,CAAC;AACH,YAAA,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;AACnC,YAAA,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC;SAC7B;QAED,IAAI,OAAO,UAAU,KAAK,WAAW,IAAI,IAAI,CAAC,YAAY,YAAY,UAAU,EAAE;YAChF,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC;SAC3D;AAED,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;AAEG;AACH,IAAA,mBAAmB,CAAC,eAA6D,EAAA;AAC/E,QAAA,IAAI,CAAC,gBAAgB,GAAG,eAAe,GAAG,aAAa,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC;AAChF,QAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,eAAe,EAAE;AACnB,YAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,cAAc;iBAC3C,MAAM,CAAC,EAAE,CAAC;iBACV,SAAS,CAAC,MAAM,IAAI,CAAC,8BAA8B,EAAE,CAAC,CAAC;SAC3D;AACD,QAAA,OAAO,IAAI,CAAC;KACb;;AAGD,IAAA,UAAU,CAAC,MAA+B,EAAA;AACxC,QAAA,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC;AAC7B,QAAA,OAAO,IAAI,CAAC;KACb;;IAGD,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;;;AAIpD,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;;;AAGrB,YAAA,IAAI,CAAC,YAAY,EAAE,MAAM,EAAE,CAAC;SAC7B;AAED,QAAA,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC;QACvB,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,mBAAmB,EAAE,CAAC;AAC3B,QAAA,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC5C,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxB,QAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;AAC9B,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;AACxB,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;AACzB,QAAA,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;AACxB,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;AACxB,QAAA,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;AAC5B,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;AACnB,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;AAC9B,QAAA,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;AAChC,QAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;AACvC,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;AAC9B,QAAA,IAAI,CAAC,gBAAgB;AACnB,YAAA,IAAI,CAAC,YAAY;AACjB,gBAAA,IAAI,CAAC,gBAAgB;AACrB,oBAAA,IAAI,CAAC,oBAAoB;AACzB,wBAAA,IAAI,CAAC,gBAAgB;AACrB,4BAAA,IAAI,CAAC,OAAO;AACZ,gCAAA,IAAI,CAAC,cAAc;AACjB,oCAAA,IAAK,CAAC;KACX;;IAGD,UAAU,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;KAC5E;;IAGD,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,IAAI,EAAE,CAAC;AACjE,QAAA,IAAI,CAAC,gBAAgB,GAAG,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,CAAC;AACrC,QAAA,IAAI,CAAC,iBAAiB,GAAG,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,CAAC;KACvC;AAED;;;AAGG;AACH,IAAA,aAAa,CAAC,MAAmB,EAAA;QAC/B,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;AAC5E,YAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AAClC,YAAA,4BAA4B,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;SAC5C;KACF;AAED;;;AAGG;AACH,IAAA,YAAY,CAAC,MAAmB,EAAA;QAC9B,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;AACrC,YAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACrC,YAAA,4BAA4B,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;SACrD;KACF;;AAGD,IAAA,aAAa,CAAC,SAAoB,EAAA;AAChC,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;AAC5B,QAAA,OAAO,IAAI,CAAC;KACb;;AAGD,IAAA,kBAAkB,CAAC,SAAsB,EAAA;AACvC,QAAA,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;KACjC;AAED;;AAEG;IACH,mBAAmB,GAAA;AACjB,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC;AACpF,QAAA,OAAO,EAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAC,CAAC;KACvC;AAED;;;AAGG;AACH,IAAA,mBAAmB,CAAC,KAAY,EAAA;AAC9B,QAAA,IAAI,CAAC,gBAAgB,GAAG,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,CAAC;QACrC,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QACnC,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAEnC,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;SACnD;AAED,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;AAGG;AACH,IAAA,oBAAoB,CAAC,KAAuB,EAAA;AAC1C,QAAA,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;AAC/B,QAAA,OAAO,IAAI,CAAC;KACb;;IAGD,4BAA4B,GAAA;AAC1B,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,yBAAyB,CAAC;AAEhD,QAAA,IAAI,QAAQ,IAAI,IAAI,CAAC,cAAc,EAAE;AACnC,YAAA,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,8BAA8B,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAC;SAC1F;KACF;;IAGO,gBAAgB,GAAA;AACtB,QAAA,IAAI,CAAC,wBAAwB,CAAC,WAAW,EAAE,CAAC;AAC5C,QAAA,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,CAAC;AAC1C,QAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;AACvC,QAAA,IAAI,CAAC,cAAc,EAAE,EAAE,mBAAmB,CACxC,aAAa,EACb,oBAAoB,EACpBA,6BAA2B,CAC5B,CAAC;KACH;;IAGO,eAAe,GAAA;AACrB,QAAA,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;KACtB;;IAGO,mBAAmB,GAAA;AACzB,QAAA,IAAI,CAAC,YAAY,EAAE,MAAM,EAAE,CAAC;AAC5B,QAAA,IAAI,CAAC,eAAe,EAAE,OAAO,EAAE,CAAC;QAChC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,GAAG,IAAK,CAAC;KAClD;AAsGD;;;AAGG;AACK,IAAA,gBAAgB,CAAC,KAA8B,EAAA;;;;;QAKrD,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;YAC5C,OAAO;SACR;QAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxB,QAAA,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAI,CAAC,6BAA6B,EAAE,CAAC;AAErC,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AAChB,YAAA,IAAI,CAAC,YAAY,CAAC,KAAiC,CAAC,uBAAuB;gBAC1E,IAAI,CAAC,wBAAwB,CAAC;SACjC;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC7B,OAAO;SACR;AAED,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;AAE1C,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE;;AAEvB,YAAA,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;AACrC,YAAA,IAAI,CAAC,4BAA4B,EAAE,CAAC,IAAI,CAAC,MAAK;AAC5C,gBAAA,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;gBAClC,IAAI,CAAC,wBAAwB,EAAE,CAAC;AAChC,gBAAA,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AAC5C,aAAC,CAAC,CAAC;SACJ;aAAM;;;;YAIL,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;YACnD,MAAM,eAAe,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC;YAC9D,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;AACnD,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAK;AACpB,gBAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;AACd,oBAAA,MAAM,EAAE,IAAI;AACZ,oBAAA,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC;AAChD,oBAAA,SAAS,EAAE,eAAe;oBAC1B,KAAK;AACN,iBAAA,CAAC,CAAC;AACL,aAAC,CAAC,CAAC;YACH,IAAI,CAAC,wBAAwB,EAAE,CAAC;AAChC,YAAA,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;SAC3C;KACF;;AAGO,IAAA,kBAAkB,CAAC,KAA8B,EAAA;AACvD,QAAA,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE;AACvB,YAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;SACvC;QAED,IAAI,CAAC,6BAA6B,EAAE,CAAC;;AAGrC,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;AACzC,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;QAE1C,IAAI,UAAU,EAAE;;;AAGd,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;gBAClC,UAAU,CAAC,gBAAgB,CACzB,aAAa,EACb,oBAAoB,EACpBA,6BAA2B,CAC5B,CAAC;AACJ,aAAC,CAAC,CAAC;SACJ;QAED,IAAI,aAAa,EAAE;AACjB,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC;AAClC,YAAA,MAAM,MAAM,GAAG,OAAO,CAAC,UAAyB,CAAC;AACjD,YAAA,MAAM,WAAW,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC,CAAC;YAC3E,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC;;AAGjF,YAAA,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;;;YAIrC,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,KAAK,CAAC,SAAS,IAAI,EAAE,CAAC;;;YAIvD,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU,CAC5B,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,eAAgB,EACrB,IAAI,CAAC,gBAAgB,IAAI,IAAI,EAC7B,IAAI,CAAC,YAAY,IAAI,IAAI,EACzB,IAAI,CAAC,qBAAqB,EAC1B,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,CAC5B,CAAC;AACF,YAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC;;;;AAKzE,YAAA,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE,uBAAuB,CAAC,CAAC;AAC1D,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC;AAC3E,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;YACzC,aAAa,CAAC,KAAK,EAAE,CAAC;AACtB,YAAA,IAAI,CAAC,iBAAiB,GAAG,aAAa,CAAC;YACvC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;SACvD;aAAM;AACL,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;YACzC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,aAAa,GAAG,SAAU,CAAC;SAC1D;;;AAID,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC,oBAAoB,EAAE,GAAG,EAAE,CAAC,CAAC;KACxF;AAED;;;;;AAKG;IACK,uBAAuB,CAAC,gBAA6B,EAAE,KAA8B,EAAA;;;AAG3F,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,KAAK,CAAC,eAAe,EAAE,CAAC;SACzB;AAED,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;AACrC,QAAA,MAAM,eAAe,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;QAC5C,MAAM,sBAAsB,GAAG,CAAC,eAAe,IAAK,KAAoB,CAAC,MAAM,KAAK,CAAC,CAAC;AACtF,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC;AACtC,QAAA,MAAM,MAAM,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;QACtC,MAAM,gBAAgB,GACpB,CAAC,eAAe;AAChB,YAAA,IAAI,CAAC,mBAAmB;YACxB,IAAI,CAAC,mBAAmB,GAAG,uBAAuB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAClE,MAAM,WAAW,GAAG,eAAe;AACjC,cAAE,gCAAgC,CAAC,KAAmB,CAAC;AACvD,cAAE,+BAA+B,CAAC,KAAmB,CAAC,CAAC;;;;;;;AAQzD,QAAA,IAAI,MAAM,IAAK,MAAsB,CAAC,SAAS,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,EAAE;YAC7E,KAAK,CAAC,cAAc,EAAE,CAAC;SACxB;;QAGD,IAAI,UAAU,IAAI,sBAAsB,IAAI,gBAAgB,IAAI,WAAW,EAAE;YAC3E,OAAO;SACR;;;;AAKD,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;AACxB,YAAA,MAAM,UAAU,GAAG,WAAW,CAAC,KAAgC,CAAC;YAChE,IAAI,CAAC,wBAAwB,GAAG,UAAU,CAAC,uBAAuB,IAAI,EAAE,CAAC;AACzE,YAAA,UAAU,CAAC,uBAAuB,GAAG,aAAa,CAAC;SACpD;QAED,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;;;QAIlD,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,qBAAqB,EAAE,CAAC;AACjE,QAAA,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAChG,QAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC1F,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,iBAAiB;AAC9C,aAAA,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;AAC/B,aAAA,SAAS,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC;AAE/D,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,aAAa,GAAG,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAClE;;;;AAKD,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;AAC9C,QAAA,IAAI,CAAC,wBAAwB;YAC3B,eAAe,IAAI,eAAe,CAAC,QAAQ,IAAI,CAAC,eAAe,CAAC,SAAS;kBACrE,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC;AACd,kBAAE,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,eAAe,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;AACvF,QAAA,MAAM,eAAe,IAClB,IAAI,CAAC,qBAAqB;AAC3B,YAAA,IAAI,CAAC,yBAAyB;AAC5B,gBAAA,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC,CAAC;AAC3C,QAAA,IAAI,CAAC,sBAAsB,GAAG,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,CAAC;AAC3C,QAAA,IAAI,CAAC,qCAAqC,GAAG,EAAC,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,eAAe,CAAC,CAAC,EAAC,CAAC;AAC1F,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KACnD;;AAGO,IAAA,qBAAqB,CAAC,KAA8B,EAAA;;;;;QAK1D,gBAAgB,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,EAAE,uBAAuB,CAAC,CAAC;AACnE,QAAA,IAAI,CAAC,OAAO,CAAC,UAAW,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAEvE,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,mBAAmB,EAAE,CAAC;AAC3B,QAAA,IAAI,CAAC,eAAe;AAClB,YAAA,IAAI,CAAC,aAAa;AAClB,gBAAA,IAAI,CAAC,YAAY;AACjB,oBAAA,IAAI,CAAC,iBAAiB;AACpB,wBAAA,SAAS,CAAC;;AAGd,QAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAK;AACpB,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,cAAe,CAAC;YACvC,MAAM,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,eAAe,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC;YAC9D,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;AACxD,YAAA,MAAM,sBAAsB,GAAG,SAAS,CAAC,gBAAgB,CACvD,eAAe,CAAC,CAAC,EACjB,eAAe,CAAC,CAAC,CAClB,CAAC;AAEF,YAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,eAAe,EAAE,KAAK,EAAC,CAAC,CAAC;AAC7E,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;AAChB,gBAAA,IAAI,EAAE,IAAI;gBACV,YAAY;gBACZ,aAAa,EAAE,IAAI,CAAC,aAAa;AACjC,gBAAA,SAAS,EAAE,SAAS;gBACpB,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;gBACzC,sBAAsB;gBACtB,QAAQ;AACR,gBAAA,SAAS,EAAE,eAAe;gBAC1B,KAAK;AACN,aAAA,CAAC,CAAC;YACH,SAAS,CAAC,IAAI,CACZ,IAAI,EACJ,YAAY,EACZ,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,iBAAiB,EACtB,sBAAsB,EACtB,QAAQ,EACR,eAAe,EACf,KAAK,CACN,CAAC;AACF,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC;AAC/C,SAAC,CAAC,CAAC;KACJ;AAED;;;AAGG;AACK,IAAA,0BAA0B,CAAC,EAAC,CAAC,EAAE,CAAC,EAAQ,EAAE,EAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAQ,EAAA;;AAEzE,QAAA,IAAI,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,gCAAgC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;;;;;AAMvF,QAAA,IACE,CAAC,YAAY;AACb,YAAA,IAAI,CAAC,cAAc,KAAK,IAAI,CAAC,iBAAiB;YAC9C,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC,EAC7C;AACA,YAAA,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC;SACvC;QAED,IAAI,YAAY,IAAI,YAAY,KAAK,IAAI,CAAC,cAAc,EAAE;AACxD,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAK;;AAEpB,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,cAAe,EAAC,CAAC,CAAC;AAChE,gBAAA,IAAI,CAAC,cAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;AAEhC,gBAAA,IAAI,CAAC,cAAc,GAAG,YAAa,CAAC;AACpC,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CACvB,IAAI,EACJ,CAAC,EACD,CAAC,EACD,YAAY,KAAK,IAAI,CAAC,iBAAiB;;;AAGrC,oBAAA,YAAY,CAAC,eAAe;sBAC1B,IAAI,CAAC,aAAa;sBAClB,SAAS,CACd,CAAC;AACF,gBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;AAChB,oBAAA,IAAI,EAAE,IAAI;AACV,oBAAA,SAAS,EAAE,YAAa;AACxB,oBAAA,YAAY,EAAE,YAAa,CAAC,YAAY,CAAC,IAAI,CAAC;AAC/C,iBAAA,CAAC,CAAC;AACL,aAAC,CAAC,CAAC;SACJ;;AAGD,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;YACrB,IAAI,CAAC,cAAe,CAAC,0BAA0B,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC5D,YAAA,IAAI,CAAC,cAAe,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;AAExE,YAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAC1B,gBAAA,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aACnC;iBAAM;AACL,gBAAA,IAAI,CAAC,sBAAsB,CACzB,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,EACnC,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CACpC,CAAC;aACH;SACF;KACF;AAED;;;AAGG;IACK,4BAA4B,GAAA;;AAElC,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACnB,YAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC1B;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,qBAAqB,EAAE,CAAC;;AAGlE,QAAA,IAAI,CAAC,QAAS,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;;QAG9C,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,IAAI,EAAE,eAAe,CAAC,GAAG,CAAC,CAAC;;;;;QAMvE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAS,CAAC,qBAAqB,EAAE,CAAC;AAExD,QAAA,IAAI,QAAQ,KAAK,CAAC,EAAE;AAClB,YAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC1B;AAED,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AACzC,YAAA,OAAO,IAAI,OAAO,CAAC,OAAO,IAAG;AAC3B,gBAAA,MAAM,OAAO,IAAI,CAAC,KAAsB,KAAI;AAC1C,oBAAA,IACE,CAAC,KAAK;AACN,yBAAC,eAAe,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,YAAY,KAAK,WAAW,CAAC,EAChF;wBACA,IAAI,CAAC,QAAQ,EAAE,mBAAmB,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;AAC7D,wBAAA,OAAO,EAAE,CAAC;wBACV,YAAY,CAAC,OAAO,CAAC,CAAC;qBACvB;AACH,iBAAC,CAAuC,CAAC;;;;gBAKzC,MAAM,OAAO,GAAG,UAAU,CAAC,OAAmB,EAAE,QAAQ,GAAG,GAAG,CAAC,CAAC;gBAChE,IAAI,CAAC,QAAS,CAAC,gBAAgB,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;AAC5D,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;KACJ;;IAGO,yBAAyB,GAAA;AAC/B,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAAC;AACpD,QAAA,MAAM,mBAAmB,GAAG,iBAAiB,GAAG,iBAAiB,CAAC,QAAQ,GAAG,IAAI,CAAC;AAClF,QAAA,IAAI,WAAwB,CAAC;QAE7B,IAAI,mBAAmB,EAAE;AACvB,YAAA,IAAI,CAAC,eAAe,GAAG,iBAAkB,CAAC,aAAa,CAAC,kBAAkB,CACxE,mBAAmB,EACnB,iBAAkB,CAAC,OAAO,CAC3B,CAAC;AACF,YAAA,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC;YACrC,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;SACjE;aAAM;AACL,YAAA,WAAW,GAAG,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SAChD;;;AAID,QAAA,WAAW,CAAC,KAAK,CAAC,aAAa,GAAG,MAAM,CAAC;AACzC,QAAA,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;AAClD,QAAA,OAAO,WAAW,CAAC;KACpB;AAED;;;;AAIG;AACK,IAAA,4BAA4B,CAClC,WAAoB,EACpB,gBAA6B,EAC7B,KAA8B,EAAA;AAE9B,QAAA,MAAM,aAAa,GAAG,gBAAgB,KAAK,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,gBAAgB,CAAC;AACvF,QAAA,MAAM,aAAa,GAAG,aAAa,GAAG,aAAa,CAAC,qBAAqB,EAAE,GAAG,WAAW,CAAC;AAC1F,QAAA,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AACnE,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;AACzD,QAAA,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,GAAG,aAAa,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC;AACjE,QAAA,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,GAAG,aAAa,CAAC,GAAG,GAAG,cAAc,CAAC,GAAG,CAAC;QAE/D,OAAO;YACL,CAAC,EAAE,aAAa,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,GAAG,CAAC;YAC5C,CAAC,EAAE,aAAa,CAAC,GAAG,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC;SAC3C,CAAC;KACH;;AAGO,IAAA,yBAAyB,CAAC,KAA8B,EAAA;AAC9D,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;AACzD,QAAA,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC;AAC/B;;;;;;;gBAOE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,EAAC,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAC;cACnE,KAAK,CAAC;QAEV,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,GAAG,cAAc,CAAC,IAAI,CAAC;QAC5C,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,GAAG,cAAc,CAAC,GAAG,CAAC;;;AAI3C,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,CAAC;YACvD,IAAI,SAAS,EAAE;gBACb,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC;AACxD,gBAAA,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;AACf,gBAAA,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;gBACf,OAAO,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;aACtD;SACF;AAED,QAAA,OAAO,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC;KACf;;AAGO,IAAA,8BAA8B,CAAC,KAAY,EAAA;AACjD,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,IAAI,CAAC;QACpF,IAAI,EAAC,CAAC,EAAE,CAAC,EAAC,GAAG,IAAI,CAAC,iBAAiB;AACjC,cAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,eAAgB,EAAE,IAAI,CAAC,wBAAwB,CAAC;cACzF,KAAK,CAAC;QAEV,IAAI,IAAI,CAAC,QAAQ,KAAK,GAAG,IAAI,iBAAiB,KAAK,GAAG,EAAE;YACtD,CAAC;gBACC,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAC5B,qBAAC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;SAClE;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,GAAG,IAAI,iBAAiB,KAAK,GAAG,EAAE;YAC7D,CAAC;gBACC,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAC5B,qBAAC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;SAClE;AAED,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;;;AAGtB,YAAA,MAAM,EAAC,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB;kBACpD,IAAI,CAAC,wBAAwB;kBAC7B,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,CAAC;AAEjB,YAAA,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;AACxC,YAAA,MAAM,EAAC,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,aAAa,EAAC,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;AAC5E,YAAA,MAAM,IAAI,GAAG,YAAY,CAAC,GAAG,GAAG,OAAO,CAAC;YACxC,MAAM,IAAI,GAAG,YAAY,CAAC,MAAM,IAAI,aAAa,GAAG,OAAO,CAAC,CAAC;AAC7D,YAAA,MAAM,IAAI,GAAG,YAAY,CAAC,IAAI,GAAG,OAAO,CAAC;YACzC,MAAM,IAAI,GAAG,YAAY,CAAC,KAAK,IAAI,YAAY,GAAG,OAAO,CAAC,CAAC;YAE3D,CAAC,GAAGC,OAAK,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YACzB,CAAC,GAAGA,OAAK,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;SAC1B;AAED,QAAA,OAAO,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC;KACf;;AAGO,IAAA,4BAA4B,CAAC,qBAA4B,EAAA;AAC/D,QAAA,MAAM,EAAC,CAAC,EAAE,CAAC,EAAC,GAAG,qBAAqB,CAAC;AACrC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,sBAAsB,CAAC;AAC1C,QAAA,MAAM,uBAAuB,GAAG,IAAI,CAAC,qCAAqC,CAAC;;AAG3E,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,uBAAuB,CAAC,CAAC,CAAC,CAAC;AACxD,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,uBAAuB,CAAC,CAAC,CAAC,CAAC;;;;;QAMxD,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,+BAA+B,EAAE;AAC1D,YAAA,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,uBAAuB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACjD,YAAA,uBAAuB,CAAC,CAAC,GAAG,CAAC,CAAC;SAC/B;QAED,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,+BAA+B,EAAE;AAC1D,YAAA,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,uBAAuB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACjD,YAAA,uBAAuB,CAAC,CAAC,GAAG,CAAC,CAAC;SAC/B;AAED,QAAA,OAAO,KAAK,CAAC;KACd;;IAGO,6BAA6B,GAAA;QACnC,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACxC,OAAO;SACR;AAED,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;AAEpE,QAAA,IAAI,YAAY,KAAK,IAAI,CAAC,0BAA0B,EAAE;AACpD,YAAA,IAAI,CAAC,0BAA0B,GAAG,YAAY,CAAC;AAC/C,YAAA,4BAA4B,CAAC,IAAI,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;SAC/D;KACF;;AAGO,IAAA,2BAA2B,CAAC,OAAoB,EAAA;QACtD,OAAO,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,0BAA0B,CAAC,CAAC;QACxF,OAAO,CAAC,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,2BAA2B,CAAC,CAAC;QAC1F,OAAO,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,EAAE,0BAA0B,CAAC,CAAC;KAC7F;AAED;;;;AAIG;IACK,0BAA0B,CAAC,CAAS,EAAE,CAAS,EAAA;QACrD,MAAM,SAAS,GAAG,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACrC,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;;;;AAKvC,QAAA,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,EAAE;AAClC,YAAA,IAAI,CAAC,iBAAiB;AACpB,gBAAA,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,IAAI,MAAM,GAAG,MAAM,CAAC,SAAS,GAAG,EAAE,CAAC;SAC1E;;;;QAKD,MAAM,CAAC,SAAS,GAAG,iBAAiB,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;KACzE;AAED;;;;AAIG;IACK,sBAAsB,CAAC,CAAS,EAAE,CAAS,EAAA;;;AAGjD,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,EAAE,QAAQ,GAAG,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAC9F,MAAM,SAAS,GAAG,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACrC,QAAA,IAAI,CAAC,QAAS,CAAC,YAAY,CAAC,iBAAiB,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC,CAAC;KAC7E;AAED;;;AAGG;AACK,IAAA,gBAAgB,CAAC,eAAsB,EAAA;AAC7C,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC;QAElD,IAAI,cAAc,EAAE;YAClB,OAAO,EAAC,CAAC,EAAE,eAAe,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,eAAe,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,EAAC,CAAC;SAC3F;QAED,OAAO,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,CAAC;KACrB;;IAGO,wBAAwB,GAAA;QAC9B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;AACnD,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;KAC/B;AAED;;;AAGG;IACK,8BAA8B,GAAA;QACpC,IAAI,EAAC,CAAC,EAAE,CAAC,EAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAEpC,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACvE,OAAO;SACR;;QAGD,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,qBAAqB,EAAE,CAAC;QAC9D,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,EAAE,CAAC;;;AAInE,QAAA,IACE,CAAC,YAAY,CAAC,KAAK,KAAK,CAAC,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC;AACtD,aAAC,WAAW,CAAC,KAAK,KAAK,CAAC,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,CAAC,EACrD;YACA,OAAO;SACR;QAED,MAAM,YAAY,GAAG,YAAY,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;QAC1D,MAAM,aAAa,GAAG,WAAW,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC;QAC7D,MAAM,WAAW,GAAG,YAAY,CAAC,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC;QACvD,MAAM,cAAc,GAAG,WAAW,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;;;QAIhE,IAAI,YAAY,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,EAAE;AAC1C,YAAA,IAAI,YAAY,GAAG,CAAC,EAAE;gBACpB,CAAC,IAAI,YAAY,CAAC;aACnB;AAED,YAAA,IAAI,aAAa,GAAG,CAAC,EAAE;gBACrB,CAAC,IAAI,aAAa,CAAC;aACpB;SACF;aAAM;YACL,CAAC,GAAG,CAAC,CAAC;SACP;;;QAID,IAAI,YAAY,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,EAAE;AAC5C,YAAA,IAAI,WAAW,GAAG,CAAC,EAAE;gBACnB,CAAC,IAAI,WAAW,CAAC;aAClB;AAED,YAAA,IAAI,cAAc,GAAG,CAAC,EAAE;gBACtB,CAAC,IAAI,cAAc,CAAC;aACrB;SACF;aAAM;YACL,CAAC,GAAG,CAAC,CAAC;SACP;AAED,QAAA,IAAI,CAAC,KAAK,IAAI,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE;YACpE,IAAI,CAAC,mBAAmB,CAAC,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,CAAC;SAClC;KACF;;AAGO,IAAA,kBAAkB,CAAC,KAA8B,EAAA;AACvD,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC;AAElC,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,YAAA,OAAO,KAAK,CAAC;SACd;AAAM,aAAA,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE;YAC9B,OAAO,KAAK,CAAC,KAAK,CAAC;SACpB;QAED,OAAO,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;KAChC;;AAGO,IAAA,eAAe,CAAC,KAAY,EAAA;QAClC,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAEnE,IAAI,gBAAgB,EAAE;AACpB,YAAA,MAAM,MAAM,GAAG,eAAe,CAAyB,KAAK,CAAE,CAAC;;;YAI/D,IACE,IAAI,CAAC,aAAa;gBAClB,MAAM,KAAK,IAAI,CAAC,gBAAgB;gBAChC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,EACtC;AACA,gBAAA,aAAa,CAAC,IAAI,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,EAAE,gBAAgB,CAAC,IAAI,CAAC,CAAC;aAChF;YAED,IAAI,CAAC,qBAAqB,CAAC,CAAC,IAAI,gBAAgB,CAAC,IAAI,CAAC;YACtD,IAAI,CAAC,qBAAqB,CAAC,CAAC,IAAI,gBAAgB,CAAC,GAAG,CAAC;;;AAIrD,YAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACxB,IAAI,CAAC,gBAAgB,CAAC,CAAC,IAAI,gBAAgB,CAAC,IAAI,CAAC;gBACjD,IAAI,CAAC,gBAAgB,CAAC,CAAC,IAAI,gBAAgB,CAAC,GAAG,CAAC;AAChD,gBAAA,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;aACnF;SACF;KACF;;IAGO,0BAA0B,GAAA;AAChC,QAAA,QACE,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,cAAc;AACnE,YAAA,IAAI,CAAC,gBAAgB,CAAC,yBAAyB,EAAE,EACjD;KACH;AAED;;;;;AAKG;IACK,cAAc,GAAA;AACpB,QAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,SAAS,EAAE;YACxC,IAAI,CAAC,iBAAiB,GAAG,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SAC5D;QAED,OAAO,IAAI,CAAC,iBAAiB,CAAC;KAC/B;;IAGO,yBAAyB,CAC/B,aAA0B,EAC1B,UAA6B,EAAA;AAE7B,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,IAAI,QAAQ,CAAC;AAE5D,QAAA,IAAI,gBAAgB,KAAK,QAAQ,EAAE;AACjC,YAAA,OAAO,aAAa,CAAC;SACtB;AAED,QAAA,IAAI,gBAAgB,KAAK,QAAQ,EAAE;AACjC,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC;;;;AAKnC,YAAA,QACE,UAAU;AACV,gBAAA,WAAW,CAAC,iBAAiB;AAC5B,gBAAA,WAAmB,CAAC,uBAAuB;AAC3C,gBAAA,WAAmB,CAAC,oBAAoB;AACxC,gBAAA,WAAmB,CAAC,mBAAmB;gBACxC,WAAW,CAAC,IAAI,EAChB;SACH;AAED,QAAA,OAAO,aAAa,CAAC,gBAAgB,CAAC,CAAC;KACxC;;IAGO,eAAe,GAAA;;;QAGrB,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE;AACjF,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ;AAC/B,kBAAE,IAAI,CAAC,QAAS,CAAC,qBAAqB,EAAE;AACxC,kBAAE,IAAI,CAAC,eAAgB,CAAC;SAC3B;QAED,OAAO,IAAI,CAAC,YAAY,CAAC;KAC1B;;AAkBO,IAAA,gBAAgB,CAAC,KAAY,EAAA;QACnC,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAG;YACjC,OAAO,KAAK,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,KAAK,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAc,CAAC,CAAC,CAAC;AAC5F,SAAC,CAAC,CAAC;KACJ;AACF,CAAA;AAED;AACA,SAASA,OAAK,CAAC,KAAa,EAAE,GAAW,EAAE,GAAW,EAAA;AACpD,IAAA,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;AAC7C,CAAC;AAED;AACA,SAAS,YAAY,CAAC,KAA8B,EAAA;;;;IAIlD,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC;AAC/B,CAAC;AAED;AACA,SAAS,oBAAoB,CAAC,KAAY,EAAA;IACxC,KAAK,CAAC,cAAc,EAAE,CAAC;AACzB;;AC5/CA;;;;;AAKG;SACa,eAAe,CAAU,KAAU,EAAE,SAAiB,EAAE,OAAe,EAAA;AACrF,IAAA,MAAM,IAAI,GAAG,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAChD,IAAA,MAAM,EAAE,GAAG,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAE5C,IAAA,IAAI,IAAI,KAAK,EAAE,EAAE;QACf,OAAO;KACR;AAED,IAAA,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;AAC3B,IAAA,MAAM,KAAK,GAAG,EAAE,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAEjC,IAAA,KAAK,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,KAAK,EAAE;QACvC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;KAC7B;AAED,IAAA,KAAK,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;AACrB,CAAC;AAED;;;;;;AAMG;AACG,SAAU,iBAAiB,CAC/B,YAAiB,EACjB,WAAgB,EAChB,YAAoB,EACpB,WAAmB,EAAA;AAEnB,IAAA,MAAM,IAAI,GAAG,KAAK,CAAC,YAAY,EAAE,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC1D,MAAM,EAAE,GAAG,KAAK,CAAC,WAAW,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;AAElD,IAAA,IAAI,YAAY,CAAC,MAAM,EAAE;AACvB,QAAA,WAAW,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,EAAE,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC5D;AACH,CAAC;AAED;;;;;;;;AAQG;AACG,SAAU,aAAa,CAC3B,YAAiB,EACjB,WAAgB,EAChB,YAAoB,EACpB,WAAmB,EAAA;IAEnB,MAAM,EAAE,GAAG,KAAK,CAAC,WAAW,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;AAElD,IAAA,IAAI,YAAY,CAAC,MAAM,EAAE;AACvB,QAAA,WAAW,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,EAAE,YAAY,CAAC,YAAY,CAAC,CAAC,CAAC;KACvD;AACH,CAAC;AAED;AACA,SAAS,KAAK,CAAC,KAAa,EAAE,GAAW,EAAA;AACvC,IAAA,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;AAC3C;;AC1CA;;;;AAIG;MACU,sBAAsB,CAAA;IAsBjC,WACU,CAAA,QAA+C,EAC/C,iBAA+C,EAAA;QAD/C,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAuC;QAC/C,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAA8B;;QAjBjD,IAAc,CAAA,cAAA,GAA4B,EAAE,CAAC;;QAUrD,IAAW,CAAA,WAAA,GAA8B,UAAU,CAAC;AAUpD;;;;AAIG;AACK,QAAA,IAAA,CAAA,aAAa,GAAG;AACtB,YAAA,IAAI,EAAE,IAAgB;AACtB,YAAA,KAAK,EAAE,CAAC;AACR,YAAA,QAAQ,EAAE,KAAK;SAChB,CAAC;KAXE;AAaJ;;;AAGG;AACH,IAAA,KAAK,CAAC,KAAmB,EAAA;AACvB,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;KACvB;AAED;;;;;;AAMG;AACH,IAAA,IAAI,CAAC,IAAO,EAAE,QAAgB,EAAE,QAAgB,EAAE,YAAoC,EAAA;AACpF,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC;AACrC,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,gCAAgC,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;QAE/F,IAAI,QAAQ,KAAK,CAAC,CAAC,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;AAC1C,YAAA,OAAO,IAAI,CAAC;SACb;AAED,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,KAAK,YAAY,CAAC;AACvD,QAAA,MAAM,YAAY,GAAG,QAAQ,CAAC,SAAS,CAAC,WAAW,IAAI,WAAW,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;AAClF,QAAA,MAAM,oBAAoB,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAChD,MAAM,eAAe,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC,UAAU,CAAC;AAC1D,QAAA,MAAM,WAAW,GAAG,oBAAoB,CAAC,UAAU,CAAC;AACpD,QAAA,MAAM,KAAK,GAAG,YAAY,GAAG,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;;AAG/C,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;;AAG9E,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;;;AAI9E,QAAA,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;;AAGlC,QAAA,eAAe,CAAC,QAAQ,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;QAElD,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,KAAI;;AAElC,YAAA,IAAI,QAAQ,CAAC,KAAK,CAAC,KAAK,OAAO,EAAE;gBAC/B,OAAO;aACR;AAED,YAAA,MAAM,aAAa,GAAG,OAAO,CAAC,IAAI,KAAK,IAAI,CAAC;YAC5C,MAAM,MAAM,GAAG,aAAa,GAAG,UAAU,GAAG,aAAa,CAAC;YAC1D,MAAM,eAAe,GAAG,aAAa;AACnC,kBAAE,IAAI,CAAC,qBAAqB,EAAE;AAC9B,kBAAE,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;;AAGlC,YAAA,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC;;;;;YAMzB,IAAI,YAAY,EAAE;;;gBAGhB,eAAe,CAAC,KAAK,CAAC,SAAS,GAAG,iBAAiB,CACjD,CAAe,YAAA,EAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA,SAAA,CAAW,EACpD,OAAO,CAAC,gBAAgB,CACzB,CAAC;gBACF,aAAa,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;aAC9C;iBAAM;gBACL,eAAe,CAAC,KAAK,CAAC,SAAS,GAAG,iBAAiB,CACjD,CAAkB,eAAA,EAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA,MAAA,CAAQ,EACpD,OAAO,CAAC,gBAAgB,CACzB,CAAC;gBACF,aAAa,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;aAC9C;AACH,SAAC,CAAC,CAAC;;AAGH,QAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,kBAAkB,CAAC,WAAW,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAClF,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,oBAAoB,CAAC,IAAI,CAAC;AACpD,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG,YAAY,GAAG,YAAY,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC;QAE1E,OAAO,EAAC,aAAa,EAAE,YAAY,EAAE,YAAY,EAAE,QAAQ,EAAC,CAAC;KAC9D;AAED;;;;;;;AAOG;AACH,IAAA,KAAK,CAAC,IAAO,EAAE,QAAgB,EAAE,QAAgB,EAAE,KAAc,EAAA;QAC/D,MAAM,QAAQ,GACZ,KAAK,IAAI,IAAI,IAAI,KAAK,GAAG,CAAC;AACxB;;gBAEE,IAAI,CAAC,gCAAgC,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAC;cAC/D,KAAK,CAAC;AAEZ,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAChD,MAAM,YAAY,GAAG,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACpD,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;AACjD,QAAA,IAAI,oBAAoB,GAAkB,gBAAgB,CAAC,QAAQ,CAAC,CAAC;;;;AAKrE,QAAA,IAAI,oBAAoB,KAAK,IAAI,EAAE;AACjC,YAAA,oBAAoB,GAAG,gBAAgB,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;SACvD;;;AAID,QAAA,IACE,CAAC,oBAAoB;AACrB,aAAC,QAAQ,IAAI,IAAI,IAAI,QAAQ,KAAK,CAAC,CAAC,IAAI,QAAQ,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;YAC/E,IAAI,CAAC,wBAAwB,CAAC,QAAQ,EAAE,QAAQ,CAAC,EACjD;AACA,YAAA,oBAAoB,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;SAC5C;;;AAID,QAAA,IAAI,YAAY,GAAG,CAAC,CAAC,EAAE;AACrB,YAAA,gBAAgB,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;SAC1C;;;AAID,QAAA,IAAI,oBAAoB,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,oBAAoB,CAAC,EAAE;AACpF,YAAA,MAAM,OAAO,GAAG,oBAAoB,CAAC,cAAc,EAAE,CAAC;YACtD,OAAO,CAAC,aAAc,CAAC,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;YAC1D,gBAAgB,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;SAC5C;aAAM;YACL,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACtD,YAAA,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC7B;;AAGD,QAAA,WAAW,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;;;;QAKjC,IAAI,CAAC,mBAAmB,EAAE,CAAC;KAC5B;;AAGD,IAAA,SAAS,CAAC,KAAmB,EAAA;AAC3B,QAAA,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;QACvC,IAAI,CAAC,mBAAmB,EAAE,CAAC;KAC5B;;AAGD,IAAA,iBAAiB,CAAC,SAA2B,EAAA;AAC3C,QAAA,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;KACjC;;IAGD,KAAK,GAAA;;AAEH,QAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,IAAG;AACpC,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YAE1C,IAAI,WAAW,EAAE;gBACf,MAAM,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,gBAAgB,CAAC;gBAC1F,WAAW,CAAC,KAAK,CAAC,SAAS,GAAG,gBAAgB,IAAI,EAAE,CAAC;aACtD;AACH,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;AACzB,QAAA,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;AAC5B,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,IAAI,CAAC;AAC/B,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG,CAAC,CAAC;AAC7B,QAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,KAAK,CAAC;KACrC;AAED;;;AAGG;IACH,sBAAsB,GAAA;QACpB,OAAO,IAAI,CAAC,iBAAiB,CAAC;KAC/B;;AAGD,IAAA,YAAY,CAAC,IAAO,EAAA;;;;AAIlB,QAAA,MAAM,KAAK,GACT,IAAI,CAAC,WAAW,KAAK,YAAY,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK;cACzD,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE;AACvC,cAAE,IAAI,CAAC,cAAc,CAAC;AAE1B,QAAA,OAAO,KAAK,CAAC,SAAS,CAAC,WAAW,IAAI,WAAW,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;KAClE;;IAGD,cAAc,CAAC,aAAqB,EAAE,cAAsB,EAAA;;;;;QAK1D,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,EAAC,UAAU,EAAC,KAAI;AAC3C,YAAA,aAAa,CAAC,UAAU,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;AAC3D,SAAC,CAAC,CAAC;;;QAIH,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,EAAC,IAAI,EAAC,KAAI;YACrC,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;;;gBAG3C,IAAI,CAAC,4BAA4B,EAAE,CAAC;aACrC;AACH,SAAC,CAAC,CAAC;KACJ;;IAGO,mBAAmB,GAAA;AACzB,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,KAAK,YAAY,CAAC;AAEvD,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB;aACzC,GAAG,CAAC,IAAI,IAAG;AACV,YAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAClD,OAAO;gBACL,IAAI;AACJ,gBAAA,MAAM,EAAE,CAAC;AACT,gBAAA,gBAAgB,EAAE,gBAAgB,CAAC,KAAK,CAAC,SAAS,IAAI,EAAE;AACxD,gBAAA,UAAU,EAAE,oBAAoB,CAAC,gBAAgB,CAAC;aACnD,CAAC;AACJ,SAAC,CAAC;AACD,aAAA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAI;AACb,YAAA,OAAO,YAAY;kBACf,CAAC,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,CAAC,UAAU,CAAC,IAAI;AACvC,kBAAE,CAAC,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC;AAC1C,SAAC,CAAC,CAAC;KACN;AAED;;;;;AAKG;AACK,IAAA,gBAAgB,CAAC,eAAwB,EAAE,WAAoB,EAAE,KAAa,EAAA;AACpF,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,KAAK,YAAY,CAAC;QACvD,IAAI,UAAU,GAAG,YAAY;AAC3B,cAAE,WAAW,CAAC,IAAI,GAAG,eAAe,CAAC,IAAI;cACvC,WAAW,CAAC,GAAG,GAAG,eAAe,CAAC,GAAG,CAAC;;AAG1C,QAAA,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;AAChB,YAAA,UAAU,IAAI,YAAY;AACxB,kBAAE,WAAW,CAAC,KAAK,GAAG,eAAe,CAAC,KAAK;kBACzC,WAAW,CAAC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC;SACjD;AAED,QAAA,OAAO,UAAU,CAAC;KACnB;AAED;;;;;AAKG;AACK,IAAA,mBAAmB,CACzB,YAAoB,EACpB,QAAiC,EACjC,KAAa,EAAA;AAEb,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,KAAK,YAAY,CAAC;QACvD,MAAM,eAAe,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC,UAAU,CAAC;QAC1D,MAAM,gBAAgB,GAAG,QAAQ,CAAC,YAAY,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7D,QAAA,IAAI,aAAa,GAAG,eAAe,CAAC,YAAY,GAAG,OAAO,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC;QAE/E,IAAI,gBAAgB,EAAE;YACpB,MAAM,KAAK,GAAG,YAAY,GAAG,MAAM,GAAG,KAAK,CAAC;YAC5C,MAAM,GAAG,GAAG,YAAY,GAAG,OAAO,GAAG,QAAQ,CAAC;;;;;AAM9C,YAAA,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;AAChB,gBAAA,aAAa,IAAI,gBAAgB,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC;aAC5E;iBAAM;AACL,gBAAA,aAAa,IAAI,eAAe,CAAC,KAAK,CAAC,GAAG,gBAAgB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;aAC5E;SACF;AAED,QAAA,OAAO,aAAa,CAAC;KACtB;AAED;;;;AAIG;IACK,wBAAwB,CAAC,QAAgB,EAAE,QAAgB,EAAA;AACjE,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;AAClC,YAAA,OAAO,KAAK,CAAC;SACd;AAED,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;AAC1C,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,KAAK,YAAY,CAAC;;;AAIvD,QAAA,MAAM,QAAQ,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;QACrE,IAAI,QAAQ,EAAE;AACZ,YAAA,MAAM,YAAY,GAAG,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC;AACxE,YAAA,OAAO,YAAY,GAAG,QAAQ,IAAI,YAAY,CAAC,KAAK,GAAG,QAAQ,IAAI,YAAY,CAAC,MAAM,CAAC;SACxF;aAAM;YACL,MAAM,aAAa,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;AAClD,YAAA,OAAO,YAAY,GAAG,QAAQ,IAAI,aAAa,CAAC,IAAI,GAAG,QAAQ,IAAI,aAAa,CAAC,GAAG,CAAC;SACtF;KACF;AAED;;;;;;AAMG;AACK,IAAA,gCAAgC,CACtC,IAAO,EACP,QAAgB,EAChB,QAAgB,EAChB,KAA8B,EAAA;AAE9B,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,KAAK,YAAY,CAAC;AACvD,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,EAAC,IAAI,EAAE,UAAU,EAAC,KAAI;;AAEjE,YAAA,IAAI,IAAI,KAAK,IAAI,EAAE;AACjB,gBAAA,OAAO,KAAK,CAAC;aACd;YAED,IAAI,KAAK,EAAE;AACT,gBAAA,MAAM,SAAS,GAAG,YAAY,GAAG,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;;;;AAKnD,gBAAA,IACE,IAAI,KAAK,IAAI,CAAC,aAAa,CAAC,IAAI;oBAChC,IAAI,CAAC,aAAa,CAAC,QAAQ;AAC3B,oBAAA,SAAS,KAAK,IAAI,CAAC,aAAa,CAAC,KAAK,EACtC;AACA,oBAAA,OAAO,KAAK,CAAC;iBACd;aACF;AAED,YAAA,OAAO,YAAY;AACjB;;AAEE,oBAAA,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC;kBAClF,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AACzF,SAAC,CAAC,CAAC;QAEH,OAAO,KAAK,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;KACvE;AACF;;ACzaD;;;AAGG;AACH,MAAM,wBAAwB,GAAG,IAAI,CAAC;AAEtC;;;AAGG;AACH,MAAM,0BAA0B,GAAG,IAAI,CAAC;AAExC;AACA,IAAK,2BAIJ,CAAA;AAJD,CAAA,UAAK,2BAA2B,EAAA;AAC9B,IAAA,2BAAA,CAAA,2BAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;AACJ,IAAA,2BAAA,CAAA,2BAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,GAAA,IAAE,CAAA;AACF,IAAA,2BAAA,CAAA,2BAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;AACN,CAAC,EAJI,2BAA2B,KAA3B,2BAA2B,GAI/B,EAAA,CAAA,CAAA,CAAA;AAED;AACA,IAAK,6BAIJ,CAAA;AAJD,CAAA,UAAK,6BAA6B,EAAA;AAChC,IAAA,6BAAA,CAAA,6BAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;AACJ,IAAA,6BAAA,CAAA,6BAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;AACJ,IAAA,6BAAA,CAAA,6BAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK,CAAA;AACP,CAAC,EAJI,6BAA6B,KAA7B,6BAA6B,GAIjC,EAAA,CAAA,CAAA,CAAA;AASD;;AAEG;MACU,WAAW,CAAA;IAkItB,WACE,CAAA,OAA8C,EACtC,iBAAyD,EACjE,SAAc,EACN,OAAe,EACf,cAA6B,EAAA;QAH7B,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAwC;QAEzD,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QACf,IAAc,CAAA,cAAA,GAAd,cAAc,CAAe;;QAlIvC,IAAQ,CAAA,QAAA,GAAY,KAAK,CAAC;;QAG1B,IAAe,CAAA,eAAA,GAAY,KAAK,CAAC;AAKjC;;;AAGG;QACH,IAAkB,CAAA,kBAAA,GAAY,KAAK,CAAC;;QAGpC,IAAc,CAAA,cAAA,GAAW,CAAC,CAAC;AAE3B;;;AAGG;AACH,QAAA,IAAA,CAAA,cAAc,GAAkD,MAAM,IAAI,CAAC;;AAG3E,QAAA,IAAA,CAAA,aAAa,GAAiE,MAAM,IAAI,CAAC;;AAGhF,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,OAAO,EAAQ,CAAC;AAE7C;;AAEG;AACM,QAAA,IAAA,CAAA,OAAO,GAAG,IAAI,OAAO,EAAiE,CAAC;AAEhG;;;AAGG;AACM,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,OAAO,EAA2C,CAAC;;AAGhE,QAAA,IAAA,CAAA,OAAO,GAAG,IAAI,OAAO,EAU1B,CAAC;;AAGI,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,OAAO,EAKzB,CAAC;;AAGI,QAAA,IAAA,CAAA,gBAAgB,GAAG,IAAI,OAAO,EAInC,CAAC;;AAGI,QAAA,IAAA,CAAA,gBAAgB,GAAG,IAAI,OAAO,EAGnC,CAAC;;QAMG,IAAW,CAAA,WAAA,GAAG,KAAK,CAAC;;QAYpB,IAAW,CAAA,WAAA,GAAuB,EAAE,CAAC;;QAGrC,IAAS,CAAA,SAAA,GAA2B,EAAE,CAAC;;AAGvC,QAAA,IAAA,CAAA,eAAe,GAAG,IAAI,GAAG,EAAe,CAAC;;AAGzC,QAAA,IAAA,CAAA,2BAA2B,GAAG,YAAY,CAAC,KAAK,CAAC;;AAGjD,QAAA,IAAA,CAAA,wBAAwB,GAAG,2BAA2B,CAAC,IAAI,CAAC;;AAG5D,QAAA,IAAA,CAAA,0BAA0B,GAAG,6BAA6B,CAAC,IAAI,CAAC;;AAMvD,QAAA,IAAA,CAAA,iBAAiB,GAAG,IAAI,OAAO,EAAQ,CAAC;;QAGjD,IAAiB,CAAA,iBAAA,GAAoB,IAAI,CAAC;;QAmX1C,IAAoB,CAAA,oBAAA,GAAG,MAAK;YAClC,IAAI,CAAC,cAAc,EAAE,CAAC;AAEtB,YAAA,QAAQ,CAAC,CAAC,EAAE,uBAAuB,CAAC;AACjC,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;iBACvC,SAAS,CAAC,MAAK;AACd,gBAAA,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;AAC9B,gBAAA,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC;gBAEvC,IAAI,IAAI,CAAC,wBAAwB,KAAK,2BAA2B,CAAC,EAAE,EAAE;oBACpE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;iBAC/B;qBAAM,IAAI,IAAI,CAAC,wBAAwB,KAAK,2BAA2B,CAAC,IAAI,EAAE;AAC7E,oBAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;iBAC9B;gBAED,IAAI,IAAI,CAAC,0BAA0B,KAAK,6BAA6B,CAAC,IAAI,EAAE;oBAC1E,IAAI,CAAC,QAAQ,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;iBAC/B;qBAAM,IAAI,IAAI,CAAC,0BAA0B,KAAK,6BAA6B,CAAC,KAAK,EAAE;AAClF,oBAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;iBAC9B;AACH,aAAC,CAAC,CAAC;AACP,SAAC,CAAC;AAtXA,QAAA,IAAI,CAAC,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;AACtC,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;AAC3C,QAAA,iBAAiB,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;QAC9C,IAAI,CAAC,gBAAgB,GAAG,IAAI,qBAAqB,CAAC,SAAS,CAAC,CAAC;AAC7D,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,sBAAsB,CAAC,IAAI,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;QACjF,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,KAAK,EAAE,IAAI,KAAK,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;KAC9F;;IAGD,OAAO,GAAA;QACL,IAAI,CAAC,cAAc,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,CAAC;AAClC,QAAA,IAAI,CAAC,2BAA2B,CAAC,WAAW,EAAE,CAAC;AAC/C,QAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;AAC9B,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;AACxB,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;AACxB,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC;AACjC,QAAA,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC;AACjC,QAAA,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;AAC7B,QAAA,IAAI,CAAC,WAAW,GAAG,IAAK,CAAC;AACzB,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;AAC9B,QAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;KAClD;;IAGD,UAAU,GAAA;QACR,OAAO,IAAI,CAAC,WAAW,CAAC;KACzB;;IAGD,KAAK,GAAA;QACH,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,wBAAwB,EAAE,CAAC;KACjC;AAED;;;;;;;AAOG;AACH,IAAA,KAAK,CAAC,IAAa,EAAE,QAAgB,EAAE,QAAgB,EAAE,KAAc,EAAA;QACrE,IAAI,CAAC,gBAAgB,EAAE,CAAC;;;QAIxB,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,eAAe,EAAE;YACzC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACxC;AAED,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;;;QAI1D,IAAI,CAAC,qBAAqB,EAAE,CAAC;;QAG7B,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAChC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAC,CAAC,CAAC;KACnF;AAED;;;AAGG;AACH,IAAA,IAAI,CAAC,IAAa,EAAA;QAChB,IAAI,CAAC,MAAM,EAAE,CAAC;AACd,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,SAAS,EAAE,IAAI,EAAC,CAAC,CAAC;KAC3C;AAED;;;;;;;;;;;;AAYG;AACH,IAAA,IAAI,CACF,IAAa,EACb,YAAoB,EACpB,aAAqB,EACrB,iBAA8B,EAC9B,sBAA+B,EAC/B,QAAe,EACf,SAAgB,EAChB,QAAiC,EAAS,EAAA;QAE1C,IAAI,CAAC,MAAM,EAAE,CAAC;AACd,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;YAChB,IAAI;YACJ,YAAY;YACZ,aAAa;AACb,YAAA,SAAS,EAAE,IAAI;YACf,iBAAiB;YACjB,sBAAsB;YACtB,QAAQ;YACR,SAAS;YACT,KAAK;AACN,SAAA,CAAC,CAAC;KACJ;AAED;;;AAGG;AACH,IAAA,SAAS,CAAC,KAAgB,EAAA;AACxB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC;AACvC,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AACzB,QAAA,KAAK,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;AAErD,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;AACrB,YAAA,MAAM,YAAY,GAAG,aAAa,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;;;AAIrE,YAAA,IAAI,YAAY,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;gBAC1D,IAAI,CAAC,MAAM,EAAE,CAAC;aACf;iBAAM;gBACL,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;aAChD;SACF;AAED,QAAA,OAAO,IAAI,CAAC;KACb;;AAGD,IAAA,aAAa,CAAC,SAAoB,EAAA;AAChC,QAAA,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,SAAS,CAAC;AACzC,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;AAIG;AACH,IAAA,WAAW,CAAC,WAA0B,EAAA;AACpC,QAAA,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,KAAK,EAAE,CAAC;AACrC,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;AAGG;AACH,IAAA,eAAe,CAAC,WAAsC,EAAA;;;AAGnD,QAAA,IAAI,CAAC,aAAiD,CAAC,WAAW,GAAG,WAAW,CAAC;AAClF,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;AAGG;AACH,IAAA,qBAAqB,CAAC,QAAuB,EAAA;QAC3C,MAAM,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;;;AAI5C,QAAA,IAAI,CAAC,mBAAmB;YACtB,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;AAC/E,QAAA,OAAO,IAAI,CAAC;KACb;;IAGD,oBAAoB,GAAA;QAClB,OAAO,IAAI,CAAC,mBAAmB,CAAC;KACjC;AAED;;;AAGG;AACH,IAAA,YAAY,CAAC,IAAa,EAAA;QACxB,OAAO,IAAI,CAAC,WAAW;cACnB,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,IAAI,CAAC;cACrC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;KACpC;AAED;;;AAGG;IACH,WAAW,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,CAAC,CAAC;KACtC;AAED;;;;;;AAMG;AACH,IAAA,SAAS,CACP,IAAa,EACb,QAAgB,EAChB,QAAgB,EAChB,YAAoC,EAAA;;QAGpC,IACE,IAAI,CAAC,eAAe;YACpB,CAAC,IAAI,CAAC,QAAQ;AACd,YAAA,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,EAAE,wBAAwB,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAClF;YACA,OAAO;SACR;AAED,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;QAE/E,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gBACf,aAAa,EAAE,MAAM,CAAC,aAAa;gBACnC,YAAY,EAAE,MAAM,CAAC,YAAY;AACjC,gBAAA,SAAS,EAAE,IAAI;gBACf,IAAI;AACL,aAAA,CAAC,CAAC;SACJ;KACF;AAED;;;;;AAKG;IACH,0BAA0B,CAAC,QAAgB,EAAE,QAAgB,EAAA;AAC3D,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,OAAO;SACR;AAED,QAAA,IAAI,UAA4C,CAAC;AACjD,QAAA,IAAI,uBAAuB,GAAG,2BAA2B,CAAC,IAAI,CAAC;AAC/D,QAAA,IAAI,yBAAyB,GAAG,6BAA6B,CAAC,IAAI,CAAC;;AAGnE,QAAA,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,OAAO,KAAI;;;AAG5D,YAAA,IAAI,OAAO,KAAK,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,UAAU,IAAI,UAAU,EAAE;gBACpE,OAAO;aACR;AAED,YAAA,IAAI,oBAAoB,CAAC,QAAQ,CAAC,UAAU,EAAE,wBAAwB,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAAE;gBAC3F,CAAC,uBAAuB,EAAE,yBAAyB,CAAC,GAAG,0BAA0B,CAC/E,OAAsB,EACtB,QAAQ,CAAC,UAAU,EACnB,IAAI,CAAC,aAAa,CAAC,SAAS,EAC5B,QAAQ,EACR,QAAQ,CACT,CAAC;AAEF,gBAAA,IAAI,uBAAuB,IAAI,yBAAyB,EAAE;oBACxD,UAAU,GAAG,OAAsB,CAAC;iBACrC;aACF;AACH,SAAC,CAAC,CAAC;;AAGH,QAAA,IAAI,CAAC,uBAAuB,IAAI,CAAC,yBAAyB,EAAE;AAC1D,YAAA,MAAM,EAAC,KAAK,EAAE,MAAM,EAAC,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC;AAC9D,YAAA,MAAM,OAAO,GAAG;gBACd,KAAK;gBACL,MAAM;AACN,gBAAA,GAAG,EAAE,CAAC;AACN,gBAAA,KAAK,EAAE,KAAK;AACZ,gBAAA,MAAM,EAAE,MAAM;AACd,gBAAA,IAAI,EAAE,CAAC;aACG,CAAC;AACb,YAAA,uBAAuB,GAAG,0BAA0B,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AACxE,YAAA,yBAAyB,GAAG,4BAA4B,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YAC5E,UAAU,GAAG,MAAM,CAAC;SACrB;AAED,QAAA,IACE,UAAU;AACV,aAAC,uBAAuB,KAAK,IAAI,CAAC,wBAAwB;gBACxD,yBAAyB,KAAK,IAAI,CAAC,0BAA0B;AAC7D,gBAAA,UAAU,KAAK,IAAI,CAAC,WAAW,CAAC,EAClC;AACA,YAAA,IAAI,CAAC,wBAAwB,GAAG,uBAAuB,CAAC;AACxD,YAAA,IAAI,CAAC,0BAA0B,GAAG,yBAAyB,CAAC;AAC5D,YAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;YAE9B,IAAI,CAAC,uBAAuB,IAAI,yBAAyB,KAAK,UAAU,EAAE;gBACxE,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;aAC3D;iBAAM;gBACL,IAAI,CAAC,cAAc,EAAE,CAAC;aACvB;SACF;KACF;;IAGD,cAAc,GAAA;AACZ,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;KAC/B;;IAGO,gBAAgB,GAAA;QACtB,MAAM,MAAM,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAgC,CAAC;AAC5E,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;AAC1B,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;;;;AAKxB,QAAA,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,gBAAgB,IAAI,MAAM,CAAC,cAAc,IAAI,EAAE,CAAC;QACjF,MAAM,CAAC,cAAc,GAAG,MAAM,CAAC,gBAAgB,GAAG,MAAM,CAAC;QACzD,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC3C,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,QAAA,IAAI,CAAC,2BAA2B,CAAC,WAAW,EAAE,CAAC;QAC/C,IAAI,CAAC,qBAAqB,EAAE,CAAC;KAC9B;;IAGO,qBAAqB,GAAA;QAC3B,MAAM,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5C,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;;;AAItD,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC,UAAW,CAAC;KAC3E;;IAGO,MAAM,GAAA;AACZ,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAEzB,MAAM,MAAM,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAgC,CAAC;QAC5E,MAAM,CAAC,cAAc,GAAG,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,CAAC;AAE1E,QAAA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;AAChE,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC3B,IAAI,CAAC,cAAc,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,2BAA2B,CAAC,WAAW,EAAE,CAAC;AAC/C,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;KAC/B;AA0BD;;;;AAIG;IACH,gBAAgB,CAAC,CAAS,EAAE,CAAS,EAAA;AACnC,QAAA,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,IAAI,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;KACzE;AAED;;;;;;AAMG;AACH,IAAA,gCAAgC,CAAC,IAAa,EAAE,CAAS,EAAE,CAAS,EAAA;QAClE,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KACxE;AAED;;;;;AAKG;AACH,IAAA,WAAW,CAAC,IAAa,EAAE,CAAS,EAAE,CAAS,EAAA;QAC7C,IACE,CAAC,IAAI,CAAC,QAAQ;YACd,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;YACxC,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,EAChC;AACA,YAAA,OAAO,KAAK,CAAC;SACd;AAED,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAuB,CAAC;;;QAI5F,IAAI,CAAC,gBAAgB,EAAE;AACrB,YAAA,OAAO,KAAK,CAAC;SACd;QAED,MAAM,aAAa,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;;;;;;;QAQlD,OAAO,gBAAgB,KAAK,aAAa,IAAI,aAAa,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;KACvF;AAED;;;AAGG;IACH,eAAe,CAAC,OAAoB,EAAE,KAAgB,EAAA;AACpD,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC;AAE5C,QAAA,IACE,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;AAC5B,YAAA,KAAK,CAAC,KAAK,CAAC,IAAI,IAAG;;;;;gBAKjB,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;aAC/E,CAAC,EACF;AACA,YAAA,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC5B,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;AACzB,gBAAA,SAAS,EAAE,OAAO;AAClB,gBAAA,QAAQ,EAAE,IAAI;gBACd,KAAK;AACN,aAAA,CAAC,CAAC;SACJ;KACF;AAED;;;AAGG;AACH,IAAA,cAAc,CAAC,OAAoB,EAAA;AACjC,QAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACrC,QAAA,IAAI,CAAC,2BAA2B,CAAC,WAAW,EAAE,CAAC;AAC/C,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAC,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;KAClE;AAED;;;AAGG;IACK,qBAAqB,GAAA;AAC3B,QAAA,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC,iBAAiB;AACtD,aAAA,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;aAC/B,SAAS,CAAC,KAAK,IAAG;AACjB,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;gBACrB,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBAEnE,IAAI,gBAAgB,EAAE;AACpB,oBAAA,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,gBAAgB,CAAC,GAAG,EAAE,gBAAgB,CAAC,IAAI,CAAC,CAAC;iBAChF;aACF;AAAM,iBAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;gBAC7B,IAAI,CAAC,qBAAqB,EAAE,CAAC;aAC9B;AACH,SAAC,CAAC,CAAC;KACN;AAED;;;;;AAKG;IACK,cAAc,GAAA;AACpB,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC3B,MAAM,UAAU,GAAG,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;YAC/D,IAAI,CAAC,iBAAiB,IAAI,UAAU,IAAI,IAAI,CAAC,SAAS,CAAa,CAAC;SACrE;QAED,OAAO,IAAI,CAAC,iBAAiB,CAAC;KAC/B;;IAGO,wBAAwB,GAAA;AAC9B,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa;AACpC,aAAA,sBAAsB,EAAE;aACxB,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;AACrC,QAAA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,eAAe,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC;KAChF;AACF,CAAA;AAED;;;;AAIG;AACH,SAAS,0BAA0B,CAAC,UAAmB,EAAE,QAAgB,EAAA;IACvE,MAAM,EAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAC,GAAG,UAAU,CAAC;AACzC,IAAA,MAAM,UAAU,GAAG,MAAM,GAAG,0BAA0B,CAAC;AAEvD,IAAA,IAAI,QAAQ,IAAI,GAAG,GAAG,UAAU,IAAI,QAAQ,IAAI,GAAG,GAAG,UAAU,EAAE;QAChE,OAAO,2BAA2B,CAAC,EAAE,CAAC;KACvC;AAAM,SAAA,IAAI,QAAQ,IAAI,MAAM,GAAG,UAAU,IAAI,QAAQ,IAAI,MAAM,GAAG,UAAU,EAAE;QAC7E,OAAO,2BAA2B,CAAC,IAAI,CAAC;KACzC;IAED,OAAO,2BAA2B,CAAC,IAAI,CAAC;AAC1C,CAAC;AAED;;;;AAIG;AACH,SAAS,4BAA4B,CAAC,UAAmB,EAAE,QAAgB,EAAA;IACzE,MAAM,EAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAC,GAAG,UAAU,CAAC;AACxC,IAAA,MAAM,UAAU,GAAG,KAAK,GAAG,0BAA0B,CAAC;AAEtD,IAAA,IAAI,QAAQ,IAAI,IAAI,GAAG,UAAU,IAAI,QAAQ,IAAI,IAAI,GAAG,UAAU,EAAE;QAClE,OAAO,6BAA6B,CAAC,IAAI,CAAC;KAC3C;AAAM,SAAA,IAAI,QAAQ,IAAI,KAAK,GAAG,UAAU,IAAI,QAAQ,IAAI,KAAK,GAAG,UAAU,EAAE;QAC3E,OAAO,6BAA6B,CAAC,KAAK,CAAC;KAC5C;IAED,OAAO,6BAA6B,CAAC,IAAI,CAAC;AAC5C,CAAC;AAED;;;;;;;;AAQG;AACH,SAAS,0BAA0B,CACjC,OAAoB,EACpB,UAAmB,EACnB,SAAoB,EACpB,QAAgB,EAChB,QAAgB,EAAA;IAEhB,MAAM,gBAAgB,GAAG,0BAA0B,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IAC1E,MAAM,kBAAkB,GAAG,4BAA4B,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;AAC9E,IAAA,IAAI,uBAAuB,GAAG,2BAA2B,CAAC,IAAI,CAAC;AAC/D,IAAA,IAAI,yBAAyB,GAAG,6BAA6B,CAAC,IAAI,CAAC;;;;;IAMnE,IAAI,gBAAgB,EAAE;AACpB,QAAA,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;AAEpC,QAAA,IAAI,gBAAgB,KAAK,2BAA2B,CAAC,EAAE,EAAE;AACvD,YAAA,IAAI,SAAS,GAAG,CAAC,EAAE;AACjB,gBAAA,uBAAuB,GAAG,2BAA2B,CAAC,EAAE,CAAC;aAC1D;SACF;aAAM,IAAI,OAAO,CAAC,YAAY,GAAG,SAAS,GAAG,OAAO,CAAC,YAAY,EAAE;AAClE,YAAA,uBAAuB,GAAG,2BAA2B,CAAC,IAAI,CAAC;SAC5D;KACF;IAED,IAAI,kBAAkB,EAAE;AACtB,QAAA,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;AAEtC,QAAA,IAAI,SAAS,KAAK,KAAK,EAAE;AACvB,YAAA,IAAI,kBAAkB,KAAK,6BAA6B,CAAC,KAAK,EAAE;;AAE9D,gBAAA,IAAI,UAAU,GAAG,CAAC,EAAE;AAClB,oBAAA,yBAAyB,GAAG,6BAA6B,CAAC,KAAK,CAAC;iBACjE;aACF;iBAAM,IAAI,OAAO,CAAC,WAAW,GAAG,UAAU,GAAG,OAAO,CAAC,WAAW,EAAE;AACjE,gBAAA,yBAAyB,GAAG,6BAA6B,CAAC,IAAI,CAAC;aAChE;SACF;aAAM;AACL,YAAA,IAAI,kBAAkB,KAAK,6BAA6B,CAAC,IAAI,EAAE;AAC7D,gBAAA,IAAI,UAAU,GAAG,CAAC,EAAE;AAClB,oBAAA,yBAAyB,GAAG,6BAA6B,CAAC,IAAI,CAAC;iBAChE;aACF;iBAAM,IAAI,OAAO,CAAC,WAAW,GAAG,UAAU,GAAG,OAAO,CAAC,WAAW,EAAE;AACjE,gBAAA,yBAAyB,GAAG,6BAA6B,CAAC,KAAK,CAAC;aACjE;SACF;KACF;AAED,IAAA,OAAO,CAAC,uBAAuB,EAAE,yBAAyB,CAAC,CAAC;AAC9D;;AC5wBA;AACA,MAAM,2BAA2B,GAAG,+BAA+B,CAAC;AAClE,IAAA,OAAO,EAAE,KAAK;AACd,IAAA,OAAO,EAAE,IAAI;AACd,CAAA,CAAC,CAAC;AAEH;AACA,MAAM,UAAU,GAAG,IAAI,GAAG,EAAkB,CAAC;AAE7C;;;AAGG;MASU,aAAa,CAAA;8GAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAb,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,mIAJd,EAAE,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,2FAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAID,aAAa,EAAA,UAAA,EAAA,CAAA;kBARzB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,UAAA,EAAA,IAAI,EAED,aAAA,EAAA,iBAAiB,CAAC,IAAI,YAC3B,EAAE,EAAA,eAAA,EACK,uBAAuB,CAAC,MAAM,EACzC,IAAA,EAAA,EAAC,2BAA2B,EAAE,EAAE,EAAC,EAAA,MAAA,EAAA,CAAA,2FAAA,CAAA,EAAA,CAAA;;AAIzC;;;;AAIG;AACH;AACA;AACA;MAEa,gBAAgB,CAAA;IAgD3B,WACU,CAAA,OAAe,EACL,SAAc,EAAA;QADxB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;AA/CjB,QAAA,IAAA,CAAA,OAAO,GAAG,MAAM,CAAC,cAAc,CAAC,CAAC;AACjC,QAAA,IAAA,CAAA,oBAAoB,GAAG,MAAM,CAAC,mBAAmB,CAAC,CAAC;;AAGnD,QAAA,IAAA,CAAA,cAAc,GAAG,IAAI,GAAG,EAAK,CAAC;;AAG9B,QAAA,IAAA,CAAA,cAAc,GAAG,IAAI,GAAG,EAAK,CAAC;;QAG9B,IAAoB,CAAA,oBAAA,GAAQ,EAAE,CAAC;;AAG/B,QAAA,IAAA,CAAA,gBAAgB,GAAG,IAAI,GAAG,EAM/B,CAAC;AAEJ;;;AAGG;QACK,IAAkB,CAAA,kBAAA,GAAG,CAAC,IAAO,KAAK,IAAI,CAAC,UAAU,EAAE,CAAC;AAE5D;;;AAGG;AACM,QAAA,IAAA,CAAA,WAAW,GAAqC,IAAI,OAAO,EAA2B,CAAC;AAEhG;;;AAGG;AACM,QAAA,IAAA,CAAA,SAAS,GAAqC,IAAI,OAAO,EAA2B,CAAC;AAE9F;;;;AAIG;AACM,QAAA,IAAA,CAAA,MAAM,GAAmB,IAAI,OAAO,EAAS,CAAC;AA+KvD;;;AAGG;AACK,QAAA,IAAA,CAAA,4BAA4B,GAAG,CAAC,KAAY,KAAI;YACtD,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;gBACxC,KAAK,CAAC,cAAc,EAAE,CAAC;aACxB;AACH,SAAC,CAAC;;AAGM,QAAA,IAAA,CAAA,4BAA4B,GAAG,CAAC,KAAiB,KAAI;YAC3D,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;;;;gBAIxC,IAAI,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE;oBAC3D,KAAK,CAAC,cAAc,EAAE,CAAC;iBACxB;AAED,gBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAC9B;AACH,SAAC,CAAC;AA/LA,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;KAC5B;;AAGD,IAAA,qBAAqB,CAAC,IAAO,EAAA;QAC3B,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAClC,YAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;SAC/B;KACF;;AAGD,IAAA,gBAAgB,CAAC,IAAO,EAAA;AACtB,QAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;;;;QAK9B,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,KAAK,CAAC,EAAE;AAClC,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;;;AAGlC,gBAAA,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAC7B,WAAW,EACX,IAAI,CAAC,4BAA4B,EACjC,2BAA2B,CAC5B,CAAC;AACJ,aAAC,CAAC,CAAC;SACJ;KACF;;AAGD,IAAA,mBAAmB,CAAC,IAAO,EAAA;AACzB,QAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;KAClC;;AAGD,IAAA,cAAc,CAAC,IAAO,EAAA;AACpB,QAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACjC,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAExB,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,KAAK,CAAC,EAAE;AAClC,YAAA,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAChC,WAAW,EACX,IAAI,CAAC,4BAA4B,EACjC,2BAA2B,CAC5B,CAAC;SACH;KACF;AAED;;;;AAIG;IACH,aAAa,CAAC,IAAO,EAAE,KAA8B,EAAA;;AAEnD,QAAA,IAAI,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;YAChD,OAAO;SACR;QAED,IAAI,CAAC,WAAW,EAAE,CAAC;AACnB,QAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAErC,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1C,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;;;;AAKpD,YAAA,IAAI,CAAC,gBAAgB;iBAClB,GAAG,CAAC,YAAY,GAAG,UAAU,GAAG,SAAS,EAAE;AAC1C,gBAAA,OAAO,EAAE,CAAC,CAAQ,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAA4B,CAAC;AACxE,gBAAA,OAAO,EAAE,IAAI;aACd,CAAC;iBACD,GAAG,CAAC,QAAQ,EAAE;AACb,gBAAA,OAAO,EAAE,CAAC,CAAQ,KAAK,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;;AAG1C,gBAAA,OAAO,EAAE,IAAI;aACd,CAAC;;;;;iBAKD,GAAG,CAAC,aAAa,EAAE;gBAClB,OAAO,EAAE,IAAI,CAAC,4BAA4B;AAC1C,gBAAA,OAAO,EAAE,2BAA2B;AACrC,aAAA,CAAC,CAAC;;;YAIL,IAAI,CAAC,YAAY,EAAE;AACjB,gBAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,EAAE;AACrC,oBAAA,OAAO,EAAE,CAAC,CAAQ,KAAK,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAe,CAAC;AAC7D,oBAAA,OAAO,EAAE,2BAA2B;AACrC,iBAAA,CAAC,CAAC;aACJ;AAED,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;gBAClC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,IAAI,KAAI;AAC7C,oBAAA,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;AACxE,iBAAC,CAAC,CAAC;AACL,aAAC,CAAC,CAAC;SACJ;KACF;;AAGD,IAAA,YAAY,CAAC,IAAO,EAAA;QAClB,MAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAEtD,QAAA,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAE3C,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC1C,IAAI,CAAC,qBAAqB,EAAE,CAAC;aAC9B;SACF;KACF;;AAGD,IAAA,UAAU,CAAC,IAAO,EAAA;QAChB,OAAO,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;KACrD;AAED;;;;;;AAMG;AACH,IAAA,QAAQ,CAAC,UAAwC,EAAA;AAC/C,QAAA,MAAM,OAAO,GAAwB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEnD,IAAI,UAAU,IAAI,UAAU,KAAK,IAAI,CAAC,SAAS,EAAE;;;;YAI/C,OAAO,CAAC,IAAI,CACV,IAAI,UAAU,CAAC,CAAC,QAAyB,KAAI;AAC3C,gBAAA,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;oBACzC,MAAM,YAAY,GAAG,IAAI,CAAC;AAC1B,oBAAA,MAAM,QAAQ,GAAG,CAAC,KAAY,KAAI;AAChC,wBAAA,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE;AACpC,4BAAA,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;yBACtB;AACH,qBAAC,CAAC;oBAED,UAAyB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;AAE9E,oBAAA,OAAO,MAAK;wBACT,UAAyB,CAAC,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;AACnF,qBAAC,CAAC;AACJ,iBAAC,CAAC,CAAC;aACJ,CAAC,CACH,CAAC;SACH;AAED,QAAA,OAAO,KAAK,CAAC,GAAG,OAAO,CAAC,CAAC;KAC1B;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC;AACvE,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC5E,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,QAAA,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;AAC5B,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;KAC3B;;IA2BO,qBAAqB,GAAA;QAC3B,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,IAAI,KAAI;AAC7C,YAAA,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;AAC3E,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;KAC/B;;;IAIO,WAAW,GAAA;QACjB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;AACjC,YAAA,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAE7B,YAAA,MAAM,YAAY,GAAG,eAAe,CAAC,aAAa,EAAE;gBAClD,mBAAmB,EAAE,IAAI,CAAC,oBAAoB;AAC/C,aAAA,CAAC,CAAC;AAEH,YAAA,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAK;AAC1B,gBAAA,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAChC,gBAAA,IAAI,UAAU,CAAC,IAAI,KAAK,CAAC,EAAE;oBACzB,YAAY,CAAC,OAAO,EAAE,CAAC;iBACxB;AACH,aAAC,CAAC,CAAC;SACJ;KACF;AA/QU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,wCAkDjB,QAAQ,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAlDP,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,cADJ,MAAM,EAAA,CAAA,CAAA,EAAA;;2FAClB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAD5B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;0BAmD3B,MAAM;2BAAC,QAAQ,CAAA;;;AC5FpB;AACA,MAAM,cAAc,GAAG;AACrB,IAAA,kBAAkB,EAAE,CAAC;AACrB,IAAA,+BAA+B,EAAE,CAAC;CACnC,CAAC;AAEF;;AAEG;MAEU,QAAQ,CAAA;AACnB,IAAA,WAAA,CAC4B,SAAc,EAChC,OAAe,EACf,cAA6B,EAC7B,iBAAyD,EAAA;QAHvC,IAAS,CAAA,SAAA,GAAT,SAAS,CAAK;QAChC,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QACf,IAAc,CAAA,cAAA,GAAd,cAAc,CAAe;QAC7B,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAwC;KAC/D;AAEJ;;;;AAIG;AACH,IAAA,UAAU,CACR,OAA8C,EAC9C,MAAA,GAAwB,cAAc,EAAA;QAEtC,OAAO,IAAI,OAAO,CAChB,OAAO,EACP,MAAM,EACN,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,iBAAiB,CACvB,CAAC;KACH;AAED;;;AAGG;AACH,IAAA,cAAc,CAAU,OAA8C,EAAA;QACpE,OAAO,IAAI,WAAW,CACpB,OAAO,EACP,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,cAAc,CACpB,CAAC;KACH;AAvCU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,QAAQ,kBAET,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,gBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAFP,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,QAAQ,cADI,MAAM,EAAA,CAAA,CAAA,EAAA;;2FAClB,QAAQ,EAAA,UAAA,EAAA,CAAA;kBADpB,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;0BAG3B,MAAM;2BAAC,QAAQ,CAAA;;;AChBpB;;;;;AAKG;MACU,eAAe,GAAG,IAAI,cAAc,CAAU,iBAAiB;;ACT5E;;;;AAIG;AACa,SAAA,iBAAiB,CAAC,IAAU,EAAE,IAAY,EAAA;AACxD,IAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,EAAE;AACvB,QAAA,MAAM,KAAK,CACT,CAAG,EAAA,IAAI,CAAwC,sCAAA,CAAA,GAAG,CAA0B,uBAAA,EAAA,IAAI,CAAC,QAAQ,CAAI,EAAA,CAAA,CAC9F,CAAC;KACH;AACH;;ACKA;;;;AAIG;MACU,eAAe,GAAG,IAAI,cAAc,CAAgB,eAAe,EAAE;AAElF;MASa,aAAa,CAAA;;AAKxB,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACvB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC/B;IAGD,WACS,CAAA,OAAgC,EACkB,WAAqB,EAAA;QADvE,IAAO,CAAA,OAAA,GAAP,OAAO,CAAyB;QACkB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAU;;AAfvE,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,OAAO,EAAiB,CAAC;QAW9C,IAAS,CAAA,SAAA,GAAG,KAAK,CAAC;AAMxB,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,iBAAiB,CAAC,OAAO,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC;SAC3D;AAED,QAAA,WAAW,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;KAC/B;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,WAAW,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC;AACtC,QAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;KAC/B;AA7BU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,4CAiBd,eAAe,EAAA,QAAA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAjBd,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,EAK2B,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,uBAAA,EAAA,UAAA,EAAA,gBAAgB,CAPxD,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,iBAAA,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,eAAe,EAAE,WAAW,EAAE,aAAa,EAAC,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAExD,aAAa,EAAA,UAAA,EAAA,CAAA;kBARzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iBAAiB;AAC3B,oBAAA,UAAU,EAAE,IAAI;AAChB,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,iBAAiB;AAC3B,qBAAA;oBACD,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,eAAe,EAAE,WAAW,EAAe,aAAA,EAAC,CAAC;AACpE,iBAAA,CAAA;;0BAkBI,MAAM;2BAAC,eAAe,CAAA;;0BAAG,QAAQ;;0BAAI,QAAQ;yCAX5C,QAAQ,EAAA,CAAA;sBADX,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,uBAAuB,EAAE,SAAS,EAAE,gBAAgB,EAAC,CAAA;;;ACtBtE;;;AAGG;MACU,eAAe,GAAG,IAAI,cAAc,CAAiB,iBAAiB;;ACyBnF,MAAM,eAAe,GAAG,UAAU,CAAC;AAEnC;;;;AAIG;MACU,aAAa,GAAG,IAAI,cAAc,CAAc,aAAa,EAAE;AAE5E;MAYa,OAAO,CAAA;aAEH,IAAc,CAAA,cAAA,GAAc,EAAd,CAAiB,EAAA;;AA0C9C,IAAA,IACI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;KAC9E;IACD,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;KACzC;AAqFD,IAAA,WAAA;;IAES,OAAgC;;IAEe,aAA0B;AAChF;;;AAGG;AACe,IAAA,SAAc,EACxB,OAAe,EACf,iBAAmC,EACN,MAAsB,EACvC,IAAoB,EACxC,QAAkB,EACV,kBAAqC,EACQ,WAA2B,EACvB,WAAqB,EAAA;QAfvE,IAAO,CAAA,OAAA,GAAP,OAAO,CAAyB;QAEe,IAAa,CAAA,aAAA,GAAb,aAAa,CAAa;QAMxE,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QACf,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAkB;QAEvB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAgB;QAEhC,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAmB;QACQ,IAAW,CAAA,WAAA,GAAX,WAAW,CAAgB;QACvB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAU;AAxJ/D,QAAA,IAAA,CAAA,UAAU,GAAG,IAAI,OAAO,EAAQ,CAAC;AAE1C,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,eAAe,CAAkB,EAAE,CAAC,CAAC;;AAmFzB,QAAA,IAAA,CAAA,OAAO,GACxC,IAAI,YAAY,EAAgB,CAAC;;AAGC,QAAA,IAAA,CAAA,QAAQ,GAC1C,IAAI,YAAY,EAAkB,CAAC;;AAGJ,QAAA,IAAA,CAAA,KAAK,GAA6B,IAAI,YAAY,EAAc,CAAC;;AAG/D,QAAA,IAAA,CAAA,OAAO,GAAoC,IAAI,YAAY,EAE3F,CAAC;;AAG8B,QAAA,IAAA,CAAA,MAAM,GAAmC,IAAI,YAAY,EAExF,CAAC;;AAG+B,QAAA,IAAA,CAAA,OAAO,GAAmC,IAAI,YAAY,EAE1F,CAAC;AAEJ;;;AAGG;AAEM,QAAA,IAAA,CAAA,KAAK,GAA+B,IAAI,UAAU,CACzD,CAAC,QAAkC,KAAI;AACrC,YAAA,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK;AACrC,iBAAA,IAAI,CACH,GAAG,CAAC,UAAU,KAAK;AACjB,gBAAA,MAAM,EAAE,IAAI;gBACZ,eAAe,EAAE,UAAU,CAAC,eAAe;gBAC3C,KAAK,EAAE,UAAU,CAAC,KAAK;gBACvB,KAAK,EAAE,UAAU,CAAC,KAAK;gBACvB,QAAQ,EAAE,UAAU,CAAC,QAAQ;AAC9B,aAAA,CAAC,CAAC,CACJ;iBACA,SAAS,CAAC,QAAQ,CAAC,CAAC;AAEvB,YAAA,OAAO,MAAK;gBACV,YAAY,CAAC,WAAW,EAAE,CAAC;AAC7B,aAAC,CAAC;AACJ,SAAC,CACF,CAAC;QAqBA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,OAAO,EAAE;AAC3C,YAAA,kBAAkB,EAChB,MAAM,IAAI,MAAM,CAAC,kBAAkB,IAAI,IAAI,GAAG,MAAM,CAAC,kBAAkB,GAAG,CAAC;AAC7E,YAAA,+BAA+B,EAC7B,MAAM,IAAI,MAAM,CAAC,+BAA+B,IAAI,IAAI;kBACpD,MAAM,CAAC,+BAA+B;AACxC,kBAAE,CAAC;YACP,MAAM,EAAE,MAAM,EAAE,MAAM;AACvB,SAAA,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;;;;AAK1B,QAAA,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAElC,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;SAC9B;;;;;;;;QASD,IAAI,aAAa,EAAE;YACjB,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;AAC7D,YAAA,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SAC7B;AAED,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAChC,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KACnC;AAED;;;AAGG;IACH,qBAAqB,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,qBAAqB,EAAE,CAAC;KAC9C;;IAGD,cAAc,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC;KACvC;;IAGD,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;KACvB;AAED;;AAEG;IACH,mBAAmB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CAAC;KAC5C;AAED;;;AAGG;AACH,IAAA,mBAAmB,CAAC,KAAY,EAAA;AAC9B,QAAA,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;KAC1C;IAED,eAAe,GAAA;;;AAGb,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;;;;;YAKlC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;gBAC7E,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAE7B,gBAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;oBACzB,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;iBAC1D;AACH,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;KACJ;AAED,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,MAAM,kBAAkB,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAC;AAC1D,QAAA,MAAM,cAAc,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;;;AAInD,QAAA,IAAI,kBAAkB,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE;YACzD,IAAI,CAAC,kBAAkB,EAAE,CAAC;SAC3B;;QAGD,IAAI,cAAc,IAAI,CAAC,cAAc,CAAC,WAAW,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAC1E,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAC1D;KACF;IAED,WAAW,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,YAAA,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;SACrC;QAED,MAAM,KAAK,GAAG,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACnD,QAAA,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACd,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SACzC;;AAGD,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;AACzB,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;AACvB,YAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;AAC3B,YAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;AAC1B,SAAC,CAAC,CAAC;KACJ;AAED,IAAA,UAAU,CAAC,MAAqB,EAAA;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;AACzC,QAAA,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACrB,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KAC7B;AAED,IAAA,aAAa,CAAC,MAAqB,EAAA;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;QACzC,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAEtC,QAAA,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;AACd,YAAA,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACzB,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC7B;KACF;AAED,IAAA,mBAAmB,CAAC,OAAuB,EAAA;AACzC,QAAA,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC;KACjC;AAED,IAAA,qBAAqB,CAAC,OAAuB,EAAA;AAC3C,QAAA,IAAI,OAAO,KAAK,IAAI,CAAC,gBAAgB,EAAE;AACrC,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;SAC9B;KACF;AAED,IAAA,uBAAuB,CAAC,WAA+B,EAAA;AACrD,QAAA,IAAI,CAAC,oBAAoB,GAAG,WAAW,CAAC;KACzC;AAED,IAAA,yBAAyB,CAAC,WAA+B,EAAA;AACvD,QAAA,IAAI,WAAW,KAAK,IAAI,CAAC,oBAAoB,EAAE;AAC7C,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;SAClC;KACF;;IAGO,kBAAkB,GAAA;AACxB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,aAA4B,CAAC;QAC1D,IAAI,WAAW,GAAG,OAAO,CAAC;AAC1B,QAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,WAAW;gBACT,OAAO,CAAC,OAAO,KAAK,SAAS;sBACxB,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAiB;AAC5D;wBACG,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAiB,CAAC;SACjF;QAED,IAAI,WAAW,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAClE,YAAA,iBAAiB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;SAC3C;QAED,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,WAAW,IAAI,OAAO,CAAC,CAAC;KACvD;;IAGO,mBAAmB,GAAA;AACzB,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC;QAEtC,IAAI,CAAC,QAAQ,EAAE;AACb,YAAA,OAAO,IAAI,CAAC;SACb;AAED,QAAA,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;YAChC,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAc,QAAQ,CAAC,CAAC;SAClE;AAED,QAAA,OAAO,aAAa,CAAC,QAAQ,CAAC,CAAC;KAChC;;AAGO,IAAA,WAAW,CAAC,GAAwB,EAAA;AAC1C,QAAA,GAAG,CAAC,aAAa,CAAC,SAAS,CAAC,MAAK;AAC/B,YAAA,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE;AACrB,gBAAA,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;AACtB,gBAAA,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;AAC3C,gBAAA,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB;AAC3C,sBAAE;AACE,wBAAA,QAAQ,EAAE,IAAI,CAAC,oBAAoB,CAAC,WAAW;AAC/C,wBAAA,OAAO,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI;wBACvC,aAAa,EAAE,IAAI,CAAC,iBAAiB;AACtC,qBAAA;sBACD,IAAI,CAAC;AACT,gBAAA,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB;AACnC,sBAAE;AACE,wBAAA,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,WAAW;AAC3C,wBAAA,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI;AACnC,wBAAA,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,SAAS;wBAC1C,aAAa,EAAE,IAAI,CAAC,iBAAiB;AACtC,qBAAA;sBACD,IAAI,CAAC;AAET,gBAAA,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,gBAAA,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,gBAAA,GAAG,CAAC,cAAc;AAChB,oBAAA,OAAO,cAAc,KAAK,QAAQ,IAAI,cAAc;AAClD,0BAAE,cAAc;AAChB,0BAAE,oBAAoB,CAAC,cAAc,CAAC,CAAC;AAC3C,gBAAA,GAAG,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;AAC/C,gBAAA,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;gBACrC,GAAG;AACA,qBAAA,mBAAmB,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;qBAC/C,uBAAuB,CAAC,WAAW,CAAC;qBACpC,mBAAmB,CAAC,OAAO,CAAC;AAC5B,qBAAA,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,IAAI,QAAQ,CAAC,CAAC;gBAE3D,IAAI,GAAG,EAAE;AACP,oBAAA,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;iBAC9B;aACF;AACH,SAAC,CAAC,CAAC;;AAGH,QAAA,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;;AAE7C,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;gBAC1C,OAAO;aACR;;;YAID,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,aAAa,CAAC;YACtD,OAAO,MAAM,EAAE;gBACb,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE;oBAC9C,GAAG,CAAC,UAAU,CACZ,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,IAAG;AACjC,wBAAA,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,KAAK,MAAM,CAAC;AAC/C,qBAAC,CAAC,EAAE,QAAQ,IAAI,IAAI,CACrB,CAAC;oBACF,MAAM;iBACP;AACD,gBAAA,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC;aAC/B;AACH,SAAC,CAAC,CAAC;KACJ;;AAGO,IAAA,aAAa,CAAC,GAAwB,EAAA;AAC5C,QAAA,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,IAAG;AACjC,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,KAAK,EAAC,CAAC,CAAC;;;AAI3D,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACzC,SAAC,CAAC,CAAC;AAEH,QAAA,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,YAAY,IAAG;AACpC,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,YAAY,CAAC,KAAK,EAAC,CAAC,CAAC;AAChE,SAAC,CAAC,CAAC;AAEH,QAAA,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,IAAG;AAC7B,YAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;AACd,gBAAA,MAAM,EAAE,IAAI;gBACZ,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,KAAK,EAAE,QAAQ,CAAC,KAAK;AACtB,aAAA,CAAC,CAAC;;;AAIH,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACzC,SAAC,CAAC,CAAC;AAEH,QAAA,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,IAAG;AACjC,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;AAChB,gBAAA,SAAS,EAAE,UAAU,CAAC,SAAS,CAAC,IAAI;AACpC,gBAAA,IAAI,EAAE,IAAI;gBACV,YAAY,EAAE,UAAU,CAAC,YAAY;AACtC,aAAA,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;AAEH,QAAA,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,IAAG;AAC/B,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;AACf,gBAAA,SAAS,EAAE,SAAS,CAAC,SAAS,CAAC,IAAI;AACnC,gBAAA,IAAI,EAAE,IAAI;AACX,aAAA,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;AAEH,QAAA,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,IAAG;AAChC,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;gBAChB,aAAa,EAAE,SAAS,CAAC,aAAa;gBACtC,YAAY,EAAE,SAAS,CAAC,YAAY;AACpC,gBAAA,iBAAiB,EAAE,SAAS,CAAC,iBAAiB,CAAC,IAAI;AACnD,gBAAA,SAAS,EAAE,SAAS,CAAC,SAAS,CAAC,IAAI;gBACnC,sBAAsB,EAAE,SAAS,CAAC,sBAAsB;AACxD,gBAAA,IAAI,EAAE,IAAI;gBACV,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,SAAS,EAAE,SAAS,CAAC,SAAS;gBAC9B,KAAK,EAAE,SAAS,CAAC,KAAK;AACvB,aAAA,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;KACJ;;AAGO,IAAA,eAAe,CAAC,MAAsB,EAAA;AAC5C,QAAA,MAAM,EACJ,QAAQ,EACR,cAAc,EACd,iBAAiB,EACjB,YAAY,EACZ,eAAe,EACf,gBAAgB,EAChB,mBAAmB,EACnB,gBAAgB,GACjB,GAAG,MAAM,CAAC;AAEX,QAAA,IAAI,CAAC,QAAQ,GAAG,gBAAgB,IAAI,IAAI,GAAG,KAAK,GAAG,gBAAgB,CAAC;AACpE,QAAA,IAAI,CAAC,cAAc,GAAG,cAAc,IAAI,CAAC,CAAC;QAE1C,IAAI,QAAQ,EAAE;AACZ,YAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;SAC1B;QAED,IAAI,iBAAiB,EAAE;AACrB,YAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;SAC5C;QAED,IAAI,YAAY,EAAE;AAChB,YAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;SAClC;QAED,IAAI,eAAe,EAAE;AACnB,YAAA,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;SACxC;QAED,IAAI,mBAAmB,EAAE;AACvB,YAAA,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;SAChD;QAED,IAAI,gBAAgB,EAAE;AACpB,YAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;SAC1C;KACF;;IAGO,qBAAqB,GAAA;;AAE3B,QAAA,IAAI,CAAC,QAAQ;aACV,IAAI;;QAEH,GAAG,CAAC,OAAO,IAAG;AACZ,YAAA,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;;;;YAK7D,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAChD,gBAAA,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aACnC;AAED,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;AAC5C,SAAC,CAAC;;AAEF,QAAA,SAAS,CAAC,CAAC,OAAwB,KAAI;YACrC,OAAO,KAAK,CACV,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CACpC,CAAC;SAChC,CAAC,EACF,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAC3B;aACA,SAAS,CAAC,cAAc,IAAG;;AAE1B,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC9B,YAAA,MAAM,MAAM,GAAG,cAAc,CAAC,OAAO,CAAC,aAAa,CAAC;YACpD,cAAc,CAAC,QAAQ,GAAG,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AACzF,SAAC,CAAC,CAAC;KACN;8GAjiBU,OAAO,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EA4IR,aAAa,EAKb,QAAA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,QAAQ,mEAGI,eAAe,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,IAAA,CAAA,cAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,QAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAIP,eAAe,EAAA,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EACX,eAAe,EAAA,QAAA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAzJtC,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,OAAO,EA4C2B,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,CAAA,aAAA,EAAA,MAAA,CAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,EAAA,UAAA,CAAA,EAAA,mBAAA,EAAA,CAAA,oBAAA,EAAA,qBAAA,CAAA,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,CAAA,EAAA,cAAA,EAAA,CAAA,mBAAA,EAAA,gBAAA,CAAA,EAAA,gBAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,EAAA,UAAA,EAAA,gBAAgB,CA9ClD,EAAA,iBAAA,EAAA,CAAA,0BAAA,EAAA,mBAAA,CAAA,EAAA,YAAA,EAAA,CAAA,qBAAA,EAAA,cAAA,CAAA,EAAA,gBAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,OAAA,EAAA,gBAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,KAAA,EAAA,cAAA,EAAA,OAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,eAAA,EAAA,OAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,cAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,yBAAA,EAAA,UAAA,EAAA,yBAAA,EAAA,uBAAA,EAAA,EAAA,cAAA,EAAA,UAAA,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,eAAe,EAAE,WAAW,EAAE,OAAO,EAAC,CAAC,EAAA,QAAA,EAAA,CAAA,SAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAElD,OAAO,EAAA,UAAA,EAAA,CAAA;kBAXnB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,WAAW;AACrB,oBAAA,QAAQ,EAAE,SAAS;AACnB,oBAAA,UAAU,EAAE,IAAI;AAChB,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,eAAe;AACxB,wBAAA,2BAA2B,EAAE,UAAU;AACvC,wBAAA,2BAA2B,EAAE,uBAAuB;AACrD,qBAAA;oBACD,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,eAAe,EAAE,WAAW,EAAS,OAAA,EAAC,CAAC;AAC9D,iBAAA,CAAA;;0BA6II,MAAM;2BAAC,aAAa,CAAA;;0BAAG,QAAQ;;0BAAI,QAAQ;;0BAK3C,MAAM;2BAAC,QAAQ,CAAA;;0BAGf,QAAQ;;0BAAI,MAAM;2BAAC,eAAe,CAAA;;0BAClC,QAAQ;;0BAGR,QAAQ;;0BAAI,IAAI;;0BAAI,MAAM;2BAAC,eAAe,CAAA;;0BAC1C,QAAQ;;0BAAI,QAAQ;;0BAAI,MAAM;2BAAC,eAAe,CAAA;yCA9I3B,IAAI,EAAA,CAAA;sBAAzB,KAAK;uBAAC,aAAa,CAAA;gBAGM,QAAQ,EAAA,CAAA;sBAAjC,KAAK;uBAAC,iBAAiB,CAAA;gBAOK,mBAAmB,EAAA,CAAA;sBAA/C,KAAK;uBAAC,oBAAoB,CAAA;gBAQD,eAAe,EAAA,CAAA;sBAAxC,KAAK;uBAAC,iBAAiB,CAAA;gBAMI,cAAc,EAAA,CAAA;sBAAzC,KAAK;uBAAC,mBAAmB,CAAA;gBAMQ,gBAAgB,EAAA,CAAA;sBAAjD,KAAK;uBAAC,yBAAyB,CAAA;gBAI5B,QAAQ,EAAA,CAAA;sBADX,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,iBAAiB,EAAE,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAgB3B,iBAAiB,EAAA,CAAA;sBAAnD,KAAK;uBAAC,0BAA0B,CAAA;gBAQH,YAAY,EAAA,CAAA;sBAAzC,KAAK;uBAAC,qBAAqB,CAAA;gBAeM,gBAAgB,EAAA,CAAA;sBAAjD,KAAK;uBAAC,yBAAyB,CAAA;gBAGG,OAAO,EAAA,CAAA;sBAAzC,MAAM;uBAAC,gBAAgB,CAAA;gBAIY,QAAQ,EAAA,CAAA;sBAA3C,MAAM;uBAAC,iBAAiB,CAAA;gBAIQ,KAAK,EAAA,CAAA;sBAArC,MAAM;uBAAC,cAAc,CAAA;gBAGa,OAAO,EAAA,CAAA;sBAAzC,MAAM;uBAAC,gBAAgB,CAAA;gBAKU,MAAM,EAAA,CAAA;sBAAvC,MAAM;uBAAC,eAAe,CAAA;gBAKY,OAAO,EAAA,CAAA;sBAAzC,MAAM;uBAAC,gBAAgB,CAAA;gBASf,KAAK,EAAA,CAAA;sBADb,MAAM;uBAAC,cAAc,CAAA;;;AClLxB;;;;AAIG;MACU,mBAAmB,GAAG,IAAI,cAAc,CACnD,kBAAkB,EAClB;AAEF;;;;;AAKG;MAOU,gBAAgB,CAAA;AAN7B,IAAA,WAAA,GAAA;;AAQW,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,GAAG,EAAK,CAAC;;QAI/B,IAAQ,CAAA,QAAA,GAAY,KAAK,CAAC;AAK3B,KAAA;IAHC,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;KACrB;8GAVU,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAhB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,gBAAgB,EAK2B,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,0BAAA,EAAA,UAAA,EAAA,gBAAgB,CAP3D,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,mBAAmB,EAAE,WAAW,EAAE,gBAAgB,EAAC,CAAC,EAAA,QAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAE/D,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAN5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oBAAoB;AAC9B,oBAAA,QAAQ,EAAE,kBAAkB;AAC5B,oBAAA,UAAU,EAAE,IAAI;oBAChB,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,mBAAmB,EAAE,WAAW,EAAkB,gBAAA,EAAC,CAAC;AAC3E,iBAAA,CAAA;8BAOC,QAAQ,EAAA,CAAA;sBADP,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,0BAA0B,EAAE,SAAS,EAAE,gBAAgB,EAAC,CAAA;;;ACDzE;AACA,IAAI,gBAAgB,GAAG,CAAC,CAAC;AAEzB;MAkBa,WAAW,CAAA;;aAQP,IAAU,CAAA,UAAA,GAAkB,EAAlB,CAAqB,EAAA;;AA6B9C,IAAA,IACI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;KAClE;IACD,IAAI,QAAQ,CAAC,KAAc,EAAA;;;;;QAKzB,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;KACrD;AAwDD,IAAA,WAAA;;IAES,OAAgC,EACvC,QAAkB,EACV,kBAAqC,EACrC,iBAAmC,EACvB,IAAqB,EAIjC,MAAsC,EACT,MAAuB,EAAA;QATrD,IAAO,CAAA,OAAA,GAAP,OAAO,CAAyB;QAE/B,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAmB;QACrC,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAkB;QACvB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAiB;QAIjC,IAAM,CAAA,MAAA,GAAN,MAAM,CAAgC;;AA/G/B,QAAA,IAAA,CAAA,UAAU,GAAG,IAAI,OAAO,EAAQ,CAAC;AAWlD;;;;AAIG;QAEH,IAAW,CAAA,WAAA,GAAoD,EAAE,CAAC;AAQlE;;;AAGG;AACM,QAAA,IAAA,CAAA,EAAE,GAAW,CAAA,cAAA,EAAiB,gBAAgB,EAAE,EAAE,CAAC;AAuB5D;;;AAGG;AAEH,QAAA,IAAA,CAAA,cAAc,GAAkD,MAAM,IAAI,CAAC;;AAI3E,QAAA,IAAA,CAAA,aAAa,GAAiE,MAAM,IAAI,CAAC;;AAYhF,QAAA,IAAA,CAAA,OAAO,GAAsC,IAAI,YAAY,EAAuB,CAAC;AAE9F;;AAEG;AAEM,QAAA,IAAA,CAAA,OAAO,GAAkC,IAAI,YAAY,EAAmB,CAAC;AAEtF;;;AAGG;AAEM,QAAA,IAAA,CAAA,MAAM,GAAiC,IAAI,YAAY,EAAkB,CAAC;;AAI1E,QAAA,IAAA,CAAA,MAAM,GAAsC,IAAI,YAAY,EAAuB,CAAC;AAE7F;;;;;;AAMG;AACK,QAAA,IAAA,CAAA,cAAc,GAAG,IAAI,GAAG,EAAW,CAAC;AAe1C,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,iBAAiB,CAAC,OAAO,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;SACzD;QAED,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;AACrD,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC;QAE9B,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;SAC9B;QAED,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,CAAC,IAAsB,EAAE,IAA8B,KAAI;AAC5F,YAAA,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AACnD,SAAC,CAAC;AAEF,QAAA,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,CAChC,KAAa,EACb,IAAsB,EACtB,IAA8B,KAC5B;AACF,YAAA,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AACzD,SAAC,CAAC;AAEF,QAAA,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACpD,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACtC,QAAA,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAElC,IAAI,MAAM,EAAE;AACV,YAAA,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;SACzB;KACF;;AAGD,IAAA,OAAO,CAAC,IAAa,EAAA;AACnB,QAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAE9B,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,EAAE;YAClC,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAC1B;KACF;;AAGD,IAAA,UAAU,CAAC,IAAa,EAAA;AACtB,QAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAEjC,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,EAAE;YAClC,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAC1B;KACF;;IAGD,cAAc,GAAA;AACZ,QAAA,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAU,EAAE,CAAU,KAAI;AACrE,YAAA,MAAM,gBAAgB,GAAG,CAAC,CAAC,QAAQ;AAChC,iBAAA,iBAAiB,EAAE;iBACnB,uBAAuB,CAAC,CAAC,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAAC,CAAC;;;;AAK3D,YAAA,OAAO,gBAAgB,GAAG,IAAI,CAAC,2BAA2B,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACtE,SAAC,CAAC,CAAC;KACJ;IAED,WAAW,GAAA;QACT,MAAM,KAAK,GAAG,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAEnD,QAAA,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACd,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SACzC;AAED,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SACjC;AAED,QAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;AAC5B,QAAA,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;AAC5B,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;KAC5B;;AAGO,IAAA,2BAA2B,CAAC,GAA6B,EAAA;AAC/D,QAAA,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,IAAI,CAAC,IAAI,CAAC,MAAM;AACb,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC5D,iBAAA,SAAS,CAAC,KAAK,IAAI,GAAG,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;SACjD;AAED,QAAA,GAAG,CAAC,aAAa,CAAC,SAAS,CAAC,MAAK;AAC/B,YAAA,MAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,IAAI,IAAG;AACxD,gBAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC5B,oBAAA,MAAM,qBAAqB,GAAG,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC;AAEpF,oBAAA,IAAI,CAAC,qBAAqB,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAC7E,wBAAA,OAAO,CAAC,IAAI,CAAC,2DAA2D,IAAI,CAAA,CAAA,CAAG,CAAC,CAAC;qBAClF;AAED,oBAAA,OAAO,qBAAsB,CAAC;iBAC/B;AAED,gBAAA,OAAO,IAAI,CAAC;AACd,aAAC,CAAC,CAAC;AAEH,YAAA,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,IAAG;oBAChC,IAAI,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;AACjC,wBAAA,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;qBACrB;AACH,iBAAC,CAAC,CAAC;aACJ;;;AAID,YAAA,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE;AACpC,gBAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB;AAC7C,qBAAA,2BAA2B,CAAC,IAAI,CAAC,OAAO,CAAC;AACzC,qBAAA,GAAG,CAAC,UAAU,IAAI,UAAU,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,CAAC;AAC/D,gBAAA,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,CAAC;;;AAI3D,gBAAA,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;aACxC;AAED,YAAA,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,YAAA,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,YAAA,GAAG,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;AAC3C,YAAA,GAAG,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;YACjD,GAAG,CAAC,cAAc,GAAG,oBAAoB,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;YAClE,GAAG;iBACA,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC;AAC1F,iBAAA,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AACvC,SAAC,CAAC,CAAC;KACJ;;AAGO,IAAA,aAAa,CAAC,GAA6B,EAAA;AACjD,QAAA,GAAG,CAAC,aAAa,CAAC,SAAS,CAAC,MAAK;YAC/B,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACzB,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACzC,SAAC,CAAC,CAAC;AAEH,QAAA,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,IAAG;AAC5B,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;AAChB,gBAAA,SAAS,EAAE,IAAI;AACf,gBAAA,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI;gBACrB,YAAY,EAAE,KAAK,CAAC,YAAY;AACjC,aAAA,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;AAEH,QAAA,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,IAAG;AAC3B,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;AACf,gBAAA,SAAS,EAAE,IAAI;AACf,gBAAA,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI;AACtB,aAAA,CAAC,CAAC;AACH,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACzC,SAAC,CAAC,CAAC;AAEH,QAAA,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,IAAG;AAC3B,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gBACf,aAAa,EAAE,KAAK,CAAC,aAAa;gBAClC,YAAY,EAAE,KAAK,CAAC,YAAY;AAChC,gBAAA,SAAS,EAAE,IAAI;AACf,gBAAA,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI;AACtB,aAAA,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;AAEH,QAAA,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,IAAG;AAChC,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;gBAChB,aAAa,EAAE,SAAS,CAAC,aAAa;gBACtC,YAAY,EAAE,SAAS,CAAC,YAAY;AACpC,gBAAA,iBAAiB,EAAE,SAAS,CAAC,iBAAiB,CAAC,IAAI;AACnD,gBAAA,SAAS,EAAE,SAAS,CAAC,SAAS,CAAC,IAAI;AACnC,gBAAA,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,IAAI;gBACzB,sBAAsB,EAAE,SAAS,CAAC,sBAAsB;gBACxD,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,SAAS,EAAE,SAAS,CAAC,SAAS;gBAC9B,KAAK,EAAE,SAAS,CAAC,KAAK;AACvB,aAAA,CAAC,CAAC;;;AAIH,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACzC,SAAC,CAAC,CAAC;QAEH,KAAK,CAAC,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,MAC1D,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CACvC,CAAC;KACH;;AAGO,IAAA,eAAe,CAAC,MAAsB,EAAA;AAC5C,QAAA,MAAM,EAAC,QAAQ,EAAE,gBAAgB,EAAE,eAAe,EAAE,sBAAsB,EAAE,eAAe,EAAC,GAC1F,MAAM,CAAC;AAET,QAAA,IAAI,CAAC,QAAQ,GAAG,gBAAgB,IAAI,IAAI,GAAG,KAAK,GAAG,gBAAgB,CAAC;AACpE,QAAA,IAAI,CAAC,eAAe,GAAG,eAAe,IAAI,IAAI,GAAG,KAAK,GAAG,eAAe,CAAC;AACzE,QAAA,IAAI,CAAC,kBAAkB,GAAG,sBAAsB,IAAI,IAAI,GAAG,KAAK,GAAG,sBAAsB,CAAC;AAC1F,QAAA,IAAI,CAAC,WAAW,GAAG,eAAe,IAAI,UAAU,CAAC;QAEjD,IAAI,QAAQ,EAAE;AACZ,YAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;SAC1B;KACF;;IAGO,iBAAiB,GAAA;QACvB,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;KAC/E;8GArUU,WAAW,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,QAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,EAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,IAAA,CAAA,cAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EA+GZ,mBAAmB,EAAA,QAAA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAGP,eAAe,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAlH1B,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,WAAW,oUAqC2B,gBAAgB,CAAA,EAAA,eAAA,EAAA,CAAA,4BAAA,EAAA,iBAAA,EAcT,gBAAgB,CAAA,EAAA,cAAA,EAAA,CAAA,2BAAA,EAAA,gBAAA,CAAA,EAAA,aAAA,EAAA,CAAA,0BAAA,EAAA,eAAA,CAAA,EAAA,kBAAA,EAAA,CAAA,+BAAA,EAAA,oBAAA,EAeb,gBAAgB,CA/EhE,EAAA,cAAA,EAAA,CAAA,2BAAA,EAAA,gBAAA,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,OAAA,EAAA,oBAAA,EAAA,OAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,mBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,SAAA,EAAA,IAAA,EAAA,8BAAA,EAAA,UAAA,EAAA,8BAAA,EAAA,2BAAA,EAAA,+BAAA,EAAA,4BAAA,EAAA,EAAA,cAAA,EAAA,eAAA,EAAA,EAAA,SAAA,EAAA;;AAET,YAAA,EAAC,OAAO,EAAE,mBAAmB,EAAE,QAAQ,EAAE,SAAS,EAAC;AACnD,YAAA,EAAC,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,WAAW,EAAC;AACnD,SAAA,EAAA,QAAA,EAAA,CAAA,aAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FASU,WAAW,EAAA,UAAA,EAAA,CAAA;kBAjBvB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,8BAA8B;AACxC,oBAAA,QAAQ,EAAE,aAAa;AACvB,oBAAA,UAAU,EAAE,IAAI;AAChB,oBAAA,SAAS,EAAE;;AAET,wBAAA,EAAC,OAAO,EAAE,mBAAmB,EAAE,QAAQ,EAAE,SAAS,EAAC;AACnD,wBAAA,EAAC,OAAO,EAAE,aAAa,EAAE,WAAW,aAAa,EAAC;AACnD,qBAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,eAAe;AACxB,wBAAA,WAAW,EAAE,IAAI;AACjB,wBAAA,gCAAgC,EAAE,UAAU;AAC5C,wBAAA,gCAAgC,EAAE,2BAA2B;AAC7D,wBAAA,iCAAiC,EAAE,4BAA4B;AAChE,qBAAA;AACF,iBAAA,CAAA;;0BA8GI,QAAQ;;0BACR,QAAQ;;0BACR,MAAM;2BAAC,mBAAmB,CAAA;;0BAC1B,QAAQ;;0BAER,QAAQ;;0BAAI,MAAM;2BAAC,eAAe,CAAA;yCA/FrC,WAAW,EAAA,CAAA;sBADV,KAAK;uBAAC,wBAAwB,CAAA;gBAIL,IAAI,EAAA,CAAA;sBAA7B,KAAK;uBAAC,iBAAiB,CAAA;gBAGS,WAAW,EAAA,CAAA;sBAA3C,KAAK;uBAAC,wBAAwB,CAAA;gBAMtB,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAGwB,QAAQ,EAAA,CAAA;sBAArC,KAAK;uBAAC,qBAAqB,CAAA;gBAIxB,QAAQ,EAAA,CAAA;sBADX,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,qBAAqB,EAAE,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAelE,eAAe,EAAA,CAAA;sBADd,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,4BAA4B,EAAE,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAQzE,cAAc,EAAA,CAAA;sBADb,KAAK;uBAAC,2BAA2B,CAAA;gBAKlC,aAAa,EAAA,CAAA;sBADZ,KAAK;uBAAC,0BAA0B,CAAA;gBAKjC,kBAAkB,EAAA,CAAA;sBADjB,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,+BAA+B,EAAE,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAK5E,cAAc,EAAA,CAAA;sBADb,KAAK;uBAAC,2BAA2B,CAAA;gBAKzB,OAAO,EAAA,CAAA;sBADf,MAAM;uBAAC,oBAAoB,CAAA;gBAOnB,OAAO,EAAA,CAAA;sBADf,MAAM;uBAAC,oBAAoB,CAAA;gBAQnB,MAAM,EAAA,CAAA;sBADd,MAAM;uBAAC,mBAAmB,CAAA;gBAKlB,MAAM,EAAA,CAAA;sBADd,MAAM;uBAAC,mBAAmB,CAAA;;;AChI7B;;;;AAIG;MACU,gBAAgB,GAAG,IAAI,cAAc,CAAiB,gBAAgB,EAAE;AAErF;;;AAGG;MAMU,cAAc,CAAA;AASzB,IAAA,WAAA,CAAmB,WAA2B,EAAA;QAA3B,IAAW,CAAA,WAAA,GAAX,WAAW,CAAgB;QARtC,IAAK,CAAA,KAAA,GAAG,MAAM,CAAC,eAAe,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;;QAMpB,IAAS,CAAA,SAAA,GAAY,KAAK,CAAC;AAG/D,QAAA,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC,IAAI,CAAC,CAAC;KACvC;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC,IAAI,CAAC,CAAC;KACzC;8GAfU,cAAc,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,WAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAd,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,cAAc,EAON,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,6BAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,gBAAgB,CATxB,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,cAAc,EAAC,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAE1D,cAAc,EAAA,UAAA,EAAA,CAAA;kBAL1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,6BAA6B;AACvC,oBAAA,UAAU,EAAE,IAAI;oBAChB,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAgB,cAAA,EAAC,CAAC;AACtE,iBAAA,CAAA;gFAKU,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAGgC,SAAS,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;;;AC/BtC;;;;AAIG;MACU,oBAAoB,GAAG,IAAI,cAAc,CAAqB,oBAAoB,EAAE;AAEjG;;;AAGG;MAMU,kBAAkB,CAAA;AAM7B,IAAA,WAAA,CAAmB,WAA2B,EAAA;QAA3B,IAAW,CAAA,WAAA,GAAX,WAAW,CAAgB;QALtC,IAAK,CAAA,KAAA,GAAG,MAAM,CAAC,eAAe,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;AAMxD,QAAA,IAAI,CAAC,KAAK,EAAE,uBAAuB,CAAC,IAAI,CAAC,CAAC;KAC3C;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC,IAAI,CAAC,CAAC;KAC7C;8GAZU,kBAAkB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,WAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAlB,kBAAkB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iCAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,SAAA,EAFlB,CAAC,EAAC,OAAO,EAAE,oBAAoB,EAAE,WAAW,EAAE,kBAAkB,EAAC,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAElE,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAL9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iCAAiC;AAC3C,oBAAA,UAAU,EAAE,IAAI;oBAChB,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,oBAAoB,EAAE,WAAW,EAAoB,kBAAA,EAAC,CAAC;AAC9E,iBAAA,CAAA;gFAKU,IAAI,EAAA,CAAA;sBAAZ,KAAK;;;ACbR,MAAM,oBAAoB,GAAG;IAC3B,WAAW;IACX,gBAAgB;IAChB,OAAO;IACP,aAAa;IACb,cAAc;IACd,kBAAkB;CACnB,CAAC;MAOW,cAAc,CAAA;8GAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAd,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,YAbzB,WAAW;YACX,gBAAgB;YAChB,OAAO;YACP,aAAa;YACb,cAAc;YACd,kBAAkB,CAAA,EAAA,OAAA,EAAA,CAKR,mBAAmB,EAV7B,WAAW;YACX,gBAAgB;YAChB,OAAO;YACP,aAAa;YACb,cAAc;YACd,kBAAkB,CAAA,EAAA,CAAA,CAAA,EAAA;AAQP,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,EAFd,SAAA,EAAA,CAAC,QAAQ,CAAC,YADX,mBAAmB,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAGlB,cAAc,EAAA,UAAA,EAAA,CAAA;kBAL1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,oBAAoB;AAC7B,oBAAA,OAAO,EAAE,CAAC,mBAAmB,EAAE,GAAG,oBAAoB,CAAC;oBACvD,SAAS,EAAE,CAAC,QAAQ,CAAC;AACtB,iBAAA,CAAA;;;AC/BD;;AAEG;;;;"}