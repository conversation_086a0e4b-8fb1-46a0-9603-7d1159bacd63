/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
export default [[["ky.", "kp.", "aamulla", "ap.", "ip.", "illalla", "yöllä"], ["keskiyöllä", "keskip.", "aamulla", "aamup.", "iltap.", "illalla", "yöllä"], ["keskiyöllä", "keskipäivällä", "aamulla", "aamupäivällä", "iltapäivällä", "illalla", "yöllä"]], [["ky.", "kp.", "aamu", "ap.", "ip.", "ilta", "yö"], ["keskiy<PERSON>", "keskip.", "aamu", "aamup.", "iltap.", "ilta", "yö"], ["keskiy<PERSON>", "keskipäivä", "aamu", "aamup<PERSON>iv<PERSON>", "iltapäivä", "ilta", "yö"]], ["00:00", "12:00", ["05:00", "10:00"], ["10:00", "12:00"], ["12:00", "18:00"], ["18:00", "23:00"], ["23:00", "05:00"]]];
//# sourceMappingURL=data:application/json;base64,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