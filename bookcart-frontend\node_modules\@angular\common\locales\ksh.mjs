/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 0)
        return 0;
    if (n === 1)
        return 1;
    return 5;
}
export default ["ksh", [["v.M.", "n.M."], u, ["<PERSON>r vörmid<PERSON>ach<PERSON>", "Uhr nommendaachs"]], [["v.M.", "n.M."], u, ["V<PERSON>rmeddaach", "<PERSON>mmendaach"]], [["S", "M", "D", "M", "D", "F", "S"], ["Su.", "Mo.", "Di.", "Me.", "Du.", "Fr.", "Sa."], ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]], u, [["J", "F", "<PERSON>", "A", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], ["<PERSON>", "<PERSON><PERSON>b", "<PERSON><PERSON>z", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>uj", "<PERSON><PERSON>p", "<PERSON>t", "<PERSON>", "<PERSON>z"], ["<PERSON><PERSON>wa", "<PERSON><PERSON><PERSON>wa", "<PERSON><PERSON><PERSON>z", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Juuli", "Oujoß", "Septämber", "Oktohber", "Novämber", "Dezämber"]], [["J", "F", "M", "A", "M", "J", "J", "O", "S", "O", "N", "D"], ["Jan.", "Fäb.", "Mäz.", "Apr.", "Mai", "Jun.", "Jul.", "Ouj.", "Säp.", "Okt.", "Nov.", "Dez."], ["Jannewa", "Fäbrowa", "Määz", "Aprell", "Mai", "Juuni", "Juuli", "Oujoß", "Septämber", "Oktohber", "Novämber", "Dezämber"]], [["vC", "nC"], ["v. Chr.", "n. Chr."], ["vür Krestos", "noh Krestos"]], 1, [6, 0], ["d. M. y", "d. MMM. y", "d. MMMM y", "EEEE, 'dä' d. MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", " ", ";", "%", "+", "−", "×10^", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0 %", "#,##0.00 ¤", "#E0"], "EUR", "€", "Euro", { "JPY": ["JP¥", "¥"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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