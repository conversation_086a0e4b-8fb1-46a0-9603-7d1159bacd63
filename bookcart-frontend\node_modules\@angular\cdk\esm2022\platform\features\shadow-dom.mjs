/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
let shadowDomIsSupported;
/** Checks whether the user's browser support Shadow DOM. */
export function _supportsShadowDom() {
    if (shadowDomIsSupported == null) {
        const head = typeof document !== 'undefined' ? document.head : null;
        shadowDomIsSupported = !!(head && (head.createShadowRoot || head.attachShadow));
    }
    return shadowDomIsSupported;
}
/** Gets the shadow root of an element, if supported and the element is inside the Shadow DOM. */
export function _getShadowRoot(element) {
    if (_supportsShadowDom()) {
        const rootNode = element.getRootNode ? element.getRootNode() : null;
        // Note that this should be caught by `_supportsShadowDom`, but some
        // teams have been able to hit this code path on unsupported browsers.
        if (typeof ShadowRoot !== 'undefined' && ShadowRoot && rootNode instanceof ShadowRoot) {
            return rootNode;
        }
    }
    return null;
}
/**
 * Gets the currently-focused element on the page while
 * also piercing through Shadow DOM boundaries.
 */
export function _getFocusedElementPierceShadowDom() {
    let activeElement = typeof document !== 'undefined' && document
        ? document.activeElement
        : null;
    while (activeElement && activeElement.shadowRoot) {
        const newActiveElement = activeElement.shadowRoot.activeElement;
        if (newActiveElement === activeElement) {
            break;
        }
        else {
            activeElement = newActiveElement;
        }
    }
    return activeElement;
}
/** Gets the target of an event while accounting for Shadow DOM. */
export function _getEventTarget(event) {
    // If an event is bound outside the Shadow DOM, the `event.target` will
    // point to the shadow root so we have to use `composedPath` instead.
    return (event.composedPath ? event.composedPath()[0] : event.target);
}
//# sourceMappingURL=data:application/json;base64,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