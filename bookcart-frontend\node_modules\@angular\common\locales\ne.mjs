/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["ne", [["पूर्वाह्न", "अपराह्न"], u, u], u, [["आ", "सो", "म", "बु", "बि", "शु", "श"], ["आइत", "सोम", "मङ्गल", "बुध", "बिहि", "शुक्र", "शनि"], ["आइतबार", "सोमबार", "मङ्गलबार", "बुधबार", "बिहिबार", "शुक्रबार", "शनिबार"], ["आइत", "सोम", "मङ्गल", "बुध", "बिहि", "शुक्र", "शनि"]], u, [["जन", "फेब", "मार्च", "अप्र", "मे", "जुन", "जुल", "अग", "सेप", "अक्टो", "नोभे", "डिसे"], ["जनवरी", "फेब्रुअरी", "मार्च", "अप्रिल", "मे", "जुन", "जुलाई", "अगस्ट", "सेप्टेम्बर", "अक्टोबर", "नोभेम्बर", "डिसेम्बर"], u], [["जन", "फेेब", "मार्च", "अप्र", "मे", "जुन", "जुल", "अग", "सेप", "अक्टो", "नोभे", "डिसे"], ["जनवरी", "फेब्रुअरी", "मार्च", "अप्रिल", "मे", "जुन", "जुलाई", "अगस्ट", "सेप्टेम्बर", "अक्टोबर", "नोभेम्बर", "डिसेम्बर"], u], [["ईसा पूर्व", "सन्"], u, u], 0, [6, 0], ["yy/M/d", "y MMM d", "y MMMM d", "y MMMM d, EEEE"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1}, {0}", u, "{1} {0}", u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##,##0.###", "#,##,##0%", "¤ #,##,##0.00", "#E0"], "NPR", "नेरू", "नेपाली रूपैयाँ", { "BYN": [u, "р."], "JPY": ["JP¥", "¥"], "NPR": ["नेरू", "रू"], "PHP": [u, "₱"], "THB": ["฿"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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