/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { InjectionToken } from '@angular/core';
export const MAT_DATE_FORMATS = new InjectionToken('mat-date-formats');
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZGF0ZS1mb3JtYXRzLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vLi4vLi4vc3JjL21hdGVyaWFsL2NvcmUvZGF0ZXRpbWUvZGF0ZS1mb3JtYXRzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7R0FNRztBQUVILE9BQU8sRUFBQyxjQUFjLEVBQUMsTUFBTSxlQUFlLENBQUM7QUFlN0MsTUFBTSxDQUFDLE1BQU0sZ0JBQWdCLEdBQUcsSUFBSSxjQUFjLENBQWlCLGtCQUFrQixDQUFDLENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgR29vZ2xlIExMQyBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICpcbiAqIFVzZSBvZiB0aGlzIHNvdXJjZSBjb2RlIGlzIGdvdmVybmVkIGJ5IGFuIE1JVC1zdHlsZSBsaWNlbnNlIHRoYXQgY2FuIGJlXG4gKiBmb3VuZCBpbiB0aGUgTElDRU5TRSBmaWxlIGF0IGh0dHBzOi8vYW5ndWxhci5pby9saWNlbnNlXG4gKi9cblxuaW1wb3J0IHtJbmplY3Rpb25Ub2tlbn0gZnJvbSAnQGFuZ3VsYXIvY29yZSc7XG5cbmV4cG9ydCB0eXBlIE1hdERhdGVGb3JtYXRzID0ge1xuICBwYXJzZToge1xuICAgIGRhdGVJbnB1dDogYW55O1xuICB9O1xuICBkaXNwbGF5OiB7XG4gICAgZGF0ZUlucHV0OiBhbnk7XG4gICAgbW9udGhMYWJlbD86IGFueTtcbiAgICBtb250aFllYXJMYWJlbDogYW55O1xuICAgIGRhdGVBMTF5TGFiZWw6IGFueTtcbiAgICBtb250aFllYXJBMTF5TGFiZWw6IGFueTtcbiAgfTtcbn07XG5cbmV4cG9ydCBjb25zdCBNQVRfREFURV9GT1JNQVRTID0gbmV3IEluamVjdGlvblRva2VuPE1hdERhdGVGb3JtYXRzPignbWF0LWRhdGUtZm9ybWF0cycpO1xuIl19