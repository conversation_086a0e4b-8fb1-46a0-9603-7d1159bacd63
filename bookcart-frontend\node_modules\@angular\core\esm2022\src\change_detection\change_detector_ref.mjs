/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { isComponentHost } from '../render3/interfaces/type_checks';
import { DECLARATION_COMPONENT_VIEW } from '../render3/interfaces/view';
import { getCurrentTNode, getLView } from '../render3/state';
import { getComponentLViewByIndex } from '../render3/util/view_utils';
import { ViewRef } from '../render3/view_ref';
/**
 * Base class that provides change detection functionality.
 * A change-detection tree collects all views that are to be checked for changes.
 * Use the methods to add and remove views from the tree, initiate change-detection,
 * and explicitly mark views as _dirty_, meaning that they have changed and need to be re-rendered.
 *
 * @see [Using change detection hooks](guide/lifecycle-hooks#using-change-detection-hooks)
 * @see [Defining custom change detection](guide/lifecycle-hooks#defining-custom-change-detection)
 *
 * @usageNotes
 *
 * The following examples demonstrate how to modify default change-detection behavior
 * to perform explicit detection when needed.
 *
 * ### Use `markForCheck()` with `CheckOnce` strategy
 *
 * The following example sets the `OnPush` change-detection strategy for a component
 * (`CheckOnce`, rather than the default `CheckAlways`), then forces a second check
 * after an interval.
 *
 * <code-example path="core/ts/change_detect/change-detection.ts"
 * region="mark-for-check"></code-example>
 *
 * ### Detach change detector to limit how often check occurs
 *
 * The following example defines a component with a large list of read-only data
 * that is expected to change constantly, many times per second.
 * To improve performance, we want to check and update the list
 * less often than the changes actually occur. To do that, we detach
 * the component's change detector and perform an explicit local check every five seconds.
 *
 * <code-example path="core/ts/change_detect/change-detection.ts" region="detach"></code-example>
 *
 *
 * ### Reattaching a detached component
 *
 * The following example creates a component displaying live data.
 * The component detaches its change detector from the main change detector tree
 * when the `live` property is set to false, and reattaches it when the property
 * becomes true.
 *
 * <code-example path="core/ts/change_detect/change-detection.ts" region="reattach"></code-example>
 *
 * @publicApi
 */
export class ChangeDetectorRef {
    /**
     * @internal
     * @nocollapse
     */
    static { this.__NG_ELEMENT_ID__ = injectChangeDetectorRef; }
}
/** Returns a ChangeDetectorRef (a.k.a. a ViewRef) */
export function injectChangeDetectorRef(flags) {
    return createViewRef(getCurrentTNode(), getLView(), (flags & 16 /* InternalInjectFlags.ForPipe */) === 16 /* InternalInjectFlags.ForPipe */);
}
/**
 * Creates a ViewRef and stores it on the injector as ChangeDetectorRef (public alias).
 *
 * @param tNode The node that is requesting a ChangeDetectorRef
 * @param lView The view to which the node belongs
 * @param isPipe Whether the view is being injected into a pipe.
 * @returns The ChangeDetectorRef to use
 */
function createViewRef(tNode, lView, isPipe) {
    if (isComponentHost(tNode) && !isPipe) {
        // The LView represents the location where the component is declared.
        // Instead we want the LView for the component View and so we need to look it up.
        const componentView = getComponentLViewByIndex(tNode.index, lView); // look down
        return new ViewRef(componentView, componentView);
    }
    else if (tNode.type & (3 /* TNodeType.AnyRNode */ | 12 /* TNodeType.AnyContainer */ | 32 /* TNodeType.Icu */)) {
        // The LView represents the location where the injection is requested from.
        // We need to locate the containing LView (in case where the `lView` is an embedded view)
        const hostComponentView = lView[DECLARATION_COMPONENT_VIEW]; // look up
        return new ViewRef(hostComponentView, lView);
    }
    return null;
}
//# sourceMappingURL=data:application/json;base64,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