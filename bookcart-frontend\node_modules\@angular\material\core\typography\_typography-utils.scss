@use 'sass:list';
@use 'sass:math';
@use './property-getters';

// Property getters live in their own file to avoid circular dependencies, but we re-export them
// here so that historical imports from this file continue to work without needing to be updated.
@forward './property-getters';

/// Outputs the shorthand `font` CSS property, based on a set of typography values. Falls back to
/// the individual properties if a value that isn't allowed in the shorthand is passed in.
/// @param {String} $font-size The font-size value.
/// @param {String | Number} $font-weight The font-weight value.
/// @param {String | Number} $line-height The line-height value.
/// @param {String} $font-family The font-family value.
/// @returns {String} The `font` shorthand value combining the given parts.
@mixin font-shorthand($font-size, $font-weight, $line-height, $font-family) {
  // If any of the values are set to `inherit`, we can't use the shorthand
  // so we fall back to passing in the individual properties.
  @if ($font-size == inherit or
       $font-weight == inherit or
       $line-height == inherit or
       $font-family == inherit or
       $font-size == null or
       $font-weight == null or
       $line-height == null or
       $font-family == null) {

    font-size: $font-size;
    font-weight: $font-weight;
    line-height: $line-height;
    font-family: $font-family;
  }
  @else {
    // Otherwise use the shorthand `font`, because it's the least amount of bytes.
    font: $font-weight list.slash($font-size, $line-height) $font-family;
  }
}

/// Emits CSS styles for the given typography level.
/// @param {Map} $config A typography config.
/// @param {Map} $level A typography level.
@mixin typography-level($config, $level) {
  // we deliberately do not use the font shorthand here because it overrides
  // certain font properties that can't be configured in the current typography
  // config, e.g. the font-variant-caps or font-feature-settings property
  font-size: property-getters.font-size($config, $level);
  font-weight: property-getters.font-weight($config, $level);
  line-height: property-getters.line-height($config, $level);
  font-family: property-getters.font-family($config, $level);
  letter-spacing: property-getters.letter-spacing($config, $level);
}

/// Coerce a value to `em` if it is a unitless number, otherwise returns
/// the value provided.
@function private-coerce-unitless-to-em($value) {
  @return if(math.is-unitless($value), 1em * $value, $value);
}
