{"version": 3, "file": "icon.mjs", "sources": ["../../../../../../src/material/icon/trusted-types.ts", "../../../../../../src/material/icon/icon-registry.ts", "../../../../../../src/material/icon/icon.ts", "../../../../../../src/material/icon/icon-module.ts", "../../../../../../src/material/icon/icon_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @fileoverview\n * A module to facilitate use of a Trusted Types policy internally within\n * Angular Material. It lazily constructs the Trusted Types policy, providing\n * helper utilities for promoting strings to Trusted Types. When Trusted Types\n * are not available, strings are used as a fallback.\n * @security All use of this module is security-sensitive and should go through\n * security review.\n */\n\nexport declare interface TrustedHTML {\n  __brand__: 'TrustedHTML';\n}\n\nexport declare interface TrustedTypePolicyFactory {\n  createPolicy(\n    policyName: string,\n    policyOptions: {\n      createHTML?: (input: string) => string;\n    },\n  ): TrustedTypePolicy;\n}\n\nexport declare interface TrustedTypePolicy {\n  createHTML(input: string): TrustedHTML;\n}\n\n/**\n * The Trusted Types policy, or null if Trusted Types are not\n * enabled/supported, or undefined if the policy has not been created yet.\n */\nlet policy: TrustedTypePolicy | null | undefined;\n\n/**\n * Returns the Trusted Types policy, or null if Trusted Types are not\n * enabled/supported. The first call to this function will create the policy.\n */\nfunction getPolicy(): TrustedTypePolicy | null {\n  if (policy === undefined) {\n    policy = null;\n    if (typeof window !== 'undefined') {\n      const ttWindow = window as unknown as {trustedTypes?: TrustedTypePolicyFactory};\n      if (ttWindow.trustedTypes !== undefined) {\n        policy = ttWindow.trustedTypes.createPolicy('angular#components', {\n          createHTML: (s: string) => s,\n        });\n      }\n    }\n  }\n  return policy;\n}\n\n/**\n * Unsafely promote a string to a TrustedHTML, falling back to strings when\n * Trusted Types are not available.\n * @security This is a security-sensitive function; any use of this function\n * must go through security review. In particular, it must be assured that the\n * provided string will never cause an XSS vulnerability if used in a context\n * that will be interpreted as HTML by a browser, e.g. when assigning to\n * element.innerHTML.\n */\nexport function trustedHTMLFromString(html: string): TrustedHTML {\n  return getPolicy()?.createHTML(html) || (html as unknown as TrustedHTML);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {DOCUMENT} from '@angular/common';\nimport {HttpClient, HttpErrorResponse} from '@angular/common/http';\nimport {\n  ErrorHandler,\n  Inject,\n  Injectable,\n  InjectionToken,\n  OnDestroy,\n  Optional,\n  SecurityContext,\n  SkipSelf,\n} from '@angular/core';\nimport {DomSanitizer, SafeHtml, SafeResourceUrl} from '@angular/platform-browser';\nimport {forkJoin, Observable, of as observableOf, throwError as observableThrow} from 'rxjs';\nimport {catchError, finalize, map, share, tap} from 'rxjs/operators';\nimport {TrustedHTML, trustedHTMLFromString} from './trusted-types';\n\n/**\n * Returns an exception to be thrown in the case when attempting to\n * load an icon with a name that cannot be found.\n * @docs-private\n */\nexport function getMatIconNameNotFoundError(iconName: string): Error {\n  return Error(`Unable to find icon with the name \"${iconName}\"`);\n}\n\n/**\n * Returns an exception to be thrown when the consumer attempts to use\n * `<mat-icon>` without including @angular/common/http.\n * @docs-private\n */\nexport function getMatIconNoHttpProviderError(): Error {\n  return Error(\n    'Could not find HttpClient provider for use with Angular Material icons. ' +\n      'Please include the HttpClientModule from @angular/common/http in your ' +\n      'app imports.',\n  );\n}\n\n/**\n * Returns an exception to be thrown when a URL couldn't be sanitized.\n * @param url URL that was attempted to be sanitized.\n * @docs-private\n */\nexport function getMatIconFailedToSanitizeUrlError(url: SafeResourceUrl): Error {\n  return Error(\n    `The URL provided to MatIconRegistry was not trusted as a resource URL ` +\n      `via Angular's DomSanitizer. Attempted URL was \"${url}\".`,\n  );\n}\n\n/**\n * Returns an exception to be thrown when a HTML string couldn't be sanitized.\n * @param literal HTML that was attempted to be sanitized.\n * @docs-private\n */\nexport function getMatIconFailedToSanitizeLiteralError(literal: SafeHtml): Error {\n  return Error(\n    `The literal provided to MatIconRegistry was not trusted as safe HTML by ` +\n      `Angular's DomSanitizer. Attempted literal was \"${literal}\".`,\n  );\n}\n\n/** Options that can be used to configure how an icon or the icons in an icon set are presented. */\nexport interface IconOptions {\n  /** View box to set on the icon. */\n  viewBox?: string;\n\n  /** Whether or not to fetch the icon or icon set using HTTP credentials. */\n  withCredentials?: boolean;\n}\n\n/**\n * Function that will be invoked by the icon registry when trying to resolve the\n * URL from which to fetch an icon. The returned URL will be used to make a request for the icon.\n */\nexport type IconResolver = (\n  name: string,\n  namespace: string,\n) => SafeResourceUrl | SafeResourceUrlWithIconOptions | null;\n\n/** Object that specifies a URL from which to fetch an icon and the options to use for it. */\nexport interface SafeResourceUrlWithIconOptions {\n  url: SafeResourceUrl;\n  options: IconOptions;\n}\n\n/**\n * Configuration for an icon, including the URL and possibly the cached SVG element.\n * @docs-private\n */\nclass SvgIconConfig {\n  svgElement: SVGElement | null;\n\n  constructor(\n    public url: SafeResourceUrl,\n    public svgText: TrustedHTML | null,\n    public options?: IconOptions,\n  ) {}\n}\n\n/** Icon configuration whose content has already been loaded. */\ntype LoadedSvgIconConfig = SvgIconConfig & {svgText: TrustedHTML};\n\n/**\n * Service to register and display icons used by the `<mat-icon>` component.\n * - Registers icon URLs by namespace and name.\n * - Registers icon set URLs by namespace.\n * - Registers aliases for CSS classes, for use with icon fonts.\n * - Loads icons from URLs and extracts individual icons from icon sets.\n */\n@Injectable({providedIn: 'root'})\nexport class MatIconRegistry implements OnDestroy {\n  private _document: Document;\n\n  /**\n   * URLs and cached SVG elements for individual icons. Keys are of the format \"[namespace]:[icon]\".\n   */\n  private _svgIconConfigs = new Map<string, SvgIconConfig>();\n\n  /**\n   * SvgIconConfig objects and cached SVG elements for icon sets, keyed by namespace.\n   * Multiple icon sets can be registered under the same namespace.\n   */\n  private _iconSetConfigs = new Map<string, SvgIconConfig[]>();\n\n  /** Cache for icons loaded by direct URLs. */\n  private _cachedIconsByUrl = new Map<string, SVGElement>();\n\n  /** In-progress icon fetches. Used to coalesce multiple requests to the same URL. */\n  private _inProgressUrlFetches = new Map<string, Observable<TrustedHTML>>();\n\n  /** Map from font identifiers to their CSS class names. Used for icon fonts. */\n  private _fontCssClassesByAlias = new Map<string, string>();\n\n  /** Registered icon resolver functions. */\n  private _resolvers: IconResolver[] = [];\n\n  /**\n   * The CSS classes to apply when an `<mat-icon>` component has no icon name, url, or font\n   * specified. The default 'material-icons' value assumes that the material icon font has been\n   * loaded as described at https://google.github.io/material-design-icons/#icon-font-for-the-web\n   */\n  private _defaultFontSetClass = ['material-icons', 'mat-ligature-font'];\n\n  constructor(\n    @Optional() private _httpClient: HttpClient,\n    private _sanitizer: DomSanitizer,\n    @Optional() @Inject(DOCUMENT) document: any,\n    private readonly _errorHandler: ErrorHandler,\n  ) {\n    this._document = document;\n  }\n\n  /**\n   * Registers an icon by URL in the default namespace.\n   * @param iconName Name under which the icon should be registered.\n   * @param url\n   */\n  addSvgIcon(iconName: string, url: SafeResourceUrl, options?: IconOptions): this {\n    return this.addSvgIconInNamespace('', iconName, url, options);\n  }\n\n  /**\n   * Registers an icon using an HTML string in the default namespace.\n   * @param iconName Name under which the icon should be registered.\n   * @param literal SVG source of the icon.\n   */\n  addSvgIconLiteral(iconName: string, literal: SafeHtml, options?: IconOptions): this {\n    return this.addSvgIconLiteralInNamespace('', iconName, literal, options);\n  }\n\n  /**\n   * Registers an icon by URL in the specified namespace.\n   * @param namespace Namespace in which the icon should be registered.\n   * @param iconName Name under which the icon should be registered.\n   * @param url\n   */\n  addSvgIconInNamespace(\n    namespace: string,\n    iconName: string,\n    url: SafeResourceUrl,\n    options?: IconOptions,\n  ): this {\n    return this._addSvgIconConfig(namespace, iconName, new SvgIconConfig(url, null, options));\n  }\n\n  /**\n   * Registers an icon resolver function with the registry. The function will be invoked with the\n   * name and namespace of an icon when the registry tries to resolve the URL from which to fetch\n   * the icon. The resolver is expected to return a `SafeResourceUrl` that points to the icon,\n   * an object with the icon URL and icon options, or `null` if the icon is not supported. Resolvers\n   * will be invoked in the order in which they have been registered.\n   * @param resolver Resolver function to be registered.\n   */\n  addSvgIconResolver(resolver: IconResolver): this {\n    this._resolvers.push(resolver);\n    return this;\n  }\n\n  /**\n   * Registers an icon using an HTML string in the specified namespace.\n   * @param namespace Namespace in which the icon should be registered.\n   * @param iconName Name under which the icon should be registered.\n   * @param literal SVG source of the icon.\n   */\n  addSvgIconLiteralInNamespace(\n    namespace: string,\n    iconName: string,\n    literal: SafeHtml,\n    options?: IconOptions,\n  ): this {\n    const cleanLiteral = this._sanitizer.sanitize(SecurityContext.HTML, literal);\n\n    // TODO: add an ngDevMode check\n    if (!cleanLiteral) {\n      throw getMatIconFailedToSanitizeLiteralError(literal);\n    }\n\n    // Security: The literal is passed in as SafeHtml, and is thus trusted.\n    const trustedLiteral = trustedHTMLFromString(cleanLiteral);\n    return this._addSvgIconConfig(\n      namespace,\n      iconName,\n      new SvgIconConfig('', trustedLiteral, options),\n    );\n  }\n\n  /**\n   * Registers an icon set by URL in the default namespace.\n   * @param url\n   */\n  addSvgIconSet(url: SafeResourceUrl, options?: IconOptions): this {\n    return this.addSvgIconSetInNamespace('', url, options);\n  }\n\n  /**\n   * Registers an icon set using an HTML string in the default namespace.\n   * @param literal SVG source of the icon set.\n   */\n  addSvgIconSetLiteral(literal: SafeHtml, options?: IconOptions): this {\n    return this.addSvgIconSetLiteralInNamespace('', literal, options);\n  }\n\n  /**\n   * Registers an icon set by URL in the specified namespace.\n   * @param namespace Namespace in which to register the icon set.\n   * @param url\n   */\n  addSvgIconSetInNamespace(namespace: string, url: SafeResourceUrl, options?: IconOptions): this {\n    return this._addSvgIconSetConfig(namespace, new SvgIconConfig(url, null, options));\n  }\n\n  /**\n   * Registers an icon set using an HTML string in the specified namespace.\n   * @param namespace Namespace in which to register the icon set.\n   * @param literal SVG source of the icon set.\n   */\n  addSvgIconSetLiteralInNamespace(\n    namespace: string,\n    literal: SafeHtml,\n    options?: IconOptions,\n  ): this {\n    const cleanLiteral = this._sanitizer.sanitize(SecurityContext.HTML, literal);\n\n    if (!cleanLiteral) {\n      throw getMatIconFailedToSanitizeLiteralError(literal);\n    }\n\n    // Security: The literal is passed in as SafeHtml, and is thus trusted.\n    const trustedLiteral = trustedHTMLFromString(cleanLiteral);\n    return this._addSvgIconSetConfig(namespace, new SvgIconConfig('', trustedLiteral, options));\n  }\n\n  /**\n   * Defines an alias for CSS class names to be used for icon fonts. Creating an matIcon\n   * component with the alias as the fontSet input will cause the class name to be applied\n   * to the `<mat-icon>` element.\n   *\n   * If the registered font is a ligature font, then don't forget to also include the special\n   * class `mat-ligature-font` to allow the usage via attribute. So register like this:\n   *\n   * ```ts\n   * iconRegistry.registerFontClassAlias('f1', 'font1 mat-ligature-font');\n   * ```\n   *\n   * And use like this:\n   *\n   * ```html\n   * <mat-icon fontSet=\"f1\" fontIcon=\"home\"></mat-icon>\n   * ```\n   *\n   * @param alias Alias for the font.\n   * @param classNames Class names override to be used instead of the alias.\n   */\n  registerFontClassAlias(alias: string, classNames: string = alias): this {\n    this._fontCssClassesByAlias.set(alias, classNames);\n    return this;\n  }\n\n  /**\n   * Returns the CSS class name associated with the alias by a previous call to\n   * registerFontClassAlias. If no CSS class has been associated, returns the alias unmodified.\n   */\n  classNameForFontAlias(alias: string): string {\n    return this._fontCssClassesByAlias.get(alias) || alias;\n  }\n\n  /**\n   * Sets the CSS classes to be used for icon fonts when an `<mat-icon>` component does not\n   * have a fontSet input value, and is not loading an icon by name or URL.\n   */\n  setDefaultFontSetClass(...classNames: string[]): this {\n    this._defaultFontSetClass = classNames;\n    return this;\n  }\n\n  /**\n   * Returns the CSS classes to be used for icon fonts when an `<mat-icon>` component does not\n   * have a fontSet input value, and is not loading an icon by name or URL.\n   */\n  getDefaultFontSetClass(): string[] {\n    return this._defaultFontSetClass;\n  }\n\n  /**\n   * Returns an Observable that produces the icon (as an `<svg>` DOM element) from the given URL.\n   * The response from the URL may be cached so this will not always cause an HTTP request, but\n   * the produced element will always be a new copy of the originally fetched icon. (That is,\n   * it will not contain any modifications made to elements previously returned).\n   *\n   * @param safeUrl URL from which to fetch the SVG icon.\n   */\n  getSvgIconFromUrl(safeUrl: SafeResourceUrl): Observable<SVGElement> {\n    const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, safeUrl);\n\n    if (!url) {\n      throw getMatIconFailedToSanitizeUrlError(safeUrl);\n    }\n\n    const cachedIcon = this._cachedIconsByUrl.get(url);\n\n    if (cachedIcon) {\n      return observableOf(cloneSvg(cachedIcon));\n    }\n\n    return this._loadSvgIconFromConfig(new SvgIconConfig(safeUrl, null)).pipe(\n      tap(svg => this._cachedIconsByUrl.set(url!, svg)),\n      map(svg => cloneSvg(svg)),\n    );\n  }\n\n  /**\n   * Returns an Observable that produces the icon (as an `<svg>` DOM element) with the given name\n   * and namespace. The icon must have been previously registered with addIcon or addIconSet;\n   * if not, the Observable will throw an error.\n   *\n   * @param name Name of the icon to be retrieved.\n   * @param namespace Namespace in which to look for the icon.\n   */\n  getNamedSvgIcon(name: string, namespace: string = ''): Observable<SVGElement> {\n    const key = iconKey(namespace, name);\n    let config = this._svgIconConfigs.get(key);\n\n    // Return (copy of) cached icon if possible.\n    if (config) {\n      return this._getSvgFromConfig(config);\n    }\n\n    // Otherwise try to resolve the config from one of the resolver functions.\n    config = this._getIconConfigFromResolvers(namespace, name);\n\n    if (config) {\n      this._svgIconConfigs.set(key, config);\n      return this._getSvgFromConfig(config);\n    }\n\n    // See if we have any icon sets registered for the namespace.\n    const iconSetConfigs = this._iconSetConfigs.get(namespace);\n\n    if (iconSetConfigs) {\n      return this._getSvgFromIconSetConfigs(name, iconSetConfigs);\n    }\n\n    return observableThrow(getMatIconNameNotFoundError(key));\n  }\n\n  ngOnDestroy() {\n    this._resolvers = [];\n    this._svgIconConfigs.clear();\n    this._iconSetConfigs.clear();\n    this._cachedIconsByUrl.clear();\n  }\n\n  /**\n   * Returns the cached icon for a SvgIconConfig if available, or fetches it from its URL if not.\n   */\n  private _getSvgFromConfig(config: SvgIconConfig): Observable<SVGElement> {\n    if (config.svgText) {\n      // We already have the SVG element for this icon, return a copy.\n      return observableOf(cloneSvg(this._svgElementFromConfig(config as LoadedSvgIconConfig)));\n    } else {\n      // Fetch the icon from the config's URL, cache it, and return a copy.\n      return this._loadSvgIconFromConfig(config).pipe(map(svg => cloneSvg(svg)));\n    }\n  }\n\n  /**\n   * Attempts to find an icon with the specified name in any of the SVG icon sets.\n   * First searches the available cached icons for a nested element with a matching name, and\n   * if found copies the element to a new `<svg>` element. If not found, fetches all icon sets\n   * that have not been cached, and searches again after all fetches are completed.\n   * The returned Observable produces the SVG element if possible, and throws\n   * an error if no icon with the specified name can be found.\n   */\n  private _getSvgFromIconSetConfigs(\n    name: string,\n    iconSetConfigs: SvgIconConfig[],\n  ): Observable<SVGElement> {\n    // For all the icon set SVG elements we've fetched, see if any contain an icon with the\n    // requested name.\n    const namedIcon = this._extractIconWithNameFromAnySet(name, iconSetConfigs);\n\n    if (namedIcon) {\n      // We could cache namedIcon in _svgIconConfigs, but since we have to make a copy every\n      // time anyway, there's probably not much advantage compared to just always extracting\n      // it from the icon set.\n      return observableOf(namedIcon);\n    }\n\n    // Not found in any cached icon sets. If there are icon sets with URLs that we haven't\n    // fetched, fetch them now and look for iconName in the results.\n    const iconSetFetchRequests: Observable<TrustedHTML | null>[] = iconSetConfigs\n      .filter(iconSetConfig => !iconSetConfig.svgText)\n      .map(iconSetConfig => {\n        return this._loadSvgIconSetFromConfig(iconSetConfig).pipe(\n          catchError((err: HttpErrorResponse) => {\n            const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, iconSetConfig.url);\n\n            // Swallow errors fetching individual URLs so the\n            // combined Observable won't necessarily fail.\n            const errorMessage = `Loading icon set URL: ${url} failed: ${err.message}`;\n            this._errorHandler.handleError(new Error(errorMessage));\n            return observableOf(null);\n          }),\n        );\n      });\n\n    // Fetch all the icon set URLs. When the requests complete, every IconSet should have a\n    // cached SVG element (unless the request failed), and we can check again for the icon.\n    return forkJoin(iconSetFetchRequests).pipe(\n      map(() => {\n        const foundIcon = this._extractIconWithNameFromAnySet(name, iconSetConfigs);\n\n        // TODO: add an ngDevMode check\n        if (!foundIcon) {\n          throw getMatIconNameNotFoundError(name);\n        }\n\n        return foundIcon;\n      }),\n    );\n  }\n\n  /**\n   * Searches the cached SVG elements for the given icon sets for a nested icon element whose \"id\"\n   * tag matches the specified name. If found, copies the nested element to a new SVG element and\n   * returns it. Returns null if no matching element is found.\n   */\n  private _extractIconWithNameFromAnySet(\n    iconName: string,\n    iconSetConfigs: SvgIconConfig[],\n  ): SVGElement | null {\n    // Iterate backwards, so icon sets added later have precedence.\n    for (let i = iconSetConfigs.length - 1; i >= 0; i--) {\n      const config = iconSetConfigs[i];\n\n      // Parsing the icon set's text into an SVG element can be expensive. We can avoid some of\n      // the parsing by doing a quick check using `indexOf` to see if there's any chance for the\n      // icon to be in the set. This won't be 100% accurate, but it should help us avoid at least\n      // some of the parsing.\n      if (config.svgText && config.svgText.toString().indexOf(iconName) > -1) {\n        const svg = this._svgElementFromConfig(config as LoadedSvgIconConfig);\n        const foundIcon = this._extractSvgIconFromSet(svg, iconName, config.options);\n        if (foundIcon) {\n          return foundIcon;\n        }\n      }\n    }\n    return null;\n  }\n\n  /**\n   * Loads the content of the icon URL specified in the SvgIconConfig and creates an SVG element\n   * from it.\n   */\n  private _loadSvgIconFromConfig(config: SvgIconConfig): Observable<SVGElement> {\n    return this._fetchIcon(config).pipe(\n      tap(svgText => (config.svgText = svgText)),\n      map(() => this._svgElementFromConfig(config as LoadedSvgIconConfig)),\n    );\n  }\n\n  /**\n   * Loads the content of the icon set URL specified in the\n   * SvgIconConfig and attaches it to the config.\n   */\n  private _loadSvgIconSetFromConfig(config: SvgIconConfig): Observable<TrustedHTML | null> {\n    if (config.svgText) {\n      return observableOf(null);\n    }\n\n    return this._fetchIcon(config).pipe(tap(svgText => (config.svgText = svgText)));\n  }\n\n  /**\n   * Searches the cached element of the given SvgIconConfig for a nested icon element whose \"id\"\n   * tag matches the specified name. If found, copies the nested element to a new SVG element and\n   * returns it. Returns null if no matching element is found.\n   */\n  private _extractSvgIconFromSet(\n    iconSet: SVGElement,\n    iconName: string,\n    options?: IconOptions,\n  ): SVGElement | null {\n    // Use the `id=\"iconName\"` syntax in order to escape special\n    // characters in the ID (versus using the #iconName syntax).\n    const iconSource = iconSet.querySelector(`[id=\"${iconName}\"]`);\n\n    if (!iconSource) {\n      return null;\n    }\n\n    // Clone the element and remove the ID to prevent multiple elements from being added\n    // to the page with the same ID.\n    const iconElement = iconSource.cloneNode(true) as Element;\n    iconElement.removeAttribute('id');\n\n    // If the icon node is itself an <svg> node, clone and return it directly. If not, set it as\n    // the content of a new <svg> node.\n    if (iconElement.nodeName.toLowerCase() === 'svg') {\n      return this._setSvgAttributes(iconElement as SVGElement, options);\n    }\n\n    // If the node is a <symbol>, it won't be rendered so we have to convert it into <svg>. Note\n    // that the same could be achieved by referring to it via <use href=\"#id\">, however the <use>\n    // tag is problematic on Firefox, because it needs to include the current page path.\n    if (iconElement.nodeName.toLowerCase() === 'symbol') {\n      return this._setSvgAttributes(this._toSvgElement(iconElement), options);\n    }\n\n    // createElement('SVG') doesn't work as expected; the DOM ends up with\n    // the correct nodes, but the SVG content doesn't render. Instead we\n    // have to create an empty SVG node using innerHTML and append its content.\n    // Elements created using DOMParser.parseFromString have the same problem.\n    // http://stackoverflow.com/questions/23003278/svg-innerhtml-in-firefox-can-not-display\n    const svg = this._svgElementFromString(trustedHTMLFromString('<svg></svg>'));\n    // Clone the node so we don't remove it from the parent icon set element.\n    svg.appendChild(iconElement);\n\n    return this._setSvgAttributes(svg, options);\n  }\n\n  /**\n   * Creates a DOM element from the given SVG string.\n   */\n  private _svgElementFromString(str: TrustedHTML): SVGElement {\n    const div = this._document.createElement('DIV');\n    div.innerHTML = str as unknown as string;\n    const svg = div.querySelector('svg') as SVGElement;\n\n    // TODO: add an ngDevMode check\n    if (!svg) {\n      throw Error('<svg> tag not found');\n    }\n\n    return svg;\n  }\n\n  /**\n   * Converts an element into an SVG node by cloning all of its children.\n   */\n  private _toSvgElement(element: Element): SVGElement {\n    const svg = this._svgElementFromString(trustedHTMLFromString('<svg></svg>'));\n    const attributes = element.attributes;\n\n    // Copy over all the attributes from the `symbol` to the new SVG, except the id.\n    for (let i = 0; i < attributes.length; i++) {\n      const {name, value} = attributes[i];\n\n      if (name !== 'id') {\n        svg.setAttribute(name, value);\n      }\n    }\n\n    for (let i = 0; i < element.childNodes.length; i++) {\n      if (element.childNodes[i].nodeType === this._document.ELEMENT_NODE) {\n        svg.appendChild(element.childNodes[i].cloneNode(true));\n      }\n    }\n\n    return svg;\n  }\n\n  /**\n   * Sets the default attributes for an SVG element to be used as an icon.\n   */\n  private _setSvgAttributes(svg: SVGElement, options?: IconOptions): SVGElement {\n    svg.setAttribute('fit', '');\n    svg.setAttribute('height', '100%');\n    svg.setAttribute('width', '100%');\n    svg.setAttribute('preserveAspectRatio', 'xMidYMid meet');\n    svg.setAttribute('focusable', 'false'); // Disable IE11 default behavior to make SVGs focusable.\n\n    if (options && options.viewBox) {\n      svg.setAttribute('viewBox', options.viewBox);\n    }\n\n    return svg;\n  }\n\n  /**\n   * Returns an Observable which produces the string contents of the given icon. Results may be\n   * cached, so future calls with the same URL may not cause another HTTP request.\n   */\n  private _fetchIcon(iconConfig: SvgIconConfig): Observable<TrustedHTML> {\n    const {url: safeUrl, options} = iconConfig;\n    const withCredentials = options?.withCredentials ?? false;\n\n    if (!this._httpClient) {\n      throw getMatIconNoHttpProviderError();\n    }\n\n    // TODO: add an ngDevMode check\n    if (safeUrl == null) {\n      throw Error(`Cannot fetch icon from URL \"${safeUrl}\".`);\n    }\n\n    const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, safeUrl);\n\n    // TODO: add an ngDevMode check\n    if (!url) {\n      throw getMatIconFailedToSanitizeUrlError(safeUrl);\n    }\n\n    // Store in-progress fetches to avoid sending a duplicate request for a URL when there is\n    // already a request in progress for that URL. It's necessary to call share() on the\n    // Observable returned by http.get() so that multiple subscribers don't cause multiple XHRs.\n    const inProgressFetch = this._inProgressUrlFetches.get(url);\n\n    if (inProgressFetch) {\n      return inProgressFetch;\n    }\n\n    const req = this._httpClient.get(url, {responseType: 'text', withCredentials}).pipe(\n      map(svg => {\n        // Security: This SVG is fetched from a SafeResourceUrl, and is thus\n        // trusted HTML.\n        return trustedHTMLFromString(svg);\n      }),\n      finalize(() => this._inProgressUrlFetches.delete(url)),\n      share(),\n    );\n\n    this._inProgressUrlFetches.set(url, req);\n    return req;\n  }\n\n  /**\n   * Registers an icon config by name in the specified namespace.\n   * @param namespace Namespace in which to register the icon config.\n   * @param iconName Name under which to register the config.\n   * @param config Config to be registered.\n   */\n  private _addSvgIconConfig(namespace: string, iconName: string, config: SvgIconConfig): this {\n    this._svgIconConfigs.set(iconKey(namespace, iconName), config);\n    return this;\n  }\n\n  /**\n   * Registers an icon set config in the specified namespace.\n   * @param namespace Namespace in which to register the icon config.\n   * @param config Config to be registered.\n   */\n  private _addSvgIconSetConfig(namespace: string, config: SvgIconConfig): this {\n    const configNamespace = this._iconSetConfigs.get(namespace);\n\n    if (configNamespace) {\n      configNamespace.push(config);\n    } else {\n      this._iconSetConfigs.set(namespace, [config]);\n    }\n\n    return this;\n  }\n\n  /** Parses a config's text into an SVG element. */\n  private _svgElementFromConfig(config: LoadedSvgIconConfig): SVGElement {\n    if (!config.svgElement) {\n      const svg = this._svgElementFromString(config.svgText);\n      this._setSvgAttributes(svg, config.options);\n      config.svgElement = svg;\n    }\n\n    return config.svgElement;\n  }\n\n  /** Tries to create an icon config through the registered resolver functions. */\n  private _getIconConfigFromResolvers(namespace: string, name: string): SvgIconConfig | undefined {\n    for (let i = 0; i < this._resolvers.length; i++) {\n      const result = this._resolvers[i](name, namespace);\n\n      if (result) {\n        return isSafeUrlWithOptions(result)\n          ? new SvgIconConfig(result.url, null, result.options)\n          : new SvgIconConfig(result, null);\n      }\n    }\n\n    return undefined;\n  }\n}\n\n/** @docs-private */\nexport function ICON_REGISTRY_PROVIDER_FACTORY(\n  parentRegistry: MatIconRegistry,\n  httpClient: HttpClient,\n  sanitizer: DomSanitizer,\n  errorHandler: ErrorHandler,\n  document?: any,\n) {\n  return parentRegistry || new MatIconRegistry(httpClient, sanitizer, document, errorHandler);\n}\n\n/** @docs-private */\nexport const ICON_REGISTRY_PROVIDER = {\n  // If there is already an MatIconRegistry available, use that. Otherwise, provide a new one.\n  provide: MatIconRegistry,\n  deps: [\n    [new Optional(), new SkipSelf(), MatIconRegistry],\n    [new Optional(), HttpClient],\n    DomSanitizer,\n    ErrorHandler,\n    [new Optional(), DOCUMENT as InjectionToken<any>],\n  ],\n  useFactory: ICON_REGISTRY_PROVIDER_FACTORY,\n};\n\n/** Clones an SVGElement while preserving type information. */\nfunction cloneSvg(svg: SVGElement): SVGElement {\n  return svg.cloneNode(true) as SVGElement;\n}\n\n/** Returns the cache key to use for an icon namespace and name. */\nfunction iconKey(namespace: string, name: string) {\n  return namespace + ':' + name;\n}\n\nfunction isSafeUrlWithOptions(value: any): value is SafeResourceUrlWithIconOptions {\n  return !!(value.url && value.options);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {DOCUMENT} from '@angular/common';\nimport {\n  AfterViewChecked,\n  Attribute,\n  booleanAttribute,\n  ChangeDetectionStrategy,\n  Component,\n  ElementRef,\n  ErrorHandler,\n  inject,\n  Inject,\n  InjectionToken,\n  Input,\n  OnDestroy,\n  OnInit,\n  Optional,\n  ViewEncapsulation,\n} from '@angular/core';\nimport {ThemePalette} from '@angular/material/core';\nimport {Subscription} from 'rxjs';\nimport {take} from 'rxjs/operators';\n\nimport {MatIconRegistry} from './icon-registry';\n\n/** Default options for `mat-icon`.  */\nexport interface MatIconDefaultOptions {\n  /** Default color of the icon. */\n  color?: ThemePalette;\n  /** Font set that the icon is a part of. */\n  fontSet?: string;\n}\n\n/** Injection token to be used to override the default options for `mat-icon`. */\nexport const MAT_ICON_DEFAULT_OPTIONS = new InjectionToken<MatIconDefaultOptions>(\n  'MAT_ICON_DEFAULT_OPTIONS',\n);\n\n/**\n * Injection token used to provide the current location to `MatIcon`.\n * Used to handle server-side rendering and to stub out during unit tests.\n * @docs-private\n */\nexport const MAT_ICON_LOCATION = new InjectionToken<MatIconLocation>('mat-icon-location', {\n  providedIn: 'root',\n  factory: MAT_ICON_LOCATION_FACTORY,\n});\n\n/**\n * Stubbed out location for `MatIcon`.\n * @docs-private\n */\nexport interface MatIconLocation {\n  getPathname: () => string;\n}\n\n/** @docs-private */\nexport function MAT_ICON_LOCATION_FACTORY(): MatIconLocation {\n  const _document = inject(DOCUMENT);\n  const _location = _document ? _document.location : null;\n\n  return {\n    // Note that this needs to be a function, rather than a property, because Angular\n    // will only resolve it once, but we want the current path on each call.\n    getPathname: () => (_location ? _location.pathname + _location.search : ''),\n  };\n}\n\n/** SVG attributes that accept a FuncIRI (e.g. `url(<something>)`). */\nconst funcIriAttributes = [\n  'clip-path',\n  'color-profile',\n  'src',\n  'cursor',\n  'fill',\n  'filter',\n  'marker',\n  'marker-start',\n  'marker-mid',\n  'marker-end',\n  'mask',\n  'stroke',\n];\n\n/** Selector that can be used to find all elements that are using a `FuncIRI`. */\nconst funcIriAttributeSelector = funcIriAttributes.map(attr => `[${attr}]`).join(', ');\n\n/** Regex that can be used to extract the id out of a FuncIRI. */\nconst funcIriPattern = /^url\\(['\"]?#(.*?)['\"]?\\)$/;\n\n/**\n * Component to display an icon. It can be used in the following ways:\n *\n * - Specify the svgIcon input to load an SVG icon from a URL previously registered with the\n *   addSvgIcon, addSvgIconInNamespace, addSvgIconSet, or addSvgIconSetInNamespace methods of\n *   MatIconRegistry. If the svgIcon value contains a colon it is assumed to be in the format\n *   \"[namespace]:[name]\", if not the value will be the name of an icon in the default namespace.\n *   Examples:\n *     `<mat-icon svgIcon=\"left-arrow\"></mat-icon>\n *     <mat-icon svgIcon=\"animals:cat\"></mat-icon>`\n *\n * - Use a font ligature as an icon by putting the ligature text in the `fontIcon` attribute or the\n *   content of the `<mat-icon>` component. If you register a custom font class, don't forget to also\n *   include the special class `mat-ligature-font`. It is recommended to use the attribute alternative\n *   to prevent the ligature text to be selectable and to appear in search engine results.\n *   By default, the Material icons font is used as described at\n *   http://google.github.io/material-design-icons/#icon-font-for-the-web. You can specify an\n *   alternate font by setting the fontSet input to either the CSS class to apply to use the\n *   desired font, or to an alias previously registered with MatIconRegistry.registerFontClassAlias.\n *   Examples:\n *     `<mat-icon fontIcon=\"home\"></mat-icon>\n *     <mat-icon>home</mat-icon>\n *     <mat-icon fontSet=\"myfont\" fontIcon=\"sun\"></mat-icon>\n *     <mat-icon fontSet=\"myfont\">sun</mat-icon>`\n *\n * - Specify a font glyph to be included via CSS rules by setting the fontSet input to specify the\n *   font, and the fontIcon input to specify the icon. Typically the fontIcon will specify a\n *   CSS class which causes the glyph to be displayed via a :before selector, as in\n *   https://fortawesome.github.io/Font-Awesome/examples/\n *   Example:\n *     `<mat-icon fontSet=\"fa\" fontIcon=\"alarm\"></mat-icon>`\n */\n@Component({\n  template: '<ng-content></ng-content>',\n  selector: 'mat-icon',\n  exportAs: 'matIcon',\n  styleUrl: 'icon.css',\n  host: {\n    'role': 'img',\n    'class': 'mat-icon notranslate',\n    '[class]': 'color ? \"mat-\" + color : \"\"',\n    '[attr.data-mat-icon-type]': '_usingFontIcon() ? \"font\" : \"svg\"',\n    '[attr.data-mat-icon-name]': '_svgName || fontIcon',\n    '[attr.data-mat-icon-namespace]': '_svgNamespace || fontSet',\n    '[attr.fontIcon]': '_usingFontIcon() ? fontIcon : null',\n    '[class.mat-icon-inline]': 'inline',\n    '[class.mat-icon-no-color]': 'color !== \"primary\" && color !== \"accent\" && color !== \"warn\"',\n  },\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: true,\n})\nexport class MatIcon implements OnInit, AfterViewChecked, OnDestroy {\n  private _defaultColor: ThemePalette;\n\n  /** Theme palette color of the icon. */\n  @Input()\n  get color() {\n    return this._color || this._defaultColor;\n  }\n  set color(value: string | null | undefined) {\n    this._color = value;\n  }\n  private _color: string | null | undefined;\n\n  /**\n   * Whether the icon should be inlined, automatically sizing the icon to match the font size of\n   * the element the icon is contained in.\n   */\n  @Input({transform: booleanAttribute})\n  inline: boolean = false;\n\n  /** Name of the icon in the SVG icon set. */\n  @Input()\n  get svgIcon(): string {\n    return this._svgIcon;\n  }\n  set svgIcon(value: string) {\n    if (value !== this._svgIcon) {\n      if (value) {\n        this._updateSvgIcon(value);\n      } else if (this._svgIcon) {\n        this._clearSvgElement();\n      }\n      this._svgIcon = value;\n    }\n  }\n  private _svgIcon: string;\n\n  /** Font set that the icon is a part of. */\n  @Input()\n  get fontSet(): string {\n    return this._fontSet;\n  }\n  set fontSet(value: string) {\n    const newValue = this._cleanupFontValue(value);\n\n    if (newValue !== this._fontSet) {\n      this._fontSet = newValue;\n      this._updateFontIconClasses();\n    }\n  }\n  private _fontSet: string;\n\n  /** Name of an icon within a font set. */\n  @Input()\n  get fontIcon(): string {\n    return this._fontIcon;\n  }\n  set fontIcon(value: string) {\n    const newValue = this._cleanupFontValue(value);\n\n    if (newValue !== this._fontIcon) {\n      this._fontIcon = newValue;\n      this._updateFontIconClasses();\n    }\n  }\n  private _fontIcon: string;\n\n  private _previousFontSetClass: string[] = [];\n  private _previousFontIconClass: string;\n\n  _svgName: string | null;\n  _svgNamespace: string | null;\n\n  /** Keeps track of the current page path. */\n  private _previousPath?: string;\n\n  /** Keeps track of the elements and attributes that we've prefixed with the current path. */\n  private _elementsWithExternalReferences?: Map<Element, {name: string; value: string}[]>;\n\n  /** Subscription to the current in-progress SVG icon request. */\n  private _currentIconFetch = Subscription.EMPTY;\n\n  constructor(\n    readonly _elementRef: ElementRef<HTMLElement>,\n    private _iconRegistry: MatIconRegistry,\n    @Attribute('aria-hidden') ariaHidden: string,\n    @Inject(MAT_ICON_LOCATION) private _location: MatIconLocation,\n    private readonly _errorHandler: ErrorHandler,\n    @Optional()\n    @Inject(MAT_ICON_DEFAULT_OPTIONS)\n    defaults?: MatIconDefaultOptions,\n  ) {\n    if (defaults) {\n      if (defaults.color) {\n        this.color = this._defaultColor = defaults.color;\n      }\n\n      if (defaults.fontSet) {\n        this.fontSet = defaults.fontSet;\n      }\n    }\n\n    // If the user has not explicitly set aria-hidden, mark the icon as hidden, as this is\n    // the right thing to do for the majority of icon use-cases.\n    if (!ariaHidden) {\n      _elementRef.nativeElement.setAttribute('aria-hidden', 'true');\n    }\n  }\n\n  /**\n   * Splits an svgIcon binding value into its icon set and icon name components.\n   * Returns a 2-element array of [(icon set), (icon name)].\n   * The separator for the two fields is ':'. If there is no separator, an empty\n   * string is returned for the icon set and the entire value is returned for\n   * the icon name. If the argument is falsy, returns an array of two empty strings.\n   * Throws an error if the name contains two or more ':' separators.\n   * Examples:\n   *   `'social:cake' -> ['social', 'cake']\n   *   'penguin' -> ['', 'penguin']\n   *   null -> ['', '']\n   *   'a:b:c' -> (throws Error)`\n   */\n  private _splitIconName(iconName: string): [string, string] {\n    if (!iconName) {\n      return ['', ''];\n    }\n    const parts = iconName.split(':');\n    switch (parts.length) {\n      case 1:\n        return ['', parts[0]]; // Use default namespace.\n      case 2:\n        return <[string, string]>parts;\n      default:\n        throw Error(`Invalid icon name: \"${iconName}\"`); // TODO: add an ngDevMode check\n    }\n  }\n\n  ngOnInit() {\n    // Update font classes because ngOnChanges won't be called if none of the inputs are present,\n    // e.g. <mat-icon>arrow</mat-icon> In this case we need to add a CSS class for the default font.\n    this._updateFontIconClasses();\n  }\n\n  ngAfterViewChecked() {\n    const cachedElements = this._elementsWithExternalReferences;\n\n    if (cachedElements && cachedElements.size) {\n      const newPath = this._location.getPathname();\n\n      // We need to check whether the URL has changed on each change detection since\n      // the browser doesn't have an API that will let us react on link clicks and\n      // we can't depend on the Angular router. The references need to be updated,\n      // because while most browsers don't care whether the URL is correct after\n      // the first render, Safari will break if the user navigates to a different\n      // page and the SVG isn't re-rendered.\n      if (newPath !== this._previousPath) {\n        this._previousPath = newPath;\n        this._prependPathToReferences(newPath);\n      }\n    }\n  }\n\n  ngOnDestroy() {\n    this._currentIconFetch.unsubscribe();\n\n    if (this._elementsWithExternalReferences) {\n      this._elementsWithExternalReferences.clear();\n    }\n  }\n\n  _usingFontIcon(): boolean {\n    return !this.svgIcon;\n  }\n\n  private _setSvgElement(svg: SVGElement) {\n    this._clearSvgElement();\n\n    // Note: we do this fix here, rather than the icon registry, because the\n    // references have to point to the URL at the time that the icon was created.\n    const path = this._location.getPathname();\n    this._previousPath = path;\n    this._cacheChildrenWithExternalReferences(svg);\n    this._prependPathToReferences(path);\n    this._elementRef.nativeElement.appendChild(svg);\n  }\n\n  private _clearSvgElement() {\n    const layoutElement: HTMLElement = this._elementRef.nativeElement;\n    let childCount = layoutElement.childNodes.length;\n\n    if (this._elementsWithExternalReferences) {\n      this._elementsWithExternalReferences.clear();\n    }\n\n    // Remove existing non-element child nodes and SVGs, and add the new SVG element. Note that\n    // we can't use innerHTML, because IE will throw if the element has a data binding.\n    while (childCount--) {\n      const child = layoutElement.childNodes[childCount];\n\n      // 1 corresponds to Node.ELEMENT_NODE. We remove all non-element nodes in order to get rid\n      // of any loose text nodes, as well as any SVG elements in order to remove any old icons.\n      if (child.nodeType !== 1 || child.nodeName.toLowerCase() === 'svg') {\n        child.remove();\n      }\n    }\n  }\n\n  private _updateFontIconClasses() {\n    if (!this._usingFontIcon()) {\n      return;\n    }\n\n    const elem: HTMLElement = this._elementRef.nativeElement;\n    const fontSetClasses = (\n      this.fontSet\n        ? this._iconRegistry.classNameForFontAlias(this.fontSet).split(/ +/)\n        : this._iconRegistry.getDefaultFontSetClass()\n    ).filter(className => className.length > 0);\n\n    this._previousFontSetClass.forEach(className => elem.classList.remove(className));\n    fontSetClasses.forEach(className => elem.classList.add(className));\n    this._previousFontSetClass = fontSetClasses;\n\n    if (\n      this.fontIcon !== this._previousFontIconClass &&\n      !fontSetClasses.includes('mat-ligature-font')\n    ) {\n      if (this._previousFontIconClass) {\n        elem.classList.remove(this._previousFontIconClass);\n      }\n      if (this.fontIcon) {\n        elem.classList.add(this.fontIcon);\n      }\n      this._previousFontIconClass = this.fontIcon;\n    }\n  }\n\n  /**\n   * Cleans up a value to be used as a fontIcon or fontSet.\n   * Since the value ends up being assigned as a CSS class, we\n   * have to trim the value and omit space-separated values.\n   */\n  private _cleanupFontValue(value: string) {\n    return typeof value === 'string' ? value.trim().split(' ')[0] : value;\n  }\n\n  /**\n   * Prepends the current path to all elements that have an attribute pointing to a `FuncIRI`\n   * reference. This is required because WebKit browsers require references to be prefixed with\n   * the current path, if the page has a `base` tag.\n   */\n  private _prependPathToReferences(path: string) {\n    const elements = this._elementsWithExternalReferences;\n\n    if (elements) {\n      elements.forEach((attrs, element) => {\n        attrs.forEach(attr => {\n          element.setAttribute(attr.name, `url('${path}#${attr.value}')`);\n        });\n      });\n    }\n  }\n\n  /**\n   * Caches the children of an SVG element that have `url()`\n   * references that we need to prefix with the current path.\n   */\n  private _cacheChildrenWithExternalReferences(element: SVGElement) {\n    const elementsWithFuncIri = element.querySelectorAll(funcIriAttributeSelector);\n    const elements = (this._elementsWithExternalReferences =\n      this._elementsWithExternalReferences || new Map());\n\n    for (let i = 0; i < elementsWithFuncIri.length; i++) {\n      funcIriAttributes.forEach(attr => {\n        const elementWithReference = elementsWithFuncIri[i];\n        const value = elementWithReference.getAttribute(attr);\n        const match = value ? value.match(funcIriPattern) : null;\n\n        if (match) {\n          let attributes = elements.get(elementWithReference);\n\n          if (!attributes) {\n            attributes = [];\n            elements.set(elementWithReference, attributes);\n          }\n\n          attributes!.push({name: attr, value: match[1]});\n        }\n      });\n    }\n  }\n\n  /** Sets a new SVG icon with a particular name. */\n  private _updateSvgIcon(rawName: string | undefined) {\n    this._svgNamespace = null;\n    this._svgName = null;\n    this._currentIconFetch.unsubscribe();\n\n    if (rawName) {\n      const [namespace, iconName] = this._splitIconName(rawName);\n\n      if (namespace) {\n        this._svgNamespace = namespace;\n      }\n\n      if (iconName) {\n        this._svgName = iconName;\n      }\n\n      this._currentIconFetch = this._iconRegistry\n        .getNamedSvgIcon(iconName, namespace)\n        .pipe(take(1))\n        .subscribe(\n          svg => this._setSvgElement(svg),\n          (err: Error) => {\n            const errorMessage = `Error retrieving icon ${namespace}:${iconName}! ${err.message}`;\n            this._errorHandler.handleError(new Error(errorMessage));\n          },\n        );\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '@angular/material/core';\nimport {MatIcon} from './icon';\n\n@NgModule({\n  imports: [MatCommonModule, MatIcon],\n  exports: [MatIcon, MatCommonModule],\n})\nexport class MatIconModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["observableOf", "observableThrow", "i1.MatIconRegistry"], "mappings": ";;;;;;;;;;;AAmCA;;;AAGG;AACH,IAAI,MAA4C,CAAC;AAEjD;;;AAGG;AACH,SAAS,SAAS,GAAA;AAChB,IAAA,IAAI,MAAM,KAAK,SAAS,EAAE;QACxB,MAAM,GAAG,IAAI,CAAC;AACd,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;YACjC,MAAM,QAAQ,GAAG,MAA8D,CAAC;AAChF,YAAA,IAAI,QAAQ,CAAC,YAAY,KAAK,SAAS,EAAE;gBACvC,MAAM,GAAG,QAAQ,CAAC,YAAY,CAAC,YAAY,CAAC,oBAAoB,EAAE;AAChE,oBAAA,UAAU,EAAE,CAAC,CAAS,KAAK,CAAC;AAC7B,iBAAA,CAAC,CAAC;aACJ;SACF;KACF;AACD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;;;AAQG;AACG,SAAU,qBAAqB,CAAC,IAAY,EAAA;IAChD,OAAO,SAAS,EAAE,EAAE,UAAU,CAAC,IAAI,CAAC,IAAK,IAA+B,CAAC;AAC3E;;AC9CA;;;;AAIG;AACG,SAAU,2BAA2B,CAAC,QAAgB,EAAA;AAC1D,IAAA,OAAO,KAAK,CAAC,CAAA,mCAAA,EAAsC,QAAQ,CAAA,CAAA,CAAG,CAAC,CAAC;AAClE,CAAC;AAED;;;;AAIG;SACa,6BAA6B,GAAA;IAC3C,OAAO,KAAK,CACV,0EAA0E;QACxE,wEAAwE;AACxE,QAAA,cAAc,CACjB,CAAC;AACJ,CAAC;AAED;;;;AAIG;AACG,SAAU,kCAAkC,CAAC,GAAoB,EAAA;IACrE,OAAO,KAAK,CACV,CAAwE,sEAAA,CAAA;QACtE,CAAkD,+CAAA,EAAA,GAAG,CAAI,EAAA,CAAA,CAC5D,CAAC;AACJ,CAAC;AAED;;;;AAIG;AACG,SAAU,sCAAsC,CAAC,OAAiB,EAAA;IACtE,OAAO,KAAK,CACV,CAA0E,wEAAA,CAAA;QACxE,CAAkD,+CAAA,EAAA,OAAO,CAAI,EAAA,CAAA,CAChE,CAAC;AACJ,CAAC;AA0BD;;;AAGG;AACH,MAAM,aAAa,CAAA;AAGjB,IAAA,WAAA,CACS,GAAoB,EACpB,OAA2B,EAC3B,OAAqB,EAAA;QAFrB,IAAG,CAAA,GAAA,GAAH,GAAG,CAAiB;QACpB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAoB;QAC3B,IAAO,CAAA,OAAA,GAAP,OAAO,CAAc;KAC1B;AACL,CAAA;AAKD;;;;;;AAMG;MAEU,eAAe,CAAA;AAiC1B,IAAA,WAAA,CACsB,WAAuB,EACnC,UAAwB,EACF,QAAa,EAC1B,aAA2B,EAAA;QAHxB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAY;QACnC,IAAU,CAAA,UAAA,GAAV,UAAU,CAAc;QAEf,IAAa,CAAA,aAAA,GAAb,aAAa,CAAc;AAlC9C;;AAEG;AACK,QAAA,IAAA,CAAA,eAAe,GAAG,IAAI,GAAG,EAAyB,CAAC;AAE3D;;;AAGG;AACK,QAAA,IAAA,CAAA,eAAe,GAAG,IAAI,GAAG,EAA2B,CAAC;;AAGrD,QAAA,IAAA,CAAA,iBAAiB,GAAG,IAAI,GAAG,EAAsB,CAAC;;AAGlD,QAAA,IAAA,CAAA,qBAAqB,GAAG,IAAI,GAAG,EAAmC,CAAC;;AAGnE,QAAA,IAAA,CAAA,sBAAsB,GAAG,IAAI,GAAG,EAAkB,CAAC;;QAGnD,IAAU,CAAA,UAAA,GAAmB,EAAE,CAAC;AAExC;;;;AAIG;AACK,QAAA,IAAA,CAAA,oBAAoB,GAAG,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,CAAC;AAQrE,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;KAC3B;AAED;;;;AAIG;AACH,IAAA,UAAU,CAAC,QAAgB,EAAE,GAAoB,EAAE,OAAqB,EAAA;AACtE,QAAA,OAAO,IAAI,CAAC,qBAAqB,CAAC,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;KAC/D;AAED;;;;AAIG;AACH,IAAA,iBAAiB,CAAC,QAAgB,EAAE,OAAiB,EAAE,OAAqB,EAAA;AAC1E,QAAA,OAAO,IAAI,CAAC,4BAA4B,CAAC,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;KAC1E;AAED;;;;;AAKG;AACH,IAAA,qBAAqB,CACnB,SAAiB,EACjB,QAAgB,EAChB,GAAoB,EACpB,OAAqB,EAAA;AAErB,QAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,aAAa,CAAC,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;KAC3F;AAED;;;;;;;AAOG;AACH,IAAA,kBAAkB,CAAC,QAAsB,EAAA;AACvC,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC/B,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;;AAKG;AACH,IAAA,4BAA4B,CAC1B,SAAiB,EACjB,QAAgB,EAChB,OAAiB,EACjB,OAAqB,EAAA;AAErB,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;;QAG7E,IAAI,CAAC,YAAY,EAAE;AACjB,YAAA,MAAM,sCAAsC,CAAC,OAAO,CAAC,CAAC;SACvD;;AAGD,QAAA,MAAM,cAAc,GAAG,qBAAqB,CAAC,YAAY,CAAC,CAAC;AAC3D,QAAA,OAAO,IAAI,CAAC,iBAAiB,CAC3B,SAAS,EACT,QAAQ,EACR,IAAI,aAAa,CAAC,EAAE,EAAE,cAAc,EAAE,OAAO,CAAC,CAC/C,CAAC;KACH;AAED;;;AAGG;IACH,aAAa,CAAC,GAAoB,EAAE,OAAqB,EAAA;QACvD,OAAO,IAAI,CAAC,wBAAwB,CAAC,EAAE,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;KACxD;AAED;;;AAGG;IACH,oBAAoB,CAAC,OAAiB,EAAE,OAAqB,EAAA;QAC3D,OAAO,IAAI,CAAC,+BAA+B,CAAC,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;KACnE;AAED;;;;AAIG;AACH,IAAA,wBAAwB,CAAC,SAAiB,EAAE,GAAoB,EAAE,OAAqB,EAAA;AACrF,QAAA,OAAO,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,IAAI,aAAa,CAAC,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;KACpF;AAED;;;;AAIG;AACH,IAAA,+BAA+B,CAC7B,SAAiB,EACjB,OAAiB,EACjB,OAAqB,EAAA;AAErB,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAE7E,IAAI,CAAC,YAAY,EAAE;AACjB,YAAA,MAAM,sCAAsC,CAAC,OAAO,CAAC,CAAC;SACvD;;AAGD,QAAA,MAAM,cAAc,GAAG,qBAAqB,CAAC,YAAY,CAAC,CAAC;AAC3D,QAAA,OAAO,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,IAAI,aAAa,CAAC,EAAE,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC;KAC7F;AAED;;;;;;;;;;;;;;;;;;;;AAoBG;AACH,IAAA,sBAAsB,CAAC,KAAa,EAAE,UAAA,GAAqB,KAAK,EAAA;QAC9D,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;AACnD,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;AAGG;AACH,IAAA,qBAAqB,CAAC,KAAa,EAAA;QACjC,OAAO,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC;KACxD;AAED;;;AAGG;IACH,sBAAsB,CAAC,GAAG,UAAoB,EAAA;AAC5C,QAAA,IAAI,CAAC,oBAAoB,GAAG,UAAU,CAAC;AACvC,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;AAGG;IACH,sBAAsB,GAAA;QACpB,OAAO,IAAI,CAAC,oBAAoB,CAAC;KAClC;AAED;;;;;;;AAOG;AACH,IAAA,iBAAiB,CAAC,OAAwB,EAAA;AACxC,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,eAAe,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QAE5E,IAAI,CAAC,GAAG,EAAE;AACR,YAAA,MAAM,kCAAkC,CAAC,OAAO,CAAC,CAAC;SACnD;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAEnD,IAAI,UAAU,EAAE;AACd,YAAA,OAAOA,EAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;SAC3C;QAED,OAAO,IAAI,CAAC,sBAAsB,CAAC,IAAI,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CACvE,GAAG,CAAC,GAAG,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAI,EAAE,GAAG,CAAC,CAAC,EACjD,GAAG,CAAC,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,CAC1B,CAAC;KACH;AAED;;;;;;;AAOG;AACH,IAAA,eAAe,CAAC,IAAY,EAAE,SAAA,GAAoB,EAAE,EAAA;QAClD,MAAM,GAAG,GAAG,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QACrC,IAAI,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;;QAG3C,IAAI,MAAM,EAAE;AACV,YAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;SACvC;;QAGD,MAAM,GAAG,IAAI,CAAC,2BAA2B,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAE3D,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AACtC,YAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;SACvC;;QAGD,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAE3D,IAAI,cAAc,EAAE;YAClB,OAAO,IAAI,CAAC,yBAAyB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;SAC7D;AAED,QAAA,OAAOC,UAAe,CAAC,2BAA2B,CAAC,GAAG,CAAC,CAAC,CAAC;KAC1D;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;AACrB,QAAA,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;AAC7B,QAAA,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;AAC7B,QAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;KAChC;AAED;;AAEG;AACK,IAAA,iBAAiB,CAAC,MAAqB,EAAA;AAC7C,QAAA,IAAI,MAAM,CAAC,OAAO,EAAE;;AAElB,YAAA,OAAOD,EAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAA6B,CAAC,CAAC,CAAC,CAAC;SAC1F;aAAM;;YAEL,OAAO,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;SAC5E;KACF;AAED;;;;;;;AAOG;IACK,yBAAyB,CAC/B,IAAY,EACZ,cAA+B,EAAA;;;QAI/B,MAAM,SAAS,GAAG,IAAI,CAAC,8BAA8B,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;QAE5E,IAAI,SAAS,EAAE;;;;AAIb,YAAA,OAAOA,EAAY,CAAC,SAAS,CAAC,CAAC;SAChC;;;QAID,MAAM,oBAAoB,GAAqC,cAAc;aAC1E,MAAM,CAAC,aAAa,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;aAC/C,GAAG,CAAC,aAAa,IAAG;AACnB,YAAA,OAAO,IAAI,CAAC,yBAAyB,CAAC,aAAa,CAAC,CAAC,IAAI,CACvD,UAAU,CAAC,CAAC,GAAsB,KAAI;AACpC,gBAAA,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,eAAe,CAAC,YAAY,EAAE,aAAa,CAAC,GAAG,CAAC,CAAC;;;gBAItF,MAAM,YAAY,GAAG,CAAyB,sBAAA,EAAA,GAAG,YAAY,GAAG,CAAC,OAAO,CAAA,CAAE,CAAC;gBAC3E,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;AACxD,gBAAA,OAAOA,EAAY,CAAC,IAAI,CAAC,CAAC;aAC3B,CAAC,CACH,CAAC;AACJ,SAAC,CAAC,CAAC;;;QAIL,OAAO,QAAQ,CAAC,oBAAoB,CAAC,CAAC,IAAI,CACxC,GAAG,CAAC,MAAK;YACP,MAAM,SAAS,GAAG,IAAI,CAAC,8BAA8B,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;;YAG5E,IAAI,CAAC,SAAS,EAAE;AACd,gBAAA,MAAM,2BAA2B,CAAC,IAAI,CAAC,CAAC;aACzC;AAED,YAAA,OAAO,SAAS,CAAC;SAClB,CAAC,CACH,CAAC;KACH;AAED;;;;AAIG;IACK,8BAA8B,CACpC,QAAgB,EAChB,cAA+B,EAAA;;AAG/B,QAAA,KAAK,IAAI,CAAC,GAAG,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AACnD,YAAA,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;;;;;AAMjC,YAAA,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;gBACtE,MAAM,GAAG,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAA6B,CAAC,CAAC;AACtE,gBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAG,EAAE,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;gBAC7E,IAAI,SAAS,EAAE;AACb,oBAAA,OAAO,SAAS,CAAC;iBAClB;aACF;SACF;AACD,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;AAGG;AACK,IAAA,sBAAsB,CAAC,MAAqB,EAAA;AAClD,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CACjC,GAAG,CAAC,OAAO,KAAK,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC,CAAC,EAC1C,GAAG,CAAC,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAA6B,CAAC,CAAC,CACrE,CAAC;KACH;AAED;;;AAGG;AACK,IAAA,yBAAyB,CAAC,MAAqB,EAAA;AACrD,QAAA,IAAI,MAAM,CAAC,OAAO,EAAE;AAClB,YAAA,OAAOA,EAAY,CAAC,IAAI,CAAC,CAAC;SAC3B;QAED,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,KAAK,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;KACjF;AAED;;;;AAIG;AACK,IAAA,sBAAsB,CAC5B,OAAmB,EACnB,QAAgB,EAChB,OAAqB,EAAA;;;QAIrB,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,CAAQ,KAAA,EAAA,QAAQ,CAAI,EAAA,CAAA,CAAC,CAAC;QAE/D,IAAI,CAAC,UAAU,EAAE;AACf,YAAA,OAAO,IAAI,CAAC;SACb;;;QAID,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAY,CAAC;AAC1D,QAAA,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;;;QAIlC,IAAI,WAAW,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,KAAK,EAAE;YAChD,OAAO,IAAI,CAAC,iBAAiB,CAAC,WAAyB,EAAE,OAAO,CAAC,CAAC;SACnE;;;;QAKD,IAAI,WAAW,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,QAAQ,EAAE;AACnD,YAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,EAAE,OAAO,CAAC,CAAC;SACzE;;;;;;QAOD,MAAM,GAAG,GAAG,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC,CAAC;;AAE7E,QAAA,GAAG,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;QAE7B,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;KAC7C;AAED;;AAEG;AACK,IAAA,qBAAqB,CAAC,GAAgB,EAAA;QAC5C,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAChD,QAAA,GAAG,CAAC,SAAS,GAAG,GAAwB,CAAC;QACzC,MAAM,GAAG,GAAG,GAAG,CAAC,aAAa,CAAC,KAAK,CAAe,CAAC;;QAGnD,IAAI,CAAC,GAAG,EAAE;AACR,YAAA,MAAM,KAAK,CAAC,qBAAqB,CAAC,CAAC;SACpC;AAED,QAAA,OAAO,GAAG,CAAC;KACZ;AAED;;AAEG;AACK,IAAA,aAAa,CAAC,OAAgB,EAAA;QACpC,MAAM,GAAG,GAAG,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC,CAAC;AAC7E,QAAA,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;;AAGtC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC1C,MAAM,EAAC,IAAI,EAAE,KAAK,EAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AAEpC,YAAA,IAAI,IAAI,KAAK,IAAI,EAAE;AACjB,gBAAA,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;aAC/B;SACF;AAED,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAClD,YAAA,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE;AAClE,gBAAA,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;aACxD;SACF;AAED,QAAA,OAAO,GAAG,CAAC;KACZ;AAED;;AAEG;IACK,iBAAiB,CAAC,GAAe,EAAE,OAAqB,EAAA;AAC9D,QAAA,GAAG,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AAC5B,QAAA,GAAG,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AACnC,QAAA,GAAG,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AAClC,QAAA,GAAG,CAAC,YAAY,CAAC,qBAAqB,EAAE,eAAe,CAAC,CAAC;QACzD,GAAG,CAAC,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;AAEvC,QAAA,IAAI,OAAO,IAAI,OAAO,CAAC,OAAO,EAAE;YAC9B,GAAG,CAAC,YAAY,CAAC,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;SAC9C;AAED,QAAA,OAAO,GAAG,CAAC;KACZ;AAED;;;AAGG;AACK,IAAA,UAAU,CAAC,UAAyB,EAAA;QAC1C,MAAM,EAAC,GAAG,EAAE,OAAO,EAAE,OAAO,EAAC,GAAG,UAAU,CAAC;AAC3C,QAAA,MAAM,eAAe,GAAG,OAAO,EAAE,eAAe,IAAI,KAAK,CAAC;AAE1D,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,MAAM,6BAA6B,EAAE,CAAC;SACvC;;AAGD,QAAA,IAAI,OAAO,IAAI,IAAI,EAAE;AACnB,YAAA,MAAM,KAAK,CAAC,CAAA,4BAAA,EAA+B,OAAO,CAAA,EAAA,CAAI,CAAC,CAAC;SACzD;AAED,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,eAAe,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;;QAG5E,IAAI,CAAC,GAAG,EAAE;AACR,YAAA,MAAM,kCAAkC,CAAC,OAAO,CAAC,CAAC;SACnD;;;;QAKD,MAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAE5D,IAAI,eAAe,EAAE;AACnB,YAAA,OAAO,eAAe,CAAC;SACxB;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,EAAC,YAAY,EAAE,MAAM,EAAE,eAAe,EAAC,CAAC,CAAC,IAAI,CACjF,GAAG,CAAC,GAAG,IAAG;;;AAGR,YAAA,OAAO,qBAAqB,CAAC,GAAG,CAAC,CAAC;SACnC,CAAC,EACF,QAAQ,CAAC,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EACtD,KAAK,EAAE,CACR,CAAC;QAEF,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACzC,QAAA,OAAO,GAAG,CAAC;KACZ;AAED;;;;;AAKG;AACK,IAAA,iBAAiB,CAAC,SAAiB,EAAE,QAAgB,EAAE,MAAqB,EAAA;AAClF,QAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAE,MAAM,CAAC,CAAC;AAC/D,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;AAIG;IACK,oBAAoB,CAAC,SAAiB,EAAE,MAAqB,EAAA;QACnE,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAE5D,IAAI,eAAe,EAAE;AACnB,YAAA,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC9B;aAAM;YACL,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;SAC/C;AAED,QAAA,OAAO,IAAI,CAAC;KACb;;AAGO,IAAA,qBAAqB,CAAC,MAA2B,EAAA;AACvD,QAAA,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;YACtB,MAAM,GAAG,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACvD,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;AAC5C,YAAA,MAAM,CAAC,UAAU,GAAG,GAAG,CAAC;SACzB;QAED,OAAO,MAAM,CAAC,UAAU,CAAC;KAC1B;;IAGO,2BAA2B,CAAC,SAAiB,EAAE,IAAY,EAAA;AACjE,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC/C,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YAEnD,IAAI,MAAM,EAAE;gBACV,OAAO,oBAAoB,CAAC,MAAM,CAAC;AACjC,sBAAE,IAAI,aAAa,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC;sBACnD,IAAI,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;aACrC;SACF;AAED,QAAA,OAAO,SAAS,CAAC;KAClB;AAhmBU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,wFAoCJ,QAAQ,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,YAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AApCnB,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,cADH,MAAM,EAAA,CAAA,CAAA,EAAA;;2FAClB,eAAe,EAAA,UAAA,EAAA,CAAA;kBAD3B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;0BAmC3B,QAAQ;;0BAER,QAAQ;;0BAAI,MAAM;2BAAC,QAAQ,CAAA;;AA+jBhC;AACM,SAAU,8BAA8B,CAC5C,cAA+B,EAC/B,UAAsB,EACtB,SAAuB,EACvB,YAA0B,EAC1B,QAAc,EAAA;AAEd,IAAA,OAAO,cAAc,IAAI,IAAI,eAAe,CAAC,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;AAC9F,CAAC;AAED;AACa,MAAA,sBAAsB,GAAG;;AAEpC,IAAA,OAAO,EAAE,eAAe;AACxB,IAAA,IAAI,EAAE;QACJ,CAAC,IAAI,QAAQ,EAAE,EAAE,IAAI,QAAQ,EAAE,EAAE,eAAe,CAAC;AACjD,QAAA,CAAC,IAAI,QAAQ,EAAE,EAAE,UAAU,CAAC;QAC5B,YAAY;QACZ,YAAY;AACZ,QAAA,CAAC,IAAI,QAAQ,EAAE,EAAE,QAA+B,CAAC;AAClD,KAAA;AACD,IAAA,UAAU,EAAE,8BAA8B;EAC1C;AAEF;AACA,SAAS,QAAQ,CAAC,GAAe,EAAA;AAC/B,IAAA,OAAO,GAAG,CAAC,SAAS,CAAC,IAAI,CAAe,CAAC;AAC3C,CAAC;AAED;AACA,SAAS,OAAO,CAAC,SAAiB,EAAE,IAAY,EAAA;AAC9C,IAAA,OAAO,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC;AAChC,CAAC;AAED,SAAS,oBAAoB,CAAC,KAAU,EAAA;IACtC,OAAO,CAAC,EAAE,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;AACxC;;ACxtBA;MACa,wBAAwB,GAAG,IAAI,cAAc,CACxD,0BAA0B,EAC1B;AAEF;;;;AAIG;MACU,iBAAiB,GAAG,IAAI,cAAc,CAAkB,mBAAmB,EAAE;AACxF,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,yBAAyB;AACnC,CAAA,EAAE;AAUH;SACgB,yBAAyB,GAAA;AACvC,IAAA,MAAM,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AACnC,IAAA,MAAM,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC;IAExD,OAAO;;;QAGL,WAAW,EAAE,OAAO,SAAS,GAAG,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,MAAM,GAAG,EAAE,CAAC;KAC5E,CAAC;AACJ,CAAC;AAED;AACA,MAAM,iBAAiB,GAAG;IACxB,WAAW;IACX,eAAe;IACf,KAAK;IACL,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,cAAc;IACd,YAAY;IACZ,YAAY;IACZ,MAAM;IACN,QAAQ;CACT,CAAC;AAEF;AACA,MAAM,wBAAwB,GAAG,iBAAiB,CAAC,GAAG,CAAC,IAAI,IAAI,CAAI,CAAA,EAAA,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAEvF;AACA,MAAM,cAAc,GAAG,2BAA2B,CAAC;AAEnD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BG;MAqBU,OAAO,CAAA;;AAIlB,IAAA,IACI,KAAK,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC;KAC1C;IACD,IAAI,KAAK,CAAC,KAAgC,EAAA;AACxC,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;KACrB;;AAWD,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;IACD,IAAI,OAAO,CAAC,KAAa,EAAA;AACvB,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,QAAQ,EAAE;YAC3B,IAAI,KAAK,EAAE;AACT,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;aAC5B;AAAM,iBAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACxB,IAAI,CAAC,gBAAgB,EAAE,CAAC;aACzB;AACD,YAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;SACvB;KACF;;AAID,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;IACD,IAAI,OAAO,CAAC,KAAa,EAAA;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;AAE/C,QAAA,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ,EAAE;AAC9B,YAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;YACzB,IAAI,CAAC,sBAAsB,EAAE,CAAC;SAC/B;KACF;;AAID,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,KAAa,EAAA;QACxB,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;AAE/C,QAAA,IAAI,QAAQ,KAAK,IAAI,CAAC,SAAS,EAAE;AAC/B,YAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;YAC1B,IAAI,CAAC,sBAAsB,EAAE,CAAC;SAC/B;KACF;IAkBD,WACW,CAAA,WAAoC,EACrC,aAA8B,EACZ,UAAkB,EACT,SAA0B,EAC5C,aAA2B,EAG5C,QAAgC,EAAA;QAPvB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAyB;QACrC,IAAa,CAAA,aAAA,GAAb,aAAa,CAAiB;QAEH,IAAS,CAAA,SAAA,GAAT,SAAS,CAAiB;QAC5C,IAAa,CAAA,aAAA,GAAb,aAAa,CAAc;AA1E9C;;;AAGG;QAEH,IAAM,CAAA,MAAA,GAAY,KAAK,CAAC;QAiDhB,IAAqB,CAAA,qBAAA,GAAa,EAAE,CAAC;;AAarC,QAAA,IAAA,CAAA,iBAAiB,GAAG,YAAY,CAAC,KAAK,CAAC;QAY7C,IAAI,QAAQ,EAAE;AACZ,YAAA,IAAI,QAAQ,CAAC,KAAK,EAAE;gBAClB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,KAAK,CAAC;aAClD;AAED,YAAA,IAAI,QAAQ,CAAC,OAAO,EAAE;AACpB,gBAAA,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;aACjC;SACF;;;QAID,IAAI,CAAC,UAAU,EAAE;YACf,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;SAC/D;KACF;AAED;;;;;;;;;;;;AAYG;AACK,IAAA,cAAc,CAAC,QAAgB,EAAA;QACrC,IAAI,CAAC,QAAQ,EAAE;AACb,YAAA,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;SACjB;QACD,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAClC,QAAA,QAAQ,KAAK,CAAC,MAAM;AAClB,YAAA,KAAK,CAAC;gBACJ,OAAO,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACxB,YAAA,KAAK,CAAC;AACJ,gBAAA,OAAyB,KAAK,CAAC;AACjC,YAAA;gBACE,MAAM,KAAK,CAAC,CAAuB,oBAAA,EAAA,QAAQ,GAAG,CAAC,CAAC;SACnD;KACF;IAED,QAAQ,GAAA;;;QAGN,IAAI,CAAC,sBAAsB,EAAE,CAAC;KAC/B;IAED,kBAAkB,GAAA;AAChB,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,+BAA+B,CAAC;AAE5D,QAAA,IAAI,cAAc,IAAI,cAAc,CAAC,IAAI,EAAE;YACzC,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;;;;;;;AAQ7C,YAAA,IAAI,OAAO,KAAK,IAAI,CAAC,aAAa,EAAE;AAClC,gBAAA,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC;AAC7B,gBAAA,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;aACxC;SACF;KACF;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,CAAC;AAErC,QAAA,IAAI,IAAI,CAAC,+BAA+B,EAAE;AACxC,YAAA,IAAI,CAAC,+BAA+B,CAAC,KAAK,EAAE,CAAC;SAC9C;KACF;IAED,cAAc,GAAA;AACZ,QAAA,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;KACtB;AAEO,IAAA,cAAc,CAAC,GAAe,EAAA;QACpC,IAAI,CAAC,gBAAgB,EAAE,CAAC;;;QAIxB,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;AAC1C,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,QAAA,IAAI,CAAC,oCAAoC,CAAC,GAAG,CAAC,CAAC;AAC/C,QAAA,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;KACjD;IAEO,gBAAgB,GAAA;AACtB,QAAA,MAAM,aAAa,GAAgB,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;AAClE,QAAA,IAAI,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC;AAEjD,QAAA,IAAI,IAAI,CAAC,+BAA+B,EAAE;AACxC,YAAA,IAAI,CAAC,+BAA+B,CAAC,KAAK,EAAE,CAAC;SAC9C;;;QAID,OAAO,UAAU,EAAE,EAAE;YACnB,MAAM,KAAK,GAAG,aAAa,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;;;AAInD,YAAA,IAAI,KAAK,CAAC,QAAQ,KAAK,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,KAAK,EAAE;gBAClE,KAAK,CAAC,MAAM,EAAE,CAAC;aAChB;SACF;KACF;IAEO,sBAAsB,GAAA;AAC5B,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE;YAC1B,OAAO;SACR;AAED,QAAA,MAAM,IAAI,GAAgB,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;AACzD,QAAA,MAAM,cAAc,GAAG,CACrB,IAAI,CAAC,OAAO;AACV,cAAE,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC;cAClE,IAAI,CAAC,aAAa,CAAC,sBAAsB,EAAE,EAC/C,MAAM,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAE5C,QAAA,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;AAClF,QAAA,cAAc,CAAC,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;AACnE,QAAA,IAAI,CAAC,qBAAqB,GAAG,cAAc,CAAC;AAE5C,QAAA,IACE,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,sBAAsB;AAC7C,YAAA,CAAC,cAAc,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAC7C;AACA,YAAA,IAAI,IAAI,CAAC,sBAAsB,EAAE;gBAC/B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;aACpD;AACD,YAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aACnC;AACD,YAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC;SAC7C;KACF;AAED;;;;AAIG;AACK,IAAA,iBAAiB,CAAC,KAAa,EAAA;QACrC,OAAO,OAAO,KAAK,KAAK,QAAQ,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;KACvE;AAED;;;;AAIG;AACK,IAAA,wBAAwB,CAAC,IAAY,EAAA;AAC3C,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,+BAA+B,CAAC;QAEtD,IAAI,QAAQ,EAAE;YACZ,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,OAAO,KAAI;AAClC,gBAAA,KAAK,CAAC,OAAO,CAAC,IAAI,IAAG;AACnB,oBAAA,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,CAAQ,KAAA,EAAA,IAAI,IAAI,IAAI,CAAC,KAAK,CAAA,EAAA,CAAI,CAAC,CAAC;AAClE,iBAAC,CAAC,CAAC;AACL,aAAC,CAAC,CAAC;SACJ;KACF;AAED;;;AAGG;AACK,IAAA,oCAAoC,CAAC,OAAmB,EAAA;QAC9D,MAAM,mBAAmB,GAAG,OAAO,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;AAC/E,QAAA,MAAM,QAAQ,IAAI,IAAI,CAAC,+BAA+B;AACpD,YAAA,IAAI,CAAC,+BAA+B,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;AAErD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,mBAAmB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACnD,YAAA,iBAAiB,CAAC,OAAO,CAAC,IAAI,IAAG;AAC/B,gBAAA,MAAM,oBAAoB,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC;gBACpD,MAAM,KAAK,GAAG,oBAAoB,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AACtD,gBAAA,MAAM,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC;gBAEzD,IAAI,KAAK,EAAE;oBACT,IAAI,UAAU,GAAG,QAAQ,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;oBAEpD,IAAI,CAAC,UAAU,EAAE;wBACf,UAAU,GAAG,EAAE,CAAC;AAChB,wBAAA,QAAQ,CAAC,GAAG,CAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC;qBAChD;AAED,oBAAA,UAAW,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC;iBACjD;AACH,aAAC,CAAC,CAAC;SACJ;KACF;;AAGO,IAAA,cAAc,CAAC,OAA2B,EAAA;AAChD,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrB,QAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,CAAC;QAErC,IAAI,OAAO,EAAE;AACX,YAAA,MAAM,CAAC,SAAS,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAE3D,IAAI,SAAS,EAAE;AACb,gBAAA,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;aAChC;YAED,IAAI,QAAQ,EAAE;AACZ,gBAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;aAC1B;AAED,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,aAAa;AACxC,iBAAA,eAAe,CAAC,QAAQ,EAAE,SAAS,CAAC;AACpC,iBAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACb,iBAAA,SAAS,CACR,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAC/B,CAAC,GAAU,KAAI;gBACb,MAAM,YAAY,GAAG,CAAA,sBAAA,EAAyB,SAAS,CAAA,CAAA,EAAI,QAAQ,CAAA,EAAA,EAAK,GAAG,CAAC,OAAO,CAAA,CAAE,CAAC;gBACtF,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;AAC1D,aAAC,CACF,CAAC;SACL;KACF;AAhUU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,OAAO,EAqFL,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAAE,eAAA,EAAA,EAAA,EAAA,KAAA,EAAA,aAAa,EAChB,SAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,iBAAiB,yCAGjB,wBAAwB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAzFvB,OAAO,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAiBC,gBAAgB,CAAA,EAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,KAAA,EAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,iCAAA,EAAA,yBAAA,EAAA,uCAAA,EAAA,yBAAA,EAAA,sBAAA,EAAA,8BAAA,EAAA,0BAAA,EAAA,eAAA,EAAA,oCAAA,EAAA,uBAAA,EAAA,QAAA,EAAA,yBAAA,EAAA,qEAAA,EAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,SAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EApCzB,2BAA2B,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,o3BAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAmB1B,OAAO,EAAA,UAAA,EAAA,CAAA;kBApBnB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,2BAA2B,EAC3B,QAAA,EAAA,UAAU,EACV,QAAA,EAAA,SAAS,EAEb,IAAA,EAAA;AACJ,wBAAA,MAAM,EAAE,KAAK;AACb,wBAAA,OAAO,EAAE,sBAAsB;AAC/B,wBAAA,SAAS,EAAE,6BAA6B;AACxC,wBAAA,2BAA2B,EAAE,mCAAmC;AAChE,wBAAA,2BAA2B,EAAE,sBAAsB;AACnD,wBAAA,gCAAgC,EAAE,0BAA0B;AAC5D,wBAAA,iBAAiB,EAAE,oCAAoC;AACvD,wBAAA,yBAAyB,EAAE,QAAQ;AACnC,wBAAA,2BAA2B,EAAE,+DAA+D;qBAC7F,EACc,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,MAAM,cACnC,IAAI,EAAA,MAAA,EAAA,CAAA,o3BAAA,CAAA,EAAA,CAAA;;0BAuFb,SAAS;2BAAC,aAAa,CAAA;;0BACvB,MAAM;2BAAC,iBAAiB,CAAA;;0BAExB,QAAQ;;0BACR,MAAM;2BAAC,wBAAwB,CAAA;yCApF9B,KAAK,EAAA,CAAA;sBADR,KAAK;gBAcN,MAAM,EAAA,CAAA;sBADL,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAKhC,OAAO,EAAA,CAAA;sBADV,KAAK;gBAkBF,OAAO,EAAA,CAAA;sBADV,KAAK;gBAgBF,QAAQ,EAAA,CAAA;sBADX,KAAK;;;MC1LK,aAAa,CAAA;8GAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAb,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,YAHd,eAAe,EAAE,OAAO,CACxB,EAAA,OAAA,EAAA,CAAA,OAAO,EAAE,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;+GAEvB,aAAa,EAAA,OAAA,EAAA,CAHd,eAAe,EACN,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAEvB,aAAa,EAAA,UAAA,EAAA,CAAA;kBAJzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,OAAO,CAAC;AACnC,oBAAA,OAAO,EAAE,CAAC,OAAO,EAAE,eAAe,CAAC;AACpC,iBAAA,CAAA;;;ACfD;;AAEG;;;;"}