/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import * as o from '../../../../output/output_ast';
import * as ir from '../../ir';
/**
 * Safe read expressions such as `a?.b` have different semantics in Angular templates as
 * compared to JavaScript. In particular, they default to `null` instead of `undefined`. This phase
 * finds all unresolved safe read expressions, and converts them into the appropriate output AST
 * reads, guarded by null checks. We generate temporaries as needed, to avoid re-evaluating the same
 * sub-expression multiple times.
 */
export function expandSafeReads(job) {
    for (const unit of job.units) {
        for (const op of unit.ops()) {
            ir.transformExpressionsInOp(op, e => safeTransform(e, { job }), ir.VisitorContextFlag.None);
            ir.transformExpressionsInOp(op, ternaryTransform, ir.VisitorContextFlag.None);
        }
    }
}
// A lookup set of all the expression kinds that require a temporary variable to be generated.
const requiresTemporary = [
    o.InvokeFunctionExpr, o.LiteralArrayExpr, o.LiteralMapExpr, ir.SafeInvokeFunctionExpr,
    ir.PipeBindingExpr
].map(e => e.constructor.name);
function needsTemporaryInSafeAccess(e) {
    // TODO: We probably want to use an expression visitor to recursively visit all descendents.
    // However, that would potentially do a lot of extra work (because it cannot short circuit), so we
    // implement the logic ourselves for now.
    if (e instanceof o.UnaryOperatorExpr) {
        return needsTemporaryInSafeAccess(e.expr);
    }
    else if (e instanceof o.BinaryOperatorExpr) {
        return needsTemporaryInSafeAccess(e.lhs) || needsTemporaryInSafeAccess(e.rhs);
    }
    else if (e instanceof o.ConditionalExpr) {
        if (e.falseCase && needsTemporaryInSafeAccess(e.falseCase))
            return true;
        return needsTemporaryInSafeAccess(e.condition) || needsTemporaryInSafeAccess(e.trueCase);
    }
    else if (e instanceof o.NotExpr) {
        return needsTemporaryInSafeAccess(e.condition);
    }
    else if (e instanceof ir.AssignTemporaryExpr) {
        return needsTemporaryInSafeAccess(e.expr);
    }
    else if (e instanceof o.ReadPropExpr) {
        return needsTemporaryInSafeAccess(e.receiver);
    }
    else if (e instanceof o.ReadKeyExpr) {
        return needsTemporaryInSafeAccess(e.receiver) || needsTemporaryInSafeAccess(e.index);
    }
    // TODO: Switch to a method which is exhaustive of newly added expression subtypes.
    return e instanceof o.InvokeFunctionExpr || e instanceof o.LiteralArrayExpr ||
        e instanceof o.LiteralMapExpr || e instanceof ir.SafeInvokeFunctionExpr ||
        e instanceof ir.PipeBindingExpr;
}
function temporariesIn(e) {
    const temporaries = new Set();
    // TODO: Although it's not currently supported by the transform helper, we should be able to
    // short-circuit exploring the tree to do less work. In particular, we don't have to penetrate
    // into the subexpressions of temporary assignments.
    ir.transformExpressionsInExpression(e, e => {
        if (e instanceof ir.AssignTemporaryExpr) {
            temporaries.add(e.xref);
        }
        return e;
    }, ir.VisitorContextFlag.None);
    return temporaries;
}
function eliminateTemporaryAssignments(e, tmps, ctx) {
    // TODO: We can be more efficient than the transform helper here. We don't need to visit any
    // descendents of temporary assignments.
    ir.transformExpressionsInExpression(e, e => {
        if (e instanceof ir.AssignTemporaryExpr && tmps.has(e.xref)) {
            const read = new ir.ReadTemporaryExpr(e.xref);
            // `TemplateDefinitionBuilder` has the (accidental?) behavior of generating assignments of
            // temporary variables to themselves. This happens because some subexpression that the
            // temporary refers to, possibly through nested temporaries, has a function call. We copy that
            // behavior here.
            return ctx.job.compatibility === ir.CompatibilityMode.TemplateDefinitionBuilder ?
                new ir.AssignTemporaryExpr(read, read.xref) :
                read;
        }
        return e;
    }, ir.VisitorContextFlag.None);
    return e;
}
/**
 * Creates a safe ternary guarded by the input expression, and with a body generated by the provided
 * callback on the input expression. Generates a temporary variable assignment if needed, and
 * deduplicates nested temporary assignments if needed.
 */
function safeTernaryWithTemporary(guard, body, ctx) {
    let result;
    if (needsTemporaryInSafeAccess(guard)) {
        const xref = ctx.job.allocateXrefId();
        result = [new ir.AssignTemporaryExpr(guard, xref), new ir.ReadTemporaryExpr(xref)];
    }
    else {
        result = [guard, guard.clone()];
        // Consider an expression like `a?.[b?.c()]?.d`. The `b?.c()` will be transformed first,
        // introducing a temporary assignment into the key. Then, as part of expanding the `?.d`. That
        // assignment will be duplicated into both the guard and expression sides. We de-duplicate it,
        // by transforming it from an assignment into a read on the expression side.
        eliminateTemporaryAssignments(result[1], temporariesIn(result[0]), ctx);
    }
    return new ir.SafeTernaryExpr(result[0], body(result[1]));
}
function isSafeAccessExpression(e) {
    return e instanceof ir.SafePropertyReadExpr || e instanceof ir.SafeKeyedReadExpr ||
        e instanceof ir.SafeInvokeFunctionExpr;
}
function isUnsafeAccessExpression(e) {
    return e instanceof o.ReadPropExpr || e instanceof o.ReadKeyExpr ||
        e instanceof o.InvokeFunctionExpr;
}
function isAccessExpression(e) {
    return isSafeAccessExpression(e) || isUnsafeAccessExpression(e);
}
function deepestSafeTernary(e) {
    if (isAccessExpression(e) && e.receiver instanceof ir.SafeTernaryExpr) {
        let st = e.receiver;
        while (st.expr instanceof ir.SafeTernaryExpr) {
            st = st.expr;
        }
        return st;
    }
    return null;
}
// TODO: When strict compatibility with TemplateDefinitionBuilder is not required, we can use `&&`
// instead to save some code size.
function safeTransform(e, ctx) {
    if (!isAccessExpression(e)) {
        return e;
    }
    const dst = deepestSafeTernary(e);
    if (dst) {
        if (e instanceof o.InvokeFunctionExpr) {
            dst.expr = dst.expr.callFn(e.args);
            return e.receiver;
        }
        if (e instanceof o.ReadPropExpr) {
            dst.expr = dst.expr.prop(e.name);
            return e.receiver;
        }
        if (e instanceof o.ReadKeyExpr) {
            dst.expr = dst.expr.key(e.index);
            return e.receiver;
        }
        if (e instanceof ir.SafeInvokeFunctionExpr) {
            dst.expr = safeTernaryWithTemporary(dst.expr, (r) => r.callFn(e.args), ctx);
            return e.receiver;
        }
        if (e instanceof ir.SafePropertyReadExpr) {
            dst.expr = safeTernaryWithTemporary(dst.expr, (r) => r.prop(e.name), ctx);
            return e.receiver;
        }
        if (e instanceof ir.SafeKeyedReadExpr) {
            dst.expr = safeTernaryWithTemporary(dst.expr, (r) => r.key(e.index), ctx);
            return e.receiver;
        }
    }
    else {
        if (e instanceof ir.SafeInvokeFunctionExpr) {
            return safeTernaryWithTemporary(e.receiver, (r) => r.callFn(e.args), ctx);
        }
        if (e instanceof ir.SafePropertyReadExpr) {
            return safeTernaryWithTemporary(e.receiver, (r) => r.prop(e.name), ctx);
        }
        if (e instanceof ir.SafeKeyedReadExpr) {
            return safeTernaryWithTemporary(e.receiver, (r) => r.key(e.index), ctx);
        }
    }
    return e;
}
function ternaryTransform(e) {
    if (!(e instanceof ir.SafeTernaryExpr)) {
        return e;
    }
    return new o.ConditionalExpr(new o.BinaryOperatorExpr(o.BinaryOperator.Equals, e.guard, o.NULL_EXPR), o.NULL_EXPR, e.expr);
}
//# sourceMappingURL=data:application/json;base64,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