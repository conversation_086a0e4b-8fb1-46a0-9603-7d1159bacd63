/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["kgp", [["AM", "PM"], u, u], u, [["N.", "P.", "R.", "T.", "V.", "P.", "S."], ["num.", "pir.", "rég.", "tẽg.", "vẽn.", "pén.", "sav."], ["numĩggu", "pir-kurã-há", "régre-kurã-há", "tẽgtũ-kurã-há", "vẽnhkãgra-kurã-há", "pénkar-kurã-há", "savnu"], ["N.", "1kh.", "2kh.", "3kh.", "4kh.", "5kh.", "S."]], u, [["1K", "2K", "3K", "4K", "5K", "6K", "7K", "8K", "9K", "10K", "11K", "12K"], ["1Ky.", "2Ky.", "3Ky.", "4Ky.", "5Ky.", "6Ky.", "7Ky.", "8Ky.", "9Ky.", "10Ky.", "11Ky.", "12Ky."], ["1-Kysã", "2-Kysã", "3-Kysã", "4-Kysã", "5-Kysã", "6-Kysã", "7-Kysã", "8-Kysã", "9-Kysã", "10-Kysã", "11-Kysã", "12-Kysã"]], u, [["C.j.", "C.kk."], u, ["Cristo jo", "Cristo kar kỹ"]], 0, [6, 0], ["dd/MM/y", "d 'ne' MMM, y", "d 'ne' MMMM, y", "EEEE, d 'ne' MMMM, y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "BRL", "R$", "Mrasir Rejar", { "AUD": ["AU$", "$"], "BYN": [u, "p."], "FJD": ["FJC", "$"], "JPY": ["JP¥", "¥"], "PHP": [u, "₱"], "PTE": ["Vẽj."], "RON": [u, "L"], "SYP": [u, "S£"], "THB": ["฿"], "TWD": ["NT$"], "USD": ["US$", "$"], "XOF": ["CFA"], "ZMK": ["SMK"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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