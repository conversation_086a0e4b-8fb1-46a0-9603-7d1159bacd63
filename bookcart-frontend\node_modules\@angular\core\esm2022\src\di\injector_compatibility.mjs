/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import '../util/ng_dev_mode';
import { RuntimeError } from '../errors';
import { emitInjectEvent } from '../render3/debug/injector_profiler';
import { stringify } from '../util/stringify';
import { resolveForwardRef } from './forward_ref';
import { getInjectImplementation, injectRootLimpMode } from './inject_switch';
import { InjectFlags } from './interface/injector';
const _THROW_IF_NOT_FOUND = {};
export const THROW_IF_NOT_FOUND = _THROW_IF_NOT_FOUND;
/*
 * Name of a property (that we patch onto DI decorator), which is used as an annotation of which
 * InjectFlag this decorator represents. This allows to avoid direct references to the DI decorators
 * in the code, thus making them tree-shakable.
 */
const DI_DECORATOR_FLAG = '__NG_DI_FLAG__';
export const NG_TEMP_TOKEN_PATH = 'ngTempTokenPath';
const NG_TOKEN_PATH = 'ngTokenPath';
const NEW_LINE = /\n/gm;
const NO_NEW_LINE = 'ɵ';
export const SOURCE = '__source';
/**
 * Current injector value used by `inject`.
 * - `undefined`: it is an error to call `inject`
 * - `null`: `inject` can be called but there is no injector (limp-mode).
 * - Injector instance: Use the injector for resolution.
 */
let _currentInjector = undefined;
export function getCurrentInjector() {
    return _currentInjector;
}
export function setCurrentInjector(injector) {
    const former = _currentInjector;
    _currentInjector = injector;
    return former;
}
export function injectInjectorOnly(token, flags = InjectFlags.Default) {
    if (_currentInjector === undefined) {
        throw new RuntimeError(-203 /* RuntimeErrorCode.MISSING_INJECTION_CONTEXT */, ngDevMode &&
            `inject() must be called from an injection context such as a constructor, a factory function, a field initializer, or a function used with \`runInInjectionContext\`.`);
    }
    else if (_currentInjector === null) {
        return injectRootLimpMode(token, undefined, flags);
    }
    else {
        const value = _currentInjector.get(token, flags & InjectFlags.Optional ? null : undefined, flags);
        ngDevMode && emitInjectEvent(token, value, flags);
        return value;
    }
}
export function ɵɵinject(token, flags = InjectFlags.Default) {
    return (getInjectImplementation() || injectInjectorOnly)(resolveForwardRef(token), flags);
}
/**
 * Throws an error indicating that a factory function could not be generated by the compiler for a
 * particular class.
 *
 * The name of the class is not mentioned here, but will be in the generated factory function name
 * and thus in the stack trace.
 *
 * @codeGenApi
 */
export function ɵɵinvalidFactoryDep(index) {
    throw new RuntimeError(202 /* RuntimeErrorCode.INVALID_FACTORY_DEPENDENCY */, ngDevMode &&
        `This constructor is not compatible with Angular Dependency Injection because its dependency at index ${index} of the parameter list is invalid.
This can happen if the dependency type is a primitive like a string or if an ancestor of this class is missing an Angular decorator.

Please check that 1) the type for the parameter at index ${index} is correct and 2) the correct Angular decorators are defined for this class and its ancestors.`);
}
/**
 * Injects a token from the currently active injector.
 * `inject` is only supported in an [injection context](/guide/dependency-injection-context). It can
 * be used during:
 * - Construction (via the `constructor`) of a class being instantiated by the DI system, such
 * as an `@Injectable` or `@Component`.
 * - In the initializer for fields of such classes.
 * - In the factory function specified for `useFactory` of a `Provider` or an `@Injectable`.
 * - In the `factory` function specified for an `InjectionToken`.
 * - In a stackframe of a function call in a DI context
 *
 * @param token A token that represents a dependency that should be injected.
 * @param flags Optional flags that control how injection is executed.
 * The flags correspond to injection strategies that can be specified with
 * parameter decorators `@Host`, `@Self`, `@SkipSelf`, and `@Optional`.
 * @returns the injected value if operation is successful, `null` otherwise.
 * @throws if called outside of a supported context.
 *
 * @usageNotes
 * In practice the `inject()` calls are allowed in a constructor, a constructor parameter and a
 * field initializer:
 *
 * ```typescript
 * @Injectable({providedIn: 'root'})
 * export class Car {
 *   radio: Radio|undefined;
 *   // OK: field initializer
 *   spareTyre = inject(Tyre);
 *
 *   constructor() {
 *     // OK: constructor body
 *     this.radio = inject(Radio);
 *   }
 * }
 * ```
 *
 * It is also legal to call `inject` from a provider's factory:
 *
 * ```typescript
 * providers: [
 *   {provide: Car, useFactory: () => {
 *     // OK: a class factory
 *     const engine = inject(Engine);
 *     return new Car(engine);
 *   }}
 * ]
 * ```
 *
 * Calls to the `inject()` function outside of the class creation context will result in error. Most
 * notably, calls to `inject()` are disallowed after a class instance was created, in methods
 * (including lifecycle hooks):
 *
 * ```typescript
 * @Component({ ... })
 * export class CarComponent {
 *   ngOnInit() {
 *     // ERROR: too late, the component instance was already created
 *     const engine = inject(Engine);
 *     engine.start();
 *   }
 * }
 * ```
 *
 * @publicApi
 */
export function inject(token, flags = InjectFlags.Default) {
    // The `as any` here _shouldn't_ be necessary, but without it JSCompiler
    // throws a disambiguation  error due to the multiple signatures.
    return ɵɵinject(token, convertToBitFlags(flags));
}
// Converts object-based DI flags (`InjectOptions`) to bit flags (`InjectFlags`).
export function convertToBitFlags(flags) {
    if (typeof flags === 'undefined' || typeof flags === 'number') {
        return flags;
    }
    // While TypeScript doesn't accept it without a cast, bitwise OR with false-y values in
    // JavaScript is a no-op. We can use that for a very codesize-efficient conversion from
    // `InjectOptions` to `InjectFlags`.
    return (0 /* InternalInjectFlags.Default */ | // comment to force a line break in the formatter
        (flags.optional && 8 /* InternalInjectFlags.Optional */) |
        (flags.host && 1 /* InternalInjectFlags.Host */) |
        (flags.self && 2 /* InternalInjectFlags.Self */) |
        (flags.skipSelf && 4 /* InternalInjectFlags.SkipSelf */));
}
export function injectArgs(types) {
    const args = [];
    for (let i = 0; i < types.length; i++) {
        const arg = resolveForwardRef(types[i]);
        if (Array.isArray(arg)) {
            if (arg.length === 0) {
                throw new RuntimeError(900 /* RuntimeErrorCode.INVALID_DIFFER_INPUT */, ngDevMode && 'Arguments array must have arguments.');
            }
            let type = undefined;
            let flags = InjectFlags.Default;
            for (let j = 0; j < arg.length; j++) {
                const meta = arg[j];
                const flag = getInjectFlag(meta);
                if (typeof flag === 'number') {
                    // Special case when we handle @Inject decorator.
                    if (flag === -1 /* DecoratorFlags.Inject */) {
                        type = meta.token;
                    }
                    else {
                        flags |= flag;
                    }
                }
                else {
                    type = meta;
                }
            }
            args.push(ɵɵinject(type, flags));
        }
        else {
            args.push(ɵɵinject(arg));
        }
    }
    return args;
}
/**
 * Attaches a given InjectFlag to a given decorator using monkey-patching.
 * Since DI decorators can be used in providers `deps` array (when provider is configured using
 * `useFactory`) without initialization (e.g. `Host`) and as an instance (e.g. `new Host()`), we
 * attach the flag to make it available both as a static property and as a field on decorator
 * instance.
 *
 * @param decorator Provided DI decorator.
 * @param flag InjectFlag that should be applied.
 */
export function attachInjectFlag(decorator, flag) {
    decorator[DI_DECORATOR_FLAG] = flag;
    decorator.prototype[DI_DECORATOR_FLAG] = flag;
    return decorator;
}
/**
 * Reads monkey-patched property that contains InjectFlag attached to a decorator.
 *
 * @param token Token that may contain monkey-patched DI flags property.
 */
export function getInjectFlag(token) {
    return token[DI_DECORATOR_FLAG];
}
export function catchInjectorError(e, token, injectorErrorName, source) {
    const tokenPath = e[NG_TEMP_TOKEN_PATH];
    if (token[SOURCE]) {
        tokenPath.unshift(token[SOURCE]);
    }
    e.message = formatError('\n' + e.message, tokenPath, injectorErrorName, source);
    e[NG_TOKEN_PATH] = tokenPath;
    e[NG_TEMP_TOKEN_PATH] = null;
    throw e;
}
export function formatError(text, obj, injectorErrorName, source = null) {
    text = text && text.charAt(0) === '\n' && text.charAt(1) == NO_NEW_LINE ? text.slice(2) : text;
    let context = stringify(obj);
    if (Array.isArray(obj)) {
        context = obj.map(stringify).join(' -> ');
    }
    else if (typeof obj === 'object') {
        let parts = [];
        for (let key in obj) {
            if (obj.hasOwnProperty(key)) {
                let value = obj[key];
                parts.push(key + ':' + (typeof value === 'string' ? JSON.stringify(value) : stringify(value)));
            }
        }
        context = `{${parts.join(', ')}}`;
    }
    return `${injectorErrorName}${source ? '(' + source + ')' : ''}[${context}]: ${text.replace(NEW_LINE, '\n  ')}`;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5qZWN0b3JfY29tcGF0aWJpbGl0eS5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uLy4uL3BhY2thZ2VzL2NvcmUvc3JjL2RpL2luamVjdG9yX2NvbXBhdGliaWxpdHkudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7OztHQU1HO0FBRUgsT0FBTyxxQkFBcUIsQ0FBQztBQUU3QixPQUFPLEVBQUMsWUFBWSxFQUFtQixNQUFNLFdBQVcsQ0FBQztBQUV6RCxPQUFPLEVBQUMsZUFBZSxFQUFDLE1BQU0sb0NBQW9DLENBQUM7QUFDbkUsT0FBTyxFQUFDLFNBQVMsRUFBQyxNQUFNLG1CQUFtQixDQUFDO0FBRTVDLE9BQU8sRUFBQyxpQkFBaUIsRUFBQyxNQUFNLGVBQWUsQ0FBQztBQUNoRCxPQUFPLEVBQUMsdUJBQXVCLEVBQUUsa0JBQWtCLEVBQUMsTUFBTSxpQkFBaUIsQ0FBQztBQUU1RSxPQUFPLEVBQWlCLFdBQVcsRUFBcUMsTUFBTSxzQkFBc0IsQ0FBQztBQUtyRyxNQUFNLG1CQUFtQixHQUFHLEVBQUUsQ0FBQztBQUMvQixNQUFNLENBQUMsTUFBTSxrQkFBa0IsR0FBRyxtQkFBbUIsQ0FBQztBQUV0RDs7OztHQUlHO0FBQ0gsTUFBTSxpQkFBaUIsR0FBRyxnQkFBZ0IsQ0FBQztBQUUzQyxNQUFNLENBQUMsTUFBTSxrQkFBa0IsR0FBRyxpQkFBaUIsQ0FBQztBQUNwRCxNQUFNLGFBQWEsR0FBRyxhQUFhLENBQUM7QUFDcEMsTUFBTSxRQUFRLEdBQUcsTUFBTSxDQUFDO0FBQ3hCLE1BQU0sV0FBVyxHQUFHLEdBQUcsQ0FBQztBQUN4QixNQUFNLENBQUMsTUFBTSxNQUFNLEdBQUcsVUFBVSxDQUFDO0FBRWpDOzs7OztHQUtHO0FBQ0gsSUFBSSxnQkFBZ0IsR0FBNEIsU0FBUyxDQUFDO0FBRTFELE1BQU0sVUFBVSxrQkFBa0I7SUFDaEMsT0FBTyxnQkFBZ0IsQ0FBQztBQUMxQixDQUFDO0FBRUQsTUFBTSxVQUFVLGtCQUFrQixDQUFDLFFBQWlDO0lBQ2xFLE1BQU0sTUFBTSxHQUFHLGdCQUFnQixDQUFDO0lBQ2hDLGdCQUFnQixHQUFHLFFBQVEsQ0FBQztJQUM1QixPQUFPLE1BQU0sQ0FBQztBQUNoQixDQUFDO0FBSUQsTUFBTSxVQUFVLGtCQUFrQixDQUFJLEtBQXVCLEVBQUUsS0FBSyxHQUFHLFdBQVcsQ0FBQyxPQUFPO0lBRXhGLElBQUksZ0JBQWdCLEtBQUssU0FBUyxFQUFFLENBQUM7UUFDbkMsTUFBTSxJQUFJLFlBQVksd0RBRWxCLFNBQVM7WUFDTCxzS0FBc0ssQ0FBQyxDQUFDO0lBQ2xMLENBQUM7U0FBTSxJQUFJLGdCQUFnQixLQUFLLElBQUksRUFBRSxDQUFDO1FBQ3JDLE9BQU8sa0JBQWtCLENBQUMsS0FBSyxFQUFFLFNBQVMsRUFBRSxLQUFLLENBQUMsQ0FBQztJQUNyRCxDQUFDO1NBQU0sQ0FBQztRQUNOLE1BQU0sS0FBSyxHQUNQLGdCQUFnQixDQUFDLEdBQUcsQ0FBQyxLQUFLLEVBQUUsS0FBSyxHQUFHLFdBQVcsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsU0FBUyxFQUFFLEtBQUssQ0FBQyxDQUFDO1FBQ3hGLFNBQVMsSUFBSSxlQUFlLENBQUMsS0FBc0IsRUFBRSxLQUFLLEVBQUUsS0FBSyxDQUFDLENBQUM7UUFDbkUsT0FBTyxLQUFLLENBQUM7SUFDZixDQUFDO0FBQ0gsQ0FBQztBQWtCRCxNQUFNLFVBQVUsUUFBUSxDQUNwQixLQUEwQyxFQUFFLEtBQUssR0FBRyxXQUFXLENBQUMsT0FBTztJQUN6RSxPQUFPLENBQUMsdUJBQXVCLEVBQUUsSUFBSSxrQkFBa0IsQ0FBQyxDQUNwRCxpQkFBaUIsQ0FBQyxLQUFnQixDQUFDLEVBQUUsS0FBSyxDQUFDLENBQUM7QUFDbEQsQ0FBQztBQUVEOzs7Ozs7OztHQVFHO0FBQ0gsTUFBTSxVQUFVLG1CQUFtQixDQUFDLEtBQWE7SUFDL0MsTUFBTSxJQUFJLFlBQVksd0RBRWxCLFNBQVM7UUFDTCx3R0FDSSxLQUFLOzs7MkRBSUwsS0FBSyxpR0FBaUcsQ0FBQyxDQUFDO0FBQ3RILENBQUM7QUFxRUQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7R0FnRUc7QUFDSCxNQUFNLFVBQVUsTUFBTSxDQUNsQixLQUEwQyxFQUMxQyxRQUFtQyxXQUFXLENBQUMsT0FBTztJQUN4RCx3RUFBd0U7SUFDeEUsaUVBQWlFO0lBQ2pFLE9BQU8sUUFBUSxDQUFDLEtBQVksRUFBRSxpQkFBaUIsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDO0FBQzFELENBQUM7QUFFRCxpRkFBaUY7QUFDakYsTUFBTSxVQUFVLGlCQUFpQixDQUFDLEtBQTBDO0lBRTFFLElBQUksT0FBTyxLQUFLLEtBQUssV0FBVyxJQUFJLE9BQU8sS0FBSyxLQUFLLFFBQVEsRUFBRSxDQUFDO1FBQzlELE9BQU8sS0FBSyxDQUFDO0lBQ2YsQ0FBQztJQUVELHVGQUF1RjtJQUN2Rix1RkFBdUY7SUFDdkYsb0NBQW9DO0lBQ3BDLE9BQU8sQ0FBQyxzQ0FBK0IsaURBQWlEO1FBQy9FLENBQUMsS0FBSyxDQUFDLFFBQVEsd0NBQWdDLENBQVk7UUFDM0QsQ0FBQyxLQUFLLENBQUMsSUFBSSxvQ0FBNEIsQ0FBWTtRQUNuRCxDQUFDLEtBQUssQ0FBQyxJQUFJLG9DQUE0QixDQUFZO1FBQ25ELENBQUMsS0FBSyxDQUFDLFFBQVEsd0NBQWdDLENBQVksQ0FBZ0IsQ0FBQztBQUN2RixDQUFDO0FBRUQsTUFBTSxVQUFVLFVBQVUsQ0FBQyxLQUFtQztJQUM1RCxNQUFNLElBQUksR0FBVSxFQUFFLENBQUM7SUFDdkIsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLEtBQUssQ0FBQyxNQUFNLEVBQUUsQ0FBQyxFQUFFLEVBQUUsQ0FBQztRQUN0QyxNQUFNLEdBQUcsR0FBRyxpQkFBaUIsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUN4QyxJQUFJLEtBQUssQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQztZQUN2QixJQUFJLEdBQUcsQ0FBQyxNQUFNLEtBQUssQ0FBQyxFQUFFLENBQUM7Z0JBQ3JCLE1BQU0sSUFBSSxZQUFZLGtEQUVsQixTQUFTLElBQUksc0NBQXNDLENBQUMsQ0FBQztZQUMzRCxDQUFDO1lBQ0QsSUFBSSxJQUFJLEdBQXdCLFNBQVMsQ0FBQztZQUMxQyxJQUFJLEtBQUssR0FBZ0IsV0FBVyxDQUFDLE9BQU8sQ0FBQztZQUU3QyxLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsR0FBRyxDQUFDLE1BQU0sRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFDO2dCQUNwQyxNQUFNLElBQUksR0FBRyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUM7Z0JBQ3BCLE1BQU0sSUFBSSxHQUFHLGFBQWEsQ0FBQyxJQUFJLENBQUMsQ0FBQztnQkFDakMsSUFBSSxPQUFPLElBQUksS0FBSyxRQUFRLEVBQUUsQ0FBQztvQkFDN0IsaURBQWlEO29CQUNqRCxJQUFJLElBQUksbUNBQTBCLEVBQUUsQ0FBQzt3QkFDbkMsSUFBSSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUM7b0JBQ3BCLENBQUM7eUJBQU0sQ0FBQzt3QkFDTixLQUFLLElBQUksSUFBSSxDQUFDO29CQUNoQixDQUFDO2dCQUNILENBQUM7cUJBQU0sQ0FBQztvQkFDTixJQUFJLEdBQUcsSUFBSSxDQUFDO2dCQUNkLENBQUM7WUFDSCxDQUFDO1lBRUQsSUFBSSxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsSUFBSyxFQUFFLEtBQUssQ0FBQyxDQUFDLENBQUM7UUFDcEMsQ0FBQzthQUFNLENBQUM7WUFDTixJQUFJLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDO1FBQzNCLENBQUM7SUFDSCxDQUFDO0lBQ0QsT0FBTyxJQUFJLENBQUM7QUFDZCxDQUFDO0FBRUQ7Ozs7Ozs7OztHQVNHO0FBQ0gsTUFBTSxVQUFVLGdCQUFnQixDQUFDLFNBQWMsRUFBRSxJQUF3QztJQUN2RixTQUFTLENBQUMsaUJBQWlCLENBQUMsR0FBRyxJQUFJLENBQUM7SUFDcEMsU0FBUyxDQUFDLFNBQVMsQ0FBQyxpQkFBaUIsQ0FBQyxHQUFHLElBQUksQ0FBQztJQUM5QyxPQUFPLFNBQVMsQ0FBQztBQUNuQixDQUFDO0FBRUQ7Ozs7R0FJRztBQUNILE1BQU0sVUFBVSxhQUFhLENBQUMsS0FBVTtJQUN0QyxPQUFPLEtBQUssQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDO0FBQ2xDLENBQUM7QUFFRCxNQUFNLFVBQVUsa0JBQWtCLENBQzlCLENBQU0sRUFBRSxLQUFVLEVBQUUsaUJBQXlCLEVBQUUsTUFBbUI7SUFDcEUsTUFBTSxTQUFTLEdBQVUsQ0FBQyxDQUFDLGtCQUFrQixDQUFDLENBQUM7SUFDL0MsSUFBSSxLQUFLLENBQUMsTUFBTSxDQUFDLEVBQUUsQ0FBQztRQUNsQixTQUFTLENBQUMsT0FBTyxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDO0lBQ25DLENBQUM7SUFDRCxDQUFDLENBQUMsT0FBTyxHQUFHLFdBQVcsQ0FBQyxJQUFJLEdBQUcsQ0FBQyxDQUFDLE9BQU8sRUFBRSxTQUFTLEVBQUUsaUJBQWlCLEVBQUUsTUFBTSxDQUFDLENBQUM7SUFDaEYsQ0FBQyxDQUFDLGFBQWEsQ0FBQyxHQUFHLFNBQVMsQ0FBQztJQUM3QixDQUFDLENBQUMsa0JBQWtCLENBQUMsR0FBRyxJQUFJLENBQUM7SUFDN0IsTUFBTSxDQUFDLENBQUM7QUFDVixDQUFDO0FBRUQsTUFBTSxVQUFVLFdBQVcsQ0FDdkIsSUFBWSxFQUFFLEdBQVEsRUFBRSxpQkFBeUIsRUFBRSxTQUFzQixJQUFJO0lBQy9FLElBQUksR0FBRyxJQUFJLElBQUksSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsS0FBSyxJQUFJLElBQUksSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsSUFBSSxXQUFXLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQztJQUMvRixJQUFJLE9BQU8sR0FBRyxTQUFTLENBQUMsR0FBRyxDQUFDLENBQUM7SUFDN0IsSUFBSSxLQUFLLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUM7UUFDdkIsT0FBTyxHQUFHLEdBQUcsQ0FBQyxHQUFHLENBQUMsU0FBUyxDQUFDLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDO0lBQzVDLENBQUM7U0FBTSxJQUFJLE9BQU8sR0FBRyxLQUFLLFFBQVEsRUFBRSxDQUFDO1FBQ25DLElBQUksS0FBSyxHQUFhLEVBQUUsQ0FBQztRQUN6QixLQUFLLElBQUksR0FBRyxJQUFJLEdBQUcsRUFBRSxDQUFDO1lBQ3BCLElBQUksR0FBRyxDQUFDLGNBQWMsQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDO2dCQUM1QixJQUFJLEtBQUssR0FBRyxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUM7Z0JBQ3JCLEtBQUssQ0FBQyxJQUFJLENBQ04sR0FBRyxHQUFHLEdBQUcsR0FBRyxDQUFDLE9BQU8sS0FBSyxLQUFLLFFBQVEsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUMxRixDQUFDO1FBQ0gsQ0FBQztRQUNELE9BQU8sR0FBRyxJQUFJLEtBQUssQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQztJQUNwQyxDQUFDO0lBQ0QsT0FBTyxHQUFHLGlCQUFpQixHQUFHLE1BQU0sQ0FBQyxDQUFDLENBQUMsR0FBRyxHQUFHLE1BQU0sR0FBRyxHQUFHLENBQUMsQ0FBQyxDQUFDLEVBQUUsSUFBSSxPQUFPLE1BQ3JFLElBQUksQ0FBQyxPQUFPLENBQUMsUUFBUSxFQUFFLE1BQU0sQ0FBQyxFQUFFLENBQUM7QUFDdkMsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuXG5pbXBvcnQgJy4uL3V0aWwvbmdfZGV2X21vZGUnO1xuXG5pbXBvcnQge1J1bnRpbWVFcnJvciwgUnVudGltZUVycm9yQ29kZX0gZnJvbSAnLi4vZXJyb3JzJztcbmltcG9ydCB7VHlwZX0gZnJvbSAnLi4vaW50ZXJmYWNlL3R5cGUnO1xuaW1wb3J0IHtlbWl0SW5qZWN0RXZlbnR9IGZyb20gJy4uL3JlbmRlcjMvZGVidWcvaW5qZWN0b3JfcHJvZmlsZXInO1xuaW1wb3J0IHtzdHJpbmdpZnl9IGZyb20gJy4uL3V0aWwvc3RyaW5naWZ5JztcblxuaW1wb3J0IHtyZXNvbHZlRm9yd2FyZFJlZn0gZnJvbSAnLi9mb3J3YXJkX3JlZic7XG5pbXBvcnQge2dldEluamVjdEltcGxlbWVudGF0aW9uLCBpbmplY3RSb290TGltcE1vZGV9IGZyb20gJy4vaW5qZWN0X3N3aXRjaCc7XG5pbXBvcnQgdHlwZSB7SW5qZWN0b3J9IGZyb20gJy4vaW5qZWN0b3InO1xuaW1wb3J0IHtEZWNvcmF0b3JGbGFncywgSW5qZWN0RmxhZ3MsIEluamVjdE9wdGlvbnMsIEludGVybmFsSW5qZWN0RmxhZ3N9IGZyb20gJy4vaW50ZXJmYWNlL2luamVjdG9yJztcbmltcG9ydCB7UHJvdmlkZXJUb2tlbn0gZnJvbSAnLi9wcm92aWRlcl90b2tlbic7XG5pbXBvcnQgdHlwZSB7SG9zdEF0dHJpYnV0ZVRva2VufSBmcm9tICcuL2hvc3RfYXR0cmlidXRlX3Rva2VuJztcblxuXG5jb25zdCBfVEhST1dfSUZfTk9UX0ZPVU5EID0ge307XG5leHBvcnQgY29uc3QgVEhST1dfSUZfTk9UX0ZPVU5EID0gX1RIUk9XX0lGX05PVF9GT1VORDtcblxuLypcbiAqIE5hbWUgb2YgYSBwcm9wZXJ0eSAodGhhdCB3ZSBwYXRjaCBvbnRvIERJIGRlY29yYXRvciksIHdoaWNoIGlzIHVzZWQgYXMgYW4gYW5ub3RhdGlvbiBvZiB3aGljaFxuICogSW5qZWN0RmxhZyB0aGlzIGRlY29yYXRvciByZXByZXNlbnRzLiBUaGlzIGFsbG93cyB0byBhdm9pZCBkaXJlY3QgcmVmZXJlbmNlcyB0byB0aGUgREkgZGVjb3JhdG9yc1xuICogaW4gdGhlIGNvZGUsIHRodXMgbWFraW5nIHRoZW0gdHJlZS1zaGFrYWJsZS5cbiAqL1xuY29uc3QgRElfREVDT1JBVE9SX0ZMQUcgPSAnX19OR19ESV9GTEFHX18nO1xuXG5leHBvcnQgY29uc3QgTkdfVEVNUF9UT0tFTl9QQVRIID0gJ25nVGVtcFRva2VuUGF0aCc7XG5jb25zdCBOR19UT0tFTl9QQVRIID0gJ25nVG9rZW5QYXRoJztcbmNvbnN0IE5FV19MSU5FID0gL1xcbi9nbTtcbmNvbnN0IE5PX05FV19MSU5FID0gJ8m1JztcbmV4cG9ydCBjb25zdCBTT1VSQ0UgPSAnX19zb3VyY2UnO1xuXG4vKipcbiAqIEN1cnJlbnQgaW5qZWN0b3IgdmFsdWUgdXNlZCBieSBgaW5qZWN0YC5cbiAqIC0gYHVuZGVmaW5lZGA6IGl0IGlzIGFuIGVycm9yIHRvIGNhbGwgYGluamVjdGBcbiAqIC0gYG51bGxgOiBgaW5qZWN0YCBjYW4gYmUgY2FsbGVkIGJ1dCB0aGVyZSBpcyBubyBpbmplY3RvciAobGltcC1tb2RlKS5cbiAqIC0gSW5qZWN0b3IgaW5zdGFuY2U6IFVzZSB0aGUgaW5qZWN0b3IgZm9yIHJlc29sdXRpb24uXG4gKi9cbmxldCBfY3VycmVudEluamVjdG9yOiBJbmplY3Rvcnx1bmRlZmluZWR8bnVsbCA9IHVuZGVmaW5lZDtcblxuZXhwb3J0IGZ1bmN0aW9uIGdldEN1cnJlbnRJbmplY3RvcigpOiBJbmplY3Rvcnx1bmRlZmluZWR8bnVsbCB7XG4gIHJldHVybiBfY3VycmVudEluamVjdG9yO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gc2V0Q3VycmVudEluamVjdG9yKGluamVjdG9yOiBJbmplY3RvcnxudWxsfHVuZGVmaW5lZCk6IEluamVjdG9yfHVuZGVmaW5lZHxudWxsIHtcbiAgY29uc3QgZm9ybWVyID0gX2N1cnJlbnRJbmplY3RvcjtcbiAgX2N1cnJlbnRJbmplY3RvciA9IGluamVjdG9yO1xuICByZXR1cm4gZm9ybWVyO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gaW5qZWN0SW5qZWN0b3JPbmx5PFQ+KHRva2VuOiBQcm92aWRlclRva2VuPFQ+KTogVDtcbmV4cG9ydCBmdW5jdGlvbiBpbmplY3RJbmplY3Rvck9ubHk8VD4odG9rZW46IFByb3ZpZGVyVG9rZW48VD4sIGZsYWdzPzogSW5qZWN0RmxhZ3MpOiBUfG51bGw7XG5leHBvcnQgZnVuY3Rpb24gaW5qZWN0SW5qZWN0b3JPbmx5PFQ+KHRva2VuOiBQcm92aWRlclRva2VuPFQ+LCBmbGFncyA9IEluamVjdEZsYWdzLkRlZmF1bHQpOiBUfFxuICAgIG51bGwge1xuICBpZiAoX2N1cnJlbnRJbmplY3RvciA9PT0gdW5kZWZpbmVkKSB7XG4gICAgdGhyb3cgbmV3IFJ1bnRpbWVFcnJvcihcbiAgICAgICAgUnVudGltZUVycm9yQ29kZS5NSVNTSU5HX0lOSkVDVElPTl9DT05URVhULFxuICAgICAgICBuZ0Rldk1vZGUgJiZcbiAgICAgICAgICAgIGBpbmplY3QoKSBtdXN0IGJlIGNhbGxlZCBmcm9tIGFuIGluamVjdGlvbiBjb250ZXh0IHN1Y2ggYXMgYSBjb25zdHJ1Y3RvciwgYSBmYWN0b3J5IGZ1bmN0aW9uLCBhIGZpZWxkIGluaXRpYWxpemVyLCBvciBhIGZ1bmN0aW9uIHVzZWQgd2l0aCBcXGBydW5JbkluamVjdGlvbkNvbnRleHRcXGAuYCk7XG4gIH0gZWxzZSBpZiAoX2N1cnJlbnRJbmplY3RvciA9PT0gbnVsbCkge1xuICAgIHJldHVybiBpbmplY3RSb290TGltcE1vZGUodG9rZW4sIHVuZGVmaW5lZCwgZmxhZ3MpO1xuICB9IGVsc2Uge1xuICAgIGNvbnN0IHZhbHVlID1cbiAgICAgICAgX2N1cnJlbnRJbmplY3Rvci5nZXQodG9rZW4sIGZsYWdzICYgSW5qZWN0RmxhZ3MuT3B0aW9uYWwgPyBudWxsIDogdW5kZWZpbmVkLCBmbGFncyk7XG4gICAgbmdEZXZNb2RlICYmIGVtaXRJbmplY3RFdmVudCh0b2tlbiBhcyBUeXBlPHVua25vd24+LCB2YWx1ZSwgZmxhZ3MpO1xuICAgIHJldHVybiB2YWx1ZTtcbiAgfVxufVxuXG4vKipcbiAqIEdlbmVyYXRlZCBpbnN0cnVjdGlvbjogaW5qZWN0cyBhIHRva2VuIGZyb20gdGhlIGN1cnJlbnRseSBhY3RpdmUgaW5qZWN0b3IuXG4gKlxuICogKEFkZGl0aW9uYWwgZG9jdW1lbnRhdGlvbiBtb3ZlZCB0byBgaW5qZWN0YCwgYXMgaXQgaXMgdGhlIHB1YmxpYyBBUEksIGFuZCBhbiBhbGlhcyBmb3IgdGhpc1xuICogaW5zdHJ1Y3Rpb24pXG4gKlxuICogQHNlZSBpbmplY3RcbiAqIEBjb2RlR2VuQXBpXG4gKiBAcHVibGljQXBpIFRoaXMgaW5zdHJ1Y3Rpb24gaGFzIGJlZW4gZW1pdHRlZCBieSBWaWV3RW5naW5lIGZvciBzb21lIHRpbWUgYW5kIGlzIGRlcGxveWVkIHRvIG5wbS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIMm1ybVpbmplY3Q8VD4odG9rZW46IFByb3ZpZGVyVG9rZW48VD4pOiBUO1xuZXhwb3J0IGZ1bmN0aW9uIMm1ybVpbmplY3Q8VD4odG9rZW46IFByb3ZpZGVyVG9rZW48VD4sIGZsYWdzPzogSW5qZWN0RmxhZ3MpOiBUfG51bGw7XG5leHBvcnQgZnVuY3Rpb24gybXJtWluamVjdCh0b2tlbjogSG9zdEF0dHJpYnV0ZVRva2VuKTogc3RyaW5nO1xuZXhwb3J0IGZ1bmN0aW9uIMm1ybVpbmplY3QodG9rZW46IEhvc3RBdHRyaWJ1dGVUb2tlbiwgZmxhZ3M/OiBJbmplY3RGbGFncyk6IHN0cmluZ3xudWxsO1xuZXhwb3J0IGZ1bmN0aW9uIMm1ybVpbmplY3Q8VD4oXG4gICAgdG9rZW46IFByb3ZpZGVyVG9rZW48VD58SG9zdEF0dHJpYnV0ZVRva2VuLCBmbGFncz86IEluamVjdEZsYWdzKTogc3RyaW5nfG51bGw7XG5leHBvcnQgZnVuY3Rpb24gybXJtWluamVjdDxUPihcbiAgICB0b2tlbjogUHJvdmlkZXJUb2tlbjxUPnxIb3N0QXR0cmlidXRlVG9rZW4sIGZsYWdzID0gSW5qZWN0RmxhZ3MuRGVmYXVsdCk6IFR8bnVsbCB7XG4gIHJldHVybiAoZ2V0SW5qZWN0SW1wbGVtZW50YXRpb24oKSB8fCBpbmplY3RJbmplY3Rvck9ubHkpKFxuICAgICAgcmVzb2x2ZUZvcndhcmRSZWYodG9rZW4gYXMgVHlwZTxUPiksIGZsYWdzKTtcbn1cblxuLyoqXG4gKiBUaHJvd3MgYW4gZXJyb3IgaW5kaWNhdGluZyB0aGF0IGEgZmFjdG9yeSBmdW5jdGlvbiBjb3VsZCBub3QgYmUgZ2VuZXJhdGVkIGJ5IHRoZSBjb21waWxlciBmb3IgYVxuICogcGFydGljdWxhciBjbGFzcy5cbiAqXG4gKiBUaGUgbmFtZSBvZiB0aGUgY2xhc3MgaXMgbm90IG1lbnRpb25lZCBoZXJlLCBidXQgd2lsbCBiZSBpbiB0aGUgZ2VuZXJhdGVkIGZhY3RvcnkgZnVuY3Rpb24gbmFtZVxuICogYW5kIHRodXMgaW4gdGhlIHN0YWNrIHRyYWNlLlxuICpcbiAqIEBjb2RlR2VuQXBpXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiDJtcm1aW52YWxpZEZhY3RvcnlEZXAoaW5kZXg6IG51bWJlcik6IG5ldmVyIHtcbiAgdGhyb3cgbmV3IFJ1bnRpbWVFcnJvcihcbiAgICAgIFJ1bnRpbWVFcnJvckNvZGUuSU5WQUxJRF9GQUNUT1JZX0RFUEVOREVOQ1ksXG4gICAgICBuZ0Rldk1vZGUgJiZcbiAgICAgICAgICBgVGhpcyBjb25zdHJ1Y3RvciBpcyBub3QgY29tcGF0aWJsZSB3aXRoIEFuZ3VsYXIgRGVwZW5kZW5jeSBJbmplY3Rpb24gYmVjYXVzZSBpdHMgZGVwZW5kZW5jeSBhdCBpbmRleCAke1xuICAgICAgICAgICAgICBpbmRleH0gb2YgdGhlIHBhcmFtZXRlciBsaXN0IGlzIGludmFsaWQuXG5UaGlzIGNhbiBoYXBwZW4gaWYgdGhlIGRlcGVuZGVuY3kgdHlwZSBpcyBhIHByaW1pdGl2ZSBsaWtlIGEgc3RyaW5nIG9yIGlmIGFuIGFuY2VzdG9yIG9mIHRoaXMgY2xhc3MgaXMgbWlzc2luZyBhbiBBbmd1bGFyIGRlY29yYXRvci5cblxuUGxlYXNlIGNoZWNrIHRoYXQgMSkgdGhlIHR5cGUgZm9yIHRoZSBwYXJhbWV0ZXIgYXQgaW5kZXggJHtcbiAgICAgICAgICAgICAgaW5kZXh9IGlzIGNvcnJlY3QgYW5kIDIpIHRoZSBjb3JyZWN0IEFuZ3VsYXIgZGVjb3JhdG9ycyBhcmUgZGVmaW5lZCBmb3IgdGhpcyBjbGFzcyBhbmQgaXRzIGFuY2VzdG9ycy5gKTtcbn1cblxuLyoqXG4gKiBAcGFyYW0gdG9rZW4gQSB0b2tlbiB0aGF0IHJlcHJlc2VudHMgYSBkZXBlbmRlbmN5IHRoYXQgc2hvdWxkIGJlIGluamVjdGVkLlxuICogQHJldHVybnMgdGhlIGluamVjdGVkIHZhbHVlIGlmIG9wZXJhdGlvbiBpcyBzdWNjZXNzZnVsLCBgbnVsbGAgb3RoZXJ3aXNlLlxuICogQHRocm93cyBpZiBjYWxsZWQgb3V0c2lkZSBvZiBhIHN1cHBvcnRlZCBjb250ZXh0LlxuICpcbiAqIEBwdWJsaWNBcGlcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGluamVjdDxUPih0b2tlbjogUHJvdmlkZXJUb2tlbjxUPik6IFQ7XG4vKipcbiAqIEBwYXJhbSB0b2tlbiBBIHRva2VuIHRoYXQgcmVwcmVzZW50cyBhIGRlcGVuZGVuY3kgdGhhdCBzaG91bGQgYmUgaW5qZWN0ZWQuXG4gKiBAcGFyYW0gZmxhZ3MgQ29udHJvbCBob3cgaW5qZWN0aW9uIGlzIGV4ZWN1dGVkLiBUaGUgZmxhZ3MgY29ycmVzcG9uZCB0byBpbmplY3Rpb24gc3RyYXRlZ2llcyB0aGF0XG4gKiAgICAgY2FuIGJlIHNwZWNpZmllZCB3aXRoIHBhcmFtZXRlciBkZWNvcmF0b3JzIGBASG9zdGAsIGBAU2VsZmAsIGBAU2tpcFNlbGZgLCBhbmQgYEBPcHRpb25hbGAuXG4gKiBAcmV0dXJucyB0aGUgaW5qZWN0ZWQgdmFsdWUgaWYgb3BlcmF0aW9uIGlzIHN1Y2Nlc3NmdWwsIGBudWxsYCBvdGhlcndpc2UuXG4gKiBAdGhyb3dzIGlmIGNhbGxlZCBvdXRzaWRlIG9mIGEgc3VwcG9ydGVkIGNvbnRleHQuXG4gKlxuICogQHB1YmxpY0FwaVxuICogQGRlcHJlY2F0ZWQgcHJlZmVyIGFuIG9wdGlvbnMgb2JqZWN0IGluc3RlYWQgb2YgYEluamVjdEZsYWdzYFxuICovXG5leHBvcnQgZnVuY3Rpb24gaW5qZWN0PFQ+KHRva2VuOiBQcm92aWRlclRva2VuPFQ+LCBmbGFncz86IEluamVjdEZsYWdzKTogVHxudWxsO1xuLyoqXG4gKiBAcGFyYW0gdG9rZW4gQSB0b2tlbiB0aGF0IHJlcHJlc2VudHMgYSBkZXBlbmRlbmN5IHRoYXQgc2hvdWxkIGJlIGluamVjdGVkLlxuICogQHBhcmFtIG9wdGlvbnMgQ29udHJvbCBob3cgaW5qZWN0aW9uIGlzIGV4ZWN1dGVkLiBPcHRpb25zIGNvcnJlc3BvbmQgdG8gaW5qZWN0aW9uIHN0cmF0ZWdpZXNcbiAqICAgICB0aGF0IGNhbiBiZSBzcGVjaWZpZWQgd2l0aCBwYXJhbWV0ZXIgZGVjb3JhdG9ycyBgQEhvc3RgLCBgQFNlbGZgLCBgQFNraXBTZWxmYCwgYW5kXG4gKiAgICAgYEBPcHRpb25hbGAuXG4gKiBAcmV0dXJucyB0aGUgaW5qZWN0ZWQgdmFsdWUgaWYgb3BlcmF0aW9uIGlzIHN1Y2Nlc3NmdWwuXG4gKiBAdGhyb3dzIGlmIGNhbGxlZCBvdXRzaWRlIG9mIGEgc3VwcG9ydGVkIGNvbnRleHQsIG9yIGlmIHRoZSB0b2tlbiBpcyBub3QgZm91bmQuXG4gKlxuICogQHB1YmxpY0FwaVxuICovXG5leHBvcnQgZnVuY3Rpb24gaW5qZWN0PFQ+KHRva2VuOiBQcm92aWRlclRva2VuPFQ+LCBvcHRpb25zOiBJbmplY3RPcHRpb25zJntvcHRpb25hbD86IGZhbHNlfSk6IFQ7XG4vKipcbiAqIEBwYXJhbSB0b2tlbiBBIHRva2VuIHRoYXQgcmVwcmVzZW50cyBhIGRlcGVuZGVuY3kgdGhhdCBzaG91bGQgYmUgaW5qZWN0ZWQuXG4gKiBAcGFyYW0gb3B0aW9ucyBDb250cm9sIGhvdyBpbmplY3Rpb24gaXMgZXhlY3V0ZWQuIE9wdGlvbnMgY29ycmVzcG9uZCB0byBpbmplY3Rpb24gc3RyYXRlZ2llc1xuICogICAgIHRoYXQgY2FuIGJlIHNwZWNpZmllZCB3aXRoIHBhcmFtZXRlciBkZWNvcmF0b3JzIGBASG9zdGAsIGBAU2VsZmAsIGBAU2tpcFNlbGZgLCBhbmRcbiAqICAgICBgQE9wdGlvbmFsYC5cbiAqIEByZXR1cm5zIHRoZSBpbmplY3RlZCB2YWx1ZSBpZiBvcGVyYXRpb24gaXMgc3VjY2Vzc2Z1bCwgIGBudWxsYCBpZiB0aGUgdG9rZW4gaXMgbm90XG4gKiAgICAgZm91bmQgYW5kIG9wdGlvbmFsIGluamVjdGlvbiBoYXMgYmVlbiByZXF1ZXN0ZWQuXG4gKiBAdGhyb3dzIGlmIGNhbGxlZCBvdXRzaWRlIG9mIGEgc3VwcG9ydGVkIGNvbnRleHQsIG9yIGlmIHRoZSB0b2tlbiBpcyBub3QgZm91bmQgYW5kIG9wdGlvbmFsXG4gKiAgICAgaW5qZWN0aW9uIHdhcyBub3QgcmVxdWVzdGVkLlxuICpcbiAqIEBwdWJsaWNBcGlcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGluamVjdDxUPih0b2tlbjogUHJvdmlkZXJUb2tlbjxUPiwgb3B0aW9uczogSW5qZWN0T3B0aW9ucyk6IFR8bnVsbDtcbi8qKlxuICogQHBhcmFtIHRva2VuIEEgdG9rZW4gdGhhdCByZXByZXNlbnRzIGEgc3RhdGljIGF0dHJpYnV0ZSBvbiB0aGUgaG9zdCBub2RlIHRoYXQgc2hvdWxkIGJlIGluamVjdGVkLlxuICogQHJldHVybnMgVmFsdWUgb2YgdGhlIGF0dHJpYnV0ZSBpZiBpdCBleGlzdHMuXG4gKiBAdGhyb3dzIElmIGNhbGxlZCBvdXRzaWRlIG9mIGEgc3VwcG9ydGVkIGNvbnRleHQgb3IgdGhlIGF0dHJpYnV0ZSBkb2VzIG5vdCBleGlzdC5cbiAqXG4gKiBAcHVibGljQXBpXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpbmplY3QodG9rZW46IEhvc3RBdHRyaWJ1dGVUb2tlbik6IHN0cmluZztcbi8qKlxuICogQHBhcmFtIHRva2VuIEEgdG9rZW4gdGhhdCByZXByZXNlbnRzIGEgc3RhdGljIGF0dHJpYnV0ZSBvbiB0aGUgaG9zdCBub2RlIHRoYXQgc2hvdWxkIGJlIGluamVjdGVkLlxuICogQHJldHVybnMgVmFsdWUgb2YgdGhlIGF0dHJpYnV0ZSBpZiBpdCBleGlzdHMsIG90aGVyd2lzZSBgbnVsbGAuXG4gKiBAdGhyb3dzIElmIGNhbGxlZCBvdXRzaWRlIG9mIGEgc3VwcG9ydGVkIGNvbnRleHQuXG4gKlxuICogQHB1YmxpY0FwaVxuICovXG5leHBvcnQgZnVuY3Rpb24gaW5qZWN0KHRva2VuOiBIb3N0QXR0cmlidXRlVG9rZW4sIG9wdGlvbnM6IHtvcHRpb25hbDogdHJ1ZX0pOiBzdHJpbmd8bnVsbDtcbi8qKlxuICogQHBhcmFtIHRva2VuIEEgdG9rZW4gdGhhdCByZXByZXNlbnRzIGEgc3RhdGljIGF0dHJpYnV0ZSBvbiB0aGUgaG9zdCBub2RlIHRoYXQgc2hvdWxkIGJlIGluamVjdGVkLlxuICogQHJldHVybnMgVmFsdWUgb2YgdGhlIGF0dHJpYnV0ZSBpZiBpdCBleGlzdHMuXG4gKiBAdGhyb3dzIElmIGNhbGxlZCBvdXRzaWRlIG9mIGEgc3VwcG9ydGVkIGNvbnRleHQgb3IgdGhlIGF0dHJpYnV0ZSBkb2VzIG5vdCBleGlzdC5cbiAqXG4gKiBAcHVibGljQXBpXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpbmplY3QodG9rZW46IEhvc3RBdHRyaWJ1dGVUb2tlbiwgb3B0aW9uczoge29wdGlvbmFsOiBmYWxzZX0pOiBzdHJpbmc7XG4vKipcbiAqIEluamVjdHMgYSB0b2tlbiBmcm9tIHRoZSBjdXJyZW50bHkgYWN0aXZlIGluamVjdG9yLlxuICogYGluamVjdGAgaXMgb25seSBzdXBwb3J0ZWQgaW4gYW4gW2luamVjdGlvbiBjb250ZXh0XSgvZ3VpZGUvZGVwZW5kZW5jeS1pbmplY3Rpb24tY29udGV4dCkuIEl0IGNhblxuICogYmUgdXNlZCBkdXJpbmc6XG4gKiAtIENvbnN0cnVjdGlvbiAodmlhIHRoZSBgY29uc3RydWN0b3JgKSBvZiBhIGNsYXNzIGJlaW5nIGluc3RhbnRpYXRlZCBieSB0aGUgREkgc3lzdGVtLCBzdWNoXG4gKiBhcyBhbiBgQEluamVjdGFibGVgIG9yIGBAQ29tcG9uZW50YC5cbiAqIC0gSW4gdGhlIGluaXRpYWxpemVyIGZvciBmaWVsZHMgb2Ygc3VjaCBjbGFzc2VzLlxuICogLSBJbiB0aGUgZmFjdG9yeSBmdW5jdGlvbiBzcGVjaWZpZWQgZm9yIGB1c2VGYWN0b3J5YCBvZiBhIGBQcm92aWRlcmAgb3IgYW4gYEBJbmplY3RhYmxlYC5cbiAqIC0gSW4gdGhlIGBmYWN0b3J5YCBmdW5jdGlvbiBzcGVjaWZpZWQgZm9yIGFuIGBJbmplY3Rpb25Ub2tlbmAuXG4gKiAtIEluIGEgc3RhY2tmcmFtZSBvZiBhIGZ1bmN0aW9uIGNhbGwgaW4gYSBESSBjb250ZXh0XG4gKlxuICogQHBhcmFtIHRva2VuIEEgdG9rZW4gdGhhdCByZXByZXNlbnRzIGEgZGVwZW5kZW5jeSB0aGF0IHNob3VsZCBiZSBpbmplY3RlZC5cbiAqIEBwYXJhbSBmbGFncyBPcHRpb25hbCBmbGFncyB0aGF0IGNvbnRyb2wgaG93IGluamVjdGlvbiBpcyBleGVjdXRlZC5cbiAqIFRoZSBmbGFncyBjb3JyZXNwb25kIHRvIGluamVjdGlvbiBzdHJhdGVnaWVzIHRoYXQgY2FuIGJlIHNwZWNpZmllZCB3aXRoXG4gKiBwYXJhbWV0ZXIgZGVjb3JhdG9ycyBgQEhvc3RgLCBgQFNlbGZgLCBgQFNraXBTZWxmYCwgYW5kIGBAT3B0aW9uYWxgLlxuICogQHJldHVybnMgdGhlIGluamVjdGVkIHZhbHVlIGlmIG9wZXJhdGlvbiBpcyBzdWNjZXNzZnVsLCBgbnVsbGAgb3RoZXJ3aXNlLlxuICogQHRocm93cyBpZiBjYWxsZWQgb3V0c2lkZSBvZiBhIHN1cHBvcnRlZCBjb250ZXh0LlxuICpcbiAqIEB1c2FnZU5vdGVzXG4gKiBJbiBwcmFjdGljZSB0aGUgYGluamVjdCgpYCBjYWxscyBhcmUgYWxsb3dlZCBpbiBhIGNvbnN0cnVjdG9yLCBhIGNvbnN0cnVjdG9yIHBhcmFtZXRlciBhbmQgYVxuICogZmllbGQgaW5pdGlhbGl6ZXI6XG4gKlxuICogYGBgdHlwZXNjcmlwdFxuICogQEluamVjdGFibGUoe3Byb3ZpZGVkSW46ICdyb290J30pXG4gKiBleHBvcnQgY2xhc3MgQ2FyIHtcbiAqICAgcmFkaW86IFJhZGlvfHVuZGVmaW5lZDtcbiAqICAgLy8gT0s6IGZpZWxkIGluaXRpYWxpemVyXG4gKiAgIHNwYXJlVHlyZSA9IGluamVjdChUeXJlKTtcbiAqXG4gKiAgIGNvbnN0cnVjdG9yKCkge1xuICogICAgIC8vIE9LOiBjb25zdHJ1Y3RvciBib2R5XG4gKiAgICAgdGhpcy5yYWRpbyA9IGluamVjdChSYWRpbyk7XG4gKiAgIH1cbiAqIH1cbiAqIGBgYFxuICpcbiAqIEl0IGlzIGFsc28gbGVnYWwgdG8gY2FsbCBgaW5qZWN0YCBmcm9tIGEgcHJvdmlkZXIncyBmYWN0b3J5OlxuICpcbiAqIGBgYHR5cGVzY3JpcHRcbiAqIHByb3ZpZGVyczogW1xuICogICB7cHJvdmlkZTogQ2FyLCB1c2VGYWN0b3J5OiAoKSA9PiB7XG4gKiAgICAgLy8gT0s6IGEgY2xhc3MgZmFjdG9yeVxuICogICAgIGNvbnN0IGVuZ2luZSA9IGluamVjdChFbmdpbmUpO1xuICogICAgIHJldHVybiBuZXcgQ2FyKGVuZ2luZSk7XG4gKiAgIH19XG4gKiBdXG4gKiBgYGBcbiAqXG4gKiBDYWxscyB0byB0aGUgYGluamVjdCgpYCBmdW5jdGlvbiBvdXRzaWRlIG9mIHRoZSBjbGFzcyBjcmVhdGlvbiBjb250ZXh0IHdpbGwgcmVzdWx0IGluIGVycm9yLiBNb3N0XG4gKiBub3RhYmx5LCBjYWxscyB0byBgaW5qZWN0KClgIGFyZSBkaXNhbGxvd2VkIGFmdGVyIGEgY2xhc3MgaW5zdGFuY2Ugd2FzIGNyZWF0ZWQsIGluIG1ldGhvZHNcbiAqIChpbmNsdWRpbmcgbGlmZWN5Y2xlIGhvb2tzKTpcbiAqXG4gKiBgYGB0eXBlc2NyaXB0XG4gKiBAQ29tcG9uZW50KHsgLi4uIH0pXG4gKiBleHBvcnQgY2xhc3MgQ2FyQ29tcG9uZW50IHtcbiAqICAgbmdPbkluaXQoKSB7XG4gKiAgICAgLy8gRVJST1I6IHRvbyBsYXRlLCB0aGUgY29tcG9uZW50IGluc3RhbmNlIHdhcyBhbHJlYWR5IGNyZWF0ZWRcbiAqICAgICBjb25zdCBlbmdpbmUgPSBpbmplY3QoRW5naW5lKTtcbiAqICAgICBlbmdpbmUuc3RhcnQoKTtcbiAqICAgfVxuICogfVxuICogYGBgXG4gKlxuICogQHB1YmxpY0FwaVxuICovXG5leHBvcnQgZnVuY3Rpb24gaW5qZWN0PFQ+KFxuICAgIHRva2VuOiBQcm92aWRlclRva2VuPFQ+fEhvc3RBdHRyaWJ1dGVUb2tlbixcbiAgICBmbGFnczogSW5qZWN0RmxhZ3N8SW5qZWN0T3B0aW9ucyA9IEluamVjdEZsYWdzLkRlZmF1bHQpIHtcbiAgLy8gVGhlIGBhcyBhbnlgIGhlcmUgX3Nob3VsZG4ndF8gYmUgbmVjZXNzYXJ5LCBidXQgd2l0aG91dCBpdCBKU0NvbXBpbGVyXG4gIC8vIHRocm93cyBhIGRpc2FtYmlndWF0aW9uICBlcnJvciBkdWUgdG8gdGhlIG11bHRpcGxlIHNpZ25hdHVyZXMuXG4gIHJldHVybiDJtcm1aW5qZWN0KHRva2VuIGFzIGFueSwgY29udmVydFRvQml0RmxhZ3MoZmxhZ3MpKTtcbn1cblxuLy8gQ29udmVydHMgb2JqZWN0LWJhc2VkIERJIGZsYWdzIChgSW5qZWN0T3B0aW9uc2ApIHRvIGJpdCBmbGFncyAoYEluamVjdEZsYWdzYCkuXG5leHBvcnQgZnVuY3Rpb24gY29udmVydFRvQml0RmxhZ3MoZmxhZ3M6IEluamVjdE9wdGlvbnN8SW5qZWN0RmxhZ3N8dW5kZWZpbmVkKTogSW5qZWN0RmxhZ3N8XG4gICAgdW5kZWZpbmVkIHtcbiAgaWYgKHR5cGVvZiBmbGFncyA9PT0gJ3VuZGVmaW5lZCcgfHwgdHlwZW9mIGZsYWdzID09PSAnbnVtYmVyJykge1xuICAgIHJldHVybiBmbGFncztcbiAgfVxuXG4gIC8vIFdoaWxlIFR5cGVTY3JpcHQgZG9lc24ndCBhY2NlcHQgaXQgd2l0aG91dCBhIGNhc3QsIGJpdHdpc2UgT1Igd2l0aCBmYWxzZS15IHZhbHVlcyBpblxuICAvLyBKYXZhU2NyaXB0IGlzIGEgbm8tb3AuIFdlIGNhbiB1c2UgdGhhdCBmb3IgYSB2ZXJ5IGNvZGVzaXplLWVmZmljaWVudCBjb252ZXJzaW9uIGZyb21cbiAgLy8gYEluamVjdE9wdGlvbnNgIHRvIGBJbmplY3RGbGFnc2AuXG4gIHJldHVybiAoSW50ZXJuYWxJbmplY3RGbGFncy5EZWZhdWx0IHwgIC8vIGNvbW1lbnQgdG8gZm9yY2UgYSBsaW5lIGJyZWFrIGluIHRoZSBmb3JtYXR0ZXJcbiAgICAgICAgICAoKGZsYWdzLm9wdGlvbmFsICYmIEludGVybmFsSW5qZWN0RmxhZ3MuT3B0aW9uYWwpIGFzIG51bWJlcikgfFxuICAgICAgICAgICgoZmxhZ3MuaG9zdCAmJiBJbnRlcm5hbEluamVjdEZsYWdzLkhvc3QpIGFzIG51bWJlcikgfFxuICAgICAgICAgICgoZmxhZ3Muc2VsZiAmJiBJbnRlcm5hbEluamVjdEZsYWdzLlNlbGYpIGFzIG51bWJlcikgfFxuICAgICAgICAgICgoZmxhZ3Muc2tpcFNlbGYgJiYgSW50ZXJuYWxJbmplY3RGbGFncy5Ta2lwU2VsZikgYXMgbnVtYmVyKSkgYXMgSW5qZWN0RmxhZ3M7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBpbmplY3RBcmdzKHR5cGVzOiAoUHJvdmlkZXJUb2tlbjxhbnk+fGFueVtdKVtdKTogYW55W10ge1xuICBjb25zdCBhcmdzOiBhbnlbXSA9IFtdO1xuICBmb3IgKGxldCBpID0gMDsgaSA8IHR5cGVzLmxlbmd0aDsgaSsrKSB7XG4gICAgY29uc3QgYXJnID0gcmVzb2x2ZUZvcndhcmRSZWYodHlwZXNbaV0pO1xuICAgIGlmIChBcnJheS5pc0FycmF5KGFyZykpIHtcbiAgICAgIGlmIChhcmcubGVuZ3RoID09PSAwKSB7XG4gICAgICAgIHRocm93IG5ldyBSdW50aW1lRXJyb3IoXG4gICAgICAgICAgICBSdW50aW1lRXJyb3JDb2RlLklOVkFMSURfRElGRkVSX0lOUFVULFxuICAgICAgICAgICAgbmdEZXZNb2RlICYmICdBcmd1bWVudHMgYXJyYXkgbXVzdCBoYXZlIGFyZ3VtZW50cy4nKTtcbiAgICAgIH1cbiAgICAgIGxldCB0eXBlOiBUeXBlPGFueT58dW5kZWZpbmVkID0gdW5kZWZpbmVkO1xuICAgICAgbGV0IGZsYWdzOiBJbmplY3RGbGFncyA9IEluamVjdEZsYWdzLkRlZmF1bHQ7XG5cbiAgICAgIGZvciAobGV0IGogPSAwOyBqIDwgYXJnLmxlbmd0aDsgaisrKSB7XG4gICAgICAgIGNvbnN0IG1ldGEgPSBhcmdbal07XG4gICAgICAgIGNvbnN0IGZsYWcgPSBnZXRJbmplY3RGbGFnKG1ldGEpO1xuICAgICAgICBpZiAodHlwZW9mIGZsYWcgPT09ICdudW1iZXInKSB7XG4gICAgICAgICAgLy8gU3BlY2lhbCBjYXNlIHdoZW4gd2UgaGFuZGxlIEBJbmplY3QgZGVjb3JhdG9yLlxuICAgICAgICAgIGlmIChmbGFnID09PSBEZWNvcmF0b3JGbGFncy5JbmplY3QpIHtcbiAgICAgICAgICAgIHR5cGUgPSBtZXRhLnRva2VuO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBmbGFncyB8PSBmbGFnO1xuICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICB0eXBlID0gbWV0YTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBhcmdzLnB1c2goybXJtWluamVjdCh0eXBlISwgZmxhZ3MpKTtcbiAgICB9IGVsc2Uge1xuICAgICAgYXJncy5wdXNoKMm1ybVpbmplY3QoYXJnKSk7XG4gICAgfVxuICB9XG4gIHJldHVybiBhcmdzO1xufVxuXG4vKipcbiAqIEF0dGFjaGVzIGEgZ2l2ZW4gSW5qZWN0RmxhZyB0byBhIGdpdmVuIGRlY29yYXRvciB1c2luZyBtb25rZXktcGF0Y2hpbmcuXG4gKiBTaW5jZSBESSBkZWNvcmF0b3JzIGNhbiBiZSB1c2VkIGluIHByb3ZpZGVycyBgZGVwc2AgYXJyYXkgKHdoZW4gcHJvdmlkZXIgaXMgY29uZmlndXJlZCB1c2luZ1xuICogYHVzZUZhY3RvcnlgKSB3aXRob3V0IGluaXRpYWxpemF0aW9uIChlLmcuIGBIb3N0YCkgYW5kIGFzIGFuIGluc3RhbmNlIChlLmcuIGBuZXcgSG9zdCgpYCksIHdlXG4gKiBhdHRhY2ggdGhlIGZsYWcgdG8gbWFrZSBpdCBhdmFpbGFibGUgYm90aCBhcyBhIHN0YXRpYyBwcm9wZXJ0eSBhbmQgYXMgYSBmaWVsZCBvbiBkZWNvcmF0b3JcbiAqIGluc3RhbmNlLlxuICpcbiAqIEBwYXJhbSBkZWNvcmF0b3IgUHJvdmlkZWQgREkgZGVjb3JhdG9yLlxuICogQHBhcmFtIGZsYWcgSW5qZWN0RmxhZyB0aGF0IHNob3VsZCBiZSBhcHBsaWVkLlxuICovXG5leHBvcnQgZnVuY3Rpb24gYXR0YWNoSW5qZWN0RmxhZyhkZWNvcmF0b3I6IGFueSwgZmxhZzogSW50ZXJuYWxJbmplY3RGbGFnc3xEZWNvcmF0b3JGbGFncyk6IGFueSB7XG4gIGRlY29yYXRvcltESV9ERUNPUkFUT1JfRkxBR10gPSBmbGFnO1xuICBkZWNvcmF0b3IucHJvdG90eXBlW0RJX0RFQ09SQVRPUl9GTEFHXSA9IGZsYWc7XG4gIHJldHVybiBkZWNvcmF0b3I7XG59XG5cbi8qKlxuICogUmVhZHMgbW9ua2V5LXBhdGNoZWQgcHJvcGVydHkgdGhhdCBjb250YWlucyBJbmplY3RGbGFnIGF0dGFjaGVkIHRvIGEgZGVjb3JhdG9yLlxuICpcbiAqIEBwYXJhbSB0b2tlbiBUb2tlbiB0aGF0IG1heSBjb250YWluIG1vbmtleS1wYXRjaGVkIERJIGZsYWdzIHByb3BlcnR5LlxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0SW5qZWN0RmxhZyh0b2tlbjogYW55KTogbnVtYmVyfHVuZGVmaW5lZCB7XG4gIHJldHVybiB0b2tlbltESV9ERUNPUkFUT1JfRkxBR107XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBjYXRjaEluamVjdG9yRXJyb3IoXG4gICAgZTogYW55LCB0b2tlbjogYW55LCBpbmplY3RvckVycm9yTmFtZTogc3RyaW5nLCBzb3VyY2U6IHN0cmluZ3xudWxsKTogbmV2ZXIge1xuICBjb25zdCB0b2tlblBhdGg6IGFueVtdID0gZVtOR19URU1QX1RPS0VOX1BBVEhdO1xuICBpZiAodG9rZW5bU09VUkNFXSkge1xuICAgIHRva2VuUGF0aC51bnNoaWZ0KHRva2VuW1NPVVJDRV0pO1xuICB9XG4gIGUubWVzc2FnZSA9IGZvcm1hdEVycm9yKCdcXG4nICsgZS5tZXNzYWdlLCB0b2tlblBhdGgsIGluamVjdG9yRXJyb3JOYW1lLCBzb3VyY2UpO1xuICBlW05HX1RPS0VOX1BBVEhdID0gdG9rZW5QYXRoO1xuICBlW05HX1RFTVBfVE9LRU5fUEFUSF0gPSBudWxsO1xuICB0aHJvdyBlO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0RXJyb3IoXG4gICAgdGV4dDogc3RyaW5nLCBvYmo6IGFueSwgaW5qZWN0b3JFcnJvck5hbWU6IHN0cmluZywgc291cmNlOiBzdHJpbmd8bnVsbCA9IG51bGwpOiBzdHJpbmcge1xuICB0ZXh0ID0gdGV4dCAmJiB0ZXh0LmNoYXJBdCgwKSA9PT0gJ1xcbicgJiYgdGV4dC5jaGFyQXQoMSkgPT0gTk9fTkVXX0xJTkUgPyB0ZXh0LnNsaWNlKDIpIDogdGV4dDtcbiAgbGV0IGNvbnRleHQgPSBzdHJpbmdpZnkob2JqKTtcbiAgaWYgKEFycmF5LmlzQXJyYXkob2JqKSkge1xuICAgIGNvbnRleHQgPSBvYmoubWFwKHN0cmluZ2lmeSkuam9pbignIC0+ICcpO1xuICB9IGVsc2UgaWYgKHR5cGVvZiBvYmogPT09ICdvYmplY3QnKSB7XG4gICAgbGV0IHBhcnRzID0gPHN0cmluZ1tdPltdO1xuICAgIGZvciAobGV0IGtleSBpbiBvYmopIHtcbiAgICAgIGlmIChvYmouaGFzT3duUHJvcGVydHkoa2V5KSkge1xuICAgICAgICBsZXQgdmFsdWUgPSBvYmpba2V5XTtcbiAgICAgICAgcGFydHMucHVzaChcbiAgICAgICAgICAgIGtleSArICc6JyArICh0eXBlb2YgdmFsdWUgPT09ICdzdHJpbmcnID8gSlNPTi5zdHJpbmdpZnkodmFsdWUpIDogc3RyaW5naWZ5KHZhbHVlKSkpO1xuICAgICAgfVxuICAgIH1cbiAgICBjb250ZXh0ID0gYHske3BhcnRzLmpvaW4oJywgJyl9fWA7XG4gIH1cbiAgcmV0dXJuIGAke2luamVjdG9yRXJyb3JOYW1lfSR7c291cmNlID8gJygnICsgc291cmNlICsgJyknIDogJyd9WyR7Y29udGV4dH1dOiAke1xuICAgICAgdGV4dC5yZXBsYWNlKE5FV19MSU5FLCAnXFxuICAnKX1gO1xufVxuIl19