/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { stringify } from '../util/stringify';
/**
 * Map of module-id to the corresponding NgModule.
 */
const modules = new Map();
/**
 * Whether to check for duplicate NgModule registrations.
 *
 * This can be disabled for testing.
 */
let checkForDuplicateNgModules = true;
function assertSameOrNotExisting(id, type, incoming) {
    if (type && type !== incoming && checkForDuplicateNgModules) {
        throw new Error(`Duplicate module registered for ${id} - ${stringify(type)} vs ${stringify(type.name)}`);
    }
}
/**
 * Adds the given NgModule type to Angular's NgModule registry.
 *
 * This is generated as a side-effect of NgModule compilation. Note that the `id` is passed in
 * explicitly and not read from the NgModule definition. This is for two reasons: it avoids a
 * megamorphic read, and in JIT there's a chicken-and-egg problem where the NgModule may not be
 * fully resolved when it's registered.
 *
 * @codeGenApi
 */
export function registerNgModuleType(ngModuleType, id) {
    const existing = modules.get(id) || null;
    assertSameOrNotExisting(id, existing, ngModuleType);
    modules.set(id, ngModuleType);
}
export function clearModulesForTest() {
    modules.clear();
}
export function getRegisteredNgModuleType(id) {
    return modules.get(id);
}
/**
 * Control whether the NgModule registration system enforces that each NgModule type registered has
 * a unique id.
 *
 * This is useful for testing as the NgModule registry cannot be properly reset between tests with
 * Angular's current API.
 */
export function setAllowDuplicateNgModuleIdsForTest(allowDuplicates) {
    checkForDuplicateNgModules = !allowDuplicates;
}
//# sourceMappingURL=data:application/json;base64,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