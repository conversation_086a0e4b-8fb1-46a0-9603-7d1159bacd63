{"version": 3, "file": "checkbox.mjs", "sources": ["../../../../../../src/material/checkbox/checkbox-config.ts", "../../../../../../src/material/checkbox/checkbox.ts", "../../../../../../src/material/checkbox/checkbox.html", "../../../../../../src/material/checkbox/checkbox-required-validator.ts", "../../../../../../src/material/checkbox/module.ts", "../../../../../../src/material/checkbox/checkbox_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {InjectionToken} from '@angular/core';\nimport {ThemePalette} from '@angular/material/core';\n\n/** Default `mat-checkbox` options that can be overridden. */\nexport interface MatCheckboxDefaultOptions {\n  /** Default theme color palette to be used for checkboxes. */\n  color?: ThemePalette;\n  /** Default checkbox click action for checkboxes. */\n  clickAction?: MatCheckboxClickAction;\n}\n\n/** Injection token to be used to override the default options for `mat-checkbox`. */\nexport const MAT_CHECKBOX_DEFAULT_OPTIONS = new InjectionToken<MatCheckboxDefaultOptions>(\n  'mat-checkbox-default-options',\n  {\n    providedIn: 'root',\n    factory: MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY,\n  },\n);\n\n/** @docs-private */\nexport function MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY(): MatCheckboxDefaultOptions {\n  return {\n    color: 'accent',\n    clickAction: 'check-indeterminate',\n  };\n}\n\n/**\n * Checkbox click action when user click on input element.\n * noop: Do not toggle checked or indeterminate.\n * check: Only toggle checked status, ignore indeterminate.\n * check-indeterminate: Toggle checked status, set indeterminate to false. Default behavior.\n * undefined: Same as `check-indeterminate`.\n */\nexport type MatCheckboxClickAction = 'noop' | 'check' | 'check-indeterminate' | undefined;\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  AfterViewInit,\n  Attribute,\n  booleanAttribute,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  EventEmitter,\n  forwardRef,\n  Inject,\n  Input,\n  NgZone,\n  numberAttribute,\n  OnChanges,\n  Optional,\n  Output,\n  SimpleChanges,\n  ViewChild,\n  ViewEncapsulation,\n  ANIMATION_MODULE_TYPE,\n} from '@angular/core';\nimport {\n  AbstractControl,\n  ControlValueAccessor,\n  NG_VALIDATORS,\n  NG_VALUE_ACCESSOR,\n  ValidationErrors,\n  Validator,\n} from '@angular/forms';\nimport {_MatInternalFormField, MatRipple} from '@angular/material/core';\nimport {FocusableOption} from '@angular/cdk/a11y';\nimport {\n  MAT_CHECKBOX_DEFAULT_OPTIONS,\n  MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY,\n  MatCheckboxDefaultOptions,\n} from './checkbox-config';\n\n/**\n * Represents the different states that require custom transitions between them.\n * @docs-private\n */\nexport enum TransitionCheckState {\n  /** The initial state of the component before any user interaction. */\n  Init,\n  /** The state representing the component when it's becoming checked. */\n  Checked,\n  /** The state representing the component when it's becoming unchecked. */\n  Unchecked,\n  /** The state representing the component when it's becoming indeterminate. */\n  Indeterminate,\n}\n\n/**\n * @deprecated Will stop being exported.\n * @breaking-change 19.0.0\n */\nexport const MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR: any = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatCheckbox),\n  multi: true,\n};\n\n/** Change event object emitted by checkbox. */\nexport class MatCheckboxChange {\n  /** The source checkbox of the event. */\n  source: MatCheckbox;\n  /** The new `checked` value of the checkbox. */\n  checked: boolean;\n}\n\n// Increasing integer for generating unique ids for checkbox components.\nlet nextUniqueId = 0;\n\n// Default checkbox configuration.\nconst defaults = MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY();\n\n@Component({\n  selector: 'mat-checkbox',\n  templateUrl: 'checkbox.html',\n  styleUrl: 'checkbox.css',\n  host: {\n    'class': 'mat-mdc-checkbox',\n    '[attr.tabindex]': 'null',\n    '[attr.aria-label]': 'null',\n    '[attr.aria-labelledby]': 'null',\n    '[class._mat-animation-noopable]': `_animationMode === 'NoopAnimations'`,\n    '[class.mdc-checkbox--disabled]': 'disabled',\n    '[id]': 'id',\n    // Add classes that users can use to more easily target disabled or checked checkboxes.\n    '[class.mat-mdc-checkbox-disabled]': 'disabled',\n    '[class.mat-mdc-checkbox-checked]': 'checked',\n    '[class]': 'color ? \"mat-\" + color : \"mat-accent\"',\n  },\n  providers: [\n    MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR,\n    {\n      provide: NG_VALIDATORS,\n      useExisting: MatCheckbox,\n      multi: true,\n    },\n  ],\n  exportAs: 'matCheckbox',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: true,\n  imports: [MatRipple, _MatInternalFormField],\n})\nexport class MatCheckbox\n  implements AfterViewInit, OnChanges, ControlValueAccessor, Validator, FocusableOption\n{\n  /** Focuses the checkbox. */\n  focus() {\n    this._inputElement.nativeElement.focus();\n  }\n\n  /** Creates the change event that will be emitted by the checkbox. */\n  protected _createChangeEvent(isChecked: boolean) {\n    const event = new MatCheckboxChange();\n    event.source = this;\n    event.checked = isChecked;\n    return event;\n  }\n\n  /** Gets the element on which to add the animation CSS classes. */\n  protected _getAnimationTargetElement() {\n    return this._inputElement?.nativeElement;\n  }\n\n  /** CSS classes to add when transitioning between the different checkbox states. */\n  protected _animationClasses = {\n    uncheckedToChecked: 'mdc-checkbox--anim-unchecked-checked',\n    uncheckedToIndeterminate: 'mdc-checkbox--anim-unchecked-indeterminate',\n    checkedToUnchecked: 'mdc-checkbox--anim-checked-unchecked',\n    checkedToIndeterminate: 'mdc-checkbox--anim-checked-indeterminate',\n    indeterminateToChecked: 'mdc-checkbox--anim-indeterminate-checked',\n    indeterminateToUnchecked: 'mdc-checkbox--anim-indeterminate-unchecked',\n  };\n\n  /**\n   * Attached to the aria-label attribute of the host element. In most cases, aria-labelledby will\n   * take precedence so this may be omitted.\n   */\n  @Input('aria-label') ariaLabel: string = '';\n\n  /**\n   * Users can specify the `aria-labelledby` attribute which will be forwarded to the input element\n   */\n  @Input('aria-labelledby') ariaLabelledby: string | null = null;\n\n  /** The 'aria-describedby' attribute is read after the element's label and field type. */\n  @Input('aria-describedby') ariaDescribedby: string;\n\n  private _uniqueId: string;\n\n  /** A unique id for the checkbox input. If none is supplied, it will be auto-generated. */\n  @Input() id: string;\n\n  /** Returns the unique id for the visual hidden input. */\n  get inputId(): string {\n    return `${this.id || this._uniqueId}-input`;\n  }\n\n  /** Whether the checkbox is required. */\n  @Input({transform: booleanAttribute}) required: boolean;\n\n  /** Whether the label should appear after or before the checkbox. Defaults to 'after' */\n  @Input() labelPosition: 'before' | 'after' = 'after';\n\n  /** Name value will be applied to the input element if present */\n  @Input() name: string | null = null;\n\n  /** Event emitted when the checkbox's `checked` value changes. */\n  @Output() readonly change = new EventEmitter<MatCheckboxChange>();\n\n  /** Event emitted when the checkbox's `indeterminate` value changes. */\n  @Output() readonly indeterminateChange: EventEmitter<boolean> = new EventEmitter<boolean>();\n\n  /** The value attribute of the native input element */\n  @Input() value: string;\n\n  /** Whether the checkbox has a ripple. */\n  @Input({transform: booleanAttribute}) disableRipple: boolean;\n\n  /** The native `<input type=\"checkbox\">` element */\n  @ViewChild('input') _inputElement: ElementRef<HTMLInputElement>;\n\n  /** The native `<label>` element */\n  @ViewChild('label') _labelElement: ElementRef<HTMLInputElement>;\n\n  /** Tabindex for the checkbox. */\n  @Input({transform: (value: unknown) => (value == null ? undefined : numberAttribute(value))})\n  tabIndex: number;\n\n  // TODO(crisbeto): this should be a ThemePalette, but some internal apps were abusing\n  // the lack of type checking previously and assigning random strings.\n  /** Palette color of the checkbox. */\n  @Input() color: string | undefined;\n\n  /**\n   * Reference to the MatRipple instance of the checkbox.\n   * @deprecated Considered an implementation detail. To be removed.\n   * @breaking-change 17.0.0\n   */\n  @ViewChild(MatRipple) ripple: MatRipple;\n\n  /**\n   * Called when the checkbox is blurred. Needed to properly implement ControlValueAccessor.\n   * @docs-private\n   */\n  _onTouched: () => any = () => {};\n\n  private _currentAnimationClass: string = '';\n  private _currentCheckState: TransitionCheckState = TransitionCheckState.Init;\n  private _controlValueAccessorChangeFn: (value: any) => void = () => {};\n  private _validatorChangeFn = () => {};\n\n  constructor(\n    public _elementRef: ElementRef<HTMLElement>,\n    private _changeDetectorRef: ChangeDetectorRef,\n    private _ngZone: NgZone,\n    @Attribute('tabindex') tabIndex: string,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) public _animationMode?: string,\n    @Optional() @Inject(MAT_CHECKBOX_DEFAULT_OPTIONS) private _options?: MatCheckboxDefaultOptions,\n  ) {\n    this._options = this._options || defaults;\n    this.color = this._options.color || defaults.color;\n    this.tabIndex = parseInt(tabIndex) || 0;\n    this.id = this._uniqueId = `mat-mdc-checkbox-${++nextUniqueId}`;\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    if (changes['required']) {\n      this._validatorChangeFn();\n    }\n  }\n\n  ngAfterViewInit() {\n    this._syncIndeterminate(this._indeterminate);\n  }\n\n  /** Whether the checkbox is checked. */\n  @Input({transform: booleanAttribute})\n  get checked(): boolean {\n    return this._checked;\n  }\n  set checked(value: boolean) {\n    if (value != this.checked) {\n      this._checked = value;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  private _checked: boolean = false;\n\n  /** Whether the checkbox is disabled. */\n  @Input({transform: booleanAttribute})\n  get disabled(): boolean {\n    return this._disabled;\n  }\n  set disabled(value: boolean) {\n    if (value !== this.disabled) {\n      this._disabled = value;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  private _disabled: boolean = false;\n\n  /**\n   * Whether the checkbox is indeterminate. This is also known as \"mixed\" mode and can be used to\n   * represent a checkbox with three states, e.g. a checkbox that represents a nested list of\n   * checkable items. Note that whenever checkbox is manually clicked, indeterminate is immediately\n   * set to false.\n   */\n  @Input({transform: booleanAttribute})\n  get indeterminate(): boolean {\n    return this._indeterminate;\n  }\n  set indeterminate(value: boolean) {\n    const changed = value != this._indeterminate;\n    this._indeterminate = value;\n\n    if (changed) {\n      if (this._indeterminate) {\n        this._transitionCheckState(TransitionCheckState.Indeterminate);\n      } else {\n        this._transitionCheckState(\n          this.checked ? TransitionCheckState.Checked : TransitionCheckState.Unchecked,\n        );\n      }\n      this.indeterminateChange.emit(this._indeterminate);\n    }\n\n    this._syncIndeterminate(this._indeterminate);\n  }\n  private _indeterminate: boolean = false;\n\n  _isRippleDisabled() {\n    return this.disableRipple || this.disabled;\n  }\n\n  /** Method being called whenever the label text changes. */\n  _onLabelTextChange() {\n    // Since the event of the `cdkObserveContent` directive runs outside of the zone, the checkbox\n    // component will be only marked for check, but no actual change detection runs automatically.\n    // Instead of going back into the zone in order to trigger a change detection which causes\n    // *all* components to be checked (if explicitly marked or not using OnPush), we only trigger\n    // an explicit change detection for the checkbox view and its children.\n    this._changeDetectorRef.detectChanges();\n  }\n\n  // Implemented as part of ControlValueAccessor.\n  writeValue(value: any) {\n    this.checked = !!value;\n  }\n\n  // Implemented as part of ControlValueAccessor.\n  registerOnChange(fn: (value: any) => void) {\n    this._controlValueAccessorChangeFn = fn;\n  }\n\n  // Implemented as part of ControlValueAccessor.\n  registerOnTouched(fn: any) {\n    this._onTouched = fn;\n  }\n\n  // Implemented as part of ControlValueAccessor.\n  setDisabledState(isDisabled: boolean) {\n    this.disabled = isDisabled;\n  }\n\n  // Implemented as a part of Validator.\n  validate(control: AbstractControl<boolean>): ValidationErrors | null {\n    return this.required && control.value !== true ? {'required': true} : null;\n  }\n\n  // Implemented as a part of Validator.\n  registerOnValidatorChange(fn: () => void): void {\n    this._validatorChangeFn = fn;\n  }\n\n  private _transitionCheckState(newState: TransitionCheckState) {\n    let oldState = this._currentCheckState;\n    let element = this._getAnimationTargetElement();\n\n    if (oldState === newState || !element) {\n      return;\n    }\n    if (this._currentAnimationClass) {\n      element.classList.remove(this._currentAnimationClass);\n    }\n\n    this._currentAnimationClass = this._getAnimationClassForCheckStateTransition(\n      oldState,\n      newState,\n    );\n    this._currentCheckState = newState;\n\n    if (this._currentAnimationClass.length > 0) {\n      element.classList.add(this._currentAnimationClass);\n\n      // Remove the animation class to avoid animation when the checkbox is moved between containers\n      const animationClass = this._currentAnimationClass;\n\n      this._ngZone.runOutsideAngular(() => {\n        setTimeout(() => {\n          element!.classList.remove(animationClass);\n        }, 1000);\n      });\n    }\n  }\n\n  private _emitChangeEvent() {\n    this._controlValueAccessorChangeFn(this.checked);\n    this.change.emit(this._createChangeEvent(this.checked));\n\n    // Assigning the value again here is redundant, but we have to do it in case it was\n    // changed inside the `change` listener which will cause the input to be out of sync.\n    if (this._inputElement) {\n      this._inputElement.nativeElement.checked = this.checked;\n    }\n  }\n\n  /** Toggles the `checked` state of the checkbox. */\n  toggle(): void {\n    this.checked = !this.checked;\n    this._controlValueAccessorChangeFn(this.checked);\n  }\n\n  protected _handleInputClick() {\n    const clickAction = this._options?.clickAction;\n\n    // If resetIndeterminate is false, and the current state is indeterminate, do nothing on click\n    if (!this.disabled && clickAction !== 'noop') {\n      // When user manually click on the checkbox, `indeterminate` is set to false.\n      if (this.indeterminate && clickAction !== 'check') {\n        Promise.resolve().then(() => {\n          this._indeterminate = false;\n          this.indeterminateChange.emit(this._indeterminate);\n        });\n      }\n\n      this._checked = !this._checked;\n      this._transitionCheckState(\n        this._checked ? TransitionCheckState.Checked : TransitionCheckState.Unchecked,\n      );\n\n      // Emit our custom change event if the native input emitted one.\n      // It is important to only emit it, if the native input triggered one, because\n      // we don't want to trigger a change event, when the `checked` variable changes for example.\n      this._emitChangeEvent();\n    } else if (!this.disabled && clickAction === 'noop') {\n      // Reset native input when clicked with noop. The native checkbox becomes checked after\n      // click, reset it to be align with `checked` value of `mat-checkbox`.\n      this._inputElement.nativeElement.checked = this.checked;\n      this._inputElement.nativeElement.indeterminate = this.indeterminate;\n    }\n  }\n\n  _onInteractionEvent(event: Event) {\n    // We always have to stop propagation on the change event.\n    // Otherwise the change event, from the input element, will bubble up and\n    // emit its event object to the `change` output.\n    event.stopPropagation();\n  }\n\n  _onBlur() {\n    // When a focused element becomes disabled, the browser *immediately* fires a blur event.\n    // Angular does not expect events to be raised during change detection, so any state change\n    // (such as a form control's 'ng-touched') will cause a changed-after-checked error.\n    // See https://github.com/angular/angular/issues/17793. To work around this, we defer\n    // telling the form control it has been touched until the next tick.\n    Promise.resolve().then(() => {\n      this._onTouched();\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n\n  private _getAnimationClassForCheckStateTransition(\n    oldState: TransitionCheckState,\n    newState: TransitionCheckState,\n  ): string {\n    // Don't transition if animations are disabled.\n    if (this._animationMode === 'NoopAnimations') {\n      return '';\n    }\n\n    switch (oldState) {\n      case TransitionCheckState.Init:\n        // Handle edge case where user interacts with checkbox that does not have [(ngModel)] or\n        // [checked] bound to it.\n        if (newState === TransitionCheckState.Checked) {\n          return this._animationClasses.uncheckedToChecked;\n        } else if (newState == TransitionCheckState.Indeterminate) {\n          return this._checked\n            ? this._animationClasses.checkedToIndeterminate\n            : this._animationClasses.uncheckedToIndeterminate;\n        }\n        break;\n      case TransitionCheckState.Unchecked:\n        return newState === TransitionCheckState.Checked\n          ? this._animationClasses.uncheckedToChecked\n          : this._animationClasses.uncheckedToIndeterminate;\n      case TransitionCheckState.Checked:\n        return newState === TransitionCheckState.Unchecked\n          ? this._animationClasses.checkedToUnchecked\n          : this._animationClasses.checkedToIndeterminate;\n      case TransitionCheckState.Indeterminate:\n        return newState === TransitionCheckState.Checked\n          ? this._animationClasses.indeterminateToChecked\n          : this._animationClasses.indeterminateToUnchecked;\n    }\n\n    return '';\n  }\n\n  /**\n   * Syncs the indeterminate value with the checkbox DOM node.\n   *\n   * We sync `indeterminate` directly on the DOM node, because in Ivy the check for whether a\n   * property is supported on an element boils down to `if (propName in element)`. Domino's\n   * HTMLInputElement doesn't have an `indeterminate` property so Ivy will warn during\n   * server-side rendering.\n   */\n  private _syncIndeterminate(value: boolean) {\n    const nativeCheckbox = this._inputElement;\n\n    if (nativeCheckbox) {\n      nativeCheckbox.nativeElement.indeterminate = value;\n    }\n  }\n\n  _onInputClick() {\n    this._handleInputClick();\n  }\n\n  _onTouchTargetClick() {\n    this._handleInputClick();\n\n    if (!this.disabled) {\n      // Normally the input should be focused already, but if the click\n      // comes from the touch target, then we might have to focus it ourselves.\n      this._inputElement.nativeElement.focus();\n    }\n  }\n\n  /**\n   *  Prevent click events that come from the `<label/>` element from bubbling. This prevents the\n   *  click handler on the host from triggering twice when clicking on the `<label/>` element. After\n   *  the click event on the `<label/>` propagates, the browsers dispatches click on the associated\n   *  `<input/>`. By preventing clicks on the label by bubbling, we ensure only one click event\n   *  bubbles when the label is clicked.\n   */\n  _preventBubblingFromLabel(event: MouseEvent) {\n    if (!!event.target && this._labelElement.nativeElement.contains(event.target as HTMLElement)) {\n      event.stopPropagation();\n    }\n  }\n}\n", "<div mat-internal-form-field [labelPosition]=\"labelPosition\" (click)=\"_preventBubblingFromLabel($event)\">\n  <div #checkbox class=\"mdc-checkbox\">\n    <!-- Render this element first so the input is on top. -->\n    <div class=\"mat-mdc-checkbox-touch-target\" (click)=\"_onTouchTargetClick()\"></div>\n    <input #input\n           type=\"checkbox\"\n           class=\"mdc-checkbox__native-control\"\n           [class.mdc-checkbox--selected]=\"checked\"\n           [attr.aria-label]=\"ariaLabel || null\"\n           [attr.aria-labelledby]=\"ariaLabelledby\"\n           [attr.aria-describedby]=\"ariaDescribedby\"\n           [attr.aria-checked]=\"indeterminate ? 'mixed' : null\"\n           [attr.name]=\"name\"\n           [attr.value]=\"value\"\n           [checked]=\"checked\"\n           [indeterminate]=\"indeterminate\"\n           [disabled]=\"disabled\"\n           [id]=\"inputId\"\n           [required]=\"required\"\n           [tabIndex]=\"disabled ? -1 : tabIndex\"\n           (blur)=\"_onBlur()\"\n           (click)=\"_onInputClick()\"\n           (change)=\"_onInteractionEvent($event)\"/>\n    <div class=\"mdc-checkbox__ripple\"></div>\n    <div class=\"mdc-checkbox__background\">\n      <svg class=\"mdc-checkbox__checkmark\"\n           focusable=\"false\"\n           viewBox=\"0 0 24 24\"\n           aria-hidden=\"true\">\n        <path class=\"mdc-checkbox__checkmark-path\"\n              fill=\"none\"\n              d=\"M1.73,12.91 8.1,19.28 22.79,4.59\"/>\n      </svg>\n      <div class=\"mdc-checkbox__mixedmark\"></div>\n    </div>\n    <div class=\"mat-mdc-checkbox-ripple mat-mdc-focus-indicator\" mat-ripple\n      [matRippleTrigger]=\"checkbox\"\n      [matRippleDisabled]=\"disableRipple || disabled\"\n      [matRippleCentered]=\"true\"></div>\n  </div>\n  <!--\n    Avoid putting a click handler on the <label/> to fix duplicate navigation stop on Talk Back\n    (#14385). Putting a click handler on the <label/> caused this bug because the browser produced\n    an unnecessary accessibility tree node.\n  -->\n  <label class=\"mdc-label\"\n         #label\n         [for]=\"inputId\">\n    <ng-content></ng-content>\n  </label>\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, forwardRef, Provider} from '@angular/core';\nimport {CheckboxRequiredValidator, NG_VALIDATORS} from '@angular/forms';\n\n/**\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nexport const MAT_CHECKBOX_REQUIRED_VALIDATOR: Provider = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => MatCheckboxRequiredValidator),\n  multi: true,\n};\n\n/**\n * Validator for Material checkbox's required attribute in template-driven checkbox.\n * Current CheckboxRequiredValidator only work with `input type=checkbox` and does not\n * work with `mat-checkbox`.\n *\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\n@Directive({\n  selector: `mat-checkbox[required][formControlName],\n             mat-checkbox[required][formControl], mat-checkbox[required][ngModel]`,\n  providers: [MAT_CHECKBOX_REQUIRED_VALIDATOR],\n  standalone: true,\n})\nexport class MatCheckboxRequiredValidator extends CheckboxRequiredValidator {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '@angular/material/core';\nimport {MatCheckbox} from './checkbox';\nimport {MatCheckboxRequiredValidator} from './checkbox-required-validator';\n\n/**\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\n@NgModule({\n  imports: [MatCheckboxRequiredValidator],\n  exports: [MatCheckboxRequiredValidator],\n})\nexport class _MatCheckboxRequiredValidatorModule {}\n\n@NgModule({\n  imports: [MatCheckbox, MatCommonModule],\n  exports: [MatCheckbox, MatCommonModule],\n})\nexport class MatCheckboxModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;AAkBA;MACa,4BAA4B,GAAG,IAAI,cAAc,CAC5D,8BAA8B,EAC9B;AACE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,oCAAoC;AAC9C,CAAA,EACD;AAEF;SACgB,oCAAoC,GAAA;IAClD,OAAO;AACL,QAAA,KAAK,EAAE,QAAQ;AACf,QAAA,WAAW,EAAE,qBAAqB;KACnC,CAAC;AACJ;;ACaA;;;AAGG;IACS,qBASX;AATD,CAAA,UAAY,oBAAoB,EAAA;;AAE9B,IAAA,oBAAA,CAAA,oBAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;;AAEJ,IAAA,oBAAA,CAAA,oBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO,CAAA;;AAEP,IAAA,oBAAA,CAAA,oBAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAS,CAAA;;AAET,IAAA,oBAAA,CAAA,oBAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,GAAA,eAAa,CAAA;AACf,CAAC,EATW,oBAAoB,KAApB,oBAAoB,GAS/B,EAAA,CAAA,CAAA,CAAA;AAED;;;AAGG;AACU,MAAA,mCAAmC,GAAQ;AACtD,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,WAAW,CAAC;AAC1C,IAAA,KAAK,EAAE,IAAI;EACX;AAEF;MACa,iBAAiB,CAAA;AAK7B,CAAA;AAED;AACA,IAAI,YAAY,GAAG,CAAC,CAAC;AAErB;AACA,MAAM,QAAQ,GAAG,oCAAoC,EAAE,CAAC;MAiC3C,WAAW,CAAA;;IAItB,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;KAC1C;;AAGS,IAAA,kBAAkB,CAAC,SAAkB,EAAA;AAC7C,QAAA,MAAM,KAAK,GAAG,IAAI,iBAAiB,EAAE,CAAC;AACtC,QAAA,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;AACpB,QAAA,KAAK,CAAC,OAAO,GAAG,SAAS,CAAC;AAC1B,QAAA,OAAO,KAAK,CAAC;KACd;;IAGS,0BAA0B,GAAA;AAClC,QAAA,OAAO,IAAI,CAAC,aAAa,EAAE,aAAa,CAAC;KAC1C;;AAgCD,IAAA,IAAI,OAAO,GAAA;QACT,OAAO,CAAA,EAAG,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,SAAS,CAAA,MAAA,CAAQ,CAAC;KAC7C;IAwDD,WACS,CAAA,WAAoC,EACnC,kBAAqC,EACrC,OAAe,EACA,QAAgB,EACW,cAAuB,EACf,QAAoC,EAAA;QALvF,IAAW,CAAA,WAAA,GAAX,WAAW,CAAyB;QACnC,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAmB;QACrC,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QAE2B,IAAc,CAAA,cAAA,GAAd,cAAc,CAAS;QACf,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAA4B;;AA7FtF,QAAA,IAAA,CAAA,iBAAiB,GAAG;AAC5B,YAAA,kBAAkB,EAAE,sCAAsC;AAC1D,YAAA,wBAAwB,EAAE,4CAA4C;AACtE,YAAA,kBAAkB,EAAE,sCAAsC;AAC1D,YAAA,sBAAsB,EAAE,0CAA0C;AAClE,YAAA,sBAAsB,EAAE,0CAA0C;AAClE,YAAA,wBAAwB,EAAE,4CAA4C;SACvE,CAAC;AAEF;;;AAGG;QACkB,IAAS,CAAA,SAAA,GAAW,EAAE,CAAC;AAE5C;;AAEG;QACuB,IAAc,CAAA,cAAA,GAAkB,IAAI,CAAC;;QAmBtD,IAAa,CAAA,aAAA,GAAuB,OAAO,CAAC;;QAG5C,IAAI,CAAA,IAAA,GAAkB,IAAI,CAAC;;AAGjB,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,YAAY,EAAqB,CAAC;;AAG/C,QAAA,IAAA,CAAA,mBAAmB,GAA0B,IAAI,YAAY,EAAW,CAAC;AA8B5F;;;AAGG;AACH,QAAA,IAAA,CAAA,UAAU,GAAc,MAAK,GAAG,CAAC;QAEzB,IAAsB,CAAA,sBAAA,GAAW,EAAE,CAAC;AACpC,QAAA,IAAA,CAAA,kBAAkB,GAAyB,oBAAoB,CAAC,IAAI,CAAC;AACrE,QAAA,IAAA,CAAA,6BAA6B,GAAyB,MAAK,GAAG,CAAC;AAC/D,QAAA,IAAA,CAAA,kBAAkB,GAAG,MAAK,GAAG,CAAC;QAqC9B,IAAQ,CAAA,QAAA,GAAY,KAAK,CAAC;QAa1B,IAAS,CAAA,SAAA,GAAY,KAAK,CAAC;QA6B3B,IAAc,CAAA,cAAA,GAAY,KAAK,CAAC;QArEtC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC;AAC1C,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC;QACnD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,SAAS,GAAG,CAAoB,iBAAA,EAAA,EAAE,YAAY,CAAA,CAAE,CAAC;KACjE;AAED,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,IAAI,OAAO,CAAC,UAAU,CAAC,EAAE;YACvB,IAAI,CAAC,kBAAkB,EAAE,CAAC;SAC3B;KACF;IAED,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;KAC9C;;AAGD,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;IACD,IAAI,OAAO,CAAC,KAAc,EAAA;AACxB,QAAA,IAAI,KAAK,IAAI,IAAI,CAAC,OAAO,EAAE;AACzB,YAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AACtB,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;SACxC;KACF;;AAID,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,QAAQ,EAAE;AAC3B,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACvB,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;SACxC;KACF;AAGD;;;;;AAKG;AACH,IAAA,IACI,aAAa,GAAA;QACf,OAAO,IAAI,CAAC,cAAc,CAAC;KAC5B;IACD,IAAI,aAAa,CAAC,KAAc,EAAA;AAC9B,QAAA,MAAM,OAAO,GAAG,KAAK,IAAI,IAAI,CAAC,cAAc,CAAC;AAC7C,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAE5B,IAAI,OAAO,EAAE;AACX,YAAA,IAAI,IAAI,CAAC,cAAc,EAAE;AACvB,gBAAA,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;aAChE;iBAAM;AACL,gBAAA,IAAI,CAAC,qBAAqB,CACxB,IAAI,CAAC,OAAO,GAAG,oBAAoB,CAAC,OAAO,GAAG,oBAAoB,CAAC,SAAS,CAC7E,CAAC;aACH;YACD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SACpD;AAED,QAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;KAC9C;IAGD,iBAAiB,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ,CAAC;KAC5C;;IAGD,kBAAkB,GAAA;;;;;;AAMhB,QAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC;KACzC;;AAGD,IAAA,UAAU,CAAC,KAAU,EAAA;AACnB,QAAA,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,KAAK,CAAC;KACxB;;AAGD,IAAA,gBAAgB,CAAC,EAAwB,EAAA;AACvC,QAAA,IAAI,CAAC,6BAA6B,GAAG,EAAE,CAAC;KACzC;;AAGD,IAAA,iBAAiB,CAAC,EAAO,EAAA;AACvB,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;KACtB;;AAGD,IAAA,gBAAgB,CAAC,UAAmB,EAAA;AAClC,QAAA,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC;KAC5B;;AAGD,IAAA,QAAQ,CAAC,OAAiC,EAAA;QACxC,OAAO,IAAI,CAAC,QAAQ,IAAI,OAAO,CAAC,KAAK,KAAK,IAAI,GAAG,EAAC,UAAU,EAAE,IAAI,EAAC,GAAG,IAAI,CAAC;KAC5E;;AAGD,IAAA,yBAAyB,CAAC,EAAc,EAAA;AACtC,QAAA,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;KAC9B;AAEO,IAAA,qBAAqB,CAAC,QAA8B,EAAA;AAC1D,QAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC;AACvC,QAAA,IAAI,OAAO,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;AAEhD,QAAA,IAAI,QAAQ,KAAK,QAAQ,IAAI,CAAC,OAAO,EAAE;YACrC,OAAO;SACR;AACD,QAAA,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC/B,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;SACvD;QAED,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,yCAAyC,CAC1E,QAAQ,EACR,QAAQ,CACT,CAAC;AACF,QAAA,IAAI,CAAC,kBAAkB,GAAG,QAAQ,CAAC;QAEnC,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1C,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;;AAGnD,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,sBAAsB,CAAC;AAEnD,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;gBAClC,UAAU,CAAC,MAAK;AACd,oBAAA,OAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;iBAC3C,EAAE,IAAI,CAAC,CAAC;AACX,aAAC,CAAC,CAAC;SACJ;KACF;IAEO,gBAAgB,GAAA;AACtB,QAAA,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACjD,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;;;AAIxD,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;SACzD;KACF;;IAGD,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC;AAC7B,QAAA,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KAClD;IAES,iBAAiB,GAAA;AACzB,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC;;QAG/C,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,WAAW,KAAK,MAAM,EAAE;;YAE5C,IAAI,IAAI,CAAC,aAAa,IAAI,WAAW,KAAK,OAAO,EAAE;AACjD,gBAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;AAC1B,oBAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;oBAC5B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AACrD,iBAAC,CAAC,CAAC;aACJ;AAED,YAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC/B,YAAA,IAAI,CAAC,qBAAqB,CACxB,IAAI,CAAC,QAAQ,GAAG,oBAAoB,CAAC,OAAO,GAAG,oBAAoB,CAAC,SAAS,CAC9E,CAAC;;;;YAKF,IAAI,CAAC,gBAAgB,EAAE,CAAC;SACzB;aAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,WAAW,KAAK,MAAM,EAAE;;;YAGnD,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;YACxD,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;SACrE;KACF;AAED,IAAA,mBAAmB,CAAC,KAAY,EAAA;;;;QAI9B,KAAK,CAAC,eAAe,EAAE,CAAC;KACzB;IAED,OAAO,GAAA;;;;;;AAML,QAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;YAC1B,IAAI,CAAC,UAAU,EAAE,CAAC;AAClB,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACzC,SAAC,CAAC,CAAC;KACJ;IAEO,yCAAyC,CAC/C,QAA8B,EAC9B,QAA8B,EAAA;;AAG9B,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,gBAAgB,EAAE;AAC5C,YAAA,OAAO,EAAE,CAAC;SACX;QAED,QAAQ,QAAQ;YACd,KAAK,oBAAoB,CAAC,IAAI;;;AAG5B,gBAAA,IAAI,QAAQ,KAAK,oBAAoB,CAAC,OAAO,EAAE;AAC7C,oBAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC;iBAClD;AAAM,qBAAA,IAAI,QAAQ,IAAI,oBAAoB,CAAC,aAAa,EAAE;oBACzD,OAAO,IAAI,CAAC,QAAQ;AAClB,0BAAE,IAAI,CAAC,iBAAiB,CAAC,sBAAsB;AAC/C,0BAAE,IAAI,CAAC,iBAAiB,CAAC,wBAAwB,CAAC;iBACrD;gBACD,MAAM;YACR,KAAK,oBAAoB,CAAC,SAAS;AACjC,gBAAA,OAAO,QAAQ,KAAK,oBAAoB,CAAC,OAAO;AAC9C,sBAAE,IAAI,CAAC,iBAAiB,CAAC,kBAAkB;AAC3C,sBAAE,IAAI,CAAC,iBAAiB,CAAC,wBAAwB,CAAC;YACtD,KAAK,oBAAoB,CAAC,OAAO;AAC/B,gBAAA,OAAO,QAAQ,KAAK,oBAAoB,CAAC,SAAS;AAChD,sBAAE,IAAI,CAAC,iBAAiB,CAAC,kBAAkB;AAC3C,sBAAE,IAAI,CAAC,iBAAiB,CAAC,sBAAsB,CAAC;YACpD,KAAK,oBAAoB,CAAC,aAAa;AACrC,gBAAA,OAAO,QAAQ,KAAK,oBAAoB,CAAC,OAAO;AAC9C,sBAAE,IAAI,CAAC,iBAAiB,CAAC,sBAAsB;AAC/C,sBAAE,IAAI,CAAC,iBAAiB,CAAC,wBAAwB,CAAC;SACvD;AAED,QAAA,OAAO,EAAE,CAAC;KACX;AAED;;;;;;;AAOG;AACK,IAAA,kBAAkB,CAAC,KAAc,EAAA;AACvC,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC;QAE1C,IAAI,cAAc,EAAE;AAClB,YAAA,cAAc,CAAC,aAAa,CAAC,aAAa,GAAG,KAAK,CAAC;SACpD;KACF;IAED,aAAa,GAAA;QACX,IAAI,CAAC,iBAAiB,EAAE,CAAC;KAC1B;IAED,mBAAmB,GAAA;QACjB,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAEzB,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;;;AAGlB,YAAA,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;SAC1C;KACF;AAED;;;;;;AAMG;AACH,IAAA,yBAAyB,CAAC,KAAiB,EAAA;AACzC,QAAA,IAAI,CAAC,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAqB,CAAC,EAAE;YAC5F,KAAK,CAAC,eAAe,EAAE,CAAC;SACzB;KACF;AAxZU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAW,EAiHT,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,UAAU,EACD,SAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,qBAAqB,6BACrB,4BAA4B,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAnHvC,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,WAAW,EAwDH,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,SAAA,EAAA,CAAA,YAAA,EAAA,WAAA,CAAA,EAAA,cAAA,EAAA,CAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,eAAA,EAAA,CAAA,kBAAA,EAAA,iBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAkBhB,EAAA,aAAA,EAAA,eAAA,EAAA,IAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAAA,gBAAgB,sCAShB,CAAC,KAAc,MAAM,KAAK,IAAI,IAAI,GAAG,SAAS,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,CAAA,EAAA,KAAA,EAAA,OAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAmDxE,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAahB,gBAAgB,CAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAkBhB,gBAAgB,CAnLxB,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,eAAA,EAAA,MAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,+BAAA,EAAA,qCAAA,EAAA,8BAAA,EAAA,UAAA,EAAA,IAAA,EAAA,IAAA,EAAA,iCAAA,EAAA,UAAA,EAAA,gCAAA,EAAA,SAAA,EAAA,OAAA,EAAA,2CAAA,EAAA,EAAA,cAAA,EAAA,kBAAA,EAAA,EAAA,SAAA,EAAA;YACT,mCAAmC;AACnC,YAAA;AACE,gBAAA,OAAO,EAAE,aAAa;AACtB,gBAAA,WAAW,EAAE,WAAW;AACxB,gBAAA,KAAK,EAAE,IAAI;AACZ,aAAA;AACF,SAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,OAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,OAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,QAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAuGU,SAAS,ECpNtB,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,aAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,ipEAmDA,ED+DY,MAAA,EAAA,CAAA,6xnBAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,SAAS,wPAAE,qBAAqB,EAAA,QAAA,EAAA,8BAAA,EAAA,MAAA,EAAA,CAAA,eAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAE/B,WAAW,EAAA,UAAA,EAAA,CAAA;kBA/BvB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,cAAc,EAGlB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,kBAAkB;AAC3B,wBAAA,iBAAiB,EAAE,MAAM;AACzB,wBAAA,mBAAmB,EAAE,MAAM;AAC3B,wBAAA,wBAAwB,EAAE,MAAM;AAChC,wBAAA,iCAAiC,EAAE,CAAqC,mCAAA,CAAA;AACxE,wBAAA,gCAAgC,EAAE,UAAU;AAC5C,wBAAA,MAAM,EAAE,IAAI;;AAEZ,wBAAA,mCAAmC,EAAE,UAAU;AAC/C,wBAAA,kCAAkC,EAAE,SAAS;AAC7C,wBAAA,SAAS,EAAE,uCAAuC;qBACnD,EACU,SAAA,EAAA;wBACT,mCAAmC;AACnC,wBAAA;AACE,4BAAA,OAAO,EAAE,aAAa;AACtB,4BAAA,WAAW,EAAa,WAAA;AACxB,4BAAA,KAAK,EAAE,IAAI;AACZ,yBAAA;AACF,qBAAA,EAAA,QAAA,EACS,aAAa,EACR,aAAA,EAAA,iBAAiB,CAAC,IAAI,mBACpB,uBAAuB,CAAC,MAAM,EAAA,UAAA,EACnC,IAAI,EACP,OAAA,EAAA,CAAC,SAAS,EAAE,qBAAqB,CAAC,EAAA,QAAA,EAAA,ipEAAA,EAAA,MAAA,EAAA,CAAA,6xnBAAA,CAAA,EAAA,CAAA;;0BAmHxC,SAAS;2BAAC,UAAU,CAAA;;0BACpB,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;;0BACxC,QAAQ;;0BAAI,MAAM;2BAAC,4BAA4B,CAAA;yCAhF7B,SAAS,EAAA,CAAA;sBAA7B,KAAK;uBAAC,YAAY,CAAA;gBAKO,cAAc,EAAA,CAAA;sBAAvC,KAAK;uBAAC,iBAAiB,CAAA;gBAGG,eAAe,EAAA,CAAA;sBAAzC,KAAK;uBAAC,kBAAkB,CAAA;gBAKhB,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAQgC,QAAQ,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAG3B,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAGG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAGa,MAAM,EAAA,CAAA;sBAAxB,MAAM;gBAGY,mBAAmB,EAAA,CAAA;sBAArC,MAAM;gBAGE,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAGgC,aAAa,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAGhB,aAAa,EAAA,CAAA;sBAAhC,SAAS;uBAAC,OAAO,CAAA;gBAGE,aAAa,EAAA,CAAA;sBAAhC,SAAS;uBAAC,OAAO,CAAA;gBAIlB,QAAQ,EAAA,CAAA;sBADP,KAAK;uBAAC,EAAC,SAAS,EAAE,CAAC,KAAc,MAAM,KAAK,IAAI,IAAI,GAAG,SAAS,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,EAAC,CAAA;gBAMnF,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAOgB,MAAM,EAAA,CAAA;sBAA3B,SAAS;uBAAC,SAAS,CAAA;gBAuChB,OAAO,EAAA,CAAA;sBADV,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAchC,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAmBhC,aAAa,EAAA,CAAA;sBADhB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;;;AE9QtC;;;AAGG;AACU,MAAA,+BAA+B,GAAa;AACvD,IAAA,OAAO,EAAE,aAAa;AACtB,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,4BAA4B,CAAC;AAC3D,IAAA,KAAK,EAAE,IAAI;EACX;AAEF;;;;;;;AAOG;AAOG,MAAO,4BAA6B,SAAQ,yBAAyB,CAAA;8GAA9D,4BAA4B,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAA5B,4BAA4B,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,6HAAA,EAAA,SAAA,EAH5B,CAAC,+BAA+B,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAGjC,4BAA4B,EAAA,UAAA,EAAA,CAAA;kBANxC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,CAAA;AACsE,iFAAA,CAAA;oBAChF,SAAS,EAAE,CAAC,+BAA+B,CAAC;AAC5C,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;;ACrBD;;;AAGG;MAKU,mCAAmC,CAAA;8GAAnC,mCAAmC,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;+GAAnC,mCAAmC,EAAA,OAAA,EAAA,CAHpC,4BAA4B,CAAA,EAAA,OAAA,EAAA,CAC5B,4BAA4B,CAAA,EAAA,CAAA,CAAA,EAAA;+GAE3B,mCAAmC,EAAA,CAAA,CAAA,EAAA;;2FAAnC,mCAAmC,EAAA,UAAA,EAAA,CAAA;kBAJ/C,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,4BAA4B,CAAC;oBACvC,OAAO,EAAE,CAAC,4BAA4B,CAAC;AACxC,iBAAA,CAAA;;MAOY,iBAAiB,CAAA;8GAAjB,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAjB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,YAHlB,WAAW,EAAE,eAAe,CAC5B,EAAA,OAAA,EAAA,CAAA,WAAW,EAAE,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;AAE3B,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,EAHlB,OAAA,EAAA,CAAA,WAAW,EAAE,eAAe,EACf,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAE3B,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAJ7B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,WAAW,EAAE,eAAe,CAAC;AACvC,oBAAA,OAAO,EAAE,CAAC,WAAW,EAAE,eAAe,CAAC;AACxC,iBAAA,CAAA;;;AC1BD;;AAEG;;;;"}