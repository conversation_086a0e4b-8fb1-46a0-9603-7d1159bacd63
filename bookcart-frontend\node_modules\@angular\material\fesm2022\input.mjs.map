{"version": 3, "file": "input.mjs", "sources": ["../../../../../../src/material/input/input-errors.ts", "../../../../../../src/material/input/input-value-accessor.ts", "../../../../../../src/material/input/input.ts", "../../../../../../src/material/input/module.ts", "../../../../../../src/material/input/input_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** @docs-private */\nexport function getMatInputUnsupportedTypeError(type: string): Error {\n  return Error(`Input type \"${type}\" isn't supported by matInput.`);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {InjectionToken} from '@angular/core';\n\n/**\n * This token is used to inject the object whose value should be set into `MatInput`. If none is\n * provided, the native `HTMLInputElement` is used. Directives like `MatDatepickerInput` can provide\n * themselves for this token, in order to make `MatInput` delegate the getting and setting of the\n * value to them.\n */\nexport const MAT_INPUT_VALUE_ACCESSOR = new InjectionToken<{value: any}>(\n  'MAT_INPUT_VALUE_ACCESSOR',\n);\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {BooleanInput, coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {getSupportedInputTypes, Platform} from '@angular/cdk/platform';\nimport {AutofillMonitor} from '@angular/cdk/text-field';\nimport {\n  AfterViewInit,\n  Directive,\n  DoCheck,\n  ElementRef,\n  Inject,\n  Input,\n  NgZone,\n  OnChanges,\n  OnDestroy,\n  Optional,\n  Self,\n} from '@angular/core';\nimport {FormGroupDirective, NgControl, NgForm, Validators} from '@angular/forms';\nimport {ErrorStateMatcher, _ErrorStateTracker} from '@angular/material/core';\nimport {MatFormFieldControl, MatFormField, MAT_FORM_FIELD} from '@angular/material/form-field';\nimport {Subject} from 'rxjs';\nimport {getMatInputUnsupportedTypeError} from './input-errors';\nimport {MAT_INPUT_VALUE_ACCESSOR} from './input-value-accessor';\n\n// Invalid input type. Using one of these will throw an MatInputUnsupportedTypeError.\nconst MAT_INPUT_INVALID_TYPES = [\n  'button',\n  'checkbox',\n  'file',\n  'hidden',\n  'image',\n  'radio',\n  'range',\n  'reset',\n  'submit',\n];\n\nlet nextUniqueId = 0;\n\n@Directive({\n  selector: `input[matInput], textarea[matInput], select[matNativeControl],\n      input[matNativeControl], textarea[matNativeControl]`,\n  exportAs: 'matInput',\n  host: {\n    'class': 'mat-mdc-input-element',\n    // The BaseMatInput parent class adds `mat-input-element`, `mat-form-field-control` and\n    // `mat-form-field-autofill-control` to the CSS class list, but this should not be added for\n    // this MDC equivalent input.\n    '[class.mat-input-server]': '_isServer',\n    '[class.mat-mdc-form-field-textarea-control]': '_isInFormField && _isTextarea',\n    '[class.mat-mdc-form-field-input-control]': '_isInFormField',\n    '[class.mdc-text-field__input]': '_isInFormField',\n    '[class.mat-mdc-native-select-inline]': '_isInlineSelect()',\n    // Native input properties that are overwritten by Angular inputs need to be synced with\n    // the native input element. Otherwise property bindings for those don't work.\n    '[id]': 'id',\n    '[disabled]': 'disabled',\n    '[required]': 'required',\n    '[attr.name]': 'name || null',\n    '[attr.readonly]': 'readonly && !_isNativeSelect || null',\n    // Only mark the input as invalid for assistive technology if it has a value since the\n    // state usually overlaps with `aria-required` when the input is empty and can be redundant.\n    '[attr.aria-invalid]': '(empty && required) ? null : errorState',\n    '[attr.aria-required]': 'required',\n    // Native input properties that are overwritten by Angular inputs need to be synced with\n    // the native input element. Otherwise property bindings for those don't work.\n    '[attr.id]': 'id',\n    '(focus)': '_focusChanged(true)',\n    '(blur)': '_focusChanged(false)',\n    '(input)': '_onInput()',\n  },\n  providers: [{provide: MatFormFieldControl, useExisting: MatInput}],\n  standalone: true,\n})\nexport class MatInput\n  implements MatFormFieldControl<any>, OnChanges, OnDestroy, AfterViewInit, DoCheck\n{\n  protected _uid = `mat-input-${nextUniqueId++}`;\n  protected _previousNativeValue: any;\n  private _inputValueAccessor: {value: any};\n  private _previousPlaceholder: string | null;\n  private _errorStateTracker: _ErrorStateTracker;\n\n  /** Whether the component is being rendered on the server. */\n  readonly _isServer: boolean;\n\n  /** Whether the component is a native html select. */\n  readonly _isNativeSelect: boolean;\n\n  /** Whether the component is a textarea. */\n  readonly _isTextarea: boolean;\n\n  /** Whether the input is inside of a form field. */\n  readonly _isInFormField: boolean;\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  focused: boolean = false;\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  readonly stateChanges: Subject<void> = new Subject<void>();\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  controlType: string = 'mat-input';\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  autofilled = false;\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  @Input()\n  get disabled(): boolean {\n    return this._disabled;\n  }\n  set disabled(value: BooleanInput) {\n    this._disabled = coerceBooleanProperty(value);\n\n    // Browsers may not fire the blur event if the input is disabled too quickly.\n    // Reset from here to ensure that the element doesn't become stuck.\n    if (this.focused) {\n      this.focused = false;\n      this.stateChanges.next();\n    }\n  }\n  protected _disabled = false;\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  @Input()\n  get id(): string {\n    return this._id;\n  }\n  set id(value: string) {\n    this._id = value || this._uid;\n  }\n  protected _id: string;\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  @Input() placeholder: string;\n\n  /**\n   * Name of the input.\n   * @docs-private\n   */\n  @Input() name: string;\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  @Input()\n  get required(): boolean {\n    return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n  }\n  set required(value: BooleanInput) {\n    this._required = coerceBooleanProperty(value);\n  }\n  protected _required: boolean | undefined;\n\n  /** Input type of the element. */\n  @Input()\n  get type(): string {\n    return this._type;\n  }\n  set type(value: string) {\n    this._type = value || 'text';\n    this._validateType();\n\n    // When using Angular inputs, developers are no longer able to set the properties on the native\n    // input element. To ensure that bindings for `type` work, we need to sync the setter\n    // with the native property. Textarea elements don't support the type property or attribute.\n    if (!this._isTextarea && getSupportedInputTypes().has(this._type)) {\n      (this._elementRef.nativeElement as HTMLInputElement).type = this._type;\n    }\n  }\n  protected _type = 'text';\n\n  /** An object used to control when error messages are shown. */\n  @Input()\n  get errorStateMatcher() {\n    return this._errorStateTracker.matcher;\n  }\n  set errorStateMatcher(value: ErrorStateMatcher) {\n    this._errorStateTracker.matcher = value;\n  }\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  @Input('aria-describedby') userAriaDescribedBy: string;\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  @Input()\n  get value(): string {\n    return this._inputValueAccessor.value;\n  }\n  set value(value: any) {\n    if (value !== this.value) {\n      this._inputValueAccessor.value = value;\n      this.stateChanges.next();\n    }\n  }\n\n  /** Whether the element is readonly. */\n  @Input()\n  get readonly(): boolean {\n    return this._readonly;\n  }\n  set readonly(value: BooleanInput) {\n    this._readonly = coerceBooleanProperty(value);\n  }\n  private _readonly = false;\n\n  /** Whether the input is in an error state. */\n  get errorState() {\n    return this._errorStateTracker.errorState;\n  }\n  set errorState(value: boolean) {\n    this._errorStateTracker.errorState = value;\n  }\n\n  protected _neverEmptyInputTypes = [\n    'date',\n    'datetime',\n    'datetime-local',\n    'month',\n    'time',\n    'week',\n  ].filter(t => getSupportedInputTypes().has(t));\n\n  constructor(\n    protected _elementRef: ElementRef<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>,\n    protected _platform: Platform,\n    @Optional() @Self() public ngControl: NgControl,\n    @Optional() parentForm: NgForm,\n    @Optional() parentFormGroup: FormGroupDirective,\n    defaultErrorStateMatcher: ErrorStateMatcher,\n    @Optional() @Self() @Inject(MAT_INPUT_VALUE_ACCESSOR) inputValueAccessor: any,\n    private _autofillMonitor: AutofillMonitor,\n    ngZone: NgZone,\n    // TODO: Remove this once the legacy appearance has been removed. We only need\n    // to inject the form field for determining whether the placeholder has been promoted.\n    @Optional() @Inject(MAT_FORM_FIELD) protected _formField?: MatFormField,\n  ) {\n    const element = this._elementRef.nativeElement;\n    const nodeName = element.nodeName.toLowerCase();\n\n    // If no input value accessor was explicitly specified, use the element as the input value\n    // accessor.\n    this._inputValueAccessor = inputValueAccessor || element;\n\n    this._previousNativeValue = this.value;\n\n    // Force setter to be called in case id was not specified.\n    this.id = this.id;\n\n    // On some versions of iOS the caret gets stuck in the wrong place when holding down the delete\n    // key. In order to get around this we need to \"jiggle\" the caret loose. Since this bug only\n    // exists on iOS, we only bother to install the listener on iOS.\n    if (_platform.IOS) {\n      ngZone.runOutsideAngular(() => {\n        _elementRef.nativeElement.addEventListener('keyup', this._iOSKeyupListener);\n      });\n    }\n\n    this._errorStateTracker = new _ErrorStateTracker(\n      defaultErrorStateMatcher,\n      ngControl,\n      parentFormGroup,\n      parentForm,\n      this.stateChanges,\n    );\n    this._isServer = !this._platform.isBrowser;\n    this._isNativeSelect = nodeName === 'select';\n    this._isTextarea = nodeName === 'textarea';\n    this._isInFormField = !!_formField;\n\n    if (this._isNativeSelect) {\n      this.controlType = (element as HTMLSelectElement).multiple\n        ? 'mat-native-select-multiple'\n        : 'mat-native-select';\n    }\n  }\n\n  ngAfterViewInit() {\n    if (this._platform.isBrowser) {\n      this._autofillMonitor.monitor(this._elementRef.nativeElement).subscribe(event => {\n        this.autofilled = event.isAutofilled;\n        this.stateChanges.next();\n      });\n    }\n  }\n\n  ngOnChanges() {\n    this.stateChanges.next();\n  }\n\n  ngOnDestroy() {\n    this.stateChanges.complete();\n\n    if (this._platform.isBrowser) {\n      this._autofillMonitor.stopMonitoring(this._elementRef.nativeElement);\n    }\n\n    if (this._platform.IOS) {\n      this._elementRef.nativeElement.removeEventListener('keyup', this._iOSKeyupListener);\n    }\n  }\n\n  ngDoCheck() {\n    if (this.ngControl) {\n      // We need to re-evaluate this on every change detection cycle, because there are some\n      // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n      // that whatever logic is in here has to be super lean or we risk destroying the performance.\n      this.updateErrorState();\n\n      // Since the input isn't a `ControlValueAccessor`, we don't have a good way of knowing when\n      // the disabled state has changed. We can't use the `ngControl.statusChanges`, because it\n      // won't fire if the input is disabled with `emitEvents = false`, despite the input becoming\n      // disabled.\n      if (this.ngControl.disabled !== null && this.ngControl.disabled !== this.disabled) {\n        this.disabled = this.ngControl.disabled;\n        this.stateChanges.next();\n      }\n    }\n\n    // We need to dirty-check the native element's value, because there are some cases where\n    // we won't be notified when it changes (e.g. the consumer isn't using forms or they're\n    // updating the value using `emitEvent: false`).\n    this._dirtyCheckNativeValue();\n\n    // We need to dirty-check and set the placeholder attribute ourselves, because whether it's\n    // present or not depends on a query which is prone to \"changed after checked\" errors.\n    this._dirtyCheckPlaceholder();\n  }\n\n  /** Focuses the input. */\n  focus(options?: FocusOptions): void {\n    this._elementRef.nativeElement.focus(options);\n  }\n\n  /** Refreshes the error state of the input. */\n  updateErrorState() {\n    this._errorStateTracker.updateErrorState();\n  }\n\n  /** Callback for the cases where the focused state of the input changes. */\n  _focusChanged(isFocused: boolean) {\n    if (isFocused !== this.focused) {\n      this.focused = isFocused;\n      this.stateChanges.next();\n    }\n  }\n\n  _onInput() {\n    // This is a noop function and is used to let Angular know whenever the value changes.\n    // Angular will run a new change detection each time the `input` event has been dispatched.\n    // It's necessary that Angular recognizes the value change, because when floatingLabel\n    // is set to false and Angular forms aren't used, the placeholder won't recognize the\n    // value changes and will not disappear.\n    // Listening to the input event wouldn't be necessary when the input is using the\n    // FormsModule or ReactiveFormsModule, because Angular forms also listens to input events.\n  }\n\n  /** Does some manual dirty checking on the native input `value` property. */\n  protected _dirtyCheckNativeValue() {\n    const newValue = this._elementRef.nativeElement.value;\n\n    if (this._previousNativeValue !== newValue) {\n      this._previousNativeValue = newValue;\n      this.stateChanges.next();\n    }\n  }\n\n  /** Does some manual dirty checking on the native input `placeholder` attribute. */\n  private _dirtyCheckPlaceholder() {\n    const placeholder = this._getPlaceholder();\n    if (placeholder !== this._previousPlaceholder) {\n      const element = this._elementRef.nativeElement;\n      this._previousPlaceholder = placeholder;\n      placeholder\n        ? element.setAttribute('placeholder', placeholder)\n        : element.removeAttribute('placeholder');\n    }\n  }\n\n  /** Gets the current placeholder of the form field. */\n  protected _getPlaceholder(): string | null {\n    return this.placeholder || null;\n  }\n\n  /** Make sure the input is a supported type. */\n  protected _validateType() {\n    if (\n      MAT_INPUT_INVALID_TYPES.indexOf(this._type) > -1 &&\n      (typeof ngDevMode === 'undefined' || ngDevMode)\n    ) {\n      throw getMatInputUnsupportedTypeError(this._type);\n    }\n  }\n\n  /** Checks whether the input type is one of the types that are never empty. */\n  protected _isNeverEmpty() {\n    return this._neverEmptyInputTypes.indexOf(this._type) > -1;\n  }\n\n  /** Checks whether the input is invalid based on the native validation. */\n  protected _isBadInput() {\n    // The `validity` property won't be present on platform-server.\n    let validity = (this._elementRef.nativeElement as HTMLInputElement).validity;\n    return validity && validity.badInput;\n  }\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get empty(): boolean {\n    return (\n      !this._isNeverEmpty() &&\n      !this._elementRef.nativeElement.value &&\n      !this._isBadInput() &&\n      !this.autofilled\n    );\n  }\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get shouldLabelFloat(): boolean {\n    if (this._isNativeSelect) {\n      // For a single-selection `<select>`, the label should float when the selected option has\n      // a non-empty display value. For a `<select multiple>`, the label *always* floats to avoid\n      // overlapping the label with the options.\n      const selectElement = this._elementRef.nativeElement as HTMLSelectElement;\n      const firstOption: HTMLOptionElement | undefined = selectElement.options[0];\n\n      // On most browsers the `selectedIndex` will always be 0, however on IE and Edge it'll be\n      // -1 if the `value` is set to something, that isn't in the list of options, at a later point.\n      return (\n        this.focused ||\n        selectElement.multiple ||\n        !this.empty ||\n        !!(selectElement.selectedIndex > -1 && firstOption && firstOption.label)\n      );\n    } else {\n      return this.focused || !this.empty;\n    }\n  }\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  setDescribedByIds(ids: string[]) {\n    if (ids.length) {\n      this._elementRef.nativeElement.setAttribute('aria-describedby', ids.join(' '));\n    } else {\n      this._elementRef.nativeElement.removeAttribute('aria-describedby');\n    }\n  }\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  onContainerClick() {\n    // Do not re-focus the input element if the element is already focused. Otherwise it can happen\n    // that someone clicks on a time input and the cursor resets to the \"hours\" field while the\n    // \"minutes\" field was actually clicked. See: https://github.com/angular/components/issues/12849\n    if (!this.focused) {\n      this.focus();\n    }\n  }\n\n  /** Whether the form control is a native select that is displayed inline. */\n  _isInlineSelect(): boolean {\n    const element = this._elementRef.nativeElement as HTMLSelectElement;\n    return this._isNativeSelect && (element.multiple || element.size > 1);\n  }\n\n  private _iOSKeyupListener = (event: Event): void => {\n    const el = event.target as HTMLInputElement;\n\n    // Note: We specifically check for 0, rather than `!el.selectionStart`, because the two\n    // indicate different things. If the value is 0, it means that the caret is at the start\n    // of the input, whereas a value of `null` means that the input doesn't support\n    // manipulating the selection range. Inputs that don't support setting the selection range\n    // will throw an error so we want to avoid calling `setSelectionRange` on them. See:\n    // https://html.spec.whatwg.org/multipage/input.html#do-not-apply\n    if (!el.value && el.selectionStart === 0 && el.selectionEnd === 0) {\n      // Note: Just setting `0, 0` doesn't fix the issue. Setting\n      // `1, 1` fixes it for the first time that you type text and\n      // then hold delete. Toggling to `1, 1` and then back to\n      // `0, 0` seems to completely fix it.\n      el.setSelectionRange(1, 1);\n      el.setSelectionRange(0, 0);\n    }\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {TextFieldModule} from '@angular/cdk/text-field';\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '@angular/material/core';\nimport {MatFormFieldModule} from '@angular/material/form-field';\nimport {MatInput} from './input';\n\n@NgModule({\n  imports: [MatCommonModule, MatFormFieldModule, MatInput],\n  exports: [MatInput, MatFormFieldModule, TextFieldModule, MatCommonModule],\n})\nexport class MatInputModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAQA;AACM,SAAU,+BAA+B,CAAC,IAAY,EAAA;AAC1D,IAAA,OAAO,KAAK,CAAC,CAAA,YAAA,EAAe,IAAI,CAAA,8BAAA,CAAgC,CAAC,CAAC;AACpE;;ACDA;;;;;AAKG;MACU,wBAAwB,GAAG,IAAI,cAAc,CACxD,0BAA0B;;ACc5B;AACA,MAAM,uBAAuB,GAAG;IAC9B,QAAQ;IACR,UAAU;IACV,MAAM;IACN,QAAQ;IACR,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,QAAQ;CACT,CAAC;AAEF,IAAI,YAAY,GAAG,CAAC,CAAC;MAqCR,QAAQ,CAAA;AA6CnB;;;AAGG;AACH,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,KAAmB,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;;;AAI9C,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACrB,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;SAC1B;KACF;AAGD;;;AAGG;AACH,IAAA,IACI,EAAE,GAAA;QACJ,OAAO,IAAI,CAAC,GAAG,CAAC;KACjB;IACD,IAAI,EAAE,CAAC,KAAa,EAAA;QAClB,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC;KAC/B;AAeD;;;AAGG;AACH,IAAA,IACI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,YAAY,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC;KAC9F;IACD,IAAI,QAAQ,CAAC,KAAmB,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;KAC/C;;AAID,IAAA,IACI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;IACD,IAAI,IAAI,CAAC,KAAa,EAAA;AACpB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,MAAM,CAAC;QAC7B,IAAI,CAAC,aAAa,EAAE,CAAC;;;;AAKrB,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,sBAAsB,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YAChE,IAAI,CAAC,WAAW,CAAC,aAAkC,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;SACxE;KACF;;AAID,IAAA,IACI,iBAAiB,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;KACxC;IACD,IAAI,iBAAiB,CAAC,KAAwB,EAAA;AAC5C,QAAA,IAAI,CAAC,kBAAkB,CAAC,OAAO,GAAG,KAAK,CAAC;KACzC;AAQD;;;AAGG;AACH,IAAA,IACI,KAAK,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC;KACvC;IACD,IAAI,KAAK,CAAC,KAAU,EAAA;AAClB,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE;AACxB,YAAA,IAAI,CAAC,mBAAmB,CAAC,KAAK,GAAG,KAAK,CAAC;AACvC,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;SAC1B;KACF;;AAGD,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,KAAmB,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;KAC/C;;AAID,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC;KAC3C;IACD,IAAI,UAAU,CAAC,KAAc,EAAA;AAC3B,QAAA,IAAI,CAAC,kBAAkB,CAAC,UAAU,GAAG,KAAK,CAAC;KAC5C;AAWD,IAAA,WAAA,CACY,WAAmF,EACnF,SAAmB,EACF,SAAoB,EACnC,UAAkB,EAClB,eAAmC,EAC/C,wBAA2C,EACW,kBAAuB,EACrE,gBAAiC,EACzC,MAAc;;;IAGgC,UAAyB,EAAA;QAX7D,IAAW,CAAA,WAAA,GAAX,WAAW,CAAwE;QACnF,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QACF,IAAS,CAAA,SAAA,GAAT,SAAS,CAAW;QAKvC,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAiB;QAIK,IAAU,CAAA,UAAA,GAAV,UAAU,CAAe;AA3L/D,QAAA,IAAA,CAAA,IAAI,GAAG,CAAA,UAAA,EAAa,YAAY,EAAE,EAAE,CAAC;AAkB/C;;;AAGG;QACH,IAAO,CAAA,OAAA,GAAY,KAAK,CAAC;AAEzB;;;AAGG;AACM,QAAA,IAAA,CAAA,YAAY,GAAkB,IAAI,OAAO,EAAQ,CAAC;AAE3D;;;AAGG;QACH,IAAW,CAAA,WAAA,GAAW,WAAW,CAAC;AAElC;;;AAGG;QACH,IAAU,CAAA,UAAA,GAAG,KAAK,CAAC;QAoBT,IAAS,CAAA,SAAA,GAAG,KAAK,CAAC;QAwDlB,IAAK,CAAA,KAAA,GAAG,MAAM,CAAC;QAwCjB,IAAS,CAAA,SAAA,GAAG,KAAK,CAAC;AAUhB,QAAA,IAAA,CAAA,qBAAqB,GAAG;YAChC,MAAM;YACN,UAAU;YACV,gBAAgB;YAChB,OAAO;YACP,MAAM;YACN,MAAM;AACP,SAAA,CAAC,MAAM,CAAC,CAAC,IAAI,sBAAsB,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AA8PvC,QAAA,IAAA,CAAA,iBAAiB,GAAG,CAAC,KAAY,KAAU;AACjD,YAAA,MAAM,EAAE,GAAG,KAAK,CAAC,MAA0B,CAAC;;;;;;;AAQ5C,YAAA,IAAI,CAAC,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,cAAc,KAAK,CAAC,IAAI,EAAE,CAAC,YAAY,KAAK,CAAC,EAAE;;;;;AAKjE,gBAAA,EAAE,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3B,gBAAA,EAAE,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aAC5B;AACH,SAAC,CAAC;AA/PA,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;QAC/C,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;;;AAIhD,QAAA,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,IAAI,OAAO,CAAC;AAEzD,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,KAAK,CAAC;;AAGvC,QAAA,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;;;;AAKlB,QAAA,IAAI,SAAS,CAAC,GAAG,EAAE;AACjB,YAAA,MAAM,CAAC,iBAAiB,CAAC,MAAK;gBAC5B,WAAW,CAAC,aAAa,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;AAC9E,aAAC,CAAC,CAAC;SACJ;AAED,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,kBAAkB,CAC9C,wBAAwB,EACxB,SAAS,EACT,eAAe,EACf,UAAU,EACV,IAAI,CAAC,YAAY,CAClB,CAAC;QACF,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;AAC3C,QAAA,IAAI,CAAC,eAAe,GAAG,QAAQ,KAAK,QAAQ,CAAC;AAC7C,QAAA,IAAI,CAAC,WAAW,GAAG,QAAQ,KAAK,UAAU,CAAC;AAC3C,QAAA,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,UAAU,CAAC;AAEnC,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AACxB,YAAA,IAAI,CAAC,WAAW,GAAI,OAA6B,CAAC,QAAQ;AACxD,kBAAE,4BAA4B;kBAC5B,mBAAmB,CAAC;SACzB;KACF;IAED,eAAe,GAAA;AACb,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;AAC5B,YAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,SAAS,CAAC,KAAK,IAAG;AAC9E,gBAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,YAAY,CAAC;AACrC,gBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;AAC3B,aAAC,CAAC,CAAC;SACJ;KACF;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;KAC1B;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;AAE7B,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAC5B,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;SACtE;AAED,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE;AACtB,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;SACrF;KACF;IAED,SAAS,GAAA;AACP,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;;;;YAIlB,IAAI,CAAC,gBAAgB,EAAE,CAAC;;;;;AAMxB,YAAA,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,KAAK,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,EAAE;gBACjF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;AACxC,gBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;aAC1B;SACF;;;;QAKD,IAAI,CAAC,sBAAsB,EAAE,CAAC;;;QAI9B,IAAI,CAAC,sBAAsB,EAAE,CAAC;KAC/B;;AAGD,IAAA,KAAK,CAAC,OAAsB,EAAA;QAC1B,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;KAC/C;;IAGD,gBAAgB,GAAA;AACd,QAAA,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,CAAC;KAC5C;;AAGD,IAAA,aAAa,CAAC,SAAkB,EAAA;AAC9B,QAAA,IAAI,SAAS,KAAK,IAAI,CAAC,OAAO,EAAE;AAC9B,YAAA,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;AACzB,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;SAC1B;KACF;IAED,QAAQ,GAAA;;;;;;;;KAQP;;IAGS,sBAAsB,GAAA;QAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC;AAEtD,QAAA,IAAI,IAAI,CAAC,oBAAoB,KAAK,QAAQ,EAAE;AAC1C,YAAA,IAAI,CAAC,oBAAoB,GAAG,QAAQ,CAAC;AACrC,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;SAC1B;KACF;;IAGO,sBAAsB,GAAA;AAC5B,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;AAC3C,QAAA,IAAI,WAAW,KAAK,IAAI,CAAC,oBAAoB,EAAE;AAC7C,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;AAC/C,YAAA,IAAI,CAAC,oBAAoB,GAAG,WAAW,CAAC;YACxC,WAAW;kBACP,OAAO,CAAC,YAAY,CAAC,aAAa,EAAE,WAAW,CAAC;AAClD,kBAAE,OAAO,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;SAC5C;KACF;;IAGS,eAAe,GAAA;AACvB,QAAA,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC;KACjC;;IAGS,aAAa,GAAA;QACrB,IACE,uBAAuB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aAC/C,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAC/C;AACA,YAAA,MAAM,+BAA+B,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACnD;KACF;;IAGS,aAAa,GAAA;AACrB,QAAA,OAAO,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;KAC5D;;IAGS,WAAW,GAAA;;QAEnB,IAAI,QAAQ,GAAI,IAAI,CAAC,WAAW,CAAC,aAAkC,CAAC,QAAQ,CAAC;AAC7E,QAAA,OAAO,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC;KACtC;AAED;;;AAGG;AACH,IAAA,IAAI,KAAK,GAAA;AACP,QAAA,QACE,CAAC,IAAI,CAAC,aAAa,EAAE;AACrB,YAAA,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK;YACrC,CAAC,IAAI,CAAC,WAAW,EAAE;AACnB,YAAA,CAAC,IAAI,CAAC,UAAU,EAChB;KACH;AAED;;;AAGG;AACH,IAAA,IAAI,gBAAgB,GAAA;AAClB,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE;;;;AAIxB,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,aAAkC,CAAC;YAC1E,MAAM,WAAW,GAAkC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;;;YAI5E,QACE,IAAI,CAAC,OAAO;AACZ,gBAAA,aAAa,CAAC,QAAQ;gBACtB,CAAC,IAAI,CAAC,KAAK;AACX,gBAAA,CAAC,EAAE,aAAa,CAAC,aAAa,GAAG,CAAC,CAAC,IAAI,WAAW,IAAI,WAAW,CAAC,KAAK,CAAC,EACxE;SACH;aAAM;YACL,OAAO,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;SACpC;KACF;AAED;;;AAGG;AACH,IAAA,iBAAiB,CAAC,GAAa,EAAA;AAC7B,QAAA,IAAI,GAAG,CAAC,MAAM,EAAE;AACd,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,kBAAkB,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;SAChF;aAAM;YACL,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC;SACpE;KACF;AAED;;;AAGG;IACH,gBAAgB,GAAA;;;;AAId,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,KAAK,EAAE,CAAC;SACd;KACF;;IAGD,eAAe,GAAA;AACb,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAkC,CAAC;AACpE,QAAA,OAAO,IAAI,CAAC,eAAe,KAAK,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;KACvE;8GA5aU,QAAQ,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,kBAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAyLW,wBAAwB,EAAA,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,eAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAKhC,cAAc,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGA9LzB,QAAQ,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,2HAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,aAAA,EAAA,IAAA,EAAA,MAAA,EAAA,QAAA,EAAA,UAAA,EAAA,IAAA,EAAA,MAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,mBAAA,EAAA,CAAA,kBAAA,EAAA,qBAAA,CAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,sBAAA,EAAA,OAAA,EAAA,YAAA,EAAA,EAAA,UAAA,EAAA,EAAA,wBAAA,EAAA,WAAA,EAAA,2CAAA,EAAA,+BAAA,EAAA,wCAAA,EAAA,gBAAA,EAAA,6BAAA,EAAA,gBAAA,EAAA,oCAAA,EAAA,mBAAA,EAAA,IAAA,EAAA,IAAA,EAAA,UAAA,EAAA,UAAA,EAAA,UAAA,EAAA,UAAA,EAAA,WAAA,EAAA,cAAA,EAAA,eAAA,EAAA,sCAAA,EAAA,mBAAA,EAAA,yCAAA,EAAA,oBAAA,EAAA,UAAA,EAAA,SAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,uBAAA,EAAA,EAAA,SAAA,EAHR,CAAC,EAAC,OAAO,EAAE,mBAAmB,EAAE,WAAW,EAAE,QAAQ,EAAC,CAAC,EAAA,QAAA,EAAA,CAAA,UAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAGvD,QAAQ,EAAA,UAAA,EAAA,CAAA;kBAnCpB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,CAAA;AAC8C,yDAAA,CAAA;AACxD,oBAAA,QAAQ,EAAE,UAAU;AACpB,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,uBAAuB;;;;AAIhC,wBAAA,0BAA0B,EAAE,WAAW;AACvC,wBAAA,6CAA6C,EAAE,+BAA+B;AAC9E,wBAAA,0CAA0C,EAAE,gBAAgB;AAC5D,wBAAA,+BAA+B,EAAE,gBAAgB;AACjD,wBAAA,sCAAsC,EAAE,mBAAmB;;;AAG3D,wBAAA,MAAM,EAAE,IAAI;AACZ,wBAAA,YAAY,EAAE,UAAU;AACxB,wBAAA,YAAY,EAAE,UAAU;AACxB,wBAAA,aAAa,EAAE,cAAc;AAC7B,wBAAA,iBAAiB,EAAE,sCAAsC;;;AAGzD,wBAAA,qBAAqB,EAAE,yCAAyC;AAChE,wBAAA,sBAAsB,EAAE,UAAU;;;AAGlC,wBAAA,WAAW,EAAE,IAAI;AACjB,wBAAA,SAAS,EAAE,qBAAqB;AAChC,wBAAA,QAAQ,EAAE,sBAAsB;AAChC,wBAAA,SAAS,EAAE,YAAY;AACxB,qBAAA;oBACD,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,mBAAmB,EAAE,WAAW,EAAU,QAAA,EAAC,CAAC;AAClE,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BAsLI,QAAQ;;0BAAI,IAAI;;0BAChB,QAAQ;;0BACR,QAAQ;;0BAER,QAAQ;;0BAAI,IAAI;;0BAAI,MAAM;2BAAC,wBAAwB,CAAA;;0BAKnD,QAAQ;;0BAAI,MAAM;2BAAC,cAAc,CAAA;yCA5IhC,QAAQ,EAAA,CAAA;sBADX,KAAK;gBAqBF,EAAE,EAAA,CAAA;sBADL,KAAK;gBAaG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAMG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAOF,QAAQ,EAAA,CAAA;sBADX,KAAK;gBAWF,IAAI,EAAA,CAAA;sBADP,KAAK;gBAmBF,iBAAiB,EAAA,CAAA;sBADpB,KAAK;gBAYqB,mBAAmB,EAAA,CAAA;sBAA7C,KAAK;uBAAC,kBAAkB,CAAA;gBAOrB,KAAK,EAAA,CAAA;sBADR,KAAK;gBAaF,QAAQ,EAAA,CAAA;sBADX,KAAK;;;MCvNK,cAAc,CAAA;8GAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAd,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,EAHf,OAAA,EAAA,CAAA,eAAe,EAAE,kBAAkB,EAAE,QAAQ,CAC7C,EAAA,OAAA,EAAA,CAAA,QAAQ,EAAE,kBAAkB,EAAE,eAAe,EAAE,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;+GAE7D,cAAc,EAAA,OAAA,EAAA,CAHf,eAAe,EAAE,kBAAkB,EACzB,kBAAkB,EAAE,eAAe,EAAE,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAE7D,cAAc,EAAA,UAAA,EAAA,CAAA;kBAJ1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,kBAAkB,EAAE,QAAQ,CAAC;oBACxD,OAAO,EAAE,CAAC,QAAQ,EAAE,kBAAkB,EAAE,eAAe,EAAE,eAAe,CAAC;AAC1E,iBAAA,CAAA;;;ACjBD;;AAEG;;;;"}