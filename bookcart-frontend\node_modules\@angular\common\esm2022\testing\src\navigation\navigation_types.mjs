/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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