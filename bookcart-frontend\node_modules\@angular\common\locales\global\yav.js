/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['yav'] = ["yav",[["kiɛmɛ́ɛm","kisɛ́ndɛ"],u,u],u,[["s","m","m","e","k","f","s"],["sd","md","mw","et","kl","fl","ss"],["sɔ́ndiɛ","móndie","muányáŋmóndie","metúkpíápɛ","kúpélimetúkpiapɛ","feléte","s<PERSON>elé"],["sd","md","mw","et","kl","fl","ss"]],u,[["1","2","3","4","5","6","7","8","9","10","11","12"],["o.1","o.2","o.3","o.4","o.5","o.6","o.7","o.8","o.9","o.10","o.11","o.12"],["pikítíkítie, oólí ú kutúan","siɛyɛ́, oóli ú kándíɛ","ɔnsúmbɔl, oóli ú kátátúɛ","mesiŋ, oóli ú kénie","ensil, oóli ú kátánuɛ","ɔsɔn","efute","pisuyú","imɛŋ i puɔs","imɛŋ i putúk,oóli ú kátíɛ","makandikɛ","pilɔndɔ́"]],u,[["k.Y.","+J.C."],u,["katikupíen Yésuse","ékélémkúnupíén n"]],1,[6,0],["d/M/y","d MMM y","d MMMM y","EEEE d MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[","," ",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","#,##0.00 ¤","#E0"],"XAF","FCFA","XAF",{"JPY":["JP¥","¥"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    