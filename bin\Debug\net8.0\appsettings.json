{"ConnectionStrings": {"DefaultConnection": "Server=DESKTOP-6LT5S8F\\MSSQLSERVER2019;Database=BookCartDb;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Urls": "http://localhost:5002", "JwtSettings": {"SecretKey": "BookCartSecretKeyForJWTTokenGeneration2024!@#$%^&*()_+", "Issuer": "BookCartAPI", "Audience": "BookCartUsers", "ExpirationHours": "24"}}