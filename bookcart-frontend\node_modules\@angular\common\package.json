{"name": "@angular/common", "version": "17.3.12", "description": "Angular - commonly needed directives and services", "author": "angular", "license": "MIT", "engines": {"node": "^18.13.0 || >=20.9.0"}, "locales": "locales", "dependencies": {"tslib": "^2.3.0"}, "exports": {"./locales/global/*": {"default": "./locales/global/*.js"}, "./locales/*": {"types": "./locales/*.d.ts", "default": "./locales/*.mjs"}, "./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "esm2022": "./esm2022/common.mjs", "esm": "./esm2022/common.mjs", "default": "./fesm2022/common.mjs"}, "./http": {"types": "./http/index.d.ts", "esm2022": "./esm2022/http/http.mjs", "esm": "./esm2022/http/http.mjs", "default": "./fesm2022/http.mjs"}, "./http/testing": {"types": "./http/testing/index.d.ts", "esm2022": "./esm2022/http/testing/testing.mjs", "esm": "./esm2022/http/testing/testing.mjs", "default": "./fesm2022/http/testing.mjs"}, "./testing": {"types": "./testing/index.d.ts", "esm2022": "./esm2022/testing/testing.mjs", "esm": "./esm2022/testing/testing.mjs", "default": "./fesm2022/testing.mjs"}, "./upgrade": {"types": "./upgrade/index.d.ts", "esm2022": "./esm2022/upgrade/upgrade.mjs", "esm": "./esm2022/upgrade/upgrade.mjs", "default": "./fesm2022/upgrade.mjs"}}, "peerDependencies": {"@angular/core": "17.3.12", "rxjs": "^6.5.3 || ^7.4.0"}, "repository": {"type": "git", "url": "https://github.com/angular/angular.git", "directory": "packages/common"}, "ng-update": {"packageGroup": ["@angular/core", "@angular/bazel", "@angular/common", "@angular/compiler", "@angular/compiler-cli", "@angular/animations", "@angular/elements", "@angular/platform-browser", "@angular/platform-browser-dynamic", "@angular/forms", "@angular/platform-server", "@angular/upgrade", "@angular/router", "@angular/language-service", "@angular/localize", "@angular/service-worker"]}, "sideEffects": ["**/global/*.js", "**/closure-locale.*"], "module": "./fesm2022/common.mjs", "typings": "./index.d.ts", "type": "module"}