/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\.?/, '').length;
    if (i === 1 && v === 0)
        return 1;
    return 5;
}
export default ["ca-ES-valencia", [["a. m.", "p. m."], u, u], u, [["dg", "dl", "dt", "dc", "dj", "dv", "ds"], ["dg.", "dl.", "dt.", "dc.", "dj.", "dv.", "ds."], ["diumenge", "dilluns", "dimarts", "dimecres", "dijous", "divendres", "dissabte"], ["dg.", "dl.", "dt.", "dc.", "dj.", "dv.", "ds."]], u, [["GN", "FB", "MÇ", "AB", "MG", "JN", "JL", "AG", "ST", "OC", "NV", "DS"], ["de gen.", "de febr.", "de març", "d’abr.", "de maig", "de juny", "de jul.", "d’ag.", "de set.", "d’oct.", "de nov.", "de des."], ["de gener", "de febrer", "de març", "d’abril", "de maig", "de juny", "de juliol", "d’agost", "de setembre", "d’octubre", "de novembre", "de desembre"]], [["GN", "FB", "MÇ", "AB", "MG", "JN", "JL", "AG", "ST", "OC", "NV", "DS"], ["gen.", "febr.", "març", "abr.", "maig", "juny", "jul.", "ag.", "set.", "oct.", "nov.", "des."], ["gener", "febrer", "març", "abril", "maig", "juny", "juliol", "agost", "setembre", "octubre", "novembre", "desembre"]], [["aC", "dC"], u, ["abans de Crist", "després de Crist"]], 1, [6, 0], ["d/M/yy", "d MMM y", "d MMMM 'de' y", "EEEE, d MMMM 'de' y"], ["H:mm", "H:mm:ss", "H:mm:ss z", "H:mm:ss (zzzz)"], ["{1} {0}", "{1}, {0}", "{1}, 'a' 'les' {0}", u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "#,##0.00 ¤", "#E0"], "EUR", "€", "euro", { "AUD": ["AU$", "$"], "BRL": [u, "R$"], "BYN": [u, "р."], "CAD": [u, "$"], "CNY": [u, "¥"], "ESP": ["₧"], "MXN": [u, "$"], "PHP": [u, "₱"], "THB": ["฿"], "USD": [u, "$"], "VEF": [u, "Bs F"], "XCD": [u, "$"], "XXX": [] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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