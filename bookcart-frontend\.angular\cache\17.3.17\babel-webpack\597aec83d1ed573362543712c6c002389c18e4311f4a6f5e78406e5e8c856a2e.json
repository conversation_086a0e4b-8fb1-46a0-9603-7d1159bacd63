{"ast": null, "code": "import { throwError } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport class AuthInterceptor {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  intercept(req, next) {\n    // Get the auth token from the service\n    const authToken = this.authService.getToken();\n    // Clone the request and add the authorization header if token exists\n    let authReq = req;\n    if (authToken) {\n      authReq = req.clone({\n        headers: req.headers.set('Authorization', `Bearer ${authToken}`)\n      });\n    }\n    // Send the cloned request with header to the next handler\n    return next.handle(authReq).pipe(catchError(error => {\n      if (error.status === 401) {\n        // Token expired or invalid, logout user\n        this.authService.logout();\n      } else if (error.status === 403) {\n        // Forbidden, redirect to unauthorized page\n        this.router.navigate(['/unauthorized']);\n      }\n      return throwError(() => error);\n    }));\n  }\n  static {\n    this.ɵfac = function AuthInterceptor_Factory(t) {\n      return new (t || AuthInterceptor)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthInterceptor,\n      factory: AuthInterceptor.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["throwError", "catchError", "AuthInterceptor", "constructor", "authService", "router", "intercept", "req", "next", "authToken", "getToken", "authReq", "clone", "headers", "set", "handle", "pipe", "error", "status", "logout", "navigate", "i0", "ɵɵinject", "i1", "AuthService", "i2", "Router", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Desktop\\BookCart\\bookcart-frontend\\src\\app\\shared\\interceptors\\auth.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse } from '@angular/common/http';\nimport { Observable, throwError } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { AuthService } from '../services/auth.service';\nimport { Router } from '@angular/router';\n\n@Injectable()\nexport class AuthInterceptor implements HttpInterceptor {\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  intercept(req: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<any>> {\n    // Get the auth token from the service\n    const authToken = this.authService.getToken();\n\n    // Clone the request and add the authorization header if token exists\n    let authReq = req;\n    if (authToken) {\n      authReq = req.clone({\n        headers: req.headers.set('Authorization', `Bearer ${authToken}`)\n      });\n    }\n\n    // Send the cloned request with header to the next handler\n    return next.handle(authReq).pipe(\n      catchError((error: HttpErrorResponse) => {\n        if (error.status === 401) {\n          // Token expired or invalid, logout user\n          this.authService.logout();\n        } else if (error.status === 403) {\n          // Forbidden, redirect to unauthorized page\n          this.router.navigate(['/unauthorized']);\n        }\n        return throwError(() => error);\n      })\n    );\n  }\n}\n"], "mappings": "AAEA,SAAqBA,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,QAAQ,gBAAgB;;;;AAK3C,OAAM,MAAOC,eAAe;EAE1BC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,SAASA,CAACC,GAAqB,EAAEC,IAAiB;IAChD;IACA,MAAMC,SAAS,GAAG,IAAI,CAACL,WAAW,CAACM,QAAQ,EAAE;IAE7C;IACA,IAAIC,OAAO,GAAGJ,GAAG;IACjB,IAAIE,SAAS,EAAE;MACbE,OAAO,GAAGJ,GAAG,CAACK,KAAK,CAAC;QAClBC,OAAO,EAAEN,GAAG,CAACM,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,UAAUL,SAAS,EAAE;OAChE,CAAC;;IAGJ;IACA,OAAOD,IAAI,CAACO,MAAM,CAACJ,OAAO,CAAC,CAACK,IAAI,CAC9Bf,UAAU,CAAEgB,KAAwB,IAAI;MACtC,IAAIA,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;QACxB;QACA,IAAI,CAACd,WAAW,CAACe,MAAM,EAAE;OAC1B,MAAM,IAAIF,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;QAC/B;QACA,IAAI,CAACb,MAAM,CAACe,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;;MAEzC,OAAOpB,UAAU,CAAC,MAAMiB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;;;uBAhCWf,eAAe,EAAAmB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAfxB,eAAe;MAAAyB,OAAA,EAAfzB,eAAe,CAAA0B;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}