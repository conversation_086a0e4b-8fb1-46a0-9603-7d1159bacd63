"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.formatDiagnostics = void 0;
const ts = require("typescript");
const virtual_host_1 = require("./virtual-host");
/** Formats the specified diagnostics with respect to the given file system. */
function formatDiagnostics(diagnostics, fileSystem) {
    const formatHost = (0, virtual_host_1.createFormatDiagnosticHost)(fileSystem);
    return ts.formatDiagnosticsWithColorAndContext(diagnostics, formatHost);
}
exports.formatDiagnostics = formatDiagnostics;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZGlhZ25vc3RpY3MuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi8uLi9zcmMvY2RrL3NjaGVtYXRpY3MvdXBkYXRlLXRvb2wvdXRpbHMvZGlhZ25vc3RpY3MudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUFBOzs7Ozs7R0FNRzs7O0FBRUgsaUNBQWlDO0FBRWpDLGlEQUEwRDtBQUUxRCwrRUFBK0U7QUFDL0UsU0FBZ0IsaUJBQWlCLENBQUMsV0FBNEIsRUFBRSxVQUFzQjtJQUNwRixNQUFNLFVBQVUsR0FBRyxJQUFBLHlDQUEwQixFQUFDLFVBQVUsQ0FBQyxDQUFDO0lBQzFELE9BQU8sRUFBRSxDQUFDLG9DQUFvQyxDQUFDLFdBQVcsRUFBRSxVQUFVLENBQUMsQ0FBQztBQUMxRSxDQUFDO0FBSEQsOENBR0MiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgR29vZ2xlIExMQyBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICpcbiAqIFVzZSBvZiB0aGlzIHNvdXJjZSBjb2RlIGlzIGdvdmVybmVkIGJ5IGFuIE1JVC1zdHlsZSBsaWNlbnNlIHRoYXQgY2FuIGJlXG4gKiBmb3VuZCBpbiB0aGUgTElDRU5TRSBmaWxlIGF0IGh0dHBzOi8vYW5ndWxhci5pby9saWNlbnNlXG4gKi9cblxuaW1wb3J0ICogYXMgdHMgZnJvbSAndHlwZXNjcmlwdCc7XG5pbXBvcnQge0ZpbGVTeXN0ZW19IGZyb20gJy4uL2ZpbGUtc3lzdGVtJztcbmltcG9ydCB7Y3JlYXRlRm9ybWF0RGlhZ25vc3RpY0hvc3R9IGZyb20gJy4vdmlydHVhbC1ob3N0JztcblxuLyoqIEZvcm1hdHMgdGhlIHNwZWNpZmllZCBkaWFnbm9zdGljcyB3aXRoIHJlc3BlY3QgdG8gdGhlIGdpdmVuIGZpbGUgc3lzdGVtLiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdERpYWdub3N0aWNzKGRpYWdub3N0aWNzOiB0cy5EaWFnbm9zdGljW10sIGZpbGVTeXN0ZW06IEZpbGVTeXN0ZW0pOiBzdHJpbmcge1xuICBjb25zdCBmb3JtYXRIb3N0ID0gY3JlYXRlRm9ybWF0RGlhZ25vc3RpY0hvc3QoZmlsZVN5c3RlbSk7XG4gIHJldHVybiB0cy5mb3JtYXREaWFnbm9zdGljc1dpdGhDb2xvckFuZENvbnRleHQoZGlhZ25vc3RpY3MsIGZvcm1hdEhvc3QpO1xufVxuIl19