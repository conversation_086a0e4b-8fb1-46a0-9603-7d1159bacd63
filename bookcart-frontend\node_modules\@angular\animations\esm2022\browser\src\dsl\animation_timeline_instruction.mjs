export function createTimelineInstruction(element, keyframes, preStyleProps, postStyleProps, duration, delay, easing = null, subTimeline = false) {
    return {
        type: 1 /* AnimationTransitionInstructionType.TimelineAnimation */,
        element,
        keyframes,
        preStyleProps,
        postStyleProps,
        duration,
        delay,
        totalTime: duration + delay,
        easing,
        subTimeline,
    };
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiYW5pbWF0aW9uX3RpbWVsaW5lX2luc3RydWN0aW9uLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vLi4vLi4vLi4vcGFja2FnZXMvYW5pbWF0aW9ucy9icm93c2VyL3NyYy9kc2wvYW5pbWF0aW9uX3RpbWVsaW5lX2luc3RydWN0aW9uLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQTJCQSxNQUFNLFVBQVUseUJBQXlCLENBQ3ZDLE9BQVksRUFDWixTQUErQixFQUMvQixhQUF1QixFQUN2QixjQUF3QixFQUN4QixRQUFnQixFQUNoQixLQUFhLEVBQ2IsU0FBd0IsSUFBSSxFQUM1QixjQUF1QixLQUFLO0lBRTVCLE9BQU87UUFDTCxJQUFJLDhEQUFzRDtRQUMxRCxPQUFPO1FBQ1AsU0FBUztRQUNULGFBQWE7UUFDYixjQUFjO1FBQ2QsUUFBUTtRQUNSLEtBQUs7UUFDTCxTQUFTLEVBQUUsUUFBUSxHQUFHLEtBQUs7UUFDM0IsTUFBTTtRQUNOLFdBQVc7S0FDWixDQUFDO0FBQ0osQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuaW1wb3J0IHvJtVN0eWxlRGF0YU1hcH0gZnJvbSAnQGFuZ3VsYXIvYW5pbWF0aW9ucyc7XG5cbmltcG9ydCB7XG4gIEFuaW1hdGlvbkVuZ2luZUluc3RydWN0aW9uLFxuICBBbmltYXRpb25UcmFuc2l0aW9uSW5zdHJ1Y3Rpb25UeXBlLFxufSBmcm9tICcuLi9yZW5kZXIvYW5pbWF0aW9uX2VuZ2luZV9pbnN0cnVjdGlvbic7XG5cbmV4cG9ydCBpbnRlcmZhY2UgQW5pbWF0aW9uVGltZWxpbmVJbnN0cnVjdGlvbiBleHRlbmRzIEFuaW1hdGlvbkVuZ2luZUluc3RydWN0aW9uIHtcbiAgZWxlbWVudDogYW55O1xuICBrZXlmcmFtZXM6IEFycmF5PMm1U3R5bGVEYXRhTWFwPjtcbiAgcHJlU3R5bGVQcm9wczogc3RyaW5nW107XG4gIHBvc3RTdHlsZVByb3BzOiBzdHJpbmdbXTtcbiAgZHVyYXRpb246IG51bWJlcjtcbiAgZGVsYXk6IG51bWJlcjtcbiAgdG90YWxUaW1lOiBudW1iZXI7XG4gIGVhc2luZzogc3RyaW5nIHwgbnVsbDtcbiAgc3RyZXRjaFN0YXJ0aW5nS2V5ZnJhbWU/OiBib29sZWFuO1xuICBzdWJUaW1lbGluZTogYm9vbGVhbjtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZVRpbWVsaW5lSW5zdHJ1Y3Rpb24oXG4gIGVsZW1lbnQ6IGFueSxcbiAga2V5ZnJhbWVzOiBBcnJheTzJtVN0eWxlRGF0YU1hcD4sXG4gIHByZVN0eWxlUHJvcHM6IHN0cmluZ1tdLFxuICBwb3N0U3R5bGVQcm9wczogc3RyaW5nW10sXG4gIGR1cmF0aW9uOiBudW1iZXIsXG4gIGRlbGF5OiBudW1iZXIsXG4gIGVhc2luZzogc3RyaW5nIHwgbnVsbCA9IG51bGwsXG4gIHN1YlRpbWVsaW5lOiBib29sZWFuID0gZmFsc2UsXG4pOiBBbmltYXRpb25UaW1lbGluZUluc3RydWN0aW9uIHtcbiAgcmV0dXJuIHtcbiAgICB0eXBlOiBBbmltYXRpb25UcmFuc2l0aW9uSW5zdHJ1Y3Rpb25UeXBlLlRpbWVsaW5lQW5pbWF0aW9uLFxuICAgIGVsZW1lbnQsXG4gICAga2V5ZnJhbWVzLFxuICAgIHByZVN0eWxlUHJvcHMsXG4gICAgcG9zdFN0eWxlUHJvcHMsXG4gICAgZHVyYXRpb24sXG4gICAgZGVsYXksXG4gICAgdG90YWxUaW1lOiBkdXJhdGlvbiArIGRlbGF5LFxuICAgIGVhc2luZyxcbiAgICBzdWJUaW1lbGluZSxcbiAgfTtcbn1cbiJdfQ==