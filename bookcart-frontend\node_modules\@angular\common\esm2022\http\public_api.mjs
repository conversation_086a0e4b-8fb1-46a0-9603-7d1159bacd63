/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export { HttpBackend, HttpHandler } from './src/backend';
export { HttpClient } from './src/client';
export { HttpContext, HttpContextToken } from './src/context';
export { FetchBackend } from './src/fetch';
export { HttpHeaders } from './src/headers';
export { HTTP_INTERCEPTORS, HttpInterceptorHandler as ɵHttpInterceptorHandler, HttpInterceptorHandler as ɵHttpInterceptingHandler, } from './src/interceptor';
export { JsonpClientBackend, JsonpInterceptor } from './src/jsonp';
export { HttpClientJsonpModule, HttpClientModule, HttpClientXsrfModule } from './src/module';
export { HttpParams, HttpUrlEncodingCodec, } from './src/params';
export { HttpFeatureKind, provideHttpClient, withFet<PERSON>, withInterceptors, withInterceptorsFromDi, withJsonpSupport, withNoXsrfProtection, withRequestsMadeViaParent, withXsrfConfiguration, } from './src/provider';
export { HttpRequest } from './src/request';
export { HttpErrorResponse, HttpEventType, HttpHeaderResponse, HttpResponse, HttpResponseBase, HttpStatusCode, } from './src/response';
export { withHttpTransferCache as ɵwithHttpTransferCache, } from './src/transfer_cache';
export { HttpXhrBackend } from './src/xhr';
export { HttpXsrfTokenExtractor } from './src/xsrf';
// Private exports
export * from './src/private_export';
//# sourceMappingURL=data:application/json;base64,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