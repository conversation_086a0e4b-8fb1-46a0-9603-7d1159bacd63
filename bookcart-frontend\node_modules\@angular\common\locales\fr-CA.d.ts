/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    AUD: string[];
    BEF: string[];
    BYN: (string | undefined)[];
    CAD: string[];
    CYP: string[];
    EGP: (string | undefined)[];
    FRF: string[];
    GEL: never[];
    HKD: string[];
    IEP: string[];
    ILP: string[];
    ILS: (string | undefined)[];
    INR: (string | undefined)[];
    ITL: string[];
    KRW: (string | undefined)[];
    LBP: (string | undefined)[];
    MTP: string[];
    MXN: (string | undefined)[];
    NZD: string[];
    PHP: (string | undefined)[];
    RHD: string[];
    RON: (string | undefined)[];
    RWF: (string | undefined)[];
    SGD: string[];
    TOP: (string | undefined)[];
    TWD: (string | undefined)[];
    USD: string[];
    VND: (string | undefined)[];
    XAF: never[];
    XCD: (string | undefined)[];
    XOF: never[];
    XPF: never[];
} | undefined)[];
export default _default;
