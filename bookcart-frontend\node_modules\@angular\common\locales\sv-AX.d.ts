/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    AUD: (string | undefined)[];
    BBD: string[];
    BMD: string[];
    BRL: string[];
    BSD: string[];
    BYN: (string | undefined)[];
    BZD: string[];
    CNY: (string | undefined)[];
    DKK: string[];
    DOP: string[];
    EEK: string[];
    EGP: string[];
    ESP: never[];
    GBP: (string | undefined)[];
    HKD: (string | undefined)[];
    IEP: string[];
    INR: (string | undefined)[];
    ISK: string[];
    JMD: string[];
    JPY: (string | undefined)[];
    KRW: (string | undefined)[];
    NOK: string[];
    NZD: (string | undefined)[];
    PHP: (string | undefined)[];
    RON: (string | undefined)[];
    SEK: string[];
    TWD: (string | undefined)[];
    USD: string[];
    VND: (string | undefined)[];
} | undefined)[];
export default _default;
