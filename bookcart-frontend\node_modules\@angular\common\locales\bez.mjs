/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["bez", [["pamilau", "pamunyi"], u, u], u, [["M", "J", "H", "H", "H", "W", "J"], ["Mul", "Vil", "Hiv", "Hid", "Hit", "Hih", "Lem"], ["pa mulungu", "pa shahuviluha", "pa hivili", "pa hidatu", "pa hitayi", "pa hihanu", "pa shahulembela"], ["Mul", "Vil", "Hiv", "Hid", "<PERSON>", "<PERSON>h", "<PERSON><PERSON>"]], u, [["<PERSON>", "V", "D", "T", "H", "S", "S", "N", "T", "K", "K", "K"], ["Hut", "Vil", "Dat", "<PERSON>", "<PERSON>", "Sit", "Sab", "Nan", "Tis", "Kum", "Kmj", "Kmb"], ["pa mwedzi gwa hutala", "pa mwedzi gwa wuvili", "pa mwedzi gwa wudatu", "pa mwedzi gwa wutai", "pa mwedzi gwa wuhanu", "pa mwedzi gwa sita", "pa mwedzi gwa saba", "pa mwedzi gwa nane", "pa mwedzi gwa tisa", "pa mwedzi gwa kumi", "pa mwedzi gwa kumi na moja", "pa mwedzi gwa kumi na mbili"]], u, [["KM", "BM"], u, ["Kabla ya Mtwaa", "Baada ya Mtwaa"]], 1, [6, 0], ["dd/MM/y", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "#,##0.00¤", "#E0"], "TZS", "TSh", "Shilingi ya Hutanzania", { "JPY": ["JP¥", "¥"], "TZS": ["TSh"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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