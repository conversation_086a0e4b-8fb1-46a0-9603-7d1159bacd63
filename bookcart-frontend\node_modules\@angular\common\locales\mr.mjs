/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["mr", [["a", "p"], ["AM", "PM"], u], [["AM", "PM"], u, u], [["र", "सो", "मं", "बु", "गु", "शु", "श"], ["रवि", "सोम", "मंगळ", "बुध", "गुरु", "शुक्र", "शनि"], ["रविवार", "सोमवार", "मंगळवार", "बुधवार", "गुरुवार", "शुक्रवार", "शनिवार"], ["र", "सो", "मं", "बु", "गु", "शु", "श"]], u, [["जा", "फे", "मा", "ए", "मे", "जू", "जु", "ऑ", "स", "ऑ", "नो", "डि"], ["जाने", "फेब्रु", "मार्च", "एप्रि", "मे", "जून", "जुलै", "ऑग", "सप्टें", "ऑक्टो", "नोव्हें", "डिसें"], ["जानेवारी", "फेब्रुवारी", "मार्च", "एप्रिल", "मे", "जून", "जुलै", "ऑगस्ट", "सप्टेंबर", "ऑक्टोबर", "नोव्हेंबर", "डिसेंबर"]], u, [["इ. स. पू.", "इ. स."], u, ["ईसवीसनपूर्व", "ईसवीसन"]], 0, [0, 0], ["d/M/yy", "d MMM, y", "d MMMM, y", "EEEE, d MMMM, y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1}, {0}", u, "{1} रोजी {0}", u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##,##0.###", "#,##0%", "¤#,##0.00", "[#E0]"], "INR", "₹", "भारतीय रुपया", { "JPY": ["JP¥", "¥"], "PHP": [u, "₱"], "THB": ["฿"], "TWD": ["NT$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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