{"ast": null, "code": "import { BehaviorSubject, tap } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class CartService {\n  constructor(http) {\n    this.http = http;\n    this.API_URL = 'http://localhost:5001/api/cart';\n    this.cartSubject = new BehaviorSubject(null);\n    this.cart$ = this.cartSubject.asObservable();\n  }\n  // Get current user's cart\n  getMyCart() {\n    return this.http.get(`${this.API_URL}/my-cart`).pipe(tap(cart => this.cartSubject.next(cart)));\n  }\n  // Add item to cart\n  addToCart(request) {\n    return this.http.post(`${this.API_URL}/add`, request).pipe(tap(cart => this.cartSubject.next(cart)));\n  }\n  // Update cart item quantity\n  updateCartItem(cartItemId, request) {\n    return this.http.put(`${this.API_URL}/item/${cartItemId}`, request).pipe(tap(cart => this.cartSubject.next(cart)));\n  }\n  // Remove item from cart\n  removeFromCart(cartItemId) {\n    return this.http.delete(`${this.API_URL}/item/${cartItemId}`).pipe(tap(() => {\n      // Refresh cart after removal\n      this.getMyCart().subscribe();\n    }));\n  }\n  // Clear entire cart\n  clearCart() {\n    return this.http.delete(`${this.API_URL}/clear`).pipe(tap(() => this.cartSubject.next(null)));\n  }\n  // Get current cart value\n  getCurrentCart() {\n    return this.cartSubject.value;\n  }\n  // Get cart item count\n  getCartItemCount() {\n    const cart = this.cartSubject.value;\n    return cart ? cart.cartItems.reduce((total, item) => total + item.quantity, 0) : 0;\n  }\n  // Get cart total amount\n  getCartTotal() {\n    const cart = this.cartSubject.value;\n    return cart ? cart.totalAmount : 0;\n  }\n  static {\n    this.ɵfac = function CartService_Factory(t) {\n      return new (t || CartService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CartService,\n      factory: CartService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "tap", "CartService", "constructor", "http", "API_URL", "cartSubject", "cart$", "asObservable", "getMyCart", "get", "pipe", "cart", "next", "addToCart", "request", "post", "updateCartItem", "cartItemId", "put", "removeFromCart", "delete", "subscribe", "clearCart", "getCurrentCart", "value", "getCartItemCount", "cartItems", "reduce", "total", "item", "quantity", "getCartTotal", "totalAmount", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\BookCart\\bookcart-frontend\\src\\app\\shared\\services\\cart.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { BehaviorSubject, Observable, tap } from 'rxjs';\n\nexport interface CartItem {\n  cartItemId: number;\n  cartId: number;\n  bookId: number;\n  bookTitle: string;\n  bookAuthor: string;\n  bookImageUrl?: string;\n  quantity: number;\n  unitPrice: number;\n  totalPrice: number;\n  addedDate: string;\n}\n\nexport interface Cart {\n  cartId: number;\n  userId: string;\n  createdDate: string;\n  lastModified: string;\n  cartItems: CartItem[];\n  totalAmount: number;\n}\n\nexport interface AddToCartRequest {\n  bookId: number;\n  quantity: number;\n}\n\nexport interface UpdateCartItemRequest {\n  quantity: number;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class CartService {\n  private readonly API_URL = 'http://localhost:5001/api/cart';\n  private cartSubject = new BehaviorSubject<Cart | null>(null);\n  public cart$ = this.cartSubject.asObservable();\n\n  constructor(private http: HttpClient) { }\n\n  // Get current user's cart\n  getMyCart(): Observable<Cart> {\n    return this.http.get<Cart>(`${this.API_URL}/my-cart`)\n      .pipe(\n        tap(cart => this.cartSubject.next(cart))\n      );\n  }\n\n  // Add item to cart\n  addToCart(request: AddToCartRequest): Observable<Cart> {\n    return this.http.post<Cart>(`${this.API_URL}/add`, request)\n      .pipe(\n        tap(cart => this.cartSubject.next(cart))\n      );\n  }\n\n  // Update cart item quantity\n  updateCartItem(cartItemId: number, request: UpdateCartItemRequest): Observable<Cart> {\n    return this.http.put<Cart>(`${this.API_URL}/item/${cartItemId}`, request)\n      .pipe(\n        tap(cart => this.cartSubject.next(cart))\n      );\n  }\n\n  // Remove item from cart\n  removeFromCart(cartItemId: number): Observable<void> {\n    return this.http.delete<void>(`${this.API_URL}/item/${cartItemId}`)\n      .pipe(\n        tap(() => {\n          // Refresh cart after removal\n          this.getMyCart().subscribe();\n        })\n      );\n  }\n\n  // Clear entire cart\n  clearCart(): Observable<void> {\n    return this.http.delete<void>(`${this.API_URL}/clear`)\n      .pipe(\n        tap(() => this.cartSubject.next(null))\n      );\n  }\n\n  // Get current cart value\n  getCurrentCart(): Cart | null {\n    return this.cartSubject.value;\n  }\n\n  // Get cart item count\n  getCartItemCount(): number {\n    const cart = this.cartSubject.value;\n    return cart ? cart.cartItems.reduce((total, item) => total + item.quantity, 0) : 0;\n  }\n\n  // Get cart total amount\n  getCartTotal(): number {\n    const cart = this.cartSubject.value;\n    return cart ? cart.totalAmount : 0;\n  }\n}\n"], "mappings": "AAEA,SAASA,eAAe,EAAcC,GAAG,QAAQ,MAAM;;;AAoCvD,OAAM,MAAOC,WAAW;EAKtBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAJP,KAAAC,OAAO,GAAG,gCAAgC;IACnD,KAAAC,WAAW,GAAG,IAAIN,eAAe,CAAc,IAAI,CAAC;IACrD,KAAAO,KAAK,GAAG,IAAI,CAACD,WAAW,CAACE,YAAY,EAAE;EAEN;EAExC;EACAC,SAASA,CAAA;IACP,OAAO,IAAI,CAACL,IAAI,CAACM,GAAG,CAAO,GAAG,IAAI,CAACL,OAAO,UAAU,CAAC,CAClDM,IAAI,CACHV,GAAG,CAACW,IAAI,IAAI,IAAI,CAACN,WAAW,CAACO,IAAI,CAACD,IAAI,CAAC,CAAC,CACzC;EACL;EAEA;EACAE,SAASA,CAACC,OAAyB;IACjC,OAAO,IAAI,CAACX,IAAI,CAACY,IAAI,CAAO,GAAG,IAAI,CAACX,OAAO,MAAM,EAAEU,OAAO,CAAC,CACxDJ,IAAI,CACHV,GAAG,CAACW,IAAI,IAAI,IAAI,CAACN,WAAW,CAACO,IAAI,CAACD,IAAI,CAAC,CAAC,CACzC;EACL;EAEA;EACAK,cAAcA,CAACC,UAAkB,EAAEH,OAA8B;IAC/D,OAAO,IAAI,CAACX,IAAI,CAACe,GAAG,CAAO,GAAG,IAAI,CAACd,OAAO,SAASa,UAAU,EAAE,EAAEH,OAAO,CAAC,CACtEJ,IAAI,CACHV,GAAG,CAACW,IAAI,IAAI,IAAI,CAACN,WAAW,CAACO,IAAI,CAACD,IAAI,CAAC,CAAC,CACzC;EACL;EAEA;EACAQ,cAAcA,CAACF,UAAkB;IAC/B,OAAO,IAAI,CAACd,IAAI,CAACiB,MAAM,CAAO,GAAG,IAAI,CAAChB,OAAO,SAASa,UAAU,EAAE,CAAC,CAChEP,IAAI,CACHV,GAAG,CAAC,MAAK;MACP;MACA,IAAI,CAACQ,SAAS,EAAE,CAACa,SAAS,EAAE;IAC9B,CAAC,CAAC,CACH;EACL;EAEA;EACAC,SAASA,CAAA;IACP,OAAO,IAAI,CAACnB,IAAI,CAACiB,MAAM,CAAO,GAAG,IAAI,CAAChB,OAAO,QAAQ,CAAC,CACnDM,IAAI,CACHV,GAAG,CAAC,MAAM,IAAI,CAACK,WAAW,CAACO,IAAI,CAAC,IAAI,CAAC,CAAC,CACvC;EACL;EAEA;EACAW,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAClB,WAAW,CAACmB,KAAK;EAC/B;EAEA;EACAC,gBAAgBA,CAAA;IACd,MAAMd,IAAI,GAAG,IAAI,CAACN,WAAW,CAACmB,KAAK;IACnC,OAAOb,IAAI,GAAGA,IAAI,CAACe,SAAS,CAACC,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAKD,KAAK,GAAGC,IAAI,CAACC,QAAQ,EAAE,CAAC,CAAC,GAAG,CAAC;EACpF;EAEA;EACAC,YAAYA,CAAA;IACV,MAAMpB,IAAI,GAAG,IAAI,CAACN,WAAW,CAACmB,KAAK;IACnC,OAAOb,IAAI,GAAGA,IAAI,CAACqB,WAAW,GAAG,CAAC;EACpC;;;uBAjEW/B,WAAW,EAAAgC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAXnC,WAAW;MAAAoC,OAAA,EAAXpC,WAAW,CAAAqC,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}