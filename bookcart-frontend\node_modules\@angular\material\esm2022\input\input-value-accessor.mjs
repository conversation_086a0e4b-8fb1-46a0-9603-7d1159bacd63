/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { InjectionToken } from '@angular/core';
/**
 * This token is used to inject the object whose value should be set into `MatInput`. If none is
 * provided, the native `HTMLInputElement` is used. Directives like `MatDatepickerInput` can provide
 * themselves for this token, in order to make `MatInput` delegate the getting and setting of the
 * value to them.
 */
export const MAT_INPUT_VALUE_ACCESSOR = new InjectionToken('MAT_INPUT_VALUE_ACCESSOR');
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5wdXQtdmFsdWUtYWNjZXNzb3IuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi9zcmMvbWF0ZXJpYWwvaW5wdXQvaW5wdXQtdmFsdWUtYWNjZXNzb3IudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7OztHQU1HO0FBRUgsT0FBTyxFQUFDLGNBQWMsRUFBQyxNQUFNLGVBQWUsQ0FBQztBQUU3Qzs7Ozs7R0FLRztBQUNILE1BQU0sQ0FBQyxNQUFNLHdCQUF3QixHQUFHLElBQUksY0FBYyxDQUN4RCwwQkFBMEIsQ0FDM0IsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuXG5pbXBvcnQge0luamVjdGlvblRva2VufSBmcm9tICdAYW5ndWxhci9jb3JlJztcblxuLyoqXG4gKiBUaGlzIHRva2VuIGlzIHVzZWQgdG8gaW5qZWN0IHRoZSBvYmplY3Qgd2hvc2UgdmFsdWUgc2hvdWxkIGJlIHNldCBpbnRvIGBNYXRJbnB1dGAuIElmIG5vbmUgaXNcbiAqIHByb3ZpZGVkLCB0aGUgbmF0aXZlIGBIVE1MSW5wdXRFbGVtZW50YCBpcyB1c2VkLiBEaXJlY3RpdmVzIGxpa2UgYE1hdERhdGVwaWNrZXJJbnB1dGAgY2FuIHByb3ZpZGVcbiAqIHRoZW1zZWx2ZXMgZm9yIHRoaXMgdG9rZW4sIGluIG9yZGVyIHRvIG1ha2UgYE1hdElucHV0YCBkZWxlZ2F0ZSB0aGUgZ2V0dGluZyBhbmQgc2V0dGluZyBvZiB0aGVcbiAqIHZhbHVlIHRvIHRoZW0uXG4gKi9cbmV4cG9ydCBjb25zdCBNQVRfSU5QVVRfVkFMVUVfQUNDRVNTT1IgPSBuZXcgSW5qZWN0aW9uVG9rZW48e3ZhbHVlOiBhbnl9PihcbiAgJ01BVF9JTlBVVF9WQUxVRV9BQ0NFU1NPUicsXG4pO1xuIl19