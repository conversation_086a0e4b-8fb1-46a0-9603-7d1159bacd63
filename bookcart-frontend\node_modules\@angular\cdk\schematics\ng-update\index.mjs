"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateToV17 = void 0;
const target_version_1 = require("../update-tool/target-version");
const upgrade_data_1 = require("./upgrade-data");
const devkit_migration_rule_1 = require("./devkit-migration-rule");
const cdkMigrations = [];
/** Entry point for the migration schematics with target of Angular CDK 17.0.0 */
function updateToV17() {
    return (0, devkit_migration_rule_1.createMigrationSchematicRule)(target_version_1.TargetVersion.V17, cdkMigrations, upgrade_data_1.cdkUpgradeData, onMigrationComplete);
}
exports.updateToV17 = updateToV17;
/** Function that will be called when the migration completed. */
function onMigrationComplete(context, targetVersion, hasFailures) {
    context.logger.info('');
    context.logger.info(`  ✓  Updated Angular CDK to ${targetVersion}`);
    context.logger.info('');
    if (hasFailures) {
        context.logger.warn('  ⚠  Some issues were detected but could not be fixed automatically. Please check the ' +
            'output above and fix these issues manually.');
    }
}
//# sourceMappingURL=data:application/json;base64,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