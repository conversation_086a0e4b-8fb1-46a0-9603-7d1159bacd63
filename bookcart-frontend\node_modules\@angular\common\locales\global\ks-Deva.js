/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['ks-deva'] = ["ks-Deva",[["AM","PM"],u,u],u,[["अ","च","ब","ब","ब","ज","ब"],["आथवार","चंदिरवार","बुवार","बोदवार","ब्रेसवार","जुम्मा","बटवार"],u,u],u,[["ज","फ़","म","अ","म","ज","ज","अ","स","ओ","न","द"],["जनवरी","फ़रवरी","मार्च","अप्रैल","मे","जून","जुलाई","अगस्त","सतुंबर","अकतुम्बर","नवूमबर","दसूमबर"],u],u,[["BC","AD"],u,u],0,[0,0],["d/M/yy","d MMM y","d MMMM y","EEEE, d MMMM y"],["a h:mm","a h:mm:ss","a h:mm:ss z","a h:mm:ss zzzz"],["{1}, {0}",u,"{0} पेठ {1}",u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤ #,##0.00","#E0"],"INR","₹","इंडियन रूपी",{"JPY":["JP¥","¥"]},"ltr", plural, []];
  })(globalThis);
    