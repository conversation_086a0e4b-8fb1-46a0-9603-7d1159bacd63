/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val, i = Math.floor(Math.abs(val));
    if (i === 0 || i === 1)
        return 1;
    return 5;
}
export default ["hy", [["ա", "հ"], ["AM", "PM"], u], [["AM", "PM"], u, u], [["Կ", "Ե", "Ե", "Չ", "Հ", "Ո", "Շ"], ["կիր", "երկ", "երք", "չրք", "հնգ", "ուր", "շբթ"], ["կիրակի", "երկուշաբթի", "երեքշաբթի", "չորեքշաբթի", "հինգշաբթի", "ուրբաթ", "շաբաթ"], ["կր", "եկ", "եք", "չք", "հգ", "ու", "շբ"]], u, [["Հ", "Փ", "Մ", "Ա", "Մ", "Հ", "Հ", "Օ", "Ս", "Հ", "Ն", "Դ"], ["հնվ", "փտվ", "մրտ", "ապր", "մյս", "հնս", "հլս", "օգս", "սեպ", "հոկ", "նոյ", "դեկ"], ["հունվարի", "փետրվարի", "մարտի", "ապրիլի", "մայիսի", "հունիսի", "հուլիսի", "օգոստոսի", "սեպտեմբերի", "հոկտեմբերի", "նոյեմբերի", "դեկտեմբերի"]], [["Հ", "Փ", "Մ", "Ա", "Մ", "Հ", "Հ", "Օ", "Ս", "Հ", "Ն", "Դ"], ["հնվ", "փտվ", "մրտ", "ապր", "մյս", "հնս", "հլս", "օգս", "սեպ", "հոկ", "նոյ", "դեկ"], ["հունվար", "փետրվար", "մարտ", "ապրիլ", "մայիս", "հունիս", "հուլիս", "օգոստոս", "սեպտեմբեր", "հոկտեմբեր", "նոյեմբեր", "դեկտեմբեր"]], [["մ.թ.ա.", "մ.թ."], u, ["Քրիստոսից առաջ", "Քրիստոսից հետո"]], 1, [6, 0], ["dd.MM.yy", "dd MMM, y թ.", "dd MMMM, y թ.", "y թ. MMMM d, EEEE"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1}, {0}", u, u, u], [",", " ", ";", "%", "+", "-", "E", "×", "‰", "∞", "ՈչԹ", ":"], ["#,##0.###", "#,##0%", "#,##0.00 ¤", "#E0"], "AMD", "֏", "հայկական դրամ", { "AMD": ["֏"], "BYN": [u, "р."], "JPY": ["JP¥", "¥"], "PHP": [u, "₱"], "THB": ["฿"], "TWD": ["NT$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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**********************************************************/VpdW01aLVpdaAXCJdXSxbW1wi1bQu1aku1aEuXCIsXCLVtC7VqS5cIl0sdSxbXCLVlNaA1avVvdW/1bjVvdWr1oEg1aHVvNWh1btcIixcItWU1oDVq9W91b/VuNW91avWgSDVsNWl1b/VuFwiXV0sMSxbNiwwXSxbXCJkZC5NTS55eVwiLFwiZGQgTU1NLCB5INWpLlwiLFwiZGQgTU1NTSwgeSDVqS5cIixcInkg1akuIE1NTU0gZCwgRUVFRVwiXSxbXCJISDptbVwiLFwiSEg6bW06c3NcIixcIkhIOm1tOnNzIHpcIixcIkhIOm1tOnNzIHp6enpcIl0sW1wiezF9LCB7MH1cIix1LHUsdV0sW1wiLFwiLFwiwqBcIixcIjtcIixcIiVcIixcIitcIixcIi1cIixcIkVcIixcIsOXXCIsXCLigLBcIixcIuKInlwiLFwi1YjVudS5XCIsXCI6XCJdLFtcIiMsIyMwLiMjI1wiLFwiIywjIzAlXCIsXCIjLCMjMC4wMMKgwqRcIixcIiNFMFwiXSxcIkFNRFwiLFwi1o9cIixcItWw1aHVtdWv1aHVr9Wh1bYg1aTWgNWh1bRcIix7XCJBTURcIjpbXCLWj1wiXSxcIkJZTlwiOlt1LFwi0YAuXCJdLFwiSlBZXCI6W1wiSlDCpVwiLFwiwqVcIl0sXCJQSFBcIjpbdSxcIuKCsVwiXSxcIlRIQlwiOltcIuC4v1wiXSxcIlRXRFwiOltcIk5UJFwiXX0sXCJsdHJcIiwgcGx1cmFsXTtcbiJdfQ==