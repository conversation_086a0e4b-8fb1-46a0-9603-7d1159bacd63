/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['nmg'] = ["nmg",[["maná","kugú"],u,u],u,[["s","m","s","s","s","m","s"],["sɔ́n","mɔ́n","smb","sml","smn","mbs","sas"],["sɔ́ndɔ","mɔ́ndɔ","sɔ́ndɔ mafú mába","sɔ́ndɔ mafú málal","sɔ́ndɔ mafú mána","mab<PERSON><PERSON><PERSON> má sukul","sásadi"],["sɔ́n","mɔ́n","smb","sml","smn","mbs","sas"]],u,[["1","2","3","4","5","6","7","8","9","10","11","12"],["ng1","ng2","ng3","ng4","ng5","ng6","ng7","ng8","ng9","ng10","ng11","kris"],["ngwɛn matáhra","ngwɛn ńmba","ngwɛn ńlal","ngwɛn ńna","ngwɛn ńtan","ngwɛn ńtuó","ngwɛn hɛmbuɛrí","ngwɛn lɔmbi","ngwɛn rɛbvuâ","ngwɛn wum","ngwɛn wum navǔr","krísimin"]],u,[["BL","PB"],u,["Bó Lahlɛ̄","Pfiɛ Burī"]],1,[6,0],["d/M/y","d MMM y","d MMMM y","EEEE d MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[","," ",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","#,##0.00 ¤","#E0"],"XAF","FCFA","Fraŋ CFA BEAC",{"JPY":["JP¥","¥"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    