/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { DOCUMENT, PlatformLocation } from '@angular/common';
import { inject } from '@angular/core';
// @ng_package: ignore-cross-repo-import
import { PlatformNavigation } from '../../../src/navigation/platform_navigation';
import { FakeNavigationPlatformLocation, MOCK_PLATFORM_LOCATION_CONFIG, } from '../mock_platform_location';
import { FakeNavigation } from './fake_navigation';
/**
 * Return a provider for the `FakeNavigation` in place of the real Navigation API.
 */
export function provideFakePlatformNavigation() {
    return [
        {
            provide: PlatformNavigation,
            useFactory: () => {
                const config = inject(MOCK_PLATFORM_LOCATION_CONFIG, { optional: true });
                return new FakeNavigation(inject(DOCUMENT).defaultView, config?.startUrl ?? 'http://_empty_/');
            },
        },
        { provide: PlatformLocation, useClass: FakeNavigationPlatformLocation },
    ];
}
//# sourceMappingURL=data:application/json;base64,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