/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['nus'] = ["nus",[["RW","TŊ"],u,u],u,[["C","J","R","D","Ŋ","D","B"],["<PERSON>äŋ","Ji<PERSON>","Rɛw","Diɔ̱k","Ŋuaan","<PERSON>hiee<PERSON>","Bäkɛl"],["<PERSON>äŋ kuɔth","Ji<PERSON> la̱t","<PERSON>ɛw lätni","Diɔ̱k lätni","Ŋuaan lätni","<PERSON><PERSON><PERSON><PERSON> lätni","<PERSON><PERSON><PERSON><PERSON><PERSON> lätni"],["<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON>ɛw","Diɔ̱k","Ŋuaan","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON>kɛl"]],u,[["T","P","D","G","<PERSON>","K","P","T","T","L","K","T"],["Tiop","Pɛt","Duɔ̱ɔ̱","Guak","Duä","Kor","Pay","Thoo","Tɛɛ","Laa","Kur","Tid"],["Tiop thar pɛt","Pɛt","Duɔ̱ɔ̱ŋ","Guak","Duät","Kornyoot","Pay yie̱tni","Tho̱o̱r","Tɛɛr","Laath","Kur","Tio̱p in di̱i̱t"]],u,[["AY","ƐY"],u,["A ka̱n Yecu ni dap","Ɛ ca Yecu dap"]],1,[6,0],["d/MM/y","d MMM y","d MMMM y","EEEE d MMMM y"],["h:mm a","h:mm:ss a","z h:mm:ss a","zzzz h:mm:ss a"],["{1} {0}",u,u,u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤#,##0.00","#E0"],"SSP","£","SSP",{"GBP":["GB£","£"],"JPY":["JP¥","¥"],"SSP":["£"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    