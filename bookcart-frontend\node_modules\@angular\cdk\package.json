{"name": "@angular/cdk", "version": "17.3.10", "description": "Angular Material Component Development Kit", "repository": {"type": "git", "url": "https://github.com/angular/components.git"}, "keywords": ["angular", "cdk", "component", "development", "kit"], "license": "MIT", "bugs": {"url": "https://github.com/angular/components/issues"}, "homepage": "https://github.com/angular/components#readme", "exports": {".": {"sass": "./_index.scss", "types": "./index.d.ts", "esm2022": "./esm2022/index.mjs", "esm": "./esm2022/index.mjs", "default": "./fesm2022/cdk.mjs"}, "./a11y-prebuilt.css": {"style": "./a11y-prebuilt.css"}, "./a11y-prebuilt": {"style": "./a11y-prebuilt.css"}, "./overlay-prebuilt.css": {"style": "./overlay-prebuilt.css"}, "./overlay-prebuilt": {"style": "./overlay-prebuilt.css"}, "./text-field-prebuilt.css": {"style": "./text-field-prebuilt.css"}, "./text-field-prebuilt": {"style": "./text-field-prebuilt.css"}, "./schematics": {"default": "./schematics/index.js"}, "./package.json": {"default": "./package.json"}, "./a11y": {"types": "./a11y/index.d.ts", "esm2022": "./esm2022/a11y/a11y_public_index.mjs", "esm": "./esm2022/a11y/a11y_public_index.mjs", "default": "./fesm2022/a11y.mjs"}, "./accordion": {"types": "./accordion/index.d.ts", "esm2022": "./esm2022/accordion/accordion_public_index.mjs", "esm": "./esm2022/accordion/accordion_public_index.mjs", "default": "./fesm2022/accordion.mjs"}, "./bidi": {"types": "./bidi/index.d.ts", "esm2022": "./esm2022/bidi/bidi_public_index.mjs", "esm": "./esm2022/bidi/bidi_public_index.mjs", "default": "./fesm2022/bidi.mjs"}, "./clipboard": {"types": "./clipboard/index.d.ts", "esm2022": "./esm2022/clipboard/clipboard_public_index.mjs", "esm": "./esm2022/clipboard/clipboard_public_index.mjs", "default": "./fesm2022/clipboard.mjs"}, "./coercion": {"types": "./coercion/index.d.ts", "esm2022": "./esm2022/coercion/index.mjs", "esm": "./esm2022/coercion/index.mjs", "default": "./fesm2022/coercion.mjs"}, "./collections": {"types": "./collections/index.d.ts", "esm2022": "./esm2022/collections/collections_public_index.mjs", "esm": "./esm2022/collections/collections_public_index.mjs", "default": "./fesm2022/collections.mjs"}, "./dialog": {"types": "./dialog/index.d.ts", "esm2022": "./esm2022/dialog/dialog_public_index.mjs", "esm": "./esm2022/dialog/dialog_public_index.mjs", "default": "./fesm2022/dialog.mjs"}, "./drag-drop": {"types": "./drag-drop/index.d.ts", "esm2022": "./esm2022/drag-drop/drag-drop_public_index.mjs", "esm": "./esm2022/drag-drop/drag-drop_public_index.mjs", "default": "./fesm2022/drag-drop.mjs"}, "./keycodes": {"types": "./keycodes/index.d.ts", "esm2022": "./esm2022/keycodes/keycodes_public_index.mjs", "esm": "./esm2022/keycodes/keycodes_public_index.mjs", "default": "./fesm2022/keycodes.mjs"}, "./layout": {"types": "./layout/index.d.ts", "esm2022": "./esm2022/layout/layout_public_index.mjs", "esm": "./esm2022/layout/layout_public_index.mjs", "default": "./fesm2022/layout.mjs"}, "./listbox": {"types": "./listbox/index.d.ts", "esm2022": "./esm2022/listbox/listbox_public_index.mjs", "esm": "./esm2022/listbox/listbox_public_index.mjs", "default": "./fesm2022/listbox.mjs"}, "./menu": {"types": "./menu/index.d.ts", "esm2022": "./esm2022/menu/menu_public_index.mjs", "esm": "./esm2022/menu/menu_public_index.mjs", "default": "./fesm2022/menu.mjs"}, "./observers": {"types": "./observers/index.d.ts", "esm2022": "./esm2022/observers/observers_public_index.mjs", "esm": "./esm2022/observers/observers_public_index.mjs", "default": "./fesm2022/observers.mjs"}, "./observers/private": {"types": "./observers/private/index.d.ts", "esm2022": "./esm2022/observers/private/private_public_index.mjs", "esm": "./esm2022/observers/private/private_public_index.mjs", "default": "./fesm2022/observers/private.mjs"}, "./overlay": {"types": "./overlay/index.d.ts", "esm2022": "./esm2022/overlay/overlay_public_index.mjs", "esm": "./esm2022/overlay/overlay_public_index.mjs", "default": "./fesm2022/overlay.mjs"}, "./platform": {"types": "./platform/index.d.ts", "esm2022": "./esm2022/platform/platform_public_index.mjs", "esm": "./esm2022/platform/platform_public_index.mjs", "default": "./fesm2022/platform.mjs"}, "./portal": {"types": "./portal/index.d.ts", "esm2022": "./esm2022/portal/portal_public_index.mjs", "esm": "./esm2022/portal/portal_public_index.mjs", "default": "./fesm2022/portal.mjs"}, "./scrolling": {"types": "./scrolling/index.d.ts", "esm2022": "./esm2022/scrolling/scrolling_public_index.mjs", "esm": "./esm2022/scrolling/scrolling_public_index.mjs", "default": "./fesm2022/scrolling.mjs"}, "./stepper": {"types": "./stepper/index.d.ts", "esm2022": "./esm2022/stepper/stepper_public_index.mjs", "esm": "./esm2022/stepper/stepper_public_index.mjs", "default": "./fesm2022/stepper.mjs"}, "./table": {"types": "./table/index.d.ts", "esm2022": "./esm2022/table/table_public_index.mjs", "esm": "./esm2022/table/table_public_index.mjs", "default": "./fesm2022/table.mjs"}, "./testing": {"types": "./testing/index.d.ts", "esm2022": "./esm2022/testing/index.mjs", "esm": "./esm2022/testing/index.mjs", "default": "./fesm2022/testing.mjs"}, "./testing/selenium-webdriver": {"types": "./testing/selenium-webdriver/index.d.ts", "esm2022": "./esm2022/testing/selenium-webdriver/index.mjs", "esm": "./esm2022/testing/selenium-webdriver/index.mjs", "default": "./fesm2022/testing/selenium-webdriver.mjs"}, "./testing/testbed": {"types": "./testing/testbed/index.d.ts", "esm2022": "./esm2022/testing/testbed/index.mjs", "esm": "./esm2022/testing/testbed/index.mjs", "default": "./fesm2022/testing/testbed.mjs"}, "./text-field": {"types": "./text-field/index.d.ts", "esm2022": "./esm2022/text-field/text-field_public_index.mjs", "esm": "./esm2022/text-field/text-field_public_index.mjs", "default": "./fesm2022/text-field.mjs"}, "./tree": {"types": "./tree/index.d.ts", "esm2022": "./esm2022/tree/tree_public_index.mjs", "esm": "./esm2022/tree/tree_public_index.mjs", "default": "./fesm2022/tree.mjs"}}, "peerDependencies": {"@angular/core": "^17.0.0 || ^18.0.0", "@angular/common": "^17.0.0 || ^18.0.0", "rxjs": "^6.5.3 || ^7.4.0"}, "dependencies": {"tslib": "^2.3.0"}, "optionalDependencies": {"parse5": "^7.1.2"}, "schematics": "./schematics/collection.json", "ng-update": {"migrations": "./schematics/migration.json"}, "sideEffects": false, "module": "./fesm2022/cdk.mjs", "typings": "./index.d.ts", "type": "module"}