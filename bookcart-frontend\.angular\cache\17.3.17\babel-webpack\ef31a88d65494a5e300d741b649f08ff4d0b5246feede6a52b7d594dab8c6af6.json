{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/icon\";\nexport class UnauthorizedComponent {\n  constructor(router) {\n    this.router = router;\n  }\n  goHome() {\n    this.router.navigate(['/books']);\n  }\n  goBack() {\n    window.history.back();\n  }\n  static {\n    this.ɵfac = function UnauthorizedComponent_Factory(t) {\n      return new (t || UnauthorizedComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UnauthorizedComponent,\n      selectors: [[\"app-unauthorized\"]],\n      decls: 21,\n      vars: 0,\n      consts: [[1, \"unauthorized-container\"], [1, \"unauthorized-card\"], [1, \"unauthorized-content\"], [1, \"unauthorized-icon\"], [1, \"actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-button\", \"\", 3, \"click\"]],\n      template: function UnauthorizedComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-content\")(3, \"div\", 2)(4, \"mat-icon\", 3);\n          i0.ɵɵtext(5, \"block\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"h1\");\n          i0.ɵɵtext(7, \"Access Denied\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\");\n          i0.ɵɵtext(9, \"You don't have permission to access this page.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"p\");\n          i0.ɵɵtext(11, \"Please contact an administrator if you believe this is an error.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 4)(13, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function UnauthorizedComponent_Template_button_click_13_listener() {\n            return ctx.goHome();\n          });\n          i0.ɵɵelementStart(14, \"mat-icon\");\n          i0.ɵɵtext(15, \"home\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(16, \" Go to Home \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function UnauthorizedComponent_Template_button_click_17_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵelementStart(18, \"mat-icon\");\n          i0.ɵɵtext(19, \"arrow_back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(20, \" Go Back \");\n          i0.ɵɵelementEnd()()()()()();\n        }\n      },\n      dependencies: [i2.MatButton, i3.MatCard, i3.MatCardContent, i4.MatIcon],\n      styles: [\".unauthorized-container[_ngcontent-%COMP%] {\\n      display: flex;\\n      justify-content: center;\\n      align-items: center;\\n      min-height: calc(100vh - 64px);\\n      padding: 20px;\\n    }\\n    .unauthorized-card[_ngcontent-%COMP%] {\\n      max-width: 500px;\\n      width: 100%;\\n    }\\n    .unauthorized-content[_ngcontent-%COMP%] {\\n      display: flex;\\n      flex-direction: column;\\n      align-items: center;\\n      text-align: center;\\n      padding: 40px 20px;\\n    }\\n    .unauthorized-icon[_ngcontent-%COMP%] {\\n      font-size: 80px;\\n      height: 80px;\\n      width: 80px;\\n      color: #f44336;\\n      margin-bottom: 20px;\\n    }\\n    h1[_ngcontent-%COMP%] {\\n      margin: 0 0 16px 0;\\n      color: #333;\\n    }\\n    p[_ngcontent-%COMP%] {\\n      margin: 0 0 12px 0;\\n      color: #666;\\n    }\\n    .actions[_ngcontent-%COMP%] {\\n      display: flex;\\n      gap: 12px;\\n      margin-top: 24px;\\n    }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvdW5hdXRob3JpemVkL3VuYXV0aG9yaXplZC5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtJQUNJO01BQ0UsYUFBYTtNQUNiLHVCQUF1QjtNQUN2QixtQkFBbUI7TUFDbkIsOEJBQThCO01BQzlCLGFBQWE7SUFDZjtJQUNBO01BQ0UsZ0JBQWdCO01BQ2hCLFdBQVc7SUFDYjtJQUNBO01BQ0UsYUFBYTtNQUNiLHNCQUFzQjtNQUN0QixtQkFBbUI7TUFDbkIsa0JBQWtCO01BQ2xCLGtCQUFrQjtJQUNwQjtJQUNBO01BQ0UsZUFBZTtNQUNmLFlBQVk7TUFDWixXQUFXO01BQ1gsY0FBYztNQUNkLG1CQUFtQjtJQUNyQjtJQUNBO01BQ0Usa0JBQWtCO01BQ2xCLFdBQVc7SUFDYjtJQUNBO01BQ0Usa0JBQWtCO01BQ2xCLFdBQVc7SUFDYjtJQUNBO01BQ0UsYUFBYTtNQUNiLFNBQVM7TUFDVCxnQkFBZ0I7SUFDbEIiLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAudW5hdXRob3JpemVkLWNvbnRhaW5lciB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgbWluLWhlaWdodDogY2FsYygxMDB2aCAtIDY0cHgpO1xuICAgICAgcGFkZGluZzogMjBweDtcbiAgICB9XG4gICAgLnVuYXV0aG9yaXplZC1jYXJkIHtcbiAgICAgIG1heC13aWR0aDogNTAwcHg7XG4gICAgICB3aWR0aDogMTAwJTtcbiAgICB9XG4gICAgLnVuYXV0aG9yaXplZC1jb250ZW50IHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICAgIHBhZGRpbmc6IDQwcHggMjBweDtcbiAgICB9XG4gICAgLnVuYXV0aG9yaXplZC1pY29uIHtcbiAgICAgIGZvbnQtc2l6ZTogODBweDtcbiAgICAgIGhlaWdodDogODBweDtcbiAgICAgIHdpZHRoOiA4MHB4O1xuICAgICAgY29sb3I6ICNmNDQzMzY7XG4gICAgICBtYXJnaW4tYm90dG9tOiAyMHB4O1xuICAgIH1cbiAgICBoMSB7XG4gICAgICBtYXJnaW46IDAgMCAxNnB4IDA7XG4gICAgICBjb2xvcjogIzMzMztcbiAgICB9XG4gICAgcCB7XG4gICAgICBtYXJnaW46IDAgMCAxMnB4IDA7XG4gICAgICBjb2xvcjogIzY2NjtcbiAgICB9XG4gICAgLmFjdGlvbnMge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGdhcDogMTJweDtcbiAgICAgIG1hcmdpbi10b3A6IDI0cHg7XG4gICAgfVxuICAiXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["UnauthorizedComponent", "constructor", "router", "goHome", "navigate", "goBack", "window", "history", "back", "i0", "ɵɵdirectiveInject", "i1", "Router", "selectors", "decls", "vars", "consts", "template", "UnauthorizedComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "UnauthorizedComponent_Template_button_click_13_listener", "UnauthorizedComponent_Template_button_click_17_listener"], "sources": ["C:\\Users\\<USER>\\Desktop\\BookCart\\bookcart-frontend\\src\\app\\shared\\components\\unauthorized\\unauthorized.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-unauthorized',\n  template: `\n    <div class=\"unauthorized-container\">\n      <mat-card class=\"unauthorized-card\">\n        <mat-card-content>\n          <div class=\"unauthorized-content\">\n            <mat-icon class=\"unauthorized-icon\">block</mat-icon>\n            <h1>Access Denied</h1>\n            <p>You don't have permission to access this page.</p>\n            <p>Please contact an administrator if you believe this is an error.</p>\n            \n            <div class=\"actions\">\n              <button mat-raised-button color=\"primary\" (click)=\"goHome()\">\n                <mat-icon>home</mat-icon>\n                Go to Home\n              </button>\n              <button mat-button (click)=\"goBack()\">\n                <mat-icon>arrow_back</mat-icon>\n                Go Back\n              </button>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  `,\n  styles: [`\n    .unauthorized-container {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      min-height: calc(100vh - 64px);\n      padding: 20px;\n    }\n    .unauthorized-card {\n      max-width: 500px;\n      width: 100%;\n    }\n    .unauthorized-content {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      text-align: center;\n      padding: 40px 20px;\n    }\n    .unauthorized-icon {\n      font-size: 80px;\n      height: 80px;\n      width: 80px;\n      color: #f44336;\n      margin-bottom: 20px;\n    }\n    h1 {\n      margin: 0 0 16px 0;\n      color: #333;\n    }\n    p {\n      margin: 0 0 12px 0;\n      color: #666;\n    }\n    .actions {\n      display: flex;\n      gap: 12px;\n      margin-top: 24px;\n    }\n  `]\n})\nexport class UnauthorizedComponent {\n  constructor(private router: Router) {}\n\n  goHome(): void {\n    this.router.navigate(['/books']);\n  }\n\n  goBack(): void {\n    window.history.back();\n  }\n}\n"], "mappings": ";;;;;AAuEA,OAAM,MAAOA,qBAAqB;EAChCC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;EAAW;EAErCC,MAAMA,CAAA;IACJ,IAAI,CAACD,MAAM,CAACE,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAC,MAAMA,CAAA;IACJC,MAAM,CAACC,OAAO,CAACC,IAAI,EAAE;EACvB;;;uBATWR,qBAAqB,EAAAS,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAArBZ,qBAAqB;MAAAa,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA7DtBV,EAJR,CAAAY,cAAA,aAAoC,kBACE,uBAChB,aACkB,kBACI;UAAAZ,EAAA,CAAAa,MAAA,YAAK;UAAAb,EAAA,CAAAc,YAAA,EAAW;UACpDd,EAAA,CAAAY,cAAA,SAAI;UAAAZ,EAAA,CAAAa,MAAA,oBAAa;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACtBd,EAAA,CAAAY,cAAA,QAAG;UAAAZ,EAAA,CAAAa,MAAA,qDAA8C;UAAAb,EAAA,CAAAc,YAAA,EAAI;UACrDd,EAAA,CAAAY,cAAA,SAAG;UAAAZ,EAAA,CAAAa,MAAA,wEAAgE;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAGrEd,EADF,CAAAY,cAAA,cAAqB,iBAC0C;UAAnBZ,EAAA,CAAAe,UAAA,mBAAAC,wDAAA;YAAA,OAASL,GAAA,CAAAjB,MAAA,EAAQ;UAAA,EAAC;UAC1DM,EAAA,CAAAY,cAAA,gBAAU;UAAAZ,EAAA,CAAAa,MAAA,YAAI;UAAAb,EAAA,CAAAc,YAAA,EAAW;UACzBd,EAAA,CAAAa,MAAA,oBACF;UAAAb,EAAA,CAAAc,YAAA,EAAS;UACTd,EAAA,CAAAY,cAAA,iBAAsC;UAAnBZ,EAAA,CAAAe,UAAA,mBAAAE,wDAAA;YAAA,OAASN,GAAA,CAAAf,MAAA,EAAQ;UAAA,EAAC;UACnCI,EAAA,CAAAY,cAAA,gBAAU;UAAAZ,EAAA,CAAAa,MAAA,kBAAU;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAC/Bd,EAAA,CAAAa,MAAA,iBACF;UAKVb,EALU,CAAAc,YAAA,EAAS,EACL,EACF,EACW,EACV,EACP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}