/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { InjectionToken } from './injection_token';
/**
 * An internal token whose presence in an injector indicates that the injector should treat itself
 * as a root scoped injector when processing requests for unknown tokens which may indicate
 * they are provided in the root scope.
 */
export const INJECTOR_SCOPE = new InjectionToken(ngDevMode ? 'Set Injector scope.' : '');
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic2NvcGUuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9jb3JlL3NyYy9kaS9zY29wZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7O0dBTUc7QUFFSCxPQUFPLEVBQUMsY0FBYyxFQUFDLE1BQU0sbUJBQW1CLENBQUM7QUFLakQ7Ozs7R0FJRztBQUNILE1BQU0sQ0FBQyxNQUFNLGNBQWMsR0FDdkIsSUFBSSxjQUFjLENBQXFCLFNBQVMsQ0FBQyxDQUFDLENBQUMscUJBQXFCLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5cbmltcG9ydCB7SW5qZWN0aW9uVG9rZW59IGZyb20gJy4vaW5qZWN0aW9uX3Rva2VuJztcblxuXG5leHBvcnQgdHlwZSBJbmplY3RvclNjb3BlID0gJ3Jvb3QnfCdwbGF0Zm9ybSd8J2Vudmlyb25tZW50JztcblxuLyoqXG4gKiBBbiBpbnRlcm5hbCB0b2tlbiB3aG9zZSBwcmVzZW5jZSBpbiBhbiBpbmplY3RvciBpbmRpY2F0ZXMgdGhhdCB0aGUgaW5qZWN0b3Igc2hvdWxkIHRyZWF0IGl0c2VsZlxuICogYXMgYSByb290IHNjb3BlZCBpbmplY3RvciB3aGVuIHByb2Nlc3NpbmcgcmVxdWVzdHMgZm9yIHVua25vd24gdG9rZW5zIHdoaWNoIG1heSBpbmRpY2F0ZVxuICogdGhleSBhcmUgcHJvdmlkZWQgaW4gdGhlIHJvb3Qgc2NvcGUuXG4gKi9cbmV4cG9ydCBjb25zdCBJTkpFQ1RPUl9TQ09QRSA9XG4gICAgbmV3IEluamVjdGlvblRva2VuPEluamVjdG9yU2NvcGV8bnVsbD4obmdEZXZNb2RlID8gJ1NldCBJbmplY3RvciBzY29wZS4nIDogJycpO1xuIl19