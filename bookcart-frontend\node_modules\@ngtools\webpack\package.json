{"name": "@ngtools/webpack", "version": "17.3.17", "description": "Webpack plugin that AoT compiles your Angular components and modules.", "main": "./src/index.js", "typings": "src/index.d.ts", "license": "MIT", "keywords": ["Angular CLI", "Angular DevKit", "angular", "aot", "devkit", "plugin", "sdk", "webpack"], "repository": {"type": "git", "url": "https://github.com/angular/angular-cli.git"}, "author": "Angular Authors", "bugs": {"url": "https://github.com/angular/angular-cli/issues"}, "homepage": "https://github.com/angular/angular-cli", "dependencies": {}, "peerDependencies": {"@angular/compiler-cli": "^17.0.0", "typescript": ">=5.2 <5.5", "webpack": "^5.54.0"}, "engines": {"node": "^18.13.0 || >=20.9.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}