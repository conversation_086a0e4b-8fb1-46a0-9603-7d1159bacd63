/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['luo'] = ["luo",[["OD","OT"],u,u],u,[["J","W","T","T","T","T","N"],["JMP","WUT","TAR","TAD","TAN","TAB","NGS"],["Jumapil","Wuok Tich","Tich Ariyo","Tich Adek","Tich Ang’wen","<PERSON>ich Abich","<PERSON>es<PERSON>"],["<PERSON><PERSON>","WUT","T<PERSON>","TAD","TAN","TAB","NGS"]],u,[["C","R","D","N","B","U","B","B","C","P","C","P"],["DAC","DAR","DAD","DAN","DAH","DAU","DAO","DAB","DOC","DAP","DGI","DAG"],["Dwe mar Achiel","Dwe mar Ariyo","Dwe mar Adek","Dwe mar Ang’wen","Dwe mar Abich","Dwe mar Auchiel","Dwe mar Abiriyo","Dwe mar Aboro","Dwe mar Ochiko","Dwe mar Apar","Dwe mar gi achiel","Dwe mar Apar gi ariyo"]],u,[["BC","AD"],u,["Kapok Kristo obiro","Ka Kristo osebiro"]],0,[6,0],["dd/MM/y","d MMM y","d MMMM y","EEEE, d MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","#,##0.00¤","#E0"],"KES","Ksh","Siling mar Kenya",{"JPY":["JP¥","¥"],"KES":["Ksh"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    