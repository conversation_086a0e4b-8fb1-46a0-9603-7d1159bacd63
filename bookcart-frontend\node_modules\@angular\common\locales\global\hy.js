/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val, i = Math.floor(Math.abs(val));

if (i === 0 || i === 1)
    return 1;
return 5;
}
    global.ng.common.locales['hy'] = ["hy",[["ա","հ"],["AM","PM"],u],[["AM","PM"],u,u],[["Կ","Ե","Ե","Չ","Հ","Ո","Շ"],["կիր","երկ","երք","չրք","հնգ","ուր","շբթ"],["կիրակի","երկուշաբթի","երեքշաբթի","չորեքշաբթի","հինգշաբթի","ուրբաթ","շաբաթ"],["կր","եկ","եք","չք","հգ","ու","շբ"]],u,[["Հ","Փ","Մ","Ա","Մ","Հ","Հ","Օ","Ս","Հ","Ն","Դ"],["հնվ","փտվ","մրտ","ապր","մյս","հնս","հլս","օգս","սեպ","հոկ","նոյ","դեկ"],["հունվարի","փետրվարի","մարտի","ապրիլի","մայիսի","հունիսի","հուլիսի","օգոստոսի","սեպտեմբերի","հոկտեմբերի","նոյեմբերի","դեկտեմբերի"]],[["Հ","Փ","Մ","Ա","Մ","Հ","Հ","Օ","Ս","Հ","Ն","Դ"],["հնվ","փտվ","մրտ","ապր","մյս","հնս","հլս","օգս","սեպ","հոկ","նոյ","դեկ"],["հունվար","փետրվար","մարտ","ապրիլ","մայիս","հունիս","հուլիս","օգոստոս","սեպտեմբեր","հոկտեմբեր","նոյեմբեր","դեկտեմբեր"]],[["մ.թ.ա.","մ.թ."],u,["Քրիստոսից առաջ","Քրիստոսից հետո"]],1,[6,0],["dd.MM.yy","dd MMM, y թ.","dd MMMM, y թ.","y թ. MMMM d, EEEE"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1}, {0}",u,u,u],[","," ",";","%","+","-","E","×","‰","∞","ՈչԹ",":"],["#,##0.###","#,##0%","#,##0.00 ¤","#E0"],"AMD","֏","հայկական դրամ",{"AMD":["֏"],"BYN":[u,"р."],"JPY":["JP¥","¥"],"PHP":[u,"₱"],"THB":["฿"],"TWD":["NT$"]},"ltr", plural, [[["կգ․","կօ․","առվ","ցրկ","երկ","գշր"],["կեսգիշեր","կեսօր","առավոտյան","ցերեկը","երեկոյան","գիշերը"],["կեսգիշերին","կեսօրին","առավոտյան","ցերեկվա","երեկոյան","գիշերվա"]],[["կեսգիշեր","կեսօր","առավոտ","ցերեկ","երեկո","գիշեր"],u,u],["00:00","12:00",["06:00","12:00"],["12:00","18:00"],["18:00","24:00"],["00:00","06:00"]]]];
  })(globalThis);
    