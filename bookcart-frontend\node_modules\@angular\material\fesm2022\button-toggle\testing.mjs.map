{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../../src/material/button-toggle/testing/button-toggle-harness.ts", "../../../../../../../src/material/button-toggle/testing/button-toggle-group-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ComponentHarness, HarnessPredicate} from '@angular/cdk/testing';\nimport {coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {MatButtonToggleAppearance} from '@angular/material/button-toggle';\nimport {ButtonToggleHarnessFilters} from './button-toggle-harness-filters';\n\n/** Harness for interacting with a standard mat-button-toggle in tests. */\nexport class MatButtonToggleHarness extends ComponentHarness {\n  /** The selector for the host element of a `MatButton` instance. */\n  static hostSelector = '.mat-button-toggle';\n\n  private _label = this.locatorFor('.mat-button-toggle-label-content');\n  private _button = this.locatorFor('.mat-button-toggle-button');\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatButtonToggleHarness` that meets\n   * certain criteria.\n   * @param options Options for filtering which button toggle instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: ButtonToggleHarnessFilters = {}): HarnessPredicate<MatButtonToggleHarness> {\n    return new HarnessPredicate(MatButtonToggleHarness, options)\n      .addOption('text', options.text, (harness, text) =>\n        HarnessPredicate.stringMatches(harness.getText(), text),\n      )\n      .addOption('name', options.name, (harness, name) =>\n        HarnessPredicate.stringMatches(harness.getName(), name),\n      )\n      .addOption(\n        'checked',\n        options.checked,\n        async (harness, checked) => (await harness.isChecked()) === checked,\n      )\n      .addOption('disabled', options.disabled, async (harness, disabled) => {\n        return (await harness.isDisabled()) === disabled;\n      });\n  }\n\n  /** Gets a boolean promise indicating if the button toggle is checked. */\n  async isChecked(): Promise<boolean> {\n    const checked = (await this._button()).getAttribute('aria-pressed');\n    return coerceBooleanProperty(await checked);\n  }\n\n  /** Gets a boolean promise indicating if the button toggle is disabled. */\n  async isDisabled(): Promise<boolean> {\n    const disabled = (await this._button()).getAttribute('disabled');\n    return coerceBooleanProperty(await disabled);\n  }\n\n  /** Gets a promise for the button toggle's name. */\n  async getName(): Promise<string | null> {\n    return (await this._button()).getAttribute('name');\n  }\n\n  /** Gets a promise for the button toggle's aria-label. */\n  async getAriaLabel(): Promise<string | null> {\n    return (await this._button()).getAttribute('aria-label');\n  }\n\n  /** Gets a promise for the button toggles's aria-labelledby. */\n  async getAriaLabelledby(): Promise<string | null> {\n    return (await this._button()).getAttribute('aria-labelledby');\n  }\n\n  /** Gets a promise for the button toggle's text. */\n  async getText(): Promise<string> {\n    return (await this._label()).text();\n  }\n\n  /** Gets the appearance that the button toggle is using. */\n  async getAppearance(): Promise<MatButtonToggleAppearance> {\n    const host = await this.host();\n    const className = 'mat-button-toggle-appearance-standard';\n    return (await host.hasClass(className)) ? 'standard' : 'legacy';\n  }\n\n  /** Focuses the toggle. */\n  async focus(): Promise<void> {\n    return (await this._button()).focus();\n  }\n\n  /** Blurs the toggle. */\n  async blur(): Promise<void> {\n    return (await this._button()).blur();\n  }\n\n  /** Whether the toggle is focused. */\n  async isFocused(): Promise<boolean> {\n    return (await this._button()).isFocused();\n  }\n\n  /** Toggle the checked state of the buttons toggle. */\n  async toggle(): Promise<void> {\n    return (await this._button()).click();\n  }\n\n  /**\n   * Puts the button toggle in a checked state by toggling it if it's\n   * currently unchecked, or doing nothing if it is already checked.\n   */\n  async check(): Promise<void> {\n    if (!(await this.isChecked())) {\n      await this.toggle();\n    }\n  }\n\n  /**\n   * Puts the button toggle in an unchecked state by toggling it if it's\n   * currently checked, or doing nothing if it's already unchecked.\n   */\n  async uncheck(): Promise<void> {\n    if (await this.isChecked()) {\n      await this.toggle();\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ComponentHarness, HarnessPredicate} from '@angular/cdk/testing';\nimport {MatButtonToggleAppearance} from '@angular/material/button-toggle';\nimport {ButtonToggleGroupHarnessFilters} from './button-toggle-group-harness-filters';\nimport {ButtonToggleHarnessFilters} from './button-toggle-harness-filters';\nimport {MatButtonToggleHarness} from './button-toggle-harness';\n\n/** Harness for interacting with a standard mat-button-toggle in tests. */\nexport class MatButtonToggleGroupHarness extends ComponentHarness {\n  /** The selector for the host element of a `MatButton` instance. */\n  static hostSelector = '.mat-button-toggle-group';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatButtonToggleGroupHarness`\n   * that meets certain criteria.\n   * @param options Options for filtering which button toggle instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(\n    options: ButtonToggleGroupHarnessFilters = {},\n  ): HarnessPredicate<MatButtonToggleGroupHarness> {\n    return new HarnessPredicate(MatButtonToggleGroupHarness, options).addOption(\n      'disabled',\n      options.disabled,\n      async (harness, disabled) => {\n        return (await harness.isDisabled()) === disabled;\n      },\n    );\n  }\n\n  /**\n   * Gets the button toggles that are inside the group.\n   * @param filter Optionally filters which toggles are included.\n   */\n  async getToggles(filter: ButtonToggleHarnessFilters = {}): Promise<MatButtonToggleHarness[]> {\n    return this.locatorForAll(MatButtonToggleHarness.with(filter))();\n  }\n\n  /** Gets whether the button toggle group is disabled. */\n  async isDisabled(): Promise<boolean> {\n    return (await (await this.host()).getAttribute('aria-disabled')) === 'true';\n  }\n\n  /** Gets whether the button toggle group is laid out vertically. */\n  async isVertical(): Promise<boolean> {\n    return (await this.host()).hasClass('mat-button-toggle-vertical');\n  }\n\n  /** Gets the appearance that the group is using. */\n  async getAppearance(): Promise<MatButtonToggleAppearance> {\n    const host = await this.host();\n    const className = 'mat-button-toggle-group-appearance-standard';\n    return (await host.hasClass(className)) ? 'standard' : 'legacy';\n  }\n}\n"], "names": [], "mappings": ";;;AAaA;AACM,MAAO,sBAAuB,SAAQ,gBAAgB,CAAA;AAA5D,IAAA,WAAA,GAAA;;AAIU,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,kCAAkC,CAAC,CAAC;AAC7D,QAAA,IAAA,CAAA,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,2BAA2B,CAAC,CAAC;KAwGhE;;aA3GQ,IAAY,CAAA,YAAA,GAAG,oBAAH,CAAwB,EAAA;AAK3C;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAAsC,EAAE,EAAA;AAClD,QAAA,OAAO,IAAI,gBAAgB,CAAC,sBAAsB,EAAE,OAAO,CAAC;aACzD,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,IAAI,KAC7C,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,CACxD;aACA,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,IAAI,KAC7C,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,CACxD;aACA,SAAS,CACR,SAAS,EACT,OAAO,CAAC,OAAO,EACf,OAAO,OAAO,EAAE,OAAO,KAAK,CAAC,MAAM,OAAO,CAAC,SAAS,EAAE,MAAM,OAAO,CACpE;AACA,aAAA,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,OAAO,EAAE,QAAQ,KAAI;YACnE,OAAO,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,QAAQ,CAAC;AACnD,SAAC,CAAC,CAAC;KACN;;AAGD,IAAA,MAAM,SAAS,GAAA;AACb,QAAA,MAAM,OAAO,GAAG,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,EAAE,YAAY,CAAC,cAAc,CAAC,CAAC;AACpE,QAAA,OAAO,qBAAqB,CAAC,MAAM,OAAO,CAAC,CAAC;KAC7C;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,MAAM,QAAQ,GAAG,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC;AACjE,QAAA,OAAO,qBAAqB,CAAC,MAAM,QAAQ,CAAC,CAAC;KAC9C;;AAGD,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;KACpD;;AAGD,IAAA,MAAM,YAAY,GAAA;AAChB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,EAAE,YAAY,CAAC,YAAY,CAAC,CAAC;KAC1D;;AAGD,IAAA,MAAM,iBAAiB,GAAA;AACrB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,EAAE,YAAY,CAAC,iBAAiB,CAAC,CAAC;KAC/D;;AAGD,IAAA,MAAM,OAAO,GAAA;QACX,OAAO,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,CAAC;KACrC;;AAGD,IAAA,MAAM,aAAa,GAAA;AACjB,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAC/B,MAAM,SAAS,GAAG,uCAAuC,CAAC;AAC1D,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,UAAU,GAAG,QAAQ,CAAC;KACjE;;AAGD,IAAA,MAAM,KAAK,GAAA;QACT,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC;KACvC;;AAGD,IAAA,MAAM,IAAI,GAAA;QACR,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;KACtC;;AAGD,IAAA,MAAM,SAAS,GAAA;QACb,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,CAAC;KAC3C;;AAGD,IAAA,MAAM,MAAM,GAAA;QACV,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC;KACvC;AAED;;;AAGG;AACH,IAAA,MAAM,KAAK,GAAA;QACT,IAAI,EAAE,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE;AAC7B,YAAA,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;SACrB;KACF;AAED;;;AAGG;AACH,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,IAAI,MAAM,IAAI,CAAC,SAAS,EAAE,EAAE;AAC1B,YAAA,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;SACrB;KACF;;;AC5GH;AACM,MAAO,2BAA4B,SAAQ,gBAAgB,CAAA;;aAExD,IAAY,CAAA,YAAA,GAAG,0BAA0B,CAAC,EAAA;AAEjD;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CACT,OAAA,GAA2C,EAAE,EAAA;QAE7C,OAAO,IAAI,gBAAgB,CAAC,2BAA2B,EAAE,OAAO,CAAC,CAAC,SAAS,CACzE,UAAU,EACV,OAAO,CAAC,QAAQ,EAChB,OAAO,OAAO,EAAE,QAAQ,KAAI;YAC1B,OAAO,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,QAAQ,CAAC;AACnD,SAAC,CACF,CAAC;KACH;AAED;;;AAGG;AACH,IAAA,MAAM,UAAU,CAAC,MAAA,GAAqC,EAAE,EAAA;AACtD,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;KAClE;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,eAAe,CAAC,MAAM,MAAM,CAAC;KAC7E;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,4BAA4B,CAAC,CAAC;KACnE;;AAGD,IAAA,MAAM,aAAa,GAAA;AACjB,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAC/B,MAAM,SAAS,GAAG,6CAA6C,CAAC;AAChE,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,UAAU,GAAG,QAAQ,CAAC;KACjE;;;;;"}