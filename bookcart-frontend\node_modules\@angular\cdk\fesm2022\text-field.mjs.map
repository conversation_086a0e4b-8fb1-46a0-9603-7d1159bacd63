{"version": 3, "file": "text-field.mjs", "sources": ["../../../../../../src/cdk/text-field/autofill.ts", "../../../../../../src/cdk/text-field/autosize.ts", "../../../../../../src/cdk/text-field/text-field-module.ts", "../../../../../../src/cdk/text-field/text-field_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Platform, normalizePassiveListenerOptions} from '@angular/cdk/platform';\nimport {\n  Directive,\n  ElementRef,\n  EventEmitter,\n  Injectable,\n  NgZone,\n  OnDestroy,\n  OnInit,\n  Output,\n} from '@angular/core';\nimport {coerceElement} from '@angular/cdk/coercion';\nimport {EMPTY, Observable, Subject} from 'rxjs';\n\n/** An event that is emitted when the autofill state of an input changes. */\nexport type AutofillEvent = {\n  /** The element whose autofill state changes. */\n  target: Element;\n  /** Whether the element is currently autofilled. */\n  isAutofilled: boolean;\n};\n\n/** Used to track info about currently monitored elements. */\ntype MonitoredElementInfo = {\n  readonly subject: Subject<AutofillEvent>;\n  unlisten: () => void;\n};\n\n/** Options to pass to the animationstart listener. */\nconst listenerOptions = normalizePassiveListenerOptions({passive: true});\n\n/**\n * An injectable service that can be used to monitor the autofill state of an input.\n * Based on the following blog post:\n * https://medium.com/@brunn/detecting-autofilled-fields-in-javascript-aed598d25da7\n */\n@Injectable({providedIn: 'root'})\nexport class AutofillMonitor implements OnDestroy {\n  private _monitoredElements = new Map<Element, MonitoredElementInfo>();\n\n  constructor(\n    private _platform: Platform,\n    private _ngZone: NgZone,\n  ) {}\n\n  /**\n   * Monitor for changes in the autofill state of the given input element.\n   * @param element The element to monitor.\n   * @return A stream of autofill state changes.\n   */\n  monitor(element: Element): Observable<AutofillEvent>;\n\n  /**\n   * Monitor for changes in the autofill state of the given input element.\n   * @param element The element to monitor.\n   * @return A stream of autofill state changes.\n   */\n  monitor(element: ElementRef<Element>): Observable<AutofillEvent>;\n\n  monitor(elementOrRef: Element | ElementRef<Element>): Observable<AutofillEvent> {\n    if (!this._platform.isBrowser) {\n      return EMPTY;\n    }\n\n    const element = coerceElement(elementOrRef);\n    const info = this._monitoredElements.get(element);\n\n    if (info) {\n      return info.subject;\n    }\n\n    const result = new Subject<AutofillEvent>();\n    const cssClass = 'cdk-text-field-autofilled';\n    const listener = ((event: AnimationEvent) => {\n      // Animation events fire on initial element render, we check for the presence of the autofill\n      // CSS class to make sure this is a real change in state, not just the initial render before\n      // we fire off events.\n      if (\n        event.animationName === 'cdk-text-field-autofill-start' &&\n        !element.classList.contains(cssClass)\n      ) {\n        element.classList.add(cssClass);\n        this._ngZone.run(() => result.next({target: event.target as Element, isAutofilled: true}));\n      } else if (\n        event.animationName === 'cdk-text-field-autofill-end' &&\n        element.classList.contains(cssClass)\n      ) {\n        element.classList.remove(cssClass);\n        this._ngZone.run(() => result.next({target: event.target as Element, isAutofilled: false}));\n      }\n    }) as EventListenerOrEventListenerObject;\n\n    this._ngZone.runOutsideAngular(() => {\n      element.addEventListener('animationstart', listener, listenerOptions);\n      element.classList.add('cdk-text-field-autofill-monitored');\n    });\n\n    this._monitoredElements.set(element, {\n      subject: result,\n      unlisten: () => {\n        element.removeEventListener('animationstart', listener, listenerOptions);\n      },\n    });\n\n    return result;\n  }\n\n  /**\n   * Stop monitoring the autofill state of the given input element.\n   * @param element The element to stop monitoring.\n   */\n  stopMonitoring(element: Element): void;\n\n  /**\n   * Stop monitoring the autofill state of the given input element.\n   * @param element The element to stop monitoring.\n   */\n  stopMonitoring(element: ElementRef<Element>): void;\n\n  stopMonitoring(elementOrRef: Element | ElementRef<Element>): void {\n    const element = coerceElement(elementOrRef);\n    const info = this._monitoredElements.get(element);\n\n    if (info) {\n      info.unlisten();\n      info.subject.complete();\n      element.classList.remove('cdk-text-field-autofill-monitored');\n      element.classList.remove('cdk-text-field-autofilled');\n      this._monitoredElements.delete(element);\n    }\n  }\n\n  ngOnDestroy() {\n    this._monitoredElements.forEach((_info, element) => this.stopMonitoring(element));\n  }\n}\n\n/** A directive that can be used to monitor the autofill state of an input. */\n@Directive({\n  selector: '[cdkAutofill]',\n  standalone: true,\n})\nexport class CdkAutofill implements OnDestroy, OnInit {\n  /** Emits when the autofill state of the element changes. */\n  @Output() readonly cdkAutofill = new EventEmitter<AutofillEvent>();\n\n  constructor(\n    private _elementRef: ElementRef<HTMLElement>,\n    private _autofillMonitor: AutofillMonitor,\n  ) {}\n\n  ngOnInit() {\n    this._autofillMonitor\n      .monitor(this._elementRef)\n      .subscribe(event => this.cdkAutofill.emit(event));\n  }\n\n  ngOnDestroy() {\n    this._autofillMonitor.stopMonitoring(this._elementRef);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NumberInput, coerceNumberProperty} from '@angular/cdk/coercion';\nimport {\n  Directive,\n  ElementRef,\n  Input,\n  AfterViewInit,\n  DoCheck,\n  OnDestroy,\n  NgZone,\n  Optional,\n  Inject,\n  booleanAttribute,\n} from '@angular/core';\nimport {Platform} from '@angular/cdk/platform';\nimport {auditTime, takeUntil} from 'rxjs/operators';\nimport {fromEvent, Subject} from 'rxjs';\nimport {DOCUMENT} from '@angular/common';\n\n/** Directive to automatically resize a textarea to fit its content. */\n@Directive({\n  selector: 'textarea[cdkTextareaAutosize]',\n  exportAs: 'cdkTextareaAutosize',\n  host: {\n    'class': 'cdk-textarea-autosize',\n    // Textarea elements that have the directive applied should have a single row by default.\n    // Browsers normally show two rows by default and therefore this limits the minRows binding.\n    'rows': '1',\n    '(input)': '_noopInputHandler()',\n  },\n  standalone: true,\n})\nexport class CdkTextareaAutosize implements AfterViewInit, DoCheck, OnDestroy {\n  /** Keep track of the previous textarea value to avoid resizing when the value hasn't changed. */\n  private _previousValue?: string;\n  private _initialHeight: string | undefined;\n  private readonly _destroyed = new Subject<void>();\n\n  private _minRows: number;\n  private _maxRows: number;\n  private _enabled: boolean = true;\n\n  /**\n   * Value of minRows as of last resize. If the minRows has decreased, the\n   * height of the textarea needs to be recomputed to reflect the new minimum. The maxHeight\n   * does not have the same problem because it does not affect the textarea's scrollHeight.\n   */\n  private _previousMinRows: number = -1;\n\n  private _textareaElement: HTMLTextAreaElement;\n\n  /** Minimum amount of rows in the textarea. */\n  @Input('cdkAutosizeMinRows')\n  get minRows(): number {\n    return this._minRows;\n  }\n  set minRows(value: NumberInput) {\n    this._minRows = coerceNumberProperty(value);\n    this._setMinHeight();\n  }\n\n  /** Maximum amount of rows in the textarea. */\n  @Input('cdkAutosizeMaxRows')\n  get maxRows(): number {\n    return this._maxRows;\n  }\n  set maxRows(value: NumberInput) {\n    this._maxRows = coerceNumberProperty(value);\n    this._setMaxHeight();\n  }\n\n  /** Whether autosizing is enabled or not */\n  @Input({alias: 'cdkTextareaAutosize', transform: booleanAttribute})\n  get enabled(): boolean {\n    return this._enabled;\n  }\n  set enabled(value: boolean) {\n    // Only act if the actual value changed. This specifically helps to not run\n    // resizeToFitContent too early (i.e. before ngAfterViewInit)\n    if (this._enabled !== value) {\n      (this._enabled = value) ? this.resizeToFitContent(true) : this.reset();\n    }\n  }\n\n  @Input()\n  get placeholder(): string {\n    return this._textareaElement.placeholder;\n  }\n  set placeholder(value: string) {\n    this._cachedPlaceholderHeight = undefined;\n\n    if (value) {\n      this._textareaElement.setAttribute('placeholder', value);\n    } else {\n      this._textareaElement.removeAttribute('placeholder');\n    }\n\n    this._cacheTextareaPlaceholderHeight();\n  }\n\n  /** Cached height of a textarea with a single row. */\n  private _cachedLineHeight: number;\n  /** Cached height of a textarea with only the placeholder. */\n  private _cachedPlaceholderHeight?: number;\n\n  /** Used to reference correct document/window */\n  protected _document?: Document;\n\n  private _hasFocus: boolean;\n\n  private _isViewInited = false;\n\n  constructor(\n    private _elementRef: ElementRef<HTMLElement>,\n    private _platform: Platform,\n    private _ngZone: NgZone,\n    /** @breaking-change 11.0.0 make document required */\n    @Optional() @Inject(DOCUMENT) document?: any,\n  ) {\n    this._document = document;\n\n    this._textareaElement = this._elementRef.nativeElement as HTMLTextAreaElement;\n  }\n\n  /** Sets the minimum height of the textarea as determined by minRows. */\n  _setMinHeight(): void {\n    const minHeight =\n      this.minRows && this._cachedLineHeight ? `${this.minRows * this._cachedLineHeight}px` : null;\n\n    if (minHeight) {\n      this._textareaElement.style.minHeight = minHeight;\n    }\n  }\n\n  /** Sets the maximum height of the textarea as determined by maxRows. */\n  _setMaxHeight(): void {\n    const maxHeight =\n      this.maxRows && this._cachedLineHeight ? `${this.maxRows * this._cachedLineHeight}px` : null;\n\n    if (maxHeight) {\n      this._textareaElement.style.maxHeight = maxHeight;\n    }\n  }\n\n  ngAfterViewInit() {\n    if (this._platform.isBrowser) {\n      // Remember the height which we started with in case autosizing is disabled\n      this._initialHeight = this._textareaElement.style.height;\n      this.resizeToFitContent();\n\n      this._ngZone.runOutsideAngular(() => {\n        const window = this._getWindow();\n\n        fromEvent(window, 'resize')\n          .pipe(auditTime(16), takeUntil(this._destroyed))\n          .subscribe(() => this.resizeToFitContent(true));\n\n        this._textareaElement.addEventListener('focus', this._handleFocusEvent);\n        this._textareaElement.addEventListener('blur', this._handleFocusEvent);\n      });\n\n      this._isViewInited = true;\n      this.resizeToFitContent(true);\n    }\n  }\n\n  ngOnDestroy() {\n    this._textareaElement.removeEventListener('focus', this._handleFocusEvent);\n    this._textareaElement.removeEventListener('blur', this._handleFocusEvent);\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n\n  /**\n   * Cache the height of a single-row textarea if it has not already been cached.\n   *\n   * We need to know how large a single \"row\" of a textarea is in order to apply minRows and\n   * maxRows. For the initial version, we will assume that the height of a single line in the\n   * textarea does not ever change.\n   */\n  private _cacheTextareaLineHeight(): void {\n    if (this._cachedLineHeight) {\n      return;\n    }\n\n    // Use a clone element because we have to override some styles.\n    let textareaClone = this._textareaElement.cloneNode(false) as HTMLTextAreaElement;\n    textareaClone.rows = 1;\n\n    // Use `position: absolute` so that this doesn't cause a browser layout and use\n    // `visibility: hidden` so that nothing is rendered. Clear any other styles that\n    // would affect the height.\n    textareaClone.style.position = 'absolute';\n    textareaClone.style.visibility = 'hidden';\n    textareaClone.style.border = 'none';\n    textareaClone.style.padding = '0';\n    textareaClone.style.height = '';\n    textareaClone.style.minHeight = '';\n    textareaClone.style.maxHeight = '';\n\n    // In Firefox it happens that textarea elements are always bigger than the specified amount\n    // of rows. This is because Firefox tries to add extra space for the horizontal scrollbar.\n    // As a workaround that removes the extra space for the scrollbar, we can just set overflow\n    // to hidden. This ensures that there is no invalid calculation of the line height.\n    // See Firefox bug report: https://bugzilla.mozilla.org/show_bug.cgi?id=33654\n    textareaClone.style.overflow = 'hidden';\n\n    this._textareaElement.parentNode!.appendChild(textareaClone);\n    this._cachedLineHeight = textareaClone.clientHeight;\n    textareaClone.remove();\n\n    // Min and max heights have to be re-calculated if the cached line height changes\n    this._setMinHeight();\n    this._setMaxHeight();\n  }\n\n  private _measureScrollHeight(): number {\n    const element = this._textareaElement;\n    const previousMargin = element.style.marginBottom || '';\n    const isFirefox = this._platform.FIREFOX;\n    const needsMarginFiller = isFirefox && this._hasFocus;\n    const measuringClass = isFirefox\n      ? 'cdk-textarea-autosize-measuring-firefox'\n      : 'cdk-textarea-autosize-measuring';\n\n    // In some cases the page might move around while we're measuring the `textarea` on Firefox. We\n    // work around it by assigning a temporary margin with the same height as the `textarea` so that\n    // it occupies the same amount of space. See #23233.\n    if (needsMarginFiller) {\n      element.style.marginBottom = `${element.clientHeight}px`;\n    }\n\n    // Reset the textarea height to auto in order to shrink back to its default size.\n    // Also temporarily force overflow:hidden, so scroll bars do not interfere with calculations.\n    element.classList.add(measuringClass);\n    // The measuring class includes a 2px padding to workaround an issue with Chrome,\n    // so we account for that extra space here by subtracting 4 (2px top + 2px bottom).\n    const scrollHeight = element.scrollHeight - 4;\n    element.classList.remove(measuringClass);\n\n    if (needsMarginFiller) {\n      element.style.marginBottom = previousMargin;\n    }\n\n    return scrollHeight;\n  }\n\n  private _cacheTextareaPlaceholderHeight(): void {\n    if (!this._isViewInited || this._cachedPlaceholderHeight != undefined) {\n      return;\n    }\n    if (!this.placeholder) {\n      this._cachedPlaceholderHeight = 0;\n      return;\n    }\n\n    const value = this._textareaElement.value;\n\n    this._textareaElement.value = this._textareaElement.placeholder;\n    this._cachedPlaceholderHeight = this._measureScrollHeight();\n    this._textareaElement.value = value;\n  }\n\n  /** Handles `focus` and `blur` events. */\n  private _handleFocusEvent = (event: FocusEvent) => {\n    this._hasFocus = event.type === 'focus';\n  };\n\n  ngDoCheck() {\n    if (this._platform.isBrowser) {\n      this.resizeToFitContent();\n    }\n  }\n\n  /**\n   * Resize the textarea to fit its content.\n   * @param force Whether to force a height recalculation. By default the height will be\n   *    recalculated only if the value changed since the last call.\n   */\n  resizeToFitContent(force: boolean = false) {\n    // If autosizing is disabled, just skip everything else\n    if (!this._enabled) {\n      return;\n    }\n\n    this._cacheTextareaLineHeight();\n    this._cacheTextareaPlaceholderHeight();\n\n    // If we haven't determined the line-height yet, we know we're still hidden and there's no point\n    // in checking the height of the textarea.\n    if (!this._cachedLineHeight) {\n      return;\n    }\n\n    const textarea = this._elementRef.nativeElement as HTMLTextAreaElement;\n    const value = textarea.value;\n\n    // Only resize if the value or minRows have changed since these calculations can be expensive.\n    if (!force && this._minRows === this._previousMinRows && value === this._previousValue) {\n      return;\n    }\n\n    const scrollHeight = this._measureScrollHeight();\n    const height = Math.max(scrollHeight, this._cachedPlaceholderHeight || 0);\n\n    // Use the scrollHeight to know how large the textarea *would* be if fit its entire value.\n    textarea.style.height = `${height}px`;\n\n    this._ngZone.runOutsideAngular(() => {\n      if (typeof requestAnimationFrame !== 'undefined') {\n        requestAnimationFrame(() => this._scrollToCaretPosition(textarea));\n      } else {\n        setTimeout(() => this._scrollToCaretPosition(textarea));\n      }\n    });\n\n    this._previousValue = value;\n    this._previousMinRows = this._minRows;\n  }\n\n  /**\n   * Resets the textarea to its original size\n   */\n  reset() {\n    // Do not try to change the textarea, if the initialHeight has not been determined yet\n    // This might potentially remove styles when reset() is called before ngAfterViewInit\n    if (this._initialHeight !== undefined) {\n      this._textareaElement.style.height = this._initialHeight;\n    }\n  }\n\n  _noopInputHandler() {\n    // no-op handler that ensures we're running change detection on input events.\n  }\n\n  /** Access injected document if available or fallback to global document reference */\n  private _getDocument(): Document {\n    return this._document || document;\n  }\n\n  /** Use defaultView of injected document if available or fallback to global window reference */\n  private _getWindow(): Window {\n    const doc = this._getDocument();\n    return doc.defaultView || window;\n  }\n\n  /**\n   * Scrolls a textarea to the caret position. On Firefox resizing the textarea will\n   * prevent it from scrolling to the caret position. We need to re-set the selection\n   * in order for it to scroll to the proper position.\n   */\n  private _scrollToCaretPosition(textarea: HTMLTextAreaElement) {\n    const {selectionStart, selectionEnd} = textarea;\n\n    // IE will throw an \"Unspecified error\" if we try to set the selection range after the\n    // element has been removed from the DOM. Assert that the directive hasn't been destroyed\n    // between the time we requested the animation frame and when it was executed.\n    // Also note that we have to assert that the textarea is focused before we set the\n    // selection range. Setting the selection range on a non-focused textarea will cause\n    // it to receive focus on IE and Edge.\n    if (!this._destroyed.isStopped && this._hasFocus) {\n      textarea.setSelectionRange(selectionStart, selectionEnd);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {CdkAutofill} from './autofill';\nimport {CdkTextareaAutosize} from './autosize';\n\n@NgModule({\n  imports: [CdkAutofill, CdkTextareaAutosize],\n  exports: [CdkAutofill, CdkTextareaAutosize],\n})\nexport class TextFieldModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;;;;;AAoCA;AACA,MAAM,eAAe,GAAG,+BAA+B,CAAC,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC;AAEzE;;;;AAIG;MAEU,eAAe,CAAA;IAG1B,WACU,CAAA,SAAmB,EACnB,OAAe,EAAA;QADf,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QACnB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;AAJjB,QAAA,IAAA,CAAA,kBAAkB,GAAG,IAAI,GAAG,EAAiC,CAAC;KAKlE;AAgBJ,IAAA,OAAO,CAAC,YAA2C,EAAA;AACjD,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;AAC7B,YAAA,OAAO,KAAK,CAAC;SACd;AAED,QAAA,MAAM,OAAO,GAAG,aAAa,CAAC,YAAY,CAAC,CAAC;QAC5C,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAElD,IAAI,IAAI,EAAE;YACR,OAAO,IAAI,CAAC,OAAO,CAAC;SACrB;AAED,QAAA,MAAM,MAAM,GAAG,IAAI,OAAO,EAAiB,CAAC;QAC5C,MAAM,QAAQ,GAAG,2BAA2B,CAAC;AAC7C,QAAA,MAAM,QAAQ,IAAI,CAAC,KAAqB,KAAI;;;;AAI1C,YAAA,IACE,KAAK,CAAC,aAAa,KAAK,+BAA+B;gBACvD,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EACrC;AACA,gBAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBAChC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,MAAM,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,KAAK,CAAC,MAAiB,EAAE,YAAY,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC;aAC5F;AAAM,iBAAA,IACL,KAAK,CAAC,aAAa,KAAK,6BAA6B;gBACrD,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EACpC;AACA,gBAAA,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBACnC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,MAAM,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,KAAK,CAAC,MAAiB,EAAE,YAAY,EAAE,KAAK,EAAC,CAAC,CAAC,CAAC;aAC7F;AACH,SAAC,CAAuC,CAAC;AAEzC,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;YAClC,OAAO,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC;AACtE,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;AAC7D,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,OAAO,EAAE;AACnC,YAAA,OAAO,EAAE,MAAM;YACf,QAAQ,EAAE,MAAK;gBACb,OAAO,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC;aAC1E;AACF,SAAA,CAAC,CAAC;AAEH,QAAA,OAAO,MAAM,CAAC;KACf;AAcD,IAAA,cAAc,CAAC,YAA2C,EAAA;AACxD,QAAA,MAAM,OAAO,GAAG,aAAa,CAAC,YAAY,CAAC,CAAC;QAC5C,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAElD,IAAI,IAAI,EAAE;YACR,IAAI,CAAC,QAAQ,EAAE,CAAC;AAChB,YAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;AACxB,YAAA,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,mCAAmC,CAAC,CAAC;AAC9D,YAAA,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,2BAA2B,CAAC,CAAC;AACtD,YAAA,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;SACzC;KACF;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC;KACnF;8GAjGU,eAAe,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAAf,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,cADH,MAAM,EAAA,CAAA,CAAA,EAAA;;2FAClB,eAAe,EAAA,UAAA,EAAA,CAAA;kBAD3B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;AAqGhC;MAKa,WAAW,CAAA;IAItB,WACU,CAAA,WAAoC,EACpC,gBAAiC,EAAA;QADjC,IAAW,CAAA,WAAA,GAAX,WAAW,CAAyB;QACpC,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAiB;;AAJxB,QAAA,IAAA,CAAA,WAAW,GAAG,IAAI,YAAY,EAAiB,CAAC;KAK/D;IAEJ,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,gBAAgB;AAClB,aAAA,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC;AACzB,aAAA,SAAS,CAAC,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;KACrD;IAED,WAAW,GAAA;QACT,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;KACxD;8GAjBU,WAAW,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,eAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAX,WAAW,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,eAAA,EAAA,OAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAX,WAAW,EAAA,UAAA,EAAA,CAAA;kBAJvB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,eAAe;AACzB,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;0GAGoB,WAAW,EAAA,CAAA;sBAA7B,MAAM;;;AC9HT;MAaa,mBAAmB,CAAA;;AAoB9B,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;IACD,IAAI,OAAO,CAAC,KAAkB,EAAA;AAC5B,QAAA,IAAI,CAAC,QAAQ,GAAG,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAC5C,IAAI,CAAC,aAAa,EAAE,CAAC;KACtB;;AAGD,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;IACD,IAAI,OAAO,CAAC,KAAkB,EAAA;AAC5B,QAAA,IAAI,CAAC,QAAQ,GAAG,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAC5C,IAAI,CAAC,aAAa,EAAE,CAAC;KACtB;;AAGD,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;IACD,IAAI,OAAO,CAAC,KAAc,EAAA;;;AAGxB,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,EAAE;YAC3B,CAAC,IAAI,CAAC,QAAQ,GAAG,KAAK,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;SACxE;KACF;AAED,IAAA,IACI,WAAW,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC;KAC1C;IACD,IAAI,WAAW,CAAC,KAAa,EAAA;AAC3B,QAAA,IAAI,CAAC,wBAAwB,GAAG,SAAS,CAAC;QAE1C,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;SAC1D;aAAM;AACL,YAAA,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;SACtD;QAED,IAAI,CAAC,+BAA+B,EAAE,CAAC;KACxC;AAcD,IAAA,WAAA,CACU,WAAoC,EACpC,SAAmB,EACnB,OAAe;;IAEO,QAAc,EAAA;QAJpC,IAAW,CAAA,WAAA,GAAX,WAAW,CAAyB;QACpC,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QACnB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;AA/ER,QAAA,IAAA,CAAA,UAAU,GAAG,IAAI,OAAO,EAAQ,CAAC;QAI1C,IAAQ,CAAA,QAAA,GAAY,IAAI,CAAC;AAEjC;;;;AAIG;QACK,IAAgB,CAAA,gBAAA,GAAW,CAAC,CAAC,CAAC;QA+D9B,IAAa,CAAA,aAAA,GAAG,KAAK,CAAC;;AA0JtB,QAAA,IAAA,CAAA,iBAAiB,GAAG,CAAC,KAAiB,KAAI;YAChD,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,KAAK,OAAO,CAAC;AAC1C,SAAC,CAAC;AAnJA,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAE1B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,aAAoC,CAAC;KAC/E;;IAGD,aAAa,GAAA;QACX,MAAM,SAAS,GACb,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,iBAAiB,GAAG,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAA,EAAA,CAAI,GAAG,IAAI,CAAC;QAE/F,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;SACnD;KACF;;IAGD,aAAa,GAAA;QACX,MAAM,SAAS,GACb,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,iBAAiB,GAAG,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAA,EAAA,CAAI,GAAG,IAAI,CAAC;QAE/F,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;SACnD;KACF;IAED,eAAe,GAAA;AACb,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;;YAE5B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,MAAM,CAAC;YACzD,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAE1B,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,gBAAA,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;AAEjC,gBAAA,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC;AACxB,qBAAA,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;qBAC/C,SAAS,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;gBAElD,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBACxE,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;AACzE,aAAC,CAAC,CAAC;AAEH,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,YAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;SAC/B;KACF;IAED,WAAW,GAAA;QACT,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC3E,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;AAC1E,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;KAC5B;AAED;;;;;;AAMG;IACK,wBAAwB,GAAA;AAC9B,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,OAAO;SACR;;QAGD,IAAI,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,KAAK,CAAwB,CAAC;AAClF,QAAA,aAAa,CAAC,IAAI,GAAG,CAAC,CAAC;;;;AAKvB,QAAA,aAAa,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;AAC1C,QAAA,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC;AAC1C,QAAA,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;AACpC,QAAA,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;AAClC,QAAA,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC;AAChC,QAAA,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;AACnC,QAAA,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;;;;;;AAOnC,QAAA,aAAa,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAExC,IAAI,CAAC,gBAAgB,CAAC,UAAW,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;AAC7D,QAAA,IAAI,CAAC,iBAAiB,GAAG,aAAa,CAAC,YAAY,CAAC;QACpD,aAAa,CAAC,MAAM,EAAE,CAAC;;QAGvB,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,aAAa,EAAE,CAAC;KACtB;IAEO,oBAAoB,GAAA;AAC1B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC;QACtC,MAAM,cAAc,GAAG,OAAO,CAAC,KAAK,CAAC,YAAY,IAAI,EAAE,CAAC;AACxD,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;AACzC,QAAA,MAAM,iBAAiB,GAAG,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC;QACtD,MAAM,cAAc,GAAG,SAAS;AAC9B,cAAE,yCAAyC;cACzC,iCAAiC,CAAC;;;;QAKtC,IAAI,iBAAiB,EAAE;YACrB,OAAO,CAAC,KAAK,CAAC,YAAY,GAAG,GAAG,OAAO,CAAC,YAAY,CAAA,EAAA,CAAI,CAAC;SAC1D;;;AAID,QAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;;;AAGtC,QAAA,MAAM,YAAY,GAAG,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC;AAC9C,QAAA,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QAEzC,IAAI,iBAAiB,EAAE;AACrB,YAAA,OAAO,CAAC,KAAK,CAAC,YAAY,GAAG,cAAc,CAAC;SAC7C;AAED,QAAA,OAAO,YAAY,CAAC;KACrB;IAEO,+BAA+B,GAAA;QACrC,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,wBAAwB,IAAI,SAAS,EAAE;YACrE,OAAO;SACR;AACD,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AACrB,YAAA,IAAI,CAAC,wBAAwB,GAAG,CAAC,CAAC;YAClC,OAAO;SACR;AAED,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;QAE1C,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC;AAChE,QAAA,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5D,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAG,KAAK,CAAC;KACrC;IAOD,SAAS,GAAA;AACP,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAC5B,IAAI,CAAC,kBAAkB,EAAE,CAAC;SAC3B;KACF;AAED;;;;AAIG;IACH,kBAAkB,CAAC,QAAiB,KAAK,EAAA;;AAEvC,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,OAAO;SACR;QAED,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAChC,IAAI,CAAC,+BAA+B,EAAE,CAAC;;;AAIvC,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC3B,OAAO;SACR;AAED,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,aAAoC,CAAC;AACvE,QAAA,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;;AAG7B,QAAA,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,gBAAgB,IAAI,KAAK,KAAK,IAAI,CAAC,cAAc,EAAE;YACtF,OAAO;SACR;AAED,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;AACjD,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,wBAAwB,IAAI,CAAC,CAAC,CAAC;;QAG1E,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,CAAG,EAAA,MAAM,IAAI,CAAC;AAEtC,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,IAAI,OAAO,qBAAqB,KAAK,WAAW,EAAE;gBAChD,qBAAqB,CAAC,MAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC;aACpE;iBAAM;gBACL,UAAU,CAAC,MAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC;aACzD;AACH,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;AAC5B,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC;KACvC;AAED;;AAEG;IACH,KAAK,GAAA;;;AAGH,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE;YACrC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC;SAC1D;KACF;IAED,iBAAiB,GAAA;;KAEhB;;IAGO,YAAY,GAAA;AAClB,QAAA,OAAO,IAAI,CAAC,SAAS,IAAI,QAAQ,CAAC;KACnC;;IAGO,UAAU,GAAA;AAChB,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;AAChC,QAAA,OAAO,GAAG,CAAC,WAAW,IAAI,MAAM,CAAC;KAClC;AAED;;;;AAIG;AACK,IAAA,sBAAsB,CAAC,QAA6B,EAAA;AAC1D,QAAA,MAAM,EAAC,cAAc,EAAE,YAAY,EAAC,GAAG,QAAQ,CAAC;;;;;;;QAQhD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE;AAChD,YAAA,QAAQ,CAAC,iBAAiB,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;SAC1D;KACF;AA3UU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,mBAAmB,0FAqFR,QAAQ,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AArFnB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,mBAAmB,+MAwCmB,gBAAgB,CAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,GAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,qBAAA,EAAA,EAAA,cAAA,EAAA,uBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAxCtD,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAZ/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,+BAA+B;AACzC,oBAAA,QAAQ,EAAE,qBAAqB;AAC/B,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,uBAAuB;;;AAGhC,wBAAA,MAAM,EAAE,GAAG;AACX,wBAAA,SAAS,EAAE,qBAAqB;AACjC,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BAsFI,QAAQ;;0BAAI,MAAM;2BAAC,QAAQ,CAAA;yCAhE1B,OAAO,EAAA,CAAA;sBADV,KAAK;uBAAC,oBAAoB,CAAA;gBAWvB,OAAO,EAAA,CAAA;sBADV,KAAK;uBAAC,oBAAoB,CAAA;gBAWvB,OAAO,EAAA,CAAA;sBADV,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,qBAAqB,EAAE,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAa9D,WAAW,EAAA,CAAA;sBADd,KAAK;;;MC3EK,eAAe,CAAA;8GAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAf,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YAHhB,WAAW,EAAE,mBAAmB,CAChC,EAAA,OAAA,EAAA,CAAA,WAAW,EAAE,mBAAmB,CAAA,EAAA,CAAA,CAAA,EAAA;+GAE/B,eAAe,EAAA,CAAA,CAAA,EAAA;;2FAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAJ3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,WAAW,EAAE,mBAAmB,CAAC;AAC3C,oBAAA,OAAO,EAAE,CAAC,WAAW,EAAE,mBAAmB,CAAC;AAC5C,iBAAA,CAAA;;;ACfD;;AAEG;;;;"}