@use 'sass:color';
@use '@material/chips/chip-theme' as mdc-chip-theme;
@use '../core/tokens/m2/mdc/chip' as tokens-mdc-chip;
@use '../core/tokens/m2/mat/chip' as tokens-mat-chip;
@use '../core/tokens/token-utils';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/theming/validation';
@use '../core/typography/typography';

/// Outputs base theme styles (styles not dependent on the color, typography, or density settings)
/// for the mat-chips.
/// @param {Map} $theme The theme to generate base styles for.
@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, base));
  }
  @else {
    .mat-mdc-standard-chip {
      @include mdc-chip-theme.theme(tokens-mdc-chip.get-unthemable-tokens());
      @include token-utils.create-token-values(
          tokens-mat-chip.$prefix, tokens-mat-chip.get-unthemable-tokens());
    }
  }
}

/// Outputs color theme styles for the mat-chips.
/// @param {Map} $theme The theme to generate color styles for.
/// @param {ArgList} Additional optional arguments (only supported for M3 themes):
///   $color-variant: The color variant to use for the selected chip: primary, secondary, tertiary,
///      or error (If not specified, default secondary color will be used).
@mixin color($theme, $options...) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, color), $options...);
  }
  @else {
    .mat-mdc-standard-chip {
      $default-color-tokens: tokens-mdc-chip.get-color-tokens($theme);
      @include mdc-chip-theme.theme($default-color-tokens);
      @include token-utils.create-token-values(
          tokens-mat-chip.$prefix, tokens-mat-chip.get-color-tokens($theme));

      &.mat-mdc-chip-selected,
      &.mat-mdc-chip-highlighted {
        &.mat-primary {
          $primary-color-tokens: tokens-mdc-chip.get-color-tokens($theme, primary);
          @include mdc-chip-theme.theme($primary-color-tokens);
          @include token-utils.create-token-values(
              tokens-mat-chip.$prefix, tokens-mat-chip.get-color-tokens($theme, primary));
        }

        &.mat-accent {
          $accent-color-tokens: tokens-mdc-chip.get-color-tokens($theme, accent);
          @include mdc-chip-theme.theme($accent-color-tokens);
          @include token-utils.create-token-values(
              tokens-mat-chip.$prefix, tokens-mat-chip.get-color-tokens($theme, accent));
        }

        &.mat-warn {
          $warn-color-tokens: tokens-mdc-chip.get-color-tokens($theme, warn);
          @include mdc-chip-theme.theme($warn-color-tokens);
          @include token-utils.create-token-values(
              tokens-mat-chip.$prefix, tokens-mat-chip.get-color-tokens($theme, warn));
        }
      }
    }
  }
}

/// Outputs typography theme styles for the mat-chips.
/// @param {Map} $theme The theme to generate typography styles for.
@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, typography));
  }
  @else {
    $typography-tokens: tokens-mdc-chip.get-typography-tokens($theme);

    .mat-mdc-standard-chip {
      @include mdc-chip-theme.theme($typography-tokens);
      @include token-utils.create-token-values(
          tokens-mat-chip.$prefix, tokens-mat-chip.get-typography-tokens($theme));
    }
  }
}

/// Outputs density theme styles for the mat-chips.
/// @param {Map} $theme The theme to generate density styles for.
@mixin density($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, density));
  }
  @else {
    $density-tokens: tokens-mdc-chip.get-density-tokens($theme);

    .mat-mdc-chip.mat-mdc-standard-chip {
      @include mdc-chip-theme.theme($density-tokens);
      @include token-utils.create-token-values(
          tokens-mat-chip.$prefix, tokens-mat-chip.get-density-tokens($theme));
    }
  }
}

/// Outputs all (base, color, typography, and density) theme styles for the mat-chips.
/// @param {Map} $theme The theme to generate styles for.
/// @param {ArgList} Additional optional arguments (only supported for M3 themes):
///   $color-variant: The color variant to use for the selected chip: primary, secondary, tertiary,
///      or error (If not specified, default secondary color will be used).
@mixin theme($theme, $options...) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-chips') {
    @if inspection.get-theme-version($theme) == 1 {
      @include _theme-from-tokens(inspection.get-theme-tokens($theme), $options...);
    }
    @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}

@mixin _theme-from-tokens($tokens, $options...) {
  @include validation.selector-defined(
      'Calls to Angular Material theme mixins with an M3 theme must be wrapped in a selector');
  $mdc-chip-tokens: token-utils.get-tokens-for($tokens, tokens-mdc-chip.$prefix, $options...);
  $mat-chip-tokens: token-utils.get-tokens-for($tokens, tokens-mat-chip.$prefix, $options...);
  @include mdc-chip-theme.theme($mdc-chip-tokens);
  @include token-utils.create-token-values(tokens-mat-chip.$prefix, $mat-chip-tokens);
}
