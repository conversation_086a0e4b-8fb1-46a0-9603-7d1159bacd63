/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { makeParamDecorator } from '../util/decorators';
import { attachInjectFlag } from './injector_compatibility';
/**
 * Inject decorator and metadata.
 *
 * @Annotation
 * @publicApi
 */
export const Inject = attachInjectFlag(
// Disable tslint because `DecoratorFlags` is a const enum which gets inlined.
// tslint:disable-next-line: no-toplevel-property-access
makeParamDecorator('Inject', (token) => ({ token })), -1 /* DecoratorFlags.Inject */);
/**
 * Optional decorator and metadata.
 *
 * @Annotation
 * @publicApi
 */
export const Optional = 
// Disable tslint because `InternalInjectFlags` is a const enum which gets inlined.
// tslint:disable-next-line: no-toplevel-property-access
attachInjectFlag(makeParamDecorator('Optional'), 8 /* InternalInjectFlags.Optional */);
/**
 * Self decorator and metadata.
 *
 * @Annotation
 * @publicApi
 */
export const Self = 
// Disable tslint because `InternalInjectFlags` is a const enum which gets inlined.
// tslint:disable-next-line: no-toplevel-property-access
attachInjectFlag(makeParamDecorator('Self'), 2 /* InternalInjectFlags.Self */);
/**
 * `SkipSelf` decorator and metadata.
 *
 * @Annotation
 * @publicApi
 */
export const SkipSelf = 
// Disable tslint because `InternalInjectFlags` is a const enum which gets inlined.
// tslint:disable-next-line: no-toplevel-property-access
attachInjectFlag(makeParamDecorator('SkipSelf'), 4 /* InternalInjectFlags.SkipSelf */);
/**
 * Host decorator and metadata.
 *
 * @Annotation
 * @publicApi
 */
export const Host = 
// Disable tslint because `InternalInjectFlags` is a const enum which gets inlined.
// tslint:disable-next-line: no-toplevel-property-access
attachInjectFlag(makeParamDecorator('Host'), 1 /* InternalInjectFlags.Host */);
//# sourceMappingURL=data:application/json;base64,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