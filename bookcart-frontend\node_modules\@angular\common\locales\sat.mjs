/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    if (n === 2)
        return 2;
    return 5;
}
export default ["sat", [["AM", "PM"], u, ["ᱥᱮᱛᱟᱜ", "ᱧᱤᱫᱟᱹ"]], [["AM", "PM"], u, u], [["ᱥ", "ᱚ", "ᱵ", "ᱥ", "ᱥ", "ᱡ", "ᱧ"], ["ᱥᱤᱸ", "ᱚᱛ", "ᱵᱟ", "ᱥᱟᱹ", "ᱥᱟᱹᱨ", "ᱡᱟᱹ", "ᱧᱩ"], ["ᱥᱤᱸᱜᱮ", "ᱚᱛᱮ", "ᱵᱟᱞᱮ", "ᱥᱟᱹᱜᱩᱱ", "ᱥᱟᱹᱨᱫᱤ", "ᱡᱟᱹᱨᱩᱢ", "ᱧᱩᱦᱩᱢ"], ["ᱥᱤᱸ", "ᱚᱛ", "ᱵᱟ", "ᱥᱟᱹ", "ᱥᱟᱹᱨ", "ᱡᱟᱹ", "ᱧᱩ"]], u, [["ᱡ", "ᱯ", "ᱢ", "ᱟ", "ᱢ", "ᱡ", "ᱡ", "ᱟ", "ᱥ", "ᱚ", "ᱱ", "ᱫ"], ["ᱡᱟᱱ", "ᱯᱷᱟ", "ᱢᱟᱨ", "ᱟᱯᱨ", "ᱢᱮ", "ᱡᱩᱱ", "ᱡᱩᱞ", "ᱟᱜᱟ", "ᱥᱮᱯ", "ᱚᱠᱴ", "ᱱᱟᱣ", "ᱫᱤᱥ"], ["ᱡᱟᱱᱣᱟᱨᱤ", "ᱯᱷᱟᱨᱣᱟᱨᱤ", "ᱢᱟᱨᱪ", "ᱟᱯᱨᱮᱞ", "ᱢᱮ", "ᱡᱩᱱ", "ᱡᱩᱞᱟᱭ", "ᱟᱜᱟᱥᱛ", "ᱥᱮᱯᱴᱮᱢᱵᱟᱨ", "ᱚᱠᱴᱚᱵᱟᱨ", "ᱱᱟᱣᱟᱢᱵᱟᱨ", "ᱫᱤᱥᱟᱢᱵᱟᱨ"]], u, [["ᱥᱮᱨᱢᱟ ᱞᱟᱦᱟ", "ᱤᱥᱣᱤ"], u, u], 0, [0, 0], ["d/M/yy", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "INR", "₹", "ᱥᱤᱧᱚᱛ ᱨᱮᱱᱟᱜ ᱴᱟᱠᱟ", { "JPY": ["JP¥", "¥"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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