/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["mai", [["AM", "PM"], u, ["भोर", "सांझ"]], [["AM", "PM"], u, u], [["र", "सो", "मं", "बु", "गु", "शु", "श"], ["रवि", "सोम", "मंगल", "बुध", "गुरु", "शुक्र", "शनि"], ["रवि दिन", "सोम दिन", "मंगल दिन", "बुध दिन", "बृहस्पति दिन", "शुक्र दिन", "शनि दिन"], ["रवि", "सोम", "मंगल", "बुध", "गुरु", "शुक्र", "शनि"]], u, [["ज", "फ", "मा", "अ", "म", "जू", "जु", "अ", "सि", "अ", "न", "दि"], ["जन॰", "फ़र॰", "मार्च", "अप्रैल", "मई", "जून", "जुल॰", "अग॰", "सित॰", "अक्तू॰", "नव॰", "दिस॰"], ["जनवरी", "फरवरी", "मार्च", "अप्रैल", "मई", "जून", "जुलाई", "अगस्त", "सितंबर", "अक्तूबर", "नवंबर", "दिसंबर"]], [["ज", "फ", "मा", "अ", "म", "जू", "जु", "अ", "सि", "अ", "न", "दि"], ["जन॰", "फर॰", "मार्च", "अप्रैल", "मई", "जून", "जुल॰", "अग॰", "सित॰", "अक्तू॰", "नव॰", "दिस॰"], ["जनवरी", "फरवरी", "मार्च", "अप्रैल", "मई", "जून", "जुलाई", "अगस्त", "सितंबर", "अक्टूबर", "नवंबर", "दिसंबर"]], [["ईसा-पूर्व", "ईसवी"], u, u], 0, [0, 0], ["d/M/yy", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1}, {0}", u, "{1} के {0}", u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "[#E0]"], "INR", "₹", "भारतीय रुपया", { "JPY": ["JP¥", "¥"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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