/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export const MAC_ENTER = 3;
export const BACKSPACE = 8;
export const TAB = 9;
export const NUM_CENTER = 12;
export const ENTER = 13;
export const SHIFT = 16;
export const CONTROL = 17;
export const ALT = 18;
export const PAUSE = 19;
export const CAPS_LOCK = 20;
export const ESCAPE = 27;
export const SPACE = 32;
export const PAGE_UP = 33;
export const PAGE_DOWN = 34;
export const END = 35;
export const HOME = 36;
export const LEFT_ARROW = 37;
export const UP_ARROW = 38;
export const RIGHT_ARROW = 39;
export const DOWN_ARROW = 40;
export const PLUS_SIGN = 43;
export const PRINT_SCREEN = 44;
export const INSERT = 45;
export const DELETE = 46;
export const ZERO = 48;
export const ONE = 49;
export const TWO = 50;
export const THREE = 51;
export const FOUR = 52;
export const FIVE = 53;
export const SIX = 54;
export const SEVEN = 55;
export const EIGHT = 56;
export const NINE = 57;
export const FF_SEMICOLON = 59; // Firefox (Gecko) fires this for semicolon instead of 186
export const FF_EQUALS = 61; // Firefox (Gecko) fires this for equals instead of 187
export const QUESTION_MARK = 63;
export const AT_SIGN = 64;
export const A = 65;
export const B = 66;
export const C = 67;
export const D = 68;
export const E = 69;
export const F = 70;
export const G = 71;
export const H = 72;
export const I = 73;
export const J = 74;
export const K = 75;
export const L = 76;
export const M = 77;
export const N = 78;
export const O = 79;
export const P = 80;
export const Q = 81;
export const R = 82;
export const S = 83;
export const T = 84;
export const U = 85;
export const V = 86;
export const W = 87;
export const X = 88;
export const Y = 89;
export const Z = 90;
export const META = 91; // WIN_KEY_LEFT
export const MAC_WK_CMD_LEFT = 91;
export const MAC_WK_CMD_RIGHT = 93;
export const CONTEXT_MENU = 93;
export const NUMPAD_ZERO = 96;
export const NUMPAD_ONE = 97;
export const NUMPAD_TWO = 98;
export const NUMPAD_THREE = 99;
export const NUMPAD_FOUR = 100;
export const NUMPAD_FIVE = 101;
export const NUMPAD_SIX = 102;
export const NUMPAD_SEVEN = 103;
export const NUMPAD_EIGHT = 104;
export const NUMPAD_NINE = 105;
export const NUMPAD_MULTIPLY = 106;
export const NUMPAD_PLUS = 107;
export const NUMPAD_MINUS = 109;
export const NUMPAD_PERIOD = 110;
export const NUMPAD_DIVIDE = 111;
export const F1 = 112;
export const F2 = 113;
export const F3 = 114;
export const F4 = 115;
export const F5 = 116;
export const F6 = 117;
export const F7 = 118;
export const F8 = 119;
export const F9 = 120;
export const F10 = 121;
export const F11 = 122;
export const F12 = 123;
export const NUM_LOCK = 144;
export const SCROLL_LOCK = 145;
export const FIRST_MEDIA = 166;
export const FF_MINUS = 173;
export const MUTE = 173; // Firefox (Gecko) fires 181 for MUTE
export const VOLUME_DOWN = 174; // Firefox (Gecko) fires 182 for VOLUME_DOWN
export const VOLUME_UP = 175; // Firefox (Gecko) fires 183 for VOLUME_UP
export const FF_MUTE = 181;
export const FF_VOLUME_DOWN = 182;
export const LAST_MEDIA = 183;
export const FF_VOLUME_UP = 183;
export const SEMICOLON = 186; // Firefox (Gecko) fires 59 for SEMICOLON
export const EQUALS = 187; // Firefox (Gecko) fires 61 for EQUALS
export const COMMA = 188;
export const DASH = 189; // Firefox (Gecko) fires 173 for DASH/MINUS
export const PERIOD = 190;
export const SLASH = 191;
export const APOSTROPHE = 192;
export const TILDE = 192;
export const OPEN_SQUARE_BRACKET = 219;
export const BACKSLASH = 220;
export const CLOSE_SQUARE_BRACKET = 221;
export const SINGLE_QUOTE = 222;
export const MAC_META = 224;
//# sourceMappingURL=data:application/json;base64,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