/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['wo'] = ["wo",[["Sub","Ngo"],u,u],u,[["Dib","Alt","Tal","<PERSON><PERSON>","<PERSON><PERSON>","Àjj","<PERSON>e"],u,["<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON>j<PERSON><PERSON>","<PERSON>eer"],["Dib","Alt","Tal","<PERSON><PERSON>","<PERSON>x","<PERSON>jj","<PERSON>e"]],u,[["1","2","3","4","5","6","7","8","9","10","11","12"],["<PERSON>","Few","<PERSON>","Awr","<PERSON>e","Suw","<PERSON>","<PERSON>t","Sàt","Okt","Now","<PERSON>"],["<PERSON>wiyee","<PERSON>riyee","<PERSON>","Awril","Mee","Suwe","<PERSON>et","Ut","Sàtt<PERSON>r","<PERSON>toobar","<PERSON><PERSON>mbar","<PERSON>àmbar"]],u,[["J<PERSON>","<PERSON>"],u,["av. JC","AD"]],1,[6,0],["dd-MM-y","d MMM, y","d MMMM, y","EEEE, d MMM, y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} - {0}",u,"{1} 'ci' {0}",u],[",",".",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤ #,##0.00","#E0"],"XOF","F CFA","Franc CFA bu Afrik Sowwu-jant",{"JPY":["JP¥","¥"]},"ltr", plural, []];
  })(globalThis);
    