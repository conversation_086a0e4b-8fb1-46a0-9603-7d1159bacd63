/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val, i = Math.floor(Math.abs(val));
    if (i === 0 || n === 1)
        return 1;
    return 5;
}
export default ["bn", [["AM", "PM"], u, u], u, [["র", "সো", "ম", "বু", "বৃ", "শু", "শ"], ["রবি", "সোম", "মঙ্গল", "বুধ", "বৃহস্পতি", "শুক্র", "শনি"], ["রবিবার", "সোমবার", "মঙ্গলবার", "বুধবার", "বৃহস্পতিবার", "শুক্রবার", "শনিবার"], ["রঃ", "সোঃ", "মঃ", "বুঃ", "বৃঃ", "শুঃ", "শনি"]], u, [["জা", "ফে", "মা", "এ", "মে", "জুন", "জু", "আ", "সে", "অ", "ন", "ডি"], ["জানু", "ফেব", "মার্চ", "এপ্রিল", "মে", "জুন", "জুলাই", "আগস্ট", "সেপ্টেম্বর", "অক্টোবর", "নভেম্বর", "ডিসেম্বর"], ["জানুয়ারী", "ফেব্রুয়ারী", "মার্চ", "এপ্রিল", "মে", "জুন", "জুলাই", "আগস্ট", "সেপ্টেম্বর", "অক্টোবর", "নভেম্বর", "ডিসেম্বর"]], [["জা", "ফে", "মা", "এ", "মে", "জুন", "জু", "আ", "সে", "অ", "ন", "ডি"], ["জানুয়ারী", "ফেব্রুয়ারী", "মার্চ", "এপ্রিল", "মে", "জুন", "জুলাই", "আগস্ট", "সেপ্টেম্বর", "অক্টোবর", "নভেম্বর", "ডিসেম্বর"], u], [["খ্রিস্টপূর্ব", "খৃষ্টাব্দ"], u, ["খ্রিস্টপূর্ব", "খ্রীষ্টাব্দ"]], 0, [6, 0], ["d/M/yy", "d MMM, y", "d MMMM, y", "EEEE, d MMMM, y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##,##0.###", "#,##,##0%", "#,##,##0.00¤", "#E0"], "BDT", "৳", "বাংলাদেশী টাকা", { "BDT": ["৳"], "BYN": [u, "р."], "JPY": ["JP¥", "¥"], "PHP": [u, "₱"], "THB": ["฿"], "TWD": ["NT$"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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