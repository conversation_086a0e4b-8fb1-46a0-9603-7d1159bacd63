{"ast": null, "code": "import { coerceElement, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, booleanAttribute, Directive, Output, Input, NgModule } from '@angular/core';\nimport { Observable, Subject } from 'rxjs';\nimport { map, filter, debounceTime } from 'rxjs/operators';\n\n// <PERSON><PERSON> may add, remove, or edit comment nodes during change detection. We don't care about\n// these changes because they don't affect the user-preceived content, and worse it can cause\n// infinite change detection cycles where the change detection updates a comment, triggering the\n// MutationObserver, triggering another change detection and kicking the cycle off again.\nfunction shouldIgnoreRecord(record) {\n  // Ignore changes to comment text.\n  if (record.type === 'characterData' && record.target instanceof Comment) {\n    return true;\n  }\n  // Ignore addition / removal of comments.\n  if (record.type === 'childList') {\n    for (let i = 0; i < record.addedNodes.length; i++) {\n      if (!(record.addedNodes[i] instanceof Comment)) {\n        return false;\n      }\n    }\n    for (let i = 0; i < record.removedNodes.length; i++) {\n      if (!(record.removedNodes[i] instanceof Comment)) {\n        return false;\n      }\n    }\n    return true;\n  }\n  // Observe everything else.\n  return false;\n}\n/**\n * Factory that creates a new MutationObserver and allows us to stub it out in unit tests.\n * @docs-private\n */\nclass MutationObserverFactory {\n  create(callback) {\n    return typeof MutationObserver === 'undefined' ? null : new MutationObserver(callback);\n  }\n  static {\n    this.ɵfac = function MutationObserverFactory_Factory(t) {\n      return new (t || MutationObserverFactory)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MutationObserverFactory,\n      factory: MutationObserverFactory.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MutationObserverFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** An injectable service that allows watching elements for changes to their content. */\nclass ContentObserver {\n  constructor(_mutationObserverFactory) {\n    this._mutationObserverFactory = _mutationObserverFactory;\n    /** Keeps track of the existing MutationObservers so they can be reused. */\n    this._observedElements = new Map();\n  }\n  ngOnDestroy() {\n    this._observedElements.forEach((_, element) => this._cleanupObserver(element));\n  }\n  observe(elementOrRef) {\n    const element = coerceElement(elementOrRef);\n    return new Observable(observer => {\n      const stream = this._observeElement(element);\n      const subscription = stream.pipe(map(records => records.filter(record => !shouldIgnoreRecord(record))), filter(records => !!records.length)).subscribe(observer);\n      return () => {\n        subscription.unsubscribe();\n        this._unobserveElement(element);\n      };\n    });\n  }\n  /**\n   * Observes the given element by using the existing MutationObserver if available, or creating a\n   * new one if not.\n   */\n  _observeElement(element) {\n    if (!this._observedElements.has(element)) {\n      const stream = new Subject();\n      const observer = this._mutationObserverFactory.create(mutations => stream.next(mutations));\n      if (observer) {\n        observer.observe(element, {\n          characterData: true,\n          childList: true,\n          subtree: true\n        });\n      }\n      this._observedElements.set(element, {\n        observer,\n        stream,\n        count: 1\n      });\n    } else {\n      this._observedElements.get(element).count++;\n    }\n    return this._observedElements.get(element).stream;\n  }\n  /**\n   * Un-observes the given element and cleans up the underlying MutationObserver if nobody else is\n   * observing this element.\n   */\n  _unobserveElement(element) {\n    if (this._observedElements.has(element)) {\n      this._observedElements.get(element).count--;\n      if (!this._observedElements.get(element).count) {\n        this._cleanupObserver(element);\n      }\n    }\n  }\n  /** Clean up the underlying MutationObserver for the specified element. */\n  _cleanupObserver(element) {\n    if (this._observedElements.has(element)) {\n      const {\n        observer,\n        stream\n      } = this._observedElements.get(element);\n      if (observer) {\n        observer.disconnect();\n      }\n      stream.complete();\n      this._observedElements.delete(element);\n    }\n  }\n  static {\n    this.ɵfac = function ContentObserver_Factory(t) {\n      return new (t || ContentObserver)(i0.ɵɵinject(MutationObserverFactory));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ContentObserver,\n      factory: ContentObserver.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ContentObserver, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: MutationObserverFactory\n  }], null);\n})();\n/**\n * Directive that triggers a callback whenever the content of\n * its associated element has changed.\n */\nclass CdkObserveContent {\n  /**\n   * Whether observing content is disabled. This option can be used\n   * to disconnect the underlying MutationObserver until it is needed.\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._disabled ? this._unsubscribe() : this._subscribe();\n  }\n  /** Debounce interval for emitting the changes. */\n  get debounce() {\n    return this._debounce;\n  }\n  set debounce(value) {\n    this._debounce = coerceNumberProperty(value);\n    this._subscribe();\n  }\n  constructor(_contentObserver, _elementRef, _ngZone) {\n    this._contentObserver = _contentObserver;\n    this._elementRef = _elementRef;\n    this._ngZone = _ngZone;\n    /** Event emitted for each change in the element's content. */\n    this.event = new EventEmitter();\n    this._disabled = false;\n    this._currentSubscription = null;\n  }\n  ngAfterContentInit() {\n    if (!this._currentSubscription && !this.disabled) {\n      this._subscribe();\n    }\n  }\n  ngOnDestroy() {\n    this._unsubscribe();\n  }\n  _subscribe() {\n    this._unsubscribe();\n    const stream = this._contentObserver.observe(this._elementRef);\n    // TODO(mmalerba): We shouldn't be emitting on this @Output() outside the zone.\n    // Consider brining it back inside the zone next time we're making breaking changes.\n    // Bringing it back inside can cause things like infinite change detection loops and changed\n    // after checked errors if people's code isn't handling it properly.\n    this._ngZone.runOutsideAngular(() => {\n      this._currentSubscription = (this.debounce ? stream.pipe(debounceTime(this.debounce)) : stream).subscribe(this.event);\n    });\n  }\n  _unsubscribe() {\n    this._currentSubscription?.unsubscribe();\n  }\n  static {\n    this.ɵfac = function CdkObserveContent_Factory(t) {\n      return new (t || CdkObserveContent)(i0.ɵɵdirectiveInject(ContentObserver), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkObserveContent,\n      selectors: [[\"\", \"cdkObserveContent\", \"\"]],\n      inputs: {\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"cdkObserveContentDisabled\", \"disabled\", booleanAttribute],\n        debounce: \"debounce\"\n      },\n      outputs: {\n        event: \"cdkObserveContent\"\n      },\n      exportAs: [\"cdkObserveContent\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkObserveContent, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkObserveContent]',\n      exportAs: 'cdkObserveContent',\n      standalone: true\n    }]\n  }], () => [{\n    type: ContentObserver\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.NgZone\n  }], {\n    event: [{\n      type: Output,\n      args: ['cdkObserveContent']\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkObserveContentDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    debounce: [{\n      type: Input\n    }]\n  });\n})();\nclass ObserversModule {\n  static {\n    this.ɵfac = function ObserversModule_Factory(t) {\n      return new (t || ObserversModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: ObserversModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MutationObserverFactory]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ObserversModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CdkObserveContent],\n      exports: [CdkObserveContent],\n      providers: [MutationObserverFactory]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkObserveContent, ContentObserver, MutationObserverFactory, ObserversModule };", "map": {"version": 3, "names": ["coerceElement", "coerceNumberProperty", "i0", "Injectable", "EventEmitter", "booleanAttribute", "Directive", "Output", "Input", "NgModule", "Observable", "Subject", "map", "filter", "debounceTime", "shouldIgnoreRecord", "record", "type", "target", "Comment", "i", "addedNodes", "length", "removedNodes", "MutationObserverFactory", "create", "callback", "MutationObserver", "ɵfac", "MutationObserverFactory_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "args", "ContentObserver", "constructor", "_mutationObserverFactory", "_observedElements", "Map", "ngOnDestroy", "for<PERSON>ach", "_", "element", "_cleanupObserver", "observe", "elementOrRef", "observer", "stream", "_observeElement", "subscription", "pipe", "records", "subscribe", "unsubscribe", "_unobserveElement", "has", "mutations", "next", "characterData", "childList", "subtree", "set", "count", "get", "disconnect", "complete", "delete", "ContentObserver_Factory", "ɵɵinject", "CdkObserveContent", "disabled", "_disabled", "value", "_unsubscribe", "_subscribe", "debounce", "_debounce", "_contentObserver", "_elementRef", "_ngZone", "event", "_currentSubscription", "ngAfterContentInit", "runOutsideAngular", "CdkObserveContent_Factory", "ɵɵdirectiveInject", "ElementRef", "NgZone", "ɵdir", "ɵɵdefineDirective", "selectors", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "outputs", "exportAs", "standalone", "features", "ɵɵInputTransformsFeature", "selector", "alias", "transform", "ObserversModule", "ObserversModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "providers", "imports", "exports"], "sources": ["C:/Users/<USER>/Desktop/BookCart/bookcart-frontend/node_modules/@angular/cdk/fesm2022/observers.mjs"], "sourcesContent": ["import { coerceElement, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, booleanAttribute, Directive, Output, Input, NgModule } from '@angular/core';\nimport { Observable, Subject } from 'rxjs';\nimport { map, filter, debounceTime } from 'rxjs/operators';\n\n// <PERSON><PERSON> may add, remove, or edit comment nodes during change detection. We don't care about\n// these changes because they don't affect the user-preceived content, and worse it can cause\n// infinite change detection cycles where the change detection updates a comment, triggering the\n// MutationObserver, triggering another change detection and kicking the cycle off again.\nfunction shouldIgnoreRecord(record) {\n    // Ignore changes to comment text.\n    if (record.type === 'characterData' && record.target instanceof Comment) {\n        return true;\n    }\n    // Ignore addition / removal of comments.\n    if (record.type === 'childList') {\n        for (let i = 0; i < record.addedNodes.length; i++) {\n            if (!(record.addedNodes[i] instanceof Comment)) {\n                return false;\n            }\n        }\n        for (let i = 0; i < record.removedNodes.length; i++) {\n            if (!(record.removedNodes[i] instanceof Comment)) {\n                return false;\n            }\n        }\n        return true;\n    }\n    // Observe everything else.\n    return false;\n}\n/**\n * Factory that creates a new MutationObserver and allows us to stub it out in unit tests.\n * @docs-private\n */\nclass MutationObserverFactory {\n    create(callback) {\n        return typeof MutationObserver === 'undefined' ? null : new MutationObserver(callback);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MutationObserverFactory, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MutationObserverFactory, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MutationObserverFactory, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/** An injectable service that allows watching elements for changes to their content. */\nclass ContentObserver {\n    constructor(_mutationObserverFactory) {\n        this._mutationObserverFactory = _mutationObserverFactory;\n        /** Keeps track of the existing MutationObservers so they can be reused. */\n        this._observedElements = new Map();\n    }\n    ngOnDestroy() {\n        this._observedElements.forEach((_, element) => this._cleanupObserver(element));\n    }\n    observe(elementOrRef) {\n        const element = coerceElement(elementOrRef);\n        return new Observable((observer) => {\n            const stream = this._observeElement(element);\n            const subscription = stream\n                .pipe(map(records => records.filter(record => !shouldIgnoreRecord(record))), filter(records => !!records.length))\n                .subscribe(observer);\n            return () => {\n                subscription.unsubscribe();\n                this._unobserveElement(element);\n            };\n        });\n    }\n    /**\n     * Observes the given element by using the existing MutationObserver if available, or creating a\n     * new one if not.\n     */\n    _observeElement(element) {\n        if (!this._observedElements.has(element)) {\n            const stream = new Subject();\n            const observer = this._mutationObserverFactory.create(mutations => stream.next(mutations));\n            if (observer) {\n                observer.observe(element, {\n                    characterData: true,\n                    childList: true,\n                    subtree: true,\n                });\n            }\n            this._observedElements.set(element, { observer, stream, count: 1 });\n        }\n        else {\n            this._observedElements.get(element).count++;\n        }\n        return this._observedElements.get(element).stream;\n    }\n    /**\n     * Un-observes the given element and cleans up the underlying MutationObserver if nobody else is\n     * observing this element.\n     */\n    _unobserveElement(element) {\n        if (this._observedElements.has(element)) {\n            this._observedElements.get(element).count--;\n            if (!this._observedElements.get(element).count) {\n                this._cleanupObserver(element);\n            }\n        }\n    }\n    /** Clean up the underlying MutationObserver for the specified element. */\n    _cleanupObserver(element) {\n        if (this._observedElements.has(element)) {\n            const { observer, stream } = this._observedElements.get(element);\n            if (observer) {\n                observer.disconnect();\n            }\n            stream.complete();\n            this._observedElements.delete(element);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: ContentObserver, deps: [{ token: MutationObserverFactory }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: ContentObserver, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: ContentObserver, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: MutationObserverFactory }] });\n/**\n * Directive that triggers a callback whenever the content of\n * its associated element has changed.\n */\nclass CdkObserveContent {\n    /**\n     * Whether observing content is disabled. This option can be used\n     * to disconnect the underlying MutationObserver until it is needed.\n     */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = value;\n        this._disabled ? this._unsubscribe() : this._subscribe();\n    }\n    /** Debounce interval for emitting the changes. */\n    get debounce() {\n        return this._debounce;\n    }\n    set debounce(value) {\n        this._debounce = coerceNumberProperty(value);\n        this._subscribe();\n    }\n    constructor(_contentObserver, _elementRef, _ngZone) {\n        this._contentObserver = _contentObserver;\n        this._elementRef = _elementRef;\n        this._ngZone = _ngZone;\n        /** Event emitted for each change in the element's content. */\n        this.event = new EventEmitter();\n        this._disabled = false;\n        this._currentSubscription = null;\n    }\n    ngAfterContentInit() {\n        if (!this._currentSubscription && !this.disabled) {\n            this._subscribe();\n        }\n    }\n    ngOnDestroy() {\n        this._unsubscribe();\n    }\n    _subscribe() {\n        this._unsubscribe();\n        const stream = this._contentObserver.observe(this._elementRef);\n        // TODO(mmalerba): We shouldn't be emitting on this @Output() outside the zone.\n        // Consider brining it back inside the zone next time we're making breaking changes.\n        // Bringing it back inside can cause things like infinite change detection loops and changed\n        // after checked errors if people's code isn't handling it properly.\n        this._ngZone.runOutsideAngular(() => {\n            this._currentSubscription = (this.debounce ? stream.pipe(debounceTime(this.debounce)) : stream).subscribe(this.event);\n        });\n    }\n    _unsubscribe() {\n        this._currentSubscription?.unsubscribe();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkObserveContent, deps: [{ token: ContentObserver }, { token: i0.ElementRef }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: CdkObserveContent, isStandalone: true, selector: \"[cdkObserveContent]\", inputs: { disabled: [\"cdkObserveContentDisabled\", \"disabled\", booleanAttribute], debounce: \"debounce\" }, outputs: { event: \"cdkObserveContent\" }, exportAs: [\"cdkObserveContent\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkObserveContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkObserveContent]',\n                    exportAs: 'cdkObserveContent',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: ContentObserver }, { type: i0.ElementRef }, { type: i0.NgZone }], propDecorators: { event: [{\n                type: Output,\n                args: ['cdkObserveContent']\n            }], disabled: [{\n                type: Input,\n                args: [{ alias: 'cdkObserveContentDisabled', transform: booleanAttribute }]\n            }], debounce: [{\n                type: Input\n            }] } });\nclass ObserversModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: ObserversModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: ObserversModule, imports: [CdkObserveContent], exports: [CdkObserveContent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: ObserversModule, providers: [MutationObserverFactory] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: ObserversModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CdkObserveContent],\n                    exports: [CdkObserveContent],\n                    providers: [MutationObserverFactory],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkObserveContent, ContentObserver, MutationObserverFactory, ObserversModule };\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,oBAAoB,QAAQ,uBAAuB;AAC3E,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAC9G,SAASC,UAAU,EAAEC,OAAO,QAAQ,MAAM;AAC1C,SAASC,GAAG,EAAEC,MAAM,EAAEC,YAAY,QAAQ,gBAAgB;;AAE1D;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACC,MAAM,EAAE;EAChC;EACA,IAAIA,MAAM,CAACC,IAAI,KAAK,eAAe,IAAID,MAAM,CAACE,MAAM,YAAYC,OAAO,EAAE;IACrE,OAAO,IAAI;EACf;EACA;EACA,IAAIH,MAAM,CAACC,IAAI,KAAK,WAAW,EAAE;IAC7B,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,CAACK,UAAU,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC/C,IAAI,EAAEJ,MAAM,CAACK,UAAU,CAACD,CAAC,CAAC,YAAYD,OAAO,CAAC,EAAE;QAC5C,OAAO,KAAK;MAChB;IACJ;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,CAACO,YAAY,CAACD,MAAM,EAAEF,CAAC,EAAE,EAAE;MACjD,IAAI,EAAEJ,MAAM,CAACO,YAAY,CAACH,CAAC,CAAC,YAAYD,OAAO,CAAC,EAAE;QAC9C,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf;EACA;EACA,OAAO,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA,MAAMK,uBAAuB,CAAC;EAC1BC,MAAMA,CAACC,QAAQ,EAAE;IACb,OAAO,OAAOC,gBAAgB,KAAK,WAAW,GAAG,IAAI,GAAG,IAAIA,gBAAgB,CAACD,QAAQ,CAAC;EAC1F;EACA;IAAS,IAAI,CAACE,IAAI,YAAAC,gCAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFN,uBAAuB;IAAA,CAAoD;EAAE;EACvL;IAAS,IAAI,CAACO,KAAK,kBAD6E7B,EAAE,CAAA8B,kBAAA;MAAAC,KAAA,EACYT,uBAAuB;MAAAU,OAAA,EAAvBV,uBAAuB,CAAAI,IAAA;MAAAO,UAAA,EAAc;IAAM,EAAG;EAAE;AAClK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGlC,EAAE,CAAAmC,iBAAA,CAGXb,uBAAuB,EAAc,CAAC;IACrHP,IAAI,EAAEd,UAAU;IAChBmC,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AACV;AACA,MAAMI,eAAe,CAAC;EAClBC,WAAWA,CAACC,wBAAwB,EAAE;IAClC,IAAI,CAACA,wBAAwB,GAAGA,wBAAwB;IACxD;IACA,IAAI,CAACC,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC;EACtC;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACF,iBAAiB,CAACG,OAAO,CAAC,CAACC,CAAC,EAAEC,OAAO,KAAK,IAAI,CAACC,gBAAgB,CAACD,OAAO,CAAC,CAAC;EAClF;EACAE,OAAOA,CAACC,YAAY,EAAE;IAClB,MAAMH,OAAO,GAAG/C,aAAa,CAACkD,YAAY,CAAC;IAC3C,OAAO,IAAIxC,UAAU,CAAEyC,QAAQ,IAAK;MAChC,MAAMC,MAAM,GAAG,IAAI,CAACC,eAAe,CAACN,OAAO,CAAC;MAC5C,MAAMO,YAAY,GAAGF,MAAM,CACtBG,IAAI,CAAC3C,GAAG,CAAC4C,OAAO,IAAIA,OAAO,CAAC3C,MAAM,CAACG,MAAM,IAAI,CAACD,kBAAkB,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,MAAM,CAAC2C,OAAO,IAAI,CAAC,CAACA,OAAO,CAAClC,MAAM,CAAC,CAAC,CAChHmC,SAAS,CAACN,QAAQ,CAAC;MACxB,OAAO,MAAM;QACTG,YAAY,CAACI,WAAW,CAAC,CAAC;QAC1B,IAAI,CAACC,iBAAiB,CAACZ,OAAO,CAAC;MACnC,CAAC;IACL,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIM,eAAeA,CAACN,OAAO,EAAE;IACrB,IAAI,CAAC,IAAI,CAACL,iBAAiB,CAACkB,GAAG,CAACb,OAAO,CAAC,EAAE;MACtC,MAAMK,MAAM,GAAG,IAAIzC,OAAO,CAAC,CAAC;MAC5B,MAAMwC,QAAQ,GAAG,IAAI,CAACV,wBAAwB,CAAChB,MAAM,CAACoC,SAAS,IAAIT,MAAM,CAACU,IAAI,CAACD,SAAS,CAAC,CAAC;MAC1F,IAAIV,QAAQ,EAAE;QACVA,QAAQ,CAACF,OAAO,CAACF,OAAO,EAAE;UACtBgB,aAAa,EAAE,IAAI;UACnBC,SAAS,EAAE,IAAI;UACfC,OAAO,EAAE;QACb,CAAC,CAAC;MACN;MACA,IAAI,CAACvB,iBAAiB,CAACwB,GAAG,CAACnB,OAAO,EAAE;QAAEI,QAAQ;QAAEC,MAAM;QAAEe,KAAK,EAAE;MAAE,CAAC,CAAC;IACvE,CAAC,MACI;MACD,IAAI,CAACzB,iBAAiB,CAAC0B,GAAG,CAACrB,OAAO,CAAC,CAACoB,KAAK,EAAE;IAC/C;IACA,OAAO,IAAI,CAACzB,iBAAiB,CAAC0B,GAAG,CAACrB,OAAO,CAAC,CAACK,MAAM;EACrD;EACA;AACJ;AACA;AACA;EACIO,iBAAiBA,CAACZ,OAAO,EAAE;IACvB,IAAI,IAAI,CAACL,iBAAiB,CAACkB,GAAG,CAACb,OAAO,CAAC,EAAE;MACrC,IAAI,CAACL,iBAAiB,CAAC0B,GAAG,CAACrB,OAAO,CAAC,CAACoB,KAAK,EAAE;MAC3C,IAAI,CAAC,IAAI,CAACzB,iBAAiB,CAAC0B,GAAG,CAACrB,OAAO,CAAC,CAACoB,KAAK,EAAE;QAC5C,IAAI,CAACnB,gBAAgB,CAACD,OAAO,CAAC;MAClC;IACJ;EACJ;EACA;EACAC,gBAAgBA,CAACD,OAAO,EAAE;IACtB,IAAI,IAAI,CAACL,iBAAiB,CAACkB,GAAG,CAACb,OAAO,CAAC,EAAE;MACrC,MAAM;QAAEI,QAAQ;QAAEC;MAAO,CAAC,GAAG,IAAI,CAACV,iBAAiB,CAAC0B,GAAG,CAACrB,OAAO,CAAC;MAChE,IAAII,QAAQ,EAAE;QACVA,QAAQ,CAACkB,UAAU,CAAC,CAAC;MACzB;MACAjB,MAAM,CAACkB,QAAQ,CAAC,CAAC;MACjB,IAAI,CAAC5B,iBAAiB,CAAC6B,MAAM,CAACxB,OAAO,CAAC;IAC1C;EACJ;EACA;IAAS,IAAI,CAACnB,IAAI,YAAA4C,wBAAA1C,CAAA;MAAA,YAAAA,CAAA,IAAwFS,eAAe,EA3EzBrC,EAAE,CAAAuE,QAAA,CA2EyCjD,uBAAuB;IAAA,CAA6C;EAAE;EACjN;IAAS,IAAI,CAACO,KAAK,kBA5E6E7B,EAAE,CAAA8B,kBAAA;MAAAC,KAAA,EA4EYM,eAAe;MAAAL,OAAA,EAAfK,eAAe,CAAAX,IAAA;MAAAO,UAAA,EAAc;IAAM,EAAG;EAAE;AAC1J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA9EoGlC,EAAE,CAAAmC,iBAAA,CA8EXE,eAAe,EAAc,CAAC;IAC7GtB,IAAI,EAAEd,UAAU;IAChBmC,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAElB,IAAI,EAAEO;EAAwB,CAAC,CAAC;AAAA;AACrE;AACA;AACA;AACA;AACA,MAAMkD,iBAAiB,CAAC;EACpB;AACJ;AACA;AACA;EACI,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACE,KAAK,EAAE;IAChB,IAAI,CAACD,SAAS,GAAGC,KAAK;IACtB,IAAI,CAACD,SAAS,GAAG,IAAI,CAACE,YAAY,CAAC,CAAC,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;EAC5D;EACA;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACH,KAAK,EAAE;IAChB,IAAI,CAACI,SAAS,GAAGhF,oBAAoB,CAAC4E,KAAK,CAAC;IAC5C,IAAI,CAACE,UAAU,CAAC,CAAC;EACrB;EACAvC,WAAWA,CAAC0C,gBAAgB,EAAEC,WAAW,EAAEC,OAAO,EAAE;IAChD,IAAI,CAACF,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB;IACA,IAAI,CAACC,KAAK,GAAG,IAAIjF,YAAY,CAAC,CAAC;IAC/B,IAAI,CAACwE,SAAS,GAAG,KAAK;IACtB,IAAI,CAACU,oBAAoB,GAAG,IAAI;EACpC;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACD,oBAAoB,IAAI,CAAC,IAAI,CAACX,QAAQ,EAAE;MAC9C,IAAI,CAACI,UAAU,CAAC,CAAC;IACrB;EACJ;EACAnC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACkC,YAAY,CAAC,CAAC;EACvB;EACAC,UAAUA,CAAA,EAAG;IACT,IAAI,CAACD,YAAY,CAAC,CAAC;IACnB,MAAM1B,MAAM,GAAG,IAAI,CAAC8B,gBAAgB,CAACjC,OAAO,CAAC,IAAI,CAACkC,WAAW,CAAC;IAC9D;IACA;IACA;IACA;IACA,IAAI,CAACC,OAAO,CAACI,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAACF,oBAAoB,GAAG,CAAC,IAAI,CAACN,QAAQ,GAAG5B,MAAM,CAACG,IAAI,CAACzC,YAAY,CAAC,IAAI,CAACkE,QAAQ,CAAC,CAAC,GAAG5B,MAAM,EAAEK,SAAS,CAAC,IAAI,CAAC4B,KAAK,CAAC;IACzH,CAAC,CAAC;EACN;EACAP,YAAYA,CAAA,EAAG;IACX,IAAI,CAACQ,oBAAoB,EAAE5B,WAAW,CAAC,CAAC;EAC5C;EACA;IAAS,IAAI,CAAC9B,IAAI,YAAA6D,0BAAA3D,CAAA;MAAA,YAAAA,CAAA,IAAwF4C,iBAAiB,EAzI3BxE,EAAE,CAAAwF,iBAAA,CAyI2CnD,eAAe,GAzI5DrC,EAAE,CAAAwF,iBAAA,CAyIuExF,EAAE,CAACyF,UAAU,GAzItFzF,EAAE,CAAAwF,iBAAA,CAyIiGxF,EAAE,CAAC0F,MAAM;IAAA,CAA4C;EAAE;EAC1P;IAAS,IAAI,CAACC,IAAI,kBA1I8E3F,EAAE,CAAA4F,iBAAA;MAAA7E,IAAA,EA0IJyD,iBAAiB;MAAAqB,SAAA;MAAAC,MAAA;QAAArB,QAAA,GA1IfzE,EAAE,CAAA+F,YAAA,CAAAC,0BAAA,2CA0IkI7F,gBAAgB;QAAA2E,QAAA;MAAA;MAAAmB,OAAA;QAAAd,KAAA;MAAA;MAAAe,QAAA;MAAAC,UAAA;MAAAC,QAAA,GA1IpJpG,EAAE,CAAAqG,wBAAA;IAAA,EA0IsQ;EAAE;AAC9W;AACA;EAAA,QAAAnE,SAAA,oBAAAA,SAAA,KA5IoGlC,EAAE,CAAAmC,iBAAA,CA4IXqC,iBAAiB,EAAc,CAAC;IAC/GzD,IAAI,EAAEX,SAAS;IACfgC,IAAI,EAAE,CAAC;MACCkE,QAAQ,EAAE,qBAAqB;MAC/BJ,QAAQ,EAAE,mBAAmB;MAC7BC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEpF,IAAI,EAAEsB;EAAgB,CAAC,EAAE;IAAEtB,IAAI,EAAEf,EAAE,CAACyF;EAAW,CAAC,EAAE;IAAE1E,IAAI,EAAEf,EAAE,CAAC0F;EAAO,CAAC,CAAC,EAAkB;IAAEP,KAAK,EAAE,CAAC;MACvHpE,IAAI,EAAEV,MAAM;MACZ+B,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAEqC,QAAQ,EAAE,CAAC;MACX1D,IAAI,EAAET,KAAK;MACX8B,IAAI,EAAE,CAAC;QAAEmE,KAAK,EAAE,2BAA2B;QAAEC,SAAS,EAAErG;MAAiB,CAAC;IAC9E,CAAC,CAAC;IAAE2E,QAAQ,EAAE,CAAC;MACX/D,IAAI,EAAET;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMmG,eAAe,CAAC;EAClB;IAAS,IAAI,CAAC/E,IAAI,YAAAgF,wBAAA9E,CAAA;MAAA,YAAAA,CAAA,IAAwF6E,eAAe;IAAA,CAAkD;EAAE;EAC7K;IAAS,IAAI,CAACE,IAAI,kBA9J8E3G,EAAE,CAAA4G,gBAAA;MAAA7F,IAAA,EA8JS0F;IAAe,EAA+D;EAAE;EAC3L;IAAS,IAAI,CAACI,IAAI,kBA/J8E7G,EAAE,CAAA8G,gBAAA;MAAAC,SAAA,EA+JqC,CAACzF,uBAAuB;IAAC,EAAG;EAAE;AACzK;AACA;EAAA,QAAAY,SAAA,oBAAAA,SAAA,KAjKoGlC,EAAE,CAAAmC,iBAAA,CAiKXsE,eAAe,EAAc,CAAC;IAC7G1F,IAAI,EAAER,QAAQ;IACd6B,IAAI,EAAE,CAAC;MACC4E,OAAO,EAAE,CAACxC,iBAAiB,CAAC;MAC5ByC,OAAO,EAAE,CAACzC,iBAAiB,CAAC;MAC5BuC,SAAS,EAAE,CAACzF,uBAAuB;IACvC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASkD,iBAAiB,EAAEnC,eAAe,EAAEf,uBAAuB,EAAEmF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}