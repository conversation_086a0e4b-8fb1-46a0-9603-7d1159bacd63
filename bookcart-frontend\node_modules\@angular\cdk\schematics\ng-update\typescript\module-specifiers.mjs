"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.isMaterialExportDeclaration = exports.isMaterialImportDeclaration = exports.cdkModuleSpecifier = exports.materialModuleSpecifier = void 0;
const imports_1 = require("../typescript/imports");
/** Name of the Angular Material module specifier. */
exports.materialModuleSpecifier = '@angular/material';
/** Name of the Angular CDK module specifier. */
exports.cdkModuleSpecifier = '@angular/cdk';
/** Whether the specified node is part of an Angular Material or CDK import declaration. */
function isMaterialImportDeclaration(node) {
    return isMaterialDeclaration((0, imports_1.getImportDeclaration)(node));
}
exports.isMaterialImportDeclaration = isMaterialImportDeclaration;
/** Whether the specified node is part of an Angular Material or CDK import declaration. */
function isMaterialExportDeclaration(node) {
    return isMaterialDeclaration((0, imports_1.getExportDeclaration)(node));
}
exports.isMaterialExportDeclaration = isMaterialExportDeclaration;
/** Whether the declaration is part of Angular Material. */
function isMaterialDeclaration(declaration) {
    if (!declaration.moduleSpecifier) {
        return false;
    }
    const moduleSpecifier = declaration.moduleSpecifier.getText();
    return (moduleSpecifier.indexOf(exports.materialModuleSpecifier) !== -1 ||
        moduleSpecifier.indexOf(exports.cdkModuleSpecifier) !== -1);
}
//# sourceMappingURL=data:application/json;base64,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