/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// TODO(atscott): flip default internally ASAP and externally for v18 (#52928)
let _ensureDirtyViewsAreAlwaysReachable = false;
export function getEnsureDirtyViewsAreAlwaysReachable() {
    return _ensureDirtyViewsAreAlwaysReachable;
}
export function setEnsureDirtyViewsAreAlwaysReachable(v) {
    _ensureDirtyViewsAreAlwaysReachable = v;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZmxhZ3MuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9jb3JlL3NyYy9jaGFuZ2VfZGV0ZWN0aW9uL2ZsYWdzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7R0FNRztBQUVILDhFQUE4RTtBQUM5RSxJQUFJLG1DQUFtQyxHQUFHLEtBQUssQ0FBQztBQUVoRCxNQUFNLFVBQVUscUNBQXFDO0lBQ25ELE9BQU8sbUNBQW1DLENBQUM7QUFDN0MsQ0FBQztBQUNELE1BQU0sVUFBVSxxQ0FBcUMsQ0FBQyxDQUFVO0lBQzlELG1DQUFtQyxHQUFHLENBQUMsQ0FBQztBQUMxQyxDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5cbi8vIFRPRE8oYXRzY290dCk6IGZsaXAgZGVmYXVsdCBpbnRlcm5hbGx5IEFTQVAgYW5kIGV4dGVybmFsbHkgZm9yIHYxOCAoIzUyOTI4KVxubGV0IF9lbnN1cmVEaXJ0eVZpZXdzQXJlQWx3YXlzUmVhY2hhYmxlID0gZmFsc2U7XG5cbmV4cG9ydCBmdW5jdGlvbiBnZXRFbnN1cmVEaXJ0eVZpZXdzQXJlQWx3YXlzUmVhY2hhYmxlKCkge1xuICByZXR1cm4gX2Vuc3VyZURpcnR5Vmlld3NBcmVBbHdheXNSZWFjaGFibGU7XG59XG5leHBvcnQgZnVuY3Rpb24gc2V0RW5zdXJlRGlydHlWaWV3c0FyZUFsd2F5c1JlYWNoYWJsZSh2OiBib29sZWFuKSB7XG4gIF9lbnN1cmVEaXJ0eVZpZXdzQXJlQWx3YXlzUmVhY2hhYmxlID0gdjtcbn1cbiJdfQ==