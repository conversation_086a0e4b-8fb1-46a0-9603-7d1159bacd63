/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { InjectionToken } from '@angular/core';
/**
 * Injection token that can be used to reference instances of an `ListOption`. It serves
 * as alternative token to an actual implementation which could result in undesired
 * retention of the class or circular references breaking runtime execution.
 * @docs-private
 */
export const LIST_OPTION = new InjectionToken('ListOption');
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibGlzdC1vcHRpb24tdHlwZXMuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi9zcmMvbWF0ZXJpYWwvbGlzdC9saXN0LW9wdGlvbi10eXBlcy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7O0dBTUc7QUFFSCxPQUFPLEVBQUMsY0FBYyxFQUFDLE1BQU0sZUFBZSxDQUFDO0FBaUI3Qzs7Ozs7R0FLRztBQUNILE1BQU0sQ0FBQyxNQUFNLFdBQVcsR0FBRyxJQUFJLGNBQWMsQ0FBYSxZQUFZLENBQUMsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuXG5pbXBvcnQge0luamVjdGlvblRva2VufSBmcm9tICdAYW5ndWxhci9jb3JlJztcblxuLyoqXG4gKiBUeXBlIGRlc2NyaWJpbmcgcG9zc2libGUgcG9zaXRpb25zIG9mIGEgY2hlY2tib3ggb3IgcmFkaW8gaW4gYSBsaXN0IG9wdGlvblxuICogd2l0aCByZXNwZWN0IHRvIHRoZSBsaXN0IGl0ZW0ncyB0ZXh0LlxuICovXG5leHBvcnQgdHlwZSBNYXRMaXN0T3B0aW9uVG9nZ2xlUG9zaXRpb24gPSAnYmVmb3JlJyB8ICdhZnRlcic7XG5cbi8qKlxuICogSW50ZXJmYWNlIGRlc2NyaWJpbmcgYSBsaXN0IG9wdGlvbi4gVGhpcyBpcyB1c2VkIHRvIGF2b2lkIGNpcmN1bGFyXG4gKiBkZXBlbmRlbmNpZXMgYmV0d2VlbiB0aGUgbGlzdC1vcHRpb24gYW5kIHRoZSBzdHlsZXIgZGlyZWN0aXZlcy5cbiAqIEBkb2NzLXByaXZhdGVcbiAqL1xuZXhwb3J0IGludGVyZmFjZSBMaXN0T3B0aW9uIHtcbiAgX2dldFRvZ2dsZVBvc2l0aW9uKCk6IE1hdExpc3RPcHRpb25Ub2dnbGVQb3NpdGlvbjtcbn1cblxuLyoqXG4gKiBJbmplY3Rpb24gdG9rZW4gdGhhdCBjYW4gYmUgdXNlZCB0byByZWZlcmVuY2UgaW5zdGFuY2VzIG9mIGFuIGBMaXN0T3B0aW9uYC4gSXQgc2VydmVzXG4gKiBhcyBhbHRlcm5hdGl2ZSB0b2tlbiB0byBhbiBhY3R1YWwgaW1wbGVtZW50YXRpb24gd2hpY2ggY291bGQgcmVzdWx0IGluIHVuZGVzaXJlZFxuICogcmV0ZW50aW9uIG9mIHRoZSBjbGFzcyBvciBjaXJjdWxhciByZWZlcmVuY2VzIGJyZWFraW5nIHJ1bnRpbWUgZXhlY3V0aW9uLlxuICogQGRvY3MtcHJpdmF0ZVxuICovXG5leHBvcnQgY29uc3QgTElTVF9PUFRJT04gPSBuZXcgSW5qZWN0aW9uVG9rZW48TGlzdE9wdGlvbj4oJ0xpc3RPcHRpb24nKTtcbiJdfQ==