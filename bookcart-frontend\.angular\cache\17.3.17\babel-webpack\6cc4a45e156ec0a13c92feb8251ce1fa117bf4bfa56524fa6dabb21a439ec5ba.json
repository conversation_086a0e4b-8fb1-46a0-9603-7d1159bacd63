{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport class RoleGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate(route) {\n    const expectedRole = route.data['expectedRole'];\n    if (this.authService.isAuthenticated() && this.authService.hasRole(expectedRole)) {\n      return true;\n    } else {\n      this.router.navigate(['/unauthorized']);\n      return false;\n    }\n  }\n  static {\n    this.ɵfac = function RoleGuard_Factory(t) {\n      return new (t || RoleGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: RoleGuard,\n      factory: RoleGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "constructor", "authService", "router", "canActivate", "route", "expectedRole", "data", "isAuthenticated", "hasRole", "navigate", "i0", "ɵɵinject", "i1", "AuthService", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\BookCart\\bookcart-frontend\\src\\app\\shared\\guards\\role.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { CanActivate, ActivatedRouteSnapshot, Router } from '@angular/router';\nimport { AuthService } from '../services/auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class RoleGuard implements CanActivate {\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  canActivate(route: ActivatedRouteSnapshot): boolean {\n    const expectedRole = route.data['expectedRole'];\n    \n    if (this.authService.isAuthenticated() && this.authService.hasRole(expectedRole)) {\n      return true;\n    } else {\n      this.router.navigate(['/unauthorized']);\n      return false;\n    }\n  }\n}\n"], "mappings": ";;;AAOA,OAAM,MAAOA,SAAS;EAEpBC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,WAAWA,CAACC,KAA6B;IACvC,MAAMC,YAAY,GAAGD,KAAK,CAACE,IAAI,CAAC,cAAc,CAAC;IAE/C,IAAI,IAAI,CAACL,WAAW,CAACM,eAAe,EAAE,IAAI,IAAI,CAACN,WAAW,CAACO,OAAO,CAACH,YAAY,CAAC,EAAE;MAChF,OAAO,IAAI;KACZ,MAAM;MACL,IAAI,CAACH,MAAM,CAACO,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;MACvC,OAAO,KAAK;;EAEhB;;;uBAhBWV,SAAS,EAAAW,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAThB,SAAS;MAAAiB,OAAA,EAATjB,SAAS,CAAAkB,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}