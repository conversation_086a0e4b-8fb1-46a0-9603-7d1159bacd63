{"version": 3, "sources": ["../../../../../../packages/compiler-cli/src/transformers/jit_transforms/downlevel_decorators_transform.ts", "../../../../../../packages/compiler-cli/src/transformers/jit_transforms/initializer_api_transforms/transform.ts", "../../../../../../packages/compiler-cli/src/transformers/jit_transforms/initializer_api_transforms/transform_api.ts", "../../../../../../packages/compiler-cli/src/transformers/jit_transforms/initializer_api_transforms/input_function.ts", "../../../../../../packages/compiler-cli/src/transformers/jit_transforms/initializer_api_transforms/model_function.ts", "../../../../../../packages/compiler-cli/src/transformers/jit_transforms/initializer_api_transforms/output_function.ts", "../../../../../../packages/compiler-cli/src/transformers/jit_transforms/initializer_api_transforms/query_functions.ts", "../../../../../../packages/compiler-cli/src/transformers/jit_transforms/index.ts", "../../../../../../packages/compiler-cli/private/tooling.ts"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAQA,OAAO,QAAQ;AASf,SAASA,oBAAmB,WAAsB,QAAe;AAC/D,SAAO,UAAW,UAAU,WAAW,QAAQ,UAAU,OAAO,SAAS;AAC3E;AAwBA,IAAM,kCAAkC;AASxC,SAAS,mCACL,WAAyB,aAA4B;AACvD,QAAM,qBAAoD,CAAA;AAC1D,QAAM,OAAO,UAAU;AACvB,UAAQ,KAAK,MAAM;IACjB,KAAK,GAAG,WAAW;AAEjB,yBAAmB,KAAK,GAAG,QAAQ,yBAAyB,QAAQ,IAAI,CAAC;AACzE;IACF,KAAK,GAAG,WAAW;AAEjB,YAAM,OAAO;AACb,yBAAmB,KAAK,GAAG,QAAQ,yBAAyB,QAAQ,KAAK,UAAU,CAAC;AACpF,UAAI,KAAK,UAAU,QAAQ;AACzB,cAAM,OAAwB,CAAA;AAC9B,mBAAW,OAAO,KAAK,WAAW;AAChC,eAAK,KAAK,GAAG;QACf;AACA,cAAM,mBACF,GAAG,QAAQ,6BAA6B,GAAG,QAAQ,gBAAgB,MAAM,IAAI,CAAC;AAClF,2BAAmB,KAAK,GAAG,QAAQ,yBAAyB,QAAQ,gBAAgB,CAAC;MACvF;AACA;IACF;AACE,kBAAY,KAAK;QACf,MAAM,UAAU,cAAa;QAC7B,OAAO,UAAU,SAAQ;QACzB,QAAQ,UAAU,OAAM,IAAK,UAAU,SAAQ;QAC/C,aACI,GAAG,GAAG,WAAW,UAAU;QAC/B,UAAU,GAAG,mBAAmB;QAChC,MAAM;OACP;AACD;EACJ;AACA,SAAO,GAAG,QAAQ,8BAA8B,kBAAkB;AACpE;AAeA,SAAS,kCACL,aACA,wBACA,gBACA,0BAAiC;AACnC,QAAM,SAA0B,CAAA;AAEhC,aAAW,aAAa,gBAAgB;AACtC,QAAI,CAAC,UAAU,QAAQ,UAAU,WAAW,WAAW,GAAG;AACxD,aAAO,KAAK,GAAG,QAAQ,WAAU,CAAE;AACnC;IACF;AAEA,UAAM,YAAY,UAAU,OACxB,0BAA0B,wBAAwB,UAAU,IAAI,IAChE;AACJ,UAAM,UAAU,CAAC,GAAG,QAAQ,yBACxB,QAAQ,aAAa,GAAG,QAAQ,iBAAiB,WAAW,CAAC,CAAC;AAElE,UAAM,aAA2C,CAAA;AACjD,eAAW,QAAQ,UAAU,YAAY;AACvC,iBAAW,KAAK,mCAAmC,MAAM,WAAW,CAAC;IACvE;AACA,QAAI,WAAW,QAAQ;AACrB,cAAQ,KAAK,GAAG,QAAQ,yBACpB,cAAc,GAAG,QAAQ,6BAA6B,UAAU,CAAC,CAAC;IACxE;AACA,WAAO,KAAK,GAAG,QAAQ,8BAA8B,OAAO,CAAC;EAC/D;AAEA,QAAM,cAAc,GAAG,QAAQ,oBAC3B,QAAW,QAAW,CAAA,GAAI,QAC1B,GAAG,QAAQ,YAAY,GAAG,WAAW,sBAAsB,GAC3D,GAAG,QAAQ,6BAA6B,QAAQ,IAAI,CAAC;AACzD,QAAM,WAAW,GAAG,QAAQ,0BACxB,CAAC,GAAG,QAAQ,YAAY,GAAG,WAAW,aAAa,CAAC,GAAG,kBAAkB,QAAW,QACpF,WAAW;AACf,MAAI,0BAA0B;AAC5B,OAAG,4BAA4B,UAAU;MACvC;QACE,MAAM,GAAG,WAAW;QACpB,MAAM;UACJ;UACA;UACA;UACA,+BAA+B;UAC/B;UACA;UACA;UACA,KAAK,IAAI;QACX,KAAK;QACL,KAAK;QACL,oBAAoB;;KAEvB;EACH;AACA,SAAO;AACT;AAUA,SAAS,0BACL,wBACA,MAAiB;AACnB,MAAI,OAAO,KAAK;AAChB,MAAI,GAAG,kBAAkB,IAAI,GAAG;AAE9B,WAAO,KAAK,QAAQ;EACtB;AACA,UAAQ,MAAM;IACZ,KAAK,GAAG,WAAW;IACnB,KAAK,GAAG,WAAW;AACjB,aAAO,GAAG,QAAQ,iBAAiB,UAAU;IAC/C,KAAK,GAAG,WAAW;IACnB,KAAK,GAAG,WAAW;AACjB,aAAO,GAAG,QAAQ,iBAAiB,OAAO;IAC5C,KAAK,GAAG,WAAW;IACnB,KAAK,GAAG,WAAW;IACnB,KAAK,GAAG,WAAW;IACnB,KAAK,GAAG,WAAW;AACjB,aAAO,GAAG,QAAQ,iBAAiB,SAAS;IAC9C,KAAK,GAAG,WAAW;IACnB,KAAK,GAAG,WAAW;AACjB,aAAO,GAAG,QAAQ,iBAAiB,QAAQ;IAC7C,KAAK,GAAG,WAAW;AACjB,aAAO,GAAG,QAAQ,iBAAiB,QAAQ;IAC7C,KAAK,GAAG,WAAW;IACnB,KAAK,GAAG,WAAW;AACjB,aAAO,GAAG,QAAQ,iBAAiB,QAAQ;IAC7C,KAAK,GAAG,WAAW;AACjB,YAAM,UAAU;AAEhB,aAAO,uBAAuB,QAAQ,QAAQ;IAChD,KAAK,GAAG,WAAW;AACjB,YAAM,iBACD,KACI,MAAM,OACH,OAAK,EAAE,GAAG,kBAAkB,CAAC,KAAK,EAAE,QAAQ,SAAS,GAAG,WAAW,YAAY;AAC3F,aAAO,eAAe,WAAW,IAC7B,0BAA0B,wBAAwB,eAAe,EAAE,IACnE;IACN;AACE,aAAO;EACX;AACF;AASA,SAAS,qBAAqB,aAA6B,QAAiB;AAC1E,MAAI,OAAO,QAAQ,GAAG,YAAY,OAAO;AACvC,aAAS,YAAY,iBAAiB,MAAM;EAC9C;AAIA,UAAQ,OAAO,QAAQ,GAAG,YAAY,QAAQ,GAAG,YAAY,uBAAuB;AACtF;AAyBM,SAAU,gCACZ,aAA6B,MAAsB,aACnD,QAAiB,0BAAiC;AACpD,WAAS,uBAAuB,MAAe,WAAiB;AAC9D,QAAI,CAAC,0BAA0B;AAC7B;IACF;AAEA,OAAG,4BAA4B,MAAM;MACnC;QACE,MAAM,GAAG,WAAW;QACpB,MAAM,YAAY;QAClB,KAAK;QACL,KAAK;QACL,oBAAoB;;KAEvB;EACH;AAYA,WAAS,kCACLC,cACA,YAAuC;AAGzC,UAAM,UAAyC,CAAA;AAC/C,eAAW,CAAC,MAAM,UAAU,KAAK,WAAW,QAAO,GAAI;AACrD,cAAQ,KAAK,GAAG,QAAQ,yBACpB,MACA,GAAG,QAAQ,6BACP,WAAW,IAAI,UAAQ,mCAAmC,MAAMA,YAAW,CAAC,CAAC,CAAC,CAAC;IACzF;AACA,UAAM,cAAc,GAAG,QAAQ,8BAA8B,SAAS,IAAI;AAC1E,UAAM,OAAO,GAAG,QAAQ,0BACpB,CAAC,GAAG,QAAQ,YAAY,GAAG,WAAW,aAAa,CAAC,GAAG,kBAAkB,QACzE,QAAW,WAAW;AAC1B,2BAAuB,MAAM,mBAAmB,kCAAkC;AAClF,WAAO;EACT;AAEA,SAAO,CAAC,YAAqC;AAM3C,UAAM,2BAA2B,sCAAsC,OAAO;AAQ9E,aAAS,uBAAuB,MAAmB;AACjD,YAAM,SAAS,YAAY,oBAAoB,IAAI;AAGnD,UAAI,CAAC,UAAU,CAAC,qBAAqB,aAAa,MAAM,KAAK,CAAC,OAAO,gBACjE,OAAO,aAAa,WAAW,GAAG;AACpC,eAAO;MACT;AAGA,UAAI,GAAG,gBAAgB,IAAI,GAAG;AAC5B,cAAM,gBAAgB,uBAAuB,KAAK,IAAI;AACtD,YAAI,kBAAkB,QAAW;AAC/B,iBAAO;QACT;AACA,eAAO,GAAG,QAAQ,+BAA+B,eAAe,KAAK,KAAK;MAC5E;AACA,YAAM,OAAO,OAAO,aAAa;AAIjC,UAAI,yBAAyB,IAAI,GAAG;AAClC,iCAAyB,IAAI,IAAI;AAWjC,YAAI,KAAK,SAAS,QAAW;AAC3B,iBAAO,GAAG,gBAAgB,GAAG,QAAQ,iBAAiB,KAAK,KAAK,IAAI,GAAG,KAAK,IAAI;QAClF;MACF;AAIA,aAAO,GAAG,gBAAgB,GAAG,QAAQ,iBAAiB,KAAK,IAAI,GAAG,IAAI;IACxE;AAOA,aAAS,sBAAsB,SAAwB;AAErD,gBAAU,GAAG,eAAe,SAAS,2BAA2B,OAAO;AACvE,YAAM,mBAAmC,CAAA;AACzC,YAAM,UAA0B,CAAA;AAChC,YAAM,aAAa,KAAK,2BAA2B,OAAO,KAAK,CAAA;AAC/D,iBAAW,aAAa,YAAY;AAGlC,cAAM,gBAAgB,UAAU;AAChC,YAAI,CAACD,oBAAmB,WAAW,MAAM,GAAG;AAC1C,2BAAiB,KAAK,aAAa;AACnC;QACF;AACA,gBAAQ,KAAK,aAAa;MAC5B;AACA,UAAI,CAAC,QAAQ;AAAQ,eAAO,CAAC,QAAW,SAAS,CAAA,CAAE;AAEnD,UAAI,CAAC,QAAQ,QAAQ,CAAC,GAAG,aAAa,QAAQ,IAAI,GAAG;AAGnD,oBAAY,KAAK;UACf,MAAM,QAAQ,cAAa;UAC3B,OAAO,QAAQ,SAAQ;UACvB,QAAQ,QAAQ,OAAM,IAAK,QAAQ,SAAQ;UAC3C,aAAa;UACb,UAAU,GAAG,mBAAmB;UAChC,MAAM;SACP;AACD,eAAO,CAAC,QAAW,SAAS,CAAA,CAAE;MAChC;AAEA,YAAM,mBAAmB,GAAG,iBAAiB,OAAO,IAAI,GAAG,aAAa,OAAO,IAAI;AACnF,UAAI;AAEJ,UAAI,iBAAiB,WAAU,qDAAkB,SAAQ;AACvD,oBAAY,GAAG,aACX,GAAG,QAAQ,gBAAgB,CAAC,GAAG,kBAAkB,GAAI,oBAAoB,CAAA,CAAG,CAAC,GAC5E,QAA4B,SAAS;MAC5C;AAEA,aAAO,CAAC,QAAQ,KAAK,MAAM,+BAA+B,SAAS,SAAS,GAAG,OAAO;IACxF;AAMA,aAAS,qBAAqB,MAA+B;AAE3D,aAAO,GAAG,eAAe,MAAM,2BAA2B,OAAO;AAEjE,YAAM,gBAA2C,CAAA;AACjD,YAAM,gBAAgB,KAAK;AAC3B,YAAM,iBAA4C,CAAA;AAElD,iBAAW,SAAS,eAAe;AACjC,cAAM,mBAAmC,CAAA;AACzC,cAAM,YAAqC,EAAC,YAAY,CAAA,GAAI,MAAM,KAAI;AACtE,cAAM,aAAa,KAAK,2BAA2B,KAAK,KAAK,CAAA;AAE7D,mBAAW,aAAa,YAAY;AAGlC,gBAAM,gBAAgB,UAAU;AAChC,cAAI,CAACA,oBAAmB,WAAW,MAAM,GAAG;AAC1C,6BAAiB,KAAK,aAAa;AACnC;UACF;AACA,oBAAW,WAAW,KAAK,aAAa;QAC1C;AACA,YAAI,MAAM,MAAM;AAKd,oBAAW,OAAO,MAAM;QAC1B;AACA,uBAAe,KAAK,SAAS;AAG7B,YAAI;AACJ,cAAM,iBAAiB,GAAG,aAAa,KAAK;AAE5C,YAAI,iBAAiB,WAAU,iDAAgB,SAAQ;AACrD,sBAAY,CAAC,GAAG,kBAAkB,GAAI,kBAAkB,CAAA,CAAG;QAC7D;AAEA,cAAM,WAAW,GAAG,QAAQ,2BACxB,OAAO,WAAW,MAAM,gBAAgB,MAAM,MAAM,MAAM,eAAe,MAAM,MAC/E,MAAM,WAAW;AACrB,sBAAc,KAAK,QAAQ;MAC7B;AACA,YAAM,UAAU,GAAG,QAAQ,6BACvB,MAAM,GAAG,aAAa,IAAI,GAAG,eAAe,KAAK,IAAI;AACzD,aAAO,CAAC,SAAS,cAAc;IACjC;AASA,aAAS,0BAA0B,WAA8B;AAC/D,YAAM,aAAgC,CAAA;AACtC,YAAM,sBAAsB,oBAAI,IAAG;AACnC,UAAI,kBAAkD;AAEtD,iBAAW,UAAU,UAAU,SAAS;AACtC,gBAAQ,OAAO,MAAM;UACnB,KAAK,GAAG,WAAW;UACnB,KAAK,GAAG,WAAW;UACnB,KAAK,GAAG,WAAW;UACnB,KAAK,GAAG,WAAW,mBAAmB;AACpC,kBAAM,CAAC,MAAM,WAAW,UAAU,IAAI,sBAAsB,MAAM;AAClE,uBAAW,KAAK,SAAS;AACzB,gBAAI;AAAM,kCAAoB,IAAI,MAAM,UAAU;AAClD;UACF;UACA,KAAK,GAAG,WAAW,aAAa;AAC9B,kBAAM,OAAO;AACb,gBAAI,CAAC,KAAK;AAAM;AAChB,kBAAM,CAAC,WAAW,cAAc,IAC5B,qBAAqB,MAAmC;AAC5D,8BAAkB;AAClB,uBAAW,KAAK,SAAS;AACzB;UACF;UACA;AACE;QACJ;AACA,mBAAW,KAAK,GAAG,eAAe,QAAQ,2BAA2B,OAAO,CAAC;MAC/E;AAIA,YAAM,4BAA4B,KAAK,2BAA2B,SAAS,KAAK,CAAA;AAIhF,YAAM,sBACF,0BAA0B,KAAK,OAAKA,oBAAmB,GAAG,MAAM,CAAC;AAErE,UAAI,iBAAiB;AACnB,YAAI,uBAAuB,gBAAgB,KAAK,OAAK,CAAC,CAAC,EAAE,WAAW,MAAM,GAAG;AAG3E,qBAAW,KAAK,kCACZ,aAAa,wBAAwB,iBAAiB,wBAAwB,CAAC;QACrF;MACF;AACA,UAAI,oBAAoB,MAAM;AAC5B,mBAAW,KAAK,kCAAkC,aAAa,mBAAmB,CAAC;MACrF;AAEA,YAAM,UAAU,GAAG,aACf,GAAG,QAAQ,gBAAgB,YAAY,UAAU,QAAQ,gBAAgB,GACzE,UAAU,OAAO;AAErB,aAAO,GAAG,QAAQ,uBACd,WAAW,UAAU,WAAW,UAAU,MAAM,UAAU,gBAC1D,UAAU,iBAAiB,OAAO;IACxC;AAOA,aAAS,0BAA0B,MAAa;AAC9C,UAAI,GAAG,mBAAmB,IAAI,GAAG;AAC/B,eAAO,0BAA0B,IAAI;MACvC;AACA,aAAO,GAAG,eAAe,MAAM,2BAA2B,OAAO;IACnE;AAEA,WAAO,CAAC,OAAqB;AAI3B,aAAO,GAAG,eAAe,IAAI,2BAA2B,OAAO;IACjE;EACF;AACF;AAEA,SAAS,+BACL,MAAuB,WAA+C;AACxE,MAAI;AAEJ,MAAI,GAAG,oBAAoB,IAAI,GAAG;AAChC,YAAQ,GAAG,QAAQ,wBACf,WAAW,KAAK,eAAe,KAAK,MAAM,KAAK,eAAe,KAAK,gBACnE,KAAK,YAAY,KAAK,MAAM,KAAK,IAAI;EAC3C,WAAW,GAAG,sBAAsB,IAAI,GAAG;AACzC,YAAQ,GAAG,QAAQ,0BACf,WAAW,KAAK,MAAM,KAAK,eAAe,KAAK,MAAM,KAAK,WAAW;EAC3E,WAAW,GAAG,cAAc,IAAI,GAAG;AACjC,YAAQ,GAAG,QAAQ,6BACf,WAAW,KAAK,MAAM,KAAK,YAAY,KAAK,MAAM,KAAK,IAAI;EACjE,WAAW,GAAG,cAAc,IAAI,GAAG;AACjC,YACI,GAAG,QAAQ,6BAA6B,WAAW,KAAK,MAAM,KAAK,YAAY,KAAK,IAAI;EAC9F,OAAO;AACL,UAAM,IAAI,MAAM,0CAA0C,GAAG,WAAW,KAAK,OAAO;EACtF;AAEA,SAAO,GAAG,gBAAgB,OAAO,IAAI;AACvC;;;ACtjBA,OAAOE,SAAQ;;;ACDf,OAAOC,SAAQ;AAmBT,SAAU,0CACZ,SAAyB,eAA8B,kBACvD,YAA2B,eAAqB;AAClD,QAAM,2BAA2BA,IAAG,aAAa,iBAAiB,UAAU,IACxE,iBAAiB,aACjB,iBAAiB,WAAW;AAEhC,SAAO,QAAQ;IACX,cAAc,UAAU;MACtB,uBAAuB;MACvB,kBAAkB;MAClB,eAAe;KAChB;IAIDA,IAAG,gBAAgB,QAAQ,iBAAiB,aAAa,GAAG,wBAAwB;EAAC;AAC3F;AAGM,SAAU,UAAU,SAAyB,MAAmB;AACpE,SAAO,QAAQ,mBAAmB,MAAM,QAAQ,sBAAsBA,IAAG,WAAW,UAAU,CAAC;AACjG;;;ACvBO,IAAM,wBAA2C,CACpD,QACA,MACA,SACA,eACA,eACA,gBACA,WACI;AAlCR;AAoCE,OAAI,UAAK,2BAA2B,OAAO,IAAI,MAA3C,mBACM,KAAK,OAAK,mBAAmB,GAAG,SAAS,MAAM,IAAI;AAC3D,WAAO,OAAO;EAChB;AAEA,QAAM,eAAe,2BAA2B,QAAQ,MAAM,aAAa;AAC3E,MAAI,iBAAiB,MAAM;AACzB,WAAO,OAAO;EAChB;AAEA,QAAM,SAA4D;IAChE,YAAY,QAAQ,WAAU;IAC9B,SAAS,QAAQ,oBAAoB,aAAa,mBAAmB;IACrE,YAAY,aAAa,WAAW,QAAQ,WAAU,IAAK,QAAQ,YAAW;IAI9E,aAAa,QAAQ,iBAAiB,WAAW;;AAGnD,QAAM,aAAa,OAAO,KAAK,cAAa;AAC5C,QAAM,eAAe,QAAQ,gBACzB,QAAQ,qBACJ,0CACI,SAAS,eAAe,gBAAgB,YAAY,OAAO,GAC/D,QACA;IAIE,UACI,SACA,QAAQ,8BAA8B,OAAO,QAAQ,MAAM,EAAE,IACzD,CAAC,CAAC,MAAM,KAAK,MAAM,QAAQ,yBAAyB,MAAM,KAAK,CAAC,CAAC,CAAC;GAC3E,CAAC;AAGV,SAAO,QAAQ,0BACX,OAAO,MACP,CAAC,cAAc,IAAI,YAAO,KAAK,cAAZ,YAAyB,CAAA,CAAG,GAC/C,OAAO,MACP,OAAO,KAAK,eACZ,OAAO,KAAK,MACZ,OAAO,KAAK,WAAW;AAE7B;;;ACxEA,OAAOC,SAAQ;AAWR,IAAM,uBAA0C,CACnD,QACA,MACA,SACA,eACA,eACA,gBACA,WACI;AA5BR;AA6BE,OAAI,UAAK,2BAA2B,OAAO,IAAI,MAA3C,mBAA8C,KAAK,OAAI;AACrD,WAAO,mBAAmB,GAAG,SAAS,MAAM,KAAK,mBAAmB,GAAG,UAAU,MAAM;EACzF,IAAI;AACN,WAAO,OAAO;EAChB;AAEA,QAAM,eAAe,2BACjB,QACA,MACA,aAAa;AAGjB,MAAI,iBAAiB,MAAM;AACzB,WAAO,OAAO;EAChB;AAEA,QAAM,cAAc,QAAQ,8BAA8B;IACxD,QAAQ,yBACJ,YAAY,aAAa,MAAM,WAAW,QAAQ,WAAU,IAAK,QAAQ,YAAW,CAAE;IAC1F,QAAQ,yBACJ,SAAS,QAAQ,oBAAoB,aAAa,MAAM,mBAAmB,CAAC;IAChF,QAAQ,yBACJ,YAAY,aAAa,MAAM,WAAW,QAAQ,WAAU,IAAK,QAAQ,YAAW,CAAE;GAC3F;AAED,QAAM,aAAa,OAAO,KAAK,cAAa;AAC5C,QAAM,iBAAiB;IACnB;IAIA,QAAQ,mBACJ,aAAa,QAAQ,sBAAsBC,IAAG,WAAW,UAAU,CAAC;IACxE;IAAgB;IAAS;IAAY;EAAa;AAEtD,QAAM,kBAAkB,gBACpB,UAAU,QAAQ,oBAAoB,aAAa,OAAO,mBAAmB,GAC7E,gBAAgB,SAAS,YAAY,aAAa;AAEtD,SAAO,QAAQ,0BACX,OAAO,MACP,CAAC,gBAAgB,iBAAiB,IAAI,YAAO,KAAK,cAAZ,YAAyB,CAAA,CAAG,GAClE,OAAO,KAAK,MACZ,OAAO,KAAK,eACZ,OAAO,KAAK,MACZ,OAAO,KAAK,WAAW;AAE7B;AAEA,SAAS,gBACL,MAAc,QAAuB,gBAA2B,SAChE,YAA2B,eAA4B;AACzD,QAAM,aAAa,0CACf,SAAS,eAAe,gBAAgB,YAAY,IAAI;AAE5D,SAAO,QAAQ,gBAAgB,QAAQ,qBAAqB,YAAY,QAAW,CAAC,MAAM,CAAC,CAAC;AAC9F;;;AC/DO,IAAM,gCAAmD,CAC5D,QACA,MACA,SACA,eACA,eACA,gBACA,WACI;AA9BR;AAgCE,OAAI,UAAK,2BAA2B,OAAO,IAAI,MAA3C,mBACM,KAAK,OAAK,mBAAmB,GAAG,UAAU,MAAM,IAAI;AAC5D,WAAO,OAAO;EAChB;AAEA,QAAM,SAAS,+BACX,QACA,MACA,aAAa;AAEjB,MAAI,WAAW,MAAM;AACnB,WAAO,OAAO;EAChB;AAEA,QAAM,aAAa,OAAO,KAAK,cAAa;AAC5C,QAAM,eAAe,QAAQ,gBACzB,QAAQ,qBACJ,0CACI,SAAS,eAAe,gBAAgB,YAAY,QAAQ,GAChE,QAAW,CAAC,QAAQ,oBAAoB,OAAO,SAAS,mBAAmB,CAAC,CAAC,CAAC;AAGtF,SAAO,QAAQ,0BACX,OAAO,MACP,CAAC,cAAc,IAAI,YAAO,KAAK,cAAZ,YAAyB,CAAA,CAAG,GAC/C,OAAO,KAAK,MACZ,OAAO,KAAK,eACZ,OAAO,KAAK,MACZ,OAAO,KAAK,WAAW;AAE7B;;;ACjDA,IAAM,2BAA8D;EAClE,WAAW;EACX,cAAc;EACd,cAAc;EACd,iBAAiB;;AAcZ,IAAM,2BAA8C,CACvD,QACA,MACA,SACA,eACA,eACA,gBACA,WACI;AAvCR;AAwCE,QAAM,aAAa,KAAK,2BAA2B,OAAO,IAAI;AAG9D,QAAM,kBACF,cAAc,qBAAqB,YAAY,qBAAqB,MAAM;AAC9E,MAAI,oBAAoB,QAAQ,gBAAgB,SAAS,GAAG;AAC1D,WAAO,OAAO;EAChB;AAEA,QAAM,kBAAkB,mCACpB,QACA,MACA,aAAa;AAEjB,MAAI,oBAAoB,MAAM;AAC5B,WAAO,OAAO;EAChB;AAEA,QAAM,aAAa,OAAO,KAAK,cAAa;AAC5C,QAAM,WAAW,gBAAgB,KAAK;AACtC,QAAM,eAAe,QAAQ,gBACzB,QAAQ;IACJ,0CACI,SAAS,eAAe,gBAAgB,YACxC,yBAAyB,gBAAgB,KAAK;IAClD;IAGA;MACE,gBAAgB,KAAK,UAAU;MAG/B,UAAU,SAAS,QAAQ,8BAA8B;QACvD,GAAI,SAAS,SAAS,IAAI,CAAC,QAAQ,uBAAuB,SAAS,EAAE,CAAC,IAAI,CAAA;QAC1E,QAAQ,yBAAyB,YAAY,QAAQ,WAAU,CAAE;OAClE,CAAC;;EACH,CAAC;AAGV,SAAO,QAAQ,0BACX,OAAO,MACP,CAAC,cAAc,IAAI,YAAO,KAAK,cAAZ,YAAyB,CAAA,CAAG,GAC/C,OAAO,KAAK,MACZ,OAAO,KAAK,eACZ,OAAO,KAAK,MACZ,OAAO,KAAK,WAAW;AAE7B;;;ALhEA,IAAM,uBAAuB,CAAC,aAAa,WAAW;AAMtD,IAAM,qBAA0C;EAC9C;EACA;EACA;EACA;;AAUI,SAAU,8BACZ,MACA,eACA,QAAe;AAEjB,SAAO,SAAM;AACX,WAAO,gBAAa;AAClB,YAAM,gBAAgB,IAAI,cAAa;AAEvC,mBAAaC,IAAG,UACZ,YACA,uBAAuB,KAAK,MAAM,eAAe,eAAe,MAAM,GACtEA,IAAG,YAAY;AAGnB,aAAO,cAAc,gBAAgB,KAAK,UAAU;IACtD;EACF;AACF;AAEA,SAAS,uBACL,KACA,MACA,eACA,eACA,QAAe;AAEjB,QAAM,UAAwC,CAAC,SAA0B;AAtE3E;AAuEI,QAAIA,IAAG,mBAAmB,IAAI,KAAK,KAAK,SAAS,QAAW;AAC1D,YAAM,oBAAmB,UAAK,2BAA2B,IAAI,MAApC,mBAAuC,KAC5D,CAAC,MAAM,qBAAqB,KAAK,UAAQ,mBAAmB,GAAG,MAAM,MAAM,CAAC;AAEhF,UAAI,qBAAqB,QAAW;AAClC,YAAI,aAAa;AAEjB,cAAM,UAAU,KAAK,QAAQ,IAAI,gBAAa;AAC5C,cAAI,CAACA,IAAG,sBAAsB,UAAU,GAAG;AACzC,mBAAO;UACT;AACA,gBAAM,SAAS,mBAAmB,UAAU;AAC5C,cAAI,WAAW,MAAM;AACnB,mBAAO;UACT;AAGA,qBAAW,aAAa,oBAAoB;AAC1C,kBAAM,UAAU,UACZ,EAAC,GAAG,QAAQ,MAAM,WAAU,GAAG,MAAM,IAAI,SAAS,eAAe,eACjE,kBAAkB,MAAM;AAE5B,gBAAI,YAAY,OAAO,MAAM;AAC3B,2BAAa;AACb,qBAAO;YACT;UACF;AAEA,iBAAO;QACT,CAAC;AAED,YAAI,YAAY;AACd,iBAAO,IAAI,QAAQ,uBACf,MAAM,KAAK,WAAW,KAAK,MAAM,KAAK,gBAAgB,KAAK,iBAAiB,OAAO;QACzF;MACF;IACF;AAEA,WAAOA,IAAG,eAAe,MAAM,SAAS,GAAG;EAC7C;AACA,SAAO;AACT;;;AM3EM,SAAU,+BACZ,SAAqB,SAAS,OAAK;AACrC,QAAM,cAAc,QAAQ,eAAc;AAC1C,QAAM,iBAAiB,IAAI,yBAAyB,WAAW;AAC/D,QAAM,gBAAgB,IAAI,uBAAsB;AAEhD,QAAM,8BAA8B;IAChC;IAAa;IAAgB,CAAA;IAAI;IACL;EAAK;AAErC,QAAM,8BACF,8BAA8B,gBAAgB,eAAe,MAAM;AAEvE,SAAO,CAAC,QAAO;AACb,WAAO,CAAC,eAAc;AACpB,mBAAa,4BAA4B,GAAG,EAAE,UAAU;AACxD,mBAAa,4BAA4B,GAAG,EAAE,UAAU;AAExD,aAAO;IACT;EACF;AACF;;;ACnCO,IAAM,yBAAyB;EACpC,WAAW;EACX,mBAAmB;;AAGd,IAAM,kCAAkC;EAC7C,GAAG;EACH,WAAW;;AASN,IAAM,0CACT,CAAC,SAAqB,SAAS,UAA+C;AAC5E,SAAO,+BAA+B,SAAS,MAAM;AACvD;", "names": ["isAngularDecorator", "diagnostics", "ts", "ts", "ts", "ts", "ts"]}