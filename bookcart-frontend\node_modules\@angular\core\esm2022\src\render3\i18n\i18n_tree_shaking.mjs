/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
let _icuContainerIterate;
/**
 * Iterator which provides ability to visit all of the `TIcuContainerNode` root `RNode`s.
 */
export function icuContainerIterate(tIcuContainerNode, lView) {
    return _icuContainerIterate(tIcuContainerNode, lView);
}
/**
 * Ensures that `IcuContainerVisitor`'s implementation is present.
 *
 * This function is invoked when i18n instruction comes across an ICU. The purpose is to allow the
 * bundler to tree shake ICU logic and only load it if ICU instruction is executed.
 */
export function ensureIcuContainerVisitorLoaded(loader) {
    if (_icuContainerIterate === undefined) {
        // Do not inline this function. We want to keep `ensureIcuContainerVisitorLoaded` light, so it
        // can be inlined into call-site.
        _icuContainerIterate = loader();
    }
}
//# sourceMappingURL=data:application/json;base64,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