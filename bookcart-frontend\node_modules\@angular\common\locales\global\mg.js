/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

if (n === Math.floor(n) && (n >= 0 && n <= 1))
    return 1;
return 5;
}
    global.ng.common.locales['mg'] = ["mg",[["AM","PM"],u,u],u,[["A","A","T","A","A","Z","A"],["<PERSON>ah","<PERSON><PERSON>","<PERSON>l","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>"],["<PERSON><PERSON><PERSON>","Alats<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>"],["<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>l","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>"]],u,[["J","F","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>"],["<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON>l","<PERSON><PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON>"],["<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","Aogositra","Septambra","Oktobra","Novambra","Desambra"]],u,[["BC","AD"],u,["Alohan’i JK","Aorian’i JK"]],1,[6,0],["y-MM-dd","y MMM d","d MMMM y","EEEE d MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤ #,##0.00","#E0"],"MGA","Ar","Ariary",{"JPY":["JP¥","¥"],"MGA":["Ar"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    