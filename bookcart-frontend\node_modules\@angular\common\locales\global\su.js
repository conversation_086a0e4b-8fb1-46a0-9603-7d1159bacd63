/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['su'] = ["su",[["AM","PM"],u,u],u,[["M","S","S","R","K","J","S"],["Mng","<PERSON>","<PERSON>","<PERSON>b","Kem","<PERSON><PERSON>","<PERSON>p"],["<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>asa","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","Saptu"],["Mng","<PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>p"]],u,[["<PERSON>","<PERSON>","M","A","M","J","<PERSON>","A","S","O","N","D"],["<PERSON>","<PERSON><PERSON>b","<PERSON>","<PERSON>","<PERSON><PERSON>i","<PERSON>","<PERSON>","Ags","Sép","<PERSON>t","<PERSON>p","<PERSON><PERSON>"],["<PERSON>uari","<PERSON><PERSON>bruari","<PERSON>t","<PERSON>","<PERSON><PERSON>i","<PERSON>i","<PERSON>i","<PERSON>gus<PERSON>","<PERSON><PERSON>pt<PERSON>mber","<PERSON>to<PERSON>","<PERSON>p<PERSON>mber","<PERSON><PERSON><PERSON>mber"]],u,[["SM","M"],u,u],0,[6,0],["d/M/yy","d MMM y","d MMMM y","EEEE, d MMMM y"],["H.mm","H.mm.ss","H.mm.ss z","H.mm.ss zzzz"],["{1}, {0}",u,"{1} 'jam' {0}",u],[",",".",";","%","+","-","E","×","‰","∞","NaN","."],["#,##0.###","#,##0%","¤#,##0.00","#E0"],"IDR","Rp","Rupee Indonésia",{"IDR":["Rp"]},"ltr", plural, []];
  })(globalThis);
    