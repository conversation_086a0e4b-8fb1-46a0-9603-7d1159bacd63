{"ast": null, "code": "import { Observable } from '../Observable';\nimport { isFunction } from '../util/isFunction';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nexport function fromEventPattern(addHandler, removeHandler, resultSelector) {\n  if (resultSelector) {\n    return fromEventPattern(addHand<PERSON>, removeHandler).pipe(mapOneOrManyArgs(resultSelector));\n  }\n  return new Observable(subscriber => {\n    const handler = (...e) => subscriber.next(e.length === 1 ? e[0] : e);\n    const retValue = addHandler(handler);\n    return isFunction(removeHandler) ? () => removeHandler(handler, retValue) : undefined;\n  });\n}", "map": {"version": 3, "names": ["Observable", "isFunction", "mapOneOrManyArgs", "fromEventPattern", "add<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "resultSelector", "pipe", "subscriber", "handler", "e", "next", "length", "retValue", "undefined"], "sources": ["C:/Users/<USER>/Desktop/BookCart/bookcart-frontend/node_modules/rxjs/dist/esm/internal/observable/fromEventPattern.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { isFunction } from '../util/isFunction';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nexport function fromEventPattern(addHandler, removeHandler, resultSelector) {\n    if (resultSelector) {\n        return fromEventPattern(addHandler, removeHandler).pipe(mapOneOrManyArgs(resultSelector));\n    }\n    return new Observable((subscriber) => {\n        const handler = (...e) => subscriber.next(e.length === 1 ? e[0] : e);\n        const retValue = addHandler(handler);\n        return isFunction(removeHandler) ? () => removeHandler(handler, retValue) : undefined;\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,OAAO,SAASC,gBAAgBA,CAACC,UAAU,EAAEC,aAAa,EAAEC,cAAc,EAAE;EACxE,IAAIA,cAAc,EAAE;IAChB,OAAOH,gBAAgB,CAACC,UAAU,EAAEC,aAAa,CAAC,CAACE,IAAI,CAACL,gBAAgB,CAACI,cAAc,CAAC,CAAC;EAC7F;EACA,OAAO,IAAIN,UAAU,CAAEQ,UAAU,IAAK;IAClC,MAAMC,OAAO,GAAGA,CAAC,GAAGC,CAAC,KAAKF,UAAU,CAACG,IAAI,CAACD,CAAC,CAACE,MAAM,KAAK,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC;IACpE,MAAMG,QAAQ,GAAGT,UAAU,CAACK,OAAO,CAAC;IACpC,OAAOR,UAAU,CAACI,aAAa,CAAC,GAAG,MAAMA,aAAa,CAACI,OAAO,EAAEI,QAAQ,CAAC,GAAGC,SAAS;EACzF,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}