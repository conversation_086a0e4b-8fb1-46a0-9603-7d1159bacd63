/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { InjectionToken } from '@angular/core';
/** Injection token to be used to override the default options for `mat-checkbox`. */
export const MAT_CHECKBOX_DEFAULT_OPTIONS = new InjectionToken('mat-checkbox-default-options', {
    providedIn: 'root',
    factory: MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY,
});
/** @docs-private */
export function MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY() {
    return {
        color: 'accent',
        clickAction: 'check-indeterminate',
    };
}
//# sourceMappingURL=data:application/json;base64,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