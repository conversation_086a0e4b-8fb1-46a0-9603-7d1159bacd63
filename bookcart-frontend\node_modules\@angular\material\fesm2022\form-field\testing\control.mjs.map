{"version": 3, "file": "control.mjs", "sources": ["../../../../../../../../src/material/form-field/testing/control/form-field-control-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ComponentHarness} from '@angular/cdk/testing';\n\n/**\n * Base class for custom form-field control harnesses. Harnesses for\n * custom controls with form-fields need to implement this interface.\n */\nexport abstract class MatFormFieldControlHarness extends ComponentHarness {}\n"], "names": [], "mappings": ";;AAUA;;;AAGG;AACG,MAAgB,0BAA2B,SAAQ,gBAAgB,CAAA;AAAG;;;;"}