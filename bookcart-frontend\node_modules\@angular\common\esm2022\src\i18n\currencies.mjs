/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/** @internal */
export const CURRENCIES_EN = { "ADP": [undefined, undefined, 0], "AFN": [undefined, "؋", 0], "ALL": [undefined, undefined, 0], "AMD": [undefined, "֏", 2], "AOA": [undefined, "Kz"], "ARS": [undefined, "$"], "AUD": ["A$", "$"], "AZN": [undefined, "₼"], "BAM": [undefined, "KM"], "BBD": [undefined, "$"], "BDT": [undefined, "৳"], "BHD": [undefined, undefined, 3], "BIF": [undefined, undefined, 0], "BMD": [undefined, "$"], "BND": [undefined, "$"], "BOB": [undefined, "Bs"], "BRL": ["R$"], "BSD": [undefined, "$"], "BWP": [undefined, "P"], "BYN": [undefined, undefined, 2], "BYR": [undefined, undefined, 0], "BZD": [undefined, "$"], "CAD": ["CA$", "$", 2], "CHF": [undefined, undefined, 2], "CLF": [undefined, undefined, 4], "CLP": [undefined, "$", 0], "CNY": ["CN¥", "¥"], "COP": [undefined, "$", 2], "CRC": [undefined, "₡", 2], "CUC": [undefined, "$"], "CUP": [undefined, "$"], "CZK": [undefined, "Kč", 2], "DJF": [undefined, undefined, 0], "DKK": [undefined, "kr", 2], "DOP": [undefined, "$"], "EGP": [undefined, "E£"], "ESP": [undefined, "₧", 0], "EUR": ["€"], "FJD": [undefined, "$"], "FKP": [undefined, "£"], "GBP": ["£"], "GEL": [undefined, "₾"], "GHS": [undefined, "GH₵"], "GIP": [undefined, "£"], "GNF": [undefined, "FG", 0], "GTQ": [undefined, "Q"], "GYD": [undefined, "$", 2], "HKD": ["HK$", "$"], "HNL": [undefined, "L"], "HRK": [undefined, "kn"], "HUF": [undefined, "Ft", 2], "IDR": [undefined, "Rp", 2], "ILS": ["₪"], "INR": ["₹"], "IQD": [undefined, undefined, 0], "IRR": [undefined, undefined, 0], "ISK": [undefined, "kr", 0], "ITL": [undefined, undefined, 0], "JMD": [undefined, "$"], "JOD": [undefined, undefined, 3], "JPY": ["¥", undefined, 0], "KHR": [undefined, "៛"], "KMF": [undefined, "CF", 0], "KPW": [undefined, "₩", 0], "KRW": ["₩", undefined, 0], "KWD": [undefined, undefined, 3], "KYD": [undefined, "$"], "KZT": [undefined, "₸"], "LAK": [undefined, "₭", 0], "LBP": [undefined, "L£", 0], "LKR": [undefined, "Rs"], "LRD": [undefined, "$"], "LTL": [undefined, "Lt"], "LUF": [undefined, undefined, 0], "LVL": [undefined, "Ls"], "LYD": [undefined, undefined, 3], "MGA": [undefined, "Ar", 0], "MGF": [undefined, undefined, 0], "MMK": [undefined, "K", 0], "MNT": [undefined, "₮", 2], "MRO": [undefined, undefined, 0], "MUR": [undefined, "Rs", 2], "MXN": ["MX$", "$"], "MYR": [undefined, "RM"], "NAD": [undefined, "$"], "NGN": [undefined, "₦"], "NIO": [undefined, "C$"], "NOK": [undefined, "kr", 2], "NPR": [undefined, "Rs"], "NZD": ["NZ$", "$"], "OMR": [undefined, undefined, 3], "PHP": ["₱"], "PKR": [undefined, "Rs", 2], "PLN": [undefined, "zł"], "PYG": [undefined, "₲", 0], "RON": [undefined, "lei"], "RSD": [undefined, undefined, 0], "RUB": [undefined, "₽"], "RWF": [undefined, "RF", 0], "SBD": [undefined, "$"], "SEK": [undefined, "kr", 2], "SGD": [undefined, "$"], "SHP": [undefined, "£"], "SLE": [undefined, undefined, 2], "SLL": [undefined, undefined, 0], "SOS": [undefined, undefined, 0], "SRD": [undefined, "$"], "SSP": [undefined, "£"], "STD": [undefined, undefined, 0], "STN": [undefined, "Db"], "SYP": [undefined, "£", 0], "THB": [undefined, "฿"], "TMM": [undefined, undefined, 0], "TND": [undefined, undefined, 3], "TOP": [undefined, "T$"], "TRL": [undefined, undefined, 0], "TRY": [undefined, "₺"], "TTD": [undefined, "$"], "TWD": ["NT$", "$", 2], "TZS": [undefined, undefined, 2], "UAH": [undefined, "₴"], "UGX": [undefined, undefined, 0], "USD": ["$"], "UYI": [undefined, undefined, 0], "UYU": [undefined, "$"], "UYW": [undefined, undefined, 4], "UZS": [undefined, undefined, 2], "VEF": [undefined, "Bs", 2], "VND": ["₫", undefined, 0], "VUV": [undefined, undefined, 0], "XAF": ["FCFA", undefined, 0], "XCD": ["EC$", "$"], "XOF": ["F CFA", undefined, 0], "XPF": ["CFPF", undefined, 0], "XXX": ["¤"], "YER": [undefined, undefined, 0], "ZAR": [undefined, "R"], "ZMK": [undefined, undefined, 0], "ZMW": [undefined, "ZK"], "ZWD": [undefined, undefined, 0] };
//# sourceMappingURL=data:application/json;base64,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