/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["ku", [["BN", "PN"], u, u], u, [["Y", "D", "S", "Ç", "P", "Î", "Ş"], ["yş", "dş", "sş", "çş", "pş", "în", "ş"], ["yekşem", "duşem", "sêşem", "çarşem", "pêncşem", "în", "şemî"], ["yş", "dş", "sş", "çş", "pş", "în", "ş"]], u, [["R", "R", "A", "A", "G", "P", "T", "G", "R", "K", "S", "B"], ["rêb", "reş", "ada", "avr", "gul", "pûş", "tîr", "gel", "rez", "kew", "ser", "ber"], ["rêbendanê", "reşemiyê", "adarê", "avrêlê", "gulanê", "pûşperê", "tîrmehê", "gelawêjê", "rezberê", "kewçêrê", "sermawezê", "berfanbarê"]], [["R", "R", "A", "A", "G", "P", "T", "G", "R", "K", "S", "B"], ["rêb", "reş", "ada", "avr", "gul", "pûş", "tîr", "gel", "rez", "kew", "ser", "ber"], ["rêbendan", "reşemî", "adar", "avrêl", "gulan", "pûşper", "tîrmeh", "gelawêj", "rezber", "kewçêr", "sermawez", "berfanbar"]], [["BZ", "PZ"], u, ["berî zayînê", "piştî zayînê"]], 1, [6, 0], ["y-MM-dd", "y MMM d", "y MMMM d", "y MMMM d, EEEE"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "%#,##0", "#,##0.00 ¤", "#E0"], "TRY", "₺", "TRY", { "JPY": ["JP¥", "¥"], "TRY": ["₺"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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