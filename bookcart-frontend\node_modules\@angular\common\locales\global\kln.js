/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['kln'] = ["kln",[["krn","koosk"],u,["karoon","kooskoliny"]],[["krn","koosk"],u,u],[["T","T","O","S","A","M","L"],["Kts","Kot","Koo","Kos","Koa","Kom","Kol"],["Kotis<PERSON>","Kota<PERSON>","Koaeng’","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON>’wan","<PERSON><PERSON><PERSON>","<PERSON><PERSON>"],["Kts","Ko<PERSON>","Ko<PERSON>","<PERSON><PERSON>","Ko<PERSON>","Kom","Kol"]],u,[["M","N","T","<PERSON>","M","<PERSON>","N","R","B","E","K","K"],["Mul","Ngat","Taa","Iwo","Mam","Paa","Nge","Roo","Bur","<PERSON>pe","Kpt","Kpa"],["Mulgul","Ng’atyaato","Kiptaamo","Iwootkuut","Mamuut","Paagi","Ng’eiyeet","Rooptui","Bureet","Epeeso","Kipsuunde ne taai","Kipsuunde nebo aeng’"]],u,[["AM","KO"],u,["Amait kesich Jesu","Kokakesich Jesu"]],0,[6,0],["dd/MM/y","d MMM y","d MMMM y","EEEE, d MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤#,##0.00","#E0"],"KES","Ksh","Silingitab ya Kenya",{"JPY":["JP¥","¥"],"KES":["Ksh"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    