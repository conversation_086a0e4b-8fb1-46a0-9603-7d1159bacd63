/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['id'] = ["id",[["AM","PM"],u,u],u,[["M","S","S","R","K","J","S"],["<PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON>b","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>b"],["<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>bt<PERSON>"],["<PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>b"]],u,[["<PERSON>","<PERSON>","<PERSON>","A","M","J","<PERSON>","A","<PERSON>","O","N","D"],["<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON>t","<PERSON>","<PERSON>"],["<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON>t","April","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","September","<PERSON>tober","November","<PERSON>em<PERSON>"]],u,[["<PERSON>","<PERSON>"],u,["<PERSON><PERSON>um Masehi","Masehi"]],0,[6,0],["dd/MM/yy","d MMM y","d MMMM y","EEEE, dd MMMM y"],["HH.mm","HH.mm.ss","HH.mm.ss z","HH.mm.ss zzzz"],["{1} {0}",u,u,u],[",",".",";","%","+","-","E","×","‰","∞","NaN","."],["#,##0.###","#,##0%","¤#,##0.00","#E0"],"IDR","Rp","Rupiah Indonesia",{"AUD":["AU$","$"],"BYN":[u,"р."],"IDR":["Rp"],"INR":["Rs","₹"],"JPY":["JP¥","¥"],"PHP":[u,"₱"],"THB":["฿"],"TWD":["NT$"],"USD":["US$","$"],"XXX":[]},"ltr", plural, [[["tengah malam","tengah hari","pagi","siang","sore","malam"],u,u],u,["00:00","12:00",["00:00","10:00"],["10:00","15:00"],["15:00","18:00"],["18:00","24:00"]]]];
  })(globalThis);
    