@use '../../token-utils';
@use '../../../style/sass-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, switch);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    disabled-selected-handle-opacity: 0.38,
    disabled-unselected-handle-opacity: 0.38,

    unselected-handle-size: 20px,
    selected-handle-size: 20px,
    pressed-handle-size: 20px,
    with-icon-handle-size: 20px,

    selected-handle-horizontal-margin: 0,
    selected-with-icon-handle-horizontal-margin: 0,
    selected-pressed-handle-horizontal-margin: 0,
    unselected-handle-horizontal-margin: 0,
    unselected-with-icon-handle-horizontal-margin: 0,
    unselected-pressed-handle-horizontal-margin: 0,

    // The hidden and visible track represent whichever track is visible or
    // hidden in the ui. This could be the .mdc-switch__track :before or
    // :after depending on whether the switch is selected or unselected.
    //
    // The m2 slide-toggle uses transforms to hide & show the tracks while
    // the m3 slide-toggle uses opacity.

    visible-track-opacity: 1,
    hidden-track-opacity: 1,
    visible-track-transition: transform 75ms 0ms cubic-bezier(0, 0, 0.2, 1),
    hidden-track-transition: transform 75ms 0ms cubic-bezier(0.4, 0, 0.6, 1),

    track-outline-width: 1px,
    track-outline-color: transparent,
    selected-track-outline-width: 1px,
    disabled-unselected-track-outline-width: 1px,
    disabled-unselected-track-outline-color: transparent,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  @return ();
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return ();
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-utils.$placeholder-color-config),
      get-typography-tokens(token-utils.$placeholder-typography-config),
      get-density-tokens(token-utils.$placeholder-density-config)
  );
}
