{"version": 3, "file": "forms.mjs", "sources": ["../../../../../../packages/forms/src/directives/control_value_accessor.ts", "../../../../../../packages/forms/src/directives/checkbox_value_accessor.ts", "../../../../../../packages/forms/src/directives/default_value_accessor.ts", "../../../../../../packages/forms/src/validators.ts", "../../../../../../packages/forms/src/directives/abstract_control_directive.ts", "../../../../../../packages/forms/src/directives/control_container.ts", "../../../../../../packages/forms/src/directives/ng_control.ts", "../../../../../../packages/forms/src/directives/ng_control_status.ts", "../../../../../../packages/forms/src/directives/error_examples.ts", "../../../../../../packages/forms/src/directives/reactive_errors.ts", "../../../../../../packages/forms/src/model/abstract_model.ts", "../../../../../../packages/forms/src/model/form_group.ts", "../../../../../../packages/forms/src/directives/shared.ts", "../../../../../../packages/forms/src/directives/ng_form.ts", "../../../../../../packages/forms/src/util.ts", "../../../../../../packages/forms/src/model/form_control.ts", "../../../../../../packages/forms/src/directives/abstract_form_group_directive.ts", "../../../../../../packages/forms/src/directives/template_driven_errors.ts", "../../../../../../packages/forms/src/directives/ng_model_group.ts", "../../../../../../packages/forms/src/directives/ng_model.ts", "../../../../../../packages/forms/src/directives/ng_no_validate_directive.ts", "../../../../../../packages/forms/src/directives/number_value_accessor.ts", "../../../../../../packages/forms/src/directives/radio_control_value_accessor.ts", "../../../../../../packages/forms/src/directives/range_value_accessor.ts", "../../../../../../packages/forms/src/directives/reactive_directives/form_control_directive.ts", "../../../../../../packages/forms/src/directives/reactive_directives/form_group_directive.ts", "../../../../../../packages/forms/src/directives/reactive_directives/form_group_name.ts", "../../../../../../packages/forms/src/directives/reactive_directives/form_control_name.ts", "../../../../../../packages/forms/src/directives/select_control_value_accessor.ts", "../../../../../../packages/forms/src/directives/select_multiple_control_value_accessor.ts", "../../../../../../packages/forms/src/directives/validators.ts", "../../../../../../packages/forms/src/directives.ts", "../../../../../../packages/forms/src/model/form_array.ts", "../../../../../../packages/forms/src/form_builder.ts", "../../../../../../packages/forms/src/version.ts", "../../../../../../packages/forms/src/form_providers.ts", "../../../../../../packages/forms/src/forms.ts", "../../../../../../packages/forms/public_api.ts", "../../../../../../packages/forms/index.ts", "../../../../../../packages/forms/forms.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, ElementRef, InjectionToken, Renderer2} from '@angular/core';\n\n/**\n * @description\n * Defines an interface that acts as a bridge between the Angular forms API and a\n * native element in the DOM.\n *\n * Implement this interface to create a custom form control directive\n * that integrates with Angular forms.\n *\n * @see {@link DefaultValueAccessor}\n *\n * @publicApi\n */\nexport interface ControlValueAccessor {\n  /**\n   * @description\n   * Writes a new value to the element.\n   *\n   * This method is called by the forms API to write to the view when programmatic\n   * changes from model to view are requested.\n   *\n   * @usageNotes\n   * ### Write a value to the element\n   *\n   * The following example writes a value to the native DOM element.\n   *\n   * ```ts\n   * writeValue(value: any): void {\n   *   this._renderer.setProperty(this._elementRef.nativeElement, 'value', value);\n   * }\n   * ```\n   *\n   * @param obj The new value for the element\n   */\n  writeValue(obj: any): void;\n\n  /**\n   * @description\n   * Registers a callback function that is called when the control's value\n   * changes in the UI.\n   *\n   * This method is called by the forms API on initialization to update the form\n   * model when values propagate from the view to the model.\n   *\n   * When implementing the `registerOnChange` method in your own value accessor,\n   * save the given function so your class calls it at the appropriate time.\n   *\n   * @usageNotes\n   * ### Store the change function\n   *\n   * The following example stores the provided function as an internal method.\n   *\n   * ```ts\n   * registerOnChange(fn: (_: any) => void): void {\n   *   this._onChange = fn;\n   * }\n   * ```\n   *\n   * When the value changes in the UI, call the registered\n   * function to allow the forms API to update itself:\n   *\n   * ```ts\n   * host: {\n   *    '(change)': '_onChange($event.target.value)'\n   * }\n   * ```\n   *\n   * @param fn The callback function to register\n   */\n  registerOnChange(fn: any): void;\n\n  /**\n   * @description\n   * Registers a callback function that is called by the forms API on initialization\n   * to update the form model on blur.\n   *\n   * When implementing `registerOnTouched` in your own value accessor, save the given\n   * function so your class calls it when the control should be considered\n   * blurred or \"touched\".\n   *\n   * @usageNotes\n   * ### Store the callback function\n   *\n   * The following example stores the provided function as an internal method.\n   *\n   * ```ts\n   * registerOnTouched(fn: any): void {\n   *   this._onTouched = fn;\n   * }\n   * ```\n   *\n   * On blur (or equivalent), your class should call the registered function to allow\n   * the forms API to update itself:\n   *\n   * ```ts\n   * host: {\n   *    '(blur)': '_onTouched()'\n   * }\n   * ```\n   *\n   * @param fn The callback function to register\n   */\n  registerOnTouched(fn: any): void;\n\n  /**\n   * @description\n   * Function that is called by the forms API when the control status changes to\n   * or from 'DISABLED'. Depending on the status, it enables or disables the\n   * appropriate DOM element.\n   *\n   * @usageNotes\n   * The following is an example of writing the disabled property to a native DOM element:\n   *\n   * ```ts\n   * setDisabledState(isDisabled: boolean): void {\n   *   this._renderer.setProperty(this._elementRef.nativeElement, 'disabled', isDisabled);\n   * }\n   * ```\n   *\n   * @param isDisabled The disabled status to set on the element\n   */\n  setDisabledState?(isDisabled: boolean): void;\n}\n\n/**\n * Base class for all ControlValueAccessor classes defined in Forms package.\n * Contains common logic and utility functions.\n *\n * Note: this is an *internal-only* class and should not be extended or used directly in\n * applications code.\n */\n@Directive()\nexport class BaseControlValueAccessor {\n  /**\n   * The registered callback function called when a change or input event occurs on the input\n   * element.\n   * @nodoc\n   */\n  onChange = (_: any) => {};\n\n  /**\n   * The registered callback function called when a blur event occurs on the input element.\n   * @nodoc\n   */\n  onTouched = () => {};\n\n  constructor(private _renderer: Renderer2, private _elementRef: ElementRef) {}\n\n  /**\n   * Helper method that sets a property on a target element using the current Renderer\n   * implementation.\n   * @nodoc\n   */\n  protected setProperty(key: string, value: any): void {\n    this._renderer.setProperty(this._elementRef.nativeElement, key, value);\n  }\n\n  /**\n   * Registers a function called when the control is touched.\n   * @nodoc\n   */\n  registerOnTouched(fn: () => void): void {\n    this.onTouched = fn;\n  }\n\n  /**\n   * Registers a function called when the control value changes.\n   * @nodoc\n   */\n  registerOnChange(fn: (_: any) => {}): void {\n    this.onChange = fn;\n  }\n\n  /**\n   * Sets the \"disabled\" property on the range input element.\n   * @nodoc\n   */\n  setDisabledState(isDisabled: boolean): void {\n    this.setProperty('disabled', isDisabled);\n  }\n}\n\n/**\n * Base class for all built-in ControlValueAccessor classes (except DefaultValueAccessor, which is\n * used in case no other CVAs can be found). We use this class to distinguish between default CVA,\n * built-in CVAs and custom CVAs, so that Forms logic can recognize built-in CVAs and treat custom\n * ones with higher priority (when both built-in and custom CVAs are present).\n *\n * Note: this is an *internal-only* class and should not be extended or used directly in\n * applications code.\n */\n@Directive()\nexport class BuiltInControlValueAccessor extends BaseControlValueAccessor {\n}\n\n/**\n * Used to provide a `ControlValueAccessor` for form controls.\n *\n * See `DefaultValueAccessor` for how to implement one.\n *\n * @publicApi\n */\nexport const NG_VALUE_ACCESSOR =\n    new InjectionToken<ReadonlyArray<ControlValueAccessor>>(ngDevMode ? 'NgValueAccessor' : '');\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, forwardRef, Provider} from '@angular/core';\n\nimport {BuiltInControlValueAccessor, ControlValueAccessor, NG_VALUE_ACCESSOR} from './control_value_accessor';\n\nconst CHECKBOX_VALUE_ACCESSOR: Provider = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => CheckboxControlValueAccessor),\n  multi: true,\n};\n\n/**\n * @description\n * A `ControlValueAccessor` for writing a value and listening to changes on a checkbox input\n * element.\n *\n * @usageNotes\n *\n * ### Using a checkbox with a reactive form.\n *\n * The following example shows how to use a checkbox with a reactive form.\n *\n * ```ts\n * const rememberLoginControl = new FormControl();\n * ```\n *\n * ```\n * <input type=\"checkbox\" [formControl]=\"rememberLoginControl\">\n * ```\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n@Directive({\n  selector:\n      'input[type=checkbox][formControlName],input[type=checkbox][formControl],input[type=checkbox][ngModel]',\n  host: {'(change)': 'onChange($event.target.checked)', '(blur)': 'onTouched()'},\n  providers: [CHECKBOX_VALUE_ACCESSOR]\n})\nexport class CheckboxControlValueAccessor extends BuiltInControlValueAccessor implements\n    ControlValueAccessor {\n  /**\n   * Sets the \"checked\" property on the input element.\n   * @nodoc\n   */\n  writeValue(value: any): void {\n    this.setProperty('checked', value);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵgetDOM as getDOM} from '@angular/common';\nimport {Directive, ElementRef, forwardRef, Inject, InjectionToken, Optional, Provider, Renderer2} from '@angular/core';\n\nimport {BaseControlValueAccessor, ControlValueAccessor, NG_VALUE_ACCESSOR} from './control_value_accessor';\n\nexport const DEFAULT_VALUE_ACCESSOR: Provider = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => DefaultValueAccessor),\n  multi: true\n};\n\n/**\n * We must check whether the agent is Android because composition events\n * behave differently between iOS and Android.\n */\nfunction _isAndroid(): boolean {\n  const userAgent = getDOM() ? getDOM().getUserAgent() : '';\n  return /android (\\d+)/.test(userAgent.toLowerCase());\n}\n\n/**\n * @description\n * Provide this token to control if form directives buffer IME input until\n * the \"compositionend\" event occurs.\n * @publicApi\n */\nexport const COMPOSITION_BUFFER_MODE =\n    new InjectionToken<boolean>(ngDevMode ? 'CompositionEventMode' : '');\n\n/**\n * The default `ControlValueAccessor` for writing a value and listening to changes on input\n * elements. The accessor is used by the `FormControlDirective`, `FormControlName`, and\n * `NgModel` directives.\n *\n * {@searchKeywords ngDefaultControl}\n *\n * @usageNotes\n *\n * ### Using the default value accessor\n *\n * The following example shows how to use an input element that activates the default value accessor\n * (in this case, a text field).\n *\n * ```ts\n * const firstNameControl = new FormControl();\n * ```\n *\n * ```\n * <input type=\"text\" [formControl]=\"firstNameControl\">\n * ```\n *\n * This value accessor is used by default for `<input type=\"text\">` and `<textarea>` elements, but\n * you could also use it for custom components that have similar behavior and do not require special\n * processing. In order to attach the default value accessor to a custom element, add the\n * `ngDefaultControl` attribute as shown below.\n *\n * ```\n * <custom-input-component ngDefaultControl [(ngModel)]=\"value\"></custom-input-component>\n * ```\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n@Directive({\n  selector:\n      'input:not([type=checkbox])[formControlName],textarea[formControlName],input:not([type=checkbox])[formControl],textarea[formControl],input:not([type=checkbox])[ngModel],textarea[ngModel],[ngDefaultControl]',\n  // TODO: vsavkin replace the above selector with the one below it once\n  // https://github.com/angular/angular/issues/3011 is implemented\n  // selector: '[ngModel],[formControl],[formControlName]',\n  host: {\n    '(input)': '$any(this)._handleInput($event.target.value)',\n    '(blur)': 'onTouched()',\n    '(compositionstart)': '$any(this)._compositionStart()',\n    '(compositionend)': '$any(this)._compositionEnd($event.target.value)'\n  },\n  providers: [DEFAULT_VALUE_ACCESSOR]\n})\nexport class DefaultValueAccessor extends BaseControlValueAccessor implements ControlValueAccessor {\n  /** Whether the user is creating a composition string (IME events). */\n  private _composing = false;\n\n  constructor(\n      renderer: Renderer2, elementRef: ElementRef,\n      @Optional() @Inject(COMPOSITION_BUFFER_MODE) private _compositionMode: boolean) {\n    super(renderer, elementRef);\n    if (this._compositionMode == null) {\n      this._compositionMode = !_isAndroid();\n    }\n  }\n\n  /**\n   * Sets the \"value\" property on the input element.\n   * @nodoc\n   */\n  writeValue(value: any): void {\n    const normalizedValue = value == null ? '' : value;\n    this.setProperty('value', normalizedValue);\n  }\n\n  /** @internal */\n  _handleInput(value: any): void {\n    if (!this._compositionMode || (this._compositionMode && !this._composing)) {\n      this.onChange(value);\n    }\n  }\n\n  /** @internal */\n  _compositionStart(): void {\n    this._composing = true;\n  }\n\n  /** @internal */\n  _compositionEnd(value: any): void {\n    this._composing = false;\n    this._compositionMode && this.onChange(value);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {InjectionToken, ɵisPromise as isPromise, ɵisSubscribable as isSubscribable, ɵRuntimeError as RuntimeError} from '@angular/core';\nimport {forkJoin, from, Observable} from 'rxjs';\nimport {map} from 'rxjs/operators';\n\nimport {AsyncValidator, AsyncValidatorFn, ValidationErrors, Validator, ValidatorFn} from './directives/validators';\nimport {RuntimeErrorCode} from './errors';\nimport {AbstractControl} from './model/abstract_model';\n\n\nfunction isEmptyInputValue(value: any): boolean {\n  /**\n   * Check if the object is a string or array before evaluating the length attribute.\n   * This avoids falsely rejecting objects that contain a custom length attribute.\n   * For example, the object {id: 1, length: 0, width: 0} should not be returned as empty.\n   */\n  return value == null ||\n      ((typeof value === 'string' || Array.isArray(value)) && value.length === 0);\n}\n\nfunction hasValidLength(value: any): boolean {\n  // non-strict comparison is intentional, to check for both `null` and `undefined` values\n  return value != null && typeof value.length === 'number';\n}\n\n/**\n * @description\n * An `InjectionToken` for registering additional synchronous validators used with\n * `AbstractControl`s.\n *\n * @see {@link NG_ASYNC_VALIDATORS}\n *\n * @usageNotes\n *\n * ### Providing a custom validator\n *\n * The following example registers a custom validator directive. Adding the validator to the\n * existing collection of validators requires the `multi: true` option.\n *\n * ```typescript\n * @Directive({\n *   selector: '[customValidator]',\n *   providers: [{provide: NG_VALIDATORS, useExisting: CustomValidatorDirective, multi: true}]\n * })\n * class CustomValidatorDirective implements Validator {\n *   validate(control: AbstractControl): ValidationErrors | null {\n *     return { 'custom': true };\n *   }\n * }\n * ```\n *\n * @publicApi\n */\nexport const NG_VALIDATORS =\n    new InjectionToken<ReadonlyArray<Validator|Function>>(ngDevMode ? 'NgValidators' : '');\n\n/**\n * @description\n * An `InjectionToken` for registering additional asynchronous validators used with\n * `AbstractControl`s.\n *\n * @see {@link NG_VALIDATORS}\n *\n * @usageNotes\n *\n * ### Provide a custom async validator directive\n *\n * The following example implements the `AsyncValidator` interface to create an\n * async validator directive with a custom error key.\n *\n * ```typescript\n * @Directive({\n *   selector: '[customAsyncValidator]',\n *   providers: [{provide: NG_ASYNC_VALIDATORS, useExisting: CustomAsyncValidatorDirective, multi:\n * true}]\n * })\n * class CustomAsyncValidatorDirective implements AsyncValidator {\n *   validate(control: AbstractControl): Promise<ValidationErrors|null> {\n *     return Promise.resolve({'custom': true});\n *   }\n * }\n * ```\n *\n * @publicApi\n */\nexport const NG_ASYNC_VALIDATORS =\n    new InjectionToken<ReadonlyArray<Validator|Function>>(ngDevMode ? 'NgAsyncValidators' : '');\n\n/**\n * A regular expression that matches valid e-mail addresses.\n *\n * At a high level, this regexp matches e-mail addresses of the format `local-part@tld`, where:\n * - `local-part` consists of one or more of the allowed characters (alphanumeric and some\n *   punctuation symbols).\n * - `local-part` cannot begin or end with a period (`.`).\n * - `local-part` cannot be longer than 64 characters.\n * - `tld` consists of one or more `labels` separated by periods (`.`). For example `localhost` or\n *   `foo.com`.\n * - A `label` consists of one or more of the allowed characters (alphanumeric, dashes (`-`) and\n *   periods (`.`)).\n * - A `label` cannot begin or end with a dash (`-`) or a period (`.`).\n * - A `label` cannot be longer than 63 characters.\n * - The whole address cannot be longer than 254 characters.\n *\n * ## Implementation background\n *\n * This regexp was ported over from AngularJS (see there for git history):\n * https://github.com/angular/angular.js/blob/c133ef836/src/ng/directive/input.js#L27\n * It is based on the\n * [WHATWG version](https://html.spec.whatwg.org/multipage/input.html#valid-e-mail-address) with\n * some enhancements to incorporate more RFC rules (such as rules related to domain names and the\n * lengths of different parts of the address). The main differences from the WHATWG version are:\n *   - Disallow `local-part` to begin or end with a period (`.`).\n *   - Disallow `local-part` length to exceed 64 characters.\n *   - Disallow total address length to exceed 254 characters.\n *\n * See [this commit](https://github.com/angular/angular.js/commit/f3f5cf72e) for more details.\n */\nconst EMAIL_REGEXP =\n    /^(?=.{1,254}$)(?=.{1,64}@)[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:\\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;\n\n/**\n * @description\n * Provides a set of built-in validators that can be used by form controls.\n *\n * A validator is a function that processes a `FormControl` or collection of\n * controls and returns an error map or null. A null map means that validation has passed.\n *\n * @see [Form Validation](/guide/form-validation)\n *\n * @publicApi\n */\nexport class Validators {\n  /**\n   * @description\n   * Validator that requires the control's value to be greater than or equal to the provided number.\n   *\n   * @usageNotes\n   *\n   * ### Validate against a minimum of 3\n   *\n   * ```typescript\n   * const control = new FormControl(2, Validators.min(3));\n   *\n   * console.log(control.errors); // {min: {min: 3, actual: 2}}\n   * ```\n   *\n   * @returns A validator function that returns an error map with the\n   * `min` property if the validation check fails, otherwise `null`.\n   *\n   * @see {@link updateValueAndValidity()}\n   *\n   */\n  static min(min: number): ValidatorFn {\n    return minValidator(min);\n  }\n\n  /**\n   * @description\n   * Validator that requires the control's value to be less than or equal to the provided number.\n   *\n   * @usageNotes\n   *\n   * ### Validate against a maximum of 15\n   *\n   * ```typescript\n   * const control = new FormControl(16, Validators.max(15));\n   *\n   * console.log(control.errors); // {max: {max: 15, actual: 16}}\n   * ```\n   *\n   * @returns A validator function that returns an error map with the\n   * `max` property if the validation check fails, otherwise `null`.\n   *\n   * @see {@link updateValueAndValidity()}\n   *\n   */\n  static max(max: number): ValidatorFn {\n    return maxValidator(max);\n  }\n\n  /**\n   * @description\n   * Validator that requires the control have a non-empty value.\n   *\n   * @usageNotes\n   *\n   * ### Validate that the field is non-empty\n   *\n   * ```typescript\n   * const control = new FormControl('', Validators.required);\n   *\n   * console.log(control.errors); // {required: true}\n   * ```\n   *\n   * @returns An error map with the `required` property\n   * if the validation check fails, otherwise `null`.\n   *\n   * @see {@link updateValueAndValidity()}\n   *\n   */\n  static required(control: AbstractControl): ValidationErrors|null {\n    return requiredValidator(control);\n  }\n\n  /**\n   * @description\n   * Validator that requires the control's value be true. This validator is commonly\n   * used for required checkboxes.\n   *\n   * @usageNotes\n   *\n   * ### Validate that the field value is true\n   *\n   * ```typescript\n   * const control = new FormControl('some value', Validators.requiredTrue);\n   *\n   * console.log(control.errors); // {required: true}\n   * ```\n   *\n   * @returns An error map that contains the `required` property\n   * set to `true` if the validation check fails, otherwise `null`.\n   *\n   * @see {@link updateValueAndValidity()}\n   *\n   */\n  static requiredTrue(control: AbstractControl): ValidationErrors|null {\n    return requiredTrueValidator(control);\n  }\n\n  /**\n   * @description\n   * Validator that requires the control's value pass an email validation test.\n   *\n   * Tests the value using a [regular\n   * expression](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Regular_Expressions)\n   * pattern suitable for common use cases. The pattern is based on the definition of a valid email\n   * address in the [WHATWG HTML\n   * specification](https://html.spec.whatwg.org/multipage/input.html#valid-e-mail-address) with\n   * some enhancements to incorporate more RFC rules (such as rules related to domain names and the\n   * lengths of different parts of the address).\n   *\n   * The differences from the WHATWG version include:\n   * - Disallow `local-part` (the part before the `@` symbol) to begin or end with a period (`.`).\n   * - Disallow `local-part` to be longer than 64 characters.\n   * - Disallow the whole address to be longer than 254 characters.\n   *\n   * If this pattern does not satisfy your business needs, you can use `Validators.pattern()` to\n   * validate the value against a different pattern.\n   *\n   * @usageNotes\n   *\n   * ### Validate that the field matches a valid email pattern\n   *\n   * ```typescript\n   * const control = new FormControl('bad@', Validators.email);\n   *\n   * console.log(control.errors); // {email: true}\n   * ```\n   *\n   * @returns An error map with the `email` property\n   * if the validation check fails, otherwise `null`.\n   *\n   * @see {@link updateValueAndValidity()}\n   *\n   */\n  static email(control: AbstractControl): ValidationErrors|null {\n    return emailValidator(control);\n  }\n\n  /**\n   * @description\n   * Validator that requires the length of the control's value to be greater than or equal\n   * to the provided minimum length. This validator is also provided by default if you use the\n   * the HTML5 `minlength` attribute. Note that the `minLength` validator is intended to be used\n   * only for types that have a numeric `length` property, such as strings or arrays. The\n   * `minLength` validator logic is also not invoked for values when their `length` property is 0\n   * (for example in case of an empty string or an empty array), to support optional controls. You\n   * can use the standard `required` validator if empty values should not be considered valid.\n   *\n   * @usageNotes\n   *\n   * ### Validate that the field has a minimum of 3 characters\n   *\n   * ```typescript\n   * const control = new FormControl('ng', Validators.minLength(3));\n   *\n   * console.log(control.errors); // {minlength: {requiredLength: 3, actualLength: 2}}\n   * ```\n   *\n   * ```html\n   * <input minlength=\"5\">\n   * ```\n   *\n   * @returns A validator function that returns an error map with the\n   * `minlength` property if the validation check fails, otherwise `null`.\n   *\n   * @see {@link updateValueAndValidity()}\n   *\n   */\n  static minLength(minLength: number): ValidatorFn {\n    return minLengthValidator(minLength);\n  }\n\n  /**\n   * @description\n   * Validator that requires the length of the control's value to be less than or equal\n   * to the provided maximum length. This validator is also provided by default if you use the\n   * the HTML5 `maxlength` attribute. Note that the `maxLength` validator is intended to be used\n   * only for types that have a numeric `length` property, such as strings or arrays.\n   *\n   * @usageNotes\n   *\n   * ### Validate that the field has maximum of 5 characters\n   *\n   * ```typescript\n   * const control = new FormControl('Angular', Validators.maxLength(5));\n   *\n   * console.log(control.errors); // {maxlength: {requiredLength: 5, actualLength: 7}}\n   * ```\n   *\n   * ```html\n   * <input maxlength=\"5\">\n   * ```\n   *\n   * @returns A validator function that returns an error map with the\n   * `maxlength` property if the validation check fails, otherwise `null`.\n   *\n   * @see {@link updateValueAndValidity()}\n   *\n   */\n  static maxLength(maxLength: number): ValidatorFn {\n    return maxLengthValidator(maxLength);\n  }\n\n  /**\n   * @description\n   * Validator that requires the control's value to match a regex pattern. This validator is also\n   * provided by default if you use the HTML5 `pattern` attribute.\n   *\n   * @usageNotes\n   *\n   * ### Validate that the field only contains letters or spaces\n   *\n   * ```typescript\n   * const control = new FormControl('1', Validators.pattern('[a-zA-Z ]*'));\n   *\n   * console.log(control.errors); // {pattern: {requiredPattern: '^[a-zA-Z ]*$', actualValue: '1'}}\n   * ```\n   *\n   * ```html\n   * <input pattern=\"[a-zA-Z ]*\">\n   * ```\n   *\n   * ### Pattern matching with the global or sticky flag\n   *\n   * `RegExp` objects created with the `g` or `y` flags that are passed into `Validators.pattern`\n   * can produce different results on the same input when validations are run consecutively. This is\n   * due to how the behavior of `RegExp.prototype.test` is\n   * specified in [ECMA-262](https://tc39.es/ecma262/#sec-regexpbuiltinexec)\n   * (`RegExp` preserves the index of the last match when the global or sticky flag is used).\n   * Due to this behavior, it is recommended that when using\n   * `Validators.pattern` you **do not** pass in a `RegExp` object with either the global or sticky\n   * flag enabled.\n   *\n   * ```typescript\n   * // Not recommended (since the `g` flag is used)\n   * const controlOne = new FormControl('1', Validators.pattern(/foo/g));\n   *\n   * // Good\n   * const controlTwo = new FormControl('1', Validators.pattern(/foo/));\n   * ```\n   *\n   * @param pattern A regular expression to be used as is to test the values, or a string.\n   * If a string is passed, the `^` character is prepended and the `$` character is\n   * appended to the provided string (if not already present), and the resulting regular\n   * expression is used to test the values.\n   *\n   * @returns A validator function that returns an error map with the\n   * `pattern` property if the validation check fails, otherwise `null`.\n   *\n   * @see {@link updateValueAndValidity()}\n   *\n   */\n  static pattern(pattern: string|RegExp): ValidatorFn {\n    return patternValidator(pattern);\n  }\n\n  /**\n   * @description\n   * Validator that performs no operation.\n   *\n   * @see {@link updateValueAndValidity()}\n   *\n   */\n  static nullValidator(control: AbstractControl): ValidationErrors|null {\n    return nullValidator(control);\n  }\n\n  /**\n   * @description\n   * Compose multiple validators into a single function that returns the union\n   * of the individual error maps for the provided control.\n   *\n   * @returns A validator function that returns an error map with the\n   * merged error maps of the validators if the validation check fails, otherwise `null`.\n   *\n   * @see {@link updateValueAndValidity()}\n   *\n   */\n  static compose(validators: null): null;\n  static compose(validators: (ValidatorFn|null|undefined)[]): ValidatorFn|null;\n  static compose(validators: (ValidatorFn|null|undefined)[]|null): ValidatorFn|null {\n    return compose(validators);\n  }\n\n  /**\n   * @description\n   * Compose multiple async validators into a single function that returns the union\n   * of the individual error objects for the provided control.\n   *\n   * @returns A validator function that returns an error map with the\n   * merged error objects of the async validators if the validation check fails, otherwise `null`.\n   *\n   * @see {@link updateValueAndValidity()}\n   *\n   */\n  static composeAsync(validators: (AsyncValidatorFn|null)[]): AsyncValidatorFn|null {\n    return composeAsync(validators);\n  }\n}\n\n/**\n * Validator that requires the control's value to be greater than or equal to the provided number.\n * See `Validators.min` for additional information.\n */\nexport function minValidator(min: number): ValidatorFn {\n  return (control: AbstractControl): ValidationErrors|null => {\n    if (isEmptyInputValue(control.value) || isEmptyInputValue(min)) {\n      return null;  // don't validate empty values to allow optional controls\n    }\n    const value = parseFloat(control.value);\n    // Controls with NaN values after parsing should be treated as not having a\n    // minimum, per the HTML forms spec: https://www.w3.org/TR/html5/forms.html#attr-input-min\n    return !isNaN(value) && value < min ? {'min': {'min': min, 'actual': control.value}} : null;\n  };\n}\n\n/**\n * Validator that requires the control's value to be less than or equal to the provided number.\n * See `Validators.max` for additional information.\n */\nexport function maxValidator(max: number): ValidatorFn {\n  return (control: AbstractControl): ValidationErrors|null => {\n    if (isEmptyInputValue(control.value) || isEmptyInputValue(max)) {\n      return null;  // don't validate empty values to allow optional controls\n    }\n    const value = parseFloat(control.value);\n    // Controls with NaN values after parsing should be treated as not having a\n    // maximum, per the HTML forms spec: https://www.w3.org/TR/html5/forms.html#attr-input-max\n    return !isNaN(value) && value > max ? {'max': {'max': max, 'actual': control.value}} : null;\n  };\n}\n\n/**\n * Validator that requires the control have a non-empty value.\n * See `Validators.required` for additional information.\n */\nexport function requiredValidator(control: AbstractControl): ValidationErrors|null {\n  return isEmptyInputValue(control.value) ? {'required': true} : null;\n}\n\n/**\n * Validator that requires the control's value be true. This validator is commonly\n * used for required checkboxes.\n * See `Validators.requiredTrue` for additional information.\n */\nexport function requiredTrueValidator(control: AbstractControl): ValidationErrors|null {\n  return control.value === true ? null : {'required': true};\n}\n\n/**\n * Validator that requires the control's value pass an email validation test.\n * See `Validators.email` for additional information.\n */\nexport function emailValidator(control: AbstractControl): ValidationErrors|null {\n  if (isEmptyInputValue(control.value)) {\n    return null;  // don't validate empty values to allow optional controls\n  }\n  return EMAIL_REGEXP.test(control.value) ? null : {'email': true};\n}\n\n/**\n * Validator that requires the length of the control's value to be greater than or equal\n * to the provided minimum length. See `Validators.minLength` for additional information.\n */\nexport function minLengthValidator(minLength: number): ValidatorFn {\n  return (control: AbstractControl): ValidationErrors|null => {\n    if (isEmptyInputValue(control.value) || !hasValidLength(control.value)) {\n      // don't validate empty values to allow optional controls\n      // don't validate values without `length` property\n      return null;\n    }\n\n    return control.value.length < minLength ?\n        {'minlength': {'requiredLength': minLength, 'actualLength': control.value.length}} :\n        null;\n  };\n}\n\n/**\n * Validator that requires the length of the control's value to be less than or equal\n * to the provided maximum length. See `Validators.maxLength` for additional information.\n */\nexport function maxLengthValidator(maxLength: number): ValidatorFn {\n  return (control: AbstractControl): ValidationErrors|null => {\n    return hasValidLength(control.value) && control.value.length > maxLength ?\n        {'maxlength': {'requiredLength': maxLength, 'actualLength': control.value.length}} :\n        null;\n  };\n}\n\n/**\n * Validator that requires the control's value to match a regex pattern.\n * See `Validators.pattern` for additional information.\n */\nexport function patternValidator(pattern: string|RegExp): ValidatorFn {\n  if (!pattern) return nullValidator;\n  let regex: RegExp;\n  let regexStr: string;\n  if (typeof pattern === 'string') {\n    regexStr = '';\n\n    if (pattern.charAt(0) !== '^') regexStr += '^';\n\n    regexStr += pattern;\n\n    if (pattern.charAt(pattern.length - 1) !== '$') regexStr += '$';\n\n    regex = new RegExp(regexStr);\n  } else {\n    regexStr = pattern.toString();\n    regex = pattern;\n  }\n  return (control: AbstractControl): ValidationErrors|null => {\n    if (isEmptyInputValue(control.value)) {\n      return null;  // don't validate empty values to allow optional controls\n    }\n    const value: string = control.value;\n    return regex.test(value) ? null :\n                               {'pattern': {'requiredPattern': regexStr, 'actualValue': value}};\n  };\n}\n\n/**\n * Function that has `ValidatorFn` shape, but performs no operation.\n */\nexport function nullValidator(control: AbstractControl): ValidationErrors|null {\n  return null;\n}\n\nfunction isPresent(o: any): boolean {\n  return o != null;\n}\n\nexport function toObservable(value: any): Observable<any> {\n  const obs = isPromise(value) ? from(value) : value;\n  if ((typeof ngDevMode === 'undefined' || ngDevMode) && !(isSubscribable(obs))) {\n    let errorMessage = `Expected async validator to return Promise or Observable.`;\n    // A synchronous validator will return object or null.\n    if (typeof value === 'object') {\n      errorMessage +=\n          ' Are you using a synchronous validator where an async validator is expected?';\n    }\n    throw new RuntimeError(RuntimeErrorCode.WRONG_VALIDATOR_RETURN_TYPE, errorMessage);\n  }\n  return obs;\n}\n\nfunction mergeErrors(arrayOfErrors: (ValidationErrors|null)[]): ValidationErrors|null {\n  let res: {[key: string]: any} = {};\n  arrayOfErrors.forEach((errors: ValidationErrors|null) => {\n    res = errors != null ? {...res!, ...errors} : res!;\n  });\n\n  return Object.keys(res).length === 0 ? null : res;\n}\n\ntype GenericValidatorFn = (control: AbstractControl) => any;\n\nfunction executeValidators<V extends GenericValidatorFn>(\n    control: AbstractControl, validators: V[]): ReturnType<V>[] {\n  return validators.map(validator => validator(control));\n}\n\nfunction isValidatorFn<V>(validator: V|Validator|AsyncValidator): validator is V {\n  return !(validator as Validator).validate;\n}\n\n/**\n * Given the list of validators that may contain both functions as well as classes, return the list\n * of validator functions (convert validator classes into validator functions). This is needed to\n * have consistent structure in validators list before composing them.\n *\n * @param validators The set of validators that may contain validators both in plain function form\n *     as well as represented as a validator class.\n */\nexport function normalizeValidators<V>(validators: (V|Validator|AsyncValidator)[]): V[] {\n  return validators.map(validator => {\n    return isValidatorFn<V>(validator) ?\n        validator :\n        ((c: AbstractControl) => validator.validate(c)) as unknown as V;\n  });\n}\n\n/**\n * Merges synchronous validators into a single validator function.\n * See `Validators.compose` for additional information.\n */\nfunction compose(validators: (ValidatorFn|null|undefined)[]|null): ValidatorFn|null {\n  if (!validators) return null;\n  const presentValidators: ValidatorFn[] = validators.filter(isPresent) as any;\n  if (presentValidators.length == 0) return null;\n\n  return function(control: AbstractControl) {\n    return mergeErrors(executeValidators<ValidatorFn>(control, presentValidators));\n  };\n}\n\n/**\n * Accepts a list of validators of different possible shapes (`Validator` and `ValidatorFn`),\n * normalizes the list (converts everything to `ValidatorFn`) and merges them into a single\n * validator function.\n */\nexport function composeValidators(validators: Array<Validator|ValidatorFn>): ValidatorFn|null {\n  return validators != null ? compose(normalizeValidators<ValidatorFn>(validators)) : null;\n}\n\n/**\n * Merges asynchronous validators into a single validator function.\n * See `Validators.composeAsync` for additional information.\n */\nfunction composeAsync(validators: (AsyncValidatorFn|null)[]): AsyncValidatorFn|null {\n  if (!validators) return null;\n  const presentValidators: AsyncValidatorFn[] = validators.filter(isPresent) as any;\n  if (presentValidators.length == 0) return null;\n\n  return function(control: AbstractControl) {\n    const observables =\n        executeValidators<AsyncValidatorFn>(control, presentValidators).map(toObservable);\n    return forkJoin(observables).pipe(map(mergeErrors));\n  };\n}\n\n/**\n * Accepts a list of async validators of different possible shapes (`AsyncValidator` and\n * `AsyncValidatorFn`), normalizes the list (converts everything to `AsyncValidatorFn`) and merges\n * them into a single validator function.\n */\nexport function composeAsyncValidators(validators: Array<AsyncValidator|AsyncValidatorFn>):\n    AsyncValidatorFn|null {\n  return validators != null ? composeAsync(normalizeValidators<AsyncValidatorFn>(validators)) :\n                              null;\n}\n\n/**\n * Merges raw control validators with a given directive validator and returns the combined list of\n * validators as an array.\n */\nexport function mergeValidators<V>(controlValidators: V|V[]|null, dirValidator: V): V[] {\n  if (controlValidators === null) return [dirValidator];\n  return Array.isArray(controlValidators) ? [...controlValidators, dirValidator] :\n                                            [controlValidators, dirValidator];\n}\n\n/**\n * Retrieves the list of raw synchronous validators attached to a given control.\n */\nexport function getControlValidators(control: AbstractControl): ValidatorFn|ValidatorFn[]|null {\n  return (control as any)._rawValidators as ValidatorFn | ValidatorFn[] | null;\n}\n\n/**\n * Retrieves the list of raw asynchronous validators attached to a given control.\n */\nexport function getControlAsyncValidators(control: AbstractControl): AsyncValidatorFn|\n    AsyncValidatorFn[]|null {\n  return (control as any)._rawAsyncValidators as AsyncValidatorFn | AsyncValidatorFn[] | null;\n}\n\n/**\n * Accepts a singleton validator, an array, or null, and returns an array type with the provided\n * validators.\n *\n * @param validators A validator, validators, or null.\n * @returns A validators array.\n */\nexport function makeValidatorsArray<T extends ValidatorFn|AsyncValidatorFn>(validators: T|T[]|\n                                                                            null): T[] {\n  if (!validators) return [];\n  return Array.isArray(validators) ? validators : [validators];\n}\n\n/**\n * Determines whether a validator or validators array has a given validator.\n *\n * @param validators The validator or validators to compare against.\n * @param validator The validator to check.\n * @returns Whether the validator is present.\n */\nexport function hasValidator<T extends ValidatorFn|AsyncValidatorFn>(\n    validators: T|T[]|null, validator: T): boolean {\n  return Array.isArray(validators) ? validators.includes(validator) : validators === validator;\n}\n\n/**\n * Combines two arrays of validators into one. If duplicates are provided, only one will be added.\n *\n * @param validators The new validators.\n * @param currentValidators The base array of current validators.\n * @returns An array of validators.\n */\nexport function addValidators<T extends ValidatorFn|AsyncValidatorFn>(\n    validators: T|T[], currentValidators: T|T[]|null): T[] {\n  const current = makeValidatorsArray(currentValidators);\n  const validatorsToAdd = makeValidatorsArray(validators);\n  validatorsToAdd.forEach((v: T) => {\n    // Note: if there are duplicate entries in the new validators array,\n    // only the first one would be added to the current list of validators.\n    // Duplicate ones would be ignored since `hasValidator` would detect\n    // the presence of a validator function and we update the current list in place.\n    if (!hasValidator(current, v)) {\n      current.push(v);\n    }\n  });\n  return current;\n}\n\nexport function removeValidators<T extends ValidatorFn|AsyncValidatorFn>(\n    validators: T|T[], currentValidators: T|T[]|null): T[] {\n  return makeValidatorsArray(currentValidators).filter(v => !hasValidator(validators, v));\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Observable} from 'rxjs';\n\nimport {AbstractControl} from '../model/abstract_model';\nimport {composeAsyncValidators, composeValidators} from '../validators';\n\nimport {AsyncValidator, AsyncValidatorFn, ValidationErrors, Validator, ValidatorFn} from './validators';\n\n\n/**\n * @description\n * Base class for control directives.\n *\n * This class is only used internally in the `ReactiveFormsModule` and the `FormsModule`.\n *\n * @publicApi\n */\nexport abstract class AbstractControlDirective {\n  /**\n   * @description\n   * A reference to the underlying control.\n   *\n   * @returns the control that backs this directive. Most properties fall through to that instance.\n   */\n  abstract get control(): AbstractControl|null;\n\n  /**\n   * @description\n   * Reports the value of the control if it is present, otherwise null.\n   */\n  get value(): any {\n    return this.control ? this.control.value : null;\n  }\n\n  /**\n   * @description\n   * Reports whether the control is valid. A control is considered valid if no\n   * validation errors exist with the current value.\n   * If the control is not present, null is returned.\n   */\n  get valid(): boolean|null {\n    return this.control ? this.control.valid : null;\n  }\n\n  /**\n   * @description\n   * Reports whether the control is invalid, meaning that an error exists in the input value.\n   * If the control is not present, null is returned.\n   */\n  get invalid(): boolean|null {\n    return this.control ? this.control.invalid : null;\n  }\n\n  /**\n   * @description\n   * Reports whether a control is pending, meaning that async validation is occurring and\n   * errors are not yet available for the input value. If the control is not present, null is\n   * returned.\n   */\n  get pending(): boolean|null {\n    return this.control ? this.control.pending : null;\n  }\n\n  /**\n   * @description\n   * Reports whether the control is disabled, meaning that the control is disabled\n   * in the UI and is exempt from validation checks and excluded from aggregate\n   * values of ancestor controls. If the control is not present, null is returned.\n   */\n  get disabled(): boolean|null {\n    return this.control ? this.control.disabled : null;\n  }\n\n  /**\n   * @description\n   * Reports whether the control is enabled, meaning that the control is included in ancestor\n   * calculations of validity or value. If the control is not present, null is returned.\n   */\n  get enabled(): boolean|null {\n    return this.control ? this.control.enabled : null;\n  }\n\n  /**\n   * @description\n   * Reports the control's validation errors. If the control is not present, null is returned.\n   */\n  get errors(): ValidationErrors|null {\n    return this.control ? this.control.errors : null;\n  }\n\n  /**\n   * @description\n   * Reports whether the control is pristine, meaning that the user has not yet changed\n   * the value in the UI. If the control is not present, null is returned.\n   */\n  get pristine(): boolean|null {\n    return this.control ? this.control.pristine : null;\n  }\n\n  /**\n   * @description\n   * Reports whether the control is dirty, meaning that the user has changed\n   * the value in the UI. If the control is not present, null is returned.\n   */\n  get dirty(): boolean|null {\n    return this.control ? this.control.dirty : null;\n  }\n\n  /**\n   * @description\n   * Reports whether the control is touched, meaning that the user has triggered\n   * a `blur` event on it. If the control is not present, null is returned.\n   */\n  get touched(): boolean|null {\n    return this.control ? this.control.touched : null;\n  }\n\n  /**\n   * @description\n   * Reports the validation status of the control. Possible values include:\n   * 'VALID', 'INVALID', 'DISABLED', and 'PENDING'.\n   * If the control is not present, null is returned.\n   */\n  get status(): string|null {\n    return this.control ? this.control.status : null;\n  }\n\n  /**\n   * @description\n   * Reports whether the control is untouched, meaning that the user has not yet triggered\n   * a `blur` event on it. If the control is not present, null is returned.\n   */\n  get untouched(): boolean|null {\n    return this.control ? this.control.untouched : null;\n  }\n\n  /**\n   * @description\n   * Returns a multicasting observable that emits a validation status whenever it is\n   * calculated for the control. If the control is not present, null is returned.\n   */\n  get statusChanges(): Observable<any>|null {\n    return this.control ? this.control.statusChanges : null;\n  }\n\n  /**\n   * @description\n   * Returns a multicasting observable of value changes for the control that emits every time the\n   * value of the control changes in the UI or programmatically.\n   * If the control is not present, null is returned.\n   */\n  get valueChanges(): Observable<any>|null {\n    return this.control ? this.control.valueChanges : null;\n  }\n\n  /**\n   * @description\n   * Returns an array that represents the path from the top-level form to this control.\n   * Each index is the string name of the control on that level.\n   */\n  get path(): string[]|null {\n    return null;\n  }\n\n  /**\n   * Contains the result of merging synchronous validators into a single validator function\n   * (combined using `Validators.compose`).\n   */\n  private _composedValidatorFn: ValidatorFn|null|undefined;\n\n  /**\n   * Contains the result of merging asynchronous validators into a single validator function\n   * (combined using `Validators.composeAsync`).\n   */\n  private _composedAsyncValidatorFn: AsyncValidatorFn|null|undefined;\n\n  /**\n   * Set of synchronous validators as they were provided while calling `setValidators` function.\n   * @internal\n   */\n  _rawValidators: Array<Validator|ValidatorFn> = [];\n\n  /**\n   * Set of asynchronous validators as they were provided while calling `setAsyncValidators`\n   * function.\n   * @internal\n   */\n  _rawAsyncValidators: Array<AsyncValidator|AsyncValidatorFn> = [];\n\n  /**\n   * Sets synchronous validators for this directive.\n   * @internal\n   */\n  _setValidators(validators: Array<Validator|ValidatorFn>|undefined): void {\n    this._rawValidators = validators || [];\n    this._composedValidatorFn = composeValidators(this._rawValidators);\n  }\n\n  /**\n   * Sets asynchronous validators for this directive.\n   * @internal\n   */\n  _setAsyncValidators(validators: Array<AsyncValidator|AsyncValidatorFn>|undefined): void {\n    this._rawAsyncValidators = validators || [];\n    this._composedAsyncValidatorFn = composeAsyncValidators(this._rawAsyncValidators);\n  }\n\n  /**\n   * @description\n   * Synchronous validator function composed of all the synchronous validators registered with this\n   * directive.\n   */\n  get validator(): ValidatorFn|null {\n    return this._composedValidatorFn || null;\n  }\n\n  /**\n   * @description\n   * Asynchronous validator function composed of all the asynchronous validators registered with\n   * this directive.\n   */\n  get asyncValidator(): AsyncValidatorFn|null {\n    return this._composedAsyncValidatorFn || null;\n  }\n\n  /*\n   * The set of callbacks to be invoked when directive instance is being destroyed.\n   */\n  private _onDestroyCallbacks: (() => void)[] = [];\n\n  /**\n   * Internal function to register callbacks that should be invoked\n   * when directive instance is being destroyed.\n   * @internal\n   */\n  _registerOnDestroy(fn: () => void): void {\n    this._onDestroyCallbacks.push(fn);\n  }\n\n  /**\n   * Internal function to invoke all registered \"on destroy\" callbacks.\n   * Note: calling this function also clears the list of callbacks.\n   * @internal\n   */\n  _invokeOnDestroyCallbacks(): void {\n    this._onDestroyCallbacks.forEach(fn => fn());\n    this._onDestroyCallbacks = [];\n  }\n\n  /**\n   * @description\n   * Resets the control with the provided value if the control is present.\n   */\n  reset(value: any = undefined): void {\n    if (this.control) this.control.reset(value);\n  }\n\n  /**\n   * @description\n   * Reports whether the control with the given path has the error specified.\n   *\n   * @param errorCode The code of the error to check\n   * @param path A list of control names that designates how to move from the current control\n   * to the control that should be queried for errors.\n   *\n   * @usageNotes\n   * For example, for the following `FormGroup`:\n   *\n   * ```\n   * form = new FormGroup({\n   *   address: new FormGroup({ street: new FormControl() })\n   * });\n   * ```\n   *\n   * The path to the 'street' control from the root form would be 'address' -> 'street'.\n   *\n   * It can be provided to this method in one of two formats:\n   *\n   * 1. An array of string control names, e.g. `['address', 'street']`\n   * 1. A period-delimited list of control names in one string, e.g. `'address.street'`\n   *\n   * If no path is given, this method checks for the error on the current control.\n   *\n   * @returns whether the given error is present in the control at the given path.\n   *\n   * If the control is not present, false is returned.\n   */\n  hasError(errorCode: string, path?: Array<string|number>|string): boolean {\n    return this.control ? this.control.hasError(errorCode, path) : false;\n  }\n\n  /**\n   * @description\n   * Reports error data for the control with the given path.\n   *\n   * @param errorCode The code of the error to check\n   * @param path A list of control names that designates how to move from the current control\n   * to the control that should be queried for errors.\n   *\n   * @usageNotes\n   * For example, for the following `FormGroup`:\n   *\n   * ```\n   * form = new FormGroup({\n   *   address: new FormGroup({ street: new FormControl() })\n   * });\n   * ```\n   *\n   * The path to the 'street' control from the root form would be 'address' -> 'street'.\n   *\n   * It can be provided to this method in one of two formats:\n   *\n   * 1. An array of string control names, e.g. `['address', 'street']`\n   * 1. A period-delimited list of control names in one string, e.g. `'address.street'`\n   *\n   * @returns error data for that particular error. If the control or error is not present,\n   * null is returned.\n   */\n  getError(errorCode: string, path?: Array<string|number>|string): any {\n    return this.control ? this.control.getError(errorCode, path) : null;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AbstractControlDirective} from './abstract_control_directive';\nimport {Form} from './form_interface';\n\n\n/**\n * @description\n * A base class for directives that contain multiple registered instances of `NgControl`.\n * Only used by the forms module.\n *\n * @publicApi\n */\nexport abstract class ControlContainer extends AbstractControlDirective {\n  /**\n   * @description\n   * The name for the control\n   */\n  // TODO(issue/24571): remove '!'.\n  name!: string|number|null;\n\n  /**\n   * @description\n   * The top-level form directive for the control.\n   */\n  get formDirective(): Form|null {\n    return null;\n  }\n\n  /**\n   * @description\n   * The path to this group.\n   */\n  override get path(): string[]|null {\n    return null;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AbstractControlDirective} from './abstract_control_directive';\nimport {ControlContainer} from './control_container';\nimport {ControlValueAccessor} from './control_value_accessor';\n\n\n/**\n * @description\n * A base class that all `FormControl`-based directives extend. It binds a `FormControl`\n * object to a DOM element.\n *\n * @publicApi\n */\nexport abstract class NgControl extends AbstractControlDirective {\n  /**\n   * @description\n   * The parent form for the control.\n   *\n   * @internal\n   */\n  _parent: ControlContainer|null = null;\n\n  /**\n   * @description\n   * The name for the control\n   */\n  name: string|number|null = null;\n\n  /**\n   * @description\n   * The value accessor for the control\n   */\n  valueAccessor: ControlValueAccessor|null = null;\n\n  /**\n   * @description\n   * The callback method to update the model from the view when requested\n   *\n   * @param newValue The new value for the view\n   */\n  abstract viewToModelUpdate(newValue: any): void;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, Optional, Self, ɵWritable as Writable} from '@angular/core';\n\nimport {AbstractControlDirective} from './abstract_control_directive';\nimport {ControlContainer} from './control_container';\nimport {NgControl} from './ng_control';\nimport {type NgForm} from './ng_form';\nimport {type FormGroupDirective} from './reactive_directives/form_group_directive';\n\n// DO NOT REFACTOR!\n// Each status is represented by a separate function to make sure that\n// advanced Closure Compiler optimizations related to property renaming\n// can work correctly.\nexport class AbstractControlStatus {\n  private _cd: AbstractControlDirective|null;\n\n  constructor(cd: AbstractControlDirective|null) {\n    this._cd = cd;\n  }\n\n  protected get isTouched() {\n    return !!this._cd?.control?.touched;\n  }\n\n  protected get isUntouched() {\n    return !!this._cd?.control?.untouched;\n  }\n\n  protected get isPristine() {\n    return !!this._cd?.control?.pristine;\n  }\n\n  protected get isDirty() {\n    return !!this._cd?.control?.dirty;\n  }\n\n  protected get isValid() {\n    return !!this._cd?.control?.valid;\n  }\n\n  protected get isInvalid() {\n    return !!this._cd?.control?.invalid;\n  }\n\n  protected get isPending() {\n    return !!this._cd?.control?.pending;\n  }\n\n  protected get isSubmitted() {\n    // We check for the `submitted` field from `NgForm` and `FormGroupDirective` classes, but\n    // we avoid instanceof checks to prevent non-tree-shakable references to those types.\n    return !!(this._cd as Writable<NgForm|FormGroupDirective>| null)?.submitted;\n  }\n}\n\nexport const ngControlStatusHost = {\n  '[class.ng-untouched]': 'isUntouched',\n  '[class.ng-touched]': 'isTouched',\n  '[class.ng-pristine]': 'isPristine',\n  '[class.ng-dirty]': 'isDirty',\n  '[class.ng-valid]': 'isValid',\n  '[class.ng-invalid]': 'isInvalid',\n  '[class.ng-pending]': 'isPending',\n};\n\nexport const ngGroupStatusHost = {\n  ...ngControlStatusHost,\n  '[class.ng-submitted]': 'isSubmitted',\n};\n\n/**\n * @description\n * Directive automatically applied to Angular form controls that sets CSS classes\n * based on control status.\n *\n * @usageNotes\n *\n * ### CSS classes applied\n *\n * The following classes are applied as the properties become true:\n *\n * * ng-valid\n * * ng-invalid\n * * ng-pending\n * * ng-pristine\n * * ng-dirty\n * * ng-untouched\n * * ng-touched\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n@Directive({selector: '[formControlName],[ngModel],[formControl]', host: ngControlStatusHost})\nexport class NgControlStatus extends AbstractControlStatus {\n  constructor(@Self() cd: NgControl) {\n    super(cd);\n  }\n}\n\n/**\n * @description\n * Directive automatically applied to Angular form groups that sets CSS classes\n * based on control status (valid/invalid/dirty/etc). On groups, this includes the additional\n * class ng-submitted.\n *\n * @see {@link NgControlStatus}\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n@Directive({\n  selector:\n      '[formGroupName],[formArrayName],[ngModelGroup],[formGroup],form:not([ngNoForm]),[ngForm]',\n  host: ngGroupStatusHost\n})\nexport class NgControlStatusGroup extends AbstractControlStatus {\n  constructor(@Optional() @Self() cd: ControlContainer) {\n    super(cd);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport const formControlNameExample = `\n  <div [formGroup]=\"myGroup\">\n    <input formControlName=\"firstName\">\n  </div>\n\n  In your class:\n\n  this.myGroup = new FormGroup({\n      firstName: new FormControl()\n  });`;\n\nexport const formGroupNameExample = `\n  <div [formGroup]=\"myGroup\">\n      <div formGroupName=\"person\">\n        <input formControlName=\"firstName\">\n      </div>\n  </div>\n\n  In your class:\n\n  this.myGroup = new FormGroup({\n      person: new FormGroup({ firstName: new FormControl() })\n  });`;\n\nexport const formArrayNameExample = `\n  <div [formGroup]=\"myGroup\">\n    <div formArrayName=\"cities\">\n      <div *ngFor=\"let city of cityArray.controls; index as i\">\n        <input [formControlName]=\"i\">\n      </div>\n    </div>\n  </div>\n\n  In your class:\n\n  this.cityArray = new FormArray([new FormControl('SF')]);\n  this.myGroup = new FormGroup({\n    cities: this.cityArray\n  });`;\n\nexport const ngModelGroupExample = `\n  <form>\n      <div ngModelGroup=\"person\">\n        <input [(ngModel)]=\"person.name\" name=\"firstName\">\n      </div>\n  </form>`;\n\nexport const ngModelWithFormGroupExample = `\n  <div [formGroup]=\"myGroup\">\n      <input formControlName=\"firstName\">\n      <input [(ngModel)]=\"showMoreControls\" [ngModelOptions]=\"{standalone: true}\">\n  </div>\n`;\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵRuntimeError as RuntimeError} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../errors';\n\nimport {formArrayNameExample, formControlNameExample, formGroupNameExample, ngModelGroupExample} from './error_examples';\n\n\nexport function controlParentException(): Error {\n  return new RuntimeError(\n      RuntimeErrorCode.FORM_CONTROL_NAME_MISSING_PARENT,\n      `formControlName must be used with a parent formGroup directive.  You'll want to add a formGroup\n      directive and pass it an existing FormGroup instance (you can create one in your class).\n\n    Example:\n\n    ${formControlNameExample}`);\n}\n\nexport function ngModelGroupException(): Error {\n  return new RuntimeError(\n      RuntimeErrorCode.FORM_CONTROL_NAME_INSIDE_MODEL_GROUP,\n      `formControlName cannot be used with an ngModelGroup parent. It is only compatible with parents\n      that also have a \"form\" prefix: formGroupName, formArrayName, or formGroup.\n\n      Option 1:  Update the parent to be formGroupName (reactive form strategy)\n\n      ${formGroupNameExample}\n\n      Option 2: Use ngModel instead of formControlName (template-driven strategy)\n\n      ${ngModelGroupExample}`);\n}\n\nexport function missingFormException(): Error {\n  return new RuntimeError(\n      RuntimeErrorCode.FORM_GROUP_MISSING_INSTANCE,\n      `formGroup expects a FormGroup instance. Please pass one in.\n\n      Example:\n\n      ${formControlNameExample}`);\n}\n\nexport function groupParentException(): Error {\n  return new RuntimeError(\n      RuntimeErrorCode.FORM_GROUP_NAME_MISSING_PARENT,\n      `formGroupName must be used with a parent formGroup directive.  You'll want to add a formGroup\n    directive and pass it an existing FormGroup instance (you can create one in your class).\n\n    Example:\n\n    ${formGroupNameExample}`);\n}\n\nexport function arrayParentException(): Error {\n  return new RuntimeError(\n      RuntimeErrorCode.FORM_ARRAY_NAME_MISSING_PARENT,\n      `formArrayName must be used with a parent formGroup directive.  You'll want to add a formGroup\n      directive and pass it an existing FormGroup instance (you can create one in your class).\n\n      Example:\n\n      ${formArrayNameExample}`);\n}\n\nexport const disabledAttrWarning = `\n  It looks like you're using the disabled attribute with a reactive form directive. If you set disabled to true\n  when you set up this control in your component class, the disabled attribute will actually be set in the DOM for\n  you. We recommend using this approach to avoid 'changed after checked' errors.\n\n  Example:\n  // Specify the \\`disabled\\` property at control creation time:\n  form = new FormGroup({\n    first: new FormControl({value: 'Nancy', disabled: true}, Validators.required),\n    last: new FormControl('Drew', Validators.required)\n  });\n\n  // Controls can also be enabled/disabled after creation:\n  form.get('first')?.enable();\n  form.get('last')?.disable();\n`;\n\nexport const asyncValidatorsDroppedWithOptsWarning = `\n  It looks like you're constructing using a FormControl with both an options argument and an\n  async validators argument. Mixing these arguments will cause your async validators to be dropped.\n  You should either put all your validators in the options object, or in separate validators\n  arguments. For example:\n\n  // Using validators arguments\n  fc = new FormControl(42, Validators.required, myAsyncValidator);\n\n  // Using AbstractControlOptions\n  fc = new FormControl(42, {validators: Validators.required, asyncValidators: myAV});\n\n  // Do NOT mix them: async validators will be dropped!\n  fc = new FormControl(42, {validators: Validators.required}, /* Oops! */ myAsyncValidator);\n`;\n\nexport function ngModelWarning(directiveName: string): string {\n  return `\n  It looks like you're using ngModel on the same form field as ${directiveName}.\n  Support for using the ngModel input property and ngModelChange event with\n  reactive form directives has been deprecated in Angular v6 and will be removed\n  in a future version of Angular.\n\n  For more information on this, see our API docs here:\n  https://angular.io/api/forms/${\n      directiveName === 'formControl' ? 'FormControlDirective' : 'FormControlName'}#use-with-ngmodel\n  `;\n}\n\nfunction describeKey(isFormGroup: boolean, key: string|number): string {\n  return isFormGroup ? `with name: '${key}'` : `at index: ${key}`;\n}\n\nexport function noControlsError(isFormGroup: boolean): string {\n  return `\n    There are no form controls registered with this ${\n      isFormGroup ? 'group' : 'array'} yet. If you're using ngModel,\n    you may want to check next tick (e.g. use setTimeout).\n  `;\n}\n\nexport function missingControlError(isFormGroup: boolean, key: string|number): string {\n  return `Cannot find form control ${describeKey(isFormGroup, key)}`;\n}\n\nexport function missingControlValueError(isFormGroup: boolean, key: string|number): string {\n  return `Must supply a value for form control ${describeKey(isFormGroup, key)}`;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {EventEmitter, ɵRuntimeError as RuntimeError, ɵWritable as Writable} from '@angular/core';\nimport {Observable} from 'rxjs';\n\nimport {asyncValidatorsDroppedWithOptsWarning, missingControlError, missingControlValueError, noControlsError} from '../directives/reactive_errors';\nimport {AsyncValidatorFn, ValidationErrors, ValidatorFn} from '../directives/validators';\nimport {RuntimeErrorCode} from '../errors';\nimport {FormArray, FormGroup} from '../forms';\nimport {addValidators, composeAsyncValidators, composeValidators, hasValidator, removeValidators, toObservable} from '../validators';\n\n\n/**\n * Reports that a control is valid, meaning that no errors exist in the input value.\n *\n * @see {@link status}\n */\nexport const VALID = 'VALID';\n\n/**\n * Reports that a control is invalid, meaning that an error exists in the input value.\n *\n * @see {@link status}\n */\nexport const INVALID = 'INVALID';\n\n/**\n * Reports that a control is pending, meaning that async validation is occurring and\n * errors are not yet available for the input value.\n *\n * @see {@link markAsPending}\n * @see {@link status}\n */\nexport const PENDING = 'PENDING';\n\n/**\n * Reports that a control is disabled, meaning that the control is exempt from ancestor\n * calculations of validity or value.\n *\n * @see {@link markAsDisabled}\n * @see {@link status}\n */\nexport const DISABLED = 'DISABLED';\n\n/**\n * A form can have several different statuses. Each\n * possible status is returned as a string literal.\n *\n * * **VALID**: Reports that a control is valid, meaning that no errors exist in the input\n * value.\n * * **INVALID**: Reports that a control is invalid, meaning that an error exists in the input\n * value.\n * * **PENDING**: Reports that a control is pending, meaning that async validation is\n * occurring and errors are not yet available for the input value.\n * * **DISABLED**: Reports that a control is\n * disabled, meaning that the control is exempt from ancestor calculations of validity or value.\n *\n * @publicApi\n */\nexport type FormControlStatus = 'VALID'|'INVALID'|'PENDING'|'DISABLED';\n\n/**\n * Gets validators from either an options object or given validators.\n */\nexport function pickValidators(validatorOrOpts?: ValidatorFn|ValidatorFn[]|AbstractControlOptions|\n                               null): ValidatorFn|ValidatorFn[]|null {\n  return (isOptionsObj(validatorOrOpts) ? validatorOrOpts.validators : validatorOrOpts) || null;\n}\n\n/**\n * Creates validator function by combining provided validators.\n */\nfunction coerceToValidator(validator: ValidatorFn|ValidatorFn[]|null): ValidatorFn|null {\n  return Array.isArray(validator) ? composeValidators(validator) : validator || null;\n}\n\n/**\n * Gets async validators from either an options object or given validators.\n */\nexport function pickAsyncValidators(\n    asyncValidator?: AsyncValidatorFn|AsyncValidatorFn[]|null,\n    validatorOrOpts?: ValidatorFn|ValidatorFn[]|AbstractControlOptions|null): AsyncValidatorFn|\n    AsyncValidatorFn[]|null {\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    if (isOptionsObj(validatorOrOpts) && asyncValidator) {\n      console.warn(asyncValidatorsDroppedWithOptsWarning);\n    }\n  }\n  return (isOptionsObj(validatorOrOpts) ? validatorOrOpts.asyncValidators : asyncValidator) || null;\n}\n\n/**\n * Creates async validator function by combining provided async validators.\n */\nfunction coerceToAsyncValidator(asyncValidator?: AsyncValidatorFn|AsyncValidatorFn[]|\n                                null): AsyncValidatorFn|null {\n  return Array.isArray(asyncValidator) ? composeAsyncValidators(asyncValidator) :\n                                         asyncValidator || null;\n}\n\nexport type FormHooks = 'change'|'blur'|'submit';\n\n/**\n * Interface for options provided to an `AbstractControl`.\n *\n * @publicApi\n */\nexport interface AbstractControlOptions {\n  /**\n   * @description\n   * The list of validators applied to a control.\n   */\n  validators?: ValidatorFn|ValidatorFn[]|null;\n  /**\n   * @description\n   * The list of async validators applied to control.\n   */\n  asyncValidators?: AsyncValidatorFn|AsyncValidatorFn[]|null;\n  /**\n   * @description\n   * The event name for control to update upon.\n   */\n  updateOn?: 'change'|'blur'|'submit';\n}\n\nexport function isOptionsObj(validatorOrOpts?: ValidatorFn|ValidatorFn[]|AbstractControlOptions|\n                             null): validatorOrOpts is AbstractControlOptions {\n  return validatorOrOpts != null && !Array.isArray(validatorOrOpts) &&\n      typeof validatorOrOpts === 'object';\n}\n\nexport function assertControlPresent(parent: any, isGroup: boolean, key: string|number): void {\n  const controls = parent.controls as {[key: string|number]: unknown};\n  const collection = isGroup ? Object.keys(controls) : controls;\n  if (!collection.length) {\n    throw new RuntimeError(\n        RuntimeErrorCode.NO_CONTROLS,\n        (typeof ngDevMode === 'undefined' || ngDevMode) ? noControlsError(isGroup) : '');\n  }\n  if (!controls[key]) {\n    throw new RuntimeError(\n        RuntimeErrorCode.MISSING_CONTROL,\n        (typeof ngDevMode === 'undefined' || ngDevMode) ? missingControlError(isGroup, key) : '');\n  }\n}\n\nexport function assertAllValuesPresent(control: any, isGroup: boolean, value: any): void {\n  control._forEachChild((_: unknown, key: string|number) => {\n    if (value[key] === undefined) {\n      throw new RuntimeError(\n          RuntimeErrorCode.MISSING_CONTROL_VALUE,\n          (typeof ngDevMode === 'undefined' || ngDevMode) ? missingControlValueError(isGroup, key) :\n                                                            '');\n    }\n  });\n}\n\n// IsAny checks if T is `any`, by checking a condition that couldn't possibly be true otherwise.\nexport type ɵIsAny<T, Y, N> = 0 extends(1&T) ? Y : N;\n\n/**\n * `TypedOrUntyped` allows one of two different types to be selected, depending on whether the Forms\n * class it's applied to is typed or not.\n *\n * This is for internal Angular usage to support typed forms; do not directly use it.\n */\nexport type ɵTypedOrUntyped<T, Typed, Untyped> = ɵIsAny<T, Untyped, Typed>;\n\n/**\n * Value gives the value type corresponding to a control type.\n *\n * Note that the resulting type will follow the same rules as `.value` on your control, group, or\n * array, including `undefined` for each group element which might be disabled.\n *\n * If you are trying to extract a value type for a data model, you probably want {@link RawValue},\n * which will not have `undefined` in group keys.\n *\n * @usageNotes\n *\n * ### `FormControl` value type\n *\n * You can extract the value type of a single control:\n *\n * ```ts\n * type NameControl = FormControl<string>;\n * type NameValue = Value<NameControl>;\n * ```\n *\n * The resulting type is `string`.\n *\n * ### `FormGroup` value type\n *\n * Imagine you have an interface defining the controls in your group. You can extract the shape of\n * the values as follows:\n *\n * ```ts\n * interface PartyFormControls {\n *   address: FormControl<string>;\n * }\n *\n * // Value operates on controls; the object must be wrapped in a FormGroup.\n * type PartyFormValues = Value<FormGroup<PartyFormControls>>;\n * ```\n *\n * The resulting type is `{address: string|undefined}`.\n *\n * ### `FormArray` value type\n *\n * You can extract values from FormArrays as well:\n *\n * ```ts\n * type GuestNamesControls = FormArray<FormControl<string>>;\n *\n * type NamesValues = Value<GuestNamesControls>;\n * ```\n *\n * The resulting type is `string[]`.\n *\n * **Internal: not for public use.**\n */\nexport type ɵValue<T extends AbstractControl|undefined> =\n    T extends AbstractControl<any, any>? T['value'] : never;\n\n/**\n * RawValue gives the raw value type corresponding to a control type.\n *\n * Note that the resulting type will follow the same rules as `.getRawValue()` on your control,\n * group, or array. This means that all controls inside a group will be required, not optional,\n * regardless of their disabled state.\n *\n * You may also wish to use {@link ɵValue}, which will have `undefined` in group keys (which can be\n * disabled).\n *\n * @usageNotes\n *\n * ### `FormGroup` raw value type\n *\n * Imagine you have an interface defining the controls in your group. You can extract the shape of\n * the raw values as follows:\n *\n * ```ts\n * interface PartyFormControls {\n *   address: FormControl<string>;\n * }\n *\n * // RawValue operates on controls; the object must be wrapped in a FormGroup.\n * type PartyFormValues = RawValue<FormGroup<PartyFormControls>>;\n * ```\n *\n * The resulting type is `{address: string}`. (Note the absence of `undefined`.)\n *\n *  **Internal: not for public use.**\n */\nexport type ɵRawValue<T extends AbstractControl|undefined> = T extends AbstractControl<any, any>?\n    (T['setValue'] extends((v: infer R) => void) ? R : never) :\n    never;\n\n// Disable clang-format to produce clearer formatting for these multiline types.\n// clang-format off\n\n/**\n * Tokenize splits a string literal S by a delimiter D.\n */\nexport type ɵTokenize<S extends string, D extends string> =\n  string extends S ? string[] : /* S must be a literal */\n    S extends `${infer T}${D}${infer U}` ? [T, ...ɵTokenize<U, D>] :\n      [S] /* Base case */\n  ;\n\n/**\n * CoerceStrArrToNumArr accepts an array of strings, and converts any numeric string to a number.\n */\nexport type ɵCoerceStrArrToNumArr<S> =\n// Extract the head of the array.\n  S extends [infer Head, ...infer Tail] ?\n    // Using a template literal type, coerce the head to `number` if possible.\n    // Then, recurse on the tail.\n    Head extends `${number}` ?\n      [number, ...ɵCoerceStrArrToNumArr<Tail>] :\n      [Head, ...ɵCoerceStrArrToNumArr<Tail>] :\n    [];\n\n/**\n * Navigate takes a type T and an array K, and returns the type of T[K[0]][K[1]][K[2]]...\n */\nexport type ɵNavigate<T, K extends(Array<string|number>)> =\n  T extends object ? /* T must be indexable (object or array) */\n    (K extends [infer Head, ...infer Tail] ? /* Split K into head and tail */\n      (Head extends keyof T ? /* head(K) must index T */\n        (Tail extends(string|number)[] ? /* tail(K) must be an array */\n          [] extends Tail ? T[Head] : /* base case: K can be split, but Tail is empty */\n            (ɵNavigate<T[Head], Tail>) /* explore T[head(K)] by tail(K) */ :\n          any) /* tail(K) was not an array, give up */ :\n        never) /* head(K) does not index T, give up */ :\n      any) /* K cannot be split, give up */ :\n    any /* T is not indexable, give up */\n  ;\n\n/**\n * ɵWriteable removes readonly from all keys.\n */\nexport type ɵWriteable<T> = {\n  -readonly[P in keyof T]: T[P]\n};\n\n/**\n * GetProperty takes a type T and some property names or indices K.\n * If K is a dot-separated string, it is tokenized into an array before proceeding.\n * Then, the type of the nested property at K is computed: T[K[0]][K[1]][K[2]]...\n * This works with both objects, which are indexed by property name, and arrays, which are indexed\n * numerically.\n *\n * For internal use only.\n */\nexport type ɵGetProperty<T, K> =\n    // K is a string\n    K extends string ? ɵGetProperty<T, ɵCoerceStrArrToNumArr<ɵTokenize<K, '.'>>> :\n    // Is it an array\n    ɵWriteable<K> extends Array<string|number> ? ɵNavigate<T, ɵWriteable<K>> :\n    // Fall through permissively if we can't calculate the type of K.\n    any;\n\n// clang-format on\n\n/**\n * This is the base class for `FormControl`, `FormGroup`, and `FormArray`.\n *\n * It provides some of the shared behavior that all controls and groups of controls have, like\n * running validators, calculating status, and resetting state. It also defines the properties\n * that are shared between all sub-classes, like `value`, `valid`, and `dirty`. It shouldn't be\n * instantiated directly.\n *\n * The first type parameter TValue represents the value type of the control (`control.value`).\n * The optional type parameter TRawValue  represents the raw value type (`control.getRawValue()`).\n *\n * @see [Forms Guide](/guide/forms)\n * @see [Reactive Forms Guide](/guide/reactive-forms)\n * @see [Dynamic Forms Guide](/guide/dynamic-form)\n *\n * @publicApi\n */\nexport abstract class AbstractControl<TValue = any, TRawValue extends TValue = TValue> {\n  /** @internal */\n  _pendingDirty = false;\n\n  /**\n   * Indicates that a control has its own pending asynchronous validation in progress.\n   *\n   * @internal\n   */\n  _hasOwnPendingAsyncValidator = false;\n\n  /** @internal */\n  _pendingTouched = false;\n\n  /** @internal */\n  _onCollectionChange = () => {};\n\n  /** @internal */\n  _updateOn?: FormHooks;\n\n  private _parent: FormGroup|FormArray|null = null;\n  private _asyncValidationSubscription: any;\n\n  /**\n   * Contains the result of merging synchronous validators into a single validator function\n   * (combined using `Validators.compose`).\n   *\n   * @internal\n   */\n  private _composedValidatorFn!: ValidatorFn|null;\n\n  /**\n   * Contains the result of merging asynchronous validators into a single validator function\n   * (combined using `Validators.composeAsync`).\n   *\n   * @internal\n   */\n  private _composedAsyncValidatorFn!: AsyncValidatorFn|null;\n\n  /**\n   * Synchronous validators as they were provided:\n   *  - in `AbstractControl` constructor\n   *  - as an argument while calling `setValidators` function\n   *  - while calling the setter on the `validator` field (e.g. `control.validator = validatorFn`)\n   *\n   * @internal\n   */\n  private _rawValidators!: ValidatorFn|ValidatorFn[]|null;\n\n  /**\n   * Asynchronous validators as they were provided:\n   *  - in `AbstractControl` constructor\n   *  - as an argument while calling `setAsyncValidators` function\n   *  - while calling the setter on the `asyncValidator` field (e.g. `control.asyncValidator =\n   * asyncValidatorFn`)\n   *\n   * @internal\n   */\n  private _rawAsyncValidators!: AsyncValidatorFn|AsyncValidatorFn[]|null;\n\n  /**\n   * The current value of the control.\n   *\n   * * For a `FormControl`, the current value.\n   * * For an enabled `FormGroup`, the values of enabled controls as an object\n   * with a key-value pair for each member of the group.\n   * * For a disabled `FormGroup`, the values of all controls as an object\n   * with a key-value pair for each member of the group.\n   * * For a `FormArray`, the values of enabled controls as an array.\n   *\n   */\n  public readonly value!: TValue;\n\n  /**\n   * Initialize the AbstractControl instance.\n   *\n   * @param validators The function or array of functions that is used to determine the validity of\n   *     this control synchronously.\n   * @param asyncValidators The function or array of functions that is used to determine validity of\n   *     this control asynchronously.\n   */\n  constructor(\n      validators: ValidatorFn|ValidatorFn[]|null,\n      asyncValidators: AsyncValidatorFn|AsyncValidatorFn[]|null) {\n    this._assignValidators(validators);\n    this._assignAsyncValidators(asyncValidators);\n  }\n\n  /**\n   * Returns the function that is used to determine the validity of this control synchronously.\n   * If multiple validators have been added, this will be a single composed function.\n   * See `Validators.compose()` for additional information.\n   */\n  get validator(): ValidatorFn|null {\n    return this._composedValidatorFn;\n  }\n  set validator(validatorFn: ValidatorFn|null) {\n    this._rawValidators = this._composedValidatorFn = validatorFn;\n  }\n\n  /**\n   * Returns the function that is used to determine the validity of this control asynchronously.\n   * If multiple validators have been added, this will be a single composed function.\n   * See `Validators.compose()` for additional information.\n   */\n  get asyncValidator(): AsyncValidatorFn|null {\n    return this._composedAsyncValidatorFn;\n  }\n  set asyncValidator(asyncValidatorFn: AsyncValidatorFn|null) {\n    this._rawAsyncValidators = this._composedAsyncValidatorFn = asyncValidatorFn;\n  }\n\n  /**\n   * The parent control.\n   */\n  get parent(): FormGroup|FormArray|null {\n    return this._parent;\n  }\n\n  /**\n   * The validation status of the control.\n   *\n   * @see {@link FormControlStatus}\n   *\n   * These status values are mutually exclusive, so a control cannot be\n   * both valid AND invalid or invalid AND disabled.\n   */\n  public readonly status!: FormControlStatus;\n\n  /**\n   * A control is `valid` when its `status` is `VALID`.\n   *\n   * @see {@link AbstractControl.status}\n   *\n   * @returns True if the control has passed all of its validation tests,\n   * false otherwise.\n   */\n  get valid(): boolean {\n    return this.status === VALID;\n  }\n\n  /**\n   * A control is `invalid` when its `status` is `INVALID`.\n   *\n   * @see {@link AbstractControl.status}\n   *\n   * @returns True if this control has failed one or more of its validation checks,\n   * false otherwise.\n   */\n  get invalid(): boolean {\n    return this.status === INVALID;\n  }\n\n  /**\n   * A control is `pending` when its `status` is `PENDING`.\n   *\n   * @see {@link AbstractControl.status}\n   *\n   * @returns True if this control is in the process of conducting a validation check,\n   * false otherwise.\n   */\n  get pending(): boolean {\n    return this.status == PENDING;\n  }\n\n  /**\n   * A control is `disabled` when its `status` is `DISABLED`.\n   *\n   * Disabled controls are exempt from validation checks and\n   * are not included in the aggregate value of their ancestor\n   * controls.\n   *\n   * @see {@link AbstractControl.status}\n   *\n   * @returns True if the control is disabled, false otherwise.\n   */\n  get disabled(): boolean {\n    return this.status === DISABLED;\n  }\n\n  /**\n   * A control is `enabled` as long as its `status` is not `DISABLED`.\n   *\n   * @returns True if the control has any status other than 'DISABLED',\n   * false if the status is 'DISABLED'.\n   *\n   * @see {@link AbstractControl.status}\n   *\n   */\n  get enabled(): boolean {\n    return this.status !== DISABLED;\n  }\n\n  /**\n   * An object containing any errors generated by failing validation,\n   * or null if there are no errors.\n   */\n  public readonly errors!: ValidationErrors|null;\n\n  /**\n   * A control is `pristine` if the user has not yet changed\n   * the value in the UI.\n   *\n   * @returns True if the user has not yet changed the value in the UI; compare `dirty`.\n   * Programmatic changes to a control's value do not mark it dirty.\n   */\n  public readonly pristine: boolean = true;\n\n  /**\n   * A control is `dirty` if the user has changed the value\n   * in the UI.\n   *\n   * @returns True if the user has changed the value of this control in the UI; compare `pristine`.\n   * Programmatic changes to a control's value do not mark it dirty.\n   */\n  get dirty(): boolean {\n    return !this.pristine;\n  }\n\n  /**\n   * True if the control is marked as `touched`.\n   *\n   * A control is marked `touched` once the user has triggered\n   * a `blur` event on it.\n   */\n  public readonly touched: boolean = false;\n\n  /**\n   * True if the control has not been marked as touched\n   *\n   * A control is `untouched` if the user has not yet triggered\n   * a `blur` event on it.\n   */\n  get untouched(): boolean {\n    return !this.touched;\n  }\n\n  /**\n   * A multicasting observable that emits an event every time the value of the control changes, in\n   * the UI or programmatically. It also emits an event each time you call enable() or disable()\n   * without passing along {emitEvent: false} as a function argument.\n   *\n   * **Note**: the emit happens right after a value of this control is updated. The value of a\n   * parent control (for example if this FormControl is a part of a FormGroup) is updated later, so\n   * accessing a value of a parent control (using the `value` property) from the callback of this\n   * event might result in getting a value that has not been updated yet. Subscribe to the\n   * `valueChanges` event of the parent control instead.\n   */\n  public readonly valueChanges!: Observable<TValue>;\n\n  /**\n   * A multicasting observable that emits an event every time the validation `status` of the control\n   * recalculates.\n   *\n   * @see {@link FormControlStatus}\n   * @see {@link AbstractControl.status}\n   *\n   */\n  public readonly statusChanges!: Observable<FormControlStatus>;\n\n  /**\n   * Reports the update strategy of the `AbstractControl` (meaning\n   * the event on which the control updates itself).\n   * Possible values: `'change'` | `'blur'` | `'submit'`\n   * Default value: `'change'`\n   */\n  get updateOn(): FormHooks {\n    return this._updateOn ? this._updateOn : (this.parent ? this.parent.updateOn : 'change');\n  }\n\n  /**\n   * Sets the synchronous validators that are active on this control.  Calling\n   * this overwrites any existing synchronous validators.\n   *\n   * When you add or remove a validator at run time, you must call\n   * `updateValueAndValidity()` for the new validation to take effect.\n   *\n   * If you want to add a new validator without affecting existing ones, consider\n   * using `addValidators()` method instead.\n   */\n  setValidators(validators: ValidatorFn|ValidatorFn[]|null): void {\n    this._assignValidators(validators);\n  }\n\n  /**\n   * Sets the asynchronous validators that are active on this control. Calling this\n   * overwrites any existing asynchronous validators.\n   *\n   * When you add or remove a validator at run time, you must call\n   * `updateValueAndValidity()` for the new validation to take effect.\n   *\n   * If you want to add a new validator without affecting existing ones, consider\n   * using `addAsyncValidators()` method instead.\n   */\n  setAsyncValidators(validators: AsyncValidatorFn|AsyncValidatorFn[]|null): void {\n    this._assignAsyncValidators(validators);\n  }\n\n  /**\n   * Add a synchronous validator or validators to this control, without affecting other validators.\n   *\n   * When you add or remove a validator at run time, you must call\n   * `updateValueAndValidity()` for the new validation to take effect.\n   *\n   * Adding a validator that already exists will have no effect. If duplicate validator functions\n   * are present in the `validators` array, only the first instance would be added to a form\n   * control.\n   *\n   * @param validators The new validator function or functions to add to this control.\n   */\n  addValidators(validators: ValidatorFn|ValidatorFn[]): void {\n    this.setValidators(addValidators(validators, this._rawValidators));\n  }\n\n  /**\n   * Add an asynchronous validator or validators to this control, without affecting other\n   * validators.\n   *\n   * When you add or remove a validator at run time, you must call\n   * `updateValueAndValidity()` for the new validation to take effect.\n   *\n   * Adding a validator that already exists will have no effect.\n   *\n   * @param validators The new asynchronous validator function or functions to add to this control.\n   */\n  addAsyncValidators(validators: AsyncValidatorFn|AsyncValidatorFn[]): void {\n    this.setAsyncValidators(addValidators(validators, this._rawAsyncValidators));\n  }\n\n  /**\n   * Remove a synchronous validator from this control, without affecting other validators.\n   * Validators are compared by function reference; you must pass a reference to the exact same\n   * validator function as the one that was originally set. If a provided validator is not found,\n   * it is ignored.\n   *\n   * @usageNotes\n   *\n   * ### Reference to a ValidatorFn\n   *\n   * ```\n   * // Reference to the RequiredValidator\n   * const ctrl = new FormControl<string | null>('', Validators.required);\n   * ctrl.removeValidators(Validators.required);\n   *\n   * // Reference to anonymous function inside MinValidator\n   * const minValidator = Validators.min(3);\n   * const ctrl = new FormControl<string | null>('', minValidator);\n   * expect(ctrl.hasValidator(minValidator)).toEqual(true)\n   * expect(ctrl.hasValidator(Validators.min(3))).toEqual(false)\n   *\n   * ctrl.removeValidators(minValidator);\n   * ```\n   *\n   * When you add or remove a validator at run time, you must call\n   * `updateValueAndValidity()` for the new validation to take effect.\n   *\n   * @param validators The validator or validators to remove.\n   */\n  removeValidators(validators: ValidatorFn|ValidatorFn[]): void {\n    this.setValidators(removeValidators(validators, this._rawValidators));\n  }\n\n  /**\n   * Remove an asynchronous validator from this control, without affecting other validators.\n   * Validators are compared by function reference; you must pass a reference to the exact same\n   * validator function as the one that was originally set. If a provided validator is not found, it\n   * is ignored.\n   *\n   * When you add or remove a validator at run time, you must call\n   * `updateValueAndValidity()` for the new validation to take effect.\n   *\n   * @param validators The asynchronous validator or validators to remove.\n   */\n  removeAsyncValidators(validators: AsyncValidatorFn|AsyncValidatorFn[]): void {\n    this.setAsyncValidators(removeValidators(validators, this._rawAsyncValidators));\n  }\n\n  /**\n   * Check whether a synchronous validator function is present on this control. The provided\n   * validator must be a reference to the exact same function that was provided.\n   *\n   * @usageNotes\n   *\n   * ### Reference to a ValidatorFn\n   *\n   * ```\n   * // Reference to the RequiredValidator\n   * const ctrl = new FormControl<number | null>(0, Validators.required);\n   * expect(ctrl.hasValidator(Validators.required)).toEqual(true)\n   *\n   * // Reference to anonymous function inside MinValidator\n   * const minValidator = Validators.min(3);\n   * const ctrl = new FormControl<number | null>(0, minValidator);\n   * expect(ctrl.hasValidator(minValidator)).toEqual(true)\n   * expect(ctrl.hasValidator(Validators.min(3))).toEqual(false)\n   * ```\n   *\n   * @param validator The validator to check for presence. Compared by function reference.\n   * @returns Whether the provided validator was found on this control.\n   */\n  hasValidator(validator: ValidatorFn): boolean {\n    return hasValidator(this._rawValidators, validator);\n  }\n\n  /**\n   * Check whether an asynchronous validator function is present on this control. The provided\n   * validator must be a reference to the exact same function that was provided.\n   *\n   * @param validator The asynchronous validator to check for presence. Compared by function\n   *     reference.\n   * @returns Whether the provided asynchronous validator was found on this control.\n   */\n  hasAsyncValidator(validator: AsyncValidatorFn): boolean {\n    return hasValidator(this._rawAsyncValidators, validator);\n  }\n\n  /**\n   * Empties out the synchronous validator list.\n   *\n   * When you add or remove a validator at run time, you must call\n   * `updateValueAndValidity()` for the new validation to take effect.\n   *\n   */\n  clearValidators(): void {\n    this.validator = null;\n  }\n\n  /**\n   * Empties out the async validator list.\n   *\n   * When you add or remove a validator at run time, you must call\n   * `updateValueAndValidity()` for the new validation to take effect.\n   *\n   */\n  clearAsyncValidators(): void {\n    this.asyncValidator = null;\n  }\n\n  /**\n   * Marks the control as `touched`. A control is touched by focus and\n   * blur events that do not change the value.\n   *\n   * @see {@link markAsUntouched()}\n   * @see {@link markAsDirty()}\n   * @see {@link markAsPristine()}\n   *\n   * @param opts Configuration options that determine how the control propagates changes\n   * and emits events after marking is applied.\n   * * `onlySelf`: When true, mark only this control. When false or not supplied,\n   * marks all direct ancestors. Default is false.\n   */\n  markAsTouched(opts: {onlySelf?: boolean} = {}): void {\n    (this as Writable<this>).touched = true;\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent.markAsTouched(opts);\n    }\n  }\n\n  /**\n   * Marks the control and all its descendant controls as `touched`.\n   * @see {@link markAsTouched()}\n   */\n  markAllAsTouched(): void {\n    this.markAsTouched({onlySelf: true});\n\n    this._forEachChild((control: AbstractControl) => control.markAllAsTouched());\n  }\n\n  /**\n   * Marks the control as `untouched`.\n   *\n   * If the control has any children, also marks all children as `untouched`\n   * and recalculates the `touched` status of all parent controls.\n   *\n   * @see {@link markAsTouched()}\n   * @see {@link markAsDirty()}\n   * @see {@link markAsPristine()}\n   *\n   * @param opts Configuration options that determine how the control propagates changes\n   * and emits events after the marking is applied.\n   * * `onlySelf`: When true, mark only this control. When false or not supplied,\n   * marks all direct ancestors. Default is false.\n   */\n  markAsUntouched(opts: {onlySelf?: boolean} = {}): void {\n    (this as Writable<this>).touched = false;\n    this._pendingTouched = false;\n\n    this._forEachChild((control: AbstractControl) => {\n      control.markAsUntouched({onlySelf: true});\n    });\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent._updateTouched(opts);\n    }\n  }\n\n  /**\n   * Marks the control as `dirty`. A control becomes dirty when\n   * the control's value is changed through the UI; compare `markAsTouched`.\n   *\n   * @see {@link markAsTouched()}\n   * @see {@link markAsUntouched()}\n   * @see {@link markAsPristine()}\n   *\n   * @param opts Configuration options that determine how the control propagates changes\n   * and emits events after marking is applied.\n   * * `onlySelf`: When true, mark only this control. When false or not supplied,\n   * marks all direct ancestors. Default is false.\n   */\n  markAsDirty(opts: {onlySelf?: boolean} = {}): void {\n    (this as Writable<this>).pristine = false;\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent.markAsDirty(opts);\n    }\n  }\n\n  /**\n   * Marks the control as `pristine`.\n   *\n   * If the control has any children, marks all children as `pristine`,\n   * and recalculates the `pristine` status of all parent\n   * controls.\n   *\n   * @see {@link markAsTouched()}\n   * @see {@link markAsUntouched()}\n   * @see {@link markAsDirty()}\n   *\n   * @param opts Configuration options that determine how the control emits events after\n   * marking is applied.\n   * * `onlySelf`: When true, mark only this control. When false or not supplied,\n   * marks all direct ancestors. Default is false.\n   */\n  markAsPristine(opts: {onlySelf?: boolean} = {}): void {\n    (this as Writable<this>).pristine = true;\n    this._pendingDirty = false;\n\n    this._forEachChild((control: AbstractControl) => {\n      control.markAsPristine({onlySelf: true});\n    });\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent._updatePristine(opts);\n    }\n  }\n\n  /**\n   * Marks the control as `pending`.\n   *\n   * A control is pending while the control performs async validation.\n   *\n   * @see {@link AbstractControl.status}\n   *\n   * @param opts Configuration options that determine how the control propagates changes and\n   * emits events after marking is applied.\n   * * `onlySelf`: When true, mark only this control. When false or not supplied,\n   * marks all direct ancestors. Default is false.\n   * * `emitEvent`: When true or not supplied (the default), the `statusChanges`\n   * observable emits an event with the latest status the control is marked pending.\n   * When false, no events are emitted.\n   *\n   */\n  markAsPending(opts: {onlySelf?: boolean, emitEvent?: boolean} = {}): void {\n    (this as Writable<this>).status = PENDING;\n\n    if (opts.emitEvent !== false) {\n      (this.statusChanges as EventEmitter<FormControlStatus>).emit(this.status);\n    }\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent.markAsPending(opts);\n    }\n  }\n\n  /**\n   * Disables the control. This means the control is exempt from validation checks and\n   * excluded from the aggregate value of any parent. Its status is `DISABLED`.\n   *\n   * If the control has children, all children are also disabled.\n   *\n   * @see {@link AbstractControl.status}\n   *\n   * @param opts Configuration options that determine how the control propagates\n   * changes and emits events after the control is disabled.\n   * * `onlySelf`: When true, mark only this control. When false or not supplied,\n   * marks all direct ancestors. Default is false.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges`\n   * observables emit events with the latest status and value when the control is disabled.\n   * When false, no events are emitted.\n   */\n  disable(opts: {onlySelf?: boolean, emitEvent?: boolean} = {}): void {\n    // If parent has been marked artificially dirty we don't want to re-calculate the\n    // parent's dirtiness based on the children.\n    const skipPristineCheck = this._parentMarkedDirty(opts.onlySelf);\n\n    (this as Writable<this>).status = DISABLED;\n    (this as Writable<this>).errors = null;\n    this._forEachChild((control: AbstractControl) => {\n      control.disable({...opts, onlySelf: true});\n    });\n    this._updateValue();\n\n    if (opts.emitEvent !== false) {\n      (this.valueChanges as EventEmitter<TValue>).emit(this.value);\n      (this.statusChanges as EventEmitter<FormControlStatus>).emit(this.status);\n    }\n\n    this._updateAncestors({...opts, skipPristineCheck});\n    this._onDisabledChange.forEach((changeFn) => changeFn(true));\n  }\n\n  /**\n   * Enables the control. This means the control is included in validation checks and\n   * the aggregate value of its parent. Its status recalculates based on its value and\n   * its validators.\n   *\n   * By default, if the control has children, all children are enabled.\n   *\n   * @see {@link AbstractControl.status}\n   *\n   * @param opts Configure options that control how the control propagates changes and\n   * emits events when marked as untouched\n   * * `onlySelf`: When true, mark only this control. When false or not supplied,\n   * marks all direct ancestors. Default is false.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges`\n   * observables emit events with the latest status and value when the control is enabled.\n   * When false, no events are emitted.\n   */\n  enable(opts: {onlySelf?: boolean, emitEvent?: boolean} = {}): void {\n    // If parent has been marked artificially dirty we don't want to re-calculate the\n    // parent's dirtiness based on the children.\n    const skipPristineCheck = this._parentMarkedDirty(opts.onlySelf);\n\n    (this as Writable<this>).status = VALID;\n    this._forEachChild((control: AbstractControl) => {\n      control.enable({...opts, onlySelf: true});\n    });\n    this.updateValueAndValidity({onlySelf: true, emitEvent: opts.emitEvent});\n\n    this._updateAncestors({...opts, skipPristineCheck});\n    this._onDisabledChange.forEach((changeFn) => changeFn(false));\n  }\n\n  private _updateAncestors(\n      opts: {onlySelf?: boolean, emitEvent?: boolean, skipPristineCheck?: boolean}): void {\n    if (this._parent && !opts.onlySelf) {\n      this._parent.updateValueAndValidity(opts);\n      if (!opts.skipPristineCheck) {\n        this._parent._updatePristine();\n      }\n      this._parent._updateTouched();\n    }\n  }\n\n  /**\n   * Sets the parent of the control\n   *\n   * @param parent The new parent.\n   */\n  setParent(parent: FormGroup|FormArray|null): void {\n    this._parent = parent;\n  }\n\n  /**\n   * Sets the value of the control. Abstract method (implemented in sub-classes).\n   */\n  abstract setValue(value: TRawValue, options?: Object): void;\n\n  /**\n   * Patches the value of the control. Abstract method (implemented in sub-classes).\n   */\n  abstract patchValue(value: TValue, options?: Object): void;\n\n  /**\n   * Resets the control. Abstract method (implemented in sub-classes).\n   */\n  abstract reset(value?: TValue, options?: Object): void;\n\n  /**\n   * The raw value of this control. For most control implementations, the raw value will include\n   * disabled children.\n   */\n  getRawValue(): any {\n    return this.value;\n  }\n\n  /**\n   * Recalculates the value and validation status of the control.\n   *\n   * By default, it also updates the value and validity of its ancestors.\n   *\n   * @param opts Configuration options determine how the control propagates changes and emits events\n   * after updates and validity checks are applied.\n   * * `onlySelf`: When true, only update this control. When false or not supplied,\n   * update all direct ancestors. Default is false.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges`\n   * observables emit events with the latest status and value when the control is updated.\n   * When false, no events are emitted.\n   */\n  updateValueAndValidity(opts: {onlySelf?: boolean, emitEvent?: boolean} = {}): void {\n    this._setInitialStatus();\n    this._updateValue();\n\n    if (this.enabled) {\n      this._cancelExistingSubscription();\n      (this as Writable<this>).errors = this._runValidator();\n      (this as Writable<this>).status = this._calculateStatus();\n\n      if (this.status === VALID || this.status === PENDING) {\n        this._runAsyncValidator(opts.emitEvent);\n      }\n    }\n\n    if (opts.emitEvent !== false) {\n      (this.valueChanges as EventEmitter<TValue>).emit(this.value);\n      (this.statusChanges as EventEmitter<FormControlStatus>).emit(this.status);\n    }\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent.updateValueAndValidity(opts);\n    }\n  }\n\n  /** @internal */\n  _updateTreeValidity(opts: {emitEvent?: boolean} = {emitEvent: true}): void {\n    this._forEachChild((ctrl: AbstractControl) => ctrl._updateTreeValidity(opts));\n    this.updateValueAndValidity({onlySelf: true, emitEvent: opts.emitEvent});\n  }\n\n  private _setInitialStatus() {\n    (this as Writable<this>).status = this._allControlsDisabled() ? DISABLED : VALID;\n  }\n\n  private _runValidator(): ValidationErrors|null {\n    return this.validator ? this.validator(this) : null;\n  }\n\n  private _runAsyncValidator(emitEvent?: boolean): void {\n    if (this.asyncValidator) {\n      (this as Writable<this>).status = PENDING;\n      this._hasOwnPendingAsyncValidator = true;\n      const obs = toObservable(this.asyncValidator(this));\n      this._asyncValidationSubscription = obs.subscribe((errors: ValidationErrors|null) => {\n        this._hasOwnPendingAsyncValidator = false;\n        // This will trigger the recalculation of the validation status, which depends on\n        // the state of the asynchronous validation (whether it is in progress or not). So, it is\n        // necessary that we have updated the `_hasOwnPendingAsyncValidator` boolean flag first.\n        this.setErrors(errors, {emitEvent});\n      });\n    }\n  }\n\n  private _cancelExistingSubscription(): void {\n    if (this._asyncValidationSubscription) {\n      this._asyncValidationSubscription.unsubscribe();\n      this._hasOwnPendingAsyncValidator = false;\n    }\n  }\n\n  /**\n   * Sets errors on a form control when running validations manually, rather than automatically.\n   *\n   * Calling `setErrors` also updates the validity of the parent control.\n   *\n   * @param opts Configuration options that determine how the control propagates\n   * changes and emits events after the control errors are set.\n   * * `emitEvent`: When true or not supplied (the default), the `statusChanges`\n   * observable emits an event after the errors are set.\n   *\n   * @usageNotes\n   *\n   * ### Manually set the errors for a control\n   *\n   * ```\n   * const login = new FormControl('someLogin');\n   * login.setErrors({\n   *   notUnique: true\n   * });\n   *\n   * expect(login.valid).toEqual(false);\n   * expect(login.errors).toEqual({ notUnique: true });\n   *\n   * login.setValue('someOtherLogin');\n   *\n   * expect(login.valid).toEqual(true);\n   * ```\n   */\n  setErrors(errors: ValidationErrors|null, opts: {emitEvent?: boolean} = {}): void {\n    (this as Writable<this>).errors = errors;\n    this._updateControlsErrors(opts.emitEvent !== false);\n  }\n\n  /**\n   * Retrieves a child control given the control's name or path.\n   *\n   * This signature for get supports strings and `const` arrays (`.get(['foo', 'bar'] as const)`).\n   */\n  get<P extends string|(readonly(string|number)[])>(path: P):\n      AbstractControl<ɵGetProperty<TRawValue, P>>|null;\n\n  /**\n   * Retrieves a child control given the control's name or path.\n   *\n   * This signature for `get` supports non-const (mutable) arrays. Inferred type\n   * information will not be as robust, so prefer to pass a `readonly` array if possible.\n   */\n  get<P extends string|Array<string|number>>(path: P):\n      AbstractControl<ɵGetProperty<TRawValue, P>>|null;\n\n  /**\n   * Retrieves a child control given the control's name or path.\n   *\n   * @param path A dot-delimited string or array of string/number values that define the path to the\n   * control. If a string is provided, passing it as a string literal will result in improved type\n   * information. Likewise, if an array is provided, passing it `as const` will cause improved type\n   * information to be available.\n   *\n   * @usageNotes\n   * ### Retrieve a nested control\n   *\n   * For example, to get a `name` control nested within a `person` sub-group:\n   *\n   * * `this.form.get('person.name');`\n   *\n   * -OR-\n   *\n   * * `this.form.get(['person', 'name'] as const);` // `as const` gives improved typings\n   *\n   * ### Retrieve a control in a FormArray\n   *\n   * When accessing an element inside a FormArray, you can use an element index.\n   * For example, to get a `price` control from the first element in an `items` array you can use:\n   *\n   * * `this.form.get('items.0.price');`\n   *\n   * -OR-\n   *\n   * * `this.form.get(['items', 0, 'price']);`\n   */\n  get<P extends string|((string | number)[])>(path: P):\n      AbstractControl<ɵGetProperty<TRawValue, P>>|null {\n    let currPath: Array<string|number>|string = path;\n    if (currPath == null) return null;\n    if (!Array.isArray(currPath)) currPath = currPath.split('.');\n    if (currPath.length === 0) return null;\n    return currPath.reduce(\n        (control: AbstractControl|null, name) => control && control._find(name), this);\n  }\n\n  /**\n   * @description\n   * Reports error data for the control with the given path.\n   *\n   * @param errorCode The code of the error to check\n   * @param path A list of control names that designates how to move from the current control\n   * to the control that should be queried for errors.\n   *\n   * @usageNotes\n   * For example, for the following `FormGroup`:\n   *\n   * ```\n   * form = new FormGroup({\n   *   address: new FormGroup({ street: new FormControl() })\n   * });\n   * ```\n   *\n   * The path to the 'street' control from the root form would be 'address' -> 'street'.\n   *\n   * It can be provided to this method in one of two formats:\n   *\n   * 1. An array of string control names, e.g. `['address', 'street']`\n   * 1. A period-delimited list of control names in one string, e.g. `'address.street'`\n   *\n   * @returns error data for that particular error. If the control or error is not present,\n   * null is returned.\n   */\n  getError(errorCode: string, path?: Array<string|number>|string): any {\n    const control = path ? this.get(path) : this;\n    return control && control.errors ? control.errors[errorCode] : null;\n  }\n\n  /**\n   * @description\n   * Reports whether the control with the given path has the error specified.\n   *\n   * @param errorCode The code of the error to check\n   * @param path A list of control names that designates how to move from the current control\n   * to the control that should be queried for errors.\n   *\n   * @usageNotes\n   * For example, for the following `FormGroup`:\n   *\n   * ```\n   * form = new FormGroup({\n   *   address: new FormGroup({ street: new FormControl() })\n   * });\n   * ```\n   *\n   * The path to the 'street' control from the root form would be 'address' -> 'street'.\n   *\n   * It can be provided to this method in one of two formats:\n   *\n   * 1. An array of string control names, e.g. `['address', 'street']`\n   * 1. A period-delimited list of control names in one string, e.g. `'address.street'`\n   *\n   * If no path is given, this method checks for the error on the current control.\n   *\n   * @returns whether the given error is present in the control at the given path.\n   *\n   * If the control is not present, false is returned.\n   */\n  hasError(errorCode: string, path?: Array<string|number>|string): boolean {\n    return !!this.getError(errorCode, path);\n  }\n\n  /**\n   * Retrieves the top-level ancestor of this control.\n   */\n  get root(): AbstractControl {\n    let x: AbstractControl = this;\n\n    while (x._parent) {\n      x = x._parent;\n    }\n\n    return x;\n  }\n\n  /** @internal */\n  _updateControlsErrors(emitEvent: boolean): void {\n    (this as Writable<this>).status = this._calculateStatus();\n\n    if (emitEvent) {\n      (this.statusChanges as EventEmitter<FormControlStatus>).emit(this.status);\n    }\n\n    if (this._parent) {\n      this._parent._updateControlsErrors(emitEvent);\n    }\n  }\n\n  /** @internal */\n  _initObservables() {\n    (this as Writable<this>).valueChanges = new EventEmitter();\n    (this as Writable<this>).statusChanges = new EventEmitter();\n  }\n\n\n  private _calculateStatus(): FormControlStatus {\n    if (this._allControlsDisabled()) return DISABLED;\n    if (this.errors) return INVALID;\n    if (this._hasOwnPendingAsyncValidator || this._anyControlsHaveStatus(PENDING)) return PENDING;\n    if (this._anyControlsHaveStatus(INVALID)) return INVALID;\n    return VALID;\n  }\n\n  /** @internal */\n  abstract _updateValue(): void;\n\n  /** @internal */\n  abstract _forEachChild(cb: (c: AbstractControl) => void): void;\n\n  /** @internal */\n  abstract _anyControls(condition: (c: AbstractControl) => boolean): boolean;\n\n  /** @internal */\n  abstract _allControlsDisabled(): boolean;\n\n  /** @internal */\n  abstract _syncPendingControls(): boolean;\n\n  /** @internal */\n  _anyControlsHaveStatus(status: FormControlStatus): boolean {\n    return this._anyControls((control: AbstractControl) => control.status === status);\n  }\n\n  /** @internal */\n  _anyControlsDirty(): boolean {\n    return this._anyControls((control: AbstractControl) => control.dirty);\n  }\n\n  /** @internal */\n  _anyControlsTouched(): boolean {\n    return this._anyControls((control: AbstractControl) => control.touched);\n  }\n\n  /** @internal */\n  _updatePristine(opts: {onlySelf?: boolean} = {}): void {\n    (this as Writable<this>).pristine = !this._anyControlsDirty();\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent._updatePristine(opts);\n    }\n  }\n\n  /** @internal */\n  _updateTouched(opts: {onlySelf?: boolean} = {}): void {\n    (this as Writable<this>).touched = this._anyControlsTouched();\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent._updateTouched(opts);\n    }\n  }\n\n  /** @internal */\n  _onDisabledChange: Array<(isDisabled: boolean) => void> = [];\n\n  /** @internal */\n  _registerOnCollectionChange(fn: () => void): void {\n    this._onCollectionChange = fn;\n  }\n\n  /** @internal */\n  _setUpdateStrategy(opts?: ValidatorFn|ValidatorFn[]|AbstractControlOptions|null): void {\n    if (isOptionsObj(opts) && opts.updateOn != null) {\n      this._updateOn = opts.updateOn!;\n    }\n  }\n  /**\n   * Check to see if parent has been marked artificially dirty.\n   *\n   * @internal\n   */\n  private _parentMarkedDirty(onlySelf?: boolean): boolean {\n    const parentDirty = this._parent && this._parent.dirty;\n    return !onlySelf && !!parentDirty && !this._parent!._anyControlsDirty();\n  }\n\n  /** @internal */\n  _find(name: string|number): AbstractControl|null {\n    return null;\n  }\n\n  /**\n   * Internal implementation of the `setValidators` method. Needs to be separated out into a\n   * different method, because it is called in the constructor and it can break cases where\n   * a control is extended.\n   */\n  private _assignValidators(validators: ValidatorFn|ValidatorFn[]|null): void {\n    this._rawValidators = Array.isArray(validators) ? validators.slice() : validators;\n    this._composedValidatorFn = coerceToValidator(this._rawValidators);\n  }\n\n  /**\n   * Internal implementation of the `setAsyncValidators` method. Needs to be separated out into a\n   * different method, because it is called in the constructor and it can break cases where\n   * a control is extended.\n   */\n  private _assignAsyncValidators(validators: AsyncValidatorFn|AsyncValidatorFn[]|null): void {\n    this._rawAsyncValidators = Array.isArray(validators) ? validators.slice() : validators;\n    this._composedAsyncValidatorFn = coerceToAsyncValidator(this._rawAsyncValidators);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵWritable as Writable} from '@angular/core';\n\nimport {AsyncValidatorFn, ValidatorFn} from '../directives/validators';\n\nimport {AbstractControl, AbstractControlOptions, assertAllValuesPresent, assertControlPresent, pickAsyncValidators, pickValidators, ɵRawValue, ɵTypedOrUntyped, ɵValue} from './abstract_model';\n\n/**\n * FormGroupValue extracts the type of `.value` from a FormGroup's inner object type. The untyped\n * case falls back to {[key: string]: any}.\n *\n * Angular uses this type internally to support Typed Forms; do not use it directly.\n *\n * For internal use only.\n */\nexport type ɵFormGroupValue<T extends {[K in keyof T]?: AbstractControl<any>}> =\n    ɵTypedOrUntyped<T, Partial<{[K in keyof T]: ɵValue<T[K]>}>, {[key: string]: any}>;\n\n/**\n * FormGroupRawValue extracts the type of `.getRawValue()` from a FormGroup's inner object type. The\n * untyped case falls back to {[key: string]: any}.\n *\n * Angular uses this type internally to support Typed Forms; do not use it directly.\n *\n * For internal use only.\n */\nexport type ɵFormGroupRawValue<T extends {[K in keyof T]?: AbstractControl<any>}> =\n    ɵTypedOrUntyped<T, {[K in keyof T]: ɵRawValue<T[K]>}, {[key: string]: any}>;\n\n/**\n * OptionalKeys returns the union of all optional keys in the object.\n *\n * Angular uses this type internally to support Typed Forms; do not use it directly.\n */\nexport type ɵOptionalKeys<T> = {\n  [K in keyof T] -?: undefined extends T[K] ? K : never\n}[keyof T];\n\n/**\n * Tracks the value and validity state of a group of `FormControl` instances.\n *\n * A `FormGroup` aggregates the values of each child `FormControl` into one object,\n * with each control name as the key.  It calculates its status by reducing the status values\n * of its children. For example, if one of the controls in a group is invalid, the entire\n * group becomes invalid.\n *\n * `FormGroup` is one of the four fundamental building blocks used to define forms in Angular,\n * along with `FormControl`, `FormArray`, and `FormRecord`.\n *\n * When instantiating a `FormGroup`, pass in a collection of child controls as the first\n * argument. The key for each child registers the name for the control.\n *\n * `FormGroup` is intended for use cases where the keys are known ahead of time.\n * If you need to dynamically add and remove controls, use {@link FormRecord} instead.\n *\n * `FormGroup` accepts an optional type parameter `TControl`, which is an object type with inner\n * control types as values.\n *\n * @usageNotes\n *\n * ### Create a form group with 2 controls\n *\n * ```\n * const form = new FormGroup({\n *   first: new FormControl('Nancy', Validators.minLength(2)),\n *   last: new FormControl('Drew'),\n * });\n *\n * console.log(form.value);   // {first: 'Nancy', last; 'Drew'}\n * console.log(form.status);  // 'VALID'\n * ```\n *\n * ### The type argument, and optional controls\n *\n * `FormGroup` accepts one generic argument, which is an object containing its inner controls.\n * This type will usually be inferred automatically, but you can always specify it explicitly if you\n * wish.\n *\n * If you have controls that are optional (i.e. they can be removed, you can use the `?` in the\n * type):\n *\n * ```\n * const form = new FormGroup<{\n *   first: FormControl<string|null>,\n *   middle?: FormControl<string|null>, // Middle name is optional.\n *   last: FormControl<string|null>,\n * }>({\n *   first: new FormControl('Nancy'),\n *   last: new FormControl('Drew'),\n * });\n * ```\n *\n * ### Create a form group with a group-level validator\n *\n * You include group-level validators as the second arg, or group-level async\n * validators as the third arg. These come in handy when you want to perform validation\n * that considers the value of more than one child control.\n *\n * ```\n * const form = new FormGroup({\n *   password: new FormControl('', Validators.minLength(2)),\n *   passwordConfirm: new FormControl('', Validators.minLength(2)),\n * }, passwordMatchValidator);\n *\n *\n * function passwordMatchValidator(g: FormGroup) {\n *    return g.get('password').value === g.get('passwordConfirm').value\n *       ? null : {'mismatch': true};\n * }\n * ```\n *\n * Like `FormControl` instances, you choose to pass in\n * validators and async validators as part of an options object.\n *\n * ```\n * const form = new FormGroup({\n *   password: new FormControl('')\n *   passwordConfirm: new FormControl('')\n * }, { validators: passwordMatchValidator, asyncValidators: otherValidator });\n * ```\n *\n * ### Set the updateOn property for all controls in a form group\n *\n * The options object is used to set a default value for each child\n * control's `updateOn` property. If you set `updateOn` to `'blur'` at the\n * group level, all child controls default to 'blur', unless the child\n * has explicitly specified a different `updateOn` value.\n *\n * ```ts\n * const c = new FormGroup({\n *   one: new FormControl()\n * }, { updateOn: 'blur' });\n * ```\n *\n * ### Using a FormGroup with optional controls\n *\n * It is possible to have optional controls in a FormGroup. An optional control can be removed later\n * using `removeControl`, and can be omitted when calling `reset`. Optional controls must be\n * declared optional in the group's type.\n *\n * ```ts\n * const c = new FormGroup<{one?: FormControl<string>}>({\n *   one: new FormControl('')\n * });\n * ```\n *\n * Notice that `c.value.one` has type `string|null|undefined`. This is because calling `c.reset({})`\n * without providing the optional key `one` will cause it to become `null`.\n *\n * @publicApi\n */\nexport class FormGroup<TControl extends {[K in keyof TControl]: AbstractControl<any>} = any> extends\n    AbstractControl<\n        ɵTypedOrUntyped<TControl, ɵFormGroupValue<TControl>, any>,\n        ɵTypedOrUntyped<TControl, ɵFormGroupRawValue<TControl>, any>> {\n  /**\n   * Creates a new `FormGroup` instance.\n   *\n   * @param controls A collection of child controls. The key for each child is the name\n   * under which it is registered.\n   *\n   * @param validatorOrOpts A synchronous validator function, or an array of\n   * such functions, or an `AbstractControlOptions` object that contains validation functions\n   * and a validation trigger.\n   *\n   * @param asyncValidator A single async validator or array of async validator functions\n   *\n   */\n  constructor(\n      controls: TControl, validatorOrOpts?: ValidatorFn|ValidatorFn[]|AbstractControlOptions|null,\n      asyncValidator?: AsyncValidatorFn|AsyncValidatorFn[]|null) {\n    super(pickValidators(validatorOrOpts), pickAsyncValidators(asyncValidator, validatorOrOpts));\n    (typeof ngDevMode === 'undefined' || ngDevMode) && validateFormGroupControls(controls);\n    this.controls = controls;\n    this._initObservables();\n    this._setUpdateStrategy(validatorOrOpts);\n    this._setUpControls();\n    this.updateValueAndValidity({\n      onlySelf: true,\n      // If `asyncValidator` is present, it will trigger control status change from `PENDING` to\n      // `VALID` or `INVALID`. The status should be broadcasted via the `statusChanges` observable,\n      // so we set `emitEvent` to `true` to allow that during the control creation process.\n      emitEvent: !!this.asyncValidator\n    });\n  }\n\n  public controls: ɵTypedOrUntyped<TControl, TControl, {[key: string]: AbstractControl<any>}>;\n\n  /**\n   * Registers a control with the group's list of controls. In a strongly-typed group, the control\n   * must be in the group's type (possibly as an optional key).\n   *\n   * This method does not update the value or validity of the control.\n   * Use {@link FormGroup#addControl addControl} instead.\n   *\n   * @param name The control name to register in the collection\n   * @param control Provides the control for the given name\n   */\n  registerControl<K extends string&keyof TControl>(name: K, control: TControl[K]): TControl[K];\n  registerControl(\n      this: FormGroup<{[key: string]: AbstractControl<any>}>, name: string,\n      control: AbstractControl<any>): AbstractControl<any>;\n\n  registerControl<K extends string&keyof TControl>(name: K, control: TControl[K]): TControl[K] {\n    if (this.controls[name]) return (this.controls as any)[name];\n    this.controls[name] = control;\n    control.setParent(this as FormGroup);\n    control._registerOnCollectionChange(this._onCollectionChange);\n    return control;\n  }\n\n  /**\n   * Add a control to this group. In a strongly-typed group, the control must be in the group's type\n   * (possibly as an optional key).\n   *\n   * If a control with a given name already exists, it would *not* be replaced with a new one.\n   * If you want to replace an existing control, use the {@link FormGroup#setControl setControl}\n   * method instead. This method also updates the value and validity of the control.\n   *\n   * @param name The control name to add to the collection\n   * @param control Provides the control for the given name\n   * @param options Specifies whether this FormGroup instance should emit events after a new\n   *     control is added.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges` observables emit events with the latest status and value when the control is\n   * added. When false, no events are emitted.\n   */\n  addControl(\n      this: FormGroup<{[key: string]: AbstractControl<any>}>, name: string,\n      control: AbstractControl, options?: {emitEvent?: boolean}): void;\n  addControl<K extends string&keyof TControl>(name: K, control: Required<TControl>[K], options?: {\n    emitEvent?: boolean\n  }): void;\n\n  addControl<K extends string&keyof TControl>(name: K, control: Required<TControl>[K], options: {\n    emitEvent?: boolean\n  } = {}): void {\n    this.registerControl(name, control);\n    this.updateValueAndValidity({emitEvent: options.emitEvent});\n    this._onCollectionChange();\n  }\n\n  removeControl(this: FormGroup<{[key: string]: AbstractControl<any>}>, name: string, options?: {\n    emitEvent?: boolean;\n  }): void;\n  removeControl<S extends string>(name: ɵOptionalKeys<TControl>&S, options?: {\n    emitEvent?: boolean;\n  }): void;\n\n  /**\n   * Remove a control from this group. In a strongly-typed group, required controls cannot be\n   * removed.\n   *\n   * This method also updates the value and validity of the control.\n   *\n   * @param name The control name to remove from the collection\n   * @param options Specifies whether this FormGroup instance should emit events after a\n   *     control is removed.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges` observables emit events with the latest status and value when the control is\n   * removed. When false, no events are emitted.\n   */\n  removeControl(name: string, options: {emitEvent?: boolean;} = {}): void {\n    if ((this.controls as any)[name])\n      (this.controls as any)[name]._registerOnCollectionChange(() => {});\n    delete ((this.controls as any)[name]);\n    this.updateValueAndValidity({emitEvent: options.emitEvent});\n    this._onCollectionChange();\n  }\n\n  /**\n   * Replace an existing control. In a strongly-typed group, the control must be in the group's type\n   * (possibly as an optional key).\n   *\n   * If a control with a given name does not exist in this `FormGroup`, it will be added.\n   *\n   * @param name The control name to replace in the collection\n   * @param control Provides the control for the given name\n   * @param options Specifies whether this FormGroup instance should emit events after an\n   *     existing control is replaced.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges` observables emit events with the latest status and value when the control is\n   * replaced with a new one. When false, no events are emitted.\n   */\n  setControl<K extends string&keyof TControl>(name: K, control: TControl[K], options?: {\n    emitEvent?: boolean\n  }): void;\n  setControl(\n      this: FormGroup<{[key: string]: AbstractControl<any>}>, name: string,\n      control: AbstractControl, options?: {emitEvent?: boolean}): void;\n\n  setControl<K extends string&keyof TControl>(name: K, control: TControl[K], options: {\n    emitEvent?: boolean\n  } = {}): void {\n    if (this.controls[name]) this.controls[name]._registerOnCollectionChange(() => {});\n    delete (this.controls[name]);\n    if (control) this.registerControl(name, control);\n    this.updateValueAndValidity({emitEvent: options.emitEvent});\n    this._onCollectionChange();\n  }\n\n  /**\n   * Check whether there is an enabled control with the given name in the group.\n   *\n   * Reports false for disabled controls. If you'd like to check for existence in the group\n   * only, use {@link AbstractControl#get get} instead.\n   *\n   * @param controlName The control name to check for existence in the collection\n   *\n   * @returns false for disabled controls, true otherwise.\n   */\n  contains<K extends string>(controlName: K): boolean;\n  contains(this: FormGroup<{[key: string]: AbstractControl<any>}>, controlName: string): boolean;\n\n  contains<K extends string&keyof TControl>(controlName: K): boolean {\n    return this.controls.hasOwnProperty(controlName) && this.controls[controlName].enabled;\n  }\n\n  /**\n   * Sets the value of the `FormGroup`. It accepts an object that matches\n   * the structure of the group, with control names as keys.\n   *\n   * @usageNotes\n   * ### Set the complete value for the form group\n   *\n   * ```\n   * const form = new FormGroup({\n   *   first: new FormControl(),\n   *   last: new FormControl()\n   * });\n   *\n   * console.log(form.value);   // {first: null, last: null}\n   *\n   * form.setValue({first: 'Nancy', last: 'Drew'});\n   * console.log(form.value);   // {first: 'Nancy', last: 'Drew'}\n   * ```\n   *\n   * @throws When strict checks fail, such as setting the value of a control\n   * that doesn't exist or if you exclude a value of a control that does exist.\n   *\n   * @param value The new value for the control that matches the structure of the group.\n   * @param options Configuration options that determine how the control propagates changes\n   * and emits events after the value changes.\n   * The configuration options are passed to the {@link AbstractControl#updateValueAndValidity\n   * updateValueAndValidity} method.\n   *\n   * * `onlySelf`: When true, each change only affects this control, and not its parent. Default is\n   * false.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges`\n   * observables emit events with the latest status and value when the control value is updated.\n   * When false, no events are emitted.\n   */\n  override setValue(value: ɵFormGroupRawValue<TControl>, options: {\n    onlySelf?: boolean,\n    emitEvent?: boolean\n  } = {}): void {\n    assertAllValuesPresent(this, true, value);\n    (Object.keys(value) as Array<keyof TControl>).forEach(name => {\n      assertControlPresent(this, true, name as any);\n      (this.controls as any)[name].setValue(\n          (value as any)[name], {onlySelf: true, emitEvent: options.emitEvent});\n    });\n    this.updateValueAndValidity(options);\n  }\n\n  /**\n   * Patches the value of the `FormGroup`. It accepts an object with control\n   * names as keys, and does its best to match the values to the correct controls\n   * in the group.\n   *\n   * It accepts both super-sets and sub-sets of the group without throwing an error.\n   *\n   * @usageNotes\n   * ### Patch the value for a form group\n   *\n   * ```\n   * const form = new FormGroup({\n   *    first: new FormControl(),\n   *    last: new FormControl()\n   * });\n   * console.log(form.value);   // {first: null, last: null}\n   *\n   * form.patchValue({first: 'Nancy'});\n   * console.log(form.value);   // {first: 'Nancy', last: null}\n   * ```\n   *\n   * @param value The object that matches the structure of the group.\n   * @param options Configuration options that determine how the control propagates changes and\n   * emits events after the value is patched.\n   * * `onlySelf`: When true, each change only affects this control and not its parent. Default is\n   * true.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges` observables emit events with the latest status and value when the control value\n   * is updated. When false, no events are emitted. The configuration options are passed to\n   * the {@link AbstractControl#updateValueAndValidity updateValueAndValidity} method.\n   */\n  override patchValue(value: ɵFormGroupValue<TControl>, options: {\n    onlySelf?: boolean,\n    emitEvent?: boolean\n  } = {}): void {\n    // Even though the `value` argument type doesn't allow `null` and `undefined` values, the\n    // `patchValue` can be called recursively and inner data structures might have these values, so\n    // we just ignore such cases when a field containing FormGroup instance receives `null` or\n    // `undefined` as a value.\n    if (value == null /* both `null` and `undefined` */) return;\n    (Object.keys(value) as Array<keyof TControl>).forEach(name => {\n      // The compiler cannot see through the uninstantiated conditional type of `this.controls`, so\n      // `as any` is required.\n      const control = (this.controls as any)[name];\n      if (control) {\n        control.patchValue(\n            /* Guaranteed to be present, due to the outer forEach. */ value\n                [name as keyof ɵFormGroupValue<TControl>]!,\n            {onlySelf: true, emitEvent: options.emitEvent});\n      }\n    });\n    this.updateValueAndValidity(options);\n  }\n\n  /**\n   * Resets the `FormGroup`, marks all descendants `pristine` and `untouched` and sets\n   * the value of all descendants to their default values, or null if no defaults were provided.\n   *\n   * You reset to a specific form state by passing in a map of states\n   * that matches the structure of your form, with control names as keys. The state\n   * is a standalone value or a form state object with both a value and a disabled\n   * status.\n   *\n   * @param value Resets the control with an initial value,\n   * or an object that defines the initial value and disabled state.\n   *\n   * @param options Configuration options that determine how the control propagates changes\n   * and emits events when the group is reset.\n   * * `onlySelf`: When true, each change only affects this control, and not its parent. Default is\n   * false.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges`\n   * observables emit events with the latest status and value when the control is reset.\n   * When false, no events are emitted.\n   * The configuration options are passed to the {@link AbstractControl#updateValueAndValidity\n   * updateValueAndValidity} method.\n   *\n   * @usageNotes\n   *\n   * ### Reset the form group values\n   *\n   * ```ts\n   * const form = new FormGroup({\n   *   first: new FormControl('first name'),\n   *   last: new FormControl('last name')\n   * });\n   *\n   * console.log(form.value);  // {first: 'first name', last: 'last name'}\n   *\n   * form.reset({ first: 'name', last: 'last name' });\n   *\n   * console.log(form.value);  // {first: 'name', last: 'last name'}\n   * ```\n   *\n   * ### Reset the form group values and disabled status\n   *\n   * ```\n   * const form = new FormGroup({\n   *   first: new FormControl('first name'),\n   *   last: new FormControl('last name')\n   * });\n   *\n   * form.reset({\n   *   first: {value: 'name', disabled: true},\n   *   last: 'last'\n   * });\n   *\n   * console.log(form.value);  // {last: 'last'}\n   * console.log(form.get('first').status);  // 'DISABLED'\n   * ```\n   */\n  override reset(\n      value: ɵTypedOrUntyped<TControl, ɵFormGroupValue<TControl>, any> = {} as unknown as\n          ɵFormGroupValue<TControl>,\n      options: {onlySelf?: boolean, emitEvent?: boolean} = {}): void {\n    this._forEachChild((control: AbstractControl, name) => {\n      control.reset(\n          value ? (value as any)[name] : null, {onlySelf: true, emitEvent: options.emitEvent});\n    });\n    this._updatePristine(options);\n    this._updateTouched(options);\n    this.updateValueAndValidity(options);\n  }\n\n  /**\n   * The aggregate value of the `FormGroup`, including any disabled controls.\n   *\n   * Retrieves all values regardless of disabled status.\n   */\n  override getRawValue(): ɵTypedOrUntyped<TControl, ɵFormGroupRawValue<TControl>, any> {\n    return this._reduceChildren({}, (acc, control, name) => {\n      (acc as any)[name] = (control as any).getRawValue();\n      return acc;\n    }) as any;\n  }\n\n  /** @internal */\n  override _syncPendingControls(): boolean {\n    let subtreeUpdated = this._reduceChildren(false, (updated: boolean, child) => {\n      return child._syncPendingControls() ? true : updated;\n    });\n    if (subtreeUpdated) this.updateValueAndValidity({onlySelf: true});\n    return subtreeUpdated;\n  }\n\n  /** @internal */\n  override _forEachChild(cb: (v: any, k: any) => void): void {\n    Object.keys(this.controls).forEach(key => {\n      // The list of controls can change (for ex. controls might be removed) while the loop\n      // is running (as a result of invoking Forms API in `valueChanges` subscription), so we\n      // have to null check before invoking the callback.\n      const control = (this.controls as any)[key];\n      control && cb(control, key);\n    });\n  }\n\n  /** @internal */\n  _setUpControls(): void {\n    this._forEachChild((control) => {\n      control.setParent(this);\n      control._registerOnCollectionChange(this._onCollectionChange);\n    });\n  }\n\n  /** @internal */\n  override _updateValue(): void {\n    (this as Writable<this>).value = this._reduceValue() as any;\n  }\n\n  /** @internal */\n  override _anyControls(condition: (c: AbstractControl) => boolean): boolean {\n    for (const [controlName, control] of Object.entries(this.controls)) {\n      if (this.contains(controlName as any) && condition(control as any)) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  /** @internal */\n  _reduceValue(): Partial<TControl> {\n    let acc: Partial<TControl> = {};\n    return this._reduceChildren(acc, (acc, control, name) => {\n      if (control.enabled || this.disabled) {\n        acc[name] = control.value;\n      }\n      return acc;\n    });\n  }\n\n  /** @internal */\n  _reduceChildren<T, K extends keyof TControl>(\n      initValue: T, fn: (acc: T, control: TControl[K], name: K) => T): T {\n    let res = initValue;\n    this._forEachChild((control: TControl[K], name: K) => {\n      res = fn(res, control, name);\n    });\n    return res;\n  }\n\n  /** @internal */\n  override _allControlsDisabled(): boolean {\n    for (const controlName of (Object.keys(this.controls) as Array<keyof TControl>)) {\n      if ((this.controls as any)[controlName].enabled) {\n        return false;\n      }\n    }\n    return Object.keys(this.controls).length > 0 || this.disabled;\n  }\n\n  /** @internal */\n  override _find(name: string|number): AbstractControl|null {\n    return this.controls.hasOwnProperty(name as string) ?\n        (this.controls as any)[name as keyof TControl] :\n        null;\n  }\n}\n\n/**\n * Will validate that none of the controls has a key with a dot\n * Throws other wise\n */\nfunction validateFormGroupControls<TControl>(\n    controls: {[K in keyof TControl]: AbstractControl<any, any>;}) {\n  const invalidKeys = Object.keys(controls).filter(key => key.includes('.'));\n  if (invalidKeys.length > 0) {\n    // TODO: make this an error once there are no more uses in G3\n    console.warn(`FormGroup keys cannot include \\`.\\`, please replace the keys for: ${\n        invalidKeys.join(',')}.`);\n  }\n}\n\n\ninterface UntypedFormGroupCtor {\n  new(controls: {[key: string]: AbstractControl},\n      validatorOrOpts?: ValidatorFn|ValidatorFn[]|AbstractControlOptions|null,\n      asyncValidator?: AsyncValidatorFn|AsyncValidatorFn[]|null): UntypedFormGroup;\n\n  /**\n   * The presence of an explicit `prototype` property provides backwards-compatibility for apps that\n   * manually inspect the prototype chain.\n   */\n  prototype: FormGroup<any>;\n}\n\n/**\n * UntypedFormGroup is a non-strongly-typed version of `FormGroup`.\n */\nexport type UntypedFormGroup = FormGroup<any>;\n\nexport const UntypedFormGroup: UntypedFormGroupCtor = FormGroup;\n\n/**\n * @description\n * Asserts that the given control is an instance of `FormGroup`\n *\n * @publicApi\n */\nexport const isFormGroup = (control: unknown): control is FormGroup => control instanceof FormGroup;\n\n/**\n * Tracks the value and validity state of a collection of `FormControl` instances, each of which has\n * the same value type.\n *\n * `FormRecord` is very similar to {@link FormGroup}, except it can be used with a dynamic keys,\n * with controls added and removed as needed.\n *\n * `FormRecord` accepts one generic argument, which describes the type of the controls it contains.\n *\n * @usageNotes\n *\n * ```\n * let numbers = new FormRecord({bill: new FormControl('415-123-456')});\n * numbers.addControl('bob', new FormControl('415-234-567'));\n * numbers.removeControl('bill');\n * ```\n *\n * @publicApi\n */\nexport class FormRecord<TControl extends AbstractControl = AbstractControl> extends\n    FormGroup<{[key: string]: TControl}> {}\n\nexport interface FormRecord<TControl> {\n  /**\n   * Registers a control with the records's list of controls.\n   *\n   * See `FormGroup#registerControl` for additional information.\n   */\n  registerControl(name: string, control: TControl): TControl;\n\n  /**\n   * Add a control to this group.\n   *\n   * See `FormGroup#addControl` for additional information.\n   */\n  addControl(name: string, control: TControl, options?: {emitEvent?: boolean}): void;\n\n  /**\n   * Remove a control from this group.\n   *\n   * See `FormGroup#removeControl` for additional information.\n   */\n  removeControl(name: string, options?: {emitEvent?: boolean}): void;\n\n  /**\n   * Replace an existing control.\n   *\n   * See `FormGroup#setControl` for additional information.\n   */\n  setControl(name: string, control: TControl, options?: {emitEvent?: boolean}): void;\n\n  /**\n   * Check whether there is an enabled control with the given name in the group.\n   *\n   * See `FormGroup#contains` for additional information.\n   */\n  contains(controlName: string): boolean;\n\n  /**\n   * Sets the value of the `FormRecord`. It accepts an object that matches\n   * the structure of the group, with control names as keys.\n   *\n   * See `FormGroup#setValue` for additional information.\n   */\n  setValue(value: {[key: string]: ɵValue<TControl>}, options?: {\n    onlySelf?: boolean,\n    emitEvent?: boolean\n  }): void;\n\n  /**\n   * Patches the value of the `FormRecord`. It accepts an object with control\n   * names as keys, and does its best to match the values to the correct controls\n   * in the group.\n   *\n   * See `FormGroup#patchValue` for additional information.\n   */\n  patchValue(value: {[key: string]: ɵValue<TControl>}, options?: {\n    onlySelf?: boolean,\n    emitEvent?: boolean\n  }): void;\n\n  /**\n   * Resets the `FormRecord`, marks all descendants `pristine` and `untouched` and sets\n   * the value of all descendants to null.\n   *\n   * See `FormGroup#reset` for additional information.\n   */\n  reset(value?: {[key: string]: ɵValue<TControl>}, options?: {\n    onlySelf?: boolean,\n    emitEvent?: boolean\n  }): void;\n\n  /**\n   * The aggregate value of the `FormRecord`, including any disabled controls.\n   *\n   * See `FormGroup#getRawValue` for additional information.\n   */\n  getRawValue(): {[key: string]: ɵRawValue<TControl>};\n}\n\n/**\n * @description\n * Asserts that the given control is an instance of `FormRecord`\n *\n * @publicApi\n */\nexport const isFormRecord = (control: unknown): control is FormRecord =>\n    control instanceof FormRecord;\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Inject, InjectionToken, ɵRuntimeError as RuntimeError} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../errors';\nimport {AbstractControl} from '../model/abstract_model';\nimport {FormArray} from '../model/form_array';\nimport {FormControl} from '../model/form_control';\nimport {FormGroup} from '../model/form_group';\nimport {getControlAsyncValidators, getControlValidators, mergeValidators} from '../validators';\n\nimport {AbstractControlDirective} from './abstract_control_directive';\nimport {AbstractFormGroupDirective} from './abstract_form_group_directive';\nimport {ControlContainer} from './control_container';\nimport {BuiltInControlValueAccessor, ControlValueAccessor} from './control_value_accessor';\nimport {DefaultValueAccessor} from './default_value_accessor';\nimport {NgControl} from './ng_control';\nimport {FormArrayName} from './reactive_directives/form_group_name';\nimport {ngModelWarning} from './reactive_errors';\nimport {AsyncValidatorFn, Validator, ValidatorFn} from './validators';\n\n/**\n * Token to provide to allow SetDisabledState to always be called when a CVA is added, regardless of\n * whether the control is disabled or enabled.\n *\n * @see {@link FormsModule#withconfig}\n */\nexport const CALL_SET_DISABLED_STATE = new InjectionToken(\n    'CallSetDisabledState', {providedIn: 'root', factory: () => setDisabledStateDefault});\n\n/**\n * The type for CALL_SET_DISABLED_STATE. If `always`, then ControlValueAccessor will always call\n * `setDisabledState` when attached, which is the most correct behavior. Otherwise, it will only be\n * called when disabled, which is the legacy behavior for compatibility.\n *\n * @publicApi\n * @see {@link FormsModule#withconfig}\n */\nexport type SetDisabledStateOption = 'whenDisabledForLegacyCode'|'always';\n\n/**\n * Whether to use the fixed setDisabledState behavior by default.\n */\nexport const setDisabledStateDefault: SetDisabledStateOption = 'always';\n\nexport function controlPath(name: string|null, parent: ControlContainer): string[] {\n  return [...parent.path!, name!];\n}\n\n/**\n * Links a Form control and a Form directive by setting up callbacks (such as `onChange`) on both\n * instances. This function is typically invoked when form directive is being initialized.\n *\n * @param control Form control instance that should be linked.\n * @param dir Directive that should be linked with a given control.\n */\nexport function setUpControl(\n    control: FormControl, dir: NgControl,\n    callSetDisabledState: SetDisabledStateOption = setDisabledStateDefault): void {\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    if (!control) _throwError(dir, 'Cannot find control with');\n    if (!dir.valueAccessor) _throwMissingValueAccessorError(dir);\n  }\n\n  setUpValidators(control, dir);\n\n  dir.valueAccessor!.writeValue(control.value);\n\n  // The legacy behavior only calls the CVA's `setDisabledState` if the control is disabled.\n  // If the `callSetDisabledState` option is set to `always`, then this bug is fixed and\n  // the method is always called.\n  if (control.disabled || callSetDisabledState === 'always') {\n    dir.valueAccessor!.setDisabledState?.(control.disabled);\n  }\n\n  setUpViewChangePipeline(control, dir);\n  setUpModelChangePipeline(control, dir);\n\n  setUpBlurPipeline(control, dir);\n\n  setUpDisabledChangeHandler(control, dir);\n}\n\n/**\n * Reverts configuration performed by the `setUpControl` control function.\n * Effectively disconnects form control with a given form directive.\n * This function is typically invoked when corresponding form directive is being destroyed.\n *\n * @param control Form control which should be cleaned up.\n * @param dir Directive that should be disconnected from a given control.\n * @param validateControlPresenceOnChange Flag that indicates whether onChange handler should\n *     contain asserts to verify that it's not called once directive is destroyed. We need this flag\n *     to avoid potentially breaking changes caused by better control cleanup introduced in #39235.\n */\nexport function cleanUpControl(\n    control: FormControl|null, dir: NgControl,\n    validateControlPresenceOnChange: boolean = true): void {\n  const noop = () => {\n    if (validateControlPresenceOnChange && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      _noControlError(dir);\n    }\n  };\n\n  // The `valueAccessor` field is typically defined on FromControl and FormControlName directive\n  // instances and there is a logic in `selectValueAccessor` function that throws if it's not the\n  // case. We still check the presence of `valueAccessor` before invoking its methods to make sure\n  // that cleanup works correctly if app code or tests are setup to ignore the error thrown from\n  // `selectValueAccessor`. See https://github.com/angular/angular/issues/40521.\n  if (dir.valueAccessor) {\n    dir.valueAccessor.registerOnChange(noop);\n    dir.valueAccessor.registerOnTouched(noop);\n  }\n\n  cleanUpValidators(control, dir);\n\n  if (control) {\n    dir._invokeOnDestroyCallbacks();\n    control._registerOnCollectionChange(() => {});\n  }\n}\n\nfunction registerOnValidatorChange<V>(validators: (V|Validator)[], onChange: () => void): void {\n  validators.forEach((validator: V|Validator) => {\n    if ((<Validator>validator).registerOnValidatorChange)\n      (<Validator>validator).registerOnValidatorChange!(onChange);\n  });\n}\n\n/**\n * Sets up disabled change handler function on a given form control if ControlValueAccessor\n * associated with a given directive instance supports the `setDisabledState` call.\n *\n * @param control Form control where disabled change handler should be setup.\n * @param dir Corresponding directive instance associated with this control.\n */\nexport function setUpDisabledChangeHandler(control: FormControl, dir: NgControl): void {\n  if (dir.valueAccessor!.setDisabledState) {\n    const onDisabledChange = (isDisabled: boolean) => {\n      dir.valueAccessor!.setDisabledState!(isDisabled);\n    };\n    control.registerOnDisabledChange(onDisabledChange);\n\n    // Register a callback function to cleanup disabled change handler\n    // from a control instance when a directive is destroyed.\n    dir._registerOnDestroy(() => {\n      control._unregisterOnDisabledChange(onDisabledChange);\n    });\n  }\n}\n\n/**\n * Sets up sync and async directive validators on provided form control.\n * This function merges validators from the directive into the validators of the control.\n *\n * @param control Form control where directive validators should be setup.\n * @param dir Directive instance that contains validators to be setup.\n */\nexport function setUpValidators(control: AbstractControl, dir: AbstractControlDirective): void {\n  const validators = getControlValidators(control);\n  if (dir.validator !== null) {\n    control.setValidators(mergeValidators<ValidatorFn>(validators, dir.validator));\n  } else if (typeof validators === 'function') {\n    // If sync validators are represented by a single validator function, we force the\n    // `Validators.compose` call to happen by executing the `setValidators` function with\n    // an array that contains that function. We need this to avoid possible discrepancies in\n    // validators behavior, so sync validators are always processed by the `Validators.compose`.\n    // Note: we should consider moving this logic inside the `setValidators` function itself, so we\n    // have consistent behavior on AbstractControl API level. The same applies to the async\n    // validators logic below.\n    control.setValidators([validators]);\n  }\n\n  const asyncValidators = getControlAsyncValidators(control);\n  if (dir.asyncValidator !== null) {\n    control.setAsyncValidators(\n        mergeValidators<AsyncValidatorFn>(asyncValidators, dir.asyncValidator));\n  } else if (typeof asyncValidators === 'function') {\n    control.setAsyncValidators([asyncValidators]);\n  }\n\n  // Re-run validation when validator binding changes, e.g. minlength=3 -> minlength=4\n  const onValidatorChange = () => control.updateValueAndValidity();\n  registerOnValidatorChange<ValidatorFn>(dir._rawValidators, onValidatorChange);\n  registerOnValidatorChange<AsyncValidatorFn>(dir._rawAsyncValidators, onValidatorChange);\n}\n\n/**\n * Cleans up sync and async directive validators on provided form control.\n * This function reverts the setup performed by the `setUpValidators` function, i.e.\n * removes directive-specific validators from a given control instance.\n *\n * @param control Form control from where directive validators should be removed.\n * @param dir Directive instance that contains validators to be removed.\n * @returns true if a control was updated as a result of this action.\n */\nexport function cleanUpValidators(\n    control: AbstractControl|null, dir: AbstractControlDirective): boolean {\n  let isControlUpdated = false;\n  if (control !== null) {\n    if (dir.validator !== null) {\n      const validators = getControlValidators(control);\n      if (Array.isArray(validators) && validators.length > 0) {\n        // Filter out directive validator function.\n        const updatedValidators = validators.filter((validator) => validator !== dir.validator);\n        if (updatedValidators.length !== validators.length) {\n          isControlUpdated = true;\n          control.setValidators(updatedValidators);\n        }\n      }\n    }\n\n    if (dir.asyncValidator !== null) {\n      const asyncValidators = getControlAsyncValidators(control);\n      if (Array.isArray(asyncValidators) && asyncValidators.length > 0) {\n        // Filter out directive async validator function.\n        const updatedAsyncValidators =\n            asyncValidators.filter((asyncValidator) => asyncValidator !== dir.asyncValidator);\n        if (updatedAsyncValidators.length !== asyncValidators.length) {\n          isControlUpdated = true;\n          control.setAsyncValidators(updatedAsyncValidators);\n        }\n      }\n    }\n  }\n\n  // Clear onValidatorChange callbacks by providing a noop function.\n  const noop = () => {};\n  registerOnValidatorChange<ValidatorFn>(dir._rawValidators, noop);\n  registerOnValidatorChange<AsyncValidatorFn>(dir._rawAsyncValidators, noop);\n\n  return isControlUpdated;\n}\n\nfunction setUpViewChangePipeline(control: FormControl, dir: NgControl): void {\n  dir.valueAccessor!.registerOnChange((newValue: any) => {\n    control._pendingValue = newValue;\n    control._pendingChange = true;\n    control._pendingDirty = true;\n\n    if (control.updateOn === 'change') updateControl(control, dir);\n  });\n}\n\nfunction setUpBlurPipeline(control: FormControl, dir: NgControl): void {\n  dir.valueAccessor!.registerOnTouched(() => {\n    control._pendingTouched = true;\n\n    if (control.updateOn === 'blur' && control._pendingChange) updateControl(control, dir);\n    if (control.updateOn !== 'submit') control.markAsTouched();\n  });\n}\n\nfunction updateControl(control: FormControl, dir: NgControl): void {\n  if (control._pendingDirty) control.markAsDirty();\n  control.setValue(control._pendingValue, {emitModelToViewChange: false});\n  dir.viewToModelUpdate(control._pendingValue);\n  control._pendingChange = false;\n}\n\nfunction setUpModelChangePipeline(control: FormControl, dir: NgControl): void {\n  const onChange = (newValue?: any, emitModelEvent?: boolean) => {\n    // control -> view\n    dir.valueAccessor!.writeValue(newValue);\n\n    // control -> ngModel\n    if (emitModelEvent) dir.viewToModelUpdate(newValue);\n  };\n  control.registerOnChange(onChange);\n\n  // Register a callback function to cleanup onChange handler\n  // from a control instance when a directive is destroyed.\n  dir._registerOnDestroy(() => {\n    control._unregisterOnChange(onChange);\n  });\n}\n\n/**\n * Links a FormGroup or FormArray instance and corresponding Form directive by setting up validators\n * present in the view.\n *\n * @param control FormGroup or FormArray instance that should be linked.\n * @param dir Directive that provides view validators.\n */\nexport function setUpFormContainer(\n    control: FormGroup|FormArray, dir: AbstractFormGroupDirective|FormArrayName) {\n  if (control == null && (typeof ngDevMode === 'undefined' || ngDevMode))\n    _throwError(dir, 'Cannot find control with');\n  setUpValidators(control, dir);\n}\n\n/**\n * Reverts the setup performed by the `setUpFormContainer` function.\n *\n * @param control FormGroup or FormArray instance that should be cleaned up.\n * @param dir Directive that provided view validators.\n * @returns true if a control was updated as a result of this action.\n */\nexport function cleanUpFormContainer(\n    control: FormGroup|FormArray, dir: AbstractFormGroupDirective|FormArrayName): boolean {\n  return cleanUpValidators(control, dir);\n}\n\nfunction _noControlError(dir: NgControl) {\n  return _throwError(dir, 'There is no FormControl instance attached to form control element with');\n}\n\nfunction _throwError(dir: AbstractControlDirective, message: string): void {\n  const messageEnd = _describeControlLocation(dir);\n  throw new Error(`${message} ${messageEnd}`);\n}\n\nfunction _describeControlLocation(dir: AbstractControlDirective): string {\n  const path = dir.path;\n  if (path && path.length > 1) return `path: '${path.join(' -> ')}'`;\n  if (path?.[0]) return `name: '${path}'`;\n  return 'unspecified name attribute';\n}\n\nfunction _throwMissingValueAccessorError(dir: AbstractControlDirective) {\n  const loc = _describeControlLocation(dir);\n  throw new RuntimeError(\n      RuntimeErrorCode.NG_MISSING_VALUE_ACCESSOR, `No value accessor for form control ${loc}.`);\n}\n\nfunction _throwInvalidValueAccessorError(dir: AbstractControlDirective) {\n  const loc = _describeControlLocation(dir);\n  throw new RuntimeError(\n      RuntimeErrorCode.NG_VALUE_ACCESSOR_NOT_PROVIDED,\n      `Value accessor was not provided as an array for form control with ${loc}. ` +\n          `Check that the \\`NG_VALUE_ACCESSOR\\` token is configured as a \\`multi: true\\` provider.`);\n}\n\nexport function isPropertyUpdated(changes: {[key: string]: any}, viewModel: any): boolean {\n  if (!changes.hasOwnProperty('model')) return false;\n  const change = changes['model'];\n\n  if (change.isFirstChange()) return true;\n  return !Object.is(viewModel, change.currentValue);\n}\n\nexport function isBuiltInAccessor(valueAccessor: ControlValueAccessor): boolean {\n  // Check if a given value accessor is an instance of a class that directly extends\n  // `BuiltInControlValueAccessor` one.\n  return Object.getPrototypeOf(valueAccessor.constructor) === BuiltInControlValueAccessor;\n}\n\nexport function syncPendingControls(form: FormGroup, directives: Set<NgControl>|NgControl[]): void {\n  form._syncPendingControls();\n  directives.forEach((dir: NgControl) => {\n    const control = dir.control as FormControl;\n    if (control.updateOn === 'submit' && control._pendingChange) {\n      dir.viewToModelUpdate(control._pendingValue);\n      control._pendingChange = false;\n    }\n  });\n}\n\n// TODO: vsavkin remove it once https://github.com/angular/angular/issues/3011 is implemented\nexport function selectValueAccessor(\n    dir: NgControl, valueAccessors: ControlValueAccessor[]): ControlValueAccessor|null {\n  if (!valueAccessors) return null;\n\n  if (!Array.isArray(valueAccessors) && (typeof ngDevMode === 'undefined' || ngDevMode))\n    _throwInvalidValueAccessorError(dir);\n\n  let defaultAccessor: ControlValueAccessor|undefined = undefined;\n  let builtinAccessor: ControlValueAccessor|undefined = undefined;\n  let customAccessor: ControlValueAccessor|undefined = undefined;\n\n  valueAccessors.forEach((v: ControlValueAccessor) => {\n    if (v.constructor === DefaultValueAccessor) {\n      defaultAccessor = v;\n    } else if (isBuiltInAccessor(v)) {\n      if (builtinAccessor && (typeof ngDevMode === 'undefined' || ngDevMode))\n        _throwError(dir, 'More than one built-in value accessor matches form control with');\n      builtinAccessor = v;\n    } else {\n      if (customAccessor && (typeof ngDevMode === 'undefined' || ngDevMode))\n        _throwError(dir, 'More than one custom value accessor matches form control with');\n      customAccessor = v;\n    }\n  });\n\n  if (customAccessor) return customAccessor;\n  if (builtinAccessor) return builtinAccessor;\n  if (defaultAccessor) return defaultAccessor;\n\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    _throwError(dir, 'No valid value accessor for form control with');\n  }\n  return null;\n}\n\nexport function removeListItem<T>(list: T[], el: T): void {\n  const index = list.indexOf(el);\n  if (index > -1) list.splice(index, 1);\n}\n\n// TODO(kara): remove after deprecation period\nexport function _ngModelWarning(\n    name: string, type: {_ngModelWarningSentOnce: boolean},\n    instance: {_ngModelWarningSent: boolean}, warningConfig: string|null) {\n  if (warningConfig === 'never') return;\n\n  if (((warningConfig === null || warningConfig === 'once') && !type._ngModelWarningSentOnce) ||\n      (warningConfig === 'always' && !instance._ngModelWarningSent)) {\n    console.warn(ngModelWarning(name));\n    type._ngModelWarningSentOnce = true;\n    instance._ngModelWarningSent = true;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AfterViewInit, Directive, EventEmitter, forwardRef, Inject, Input, Optional, Provider, Self, ɵWritable as Writable} from '@angular/core';\n\nimport {AbstractControl, FormHooks} from '../model/abstract_model';\nimport {FormControl} from '../model/form_control';\nimport {FormGroup} from '../model/form_group';\nimport {composeAsyncValidators, composeValidators, NG_ASYNC_VALIDATORS, NG_VALIDATORS} from '../validators';\n\nimport {ControlContainer} from './control_container';\nimport {Form} from './form_interface';\nimport {NgControl} from './ng_control';\nimport {NgModel} from './ng_model';\nimport {NgModelGroup} from './ng_model_group';\nimport {CALL_SET_DISABLED_STATE, SetDisabledStateOption, setUpControl, setUpFormContainer, syncPendingControls} from './shared';\nimport {AsyncValidator, AsyncValidatorFn, Validator, ValidatorFn} from './validators';\n\nconst formDirectiveProvider: Provider = {\n  provide: ControlContainer,\n  useExisting: forwardRef(() => NgForm)\n};\n\nconst resolvedPromise = (() => Promise.resolve())();\n\n/**\n * @description\n * Creates a top-level `FormGroup` instance and binds it to a form\n * to track aggregate form value and validation status.\n *\n * As soon as you import the `FormsModule`, this directive becomes active by default on\n * all `<form>` tags.  You don't need to add a special selector.\n *\n * You optionally export the directive into a local template variable using `ngForm` as the key\n * (ex: `#myForm=\"ngForm\"`). This is optional, but useful.  Many properties from the underlying\n * `FormGroup` instance are duplicated on the directive itself, so a reference to it\n * gives you access to the aggregate value and validity status of the form, as well as\n * user interaction properties like `dirty` and `touched`.\n *\n * To register child controls with the form, use `NgModel` with a `name`\n * attribute. You may use `NgModelGroup` to create sub-groups within the form.\n *\n * If necessary, listen to the directive's `ngSubmit` event to be notified when the user has\n * triggered a form submission. The `ngSubmit` event emits the original form\n * submission event.\n *\n * In template driven forms, all `<form>` tags are automatically tagged as `NgForm`.\n * To import the `FormsModule` but skip its usage in some forms,\n * for example, to use native HTML5 validation, add the `ngNoForm` and the `<form>`\n * tags won't create an `NgForm` directive. In reactive forms, using `ngNoForm` is\n * unnecessary because the `<form>` tags are inert. In that case, you would\n * refrain from using the `formGroup` directive.\n *\n * @usageNotes\n *\n * ### Listening for form submission\n *\n * The following example shows how to capture the form values from the \"ngSubmit\" event.\n *\n * {@example forms/ts/simpleForm/simple_form_example.ts region='Component'}\n *\n * ### Setting the update options\n *\n * The following example shows you how to change the \"updateOn\" option from its default using\n * ngFormOptions.\n *\n * ```html\n * <form [ngFormOptions]=\"{updateOn: 'blur'}\">\n *    <input name=\"one\" ngModel>  <!-- this ngModel will update on blur -->\n * </form>\n * ```\n *\n * ### Native DOM validation UI\n *\n * In order to prevent the native DOM form validation UI from interfering with Angular's form\n * validation, Angular automatically adds the `novalidate` attribute on any `<form>` whenever\n * `FormModule` or `ReactiveFormModule` are imported into the application.\n * If you want to explicitly enable native DOM validation UI with Angular forms, you can add the\n * `ngNativeValidate` attribute to the `<form>` element:\n *\n * ```html\n * <form ngNativeValidate>\n *   ...\n * </form>\n * ```\n *\n * @ngModule FormsModule\n * @publicApi\n */\n@Directive({\n  selector: 'form:not([ngNoForm]):not([formGroup]),ng-form,[ngForm]',\n  providers: [formDirectiveProvider],\n  host: {'(submit)': 'onSubmit($event)', '(reset)': 'onReset()'},\n  outputs: ['ngSubmit'],\n  exportAs: 'ngForm'\n})\nexport class NgForm extends ControlContainer implements Form, AfterViewInit {\n  /**\n   * @description\n   * Returns whether the form submission has been triggered.\n   */\n  public readonly submitted: boolean = false;\n\n  private _directives = new Set<NgModel>();\n\n  /**\n   * @description\n   * The `FormGroup` instance created for this form.\n   */\n  form: FormGroup;\n\n  /**\n   * @description\n   * Event emitter for the \"ngSubmit\" event\n   */\n  ngSubmit = new EventEmitter();\n\n  /**\n   * @description\n   * Tracks options for the `NgForm` instance.\n   *\n   * **updateOn**: Sets the default `updateOn` value for all child `NgModels` below it\n   * unless explicitly set by a child `NgModel` using `ngModelOptions`). Defaults to 'change'.\n   * Possible values: `'change'` | `'blur'` | `'submit'`.\n   *\n   */\n  // TODO(issue/24571): remove '!'.\n  @Input('ngFormOptions') options!: {updateOn?: FormHooks};\n\n  constructor(\n      @Optional() @Self() @Inject(NG_VALIDATORS) validators: (Validator|ValidatorFn)[],\n      @Optional() @Self() @Inject(NG_ASYNC_VALIDATORS) asyncValidators:\n          (AsyncValidator|AsyncValidatorFn)[],\n      @Optional() @Inject(CALL_SET_DISABLED_STATE) private callSetDisabledState?:\n          SetDisabledStateOption) {\n    super();\n    this.form =\n        new FormGroup({}, composeValidators(validators), composeAsyncValidators(asyncValidators));\n  }\n\n  /** @nodoc */\n  ngAfterViewInit() {\n    this._setUpdateStrategy();\n  }\n\n  /**\n   * @description\n   * The directive instance.\n   */\n  override get formDirective(): Form {\n    return this;\n  }\n\n  /**\n   * @description\n   * The internal `FormGroup` instance.\n   */\n  override get control(): FormGroup {\n    return this.form;\n  }\n\n  /**\n   * @description\n   * Returns an array representing the path to this group. Because this directive\n   * always lives at the top level of a form, it is always an empty array.\n   */\n  override get path(): string[] {\n    return [];\n  }\n\n  /**\n   * @description\n   * Returns a map of the controls in this group.\n   */\n  get controls(): {[key: string]: AbstractControl} {\n    return this.form.controls;\n  }\n\n  /**\n   * @description\n   * Method that sets up the control directive in this group, re-calculates its value\n   * and validity, and adds the instance to the internal list of directives.\n   *\n   * @param dir The `NgModel` directive instance.\n   */\n  addControl(dir: NgModel): void {\n    resolvedPromise.then(() => {\n      const container = this._findContainer(dir.path);\n      (dir as Writable<NgModel>).control =\n          <FormControl>container.registerControl(dir.name, dir.control);\n      setUpControl(dir.control, dir, this.callSetDisabledState);\n      dir.control.updateValueAndValidity({emitEvent: false});\n      this._directives.add(dir);\n    });\n  }\n\n  /**\n   * @description\n   * Retrieves the `FormControl` instance from the provided `NgModel` directive.\n   *\n   * @param dir The `NgModel` directive instance.\n   */\n  getControl(dir: NgModel): FormControl {\n    return <FormControl>this.form.get(dir.path);\n  }\n\n  /**\n   * @description\n   * Removes the `NgModel` instance from the internal list of directives\n   *\n   * @param dir The `NgModel` directive instance.\n   */\n  removeControl(dir: NgModel): void {\n    resolvedPromise.then(() => {\n      const container = this._findContainer(dir.path);\n      if (container) {\n        container.removeControl(dir.name);\n      }\n      this._directives.delete(dir);\n    });\n  }\n\n  /**\n   * @description\n   * Adds a new `NgModelGroup` directive instance to the form.\n   *\n   * @param dir The `NgModelGroup` directive instance.\n   */\n  addFormGroup(dir: NgModelGroup): void {\n    resolvedPromise.then(() => {\n      const container = this._findContainer(dir.path);\n      const group = new FormGroup({});\n      setUpFormContainer(group, dir);\n      container.registerControl(dir.name, group);\n      group.updateValueAndValidity({emitEvent: false});\n    });\n  }\n\n  /**\n   * @description\n   * Removes the `NgModelGroup` directive instance from the form.\n   *\n   * @param dir The `NgModelGroup` directive instance.\n   */\n  removeFormGroup(dir: NgModelGroup): void {\n    resolvedPromise.then(() => {\n      const container = this._findContainer(dir.path);\n      if (container) {\n        container.removeControl(dir.name);\n      }\n    });\n  }\n\n  /**\n   * @description\n   * Retrieves the `FormGroup` for a provided `NgModelGroup` directive instance\n   *\n   * @param dir The `NgModelGroup` directive instance.\n   */\n  getFormGroup(dir: NgModelGroup): FormGroup {\n    return <FormGroup>this.form.get(dir.path);\n  }\n\n  /**\n   * Sets the new value for the provided `NgControl` directive.\n   *\n   * @param dir The `NgControl` directive instance.\n   * @param value The new value for the directive's control.\n   */\n  updateModel(dir: NgControl, value: any): void {\n    resolvedPromise.then(() => {\n      const ctrl = <FormControl>this.form.get(dir.path!);\n      ctrl.setValue(value);\n    });\n  }\n\n  /**\n   * @description\n   * Sets the value for this `FormGroup`.\n   *\n   * @param value The new value\n   */\n  setValue(value: {[key: string]: any}): void {\n    this.control.setValue(value);\n  }\n\n  /**\n   * @description\n   * Method called when the \"submit\" event is triggered on the form.\n   * Triggers the `ngSubmit` emitter to emit the \"submit\" event as its payload.\n   *\n   * @param $event The \"submit\" event object\n   */\n  onSubmit($event: Event): boolean {\n    (this as Writable<this>).submitted = true;\n    syncPendingControls(this.form, this._directives);\n    this.ngSubmit.emit($event);\n    // Forms with `method=\"dialog\"` have some special behavior\n    // that won't reload the page and that shouldn't be prevented.\n    return ($event?.target as HTMLFormElement | null)?.method === 'dialog';\n  }\n\n  /**\n   * @description\n   * Method called when the \"reset\" event is triggered on the form.\n   */\n  onReset(): void {\n    this.resetForm();\n  }\n\n  /**\n   * @description\n   * Resets the form to an initial value and resets its submitted status.\n   *\n   * @param value The new value for the form.\n   */\n  resetForm(value: any = undefined): void {\n    this.form.reset(value);\n    (this as Writable<this>).submitted = false;\n  }\n\n  private _setUpdateStrategy() {\n    if (this.options && this.options.updateOn != null) {\n      this.form._updateOn = this.options.updateOn;\n    }\n  }\n\n  private _findContainer(path: string[]): FormGroup {\n    path.pop();\n    return path.length ? <FormGroup>this.form.get(path) : this.form;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport function removeListItem<T>(list: T[], el: T): void {\n  const index = list.indexOf(el);\n  if (index > -1) list.splice(index, 1);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵWritable as Writable} from '@angular/core';\n\nimport {AsyncValidatorFn, ValidatorFn} from '../directives/validators';\nimport {removeListItem} from '../util';\n\nimport {AbstractControl, AbstractControlOptions, isOptionsObj, pickAsyncValidators, pickValidators} from './abstract_model';\n\n/**\n * FormControlState is a boxed form value. It is an object with a `value` key and a `disabled` key.\n *\n * @publicApi\n */\nexport interface FormControlState<T> {\n  value: T;\n  disabled: boolean;\n}\n\n/**\n * Interface for options provided to a `FormControl`.\n *\n * This interface extends all options from {@link AbstractControlOptions}, plus some options\n * unique to `FormControl`.\n *\n * @publicApi\n */\nexport interface FormControlOptions extends AbstractControlOptions {\n  /**\n   * @description\n   * Whether to use the initial value used to construct the `FormControl` as its default value\n   * as well. If this option is false or not provided, the default value of a FormControl is `null`.\n   * When a FormControl is reset without an explicit value, its value reverts to\n   * its default value.\n   */\n  nonNullable?: boolean;\n\n  /**\n   * @deprecated Use `nonNullable` instead.\n   */\n  initialValueIsDefault?: boolean;\n}\n\n/**\n * Tracks the value and validation status of an individual form control.\n *\n * This is one of the four fundamental building blocks of Angular forms, along with\n * `FormGroup`, `FormArray` and `FormRecord`. It extends the `AbstractControl` class that\n * implements most of the base functionality for accessing the value, validation status,\n * user interactions and events.\n *\n * `FormControl` takes a single generic argument, which describes the type of its value. This\n * argument always implicitly includes `null` because the control can be reset. To change this\n * behavior, set `nonNullable` or see the usage notes below.\n *\n * See [usage examples below](#usage-notes).\n *\n * @see {@link AbstractControl}\n * @see [Reactive Forms Guide](guide/reactive-forms)\n * @see [Usage Notes](#usage-notes)\n *\n * @publicApi\n *\n * @overriddenImplementation ɵFormControlCtor\n *\n * @usageNotes\n *\n * ### Initializing Form Controls\n *\n * Instantiate a `FormControl`, with an initial value.\n *\n * ```ts\n * const control = new FormControl('some value');\n * console.log(control.value);     // 'some value'\n * ```\n *\n * The following example initializes the control with a form state object. The `value`\n * and `disabled` keys are required in this case.\n *\n * ```ts\n * const control = new FormControl({ value: 'n/a', disabled: true });\n * console.log(control.value);     // 'n/a'\n * console.log(control.status);    // 'DISABLED'\n * ```\n *\n * The following example initializes the control with a synchronous validator.\n *\n * ```ts\n * const control = new FormControl('', Validators.required);\n * console.log(control.value);      // ''\n * console.log(control.status);     // 'INVALID'\n * ```\n *\n * The following example initializes the control using an options object.\n *\n * ```ts\n * const control = new FormControl('', {\n *    validators: Validators.required,\n *    asyncValidators: myAsyncValidator\n * });\n * ```\n *\n * ### The single type argument\n *\n * `FormControl` accepts a generic argument, which describes the type of its value.\n * In most cases, this argument will be inferred.\n *\n * If you are initializing the control to `null`, or you otherwise wish to provide a\n * wider type, you may specify the argument explicitly:\n *\n * ```\n * let fc = new FormControl<string|null>(null);\n * fc.setValue('foo');\n * ```\n *\n * You might notice that `null` is always added to the type of the control.\n * This is because the control will become `null` if you call `reset`. You can change\n * this behavior by setting `{nonNullable: true}`.\n *\n * ### Configure the control to update on a blur event\n *\n * Set the `updateOn` option to `'blur'` to update on the blur `event`.\n *\n * ```ts\n * const control = new FormControl('', { updateOn: 'blur' });\n * ```\n *\n * ### Configure the control to update on a submit event\n *\n * Set the `updateOn` option to `'submit'` to update on a submit `event`.\n *\n * ```ts\n * const control = new FormControl('', { updateOn: 'submit' });\n * ```\n *\n * ### Reset the control back to a specific value\n *\n * You reset to a specific form state by passing through a standalone\n * value or a form state object that contains both a value and a disabled state\n * (these are the only two properties that cannot be calculated).\n *\n * ```ts\n * const control = new FormControl('Nancy');\n *\n * console.log(control.value); // 'Nancy'\n *\n * control.reset('Drew');\n *\n * console.log(control.value); // 'Drew'\n * ```\n *\n * ### Reset the control to its initial value\n *\n * If you wish to always reset the control to its initial value (instead of null),\n * you can pass the `nonNullable` option:\n *\n * ```\n * const control = new FormControl('Nancy', {nonNullable: true});\n *\n * console.log(control.value); // 'Nancy'\n *\n * control.reset();\n *\n * console.log(control.value); // 'Nancy'\n * ```\n *\n * ### Reset the control back to an initial value and disabled\n *\n * ```\n * const control = new FormControl('Nancy');\n *\n * console.log(control.value); // 'Nancy'\n * console.log(control.status); // 'VALID'\n *\n * control.reset({ value: 'Drew', disabled: true });\n *\n * console.log(control.value); // 'Drew'\n * console.log(control.status); // 'DISABLED'\n * ```\n */\nexport interface FormControl<TValue = any> extends AbstractControl<TValue> {\n  /**\n   * The default value of this FormControl, used whenever the control is reset without an explicit\n   * value. See {@link FormControlOptions#nonNullable} for more information on configuring\n   * a default value.\n   */\n  readonly defaultValue: TValue;\n\n  /** @internal */\n  _onChange: Function[];\n\n  /**\n   * This field holds a pending value that has not yet been applied to the form's value.\n   * @internal\n   */\n  _pendingValue: TValue;\n\n  /** @internal */\n  _pendingChange: boolean;\n\n  /**\n   * Sets a new value for the form control.\n   *\n   * @param value The new value for the control.\n   * @param options Configuration options that determine how the control propagates changes\n   * and emits events when the value changes.\n   * The configuration options are passed to the {@link AbstractControl#updateValueAndValidity\n   * updateValueAndValidity} method.\n   *\n   * * `onlySelf`: When true, each change only affects this control, and not its parent. Default is\n   * false.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges`\n   * observables emit events with the latest status and value when the control value is updated.\n   * When false, no events are emitted.\n   * * `emitModelToViewChange`: When true or not supplied  (the default), each change triggers an\n   * `onChange` event to\n   * update the view.\n   * * `emitViewToModelChange`: When true or not supplied (the default), each change triggers an\n   * `ngModelChange`\n   * event to update the model.\n   *\n   */\n  setValue(value: TValue, options?: {\n    onlySelf?: boolean,\n    emitEvent?: boolean,\n    emitModelToViewChange?: boolean,\n    emitViewToModelChange?: boolean\n  }): void;\n\n  /**\n   * Patches the value of a control.\n   *\n   * This function is functionally the same as {@link FormControl#setValue setValue} at this level.\n   * It exists for symmetry with {@link FormGroup#patchValue patchValue} on `FormGroups` and\n   * `FormArrays`, where it does behave differently.\n   *\n   * @see {@link FormControl#setValue} for options\n   */\n  patchValue(value: TValue, options?: {\n    onlySelf?: boolean,\n    emitEvent?: boolean,\n    emitModelToViewChange?: boolean,\n    emitViewToModelChange?: boolean\n  }): void;\n\n  /**\n   * Resets the form control, marking it `pristine` and `untouched`, and resetting\n   * the value. The new value will be the provided value (if passed), `null`, or the initial value\n   * if `nonNullable` was set in the constructor via {@link FormControlOptions}.\n   *\n   * ```ts\n   * // By default, the control will reset to null.\n   * const dog = new FormControl('spot');\n   * dog.reset(); // dog.value is null\n   *\n   * // If this flag is set, the control will instead reset to the initial value.\n   * const cat = new FormControl('tabby', {nonNullable: true});\n   * cat.reset(); // cat.value is \"tabby\"\n   *\n   * // A value passed to reset always takes precedence.\n   * const fish = new FormControl('finn', {nonNullable: true});\n   * fish.reset('bubble'); // fish.value is \"bubble\"\n   * ```\n   *\n   * @param formState Resets the control with an initial value,\n   * or an object that defines the initial value and disabled state.\n   *\n   * @param options Configuration options that determine how the control propagates changes\n   * and emits events after the value changes.\n   *\n   * * `onlySelf`: When true, each change only affects this control, and not its parent. Default is\n   * false.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges`\n   * observables emit events with the latest status and value when the control is reset.\n   * When false, no events are emitted.\n   *\n   */\n  reset(formState?: TValue|FormControlState<TValue>, options?: {\n    onlySelf?: boolean,\n    emitEvent?: boolean\n  }): void;\n\n  /**\n   * For a simple FormControl, the raw value is equivalent to the value.\n   */\n  getRawValue(): TValue;\n\n  /**\n   * @internal\n   */\n  _updateValue(): void;\n\n  /**\n   * @internal\n   */\n  _anyControls(condition: (c: AbstractControl) => boolean): boolean;\n\n  /**\n   * @internal\n   */\n  _allControlsDisabled(): boolean;\n\n\n  /**\n   * Register a listener for change events.\n   *\n   * @param fn The method that is called when the value changes\n   */\n  registerOnChange(fn: Function): void;\n\n\n  /**\n   * Internal function to unregister a change events listener.\n   * @internal\n   */\n  _unregisterOnChange(fn: (value?: any, emitModelEvent?: boolean) => void): void;\n\n  /**\n   * Register a listener for disabled events.\n   *\n   * @param fn The method that is called when the disabled status changes.\n   */\n  registerOnDisabledChange(fn: (isDisabled: boolean) => void): void;\n\n  /**\n   * Internal function to unregister a disabled event listener.\n   * @internal\n   */\n  _unregisterOnDisabledChange(fn: (isDisabled: boolean) => void): void;\n\n  /**\n   * @internal\n   */\n  _forEachChild(cb: (c: AbstractControl) => void): void;\n\n  /** @internal */\n  _syncPendingControls(): boolean;\n}\n\n// This internal interface is present to avoid a naming clash, resulting in the wrong `FormControl`\n// symbol being used.\ntype FormControlInterface<TValue = any> = FormControl<TValue>;\n\n/**\n * Various available constructors for `FormControl`.\n * Do not use this interface directly. Instead, use `FormControl`:\n * ```\n * const fc = new FormControl('foo');\n * ```\n * This symbol is prefixed with ɵ to make plain that it is an internal symbol.\n */\nexport interface ɵFormControlCtor {\n  /**\n   * Construct a FormControl with no initial value or validators.\n   */\n  new(): FormControl<any>;\n\n  /**\n   * Creates a new `FormControl` instance.\n   *\n   * @param formState Initializes the control with an initial value,\n   * or an object that defines the initial value and disabled state.\n   *\n   * @param validatorOrOpts A synchronous validator function, or an array of\n   * such functions, or a `FormControlOptions` object that contains validation functions\n   * and a validation trigger.\n   *\n   * @param asyncValidator A single async validator or array of async validator functions\n   */\n  new<T = any>(value: FormControlState<T>|T, opts: FormControlOptions&{nonNullable: true}):\n      FormControl<T>;\n\n  /**\n   * @deprecated Use `nonNullable` instead.\n   */\n  new<T = any>(value: FormControlState<T>|T, opts: FormControlOptions&{\n    initialValueIsDefault: true\n  }): FormControl<T>;\n\n  /**\n   * @deprecated When passing an `options` argument, the `asyncValidator` argument has no effect.\n   */\n  new<T = any>(\n      value: FormControlState<T>|T, opts: FormControlOptions,\n      asyncValidator: AsyncValidatorFn|AsyncValidatorFn[]): FormControl<T|null>;\n\n  new<T = any>(\n      value: FormControlState<T>|T,\n      validatorOrOpts?: ValidatorFn|ValidatorFn[]|FormControlOptions|null,\n      asyncValidator?: AsyncValidatorFn|AsyncValidatorFn[]|null): FormControl<T|null>;\n\n  /**\n   * The presence of an explicit `prototype` property provides backwards-compatibility for apps that\n   * manually inspect the prototype chain.\n   */\n  prototype: FormControl<any>;\n}\n\nfunction isFormControlState(formState: unknown): formState is FormControlState<unknown> {\n  return typeof formState === 'object' && formState !== null &&\n      Object.keys(formState).length === 2 && 'value' in formState && 'disabled' in formState;\n}\n\nexport const FormControl: ɵFormControlCtor =\n    (class FormControl<TValue = any> extends AbstractControl<\n         TValue> implements FormControlInterface<TValue> {\n      /** @publicApi */\n      public readonly defaultValue: TValue = null as unknown as TValue;\n\n      /** @internal */\n      _onChange: Array<Function> = [];\n\n      /** @internal */\n      _pendingValue!: TValue;\n\n      /** @internal */\n      _pendingChange: boolean = false;\n\n      constructor(\n          // formState and defaultValue will only be null if T is nullable\n          formState: FormControlState<TValue>|TValue = null as unknown as TValue,\n          validatorOrOpts?: ValidatorFn|ValidatorFn[]|FormControlOptions|null,\n          asyncValidator?: AsyncValidatorFn|AsyncValidatorFn[]|null) {\n        super(\n            pickValidators(validatorOrOpts), pickAsyncValidators(asyncValidator, validatorOrOpts));\n        this._applyFormState(formState);\n        this._setUpdateStrategy(validatorOrOpts);\n        this._initObservables();\n        this.updateValueAndValidity({\n          onlySelf: true,\n          // If `asyncValidator` is present, it will trigger control status change from `PENDING` to\n          // `VALID` or `INVALID`.\n          // The status should be broadcasted via the `statusChanges` observable, so we set\n          // `emitEvent` to `true` to allow that during the control creation process.\n          emitEvent: !!this.asyncValidator\n        });\n        if (isOptionsObj(validatorOrOpts) &&\n            (validatorOrOpts.nonNullable || validatorOrOpts.initialValueIsDefault)) {\n          if (isFormControlState(formState)) {\n            this.defaultValue = formState.value;\n          } else {\n            this.defaultValue = formState;\n          }\n        }\n      }\n\n      override setValue(value: TValue, options: {\n        onlySelf?: boolean,\n        emitEvent?: boolean,\n        emitModelToViewChange?: boolean,\n        emitViewToModelChange?: boolean\n      } = {}): void {\n        (this as Writable<this>).value = this._pendingValue = value;\n        if (this._onChange.length && options.emitModelToViewChange !== false) {\n          this._onChange.forEach(\n              (changeFn) => changeFn(this.value, options.emitViewToModelChange !== false));\n        }\n        this.updateValueAndValidity(options);\n      }\n\n      override patchValue(value: TValue, options: {\n        onlySelf?: boolean,\n        emitEvent?: boolean,\n        emitModelToViewChange?: boolean,\n        emitViewToModelChange?: boolean\n      } = {}): void {\n        this.setValue(value, options);\n      }\n\n      override reset(\n          formState: TValue|FormControlState<TValue> = this.defaultValue,\n          options: {onlySelf?: boolean, emitEvent?: boolean} = {}): void {\n        this._applyFormState(formState);\n        this.markAsPristine(options);\n        this.markAsUntouched(options);\n        this.setValue(this.value, options);\n        this._pendingChange = false;\n      }\n\n      /**  @internal */\n      override _updateValue(): void {}\n\n      /**  @internal */\n      override _anyControls(condition: (c: AbstractControl) => boolean): boolean {\n        return false;\n      }\n\n      /**  @internal */\n      override _allControlsDisabled(): boolean {\n        return this.disabled;\n      }\n\n      registerOnChange(fn: Function): void {\n        this._onChange.push(fn);\n      }\n\n      /** @internal */\n      _unregisterOnChange(fn: (value?: any, emitModelEvent?: boolean) => void): void {\n        removeListItem(this._onChange, fn);\n      }\n\n      registerOnDisabledChange(fn: (isDisabled: boolean) => void): void {\n        this._onDisabledChange.push(fn);\n      }\n\n      /** @internal */\n      _unregisterOnDisabledChange(fn: (isDisabled: boolean) => void): void {\n        removeListItem(this._onDisabledChange, fn);\n      }\n\n      /** @internal */\n      override _forEachChild(cb: (c: AbstractControl) => void): void {}\n\n      /** @internal */\n      override _syncPendingControls(): boolean {\n        if (this.updateOn === 'submit') {\n          if (this._pendingDirty) this.markAsDirty();\n          if (this._pendingTouched) this.markAsTouched();\n          if (this._pendingChange) {\n            this.setValue(this._pendingValue, {onlySelf: true, emitModelToViewChange: false});\n            return true;\n          }\n        }\n        return false;\n      }\n\n      private _applyFormState(formState: FormControlState<TValue>|TValue) {\n        if (isFormControlState(formState)) {\n          (this as Writable<this>).value = this._pendingValue = formState.value;\n          formState.disabled ? this.disable({onlySelf: true, emitEvent: false}) :\n                               this.enable({onlySelf: true, emitEvent: false});\n        } else {\n          (this as Writable<this>).value = this._pendingValue = formState;\n        }\n      }\n    });\n\ninterface UntypedFormControlCtor {\n  new(): UntypedFormControl;\n\n  new(formState?: any, validatorOrOpts?: ValidatorFn|ValidatorFn[]|FormControlOptions|null,\n      asyncValidator?: AsyncValidatorFn|AsyncValidatorFn[]|null): UntypedFormControl;\n\n  /**\n   * The presence of an explicit `prototype` property provides backwards-compatibility for apps that\n   * manually inspect the prototype chain.\n   */\n  prototype: FormControl<any>;\n}\n\n/**\n * UntypedFormControl is a non-strongly-typed version of `FormControl`.\n */\nexport type UntypedFormControl = FormControl<any>;\n\nexport const UntypedFormControl: UntypedFormControlCtor = FormControl;\n\n/**\n * @description\n * Asserts that the given control is an instance of `FormControl`\n *\n * @publicApi\n */\nexport const isFormControl = (control: unknown): control is FormControl =>\n    control instanceof FormControl;\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, OnDestroy, OnInit} from '@angular/core';\n\nimport {FormGroup} from '../model/form_group';\n\nimport {ControlContainer} from './control_container';\nimport {Form} from './form_interface';\nimport {controlPath} from './shared';\n\n\n\n/**\n * @description\n * A base class for code shared between the `NgModelGroup` and `FormGroupName` directives.\n *\n * @publicApi\n */\n@Directive()\nexport class AbstractFormGroupDirective extends ControlContainer implements OnInit, OnDestroy {\n  /**\n   * @description\n   * The parent control for the group\n   *\n   * @internal\n   */\n  // TODO(issue/24571): remove '!'.\n  _parent!: ControlContainer;\n\n  /** @nodoc */\n  ngOnInit(): void {\n    this._checkParentType();\n    // Register the group with its parent group.\n    this.formDirective!.addFormGroup(this);\n  }\n\n  /** @nodoc */\n  ngOnDestroy(): void {\n    if (this.formDirective) {\n      // Remove the group from its parent group.\n      this.formDirective.removeFormGroup(this);\n    }\n  }\n\n  /**\n   * @description\n   * The `FormGroup` bound to this directive.\n   */\n  override get control(): FormGroup {\n    return this.formDirective!.getFormGroup(this);\n  }\n\n  /**\n   * @description\n   * The path to this group from the top-level directive.\n   */\n  override get path(): string[] {\n    return controlPath(this.name == null ? this.name : this.name.toString(), this._parent);\n  }\n\n  /**\n   * @description\n   * The top-level directive for this group if present, otherwise null.\n   */\n  override get formDirective(): Form|null {\n    return this._parent ? this._parent.formDirective : null;\n  }\n\n  /** @internal */\n  _checkParentType(): void {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵRuntimeError as RuntimeError} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../errors';\n\nimport {formControlNameExample, formGroupNameExample, ngModelGroupExample, ngModelWithFormGroupExample} from './error_examples';\n\n\nexport function modelParentException(): Error {\n  return new RuntimeError(RuntimeErrorCode.NGMODEL_IN_FORM_GROUP, `\n    ngModel cannot be used to register form controls with a parent formGroup directive.  Try using\n    formGroup's partner directive \"formControlName\" instead.  Example:\n\n    ${formControlNameExample}\n\n    Or, if you'd like to avoid registering this form control, indicate that it's standalone in ngModelOptions:\n\n    Example:\n\n    ${ngModelWithFormGroupExample}`);\n}\n\nexport function formGroupNameException(): Error {\n  return new RuntimeError(RuntimeErrorCode.NGMODEL_IN_FORM_GROUP_NAME, `\n    ngModel cannot be used to register form controls with a parent formGroupName or formArrayName directive.\n\n    Option 1: Use formControlName instead of ngModel (reactive strategy):\n\n    ${formGroupNameExample}\n\n    Option 2:  Update ngModel's parent be ngModelGroup (template-driven strategy):\n\n    ${ngModelGroupExample}`);\n}\n\nexport function missingNameException(): Error {\n  return new RuntimeError(\n      RuntimeErrorCode.NGMODEL_WITHOUT_NAME,\n      `If ngModel is used within a form tag, either the name attribute must be set or the form\n    control must be defined as 'standalone' in ngModelOptions.\n\n    Example 1: <input [(ngModel)]=\"person.firstName\" name=\"first\">\n    Example 2: <input [(ngModel)]=\"person.firstName\" [ngModelOptions]=\"{standalone: true}\">`);\n}\n\nexport function modelGroupParentException(): Error {\n  return new RuntimeError(RuntimeErrorCode.NGMODELGROUP_IN_FORM_GROUP, `\n    ngModelGroup cannot be used with a parent formGroup directive.\n\n    Option 1: Use formGroupName instead of ngModelGroup (reactive strategy):\n\n    ${formGroupNameExample}\n\n    Option 2:  Use a regular form tag instead of the formGroup directive (template-driven strategy):\n\n    ${ngModelGroupExample}`);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, forwardRef, Host, Inject, Input, OnD<PERSON>roy, OnInit, Optional, Self, SkipSelf} from '@angular/core';\n\nimport {NG_ASYNC_VALIDATORS, NG_VALIDATORS} from '../validators';\n\nimport {AbstractFormGroupDirective} from './abstract_form_group_directive';\nimport {ControlContainer} from './control_container';\nimport {NgForm} from './ng_form';\nimport {modelGroupParentException} from './template_driven_errors';\nimport {AsyncValidator, AsyncValidatorFn, Validator, ValidatorFn} from './validators';\n\nexport const modelGroupProvider: any = {\n  provide: ControlContainer,\n  useExisting: forwardRef(() => NgModelGroup)\n};\n\n/**\n * @description\n * Creates and binds a `FormGroup` instance to a DOM element.\n *\n * This directive can only be used as a child of `NgForm` (within `<form>` tags).\n *\n * Use this directive to validate a sub-group of your form separately from the\n * rest of your form, or if some values in your domain model make more sense\n * to consume together in a nested object.\n *\n * Provide a name for the sub-group and it will become the key\n * for the sub-group in the form's full value. If you need direct access, export the directive into\n * a local template variable using `ngModelGroup` (ex: `#myGroup=\"ngModelGroup\"`).\n *\n * @usageNotes\n *\n * ### Consuming controls in a grouping\n *\n * The following example shows you how to combine controls together in a sub-group\n * of the form.\n *\n * {@example forms/ts/ngModelGroup/ng_model_group_example.ts region='Component'}\n *\n * @ngModule FormsModule\n * @publicApi\n */\n@Directive({selector: '[ngModelGroup]', providers: [modelGroupProvider], exportAs: 'ngModelGroup'})\nexport class NgModelGroup extends AbstractFormGroupDirective implements OnInit, OnDestroy {\n  /**\n   * @description\n   * Tracks the name of the `NgModelGroup` bound to the directive. The name corresponds\n   * to a key in the parent `NgForm`.\n   */\n  @Input('ngModelGroup') override name: string = '';\n\n  constructor(\n      @Host() @SkipSelf() parent: ControlContainer,\n      @Optional() @Self() @Inject(NG_VALIDATORS) validators: (Validator|ValidatorFn)[],\n      @Optional() @Self() @Inject(NG_ASYNC_VALIDATORS) asyncValidators:\n          (AsyncValidator|AsyncValidatorFn)[]) {\n    super();\n    this._parent = parent;\n    this._setValidators(validators);\n    this._setAsyncValidators(asyncValidators);\n  }\n\n  /** @internal */\n  override _checkParentType(): void {\n    if (!(this._parent instanceof NgModelGroup) && !(this._parent instanceof NgForm) &&\n        (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw modelGroupParentException();\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {booleanAttribute, ChangeDetectorRef, Directive, EventEmitter, forwardRef, Host, Inject, Input, OnChanges, OnDestroy, Optional, Output, Provider, Self, SimpleChanges} from '@angular/core';\n\nimport {FormHooks} from '../model/abstract_model';\nimport {FormControl} from '../model/form_control';\nimport {NG_ASYNC_VALIDATORS, NG_VALIDATORS} from '../validators';\n\nimport {AbstractFormGroupDirective} from './abstract_form_group_directive';\nimport {ControlContainer} from './control_container';\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from './control_value_accessor';\nimport {NgControl} from './ng_control';\nimport {NgForm} from './ng_form';\nimport {NgModelGroup} from './ng_model_group';\nimport {CALL_SET_DISABLED_STATE, controlPath, isPropertyUpdated, selectValueAccessor, SetDisabledStateOption, setUpControl} from './shared';\nimport {formGroupNameException, missingNameException, modelParentException} from './template_driven_errors';\nimport {AsyncValidator, AsyncValidatorFn, Validator, ValidatorFn} from './validators';\n\nconst formControlBinding: Provider = {\n  provide: NgControl,\n  useExisting: forwardRef(() => NgModel)\n};\n\n/**\n * `ngModel` forces an additional change detection run when its inputs change:\n * E.g.:\n * ```\n * <div>{{myModel.valid}}</div>\n * <input [(ngModel)]=\"myValue\" #myModel=\"ngModel\">\n * ```\n * I.e. `ngModel` can export itself on the element and then be used in the template.\n * Normally, this would result in expressions before the `input` that use the exported directive\n * to have an old value as they have been\n * dirty checked before. As this is a very common case for `ngModel`, we added this second change\n * detection run.\n *\n * Notes:\n * - this is just one extra run no matter how many `ngModel`s have been changed.\n * - this is a general problem when using `exportAs` for directives!\n */\nconst resolvedPromise = (() => Promise.resolve())();\n\n/**\n * @description\n * Creates a `FormControl` instance from a [domain\n * model](https://en.wikipedia.org/wiki/Domain_model) and binds it to a form control element.\n *\n * The `FormControl` instance tracks the value, user interaction, and\n * validation status of the control and keeps the view synced with the model. If used\n * within a parent form, the directive also registers itself with the form as a child\n * control.\n *\n * This directive is used by itself or as part of a larger form. Use the\n * `ngModel` selector to activate it.\n *\n * It accepts a domain model as an optional `Input`. If you have a one-way binding\n * to `ngModel` with `[]` syntax, changing the domain model's value in the component\n * class sets the value in the view. If you have a two-way binding with `[()]` syntax\n * (also known as 'banana-in-a-box syntax'), the value in the UI always syncs back to\n * the domain model in your class.\n *\n * To inspect the properties of the associated `FormControl` (like the validity state),\n * export the directive into a local template variable using `ngModel` as the key (ex:\n * `#myVar=\"ngModel\"`). You can then access the control using the directive's `control` property.\n * However, the most commonly used properties (like `valid` and `dirty`) also exist on the control\n * for direct access. See a full list of properties directly available in\n * `AbstractControlDirective`.\n *\n * @see {@link RadioControlValueAccessor}\n * @see {@link SelectControlValueAccessor}\n *\n * @usageNotes\n *\n * ### Using ngModel on a standalone control\n *\n * The following examples show a simple standalone control using `ngModel`:\n *\n * {@example forms/ts/simpleNgModel/simple_ng_model_example.ts region='Component'}\n *\n * When using the `ngModel` within `<form>` tags, you'll also need to supply a `name` attribute\n * so that the control can be registered with the parent form under that name.\n *\n * In the context of a parent form, it's often unnecessary to include one-way or two-way binding,\n * as the parent form syncs the value for you. You access its properties by exporting it into a\n * local template variable using `ngForm` such as (`#f=\"ngForm\"`). Use the variable where\n * needed on form submission.\n *\n * If you do need to populate initial values into your form, using a one-way binding for\n * `ngModel` tends to be sufficient as long as you use the exported form's value rather\n * than the domain model's value on submit.\n *\n * ### Using ngModel within a form\n *\n * The following example shows controls using `ngModel` within a form:\n *\n * {@example forms/ts/simpleForm/simple_form_example.ts region='Component'}\n *\n * ### Using a standalone ngModel within a group\n *\n * The following example shows you how to use a standalone ngModel control\n * within a form. This controls the display of the form, but doesn't contain form data.\n *\n * ```html\n * <form>\n *   <input name=\"login\" ngModel placeholder=\"Login\">\n *   <input type=\"checkbox\" ngModel [ngModelOptions]=\"{standalone: true}\"> Show more options?\n * </form>\n * <!-- form value: {login: ''} -->\n * ```\n *\n * ### Setting the ngModel `name` attribute through options\n *\n * The following example shows you an alternate way to set the name attribute. Here,\n * an attribute identified as name is used within a custom form control component. To still be able\n * to specify the NgModel's name, you must specify it using the `ngModelOptions` input instead.\n *\n * ```html\n * <form>\n *   <my-custom-form-control name=\"Nancy\" ngModel [ngModelOptions]=\"{name: 'user'}\">\n *   </my-custom-form-control>\n * </form>\n * <!-- form value: {user: ''} -->\n * ```\n *\n * @ngModule FormsModule\n * @publicApi\n */\n@Directive({\n  selector: '[ngModel]:not([formControlName]):not([formControl])',\n  providers: [formControlBinding],\n  exportAs: 'ngModel'\n})\nexport class NgModel extends NgControl implements OnChanges, OnDestroy {\n  public override readonly control: FormControl = new FormControl();\n\n  // At runtime we coerce arbitrary values assigned to the \"disabled\" input to a \"boolean\".\n  // This is not reflected in the type of the property because outside of templates, consumers\n  // should only deal with booleans. In templates, a string is allowed for convenience and to\n  // match the native \"disabled attribute\" semantics which can be observed on input elements.\n  // This static member tells the compiler that values of type \"string\" can also be assigned\n  // to the input in a template.\n  /** @nodoc */\n  static ngAcceptInputType_isDisabled: boolean|string;\n\n  /** @internal */\n  _registered = false;\n\n  /**\n   * Internal reference to the view model value.\n   * @nodoc\n   */\n  viewModel: any;\n\n  /**\n   * @description\n   * Tracks the name bound to the directive. If a parent form exists, it\n   * uses this name as a key to retrieve this control's value.\n   */\n  @Input() override name: string = '';\n\n  /**\n   * @description\n   * Tracks whether the control is disabled.\n   */\n  // TODO(issue/24571): remove '!'.\n  @Input('disabled') isDisabled!: boolean;\n\n  /**\n   * @description\n   * Tracks the value bound to this directive.\n   */\n  @Input('ngModel') model: any;\n\n  /**\n   * @description\n   * Tracks the configuration options for this `ngModel` instance.\n   *\n   * **name**: An alternative to setting the name attribute on the form control element. See\n   * the [example](api/forms/NgModel#using-ngmodel-on-a-standalone-control) for using `NgModel`\n   * as a standalone control.\n   *\n   * **standalone**: When set to true, the `ngModel` will not register itself with its parent form,\n   * and acts as if it's not in the form. Defaults to false. If no parent form exists, this option\n   * has no effect.\n   *\n   * **updateOn**: Defines the event upon which the form control value and validity update.\n   * Defaults to 'change'. Possible values: `'change'` | `'blur'` | `'submit'`.\n   *\n   */\n  // TODO(issue/24571): remove '!'.\n  @Input('ngModelOptions') options!: {name?: string, standalone?: boolean, updateOn?: FormHooks};\n\n  /**\n   * @description\n   * Event emitter for producing the `ngModelChange` event after\n   * the view model updates.\n   */\n  @Output('ngModelChange') update = new EventEmitter();\n\n  constructor(\n      @Optional() @Host() parent: ControlContainer,\n      @Optional() @Self() @Inject(NG_VALIDATORS) validators: (Validator|ValidatorFn)[],\n      @Optional() @Self() @Inject(NG_ASYNC_VALIDATORS) asyncValidators:\n          (AsyncValidator|AsyncValidatorFn)[],\n      @Optional() @Self() @Inject(NG_VALUE_ACCESSOR) valueAccessors: ControlValueAccessor[],\n      @Optional() @Inject(ChangeDetectorRef) private _changeDetectorRef?: ChangeDetectorRef|null,\n      @Optional() @Inject(CALL_SET_DISABLED_STATE) private callSetDisabledState?:\n          SetDisabledStateOption) {\n    super();\n    this._parent = parent;\n    this._setValidators(validators);\n    this._setAsyncValidators(asyncValidators);\n    this.valueAccessor = selectValueAccessor(this, valueAccessors);\n  }\n\n  /** @nodoc */\n  ngOnChanges(changes: SimpleChanges) {\n    this._checkForErrors();\n    if (!this._registered || 'name' in changes) {\n      if (this._registered) {\n        this._checkName();\n        if (this.formDirective) {\n          // We can't call `formDirective.removeControl(this)`, because the `name` has already been\n          // changed. We also can't reset the name temporarily since the logic in `removeControl`\n          // is inside a promise and it won't run immediately. We work around it by giving it an\n          // object with the same shape instead.\n          const oldName = changes['name'].previousValue;\n          this.formDirective.removeControl({name: oldName, path: this._getPath(oldName)});\n        }\n      }\n      this._setUpControl();\n    }\n    if ('isDisabled' in changes) {\n      this._updateDisabled(changes);\n    }\n\n    if (isPropertyUpdated(changes, this.viewModel)) {\n      this._updateValue(this.model);\n      this.viewModel = this.model;\n    }\n  }\n\n  /** @nodoc */\n  ngOnDestroy(): void {\n    this.formDirective && this.formDirective.removeControl(this);\n  }\n\n  /**\n   * @description\n   * Returns an array that represents the path from the top-level form to this control.\n   * Each index is the string name of the control on that level.\n   */\n  override get path(): string[] {\n    return this._getPath(this.name);\n  }\n\n  /**\n   * @description\n   * The top-level directive for this control if present, otherwise null.\n   */\n  get formDirective(): any {\n    return this._parent ? this._parent.formDirective : null;\n  }\n\n  /**\n   * @description\n   * Sets the new value for the view model and emits an `ngModelChange` event.\n   *\n   * @param newValue The new value emitted by `ngModelChange`.\n   */\n  override viewToModelUpdate(newValue: any): void {\n    this.viewModel = newValue;\n    this.update.emit(newValue);\n  }\n\n  private _setUpControl(): void {\n    this._setUpdateStrategy();\n    this._isStandalone() ? this._setUpStandalone() : this.formDirective.addControl(this);\n    this._registered = true;\n  }\n\n  private _setUpdateStrategy(): void {\n    if (this.options && this.options.updateOn != null) {\n      this.control._updateOn = this.options.updateOn;\n    }\n  }\n\n  private _isStandalone(): boolean {\n    return !this._parent || !!(this.options && this.options.standalone);\n  }\n\n  private _setUpStandalone(): void {\n    setUpControl(this.control, this, this.callSetDisabledState);\n    this.control.updateValueAndValidity({emitEvent: false});\n  }\n\n  private _checkForErrors(): void {\n    if (!this._isStandalone()) {\n      this._checkParentType();\n    }\n    this._checkName();\n  }\n\n  private _checkParentType(): void {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!(this._parent instanceof NgModelGroup) &&\n          this._parent instanceof AbstractFormGroupDirective) {\n        throw formGroupNameException();\n      } else if (!(this._parent instanceof NgModelGroup) && !(this._parent instanceof NgForm)) {\n        throw modelParentException();\n      }\n    }\n  }\n\n  private _checkName(): void {\n    if (this.options && this.options.name) this.name = this.options.name;\n\n    if (!this._isStandalone() && !this.name && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw missingNameException();\n    }\n  }\n\n  private _updateValue(value: any): void {\n    resolvedPromise.then(() => {\n      this.control.setValue(value, {emitViewToModelChange: false});\n      this._changeDetectorRef?.markForCheck();\n    });\n  }\n\n  private _updateDisabled(changes: SimpleChanges) {\n    const disabledValue = changes['isDisabled'].currentValue;\n    // checking for 0 to avoid breaking change\n    const isDisabled = disabledValue !== 0 && booleanAttribute(disabledValue);\n\n    resolvedPromise.then(() => {\n      if (isDisabled && !this.control.disabled) {\n        this.control.disable();\n      } else if (!isDisabled && this.control.disabled) {\n        this.control.enable();\n      }\n\n      this._changeDetectorRef?.markForCheck();\n    });\n  }\n\n  private _getPath(controlName: string): string[] {\n    return this._parent ? controlPath(controlName, this._parent) : [controlName];\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive} from '@angular/core';\n\n/**\n * @description\n *\n * Adds `novalidate` attribute to all forms by default.\n *\n * `novalidate` is used to disable browser's native form validation.\n *\n * If you want to use native validation with Angular forms, just add `ngNativeValidate` attribute:\n *\n * ```\n * <form ngNativeValidate></form>\n * ```\n *\n * @publicApi\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n */\n@Directive({\n  selector: 'form:not([ngNoForm]):not([ngNativeValidate])',\n  host: {'novalidate': ''},\n})\nexport class ɵNgNoValidate {\n}\n\nexport {ɵNgNoValidate as NgNoValidate};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, ElementRef, forwardRef, Provider} from '@angular/core';\n\nimport {BuiltInControlValueAccessor, ControlValueAccessor, NG_VALUE_ACCESSOR} from './control_value_accessor';\n\nconst NUMBER_VALUE_ACCESSOR: Provider = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => NumberValueAccessor),\n  multi: true\n};\n\n/**\n * @description\n * The `ControlValueAccessor` for writing a number value and listening to number input changes.\n * The value accessor is used by the `FormControlDirective`, `FormControlName`, and `NgModel`\n * directives.\n *\n * @usageNotes\n *\n * ### Using a number input with a reactive form.\n *\n * The following example shows how to use a number input with a reactive form.\n *\n * ```ts\n * const totalCountControl = new FormControl();\n * ```\n *\n * ```\n * <input type=\"number\" [formControl]=\"totalCountControl\">\n * ```\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n@Directive({\n  selector:\n      'input[type=number][formControlName],input[type=number][formControl],input[type=number][ngModel]',\n  host: {'(input)': 'onChange($event.target.value)', '(blur)': 'onTouched()'},\n  providers: [NUMBER_VALUE_ACCESSOR]\n})\nexport class NumberValueAccessor extends BuiltInControlValueAccessor implements\n    ControlValueAccessor {\n  /**\n   * Sets the \"value\" property on the input element.\n   * @nodoc\n   */\n  writeValue(value: number): void {\n    // The value needs to be normalized for IE9, otherwise it is set to 'null' when null\n    const normalizedValue = value == null ? '' : value;\n    this.setProperty('value', normalizedValue);\n  }\n\n  /**\n   * Registers a function called when the control value changes.\n   * @nodoc\n   */\n  override registerOnChange(fn: (_: number|null) => void): void {\n    this.onChange = (value) => {\n      fn(value == '' ? null : parseFloat(value));\n    };\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, ElementRef, forwardRef, inject, Injectable, Injector, Input, On<PERSON><PERSON>roy, OnInit, Provider, Renderer2, ɵRuntimeError as RuntimeError} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../errors';\n\nimport {BuiltInControlValueAccessor, ControlValueAccessor, NG_VALUE_ACCESSOR} from './control_value_accessor';\nimport {NgControl} from './ng_control';\nimport {CALL_SET_DISABLED_STATE, setDisabledStateDefault} from './shared';\n\nconst RADIO_VALUE_ACCESSOR: Provider = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => RadioControlValueAccessor),\n  multi: true\n};\n\nfunction throwNameError() {\n  throw new RuntimeError(RuntimeErrorCode.NAME_AND_FORM_CONTROL_NAME_MUST_MATCH, `\n      If you define both a name and a formControlName attribute on your radio button, their values\n      must match. Ex: <input type=\"radio\" formControlName=\"food\" name=\"food\">\n    `);\n}\n\n/**\n * @description\n * Class used by Angular to track radio buttons. For internal use only.\n */\n@Injectable({providedIn: 'root'})\nexport class RadioControlRegistry {\n  private _accessors: any[] = [];\n\n  /**\n   * @description\n   * Adds a control to the internal registry. For internal use only.\n   */\n  add(control: NgControl, accessor: RadioControlValueAccessor) {\n    this._accessors.push([control, accessor]);\n  }\n\n  /**\n   * @description\n   * Removes a control from the internal registry. For internal use only.\n   */\n  remove(accessor: RadioControlValueAccessor) {\n    for (let i = this._accessors.length - 1; i >= 0; --i) {\n      if (this._accessors[i][1] === accessor) {\n        this._accessors.splice(i, 1);\n        return;\n      }\n    }\n  }\n\n  /**\n   * @description\n   * Selects a radio button. For internal use only.\n   */\n  select(accessor: RadioControlValueAccessor) {\n    this._accessors.forEach((c) => {\n      if (this._isSameGroup(c, accessor) && c[1] !== accessor) {\n        c[1].fireUncheck(accessor.value);\n      }\n    });\n  }\n\n  private _isSameGroup(\n      controlPair: [NgControl, RadioControlValueAccessor],\n      accessor: RadioControlValueAccessor): boolean {\n    if (!controlPair[0].control) return false;\n    return controlPair[0]._parent === accessor._control._parent &&\n        controlPair[1].name === accessor.name;\n  }\n}\n\n/**\n * @description\n * The `ControlValueAccessor` for writing radio control values and listening to radio control\n * changes. The value accessor is used by the `FormControlDirective`, `FormControlName`, and\n * `NgModel` directives.\n *\n * @usageNotes\n *\n * ### Using radio buttons with reactive form directives\n *\n * The follow example shows how to use radio buttons in a reactive form. When using radio buttons in\n * a reactive form, radio buttons in the same group should have the same `formControlName`.\n * Providing a `name` attribute is optional.\n *\n * {@example forms/ts/reactiveRadioButtons/reactive_radio_button_example.ts region='Reactive'}\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n@Directive({\n  selector:\n      'input[type=radio][formControlName],input[type=radio][formControl],input[type=radio][ngModel]',\n  host: {'(change)': 'onChange()', '(blur)': 'onTouched()'},\n  providers: [RADIO_VALUE_ACCESSOR]\n})\nexport class RadioControlValueAccessor extends BuiltInControlValueAccessor implements\n    ControlValueAccessor, OnDestroy, OnInit {\n  /** @internal */\n  // TODO(issue/24571): remove '!'.\n  _state!: boolean;\n  /** @internal */\n  // TODO(issue/24571): remove '!'.\n  _control!: NgControl;\n  /** @internal */\n  // TODO(issue/24571): remove '!'.\n  _fn!: Function;\n\n  private setDisabledStateFired = false;\n\n  /**\n   * The registered callback function called when a change event occurs on the input element.\n   * Note: we declare `onChange` here (also used as host listener) as a function with no arguments\n   * to override the `onChange` function (which expects 1 argument) in the parent\n   * `BaseControlValueAccessor` class.\n   * @nodoc\n   */\n  override onChange = () => {};\n\n  /**\n   * @description\n   * Tracks the name of the radio input element.\n   */\n  // TODO(issue/24571): remove '!'.\n  @Input() name!: string;\n\n  /**\n   * @description\n   * Tracks the name of the `FormControl` bound to the directive. The name corresponds\n   * to a key in the parent `FormGroup` or `FormArray`.\n   */\n  // TODO(issue/24571): remove '!'.\n  @Input() formControlName!: string;\n\n  /**\n   * @description\n   * Tracks the value of the radio input element\n   */\n  @Input() value: any;\n\n  private callSetDisabledState =\n      inject(CALL_SET_DISABLED_STATE, {optional: true}) ?? setDisabledStateDefault;\n\n  constructor(\n      renderer: Renderer2, elementRef: ElementRef, private _registry: RadioControlRegistry,\n      private _injector: Injector) {\n    super(renderer, elementRef);\n  }\n\n  /** @nodoc */\n  ngOnInit(): void {\n    this._control = this._injector.get(NgControl);\n    this._checkName();\n    this._registry.add(this._control, this);\n  }\n\n  /** @nodoc */\n  ngOnDestroy(): void {\n    this._registry.remove(this);\n  }\n\n  /**\n   * Sets the \"checked\" property value on the radio input element.\n   * @nodoc\n   */\n  writeValue(value: any): void {\n    this._state = value === this.value;\n    this.setProperty('checked', this._state);\n  }\n\n  /**\n   * Registers a function called when the control value changes.\n   * @nodoc\n   */\n  override registerOnChange(fn: (_: any) => {}): void {\n    this._fn = fn;\n    this.onChange = () => {\n      fn(this.value);\n      this._registry.select(this);\n    };\n  }\n\n  /** @nodoc */\n  override setDisabledState(isDisabled: boolean): void {\n    /**\n     * `setDisabledState` is supposed to be called whenever the disabled state of a control changes,\n     * including upon control creation. However, a longstanding bug caused the method to not fire\n     * when an *enabled* control was attached. This bug was fixed in v15 in #47576.\n     *\n     * This had a side effect: previously, it was possible to instantiate a reactive form control\n     * with `[attr.disabled]=true`, even though the corresponding control was enabled in the\n     * model. This resulted in a mismatch between the model and the DOM. Now, because\n     * `setDisabledState` is always called, the value in the DOM will be immediately overwritten\n     * with the \"correct\" enabled value.\n     *\n     * However, the fix also created an exceptional case: radio buttons. Because Reactive Forms\n     * models the entire group of radio buttons as a single `FormControl`, there is no way to\n     * control the disabled state for individual radios, so they can no longer be configured as\n     * disabled. Thus, we keep the old behavior for radio buttons, so that `[attr.disabled]`\n     * continues to work. Specifically, we drop the first call to `setDisabledState` if `disabled`\n     * is `false`, and we are not in legacy mode.\n     */\n    if (this.setDisabledStateFired || isDisabled ||\n        this.callSetDisabledState === 'whenDisabledForLegacyCode') {\n      this.setProperty('disabled', isDisabled);\n    }\n    this.setDisabledStateFired = true;\n  }\n\n  /**\n   * Sets the \"value\" on the radio input element and unchecks it.\n   *\n   * @param value\n   */\n  fireUncheck(value: any): void {\n    this.writeValue(value);\n  }\n\n  private _checkName(): void {\n    if (this.name && this.formControlName && this.name !== this.formControlName &&\n        (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwNameError();\n    }\n    if (!this.name && this.formControlName) this.name = this.formControlName;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, forwardRef, Provider} from '@angular/core';\n\nimport {BuiltInControlValueAccessor, ControlValueAccessor, NG_VALUE_ACCESSOR} from './control_value_accessor';\n\nconst RANGE_VALUE_ACCESSOR: Provider = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => RangeValueAccessor),\n  multi: true\n};\n\n/**\n * @description\n * The `ControlValueAccessor` for writing a range value and listening to range input changes.\n * The value accessor is used by the `FormControlDirective`, `FormControlName`, and  `NgModel`\n * directives.\n *\n * @usageNotes\n *\n * ### Using a range input with a reactive form\n *\n * The following example shows how to use a range input with a reactive form.\n *\n * ```ts\n * const ageControl = new FormControl();\n * ```\n *\n * ```\n * <input type=\"range\" [formControl]=\"ageControl\">\n * ```\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n@Directive({\n  selector:\n      'input[type=range][formControlName],input[type=range][formControl],input[type=range][ngModel]',\n  host: {\n    '(change)': 'onChange($event.target.value)',\n    '(input)': 'onChange($event.target.value)',\n    '(blur)': 'onTouched()'\n  },\n  providers: [RANGE_VALUE_ACCESSOR]\n})\nexport class RangeValueAccessor extends BuiltInControlValueAccessor implements\n    ControlValueAccessor {\n  /**\n   * Sets the \"value\" property on the input element.\n   * @nodoc\n   */\n  writeValue(value: any): void {\n    this.setProperty('value', parseFloat(value));\n  }\n\n  /**\n   * Registers a function called when the control value changes.\n   * @nodoc\n   */\n  override registerOnChange(fn: (_: number|null) => void): void {\n    this.onChange = (value) => {\n      fn(value == '' ? null : parseFloat(value));\n    };\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, EventEmitter, forwardRef, Inject, InjectionToken, Input, OnChanges, OnDestroy, Optional, Output, Provider, Self, SimpleChanges} from '@angular/core';\n\nimport {FormControl} from '../../model/form_control';\nimport {NG_ASYNC_VALIDATORS, NG_VALIDATORS} from '../../validators';\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from '../control_value_accessor';\nimport {NgControl} from '../ng_control';\nimport {disabledAttrWarning} from '../reactive_errors';\nimport {_ngModelWarning, CALL_SET_DISABLED_STATE, cleanUpControl, isPropertyUpdated, selectValueAccessor, SetDisabledStateOption, setUpControl} from '../shared';\nimport {AsyncValidator, AsyncValidatorFn, Validator, ValidatorFn} from '../validators';\n\n\n/**\n * Token to provide to turn off the ngModel warning on formControl and formControlName.\n */\nexport const NG_MODEL_WITH_FORM_CONTROL_WARNING =\n    new InjectionToken(ngDevMode ? 'NgModelWithFormControlWarning' : '');\n\nconst formControlBinding: Provider = {\n  provide: NgControl,\n  useExisting: forwardRef(() => FormControlDirective)\n};\n\n/**\n * @description\n * Synchronizes a standalone `FormControl` instance to a form control element.\n *\n * Note that support for using the `ngModel` input property and `ngModelChange` event with reactive\n * form directives was deprecated in Angular v6 and is scheduled for removal in\n * a future version of Angular.\n * For details, see [Deprecated features](guide/deprecations#ngmodel-with-reactive-forms).\n *\n * @see [Reactive Forms Guide](guide/reactive-forms)\n * @see {@link FormControl}\n * @see {@link AbstractControl}\n *\n * @usageNotes\n *\n * The following example shows how to register a standalone control and set its value.\n *\n * {@example forms/ts/simpleFormControl/simple_form_control_example.ts region='Component'}\n *\n * @ngModule ReactiveFormsModule\n * @publicApi\n */\n@Directive({selector: '[formControl]', providers: [formControlBinding], exportAs: 'ngForm'})\nexport class FormControlDirective extends NgControl implements OnChanges, OnDestroy {\n  /**\n   * Internal reference to the view model value.\n   * @nodoc\n   */\n  viewModel: any;\n\n  /**\n   * @description\n   * Tracks the `FormControl` instance bound to the directive.\n   */\n  // TODO(issue/24571): remove '!'.\n  @Input('formControl') form!: FormControl;\n\n  /**\n   * @description\n   * Triggers a warning in dev mode that this input should not be used with reactive forms.\n   */\n  @Input('disabled')\n  set isDisabled(isDisabled: boolean) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      console.warn(disabledAttrWarning);\n    }\n  }\n\n  // TODO(kara): remove next 4 properties once deprecation period is over\n\n  /** @deprecated as of v6 */\n  @Input('ngModel') model: any;\n\n  /** @deprecated as of v6 */\n  @Output('ngModelChange') update = new EventEmitter();\n\n  /**\n   * @description\n   * Static property used to track whether any ngModel warnings have been sent across\n   * all instances of FormControlDirective. Used to support warning config of \"once\".\n   *\n   * @internal\n   */\n  static _ngModelWarningSentOnce = false;\n\n  /**\n   * @description\n   * Instance property used to track whether an ngModel warning has been sent out for this\n   * particular `FormControlDirective` instance. Used to support warning config of \"always\".\n   *\n   * @internal\n   */\n  _ngModelWarningSent = false;\n\n  constructor(\n      @Optional() @Self() @Inject(NG_VALIDATORS) validators: (Validator|ValidatorFn)[],\n      @Optional() @Self() @Inject(NG_ASYNC_VALIDATORS) asyncValidators:\n          (AsyncValidator|AsyncValidatorFn)[],\n      @Optional() @Self() @Inject(NG_VALUE_ACCESSOR) valueAccessors: ControlValueAccessor[],\n      @Optional() @Inject(NG_MODEL_WITH_FORM_CONTROL_WARNING) private _ngModelWarningConfig: string|\n      null,\n      @Optional() @Inject(CALL_SET_DISABLED_STATE) private callSetDisabledState?:\n          SetDisabledStateOption) {\n    super();\n    this._setValidators(validators);\n    this._setAsyncValidators(asyncValidators);\n    this.valueAccessor = selectValueAccessor(this, valueAccessors);\n  }\n\n  /** @nodoc */\n  ngOnChanges(changes: SimpleChanges): void {\n    if (this._isControlChanged(changes)) {\n      const previousForm = changes['form'].previousValue;\n      if (previousForm) {\n        cleanUpControl(previousForm, this, /* validateControlPresenceOnChange */ false);\n      }\n      setUpControl(this.form, this, this.callSetDisabledState);\n      this.form.updateValueAndValidity({emitEvent: false});\n    }\n    if (isPropertyUpdated(changes, this.viewModel)) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        _ngModelWarning('formControl', FormControlDirective, this, this._ngModelWarningConfig);\n      }\n      this.form.setValue(this.model);\n      this.viewModel = this.model;\n    }\n  }\n\n  /** @nodoc */\n  ngOnDestroy() {\n    if (this.form) {\n      cleanUpControl(this.form, this, /* validateControlPresenceOnChange */ false);\n    }\n  }\n\n  /**\n   * @description\n   * Returns an array that represents the path from the top-level form to this control.\n   * Each index is the string name of the control on that level.\n   */\n  override get path(): string[] {\n    return [];\n  }\n\n  /**\n   * @description\n   * The `FormControl` bound to this directive.\n   */\n  override get control(): FormControl {\n    return this.form;\n  }\n\n  /**\n   * @description\n   * Sets the new value for the view model and emits an `ngModelChange` event.\n   *\n   * @param newValue The new value for the view model.\n   */\n  override viewToModelUpdate(newValue: any): void {\n    this.viewModel = newValue;\n    this.update.emit(newValue);\n  }\n\n  private _isControlChanged(changes: {[key: string]: any}): boolean {\n    return changes.hasOwnProperty('form');\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, EventEmitter, forwardRef, Inject, Input, OnChanges, OnDestroy, Optional, Output, Provider, Self, SimpleChanges, ɵWritable as Writable} from '@angular/core';\n\nimport {FormArray} from '../../model/form_array';\nimport {FormControl, isFormControl} from '../../model/form_control';\nimport {FormGroup} from '../../model/form_group';\nimport {NG_ASYNC_VALIDATORS, NG_VALIDATORS} from '../../validators';\nimport {ControlContainer} from '../control_container';\nimport {Form} from '../form_interface';\nimport {missingFormException} from '../reactive_errors';\nimport {CALL_SET_DISABLED_STATE, cleanUpControl, cleanUpFormContainer, cleanUpValidators, removeListItem, SetDisabledStateOption, setUpControl, setUpFormContainer, setUpValidators, syncPendingControls} from '../shared';\nimport {AsyncValidator, AsyncValidatorFn, Validator, ValidatorFn} from '../validators';\n\nimport {FormControlName} from './form_control_name';\nimport {FormArrayName, FormGroupName} from './form_group_name';\n\nconst formDirectiveProvider: Provider = {\n  provide: ControlContainer,\n  useExisting: forwardRef(() => FormGroupDirective)\n};\n\n/**\n * @description\n *\n * Binds an existing `FormGroup` or `FormRecord` to a DOM element.\n *\n * This directive accepts an existing `FormGroup` instance. It will then use this\n * `FormGroup` instance to match any child `FormControl`, `FormGroup`/`FormRecord`,\n * and `FormArray` instances to child `FormControlName`, `FormGroupName`,\n * and `FormArrayName` directives.\n *\n * @see [Reactive Forms Guide](guide/reactive-forms)\n * @see {@link AbstractControl}\n *\n * @usageNotes\n * ### Register Form Group\n *\n * The following example registers a `FormGroup` with first name and last name controls,\n * and listens for the *ngSubmit* event when the button is clicked.\n *\n * {@example forms/ts/simpleFormGroup/simple_form_group_example.ts region='Component'}\n *\n * @ngModule ReactiveFormsModule\n * @publicApi\n */\n@Directive({\n  selector: '[formGroup]',\n  providers: [formDirectiveProvider],\n  host: {'(submit)': 'onSubmit($event)', '(reset)': 'onReset()'},\n  exportAs: 'ngForm'\n})\nexport class FormGroupDirective extends ControlContainer implements Form, OnChanges, OnDestroy {\n  /**\n   * @description\n   * Reports whether the form submission has been triggered.\n   */\n  public readonly submitted: boolean = false;\n\n  /**\n   * Reference to an old form group input value, which is needed to cleanup old instance in case it\n   * was replaced with a new one.\n   */\n  private _oldForm: FormGroup|undefined;\n\n  /**\n   * Callback that should be invoked when controls in FormGroup or FormArray collection change\n   * (added or removed). This callback triggers corresponding DOM updates.\n   */\n  private readonly _onCollectionChange = () => this._updateDomValue();\n\n  /**\n   * @description\n   * Tracks the list of added `FormControlName` instances\n   */\n  directives: FormControlName[] = [];\n\n  /**\n   * @description\n   * Tracks the `FormGroup` bound to this directive.\n   */\n  @Input('formGroup') form: FormGroup = null!;\n\n  /**\n   * @description\n   * Emits an event when the form submission has been triggered.\n   */\n  @Output() ngSubmit = new EventEmitter();\n\n  constructor(\n      @Optional() @Self() @Inject(NG_VALIDATORS) validators: (Validator|ValidatorFn)[],\n      @Optional() @Self() @Inject(NG_ASYNC_VALIDATORS) asyncValidators:\n          (AsyncValidator|AsyncValidatorFn)[],\n      @Optional() @Inject(CALL_SET_DISABLED_STATE) private callSetDisabledState?:\n          SetDisabledStateOption) {\n    super();\n    this._setValidators(validators);\n    this._setAsyncValidators(asyncValidators);\n  }\n\n  /** @nodoc */\n  ngOnChanges(changes: SimpleChanges): void {\n    this._checkFormPresent();\n    if (changes.hasOwnProperty('form')) {\n      this._updateValidators();\n      this._updateDomValue();\n      this._updateRegistrations();\n      this._oldForm = this.form;\n    }\n  }\n\n  /** @nodoc */\n  ngOnDestroy() {\n    if (this.form) {\n      cleanUpValidators(this.form, this);\n\n      // Currently the `onCollectionChange` callback is rewritten each time the\n      // `_registerOnCollectionChange` function is invoked. The implication is that cleanup should\n      // happen *only* when the `onCollectionChange` callback was set by this directive instance.\n      // Otherwise it might cause overriding a callback of some other directive instances. We should\n      // consider updating this logic later to make it similar to how `onChange` callbacks are\n      // handled, see https://github.com/angular/angular/issues/39732 for additional info.\n      if (this.form._onCollectionChange === this._onCollectionChange) {\n        this.form._registerOnCollectionChange(() => {});\n      }\n    }\n  }\n\n  /**\n   * @description\n   * Returns this directive's instance.\n   */\n  override get formDirective(): Form {\n    return this;\n  }\n\n  /**\n   * @description\n   * Returns the `FormGroup` bound to this directive.\n   */\n  override get control(): FormGroup {\n    return this.form;\n  }\n\n  /**\n   * @description\n   * Returns an array representing the path to this group. Because this directive\n   * always lives at the top level of a form, it always an empty array.\n   */\n  override get path(): string[] {\n    return [];\n  }\n\n  /**\n   * @description\n   * Method that sets up the control directive in this group, re-calculates its value\n   * and validity, and adds the instance to the internal list of directives.\n   *\n   * @param dir The `FormControlName` directive instance.\n   */\n  addControl(dir: FormControlName): FormControl {\n    const ctrl: any = this.form.get(dir.path);\n    setUpControl(ctrl, dir, this.callSetDisabledState);\n    ctrl.updateValueAndValidity({emitEvent: false});\n    this.directives.push(dir);\n    return ctrl;\n  }\n\n  /**\n   * @description\n   * Retrieves the `FormControl` instance from the provided `FormControlName` directive\n   *\n   * @param dir The `FormControlName` directive instance.\n   */\n  getControl(dir: FormControlName): FormControl {\n    return <FormControl>this.form.get(dir.path);\n  }\n\n  /**\n   * @description\n   * Removes the `FormControlName` instance from the internal list of directives\n   *\n   * @param dir The `FormControlName` directive instance.\n   */\n  removeControl(dir: FormControlName): void {\n    cleanUpControl(dir.control || null, dir, /* validateControlPresenceOnChange */ false);\n    removeListItem(this.directives, dir);\n  }\n\n  /**\n   * Adds a new `FormGroupName` directive instance to the form.\n   *\n   * @param dir The `FormGroupName` directive instance.\n   */\n  addFormGroup(dir: FormGroupName): void {\n    this._setUpFormContainer(dir);\n  }\n\n  /**\n   * Performs the necessary cleanup when a `FormGroupName` directive instance is removed from the\n   * view.\n   *\n   * @param dir The `FormGroupName` directive instance.\n   */\n  removeFormGroup(dir: FormGroupName): void {\n    this._cleanUpFormContainer(dir);\n  }\n\n  /**\n   * @description\n   * Retrieves the `FormGroup` for a provided `FormGroupName` directive instance\n   *\n   * @param dir The `FormGroupName` directive instance.\n   */\n  getFormGroup(dir: FormGroupName): FormGroup {\n    return <FormGroup>this.form.get(dir.path);\n  }\n\n  /**\n   * Performs the necessary setup when a `FormArrayName` directive instance is added to the view.\n   *\n   * @param dir The `FormArrayName` directive instance.\n   */\n  addFormArray(dir: FormArrayName): void {\n    this._setUpFormContainer(dir);\n  }\n\n  /**\n   * Performs the necessary cleanup when a `FormArrayName` directive instance is removed from the\n   * view.\n   *\n   * @param dir The `FormArrayName` directive instance.\n   */\n  removeFormArray(dir: FormArrayName): void {\n    this._cleanUpFormContainer(dir);\n  }\n\n  /**\n   * @description\n   * Retrieves the `FormArray` for a provided `FormArrayName` directive instance.\n   *\n   * @param dir The `FormArrayName` directive instance.\n   */\n  getFormArray(dir: FormArrayName): FormArray {\n    return <FormArray>this.form.get(dir.path);\n  }\n\n  /**\n   * Sets the new value for the provided `FormControlName` directive.\n   *\n   * @param dir The `FormControlName` directive instance.\n   * @param value The new value for the directive's control.\n   */\n  updateModel(dir: FormControlName, value: any): void {\n    const ctrl = <FormControl>this.form.get(dir.path);\n    ctrl.setValue(value);\n  }\n\n  /**\n   * @description\n   * Method called with the \"submit\" event is triggered on the form.\n   * Triggers the `ngSubmit` emitter to emit the \"submit\" event as its payload.\n   *\n   * @param $event The \"submit\" event object\n   */\n  onSubmit($event: Event): boolean {\n    (this as Writable<this>).submitted = true;\n    syncPendingControls(this.form, this.directives);\n    this.ngSubmit.emit($event);\n    // Forms with `method=\"dialog\"` have some special behavior that won't reload the page and that\n    // shouldn't be prevented. Note that we need to null check the `event` and the `target`, because\n    // some internal apps call this method directly with the wrong arguments.\n    return ($event?.target as HTMLFormElement | null)?.method === 'dialog';\n  }\n\n  /**\n   * @description\n   * Method called when the \"reset\" event is triggered on the form.\n   */\n  onReset(): void {\n    this.resetForm();\n  }\n\n  /**\n   * @description\n   * Resets the form to an initial value and resets its submitted status.\n   *\n   * @param value The new value for the form.\n   */\n  resetForm(value: any = undefined): void {\n    this.form.reset(value);\n    (this as Writable<this>).submitted = false;\n  }\n\n  /** @internal */\n  _updateDomValue() {\n    this.directives.forEach(dir => {\n      const oldCtrl = dir.control;\n      const newCtrl = this.form.get(dir.path);\n      if (oldCtrl !== newCtrl) {\n        // Note: the value of the `dir.control` may not be defined, for example when it's a first\n        // `FormControl` that is added to a `FormGroup` instance (via `addControl` call).\n        cleanUpControl(oldCtrl || null, dir);\n\n        // Check whether new control at the same location inside the corresponding `FormGroup` is an\n        // instance of `FormControl` and perform control setup only if that's the case.\n        // Note: we don't need to clear the list of directives (`this.directives`) here, it would be\n        // taken care of in the `removeControl` method invoked when corresponding `formControlName`\n        // directive instance is being removed (invoked from `FormControlName.ngOnDestroy`).\n        if (isFormControl(newCtrl)) {\n          setUpControl(newCtrl, dir, this.callSetDisabledState);\n          (dir as Writable<FormControlName>).control = newCtrl;\n        }\n      }\n    });\n\n    this.form._updateTreeValidity({emitEvent: false});\n  }\n\n  private _setUpFormContainer(dir: FormArrayName|FormGroupName): void {\n    const ctrl: any = this.form.get(dir.path);\n    setUpFormContainer(ctrl, dir);\n    // NOTE: this operation looks unnecessary in case no new validators were added in\n    // `setUpFormContainer` call. Consider updating this code to match the logic in\n    // `_cleanUpFormContainer` function.\n    ctrl.updateValueAndValidity({emitEvent: false});\n  }\n\n  private _cleanUpFormContainer(dir: FormArrayName|FormGroupName): void {\n    if (this.form) {\n      const ctrl: any = this.form.get(dir.path);\n      if (ctrl) {\n        const isControlUpdated = cleanUpFormContainer(ctrl, dir);\n        if (isControlUpdated) {\n          // Run validity check only in case a control was updated (i.e. view validators were\n          // removed) as removing view validators might cause validity to change.\n          ctrl.updateValueAndValidity({emitEvent: false});\n        }\n      }\n    }\n  }\n\n  private _updateRegistrations() {\n    this.form._registerOnCollectionChange(this._onCollectionChange);\n    if (this._oldForm) {\n      this._oldForm._registerOnCollectionChange(() => {});\n    }\n  }\n\n  private _updateValidators() {\n    setUpValidators(this.form, this);\n    if (this._oldForm) {\n      cleanUpValidators(this._oldForm, this);\n    }\n  }\n\n  private _checkFormPresent() {\n    if (!this.form && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw missingFormException();\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, forwardRef, Host, Inject, Input, OnD<PERSON>roy, OnInit, Optional, Provider, Self, SkipSelf} from '@angular/core';\n\nimport {FormArray} from '../../model/form_array';\nimport {NG_ASYNC_VALIDATORS, NG_VALIDATORS} from '../../validators';\nimport {AbstractFormGroupDirective} from '../abstract_form_group_directive';\nimport {ControlContainer} from '../control_container';\nimport {arrayParentException, groupParentException} from '../reactive_errors';\nimport {controlPath} from '../shared';\nimport {AsyncValidator, AsyncValidatorFn, Validator, ValidatorFn} from '../validators';\n\nimport {FormGroupDirective} from './form_group_directive';\n\nconst formGroupNameProvider: Provider = {\n  provide: ControlContainer,\n  useExisting: forwardRef(() => FormGroupName)\n};\n\n/**\n * @description\n *\n * Syncs a nested `FormGroup` or `FormRecord` to a DOM element.\n *\n * This directive can only be used with a parent `FormGroupDirective`.\n *\n * It accepts the string name of the nested `FormGroup` or `FormRecord` to link, and\n * looks for a `FormGroup` or `FormRecord` registered with that name in the parent\n * `FormGroup` instance you passed into `FormGroupDirective`.\n *\n * Use nested form groups to validate a sub-group of a\n * form separately from the rest or to group the values of certain\n * controls into their own nested object.\n *\n * @see [Reactive Forms Guide](guide/reactive-forms)\n *\n * @usageNotes\n *\n * ### Access the group by name\n *\n * The following example uses the `AbstractControl.get` method to access the\n * associated `FormGroup`\n *\n * ```ts\n *   this.form.get('name');\n * ```\n *\n * ### Access individual controls in the group\n *\n * The following example uses the `AbstractControl.get` method to access\n * individual controls within the group using dot syntax.\n *\n * ```ts\n *   this.form.get('name.first');\n * ```\n *\n * ### Register a nested `FormGroup`.\n *\n * The following example registers a nested *name* `FormGroup` within an existing `FormGroup`,\n * and provides methods to retrieve the nested `FormGroup` and individual controls.\n *\n * {@example forms/ts/nestedFormGroup/nested_form_group_example.ts region='Component'}\n *\n * @ngModule ReactiveFormsModule\n * @publicApi\n */\n@Directive({selector: '[formGroupName]', providers: [formGroupNameProvider]})\nexport class FormGroupName extends AbstractFormGroupDirective implements OnInit, OnDestroy {\n  /**\n   * @description\n   * Tracks the name of the `FormGroup` bound to the directive. The name corresponds\n   * to a key in the parent `FormGroup` or `FormArray`.\n   * Accepts a name as a string or a number.\n   * The name in the form of a string is useful for individual forms,\n   * while the numerical form allows for form groups to be bound\n   * to indices when iterating over groups in a `FormArray`.\n   */\n  @Input('formGroupName') override name: string|number|null = null;\n\n  constructor(\n      @Optional() @Host() @SkipSelf() parent: ControlContainer,\n      @Optional() @Self() @Inject(NG_VALIDATORS) validators: (Validator|ValidatorFn)[],\n      @Optional() @Self() @Inject(NG_ASYNC_VALIDATORS) asyncValidators:\n          (AsyncValidator|AsyncValidatorFn)[]) {\n    super();\n    this._parent = parent;\n    this._setValidators(validators);\n    this._setAsyncValidators(asyncValidators);\n  }\n\n  /** @internal */\n  override _checkParentType(): void {\n    if (_hasInvalidParent(this._parent) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw groupParentException();\n    }\n  }\n}\n\nexport const formArrayNameProvider: any = {\n  provide: ControlContainer,\n  useExisting: forwardRef(() => FormArrayName)\n};\n\n/**\n * @description\n *\n * Syncs a nested `FormArray` to a DOM element.\n *\n * This directive is designed to be used with a parent `FormGroupDirective` (selector:\n * `[formGroup]`).\n *\n * It accepts the string name of the nested `FormArray` you want to link, and\n * will look for a `FormArray` registered with that name in the parent\n * `FormGroup` instance you passed into `FormGroupDirective`.\n *\n * @see [Reactive Forms Guide](guide/reactive-forms)\n * @see {@link AbstractControl}\n *\n * @usageNotes\n *\n * ### Example\n *\n * {@example forms/ts/nestedFormArray/nested_form_array_example.ts region='Component'}\n *\n * @ngModule ReactiveFormsModule\n * @publicApi\n */\n@Directive({selector: '[formArrayName]', providers: [formArrayNameProvider]})\nexport class FormArrayName extends ControlContainer implements OnInit, OnDestroy {\n  /** @internal */\n  _parent: ControlContainer;\n\n  /**\n   * @description\n   * Tracks the name of the `FormArray` bound to the directive. The name corresponds\n   * to a key in the parent `FormGroup` or `FormArray`.\n   * Accepts a name as a string or a number.\n   * The name in the form of a string is useful for individual forms,\n   * while the numerical form allows for form arrays to be bound\n   * to indices when iterating over arrays in a `FormArray`.\n   */\n  @Input('formArrayName') override name: string|number|null = null;\n\n  constructor(\n      @Optional() @Host() @SkipSelf() parent: ControlContainer,\n      @Optional() @Self() @Inject(NG_VALIDATORS) validators: (Validator|ValidatorFn)[],\n      @Optional() @Self() @Inject(NG_ASYNC_VALIDATORS) asyncValidators:\n          (AsyncValidator|AsyncValidatorFn)[]) {\n    super();\n    this._parent = parent;\n    this._setValidators(validators);\n    this._setAsyncValidators(asyncValidators);\n  }\n\n  /**\n   * A lifecycle method called when the directive's inputs are initialized. For internal use only.\n   * @throws If the directive does not have a valid parent.\n   * @nodoc\n   */\n  ngOnInit(): void {\n    this._checkParentType();\n    this.formDirective!.addFormArray(this);\n  }\n\n  /**\n   * A lifecycle method called before the directive's instance is destroyed. For internal use only.\n   * @nodoc\n   */\n  ngOnDestroy(): void {\n    if (this.formDirective) {\n      this.formDirective.removeFormArray(this);\n    }\n  }\n\n  /**\n   * @description\n   * The `FormArray` bound to this directive.\n   */\n  override get control(): FormArray {\n    return this.formDirective!.getFormArray(this);\n  }\n\n  /**\n   * @description\n   * The top-level directive for this group if present, otherwise null.\n   */\n  override get formDirective(): FormGroupDirective|null {\n    return this._parent ? <FormGroupDirective>this._parent.formDirective : null;\n  }\n\n  /**\n   * @description\n   * Returns an array that represents the path from the top-level form to this control.\n   * Each index is the string name of the control on that level.\n   */\n  override get path(): string[] {\n    return controlPath(this.name == null ? this.name : this.name.toString(), this._parent);\n  }\n\n  private _checkParentType(): void {\n    if (_hasInvalidParent(this._parent) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw arrayParentException();\n    }\n  }\n}\n\nfunction _hasInvalidParent(parent: ControlContainer): boolean {\n  return !(parent instanceof FormGroupName) && !(parent instanceof FormGroupDirective) &&\n      !(parent instanceof FormArrayName);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, EventEmitter, forwardRef, Host, Inject, Input, OnChanges, OnDestroy, Optional, Output, Provider, Self, SimpleChang<PERSON>, Skip<PERSON>elf, ɵWritable as Writable} from '@angular/core';\n\nimport {FormControl} from '../../model/form_control';\nimport {NG_ASYNC_VALIDATORS, NG_VALIDATORS} from '../../validators';\nimport {AbstractFormGroupDirective} from '../abstract_form_group_directive';\nimport {ControlContainer} from '../control_container';\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from '../control_value_accessor';\nimport {NgControl} from '../ng_control';\nimport {controlParentException, disabledAttrWarning, ngModelGroupException} from '../reactive_errors';\nimport {_ngModelWarning, controlPath, isPropertyUpdated, selectValueAccessor} from '../shared';\nimport {AsyncValidator, AsyncValidatorFn, Validator, ValidatorFn} from '../validators';\n\nimport {NG_MODEL_WITH_FORM_CONTROL_WARNING} from './form_control_directive';\nimport {FormGroupDirective} from './form_group_directive';\nimport {FormArrayName, FormGroupName} from './form_group_name';\n\nconst controlNameBinding: Provider = {\n  provide: NgControl,\n  useExisting: forwardRef(() => FormControlName)\n};\n\n/**\n * @description\n * Syncs a `FormControl` in an existing `FormGroup` to a form control\n * element by name.\n *\n * @see [Reactive Forms Guide](guide/reactive-forms)\n * @see {@link FormControl}\n * @see {@link AbstractControl}\n *\n * @usageNotes\n *\n * ### Register `FormControl` within a group\n *\n * The following example shows how to register multiple form controls within a form group\n * and set their value.\n *\n * {@example forms/ts/simpleFormGroup/simple_form_group_example.ts region='Component'}\n *\n * To see `formControlName` examples with different form control types, see:\n *\n * * Radio buttons: `RadioControlValueAccessor`\n * * Selects: `SelectControlValueAccessor`\n *\n * ### Use with ngModel is deprecated\n *\n * Support for using the `ngModel` input property and `ngModelChange` event with reactive\n * form directives has been deprecated in Angular v6 and is scheduled for removal in\n * a future version of Angular.\n *\n * For details, see [Deprecated features](guide/deprecations#ngmodel-with-reactive-forms).\n *\n * @ngModule ReactiveFormsModule\n * @publicApi\n */\n@Directive({selector: '[formControlName]', providers: [controlNameBinding]})\nexport class FormControlName extends NgControl implements OnChanges, OnDestroy {\n  private _added = false;\n  /**\n   * Internal reference to the view model value.\n   * @internal\n   */\n  viewModel: any;\n\n  /**\n   * @description\n   * Tracks the `FormControl` instance bound to the directive.\n   */\n  // TODO(issue/24571): remove '!'.\n  override readonly control!: FormControl;\n\n  /**\n   * @description\n   * Tracks the name of the `FormControl` bound to the directive. The name corresponds\n   * to a key in the parent `FormGroup` or `FormArray`.\n   * Accepts a name as a string or a number.\n   * The name in the form of a string is useful for individual forms,\n   * while the numerical form allows for form controls to be bound\n   * to indices when iterating over controls in a `FormArray`.\n   */\n  @Input('formControlName') override name: string|number|null = null;\n\n  /**\n   * @description\n   * Triggers a warning in dev mode that this input should not be used with reactive forms.\n   */\n  @Input('disabled')\n  set isDisabled(isDisabled: boolean) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      console.warn(disabledAttrWarning);\n    }\n  }\n\n  // TODO(kara): remove next 4 properties once deprecation period is over\n\n  /** @deprecated as of v6 */\n  @Input('ngModel') model: any;\n\n  /** @deprecated as of v6 */\n  @Output('ngModelChange') update = new EventEmitter();\n\n  /**\n   * @description\n   * Static property used to track whether any ngModel warnings have been sent across\n   * all instances of FormControlName. Used to support warning config of \"once\".\n   *\n   * @internal\n   */\n  static _ngModelWarningSentOnce = false;\n\n  /**\n   * @description\n   * Instance property used to track whether an ngModel warning has been sent out for this\n   * particular FormControlName instance. Used to support warning config of \"always\".\n   *\n   * @internal\n   */\n  _ngModelWarningSent = false;\n\n  constructor(\n      @Optional() @Host() @SkipSelf() parent: ControlContainer,\n      @Optional() @Self() @Inject(NG_VALIDATORS) validators: (Validator|ValidatorFn)[],\n      @Optional() @Self() @Inject(NG_ASYNC_VALIDATORS) asyncValidators:\n          (AsyncValidator|AsyncValidatorFn)[],\n      @Optional() @Self() @Inject(NG_VALUE_ACCESSOR) valueAccessors: ControlValueAccessor[],\n      @Optional() @Inject(NG_MODEL_WITH_FORM_CONTROL_WARNING) private _ngModelWarningConfig: string|\n      null) {\n    super();\n    this._parent = parent;\n    this._setValidators(validators);\n    this._setAsyncValidators(asyncValidators);\n    this.valueAccessor = selectValueAccessor(this, valueAccessors);\n  }\n\n  /** @nodoc */\n  ngOnChanges(changes: SimpleChanges) {\n    if (!this._added) this._setUpControl();\n    if (isPropertyUpdated(changes, this.viewModel)) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        _ngModelWarning('formControlName', FormControlName, this, this._ngModelWarningConfig);\n      }\n      this.viewModel = this.model;\n      this.formDirective.updateModel(this, this.model);\n    }\n  }\n\n  /** @nodoc */\n  ngOnDestroy(): void {\n    if (this.formDirective) {\n      this.formDirective.removeControl(this);\n    }\n  }\n\n  /**\n   * @description\n   * Sets the new value for the view model and emits an `ngModelChange` event.\n   *\n   * @param newValue The new value for the view model.\n   */\n  override viewToModelUpdate(newValue: any): void {\n    this.viewModel = newValue;\n    this.update.emit(newValue);\n  }\n\n  /**\n   * @description\n   * Returns an array that represents the path from the top-level form to this control.\n   * Each index is the string name of the control on that level.\n   */\n  override get path(): string[] {\n    return controlPath(this.name == null ? this.name : this.name.toString(), this._parent!);\n  }\n\n  /**\n   * @description\n   * The top-level directive for this group if present, otherwise null.\n   */\n  get formDirective(): any {\n    return this._parent ? this._parent.formDirective : null;\n  }\n\n  private _checkParentType(): void {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!(this._parent instanceof FormGroupName) &&\n          this._parent instanceof AbstractFormGroupDirective) {\n        throw ngModelGroupException();\n      } else if (\n          !(this._parent instanceof FormGroupName) &&\n          !(this._parent instanceof FormGroupDirective) &&\n          !(this._parent instanceof FormArrayName)) {\n        throw controlParentException();\n      }\n    }\n  }\n\n  private _setUpControl() {\n    this._checkParentType();\n    (this as Writable<this>).control = this.formDirective.addControl(this);\n    this._added = true;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, ElementRef, forwardRef, Host, Input, OnDestroy, Optional, Provider, Renderer2, ɵRuntimeError as RuntimeError} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../errors';\n\nimport {BuiltInControlValueAccessor, ControlValueAccessor, NG_VALUE_ACCESSOR} from './control_value_accessor';\n\nconst SELECT_VALUE_ACCESSOR: Provider = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => SelectControlValueAccessor),\n  multi: true\n};\n\nfunction _buildValueString(id: string|null, value: any): string {\n  if (id == null) return `${value}`;\n  if (value && typeof value === 'object') value = 'Object';\n  return `${id}: ${value}`.slice(0, 50);\n}\n\nfunction _extractId(valueString: string): string {\n  return valueString.split(':')[0];\n}\n\n/**\n * @description\n * The `ControlValueAccessor` for writing select control values and listening to select control\n * changes. The value accessor is used by the `FormControlDirective`, `FormControlName`, and\n * `NgModel` directives.\n *\n * @usageNotes\n *\n * ### Using select controls in a reactive form\n *\n * The following examples show how to use a select control in a reactive form.\n *\n * {@example forms/ts/reactiveSelectControl/reactive_select_control_example.ts region='Component'}\n *\n * ### Using select controls in a template-driven form\n *\n * To use a select in a template-driven form, simply add an `ngModel` and a `name`\n * attribute to the main `<select>` tag.\n *\n * {@example forms/ts/selectControl/select_control_example.ts region='Component'}\n *\n * ### Customizing option selection\n *\n * Angular uses object identity to select option. It's possible for the identities of items\n * to change while the data does not. This can happen, for example, if the items are produced\n * from an RPC to the server, and that RPC is re-run. Even if the data hasn't changed, the\n * second response will produce objects with different identities.\n *\n * To customize the default option comparison algorithm, `<select>` supports `compareWith` input.\n * `compareWith` takes a **function** which has two arguments: `option1` and `option2`.\n * If `compareWith` is given, Angular selects option by the return value of the function.\n *\n * ```ts\n * const selectedCountriesControl = new FormControl();\n * ```\n *\n * ```\n * <select [compareWith]=\"compareFn\"  [formControl]=\"selectedCountriesControl\">\n *     <option *ngFor=\"let country of countries\" [ngValue]=\"country\">\n *         {{country.name}}\n *     </option>\n * </select>\n *\n * compareFn(c1: Country, c2: Country): boolean {\n *     return c1 && c2 ? c1.id === c2.id : c1 === c2;\n * }\n * ```\n *\n * **Note:** We listen to the 'change' event because 'input' events aren't fired\n * for selects in IE, see:\n * https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/input_event#browser_compatibility\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n@Directive({\n  selector:\n      'select:not([multiple])[formControlName],select:not([multiple])[formControl],select:not([multiple])[ngModel]',\n  host: {'(change)': 'onChange($event.target.value)', '(blur)': 'onTouched()'},\n  providers: [SELECT_VALUE_ACCESSOR]\n})\nexport class SelectControlValueAccessor extends BuiltInControlValueAccessor implements\n    ControlValueAccessor {\n  /** @nodoc */\n  value: any;\n\n  /** @internal */\n  _optionMap: Map<string, any> = new Map<string, any>();\n\n  /** @internal */\n  _idCounter: number = 0;\n\n  /**\n   * @description\n   * Tracks the option comparison algorithm for tracking identities when\n   * checking for changes.\n   */\n  @Input()\n  set compareWith(fn: (o1: any, o2: any) => boolean) {\n    if (typeof fn !== 'function' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw new RuntimeError(\n          RuntimeErrorCode.COMPAREWITH_NOT_A_FN,\n          `compareWith must be a function, but received ${JSON.stringify(fn)}`);\n    }\n    this._compareWith = fn;\n  }\n\n  private _compareWith: (o1: any, o2: any) => boolean = Object.is;\n\n  /**\n   * Sets the \"value\" property on the select element.\n   * @nodoc\n   */\n  writeValue(value: any): void {\n    this.value = value;\n    const id: string|null = this._getOptionId(value);\n    const valueString = _buildValueString(id, value);\n    this.setProperty('value', valueString);\n  }\n\n  /**\n   * Registers a function called when the control value changes.\n   * @nodoc\n   */\n  override registerOnChange(fn: (value: any) => any): void {\n    this.onChange = (valueString: string) => {\n      this.value = this._getOptionValue(valueString);\n      fn(this.value);\n    };\n  }\n\n  /** @internal */\n  _registerOption(): string {\n    return (this._idCounter++).toString();\n  }\n\n  /** @internal */\n  _getOptionId(value: any): string|null {\n    for (const id of this._optionMap.keys()) {\n      if (this._compareWith(this._optionMap.get(id), value)) return id;\n    }\n    return null;\n  }\n\n  /** @internal */\n  _getOptionValue(valueString: string): any {\n    const id: string = _extractId(valueString);\n    return this._optionMap.has(id) ? this._optionMap.get(id) : valueString;\n  }\n}\n\n/**\n * @description\n * Marks `<option>` as dynamic, so Angular can be notified when options change.\n *\n * @see {@link SelectControlValueAccessor}\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n@Directive({selector: 'option'})\nexport class NgSelectOption implements OnDestroy {\n  /**\n   * @description\n   * ID of the option element\n   */\n  // TODO(issue/24571): remove '!'.\n  id!: string;\n\n  constructor(\n      private _element: ElementRef, private _renderer: Renderer2,\n      @Optional() @Host() private _select: SelectControlValueAccessor) {\n    if (this._select) this.id = this._select._registerOption();\n  }\n\n  /**\n   * @description\n   * Tracks the value bound to the option element. Unlike the value binding,\n   * ngValue supports binding to objects.\n   */\n  @Input('ngValue')\n  set ngValue(value: any) {\n    if (this._select == null) return;\n    this._select._optionMap.set(this.id, value);\n    this._setElementValue(_buildValueString(this.id, value));\n    this._select.writeValue(this._select.value);\n  }\n\n  /**\n   * @description\n   * Tracks simple string values bound to the option element.\n   * For objects, use the `ngValue` input binding.\n   */\n  @Input('value')\n  set value(value: any) {\n    this._setElementValue(value);\n    if (this._select) this._select.writeValue(this._select.value);\n  }\n\n  /** @internal */\n  _setElementValue(value: string): void {\n    this._renderer.setProperty(this._element.nativeElement, 'value', value);\n  }\n\n  /** @nodoc */\n  ngOnDestroy(): void {\n    if (this._select) {\n      this._select._optionMap.delete(this.id);\n      this._select.writeValue(this._select.value);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, ElementRef, forwardRef, Host, Input, OnDestroy, Optional, Provider, Renderer2, ɵRuntimeError as RuntimeError} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../errors';\n\nimport {BuiltInControlValueAccessor, ControlValueAccessor, NG_VALUE_ACCESSOR} from './control_value_accessor';\n\nconst SELECT_MULTIPLE_VALUE_ACCESSOR: Provider = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => SelectMultipleControlValueAccessor),\n  multi: true\n};\n\nfunction _buildValueString(id: string, value: any): string {\n  if (id == null) return `${value}`;\n  if (typeof value === 'string') value = `'${value}'`;\n  if (value && typeof value === 'object') value = 'Object';\n  return `${id}: ${value}`.slice(0, 50);\n}\n\nfunction _extractId(valueString: string): string {\n  return valueString.split(':')[0];\n}\n\n/** Mock interface for HTML Options */\ninterface HTMLOption {\n  value: string;\n  selected: boolean;\n}\n\n/** Mock interface for HTMLCollection */\nabstract class HTMLCollection {\n  // TODO(issue/24571): remove '!'.\n  length!: number;\n  abstract item(_: number): HTMLOption;\n}\n\n/**\n * @description\n * The `ControlValueAccessor` for writing multi-select control values and listening to multi-select\n * control changes. The value accessor is used by the `FormControlDirective`, `FormControlName`, and\n * `NgModel` directives.\n *\n * @see {@link SelectControlValueAccessor}\n *\n * @usageNotes\n *\n * ### Using a multi-select control\n *\n * The follow example shows you how to use a multi-select control with a reactive form.\n *\n * ```ts\n * const countryControl = new FormControl();\n * ```\n *\n * ```\n * <select multiple name=\"countries\" [formControl]=\"countryControl\">\n *   <option *ngFor=\"let country of countries\" [ngValue]=\"country\">\n *     {{ country.name }}\n *   </option>\n * </select>\n * ```\n *\n * ### Customizing option selection\n *\n * To customize the default option comparison algorithm, `<select>` supports `compareWith` input.\n * See the `SelectControlValueAccessor` for usage.\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n@Directive({\n  selector:\n      'select[multiple][formControlName],select[multiple][formControl],select[multiple][ngModel]',\n  host: {'(change)': 'onChange($event.target)', '(blur)': 'onTouched()'},\n  providers: [SELECT_MULTIPLE_VALUE_ACCESSOR]\n})\nexport class SelectMultipleControlValueAccessor extends BuiltInControlValueAccessor implements\n    ControlValueAccessor {\n  /**\n   * The current value.\n   * @nodoc\n   */\n  value: any;\n\n  /** @internal */\n  _optionMap: Map<string, ɵNgSelectMultipleOption> = new Map<string, ɵNgSelectMultipleOption>();\n\n  /** @internal */\n  _idCounter: number = 0;\n\n  /**\n   * @description\n   * Tracks the option comparison algorithm for tracking identities when\n   * checking for changes.\n   */\n  @Input()\n  set compareWith(fn: (o1: any, o2: any) => boolean) {\n    if (typeof fn !== 'function' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw new RuntimeError(\n          RuntimeErrorCode.COMPAREWITH_NOT_A_FN,\n          `compareWith must be a function, but received ${JSON.stringify(fn)}`);\n    }\n    this._compareWith = fn;\n  }\n\n  private _compareWith: (o1: any, o2: any) => boolean = Object.is;\n\n  /**\n   * Sets the \"value\" property on one or of more of the select's options.\n   * @nodoc\n   */\n  writeValue(value: any): void {\n    this.value = value;\n    let optionSelectedStateSetter: (opt: ɵNgSelectMultipleOption, o: any) => void;\n    if (Array.isArray(value)) {\n      // convert values to ids\n      const ids = value.map((v) => this._getOptionId(v));\n      optionSelectedStateSetter = (opt, o) => {\n        opt._setSelected(ids.indexOf(o.toString()) > -1);\n      };\n    } else {\n      optionSelectedStateSetter = (opt, o) => {\n        opt._setSelected(false);\n      };\n    }\n    this._optionMap.forEach(optionSelectedStateSetter);\n  }\n\n  /**\n   * Registers a function called when the control value changes\n   * and writes an array of the selected options.\n   * @nodoc\n   */\n  override registerOnChange(fn: (value: any) => any): void {\n    this.onChange = (element: HTMLSelectElement) => {\n      const selected: Array<any> = [];\n      const selectedOptions = element.selectedOptions;\n      if (selectedOptions !== undefined) {\n        const options = selectedOptions;\n        for (let i = 0; i < options.length; i++) {\n          const opt = options[i];\n          const val = this._getOptionValue(opt.value);\n          selected.push(val);\n        }\n      }\n      // Degrade to use `options` when `selectedOptions` property is not available.\n      // Note: the `selectedOptions` is available in all supported browsers, but the Domino lib\n      // doesn't have it currently, see https://github.com/fgnass/domino/issues/177.\n      else {\n        const options = element.options;\n        for (let i = 0; i < options.length; i++) {\n          const opt = options[i];\n          if (opt.selected) {\n            const val = this._getOptionValue(opt.value);\n            selected.push(val);\n          }\n        }\n      }\n      this.value = selected;\n      fn(selected);\n    };\n  }\n\n  /** @internal */\n  _registerOption(value: ɵNgSelectMultipleOption): string {\n    const id: string = (this._idCounter++).toString();\n    this._optionMap.set(id, value);\n    return id;\n  }\n\n  /** @internal */\n  _getOptionId(value: any): string|null {\n    for (const id of this._optionMap.keys()) {\n      if (this._compareWith(this._optionMap.get(id)!._value, value)) return id;\n    }\n    return null;\n  }\n\n  /** @internal */\n  _getOptionValue(valueString: string): any {\n    const id: string = _extractId(valueString);\n    return this._optionMap.has(id) ? this._optionMap.get(id)!._value : valueString;\n  }\n}\n\n/**\n * @description\n * Marks `<option>` as dynamic, so Angular can be notified when options change.\n *\n * @see {@link SelectMultipleControlValueAccessor}\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n@Directive({selector: 'option'})\nexport class ɵNgSelectMultipleOption implements OnDestroy {\n  // TODO(issue/24571): remove '!'.\n  id!: string;\n  /** @internal */\n  _value: any;\n\n  constructor(\n      private _element: ElementRef, private _renderer: Renderer2,\n      @Optional() @Host() private _select: SelectMultipleControlValueAccessor) {\n    if (this._select) {\n      this.id = this._select._registerOption(this);\n    }\n  }\n\n  /**\n   * @description\n   * Tracks the value bound to the option element. Unlike the value binding,\n   * ngValue supports binding to objects.\n   */\n  @Input('ngValue')\n  set ngValue(value: any) {\n    if (this._select == null) return;\n    this._value = value;\n    this._setElementValue(_buildValueString(this.id, value));\n    this._select.writeValue(this._select.value);\n  }\n\n  /**\n   * @description\n   * Tracks simple string values bound to the option element.\n   * For objects, use the `ngValue` input binding.\n   */\n  @Input('value')\n  set value(value: any) {\n    if (this._select) {\n      this._value = value;\n      this._setElementValue(_buildValueString(this.id, value));\n      this._select.writeValue(this._select.value);\n    } else {\n      this._setElementValue(value);\n    }\n  }\n\n  /** @internal */\n  _setElementValue(value: string): void {\n    this._renderer.setProperty(this._element.nativeElement, 'value', value);\n  }\n\n  /** @internal */\n  _setSelected(selected: boolean) {\n    this._renderer.setProperty(this._element.nativeElement, 'selected', selected);\n  }\n\n  /** @nodoc */\n  ngOnDestroy(): void {\n    if (this._select) {\n      this._select._optionMap.delete(this.id);\n      this._select.writeValue(this._select.value);\n    }\n  }\n}\n\nexport {ɵNgSelectMultipleOption as NgSelectMultipleOption};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {booleanAttribute, Directive, forwardRef, Input, OnChanges, Provider, SimpleChanges} from '@angular/core';\nimport {Observable} from 'rxjs';\n\nimport {AbstractControl} from '../model/abstract_model';\nimport {emailValidator, maxLengthValidator, maxValidator, minLengthValidator, minValidator, NG_VALIDATORS, nullValidator, patternValidator, requiredTrueValidator, requiredValidator} from '../validators';\n\n/**\n * Method that updates string to integer if not already a number\n *\n * @param value The value to convert to integer.\n * @returns value of parameter converted to number or integer.\n */\nfunction toInteger(value: string|number): number {\n  return typeof value === 'number' ? value : parseInt(value, 10);\n}\n\n/**\n * Method that ensures that provided value is a float (and converts it to float if needed).\n *\n * @param value The value to convert to float.\n * @returns value of parameter converted to number or float.\n */\nfunction toFloat(value: string|number): number {\n  return typeof value === 'number' ? value : parseFloat(value);\n}\n\n/**\n * @description\n * Defines the map of errors returned from failed validation checks.\n *\n * @publicApi\n */\nexport type ValidationErrors = {\n  [key: string]: any\n};\n\n/**\n * @description\n * An interface implemented by classes that perform synchronous validation.\n *\n * @usageNotes\n *\n * ### Provide a custom validator\n *\n * The following example implements the `Validator` interface to create a\n * validator directive with a custom error key.\n *\n * ```typescript\n * @Directive({\n *   selector: '[customValidator]',\n *   providers: [{provide: NG_VALIDATORS, useExisting: CustomValidatorDirective, multi: true}]\n * })\n * class CustomValidatorDirective implements Validator {\n *   validate(control: AbstractControl): ValidationErrors|null {\n *     return {'custom': true};\n *   }\n * }\n * ```\n *\n * @publicApi\n */\nexport interface Validator {\n  /**\n   * @description\n   * Method that performs synchronous validation against the provided control.\n   *\n   * @param control The control to validate against.\n   *\n   * @returns A map of validation errors if validation fails,\n   * otherwise null.\n   */\n  validate(control: AbstractControl): ValidationErrors|null;\n\n  /**\n   * @description\n   * Registers a callback function to call when the validator inputs change.\n   *\n   * @param fn The callback function\n   */\n  registerOnValidatorChange?(fn: () => void): void;\n}\n\n/**\n * A base class for Validator-based Directives. The class contains common logic shared across such\n * Directives.\n *\n * For internal use only, this class is not intended for use outside of the Forms package.\n */\n@Directive()\nabstract class AbstractValidatorDirective implements Validator, OnChanges {\n  private _validator: ValidatorFn = nullValidator;\n  private _onChange!: () => void;\n\n  /**\n   * A flag that tracks whether this validator is enabled.\n   *\n   * Marking it `internal` (vs `protected`), so that this flag can be used in host bindings of\n   * directive classes that extend this base class.\n   * @internal\n   */\n  _enabled?: boolean;\n\n  /**\n   * Name of an input that matches directive selector attribute (e.g. `minlength` for\n   * `MinLengthDirective`). An input with a given name might contain configuration information (like\n   * `minlength='10'`) or a flag that indicates whether validator should be enabled (like\n   * `[required]='false'`).\n   *\n   * @internal\n   */\n  abstract inputName: string;\n\n  /**\n   * Creates an instance of a validator (specific to a directive that extends this base class).\n   *\n   * @internal\n   */\n  abstract createValidator(input: unknown): ValidatorFn;\n\n  /**\n   * Performs the necessary input normalization based on a specific logic of a Directive.\n   * For example, the function might be used to convert string-based representation of the\n   * `minlength` input to an integer value that can later be used in the `Validators.minLength`\n   * validator.\n   *\n   * @internal\n   */\n  abstract normalizeInput(input: unknown): unknown;\n\n  /** @nodoc */\n  ngOnChanges(changes: SimpleChanges): void {\n    if (this.inputName in changes) {\n      const input = this.normalizeInput(changes[this.inputName].currentValue);\n      this._enabled = this.enabled(input);\n      this._validator = this._enabled ? this.createValidator(input) : nullValidator;\n      if (this._onChange) {\n        this._onChange();\n      }\n    }\n  }\n\n  /** @nodoc */\n  validate(control: AbstractControl): ValidationErrors|null {\n    return this._validator(control);\n  }\n\n  /** @nodoc */\n  registerOnValidatorChange(fn: () => void): void {\n    this._onChange = fn;\n  }\n\n  /**\n   * @description\n   * Determines whether this validator should be active or not based on an input.\n   * Base class implementation checks whether an input is defined (if the value is different from\n   * `null` and `undefined`). Validator classes that extend this base class can override this\n   * function with the logic specific to a particular validator directive.\n   */\n  enabled(input: unknown): boolean {\n    return input != null /* both `null` and `undefined` */;\n  }\n}\n\n/**\n * @description\n * Provider which adds `MaxValidator` to the `NG_VALIDATORS` multi-provider list.\n */\nexport const MAX_VALIDATOR: Provider = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => MaxValidator),\n  multi: true\n};\n\n/**\n * A directive which installs the {@link MaxValidator} for any `formControlName`,\n * `formControl`, or control with `ngModel` that also has a `max` attribute.\n *\n * @see [Form Validation](guide/form-validation)\n *\n * @usageNotes\n *\n * ### Adding a max validator\n *\n * The following example shows how to add a max validator to an input attached to an\n * ngModel binding.\n *\n * ```html\n * <input type=\"number\" ngModel max=\"4\">\n * ```\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n@Directive({\n  selector:\n      'input[type=number][max][formControlName],input[type=number][max][formControl],input[type=number][max][ngModel]',\n  providers: [MAX_VALIDATOR],\n  host: {'[attr.max]': '_enabled ? max : null'}\n})\nexport class MaxValidator extends AbstractValidatorDirective {\n  /**\n   * @description\n   * Tracks changes to the max bound to this directive.\n   */\n  @Input() max!: string|number|null;\n  /** @internal */\n  override inputName = 'max';\n  /** @internal */\n  override normalizeInput = (input: string|number): number => toFloat(input);\n  /** @internal */\n  override createValidator = (max: number): ValidatorFn => maxValidator(max);\n}\n\n/**\n * @description\n * Provider which adds `MinValidator` to the `NG_VALIDATORS` multi-provider list.\n */\nexport const MIN_VALIDATOR: Provider = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => MinValidator),\n  multi: true\n};\n\n/**\n * A directive which installs the {@link MinValidator} for any `formControlName`,\n * `formControl`, or control with `ngModel` that also has a `min` attribute.\n *\n * @see [Form Validation](guide/form-validation)\n *\n * @usageNotes\n *\n * ### Adding a min validator\n *\n * The following example shows how to add a min validator to an input attached to an\n * ngModel binding.\n *\n * ```html\n * <input type=\"number\" ngModel min=\"4\">\n * ```\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n@Directive({\n  selector:\n      'input[type=number][min][formControlName],input[type=number][min][formControl],input[type=number][min][ngModel]',\n  providers: [MIN_VALIDATOR],\n  host: {'[attr.min]': '_enabled ? min : null'}\n})\nexport class MinValidator extends AbstractValidatorDirective {\n  /**\n   * @description\n   * Tracks changes to the min bound to this directive.\n   */\n  @Input() min!: string|number|null;\n  /** @internal */\n  override inputName = 'min';\n  /** @internal */\n  override normalizeInput = (input: string|number): number => toFloat(input);\n  /** @internal */\n  override createValidator = (min: number): ValidatorFn => minValidator(min);\n}\n\n/**\n * @description\n * An interface implemented by classes that perform asynchronous validation.\n *\n * @usageNotes\n *\n * ### Provide a custom async validator directive\n *\n * The following example implements the `AsyncValidator` interface to create an\n * async validator directive with a custom error key.\n *\n * ```typescript\n * import { of } from 'rxjs';\n *\n * @Directive({\n *   selector: '[customAsyncValidator]',\n *   providers: [{provide: NG_ASYNC_VALIDATORS, useExisting: CustomAsyncValidatorDirective, multi:\n * true}]\n * })\n * class CustomAsyncValidatorDirective implements AsyncValidator {\n *   validate(control: AbstractControl): Observable<ValidationErrors|null> {\n *     return of({'custom': true});\n *   }\n * }\n * ```\n *\n * @publicApi\n */\nexport interface AsyncValidator extends Validator {\n  /**\n   * @description\n   * Method that performs async validation against the provided control.\n   *\n   * @param control The control to validate against.\n   *\n   * @returns A promise or observable that resolves a map of validation errors\n   * if validation fails, otherwise null.\n   */\n  validate(control: AbstractControl):\n      Promise<ValidationErrors|null>|Observable<ValidationErrors|null>;\n}\n\n/**\n * @description\n * Provider which adds `RequiredValidator` to the `NG_VALIDATORS` multi-provider list.\n */\nexport const REQUIRED_VALIDATOR: Provider = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => RequiredValidator),\n  multi: true\n};\n\n/**\n * @description\n * Provider which adds `CheckboxRequiredValidator` to the `NG_VALIDATORS` multi-provider list.\n */\nexport const CHECKBOX_REQUIRED_VALIDATOR: Provider = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => CheckboxRequiredValidator),\n  multi: true\n};\n\n\n/**\n * @description\n * A directive that adds the `required` validator to any controls marked with the\n * `required` attribute. The directive is provided with the `NG_VALIDATORS` multi-provider list.\n *\n * @see [Form Validation](guide/form-validation)\n *\n * @usageNotes\n *\n * ### Adding a required validator using template-driven forms\n *\n * ```\n * <input name=\"fullName\" ngModel required>\n * ```\n *\n * @ngModule FormsModule\n * @ngModule ReactiveFormsModule\n * @publicApi\n */\n@Directive({\n  selector:\n      ':not([type=checkbox])[required][formControlName],:not([type=checkbox])[required][formControl],:not([type=checkbox])[required][ngModel]',\n  providers: [REQUIRED_VALIDATOR],\n  host: {'[attr.required]': '_enabled ? \"\" : null'}\n})\nexport class RequiredValidator extends AbstractValidatorDirective {\n  /**\n   * @description\n   * Tracks changes to the required attribute bound to this directive.\n   */\n  @Input() required!: boolean|string;\n\n  /** @internal */\n  override inputName = 'required';\n\n  /** @internal */\n  override normalizeInput = booleanAttribute;\n\n  /** @internal */\n  override createValidator = (input: boolean): ValidatorFn => requiredValidator;\n\n  /** @nodoc */\n  override enabled(input: boolean): boolean {\n    return input;\n  }\n}\n\n\n/**\n * A Directive that adds the `required` validator to checkbox controls marked with the\n * `required` attribute. The directive is provided with the `NG_VALIDATORS` multi-provider list.\n *\n * @see [Form Validation](guide/form-validation)\n *\n * @usageNotes\n *\n * ### Adding a required checkbox validator using template-driven forms\n *\n * The following example shows how to add a checkbox required validator to an input attached to an\n * ngModel binding.\n *\n * ```\n * <input type=\"checkbox\" name=\"active\" ngModel required>\n * ```\n *\n * @publicApi\n * @ngModule FormsModule\n * @ngModule ReactiveFormsModule\n */\n@Directive({\n  selector:\n      'input[type=checkbox][required][formControlName],input[type=checkbox][required][formControl],input[type=checkbox][required][ngModel]',\n  providers: [CHECKBOX_REQUIRED_VALIDATOR],\n  host: {'[attr.required]': '_enabled ? \"\" : null'}\n})\nexport class CheckboxRequiredValidator extends RequiredValidator {\n  /** @internal */\n  override createValidator = (input: unknown): ValidatorFn => requiredTrueValidator;\n}\n\n/**\n * @description\n * Provider which adds `EmailValidator` to the `NG_VALIDATORS` multi-provider list.\n */\nexport const EMAIL_VALIDATOR: any = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => EmailValidator),\n  multi: true\n};\n\n/**\n * A directive that adds the `email` validator to controls marked with the\n * `email` attribute. The directive is provided with the `NG_VALIDATORS` multi-provider list.\n *\n * The email validation is based on the WHATWG HTML specification with some enhancements to\n * incorporate more RFC rules. More information can be found on the [Validators.email\n * page](api/forms/Validators#email).\n *\n * @see [Form Validation](guide/form-validation)\n *\n * @usageNotes\n *\n * ### Adding an email validator\n *\n * The following example shows how to add an email validator to an input attached to an ngModel\n * binding.\n *\n * ```\n * <input type=\"email\" name=\"email\" ngModel email>\n * <input type=\"email\" name=\"email\" ngModel email=\"true\">\n * <input type=\"email\" name=\"email\" ngModel [email]=\"true\">\n * ```\n *\n * @publicApi\n * @ngModule FormsModule\n * @ngModule ReactiveFormsModule\n */\n@Directive({\n  selector: '[email][formControlName],[email][formControl],[email][ngModel]',\n  providers: [EMAIL_VALIDATOR]\n})\nexport class EmailValidator extends AbstractValidatorDirective {\n  /**\n   * @description\n   * Tracks changes to the email attribute bound to this directive.\n   */\n  @Input() email!: boolean|string;\n\n  /** @internal */\n  override inputName = 'email';\n\n  /** @internal */\n  override normalizeInput = booleanAttribute;\n\n  /** @internal */\n  override createValidator = (input: number): ValidatorFn => emailValidator;\n\n  /** @nodoc */\n  override enabled(input: boolean): boolean {\n    return input;\n  }\n}\n\n/**\n * @description\n * A function that receives a control and synchronously returns a map of\n * validation errors if present, otherwise null.\n *\n * @publicApi\n */\nexport interface ValidatorFn {\n  (control: AbstractControl): ValidationErrors|null;\n}\n\n/**\n * @description\n * A function that receives a control and returns a Promise or observable\n * that emits validation errors if present, otherwise null.\n *\n * @publicApi\n */\nexport interface AsyncValidatorFn {\n  (control: AbstractControl): Promise<ValidationErrors|null>|Observable<ValidationErrors|null>;\n}\n\n/**\n * @description\n * Provider which adds `MinLengthValidator` to the `NG_VALIDATORS` multi-provider list.\n */\nexport const MIN_LENGTH_VALIDATOR: any = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => MinLengthValidator),\n  multi: true\n};\n\n/**\n * A directive that adds minimum length validation to controls marked with the\n * `minlength` attribute. The directive is provided with the `NG_VALIDATORS` multi-provider list.\n *\n * @see [Form Validation](guide/form-validation)\n *\n * @usageNotes\n *\n * ### Adding a minimum length validator\n *\n * The following example shows how to add a minimum length validator to an input attached to an\n * ngModel binding.\n *\n * ```html\n * <input name=\"firstName\" ngModel minlength=\"4\">\n * ```\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n@Directive({\n  selector: '[minlength][formControlName],[minlength][formControl],[minlength][ngModel]',\n  providers: [MIN_LENGTH_VALIDATOR],\n  host: {'[attr.minlength]': '_enabled ? minlength : null'}\n})\nexport class MinLengthValidator extends AbstractValidatorDirective {\n  /**\n   * @description\n   * Tracks changes to the minimum length bound to this directive.\n   */\n  @Input() minlength!: string|number|null;\n\n  /** @internal */\n  override inputName = 'minlength';\n\n  /** @internal */\n  override normalizeInput = (input: string|number): number => toInteger(input);\n\n  /** @internal */\n  override createValidator = (minlength: number): ValidatorFn => minLengthValidator(minlength);\n}\n\n/**\n * @description\n * Provider which adds `MaxLengthValidator` to the `NG_VALIDATORS` multi-provider list.\n */\nexport const MAX_LENGTH_VALIDATOR: any = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => MaxLengthValidator),\n  multi: true\n};\n\n/**\n * A directive that adds maximum length validation to controls marked with the\n * `maxlength` attribute. The directive is provided with the `NG_VALIDATORS` multi-provider list.\n *\n * @see [Form Validation](guide/form-validation)\n *\n * @usageNotes\n *\n * ### Adding a maximum length validator\n *\n * The following example shows how to add a maximum length validator to an input attached to an\n * ngModel binding.\n *\n * ```html\n * <input name=\"firstName\" ngModel maxlength=\"25\">\n * ```\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n@Directive({\n  selector: '[maxlength][formControlName],[maxlength][formControl],[maxlength][ngModel]',\n  providers: [MAX_LENGTH_VALIDATOR],\n  host: {'[attr.maxlength]': '_enabled ? maxlength : null'}\n})\nexport class MaxLengthValidator extends AbstractValidatorDirective {\n  /**\n   * @description\n   * Tracks changes to the maximum length bound to this directive.\n   */\n  @Input() maxlength!: string|number|null;\n\n  /** @internal */\n  override inputName = 'maxlength';\n\n  /** @internal */\n  override normalizeInput = (input: string|number): number => toInteger(input);\n\n  /** @internal */\n  override createValidator = (maxlength: number): ValidatorFn => maxLengthValidator(maxlength);\n}\n\n/**\n * @description\n * Provider which adds `PatternValidator` to the `NG_VALIDATORS` multi-provider list.\n */\nexport const PATTERN_VALIDATOR: any = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => PatternValidator),\n  multi: true\n};\n\n\n/**\n * @description\n * A directive that adds regex pattern validation to controls marked with the\n * `pattern` attribute. The regex must match the entire control value.\n * The directive is provided with the `NG_VALIDATORS` multi-provider list.\n *\n * @see [Form Validation](guide/form-validation)\n *\n * @usageNotes\n *\n * ### Adding a pattern validator\n *\n * The following example shows how to add a pattern validator to an input attached to an\n * ngModel binding.\n *\n * ```html\n * <input name=\"firstName\" ngModel pattern=\"[a-zA-Z ]*\">\n * ```\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n@Directive({\n  selector: '[pattern][formControlName],[pattern][formControl],[pattern][ngModel]',\n  providers: [PATTERN_VALIDATOR],\n  host: {'[attr.pattern]': '_enabled ? pattern : null'}\n})\nexport class PatternValidator extends AbstractValidatorDirective {\n  /**\n   * @description\n   * Tracks changes to the pattern bound to this directive.\n   */\n  @Input()\n  pattern!: string|RegExp;  // This input is always defined, since the name matches selector.\n\n  /** @internal */\n  override inputName = 'pattern';\n\n  /** @internal */\n  override normalizeInput = (input: string|RegExp): string|RegExp => input;\n\n  /** @internal */\n  override createValidator = (input: string|RegExp): ValidatorFn => patternValidator(input);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule, Type} from '@angular/core';\n\nimport {CheckboxControlValueAccessor} from './directives/checkbox_value_accessor';\nimport {DefaultValueAccessor} from './directives/default_value_accessor';\nimport {NgControlStatus, NgControlStatusGroup} from './directives/ng_control_status';\nimport {NgForm} from './directives/ng_form';\nimport {NgModel} from './directives/ng_model';\nimport {NgModelGroup} from './directives/ng_model_group';\nimport {NgNoValidate} from './directives/ng_no_validate_directive';\nimport {NumberValueAccessor} from './directives/number_value_accessor';\nimport {RadioControlValueAccessor} from './directives/radio_control_value_accessor';\nimport {RangeValueAccessor} from './directives/range_value_accessor';\nimport {FormControlDirective} from './directives/reactive_directives/form_control_directive';\nimport {FormControlName} from './directives/reactive_directives/form_control_name';\nimport {FormGroupDirective} from './directives/reactive_directives/form_group_directive';\nimport {FormArrayName, FormGroupName} from './directives/reactive_directives/form_group_name';\nimport {NgSelectOption, SelectControlValueAccessor} from './directives/select_control_value_accessor';\nimport {NgSelectMultipleOption, SelectMultipleControlValueAccessor} from './directives/select_multiple_control_value_accessor';\nimport {CheckboxRequiredValidator, EmailValidator, MaxLengthValidator, MaxValidator, MinLengthValidator, MinValidator, PatternValidator, RequiredValidator} from './directives/validators';\n\nexport {CheckboxControlValueAccessor} from './directives/checkbox_value_accessor';\nexport {ControlValueAccessor} from './directives/control_value_accessor';\nexport {DefaultValueAccessor} from './directives/default_value_accessor';\nexport {NgControl} from './directives/ng_control';\nexport {NgControlStatus, NgControlStatusGroup} from './directives/ng_control_status';\nexport {NgForm} from './directives/ng_form';\nexport {NgModel} from './directives/ng_model';\nexport {NgModelGroup} from './directives/ng_model_group';\nexport {NumberValueAccessor} from './directives/number_value_accessor';\nexport {RadioControlValueAccessor} from './directives/radio_control_value_accessor';\nexport {RangeValueAccessor} from './directives/range_value_accessor';\nexport {FormControlDirective, NG_MODEL_WITH_FORM_CONTROL_WARNING} from './directives/reactive_directives/form_control_directive';\nexport {FormControlName} from './directives/reactive_directives/form_control_name';\nexport {FormGroupDirective} from './directives/reactive_directives/form_group_directive';\nexport {FormArrayName, FormGroupName} from './directives/reactive_directives/form_group_name';\nexport {NgSelectOption, SelectControlValueAccessor} from './directives/select_control_value_accessor';\nexport {NgSelectMultipleOption, SelectMultipleControlValueAccessor} from './directives/select_multiple_control_value_accessor';\nexport {CALL_SET_DISABLED_STATE} from './directives/shared';\n\nexport const SHARED_FORM_DIRECTIVES: Type<any>[] = [\n  NgNoValidate,\n  NgSelectOption,\n  NgSelectMultipleOption,\n  DefaultValueAccessor,\n  NumberValueAccessor,\n  RangeValueAccessor,\n  CheckboxControlValueAccessor,\n  SelectControlValueAccessor,\n  SelectMultipleControlValueAccessor,\n  RadioControlValueAccessor,\n  NgControlStatus,\n  NgControlStatusGroup,\n  RequiredValidator,\n  MinLengthValidator,\n  MaxLengthValidator,\n  PatternValidator,\n  CheckboxRequiredValidator,\n  EmailValidator,\n  MinValidator,\n  MaxValidator,\n];\n\nexport const TEMPLATE_DRIVEN_DIRECTIVES: Type<any>[] = [NgModel, NgModelGroup, NgForm];\n\nexport const REACTIVE_DRIVEN_DIRECTIVES: Type<any>[] =\n    [FormControlDirective, FormGroupDirective, FormControlName, FormGroupName, FormArrayName];\n\n/**\n * Internal module used for sharing directives between FormsModule and ReactiveFormsModule\n */\n@NgModule({\n  declarations: SHARED_FORM_DIRECTIVES,\n  exports: SHARED_FORM_DIRECTIVES,\n})\nexport class ɵInternalFormsSharedModule {\n}\n\nexport {ɵInternalFormsSharedModule as InternalFormsSharedModule};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵWritable as Writable} from '@angular/core';\n\nimport {AsyncValidatorFn, ValidatorFn} from '../directives/validators';\n\nimport {AbstractControl, AbstractControlOptions, assertAllValuesPresent, assertControlPresent, pickAsyncValidators, pickValidators, ɵRawValue, ɵTypedOrUntyped, ɵValue} from './abstract_model';\n\n/**\n * FormArrayValue extracts the type of `.value` from a FormArray's element type, and wraps it in an\n * array.\n *\n * <PERSON><PERSON> uses this type internally to support Typed Forms; do not use it directly. The untyped\n * case falls back to any[].\n */\nexport type ɵFormArrayValue<T extends AbstractControl<any>> =\n    ɵTypedOrUntyped<T, Array<ɵValue<T>>, any[]>;\n\n/**\n * FormArrayRawValue extracts the type of `.getRawValue()` from a FormArray's element type, and\n * wraps it in an array. The untyped case falls back to any[].\n *\n * Angular uses this type internally to support Typed Forms; do not use it directly.\n */\nexport type ɵFormArrayRawValue<T extends AbstractControl<any>> =\n    ɵTypedOrUntyped<T, Array<ɵRawValue<T>>, any[]>;\n\n/**\n * Tracks the value and validity state of an array of `FormControl`,\n * `FormGroup` or `FormArray` instances.\n *\n * A `FormArray` aggregates the values of each child `FormControl` into an array.\n * It calculates its status by reducing the status values of its children. For example, if one of\n * the controls in a `FormArray` is invalid, the entire array becomes invalid.\n *\n * `FormArray` accepts one generic argument, which is the type of the controls inside.\n * If you need a heterogenous array, use {@link UntypedFormArray}.\n *\n * `FormArray` is one of the four fundamental building blocks used to define forms in Angular,\n * along with `FormControl`, `FormGroup`, and `FormRecord`.\n *\n * @usageNotes\n *\n * ### Create an array of form controls\n *\n * ```\n * const arr = new FormArray([\n *   new FormControl('Nancy', Validators.minLength(2)),\n *   new FormControl('Drew'),\n * ]);\n *\n * console.log(arr.value);   // ['Nancy', 'Drew']\n * console.log(arr.status);  // 'VALID'\n * ```\n *\n * ### Create a form array with array-level validators\n *\n * You include array-level validators and async validators. These come in handy\n * when you want to perform validation that considers the value of more than one child\n * control.\n *\n * The two types of validators are passed in separately as the second and third arg\n * respectively, or together as part of an options object.\n *\n * ```\n * const arr = new FormArray([\n *   new FormControl('Nancy'),\n *   new FormControl('Drew')\n * ], {validators: myValidator, asyncValidators: myAsyncValidator});\n * ```\n *\n * ### Set the updateOn property for all controls in a form array\n *\n * The options object is used to set a default value for each child\n * control's `updateOn` property. If you set `updateOn` to `'blur'` at the\n * array level, all child controls default to 'blur', unless the child\n * has explicitly specified a different `updateOn` value.\n *\n * ```ts\n * const arr = new FormArray([\n *    new FormControl()\n * ], {updateOn: 'blur'});\n * ```\n *\n * ### Adding or removing controls from a form array\n *\n * To change the controls in the array, use the `push`, `insert`, `removeAt` or `clear` methods\n * in `FormArray` itself. These methods ensure the controls are properly tracked in the\n * form's hierarchy. Do not modify the array of `AbstractControl`s used to instantiate\n * the `FormArray` directly, as that result in strange and unexpected behavior such\n * as broken change detection.\n *\n * @publicApi\n */\nexport class FormArray<TControl extends AbstractControl<any> = any> extends AbstractControl<\n    ɵTypedOrUntyped<TControl, ɵFormArrayValue<TControl>, any>,\n    ɵTypedOrUntyped<TControl, ɵFormArrayRawValue<TControl>, any>> {\n  /**\n   * Creates a new `FormArray` instance.\n   *\n   * @param controls An array of child controls. Each child control is given an index\n   * where it is registered.\n   *\n   * @param validatorOrOpts A synchronous validator function, or an array of\n   * such functions, or an `AbstractControlOptions` object that contains validation functions\n   * and a validation trigger.\n   *\n   * @param asyncValidator A single async validator or array of async validator functions\n   *\n   */\n  constructor(\n      controls: Array<TControl>,\n      validatorOrOpts?: ValidatorFn|ValidatorFn[]|AbstractControlOptions|null,\n      asyncValidator?: AsyncValidatorFn|AsyncValidatorFn[]|null) {\n    super(pickValidators(validatorOrOpts), pickAsyncValidators(asyncValidator, validatorOrOpts));\n    this.controls = controls;\n    this._initObservables();\n    this._setUpdateStrategy(validatorOrOpts);\n    this._setUpControls();\n    this.updateValueAndValidity({\n      onlySelf: true,\n      // If `asyncValidator` is present, it will trigger control status change from `PENDING` to\n      // `VALID` or `INVALID`.\n      // The status should be broadcasted via the `statusChanges` observable, so we set `emitEvent`\n      // to `true` to allow that during the control creation process.\n      emitEvent: !!this.asyncValidator\n    });\n  }\n\n  public controls: ɵTypedOrUntyped<TControl, Array<TControl>, Array<AbstractControl<any>>>;\n\n  /**\n   * Get the `AbstractControl` at the given `index` in the array.\n   *\n   * @param index Index in the array to retrieve the control. If `index` is negative, it will wrap\n   *     around from the back, and if index is greatly negative (less than `-length`), the result is\n   * undefined. This behavior is the same as `Array.at(index)`.\n   */\n  at(index: number): ɵTypedOrUntyped<TControl, TControl, AbstractControl<any>> {\n    return (this.controls as any)[this._adjustIndex(index)];\n  }\n\n  /**\n   * Insert a new `AbstractControl` at the end of the array.\n   *\n   * @param control Form control to be inserted\n   * @param options Specifies whether this FormArray instance should emit events after a new\n   *     control is added.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges` observables emit events with the latest status and value when the control is\n   * inserted. When false, no events are emitted.\n   */\n  push(control: TControl, options: {emitEvent?: boolean} = {}): void {\n    this.controls.push(control);\n    this._registerControl(control);\n    this.updateValueAndValidity({emitEvent: options.emitEvent});\n    this._onCollectionChange();\n  }\n\n  /**\n   * Insert a new `AbstractControl` at the given `index` in the array.\n   *\n   * @param index Index in the array to insert the control. If `index` is negative, wraps around\n   *     from the back. If `index` is greatly negative (less than `-length`), prepends to the array.\n   * This behavior is the same as `Array.splice(index, 0, control)`.\n   * @param control Form control to be inserted\n   * @param options Specifies whether this FormArray instance should emit events after a new\n   *     control is inserted.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges` observables emit events with the latest status and value when the control is\n   * inserted. When false, no events are emitted.\n   */\n  insert(index: number, control: TControl, options: {emitEvent?: boolean} = {}): void {\n    this.controls.splice(index, 0, control);\n\n    this._registerControl(control);\n    this.updateValueAndValidity({emitEvent: options.emitEvent});\n  }\n\n  /**\n   * Remove the control at the given `index` in the array.\n   *\n   * @param index Index in the array to remove the control.  If `index` is negative, wraps around\n   *     from the back. If `index` is greatly negative (less than `-length`), removes the first\n   *     element. This behavior is the same as `Array.splice(index, 1)`.\n   * @param options Specifies whether this FormArray instance should emit events after a\n   *     control is removed.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges` observables emit events with the latest status and value when the control is\n   * removed. When false, no events are emitted.\n   */\n  removeAt(index: number, options: {emitEvent?: boolean} = {}): void {\n    // Adjust the index, then clamp it at no less than 0 to prevent undesired underflows.\n    let adjustedIndex = this._adjustIndex(index);\n    if (adjustedIndex < 0) adjustedIndex = 0;\n\n    if (this.controls[adjustedIndex])\n      this.controls[adjustedIndex]._registerOnCollectionChange(() => {});\n    this.controls.splice(adjustedIndex, 1);\n    this.updateValueAndValidity({emitEvent: options.emitEvent});\n  }\n\n  /**\n   * Replace an existing control.\n   *\n   * @param index Index in the array to replace the control. If `index` is negative, wraps around\n   *     from the back. If `index` is greatly negative (less than `-length`), replaces the first\n   *     element. This behavior is the same as `Array.splice(index, 1, control)`.\n   * @param control The `AbstractControl` control to replace the existing control\n   * @param options Specifies whether this FormArray instance should emit events after an\n   *     existing control is replaced with a new one.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges` observables emit events with the latest status and value when the control is\n   * replaced with a new one. When false, no events are emitted.\n   */\n  setControl(index: number, control: TControl, options: {emitEvent?: boolean} = {}): void {\n    // Adjust the index, then clamp it at no less than 0 to prevent undesired underflows.\n    let adjustedIndex = this._adjustIndex(index);\n    if (adjustedIndex < 0) adjustedIndex = 0;\n\n    if (this.controls[adjustedIndex])\n      this.controls[adjustedIndex]._registerOnCollectionChange(() => {});\n    this.controls.splice(adjustedIndex, 1);\n\n    if (control) {\n      this.controls.splice(adjustedIndex, 0, control);\n      this._registerControl(control);\n    }\n\n    this.updateValueAndValidity({emitEvent: options.emitEvent});\n    this._onCollectionChange();\n  }\n\n  /**\n   * Length of the control array.\n   */\n  get length(): number {\n    return this.controls.length;\n  }\n\n  /**\n   * Sets the value of the `FormArray`. It accepts an array that matches\n   * the structure of the control.\n   *\n   * This method performs strict checks, and throws an error if you try\n   * to set the value of a control that doesn't exist or if you exclude the\n   * value of a control.\n   *\n   * @usageNotes\n   * ### Set the values for the controls in the form array\n   *\n   * ```\n   * const arr = new FormArray([\n   *   new FormControl(),\n   *   new FormControl()\n   * ]);\n   * console.log(arr.value);   // [null, null]\n   *\n   * arr.setValue(['Nancy', 'Drew']);\n   * console.log(arr.value);   // ['Nancy', 'Drew']\n   * ```\n   *\n   * @param value Array of values for the controls\n   * @param options Configure options that determine how the control propagates changes and\n   * emits events after the value changes\n   *\n   * * `onlySelf`: When true, each change only affects this control, and not its parent. Default\n   * is false.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges`\n   * observables emit events with the latest status and value when the control value is updated.\n   * When false, no events are emitted.\n   * The configuration options are passed to the {@link AbstractControl#updateValueAndValidity\n   * updateValueAndValidity} method.\n   */\n  override setValue(value: ɵFormArrayRawValue<TControl>, options: {\n    onlySelf?: boolean,\n    emitEvent?: boolean\n  } = {}): void {\n    assertAllValuesPresent(this, false, value);\n    value.forEach((newValue: any, index: number) => {\n      assertControlPresent(this, false, index);\n      this.at(index).setValue(newValue, {onlySelf: true, emitEvent: options.emitEvent});\n    });\n    this.updateValueAndValidity(options);\n  }\n\n  /**\n   * Patches the value of the `FormArray`. It accepts an array that matches the\n   * structure of the control, and does its best to match the values to the correct\n   * controls in the group.\n   *\n   * It accepts both super-sets and sub-sets of the array without throwing an error.\n   *\n   * @usageNotes\n   * ### Patch the values for controls in a form array\n   *\n   * ```\n   * const arr = new FormArray([\n   *    new FormControl(),\n   *    new FormControl()\n   * ]);\n   * console.log(arr.value);   // [null, null]\n   *\n   * arr.patchValue(['Nancy']);\n   * console.log(arr.value);   // ['Nancy', null]\n   * ```\n   *\n   * @param value Array of latest values for the controls\n   * @param options Configure options that determine how the control propagates changes and\n   * emits events after the value changes\n   *\n   * * `onlySelf`: When true, each change only affects this control, and not its parent. Default\n   * is false.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges` observables emit events with the latest status and value when the control\n   * value is updated. When false, no events are emitted. The configuration options are passed to\n   * the {@link AbstractControl#updateValueAndValidity updateValueAndValidity} method.\n   */\n  override patchValue(value: ɵFormArrayValue<TControl>, options: {\n    onlySelf?: boolean,\n    emitEvent?: boolean\n  } = {}): void {\n    // Even though the `value` argument type doesn't allow `null` and `undefined` values, the\n    // `patchValue` can be called recursively and inner data structures might have these values,\n    // so we just ignore such cases when a field containing FormArray instance receives `null` or\n    // `undefined` as a value.\n    if (value == null /* both `null` and `undefined` */) return;\n\n    value.forEach((newValue, index) => {\n      if (this.at(index)) {\n        this.at(index).patchValue(newValue, {onlySelf: true, emitEvent: options.emitEvent});\n      }\n    });\n    this.updateValueAndValidity(options);\n  }\n\n  /**\n   * Resets the `FormArray` and all descendants are marked `pristine` and `untouched`, and the\n   * value of all descendants to null or null maps.\n   *\n   * You reset to a specific form state by passing in an array of states\n   * that matches the structure of the control. The state is a standalone value\n   * or a form state object with both a value and a disabled status.\n   *\n   * @usageNotes\n   * ### Reset the values in a form array\n   *\n   * ```ts\n   * const arr = new FormArray([\n   *    new FormControl(),\n   *    new FormControl()\n   * ]);\n   * arr.reset(['name', 'last name']);\n   *\n   * console.log(arr.value);  // ['name', 'last name']\n   * ```\n   *\n   * ### Reset the values in a form array and the disabled status for the first control\n   *\n   * ```\n   * arr.reset([\n   *   {value: 'name', disabled: true},\n   *   'last'\n   * ]);\n   *\n   * console.log(arr.value);  // ['last']\n   * console.log(arr.at(0).status);  // 'DISABLED'\n   * ```\n   *\n   * @param value Array of values for the controls\n   * @param options Configure options that determine how the control propagates changes and\n   * emits events after the value changes\n   *\n   * * `onlySelf`: When true, each change only affects this control, and not its parent. Default\n   * is false.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges`\n   * observables emit events with the latest status and value when the control is reset.\n   * When false, no events are emitted.\n   * The configuration options are passed to the {@link AbstractControl#updateValueAndValidity\n   * updateValueAndValidity} method.\n   */\n  override reset(value: ɵTypedOrUntyped<TControl, ɵFormArrayValue<TControl>, any> = [], options: {\n    onlySelf?: boolean,\n    emitEvent?: boolean\n  } = {}): void {\n    this._forEachChild((control: AbstractControl, index: number) => {\n      control.reset(value[index], {onlySelf: true, emitEvent: options.emitEvent});\n    });\n    this._updatePristine(options);\n    this._updateTouched(options);\n    this.updateValueAndValidity(options);\n  }\n\n  /**\n   * The aggregate value of the array, including any disabled controls.\n   *\n   * Reports all values regardless of disabled status.\n   */\n  override getRawValue(): ɵFormArrayRawValue<TControl> {\n    return this.controls.map((control: AbstractControl) => control.getRawValue());\n  }\n\n  /**\n   * Remove all controls in the `FormArray`.\n   *\n   * @param options Specifies whether this FormArray instance should emit events after all\n   *     controls are removed.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges` observables emit events with the latest status and value when all controls\n   * in this FormArray instance are removed. When false, no events are emitted.\n   *\n   * @usageNotes\n   * ### Remove all elements from a FormArray\n   *\n   * ```ts\n   * const arr = new FormArray([\n   *    new FormControl(),\n   *    new FormControl()\n   * ]);\n   * console.log(arr.length);  // 2\n   *\n   * arr.clear();\n   * console.log(arr.length);  // 0\n   * ```\n   *\n   * It's a simpler and more efficient alternative to removing all elements one by one:\n   *\n   * ```ts\n   * const arr = new FormArray([\n   *    new FormControl(),\n   *    new FormControl()\n   * ]);\n   *\n   * while (arr.length) {\n   *    arr.removeAt(0);\n   * }\n   * ```\n   */\n  clear(options: {emitEvent?: boolean} = {}): void {\n    if (this.controls.length < 1) return;\n    this._forEachChild((control) => control._registerOnCollectionChange(() => {}));\n    this.controls.splice(0);\n    this.updateValueAndValidity({emitEvent: options.emitEvent});\n  }\n\n  /**\n   * Adjusts a negative index by summing it with the length of the array. For very negative\n   * indices, the result may remain negative.\n   * @internal\n   */\n  private _adjustIndex(index: number): number {\n    return index < 0 ? index + this.length : index;\n  }\n\n  /** @internal */\n  override _syncPendingControls(): boolean {\n    let subtreeUpdated = (this.controls as any).reduce((updated: any, child: any) => {\n      return child._syncPendingControls() ? true : updated;\n    }, false);\n    if (subtreeUpdated) this.updateValueAndValidity({onlySelf: true});\n    return subtreeUpdated;\n  }\n\n  /** @internal */\n  override _forEachChild(cb: (c: AbstractControl, index: number) => void): void {\n    this.controls.forEach((control: AbstractControl, index: number) => {\n      cb(control, index);\n    });\n  }\n\n  /** @internal */\n  override _updateValue(): void {\n    (this as Writable<this>).value =\n        this.controls.filter((control) => control.enabled || this.disabled)\n            .map((control) => control.value);\n  }\n\n  /** @internal */\n  override _anyControls(condition: (c: AbstractControl) => boolean): boolean {\n    return this.controls.some((control) => control.enabled && condition(control));\n  }\n\n  /** @internal */\n  _setUpControls(): void {\n    this._forEachChild((control) => this._registerControl(control));\n  }\n\n  /** @internal */\n  override _allControlsDisabled(): boolean {\n    for (const control of this.controls) {\n      if (control.enabled) return false;\n    }\n    return this.controls.length > 0 || this.disabled;\n  }\n\n  private _registerControl(control: AbstractControl) {\n    control.setParent(this);\n    control._registerOnCollectionChange(this._onCollectionChange);\n  }\n\n  /** @internal */\n  override _find(name: string|number): AbstractControl|null {\n    return this.at(name as number) ?? null;\n  }\n}\n\ninterface UntypedFormArrayCtor {\n  new(controls: AbstractControl[],\n      validatorOrOpts?: ValidatorFn|ValidatorFn[]|AbstractControlOptions|null,\n      asyncValidator?: AsyncValidatorFn|AsyncValidatorFn[]|null): UntypedFormArray;\n\n  /**\n   * The presence of an explicit `prototype` property provides backwards-compatibility for apps that\n   * manually inspect the prototype chain.\n   */\n  prototype: FormArray<any>;\n}\n\n/**\n * UntypedFormArray is a non-strongly-typed version of `FormArray`, which\n * permits heterogenous controls.\n */\nexport type UntypedFormArray = FormArray<any>;\n\nexport const UntypedFormArray: UntypedFormArrayCtor = FormArray;\n\n/**\n * @description\n * Asserts that the given control is an instance of `FormArray`\n *\n * @publicApi\n */\nexport const isFormArray = (control: unknown): control is FormArray => control instanceof FormArray;\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {inject, Injectable} from '@angular/core';\n\nimport {AsyncValidatorFn, ValidatorFn} from './directives/validators';\nimport {AbstractControl, AbstractControlOptions, FormHooks} from './model/abstract_model';\nimport {FormArray, UntypedFormArray} from './model/form_array';\nimport {FormControl, FormControlOptions, FormControlState, UntypedFormControl} from './model/form_control';\nimport {FormGroup, FormRecord, UntypedFormGroup} from './model/form_group';\n\nfunction isAbstractControlOptions(options: AbstractControlOptions|{[key: string]: any}|null|\n                                  undefined): options is AbstractControlOptions {\n  return !!options &&\n      ((options as AbstractControlOptions).asyncValidators !== undefined ||\n       (options as AbstractControlOptions).validators !== undefined ||\n       (options as AbstractControlOptions).updateOn !== undefined);\n}\n\n/**\n * The union of all validator types that can be accepted by a ControlConfig.\n */\ntype ValidatorConfig = ValidatorFn|AsyncValidatorFn|ValidatorFn[]|AsyncValidatorFn[];\n\n/**\n * The compiler may not always be able to prove that the elements of the control config are a tuple\n * (i.e. occur in a fixed order). This slightly looser type is used for inference, to catch cases\n * where the compiler cannot prove order and position.\n *\n * For example, consider the simple case `fb.group({foo: ['bar', Validators.required]})`. The\n * compiler will infer this as an array, not as a tuple.\n */\ntype PermissiveControlConfig<T> = Array<T|FormControlState<T>|ValidatorConfig>;\n\n/**\n * Helper type to allow the compiler to accept [XXXX, { updateOn: string }] as a valid shorthand\n * argument for .group()\n */\ninterface PermissiveAbstractControlOptions extends Omit<AbstractControlOptions, 'updateOn'> {\n  updateOn?: string;\n}\n\n/**\n * ControlConfig<T> is a tuple containing a value of type T, plus optional validators and async\n * validators.\n *\n * @publicApi\n */\nexport type ControlConfig<T> = [T|FormControlState<T>, (ValidatorFn|(ValidatorFn[]))?, (AsyncValidatorFn|AsyncValidatorFn[])?];\n\n// Disable clang-format to produce clearer formatting for this multiline type.\n// clang-format off\n\n/**\n * FormBuilder accepts values in various container shapes, as well as raw values.\n * Element returns the appropriate corresponding model class, given the container T.\n * The flag N, if not never, makes the resulting `FormControl` have N in its type.\n */\nexport type ɵElement<T, N extends null> =\n  // The `extends` checks are wrapped in arrays in order to prevent TypeScript from applying type unions\n  // through the distributive conditional type. This is the officially recommended solution:\n  // https://www.typescriptlang.org/docs/handbook/2/conditional-types.html#distributive-conditional-types\n  //\n  // Identify FormControl container types.\n  [T] extends [FormControl<infer U>] ? FormControl<U> :\n  // Or FormControl containers that are optional in their parent group.\n  [T] extends [FormControl<infer U>|undefined] ? FormControl<U> :\n  // FormGroup containers.\n  [T] extends [FormGroup<infer U>] ? FormGroup<U> :\n  // Optional FormGroup containers.\n  [T] extends [FormGroup<infer U>|undefined] ? FormGroup<U> :\n  // FormRecord containers.\n  [T] extends [FormRecord<infer U>] ? FormRecord<U> :\n  // Optional FormRecord containers.\n  [T] extends [FormRecord<infer U>|undefined] ? FormRecord<U> :\n  // FormArray containers.\n  [T] extends [FormArray<infer U>] ? FormArray<U> :\n  // Optional FormArray containers.\n  [T] extends [FormArray<infer U>|undefined] ? FormArray<U> :\n  // Otherwise unknown AbstractControl containers.\n  [T] extends [AbstractControl<infer U>] ? AbstractControl<U> :\n  // Optional AbstractControl containers.\n  [T] extends [AbstractControl<infer U>|undefined] ? AbstractControl<U> :\n  // FormControlState object container, which produces a nullable control.\n  [T] extends [FormControlState<infer U>] ? FormControl<U|N> :\n  // A ControlConfig tuple, which produces a nullable control.\n  [T] extends [PermissiveControlConfig<infer U>] ? FormControl<Exclude<U, ValidatorConfig| PermissiveAbstractControlOptions>|N> :\n  FormControl<T|N>;\n\n// clang-format on\n\n/**\n * @description\n * Creates an `AbstractControl` from a user-specified configuration.\n *\n * The `FormBuilder` provides syntactic sugar that shortens creating instances of a\n * `FormControl`, `FormGroup`, or `FormArray`. It reduces the amount of boilerplate needed to\n * build complex forms.\n *\n * @see [Reactive Forms Guide](guide/reactive-forms)\n *\n * @publicApi\n */\n@Injectable({providedIn: 'root'})\nexport class FormBuilder {\n  private useNonNullable: boolean = false;\n\n  /**\n   * @description\n   * Returns a FormBuilder in which automatically constructed `FormControl` elements\n   * have `{nonNullable: true}` and are non-nullable.\n   *\n   * **Constructing non-nullable controls**\n   *\n   * When constructing a control, it will be non-nullable, and will reset to its initial value.\n   *\n   * ```ts\n   * let nnfb = new FormBuilder().nonNullable;\n   * let name = nnfb.control('Alex'); // FormControl<string>\n   * name.reset();\n   * console.log(name); // 'Alex'\n   * ```\n   *\n   * **Constructing non-nullable groups or arrays**\n   *\n   * When constructing a group or array, all automatically created inner controls will be\n   * non-nullable, and will reset to their initial values.\n   *\n   * ```ts\n   * let nnfb = new FormBuilder().nonNullable;\n   * let name = nnfb.group({who: 'Alex'}); // FormGroup<{who: FormControl<string>}>\n   * name.reset();\n   * console.log(name); // {who: 'Alex'}\n   * ```\n   * **Constructing *nullable* fields on groups or arrays**\n   *\n   * It is still possible to have a nullable field. In particular, any `FormControl` which is\n   * *already* constructed will not be altered. For example:\n   *\n   * ```ts\n   * let nnfb = new FormBuilder().nonNullable;\n   * // FormGroup<{who: FormControl<string|null>}>\n   * let name = nnfb.group({who: new FormControl('Alex')});\n   * name.reset(); console.log(name); // {who: null}\n   * ```\n   *\n   * Because the inner control is constructed explicitly by the caller, the builder has\n   * no control over how it is created, and cannot exclude the `null`.\n   */\n  get nonNullable(): NonNullableFormBuilder {\n    const nnfb = new FormBuilder();\n    nnfb.useNonNullable = true;\n    return nnfb as NonNullableFormBuilder;\n  }\n\n  /**\n   * @description\n   * Constructs a new `FormGroup` instance. Accepts a single generic argument, which is an object\n   * containing all the keys and corresponding inner control types.\n   *\n   * @param controls A collection of child controls. The key for each child is the name\n   * under which it is registered.\n   *\n   * @param options Configuration options object for the `FormGroup`. The object should have the\n   * `AbstractControlOptions` type and might contain the following fields:\n   * * `validators`: A synchronous validator function, or an array of validator functions.\n   * * `asyncValidators`: A single async validator or array of async validator functions.\n   * * `updateOn`: The event upon which the control should be updated (options: 'change' | 'blur'\n   * | submit').\n   */\n  group<T extends {}>(\n      controls: T,\n      options?: AbstractControlOptions|null,\n      ): FormGroup<{[K in keyof T]: ɵElement<T[K], null>}>;\n\n  /**\n   * @description\n   * Constructs a new `FormGroup` instance.\n   *\n   * @deprecated This API is not typesafe and can result in issues with Closure Compiler renaming.\n   * Use the `FormBuilder#group` overload with `AbstractControlOptions` instead.\n   * Note that `AbstractControlOptions` expects `validators` and `asyncValidators` to be valid\n   * validators. If you have custom validators, make sure their validation function parameter is\n   * `AbstractControl` and not a sub-class, such as `FormGroup`. These functions will be called\n   * with an object of type `AbstractControl` and that cannot be automatically downcast to a\n   * subclass, so TypeScript sees this as an error. For example, change the `(group: FormGroup) =>\n   * ValidationErrors|null` signature to be `(group: AbstractControl) => ValidationErrors|null`.\n   *\n   * @param controls A record of child controls. The key for each child is the name\n   * under which the control is registered.\n   *\n   * @param options Configuration options object for the `FormGroup`. The legacy configuration\n   * object consists of:\n   * * `validator`: A synchronous validator function, or an array of validator functions.\n   * * `asyncValidator`: A single async validator or array of async validator functions\n   * Note: the legacy format is deprecated and might be removed in one of the next major versions\n   * of Angular.\n   */\n  group(\n      controls: {[key: string]: any},\n      options: {[key: string]: any},\n      ): FormGroup;\n\n  group(controls: {[key: string]: any}, options: AbstractControlOptions|{[key: string]:\n                                                                             any}|null = null):\n      FormGroup {\n    const reducedControls = this._reduceControls(controls);\n    let newOptions: FormControlOptions = {};\n    if (isAbstractControlOptions(options)) {\n      // `options` are `AbstractControlOptions`\n      newOptions = options;\n    } else if (options !== null) {\n      // `options` are legacy form group options\n      newOptions.validators = (options as any).validator;\n      newOptions.asyncValidators = (options as any).asyncValidator;\n    }\n    return new FormGroup(reducedControls, newOptions);\n  }\n\n  /**\n   * @description\n   * Constructs a new `FormRecord` instance. Accepts a single generic argument, which is an object\n   * containing all the keys and corresponding inner control types.\n   *\n   * @param controls A collection of child controls. The key for each child is the name\n   * under which it is registered.\n   *\n   * @param options Configuration options object for the `FormRecord`. The object should have the\n   * `AbstractControlOptions` type and might contain the following fields:\n   * * `validators`: A synchronous validator function, or an array of validator functions.\n   * * `asyncValidators`: A single async validator or array of async validator functions.\n   * * `updateOn`: The event upon which the control should be updated (options: 'change' | 'blur'\n   * | submit').\n   */\n  record<T>(controls: {[key: string]: T}, options: AbstractControlOptions|null = null):\n      FormRecord<ɵElement<T, null>> {\n    const reducedControls = this._reduceControls(controls);\n    // Cast to `any` because the inferred types are not as specific as Element.\n    return new FormRecord(reducedControls, options) as any;\n  }\n\n  /** @deprecated Use `nonNullable` instead. */\n  control<T>(formState: T|FormControlState<T>, opts: FormControlOptions&{\n    initialValueIsDefault: true\n  }): FormControl<T>;\n\n  control<T>(formState: T|FormControlState<T>, opts: FormControlOptions&{nonNullable: true}):\n      FormControl<T>;\n\n  /**\n   * @deprecated When passing an `options` argument, the `asyncValidator` argument has no effect.\n   */\n  control<T>(\n      formState: T|FormControlState<T>, opts: FormControlOptions,\n      asyncValidator: AsyncValidatorFn|AsyncValidatorFn[]): FormControl<T|null>;\n\n  control<T>(\n      formState: T|FormControlState<T>,\n      validatorOrOpts?: ValidatorFn|ValidatorFn[]|FormControlOptions|null,\n      asyncValidator?: AsyncValidatorFn|AsyncValidatorFn[]|null): FormControl<T|null>;\n\n  /**\n   * @description\n   * Constructs a new `FormControl` with the given state, validators and options. Sets\n   * `{nonNullable: true}` in the options to get a non-nullable control. Otherwise, the\n   * control will be nullable. Accepts a single generic argument, which is the type  of the\n   * control's value.\n   *\n   * @param formState Initializes the control with an initial state value, or\n   * with an object that contains both a value and a disabled status.\n   *\n   * @param validatorOrOpts A synchronous validator function, or an array of\n   * such functions, or a `FormControlOptions` object that contains\n   * validation functions and a validation trigger.\n   *\n   * @param asyncValidator A single async validator or array of async validator\n   * functions.\n   *\n   * @usageNotes\n   *\n   * ### Initialize a control as disabled\n   *\n   * The following example returns a control with an initial value in a disabled state.\n   *\n   * <code-example path=\"forms/ts/formBuilder/form_builder_example.ts\" region=\"disabled-control\">\n   * </code-example>\n   */\n  control<T>(\n      formState: T|FormControlState<T>,\n      validatorOrOpts?: ValidatorFn|ValidatorFn[]|FormControlOptions|null,\n      asyncValidator?: AsyncValidatorFn|AsyncValidatorFn[]|null): FormControl {\n    let newOptions: FormControlOptions = {};\n    if (!this.useNonNullable) {\n      return new FormControl(formState, validatorOrOpts, asyncValidator);\n    }\n    if (isAbstractControlOptions(validatorOrOpts)) {\n      // If the second argument is options, then they are copied.\n      newOptions = validatorOrOpts;\n    } else {\n      // If the other arguments are validators, they are copied into an options object.\n      newOptions.validators = validatorOrOpts;\n      newOptions.asyncValidators = asyncValidator;\n    }\n    return new FormControl<T>(formState, {...newOptions, nonNullable: true});\n  }\n\n  /**\n   * Constructs a new `FormArray` from the given array of configurations,\n   * validators and options. Accepts a single generic argument, which is the type of each control\n   * inside the array.\n   *\n   * @param controls An array of child controls or control configs. Each child control is given an\n   *     index when it is registered.\n   *\n   * @param validatorOrOpts A synchronous validator function, or an array of such functions, or an\n   *     `AbstractControlOptions` object that contains\n   * validation functions and a validation trigger.\n   *\n   * @param asyncValidator A single async validator or array of async validator functions.\n   */\n  array<T>(\n      controls: Array<T>, validatorOrOpts?: ValidatorFn|ValidatorFn[]|AbstractControlOptions|null,\n      asyncValidator?: AsyncValidatorFn|AsyncValidatorFn[]|null): FormArray<ɵElement<T, null>> {\n    const createdControls = controls.map(c => this._createControl(c));\n    // Cast to `any` because the inferred types are not as specific as Element.\n    return new FormArray(createdControls, validatorOrOpts, asyncValidator) as any;\n  }\n\n  /** @internal */\n  _reduceControls<T>(controls:\n                         {[k: string]: T|ControlConfig<T>|FormControlState<T>|AbstractControl<T>}):\n      {[key: string]: AbstractControl} {\n    const createdControls: {[key: string]: AbstractControl} = {};\n    Object.keys(controls).forEach(controlName => {\n      createdControls[controlName] = this._createControl(controls[controlName]);\n    });\n    return createdControls;\n  }\n\n  /** @internal */\n  _createControl<T>(controls: T|FormControlState<T>|ControlConfig<T>|FormControl<T>|\n                    AbstractControl<T>): FormControl<T>|FormControl<T|null>|AbstractControl<T> {\n    if (controls instanceof FormControl) {\n      return controls as FormControl<T>;\n    } else if (controls instanceof AbstractControl) {  // A control; just return it\n      return controls;\n    } else if (Array.isArray(controls)) {  // ControlConfig Tuple\n      const value: T|FormControlState<T> = controls[0];\n      const validator: ValidatorFn|ValidatorFn[]|null = controls.length > 1 ? controls[1]! : null;\n      const asyncValidator: AsyncValidatorFn|AsyncValidatorFn[]|null =\n          controls.length > 2 ? controls[2]! : null;\n      return this.control<T>(value, validator, asyncValidator);\n    } else {  // T or FormControlState<T>\n      return this.control<T>(controls);\n    }\n  }\n}\n\n/**\n * @description\n * `NonNullableFormBuilder` is similar to {@link FormBuilder}, but automatically constructed\n * {@link FormControl} elements have `{nonNullable: true}` and are non-nullable.\n *\n * @publicApi\n */\n@Injectable({\n  providedIn: 'root',\n  useFactory: () => inject(FormBuilder).nonNullable,\n})\nexport abstract class NonNullableFormBuilder {\n  /**\n   * Similar to `FormBuilder#group`, except any implicitly constructed `FormControl`\n   * will be non-nullable (i.e. it will have `nonNullable` set to true). Note\n   * that already-constructed controls will not be altered.\n   */\n  abstract group<T extends {}>(\n      controls: T,\n      options?: AbstractControlOptions|null,\n      ): FormGroup<{[K in keyof T]: ɵElement<T[K], never>}>;\n\n  /**\n   * Similar to `FormBuilder#record`, except any implicitly constructed `FormControl`\n   * will be non-nullable (i.e. it will have `nonNullable` set to true). Note\n   * that already-constructed controls will not be altered.\n   */\n  abstract record<T>(\n      controls: {[key: string]: T},\n      options?: AbstractControlOptions|null,\n      ): FormRecord<ɵElement<T, never>>;\n\n  /**\n   * Similar to `FormBuilder#array`, except any implicitly constructed `FormControl`\n   * will be non-nullable (i.e. it will have `nonNullable` set to true). Note\n   * that already-constructed controls will not be altered.\n   */\n  abstract array<T>(\n      controls: Array<T>, validatorOrOpts?: ValidatorFn|ValidatorFn[]|AbstractControlOptions|null,\n      asyncValidator?: AsyncValidatorFn|AsyncValidatorFn[]|null): FormArray<ɵElement<T, never>>;\n\n  /**\n   * Similar to `FormBuilder#control`, except this overridden version of `control` forces\n   * `nonNullable` to be `true`, resulting in the control always being non-nullable.\n   */\n  abstract control<T>(\n      formState: T|FormControlState<T>,\n      validatorOrOpts?: ValidatorFn|ValidatorFn[]|AbstractControlOptions|null,\n      asyncValidator?: AsyncValidatorFn|AsyncValidatorFn[]|null): FormControl<T>;\n}\n\n/**\n * UntypedFormBuilder is the same as `FormBuilder`, but it provides untyped controls.\n */\n@Injectable({providedIn: 'root'})\nexport class UntypedFormBuilder extends FormBuilder {\n  /**\n   * Like `FormBuilder#group`, except the resulting group is untyped.\n   */\n  override group(\n      controlsConfig: {[key: string]: any},\n      options?: AbstractControlOptions|null,\n      ): UntypedFormGroup;\n\n  /**\n   * @deprecated This API is not typesafe and can result in issues with Closure Compiler renaming.\n   * Use the `FormBuilder#group` overload with `AbstractControlOptions` instead.\n   */\n  override group(\n      controlsConfig: {[key: string]: any},\n      options: {[key: string]: any},\n      ): UntypedFormGroup;\n\n  override group(\n      controlsConfig: {[key: string]: any},\n      options: AbstractControlOptions|{[key: string]: any}|null = null): UntypedFormGroup {\n    return super.group(controlsConfig, options);\n  }\n\n  /**\n   * Like `FormBuilder#control`, except the resulting control is untyped.\n   */\n  override control(\n      formState: any, validatorOrOpts?: ValidatorFn|ValidatorFn[]|FormControlOptions|null,\n      asyncValidator?: AsyncValidatorFn|AsyncValidatorFn[]|null): UntypedFormControl {\n    return super.control(formState, validatorOrOpts, asyncValidator);\n  }\n\n  /**\n   * Like `FormBuilder#array`, except the resulting array is untyped.\n   */\n  override array(\n      controlsConfig: any[],\n      validatorOrOpts?: ValidatorFn|ValidatorFn[]|AbstractControlOptions|null,\n      asyncValidator?: AsyncValidatorFn|AsyncValidatorFn[]|null): UntypedFormArray {\n    return super.array(controlsConfig, validatorOrOpts, asyncValidator);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the forms package.\n */\n\nimport {Version} from '@angular/core';\n\n/**\n * @publicApi\n */\nexport const VERSION = new Version('17.3.12');\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ModuleWithProviders, NgModule} from '@angular/core';\n\nimport {InternalFormsSharedModule, NG_MODEL_WITH_FORM_CONTROL_WARNING, REACTIVE_DRIVEN_DIRECTIVES, TEMPLATE_DRIVEN_DIRECTIVES} from './directives';\nimport {CALL_SET_DISABLED_STATE, setDisabledStateDefault, SetDisabledStateOption} from './directives/shared';\n\n/**\n * Exports the required providers and directives for template-driven forms,\n * making them available for import by NgModules that import this module.\n *\n * @see [Forms Overview](/guide/forms-overview)\n * @see [Template-driven Forms Guide](/guide/forms)\n *\n * @publicApi\n */\n@NgModule({\n  declarations: TEMPLATE_DRIVEN_DIRECTIVES,\n  exports: [InternalFormsSharedModule, TEMPLATE_DRIVEN_DIRECTIVES]\n})\nexport class FormsModule {\n  /**\n   * @description\n   * Provides options for configuring the forms module.\n   *\n   * @param opts An object of configuration options\n   * * `callSetDisabledState` Configures whether to `always` call `setDisabledState`, which is more\n   * correct, or to only call it `whenDisabled`, which is the legacy behavior.\n   */\n  static withConfig(opts: {\n    callSetDisabledState?: SetDisabledStateOption,\n  }): ModuleWithProviders<FormsModule> {\n    return {\n      ngModule: FormsModule,\n      providers: [{\n        provide: CALL_SET_DISABLED_STATE,\n        useValue: opts.callSetDisabledState ?? setDisabledStateDefault\n      }]\n    };\n  }\n}\n\n/**\n * Exports the required infrastructure and directives for reactive forms,\n * making them available for import by NgModules that import this module.\n *\n * @see [Forms Overview](guide/forms-overview)\n * @see [Reactive Forms Guide](guide/reactive-forms)\n *\n * @publicApi\n */\n@NgModule({\n  declarations: [REACTIVE_DRIVEN_DIRECTIVES],\n  exports: [InternalFormsSharedModule, REACTIVE_DRIVEN_DIRECTIVES]\n})\nexport class ReactiveFormsModule {\n  /**\n   * @description\n   * Provides options for configuring the reactive forms module.\n   *\n   * @param opts An object of configuration options\n   * * `warnOnNgModelWithFormControl` Configures when to emit a warning when an `ngModel`\n   * binding is used with reactive form directives.\n   * * `callSetDisabledState` Configures whether to `always` call `setDisabledState`, which is more\n   * correct, or to only call it `whenDisabled`, which is the legacy behavior.\n   */\n  static withConfig(opts: {\n                    /** @deprecated as of v6 */ warnOnNgModelWithFormControl?: 'never'|'once'|\n                                                                            'always',\n                    callSetDisabledState?: SetDisabledStateOption,\n                    }): ModuleWithProviders<ReactiveFormsModule> {\n    return {\n      ngModule: ReactiveFormsModule,\n      providers: [\n        {\n          provide: NG_MODEL_WITH_FORM_CONTROL_WARNING,\n          useValue: opts.warnOnNgModelWithFormControl ?? 'always'\n        },\n        {\n          provide: CALL_SET_DISABLED_STATE,\n          useValue: opts.callSetDisabledState ?? setDisabledStateDefault\n        }\n      ]\n    };\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * This module is used for handling user input, by defining and building a `FormGroup` that\n * consists of `FormControl` objects, and mapping them onto the DOM. `FormControl`\n * objects can then be used to read information from the form DOM elements.\n *\n * Forms providers are not included in default providers; you must import these providers\n * explicitly.\n */\n\nexport {ɵInternalFormsSharedModule} from './directives';\nexport {AbstractControlDirective} from './directives/abstract_control_directive';\nexport {AbstractFormGroupDirective} from './directives/abstract_form_group_directive';\nexport {CheckboxControlValueAccessor} from './directives/checkbox_value_accessor';\nexport {ControlContainer} from './directives/control_container';\nexport {ControlValueAccessor, NG_VALUE_ACCESSOR} from './directives/control_value_accessor';\nexport {COMPOSITION_BUFFER_MODE, DefaultValueAccessor} from './directives/default_value_accessor';\nexport {Form} from './directives/form_interface';\nexport {NgControl} from './directives/ng_control';\nexport {NgControlStatus, NgControlStatusGroup} from './directives/ng_control_status';\nexport {NgForm} from './directives/ng_form';\nexport {NgModel} from './directives/ng_model';\nexport {NgModelGroup} from './directives/ng_model_group';\nexport {ɵNgNoValidate} from './directives/ng_no_validate_directive';\nexport {NumberValueAccessor} from './directives/number_value_accessor';\nexport {RadioControlValueAccessor} from './directives/radio_control_value_accessor';\nexport {RangeValueAccessor} from './directives/range_value_accessor';\nexport {FormControlDirective} from './directives/reactive_directives/form_control_directive';\nexport {FormControlName} from './directives/reactive_directives/form_control_name';\nexport {FormGroupDirective} from './directives/reactive_directives/form_group_directive';\nexport {FormArrayName, FormGroupName} from './directives/reactive_directives/form_group_name';\nexport {NgSelectOption, SelectControlValueAccessor} from './directives/select_control_value_accessor';\nexport {SelectMultipleControlValueAccessor, ɵNgSelectMultipleOption} from './directives/select_multiple_control_value_accessor';\nexport {SetDisabledStateOption} from './directives/shared';\nexport {AsyncValidator, AsyncValidatorFn, CheckboxRequiredValidator, EmailValidator, MaxLengthValidator, MaxValidator, MinLengthValidator, MinValidator, PatternValidator, RequiredValidator, ValidationErrors, Validator, ValidatorFn} from './directives/validators';\nexport {ControlConfig, FormBuilder, NonNullableFormBuilder, UntypedFormBuilder, ɵElement} from './form_builder';\nexport {AbstractControl, AbstractControlOptions, FormControlStatus, ɵCoerceStrArrToNumArr, ɵGetProperty, ɵNavigate, ɵRawValue, ɵTokenize, ɵTypedOrUntyped, ɵValue, ɵWriteable} from './model/abstract_model';\nexport {FormArray, isFormArray, UntypedFormArray, ɵFormArrayRawValue, ɵFormArrayValue} from './model/form_array';\nexport {FormControl, FormControlOptions, FormControlState, isFormControl, UntypedFormControl, ɵFormControlCtor} from './model/form_control';\nexport {FormGroup, FormRecord, isFormGroup, isFormRecord, UntypedFormGroup, ɵFormGroupRawValue, ɵFormGroupValue, ɵOptionalKeys} from './model/form_group';\nexport {NG_ASYNC_VALIDATORS, NG_VALIDATORS, Validators} from './validators';\nexport {VERSION} from './version';\n\nexport * from './form_providers';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\nexport * from './src/forms';\n\n// This file only reexports content of the `src` folder. Keep it that way.\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// This file is not used to build this module. It is only used during editing\n// by the TypeScript language service and during build for verification. `ngc`\n// replaces this file with production index.ts when it rewrites private symbol\n// names.\n\nexport * from './public_api';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["getDOM", "isPromise", "isSubscribable", "RuntimeError", "i1.NgControl", "i2.ControlContainer", "removeListItem", "formDirectiveProvider", "resolvedPromise", "i1.ControlContainer", "formControlBinding", "_buildValueString", "_extractId", "NgNoValidate", "NgSelectMultipleOption", "InternalFormsSharedModule", "i1.<PERSON><PERSON><PERSON><PERSON>", "i2.NgModelGroup", "i3.<PERSON><PERSON><PERSON>", "i4.FormControlDirective", "i5.FormGroupDirective", "i6.FormControlName", "i7.FormGroupName", "i7.FormArrayName"], "mappings": ";;;;;;;;;;;;AAqIA;;;;;;AAMG;MAEU,wBAAwB,CAAA;IAcnC,WAAoB,CAAA,SAAoB,EAAU,WAAuB,EAAA;QAArD,IAAS,CAAA,SAAA,GAAT,SAAS,CAAW;QAAU,IAAW,CAAA,WAAA,GAAX,WAAW,CAAY;AAbzE;;;;AAIG;AACH,QAAA,IAAA,CAAA,QAAQ,GAAG,CAAC,CAAM,KAAI,GAAG,CAAC;AAE1B;;;AAGG;AACH,QAAA,IAAA,CAAA,SAAS,GAAG,MAAK,GAAG,CAAC;KAEwD;AAE7E;;;;AAIG;IACO,WAAW,CAAC,GAAW,EAAE,KAAU,EAAA;AAC3C,QAAA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;KACxE;AAED;;;AAGG;AACH,IAAA,iBAAiB,CAAC,EAAc,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;KACrB;AAED;;;AAGG;AACH,IAAA,gBAAgB,CAAC,EAAkB,EAAA;AACjC,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;KACpB;AAED;;;AAGG;AACH,IAAA,gBAAgB,CAAC,UAAmB,EAAA;AAClC,QAAA,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;KAC1C;yHA/CU,wBAAwB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAAxB,wBAAwB,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAAxB,wBAAwB,EAAA,UAAA,EAAA,CAAA;kBADpC,SAAS;;AAmDV;;;;;;;;AAQG;AAEG,MAAO,2BAA4B,SAAQ,wBAAwB,CAAA;yHAA5D,2BAA2B,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAA3B,2BAA2B,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAA3B,2BAA2B,EAAA,UAAA,EAAA,CAAA;kBADvC,SAAS;;AAIV;;;;;;AAMG;AACU,MAAA,iBAAiB,GAC1B,IAAI,cAAc,CAAsC,SAAS,GAAG,iBAAiB,GAAG,EAAE;;ACxM9F,MAAM,uBAAuB,GAAa;AACxC,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,4BAA4B,CAAC;AAC3D,IAAA,KAAK,EAAE,IAAI;CACZ,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;AAsBG;AAOG,MAAO,4BAA6B,SAAQ,2BAA2B,CAAA;AAE3E;;;AAGG;AACH,IAAA,UAAU,CAAC,KAAU,EAAA;AACnB,QAAA,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;KACpC;yHARU,4BAA4B,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAA5B,4BAA4B,EAAA,QAAA,EAAA,uGAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,QAAA,EAAA,iCAAA,EAAA,MAAA,EAAA,aAAA,EAAA,EAAA,EAAA,SAAA,EAF5B,CAAC,uBAAuB,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAEzB,4BAA4B,EAAA,UAAA,EAAA,CAAA;kBANxC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EACJ,uGAAuG;oBAC3G,IAAI,EAAE,EAAC,UAAU,EAAE,iCAAiC,EAAE,QAAQ,EAAE,aAAa,EAAC;oBAC9E,SAAS,EAAE,CAAC,uBAAuB,CAAC;AACrC,iBAAA,CAAA;;;ACjCM,MAAM,sBAAsB,GAAa;AAC9C,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,oBAAoB,CAAC;AACnD,IAAA,KAAK,EAAE,IAAI;CACZ,CAAC;AAEF;;;AAGG;AACH,SAAS,UAAU,GAAA;AACjB,IAAA,MAAM,SAAS,GAAGA,OAAM,EAAE,GAAGA,OAAM,EAAE,CAAC,YAAY,EAAE,GAAG,EAAE,CAAC;IAC1D,OAAO,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC;AACvD,CAAC;AAED;;;;;AAKG;AACU,MAAA,uBAAuB,GAChC,IAAI,cAAc,CAAU,SAAS,GAAG,sBAAsB,GAAG,EAAE,EAAE;AAEzE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCG;AAeG,MAAO,oBAAqB,SAAQ,wBAAwB,CAAA;AAIhE,IAAA,WAAA,CACI,QAAmB,EAAE,UAAsB,EACU,gBAAyB,EAAA;AAChF,QAAA,KAAK,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QAD2B,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAS;;QAJ1E,IAAU,CAAA,UAAA,GAAG,KAAK,CAAC;AAMzB,QAAA,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,EAAE;AACjC,YAAA,IAAI,CAAC,gBAAgB,GAAG,CAAC,UAAU,EAAE,CAAC;SACvC;KACF;AAED;;;AAGG;AACH,IAAA,UAAU,CAAC,KAAU,EAAA;AACnB,QAAA,MAAM,eAAe,GAAG,KAAK,IAAI,IAAI,GAAG,EAAE,GAAG,KAAK,CAAC;AACnD,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;KAC5C;;AAGD,IAAA,YAAY,CAAC,KAAU,EAAA;AACrB,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACzE,YAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;SACtB;KACF;;IAGD,iBAAiB,GAAA;AACf,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;KACxB;;AAGD,IAAA,eAAe,CAAC,KAAU,EAAA;AACxB,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;KAC/C;AAtCU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,qEAMP,uBAAuB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GANpC,oBAAoB,EAAA,QAAA,EAAA,8MAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,8CAAA,EAAA,MAAA,EAAA,aAAA,EAAA,kBAAA,EAAA,gCAAA,EAAA,gBAAA,EAAA,iDAAA,EAAA,EAAA,EAAA,SAAA,EAFpB,CAAC,sBAAsB,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAExB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAdhC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EACJ,8MAA8M;;;;AAIlN,oBAAA,IAAI,EAAE;AACJ,wBAAA,SAAS,EAAE,8CAA8C;AACzD,wBAAA,QAAQ,EAAE,aAAa;AACvB,wBAAA,oBAAoB,EAAE,gCAAgC;AACtD,wBAAA,kBAAkB,EAAE,iDAAiD;AACtE,qBAAA;oBACD,SAAS,EAAE,CAAC,sBAAsB,CAAC;AACpC,iBAAA,CAAA;;0BAOM,QAAQ;;0BAAI,MAAM;2BAAC,uBAAuB,CAAA;;;AC3EjD,SAAS,iBAAiB,CAAC,KAAU,EAAA;AACnC;;;;AAIG;IACH,OAAO,KAAK,IAAI,IAAI;SACf,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;AAClF,CAAC;AAED,SAAS,cAAc,CAAC,KAAU,EAAA;;IAEhC,OAAO,KAAK,IAAI,IAAI,IAAI,OAAO,KAAK,CAAC,MAAM,KAAK,QAAQ,CAAC;AAC3D,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BG;AACU,MAAA,aAAa,GACtB,IAAI,cAAc,CAAoC,SAAS,GAAG,cAAc,GAAG,EAAE,EAAE;AAE3F;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BG;AACU,MAAA,mBAAmB,GAC5B,IAAI,cAAc,CAAoC,SAAS,GAAG,mBAAmB,GAAG,EAAE,EAAE;AAEhG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;AACH,MAAM,YAAY,GACd,oMAAoM,CAAC;AAEzM;;;;;;;;;;AAUG;MACU,UAAU,CAAA;AACrB;;;;;;;;;;;;;;;;;;;AAmBG;IACH,OAAO,GAAG,CAAC,GAAW,EAAA;AACpB,QAAA,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC;KAC1B;AAED;;;;;;;;;;;;;;;;;;;AAmBG;IACH,OAAO,GAAG,CAAC,GAAW,EAAA;AACpB,QAAA,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC;KAC1B;AAED;;;;;;;;;;;;;;;;;;;AAmBG;IACH,OAAO,QAAQ,CAAC,OAAwB,EAAA;AACtC,QAAA,OAAO,iBAAiB,CAAC,OAAO,CAAC,CAAC;KACnC;AAED;;;;;;;;;;;;;;;;;;;;AAoBG;IACH,OAAO,YAAY,CAAC,OAAwB,EAAA;AAC1C,QAAA,OAAO,qBAAqB,CAAC,OAAO,CAAC,CAAC;KACvC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCG;IACH,OAAO,KAAK,CAAC,OAAwB,EAAA;AACnC,QAAA,OAAO,cAAc,CAAC,OAAO,CAAC,CAAC;KAChC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;IACH,OAAO,SAAS,CAAC,SAAiB,EAAA;AAChC,QAAA,OAAO,kBAAkB,CAAC,SAAS,CAAC,CAAC;KACtC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BG;IACH,OAAO,SAAS,CAAC,SAAiB,EAAA;AAChC,QAAA,OAAO,kBAAkB,CAAC,SAAS,CAAC,CAAC;KACtC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDG;IACH,OAAO,OAAO,CAAC,OAAsB,EAAA;AACnC,QAAA,OAAO,gBAAgB,CAAC,OAAO,CAAC,CAAC;KAClC;AAED;;;;;;AAMG;IACH,OAAO,aAAa,CAAC,OAAwB,EAAA;AAC3C,QAAA,OAAO,aAAa,CAAC,OAAO,CAAC,CAAC;KAC/B;IAeD,OAAO,OAAO,CAAC,UAA+C,EAAA;AAC5D,QAAA,OAAO,OAAO,CAAC,UAAU,CAAC,CAAC;KAC5B;AAED;;;;;;;;;;AAUG;IACH,OAAO,YAAY,CAAC,UAAqC,EAAA;AACvD,QAAA,OAAO,YAAY,CAAC,UAAU,CAAC,CAAC;KACjC;AACF,CAAA;AAED;;;AAGG;AACG,SAAU,YAAY,CAAC,GAAW,EAAA;IACtC,OAAO,CAAC,OAAwB,KAA2B;AACzD,QAAA,IAAI,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,iBAAiB,CAAC,GAAG,CAAC,EAAE;YAC9D,OAAO,IAAI,CAAC;SACb;QACD,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;;AAGxC,QAAA,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,GAAG,GAAG,GAAG,EAAC,KAAK,EAAE,EAAC,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,OAAO,CAAC,KAAK,EAAC,EAAC,GAAG,IAAI,CAAC;AAC9F,KAAC,CAAC;AACJ,CAAC;AAED;;;AAGG;AACG,SAAU,YAAY,CAAC,GAAW,EAAA;IACtC,OAAO,CAAC,OAAwB,KAA2B;AACzD,QAAA,IAAI,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,iBAAiB,CAAC,GAAG,CAAC,EAAE;YAC9D,OAAO,IAAI,CAAC;SACb;QACD,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;;AAGxC,QAAA,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,GAAG,GAAG,GAAG,EAAC,KAAK,EAAE,EAAC,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,OAAO,CAAC,KAAK,EAAC,EAAC,GAAG,IAAI,CAAC;AAC9F,KAAC,CAAC;AACJ,CAAC;AAED;;;AAGG;AACG,SAAU,iBAAiB,CAAC,OAAwB,EAAA;AACxD,IAAA,OAAO,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAC,UAAU,EAAE,IAAI,EAAC,GAAG,IAAI,CAAC;AACtE,CAAC;AAED;;;;AAIG;AACG,SAAU,qBAAqB,CAAC,OAAwB,EAAA;AAC5D,IAAA,OAAO,OAAO,CAAC,KAAK,KAAK,IAAI,GAAG,IAAI,GAAG,EAAC,UAAU,EAAE,IAAI,EAAC,CAAC;AAC5D,CAAC;AAED;;;AAGG;AACG,SAAU,cAAc,CAAC,OAAwB,EAAA;AACrD,IAAA,IAAI,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QACpC,OAAO,IAAI,CAAC;KACb;IACD,OAAO,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC;AACnE,CAAC;AAED;;;AAGG;AACG,SAAU,kBAAkB,CAAC,SAAiB,EAAA;IAClD,OAAO,CAAC,OAAwB,KAA2B;AACzD,QAAA,IAAI,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;;;AAGtE,YAAA,OAAO,IAAI,CAAC;SACb;QAED,OAAO,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS;AACnC,YAAA,EAAC,WAAW,EAAE,EAAC,gBAAgB,EAAE,SAAS,EAAE,cAAc,EAAE,OAAO,CAAC,KAAK,CAAC,MAAM,EAAC,EAAC;AAClF,YAAA,IAAI,CAAC;AACX,KAAC,CAAC;AACJ,CAAC;AAED;;;AAGG;AACG,SAAU,kBAAkB,CAAC,SAAiB,EAAA;IAClD,OAAO,CAAC,OAAwB,KAA2B;AACzD,QAAA,OAAO,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS;AACpE,YAAA,EAAC,WAAW,EAAE,EAAC,gBAAgB,EAAE,SAAS,EAAE,cAAc,EAAE,OAAO,CAAC,KAAK,CAAC,MAAM,EAAC,EAAC;AAClF,YAAA,IAAI,CAAC;AACX,KAAC,CAAC;AACJ,CAAC;AAED;;;AAGG;AACG,SAAU,gBAAgB,CAAC,OAAsB,EAAA;AACrD,IAAA,IAAI,CAAC,OAAO;AAAE,QAAA,OAAO,aAAa,CAAC;AACnC,IAAA,IAAI,KAAa,CAAC;AAClB,IAAA,IAAI,QAAgB,CAAC;AACrB,IAAA,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QAC/B,QAAQ,GAAG,EAAE,CAAC;AAEd,QAAA,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG;YAAE,QAAQ,IAAI,GAAG,CAAC;QAE/C,QAAQ,IAAI,OAAO,CAAC;QAEpB,IAAI,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG;YAAE,QAAQ,IAAI,GAAG,CAAC;AAEhE,QAAA,KAAK,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC;KAC9B;SAAM;AACL,QAAA,QAAQ,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;QAC9B,KAAK,GAAG,OAAO,CAAC;KACjB;IACD,OAAO,CAAC,OAAwB,KAA2B;AACzD,QAAA,IAAI,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACpC,OAAO,IAAI,CAAC;SACb;AACD,QAAA,MAAM,KAAK,GAAW,OAAO,CAAC,KAAK,CAAC;QACpC,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI;AACJ,YAAA,EAAC,SAAS,EAAE,EAAC,iBAAiB,EAAE,QAAQ,EAAE,aAAa,EAAE,KAAK,EAAC,EAAC,CAAC;AAC9F,KAAC,CAAC;AACJ,CAAC;AAED;;AAEG;AACG,SAAU,aAAa,CAAC,OAAwB,EAAA;AACpD,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,SAAS,CAAC,CAAM,EAAA;IACvB,OAAO,CAAC,IAAI,IAAI,CAAC;AACnB,CAAC;AAEK,SAAU,YAAY,CAAC,KAAU,EAAA;AACrC,IAAA,MAAM,GAAG,GAAGC,UAAS,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;AACnD,IAAA,IAAI,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,EAAEC,eAAc,CAAC,GAAG,CAAC,CAAC,EAAE;QAC7E,IAAI,YAAY,GAAG,CAAA,yDAAA,CAA2D,CAAC;;AAE/E,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,YAAY;AACR,gBAAA,8EAA8E,CAAC;SACpF;AACD,QAAA,MAAM,IAAIC,aAAY,CAA+C,CAAA,IAAA,qDAAA,YAAY,CAAC,CAAC;KACpF;AACD,IAAA,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,WAAW,CAAC,aAAwC,EAAA;IAC3D,IAAI,GAAG,GAAyB,EAAE,CAAC;AACnC,IAAA,aAAa,CAAC,OAAO,CAAC,CAAC,MAA6B,KAAI;AACtD,QAAA,GAAG,GAAG,MAAM,IAAI,IAAI,GAAG,EAAC,GAAG,GAAI,EAAE,GAAG,MAAM,EAAC,GAAG,GAAI,CAAC;AACrD,KAAC,CAAC,CAAC;AAEH,IAAA,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;AACpD,CAAC;AAID,SAAS,iBAAiB,CACtB,OAAwB,EAAE,UAAe,EAAA;AAC3C,IAAA,OAAO,UAAU,CAAC,GAAG,CAAC,SAAS,IAAI,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;AACzD,CAAC;AAED,SAAS,aAAa,CAAI,SAAqC,EAAA;AAC7D,IAAA,OAAO,CAAE,SAAuB,CAAC,QAAQ,CAAC;AAC5C,CAAC;AAED;;;;;;;AAOG;AACG,SAAU,mBAAmB,CAAI,UAA0C,EAAA;AAC/E,IAAA,OAAO,UAAU,CAAC,GAAG,CAAC,SAAS,IAAG;AAChC,QAAA,OAAO,aAAa,CAAI,SAAS,CAAC;AAC9B,YAAA,SAAS;AACT,aAAC,CAAC,CAAkB,KAAK,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAiB,CAAC;AACtE,KAAC,CAAC,CAAC;AACL,CAAC;AAED;;;AAGG;AACH,SAAS,OAAO,CAAC,UAA+C,EAAA;AAC9D,IAAA,IAAI,CAAC,UAAU;AAAE,QAAA,OAAO,IAAI,CAAC;IAC7B,MAAM,iBAAiB,GAAkB,UAAU,CAAC,MAAM,CAAC,SAAS,CAAQ,CAAC;AAC7E,IAAA,IAAI,iBAAiB,CAAC,MAAM,IAAI,CAAC;AAAE,QAAA,OAAO,IAAI,CAAC;AAE/C,IAAA,OAAO,UAAS,OAAwB,EAAA;QACtC,OAAO,WAAW,CAAC,iBAAiB,CAAc,OAAO,EAAE,iBAAiB,CAAC,CAAC,CAAC;AACjF,KAAC,CAAC;AACJ,CAAC;AAED;;;;AAIG;AACG,SAAU,iBAAiB,CAAC,UAAwC,EAAA;AACxE,IAAA,OAAO,UAAU,IAAI,IAAI,GAAG,OAAO,CAAC,mBAAmB,CAAc,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC;AAC3F,CAAC;AAED;;;AAGG;AACH,SAAS,YAAY,CAAC,UAAqC,EAAA;AACzD,IAAA,IAAI,CAAC,UAAU;AAAE,QAAA,OAAO,IAAI,CAAC;IAC7B,MAAM,iBAAiB,GAAuB,UAAU,CAAC,MAAM,CAAC,SAAS,CAAQ,CAAC;AAClF,IAAA,IAAI,iBAAiB,CAAC,MAAM,IAAI,CAAC;AAAE,QAAA,OAAO,IAAI,CAAC;AAE/C,IAAA,OAAO,UAAS,OAAwB,EAAA;AACtC,QAAA,MAAM,WAAW,GACb,iBAAiB,CAAmB,OAAO,EAAE,iBAAiB,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;AACtF,QAAA,OAAO,QAAQ,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;AACtD,KAAC,CAAC;AACJ,CAAC;AAED;;;;AAIG;AACG,SAAU,sBAAsB,CAAC,UAAkD,EAAA;AAEvF,IAAA,OAAO,UAAU,IAAI,IAAI,GAAG,YAAY,CAAC,mBAAmB,CAAmB,UAAU,CAAC,CAAC;AAC/D,QAAA,IAAI,CAAC;AACnC,CAAC;AAED;;;AAGG;AACa,SAAA,eAAe,CAAI,iBAA6B,EAAE,YAAe,EAAA;IAC/E,IAAI,iBAAiB,KAAK,IAAI;QAAE,OAAO,CAAC,YAAY,CAAC,CAAC;AACtD,IAAA,OAAO,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,iBAAiB,EAAE,YAAY,CAAC;AACpC,QAAA,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAAC;AAC9E,CAAC;AAED;;AAEG;AACG,SAAU,oBAAoB,CAAC,OAAwB,EAAA;IAC3D,OAAQ,OAAe,CAAC,cAAoD,CAAC;AAC/E,CAAC;AAED;;AAEG;AACG,SAAU,yBAAyB,CAAC,OAAwB,EAAA;IAEhE,OAAQ,OAAe,CAAC,mBAAmE,CAAC;AAC9F,CAAC;AAED;;;;;;AAMG;AACG,SAAU,mBAAmB,CAAyC,UACI,EAAA;AAC9E,IAAA,IAAI,CAAC,UAAU;AAAE,QAAA,OAAO,EAAE,CAAC;AAC3B,IAAA,OAAO,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,UAAU,GAAG,CAAC,UAAU,CAAC,CAAC;AAC/D,CAAC;AAED;;;;;;AAMG;AACa,SAAA,YAAY,CACxB,UAAsB,EAAE,SAAY,EAAA;IACtC,OAAO,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,UAAU,KAAK,SAAS,CAAC;AAC/F,CAAC;AAED;;;;;;AAMG;AACa,SAAA,aAAa,CACzB,UAAiB,EAAE,iBAA6B,EAAA;AAClD,IAAA,MAAM,OAAO,GAAG,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;AACvD,IAAA,MAAM,eAAe,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;AACxD,IAAA,eAAe,CAAC,OAAO,CAAC,CAAC,CAAI,KAAI;;;;;QAK/B,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE;AAC7B,YAAA,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACjB;AACH,KAAC,CAAC,CAAC;AACH,IAAA,OAAO,OAAO,CAAC;AACjB,CAAC;AAEe,SAAA,gBAAgB,CAC5B,UAAiB,EAAE,iBAA6B,EAAA;AAClD,IAAA,OAAO,mBAAmB,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;AAC1F;;AC5tBA;;;;;;;AAOG;MACmB,wBAAwB,CAAA;AAA9C,IAAA,WAAA,GAAA;AA+JE;;;AAGG;QACH,IAAc,CAAA,cAAA,GAAiC,EAAE,CAAC;AAElD;;;;AAIG;QACH,IAAmB,CAAA,mBAAA,GAA2C,EAAE,CAAC;AAsCjE;;AAEG;QACK,IAAmB,CAAA,mBAAA,GAAmB,EAAE,CAAC;KA6FlD;AAvSC;;;AAGG;AACH,IAAA,IAAI,KAAK,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;KACjD;AAED;;;;;AAKG;AACH,IAAA,IAAI,KAAK,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;KACjD;AAED;;;;AAIG;AACH,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;KACnD;AAED;;;;;AAKG;AACH,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;KACnD;AAED;;;;;AAKG;AACH,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;KACpD;AAED;;;;AAIG;AACH,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;KACnD;AAED;;;AAGG;AACH,IAAA,IAAI,MAAM,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;KAClD;AAED;;;;AAIG;AACH,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;KACpD;AAED;;;;AAIG;AACH,IAAA,IAAI,KAAK,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;KACjD;AAED;;;;AAIG;AACH,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;KACnD;AAED;;;;;AAKG;AACH,IAAA,IAAI,MAAM,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;KAClD;AAED;;;;AAIG;AACH,IAAA,IAAI,SAAS,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;KACrD;AAED;;;;AAIG;AACH,IAAA,IAAI,aAAa,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;KACzD;AAED;;;;;AAKG;AACH,IAAA,IAAI,YAAY,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;KACxD;AAED;;;;AAIG;AACH,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,CAAC;KACb;AA2BD;;;AAGG;AACH,IAAA,cAAc,CAAC,UAAkD,EAAA;AAC/D,QAAA,IAAI,CAAC,cAAc,GAAG,UAAU,IAAI,EAAE,CAAC;QACvC,IAAI,CAAC,oBAAoB,GAAG,iBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;KACpE;AAED;;;AAGG;AACH,IAAA,mBAAmB,CAAC,UAA4D,EAAA;AAC9E,QAAA,IAAI,CAAC,mBAAmB,GAAG,UAAU,IAAI,EAAE,CAAC;QAC5C,IAAI,CAAC,yBAAyB,GAAG,sBAAsB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;KACnF;AAED;;;;AAIG;AACH,IAAA,IAAI,SAAS,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC;KAC1C;AAED;;;;AAIG;AACH,IAAA,IAAI,cAAc,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,yBAAyB,IAAI,IAAI,CAAC;KAC/C;AAOD;;;;AAIG;AACH,IAAA,kBAAkB,CAAC,EAAc,EAAA;AAC/B,QAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KACnC;AAED;;;;AAIG;IACH,yBAAyB,GAAA;AACvB,QAAA,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;AAC7C,QAAA,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;KAC/B;AAED;;;AAGG;IACH,KAAK,CAAC,QAAa,SAAS,EAAA;QAC1B,IAAI,IAAI,CAAC,OAAO;AAAE,YAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;KAC7C;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;IACH,QAAQ,CAAC,SAAiB,EAAE,IAAkC,EAAA;QAC5D,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC;KACtE;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BG;IACH,QAAQ,CAAC,SAAiB,EAAE,IAAkC,EAAA;QAC5D,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC;KACrE;AACF;;AC5TD;;;;;;AAMG;AACG,MAAgB,gBAAiB,SAAQ,wBAAwB,CAAA;AAQrE;;;AAGG;AACH,IAAA,IAAI,aAAa,GAAA;AACf,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;AAGG;AACH,IAAA,IAAa,IAAI,GAAA;AACf,QAAA,OAAO,IAAI,CAAC;KACb;AACF;;AC7BD;;;;;;AAMG;AACG,MAAgB,SAAU,SAAQ,wBAAwB,CAAA;AAAhE,IAAA,WAAA,GAAA;;AACE;;;;;AAKG;QACH,IAAO,CAAA,OAAA,GAA0B,IAAI,CAAC;AAEtC;;;AAGG;QACH,IAAI,CAAA,IAAA,GAAuB,IAAI,CAAC;AAEhC;;;AAGG;QACH,IAAa,CAAA,aAAA,GAA8B,IAAI,CAAC;KASjD;AAAA;;AChCD;AACA;AACA;AACA;MACa,qBAAqB,CAAA;AAGhC,IAAA,WAAA,CAAY,EAAiC,EAAA;AAC3C,QAAA,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;KACf;AAED,IAAA,IAAc,SAAS,GAAA;QACrB,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,CAAC;KACrC;AAED,IAAA,IAAc,WAAW,GAAA;QACvB,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,EAAE,SAAS,CAAC;KACvC;AAED,IAAA,IAAc,UAAU,GAAA;QACtB,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,EAAE,QAAQ,CAAC;KACtC;AAED,IAAA,IAAc,OAAO,GAAA;QACnB,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,CAAC;KACnC;AAED,IAAA,IAAc,OAAO,GAAA;QACnB,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,CAAC;KACnC;AAED,IAAA,IAAc,SAAS,GAAA;QACrB,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,CAAC;KACrC;AAED,IAAA,IAAc,SAAS,GAAA;QACrB,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,CAAC;KACrC;AAED,IAAA,IAAc,WAAW,GAAA;;;AAGvB,QAAA,OAAO,CAAC,CAAE,IAAI,CAAC,GAAiD,EAAE,SAAS,CAAC;KAC7E;AACF,CAAA;AAEM,MAAM,mBAAmB,GAAG;AACjC,IAAA,sBAAsB,EAAE,aAAa;AACrC,IAAA,oBAAoB,EAAE,WAAW;AACjC,IAAA,qBAAqB,EAAE,YAAY;AACnC,IAAA,kBAAkB,EAAE,SAAS;AAC7B,IAAA,kBAAkB,EAAE,SAAS;AAC7B,IAAA,oBAAoB,EAAE,WAAW;AACjC,IAAA,oBAAoB,EAAE,WAAW;CAClC,CAAC;AAEK,MAAM,iBAAiB,GAAG;AAC/B,IAAA,GAAG,mBAAmB;AACtB,IAAA,sBAAsB,EAAE,aAAa;CACtC,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;AAsBG;AAEG,MAAO,eAAgB,SAAQ,qBAAqB,CAAA;AACxD,IAAA,WAAA,CAAoB,EAAa,EAAA;QAC/B,KAAK,CAAC,EAAE,CAAC,CAAC;KACX;yHAHU,eAAe,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAC,SAAA,EAAA,IAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAAf,eAAe,EAAA,QAAA,EAAA,2CAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,oBAAA,EAAA,aAAA,EAAA,kBAAA,EAAA,WAAA,EAAA,mBAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,kBAAA,EAAA,WAAA,EAAA,kBAAA,EAAA,WAAA,EAAA,EAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAD3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA,EAAC,QAAQ,EAAE,2CAA2C,EAAE,IAAI,EAAE,mBAAmB,EAAC,CAAA;;0BAE9E,IAAI;;AAKnB;;;;;;;;;;;AAWG;AAMG,MAAO,oBAAqB,SAAQ,qBAAqB,CAAA;AAC7D,IAAA,WAAA,CAAgC,EAAoB,EAAA;QAClD,KAAK,CAAC,EAAE,CAAC,CAAC;KACX;yHAHU,oBAAoB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAC,gBAAA,EAAA,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAApB,oBAAoB,EAAA,QAAA,EAAA,0FAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,oBAAA,EAAA,aAAA,EAAA,kBAAA,EAAA,WAAA,EAAA,mBAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,kBAAA,EAAA,WAAA,EAAA,kBAAA,EAAA,WAAA,EAAA,oBAAA,EAAA,aAAA,EAAA,EAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAApB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBALhC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EACJ,0FAA0F;AAC9F,oBAAA,IAAI,EAAE,iBAAiB;AACxB,iBAAA,CAAA;;0BAEc,QAAQ;;0BAAI,IAAI;;;ACrHxB,MAAM,sBAAsB,GAAG,CAAA;;;;;;;;;MAShC,CAAC;AAEA,MAAM,oBAAoB,GAAG,CAAA;;;;;;;;;;;MAW9B,CAAC;AAEA,MAAM,oBAAoB,GAAG,CAAA;;;;;;;;;;;;;;MAc9B,CAAC;AAEA,MAAM,mBAAmB,GAAG,CAAA;;;;;UAKzB,CAAC;AAEJ,MAAM,2BAA2B,GAAG,CAAA;;;;;CAK1C;;SC7Ce,sBAAsB,GAAA;IACpC,OAAO,IAAIF,aAAY,CAEnB,IAAA,0DAAA,CAAA;;;;;MAKA,sBAAsB,CAAA,CAAE,CAAC,CAAC;AAChC,CAAC;SAEe,qBAAqB,GAAA;IACnC,OAAO,IAAIA,aAAY,CAEnB,IAAA,8DAAA,CAAA;;;;;QAKE,oBAAoB,CAAA;;;;QAIpB,mBAAmB,CAAA,CAAE,CAAC,CAAC;AAC/B,CAAC;SAEe,oBAAoB,GAAA;IAClC,OAAO,IAAIA,aAAY,CAEnB,IAAA,qDAAA,CAAA;;;;QAIE,sBAAsB,CAAA,CAAE,CAAC,CAAC;AAClC,CAAC;SAEe,oBAAoB,GAAA;IAClC,OAAO,IAAIA,aAAY,CAEnB,IAAA,wDAAA,CAAA;;;;;MAKA,oBAAoB,CAAA,CAAE,CAAC,CAAC;AAC9B,CAAC;SAEe,oBAAoB,GAAA;IAClC,OAAO,IAAIA,aAAY,CAEnB,IAAA,wDAAA,CAAA;;;;;QAKE,oBAAoB,CAAA,CAAE,CAAC,CAAC;AAChC,CAAC;AAEM,MAAM,mBAAmB,GAAG,CAAA;;;;;;;;;;;;;;;CAelC,CAAC;AAEK,MAAM,qCAAqC,GAAG,CAAA;;;;;;;;;;;;;;CAcpD,CAAC;AAEI,SAAU,cAAc,CAAC,aAAqB,EAAA;IAClD,OAAO,CAAA;iEACwD,aAAa,CAAA;;;;;;iCAOxE,aAAa,KAAK,aAAa,GAAG,sBAAsB,GAAG,iBAAiB,CAAA;GAC/E,CAAC;AACJ,CAAC;AAED,SAAS,WAAW,CAAC,WAAoB,EAAE,GAAkB,EAAA;AAC3D,IAAA,OAAO,WAAW,GAAG,CAAe,YAAA,EAAA,GAAG,CAAG,CAAA,CAAA,GAAG,CAAa,UAAA,EAAA,GAAG,EAAE,CAAC;AAClE,CAAC;AAEK,SAAU,eAAe,CAAC,WAAoB,EAAA;IAClD,OAAO,CAAA;AAEH,oDAAA,EAAA,WAAW,GAAG,OAAO,GAAG,OAAO,CAAA;;GAElC,CAAC;AACJ,CAAC;AAEe,SAAA,mBAAmB,CAAC,WAAoB,EAAE,GAAkB,EAAA;IAC1E,OAAO,CAAA,yBAAA,EAA4B,WAAW,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE,CAAC;AACrE,CAAC;AAEe,SAAA,wBAAwB,CAAC,WAAoB,EAAE,GAAkB,EAAA;IAC/E,OAAO,CAAA,qCAAA,EAAwC,WAAW,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE,CAAC;AACjF;;ACvHA;;;;AAIG;AACI,MAAM,KAAK,GAAG,OAAO,CAAC;AAE7B;;;;AAIG;AACI,MAAM,OAAO,GAAG,SAAS,CAAC;AAEjC;;;;;;AAMG;AACI,MAAM,OAAO,GAAG,SAAS,CAAC;AAEjC;;;;;;AAMG;AACI,MAAM,QAAQ,GAAG,UAAU,CAAC;AAmBnC;;AAEG;AACG,SAAU,cAAc,CAAC,eACI,EAAA;AACjC,IAAA,OAAO,CAAC,YAAY,CAAC,eAAe,CAAC,GAAG,eAAe,CAAC,UAAU,GAAG,eAAe,KAAK,IAAI,CAAC;AAChG,CAAC;AAED;;AAEG;AACH,SAAS,iBAAiB,CAAC,SAAyC,EAAA;AAClE,IAAA,OAAO,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,iBAAiB,CAAC,SAAS,CAAC,GAAG,SAAS,IAAI,IAAI,CAAC;AACrF,CAAC;AAED;;AAEG;AACa,SAAA,mBAAmB,CAC/B,cAAyD,EACzD,eAAuE,EAAA;AAEzE,IAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,QAAA,IAAI,YAAY,CAAC,eAAe,CAAC,IAAI,cAAc,EAAE;AACnD,YAAA,OAAO,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;SACrD;KACF;AACD,IAAA,OAAO,CAAC,YAAY,CAAC,eAAe,CAAC,GAAG,eAAe,CAAC,eAAe,GAAG,cAAc,KAAK,IAAI,CAAC;AACpG,CAAC;AAED;;AAEG;AACH,SAAS,sBAAsB,CAAC,cACI,EAAA;AAClC,IAAA,OAAO,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,sBAAsB,CAAC,cAAc,CAAC;QACtC,cAAc,IAAI,IAAI,CAAC;AAChE,CAAC;AA2BK,SAAU,YAAY,CAAC,eACI,EAAA;IAC/B,OAAO,eAAe,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC;QAC7D,OAAO,eAAe,KAAK,QAAQ,CAAC;AAC1C,CAAC;SAEe,oBAAoB,CAAC,MAAW,EAAE,OAAgB,EAAE,GAAkB,EAAA;AACpF,IAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,QAA2C,CAAC;AACpE,IAAA,MAAM,UAAU,GAAG,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC;AAC9D,IAAA,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;QACtB,MAAM,IAAIA,aAAY,CAElB,IAAA,qCAAA,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,IAAI,eAAe,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;KACtF;AACD,IAAA,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;QAClB,MAAM,IAAIA,aAAY,CAAA,IAAA,yCAElB,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,IAAI,mBAAmB,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;KAC/F;AACH,CAAC;SAEe,sBAAsB,CAAC,OAAY,EAAE,OAAgB,EAAE,KAAU,EAAA;IAC/E,OAAO,CAAC,aAAa,CAAC,CAAC,CAAU,EAAE,GAAkB,KAAI;AACvD,QAAA,IAAI,KAAK,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE;YAC5B,MAAM,IAAIA,aAAY,CAElB,IAAA,+CAAA,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,IAAI,wBAAwB,CAAC,OAAO,EAAE,GAAG,CAAC;AACtC,gBAAA,EAAE,CAAC,CAAC;SAC3D;AACH,KAAC,CAAC,CAAC;AACL,CAAC;AAuKD;AAEA;;;;;;;;;;;;;;;;AAgBG;MACmB,eAAe,CAAA;AAyEnC;;;;;;;AAOG;IACH,WACI,CAAA,UAA0C,EAC1C,eAAyD,EAAA;;QAjF7D,IAAa,CAAA,aAAA,GAAG,KAAK,CAAC;AAEtB;;;;AAIG;QACH,IAA4B,CAAA,4BAAA,GAAG,KAAK,CAAC;;QAGrC,IAAe,CAAA,eAAA,GAAG,KAAK,CAAC;;AAGxB,QAAA,IAAA,CAAA,mBAAmB,GAAG,MAAK,GAAG,CAAC;QAKvB,IAAO,CAAA,OAAA,GAA6B,IAAI,CAAC;AAmLjD;;;;;;AAMG;QACa,IAAQ,CAAA,QAAA,GAAY,IAAI,CAAC;AAazC;;;;;AAKG;QACa,IAAO,CAAA,OAAA,GAAY,KAAK,CAAC;;QAgxBzC,IAAiB,CAAA,iBAAA,GAAyC,EAAE,CAAC;AA75B3D,QAAA,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;AACnC,QAAA,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC;KAC9C;AAED;;;;AAIG;AACH,IAAA,IAAI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,oBAAoB,CAAC;KAClC;IACD,IAAI,SAAS,CAAC,WAA6B,EAAA;QACzC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,oBAAoB,GAAG,WAAW,CAAC;KAC/D;AAED;;;;AAIG;AACH,IAAA,IAAI,cAAc,GAAA;QAChB,OAAO,IAAI,CAAC,yBAAyB,CAAC;KACvC;IACD,IAAI,cAAc,CAAC,gBAAuC,EAAA;QACxD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,yBAAyB,GAAG,gBAAgB,CAAC;KAC9E;AAED;;AAEG;AACH,IAAA,IAAI,MAAM,GAAA;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;AAYD;;;;;;;AAOG;AACH,IAAA,IAAI,KAAK,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC;KAC9B;AAED;;;;;;;AAOG;AACH,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,MAAM,KAAK,OAAO,CAAC;KAChC;AAED;;;;;;;AAOG;AACH,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC;KAC/B;AAED;;;;;;;;;;AAUG;AACH,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,CAAC;KACjC;AAED;;;;;;;;AAQG;AACH,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,CAAC;KACjC;AAiBD;;;;;;AAMG;AACH,IAAA,IAAI,KAAK,GAAA;AACP,QAAA,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC;KACvB;AAUD;;;;;AAKG;AACH,IAAA,IAAI,SAAS,GAAA;AACX,QAAA,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;KACtB;AAyBD;;;;;AAKG;AACH,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAC;KAC1F;AAED;;;;;;;;;AASG;AACH,IAAA,aAAa,CAAC,UAA0C,EAAA;AACtD,QAAA,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;KACpC;AAED;;;;;;;;;AASG;AACH,IAAA,kBAAkB,CAAC,UAAoD,EAAA;AACrE,QAAA,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;KACzC;AAED;;;;;;;;;;;AAWG;AACH,IAAA,aAAa,CAAC,UAAqC,EAAA;AACjD,QAAA,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;KACpE;AAED;;;;;;;;;;AAUG;AACH,IAAA,kBAAkB,CAAC,UAA+C,EAAA;AAChE,QAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;KAC9E;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BG;AACH,IAAA,gBAAgB,CAAC,UAAqC,EAAA;AACpD,QAAA,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;KACvE;AAED;;;;;;;;;;AAUG;AACH,IAAA,qBAAqB,CAAC,UAA+C,EAAA;AACnE,QAAA,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;KACjF;AAED;;;;;;;;;;;;;;;;;;;;;;AAsBG;AACH,IAAA,YAAY,CAAC,SAAsB,EAAA;QACjC,OAAO,YAAY,CAAC,IAAI,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;KACrD;AAED;;;;;;;AAOG;AACH,IAAA,iBAAiB,CAAC,SAA2B,EAAA;QAC3C,OAAO,YAAY,CAAC,IAAI,CAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC;KAC1D;AAED;;;;;;AAMG;IACH,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;KACvB;AAED;;;;;;AAMG;IACH,oBAAoB,GAAA;AAClB,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;KAC5B;AAED;;;;;;;;;;;;AAYG;IACH,aAAa,CAAC,OAA6B,EAAE,EAAA;AAC1C,QAAA,IAAuB,CAAC,OAAO,GAAG,IAAI,CAAC;QAExC,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClC,YAAA,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;SAClC;KACF;AAED;;;AAGG;IACH,gBAAgB,GAAA;QACd,IAAI,CAAC,aAAa,CAAC,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;AAErC,QAAA,IAAI,CAAC,aAAa,CAAC,CAAC,OAAwB,KAAK,OAAO,CAAC,gBAAgB,EAAE,CAAC,CAAC;KAC9E;AAED;;;;;;;;;;;;;;AAcG;IACH,eAAe,CAAC,OAA6B,EAAE,EAAA;AAC5C,QAAA,IAAuB,CAAC,OAAO,GAAG,KAAK,CAAC;AACzC,QAAA,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;AAE7B,QAAA,IAAI,CAAC,aAAa,CAAC,CAAC,OAAwB,KAAI;YAC9C,OAAO,CAAC,eAAe,CAAC,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;AAC5C,SAAC,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClC,YAAA,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;SACnC;KACF;AAED;;;;;;;;;;;;AAYG;IACH,WAAW,CAAC,OAA6B,EAAE,EAAA;AACxC,QAAA,IAAuB,CAAC,QAAQ,GAAG,KAAK,CAAC;QAE1C,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClC,YAAA,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SAChC;KACF;AAED;;;;;;;;;;;;;;;AAeG;IACH,cAAc,CAAC,OAA6B,EAAE,EAAA;AAC3C,QAAA,IAAuB,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzC,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAE3B,QAAA,IAAI,CAAC,aAAa,CAAC,CAAC,OAAwB,KAAI;YAC9C,OAAO,CAAC,cAAc,CAAC,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;AAC3C,SAAC,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClC,YAAA,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;SACpC;KACF;AAED;;;;;;;;;;;;;;;AAeG;IACH,aAAa,CAAC,OAAkD,EAAE,EAAA;AAC/D,QAAA,IAAuB,CAAC,MAAM,GAAG,OAAO,CAAC;AAE1C,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;YAC3B,IAAI,CAAC,aAAiD,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC3E;QAED,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClC,YAAA,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;SAClC;KACF;AAED;;;;;;;;;;;;;;;;AAgBG;IACH,OAAO,CAAC,OAAkD,EAAE,EAAA;;;QAG1D,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAEhE,QAAA,IAAuB,CAAC,MAAM,GAAG,QAAQ,CAAC;AAC1C,QAAA,IAAuB,CAAC,MAAM,GAAG,IAAI,CAAC;AACvC,QAAA,IAAI,CAAC,aAAa,CAAC,CAAC,OAAwB,KAAI;AAC9C,YAAA,OAAO,CAAC,OAAO,CAAC,EAAC,GAAG,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;AAC7C,SAAC,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,EAAE,CAAC;AAEpB,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;YAC3B,IAAI,CAAC,YAAqC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5D,IAAI,CAAC,aAAiD,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC3E;QAED,IAAI,CAAC,gBAAgB,CAAC,EAAC,GAAG,IAAI,EAAE,iBAAiB,EAAC,CAAC,CAAC;AACpD,QAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;KAC9D;AAED;;;;;;;;;;;;;;;;;AAiBG;IACH,MAAM,CAAC,OAAkD,EAAE,EAAA;;;QAGzD,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAEhE,QAAA,IAAuB,CAAC,MAAM,GAAG,KAAK,CAAC;AACxC,QAAA,IAAI,CAAC,aAAa,CAAC,CAAC,OAAwB,KAAI;AAC9C,YAAA,OAAO,CAAC,MAAM,CAAC,EAAC,GAAG,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;AAC5C,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,sBAAsB,CAAC,EAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAC,CAAC,CAAC;QAEzE,IAAI,CAAC,gBAAgB,CAAC,EAAC,GAAG,IAAI,EAAE,iBAAiB,EAAC,CAAC,CAAC;AACpD,QAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;KAC/D;AAEO,IAAA,gBAAgB,CACpB,IAA4E,EAAA;QAC9E,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClC,YAAA,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;AAC1C,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;AAC3B,gBAAA,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;aAChC;AACD,YAAA,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;SAC/B;KACF;AAED;;;;AAIG;AACH,IAAA,SAAS,CAAC,MAAgC,EAAA;AACxC,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;KACvB;AAiBD;;;AAGG;IACH,WAAW,GAAA;QACT,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;AAED;;;;;;;;;;;;;AAaG;IACH,sBAAsB,CAAC,OAAkD,EAAE,EAAA;QACzE,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,YAAY,EAAE,CAAC;AAEpB,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,2BAA2B,EAAE,CAAC;AAClC,YAAA,IAAuB,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;AACtD,YAAA,IAAuB,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAE1D,YAAA,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,IAAI,IAAI,CAAC,MAAM,KAAK,OAAO,EAAE;AACpD,gBAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aACzC;SACF;AAED,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;YAC3B,IAAI,CAAC,YAAqC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5D,IAAI,CAAC,aAAiD,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC3E;QAED,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClC,YAAA,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;SAC3C;KACF;;AAGD,IAAA,mBAAmB,CAAC,IAA8B,GAAA,EAAC,SAAS,EAAE,IAAI,EAAC,EAAA;AACjE,QAAA,IAAI,CAAC,aAAa,CAAC,CAAC,IAAqB,KAAK,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;AAC9E,QAAA,IAAI,CAAC,sBAAsB,CAAC,EAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAC,CAAC,CAAC;KAC1E;IAEO,iBAAiB,GAAA;AACtB,QAAA,IAAuB,CAAC,MAAM,GAAG,IAAI,CAAC,oBAAoB,EAAE,GAAG,QAAQ,GAAG,KAAK,CAAC;KAClF;IAEO,aAAa,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;KACrD;AAEO,IAAA,kBAAkB,CAAC,SAAmB,EAAA;AAC5C,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE;AACtB,YAAA,IAAuB,CAAC,MAAM,GAAG,OAAO,CAAC;AAC1C,YAAA,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC;YACzC,MAAM,GAAG,GAAG,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;YACpD,IAAI,CAAC,4BAA4B,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,MAA6B,KAAI;AAClF,gBAAA,IAAI,CAAC,4BAA4B,GAAG,KAAK,CAAC;;;;gBAI1C,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAC,SAAS,EAAC,CAAC,CAAC;AACtC,aAAC,CAAC,CAAC;SACJ;KACF;IAEO,2BAA2B,GAAA;AACjC,QAAA,IAAI,IAAI,CAAC,4BAA4B,EAAE;AACrC,YAAA,IAAI,CAAC,4BAA4B,CAAC,WAAW,EAAE,CAAC;AAChD,YAAA,IAAI,CAAC,4BAA4B,GAAG,KAAK,CAAC;SAC3C;KACF;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BG;AACH,IAAA,SAAS,CAAC,MAA6B,EAAE,IAAA,GAA8B,EAAE,EAAA;AACtE,QAAA,IAAuB,CAAC,MAAM,GAAG,MAAM,CAAC;QACzC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,CAAC;KACtD;AAmBD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;AACH,IAAA,GAAG,CAAyC,IAAO,EAAA;QAEjD,IAAI,QAAQ,GAAgC,IAAI,CAAC;QACjD,IAAI,QAAQ,IAAI,IAAI;AAAE,YAAA,OAAO,IAAI,CAAC;AAClC,QAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC;AAAE,YAAA,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC7D,QAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC;AAAE,YAAA,OAAO,IAAI,CAAC;QACvC,OAAO,QAAQ,CAAC,MAAM,CAClB,CAAC,OAA6B,EAAE,IAAI,KAAK,OAAO,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;KACpF;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BG;IACH,QAAQ,CAAC,SAAiB,EAAE,IAAkC,EAAA;AAC5D,QAAA,MAAM,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AAC7C,QAAA,OAAO,OAAO,IAAI,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;KACrE;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;IACH,QAAQ,CAAC,SAAiB,EAAE,IAAkC,EAAA;QAC5D,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;KACzC;AAED;;AAEG;AACH,IAAA,IAAI,IAAI,GAAA;QACN,IAAI,CAAC,GAAoB,IAAI,CAAC;AAE9B,QAAA,OAAO,CAAC,CAAC,OAAO,EAAE;AAChB,YAAA,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC;SACf;AAED,QAAA,OAAO,CAAC,CAAC;KACV;;AAGD,IAAA,qBAAqB,CAAC,SAAkB,EAAA;AACrC,QAAA,IAAuB,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE1D,IAAI,SAAS,EAAE;YACZ,IAAI,CAAC,aAAiD,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC3E;AAED,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;SAC/C;KACF;;IAGD,gBAAgB,GAAA;AACb,QAAA,IAAuB,CAAC,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;AAC1D,QAAA,IAAuB,CAAC,aAAa,GAAG,IAAI,YAAY,EAAE,CAAC;KAC7D;IAGO,gBAAgB,GAAA;QACtB,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAAE,YAAA,OAAO,QAAQ,CAAC;QACjD,IAAI,IAAI,CAAC,MAAM;AAAE,YAAA,OAAO,OAAO,CAAC;QAChC,IAAI,IAAI,CAAC,4BAA4B,IAAI,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;AAAE,YAAA,OAAO,OAAO,CAAC;AAC9F,QAAA,IAAI,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;AAAE,YAAA,OAAO,OAAO,CAAC;AACzD,QAAA,OAAO,KAAK,CAAC;KACd;;AAkBD,IAAA,sBAAsB,CAAC,MAAyB,EAAA;AAC9C,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,OAAwB,KAAK,OAAO,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;KACnF;;IAGD,iBAAiB,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,OAAwB,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;KACvE;;IAGD,mBAAmB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,OAAwB,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC;KACzE;;IAGD,eAAe,CAAC,OAA6B,EAAE,EAAA;QAC5C,IAAuB,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAE9D,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClC,YAAA,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;SACpC;KACF;;IAGD,cAAc,CAAC,OAA6B,EAAE,EAAA;AAC3C,QAAA,IAAuB,CAAC,OAAO,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE9D,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClC,YAAA,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;SACnC;KACF;;AAMD,IAAA,2BAA2B,CAAC,EAAc,EAAA;AACxC,QAAA,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;KAC/B;;AAGD,IAAA,kBAAkB,CAAC,IAA4D,EAAA;QAC7E,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;AAC/C,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAS,CAAC;SACjC;KACF;AACD;;;;AAIG;AACK,IAAA,kBAAkB,CAAC,QAAkB,EAAA;QAC3C,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;AACvD,QAAA,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,OAAQ,CAAC,iBAAiB,EAAE,CAAC;KACzE;;AAGD,IAAA,KAAK,CAAC,IAAmB,EAAA;AACvB,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;AAIG;AACK,IAAA,iBAAiB,CAAC,UAA0C,EAAA;QAClE,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,KAAK,EAAE,GAAG,UAAU,CAAC;QAClF,IAAI,CAAC,oBAAoB,GAAG,iBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;KACpE;AAED;;;;AAIG;AACK,IAAA,sBAAsB,CAAC,UAAoD,EAAA;QACjF,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,KAAK,EAAE,GAAG,UAAU,CAAC;QACvF,IAAI,CAAC,yBAAyB,GAAG,sBAAsB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;KACnF;AACF;;AC90CD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgHG;AACG,MAAO,SAAgF,SACzF,eAEiE,CAAA;AACnE;;;;;;;;;;;;AAYG;AACH,IAAA,WAAA,CACI,QAAkB,EAAE,eAAuE,EAC3F,cAAyD,EAAA;AAC3D,QAAA,KAAK,CAAC,cAAc,CAAC,eAAe,CAAC,EAAE,mBAAmB,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC,CAAC;AAC7F,QAAA,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,yBAAyB,CAAC,QAAQ,CAAC,CAAC;AACvF,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxB,QAAA,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;QACzC,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,sBAAsB,CAAC;AAC1B,YAAA,QAAQ,EAAE,IAAI;;;;AAId,YAAA,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc;AACjC,SAAA,CAAC,CAAC;KACJ;IAmBD,eAAe,CAAkC,IAAO,EAAE,OAAoB,EAAA;AAC5E,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AAAE,YAAA,OAAQ,IAAI,CAAC,QAAgB,CAAC,IAAI,CAAC,CAAC;AAC7D,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;AAC9B,QAAA,OAAO,CAAC,SAAS,CAAC,IAAiB,CAAC,CAAC;AACrC,QAAA,OAAO,CAAC,2BAA2B,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AAC9D,QAAA,OAAO,OAAO,CAAC;KAChB;AAyBD,IAAA,UAAU,CAAkC,IAAO,EAAE,OAA8B,EAAE,UAEjF,EAAE,EAAA;AACJ,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACpC,IAAI,CAAC,sBAAsB,CAAC,EAAC,SAAS,EAAE,OAAO,CAAC,SAAS,EAAC,CAAC,CAAC;QAC5D,IAAI,CAAC,mBAAmB,EAAE,CAAC;KAC5B;AASD;;;;;;;;;;;;AAYG;AACH,IAAA,aAAa,CAAC,IAAY,EAAE,OAAA,GAAkC,EAAE,EAAA;AAC9D,QAAA,IAAK,IAAI,CAAC,QAAgB,CAAC,IAAI,CAAC;AAC7B,YAAA,IAAI,CAAC,QAAgB,CAAC,IAAI,CAAC,CAAC,2BAA2B,CAAC,MAAO,GAAC,CAAC,CAAC;QACrE,QAAS,IAAI,CAAC,QAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;QACtC,IAAI,CAAC,sBAAsB,CAAC,EAAC,SAAS,EAAE,OAAO,CAAC,SAAS,EAAC,CAAC,CAAC;QAC5D,IAAI,CAAC,mBAAmB,EAAE,CAAC;KAC5B;AAuBD,IAAA,UAAU,CAAkC,IAAO,EAAE,OAAoB,EAAE,UAEvE,EAAE,EAAA;AACJ,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AAAE,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,2BAA2B,CAAC,MAAO,GAAC,CAAC,CAAC;QACnF,QAAQ,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;AAC7B,QAAA,IAAI,OAAO;AAAE,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACjD,IAAI,CAAC,sBAAsB,CAAC,EAAC,SAAS,EAAE,OAAO,CAAC,SAAS,EAAC,CAAC,CAAC;QAC5D,IAAI,CAAC,mBAAmB,EAAE,CAAC;KAC5B;AAeD,IAAA,QAAQ,CAAkC,WAAc,EAAA;AACtD,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC;KACxF;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCG;AACM,IAAA,QAAQ,CAAC,KAAmC,EAAE,OAAA,GAGnD,EAAE,EAAA;AACJ,QAAA,sBAAsB,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QACzC,MAAM,CAAC,IAAI,CAAC,KAAK,CAA2B,CAAC,OAAO,CAAC,IAAI,IAAG;AAC3D,YAAA,oBAAoB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAW,CAAC,CAAC;YAC7C,IAAI,CAAC,QAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAChC,KAAa,CAAC,IAAI,CAAC,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,EAAC,CAAC,CAAC;AAC5E,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;KACtC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BG;AACM,IAAA,UAAU,CAAC,KAAgC,EAAE,OAAA,GAGlD,EAAE,EAAA;;;;;AAKJ,QAAA,IAAI,KAAK,IAAI,IAAI;YAAoC,OAAO;QAC3D,MAAM,CAAC,IAAI,CAAC,KAAK,CAA2B,CAAC,OAAO,CAAC,IAAI,IAAG;;;YAG3D,MAAM,OAAO,GAAI,IAAI,CAAC,QAAgB,CAAC,IAAI,CAAC,CAAC;YAC7C,IAAI,OAAO,EAAE;AACX,gBAAA,OAAO,CAAC,UAAU;AACd,0EAA0D,KAAK,CAC1D,IAAuC,CAAE,EAC9C,EAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,EAAC,CAAC,CAAC;aACrD;AACH,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;KACtC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwDG;AACM,IAAA,KAAK,CACV,KAAA,GAAmE,EACtC,EAC7B,UAAqD,EAAE,EAAA;QACzD,IAAI,CAAC,aAAa,CAAC,CAAC,OAAwB,EAAE,IAAI,KAAI;AACpD,YAAA,OAAO,CAAC,KAAK,CACT,KAAK,GAAI,KAAa,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,EAAC,CAAC,CAAC;AAC3F,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;AAC9B,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;AAC7B,QAAA,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;KACtC;AAED;;;;AAIG;IACM,WAAW,GAAA;AAClB,QAAA,OAAO,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,KAAI;YACpD,GAAW,CAAC,IAAI,CAAC,GAAI,OAAe,CAAC,WAAW,EAAE,CAAC;AACpD,YAAA,OAAO,GAAG,CAAC;AACb,SAAC,CAAQ,CAAC;KACX;;IAGQ,oBAAoB,GAAA;AAC3B,QAAA,IAAI,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC,OAAgB,EAAE,KAAK,KAAI;AAC3E,YAAA,OAAO,KAAK,CAAC,oBAAoB,EAAE,GAAG,IAAI,GAAG,OAAO,CAAC;AACvD,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,cAAc;YAAE,IAAI,CAAC,sBAAsB,CAAC,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;AAClE,QAAA,OAAO,cAAc,CAAC;KACvB;;AAGQ,IAAA,aAAa,CAAC,EAA4B,EAAA;AACjD,QAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,GAAG,IAAG;;;;YAIvC,MAAM,OAAO,GAAI,IAAI,CAAC,QAAgB,CAAC,GAAG,CAAC,CAAC;AAC5C,YAAA,OAAO,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAC9B,SAAC,CAAC,CAAC;KACJ;;IAGD,cAAc,GAAA;AACZ,QAAA,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,KAAI;AAC7B,YAAA,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACxB,YAAA,OAAO,CAAC,2BAA2B,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AAChE,SAAC,CAAC,CAAC;KACJ;;IAGQ,YAAY,GAAA;AAClB,QAAA,IAAuB,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,EAAS,CAAC;KAC7D;;AAGQ,IAAA,YAAY,CAAC,SAA0C,EAAA;AAC9D,QAAA,KAAK,MAAM,CAAC,WAAW,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;AAClE,YAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAkB,CAAC,IAAI,SAAS,CAAC,OAAc,CAAC,EAAE;AAClE,gBAAA,OAAO,IAAI,CAAC;aACb;SACF;AACD,QAAA,OAAO,KAAK,CAAC;KACd;;IAGD,YAAY,GAAA;QACV,IAAI,GAAG,GAAsB,EAAE,CAAC;AAChC,QAAA,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,KAAI;YACtD,IAAI,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;AACpC,gBAAA,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC;aAC3B;AACD,YAAA,OAAO,GAAG,CAAC;AACb,SAAC,CAAC,CAAC;KACJ;;IAGD,eAAe,CACX,SAAY,EAAE,EAAgD,EAAA;QAChE,IAAI,GAAG,GAAG,SAAS,CAAC;QACpB,IAAI,CAAC,aAAa,CAAC,CAAC,OAAoB,EAAE,IAAO,KAAI;YACnD,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AAC/B,SAAC,CAAC,CAAC;AACH,QAAA,OAAO,GAAG,CAAC;KACZ;;IAGQ,oBAAoB,GAAA;AAC3B,QAAA,KAAK,MAAM,WAAW,IAAK,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAA2B,EAAE;YAC/E,IAAK,IAAI,CAAC,QAAgB,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE;AAC/C,gBAAA,OAAO,KAAK,CAAC;aACd;SACF;AACD,QAAA,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC;KAC/D;;AAGQ,IAAA,KAAK,CAAC,IAAmB,EAAA;QAChC,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAc,CAAC;AAC9C,YAAA,IAAI,CAAC,QAAgB,CAAC,IAAsB,CAAC;AAC9C,YAAA,IAAI,CAAC;KACV;AACF,CAAA;AAED;;;AAGG;AACH,SAAS,yBAAyB,CAC9B,QAA6D,EAAA;IAC/D,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;AAC3E,IAAA,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;;AAE1B,QAAA,OAAO,CAAC,IAAI,CAAC,CAAA,kEAAA,EACT,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAG,CAAA,CAAA,CAAC,CAAC;KAC/B;AACH,CAAC;AAoBM,MAAM,gBAAgB,GAAyB,UAAU;AAEhE;;;;;AAKG;AACI,MAAM,WAAW,GAAG,CAAC,OAAgB,KAA2B,OAAO,YAAY,UAAU;AAEpG;;;;;;;;;;;;;;;;;;AAkBG;AACG,MAAO,UAA+D,SACxE,SAAoC,CAAA;AAAG,CAAA;AAgF3C;;;;;AAKG;AACI,MAAM,YAAY,GAAG,CAAC,OAAgB,KACzC,OAAO,YAAY;;ACzsBvB;;;;;AAKG;AACI,MAAM,uBAAuB,GAAG,IAAI,cAAc,CACrD,sBAAsB,EAAE,EAAC,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,uBAAuB,EAAC,CAAC,CAAC;AAY1F;;AAEG;AACI,MAAM,uBAAuB,GAA2B,QAAQ,CAAC;AAExD,SAAA,WAAW,CAAC,IAAiB,EAAE,MAAwB,EAAA;IACrE,OAAO,CAAC,GAAG,MAAM,CAAC,IAAK,EAAE,IAAK,CAAC,CAAC;AAClC,CAAC;AAED;;;;;;AAMG;AACG,SAAU,YAAY,CACxB,OAAoB,EAAE,GAAc,EACpC,uBAA+C,uBAAuB,EAAA;AACxE,IAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,QAAA,IAAI,CAAC,OAAO;AAAE,YAAA,WAAW,CAAC,GAAG,EAAE,0BAA0B,CAAC,CAAC;QAC3D,IAAI,CAAC,GAAG,CAAC,aAAa;YAAE,+BAA+B,CAAC,GAAG,CAAC,CAAC;KAC9D;AAED,IAAA,eAAe,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IAE9B,GAAG,CAAC,aAAc,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;;;IAK7C,IAAI,OAAO,CAAC,QAAQ,IAAI,oBAAoB,KAAK,QAAQ,EAAE;QACzD,GAAG,CAAC,aAAc,CAAC,gBAAgB,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;KACzD;AAED,IAAA,uBAAuB,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AACtC,IAAA,wBAAwB,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAEvC,IAAA,iBAAiB,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAEhC,IAAA,0BAA0B,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAC3C,CAAC;AAED;;;;;;;;;;AAUG;AACG,SAAU,cAAc,CAC1B,OAAyB,EAAE,GAAc,EACzC,kCAA2C,IAAI,EAAA;IACjD,MAAM,IAAI,GAAG,MAAK;QAChB,IAAI,+BAA+B,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YACtF,eAAe,CAAC,GAAG,CAAC,CAAC;SACtB;AACH,KAAC,CAAC;;;;;;AAOF,IAAA,IAAI,GAAG,CAAC,aAAa,EAAE;AACrB,QAAA,GAAG,CAAC,aAAa,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;AACzC,QAAA,GAAG,CAAC,aAAa,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;KAC3C;AAED,IAAA,iBAAiB,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IAEhC,IAAI,OAAO,EAAE;QACX,GAAG,CAAC,yBAAyB,EAAE,CAAC;QAChC,OAAO,CAAC,2BAA2B,CAAC,MAAO,GAAC,CAAC,CAAC;KAC/C;AACH,CAAC;AAED,SAAS,yBAAyB,CAAI,UAA2B,EAAE,QAAoB,EAAA;AACrF,IAAA,UAAU,CAAC,OAAO,CAAC,CAAC,SAAsB,KAAI;QAC5C,IAAgB,SAAU,CAAC,yBAAyB;AACtC,YAAA,SAAU,CAAC,yBAA0B,CAAC,QAAQ,CAAC,CAAC;AAChE,KAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;AAMG;AACa,SAAA,0BAA0B,CAAC,OAAoB,EAAE,GAAc,EAAA;AAC7E,IAAA,IAAI,GAAG,CAAC,aAAc,CAAC,gBAAgB,EAAE;AACvC,QAAA,MAAM,gBAAgB,GAAG,CAAC,UAAmB,KAAI;AAC/C,YAAA,GAAG,CAAC,aAAc,CAAC,gBAAiB,CAAC,UAAU,CAAC,CAAC;AACnD,SAAC,CAAC;AACF,QAAA,OAAO,CAAC,wBAAwB,CAAC,gBAAgB,CAAC,CAAC;;;AAInD,QAAA,GAAG,CAAC,kBAAkB,CAAC,MAAK;AAC1B,YAAA,OAAO,CAAC,2BAA2B,CAAC,gBAAgB,CAAC,CAAC;AACxD,SAAC,CAAC,CAAC;KACJ;AACH,CAAC;AAED;;;;;;AAMG;AACa,SAAA,eAAe,CAAC,OAAwB,EAAE,GAA6B,EAAA;AACrF,IAAA,MAAM,UAAU,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;AACjD,IAAA,IAAI,GAAG,CAAC,SAAS,KAAK,IAAI,EAAE;AAC1B,QAAA,OAAO,CAAC,aAAa,CAAC,eAAe,CAAc,UAAU,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;KAChF;AAAM,SAAA,IAAI,OAAO,UAAU,KAAK,UAAU,EAAE;;;;;;;;AAQ3C,QAAA,OAAO,CAAC,aAAa,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;KACrC;AAED,IAAA,MAAM,eAAe,GAAG,yBAAyB,CAAC,OAAO,CAAC,CAAC;AAC3D,IAAA,IAAI,GAAG,CAAC,cAAc,KAAK,IAAI,EAAE;AAC/B,QAAA,OAAO,CAAC,kBAAkB,CACtB,eAAe,CAAmB,eAAe,EAAE,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC;KAC7E;AAAM,SAAA,IAAI,OAAO,eAAe,KAAK,UAAU,EAAE;AAChD,QAAA,OAAO,CAAC,kBAAkB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC;KAC/C;;IAGD,MAAM,iBAAiB,GAAG,MAAM,OAAO,CAAC,sBAAsB,EAAE,CAAC;AACjE,IAAA,yBAAyB,CAAc,GAAG,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;AAC9E,IAAA,yBAAyB,CAAmB,GAAG,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;AAC1F,CAAC;AAED;;;;;;;;AAQG;AACa,SAAA,iBAAiB,CAC7B,OAA6B,EAAE,GAA6B,EAAA;IAC9D,IAAI,gBAAgB,GAAG,KAAK,CAAC;AAC7B,IAAA,IAAI,OAAO,KAAK,IAAI,EAAE;AACpB,QAAA,IAAI,GAAG,CAAC,SAAS,KAAK,IAAI,EAAE;AAC1B,YAAA,MAAM,UAAU,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;AACjD,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;;AAEtD,gBAAA,MAAM,iBAAiB,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,SAAS,KAAK,SAAS,KAAK,GAAG,CAAC,SAAS,CAAC,CAAC;gBACxF,IAAI,iBAAiB,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM,EAAE;oBAClD,gBAAgB,GAAG,IAAI,CAAC;AACxB,oBAAA,OAAO,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;iBAC1C;aACF;SACF;AAED,QAAA,IAAI,GAAG,CAAC,cAAc,KAAK,IAAI,EAAE;AAC/B,YAAA,MAAM,eAAe,GAAG,yBAAyB,CAAC,OAAO,CAAC,CAAC;AAC3D,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;;AAEhE,gBAAA,MAAM,sBAAsB,GACxB,eAAe,CAAC,MAAM,CAAC,CAAC,cAAc,KAAK,cAAc,KAAK,GAAG,CAAC,cAAc,CAAC,CAAC;gBACtF,IAAI,sBAAsB,CAAC,MAAM,KAAK,eAAe,CAAC,MAAM,EAAE;oBAC5D,gBAAgB,GAAG,IAAI,CAAC;AACxB,oBAAA,OAAO,CAAC,kBAAkB,CAAC,sBAAsB,CAAC,CAAC;iBACpD;aACF;SACF;KACF;;AAGD,IAAA,MAAM,IAAI,GAAG,MAAK,GAAG,CAAC;AACtB,IAAA,yBAAyB,CAAc,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACjE,IAAA,yBAAyB,CAAmB,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;AAE3E,IAAA,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAED,SAAS,uBAAuB,CAAC,OAAoB,EAAE,GAAc,EAAA;IACnE,GAAG,CAAC,aAAc,CAAC,gBAAgB,CAAC,CAAC,QAAa,KAAI;AACpD,QAAA,OAAO,CAAC,aAAa,GAAG,QAAQ,CAAC;AACjC,QAAA,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC;AAC9B,QAAA,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;AAE7B,QAAA,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ;AAAE,YAAA,aAAa,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AACjE,KAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,iBAAiB,CAAC,OAAoB,EAAE,GAAc,EAAA;AAC7D,IAAA,GAAG,CAAC,aAAc,CAAC,iBAAiB,CAAC,MAAK;AACxC,QAAA,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC;QAE/B,IAAI,OAAO,CAAC,QAAQ,KAAK,MAAM,IAAI,OAAO,CAAC,cAAc;AAAE,YAAA,aAAa,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AACvF,QAAA,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ;YAAE,OAAO,CAAC,aAAa,EAAE,CAAC;AAC7D,KAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,aAAa,CAAC,OAAoB,EAAE,GAAc,EAAA;IACzD,IAAI,OAAO,CAAC,aAAa;QAAE,OAAO,CAAC,WAAW,EAAE,CAAC;AACjD,IAAA,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,EAAE,EAAC,qBAAqB,EAAE,KAAK,EAAC,CAAC,CAAC;AACxE,IAAA,GAAG,CAAC,iBAAiB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AAC7C,IAAA,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;AACjC,CAAC;AAED,SAAS,wBAAwB,CAAC,OAAoB,EAAE,GAAc,EAAA;AACpE,IAAA,MAAM,QAAQ,GAAG,CAAC,QAAc,EAAE,cAAwB,KAAI;;AAE5D,QAAA,GAAG,CAAC,aAAc,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;;AAGxC,QAAA,IAAI,cAAc;AAAE,YAAA,GAAG,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;AACtD,KAAC,CAAC;AACF,IAAA,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;;;AAInC,IAAA,GAAG,CAAC,kBAAkB,CAAC,MAAK;AAC1B,QAAA,OAAO,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;AACxC,KAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;AAMG;AACa,SAAA,kBAAkB,CAC9B,OAA4B,EAAE,GAA6C,EAAA;IAC7E,IAAI,OAAO,IAAI,IAAI,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC;AACpE,QAAA,WAAW,CAAC,GAAG,EAAE,0BAA0B,CAAC,CAAC;AAC/C,IAAA,eAAe,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAChC,CAAC;AAED;;;;;;AAMG;AACa,SAAA,oBAAoB,CAChC,OAA4B,EAAE,GAA6C,EAAA;AAC7E,IAAA,OAAO,iBAAiB,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AACzC,CAAC;AAED,SAAS,eAAe,CAAC,GAAc,EAAA;AACrC,IAAA,OAAO,WAAW,CAAC,GAAG,EAAE,wEAAwE,CAAC,CAAC;AACpG,CAAC;AAED,SAAS,WAAW,CAAC,GAA6B,EAAE,OAAe,EAAA;AACjE,IAAA,MAAM,UAAU,GAAG,wBAAwB,CAAC,GAAG,CAAC,CAAC;IACjD,MAAM,IAAI,KAAK,CAAC,CAAA,EAAG,OAAO,CAAI,CAAA,EAAA,UAAU,CAAE,CAAA,CAAC,CAAC;AAC9C,CAAC;AAED,SAAS,wBAAwB,CAAC,GAA6B,EAAA;AAC7D,IAAA,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;AACtB,IAAA,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC;QAAE,OAAO,CAAA,OAAA,EAAU,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;AACnE,IAAA,IAAI,IAAI,GAAG,CAAC,CAAC;QAAE,OAAO,CAAA,OAAA,EAAU,IAAI,CAAA,CAAA,CAAG,CAAC;AACxC,IAAA,OAAO,4BAA4B,CAAC;AACtC,CAAC;AAED,SAAS,+BAA+B,CAAC,GAA6B,EAAA;AACpE,IAAA,MAAM,GAAG,GAAG,wBAAwB,CAAC,GAAG,CAAC,CAAC;AAC1C,IAAA,MAAM,IAAIA,aAAY,CAAA,CAAA,IAAA,mDAC0B,sCAAsC,GAAG,CAAA,CAAA,CAAG,CAAC,CAAC;AAChG,CAAC;AAED,SAAS,+BAA+B,CAAC,GAA6B,EAAA;AACpE,IAAA,MAAM,GAAG,GAAG,wBAAwB,CAAC,GAAG,CAAC,CAAC;AAC1C,IAAA,MAAM,IAAIA,aAAY,CAElB,IAAA,wDAAA,CAAA,kEAAA,EAAqE,GAAG,CAAI,EAAA,CAAA;AACxE,QAAA,CAAA,uFAAA,CAAyF,CAAC,CAAC;AACrG,CAAC;AAEe,SAAA,iBAAiB,CAAC,OAA6B,EAAE,SAAc,EAAA;AAC7E,IAAA,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC;AAAE,QAAA,OAAO,KAAK,CAAC;AACnD,IAAA,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;IAEhC,IAAI,MAAM,CAAC,aAAa,EAAE;AAAE,QAAA,OAAO,IAAI,CAAC;IACxC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC;AACpD,CAAC;AAEK,SAAU,iBAAiB,CAAC,aAAmC,EAAA;;;IAGnE,OAAO,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC,WAAW,CAAC,KAAK,2BAA2B,CAAC;AAC1F,CAAC;AAEe,SAAA,mBAAmB,CAAC,IAAe,EAAE,UAAsC,EAAA;IACzF,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,IAAA,UAAU,CAAC,OAAO,CAAC,CAAC,GAAc,KAAI;AACpC,QAAA,MAAM,OAAO,GAAG,GAAG,CAAC,OAAsB,CAAC;QAC3C,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,IAAI,OAAO,CAAC,cAAc,EAAE;AAC3D,YAAA,GAAG,CAAC,iBAAiB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AAC7C,YAAA,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;SAChC;AACH,KAAC,CAAC,CAAC;AACL,CAAC;AAED;AACgB,SAAA,mBAAmB,CAC/B,GAAc,EAAE,cAAsC,EAAA;AACxD,IAAA,IAAI,CAAC,cAAc;AAAE,QAAA,OAAO,IAAI,CAAC;AAEjC,IAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC;QACnF,+BAA+B,CAAC,GAAG,CAAC,CAAC;IAEvC,IAAI,eAAe,GAAmC,SAAS,CAAC;IAChE,IAAI,eAAe,GAAmC,SAAS,CAAC;IAChE,IAAI,cAAc,GAAmC,SAAS,CAAC;AAE/D,IAAA,cAAc,CAAC,OAAO,CAAC,CAAC,CAAuB,KAAI;AACjD,QAAA,IAAI,CAAC,CAAC,WAAW,KAAK,oBAAoB,EAAE;YAC1C,eAAe,GAAG,CAAC,CAAC;SACrB;AAAM,aAAA,IAAI,iBAAiB,CAAC,CAAC,CAAC,EAAE;YAC/B,IAAI,eAAe,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC;AACpE,gBAAA,WAAW,CAAC,GAAG,EAAE,iEAAiE,CAAC,CAAC;YACtF,eAAe,GAAG,CAAC,CAAC;SACrB;aAAM;YACL,IAAI,cAAc,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC;AACnE,gBAAA,WAAW,CAAC,GAAG,EAAE,+DAA+D,CAAC,CAAC;YACpF,cAAc,GAAG,CAAC,CAAC;SACpB;AACH,KAAC,CAAC,CAAC;AAEH,IAAA,IAAI,cAAc;AAAE,QAAA,OAAO,cAAc,CAAC;AAC1C,IAAA,IAAI,eAAe;AAAE,QAAA,OAAO,eAAe,CAAC;AAC5C,IAAA,IAAI,eAAe;AAAE,QAAA,OAAO,eAAe,CAAC;AAE5C,IAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,QAAA,WAAW,CAAC,GAAG,EAAE,+CAA+C,CAAC,CAAC;KACnE;AACD,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAEe,SAAAG,gBAAc,CAAI,IAAS,EAAE,EAAK,EAAA;IAChD,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC/B,IAAI,KAAK,GAAG,CAAC,CAAC;AAAE,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACxC,CAAC;AAED;AACM,SAAU,eAAe,CAC3B,IAAY,EAAE,IAAwC,EACtD,QAAwC,EAAE,aAA0B,EAAA;IACtE,IAAI,aAAa,KAAK,OAAO;QAAE,OAAO;AAEtC,IAAA,IAAI,CAAC,CAAC,aAAa,KAAK,IAAI,IAAI,aAAa,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC,uBAAuB;SACrF,aAAa,KAAK,QAAQ,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;QACjE,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;AACnC,QAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;AACpC,QAAA,QAAQ,CAAC,mBAAmB,GAAG,IAAI,CAAC;KACrC;AACH;;ACzYA,MAAMC,uBAAqB,GAAa;AACtC,IAAA,OAAO,EAAE,gBAAgB;AACzB,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,MAAM,CAAC;CACtC,CAAC;AAEF,MAAMC,iBAAe,GAAG,CAAC,MAAM,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;AAEpD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+DG;AAQG,MAAO,MAAO,SAAQ,gBAAgB,CAAA;AAiC1C,IAAA,WAAA,CAC+C,UAAqC,EAC/B,eACV,EACc,oBAC3B,EAAA;AAC5B,QAAA,KAAK,EAAE,CAAC;QAF+C,IAAoB,CAAA,oBAAA,GAApB,oBAAoB,CAC/C;AArC9B;;;AAGG;QACa,IAAS,CAAA,SAAA,GAAY,KAAK,CAAC;AAEnC,QAAA,IAAA,CAAA,WAAW,GAAG,IAAI,GAAG,EAAW,CAAC;AAQzC;;;AAGG;AACH,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,YAAY,EAAE,CAAC;AAqB5B,QAAA,IAAI,CAAC,IAAI;AACL,YAAA,IAAI,SAAS,CAAC,EAAE,EAAE,iBAAiB,CAAC,UAAU,CAAC,EAAE,sBAAsB,CAAC,eAAe,CAAC,CAAC,CAAC;KAC/F;;IAGD,eAAe,GAAA;QACb,IAAI,CAAC,kBAAkB,EAAE,CAAC;KAC3B;AAED;;;AAGG;AACH,IAAA,IAAa,aAAa,GAAA;AACxB,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;AAGG;AACH,IAAA,IAAa,OAAO,GAAA;QAClB,OAAO,IAAI,CAAC,IAAI,CAAC;KAClB;AAED;;;;AAIG;AACH,IAAA,IAAa,IAAI,GAAA;AACf,QAAA,OAAO,EAAE,CAAC;KACX;AAED;;;AAGG;AACH,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;KAC3B;AAED;;;;;;AAMG;AACH,IAAA,UAAU,CAAC,GAAY,EAAA;AACrB,QAAAA,iBAAe,CAAC,IAAI,CAAC,MAAK;YACxB,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC/C,YAAA,GAAyB,CAAC,OAAO;gBACjB,SAAS,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;YAClE,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAC1D,GAAG,CAAC,OAAO,CAAC,sBAAsB,CAAC,EAAC,SAAS,EAAE,KAAK,EAAC,CAAC,CAAC;AACvD,YAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC5B,SAAC,CAAC,CAAC;KACJ;AAED;;;;;AAKG;AACH,IAAA,UAAU,CAAC,GAAY,EAAA;QACrB,OAAoB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;KAC7C;AAED;;;;;AAKG;AACH,IAAA,aAAa,CAAC,GAAY,EAAA;AACxB,QAAAA,iBAAe,CAAC,IAAI,CAAC,MAAK;YACxB,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAChD,IAAI,SAAS,EAAE;AACb,gBAAA,SAAS,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;aACnC;AACD,YAAA,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC/B,SAAC,CAAC,CAAC;KACJ;AAED;;;;;AAKG;AACH,IAAA,YAAY,CAAC,GAAiB,EAAA;AAC5B,QAAAA,iBAAe,CAAC,IAAI,CAAC,MAAK;YACxB,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAChD,YAAA,MAAM,KAAK,GAAG,IAAI,SAAS,CAAC,EAAE,CAAC,CAAC;AAChC,YAAA,kBAAkB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YAC/B,SAAS,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAC3C,KAAK,CAAC,sBAAsB,CAAC,EAAC,SAAS,EAAE,KAAK,EAAC,CAAC,CAAC;AACnD,SAAC,CAAC,CAAC;KACJ;AAED;;;;;AAKG;AACH,IAAA,eAAe,CAAC,GAAiB,EAAA;AAC/B,QAAAA,iBAAe,CAAC,IAAI,CAAC,MAAK;YACxB,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAChD,IAAI,SAAS,EAAE;AACb,gBAAA,SAAS,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;aACnC;AACH,SAAC,CAAC,CAAC;KACJ;AAED;;;;;AAKG;AACH,IAAA,YAAY,CAAC,GAAiB,EAAA;QAC5B,OAAkB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;KAC3C;AAED;;;;;AAKG;IACH,WAAW,CAAC,GAAc,EAAE,KAAU,EAAA;AACpC,QAAAA,iBAAe,CAAC,IAAI,CAAC,MAAK;AACxB,YAAA,MAAM,IAAI,GAAgB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAK,CAAC,CAAC;AACnD,YAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACvB,SAAC,CAAC,CAAC;KACJ;AAED;;;;;AAKG;AACH,IAAA,QAAQ,CAAC,KAA2B,EAAA;AAClC,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;KAC9B;AAED;;;;;;AAMG;AACH,IAAA,QAAQ,CAAC,MAAa,EAAA;AACnB,QAAA,IAAuB,CAAC,SAAS,GAAG,IAAI,CAAC;QAC1C,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AACjD,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;;;AAG3B,QAAA,OAAQ,MAAM,EAAE,MAAiC,EAAE,MAAM,KAAK,QAAQ,CAAC;KACxE;AAED;;;AAGG;IACH,OAAO,GAAA;QACL,IAAI,CAAC,SAAS,EAAE,CAAC;KAClB;AAED;;;;;AAKG;IACH,SAAS,CAAC,QAAa,SAAS,EAAA;AAC9B,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACtB,QAAA,IAAuB,CAAC,SAAS,GAAG,KAAK,CAAC;KAC5C;IAEO,kBAAkB,GAAA;AACxB,QAAA,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,EAAE;YACjD,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;SAC7C;KACF;AAEO,IAAA,cAAc,CAAC,IAAc,EAAA;QACnC,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,OAAO,IAAI,CAAC,MAAM,GAAc,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;KACjE;AA1OU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,MAAM,EAkCe,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,aAAa,EACb,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,mBAAmB,yCAE3B,uBAAuB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GArCpC,MAAM,EAAA,QAAA,EAAA,wDAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,CAAA,eAAA,EAAA,SAAA,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,OAAA,EAAA,WAAA,EAAA,EAAA,EAAA,SAAA,EALN,CAACD,uBAAqB,CAAC,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAKvB,MAAM,EAAA,UAAA,EAAA,CAAA;kBAPlB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,wDAAwD;oBAClE,SAAS,EAAE,CAACA,uBAAqB,CAAC;oBAClC,IAAI,EAAE,EAAC,UAAU,EAAE,kBAAkB,EAAE,SAAS,EAAE,WAAW,EAAC;oBAC9D,OAAO,EAAE,CAAC,UAAU,CAAC;AACrB,oBAAA,QAAQ,EAAE,QAAQ;AACnB,iBAAA,CAAA;;0BAmCM,QAAQ;;0BAAI,IAAI;;0BAAI,MAAM;2BAAC,aAAa,CAAA;;0BACxC,QAAQ;;0BAAI,IAAI;;0BAAI,MAAM;2BAAC,mBAAmB,CAAA;;0BAE9C,QAAQ;;0BAAI,MAAM;2BAAC,uBAAuB,CAAA;yCANvB,OAAO,EAAA,CAAA;sBAA9B,KAAK;uBAAC,eAAe,CAAA;;;AC5HR,SAAA,cAAc,CAAI,IAAS,EAAE,EAAK,EAAA;IAChD,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC/B,IAAI,KAAK,GAAG,CAAC,CAAC;AAAE,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACxC;;AC2YA,SAAS,kBAAkB,CAAC,SAAkB,EAAA;AAC5C,IAAA,OAAO,OAAO,SAAS,KAAK,QAAQ,IAAI,SAAS,KAAK,IAAI;AACtD,QAAA,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,IAAI,SAAS,IAAI,UAAU,IAAI,SAAS,CAAC;AAC7F,CAAC;MAEY,WAAW,IACnB,MAAM,WAA0B,SAAQ,eAC7B,CAAA;AAaV,IAAA,WAAA;;AAEI,IAAA,SAAA,GAA6C,IAAyB,EACtE,eAAmE,EACnE,cAAyD,EAAA;AAC3D,QAAA,KAAK,CACD,cAAc,CAAC,eAAe,CAAC,EAAE,mBAAmB,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC,CAAC;;QAjB7E,IAAY,CAAA,YAAA,GAAW,IAAyB,CAAC;;QAGjE,IAAS,CAAA,SAAA,GAAoB,EAAE,CAAC;;QAMhC,IAAc,CAAA,cAAA,GAAY,KAAK,CAAC;AAS9B,QAAA,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;AAChC,QAAA,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;QACzC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,sBAAsB,CAAC;AAC1B,YAAA,QAAQ,EAAE,IAAI;;;;;AAKd,YAAA,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc;AACjC,SAAA,CAAC,CAAC;QACH,IAAI,YAAY,CAAC,eAAe,CAAC;aAC5B,eAAe,CAAC,WAAW,IAAI,eAAe,CAAC,qBAAqB,CAAC,EAAE;AAC1E,YAAA,IAAI,kBAAkB,CAAC,SAAS,CAAC,EAAE;AACjC,gBAAA,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,KAAK,CAAC;aACrC;iBAAM;AACL,gBAAA,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;aAC/B;SACF;KACF;AAEQ,IAAA,QAAQ,CAAC,KAAa,EAAE,OAAA,GAK7B,EAAE,EAAA;QACH,IAAuB,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC5D,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,OAAO,CAAC,qBAAqB,KAAK,KAAK,EAAE;YACpE,IAAI,CAAC,SAAS,CAAC,OAAO,CAClB,CAAC,QAAQ,KAAK,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,qBAAqB,KAAK,KAAK,CAAC,CAAC,CAAC;SAClF;AACD,QAAA,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;KACtC;AAEQ,IAAA,UAAU,CAAC,KAAa,EAAE,OAAA,GAK/B,EAAE,EAAA;AACJ,QAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;KAC/B;AAEQ,IAAA,KAAK,CACV,SAA6C,GAAA,IAAI,CAAC,YAAY,EAC9D,UAAqD,EAAE,EAAA;AACzD,QAAA,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;AAChC,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;AAC7B,QAAA,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAC9B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACnC,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;KAC7B;;AAGQ,IAAA,YAAY,MAAW;;AAGvB,IAAA,YAAY,CAAC,SAA0C,EAAA;AAC9D,QAAA,OAAO,KAAK,CAAC;KACd;;IAGQ,oBAAoB,GAAA;QAC3B,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;AAED,IAAA,gBAAgB,CAAC,EAAY,EAAA;AAC3B,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KACzB;;AAGD,IAAA,mBAAmB,CAAC,EAAmD,EAAA;AACrE,QAAA,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;KACpC;AAED,IAAA,wBAAwB,CAAC,EAAiC,EAAA;AACxD,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KACjC;;AAGD,IAAA,2BAA2B,CAAC,EAAiC,EAAA;AAC3D,QAAA,cAAc,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;KAC5C;;IAGQ,aAAa,CAAC,EAAgC,EAAA,GAAU;;IAGxD,oBAAoB,GAAA;AAC3B,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE;YAC9B,IAAI,IAAI,CAAC,aAAa;gBAAE,IAAI,CAAC,WAAW,EAAE,CAAC;YAC3C,IAAI,IAAI,CAAC,eAAe;gBAAE,IAAI,CAAC,aAAa,EAAE,CAAC;AAC/C,YAAA,IAAI,IAAI,CAAC,cAAc,EAAE;AACvB,gBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAE,qBAAqB,EAAE,KAAK,EAAC,CAAC,CAAC;AAClF,gBAAA,OAAO,IAAI,CAAC;aACb;SACF;AACD,QAAA,OAAO,KAAK,CAAC;KACd;AAEO,IAAA,eAAe,CAAC,SAA0C,EAAA;AAChE,QAAA,IAAI,kBAAkB,CAAC,SAAS,CAAC,EAAE;YAChC,IAAuB,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC,KAAK,CAAC;YACtE,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,EAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAC,CAAC;AAChD,gBAAA,IAAI,CAAC,MAAM,CAAC,EAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAC,CAAC,CAAC;SACtE;aAAM;YACJ,IAAuB,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;SACjE;KACF;AACF,CAAA,EAAE;AAoBA,MAAM,kBAAkB,GAA2B,YAAY;AAEtE;;;;;AAKG;AACI,MAAM,aAAa,GAAG,CAAC,OAAgB,KAC1C,OAAO,YAAY;;AC1iBvB;;;;;AAKG;AAEG,MAAO,0BAA2B,SAAQ,gBAAgB,CAAA;;IAW9D,QAAQ,GAAA;QACN,IAAI,CAAC,gBAAgB,EAAE,CAAC;;AAExB,QAAA,IAAI,CAAC,aAAc,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;KACxC;;IAGD,WAAW,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;;AAEtB,YAAA,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;SAC1C;KACF;AAED;;;AAGG;AACH,IAAA,IAAa,OAAO,GAAA;QAClB,OAAO,IAAI,CAAC,aAAc,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;KAC/C;AAED;;;AAGG;AACH,IAAA,IAAa,IAAI,GAAA;QACf,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;KACxF;AAED;;;AAGG;AACH,IAAA,IAAa,aAAa,GAAA;AACxB,QAAA,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;KACzD;;AAGD,IAAA,gBAAgB,MAAW;yHAlDhB,0BAA0B,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAA1B,0BAA0B,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAA1B,0BAA0B,EAAA,UAAA,EAAA,CAAA;kBADtC,SAAS;;;SCTM,oBAAoB,GAAA;IAClC,OAAO,IAAIJ,aAAY,CAAyC,IAAA,+CAAA,CAAA;;;;MAI5D,sBAAsB,CAAA;;;;;;MAMtB,2BAA2B,CAAA,CAAE,CAAC,CAAC;AACrC,CAAC;SAEe,sBAAsB,GAAA;IACpC,OAAO,IAAIA,aAAY,CAA8C,IAAA,oDAAA,CAAA;;;;;MAKjE,oBAAoB,CAAA;;;;MAIpB,mBAAmB,CAAA,CAAE,CAAC,CAAC;AAC7B,CAAC;SAEe,oBAAoB,GAAA;IAClC,OAAO,IAAIA,aAAY,CAEnB,IAAA,8CAAA,CAAA;;;;AAIsF,2FAAA,CAAA,CAAC,CAAC;AAC9F,CAAC;SAEe,yBAAyB,GAAA;IACvC,OAAO,IAAIA,aAAY,CAA8C,IAAA,oDAAA,CAAA;;;;;MAKjE,oBAAoB,CAAA;;;;MAIpB,mBAAmB,CAAA,CAAE,CAAC,CAAC;AAC7B;;AC7CO,MAAM,kBAAkB,GAAQ;AACrC,IAAA,OAAO,EAAE,gBAAgB;AACzB,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,YAAY,CAAC;CAC5C,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;AAyBG;AAEG,MAAO,YAAa,SAAQ,0BAA0B,CAAA;AAQ1D,IAAA,WAAA,CACwB,MAAwB,EACD,UAAqC,EAC/B,eACV,EAAA;AACzC,QAAA,KAAK,EAAE,CAAC;AAZV;;;;AAIG;QAC6B,IAAI,CAAA,IAAA,GAAW,EAAE,CAAC;AAQhD,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;AACtB,QAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;AAChC,QAAA,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC;KAC3C;;IAGQ,gBAAgB,GAAA;AACvB,QAAA,IAAI,EAAE,IAAI,CAAC,OAAO,YAAY,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,YAAY,MAAM,CAAC;aAC3E,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YACnD,MAAM,yBAAyB,EAAE,CAAC;SACnC;KACF;yHAzBU,YAAY,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAM,gBAAA,EAAA,IAAA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAUS,aAAa,EAAA,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EACb,mBAAmB,EAAA,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAXxC,YAAY,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,CAAA,cAAA,EAAA,MAAA,CAAA,EAAA,EAAA,SAAA,EAD0B,CAAC,kBAAkB,CAAC,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAC1D,YAAY,EAAA,UAAA,EAAA,CAAA;kBADxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA,EAAC,QAAQ,EAAE,gBAAgB,EAAE,SAAS,EAAE,CAAC,kBAAkB,CAAC,EAAE,QAAQ,EAAE,cAAc,EAAC,CAAA;;0BAU3F,IAAI;;0BAAI,QAAQ;;0BAChB,QAAQ;;0BAAI,IAAI;;0BAAI,MAAM;2BAAC,aAAa,CAAA;;0BACxC,QAAQ;;0BAAI,IAAI;;0BAAI,MAAM;2BAAC,mBAAmB,CAAA;yCALnB,IAAI,EAAA,CAAA;sBAAnC,KAAK;uBAAC,cAAc,CAAA;;;AChCvB,MAAMC,oBAAkB,GAAa;AACnC,IAAA,OAAO,EAAE,SAAS;AAClB,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,OAAO,CAAC;CACvC,CAAC;AAEF;;;;;;;;;;;;;;;;AAgBG;AACH,MAAM,eAAe,GAAG,CAAC,MAAM,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;AAEpD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoFG;AAMG,MAAO,OAAQ,SAAQ,SAAS,CAAA;IAmEpC,WACwB,CAAA,MAAwB,EACD,UAAqC,EAC/B,eACV,EACQ,cAAsC,EACtC,kBAA2C,EACrC,oBAC3B,EAAA;AAC5B,QAAA,KAAK,EAAE,CAAC;QAHyC,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAyB;QACrC,IAAoB,CAAA,oBAAA,GAApB,oBAAoB,CAC/C;AA1EL,QAAA,IAAA,CAAA,OAAO,GAAgB,IAAI,WAAW,EAAE,CAAC;;QAYlE,IAAW,CAAA,WAAA,GAAG,KAAK,CAAC;AAQpB;;;;AAIG;QACe,IAAI,CAAA,IAAA,GAAW,EAAE,CAAC;AAkCpC;;;;AAIG;AACsB,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,YAAY,EAAE,CAAC;AAYnD,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;AACtB,QAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;AAChC,QAAA,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC;QAC1C,IAAI,CAAC,aAAa,GAAG,mBAAmB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;KAChE;;AAGD,IAAA,WAAW,CAAC,OAAsB,EAAA;QAChC,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,MAAM,IAAI,OAAO,EAAE;AAC1C,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,IAAI,CAAC,UAAU,EAAE,CAAC;AAClB,gBAAA,IAAI,IAAI,CAAC,aAAa,EAAE;;;;;oBAKtB,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC;oBAC9C,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAC,CAAC,CAAC;iBACjF;aACF;YACD,IAAI,CAAC,aAAa,EAAE,CAAC;SACtB;AACD,QAAA,IAAI,YAAY,IAAI,OAAO,EAAE;AAC3B,YAAA,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;SAC/B;QAED,IAAI,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE;AAC9C,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC9B,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;SAC7B;KACF;;IAGD,WAAW,GAAA;QACT,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;KAC9D;AAED;;;;AAIG;AACH,IAAA,IAAa,IAAI,GAAA;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACjC;AAED;;;AAGG;AACH,IAAA,IAAI,aAAa,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;KACzD;AAED;;;;;AAKG;AACM,IAAA,iBAAiB,CAAC,QAAa,EAAA;AACtC,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC1B,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KAC5B;IAEO,aAAa,GAAA;QACnB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,aAAa,EAAE,GAAG,IAAI,CAAC,gBAAgB,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AACrF,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;KACzB;IAEO,kBAAkB,GAAA;AACxB,QAAA,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,EAAE;YACjD,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;SAChD;KACF;IAEO,aAAa,GAAA;AACnB,QAAA,OAAO,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,EAAE,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;KACrE;IAEO,gBAAgB,GAAA;QACtB,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC5D,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,EAAC,SAAS,EAAE,KAAK,EAAC,CAAC,CAAC;KACzD;IAEO,eAAe,GAAA;AACrB,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE;YACzB,IAAI,CAAC,gBAAgB,EAAE,CAAC;SACzB;QACD,IAAI,CAAC,UAAU,EAAE,CAAC;KACnB;IAEO,gBAAgB,GAAA;AACtB,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,IAAI,EAAE,IAAI,CAAC,OAAO,YAAY,YAAY,CAAC;AACvC,gBAAA,IAAI,CAAC,OAAO,YAAY,0BAA0B,EAAE;gBACtD,MAAM,sBAAsB,EAAE,CAAC;aAChC;AAAM,iBAAA,IAAI,EAAE,IAAI,CAAC,OAAO,YAAY,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,YAAY,MAAM,CAAC,EAAE;gBACvF,MAAM,oBAAoB,EAAE,CAAC;aAC9B;SACF;KACF;IAEO,UAAU,GAAA;QAChB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI;YAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAErE,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YAC1F,MAAM,oBAAoB,EAAE,CAAC;SAC9B;KACF;AAEO,IAAA,YAAY,CAAC,KAAU,EAAA;AAC7B,QAAA,eAAe,CAAC,IAAI,CAAC,MAAK;AACxB,YAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAC,qBAAqB,EAAE,KAAK,EAAC,CAAC,CAAC;AAC7D,YAAA,IAAI,CAAC,kBAAkB,EAAE,YAAY,EAAE,CAAC;AAC1C,SAAC,CAAC,CAAC;KACJ;AAEO,IAAA,eAAe,CAAC,OAAsB,EAAA;QAC5C,MAAM,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC;;QAEzD,MAAM,UAAU,GAAG,aAAa,KAAK,CAAC,IAAI,gBAAgB,CAAC,aAAa,CAAC,CAAC;AAE1E,QAAA,eAAe,CAAC,IAAI,CAAC,MAAK;YACxB,IAAI,UAAU,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;AACxC,gBAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;aACxB;iBAAM,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;AAC/C,gBAAA,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;aACvB;AAED,YAAA,IAAI,CAAC,kBAAkB,EAAE,YAAY,EAAE,CAAC;AAC1C,SAAC,CAAC,CAAC;KACJ;AAEO,IAAA,QAAQ,CAAC,WAAmB,EAAA;QAClC,OAAO,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;KAC9E;yHAvNU,OAAO,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAD,gBAAA,EAAA,IAAA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAqEc,aAAa,EACb,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,mBAAmB,yCAEnB,iBAAiB,EAAA,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EACzB,iBAAiB,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EACjB,uBAAuB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GA1EpC,OAAO,EAAA,QAAA,EAAA,qDAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,UAAA,EAAA,CAAA,UAAA,EAAA,YAAA,CAAA,EAAA,KAAA,EAAA,CAAA,SAAA,EAAA,OAAA,CAAA,EAAA,OAAA,EAAA,CAAA,gBAAA,EAAA,SAAA,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,eAAA,EAAA,EAAA,SAAA,EAHP,CAACC,oBAAkB,CAAC,EAAA,QAAA,EAAA,CAAA,SAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAGpB,OAAO,EAAA,UAAA,EAAA,CAAA;kBALnB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,qDAAqD;oBAC/D,SAAS,EAAE,CAACA,oBAAkB,CAAC;AAC/B,oBAAA,QAAQ,EAAE,SAAS;AACpB,iBAAA,CAAA;;0BAqEM,QAAQ;;0BAAI,IAAI;;0BAChB,QAAQ;;0BAAI,IAAI;;0BAAI,MAAM;2BAAC,aAAa,CAAA;;0BACxC,QAAQ;;0BAAI,IAAI;;0BAAI,MAAM;2BAAC,mBAAmB,CAAA;;0BAE9C,QAAQ;;0BAAI,IAAI;;0BAAI,MAAM;2BAAC,iBAAiB,CAAA;;0BAC5C,QAAQ;;0BAAI,MAAM;2BAAC,iBAAiB,CAAA;;0BACpC,QAAQ;;0BAAI,MAAM;2BAAC,uBAAuB,CAAA;yCAhD7B,IAAI,EAAA,CAAA;sBAArB,KAAK;gBAOa,UAAU,EAAA,CAAA;sBAA5B,KAAK;uBAAC,UAAU,CAAA;gBAMC,KAAK,EAAA,CAAA;sBAAtB,KAAK;uBAAC,SAAS,CAAA;gBAmBS,OAAO,EAAA,CAAA;sBAA/B,KAAK;uBAAC,gBAAgB,CAAA;gBAOE,MAAM,EAAA,CAAA;sBAA9B,MAAM;uBAAC,eAAe,CAAA;;;ACjMzB;;;;;;;;;;;;;;;;AAgBG;MAKU,aAAa,CAAA;yHAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAAb,aAAa,EAAA,QAAA,EAAA,8CAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,YAAA,EAAA,EAAA,EAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBAJzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,8CAA8C;AACxD,oBAAA,IAAI,EAAE,EAAC,YAAY,EAAE,EAAE,EAAC;AACzB,iBAAA,CAAA;;;AClBD,MAAM,qBAAqB,GAAa;AACtC,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,mBAAmB,CAAC;AAClD,IAAA,KAAK,EAAE,IAAI;CACZ,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;AAuBG;AAOG,MAAO,mBAAoB,SAAQ,2BAA2B,CAAA;AAElE;;;AAGG;AACH,IAAA,UAAU,CAAC,KAAa,EAAA;;AAEtB,QAAA,MAAM,eAAe,GAAG,KAAK,IAAI,IAAI,GAAG,EAAE,GAAG,KAAK,CAAC;AACnD,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;KAC5C;AAED;;;AAGG;AACM,IAAA,gBAAgB,CAAC,EAA4B,EAAA;AACpD,QAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,KAAK,KAAI;AACxB,YAAA,EAAE,CAAC,KAAK,IAAI,EAAE,GAAG,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;AAC7C,SAAC,CAAC;KACH;yHApBU,mBAAmB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAAnB,mBAAmB,EAAA,QAAA,EAAA,iGAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,+BAAA,EAAA,MAAA,EAAA,aAAA,EAAA,EAAA,EAAA,SAAA,EAFnB,CAAC,qBAAqB,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAEvB,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAN/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EACJ,iGAAiG;oBACrG,IAAI,EAAE,EAAC,SAAS,EAAE,+BAA+B,EAAE,QAAQ,EAAE,aAAa,EAAC;oBAC3E,SAAS,EAAE,CAAC,qBAAqB,CAAC;AACnC,iBAAA,CAAA;;;AC/BD,MAAM,oBAAoB,GAAa;AACrC,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,yBAAyB,CAAC;AACxD,IAAA,KAAK,EAAE,IAAI;CACZ,CAAC;AAEF,SAAS,cAAc,GAAA;IACrB,MAAM,IAAIP,aAAY,CAAyD,IAAA,+DAAA,CAAA;;;AAG5E,IAAA,CAAA,CAAC,CAAC;AACP,CAAC;AAED;;;AAGG;MAEU,oBAAoB,CAAA;AADjC,IAAA,WAAA,GAAA;QAEU,IAAU,CAAA,UAAA,GAAU,EAAE,CAAC;AA0ChC,KAAA;AAxCC;;;AAGG;IACH,GAAG,CAAC,OAAkB,EAAE,QAAmC,EAAA;QACzD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;KAC3C;AAED;;;AAGG;AACH,IAAA,MAAM,CAAC,QAAmC,EAAA;AACxC,QAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;AACpD,YAAA,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;gBACtC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC7B,OAAO;aACR;SACF;KACF;AAED;;;AAGG;AACH,IAAA,MAAM,CAAC,QAAmC,EAAA;QACxC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,KAAI;AAC5B,YAAA,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;gBACvD,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;aAClC;AACH,SAAC,CAAC,CAAC;KACJ;IAEO,YAAY,CAChB,WAAmD,EACnD,QAAmC,EAAA;AACrC,QAAA,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO;AAAE,YAAA,OAAO,KAAK,CAAC;QAC1C,OAAO,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,KAAK,QAAQ,CAAC,QAAQ,CAAC,OAAO;YACvD,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,CAAC;KAC3C;yHA1CU,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAApB,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,cADR,MAAM,EAAA,CAAA,CAAA,EAAA;;sGAClB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBADhC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;AA8ChC;;;;;;;;;;;;;;;;;;;AAmBG;AAOG,MAAO,yBAA0B,SAAQ,2BAA2B,CAAA;AA+CxE,IAAA,WAAA,CACI,QAAmB,EAAE,UAAsB,EAAU,SAA+B,EAC5E,SAAmB,EAAA;AAC7B,QAAA,KAAK,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QAF2B,IAAS,CAAA,SAAA,GAAT,SAAS,CAAsB;QAC5E,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QArCvB,IAAqB,CAAA,qBAAA,GAAG,KAAK,CAAC;AAEtC;;;;;;AAMG;AACM,QAAA,IAAA,CAAA,QAAQ,GAAG,MAAK,GAAG,CAAC;AAuBrB,QAAA,IAAA,CAAA,oBAAoB,GACxB,MAAM,CAAC,uBAAuB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,IAAI,uBAAuB,CAAC;KAMhF;;IAGD,QAAQ,GAAA;QACN,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC9C,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;KACzC;;IAGD,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;KAC7B;AAED;;;AAGG;AACH,IAAA,UAAU,CAAC,KAAU,EAAA;QACnB,IAAI,CAAC,MAAM,GAAG,KAAK,KAAK,IAAI,CAAC,KAAK,CAAC;QACnC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;KAC1C;AAED;;;AAGG;AACM,IAAA,gBAAgB,CAAC,EAAkB,EAAA;AAC1C,QAAA,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;AACd,QAAA,IAAI,CAAC,QAAQ,GAAG,MAAK;AACnB,YAAA,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACf,YAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC9B,SAAC,CAAC;KACH;;AAGQ,IAAA,gBAAgB,CAAC,UAAmB,EAAA;AAC3C;;;;;;;;;;;;;;;;;AAiBG;AACH,QAAA,IAAI,IAAI,CAAC,qBAAqB,IAAI,UAAU;AACxC,YAAA,IAAI,CAAC,oBAAoB,KAAK,2BAA2B,EAAE;AAC7D,YAAA,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;SAC1C;AACD,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;KACnC;AAED;;;;AAIG;AACH,IAAA,WAAW,CAAC,KAAU,EAAA;AACpB,QAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;KACxB;IAEO,UAAU,GAAA;AAChB,QAAA,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,eAAe;aACtE,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AACnD,YAAA,cAAc,EAAE,CAAC;SAClB;AACD,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,eAAe;AAAE,YAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC;KAC1E;yHAhIU,yBAAyB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,oBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,QAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAAzB,yBAAyB,EAAA,QAAA,EAAA,8FAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,aAAA,EAAA,EAAA,EAAA,SAAA,EAFzB,CAAC,oBAAoB,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAEtB,yBAAyB,EAAA,UAAA,EAAA,CAAA;kBANrC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EACJ,8FAA8F;oBAClG,IAAI,EAAE,EAAC,UAAU,EAAE,YAAY,EAAE,QAAQ,EAAE,aAAa,EAAC;oBACzD,SAAS,EAAE,CAAC,oBAAoB,CAAC;AAClC,iBAAA,CAAA;8JA6BU,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAQG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAMG,KAAK,EAAA,CAAA;sBAAb,KAAK;;;ACvIR,MAAM,oBAAoB,GAAa;AACrC,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,kBAAkB,CAAC;AACjD,IAAA,KAAK,EAAE,IAAI;CACZ,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;AAuBG;AAWG,MAAO,kBAAmB,SAAQ,2BAA2B,CAAA;AAEjE;;;AAGG;AACH,IAAA,UAAU,CAAC,KAAU,EAAA;QACnB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;KAC9C;AAED;;;AAGG;AACM,IAAA,gBAAgB,CAAC,EAA4B,EAAA;AACpD,QAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,KAAK,KAAI;AACxB,YAAA,EAAE,CAAC,KAAK,IAAI,EAAE,GAAG,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;AAC7C,SAAC,CAAC;KACH;yHAlBU,kBAAkB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAAlB,kBAAkB,EAAA,QAAA,EAAA,8FAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,QAAA,EAAA,+BAAA,EAAA,OAAA,EAAA,+BAAA,EAAA,MAAA,EAAA,aAAA,EAAA,EAAA,EAAA,SAAA,EAFlB,CAAC,oBAAoB,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAEtB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAV9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EACJ,8FAA8F;AAClG,oBAAA,IAAI,EAAE;AACJ,wBAAA,UAAU,EAAE,+BAA+B;AAC3C,wBAAA,SAAS,EAAE,+BAA+B;AAC1C,wBAAA,QAAQ,EAAE,aAAa;AACxB,qBAAA;oBACD,SAAS,EAAE,CAAC,oBAAoB,CAAC;AAClC,iBAAA,CAAA;;;AChCD;;AAEG;AACI,MAAM,kCAAkC,GAC3C,IAAI,cAAc,CAAC,SAAS,GAAG,+BAA+B,GAAG,EAAE,CAAC,CAAC;AAEzE,MAAM,kBAAkB,GAAa;AACnC,IAAA,OAAO,EAAE,SAAS;AAClB,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,oBAAoB,CAAC;CACpD,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;AAqBG;AAEG,MAAO,oBAAqB,SAAQ,SAAS,CAAA;AAcjD;;;AAGG;IACH,IACI,UAAU,CAAC,UAAmB,EAAA;AAChC,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;SACnC;KACF;AAUD;;;;;;AAMG;aACI,IAAuB,CAAA,uBAAA,GAAG,KAAH,CAAS,EAAA;IAWvC,WAC+C,CAAA,UAAqC,EAC/B,eACV,EACQ,cAAsC,EACrB,qBAC5D,EACiD,oBAC3B,EAAA;AAC5B,QAAA,KAAK,EAAE,CAAC;QAJ0D,IAAqB,CAAA,qBAAA,GAArB,qBAAqB,CACjF;QACiD,IAAoB,CAAA,oBAAA,GAApB,oBAAoB,CAC/C;;AA5BL,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,YAAY,EAAE,CAAC;AAWrD;;;;;;AAMG;QACH,IAAmB,CAAA,mBAAA,GAAG,KAAK,CAAC;AAY1B,QAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;AAChC,QAAA,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC;QAC1C,IAAI,CAAC,aAAa,GAAG,mBAAmB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;KAChE;;AAGD,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE;YACnC,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC;YACnD,IAAI,YAAY,EAAE;gBAChB,cAAc,CAAC,YAAY,EAAE,IAAI,wCAAwC,KAAK,CAAC,CAAC;aACjF;YACD,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACzD,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,EAAC,SAAS,EAAE,KAAK,EAAC,CAAC,CAAC;SACtD;QACD,IAAI,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE;AAC9C,YAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;gBACjD,eAAe,CAAC,aAAa,EAAE,oBAAoB,EAAE,IAAI,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;aACxF;YACD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC/B,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;SAC7B;KACF;;IAGD,WAAW,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,wCAAwC,KAAK,CAAC,CAAC;SAC9E;KACF;AAED;;;;AAIG;AACH,IAAA,IAAa,IAAI,GAAA;AACf,QAAA,OAAO,EAAE,CAAC;KACX;AAED;;;AAGG;AACH,IAAA,IAAa,OAAO,GAAA;QAClB,OAAO,IAAI,CAAC,IAAI,CAAC;KAClB;AAED;;;;;AAKG;AACM,IAAA,iBAAiB,CAAC,QAAa,EAAA;AACtC,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC1B,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KAC5B;AAEO,IAAA,iBAAiB,CAAC,OAA6B,EAAA;AACrD,QAAA,OAAO,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;KACvC;yHA1HU,oBAAoB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAoDC,aAAa,EACb,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,mBAAmB,yCAEnB,iBAAiB,EAAA,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EACzB,kCAAkC,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAElC,uBAAuB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GA1DpC,oBAAoB,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,CAAA,aAAA,EAAA,MAAA,CAAA,EAAA,UAAA,EAAA,CAAA,UAAA,EAAA,YAAA,CAAA,EAAA,KAAA,EAAA,CAAA,SAAA,EAAA,OAAA,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,eAAA,EAAA,EAAA,SAAA,EADiB,CAAC,kBAAkB,CAAC,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGACzD,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBADhC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA,EAAC,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,CAAC,kBAAkB,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAC,CAAA;;0BAqDpF,QAAQ;;0BAAI,IAAI;;0BAAI,MAAM;2BAAC,aAAa,CAAA;;0BACxC,QAAQ;;0BAAI,IAAI;;0BAAI,MAAM;2BAAC,mBAAmB,CAAA;;0BAE9C,QAAQ;;0BAAI,IAAI;;0BAAI,MAAM;2BAAC,iBAAiB,CAAA;;0BAC5C,QAAQ;;0BAAI,MAAM;2BAAC,kCAAkC,CAAA;;0BAErD,QAAQ;;0BAAI,MAAM;2BAAC,uBAAuB,CAAA;yCA9CzB,IAAI,EAAA,CAAA;sBAAzB,KAAK;uBAAC,aAAa,CAAA;gBAOhB,UAAU,EAAA,CAAA;sBADb,KAAK;uBAAC,UAAU,CAAA;gBAUC,KAAK,EAAA,CAAA;sBAAtB,KAAK;uBAAC,SAAS,CAAA;gBAGS,MAAM,EAAA,CAAA;sBAA9B,MAAM;uBAAC,eAAe,CAAA;;;AC7DzB,MAAM,qBAAqB,GAAa;AACtC,IAAA,OAAO,EAAE,gBAAgB;AACzB,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,kBAAkB,CAAC;CAClD,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;AAuBG;AAOG,MAAO,kBAAmB,SAAQ,gBAAgB,CAAA;AAqCtD,IAAA,WAAA,CAC+C,UAAqC,EAC/B,eACV,EACc,oBAC3B,EAAA;AAC5B,QAAA,KAAK,EAAE,CAAC;QAF+C,IAAoB,CAAA,oBAAA,GAApB,oBAAoB,CAC/C;AAzC9B;;;AAGG;QACa,IAAS,CAAA,SAAA,GAAY,KAAK,CAAC;AAQ3C;;;AAGG;QACc,IAAmB,CAAA,mBAAA,GAAG,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;AAEpE;;;AAGG;QACH,IAAU,CAAA,UAAA,GAAsB,EAAE,CAAC;AAEnC;;;AAGG;QACiB,IAAI,CAAA,IAAA,GAAc,IAAK,CAAC;AAE5C;;;AAGG;AACO,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,YAAY,EAAE,CAAC;AAStC,QAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;AAChC,QAAA,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC;KAC3C;;AAGD,IAAA,WAAW,CAAC,OAAsB,EAAA;QAChC,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACzB,QAAA,IAAI,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE;YAClC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;SAC3B;KACF;;IAGD,WAAW,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,IAAI,EAAE;AACb,YAAA,iBAAiB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;;;;;;;YAQnC,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAmB,KAAK,IAAI,CAAC,mBAAmB,EAAE;gBAC9D,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,MAAK,GAAG,CAAC,CAAC;aACjD;SACF;KACF;AAED;;;AAGG;AACH,IAAA,IAAa,aAAa,GAAA;AACxB,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;AAGG;AACH,IAAA,IAAa,OAAO,GAAA;QAClB,OAAO,IAAI,CAAC,IAAI,CAAC;KAClB;AAED;;;;AAIG;AACH,IAAA,IAAa,IAAI,GAAA;AACf,QAAA,OAAO,EAAE,CAAC;KACX;AAED;;;;;;AAMG;AACH,IAAA,UAAU,CAAC,GAAoB,EAAA;AAC7B,QAAA,MAAM,IAAI,GAAQ,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1C,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACnD,IAAI,CAAC,sBAAsB,CAAC,EAAC,SAAS,EAAE,KAAK,EAAC,CAAC,CAAC;AAChD,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC1B,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;;AAKG;AACH,IAAA,UAAU,CAAC,GAAoB,EAAA;QAC7B,OAAoB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;KAC7C;AAED;;;;;AAKG;AACH,IAAA,aAAa,CAAC,GAAoB,EAAA;AAChC,QAAA,cAAc,CAAC,GAAG,CAAC,OAAO,IAAI,IAAI,EAAE,GAAG,wCAAwC,KAAK,CAAC,CAAC;AACtF,QAAAG,gBAAc,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;KACtC;AAED;;;;AAIG;AACH,IAAA,YAAY,CAAC,GAAkB,EAAA;AAC7B,QAAA,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;KAC/B;AAED;;;;;AAKG;AACH,IAAA,eAAe,CAAC,GAAkB,EAAA;AAChC,QAAA,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC;KACjC;AAED;;;;;AAKG;AACH,IAAA,YAAY,CAAC,GAAkB,EAAA;QAC7B,OAAkB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;KAC3C;AAED;;;;AAIG;AACH,IAAA,YAAY,CAAC,GAAkB,EAAA;AAC7B,QAAA,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;KAC/B;AAED;;;;;AAKG;AACH,IAAA,eAAe,CAAC,GAAkB,EAAA;AAChC,QAAA,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC;KACjC;AAED;;;;;AAKG;AACH,IAAA,YAAY,CAAC,GAAkB,EAAA;QAC7B,OAAkB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;KAC3C;AAED;;;;;AAKG;IACH,WAAW,CAAC,GAAoB,EAAE,KAAU,EAAA;AAC1C,QAAA,MAAM,IAAI,GAAgB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAClD,QAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;KACtB;AAED;;;;;;AAMG;AACH,IAAA,QAAQ,CAAC,MAAa,EAAA;AACnB,QAAA,IAAuB,CAAC,SAAS,GAAG,IAAI,CAAC;QAC1C,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AAChD,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;;;;AAI3B,QAAA,OAAQ,MAAM,EAAE,MAAiC,EAAE,MAAM,KAAK,QAAQ,CAAC;KACxE;AAED;;;AAGG;IACH,OAAO,GAAA;QACL,IAAI,CAAC,SAAS,EAAE,CAAC;KAClB;AAED;;;;;AAKG;IACH,SAAS,CAAC,QAAa,SAAS,EAAA;AAC9B,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACtB,QAAA,IAAuB,CAAC,SAAS,GAAG,KAAK,CAAC;KAC5C;;IAGD,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,IAAG;AAC5B,YAAA,MAAM,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;AAC5B,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACxC,YAAA,IAAI,OAAO,KAAK,OAAO,EAAE;;;AAGvB,gBAAA,cAAc,CAAC,OAAO,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC;;;;;;AAOrC,gBAAA,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE;oBAC1B,YAAY,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;AACrD,oBAAA,GAAiC,CAAC,OAAO,GAAG,OAAO,CAAC;iBACtD;aACF;AACH,SAAC,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAAC,SAAS,EAAE,KAAK,EAAC,CAAC,CAAC;KACnD;AAEO,IAAA,mBAAmB,CAAC,GAAgC,EAAA;AAC1D,QAAA,MAAM,IAAI,GAAQ,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC1C,QAAA,kBAAkB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;;;;QAI9B,IAAI,CAAC,sBAAsB,CAAC,EAAC,SAAS,EAAE,KAAK,EAAC,CAAC,CAAC;KACjD;AAEO,IAAA,qBAAqB,CAAC,GAAgC,EAAA;AAC5D,QAAA,IAAI,IAAI,CAAC,IAAI,EAAE;AACb,YAAA,MAAM,IAAI,GAAQ,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAC1C,IAAI,IAAI,EAAE;gBACR,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;gBACzD,IAAI,gBAAgB,EAAE;;;oBAGpB,IAAI,CAAC,sBAAsB,CAAC,EAAC,SAAS,EAAE,KAAK,EAAC,CAAC,CAAC;iBACjD;aACF;SACF;KACF;IAEO,oBAAoB,GAAA;QAC1B,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AAChE,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,QAAQ,CAAC,2BAA2B,CAAC,MAAK,GAAG,CAAC,CAAC;SACrD;KACF;IAEO,iBAAiB,GAAA;AACvB,QAAA,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACjC,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;SACxC;KACF;IAEO,iBAAiB,GAAA;AACvB,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YACjE,MAAM,oBAAoB,EAAE,CAAC;SAC9B;KACF;AApTU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,EAsCG,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,aAAa,EACb,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,mBAAmB,yCAE3B,uBAAuB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAzCpC,kBAAkB,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,CAAA,WAAA,EAAA,MAAA,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,OAAA,EAAA,WAAA,EAAA,EAAA,EAAA,SAAA,EAJlB,CAAC,qBAAqB,CAAC,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAIvB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAN9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,aAAa;oBACvB,SAAS,EAAE,CAAC,qBAAqB,CAAC;oBAClC,IAAI,EAAE,EAAC,UAAU,EAAE,kBAAkB,EAAE,SAAS,EAAE,WAAW,EAAC;AAC9D,oBAAA,QAAQ,EAAE,QAAQ;AACnB,iBAAA,CAAA;;0BAuCM,QAAQ;;0BAAI,IAAI;;0BAAI,MAAM;2BAAC,aAAa,CAAA;;0BACxC,QAAQ;;0BAAI,IAAI;;0BAAI,MAAM;2BAAC,mBAAmB,CAAA;;0BAE9C,QAAQ;;0BAAI,MAAM;2BAAC,uBAAuB,CAAA;yCAZ3B,IAAI,EAAA,CAAA;sBAAvB,KAAK;uBAAC,WAAW,CAAA;gBAMR,QAAQ,EAAA,CAAA;sBAAjB,MAAM;;;ACzET,MAAM,qBAAqB,GAAa;AACtC,IAAA,OAAO,EAAE,gBAAgB;AACzB,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,aAAa,CAAC;CAC7C,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CG;AAEG,MAAO,aAAc,SAAQ,0BAA0B,CAAA;AAY3D,IAAA,WAAA,CACoC,MAAwB,EACb,UAAqC,EAC/B,eACV,EAAA;AACzC,QAAA,KAAK,EAAE,CAAC;AAhBV;;;;;;;;AAQG;QAC8B,IAAI,CAAA,IAAA,GAAuB,IAAI,CAAC;AAQ/D,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;AACtB,QAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;AAChC,QAAA,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC;KAC3C;;IAGQ,gBAAgB,GAAA;AACvB,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YACtF,MAAM,oBAAoB,EAAE,CAAC;SAC9B;KACF;yHA5BU,aAAa,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAG,gBAAA,EAAA,IAAA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAcQ,aAAa,EAAA,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EACb,mBAAmB,EAAA,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAfxC,aAAa,EAAA,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,CAAA,eAAA,EAAA,MAAA,CAAA,EAAA,EAAA,SAAA,EAD0B,CAAC,qBAAqB,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAC9D,aAAa,EAAA,UAAA,EAAA,CAAA;kBADzB,SAAS;mBAAC,EAAC,QAAQ,EAAE,iBAAiB,EAAE,SAAS,EAAE,CAAC,qBAAqB,CAAC,EAAC,CAAA;;0BAcrE,QAAQ;;0BAAI,IAAI;;0BAAI,QAAQ;;0BAC5B,QAAQ;;0BAAI,IAAI;;0BAAI,MAAM;2BAAC,aAAa,CAAA;;0BACxC,QAAQ;;0BAAI,IAAI;;0BAAI,MAAM;2BAAC,mBAAmB,CAAA;yCALlB,IAAI,EAAA,CAAA;sBAApC,KAAK;uBAAC,eAAe,CAAA;;AAqBjB,MAAM,qBAAqB,GAAQ;AACxC,IAAA,OAAO,EAAE,gBAAgB;AACzB,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,aAAa,CAAC;CAC7C,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;AAuBG;AAEG,MAAO,aAAc,SAAQ,gBAAgB,CAAA;AAejD,IAAA,WAAA,CACoC,MAAwB,EACb,UAAqC,EAC/B,eACV,EAAA;AACzC,QAAA,KAAK,EAAE,CAAC;AAhBV;;;;;;;;AAQG;QAC8B,IAAI,CAAA,IAAA,GAAuB,IAAI,CAAC;AAQ/D,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;AACtB,QAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;AAChC,QAAA,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC;KAC3C;AAED;;;;AAIG;IACH,QAAQ,GAAA;QACN,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxB,QAAA,IAAI,CAAC,aAAc,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;KACxC;AAED;;;AAGG;IACH,WAAW,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,YAAA,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;SAC1C;KACF;AAED;;;AAGG;AACH,IAAA,IAAa,OAAO,GAAA;QAClB,OAAO,IAAI,CAAC,aAAc,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;KAC/C;AAED;;;AAGG;AACH,IAAA,IAAa,aAAa,GAAA;AACxB,QAAA,OAAO,IAAI,CAAC,OAAO,GAAuB,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;KAC7E;AAED;;;;AAIG;AACH,IAAA,IAAa,IAAI,GAAA;QACf,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;KACxF;IAEO,gBAAgB,GAAA;AACtB,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YACtF,MAAM,oBAAoB,EAAE,CAAC;SAC9B;KACF;yHA3EU,aAAa,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAA,gBAAA,EAAA,IAAA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAiBQ,aAAa,EAAA,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EACb,mBAAmB,EAAA,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAlBxC,aAAa,EAAA,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,CAAA,eAAA,EAAA,MAAA,CAAA,EAAA,EAAA,SAAA,EAD0B,CAAC,qBAAqB,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAC9D,aAAa,EAAA,UAAA,EAAA,CAAA;kBADzB,SAAS;mBAAC,EAAC,QAAQ,EAAE,iBAAiB,EAAE,SAAS,EAAE,CAAC,qBAAqB,CAAC,EAAC,CAAA;;0BAiBrE,QAAQ;;0BAAI,IAAI;;0BAAI,QAAQ;;0BAC5B,QAAQ;;0BAAI,IAAI;;0BAAI,MAAM;2BAAC,aAAa,CAAA;;0BACxC,QAAQ;;0BAAI,IAAI;;0BAAI,MAAM;2BAAC,mBAAmB,CAAA;yCALlB,IAAI,EAAA,CAAA;sBAApC,KAAK;uBAAC,eAAe,CAAA;;AAiExB,SAAS,iBAAiB,CAAC,MAAwB,EAAA;AACjD,IAAA,OAAO,EAAE,MAAM,YAAY,aAAa,CAAC,IAAI,EAAE,MAAM,YAAY,kBAAkB,CAAC;AAChF,QAAA,EAAE,MAAM,YAAY,aAAa,CAAC,CAAC;AACzC;;AC/LA,MAAM,kBAAkB,GAAa;AACnC,IAAA,OAAO,EAAE,SAAS;AAClB,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,eAAe,CAAC;CAC/C,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCG;AAEG,MAAO,eAAgB,SAAQ,SAAS,CAAA;AA0B5C;;;AAGG;IACH,IACI,UAAU,CAAC,UAAmB,EAAA;AAChC,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;SACnC;KACF;AAUD;;;;;;AAMG;aACI,IAAuB,CAAA,uBAAA,GAAG,KAAH,CAAS,EAAA;IAWvC,WACoC,CAAA,MAAwB,EACb,UAAqC,EAC/B,eACV,EACQ,cAAsC,EACrB,qBAC5D,EAAA;AACN,QAAA,KAAK,EAAE,CAAC;QAF0D,IAAqB,CAAA,qBAAA,GAArB,qBAAqB,CACjF;QArEA,IAAM,CAAA,MAAA,GAAG,KAAK,CAAC;AAcvB;;;;;;;;AAQG;QACgC,IAAI,CAAA,IAAA,GAAuB,IAAI,CAAC;;AAmB1C,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,YAAY,EAAE,CAAC;AAWrD;;;;;;AAMG;QACH,IAAmB,CAAA,mBAAA,GAAG,KAAK,CAAC;AAW1B,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;AACtB,QAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;AAChC,QAAA,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC;QAC1C,IAAI,CAAC,aAAa,GAAG,mBAAmB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;KAChE;;AAGD,IAAA,WAAW,CAAC,OAAsB,EAAA;QAChC,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,aAAa,EAAE,CAAC;QACvC,IAAI,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE;AAC9C,YAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;gBACjD,eAAe,CAAC,iBAAiB,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;aACvF;AACD,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;YAC5B,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;SAClD;KACF;;IAGD,WAAW,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,YAAA,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;SACxC;KACF;AAED;;;;;AAKG;AACM,IAAA,iBAAiB,CAAC,QAAa,EAAA;AACtC,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC1B,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KAC5B;AAED;;;;AAIG;AACH,IAAA,IAAa,IAAI,GAAA;QACf,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,OAAQ,CAAC,CAAC;KACzF;AAED;;;AAGG;AACH,IAAA,IAAI,aAAa,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;KACzD;IAEO,gBAAgB,GAAA;AACtB,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,IAAI,EAAE,IAAI,CAAC,OAAO,YAAY,aAAa,CAAC;AACxC,gBAAA,IAAI,CAAC,OAAO,YAAY,0BAA0B,EAAE;gBACtD,MAAM,qBAAqB,EAAE,CAAC;aAC/B;AAAM,iBAAA,IACH,EAAE,IAAI,CAAC,OAAO,YAAY,aAAa,CAAC;AACxC,gBAAA,EAAE,IAAI,CAAC,OAAO,YAAY,kBAAkB,CAAC;gBAC7C,EAAE,IAAI,CAAC,OAAO,YAAY,aAAa,CAAC,EAAE;gBAC5C,MAAM,sBAAsB,EAAE,CAAC;aAChC;SACF;KACF;IAEO,aAAa,GAAA;QACnB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACvB,IAAuB,CAAC,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AACvE,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;KACpB;AA/IU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,2FAiEM,aAAa,EAAA,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EACb,mBAAmB,EAEnB,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,iBAAiB,yCACzB,kCAAkC,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GArE/C,eAAe,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,CAAA,iBAAA,EAAA,MAAA,CAAA,EAAA,UAAA,EAAA,CAAA,UAAA,EAAA,YAAA,CAAA,EAAA,KAAA,EAAA,CAAA,SAAA,EAAA,OAAA,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,eAAA,EAAA,EAAA,SAAA,EAD0B,CAAC,kBAAkB,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAC7D,eAAe,EAAA,UAAA,EAAA,CAAA;kBAD3B,SAAS;mBAAC,EAAC,QAAQ,EAAE,mBAAmB,EAAE,SAAS,EAAE,CAAC,kBAAkB,CAAC,EAAC,CAAA;;0BAiEpE,QAAQ;;0BAAI,IAAI;;0BAAI,QAAQ;;0BAC5B,QAAQ;;0BAAI,IAAI;;0BAAI,MAAM;2BAAC,aAAa,CAAA;;0BACxC,QAAQ;;0BAAI,IAAI;;0BAAI,MAAM;2BAAC,mBAAmB,CAAA;;0BAE9C,QAAQ;;0BAAI,IAAI;;0BAAI,MAAM;2BAAC,iBAAiB,CAAA;;0BAC5C,QAAQ;;0BAAI,MAAM;2BAAC,kCAAkC,CAAA;yCA7CvB,IAAI,EAAA,CAAA;sBAAtC,KAAK;uBAAC,iBAAiB,CAAA;gBAOpB,UAAU,EAAA,CAAA;sBADb,KAAK;uBAAC,UAAU,CAAA;gBAUC,KAAK,EAAA,CAAA;sBAAtB,KAAK;uBAAC,SAAS,CAAA;gBAGS,MAAM,EAAA,CAAA;sBAA9B,MAAM;uBAAC,eAAe,CAAA;;;AC7FzB,MAAM,qBAAqB,GAAa;AACtC,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,0BAA0B,CAAC;AACzD,IAAA,KAAK,EAAE,IAAI;CACZ,CAAC;AAEF,SAASE,mBAAiB,CAAC,EAAe,EAAE,KAAU,EAAA;IACpD,IAAI,EAAE,IAAI,IAAI;QAAE,OAAO,CAAA,EAAG,KAAK,CAAA,CAAE,CAAC;AAClC,IAAA,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ;QAAE,KAAK,GAAG,QAAQ,CAAC;AACzD,IAAA,OAAO,CAAG,EAAA,EAAE,CAAK,EAAA,EAAA,KAAK,CAAE,CAAA,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACxC,CAAC;AAED,SAASC,YAAU,CAAC,WAAmB,EAAA;IACrC,OAAO,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuDG;AAOG,MAAO,0BAA2B,SAAQ,2BAA2B,CAAA;AAN3E,IAAA,WAAA,GAAA;;;AAYE,QAAA,IAAA,CAAA,UAAU,GAAqB,IAAI,GAAG,EAAe,CAAC;;QAGtD,IAAU,CAAA,UAAA,GAAW,CAAC,CAAC;AAiBf,QAAA,IAAA,CAAA,YAAY,GAAkC,MAAM,CAAC,EAAE,CAAC;AA0CjE,KAAA;AAzDC;;;;AAIG;IACH,IACI,WAAW,CAAC,EAAiC,EAAA;AAC/C,QAAA,IAAI,OAAO,EAAE,KAAK,UAAU,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAC/E,YAAA,MAAM,IAAIT,aAAY,CAElB,IAAA,8CAAA,CAAA,6CAAA,EAAgD,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAE,CAAA,CAAC,CAAC;SAC3E;AACD,QAAA,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;KACxB;AAID;;;AAGG;AACH,IAAA,UAAU,CAAC,KAAU,EAAA;AACnB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,MAAM,EAAE,GAAgB,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACjD,MAAM,WAAW,GAAGQ,mBAAiB,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;AACjD,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;KACxC;AAED;;;AAGG;AACM,IAAA,gBAAgB,CAAC,EAAuB,EAAA;AAC/C,QAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,WAAmB,KAAI;YACtC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;AAC/C,YAAA,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACjB,SAAC,CAAC;KACH;;IAGD,eAAe,GAAA;QACb,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,QAAQ,EAAE,CAAC;KACvC;;AAGD,IAAA,YAAY,CAAC,KAAU,EAAA;QACrB,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE;AACvC,YAAA,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC;AAAE,gBAAA,OAAO,EAAE,CAAC;SAClE;AACD,QAAA,OAAO,IAAI,CAAC;KACb;;AAGD,IAAA,eAAe,CAAC,WAAmB,EAAA;AACjC,QAAA,MAAM,EAAE,GAAWC,YAAU,CAAC,WAAW,CAAC,CAAC;QAC3C,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC;KACxE;yHAnEU,0BAA0B,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAA1B,0BAA0B,EAAA,QAAA,EAAA,6GAAA,EAAA,MAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,QAAA,EAAA,+BAAA,EAAA,MAAA,EAAA,aAAA,EAAA,EAAA,EAAA,SAAA,EAF1B,CAAC,qBAAqB,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAEvB,0BAA0B,EAAA,UAAA,EAAA,CAAA;kBANtC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EACJ,6GAA6G;oBACjH,IAAI,EAAE,EAAC,UAAU,EAAE,+BAA+B,EAAE,QAAQ,EAAE,aAAa,EAAC;oBAC5E,SAAS,EAAE,CAAC,qBAAqB,CAAC;AACnC,iBAAA,CAAA;8BAkBK,WAAW,EAAA,CAAA;sBADd,KAAK;;AAsDR;;;;;;;;;AASG;MAEU,cAAc,CAAA;AAQzB,IAAA,WAAA,CACY,QAAoB,EAAU,SAAoB,EAC9B,OAAmC,EAAA;QADvD,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAY;QAAU,IAAS,CAAA,SAAA,GAAT,SAAS,CAAW;QAC9B,IAAO,CAAA,OAAA,GAAP,OAAO,CAA4B;QACjE,IAAI,IAAI,CAAC,OAAO;YAAE,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;KAC5D;AAED;;;;AAIG;IACH,IACI,OAAO,CAAC,KAAU,EAAA;AACpB,QAAA,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI;YAAE,OAAO;AACjC,QAAA,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;AAC5C,QAAA,IAAI,CAAC,gBAAgB,CAACD,mBAAiB,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;QACzD,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;KAC7C;AAED;;;;AAIG;IACH,IACI,KAAK,CAAC,KAAU,EAAA;AAClB,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,IAAI,CAAC,OAAO;YAAE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;KAC/D;;AAGD,IAAA,gBAAgB,CAAC,KAAa,EAAA;AAC5B,QAAA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;KACzE;;IAGD,WAAW,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACxC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SAC7C;KACF;yHAjDU,cAAc,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,0BAAA,EAAA,IAAA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAAd,cAAc,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAD1B,SAAS;mBAAC,EAAC,QAAQ,EAAE,QAAQ,EAAC,CAAA;;0BAWxB,QAAQ;;0BAAI,IAAI;yCAUjB,OAAO,EAAA,CAAA;sBADV,KAAK;uBAAC,SAAS,CAAA;gBAcZ,KAAK,EAAA,CAAA;sBADR,KAAK;uBAAC,OAAO,CAAA;;;AC/LhB,MAAM,8BAA8B,GAAa;AAC/C,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,kCAAkC,CAAC;AACjE,IAAA,KAAK,EAAE,IAAI;CACZ,CAAC;AAEF,SAAS,iBAAiB,CAAC,EAAU,EAAE,KAAU,EAAA;IAC/C,IAAI,EAAE,IAAI,IAAI;QAAE,OAAO,CAAA,EAAG,KAAK,CAAA,CAAE,CAAC;IAClC,IAAI,OAAO,KAAK,KAAK,QAAQ;AAAE,QAAA,KAAK,GAAG,CAAA,CAAA,EAAI,KAAK,CAAA,CAAA,CAAG,CAAC;AACpD,IAAA,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ;QAAE,KAAK,GAAG,QAAQ,CAAC;AACzD,IAAA,OAAO,CAAG,EAAA,EAAE,CAAK,EAAA,EAAA,KAAK,CAAE,CAAA,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACxC,CAAC;AAED,SAAS,UAAU,CAAC,WAAmB,EAAA;IACrC,OAAO,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,CAAC;AAQD;AACA,MAAe,cAAc,CAAA;AAI5B,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCG;AAOG,MAAO,kCAAmC,SAAQ,2BAA2B,CAAA;AANnF,IAAA,WAAA,GAAA;;;AAeE,QAAA,IAAA,CAAA,UAAU,GAAyC,IAAI,GAAG,EAAmC,CAAC;;QAG9F,IAAU,CAAA,UAAA,GAAW,CAAC,CAAC;AAiBf,QAAA,IAAA,CAAA,YAAY,GAAkC,MAAM,CAAC,EAAE,CAAC;AA8EjE,KAAA;AA7FC;;;;AAIG;IACH,IACI,WAAW,CAAC,EAAiC,EAAA;AAC/C,QAAA,IAAI,OAAO,EAAE,KAAK,UAAU,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAC/E,YAAA,MAAM,IAAIR,aAAY,CAElB,IAAA,8CAAA,CAAA,6CAAA,EAAgD,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAE,CAAA,CAAC,CAAC;SAC3E;AACD,QAAA,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;KACxB;AAID;;;AAGG;AACH,IAAA,UAAU,CAAC,KAAU,EAAA;AACnB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,yBAAyE,CAAC;AAC9E,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;;AAExB,YAAA,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,YAAA,yBAAyB,GAAG,CAAC,GAAG,EAAE,CAAC,KAAI;AACrC,gBAAA,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACnD,aAAC,CAAC;SACH;aAAM;AACL,YAAA,yBAAyB,GAAG,CAAC,GAAG,EAAE,CAAC,KAAI;AACrC,gBAAA,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC1B,aAAC,CAAC;SACH;AACD,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;KACpD;AAED;;;;AAIG;AACM,IAAA,gBAAgB,CAAC,EAAuB,EAAA;AAC/C,QAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,OAA0B,KAAI;YAC7C,MAAM,QAAQ,GAAe,EAAE,CAAC;AAChC,YAAA,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;AAChD,YAAA,IAAI,eAAe,KAAK,SAAS,EAAE;gBACjC,MAAM,OAAO,GAAG,eAAe,CAAC;AAChC,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACvC,oBAAA,MAAM,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;oBACvB,MAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC5C,oBAAA,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBACpB;aACF;;;;iBAII;AACH,gBAAA,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;AAChC,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACvC,oBAAA,MAAM,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AACvB,oBAAA,IAAI,GAAG,CAAC,QAAQ,EAAE;wBAChB,MAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC5C,wBAAA,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;qBACpB;iBACF;aACF;AACD,YAAA,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;YACtB,EAAE,CAAC,QAAQ,CAAC,CAAC;AACf,SAAC,CAAC;KACH;;AAGD,IAAA,eAAe,CAAC,KAA8B,EAAA;QAC5C,MAAM,EAAE,GAAW,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,QAAQ,EAAE,CAAC;QAClD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;AAC/B,QAAA,OAAO,EAAE,CAAC;KACX;;AAGD,IAAA,YAAY,CAAC,KAAU,EAAA;QACrB,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE;AACvC,YAAA,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAE,CAAC,MAAM,EAAE,KAAK,CAAC;AAAE,gBAAA,OAAO,EAAE,CAAC;SAC1E;AACD,QAAA,OAAO,IAAI,CAAC;KACb;;AAGD,IAAA,eAAe,CAAC,WAAmB,EAAA;AACjC,QAAA,MAAM,EAAE,GAAW,UAAU,CAAC,WAAW,CAAC,CAAC;QAC3C,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAE,CAAC,MAAM,GAAG,WAAW,CAAC;KAChF;yHA1GU,kCAAkC,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAAlC,kCAAkC,EAAA,QAAA,EAAA,2FAAA,EAAA,MAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,QAAA,EAAA,yBAAA,EAAA,MAAA,EAAA,aAAA,EAAA,EAAA,EAAA,SAAA,EAFlC,CAAC,8BAA8B,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAEhC,kCAAkC,EAAA,UAAA,EAAA,CAAA;kBAN9C,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EACJ,2FAA2F;oBAC/F,IAAI,EAAE,EAAC,UAAU,EAAE,yBAAyB,EAAE,QAAQ,EAAE,aAAa,EAAC;oBACtE,SAAS,EAAE,CAAC,8BAA8B,CAAC;AAC5C,iBAAA,CAAA;8BAqBK,WAAW,EAAA,CAAA;sBADd,KAAK;;AA0FR;;;;;;;;;AASG;MAEU,uBAAuB,CAAA;AAMlC,IAAA,WAAA,CACY,QAAoB,EAAU,SAAoB,EAC9B,OAA2C,EAAA;QAD/D,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAY;QAAU,IAAS,CAAA,SAAA,GAAT,SAAS,CAAW;QAC9B,IAAO,CAAA,OAAA,GAAP,OAAO,CAAoC;AACzE,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;SAC9C;KACF;AAED;;;;AAIG;IACH,IACI,OAAO,CAAC,KAAU,EAAA;AACpB,QAAA,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI;YAAE,OAAO;AACjC,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACpB,QAAA,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;QACzD,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;KAC7C;AAED;;;;AAIG;IACH,IACI,KAAK,CAAC,KAAU,EAAA;AAClB,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACpB,YAAA,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;YACzD,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SAC7C;aAAM;AACL,YAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;SAC9B;KACF;;AAGD,IAAA,gBAAgB,CAAC,KAAa,EAAA;AAC5B,QAAA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;KACzE;;AAGD,IAAA,YAAY,CAAC,QAAiB,EAAA;AAC5B,QAAA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;KAC/E;;IAGD,WAAW,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACxC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SAC7C;KACF;yHA3DU,uBAAuB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,kCAAA,EAAA,IAAA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAAvB,uBAAuB,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAAvB,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBADnC,SAAS;mBAAC,EAAC,QAAQ,EAAE,QAAQ,EAAC,CAAA;;0BASxB,QAAQ;;0BAAI,IAAI;yCAYjB,OAAO,EAAA,CAAA;sBADV,KAAK;uBAAC,SAAS,CAAA;gBAcZ,KAAK,EAAA,CAAA;sBADR,KAAK;uBAAC,OAAO,CAAA;;;AC/NhB;;;;;AAKG;AACH,SAAS,SAAS,CAAC,KAAoB,EAAA;AACrC,IAAA,OAAO,OAAO,KAAK,KAAK,QAAQ,GAAG,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AACjE,CAAC;AAED;;;;;AAKG;AACH,SAAS,OAAO,CAAC,KAAoB,EAAA;AACnC,IAAA,OAAO,OAAO,KAAK,KAAK,QAAQ,GAAG,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;AAC/D,CAAC;AA0DD;;;;;AAKG;AACH,MACe,0BAA0B,CAAA;AADzC,IAAA,WAAA,GAAA;QAEU,IAAU,CAAA,UAAA,GAAgB,aAAa,CAAC;AAuEjD,KAAA;;AA/BC,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,IAAI,IAAI,CAAC,SAAS,IAAI,OAAO,EAAE;AAC7B,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,CAAC;YACxE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACpC,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,aAAa,CAAC;AAC9E,YAAA,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB,IAAI,CAAC,SAAS,EAAE,CAAC;aAClB;SACF;KACF;;AAGD,IAAA,QAAQ,CAAC,OAAwB,EAAA;AAC/B,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;KACjC;;AAGD,IAAA,yBAAyB,CAAC,EAAc,EAAA;AACtC,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;KACrB;AAED;;;;;;AAMG;AACH,IAAA,OAAO,CAAC,KAAc,EAAA;AACpB,QAAA,OAAO,KAAK,IAAI,IAAI,mCAAmC;KACxD;yHAvEY,0BAA0B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAA1B,0BAA0B,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAA1B,0BAA0B,EAAA,UAAA,EAAA,CAAA;kBADxC,SAAS;;AA2EV;;;AAGG;AACI,MAAM,aAAa,GAAa;AACrC,IAAA,OAAO,EAAE,aAAa;AACtB,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,YAAY,CAAC;AAC3C,IAAA,KAAK,EAAE,IAAI;CACZ,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;AAoBG;AAOG,MAAO,YAAa,SAAQ,0BAA0B,CAAA;AAN5D,IAAA,WAAA,GAAA;;;QAaW,IAAS,CAAA,SAAA,GAAG,KAAK,CAAC;;QAElB,IAAc,CAAA,cAAA,GAAG,CAAC,KAAoB,KAAa,OAAO,CAAC,KAAK,CAAC,CAAC;;QAElE,IAAe,CAAA,eAAA,GAAG,CAAC,GAAW,KAAkB,YAAY,CAAC,GAAG,CAAC,CAAC;AAC5E,KAAA;yHAZY,YAAY,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAAZ,YAAY,EAAA,QAAA,EAAA,gHAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,KAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,UAAA,EAAA,uBAAA,EAAA,EAAA,EAAA,SAAA,EAHZ,CAAC,aAAa,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAGf,YAAY,EAAA,UAAA,EAAA,CAAA;kBANxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EACJ,gHAAgH;oBACpH,SAAS,EAAE,CAAC,aAAa,CAAC;AAC1B,oBAAA,IAAI,EAAE,EAAC,YAAY,EAAE,uBAAuB,EAAC;AAC9C,iBAAA,CAAA;8BAMU,GAAG,EAAA,CAAA;sBAAX,KAAK;;AASR;;;AAGG;AACI,MAAM,aAAa,GAAa;AACrC,IAAA,OAAO,EAAE,aAAa;AACtB,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,YAAY,CAAC;AAC3C,IAAA,KAAK,EAAE,IAAI;CACZ,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;AAoBG;AAOG,MAAO,YAAa,SAAQ,0BAA0B,CAAA;AAN5D,IAAA,WAAA,GAAA;;;QAaW,IAAS,CAAA,SAAA,GAAG,KAAK,CAAC;;QAElB,IAAc,CAAA,cAAA,GAAG,CAAC,KAAoB,KAAa,OAAO,CAAC,KAAK,CAAC,CAAC;;QAElE,IAAe,CAAA,eAAA,GAAG,CAAC,GAAW,KAAkB,YAAY,CAAC,GAAG,CAAC,CAAC;AAC5E,KAAA;yHAZY,YAAY,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAAZ,YAAY,EAAA,QAAA,EAAA,gHAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,KAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,UAAA,EAAA,uBAAA,EAAA,EAAA,EAAA,SAAA,EAHZ,CAAC,aAAa,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAGf,YAAY,EAAA,UAAA,EAAA,CAAA;kBANxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EACJ,gHAAgH;oBACpH,SAAS,EAAE,CAAC,aAAa,CAAC;AAC1B,oBAAA,IAAI,EAAE,EAAC,YAAY,EAAE,uBAAuB,EAAC;AAC9C,iBAAA,CAAA;8BAMU,GAAG,EAAA,CAAA;sBAAX,KAAK;;AAmDR;;;AAGG;AACI,MAAM,kBAAkB,GAAa;AAC1C,IAAA,OAAO,EAAE,aAAa;AACtB,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,iBAAiB,CAAC;AAChD,IAAA,KAAK,EAAE,IAAI;CACZ,CAAC;AAEF;;;AAGG;AACI,MAAM,2BAA2B,GAAa;AACnD,IAAA,OAAO,EAAE,aAAa;AACtB,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,yBAAyB,CAAC;AACxD,IAAA,KAAK,EAAE,IAAI;CACZ,CAAC;AAGF;;;;;;;;;;;;;;;;;;AAkBG;AAOG,MAAO,iBAAkB,SAAQ,0BAA0B,CAAA;AANjE,IAAA,WAAA,GAAA;;;QAcW,IAAS,CAAA,SAAA,GAAG,UAAU,CAAC;;QAGvB,IAAc,CAAA,cAAA,GAAG,gBAAgB,CAAC;;AAGlC,QAAA,IAAA,CAAA,eAAe,GAAG,CAAC,KAAc,KAAkB,iBAAiB,CAAC;AAM/E,KAAA;;AAHU,IAAA,OAAO,CAAC,KAAc,EAAA;AAC7B,QAAA,OAAO,KAAK,CAAC;KACd;yHAnBU,iBAAiB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAAjB,iBAAiB,EAAA,QAAA,EAAA,wIAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,eAAA,EAAA,wBAAA,EAAA,EAAA,EAAA,SAAA,EAHjB,CAAC,kBAAkB,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAGpB,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAN7B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EACJ,wIAAwI;oBAC5I,SAAS,EAAE,CAAC,kBAAkB,CAAC;AAC/B,oBAAA,IAAI,EAAE,EAAC,iBAAiB,EAAE,sBAAsB,EAAC;AAClD,iBAAA,CAAA;8BAMU,QAAQ,EAAA,CAAA;sBAAhB,KAAK;;AAkBR;;;;;;;;;;;;;;;;;;;;AAoBG;AAOG,MAAO,yBAA0B,SAAQ,iBAAiB,CAAA;AANhE,IAAA,WAAA,GAAA;;;AAQW,QAAA,IAAA,CAAA,eAAe,GAAG,CAAC,KAAc,KAAkB,qBAAqB,CAAC;AACnF,KAAA;yHAHY,yBAAyB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAAzB,yBAAyB,EAAA,QAAA,EAAA,qIAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,eAAA,EAAA,wBAAA,EAAA,EAAA,EAAA,SAAA,EAHzB,CAAC,2BAA2B,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAG7B,yBAAyB,EAAA,UAAA,EAAA,CAAA;kBANrC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EACJ,qIAAqI;oBACzI,SAAS,EAAE,CAAC,2BAA2B,CAAC;AACxC,oBAAA,IAAI,EAAE,EAAC,iBAAiB,EAAE,sBAAsB,EAAC;AAClD,iBAAA,CAAA;;AAMD;;;AAGG;AACI,MAAM,eAAe,GAAQ;AAClC,IAAA,OAAO,EAAE,aAAa;AACtB,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,cAAc,CAAC;AAC7C,IAAA,KAAK,EAAE,IAAI;CACZ,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BG;AAKG,MAAO,cAAe,SAAQ,0BAA0B,CAAA;AAJ9D,IAAA,WAAA,GAAA;;;QAYW,IAAS,CAAA,SAAA,GAAG,OAAO,CAAC;;QAGpB,IAAc,CAAA,cAAA,GAAG,gBAAgB,CAAC;;AAGlC,QAAA,IAAA,CAAA,eAAe,GAAG,CAAC,KAAa,KAAkB,cAAc,CAAC;AAM3E,KAAA;;AAHU,IAAA,OAAO,CAAC,KAAc,EAAA;AAC7B,QAAA,OAAO,KAAK,CAAC;KACd;yHAnBU,cAAc,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAAd,cAAc,EAAA,QAAA,EAAA,gEAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,SAAA,EAFd,CAAC,eAAe,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAEjB,cAAc,EAAA,UAAA,EAAA,CAAA;kBAJ1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gEAAgE;oBAC1E,SAAS,EAAE,CAAC,eAAe,CAAC;AAC7B,iBAAA,CAAA;8BAMU,KAAK,EAAA,CAAA;sBAAb,KAAK;;AAuCR;;;AAGG;AACI,MAAM,oBAAoB,GAAQ;AACvC,IAAA,OAAO,EAAE,aAAa;AACtB,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,kBAAkB,CAAC;AACjD,IAAA,KAAK,EAAE,IAAI;CACZ,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;AAoBG;AAMG,MAAO,kBAAmB,SAAQ,0BAA0B,CAAA;AALlE,IAAA,WAAA,GAAA;;;QAaW,IAAS,CAAA,SAAA,GAAG,WAAW,CAAC;;QAGxB,IAAc,CAAA,cAAA,GAAG,CAAC,KAAoB,KAAa,SAAS,CAAC,KAAK,CAAC,CAAC;;QAGpE,IAAe,CAAA,eAAA,GAAG,CAAC,SAAiB,KAAkB,kBAAkB,CAAC,SAAS,CAAC,CAAC;AAC9F,KAAA;yHAfY,kBAAkB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAAlB,kBAAkB,EAAA,QAAA,EAAA,4EAAA,EAAA,MAAA,EAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,gBAAA,EAAA,6BAAA,EAAA,EAAA,EAAA,SAAA,EAHlB,CAAC,oBAAoB,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAGtB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAL9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,4EAA4E;oBACtF,SAAS,EAAE,CAAC,oBAAoB,CAAC;AACjC,oBAAA,IAAI,EAAE,EAAC,kBAAkB,EAAE,6BAA6B,EAAC;AAC1D,iBAAA,CAAA;8BAMU,SAAS,EAAA,CAAA;sBAAjB,KAAK;;AAYR;;;AAGG;AACI,MAAM,oBAAoB,GAAQ;AACvC,IAAA,OAAO,EAAE,aAAa;AACtB,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,kBAAkB,CAAC;AACjD,IAAA,KAAK,EAAE,IAAI;CACZ,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;AAoBG;AAMG,MAAO,kBAAmB,SAAQ,0BAA0B,CAAA;AALlE,IAAA,WAAA,GAAA;;;QAaW,IAAS,CAAA,SAAA,GAAG,WAAW,CAAC;;QAGxB,IAAc,CAAA,cAAA,GAAG,CAAC,KAAoB,KAAa,SAAS,CAAC,KAAK,CAAC,CAAC;;QAGpE,IAAe,CAAA,eAAA,GAAG,CAAC,SAAiB,KAAkB,kBAAkB,CAAC,SAAS,CAAC,CAAC;AAC9F,KAAA;yHAfY,kBAAkB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAAlB,kBAAkB,EAAA,QAAA,EAAA,4EAAA,EAAA,MAAA,EAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,gBAAA,EAAA,6BAAA,EAAA,EAAA,EAAA,SAAA,EAHlB,CAAC,oBAAoB,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAGtB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAL9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,4EAA4E;oBACtF,SAAS,EAAE,CAAC,oBAAoB,CAAC;AACjC,oBAAA,IAAI,EAAE,EAAC,kBAAkB,EAAE,6BAA6B,EAAC;AAC1D,iBAAA,CAAA;8BAMU,SAAS,EAAA,CAAA;sBAAjB,KAAK;;AAYR;;;AAGG;AACI,MAAM,iBAAiB,GAAQ;AACpC,IAAA,OAAO,EAAE,aAAa;AACtB,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,gBAAgB,CAAC;AAC/C,IAAA,KAAK,EAAE,IAAI;CACZ,CAAC;AAGF;;;;;;;;;;;;;;;;;;;;;;AAsBG;AAMG,MAAO,gBAAiB,SAAQ,0BAA0B,CAAA;AALhE,IAAA,WAAA,GAAA;;;QAcW,IAAS,CAAA,SAAA,GAAG,SAAS,CAAC;;AAGtB,QAAA,IAAA,CAAA,cAAc,GAAG,CAAC,KAAoB,KAAoB,KAAK,CAAC;;QAGhE,IAAe,CAAA,eAAA,GAAG,CAAC,KAAoB,KAAkB,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAC3F,KAAA;yHAhBY,gBAAgB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAAhB,gBAAgB,EAAA,QAAA,EAAA,sEAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,cAAA,EAAA,2BAAA,EAAA,EAAA,EAAA,SAAA,EAHhB,CAAC,iBAAiB,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAGnB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAL5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sEAAsE;oBAChF,SAAS,EAAE,CAAC,iBAAiB,CAAC;AAC9B,oBAAA,IAAI,EAAE,EAAC,gBAAgB,EAAE,2BAA2B,EAAC;AACtD,iBAAA,CAAA;8BAOC,OAAO,EAAA,CAAA;sBADN,KAAK;;;AC5lBD,MAAM,sBAAsB,GAAgB;IACjDU,aAAY;IACZ,cAAc;IACdC,uBAAsB;IACtB,oBAAoB;IACpB,mBAAmB;IACnB,kBAAkB;IAClB,4BAA4B;IAC5B,0BAA0B;IAC1B,kCAAkC;IAClC,yBAAyB;IACzB,eAAe;IACf,oBAAoB;IACpB,iBAAiB;IACjB,kBAAkB;IAClB,kBAAkB;IAClB,gBAAgB;IAChB,yBAAyB;IACzB,cAAc;IACd,YAAY;IACZ,YAAY;CACb,CAAC;AAEK,MAAM,0BAA0B,GAAgB,CAAC,OAAO,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;AAEhF,MAAM,0BAA0B,GACnC,CAAC,oBAAoB,EAAE,kBAAkB,EAAE,eAAe,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;AAE9F;;AAEG;MAKU,0BAA0B,CAAA;yHAA1B,0BAA0B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAA1B,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,0BAA0B,iBAlCrCD,aAAY;YACZ,cAAc;YACdC,uBAAsB;YACtB,oBAAoB;YACpB,mBAAmB;YACnB,kBAAkB;YAClB,4BAA4B;YAC5B,0BAA0B;YAC1B,kCAAkC;YAClC,yBAAyB;YACzB,eAAe;YACf,oBAAoB;YACpB,iBAAiB;YACjB,kBAAkB;YAClB,kBAAkB;YAClB,gBAAgB;YAChB,yBAAyB;YACzB,cAAc;YACd,YAAY;AACZ,YAAA,YAAY,aAnBZD,aAAY;YACZ,cAAc;YACdC,uBAAsB;YACtB,oBAAoB;YACpB,mBAAmB;YACnB,kBAAkB;YAClB,4BAA4B;YAC5B,0BAA0B;YAC1B,kCAAkC;YAClC,yBAAyB;YACzB,eAAe;YACf,oBAAoB;YACpB,iBAAiB;YACjB,kBAAkB;YAClB,kBAAkB;YAClB,gBAAgB;YAChB,yBAAyB;YACzB,cAAc;YACd,YAAY;YACZ,YAAY,CAAA,EAAA,CAAA,CAAA,EAAA;0HAeD,0BAA0B,EAAA,CAAA,CAAA,EAAA;;sGAA1B,0BAA0B,EAAA,UAAA,EAAA,CAAA;kBAJtC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,YAAY,EAAE,sBAAsB;AACpC,oBAAA,OAAO,EAAE,sBAAsB;AAChC,iBAAA,CAAA;;;AChDD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkEG;AACG,MAAO,SAAuD,SAAQ,eAEX,CAAA;AAC/D;;;;;;;;;;;;AAYG;AACH,IAAA,WAAA,CACI,QAAyB,EACzB,eAAuE,EACvE,cAAyD,EAAA;AAC3D,QAAA,KAAK,CAAC,cAAc,CAAC,eAAe,CAAC,EAAE,mBAAmB,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC,CAAC;AAC7F,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxB,QAAA,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;QACzC,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,sBAAsB,CAAC;AAC1B,YAAA,QAAQ,EAAE,IAAI;;;;;AAKd,YAAA,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc;AACjC,SAAA,CAAC,CAAC;KACJ;AAID;;;;;;AAMG;AACH,IAAA,EAAE,CAAC,KAAa,EAAA;QACd,OAAQ,IAAI,CAAC,QAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;KACzD;AAED;;;;;;;;;AASG;AACH,IAAA,IAAI,CAAC,OAAiB,EAAE,OAAA,GAAiC,EAAE,EAAA;AACzD,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC5B,QAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAC/B,IAAI,CAAC,sBAAsB,CAAC,EAAC,SAAS,EAAE,OAAO,CAAC,SAAS,EAAC,CAAC,CAAC;QAC5D,IAAI,CAAC,mBAAmB,EAAE,CAAC;KAC5B;AAED;;;;;;;;;;;;AAYG;AACH,IAAA,MAAM,CAAC,KAAa,EAAE,OAAiB,EAAE,UAAiC,EAAE,EAAA;QAC1E,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;AAExC,QAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAC/B,IAAI,CAAC,sBAAsB,CAAC,EAAC,SAAS,EAAE,OAAO,CAAC,SAAS,EAAC,CAAC,CAAC;KAC7D;AAED;;;;;;;;;;;AAWG;AACH,IAAA,QAAQ,CAAC,KAAa,EAAE,OAAA,GAAiC,EAAE,EAAA;;QAEzD,IAAI,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC7C,IAAI,aAAa,GAAG,CAAC;YAAE,aAAa,GAAG,CAAC,CAAC;AAEzC,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;AAC9B,YAAA,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,2BAA2B,CAAC,MAAO,GAAC,CAAC,CAAC;QACrE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,sBAAsB,CAAC,EAAC,SAAS,EAAE,OAAO,CAAC,SAAS,EAAC,CAAC,CAAC;KAC7D;AAED;;;;;;;;;;;;AAYG;AACH,IAAA,UAAU,CAAC,KAAa,EAAE,OAAiB,EAAE,UAAiC,EAAE,EAAA;;QAE9E,IAAI,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC7C,IAAI,aAAa,GAAG,CAAC;YAAE,aAAa,GAAG,CAAC,CAAC;AAEzC,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;AAC9B,YAAA,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,2BAA2B,CAAC,MAAO,GAAC,CAAC,CAAC;QACrE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QAEvC,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;AAChD,YAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;SAChC;QAED,IAAI,CAAC,sBAAsB,CAAC,EAAC,SAAS,EAAE,OAAO,CAAC,SAAS,EAAC,CAAC,CAAC;QAC5D,IAAI,CAAC,mBAAmB,EAAE,CAAC;KAC5B;AAED;;AAEG;AACH,IAAA,IAAI,MAAM,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;KAC7B;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCG;AACM,IAAA,QAAQ,CAAC,KAAmC,EAAE,OAAA,GAGnD,EAAE,EAAA;AACJ,QAAA,sBAAsB,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAC3C,KAAK,CAAC,OAAO,CAAC,CAAC,QAAa,EAAE,KAAa,KAAI;AAC7C,YAAA,oBAAoB,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YACzC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,EAAC,CAAC,CAAC;AACpF,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;KACtC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BG;AACM,IAAA,UAAU,CAAC,KAAgC,EAAE,OAAA,GAGlD,EAAE,EAAA;;;;;AAKJ,QAAA,IAAI,KAAK,IAAI,IAAI;YAAoC,OAAO;QAE5D,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,KAAI;AAChC,YAAA,IAAI,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE;gBAClB,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,QAAQ,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,EAAC,CAAC,CAAC;aACrF;AACH,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;KACtC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CG;AACM,IAAA,KAAK,CAAC,KAAA,GAAmE,EAAE,EAAE,UAGlF,EAAE,EAAA;QACJ,IAAI,CAAC,aAAa,CAAC,CAAC,OAAwB,EAAE,KAAa,KAAI;YAC7D,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,EAAC,CAAC,CAAC;AAC9E,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;AAC9B,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;AAC7B,QAAA,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;KACtC;AAED;;;;AAIG;IACM,WAAW,GAAA;AAClB,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAwB,KAAK,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;KAC/E;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCG;IACH,KAAK,CAAC,UAAiC,EAAE,EAAA;AACvC,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO;AACrC,QAAA,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,2BAA2B,CAAC,SAAQ,CAAC,CAAC,CAAC;AAC/E,QAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACxB,IAAI,CAAC,sBAAsB,CAAC,EAAC,SAAS,EAAE,OAAO,CAAC,SAAS,EAAC,CAAC,CAAC;KAC7D;AAED;;;;AAIG;AACK,IAAA,YAAY,CAAC,KAAa,EAAA;AAChC,QAAA,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;KAChD;;IAGQ,oBAAoB,GAAA;AAC3B,QAAA,IAAI,cAAc,GAAI,IAAI,CAAC,QAAgB,CAAC,MAAM,CAAC,CAAC,OAAY,EAAE,KAAU,KAAI;AAC9E,YAAA,OAAO,KAAK,CAAC,oBAAoB,EAAE,GAAG,IAAI,GAAG,OAAO,CAAC;SACtD,EAAE,KAAK,CAAC,CAAC;AACV,QAAA,IAAI,cAAc;YAAE,IAAI,CAAC,sBAAsB,CAAC,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;AAClE,QAAA,OAAO,cAAc,CAAC;KACvB;;AAGQ,IAAA,aAAa,CAAC,EAA+C,EAAA;QACpE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAwB,EAAE,KAAa,KAAI;AAChE,YAAA,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AACrB,SAAC,CAAC,CAAC;KACJ;;IAGQ,YAAY,GAAA;AAClB,QAAA,IAAuB,CAAC,KAAK;AAC1B,YAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC;iBAC9D,GAAG,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;KAC1C;;AAGQ,IAAA,YAAY,CAAC,SAA0C,EAAA;QAC9D,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO,IAAI,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;KAC/E;;IAGD,cAAc,GAAA;AACZ,QAAA,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,KAAK,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;KACjE;;IAGQ,oBAAoB,GAAA;AAC3B,QAAA,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;YACnC,IAAI,OAAO,CAAC,OAAO;AAAE,gBAAA,OAAO,KAAK,CAAC;SACnC;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC;KAClD;AAEO,IAAA,gBAAgB,CAAC,OAAwB,EAAA;AAC/C,QAAA,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACxB,QAAA,OAAO,CAAC,2BAA2B,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;KAC/D;;AAGQ,IAAA,KAAK,CAAC,IAAmB,EAAA;QAChC,OAAO,IAAI,CAAC,EAAE,CAAC,IAAc,CAAC,IAAI,IAAI,CAAC;KACxC;AACF,CAAA;AAoBM,MAAM,gBAAgB,GAAyB,UAAU;AAEhE;;;;;AAKG;AACI,MAAM,WAAW,GAAG,CAAC,OAAgB,KAA2B,OAAO,YAAY;;AC5gB1F,SAAS,wBAAwB,CAAC,OACS,EAAA;IACzC,OAAO,CAAC,CAAC,OAAO;AACZ,SAAE,OAAkC,CAAC,eAAe,KAAK,SAAS;YAChE,OAAkC,CAAC,UAAU,KAAK,SAAS;AAC3D,YAAA,OAAkC,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC;AACnE,CAAC;AAwED;AAEA;;;;;;;;;;;AAWG;MAEU,WAAW,CAAA;AADxB,IAAA,WAAA,GAAA;QAEU,IAAc,CAAA,cAAA,GAAY,KAAK,CAAC;AA2PzC,KAAA;AAzPC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCG;AACH,IAAA,IAAI,WAAW,GAAA;AACb,QAAA,MAAM,IAAI,GAAG,IAAI,WAAW,EAAE,CAAC;AAC/B,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC3B,QAAA,OAAO,IAA8B,CAAC;KACvC;AAkDD,IAAA,KAAK,CAAC,QAA8B,EAAE,OAAA,GACiD,IAAI,EAAA;QAEzF,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACvD,IAAI,UAAU,GAAuB,EAAE,CAAC;AACxC,QAAA,IAAI,wBAAwB,CAAC,OAAO,CAAC,EAAE;;YAErC,UAAU,GAAG,OAAO,CAAC;SACtB;AAAM,aAAA,IAAI,OAAO,KAAK,IAAI,EAAE;;AAE3B,YAAA,UAAU,CAAC,UAAU,GAAI,OAAe,CAAC,SAAS,CAAC;AACnD,YAAA,UAAU,CAAC,eAAe,GAAI,OAAe,CAAC,cAAc,CAAC;SAC9D;AACD,QAAA,OAAO,IAAI,SAAS,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;KACnD;AAED;;;;;;;;;;;;;;AAcG;AACH,IAAA,MAAM,CAAI,QAA4B,EAAE,OAAA,GAAuC,IAAI,EAAA;QAEjF,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;;AAEvD,QAAA,OAAO,IAAI,UAAU,CAAC,eAAe,EAAE,OAAO,CAAQ,CAAC;KACxD;AAsBD;;;;;;;;;;;;;;;;;;;;;;;;;AAyBG;AACH,IAAA,OAAO,CACH,SAAgC,EAChC,eAAmE,EACnE,cAAyD,EAAA;QAC3D,IAAI,UAAU,GAAuB,EAAE,CAAC;AACxC,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,OAAO,IAAI,WAAW,CAAC,SAAS,EAAE,eAAe,EAAE,cAAc,CAAC,CAAC;SACpE;AACD,QAAA,IAAI,wBAAwB,CAAC,eAAe,CAAC,EAAE;;YAE7C,UAAU,GAAG,eAAe,CAAC;SAC9B;aAAM;;AAEL,YAAA,UAAU,CAAC,UAAU,GAAG,eAAe,CAAC;AACxC,YAAA,UAAU,CAAC,eAAe,GAAG,cAAc,CAAC;SAC7C;AACD,QAAA,OAAO,IAAI,WAAW,CAAI,SAAS,EAAE,EAAC,GAAG,UAAU,EAAE,WAAW,EAAE,IAAI,EAAC,CAAC,CAAC;KAC1E;AAED;;;;;;;;;;;;;AAaG;AACH,IAAA,KAAK,CACD,QAAkB,EAAE,eAAuE,EAC3F,cAAyD,EAAA;AAC3D,QAAA,MAAM,eAAe,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;;QAElE,OAAO,IAAI,SAAS,CAAC,eAAe,EAAE,eAAe,EAAE,cAAc,CAAQ,CAAC;KAC/E;;AAGD,IAAA,eAAe,CAAI,QAC4E,EAAA;QAE7F,MAAM,eAAe,GAAqC,EAAE,CAAC;QAC7D,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,WAAW,IAAG;AAC1C,YAAA,eAAe,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;AAC5E,SAAC,CAAC,CAAC;AACH,QAAA,OAAO,eAAe,CAAC;KACxB;;AAGD,IAAA,cAAc,CAAI,QACkB,EAAA;AAClC,QAAA,IAAI,QAAQ,YAAY,WAAW,EAAE;AACnC,YAAA,OAAO,QAA0B,CAAC;SACnC;AAAM,aAAA,IAAI,QAAQ,YAAY,eAAe,EAAE;AAC9C,YAAA,OAAO,QAAQ,CAAC;SACjB;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AAClC,YAAA,MAAM,KAAK,GAA0B,QAAQ,CAAC,CAAC,CAAC,CAAC;AACjD,YAAA,MAAM,SAAS,GAAmC,QAAQ,CAAC,MAAM,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAE,GAAG,IAAI,CAAC;AAC5F,YAAA,MAAM,cAAc,GAChB,QAAQ,CAAC,MAAM,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAE,GAAG,IAAI,CAAC;YAC9C,OAAO,IAAI,CAAC,OAAO,CAAI,KAAK,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;SAC1D;AAAM,aAAA;AACL,YAAA,OAAO,IAAI,CAAC,OAAO,CAAI,QAAQ,CAAC,CAAC;SAClC;KACF;yHA3PU,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAAX,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAW,cADC,MAAM,EAAA,CAAA,CAAA,EAAA;;sGAClB,WAAW,EAAA,UAAA,EAAA,CAAA;kBADvB,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;AA+PhC;;;;;;AAMG;MAKmB,sBAAsB,CAAA;yHAAtB,sBAAsB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAAtB,sBAAsB,EAAA,UAAA,EAH9B,MAAM,EAAA,UAAA,EACN,MAAM,MAAM,CAAC,WAAW,CAAC,CAAC,WAAW,EAAA,CAAA,CAAA,EAAA;;sGAE7B,sBAAsB,EAAA,UAAA,EAAA,CAAA;kBAJ3C,UAAU;AAAC,YAAA,IAAA,EAAA,CAAA;AACV,oBAAA,UAAU,EAAE,MAAM;oBAClB,UAAU,EAAE,MAAM,MAAM,CAAC,WAAW,CAAC,CAAC,WAAW;AAClD,iBAAA,CAAA;;AAyCD;;AAEG;AAEG,MAAO,kBAAmB,SAAQ,WAAW,CAAA;AAkBxC,IAAA,KAAK,CACV,cAAoC,EACpC,OAAA,GAA4D,IAAI,EAAA;QAClE,OAAO,KAAK,CAAC,KAAK,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;KAC7C;AAED;;AAEG;AACM,IAAA,OAAO,CACZ,SAAc,EAAE,eAAmE,EACnF,cAAyD,EAAA;QAC3D,OAAO,KAAK,CAAC,OAAO,CAAC,SAAS,EAAE,eAAe,EAAE,cAAc,CAAC,CAAC;KAClE;AAED;;AAEG;AACM,IAAA,KAAK,CACV,cAAqB,EACrB,eAAuE,EACvE,cAAyD,EAAA;QAC3D,OAAO,KAAK,CAAC,KAAK,CAAC,cAAc,EAAE,eAAe,EAAE,cAAc,CAAC,CAAC;KACrE;yHAzCU,kBAAkB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAAlB,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,cADN,MAAM,EAAA,CAAA,CAAA,EAAA;;sGAClB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAD9B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;;ACzZhC;;;;AAIG;AAIH;;AAEG;MACU,OAAO,GAAG,IAAI,OAAO,CAAC,mBAAmB;;ACNtD;;;;;;;;AAQG;MAKU,WAAW,CAAA;AACtB;;;;;;;AAOG;IACH,OAAO,UAAU,CAAC,IAEjB,EAAA;QACC,OAAO;AACL,YAAA,QAAQ,EAAE,WAAW;AACrB,YAAA,SAAS,EAAE,CAAC;AACV,oBAAA,OAAO,EAAE,uBAAuB;AAChC,oBAAA,QAAQ,EAAE,IAAI,CAAC,oBAAoB,IAAI,uBAAuB;iBAC/D,CAAC;SACH,CAAC;KACH;yHAnBU,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAX,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAW,2DAFZC,0BAAyB,EAAAC,OAAA,EAAAC,YAAA,EAAAC,MAAA,CAAA,EAAA,CAAA,CAAA,EAAA;AAExB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAW,YAFZH,0BAAyB,CAAA,EAAA,CAAA,CAAA,EAAA;;sGAExB,WAAW,EAAA,UAAA,EAAA,CAAA;kBAJvB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,YAAY,EAAE,0BAA0B;AACxC,oBAAA,OAAO,EAAE,CAACA,0BAAyB,EAAE,0BAA0B,CAAC;AACjE,iBAAA,CAAA;;AAuBD;;;;;;;;AAQG;MAKU,mBAAmB,CAAA;AAC9B;;;;;;;;;AASG;IACH,OAAO,UAAU,CAAC,IAIC,EAAA;QACjB,OAAO;AACL,YAAA,QAAQ,EAAE,mBAAmB;AAC7B,YAAA,SAAS,EAAE;AACT,gBAAA;AACE,oBAAA,OAAO,EAAE,kCAAkC;AAC3C,oBAAA,QAAQ,EAAE,IAAI,CAAC,4BAA4B,IAAI,QAAQ;AACxD,iBAAA;AACD,gBAAA;AACE,oBAAA,OAAO,EAAE,uBAAuB;AAChC,oBAAA,QAAQ,EAAE,IAAI,CAAC,oBAAoB,IAAI,uBAAuB;AAC/D,iBAAA;AACF,aAAA;SACF,CAAC;KACH;yHA7BU,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAnB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,mBAAmB,qHAFpBA,0BAAyB,EAAAI,oBAAA,EAAAC,kBAAA,EAAAC,eAAA,EAAAC,aAAA,EAAAC,aAAA,CAAA,EAAA,CAAA,CAAA,EAAA;AAExB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,mBAAmB,YAFpBR,0BAAyB,CAAA,EAAA,CAAA,CAAA,EAAA;;sGAExB,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAJ/B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,YAAY,EAAE,CAAC,0BAA0B,CAAC;AAC1C,oBAAA,OAAO,EAAE,CAACA,0BAAyB,EAAE,0BAA0B,CAAC;AACjE,iBAAA,CAAA;;;ACpDD;;;;;;;;;AASG;;ACTH;;;;AAIG;AAGH;;ACPA;;ACRA;;AAEG;;;;"}