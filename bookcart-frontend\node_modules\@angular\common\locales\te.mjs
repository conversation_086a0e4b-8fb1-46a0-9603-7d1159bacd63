/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["te", [["ఉ", "సా"], ["AM", "PM"], u], [["AM", "PM"], u, u], [["ఆ", "సో", "మ", "బు", "గు", "శు", "శ"], ["ఆది", "సోమ", "మంగళ", "బుధ", "గురు", "శుక్ర", "శని"], ["ఆదివారం", "సోమవారం", "మంగళవారం", "బుధవారం", "గురువారం", "శుక్రవారం", "శనివారం"], ["ఆది", "సోమ", "మం", "బుధ", "గురు", "శుక్ర", "శని"]], u, [["జ", "ఫి", "మా", "ఏ", "మే", "జూ", "జు", "ఆ", "సె", "అ", "న", "డి"], ["జన", "ఫిబ్ర", "మార్చి", "ఏప్రి", "మే", "జూన్", "జులై", "ఆగ", "సెప్టెం", "అక్టో", "నవం", "డిసెం"], ["జనవరి", "ఫిబ్రవరి", "మార్చి", "ఏప్రిల్", "మే", "జూన్", "జులై", "ఆగస్టు", "సెప్టెంబర్", "అక్టోబర్", "నవంబర్", "డిసెంబర్"]], u, [["క్రీపూ", "క్రీశ"], u, ["క్రీస్తు పూర్వం", "క్రీస్తు శకం"]], 0, [0, 0], ["dd-MM-yy", "d MMM, y", "d MMMM, y", "d, MMMM y, EEEE"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1} {0}", u, "{1} {0}కి", u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##,##0.###", "#,##0%", "¤#,##,##0.00", "#E0"], "INR", "₹", "భారతదేశ రూపాయి", { "BYN": [u, "р."], "JPY": ["JP¥", "¥"], "PHP": [u, "₱"], "THB": ["฿"], "TWD": ["NT$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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