{"version": 3, "file": "common.mjs", "sources": ["../../../../../../packages/common/src/dom_adapter.ts", "../../../../../../packages/common/src/navigation/platform_navigation.ts", "../../../../../../packages/common/src/dom_tokens.ts", "../../../../../../packages/common/src/location/platform_location.ts", "../../../../../../packages/common/src/location/util.ts", "../../../../../../packages/common/src/location/location_strategy.ts", "../../../../../../packages/common/src/location/hash_location_strategy.ts", "../../../../../../packages/common/src/location/location.ts", "../../../../../../packages/common/src/i18n/currencies.ts", "../../../../../../packages/common/src/i18n/locale_data_api.ts", "../../../../../../packages/common/src/i18n/format_date.ts", "../../../../../../packages/common/src/i18n/format_number.ts", "../../../../../../packages/common/src/i18n/localization.ts", "../../../../../../packages/common/src/i18n/locale_data.ts", "../../../../../../packages/common/src/cookie.ts", "../../../../../../packages/common/src/directives/ng_class.ts", "../../../../../../packages/common/src/directives/ng_component_outlet.ts", "../../../../../../packages/common/src/directives/ng_for_of.ts", "../../../../../../packages/common/src/directives/ng_if.ts", "../../../../../../packages/common/src/directives/ng_switch_equality.ts", "../../../../../../packages/common/src/directives/ng_switch.ts", "../../../../../../packages/common/src/directives/ng_plural.ts", "../../../../../../packages/common/src/directives/ng_style.ts", "../../../../../../packages/common/src/directives/ng_template_outlet.ts", "../../../../../../packages/common/src/directives/index.ts", "../../../../../../packages/common/src/pipes/invalid_pipe_argument_error.ts", "../../../../../../packages/common/src/pipes/async_pipe.ts", "../../../../../../packages/common/src/pipes/case_conversion_pipes.ts", "../../../../../../packages/common/src/pipes/date_pipe_config.ts", "../../../../../../packages/common/src/pipes/date_pipe.ts", "../../../../../../packages/common/src/pipes/i18n_plural_pipe.ts", "../../../../../../packages/common/src/pipes/i18n_select_pipe.ts", "../../../../../../packages/common/src/pipes/json_pipe.ts", "../../../../../../packages/common/src/pipes/keyvalue_pipe.ts", "../../../../../../packages/common/src/pipes/number_pipe.ts", "../../../../../../packages/common/src/pipes/slice_pipe.ts", "../../../../../../packages/common/src/pipes/index.ts", "../../../../../../packages/common/src/common_module.ts", "../../../../../../packages/common/src/platform_id.ts", "../../../../../../packages/common/src/version.ts", "../../../../../../packages/common/src/viewport_scroller.ts", "../../../../../../packages/common/src/xhr.ts", "../../../../../../packages/common/src/directives/ng_optimized_image/image_loaders/constants.ts", "../../../../../../packages/common/src/directives/ng_optimized_image/url.ts", "../../../../../../packages/common/src/directives/ng_optimized_image/image_loaders/image_loader.ts", "../../../../../../packages/common/src/directives/ng_optimized_image/image_loaders/cloudflare_loader.ts", "../../../../../../packages/common/src/directives/ng_optimized_image/image_loaders/cloudinary_loader.ts", "../../../../../../packages/common/src/directives/ng_optimized_image/image_loaders/imagekit_loader.ts", "../../../../../../packages/common/src/directives/ng_optimized_image/image_loaders/imgix_loader.ts", "../../../../../../packages/common/src/directives/ng_optimized_image/image_loaders/netlify_loader.ts", "../../../../../../packages/common/src/directives/ng_optimized_image/error_helper.ts", "../../../../../../packages/common/src/directives/ng_optimized_image/asserts.ts", "../../../../../../packages/common/src/directives/ng_optimized_image/lcp_image_observer.ts", "../../../../../../packages/common/src/directives/ng_optimized_image/preconnect_link_checker.ts", "../../../../../../packages/common/src/directives/ng_optimized_image/tokens.ts", "../../../../../../packages/common/src/directives/ng_optimized_image/preload-link-creator.ts", "../../../../../../packages/common/src/directives/ng_optimized_image/ng_optimized_image.ts", "../../../../../../packages/common/src/common.ts", "../../../../../../packages/common/public_api.ts", "../../../../../../packages/common/index.ts", "../../../../../../packages/common/common.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nlet _DOM: DomAdapter = null!;\n\nexport function getDOM(): DomAdapter {\n  return _DOM;\n}\n\nexport function setRootDomAdapter(adapter: DomAdapter) {\n  _DOM ??= adapter;\n}\n\n/* tslint:disable:requireParameterType */\n/**\n * Provides DOM operations in an environment-agnostic way.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nexport abstract class DomAdapter {\n  // Needs Domino-friendly test utility\n  abstract dispatchEvent(el: any, evt: any): any;\n  abstract readonly supportsDOMEvents: boolean;\n\n  // Used by Meta\n  abstract remove(el: any): void;\n  abstract createElement(tagName: any, doc?: any): HTMLElement;\n  abstract createHtmlDocument(): Document;\n  abstract getDefaultDocument(): Document;\n\n  // Used by By.css\n  abstract isElementNode(node: any): boolean;\n\n  // Used by Testability\n  abstract isShadowRoot(node: any): boolean;\n\n  // Used by KeyEventsPlugin\n  abstract onAndCancel(el: any, evt: any, listener: any): Function;\n\n  // Used by PlatformLocation and ServerEventManagerPlugin\n  abstract getGlobalEventTarget(doc: Document, target: string): any;\n\n  // Used by PlatformLocation\n  abstract getBaseHref(doc: Document): string | null;\n  abstract resetBaseElement(): void;\n\n  // TODO: remove dependency in DefaultValueAccessor\n  abstract getUserAgent(): string;\n\n  // Used in the legacy @angular/http package which has some usage in g3.\n  abstract getCookie(name: string): string | null;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injectable} from '@angular/core';\n\nimport {\n  NavigateEvent,\n  Navigation,\n  NavigationCurrentEntryChangeEvent,\n  NavigationHistoryEntry,\n  NavigationNavigateOptions,\n  NavigationOptions,\n  NavigationReloadOptions,\n  NavigationResult,\n  NavigationTransition,\n  NavigationUpdateCurrentEntryOptions,\n} from './navigation_types';\n\n/**\n * This class wraps the platform Navigation API which allows server-specific and test\n * implementations.\n */\n@Injectable({providedIn: 'platform', useFactory: () => (window as any).navigation})\nexport abstract class PlatformNavigation implements Navigation {\n  abstract entries(): NavigationHistoryEntry[];\n  abstract currentEntry: NavigationHistoryEntry | null;\n  abstract updateCurrentEntry(options: NavigationUpdateCurrentEntryOptions): void;\n  abstract transition: NavigationTransition | null;\n  abstract canGoBack: boolean;\n  abstract canGoForward: boolean;\n  abstract navigate(url: string, options?: NavigationNavigateOptions | undefined): NavigationResult;\n  abstract reload(options?: NavigationReloadOptions | undefined): NavigationResult;\n  abstract traverseTo(key: string, options?: NavigationOptions | undefined): NavigationResult;\n  abstract back(options?: NavigationOptions | undefined): NavigationResult;\n  abstract forward(options?: NavigationOptions | undefined): NavigationResult;\n  abstract onnavigate: ((this: Navigation, ev: NavigateEvent) => any) | null;\n  abstract onnavigatesuccess: ((this: Navigation, ev: Event) => any) | null;\n  abstract onnavigateerror: ((this: Navigation, ev: ErrorEvent) => any) | null;\n  abstract oncurrententrychange:\n    | ((this: Navigation, ev: NavigationCurrentEntryChangeEvent) => any)\n    | null;\n  abstract addEventListener(type: unknown, listener: unknown, options?: unknown): void;\n  abstract removeEventListener(type: unknown, listener: unknown, options?: unknown): void;\n  abstract dispatchEvent(event: Event): boolean;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {InjectionToken} from '@angular/core';\n\n/**\n * A DI Token representing the main rendering context.\n * In a browser and SSR this is the DOM Document.\n * When using SSR, that document is created by [<PERSON><PERSON>](https://github.com/angular/domino).\n *\n * @publicApi\n */\nexport const DOCUMENT = new InjectionToken<Document>(ngDevMode ? 'DocumentToken' : '');\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {inject, Injectable, InjectionToken} from '@angular/core';\n\nimport {getDOM} from '../dom_adapter';\nimport {DOCUMENT} from '../dom_tokens';\n\n/**\n * This class should not be used directly by an application developer. Instead, use\n * {@link Location}.\n *\n * `PlatformLocation` encapsulates all calls to DOM APIs, which allows the Router to be\n * platform-agnostic.\n * This means that we can have different implementation of `PlatformLocation` for the different\n * platforms that Angular supports. For example, `@angular/platform-browser` provides an\n * implementation specific to the browser environment, while `@angular/platform-server` provides\n * one suitable for use with server-side rendering.\n *\n * The `PlatformLocation` class is used directly by all implementations of {@link LocationStrategy}\n * when they need to interact with the DOM APIs like pushState, popState, etc.\n *\n * {@link LocationStrategy} in turn is used by the {@link Location} service which is used directly\n * by the {@link Router} in order to navigate between routes. Since all interactions between {@link\n * Router} /\n * {@link Location} / {@link LocationStrategy} and DOM APIs flow through the `PlatformLocation`\n * class, they are all platform-agnostic.\n *\n * @publicApi\n */\n@Injectable({providedIn: 'platform', useFactory: () => inject(BrowserPlatformLocation)})\nexport abstract class PlatformLocation {\n  abstract getBaseHrefFromDOM(): string;\n  abstract getState(): unknown;\n  /**\n   * Returns a function that, when executed, removes the `popstate` event handler.\n   */\n  abstract onPopState(fn: LocationChangeListener): VoidFunction;\n  /**\n   * Returns a function that, when executed, removes the `hashchange` event handler.\n   */\n  abstract onHashChange(fn: LocationChangeListener): VoidFunction;\n\n  abstract get href(): string;\n  abstract get protocol(): string;\n  abstract get hostname(): string;\n  abstract get port(): string;\n  abstract get pathname(): string;\n  abstract get search(): string;\n  abstract get hash(): string;\n\n  abstract replaceState(state: any, title: string, url: string): void;\n\n  abstract pushState(state: any, title: string, url: string): void;\n\n  abstract forward(): void;\n\n  abstract back(): void;\n\n  historyGo?(relativePosition: number): void {\n    throw new Error(ngDevMode ? 'Not implemented' : '');\n  }\n}\n\n/**\n * @description\n * Indicates when a location is initialized.\n *\n * @publicApi\n */\nexport const LOCATION_INITIALIZED = new InjectionToken<Promise<any>>(\n  ngDevMode ? 'Location Initialized' : '',\n);\n\n/**\n * @description\n * A serializable version of the event from `onPopState` or `onHashChange`\n *\n * @publicApi\n */\nexport interface LocationChangeEvent {\n  type: string;\n  state: any;\n}\n\n/**\n * @publicApi\n */\nexport interface LocationChangeListener {\n  (event: LocationChangeEvent): any;\n}\n\n/**\n * `PlatformLocation` encapsulates all of the direct calls to platform APIs.\n * This class should not be used directly by an application developer. Instead, use\n * {@link Location}.\n *\n * @publicApi\n */\n@Injectable({\n  providedIn: 'platform',\n  useFactory: () => new BrowserPlatformLocation(),\n})\nexport class BrowserPlatformLocation extends PlatformLocation {\n  private _location: Location;\n  private _history: History;\n  private _doc = inject(DOCUMENT);\n\n  constructor() {\n    super();\n    this._location = window.location;\n    this._history = window.history;\n  }\n\n  override getBaseHrefFromDOM(): string {\n    return getDOM().getBaseHref(this._doc)!;\n  }\n\n  override onPopState(fn: LocationChangeListener): VoidFunction {\n    const window = getDOM().getGlobalEventTarget(this._doc, 'window');\n    window.addEventListener('popstate', fn, false);\n    return () => window.removeEventListener('popstate', fn);\n  }\n\n  override onHashChange(fn: LocationChangeListener): VoidFunction {\n    const window = getDOM().getGlobalEventTarget(this._doc, 'window');\n    window.addEventListener('hashchange', fn, false);\n    return () => window.removeEventListener('hashchange', fn);\n  }\n\n  override get href(): string {\n    return this._location.href;\n  }\n  override get protocol(): string {\n    return this._location.protocol;\n  }\n  override get hostname(): string {\n    return this._location.hostname;\n  }\n  override get port(): string {\n    return this._location.port;\n  }\n  override get pathname(): string {\n    return this._location.pathname;\n  }\n  override get search(): string {\n    return this._location.search;\n  }\n  override get hash(): string {\n    return this._location.hash;\n  }\n  override set pathname(newPath: string) {\n    this._location.pathname = newPath;\n  }\n\n  override pushState(state: any, title: string, url: string): void {\n    this._history.pushState(state, title, url);\n  }\n\n  override replaceState(state: any, title: string, url: string): void {\n    this._history.replaceState(state, title, url);\n  }\n\n  override forward(): void {\n    this._history.forward();\n  }\n\n  override back(): void {\n    this._history.back();\n  }\n\n  override historyGo(relativePosition: number = 0): void {\n    this._history.go(relativePosition);\n  }\n\n  override getState(): unknown {\n    return this._history.state;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Joins two parts of a URL with a slash if needed.\n *\n * @param start  URL string\n * @param end    URL string\n *\n *\n * @returns The joined URL string.\n */\nexport function joinWithSlash(start: string, end: string): string {\n  if (start.length == 0) {\n    return end;\n  }\n  if (end.length == 0) {\n    return start;\n  }\n  let slashes = 0;\n  if (start.endsWith('/')) {\n    slashes++;\n  }\n  if (end.startsWith('/')) {\n    slashes++;\n  }\n  if (slashes == 2) {\n    return start + end.substring(1);\n  }\n  if (slashes == 1) {\n    return start + end;\n  }\n  return start + '/' + end;\n}\n\n/**\n * Removes a trailing slash from a URL string if needed.\n * Looks for the first occurrence of either `#`, `?`, or the end of the\n * line as `/` characters and removes the trailing slash if one exists.\n *\n * @param url URL string.\n *\n * @returns The URL string, modified if needed.\n */\nexport function stripTrailingSlash(url: string): string {\n  const match = url.match(/#|\\?|$/);\n  const pathEndIdx = (match && match.index) || url.length;\n  const droppedSlashIdx = pathEndIdx - (url[pathEndIdx - 1] === '/' ? 1 : 0);\n  return url.slice(0, droppedSlashIdx) + url.slice(pathEndIdx);\n}\n\n/**\n * Normalizes URL parameters by prepending with `?` if needed.\n *\n * @param  params String of URL parameters.\n *\n * @returns The normalized URL parameters string.\n */\nexport function normalizeQueryParams(params: string): string {\n  return params && params[0] !== '?' ? '?' + params : params;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Inject, inject, Injectable, InjectionToken, OnDestroy, Optional} from '@angular/core';\n\nimport {DOCUMENT} from '../dom_tokens';\n\nimport {LocationChangeListener, PlatformLocation} from './platform_location';\nimport {joinWithSlash, normalizeQueryParams} from './util';\n\n/**\n * Enables the `Location` service to read route state from the browser's URL.\n * Angular provides two strategies:\n * `HashLocationStrategy` and `PathLocationStrategy`.\n *\n * Applications should use the `Router` or `Location` services to\n * interact with application route state.\n *\n * For instance, `HashLocationStrategy` produces URLs like\n * <code class=\"no-auto-link\">http://example.com#/foo</code>,\n * and `PathLocationStrategy` produces\n * <code class=\"no-auto-link\">http://example.com/foo</code> as an equivalent URL.\n *\n * See these two classes for more.\n *\n * @publicApi\n */\n@Injectable({providedIn: 'root', useFactory: () => inject(PathLocationStrategy)})\nexport abstract class LocationStrategy {\n  abstract path(includeHash?: boolean): string;\n  abstract prepareExternalUrl(internal: string): string;\n  abstract getState(): unknown;\n  abstract pushState(state: any, title: string, url: string, queryParams: string): void;\n  abstract replaceState(state: any, title: string, url: string, queryParams: string): void;\n  abstract forward(): void;\n  abstract back(): void;\n  historyGo?(relativePosition: number): void {\n    throw new Error(ngDevMode ? 'Not implemented' : '');\n  }\n  abstract onPopState(fn: LocationChangeListener): void;\n  abstract getBaseHref(): string;\n}\n\n/**\n * A predefined [DI token](guide/glossary#di-token) for the base href\n * to be used with the `PathLocationStrategy`.\n * The base href is the URL prefix that should be preserved when generating\n * and recognizing URLs.\n *\n * @usageNotes\n *\n * The following example shows how to use this token to configure the root app injector\n * with a base href value, so that the DI framework can supply the dependency anywhere in the app.\n *\n * ```typescript\n * import {NgModule} from '@angular/core';\n * import {APP_BASE_HREF} from '@angular/common';\n *\n * @NgModule({\n *   providers: [{provide: APP_BASE_HREF, useValue: '/my/app'}]\n * })\n * class AppModule {}\n * ```\n *\n * @publicApi\n */\nexport const APP_BASE_HREF = new InjectionToken<string>(ngDevMode ? 'appBaseHref' : '');\n\n/**\n * @description\n * A {@link LocationStrategy} used to configure the {@link Location} service to\n * represent its state in the\n * [path](https://en.wikipedia.org/wiki/Uniform_Resource_Locator#Syntax) of the\n * browser's URL.\n *\n * If you're using `PathLocationStrategy`, you may provide a {@link APP_BASE_HREF}\n * or add a `<base href>` element to the document to override the default.\n *\n * For instance, if you provide an `APP_BASE_HREF` of `'/my/app/'` and call\n * `location.go('/foo')`, the browser's URL will become\n * `example.com/my/app/foo`. To ensure all relative URIs resolve correctly,\n * the `<base href>` and/or `APP_BASE_HREF` should end with a `/`.\n *\n * Similarly, if you add `<base href='/my/app/'/>` to the document and call\n * `location.go('/foo')`, the browser's URL will become\n * `example.com/my/app/foo`.\n *\n * Note that when using `PathLocationStrategy`, neither the query nor\n * the fragment in the `<base href>` will be preserved, as outlined\n * by the [RFC](https://tools.ietf.org/html/rfc3986#section-5.2.2).\n *\n * @usageNotes\n *\n * ### Example\n *\n * {@example common/location/ts/path_location_component.ts region='LocationComponent'}\n *\n * @publicApi\n */\n@Injectable({providedIn: 'root'})\nexport class PathLocationStrategy extends LocationStrategy implements OnDestroy {\n  private _baseHref: string;\n  private _removeListenerFns: (() => void)[] = [];\n\n  constructor(\n    private _platformLocation: PlatformLocation,\n    @Optional() @Inject(APP_BASE_HREF) href?: string,\n  ) {\n    super();\n\n    this._baseHref =\n      href ??\n      this._platformLocation.getBaseHrefFromDOM() ??\n      inject(DOCUMENT).location?.origin ??\n      '';\n  }\n\n  /** @nodoc */\n  ngOnDestroy(): void {\n    while (this._removeListenerFns.length) {\n      this._removeListenerFns.pop()!();\n    }\n  }\n\n  override onPopState(fn: LocationChangeListener): void {\n    this._removeListenerFns.push(\n      this._platformLocation.onPopState(fn),\n      this._platformLocation.onHashChange(fn),\n    );\n  }\n\n  override getBaseHref(): string {\n    return this._baseHref;\n  }\n\n  override prepareExternalUrl(internal: string): string {\n    return joinWithSlash(this._baseHref, internal);\n  }\n\n  override path(includeHash: boolean = false): string {\n    const pathname =\n      this._platformLocation.pathname + normalizeQueryParams(this._platformLocation.search);\n    const hash = this._platformLocation.hash;\n    return hash && includeHash ? `${pathname}${hash}` : pathname;\n  }\n\n  override pushState(state: any, title: string, url: string, queryParams: string) {\n    const externalUrl = this.prepareExternalUrl(url + normalizeQueryParams(queryParams));\n    this._platformLocation.pushState(state, title, externalUrl);\n  }\n\n  override replaceState(state: any, title: string, url: string, queryParams: string) {\n    const externalUrl = this.prepareExternalUrl(url + normalizeQueryParams(queryParams));\n    this._platformLocation.replaceState(state, title, externalUrl);\n  }\n\n  override forward(): void {\n    this._platformLocation.forward();\n  }\n\n  override back(): void {\n    this._platformLocation.back();\n  }\n\n  override getState(): unknown {\n    return this._platformLocation.getState();\n  }\n\n  override historyGo(relativePosition: number = 0): void {\n    this._platformLocation.historyGo?.(relativePosition);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Inject, Injectable, OnDestroy, Optional} from '@angular/core';\n\nimport {APP_BASE_HREF, LocationStrategy} from './location_strategy';\nimport {LocationChangeListener, PlatformLocation} from './platform_location';\nimport {joinWithSlash, normalizeQueryParams} from './util';\n\n/**\n * @description\n * A {@link LocationStrategy} used to configure the {@link Location} service to\n * represent its state in the\n * [hash fragment](https://en.wikipedia.org/wiki/Uniform_Resource_Locator#Syntax)\n * of the browser's URL.\n *\n * For instance, if you call `location.go('/foo')`, the browser's URL will become\n * `example.com#/foo`.\n *\n * @usageNotes\n *\n * ### Example\n *\n * {@example common/location/ts/hash_location_component.ts region='LocationComponent'}\n *\n * @publicApi\n */\n@Injectable()\nexport class HashLocationStrategy extends LocationStrategy implements OnDestroy {\n  private _baseHref: string = '';\n  private _removeListenerFns: (() => void)[] = [];\n\n  constructor(\n    private _platformLocation: PlatformLocation,\n    @Optional() @Inject(APP_BASE_HREF) _baseHref?: string,\n  ) {\n    super();\n    if (_baseHref != null) {\n      this._baseHref = _baseHref;\n    }\n  }\n\n  /** @nodoc */\n  ngOnDestroy(): void {\n    while (this._removeListenerFns.length) {\n      this._removeListenerFns.pop()!();\n    }\n  }\n\n  override onPopState(fn: LocationChangeListener): void {\n    this._removeListenerFns.push(\n      this._platformLocation.onPopState(fn),\n      this._platformLocation.onHashChange(fn),\n    );\n  }\n\n  override getBaseHref(): string {\n    return this._baseHref;\n  }\n\n  override path(includeHash: boolean = false): string {\n    // the hash value is always prefixed with a `#`\n    // and if it is empty then it will stay empty\n    const path = this._platformLocation.hash ?? '#';\n\n    return path.length > 0 ? path.substring(1) : path;\n  }\n\n  override prepareExternalUrl(internal: string): string {\n    const url = joinWithSlash(this._baseHref, internal);\n    return url.length > 0 ? '#' + url : url;\n  }\n\n  override pushState(state: any, title: string, path: string, queryParams: string) {\n    let url: string | null = this.prepareExternalUrl(path + normalizeQueryParams(queryParams));\n    if (url.length == 0) {\n      url = this._platformLocation.pathname;\n    }\n    this._platformLocation.pushState(state, title, url);\n  }\n\n  override replaceState(state: any, title: string, path: string, queryParams: string) {\n    let url = this.prepareExternalUrl(path + normalizeQueryParams(queryParams));\n    if (url.length == 0) {\n      url = this._platformLocation.pathname;\n    }\n    this._platformLocation.replaceState(state, title, url);\n  }\n\n  override forward(): void {\n    this._platformLocation.forward();\n  }\n\n  override back(): void {\n    this._platformLocation.back();\n  }\n\n  override getState(): unknown {\n    return this._platformLocation.getState();\n  }\n\n  override historyGo(relativePosition: number = 0): void {\n    this._platformLocation.historyGo?.(relativePosition);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {EventEmitter, Injectable, OnDestroy, ɵɵinject} from '@angular/core';\nimport {SubscriptionLike} from 'rxjs';\n\nimport {LocationStrategy} from './location_strategy';\nimport {joinWithSlash, normalizeQueryParams, stripTrailingSlash} from './util';\n\n/** @publicApi */\nexport interface PopStateEvent {\n  pop?: boolean;\n  state?: any;\n  type?: string;\n  url?: string;\n}\n\n/**\n * @description\n *\n * A service that applications can use to interact with a browser's URL.\n *\n * Depending on the `LocationStrategy` used, `Location` persists\n * to the URL's path or the URL's hash segment.\n *\n * @usageNotes\n *\n * It's better to use the `Router.navigate()` service to trigger route changes. Use\n * `Location` only if you need to interact with or create normalized URLs outside of\n * routing.\n *\n * `Location` is responsible for normalizing the URL against the application's base href.\n * A normalized URL is absolute from the URL host, includes the application's base href, and has no\n * trailing slash:\n * - `/my/app/user/123` is normalized\n * - `my/app/user/123` **is not** normalized\n * - `/my/app/user/123/` **is not** normalized\n *\n * ### Example\n *\n * <code-example path='common/location/ts/path_location_component.ts'\n * region='LocationComponent'></code-example>\n *\n * @publicApi\n */\n@Injectable({\n  providedIn: 'root',\n  // See #23917\n  useFactory: createLocation,\n})\nexport class Location implements OnDestroy {\n  /** @internal */\n  _subject: EventEmitter<any> = new EventEmitter();\n  /** @internal */\n  _basePath: string;\n  /** @internal */\n  _locationStrategy: LocationStrategy;\n  /** @internal */\n  _urlChangeListeners: ((url: string, state: unknown) => void)[] = [];\n  /** @internal */\n  _urlChangeSubscription: SubscriptionLike | null = null;\n\n  constructor(locationStrategy: LocationStrategy) {\n    this._locationStrategy = locationStrategy;\n    const baseHref = this._locationStrategy.getBaseHref();\n    // Note: This class's interaction with base HREF does not fully follow the rules\n    // outlined in the spec https://www.freesoft.org/CIE/RFC/1808/18.htm.\n    // Instead of trying to fix individual bugs with more and more code, we should\n    // investigate using the URL constructor and providing the base as a second\n    // argument.\n    // https://developer.mozilla.org/en-US/docs/Web/API/URL/URL#parameters\n    this._basePath = _stripOrigin(stripTrailingSlash(_stripIndexHtml(baseHref)));\n    this._locationStrategy.onPopState((ev) => {\n      this._subject.emit({\n        'url': this.path(true),\n        'pop': true,\n        'state': ev.state,\n        'type': ev.type,\n      });\n    });\n  }\n\n  /** @nodoc */\n  ngOnDestroy(): void {\n    this._urlChangeSubscription?.unsubscribe();\n    this._urlChangeListeners = [];\n  }\n\n  /**\n   * Normalizes the URL path for this location.\n   *\n   * @param includeHash True to include an anchor fragment in the path.\n   *\n   * @returns The normalized URL path.\n   */\n  // TODO: vsavkin. Remove the boolean flag and always include hash once the deprecated router is\n  // removed.\n  path(includeHash: boolean = false): string {\n    return this.normalize(this._locationStrategy.path(includeHash));\n  }\n\n  /**\n   * Reports the current state of the location history.\n   * @returns The current value of the `history.state` object.\n   */\n  getState(): unknown {\n    return this._locationStrategy.getState();\n  }\n\n  /**\n   * Normalizes the given path and compares to the current normalized path.\n   *\n   * @param path The given URL path.\n   * @param query Query parameters.\n   *\n   * @returns True if the given URL path is equal to the current normalized path, false\n   * otherwise.\n   */\n  isCurrentPathEqualTo(path: string, query: string = ''): boolean {\n    return this.path() == this.normalize(path + normalizeQueryParams(query));\n  }\n\n  /**\n   * Normalizes a URL path by stripping any trailing slashes.\n   *\n   * @param url String representing a URL.\n   *\n   * @returns The normalized URL string.\n   */\n  normalize(url: string): string {\n    return Location.stripTrailingSlash(_stripBasePath(this._basePath, _stripIndexHtml(url)));\n  }\n\n  /**\n   * Normalizes an external URL path.\n   * If the given URL doesn't begin with a leading slash (`'/'`), adds one\n   * before normalizing. Adds a hash if `HashLocationStrategy` is\n   * in use, or the `APP_BASE_HREF` if the `PathLocationStrategy` is in use.\n   *\n   * @param url String representing a URL.\n   *\n   * @returns  A normalized platform-specific URL.\n   */\n  prepareExternalUrl(url: string): string {\n    if (url && url[0] !== '/') {\n      url = '/' + url;\n    }\n    return this._locationStrategy.prepareExternalUrl(url);\n  }\n\n  // TODO: rename this method to pushState\n  /**\n   * Changes the browser's URL to a normalized version of a given URL, and pushes a\n   * new item onto the platform's history.\n   *\n   * @param path  URL path to normalize.\n   * @param query Query parameters.\n   * @param state Location history state.\n   *\n   */\n  go(path: string, query: string = '', state: any = null): void {\n    this._locationStrategy.pushState(state, '', path, query);\n    this._notifyUrlChangeListeners(\n      this.prepareExternalUrl(path + normalizeQueryParams(query)),\n      state,\n    );\n  }\n\n  /**\n   * Changes the browser's URL to a normalized version of the given URL, and replaces\n   * the top item on the platform's history stack.\n   *\n   * @param path  URL path to normalize.\n   * @param query Query parameters.\n   * @param state Location history state.\n   */\n  replaceState(path: string, query: string = '', state: any = null): void {\n    this._locationStrategy.replaceState(state, '', path, query);\n    this._notifyUrlChangeListeners(\n      this.prepareExternalUrl(path + normalizeQueryParams(query)),\n      state,\n    );\n  }\n\n  /**\n   * Navigates forward in the platform's history.\n   */\n  forward(): void {\n    this._locationStrategy.forward();\n  }\n\n  /**\n   * Navigates back in the platform's history.\n   */\n  back(): void {\n    this._locationStrategy.back();\n  }\n\n  /**\n   * Navigate to a specific page from session history, identified by its relative position to the\n   * current page.\n   *\n   * @param relativePosition  Position of the target page in the history relative to the current\n   *     page.\n   * A negative value moves backwards, a positive value moves forwards, e.g. `location.historyGo(2)`\n   * moves forward two pages and `location.historyGo(-2)` moves back two pages. When we try to go\n   * beyond what's stored in the history session, we stay in the current page. Same behaviour occurs\n   * when `relativePosition` equals 0.\n   * @see https://developer.mozilla.org/en-US/docs/Web/API/History_API#Moving_to_a_specific_point_in_history\n   */\n  historyGo(relativePosition: number = 0): void {\n    this._locationStrategy.historyGo?.(relativePosition);\n  }\n\n  /**\n   * Registers a URL change listener. Use to catch updates performed by the Angular\n   * framework that are not detectible through \"popstate\" or \"hashchange\" events.\n   *\n   * @param fn The change handler function, which take a URL and a location history state.\n   * @returns A function that, when executed, unregisters a URL change listener.\n   */\n  onUrlChange(fn: (url: string, state: unknown) => void): VoidFunction {\n    this._urlChangeListeners.push(fn);\n\n    this._urlChangeSubscription ??= this.subscribe((v) => {\n      this._notifyUrlChangeListeners(v.url, v.state);\n    });\n\n    return () => {\n      const fnIndex = this._urlChangeListeners.indexOf(fn);\n      this._urlChangeListeners.splice(fnIndex, 1);\n\n      if (this._urlChangeListeners.length === 0) {\n        this._urlChangeSubscription?.unsubscribe();\n        this._urlChangeSubscription = null;\n      }\n    };\n  }\n\n  /** @internal */\n  _notifyUrlChangeListeners(url: string = '', state: unknown) {\n    this._urlChangeListeners.forEach((fn) => fn(url, state));\n  }\n\n  /**\n   * Subscribes to the platform's `popState` events.\n   *\n   * Note: `Location.go()` does not trigger the `popState` event in the browser. Use\n   * `Location.onUrlChange()` to subscribe to URL changes instead.\n   *\n   * @param value Event that is triggered when the state history changes.\n   * @param exception The exception to throw.\n   *\n   * @see [onpopstate](https://developer.mozilla.org/en-US/docs/Web/API/WindowEventHandlers/onpopstate)\n   *\n   * @returns Subscribed events.\n   */\n  subscribe(\n    onNext: (value: PopStateEvent) => void,\n    onThrow?: ((exception: any) => void) | null,\n    onReturn?: (() => void) | null,\n  ): SubscriptionLike {\n    return this._subject.subscribe({next: onNext, error: onThrow, complete: onReturn});\n  }\n\n  /**\n   * Normalizes URL parameters by prepending with `?` if needed.\n   *\n   * @param  params String of URL parameters.\n   *\n   * @returns The normalized URL parameters string.\n   */\n  public static normalizeQueryParams: (params: string) => string = normalizeQueryParams;\n\n  /**\n   * Joins two parts of a URL with a slash if needed.\n   *\n   * @param start  URL string\n   * @param end    URL string\n   *\n   *\n   * @returns The joined URL string.\n   */\n  public static joinWithSlash: (start: string, end: string) => string = joinWithSlash;\n\n  /**\n   * Removes a trailing slash from a URL string if needed.\n   * Looks for the first occurrence of either `#`, `?`, or the end of the\n   * line as `/` characters and removes the trailing slash if one exists.\n   *\n   * @param url URL string.\n   *\n   * @returns The URL string, modified if needed.\n   */\n  public static stripTrailingSlash: (url: string) => string = stripTrailingSlash;\n}\n\nexport function createLocation() {\n  return new Location(ɵɵinject(LocationStrategy as any));\n}\n\nfunction _stripBasePath(basePath: string, url: string): string {\n  if (!basePath || !url.startsWith(basePath)) {\n    return url;\n  }\n  const strippedUrl = url.substring(basePath.length);\n  if (strippedUrl === '' || ['/', ';', '?', '#'].includes(strippedUrl[0])) {\n    return strippedUrl;\n  }\n  return url;\n}\n\nfunction _stripIndexHtml(url: string): string {\n  return url.replace(/\\/index.html$/, '');\n}\n\nfunction _stripOrigin(baseHref: string): string {\n  // DO NOT REFACTOR! Previously, this check looked like this:\n  // `/^(https?:)?\\/\\//.test(baseHref)`, but that resulted in\n  // syntactically incorrect code after Closure Compiler minification.\n  // This was likely caused by a bug in Closure Compiler, but\n  // for now, the check is rewritten to use `new RegExp` instead.\n  const isAbsoluteUrl = new RegExp('^(https?:)?//').test(baseHref);\n  if (isAbsoluteUrl) {\n    const [, pathname] = baseHref.split(/\\/\\/[^\\/]+/);\n    return pathname;\n  }\n  return baseHref;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nexport type CurrenciesSymbols = [string] | [string | undefined, string];\n\n/** @internal */\nexport const CURRENCIES_EN: {[code: string]: CurrenciesSymbols | [string | undefined, string | undefined, number]} = {\"ADP\":[undefined,undefined,0],\"AFN\":[undefined,\"؋\",0],\"ALL\":[undefined,undefined,0],\"AMD\":[undefined,\"֏\",2],\"AOA\":[undefined,\"Kz\"],\"ARS\":[undefined,\"$\"],\"AUD\":[\"A$\",\"$\"],\"AZN\":[undefined,\"₼\"],\"BAM\":[undefined,\"KM\"],\"BBD\":[undefined,\"$\"],\"BDT\":[undefined,\"৳\"],\"BHD\":[undefined,undefined,3],\"BIF\":[undefined,undefined,0],\"BMD\":[undefined,\"$\"],\"BND\":[undefined,\"$\"],\"BOB\":[undefined,\"Bs\"],\"BRL\":[\"R$\"],\"BSD\":[undefined,\"$\"],\"BWP\":[undefined,\"P\"],\"BYN\":[undefined,undefined,2],\"BYR\":[undefined,undefined,0],\"BZD\":[undefined,\"$\"],\"CAD\":[\"CA$\",\"$\",2],\"CHF\":[undefined,undefined,2],\"CLF\":[undefined,undefined,4],\"CLP\":[undefined,\"$\",0],\"CNY\":[\"CN¥\",\"¥\"],\"COP\":[undefined,\"$\",2],\"CRC\":[undefined,\"₡\",2],\"CUC\":[undefined,\"$\"],\"CUP\":[undefined,\"$\"],\"CZK\":[undefined,\"Kč\",2],\"DJF\":[undefined,undefined,0],\"DKK\":[undefined,\"kr\",2],\"DOP\":[undefined,\"$\"],\"EGP\":[undefined,\"E£\"],\"ESP\":[undefined,\"₧\",0],\"EUR\":[\"€\"],\"FJD\":[undefined,\"$\"],\"FKP\":[undefined,\"£\"],\"GBP\":[\"£\"],\"GEL\":[undefined,\"₾\"],\"GHS\":[undefined,\"GH₵\"],\"GIP\":[undefined,\"£\"],\"GNF\":[undefined,\"FG\",0],\"GTQ\":[undefined,\"Q\"],\"GYD\":[undefined,\"$\",2],\"HKD\":[\"HK$\",\"$\"],\"HNL\":[undefined,\"L\"],\"HRK\":[undefined,\"kn\"],\"HUF\":[undefined,\"Ft\",2],\"IDR\":[undefined,\"Rp\",2],\"ILS\":[\"₪\"],\"INR\":[\"₹\"],\"IQD\":[undefined,undefined,0],\"IRR\":[undefined,undefined,0],\"ISK\":[undefined,\"kr\",0],\"ITL\":[undefined,undefined,0],\"JMD\":[undefined,\"$\"],\"JOD\":[undefined,undefined,3],\"JPY\":[\"¥\",undefined,0],\"KHR\":[undefined,\"៛\"],\"KMF\":[undefined,\"CF\",0],\"KPW\":[undefined,\"₩\",0],\"KRW\":[\"₩\",undefined,0],\"KWD\":[undefined,undefined,3],\"KYD\":[undefined,\"$\"],\"KZT\":[undefined,\"₸\"],\"LAK\":[undefined,\"₭\",0],\"LBP\":[undefined,\"L£\",0],\"LKR\":[undefined,\"Rs\"],\"LRD\":[undefined,\"$\"],\"LTL\":[undefined,\"Lt\"],\"LUF\":[undefined,undefined,0],\"LVL\":[undefined,\"Ls\"],\"LYD\":[undefined,undefined,3],\"MGA\":[undefined,\"Ar\",0],\"MGF\":[undefined,undefined,0],\"MMK\":[undefined,\"K\",0],\"MNT\":[undefined,\"₮\",2],\"MRO\":[undefined,undefined,0],\"MUR\":[undefined,\"Rs\",2],\"MXN\":[\"MX$\",\"$\"],\"MYR\":[undefined,\"RM\"],\"NAD\":[undefined,\"$\"],\"NGN\":[undefined,\"₦\"],\"NIO\":[undefined,\"C$\"],\"NOK\":[undefined,\"kr\",2],\"NPR\":[undefined,\"Rs\"],\"NZD\":[\"NZ$\",\"$\"],\"OMR\":[undefined,undefined,3],\"PHP\":[\"₱\"],\"PKR\":[undefined,\"Rs\",2],\"PLN\":[undefined,\"zł\"],\"PYG\":[undefined,\"₲\",0],\"RON\":[undefined,\"lei\"],\"RSD\":[undefined,undefined,0],\"RUB\":[undefined,\"₽\"],\"RWF\":[undefined,\"RF\",0],\"SBD\":[undefined,\"$\"],\"SEK\":[undefined,\"kr\",2],\"SGD\":[undefined,\"$\"],\"SHP\":[undefined,\"£\"],\"SLE\":[undefined,undefined,2],\"SLL\":[undefined,undefined,0],\"SOS\":[undefined,undefined,0],\"SRD\":[undefined,\"$\"],\"SSP\":[undefined,\"£\"],\"STD\":[undefined,undefined,0],\"STN\":[undefined,\"Db\"],\"SYP\":[undefined,\"£\",0],\"THB\":[undefined,\"฿\"],\"TMM\":[undefined,undefined,0],\"TND\":[undefined,undefined,3],\"TOP\":[undefined,\"T$\"],\"TRL\":[undefined,undefined,0],\"TRY\":[undefined,\"₺\"],\"TTD\":[undefined,\"$\"],\"TWD\":[\"NT$\",\"$\",2],\"TZS\":[undefined,undefined,2],\"UAH\":[undefined,\"₴\"],\"UGX\":[undefined,undefined,0],\"USD\":[\"$\"],\"UYI\":[undefined,undefined,0],\"UYU\":[undefined,\"$\"],\"UYW\":[undefined,undefined,4],\"UZS\":[undefined,undefined,2],\"VEF\":[undefined,\"Bs\",2],\"VND\":[\"₫\",undefined,0],\"VUV\":[undefined,undefined,0],\"XAF\":[\"FCFA\",undefined,0],\"XCD\":[\"EC$\",\"$\"],\"XOF\":[\"F CFA\",undefined,0],\"XPF\":[\"CFPF\",undefined,0],\"XXX\":[\"¤\"],\"YER\":[undefined,undefined,0],\"ZAR\":[undefined,\"R\"],\"ZMK\":[undefined,undefined,0],\"ZMW\":[undefined,\"ZK\"],\"ZWD\":[undefined,undefined,0]};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ɵCurrencyIndex,\n  ɵExtraLocaleDataIndex,\n  ɵfindLocaleData,\n  ɵgetLocaleCurrencyCode,\n  ɵgetLocalePluralCase,\n  ɵLocaleDataIndex,\n} from '@angular/core';\n\nimport {CURRENCIES_EN, CurrenciesSymbols} from './currencies';\n\n/**\n * Format styles that can be used to represent numbers.\n * @see {@link getLocaleNumberFormat}\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nexport enum NumberFormatStyle {\n  Decimal,\n  Percent,\n  Currency,\n  Scientific,\n}\n\n/**\n * Plurality cases used for translating plurals to different languages.\n *\n * @see {@link NgPlural}\n * @see {@link NgPluralCase}\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nexport enum Plural {\n  Zero = 0,\n  One = 1,\n  Two = 2,\n  Few = 3,\n  Many = 4,\n  Other = 5,\n}\n\n/**\n * Context-dependant translation forms for strings.\n * Typically the standalone version is for the nominative form of the word,\n * and the format version is used for the genitive case.\n * @see [CLDR website](http://cldr.unicode.org/translation/date-time-1/date-time#TOC-Standalone-vs.-Format-Styles)\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nexport enum FormStyle {\n  Format,\n  Standalone,\n}\n\n/**\n * String widths available for translations.\n * The specific character widths are locale-specific.\n * Examples are given for the word \"Sunday\" in English.\n *\n * @publicApi\n */\nexport enum TranslationWidth {\n  /** 1 character for `en-US`. For example: 'S' */\n  Narrow,\n  /** 3 characters for `en-US`. For example: 'Sun' */\n  Abbreviated,\n  /** Full length for `en-US`. For example: \"Sunday\" */\n  Wide,\n  /** 2 characters for `en-US`, For example: \"Su\" */\n  Short,\n}\n\n/**\n * String widths available for date-time formats.\n * The specific character widths are locale-specific.\n * Examples are given for `en-US`.\n *\n * @see {@link getLocaleDateFormat}\n * @see {@link getLocaleTimeFormat}\n * @see {@link getLocaleDateTimeFormat}\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n * @publicApi\n */\nexport enum FormatWidth {\n  /**\n   * For `en-US`, `'M/d/yy, h:mm a'`\n   * (Example: `6/15/15, 9:03 AM`)\n   */\n  Short,\n  /**\n   * For `en-US`, `'MMM d, y, h:mm:ss a'`\n   * (Example: `Jun 15, 2015, 9:03:01 AM`)\n   */\n  Medium,\n  /**\n   * For `en-US`, `'MMMM d, y, h:mm:ss a z'`\n   * (Example: `June 15, 2015 at 9:03:01 AM GMT+1`)\n   */\n  Long,\n  /**\n   * For `en-US`, `'EEEE, MMMM d, y, h:mm:ss a zzzz'`\n   * (Example: `Monday, June 15, 2015 at 9:03:01 AM GMT+01:00`)\n   */\n  Full,\n}\n\n// This needs to be an object literal, rather than an enum, because TypeScript 5.4+\n// doesn't allow numeric keys and we have `Infinity` and `NaN`.\n/**\n * Symbols that can be used to replace placeholders in number patterns.\n * Examples are based on `en-US` values.\n *\n * @see {@link getLocaleNumberSymbol}\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n * @object-literal-as-enum\n */\nexport const NumberSymbol = {\n  /**\n   * Decimal separator.\n   * For `en-US`, the dot character.\n   * Example: 2,345`.`67\n   */\n  Decimal: 0,\n  /**\n   * Grouping separator, typically for thousands.\n   * For `en-US`, the comma character.\n   * Example: 2`,`345.67\n   */\n  Group: 1,\n  /**\n   * List-item separator.\n   * Example: \"one, two, and three\"\n   */\n  List: 2,\n  /**\n   * Sign for percentage (out of 100).\n   * Example: 23.4%\n   */\n  PercentSign: 3,\n  /**\n   * Sign for positive numbers.\n   * Example: +23\n   */\n  PlusSign: 4,\n  /**\n   * Sign for negative numbers.\n   * Example: -23\n   */\n  MinusSign: 5,\n  /**\n   * Computer notation for exponential value (n times a power of 10).\n   * Example: 1.2E3\n   */\n  Exponential: 6,\n  /**\n   * Human-readable format of exponential.\n   * Example: 1.2x103\n   */\n  SuperscriptingExponent: 7,\n  /**\n   * Sign for permille (out of 1000).\n   * Example: 23.4‰\n   */\n  PerMille: 8,\n  /**\n   * Infinity, can be used with plus and minus.\n   * Example: ∞, +∞, -∞\n   */\n  Infinity: 9,\n  /**\n   * Not a number.\n   * Example: NaN\n   */\n  NaN: 10,\n  /**\n   * Symbol used between time units.\n   * Example: 10:52\n   */\n  TimeSeparator: 11,\n  /**\n   * Decimal separator for currency values (fallback to `Decimal`).\n   * Example: $2,345.67\n   */\n  CurrencyDecimal: 12,\n  /**\n   * Group separator for currency values (fallback to `Group`).\n   * Example: $2,345.67\n   */\n  CurrencyGroup: 13,\n} as const;\n\nexport type NumberSymbol = (typeof NumberSymbol)[keyof typeof NumberSymbol];\n\n/**\n * The value for each day of the week, based on the `en-US` locale\n *\n * @publicApi\n */\nexport enum WeekDay {\n  Sunday = 0,\n  Monday,\n  Tuesday,\n  Wednesday,\n  Thursday,\n  Friday,\n  Saturday,\n}\n\n/**\n * Retrieves the locale ID from the currently loaded locale.\n * The loaded locale could be, for example, a global one rather than a regional one.\n * @param locale A locale code, such as `fr-FR`.\n * @returns The locale code. For example, `fr`.\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nexport function getLocaleId(locale: string): string {\n  return ɵfindLocaleData(locale)[ɵLocaleDataIndex.LocaleId];\n}\n\n/**\n * Retrieves day period strings for the given locale.\n *\n * @param locale A locale code for the locale format rules to use.\n * @param formStyle The required grammatical form.\n * @param width The required character width.\n * @returns An array of localized period strings. For example, `[AM, PM]` for `en-US`.\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nexport function getLocaleDayPeriods(\n  locale: string,\n  formStyle: FormStyle,\n  width: TranslationWidth,\n): Readonly<[string, string]> {\n  const data = ɵfindLocaleData(locale);\n  const amPmData = <[string, string][][]>[\n    data[ɵLocaleDataIndex.DayPeriodsFormat],\n    data[ɵLocaleDataIndex.DayPeriodsStandalone],\n  ];\n  const amPm = getLastDefinedValue(amPmData, formStyle);\n  return getLastDefinedValue(amPm, width);\n}\n\n/**\n * Retrieves days of the week for the given locale, using the Gregorian calendar.\n *\n * @param locale A locale code for the locale format rules to use.\n * @param formStyle The required grammatical form.\n * @param width The required character width.\n * @returns An array of localized name strings.\n * For example,`[Sunday, Monday, ... Saturday]` for `en-US`.\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nexport function getLocaleDayNames(\n  locale: string,\n  formStyle: FormStyle,\n  width: TranslationWidth,\n): ReadonlyArray<string> {\n  const data = ɵfindLocaleData(locale);\n  const daysData = <string[][][]>[\n    data[ɵLocaleDataIndex.DaysFormat],\n    data[ɵLocaleDataIndex.DaysStandalone],\n  ];\n  const days = getLastDefinedValue(daysData, formStyle);\n  return getLastDefinedValue(days, width);\n}\n\n/**\n * Retrieves months of the year for the given locale, using the Gregorian calendar.\n *\n * @param locale A locale code for the locale format rules to use.\n * @param formStyle The required grammatical form.\n * @param width The required character width.\n * @returns An array of localized name strings.\n * For example,  `[January, February, ...]` for `en-US`.\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nexport function getLocaleMonthNames(\n  locale: string,\n  formStyle: FormStyle,\n  width: TranslationWidth,\n): ReadonlyArray<string> {\n  const data = ɵfindLocaleData(locale);\n  const monthsData = <string[][][]>[\n    data[ɵLocaleDataIndex.MonthsFormat],\n    data[ɵLocaleDataIndex.MonthsStandalone],\n  ];\n  const months = getLastDefinedValue(monthsData, formStyle);\n  return getLastDefinedValue(months, width);\n}\n\n/**\n * Retrieves Gregorian-calendar eras for the given locale.\n * @param locale A locale code for the locale format rules to use.\n * @param width The required character width.\n\n * @returns An array of localized era strings.\n * For example, `[AD, BC]` for `en-US`.\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nexport function getLocaleEraNames(\n  locale: string,\n  width: TranslationWidth,\n): Readonly<[string, string]> {\n  const data = ɵfindLocaleData(locale);\n  const erasData = <[string, string][]>data[ɵLocaleDataIndex.Eras];\n  return getLastDefinedValue(erasData, width);\n}\n\n/**\n * Retrieves the first day of the week for the given locale.\n *\n * @param locale A locale code for the locale format rules to use.\n * @returns A day index number, using the 0-based week-day index for `en-US`\n * (Sunday = 0, Monday = 1, ...).\n * For example, for `fr-FR`, returns 1 to indicate that the first day is Monday.\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nexport function getLocaleFirstDayOfWeek(locale: string): WeekDay {\n  const data = ɵfindLocaleData(locale);\n  return data[ɵLocaleDataIndex.FirstDayOfWeek];\n}\n\n/**\n * Range of week days that are considered the week-end for the given locale.\n *\n * @param locale A locale code for the locale format rules to use.\n * @returns The range of day values, `[startDay, endDay]`.\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nexport function getLocaleWeekEndRange(locale: string): [WeekDay, WeekDay] {\n  const data = ɵfindLocaleData(locale);\n  return data[ɵLocaleDataIndex.WeekendRange];\n}\n\n/**\n * Retrieves a localized date-value formatting string.\n *\n * @param locale A locale code for the locale format rules to use.\n * @param width The format type.\n * @returns The localized formatting string.\n * @see {@link FormatWidth}\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nexport function getLocaleDateFormat(locale: string, width: FormatWidth): string {\n  const data = ɵfindLocaleData(locale);\n  return getLastDefinedValue(data[ɵLocaleDataIndex.DateFormat], width);\n}\n\n/**\n * Retrieves a localized time-value formatting string.\n *\n * @param locale A locale code for the locale format rules to use.\n * @param width The format type.\n * @returns The localized formatting string.\n * @see {@link FormatWidth}\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n\n * @publicApi\n */\nexport function getLocaleTimeFormat(locale: string, width: FormatWidth): string {\n  const data = ɵfindLocaleData(locale);\n  return getLastDefinedValue(data[ɵLocaleDataIndex.TimeFormat], width);\n}\n\n/**\n * Retrieves a localized date-time formatting string.\n *\n * @param locale A locale code for the locale format rules to use.\n * @param width The format type.\n * @returns The localized formatting string.\n * @see {@link FormatWidth}\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nexport function getLocaleDateTimeFormat(locale: string, width: FormatWidth): string {\n  const data = ɵfindLocaleData(locale);\n  const dateTimeFormatData = <string[]>data[ɵLocaleDataIndex.DateTimeFormat];\n  return getLastDefinedValue(dateTimeFormatData, width);\n}\n\n/**\n * Retrieves a localized number symbol that can be used to replace placeholders in number formats.\n * @param locale The locale code.\n * @param symbol The symbol to localize. Must be one of `NumberSymbol`.\n * @returns The character for the localized symbol.\n * @see {@link NumberSymbol}\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nexport function getLocaleNumberSymbol(locale: string, symbol: NumberSymbol): string {\n  const data = ɵfindLocaleData(locale);\n  const res = data[ɵLocaleDataIndex.NumberSymbols][symbol];\n  if (typeof res === 'undefined') {\n    if (symbol === NumberSymbol.CurrencyDecimal) {\n      return data[ɵLocaleDataIndex.NumberSymbols][NumberSymbol.Decimal];\n    } else if (symbol === NumberSymbol.CurrencyGroup) {\n      return data[ɵLocaleDataIndex.NumberSymbols][NumberSymbol.Group];\n    }\n  }\n  return res;\n}\n\n/**\n * Retrieves a number format for a given locale.\n *\n * Numbers are formatted using patterns, like `#,###.00`. For example, the pattern `#,###.00`\n * when used to format the number 12345.678 could result in \"12'345,678\". That would happen if the\n * grouping separator for your language is an apostrophe, and the decimal separator is a comma.\n *\n * <b>Important:</b> The characters `.` `,` `0` `#` (and others below) are special placeholders\n * that stand for the decimal separator, and so on, and are NOT real characters.\n * You must NOT \"translate\" the placeholders. For example, don't change `.` to `,` even though in\n * your language the decimal point is written with a comma. The symbols should be replaced by the\n * local equivalents, using the appropriate `NumberSymbol` for your language.\n *\n * Here are the special characters used in number patterns:\n *\n * | Symbol | Meaning |\n * |--------|---------|\n * | . | Replaced automatically by the character used for the decimal point. |\n * | , | Replaced by the \"grouping\" (thousands) separator. |\n * | 0 | Replaced by a digit (or zero if there aren't enough digits). |\n * | # | Replaced by a digit (or nothing if there aren't enough). |\n * | ¤ | Replaced by a currency symbol, such as $ or USD. |\n * | % | Marks a percent format. The % symbol may change position, but must be retained. |\n * | E | Marks a scientific format. The E symbol may change position, but must be retained. |\n * | ' | Special characters used as literal characters are quoted with ASCII single quotes. |\n *\n * @param locale A locale code for the locale format rules to use.\n * @param type The type of numeric value to be formatted (such as `Decimal` or `Currency`.)\n * @returns The localized format string.\n * @see {@link NumberFormatStyle}\n * @see [CLDR website](http://cldr.unicode.org/translation/number-patterns)\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nexport function getLocaleNumberFormat(locale: string, type: NumberFormatStyle): string {\n  const data = ɵfindLocaleData(locale);\n  return data[ɵLocaleDataIndex.NumberFormats][type];\n}\n\n/**\n * Retrieves the symbol used to represent the currency for the main country\n * corresponding to a given locale. For example, '$' for `en-US`.\n *\n * @param locale A locale code for the locale format rules to use.\n * @returns The localized symbol character,\n * or `null` if the main country cannot be determined.\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nexport function getLocaleCurrencySymbol(locale: string): string | null {\n  const data = ɵfindLocaleData(locale);\n  return data[ɵLocaleDataIndex.CurrencySymbol] || null;\n}\n\n/**\n * Retrieves the name of the currency for the main country corresponding\n * to a given locale. For example, 'US Dollar' for `en-US`.\n * @param locale A locale code for the locale format rules to use.\n * @returns The currency name,\n * or `null` if the main country cannot be determined.\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nexport function getLocaleCurrencyName(locale: string): string | null {\n  const data = ɵfindLocaleData(locale);\n  return data[ɵLocaleDataIndex.CurrencyName] || null;\n}\n\n/**\n * Retrieves the default currency code for the given locale.\n *\n * The default is defined as the first currency which is still in use.\n *\n * @param locale The code of the locale whose currency code we want.\n * @returns The code of the default currency for the given locale.\n *\n * @publicApi\n */\nexport function getLocaleCurrencyCode(locale: string): string | null {\n  return ɵgetLocaleCurrencyCode(locale);\n}\n\n/**\n * Retrieves the currency values for a given locale.\n * @param locale A locale code for the locale format rules to use.\n * @returns The currency values.\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n */\nfunction getLocaleCurrencies(locale: string): {[code: string]: CurrenciesSymbols} {\n  const data = ɵfindLocaleData(locale);\n  return data[ɵLocaleDataIndex.Currencies];\n}\n\n/**\n * @alias core/ɵgetLocalePluralCase\n * @publicApi\n */\nexport const getLocalePluralCase: (locale: string) => (value: number) => Plural =\n  ɵgetLocalePluralCase;\n\nfunction checkFullData(data: any) {\n  if (!data[ɵLocaleDataIndex.ExtraData]) {\n    throw new Error(\n      `Missing extra locale data for the locale \"${\n        data[ɵLocaleDataIndex.LocaleId]\n      }\". Use \"registerLocaleData\" to load new data. See the \"I18n guide\" on angular.io to know more.`,\n    );\n  }\n}\n\n/**\n * Retrieves locale-specific rules used to determine which day period to use\n * when more than one period is defined for a locale.\n *\n * There is a rule for each defined day period. The\n * first rule is applied to the first day period and so on.\n * Fall back to AM/PM when no rules are available.\n *\n * A rule can specify a period as time range, or as a single time value.\n *\n * This functionality is only available when you have loaded the full locale data.\n * See the [\"I18n guide\"](guide/i18n-common-format-data-locale).\n *\n * @param locale A locale code for the locale format rules to use.\n * @returns The rules for the locale, a single time value or array of *from-time, to-time*,\n * or null if no periods are available.\n *\n * @see {@link getLocaleExtraDayPeriods}\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nexport function getLocaleExtraDayPeriodRules(locale: string): (Time | [Time, Time])[] {\n  const data = ɵfindLocaleData(locale);\n  checkFullData(data);\n  const rules = data[ɵLocaleDataIndex.ExtraData][ɵExtraLocaleDataIndex.ExtraDayPeriodsRules] || [];\n  return rules.map((rule: string | [string, string]) => {\n    if (typeof rule === 'string') {\n      return extractTime(rule);\n    }\n    return [extractTime(rule[0]), extractTime(rule[1])];\n  });\n}\n\n/**\n * Retrieves locale-specific day periods, which indicate roughly how a day is broken up\n * in different languages.\n * For example, for `en-US`, periods are morning, noon, afternoon, evening, and midnight.\n *\n * This functionality is only available when you have loaded the full locale data.\n * See the [\"I18n guide\"](guide/i18n-common-format-data-locale).\n *\n * @param locale A locale code for the locale format rules to use.\n * @param formStyle The required grammatical form.\n * @param width The required character width.\n * @returns The translated day-period strings.\n * @see {@link getLocaleExtraDayPeriodRules}\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nexport function getLocaleExtraDayPeriods(\n  locale: string,\n  formStyle: FormStyle,\n  width: TranslationWidth,\n): string[] {\n  const data = ɵfindLocaleData(locale);\n  checkFullData(data);\n  const dayPeriodsData = <string[][][]>[\n    data[ɵLocaleDataIndex.ExtraData][ɵExtraLocaleDataIndex.ExtraDayPeriodFormats],\n    data[ɵLocaleDataIndex.ExtraData][ɵExtraLocaleDataIndex.ExtraDayPeriodStandalone],\n  ];\n  const dayPeriods = getLastDefinedValue(dayPeriodsData, formStyle) || [];\n  return getLastDefinedValue(dayPeriods, width) || [];\n}\n\n/**\n * Retrieves the writing direction of a specified locale\n * @param locale A locale code for the locale format rules to use.\n * @publicApi\n * @returns 'rtl' or 'ltr'\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n */\nexport function getLocaleDirection(locale: string): 'ltr' | 'rtl' {\n  const data = ɵfindLocaleData(locale);\n  return data[ɵLocaleDataIndex.Directionality];\n}\n\n/**\n * Retrieves the first value that is defined in an array, going backwards from an index position.\n *\n * To avoid repeating the same data (as when the \"format\" and \"standalone\" forms are the same)\n * add the first value to the locale data arrays, and add other values only if they are different.\n *\n * @param data The data array to retrieve from.\n * @param index A 0-based index into the array to start from.\n * @returns The value immediately before the given index position.\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nfunction getLastDefinedValue<T>(data: T[], index: number): T {\n  for (let i = index; i > -1; i--) {\n    if (typeof data[i] !== 'undefined') {\n      return data[i];\n    }\n  }\n  throw new Error('Locale data API: locale data undefined');\n}\n\n/**\n * Represents a time value with hours and minutes.\n *\n * @publicApi\n */\nexport type Time = {\n  hours: number;\n  minutes: number;\n};\n\n/**\n * Extracts the hours and minutes from a string like \"15:45\"\n */\nfunction extractTime(time: string): Time {\n  const [h, m] = time.split(':');\n  return {hours: +h, minutes: +m};\n}\n\n/**\n * Retrieves the currency symbol for a given currency code.\n *\n * For example, for the default `en-US` locale, the code `USD` can\n * be represented by the narrow symbol `$` or the wide symbol `US$`.\n *\n * @param code The currency code.\n * @param format The format, `wide` or `narrow`.\n * @param locale A locale code for the locale format rules to use.\n *\n * @returns The symbol, or the currency code if no symbol is available.\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nexport function getCurrencySymbol(code: string, format: 'wide' | 'narrow', locale = 'en'): string {\n  const currency = getLocaleCurrencies(locale)[code] || CURRENCIES_EN[code] || [];\n  const symbolNarrow = currency[ɵCurrencyIndex.SymbolNarrow];\n\n  if (format === 'narrow' && typeof symbolNarrow === 'string') {\n    return symbolNarrow;\n  }\n\n  return currency[ɵCurrencyIndex.Symbol] || code;\n}\n\n// Most currencies have cents, that's why the default is 2\nconst DEFAULT_NB_OF_CURRENCY_DIGITS = 2;\n\n/**\n * Reports the number of decimal digits for a given currency.\n * The value depends upon the presence of cents in that particular currency.\n *\n * @param code The currency code.\n * @returns The number of decimal digits, typically 0 or 2.\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nexport function getNumberOfCurrencyDigits(code: string): number {\n  let digits;\n  const currency = CURRENCIES_EN[code];\n  if (currency) {\n    digits = currency[ɵCurrencyIndex.NbOfDigits];\n  }\n  return typeof digits === 'number' ? digits : DEFAULT_NB_OF_CURRENCY_DIGITS;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  FormatWidth,\n  FormStyle,\n  getLocaleDateFormat,\n  getLocaleDateTimeFormat,\n  getLocaleDayNames,\n  getLocaleDayPeriods,\n  getLocaleEraNames,\n  getLocaleExtraDayPeriodRules,\n  getLocaleExtraDayPeriods,\n  getLocaleId,\n  getLocaleMonthNames,\n  getLocaleNumberSymbol,\n  getLocaleTimeFormat,\n  NumberSymbol,\n  Time,\n  TranslationWidth,\n} from './locale_data_api';\n\nexport const ISO8601_DATE_REGEX =\n  /^(\\d{4,})-?(\\d\\d)-?(\\d\\d)(?:T(\\d\\d)(?::?(\\d\\d)(?::?(\\d\\d)(?:\\.(\\d+))?)?)?(Z|([+-])(\\d\\d):?(\\d\\d))?)?$/;\n//    1        2       3         4          5          6          7          8  9     10      11\nconst NAMED_FORMATS: {[localeId: string]: {[format: string]: string}} = {};\nconst DATE_FORMATS_SPLIT =\n  /((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\\s\\S]*)/;\n\nenum ZoneWidth {\n  Short,\n  ShortGMT,\n  Long,\n  Extended,\n}\n\nenum DateType {\n  FullYear,\n  Month,\n  Date,\n  Hours,\n  Minutes,\n  Seconds,\n  FractionalSeconds,\n  Day,\n}\n\nenum TranslationType {\n  DayPeriods,\n  Days,\n  Months,\n  Eras,\n}\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Formats a date according to locale rules.\n *\n * @param value The date to format, as a Date, or a number (milliseconds since UTC epoch)\n * or an [ISO date-time string](https://www.w3.org/TR/NOTE-datetime).\n * @param format The date-time components to include. See `DatePipe` for details.\n * @param locale A locale code for the locale format rules to use.\n * @param timezone The time zone. A time zone offset from GMT (such as `'+0430'`),\n * or a standard UTC/GMT or continental US time zone abbreviation.\n * If not specified, uses host system settings.\n *\n * @returns The formatted date string.\n *\n * @see {@link DatePipe}\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nexport function formatDate(\n  value: string | number | Date,\n  format: string,\n  locale: string,\n  timezone?: string,\n): string {\n  let date = toDate(value);\n  const namedFormat = getNamedFormat(locale, format);\n  format = namedFormat || format;\n\n  let parts: string[] = [];\n  let match;\n  while (format) {\n    match = DATE_FORMATS_SPLIT.exec(format);\n    if (match) {\n      parts = parts.concat(match.slice(1));\n      const part = parts.pop();\n      if (!part) {\n        break;\n      }\n      format = part;\n    } else {\n      parts.push(format);\n      break;\n    }\n  }\n\n  let dateTimezoneOffset = date.getTimezoneOffset();\n  if (timezone) {\n    dateTimezoneOffset = timezoneToOffset(timezone, dateTimezoneOffset);\n    date = convertTimezoneToLocal(date, timezone, true);\n  }\n\n  let text = '';\n  parts.forEach((value) => {\n    const dateFormatter = getDateFormatter(value);\n    text += dateFormatter\n      ? dateFormatter(date, locale, dateTimezoneOffset)\n      : value === \"''\"\n      ? \"'\"\n      : value.replace(/(^'|'$)/g, '').replace(/''/g, \"'\");\n  });\n\n  return text;\n}\n\n/**\n * Create a new Date object with the given date value, and the time set to midnight.\n *\n * We cannot use `new Date(year, month, date)` because it maps years between 0 and 99 to 1900-1999.\n * See: https://github.com/angular/angular/issues/40377\n *\n * Note that this function returns a Date object whose time is midnight in the current locale's\n * timezone. In the future we might want to change this to be midnight in UTC, but this would be a\n * considerable breaking change.\n */\nfunction createDate(year: number, month: number, date: number): Date {\n  // The `newDate` is set to midnight (UTC) on January 1st 1970.\n  // - In PST this will be December 31st 1969 at 4pm.\n  // - In GMT this will be January 1st 1970 at 1am.\n  // Note that they even have different years, dates and months!\n  const newDate = new Date(0);\n\n  // `setFullYear()` allows years like 0001 to be set correctly. This function does not\n  // change the internal time of the date.\n  // Consider calling `setFullYear(2019, 8, 20)` (September 20, 2019).\n  // - In PST this will now be September 20, 2019 at 4pm\n  // - In GMT this will now be September 20, 2019 at 1am\n\n  newDate.setFullYear(year, month, date);\n  // We want the final date to be at local midnight, so we reset the time.\n  // - In PST this will now be September 20, 2019 at 12am\n  // - In GMT this will now be September 20, 2019 at 12am\n  newDate.setHours(0, 0, 0);\n\n  return newDate;\n}\n\nfunction getNamedFormat(locale: string, format: string): string {\n  const localeId = getLocaleId(locale);\n  NAMED_FORMATS[localeId] ??= {};\n\n  if (NAMED_FORMATS[localeId][format]) {\n    return NAMED_FORMATS[localeId][format];\n  }\n\n  let formatValue = '';\n  switch (format) {\n    case 'shortDate':\n      formatValue = getLocaleDateFormat(locale, FormatWidth.Short);\n      break;\n    case 'mediumDate':\n      formatValue = getLocaleDateFormat(locale, FormatWidth.Medium);\n      break;\n    case 'longDate':\n      formatValue = getLocaleDateFormat(locale, FormatWidth.Long);\n      break;\n    case 'fullDate':\n      formatValue = getLocaleDateFormat(locale, FormatWidth.Full);\n      break;\n    case 'shortTime':\n      formatValue = getLocaleTimeFormat(locale, FormatWidth.Short);\n      break;\n    case 'mediumTime':\n      formatValue = getLocaleTimeFormat(locale, FormatWidth.Medium);\n      break;\n    case 'longTime':\n      formatValue = getLocaleTimeFormat(locale, FormatWidth.Long);\n      break;\n    case 'fullTime':\n      formatValue = getLocaleTimeFormat(locale, FormatWidth.Full);\n      break;\n    case 'short':\n      const shortTime = getNamedFormat(locale, 'shortTime');\n      const shortDate = getNamedFormat(locale, 'shortDate');\n      formatValue = formatDateTime(getLocaleDateTimeFormat(locale, FormatWidth.Short), [\n        shortTime,\n        shortDate,\n      ]);\n      break;\n    case 'medium':\n      const mediumTime = getNamedFormat(locale, 'mediumTime');\n      const mediumDate = getNamedFormat(locale, 'mediumDate');\n      formatValue = formatDateTime(getLocaleDateTimeFormat(locale, FormatWidth.Medium), [\n        mediumTime,\n        mediumDate,\n      ]);\n      break;\n    case 'long':\n      const longTime = getNamedFormat(locale, 'longTime');\n      const longDate = getNamedFormat(locale, 'longDate');\n      formatValue = formatDateTime(getLocaleDateTimeFormat(locale, FormatWidth.Long), [\n        longTime,\n        longDate,\n      ]);\n      break;\n    case 'full':\n      const fullTime = getNamedFormat(locale, 'fullTime');\n      const fullDate = getNamedFormat(locale, 'fullDate');\n      formatValue = formatDateTime(getLocaleDateTimeFormat(locale, FormatWidth.Full), [\n        fullTime,\n        fullDate,\n      ]);\n      break;\n  }\n  if (formatValue) {\n    NAMED_FORMATS[localeId][format] = formatValue;\n  }\n  return formatValue;\n}\n\nfunction formatDateTime(str: string, opt_values: string[]) {\n  if (opt_values) {\n    str = str.replace(/\\{([^}]+)}/g, function (match, key) {\n      return opt_values != null && key in opt_values ? opt_values[key] : match;\n    });\n  }\n  return str;\n}\n\nfunction padNumber(\n  num: number,\n  digits: number,\n  minusSign = '-',\n  trim?: boolean,\n  negWrap?: boolean,\n): string {\n  let neg = '';\n  if (num < 0 || (negWrap && num <= 0)) {\n    if (negWrap) {\n      num = -num + 1;\n    } else {\n      num = -num;\n      neg = minusSign;\n    }\n  }\n  let strNum = String(num);\n  while (strNum.length < digits) {\n    strNum = '0' + strNum;\n  }\n  if (trim) {\n    strNum = strNum.slice(strNum.length - digits);\n  }\n  return neg + strNum;\n}\n\nfunction formatFractionalSeconds(milliseconds: number, digits: number): string {\n  const strMs = padNumber(milliseconds, 3);\n  return strMs.substring(0, digits);\n}\n\n/**\n * Returns a date formatter that transforms a date into its locale digit representation\n */\nfunction dateGetter(\n  name: DateType,\n  size: number,\n  offset: number = 0,\n  trim = false,\n  negWrap = false,\n): DateFormatter {\n  return function (date: Date, locale: string): string {\n    let part = getDatePart(name, date);\n    if (offset > 0 || part > -offset) {\n      part += offset;\n    }\n\n    if (name === DateType.Hours) {\n      if (part === 0 && offset === -12) {\n        part = 12;\n      }\n    } else if (name === DateType.FractionalSeconds) {\n      return formatFractionalSeconds(part, size);\n    }\n\n    const localeMinus = getLocaleNumberSymbol(locale, NumberSymbol.MinusSign);\n    return padNumber(part, size, localeMinus, trim, negWrap);\n  };\n}\n\nfunction getDatePart(part: DateType, date: Date): number {\n  switch (part) {\n    case DateType.FullYear:\n      return date.getFullYear();\n    case DateType.Month:\n      return date.getMonth();\n    case DateType.Date:\n      return date.getDate();\n    case DateType.Hours:\n      return date.getHours();\n    case DateType.Minutes:\n      return date.getMinutes();\n    case DateType.Seconds:\n      return date.getSeconds();\n    case DateType.FractionalSeconds:\n      return date.getMilliseconds();\n    case DateType.Day:\n      return date.getDay();\n    default:\n      throw new Error(`Unknown DateType value \"${part}\".`);\n  }\n}\n\n/**\n * Returns a date formatter that transforms a date into its locale string representation\n */\nfunction dateStrGetter(\n  name: TranslationType,\n  width: TranslationWidth,\n  form: FormStyle = FormStyle.Format,\n  extended = false,\n): DateFormatter {\n  return function (date: Date, locale: string): string {\n    return getDateTranslation(date, locale, name, width, form, extended);\n  };\n}\n\n/**\n * Returns the locale translation of a date for a given form, type and width\n */\nfunction getDateTranslation(\n  date: Date,\n  locale: string,\n  name: TranslationType,\n  width: TranslationWidth,\n  form: FormStyle,\n  extended: boolean,\n) {\n  switch (name) {\n    case TranslationType.Months:\n      return getLocaleMonthNames(locale, form, width)[date.getMonth()];\n    case TranslationType.Days:\n      return getLocaleDayNames(locale, form, width)[date.getDay()];\n    case TranslationType.DayPeriods:\n      const currentHours = date.getHours();\n      const currentMinutes = date.getMinutes();\n      if (extended) {\n        const rules = getLocaleExtraDayPeriodRules(locale);\n        const dayPeriods = getLocaleExtraDayPeriods(locale, form, width);\n        const index = rules.findIndex((rule) => {\n          if (Array.isArray(rule)) {\n            // morning, afternoon, evening, night\n            const [from, to] = rule;\n            const afterFrom = currentHours >= from.hours && currentMinutes >= from.minutes;\n            const beforeTo =\n              currentHours < to.hours || (currentHours === to.hours && currentMinutes < to.minutes);\n            // We must account for normal rules that span a period during the day (e.g. 6am-9am)\n            // where `from` is less (earlier) than `to`. But also rules that span midnight (e.g.\n            // 10pm - 5am) where `from` is greater (later!) than `to`.\n            //\n            // In the first case the current time must be BOTH after `from` AND before `to`\n            // (e.g. 8am is after 6am AND before 10am).\n            //\n            // In the second case the current time must be EITHER after `from` OR before `to`\n            // (e.g. 4am is before 5am but not after 10pm; and 11pm is not before 5am but it is\n            // after 10pm).\n            if (from.hours < to.hours) {\n              if (afterFrom && beforeTo) {\n                return true;\n              }\n            } else if (afterFrom || beforeTo) {\n              return true;\n            }\n          } else {\n            // noon or midnight\n            if (rule.hours === currentHours && rule.minutes === currentMinutes) {\n              return true;\n            }\n          }\n          return false;\n        });\n        if (index !== -1) {\n          return dayPeriods[index];\n        }\n      }\n      // if no rules for the day periods, we use am/pm by default\n      return getLocaleDayPeriods(locale, form, <TranslationWidth>width)[currentHours < 12 ? 0 : 1];\n    case TranslationType.Eras:\n      return getLocaleEraNames(locale, <TranslationWidth>width)[date.getFullYear() <= 0 ? 0 : 1];\n    default:\n      // This default case is not needed by TypeScript compiler, as the switch is exhaustive.\n      // However Closure Compiler does not understand that and reports an error in typed mode.\n      // The `throw new Error` below works around the problem, and the unexpected: never variable\n      // makes sure tsc still checks this code is unreachable.\n      const unexpected: never = name;\n      throw new Error(`unexpected translation type ${unexpected}`);\n  }\n}\n\n/**\n * Returns a date formatter that transforms a date and an offset into a timezone with ISO8601 or\n * GMT format depending on the width (eg: short = +0430, short:GMT = GMT+4, long = GMT+04:30,\n * extended = +04:30)\n */\nfunction timeZoneGetter(width: ZoneWidth): DateFormatter {\n  return function (date: Date, locale: string, offset: number) {\n    const zone = -1 * offset;\n    const minusSign = getLocaleNumberSymbol(locale, NumberSymbol.MinusSign);\n    const hours = zone > 0 ? Math.floor(zone / 60) : Math.ceil(zone / 60);\n    switch (width) {\n      case ZoneWidth.Short:\n        return (\n          (zone >= 0 ? '+' : '') +\n          padNumber(hours, 2, minusSign) +\n          padNumber(Math.abs(zone % 60), 2, minusSign)\n        );\n      case ZoneWidth.ShortGMT:\n        return 'GMT' + (zone >= 0 ? '+' : '') + padNumber(hours, 1, minusSign);\n      case ZoneWidth.Long:\n        return (\n          'GMT' +\n          (zone >= 0 ? '+' : '') +\n          padNumber(hours, 2, minusSign) +\n          ':' +\n          padNumber(Math.abs(zone % 60), 2, minusSign)\n        );\n      case ZoneWidth.Extended:\n        if (offset === 0) {\n          return 'Z';\n        } else {\n          return (\n            (zone >= 0 ? '+' : '') +\n            padNumber(hours, 2, minusSign) +\n            ':' +\n            padNumber(Math.abs(zone % 60), 2, minusSign)\n          );\n        }\n      default:\n        throw new Error(`Unknown zone width \"${width}\"`);\n    }\n  };\n}\n\nconst JANUARY = 0;\nconst THURSDAY = 4;\nfunction getFirstThursdayOfYear(year: number) {\n  const firstDayOfYear = createDate(year, JANUARY, 1).getDay();\n  return createDate(\n    year,\n    0,\n    1 + (firstDayOfYear <= THURSDAY ? THURSDAY : THURSDAY + 7) - firstDayOfYear,\n  );\n}\n\n/**\n *  ISO Week starts on day 1 (Monday) and ends with day 0 (Sunday)\n */\nexport function getThursdayThisIsoWeek(datetime: Date) {\n  // getDay returns 0-6 range with sunday as 0.\n  const currentDay = datetime.getDay();\n\n  // On a Sunday, read the previous Thursday since ISO weeks start on Monday.\n  const deltaToThursday = currentDay === 0 ? -3 : THURSDAY - currentDay;\n\n  return createDate(\n    datetime.getFullYear(),\n    datetime.getMonth(),\n    datetime.getDate() + deltaToThursday,\n  );\n}\n\nfunction weekGetter(size: number, monthBased = false): DateFormatter {\n  return function (date: Date, locale: string) {\n    let result;\n    if (monthBased) {\n      const nbDaysBefore1stDayOfMonth =\n        new Date(date.getFullYear(), date.getMonth(), 1).getDay() - 1;\n      const today = date.getDate();\n      result = 1 + Math.floor((today + nbDaysBefore1stDayOfMonth) / 7);\n    } else {\n      const thisThurs = getThursdayThisIsoWeek(date);\n      // Some days of a year are part of next year according to ISO 8601.\n      // Compute the firstThurs from the year of this week's Thursday\n      const firstThurs = getFirstThursdayOfYear(thisThurs.getFullYear());\n      const diff = thisThurs.getTime() - firstThurs.getTime();\n      result = 1 + Math.round(diff / 6.048e8); // 6.048e8 ms per week\n    }\n\n    return padNumber(result, size, getLocaleNumberSymbol(locale, NumberSymbol.MinusSign));\n  };\n}\n\n/**\n * Returns a date formatter that provides the week-numbering year for the input date.\n */\nfunction weekNumberingYearGetter(size: number, trim = false): DateFormatter {\n  return function (date: Date, locale: string) {\n    const thisThurs = getThursdayThisIsoWeek(date);\n    const weekNumberingYear = thisThurs.getFullYear();\n    return padNumber(\n      weekNumberingYear,\n      size,\n      getLocaleNumberSymbol(locale, NumberSymbol.MinusSign),\n      trim,\n    );\n  };\n}\n\ntype DateFormatter = (date: Date, locale: string, offset: number) => string;\n\nconst DATE_FORMATS: {[format: string]: DateFormatter} = {};\n\n// Based on CLDR formats:\n// See complete list: http://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n// See also explanations: http://cldr.unicode.org/translation/date-time\n// TODO(ocombe): support all missing cldr formats: U, Q, D, F, e, j, J, C, A, v, V, X, x\nfunction getDateFormatter(format: string): DateFormatter | null {\n  if (DATE_FORMATS[format]) {\n    return DATE_FORMATS[format];\n  }\n  let formatter;\n  switch (format) {\n    // Era name (AD/BC)\n    case 'G':\n    case 'GG':\n    case 'GGG':\n      formatter = dateStrGetter(TranslationType.Eras, TranslationWidth.Abbreviated);\n      break;\n    case 'GGGG':\n      formatter = dateStrGetter(TranslationType.Eras, TranslationWidth.Wide);\n      break;\n    case 'GGGGG':\n      formatter = dateStrGetter(TranslationType.Eras, TranslationWidth.Narrow);\n      break;\n\n    // 1 digit representation of the year, e.g. (AD 1 => 1, AD 199 => 199)\n    case 'y':\n      formatter = dateGetter(DateType.FullYear, 1, 0, false, true);\n      break;\n    // 2 digit representation of the year, padded (00-99). (e.g. AD 2001 => 01, AD 2010 => 10)\n    case 'yy':\n      formatter = dateGetter(DateType.FullYear, 2, 0, true, true);\n      break;\n    // 3 digit representation of the year, padded (000-999). (e.g. AD 2001 => 01, AD 2010 => 10)\n    case 'yyy':\n      formatter = dateGetter(DateType.FullYear, 3, 0, false, true);\n      break;\n    // 4 digit representation of the year (e.g. AD 1 => 0001, AD 2010 => 2010)\n    case 'yyyy':\n      formatter = dateGetter(DateType.FullYear, 4, 0, false, true);\n      break;\n\n    // 1 digit representation of the week-numbering year, e.g. (AD 1 => 1, AD 199 => 199)\n    case 'Y':\n      formatter = weekNumberingYearGetter(1);\n      break;\n    // 2 digit representation of the week-numbering year, padded (00-99). (e.g. AD 2001 => 01, AD\n    // 2010 => 10)\n    case 'YY':\n      formatter = weekNumberingYearGetter(2, true);\n      break;\n    // 3 digit representation of the week-numbering year, padded (000-999). (e.g. AD 1 => 001, AD\n    // 2010 => 2010)\n    case 'YYY':\n      formatter = weekNumberingYearGetter(3);\n      break;\n    // 4 digit representation of the week-numbering year (e.g. AD 1 => 0001, AD 2010 => 2010)\n    case 'YYYY':\n      formatter = weekNumberingYearGetter(4);\n      break;\n\n    // Month of the year (1-12), numeric\n    case 'M':\n    case 'L':\n      formatter = dateGetter(DateType.Month, 1, 1);\n      break;\n    case 'MM':\n    case 'LL':\n      formatter = dateGetter(DateType.Month, 2, 1);\n      break;\n\n    // Month of the year (January, ...), string, format\n    case 'MMM':\n      formatter = dateStrGetter(TranslationType.Months, TranslationWidth.Abbreviated);\n      break;\n    case 'MMMM':\n      formatter = dateStrGetter(TranslationType.Months, TranslationWidth.Wide);\n      break;\n    case 'MMMMM':\n      formatter = dateStrGetter(TranslationType.Months, TranslationWidth.Narrow);\n      break;\n\n    // Month of the year (January, ...), string, standalone\n    case 'LLL':\n      formatter = dateStrGetter(\n        TranslationType.Months,\n        TranslationWidth.Abbreviated,\n        FormStyle.Standalone,\n      );\n      break;\n    case 'LLLL':\n      formatter = dateStrGetter(\n        TranslationType.Months,\n        TranslationWidth.Wide,\n        FormStyle.Standalone,\n      );\n      break;\n    case 'LLLLL':\n      formatter = dateStrGetter(\n        TranslationType.Months,\n        TranslationWidth.Narrow,\n        FormStyle.Standalone,\n      );\n      break;\n\n    // Week of the year (1, ... 52)\n    case 'w':\n      formatter = weekGetter(1);\n      break;\n    case 'ww':\n      formatter = weekGetter(2);\n      break;\n\n    // Week of the month (1, ...)\n    case 'W':\n      formatter = weekGetter(1, true);\n      break;\n\n    // Day of the month (1-31)\n    case 'd':\n      formatter = dateGetter(DateType.Date, 1);\n      break;\n    case 'dd':\n      formatter = dateGetter(DateType.Date, 2);\n      break;\n\n    // Day of the Week StandAlone (1, 1, Mon, Monday, M, Mo)\n    case 'c':\n    case 'cc':\n      formatter = dateGetter(DateType.Day, 1);\n      break;\n    case 'ccc':\n      formatter = dateStrGetter(\n        TranslationType.Days,\n        TranslationWidth.Abbreviated,\n        FormStyle.Standalone,\n      );\n      break;\n    case 'cccc':\n      formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Wide, FormStyle.Standalone);\n      break;\n    case 'ccccc':\n      formatter = dateStrGetter(\n        TranslationType.Days,\n        TranslationWidth.Narrow,\n        FormStyle.Standalone,\n      );\n      break;\n    case 'cccccc':\n      formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Short, FormStyle.Standalone);\n      break;\n\n    // Day of the Week\n    case 'E':\n    case 'EE':\n    case 'EEE':\n      formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Abbreviated);\n      break;\n    case 'EEEE':\n      formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Wide);\n      break;\n    case 'EEEEE':\n      formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Narrow);\n      break;\n    case 'EEEEEE':\n      formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Short);\n      break;\n\n    // Generic period of the day (am-pm)\n    case 'a':\n    case 'aa':\n    case 'aaa':\n      formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Abbreviated);\n      break;\n    case 'aaaa':\n      formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Wide);\n      break;\n    case 'aaaaa':\n      formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Narrow);\n      break;\n\n    // Extended period of the day (midnight, at night, ...), standalone\n    case 'b':\n    case 'bb':\n    case 'bbb':\n      formatter = dateStrGetter(\n        TranslationType.DayPeriods,\n        TranslationWidth.Abbreviated,\n        FormStyle.Standalone,\n        true,\n      );\n      break;\n    case 'bbbb':\n      formatter = dateStrGetter(\n        TranslationType.DayPeriods,\n        TranslationWidth.Wide,\n        FormStyle.Standalone,\n        true,\n      );\n      break;\n    case 'bbbbb':\n      formatter = dateStrGetter(\n        TranslationType.DayPeriods,\n        TranslationWidth.Narrow,\n        FormStyle.Standalone,\n        true,\n      );\n      break;\n\n    // Extended period of the day (midnight, night, ...), standalone\n    case 'B':\n    case 'BB':\n    case 'BBB':\n      formatter = dateStrGetter(\n        TranslationType.DayPeriods,\n        TranslationWidth.Abbreviated,\n        FormStyle.Format,\n        true,\n      );\n      break;\n    case 'BBBB':\n      formatter = dateStrGetter(\n        TranslationType.DayPeriods,\n        TranslationWidth.Wide,\n        FormStyle.Format,\n        true,\n      );\n      break;\n    case 'BBBBB':\n      formatter = dateStrGetter(\n        TranslationType.DayPeriods,\n        TranslationWidth.Narrow,\n        FormStyle.Format,\n        true,\n      );\n      break;\n\n    // Hour in AM/PM, (1-12)\n    case 'h':\n      formatter = dateGetter(DateType.Hours, 1, -12);\n      break;\n    case 'hh':\n      formatter = dateGetter(DateType.Hours, 2, -12);\n      break;\n\n    // Hour of the day (0-23)\n    case 'H':\n      formatter = dateGetter(DateType.Hours, 1);\n      break;\n    // Hour in day, padded (00-23)\n    case 'HH':\n      formatter = dateGetter(DateType.Hours, 2);\n      break;\n\n    // Minute of the hour (0-59)\n    case 'm':\n      formatter = dateGetter(DateType.Minutes, 1);\n      break;\n    case 'mm':\n      formatter = dateGetter(DateType.Minutes, 2);\n      break;\n\n    // Second of the minute (0-59)\n    case 's':\n      formatter = dateGetter(DateType.Seconds, 1);\n      break;\n    case 'ss':\n      formatter = dateGetter(DateType.Seconds, 2);\n      break;\n\n    // Fractional second\n    case 'S':\n      formatter = dateGetter(DateType.FractionalSeconds, 1);\n      break;\n    case 'SS':\n      formatter = dateGetter(DateType.FractionalSeconds, 2);\n      break;\n    case 'SSS':\n      formatter = dateGetter(DateType.FractionalSeconds, 3);\n      break;\n\n    // Timezone ISO8601 short format (-0430)\n    case 'Z':\n    case 'ZZ':\n    case 'ZZZ':\n      formatter = timeZoneGetter(ZoneWidth.Short);\n      break;\n    // Timezone ISO8601 extended format (-04:30)\n    case 'ZZZZZ':\n      formatter = timeZoneGetter(ZoneWidth.Extended);\n      break;\n\n    // Timezone GMT short format (GMT+4)\n    case 'O':\n    case 'OO':\n    case 'OOO':\n    // Should be location, but fallback to format O instead because we don't have the data yet\n    case 'z':\n    case 'zz':\n    case 'zzz':\n      formatter = timeZoneGetter(ZoneWidth.ShortGMT);\n      break;\n    // Timezone GMT long format (GMT+0430)\n    case 'OOOO':\n    case 'ZZZZ':\n    // Should be location, but fallback to format O instead because we don't have the data yet\n    case 'zzzz':\n      formatter = timeZoneGetter(ZoneWidth.Long);\n      break;\n    default:\n      return null;\n  }\n  DATE_FORMATS[format] = formatter;\n  return formatter;\n}\n\nfunction timezoneToOffset(timezone: string, fallback: number): number {\n  // Support: IE 11 only, Edge 13-15+\n  // IE/Edge do not \"understand\" colon (`:`) in timezone\n  timezone = timezone.replace(/:/g, '');\n  const requestedTimezoneOffset = Date.parse('Jan 01, 1970 00:00:00 ' + timezone) / 60000;\n  return isNaN(requestedTimezoneOffset) ? fallback : requestedTimezoneOffset;\n}\n\nfunction addDateMinutes(date: Date, minutes: number) {\n  date = new Date(date.getTime());\n  date.setMinutes(date.getMinutes() + minutes);\n  return date;\n}\n\nfunction convertTimezoneToLocal(date: Date, timezone: string, reverse: boolean): Date {\n  const reverseValue = reverse ? -1 : 1;\n  const dateTimezoneOffset = date.getTimezoneOffset();\n  const timezoneOffset = timezoneToOffset(timezone, dateTimezoneOffset);\n  return addDateMinutes(date, reverseValue * (timezoneOffset - dateTimezoneOffset));\n}\n\n/**\n * Converts a value to date.\n *\n * Supported input formats:\n * - `Date`\n * - number: timestamp\n * - string: numeric (e.g. \"1234\"), ISO and date strings in a format supported by\n *   [Date.parse()](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date/parse).\n *   Note: ISO strings without time return a date without timeoffset.\n *\n * Throws if unable to convert to a date.\n */\nexport function toDate(value: string | number | Date): Date {\n  if (isDate(value)) {\n    return value;\n  }\n\n  if (typeof value === 'number' && !isNaN(value)) {\n    return new Date(value);\n  }\n\n  if (typeof value === 'string') {\n    value = value.trim();\n\n    if (/^(\\d{4}(-\\d{1,2}(-\\d{1,2})?)?)$/.test(value)) {\n      /* For ISO Strings without time the day, month and year must be extracted from the ISO String\n      before Date creation to avoid time offset and errors in the new Date.\n      If we only replace '-' with ',' in the ISO String (\"2015,01,01\"), and try to create a new\n      date, some browsers (e.g. IE 9) will throw an invalid Date error.\n      If we leave the '-' (\"2015-01-01\") and try to create a new Date(\"2015-01-01\") the timeoffset\n      is applied.\n      Note: ISO months are 0 for January, 1 for February, ... */\n      const [y, m = 1, d = 1] = value.split('-').map((val: string) => +val);\n      return createDate(y, m - 1, d);\n    }\n\n    const parsedNb = parseFloat(value);\n\n    // any string that only contains numbers, like \"1234\" but not like \"1234hello\"\n    if (!isNaN((value as any) - parsedNb)) {\n      return new Date(parsedNb);\n    }\n\n    let match: RegExpMatchArray | null;\n    if ((match = value.match(ISO8601_DATE_REGEX))) {\n      return isoStringToDate(match);\n    }\n  }\n\n  const date = new Date(value as any);\n  if (!isDate(date)) {\n    throw new Error(`Unable to convert \"${value}\" into a date`);\n  }\n  return date;\n}\n\n/**\n * Converts a date in ISO8601 to a Date.\n * Used instead of `Date.parse` because of browser discrepancies.\n */\nexport function isoStringToDate(match: RegExpMatchArray): Date {\n  const date = new Date(0);\n  let tzHour = 0;\n  let tzMin = 0;\n\n  // match[8] means that the string contains \"Z\" (UTC) or a timezone like \"+01:00\" or \"+0100\"\n  const dateSetter = match[8] ? date.setUTCFullYear : date.setFullYear;\n  const timeSetter = match[8] ? date.setUTCHours : date.setHours;\n\n  // if there is a timezone defined like \"+01:00\" or \"+0100\"\n  if (match[9]) {\n    tzHour = Number(match[9] + match[10]);\n    tzMin = Number(match[9] + match[11]);\n  }\n  dateSetter.call(date, Number(match[1]), Number(match[2]) - 1, Number(match[3]));\n  const h = Number(match[4] || 0) - tzHour;\n  const m = Number(match[5] || 0) - tzMin;\n  const s = Number(match[6] || 0);\n  // The ECMAScript specification (https://www.ecma-international.org/ecma-262/5.1/#sec-*********)\n  // defines that `DateTime` milliseconds should always be rounded down, so that `999.9ms`\n  // becomes `999ms`.\n  const ms = Math.floor(parseFloat('0.' + (match[7] || 0)) * 1000);\n  timeSetter.call(date, h, m, s, ms);\n  return date;\n}\n\nexport function isDate(value: any): value is Date {\n  return value instanceof Date && !isNaN(value.valueOf());\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  getLocaleNumberFormat,\n  getLocaleNumberSymbol,\n  getNumberOfCurrencyDigits,\n  NumberFormatStyle,\n  NumberSymbol,\n} from './locale_data_api';\n\nexport const NUMBER_FORMAT_REGEXP = /^(\\d+)?\\.((\\d+)(-(\\d+))?)?$/;\nconst MAX_DIGITS = 22;\nconst DECIMAL_SEP = '.';\nconst ZERO_CHAR = '0';\nconst PATTERN_SEP = ';';\nconst GROUP_SEP = ',';\nconst DIGIT_CHAR = '#';\nconst CURRENCY_CHAR = '¤';\nconst PERCENT_CHAR = '%';\n\n/**\n * Transforms a number to a locale string based on a style and a format.\n */\nfunction formatNumberToLocaleString(\n  value: number,\n  pattern: ParsedNumberFormat,\n  locale: string,\n  groupSymbol: NumberSymbol,\n  decimalSymbol: NumberSymbol,\n  digitsInfo?: string,\n  isPercent = false,\n): string {\n  let formattedText = '';\n  let isZero = false;\n\n  if (!isFinite(value)) {\n    formattedText = getLocaleNumberSymbol(locale, NumberSymbol.Infinity);\n  } else {\n    let parsedNumber = parseNumber(value);\n\n    if (isPercent) {\n      parsedNumber = toPercent(parsedNumber);\n    }\n\n    let minInt = pattern.minInt;\n    let minFraction = pattern.minFrac;\n    let maxFraction = pattern.maxFrac;\n\n    if (digitsInfo) {\n      const parts = digitsInfo.match(NUMBER_FORMAT_REGEXP);\n      if (parts === null) {\n        throw new Error(`${digitsInfo} is not a valid digit info`);\n      }\n      const minIntPart = parts[1];\n      const minFractionPart = parts[3];\n      const maxFractionPart = parts[5];\n      if (minIntPart != null) {\n        minInt = parseIntAutoRadix(minIntPart);\n      }\n      if (minFractionPart != null) {\n        minFraction = parseIntAutoRadix(minFractionPart);\n      }\n      if (maxFractionPart != null) {\n        maxFraction = parseIntAutoRadix(maxFractionPart);\n      } else if (minFractionPart != null && minFraction > maxFraction) {\n        maxFraction = minFraction;\n      }\n    }\n\n    roundNumber(parsedNumber, minFraction, maxFraction);\n\n    let digits = parsedNumber.digits;\n    let integerLen = parsedNumber.integerLen;\n    const exponent = parsedNumber.exponent;\n    let decimals = [];\n    isZero = digits.every((d) => !d);\n\n    // pad zeros for small numbers\n    for (; integerLen < minInt; integerLen++) {\n      digits.unshift(0);\n    }\n\n    // pad zeros for small numbers\n    for (; integerLen < 0; integerLen++) {\n      digits.unshift(0);\n    }\n\n    // extract decimals digits\n    if (integerLen > 0) {\n      decimals = digits.splice(integerLen, digits.length);\n    } else {\n      decimals = digits;\n      digits = [0];\n    }\n\n    // format the integer digits with grouping separators\n    const groups = [];\n    if (digits.length >= pattern.lgSize) {\n      groups.unshift(digits.splice(-pattern.lgSize, digits.length).join(''));\n    }\n\n    while (digits.length > pattern.gSize) {\n      groups.unshift(digits.splice(-pattern.gSize, digits.length).join(''));\n    }\n\n    if (digits.length) {\n      groups.unshift(digits.join(''));\n    }\n\n    formattedText = groups.join(getLocaleNumberSymbol(locale, groupSymbol));\n\n    // append the decimal digits\n    if (decimals.length) {\n      formattedText += getLocaleNumberSymbol(locale, decimalSymbol) + decimals.join('');\n    }\n\n    if (exponent) {\n      formattedText += getLocaleNumberSymbol(locale, NumberSymbol.Exponential) + '+' + exponent;\n    }\n  }\n\n  if (value < 0 && !isZero) {\n    formattedText = pattern.negPre + formattedText + pattern.negSuf;\n  } else {\n    formattedText = pattern.posPre + formattedText + pattern.posSuf;\n  }\n\n  return formattedText;\n}\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Formats a number as currency using locale rules.\n *\n * @param value The number to format.\n * @param locale A locale code for the locale format rules to use.\n * @param currency A string containing the currency symbol or its name,\n * such as \"$\" or \"Canadian Dollar\". Used in output string, but does not affect the operation\n * of the function.\n * @param currencyCode The [ISO 4217](https://en.wikipedia.org/wiki/ISO_4217)\n * currency code, such as `USD` for the US dollar and `EUR` for the euro.\n * Used to determine the number of digits in the decimal part.\n * @param digitsInfo Decimal representation options, specified by a string in the following format:\n * `{minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}`. See `DecimalPipe` for more details.\n *\n * @returns The formatted currency value.\n *\n * @see {@link formatNumber}\n * @see {@link DecimalPipe}\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nexport function formatCurrency(\n  value: number,\n  locale: string,\n  currency: string,\n  currencyCode?: string,\n  digitsInfo?: string,\n): string {\n  const format = getLocaleNumberFormat(locale, NumberFormatStyle.Currency);\n  const pattern = parseNumberFormat(format, getLocaleNumberSymbol(locale, NumberSymbol.MinusSign));\n\n  pattern.minFrac = getNumberOfCurrencyDigits(currencyCode!);\n  pattern.maxFrac = pattern.minFrac;\n\n  const res = formatNumberToLocaleString(\n    value,\n    pattern,\n    locale,\n    NumberSymbol.CurrencyGroup,\n    NumberSymbol.CurrencyDecimal,\n    digitsInfo,\n  );\n  return (\n    res\n      .replace(CURRENCY_CHAR, currency)\n      // if we have 2 time the currency character, the second one is ignored\n      .replace(CURRENCY_CHAR, '')\n      // If there is a spacing between currency character and the value and\n      // the currency character is suppressed by passing an empty string, the\n      // spacing character would remain as part of the string. Then we\n      // should remove it.\n      .trim()\n  );\n}\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Formats a number as a percentage according to locale rules.\n *\n * @param value The number to format.\n * @param locale A locale code for the locale format rules to use.\n * @param digitsInfo Decimal representation options, specified by a string in the following format:\n * `{minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}`. See `DecimalPipe` for more details.\n *\n * @returns The formatted percentage value.\n *\n * @see {@link formatNumber}\n * @see {@link DecimalPipe}\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n * @publicApi\n *\n */\nexport function formatPercent(value: number, locale: string, digitsInfo?: string): string {\n  const format = getLocaleNumberFormat(locale, NumberFormatStyle.Percent);\n  const pattern = parseNumberFormat(format, getLocaleNumberSymbol(locale, NumberSymbol.MinusSign));\n  const res = formatNumberToLocaleString(\n    value,\n    pattern,\n    locale,\n    NumberSymbol.Group,\n    NumberSymbol.Decimal,\n    digitsInfo,\n    true,\n  );\n  return res.replace(\n    new RegExp(PERCENT_CHAR, 'g'),\n    getLocaleNumberSymbol(locale, NumberSymbol.PercentSign),\n  );\n}\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Formats a number as text, with group sizing, separator, and other\n * parameters based on the locale.\n *\n * @param value The number to format.\n * @param locale A locale code for the locale format rules to use.\n * @param digitsInfo Decimal representation options, specified by a string in the following format:\n * `{minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}`. See `DecimalPipe` for more details.\n *\n * @returns The formatted text string.\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nexport function formatNumber(value: number, locale: string, digitsInfo?: string): string {\n  const format = getLocaleNumberFormat(locale, NumberFormatStyle.Decimal);\n  const pattern = parseNumberFormat(format, getLocaleNumberSymbol(locale, NumberSymbol.MinusSign));\n  return formatNumberToLocaleString(\n    value,\n    pattern,\n    locale,\n    NumberSymbol.Group,\n    NumberSymbol.Decimal,\n    digitsInfo,\n  );\n}\n\ninterface ParsedNumberFormat {\n  minInt: number;\n  // the minimum number of digits required in the fraction part of the number\n  minFrac: number;\n  // the maximum number of digits required in the fraction part of the number\n  maxFrac: number;\n  // the prefix for a positive number\n  posPre: string;\n  // the suffix for a positive number\n  posSuf: string;\n  // the prefix for a negative number (e.g. `-` or `(`))\n  negPre: string;\n  // the suffix for a negative number (e.g. `)`)\n  negSuf: string;\n  // number of digits in each group of separated digits\n  gSize: number;\n  // number of digits in the last group of digits before the decimal separator\n  lgSize: number;\n}\n\nfunction parseNumberFormat(format: string, minusSign = '-'): ParsedNumberFormat {\n  const p = {\n    minInt: 1,\n    minFrac: 0,\n    maxFrac: 0,\n    posPre: '',\n    posSuf: '',\n    negPre: '',\n    negSuf: '',\n    gSize: 0,\n    lgSize: 0,\n  };\n\n  const patternParts = format.split(PATTERN_SEP);\n  const positive = patternParts[0];\n  const negative = patternParts[1];\n\n  const positiveParts =\n      positive.indexOf(DECIMAL_SEP) !== -1\n        ? positive.split(DECIMAL_SEP)\n        : [\n            positive.substring(0, positive.lastIndexOf(ZERO_CHAR) + 1),\n            positive.substring(positive.lastIndexOf(ZERO_CHAR) + 1),\n          ],\n    integer = positiveParts[0],\n    fraction = positiveParts[1] || '';\n\n  p.posPre = integer.substring(0, integer.indexOf(DIGIT_CHAR));\n\n  for (let i = 0; i < fraction.length; i++) {\n    const ch = fraction.charAt(i);\n    if (ch === ZERO_CHAR) {\n      p.minFrac = p.maxFrac = i + 1;\n    } else if (ch === DIGIT_CHAR) {\n      p.maxFrac = i + 1;\n    } else {\n      p.posSuf += ch;\n    }\n  }\n\n  const groups = integer.split(GROUP_SEP);\n  p.gSize = groups[1] ? groups[1].length : 0;\n  p.lgSize = groups[2] || groups[1] ? (groups[2] || groups[1]).length : 0;\n\n  if (negative) {\n    const trunkLen = positive.length - p.posPre.length - p.posSuf.length,\n      pos = negative.indexOf(DIGIT_CHAR);\n\n    p.negPre = negative.substring(0, pos).replace(/'/g, '');\n    p.negSuf = negative.slice(pos + trunkLen).replace(/'/g, '');\n  } else {\n    p.negPre = minusSign + p.posPre;\n    p.negSuf = p.posSuf;\n  }\n\n  return p;\n}\n\ninterface ParsedNumber {\n  // an array of digits containing leading zeros as necessary\n  digits: number[];\n  // the exponent for numbers that would need more than `MAX_DIGITS` digits in `d`\n  exponent: number;\n  // the number of the digits in `d` that are to the left of the decimal point\n  integerLen: number;\n}\n\n// Transforms a parsed number into a percentage by multiplying it by 100\nfunction toPercent(parsedNumber: ParsedNumber): ParsedNumber {\n  // if the number is 0, don't do anything\n  if (parsedNumber.digits[0] === 0) {\n    return parsedNumber;\n  }\n\n  // Getting the current number of decimals\n  const fractionLen = parsedNumber.digits.length - parsedNumber.integerLen;\n  if (parsedNumber.exponent) {\n    parsedNumber.exponent += 2;\n  } else {\n    if (fractionLen === 0) {\n      parsedNumber.digits.push(0, 0);\n    } else if (fractionLen === 1) {\n      parsedNumber.digits.push(0);\n    }\n    parsedNumber.integerLen += 2;\n  }\n\n  return parsedNumber;\n}\n\n/**\n * Parses a number.\n * Significant bits of this parse algorithm came from https://github.com/MikeMcl/big.js/\n */\nfunction parseNumber(num: number): ParsedNumber {\n  let numStr = Math.abs(num) + '';\n  let exponent = 0,\n    digits,\n    integerLen;\n  let i, j, zeros;\n\n  // Decimal point?\n  if ((integerLen = numStr.indexOf(DECIMAL_SEP)) > -1) {\n    numStr = numStr.replace(DECIMAL_SEP, '');\n  }\n\n  // Exponential form?\n  if ((i = numStr.search(/e/i)) > 0) {\n    // Work out the exponent.\n    if (integerLen < 0) integerLen = i;\n    integerLen += +numStr.slice(i + 1);\n    numStr = numStr.substring(0, i);\n  } else if (integerLen < 0) {\n    // There was no decimal point or exponent so it is an integer.\n    integerLen = numStr.length;\n  }\n\n  // Count the number of leading zeros.\n  for (i = 0; numStr.charAt(i) === ZERO_CHAR; i++) {\n    /* empty */\n  }\n\n  if (i === (zeros = numStr.length)) {\n    // The digits are all zero.\n    digits = [0];\n    integerLen = 1;\n  } else {\n    // Count the number of trailing zeros\n    zeros--;\n    while (numStr.charAt(zeros) === ZERO_CHAR) zeros--;\n\n    // Trailing zeros are insignificant so ignore them\n    integerLen -= i;\n    digits = [];\n    // Convert string to array of digits without leading/trailing zeros.\n    for (j = 0; i <= zeros; i++, j++) {\n      digits[j] = Number(numStr.charAt(i));\n    }\n  }\n\n  // If the number overflows the maximum allowed digits then use an exponent.\n  if (integerLen > MAX_DIGITS) {\n    digits = digits.splice(0, MAX_DIGITS - 1);\n    exponent = integerLen - 1;\n    integerLen = 1;\n  }\n\n  return {digits, exponent, integerLen};\n}\n\n/**\n * Round the parsed number to the specified number of decimal places\n * This function changes the parsedNumber in-place\n */\nfunction roundNumber(parsedNumber: ParsedNumber, minFrac: number, maxFrac: number) {\n  if (minFrac > maxFrac) {\n    throw new Error(\n      `The minimum number of digits after fraction (${minFrac}) is higher than the maximum (${maxFrac}).`,\n    );\n  }\n\n  let digits = parsedNumber.digits;\n  let fractionLen = digits.length - parsedNumber.integerLen;\n  const fractionSize = Math.min(Math.max(minFrac, fractionLen), maxFrac);\n\n  // The index of the digit to where rounding is to occur\n  let roundAt = fractionSize + parsedNumber.integerLen;\n  let digit = digits[roundAt];\n\n  if (roundAt > 0) {\n    // Drop fractional digits beyond `roundAt`\n    digits.splice(Math.max(parsedNumber.integerLen, roundAt));\n\n    // Set non-fractional digits beyond `roundAt` to 0\n    for (let j = roundAt; j < digits.length; j++) {\n      digits[j] = 0;\n    }\n  } else {\n    // We rounded to zero so reset the parsedNumber\n    fractionLen = Math.max(0, fractionLen);\n    parsedNumber.integerLen = 1;\n    digits.length = Math.max(1, (roundAt = fractionSize + 1));\n    digits[0] = 0;\n    for (let i = 1; i < roundAt; i++) digits[i] = 0;\n  }\n\n  if (digit >= 5) {\n    if (roundAt - 1 < 0) {\n      for (let k = 0; k > roundAt; k--) {\n        digits.unshift(0);\n        parsedNumber.integerLen++;\n      }\n      digits.unshift(1);\n      parsedNumber.integerLen++;\n    } else {\n      digits[roundAt - 1]++;\n    }\n  }\n\n  // Pad out with zeros to get the required fraction length\n  for (; fractionLen < Math.max(0, fractionSize); fractionLen++) digits.push(0);\n\n  let dropTrailingZeros = fractionSize !== 0;\n  // Minimal length = nb of decimals required + current nb of integers\n  // Any number besides that is optional and can be removed if it's a trailing 0\n  const minLen = minFrac + parsedNumber.integerLen;\n  // Do any carrying, e.g. a digit was rounded up to 10\n  const carry = digits.reduceRight(function (carry, d, i, digits) {\n    d = d + carry;\n    digits[i] = d < 10 ? d : d - 10; // d % 10\n    if (dropTrailingZeros) {\n      // Do not keep meaningless fractional trailing zeros (e.g. 15.52000 --> 15.52)\n      if (digits[i] === 0 && i >= minLen) {\n        digits.pop();\n      } else {\n        dropTrailingZeros = false;\n      }\n    }\n    return d >= 10 ? 1 : 0; // Math.floor(d / 10);\n  }, 0);\n  if (carry) {\n    digits.unshift(carry);\n    parsedNumber.integerLen++;\n  }\n}\n\nexport function parseIntAutoRadix(text: string): number {\n  const result: number = parseInt(text);\n  if (isNaN(result)) {\n    throw new Error('Invalid integer literal when parsing ' + text);\n  }\n  return result;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Inject, Injectable, LOCALE_ID} from '@angular/core';\n\nimport {getLocalePluralCase, Plural} from './locale_data_api';\n\n/**\n * @publicApi\n */\n@Injectable({\n  providedIn: 'root',\n  useFactory: (locale: string) => new NgLocaleLocalization(locale),\n  deps: [LOCALE_ID],\n})\nexport abstract class NgLocalization {\n  abstract getPluralCategory(value: any, locale?: string): string;\n}\n\n/**\n * Returns the plural category for a given value.\n * - \"=value\" when the case exists,\n * - the plural category otherwise\n */\nexport function getPluralCategory(\n  value: number,\n  cases: string[],\n  ngLocalization: NgLocalization,\n  locale?: string,\n): string {\n  let key = `=${value}`;\n\n  if (cases.indexOf(key) > -1) {\n    return key;\n  }\n\n  key = ngLocalization.getPluralCategory(value, locale);\n\n  if (cases.indexOf(key) > -1) {\n    return key;\n  }\n\n  if (cases.indexOf('other') > -1) {\n    return 'other';\n  }\n\n  throw new Error(`No plural message found for value \"${value}\"`);\n}\n\n/**\n * Returns the plural case based on the locale\n *\n * @publicApi\n */\n@Injectable()\nexport class NgLocaleLocalization extends NgLocalization {\n  constructor(@Inject(LOCALE_ID) protected locale: string) {\n    super();\n  }\n\n  override getPluralCategory(value: any, locale?: string): string {\n    const plural = getLocalePluralCase(locale || this.locale)(value);\n\n    switch (plural) {\n      case Plural.Zero:\n        return 'zero';\n      case Plural.One:\n        return 'one';\n      case Plural.Two:\n        return 'two';\n      case Plural.Few:\n        return 'few';\n      case Plural.Many:\n        return 'many';\n      default:\n        return 'other';\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵregisterLocaleData} from '@angular/core';\n\n/**\n * Register global data to be used internally by Angular. See the\n * [\"I18n guide\"](guide/i18n-common-format-data-locale) to know how to import additional locale\n * data.\n *\n * The signature registerLocaleData(data: any, extraData?: any) is deprecated since v5.1\n *\n * @publicApi\n */\nexport function registerLocaleData(data: any, localeId?: string | any, extraData?: any): void {\n  return ɵregisterLocaleData(data, localeId, extraData);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport function parseCookieValue(cookieStr: string, name: string): string | null {\n  name = encodeURIComponent(name);\n  for (const cookie of cookieStr.split(';')) {\n    const eqIndex = cookie.indexOf('=');\n    const [cookieName, cookieValue]: string[] =\n      eqIndex == -1 ? [cookie, ''] : [cookie.slice(0, eqIndex), cookie.slice(eqIndex + 1)];\n    if (cookieName.trim() === name) {\n      return decodeURIComponent(cookieValue);\n    }\n  }\n  return null;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {\n  Directive,\n  Do<PERSON>heck,\n  ElementRef,\n  Input,\n  IterableDiffers,\n  KeyValueDiffers,\n  Renderer2,\n  ɵstringify as stringify,\n} from '@angular/core';\n\ntype NgClassSupportedTypes = string[] | Set<string> | {[klass: string]: any} | null | undefined;\n\nconst WS_REGEXP = /\\s+/;\n\nconst EMPTY_ARRAY: string[] = [];\n\n/**\n * Represents internal object used to track state of each CSS class. There are 3 different (boolean)\n * flags that, combined together, indicate state of a given CSS class:\n * - enabled: indicates if a class should be present in the DOM (true) or not (false);\n * - changed: tracks if a class was toggled (added or removed) during the custom dirty-checking\n * process; changed classes must be synchronized with the DOM;\n * - touched: tracks if a class is present in the current object bound to the class / ngClass input;\n * classes that are not present any more can be removed from the internal data structures;\n */\ninterface CssClassState {\n  // PERF: could use a bit mask to represent state as all fields are boolean flags\n  enabled: boolean;\n  changed: boolean;\n  touched: boolean;\n}\n\n/**\n * @ngModule CommonModule\n *\n * @usageNotes\n * ```\n *     <some-element [ngClass]=\"'first second'\">...</some-element>\n *\n *     <some-element [ngClass]=\"['first', 'second']\">...</some-element>\n *\n *     <some-element [ngClass]=\"{'first': true, 'second': true, 'third': false}\">...</some-element>\n *\n *     <some-element [ngClass]=\"stringExp|arrayExp|objExp\">...</some-element>\n *\n *     <some-element [ngClass]=\"{'class1 class2 class3' : true}\">...</some-element>\n * ```\n *\n * @description\n *\n * Adds and removes CSS classes on an HTML element.\n *\n * The CSS classes are updated as follows, depending on the type of the expression evaluation:\n * - `string` - the CSS classes listed in the string (space delimited) are added,\n * - `Array` - the CSS classes declared as Array elements are added,\n * - `Object` - keys are CSS classes that get added when the expression given in the value\n *              evaluates to a truthy value, otherwise they are removed.\n *\n * @publicApi\n */\n@Directive({\n  selector: '[ngClass]',\n  standalone: true,\n})\nexport class NgClass implements DoCheck {\n  private initialClasses = EMPTY_ARRAY;\n  private rawClass: NgClassSupportedTypes;\n\n  private stateMap = new Map<string, CssClassState>();\n\n  constructor(\n    private _ngEl: ElementRef,\n    private _renderer: Renderer2,\n  ) {}\n\n  @Input('class')\n  set klass(value: string) {\n    this.initialClasses = value != null ? value.trim().split(WS_REGEXP) : EMPTY_ARRAY;\n  }\n\n  @Input('ngClass')\n  set ngClass(value: string | string[] | Set<string> | {[klass: string]: any} | null | undefined) {\n    this.rawClass = typeof value === 'string' ? value.trim().split(WS_REGEXP) : value;\n  }\n\n  /*\n  The NgClass directive uses the custom change detection algorithm for its inputs. The custom\n  algorithm is necessary since inputs are represented as complex object or arrays that need to be\n  deeply-compared.\n\n  This algorithm is perf-sensitive since NgClass is used very frequently and its poor performance\n  might negatively impact runtime performance of the entire change detection cycle. The design of\n  this algorithm is making sure that:\n  - there is no unnecessary DOM manipulation (CSS classes are added / removed from the DOM only when\n  needed), even if references to bound objects change;\n  - there is no memory allocation if nothing changes (even relatively modest memory allocation\n  during the change detection cycle can result in GC pauses for some of the CD cycles).\n\n  The algorithm works by iterating over the set of bound classes, staring with [class] binding and\n  then going over [ngClass] binding. For each CSS class name:\n  - check if it was seen before (this information is tracked in the state map) and if its value\n  changed;\n  - mark it as \"touched\" - names that are not marked are not present in the latest set of binding\n  and we can remove such class name from the internal data structures;\n\n  After iteration over all the CSS class names we've got data structure with all the information\n  necessary to synchronize changes to the DOM - it is enough to iterate over the state map, flush\n  changes to the DOM and reset internal data structures so those are ready for the next change\n  detection cycle.\n   */\n  ngDoCheck(): void {\n    // classes from the [class] binding\n    for (const klass of this.initialClasses) {\n      this._updateState(klass, true);\n    }\n\n    // classes from the [ngClass] binding\n    const rawClass = this.rawClass;\n    if (Array.isArray(rawClass) || rawClass instanceof Set) {\n      for (const klass of rawClass) {\n        this._updateState(klass, true);\n      }\n    } else if (rawClass != null) {\n      for (const klass of Object.keys(rawClass)) {\n        this._updateState(klass, Boolean(rawClass[klass]));\n      }\n    }\n\n    this._applyStateDiff();\n  }\n\n  private _updateState(klass: string, nextEnabled: boolean) {\n    const state = this.stateMap.get(klass);\n    if (state !== undefined) {\n      if (state.enabled !== nextEnabled) {\n        state.changed = true;\n        state.enabled = nextEnabled;\n      }\n      state.touched = true;\n    } else {\n      this.stateMap.set(klass, {enabled: nextEnabled, changed: true, touched: true});\n    }\n  }\n\n  private _applyStateDiff() {\n    for (const stateEntry of this.stateMap) {\n      const klass = stateEntry[0];\n      const state = stateEntry[1];\n\n      if (state.changed) {\n        this._toggleClass(klass, state.enabled);\n        state.changed = false;\n      } else if (!state.touched) {\n        // A class that was previously active got removed from the new collection of classes -\n        // remove from the DOM as well.\n        if (state.enabled) {\n          this._toggleClass(klass, false);\n        }\n        this.stateMap.delete(klass);\n      }\n\n      state.touched = false;\n    }\n  }\n\n  private _toggleClass(klass: string, enabled: boolean): void {\n    if (ngDevMode) {\n      if (typeof klass !== 'string') {\n        throw new Error(\n          `NgClass can only toggle CSS classes expressed as strings, got ${stringify(klass)}`,\n        );\n      }\n    }\n    klass = klass.trim();\n    if (klass.length > 0) {\n      klass.split(WS_REGEXP).forEach((klass) => {\n        if (enabled) {\n          this._renderer.addClass(this._ngEl.nativeElement, klass);\n        } else {\n          this._renderer.removeClass(this._ngEl.nativeElement, klass);\n        }\n      });\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ComponentRef,\n  createNgModule,\n  Directive,\n  Do<PERSON>heck,\n  Injector,\n  Input,\n  NgModuleFactory,\n  NgModuleRef,\n  OnChanges,\n  OnDestroy,\n  SimpleChanges,\n  Type,\n  ViewContainerRef,\n} from '@angular/core';\n\n/**\n * Instantiates a {@link Component} type and inserts its Host View into the current View.\n * `NgComponentOutlet` provides a declarative approach for dynamic component creation.\n *\n * `NgComponentOutlet` requires a component type, if a falsy value is set the view will clear and\n * any existing component will be destroyed.\n *\n * @usageNotes\n *\n * ### Fine tune control\n *\n * You can control the component creation process by using the following optional attributes:\n *\n * * `ngComponentOutletInputs`: Optional component inputs object, which will be bind to the\n * component.\n *\n * * `ngComponentOutletInjector`: Optional custom {@link Injector} that will be used as parent for\n * the Component. Defaults to the injector of the current view container.\n *\n * * `ngComponentOutletContent`: Optional list of projectable nodes to insert into the content\n * section of the component, if it exists.\n *\n * * `ngComponentOutletNgModule`: Optional NgModule class reference to allow loading another\n * module dynamically, then loading a component from that module.\n *\n * * `ngComponentOutletNgModuleFactory`: Deprecated config option that allows providing optional\n * NgModule factory to allow loading another module dynamically, then loading a component from that\n * module. Use `ngComponentOutletNgModule` instead.\n *\n * ### Syntax\n *\n * Simple\n * ```\n * <ng-container *ngComponentOutlet=\"componentTypeExpression\"></ng-container>\n * ```\n *\n * With inputs\n * ```\n * <ng-container *ngComponentOutlet=\"componentTypeExpression;\n *                                   inputs: inputsExpression;\">\n * </ng-container>\n * ```\n *\n * Customized injector/content\n * ```\n * <ng-container *ngComponentOutlet=\"componentTypeExpression;\n *                                   injector: injectorExpression;\n *                                   content: contentNodesExpression;\">\n * </ng-container>\n * ```\n *\n * Customized NgModule reference\n * ```\n * <ng-container *ngComponentOutlet=\"componentTypeExpression;\n *                                   ngModule: ngModuleClass;\">\n * </ng-container>\n * ```\n *\n * ### A simple example\n *\n * {@example common/ngComponentOutlet/ts/module.ts region='SimpleExample'}\n *\n * A more complete example with additional options:\n *\n * {@example common/ngComponentOutlet/ts/module.ts region='CompleteExample'}\n *\n * @publicApi\n * @ngModule CommonModule\n */\n@Directive({\n  selector: '[ngComponentOutlet]',\n  standalone: true,\n})\nexport class NgComponentOutlet implements OnChanges, DoCheck, OnDestroy {\n  @Input() ngComponentOutlet: Type<any> | null = null;\n\n  @Input() ngComponentOutletInputs?: Record<string, unknown>;\n  @Input() ngComponentOutletInjector?: Injector;\n  @Input() ngComponentOutletContent?: any[][];\n\n  @Input() ngComponentOutletNgModule?: Type<any>;\n  /**\n   * @deprecated This input is deprecated, use `ngComponentOutletNgModule` instead.\n   */\n  @Input() ngComponentOutletNgModuleFactory?: NgModuleFactory<any>;\n\n  private _componentRef: ComponentRef<any> | undefined;\n  private _moduleRef: NgModuleRef<any> | undefined;\n\n  /**\n   * A helper data structure that allows us to track inputs that were part of the\n   * ngComponentOutletInputs expression. Tracking inputs is necessary for proper removal of ones\n   * that are no longer referenced.\n   */\n  private _inputsUsed = new Map<string, boolean>();\n\n  constructor(private _viewContainerRef: ViewContainerRef) {}\n\n  private _needToReCreateNgModuleInstance(changes: SimpleChanges): boolean {\n    // Note: square brackets property accessor is safe for Closure compiler optimizations (the\n    // `changes` argument of the `ngOnChanges` lifecycle hook retains the names of the fields that\n    // were changed).\n    return (\n      changes['ngComponentOutletNgModule'] !== undefined ||\n      changes['ngComponentOutletNgModuleFactory'] !== undefined\n    );\n  }\n\n  private _needToReCreateComponentInstance(changes: SimpleChanges): boolean {\n    // Note: square brackets property accessor is safe for Closure compiler optimizations (the\n    // `changes` argument of the `ngOnChanges` lifecycle hook retains the names of the fields that\n    // were changed).\n    return (\n      changes['ngComponentOutlet'] !== undefined ||\n      changes['ngComponentOutletContent'] !== undefined ||\n      changes['ngComponentOutletInjector'] !== undefined ||\n      this._needToReCreateNgModuleInstance(changes)\n    );\n  }\n\n  /** @nodoc */\n  ngOnChanges(changes: SimpleChanges) {\n    if (this._needToReCreateComponentInstance(changes)) {\n      this._viewContainerRef.clear();\n      this._inputsUsed.clear();\n      this._componentRef = undefined;\n\n      if (this.ngComponentOutlet) {\n        const injector = this.ngComponentOutletInjector || this._viewContainerRef.parentInjector;\n\n        if (this._needToReCreateNgModuleInstance(changes)) {\n          this._moduleRef?.destroy();\n\n          if (this.ngComponentOutletNgModule) {\n            this._moduleRef = createNgModule(\n              this.ngComponentOutletNgModule,\n              getParentInjector(injector),\n            );\n          } else if (this.ngComponentOutletNgModuleFactory) {\n            this._moduleRef = this.ngComponentOutletNgModuleFactory.create(\n              getParentInjector(injector),\n            );\n          } else {\n            this._moduleRef = undefined;\n          }\n        }\n\n        this._componentRef = this._viewContainerRef.createComponent(this.ngComponentOutlet, {\n          injector,\n          ngModuleRef: this._moduleRef,\n          projectableNodes: this.ngComponentOutletContent,\n        });\n      }\n    }\n  }\n\n  /** @nodoc */\n  ngDoCheck() {\n    if (this._componentRef) {\n      if (this.ngComponentOutletInputs) {\n        for (const inputName of Object.keys(this.ngComponentOutletInputs)) {\n          this._inputsUsed.set(inputName, true);\n        }\n      }\n\n      this._applyInputStateDiff(this._componentRef);\n    }\n  }\n\n  /** @nodoc */\n  ngOnDestroy() {\n    this._moduleRef?.destroy();\n  }\n\n  private _applyInputStateDiff(componentRef: ComponentRef<unknown>) {\n    for (const [inputName, touched] of this._inputsUsed) {\n      if (!touched) {\n        // The input that was previously active no longer exists and needs to be set to undefined.\n        componentRef.setInput(inputName, undefined);\n        this._inputsUsed.delete(inputName);\n      } else {\n        // Since touched is true, it can be asserted that the inputs object is not empty.\n        componentRef.setInput(inputName, this.ngComponentOutletInputs![inputName]);\n        this._inputsUsed.set(inputName, false);\n      }\n    }\n  }\n}\n\n// Helper function that returns an Injector instance of a parent NgModule.\nfunction getParentInjector(injector: Injector): Injector {\n  const parentNgModule = injector.get(NgModuleRef);\n  return parentNgModule.injector;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Directive,\n  DoCheck,\n  EmbeddedViewRef,\n  Input,\n  IterableChangeRecord,\n  IterableChanges,\n  IterableDiffer,\n  IterableDiffers,\n  NgIterable,\n  TemplateRef,\n  TrackByFunction,\n  ViewContainerRef,\n  ɵRuntimeError as RuntimeError,\n} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../errors';\n\n/**\n * @publicApi\n */\nexport class NgForOfContext<T, U extends NgIterable<T> = NgIterable<T>> {\n  constructor(\n    public $implicit: T,\n    public ngForOf: U,\n    public index: number,\n    public count: number,\n  ) {}\n\n  get first(): boolean {\n    return this.index === 0;\n  }\n\n  get last(): boolean {\n    return this.index === this.count - 1;\n  }\n\n  get even(): boolean {\n    return this.index % 2 === 0;\n  }\n\n  get odd(): boolean {\n    return !this.even;\n  }\n}\n\n/**\n * A [structural directive](guide/structural-directives) that renders\n * a template for each item in a collection.\n * The directive is placed on an element, which becomes the parent\n * of the cloned templates.\n *\n * The `ngForOf` directive is generally used in the\n * [shorthand form](guide/structural-directives#asterisk) `*ngFor`.\n * In this form, the template to be rendered for each iteration is the content\n * of an anchor element containing the directive.\n *\n * The following example shows the shorthand syntax with some options,\n * contained in an `<li>` element.\n *\n * ```\n * <li *ngFor=\"let item of items; index as i; trackBy: trackByFn\">...</li>\n * ```\n *\n * The shorthand form expands into a long form that uses the `ngForOf` selector\n * on an `<ng-template>` element.\n * The content of the `<ng-template>` element is the `<li>` element that held the\n * short-form directive.\n *\n * Here is the expanded version of the short-form example.\n *\n * ```\n * <ng-template ngFor let-item [ngForOf]=\"items\" let-i=\"index\" [ngForTrackBy]=\"trackByFn\">\n *   <li>...</li>\n * </ng-template>\n * ```\n *\n * Angular automatically expands the shorthand syntax as it compiles the template.\n * The context for each embedded view is logically merged to the current component\n * context according to its lexical position.\n *\n * When using the shorthand syntax, Angular allows only [one structural directive\n * on an element](guide/structural-directives#one-per-element).\n * If you want to iterate conditionally, for example,\n * put the `*ngIf` on a container element that wraps the `*ngFor` element.\n * For further discussion, see\n * [Structural Directives](guide/structural-directives#one-per-element).\n *\n * @usageNotes\n *\n * ### Local variables\n *\n * `NgForOf` provides exported values that can be aliased to local variables.\n * For example:\n *\n *  ```\n * <li *ngFor=\"let user of users; index as i; first as isFirst\">\n *    {{i}}/{{users.length}}. {{user}} <span *ngIf=\"isFirst\">default</span>\n * </li>\n * ```\n *\n * The following exported values can be aliased to local variables:\n *\n * - `$implicit: T`: The value of the individual items in the iterable (`ngForOf`).\n * - `ngForOf: NgIterable<T>`: The value of the iterable expression. Useful when the expression is\n * more complex then a property access, for example when using the async pipe (`userStreams |\n * async`).\n * - `index: number`: The index of the current item in the iterable.\n * - `count: number`: The length of the iterable.\n * - `first: boolean`: True when the item is the first item in the iterable.\n * - `last: boolean`: True when the item is the last item in the iterable.\n * - `even: boolean`: True when the item has an even index in the iterable.\n * - `odd: boolean`: True when the item has an odd index in the iterable.\n *\n * ### Change propagation\n *\n * When the contents of the iterator changes, `NgForOf` makes the corresponding changes to the DOM:\n *\n * * When an item is added, a new instance of the template is added to the DOM.\n * * When an item is removed, its template instance is removed from the DOM.\n * * When items are reordered, their respective templates are reordered in the DOM.\n *\n * Angular uses object identity to track insertions and deletions within the iterator and reproduce\n * those changes in the DOM. This has important implications for animations and any stateful\n * controls that are present, such as `<input>` elements that accept user input. Inserted rows can\n * be animated in, deleted rows can be animated out, and unchanged rows retain any unsaved state\n * such as user input.\n * For more on animations, see [Transitions and Triggers](guide/transition-and-triggers).\n *\n * The identities of elements in the iterator can change while the data does not.\n * This can happen, for example, if the iterator is produced from an RPC to the server, and that\n * RPC is re-run. Even if the data hasn't changed, the second response produces objects with\n * different identities, and Angular must tear down the entire DOM and rebuild it (as if all old\n * elements were deleted and all new elements inserted).\n *\n * To avoid this expensive operation, you can customize the default tracking algorithm.\n * by supplying the `trackBy` option to `NgForOf`.\n * `trackBy` takes a function that has two arguments: `index` and `item`.\n * If `trackBy` is given, Angular tracks changes by the return value of the function.\n *\n * @see [Structural Directives](guide/structural-directives)\n * @ngModule CommonModule\n * @publicApi\n */\n@Directive({\n  selector: '[ngFor][ngForOf]',\n  standalone: true,\n})\nexport class NgForOf<T, U extends NgIterable<T> = NgIterable<T>> implements DoCheck {\n  /**\n   * The value of the iterable expression, which can be used as a\n   * [template input variable](guide/structural-directives#shorthand).\n   */\n  @Input()\n  set ngForOf(ngForOf: (U & NgIterable<T>) | undefined | null) {\n    this._ngForOf = ngForOf;\n    this._ngForOfDirty = true;\n  }\n  /**\n   * Specifies a custom `TrackByFunction` to compute the identity of items in an iterable.\n   *\n   * If a custom `TrackByFunction` is not provided, `NgForOf` will use the item's [object\n   * identity](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is)\n   * as the key.\n   *\n   * `NgForOf` uses the computed key to associate items in an iterable with DOM elements\n   * it produces for these items.\n   *\n   * A custom `TrackByFunction` is useful to provide good user experience in cases when items in an\n   * iterable rendered using `NgForOf` have a natural identifier (for example, custom ID or a\n   * primary key), and this iterable could be updated with new object instances that still\n   * represent the same underlying entity (for example, when data is re-fetched from the server,\n   * and the iterable is recreated and re-rendered, but most of the data is still the same).\n   *\n   * @see {@link TrackByFunction}\n   */\n  @Input()\n  set ngForTrackBy(fn: TrackByFunction<T>) {\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && fn != null && typeof fn !== 'function') {\n      console.warn(\n        `trackBy must be a function, but received ${JSON.stringify(fn)}. ` +\n          `See https://angular.io/api/common/NgForOf#change-propagation for more information.`,\n      );\n    }\n    this._trackByFn = fn;\n  }\n\n  get ngForTrackBy(): TrackByFunction<T> {\n    return this._trackByFn;\n  }\n\n  private _ngForOf: U | undefined | null = null;\n  private _ngForOfDirty: boolean = true;\n  private _differ: IterableDiffer<T> | null = null;\n  // TODO(issue/24571): remove '!'\n  // waiting for microsoft/typescript#43662 to allow the return type `TrackByFunction|undefined` for\n  // the getter\n  private _trackByFn!: TrackByFunction<T>;\n\n  constructor(\n    private _viewContainer: ViewContainerRef,\n    private _template: TemplateRef<NgForOfContext<T, U>>,\n    private _differs: IterableDiffers,\n  ) {}\n\n  /**\n   * A reference to the template that is stamped out for each item in the iterable.\n   * @see [template reference variable](guide/template-reference-variables)\n   */\n  @Input()\n  set ngForTemplate(value: TemplateRef<NgForOfContext<T, U>>) {\n    // TODO(TS2.1): make TemplateRef<Partial<NgForRowOf<T>>> once we move to TS v2.1\n    // The current type is too restrictive; a template that just uses index, for example,\n    // should be acceptable.\n    if (value) {\n      this._template = value;\n    }\n  }\n\n  /**\n   * Applies the changes when needed.\n   * @nodoc\n   */\n  ngDoCheck(): void {\n    if (this._ngForOfDirty) {\n      this._ngForOfDirty = false;\n      // React on ngForOf changes only once all inputs have been initialized\n      const value = this._ngForOf;\n      if (!this._differ && value) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          try {\n            // CAUTION: this logic is duplicated for production mode below, as the try-catch\n            // is only present in development builds.\n            this._differ = this._differs.find(value).create(this.ngForTrackBy);\n          } catch {\n            let errorMessage =\n              `Cannot find a differ supporting object '${value}' of type '` +\n              `${getTypeName(value)}'. NgFor only supports binding to Iterables, such as Arrays.`;\n            if (typeof value === 'object') {\n              errorMessage += ' Did you mean to use the keyvalue pipe?';\n            }\n            throw new RuntimeError(RuntimeErrorCode.NG_FOR_MISSING_DIFFER, errorMessage);\n          }\n        } else {\n          // CAUTION: this logic is duplicated for development mode above, as the try-catch\n          // is only present in development builds.\n          this._differ = this._differs.find(value).create(this.ngForTrackBy);\n        }\n      }\n    }\n    if (this._differ) {\n      const changes = this._differ.diff(this._ngForOf);\n      if (changes) this._applyChanges(changes);\n    }\n  }\n\n  private _applyChanges(changes: IterableChanges<T>) {\n    const viewContainer = this._viewContainer;\n    changes.forEachOperation(\n      (\n        item: IterableChangeRecord<T>,\n        adjustedPreviousIndex: number | null,\n        currentIndex: number | null,\n      ) => {\n        if (item.previousIndex == null) {\n          // NgForOf is never \"null\" or \"undefined\" here because the differ detected\n          // that a new item needs to be inserted from the iterable. This implies that\n          // there is an iterable value for \"_ngForOf\".\n          viewContainer.createEmbeddedView(\n            this._template,\n            new NgForOfContext<T, U>(item.item, this._ngForOf!, -1, -1),\n            currentIndex === null ? undefined : currentIndex,\n          );\n        } else if (currentIndex == null) {\n          viewContainer.remove(adjustedPreviousIndex === null ? undefined : adjustedPreviousIndex);\n        } else if (adjustedPreviousIndex !== null) {\n          const view = viewContainer.get(adjustedPreviousIndex)!;\n          viewContainer.move(view, currentIndex);\n          applyViewChange(view as EmbeddedViewRef<NgForOfContext<T, U>>, item);\n        }\n      },\n    );\n\n    for (let i = 0, ilen = viewContainer.length; i < ilen; i++) {\n      const viewRef = <EmbeddedViewRef<NgForOfContext<T, U>>>viewContainer.get(i);\n      const context = viewRef.context;\n      context.index = i;\n      context.count = ilen;\n      context.ngForOf = this._ngForOf!;\n    }\n\n    changes.forEachIdentityChange((record: any) => {\n      const viewRef = <EmbeddedViewRef<NgForOfContext<T, U>>>viewContainer.get(record.currentIndex);\n      applyViewChange(viewRef, record);\n    });\n  }\n\n  /**\n   * Asserts the correct type of the context for the template that `NgForOf` will render.\n   *\n   * The presence of this method is a signal to the Ivy template type-check compiler that the\n   * `NgForOf` structural directive renders its template with a specific context type.\n   */\n  static ngTemplateContextGuard<T, U extends NgIterable<T>>(\n    dir: NgForOf<T, U>,\n    ctx: any,\n  ): ctx is NgForOfContext<T, U> {\n    return true;\n  }\n}\n\n// Also export the `NgForOf` class as `NgFor` to improve the DX for\n// cases when the directive is used as standalone, so the class name\n// matches the CSS selector (*ngFor).\nexport {NgForOf as NgFor};\n\nfunction applyViewChange<T>(\n  view: EmbeddedViewRef<NgForOfContext<T>>,\n  record: IterableChangeRecord<T>,\n) {\n  view.context.$implicit = record.item;\n}\n\nfunction getTypeName(type: any): string {\n  return type['name'] || typeof type;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Directive,\n  EmbeddedViewRef,\n  Input,\n  TemplateRef,\n  ViewContainerRef,\n  ɵstringify as stringify,\n} from '@angular/core';\n\n/**\n * A structural directive that conditionally includes a template based on the value of\n * an expression coerced to <PERSON><PERSON><PERSON>.\n * When the expression evaluates to true, <PERSON><PERSON> renders the template\n * provided in a `then` clause, and when  false or null,\n * <PERSON><PERSON> renders the template provided in an optional `else` clause. The default\n * template for the `else` clause is blank.\n *\n * A [shorthand form](guide/structural-directives#asterisk) of the directive,\n * `*ngIf=\"condition\"`, is generally used, provided\n * as an attribute of the anchor element for the inserted template.\n * <PERSON><PERSON> expands this into a more explicit version, in which the anchor element\n * is contained in an `<ng-template>` element.\n *\n * Simple form with shorthand syntax:\n *\n * ```\n * <div *ngIf=\"condition\">Content to render when condition is true.</div>\n * ```\n *\n * Simple form with expanded syntax:\n *\n * ```\n * <ng-template [ngIf]=\"condition\"><div>Content to render when condition is\n * true.</div></ng-template>\n * ```\n *\n * Form with an \"else\" block:\n *\n * ```\n * <div *ngIf=\"condition; else elseBlock\">Content to render when condition is true.</div>\n * <ng-template #elseBlock>Content to render when condition is false.</ng-template>\n * ```\n *\n * Shorthand form with \"then\" and \"else\" blocks:\n *\n * ```\n * <div *ngIf=\"condition; then thenBlock else elseBlock\"></div>\n * <ng-template #thenBlock>Content to render when condition is true.</ng-template>\n * <ng-template #elseBlock>Content to render when condition is false.</ng-template>\n * ```\n *\n * Form with storing the value locally:\n *\n * ```\n * <div *ngIf=\"condition as value; else elseBlock\">{{value}}</div>\n * <ng-template #elseBlock>Content to render when value is null.</ng-template>\n * ```\n *\n * @usageNotes\n *\n * The `*ngIf` directive is most commonly used to conditionally show an inline template,\n * as seen in the following  example.\n * The default `else` template is blank.\n *\n * {@example common/ngIf/ts/module.ts region='NgIfSimple'}\n *\n * ### Showing an alternative template using `else`\n *\n * To display a template when `expression` evaluates to false, use an `else` template\n * binding as shown in the following example.\n * The `else` binding points to an `<ng-template>`  element labeled `#elseBlock`.\n * The template can be defined anywhere in the component view, but is typically placed right after\n * `ngIf` for readability.\n *\n * {@example common/ngIf/ts/module.ts region='NgIfElse'}\n *\n * ### Using an external `then` template\n *\n * In the previous example, the then-clause template is specified inline, as the content of the\n * tag that contains the `ngIf` directive. You can also specify a template that is defined\n * externally, by referencing a labeled `<ng-template>` element. When you do this, you can\n * change which template to use at runtime, as shown in the following example.\n *\n * {@example common/ngIf/ts/module.ts region='NgIfThenElse'}\n *\n * ### Storing a conditional result in a variable\n *\n * You might want to show a set of properties from the same object. If you are waiting\n * for asynchronous data, the object can be undefined.\n * In this case, you can use `ngIf` and store the result of the condition in a local\n * variable as shown in the following example.\n *\n * {@example common/ngIf/ts/module.ts region='NgIfAs'}\n *\n * This code uses only one `AsyncPipe`, so only one subscription is created.\n * The conditional statement stores the result of `userStream|async` in the local variable `user`.\n * You can then bind the local `user` repeatedly.\n *\n * The conditional displays the data only if `userStream` returns a value,\n * so you don't need to use the\n * safe-navigation-operator (`?.`)\n * to guard against null values when accessing properties.\n * You can display an alternative template while waiting for the data.\n *\n * ### Shorthand syntax\n *\n * The shorthand syntax `*ngIf` expands into two separate template specifications\n * for the \"then\" and \"else\" clauses. For example, consider the following shorthand statement,\n * that is meant to show a loading page while waiting for data to be loaded.\n *\n * ```\n * <div class=\"hero-list\" *ngIf=\"heroes else loading\">\n *  ...\n * </div>\n *\n * <ng-template #loading>\n *  <div>Loading...</div>\n * </ng-template>\n * ```\n *\n * You can see that the \"else\" clause references the `<ng-template>`\n * with the `#loading` label, and the template for the \"then\" clause\n * is provided as the content of the anchor element.\n *\n * However, when Angular expands the shorthand syntax, it creates\n * another `<ng-template>` tag, with `ngIf` and `ngIfElse` directives.\n * The anchor element containing the template for the \"then\" clause becomes\n * the content of this unlabeled `<ng-template>` tag.\n *\n * ```\n * <ng-template [ngIf]=\"heroes\" [ngIfElse]=\"loading\">\n *  <div class=\"hero-list\">\n *   ...\n *  </div>\n * </ng-template>\n *\n * <ng-template #loading>\n *  <div>Loading...</div>\n * </ng-template>\n * ```\n *\n * The presence of the implicit template object has implications for the nesting of\n * structural directives. For more on this subject, see\n * [Structural Directives](guide/structural-directives#one-per-element).\n *\n * @ngModule CommonModule\n * @publicApi\n */\n@Directive({\n  selector: '[ngIf]',\n  standalone: true,\n})\nexport class NgIf<T = unknown> {\n  private _context: NgIfContext<T> = new NgIfContext<T>();\n  private _thenTemplateRef: TemplateRef<NgIfContext<T>> | null = null;\n  private _elseTemplateRef: TemplateRef<NgIfContext<T>> | null = null;\n  private _thenViewRef: EmbeddedViewRef<NgIfContext<T>> | null = null;\n  private _elseViewRef: EmbeddedViewRef<NgIfContext<T>> | null = null;\n\n  constructor(\n    private _viewContainer: ViewContainerRef,\n    templateRef: TemplateRef<NgIfContext<T>>,\n  ) {\n    this._thenTemplateRef = templateRef;\n  }\n\n  /**\n   * The Boolean expression to evaluate as the condition for showing a template.\n   */\n  @Input()\n  set ngIf(condition: T) {\n    this._context.$implicit = this._context.ngIf = condition;\n    this._updateView();\n  }\n\n  /**\n   * A template to show if the condition expression evaluates to true.\n   */\n  @Input()\n  set ngIfThen(templateRef: TemplateRef<NgIfContext<T>> | null) {\n    assertTemplate('ngIfThen', templateRef);\n    this._thenTemplateRef = templateRef;\n    this._thenViewRef = null; // clear previous view if any.\n    this._updateView();\n  }\n\n  /**\n   * A template to show if the condition expression evaluates to false.\n   */\n  @Input()\n  set ngIfElse(templateRef: TemplateRef<NgIfContext<T>> | null) {\n    assertTemplate('ngIfElse', templateRef);\n    this._elseTemplateRef = templateRef;\n    this._elseViewRef = null; // clear previous view if any.\n    this._updateView();\n  }\n\n  private _updateView() {\n    if (this._context.$implicit) {\n      if (!this._thenViewRef) {\n        this._viewContainer.clear();\n        this._elseViewRef = null;\n        if (this._thenTemplateRef) {\n          this._thenViewRef = this._viewContainer.createEmbeddedView(\n            this._thenTemplateRef,\n            this._context,\n          );\n        }\n      }\n    } else {\n      if (!this._elseViewRef) {\n        this._viewContainer.clear();\n        this._thenViewRef = null;\n        if (this._elseTemplateRef) {\n          this._elseViewRef = this._viewContainer.createEmbeddedView(\n            this._elseTemplateRef,\n            this._context,\n          );\n        }\n      }\n    }\n  }\n\n  /** @internal */\n  public static ngIfUseIfTypeGuard: void;\n\n  /**\n   * Assert the correct type of the expression bound to the `ngIf` input within the template.\n   *\n   * The presence of this static field is a signal to the Ivy template type check compiler that\n   * when the `NgIf` structural directive renders its template, the type of the expression bound\n   * to `ngIf` should be narrowed in some way. For `NgIf`, the binding expression itself is used to\n   * narrow its type, which allows the strictNullChecks feature of TypeScript to work with `NgIf`.\n   */\n  static ngTemplateGuard_ngIf: 'binding';\n\n  /**\n   * Asserts the correct type of the context for the template that `NgIf` will render.\n   *\n   * The presence of this method is a signal to the Ivy template type-check compiler that the\n   * `NgIf` structural directive renders its template with a specific context type.\n   */\n  static ngTemplateContextGuard<T>(\n    dir: NgIf<T>,\n    ctx: any,\n  ): ctx is NgIfContext<Exclude<T, false | 0 | '' | null | undefined>> {\n    return true;\n  }\n}\n\n/**\n * @publicApi\n */\nexport class NgIfContext<T = unknown> {\n  public $implicit: T = null!;\n  public ngIf: T = null!;\n}\n\nfunction assertTemplate(property: string, templateRef: TemplateRef<any> | null): void {\n  const isTemplateRefOrNull = !!(!templateRef || templateRef.createEmbeddedView);\n  if (!isTemplateRefOrNull) {\n    throw new Error(`${property} must be a TemplateRef, but received '${stringify(templateRef)}'.`);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A constant indicating a type of comparison that NgSwitch uses to match cases. Extracted to a\n * separate file to facilitate G3 patches.\n */\nexport const NG_SWITCH_USE_STRICT_EQUALS = true;\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Directive,\n  DoCheck,\n  Host,\n  Input,\n  Optional,\n  TemplateRef,\n  ViewContainerRef,\n  ɵformatRuntimeError as formatRuntimeError,\n  ɵRuntimeError as RuntimeError,\n} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../errors';\n\nimport {NG_SWITCH_USE_STRICT_EQUALS} from './ng_switch_equality';\n\nexport class SwitchView {\n  private _created = false;\n\n  constructor(\n    private _viewContainerRef: ViewContainerRef,\n    private _templateRef: TemplateRef<Object>,\n  ) {}\n\n  create(): void {\n    this._created = true;\n    this._viewContainerRef.createEmbeddedView(this._templateRef);\n  }\n\n  destroy(): void {\n    this._created = false;\n    this._viewContainerRef.clear();\n  }\n\n  enforceState(created: boolean) {\n    if (created && !this._created) {\n      this.create();\n    } else if (!created && this._created) {\n      this.destroy();\n    }\n  }\n}\n\n/**\n * @ngModule CommonModule\n *\n * @description\n * The `[ngSwitch]` directive on a container specifies an expression to match against.\n * The expressions to match are provided by `ngSwitchCase` directives on views within the container.\n * - Every view that matches is rendered.\n * - If there are no matches, a view with the `ngSwitchDefault` directive is rendered.\n * - Elements within the `[NgSwitch]` statement but outside of any `NgSwitchCase`\n * or `ngSwitchDefault` directive are preserved at the location.\n *\n * @usageNotes\n * Define a container element for the directive, and specify the switch expression\n * to match against as an attribute:\n *\n * ```\n * <container-element [ngSwitch]=\"switch_expression\">\n * ```\n *\n * Within the container, `*ngSwitchCase` statements specify the match expressions\n * as attributes. Include `*ngSwitchDefault` as the final case.\n *\n * ```\n * <container-element [ngSwitch]=\"switch_expression\">\n *    <some-element *ngSwitchCase=\"match_expression_1\">...</some-element>\n * ...\n *    <some-element *ngSwitchDefault>...</some-element>\n * </container-element>\n * ```\n *\n * ### Usage Examples\n *\n * The following example shows how to use more than one case to display the same view:\n *\n * ```\n * <container-element [ngSwitch]=\"switch_expression\">\n *   <!-- the same view can be shown in more than one case -->\n *   <some-element *ngSwitchCase=\"match_expression_1\">...</some-element>\n *   <some-element *ngSwitchCase=\"match_expression_2\">...</some-element>\n *   <some-other-element *ngSwitchCase=\"match_expression_3\">...</some-other-element>\n *   <!--default case when there are no matches -->\n *   <some-element *ngSwitchDefault>...</some-element>\n * </container-element>\n * ```\n *\n * The following example shows how cases can be nested:\n * ```\n * <container-element [ngSwitch]=\"switch_expression\">\n *       <some-element *ngSwitchCase=\"match_expression_1\">...</some-element>\n *       <some-element *ngSwitchCase=\"match_expression_2\">...</some-element>\n *       <some-other-element *ngSwitchCase=\"match_expression_3\">...</some-other-element>\n *       <ng-container *ngSwitchCase=\"match_expression_3\">\n *         <!-- use a ng-container to group multiple root nodes -->\n *         <inner-element></inner-element>\n *         <inner-other-element></inner-other-element>\n *       </ng-container>\n *       <some-element *ngSwitchDefault>...</some-element>\n *     </container-element>\n * ```\n *\n * @publicApi\n * @see {@link NgSwitchCase}\n * @see {@link NgSwitchDefault}\n * @see [Structural Directives](guide/structural-directives)\n *\n */\n@Directive({\n  selector: '[ngSwitch]',\n  standalone: true,\n})\nexport class NgSwitch {\n  private _defaultViews: SwitchView[] = [];\n  private _defaultUsed = false;\n  private _caseCount = 0;\n  private _lastCaseCheckIndex = 0;\n  private _lastCasesMatched = false;\n  private _ngSwitch: any;\n\n  @Input()\n  set ngSwitch(newValue: any) {\n    this._ngSwitch = newValue;\n    if (this._caseCount === 0) {\n      this._updateDefaultCases(true);\n    }\n  }\n\n  /** @internal */\n  _addCase(): number {\n    return this._caseCount++;\n  }\n\n  /** @internal */\n  _addDefault(view: SwitchView) {\n    this._defaultViews.push(view);\n  }\n\n  /** @internal */\n  _matchCase(value: any): boolean {\n    const matched = NG_SWITCH_USE_STRICT_EQUALS\n      ? value === this._ngSwitch\n      : value == this._ngSwitch;\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && matched !== (value == this._ngSwitch)) {\n      console.warn(\n        formatRuntimeError(\n          RuntimeErrorCode.EQUALITY_NG_SWITCH_DIFFERENCE,\n          'As of Angular v17 the NgSwitch directive uses strict equality comparison === instead of == to match different cases. ' +\n            `Previously the case value \"${stringifyValue(\n              value,\n            )}\" matched switch expression value \"${stringifyValue(\n              this._ngSwitch,\n            )}\", but this is no longer the case with the stricter equality check. ` +\n            'Your comparison results return different results using === vs. == and you should adjust your ngSwitch expression and / or values to conform with the strict equality requirements.',\n        ),\n      );\n    }\n    this._lastCasesMatched ||= matched;\n    this._lastCaseCheckIndex++;\n    if (this._lastCaseCheckIndex === this._caseCount) {\n      this._updateDefaultCases(!this._lastCasesMatched);\n      this._lastCaseCheckIndex = 0;\n      this._lastCasesMatched = false;\n    }\n    return matched;\n  }\n\n  private _updateDefaultCases(useDefault: boolean) {\n    if (this._defaultViews.length > 0 && useDefault !== this._defaultUsed) {\n      this._defaultUsed = useDefault;\n      for (const defaultView of this._defaultViews) {\n        defaultView.enforceState(useDefault);\n      }\n    }\n  }\n}\n\n/**\n * @ngModule CommonModule\n *\n * @description\n * Provides a switch case expression to match against an enclosing `ngSwitch` expression.\n * When the expressions match, the given `NgSwitchCase` template is rendered.\n * If multiple match expressions match the switch expression value, all of them are displayed.\n *\n * @usageNotes\n *\n * Within a switch container, `*ngSwitchCase` statements specify the match expressions\n * as attributes. Include `*ngSwitchDefault` as the final case.\n *\n * ```\n * <container-element [ngSwitch]=\"switch_expression\">\n *   <some-element *ngSwitchCase=\"match_expression_1\">...</some-element>\n *   ...\n *   <some-element *ngSwitchDefault>...</some-element>\n * </container-element>\n * ```\n *\n * Each switch-case statement contains an in-line HTML template or template reference\n * that defines the subtree to be selected if the value of the match expression\n * matches the value of the switch expression.\n *\n * As of Angular v17 the NgSwitch directive uses strict equality comparison (`===`) instead of\n * loose equality (`==`) to match different cases.\n *\n * @publicApi\n * @see {@link NgSwitch}\n * @see {@link NgSwitchDefault}\n *\n */\n@Directive({\n  selector: '[ngSwitchCase]',\n  standalone: true,\n})\nexport class NgSwitchCase implements DoCheck {\n  private _view: SwitchView;\n  /**\n   * Stores the HTML template to be selected on match.\n   */\n  @Input() ngSwitchCase: any;\n\n  constructor(\n    viewContainer: ViewContainerRef,\n    templateRef: TemplateRef<Object>,\n    @Optional() @Host() private ngSwitch: NgSwitch,\n  ) {\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && !ngSwitch) {\n      throwNgSwitchProviderNotFoundError('ngSwitchCase', 'NgSwitchCase');\n    }\n\n    ngSwitch._addCase();\n    this._view = new SwitchView(viewContainer, templateRef);\n  }\n\n  /**\n   * Performs case matching. For internal use only.\n   * @nodoc\n   */\n  ngDoCheck() {\n    this._view.enforceState(this.ngSwitch._matchCase(this.ngSwitchCase));\n  }\n}\n\n/**\n * @ngModule CommonModule\n *\n * @description\n *\n * Creates a view that is rendered when no `NgSwitchCase` expressions\n * match the `NgSwitch` expression.\n * This statement should be the final case in an `NgSwitch`.\n *\n * @publicApi\n * @see {@link NgSwitch}\n * @see {@link NgSwitchCase}\n *\n */\n@Directive({\n  selector: '[ngSwitchDefault]',\n  standalone: true,\n})\nexport class NgSwitchDefault {\n  constructor(\n    viewContainer: ViewContainerRef,\n    templateRef: TemplateRef<Object>,\n    @Optional() @Host() ngSwitch: NgSwitch,\n  ) {\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && !ngSwitch) {\n      throwNgSwitchProviderNotFoundError('ngSwitchDefault', 'NgSwitchDefault');\n    }\n\n    ngSwitch._addDefault(new SwitchView(viewContainer, templateRef));\n  }\n}\n\nfunction throwNgSwitchProviderNotFoundError(attrName: string, directiveName: string): never {\n  throw new RuntimeError(\n    RuntimeErrorCode.PARENT_NG_SWITCH_NOT_FOUND,\n    `An element with the \"${attrName}\" attribute ` +\n      `(matching the \"${directiveName}\" directive) must be located inside an element with the \"ngSwitch\" attribute ` +\n      `(matching \"NgSwitch\" directive)`,\n  );\n}\n\nfunction stringifyValue(value: unknown): string {\n  return typeof value === 'string' ? `'${value}'` : String(value);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Attribute, Directive, Host, Input, TemplateRef, ViewContainerRef} from '@angular/core';\n\nimport {getPluralCategory, NgLocalization} from '../i18n/localization';\n\nimport {SwitchView} from './ng_switch';\n\n/**\n * @ngModule CommonModule\n *\n * @usageNotes\n * ```\n * <some-element [ngPlural]=\"value\">\n *   <ng-template ngPluralCase=\"=0\">there is nothing</ng-template>\n *   <ng-template ngPluralCase=\"=1\">there is one</ng-template>\n *   <ng-template ngPluralCase=\"few\">there are a few</ng-template>\n * </some-element>\n * ```\n *\n * @description\n *\n * Adds / removes DOM sub-trees based on a numeric value. Tailored for pluralization.\n *\n * Displays DOM sub-trees that match the switch expression value, or failing that, DOM sub-trees\n * that match the switch expression's pluralization category.\n *\n * To use this directive you must provide a container element that sets the `[ngPlural]` attribute\n * to a switch expression. Inner elements with a `[ngPluralCase]` will display based on their\n * expression:\n * - if `[ngPluralCase]` is set to a value starting with `=`, it will only display if the value\n *   matches the switch expression exactly,\n * - otherwise, the view will be treated as a \"category match\", and will only display if exact\n *   value matches aren't found and the value maps to its category for the defined locale.\n *\n * See http://cldr.unicode.org/index/cldr-spec/plural-rules\n *\n * @publicApi\n */\n@Directive({\n  selector: '[ngPlural]',\n  standalone: true,\n})\nexport class NgPlural {\n  private _activeView?: SwitchView;\n  private _caseViews: {[k: string]: SwitchView} = {};\n\n  constructor(private _localization: NgLocalization) {}\n\n  @Input()\n  set ngPlural(value: number) {\n    this._updateView(value);\n  }\n\n  addCase(value: string, switchView: SwitchView): void {\n    this._caseViews[value] = switchView;\n  }\n\n  private _updateView(switchValue: number): void {\n    this._clearViews();\n\n    const cases = Object.keys(this._caseViews);\n    const key = getPluralCategory(switchValue, cases, this._localization);\n    this._activateView(this._caseViews[key]);\n  }\n\n  private _clearViews() {\n    if (this._activeView) this._activeView.destroy();\n  }\n\n  private _activateView(view: SwitchView) {\n    if (view) {\n      this._activeView = view;\n      this._activeView.create();\n    }\n  }\n}\n\n/**\n * @ngModule CommonModule\n *\n * @description\n *\n * Creates a view that will be added/removed from the parent {@link NgPlural} when the\n * given expression matches the plural expression according to CLDR rules.\n *\n * @usageNotes\n * ```\n * <some-element [ngPlural]=\"value\">\n *   <ng-template ngPluralCase=\"=0\">...</ng-template>\n *   <ng-template ngPluralCase=\"other\">...</ng-template>\n * </some-element>\n *```\n *\n * See {@link NgPlural} for more details and example.\n *\n * @publicApi\n */\n@Directive({\n  selector: '[ngPluralCase]',\n  standalone: true,\n})\nexport class NgPluralCase {\n  constructor(\n    @Attribute('ngPluralCase') public value: string,\n    template: TemplateRef<Object>,\n    viewContainer: ViewContainerRef,\n    @Host() ngPlural: NgPlural,\n  ) {\n    const isANumber: boolean = !isNaN(Number(value));\n    ngPlural.addCase(isANumber ? `=${value}` : value, new SwitchView(viewContainer, template));\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {\n  Directive,\n  DoCheck,\n  ElementRef,\n  Input,\n  KeyValueChanges,\n  KeyValueDiffer,\n  KeyValueDiffers,\n  Renderer2,\n  RendererStyleFlags2,\n} from '@angular/core';\n\n/**\n * @ngModule CommonModule\n *\n * @usageNotes\n *\n * Set the font of the containing element to the result of an expression.\n *\n * ```\n * <some-element [ngStyle]=\"{'font-style': styleExp}\">...</some-element>\n * ```\n *\n * Set the width of the containing element to a pixel value returned by an expression.\n *\n * ```\n * <some-element [ngStyle]=\"{'max-width.px': widthExp}\">...</some-element>\n * ```\n *\n * Set a collection of style values using an expression that returns key-value pairs.\n *\n * ```\n * <some-element [ngStyle]=\"objExp\">...</some-element>\n * ```\n *\n * @description\n *\n * An attribute directive that updates styles for the containing HTML element.\n * Sets one or more style properties, specified as colon-separated key-value pairs.\n * The key is a style name, with an optional `.<unit>` suffix\n * (such as 'top.px', 'font-style.em').\n * The value is an expression to be evaluated.\n * The resulting non-null value, expressed in the given unit,\n * is assigned to the given style property.\n * If the result of evaluation is null, the corresponding style is removed.\n *\n * @publicApi\n */\n@Directive({\n  selector: '[ngStyle]',\n  standalone: true,\n})\nexport class NgStyle implements DoCheck {\n  private _ngStyle: {[key: string]: string} | null | undefined = null;\n  private _differ: KeyValueDiffer<string, string | number> | null = null;\n\n  constructor(\n    private _ngEl: ElementRef,\n    private _differs: KeyValueDiffers,\n    private _renderer: Renderer2,\n  ) {}\n\n  @Input('ngStyle')\n  set ngStyle(values: {[klass: string]: any} | null | undefined) {\n    this._ngStyle = values;\n    if (!this._differ && values) {\n      this._differ = this._differs.find(values).create();\n    }\n  }\n\n  ngDoCheck() {\n    if (this._differ) {\n      const changes = this._differ.diff(this._ngStyle!);\n      if (changes) {\n        this._applyChanges(changes);\n      }\n    }\n  }\n\n  private _setStyle(nameAndUnit: string, value: string | number | null | undefined): void {\n    const [name, unit] = nameAndUnit.split('.');\n    const flags = name.indexOf('-') === -1 ? undefined : (RendererStyleFlags2.DashCase as number);\n\n    if (value != null) {\n      this._renderer.setStyle(\n        this._ngEl.nativeElement,\n        name,\n        unit ? `${value}${unit}` : value,\n        flags,\n      );\n    } else {\n      this._renderer.removeStyle(this._ngEl.nativeElement, name, flags);\n    }\n  }\n\n  private _applyChanges(changes: KeyValueChanges<string, string | number>): void {\n    changes.forEachRemovedItem((record) => this._setStyle(record.key, null));\n    changes.forEachAddedItem((record) => this._setStyle(record.key, record.currentValue));\n    changes.forEachChangedItem((record) => this._setStyle(record.key, record.currentValue));\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Directive,\n  EmbeddedViewRef,\n  Injector,\n  Input,\n  OnChanges,\n  SimpleChange,\n  SimpleChanges,\n  TemplateRef,\n  ViewContainerRef,\n} from '@angular/core';\n\n/**\n * @ngModule CommonModule\n *\n * @description\n *\n * Inserts an embedded view from a prepared `TemplateRef`.\n *\n * You can attach a context object to the `EmbeddedViewRef` by setting `[ngTemplateOutletContext]`.\n * `[ngTemplateOutletContext]` should be an object, the object's keys will be available for binding\n * by the local template `let` declarations.\n *\n * @usageNotes\n * ```\n * <ng-container *ngTemplateOutlet=\"templateRefExp; context: contextExp\"></ng-container>\n * ```\n *\n * Using the key `$implicit` in the context object will set its value as default.\n *\n * ### Example\n *\n * {@example common/ngTemplateOutlet/ts/module.ts region='NgTemplateOutlet'}\n *\n * @publicApi\n */\n@Directive({\n  selector: '[ngTemplateOutlet]',\n  standalone: true,\n})\nexport class NgTemplateOutlet<C = unknown> implements OnChanges {\n  private _viewRef: EmbeddedViewRef<C> | null = null;\n\n  /**\n   * A context object to attach to the {@link EmbeddedViewRef}. This should be an\n   * object, the object's keys will be available for binding by the local template `let`\n   * declarations.\n   * Using the key `$implicit` in the context object will set its value as default.\n   */\n  @Input() public ngTemplateOutletContext: C | null = null;\n\n  /**\n   * A string defining the template reference and optionally the context object for the template.\n   */\n  @Input() public ngTemplateOutlet: TemplateRef<C> | null = null;\n\n  /** Injector to be used within the embedded view. */\n  @Input() public ngTemplateOutletInjector: Injector | null = null;\n\n  constructor(private _viewContainerRef: ViewContainerRef) {}\n\n  ngOnChanges(changes: SimpleChanges) {\n    if (this._shouldRecreateView(changes)) {\n      const viewContainerRef = this._viewContainerRef;\n\n      if (this._viewRef) {\n        viewContainerRef.remove(viewContainerRef.indexOf(this._viewRef));\n      }\n\n      // If there is no outlet, clear the destroyed view ref.\n      if (!this.ngTemplateOutlet) {\n        this._viewRef = null;\n        return;\n      }\n\n      // Create a context forward `Proxy` that will always bind to the user-specified context,\n      // without having to destroy and re-create views whenever the context changes.\n      const viewContext = this._createContextForwardProxy();\n      this._viewRef = viewContainerRef.createEmbeddedView(this.ngTemplateOutlet, viewContext, {\n        injector: this.ngTemplateOutletInjector ?? undefined,\n      });\n    }\n  }\n\n  /**\n   * We need to re-create existing embedded view if either is true:\n   * - the outlet changed.\n   * - the injector changed.\n   */\n  private _shouldRecreateView(changes: SimpleChanges): boolean {\n    return !!changes['ngTemplateOutlet'] || !!changes['ngTemplateOutletInjector'];\n  }\n\n  /**\n   * For a given outlet instance, we create a proxy object that delegates\n   * to the user-specified context. This allows changing, or swapping out\n   * the context object completely without having to destroy/re-create the view.\n   */\n  private _createContextForwardProxy(): C {\n    return <C>new Proxy(\n      {},\n      {\n        set: (_target, prop, newValue) => {\n          if (!this.ngTemplateOutletContext) {\n            return false;\n          }\n          return Reflect.set(this.ngTemplateOutletContext, prop, newValue);\n        },\n        get: (_target, prop, receiver) => {\n          if (!this.ngTemplateOutletContext) {\n            return undefined;\n          }\n          return Reflect.get(this.ngTemplateOutletContext, prop, receiver);\n        },\n      },\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Provider} from '@angular/core';\n\nimport {NgClass} from './ng_class';\nimport {NgComponentOutlet} from './ng_component_outlet';\nimport {Ng<PERSON>or, NgForOf, NgForOfContext} from './ng_for_of';\nimport {NgIf, NgIfContext} from './ng_if';\nimport {NgPlural, NgPluralCase} from './ng_plural';\nimport {NgStyle} from './ng_style';\nimport {Ng<PERSON><PERSON>, NgSwitchCase, NgSwitchDefault} from './ng_switch';\nimport {NgTemplateOutlet} from './ng_template_outlet';\n\nexport {\n  NgClass,\n  NgComponentOutlet,\n  NgFor,\n  NgForOf,\n  NgForOfContext,\n  NgIf,\n  NgIfContext,\n  NgPlural,\n  NgPluralCase,\n  Ng<PERSON><PERSON>le,\n  NgS<PERSON>,\n  Ng<PERSON><PERSON>Case,\n  <PERSON><PERSON><PERSON>Default,\n  NgTemplateOutlet,\n};\n\n/**\n * A collection of Angular directives that are likely to be used in each and every Angular\n * application.\n */\nexport const COMMON_DIRECTIVES: Provider[] = [\n  NgClass,\n  NgComponentOutlet,\n  NgForOf,\n  NgIf,\n  NgTemplateOutlet,\n  NgStyle,\n  NgSwitch,\n  NgSwitchCase,\n  NgSwitchDefault,\n  NgPlural,\n  NgPluralCase,\n];\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Type, ɵRuntimeError as RuntimeError, ɵstringify as stringify} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../errors';\n\nexport function invalidPipeArgumentError(type: Type<any>, value: Object) {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_PIPE_ARGUMENT,\n    ngDevMode && `InvalidPipeArgument: '${value}' for pipe '${stringify(type)}'`,\n  );\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ChangeDetectorRef,\n  EventEmitter,\n  OnDestroy,\n  Pipe,\n  PipeTransform,\n  untracked,\n  ɵisPromise,\n  ɵisSubscribable,\n} from '@angular/core';\nimport {Observable, Subscribable, Unsubscribable} from 'rxjs';\n\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n\ninterface SubscriptionStrategy {\n  createSubscription(\n    async: Subscribable<any> | Promise<any>,\n    updateLatestValue: any,\n  ): Unsubscribable | Promise<any>;\n  dispose(subscription: Unsubscribable | Promise<any>): void;\n}\n\nclass SubscribableStrategy implements SubscriptionStrategy {\n  createSubscription(async: Subscribable<any>, updateLatestValue: any): Unsubscribable {\n    // Subscription can be side-effectful, and we don't want any signal reads which happen in the\n    // side effect of the subscription to be tracked by a component's template when that\n    // subscription is triggered via the async pipe. So we wrap the subscription in `untracked` to\n    // decouple from the current reactive context.\n    //\n    // `untracked` also prevents signal _writes_ which happen in the subscription side effect from\n    // being treated as signal writes during the template evaluation (which throws errors).\n    return untracked(() =>\n      async.subscribe({\n        next: updateLatestValue,\n        error: (e: any) => {\n          throw e;\n        },\n      }),\n    );\n  }\n\n  dispose(subscription: Unsubscribable): void {\n    // See the comment in `createSubscription` above on the use of `untracked`.\n    untracked(() => subscription.unsubscribe());\n  }\n}\n\nclass PromiseStrategy implements SubscriptionStrategy {\n  createSubscription(async: Promise<any>, updateLatestValue: (v: any) => any): Promise<any> {\n    return async.then(updateLatestValue, (e) => {\n      throw e;\n    });\n  }\n\n  dispose(subscription: Promise<any>): void {}\n}\n\nconst _promiseStrategy = new PromiseStrategy();\nconst _subscribableStrategy = new SubscribableStrategy();\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Unwraps a value from an asynchronous primitive.\n *\n * The `async` pipe subscribes to an `Observable` or `Promise` and returns the latest value it has\n * emitted. When a new value is emitted, the `async` pipe marks the component to be checked for\n * changes. When the component gets destroyed, the `async` pipe unsubscribes automatically to avoid\n * potential memory leaks. When the reference of the expression changes, the `async` pipe\n * automatically unsubscribes from the old `Observable` or `Promise` and subscribes to the new one.\n *\n * @usageNotes\n *\n * ### Examples\n *\n * This example binds a `Promise` to the view. Clicking the `Resolve` button resolves the\n * promise.\n *\n * {@example common/pipes/ts/async_pipe.ts region='AsyncPipePromise'}\n *\n * It's also possible to use `async` with Observables. The example below binds the `time` Observable\n * to the view. The Observable continuously updates the view with the current time.\n *\n * {@example common/pipes/ts/async_pipe.ts region='AsyncPipeObservable'}\n *\n * @publicApi\n */\n@Pipe({\n  name: 'async',\n  pure: false,\n  standalone: true,\n})\nexport class AsyncPipe implements OnDestroy, PipeTransform {\n  private _ref: ChangeDetectorRef | null;\n  private _latestValue: any = null;\n  private markForCheckOnValueUpdate = true;\n\n  private _subscription: Unsubscribable | Promise<any> | null = null;\n  private _obj: Subscribable<any> | Promise<any> | EventEmitter<any> | null = null;\n  private _strategy: SubscriptionStrategy | null = null;\n\n  constructor(ref: ChangeDetectorRef) {\n    // Assign `ref` into `this._ref` manually instead of declaring `_ref` in the constructor\n    // parameter list, as the type of `this._ref` includes `null` unlike the type of `ref`.\n    this._ref = ref;\n  }\n\n  ngOnDestroy(): void {\n    if (this._subscription) {\n      this._dispose();\n    }\n    // Clear the `ChangeDetectorRef` and its association with the view data, to mitigate\n    // potential memory leaks in Observables that could otherwise cause the view data to\n    // be retained.\n    // https://github.com/angular/angular/issues/17624\n    this._ref = null;\n  }\n\n  // NOTE(@benlesh): Because Observable has deprecated a few call patterns for `subscribe`,\n  // TypeScript has a hard time matching Observable to Subscribable, for more information\n  // see https://github.com/microsoft/TypeScript/issues/43643\n\n  transform<T>(obj: Observable<T> | Subscribable<T> | Promise<T>): T | null;\n  transform<T>(obj: null | undefined): null;\n  transform<T>(obj: Observable<T> | Subscribable<T> | Promise<T> | null | undefined): T | null;\n  transform<T>(obj: Observable<T> | Subscribable<T> | Promise<T> | null | undefined): T | null {\n    if (!this._obj) {\n      if (obj) {\n        try {\n          // Only call `markForCheck` if the value is updated asynchronously.\n          // Synchronous updates _during_ subscription should not wastefully mark for check -\n          // this value is already going to be returned from the transform function.\n          this.markForCheckOnValueUpdate = false;\n          this._subscribe(obj);\n        } finally {\n          this.markForCheckOnValueUpdate = true;\n        }\n      }\n      return this._latestValue;\n    }\n\n    if (obj !== this._obj) {\n      this._dispose();\n      return this.transform(obj);\n    }\n\n    return this._latestValue;\n  }\n\n  private _subscribe(obj: Subscribable<any> | Promise<any> | EventEmitter<any>): void {\n    this._obj = obj;\n    this._strategy = this._selectStrategy(obj);\n    this._subscription = this._strategy.createSubscription(obj, (value: Object) =>\n      this._updateLatestValue(obj, value),\n    );\n  }\n\n  private _selectStrategy(\n    obj: Subscribable<any> | Promise<any> | EventEmitter<any>,\n  ): SubscriptionStrategy {\n    if (ɵisPromise(obj)) {\n      return _promiseStrategy;\n    }\n\n    if (ɵisSubscribable(obj)) {\n      return _subscribableStrategy;\n    }\n\n    throw invalidPipeArgumentError(AsyncPipe, obj);\n  }\n\n  private _dispose(): void {\n    // Note: `dispose` is only called if a subscription has been initialized before, indicating\n    // that `this._strategy` is also available.\n    this._strategy!.dispose(this._subscription!);\n    this._latestValue = null;\n    this._subscription = null;\n    this._obj = null;\n  }\n\n  private _updateLatestValue(async: any, value: Object): void {\n    if (async === this._obj) {\n      this._latestValue = value;\n      if (this.markForCheckOnValueUpdate) {\n        this._ref?.markForCheck();\n      }\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Pipe, PipeTransform} from '@angular/core';\n\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n\n/**\n * Transforms text to all lower case.\n *\n * @see {@link UpperCasePipe}\n * @see {@link TitleCasePipe}\n * @usageNotes\n *\n * The following example defines a view that allows the user to enter\n * text, and then uses the pipe to convert the input text to all lower case.\n *\n * <code-example path=\"common/pipes/ts/lowerupper_pipe.ts\" region='LowerUpperPipe'></code-example>\n *\n * @ngModule CommonModule\n * @publicApi\n */\n@Pipe({\n  name: 'lowercase',\n  standalone: true,\n})\nexport class LowerCasePipe implements PipeTransform {\n  /**\n   * @param value The string to transform to lower case.\n   */\n  transform(value: string): string;\n  transform(value: null | undefined): null;\n  transform(value: string | null | undefined): string | null;\n  transform(value: string | null | undefined): string | null {\n    if (value == null) return null;\n    if (typeof value !== 'string') {\n      throw invalidPipeArgumentError(LowerCasePipe, value);\n    }\n    return value.toLowerCase();\n  }\n}\n\n//\n// Regex below matches any Unicode word and number compatible with ES5. In ES2018 the same result\n// can be achieved by using /[0-9\\p{L}]\\S*/gu and also known as Unicode Property Escapes\n// (https://2ality.com/2017/07/regexp-unicode-property-escapes.html). Since there is no\n// transpilation of this functionality down to ES5 without external tool, the only solution is\n// to use already transpiled form. Example can be found here -\n// https://mothereff.in/regexpu#input=var+regex+%3D+%2F%5B0-9%5Cp%7BL%7D%5D%5CS*%2Fgu%3B%0A%0A&unicodePropertyEscape=1\n//\n\nconst unicodeWordMatch =\n  /(?:[0-9A-Za-z\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0560-\\u0588\\u05D0-\\u05EA\\u05EF-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u0860-\\u086A\\u0870-\\u0887\\u0889-\\u088E\\u08A0-\\u08C9\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u09FC\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0AF9\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D\\u0C58-\\u0C5A\\u0C5D\\u0C60\\u0C61\\u0C80\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D04-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D54-\\u0D56\\u0D5F-\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E33\\u0E40-\\u0E46\\u0E81\\u0E82\\u0E84\\u0E86-\\u0E8A\\u0E8C-\\u0EA3\\u0EA5\\u0EA7-\\u0EB0\\u0EB2\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16F1-\\u16F8\\u1700-\\u1711\\u171F-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1878\\u1880-\\u1884\\u1887-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u1A00-\\u1A16\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4C\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1C80-\\u1C88\\u1C90-\\u1CBA\\u1CBD-\\u1CBF\\u1CE9-\\u1CEC\\u1CEE-\\u1CF3\\u1CF5\\u1CF6\\u1CFA\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2183\\u2184\\u2C00-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005\\u3006\\u3031-\\u3035\\u303B\\u303C\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312F\\u3131-\\u318E\\u31A0-\\u31BF\\u31F0-\\u31FF\\u3400-\\u4DBF\\u4E00-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA69D\\uA6A0-\\uA6E5\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7CA\\uA7D0\\uA7D1\\uA7D3\\uA7D5-\\uA7D9\\uA7F2-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA8FD\\uA8FE\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uA9E0-\\uA9E4\\uA9E6-\\uA9EF\\uA9FA-\\uA9FE\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA7E-\\uAAAF\\uAAB1\\uAAB5\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB69\\uAB70-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]|\\uD800[\\uDC00-\\uDC0B\\uDC0D-\\uDC26\\uDC28-\\uDC3A\\uDC3C\\uDC3D\\uDC3F-\\uDC4D\\uDC50-\\uDC5D\\uDC80-\\uDCFA\\uDE80-\\uDE9C\\uDEA0-\\uDED0\\uDF00-\\uDF1F\\uDF2D-\\uDF40\\uDF42-\\uDF49\\uDF50-\\uDF75\\uDF80-\\uDF9D\\uDFA0-\\uDFC3\\uDFC8-\\uDFCF]|\\uD801[\\uDC00-\\uDC9D\\uDCB0-\\uDCD3\\uDCD8-\\uDCFB\\uDD00-\\uDD27\\uDD30-\\uDD63\\uDD70-\\uDD7A\\uDD7C-\\uDD8A\\uDD8C-\\uDD92\\uDD94\\uDD95\\uDD97-\\uDDA1\\uDDA3-\\uDDB1\\uDDB3-\\uDDB9\\uDDBB\\uDDBC\\uDE00-\\uDF36\\uDF40-\\uDF55\\uDF60-\\uDF67\\uDF80-\\uDF85\\uDF87-\\uDFB0\\uDFB2-\\uDFBA]|\\uD802[\\uDC00-\\uDC05\\uDC08\\uDC0A-\\uDC35\\uDC37\\uDC38\\uDC3C\\uDC3F-\\uDC55\\uDC60-\\uDC76\\uDC80-\\uDC9E\\uDCE0-\\uDCF2\\uDCF4\\uDCF5\\uDD00-\\uDD15\\uDD20-\\uDD39\\uDD80-\\uDDB7\\uDDBE\\uDDBF\\uDE00\\uDE10-\\uDE13\\uDE15-\\uDE17\\uDE19-\\uDE35\\uDE60-\\uDE7C\\uDE80-\\uDE9C\\uDEC0-\\uDEC7\\uDEC9-\\uDEE4\\uDF00-\\uDF35\\uDF40-\\uDF55\\uDF60-\\uDF72\\uDF80-\\uDF91]|\\uD803[\\uDC00-\\uDC48\\uDC80-\\uDCB2\\uDCC0-\\uDCF2\\uDD00-\\uDD23\\uDE80-\\uDEA9\\uDEB0\\uDEB1\\uDF00-\\uDF1C\\uDF27\\uDF30-\\uDF45\\uDF70-\\uDF81\\uDFB0-\\uDFC4\\uDFE0-\\uDFF6]|\\uD804[\\uDC03-\\uDC37\\uDC71\\uDC72\\uDC75\\uDC83-\\uDCAF\\uDCD0-\\uDCE8\\uDD03-\\uDD26\\uDD44\\uDD47\\uDD50-\\uDD72\\uDD76\\uDD83-\\uDDB2\\uDDC1-\\uDDC4\\uDDDA\\uDDDC\\uDE00-\\uDE11\\uDE13-\\uDE2B\\uDE80-\\uDE86\\uDE88\\uDE8A-\\uDE8D\\uDE8F-\\uDE9D\\uDE9F-\\uDEA8\\uDEB0-\\uDEDE\\uDF05-\\uDF0C\\uDF0F\\uDF10\\uDF13-\\uDF28\\uDF2A-\\uDF30\\uDF32\\uDF33\\uDF35-\\uDF39\\uDF3D\\uDF50\\uDF5D-\\uDF61]|\\uD805[\\uDC00-\\uDC34\\uDC47-\\uDC4A\\uDC5F-\\uDC61\\uDC80-\\uDCAF\\uDCC4\\uDCC5\\uDCC7\\uDD80-\\uDDAE\\uDDD8-\\uDDDB\\uDE00-\\uDE2F\\uDE44\\uDE80-\\uDEAA\\uDEB8\\uDF00-\\uDF1A\\uDF40-\\uDF46]|\\uD806[\\uDC00-\\uDC2B\\uDCA0-\\uDCDF\\uDCFF-\\uDD06\\uDD09\\uDD0C-\\uDD13\\uDD15\\uDD16\\uDD18-\\uDD2F\\uDD3F\\uDD41\\uDDA0-\\uDDA7\\uDDAA-\\uDDD0\\uDDE1\\uDDE3\\uDE00\\uDE0B-\\uDE32\\uDE3A\\uDE50\\uDE5C-\\uDE89\\uDE9D\\uDEB0-\\uDEF8]|\\uD807[\\uDC00-\\uDC08\\uDC0A-\\uDC2E\\uDC40\\uDC72-\\uDC8F\\uDD00-\\uDD06\\uDD08\\uDD09\\uDD0B-\\uDD30\\uDD46\\uDD60-\\uDD65\\uDD67\\uDD68\\uDD6A-\\uDD89\\uDD98\\uDEE0-\\uDEF2\\uDFB0]|\\uD808[\\uDC00-\\uDF99]|\\uD809[\\uDC80-\\uDD43]|\\uD80B[\\uDF90-\\uDFF0]|[\\uD80C\\uD81C-\\uD820\\uD822\\uD840-\\uD868\\uD86A-\\uD86C\\uD86F-\\uD872\\uD874-\\uD879\\uD880-\\uD883][\\uDC00-\\uDFFF]|\\uD80D[\\uDC00-\\uDC2E]|\\uD811[\\uDC00-\\uDE46]|\\uD81A[\\uDC00-\\uDE38\\uDE40-\\uDE5E\\uDE70-\\uDEBE\\uDED0-\\uDEED\\uDF00-\\uDF2F\\uDF40-\\uDF43\\uDF63-\\uDF77\\uDF7D-\\uDF8F]|\\uD81B[\\uDE40-\\uDE7F\\uDF00-\\uDF4A\\uDF50\\uDF93-\\uDF9F\\uDFE0\\uDFE1\\uDFE3]|\\uD821[\\uDC00-\\uDFF7]|\\uD823[\\uDC00-\\uDCD5\\uDD00-\\uDD08]|\\uD82B[\\uDFF0-\\uDFF3\\uDFF5-\\uDFFB\\uDFFD\\uDFFE]|\\uD82C[\\uDC00-\\uDD22\\uDD50-\\uDD52\\uDD64-\\uDD67\\uDD70-\\uDEFB]|\\uD82F[\\uDC00-\\uDC6A\\uDC70-\\uDC7C\\uDC80-\\uDC88\\uDC90-\\uDC99]|\\uD835[\\uDC00-\\uDC54\\uDC56-\\uDC9C\\uDC9E\\uDC9F\\uDCA2\\uDCA5\\uDCA6\\uDCA9-\\uDCAC\\uDCAE-\\uDCB9\\uDCBB\\uDCBD-\\uDCC3\\uDCC5-\\uDD05\\uDD07-\\uDD0A\\uDD0D-\\uDD14\\uDD16-\\uDD1C\\uDD1E-\\uDD39\\uDD3B-\\uDD3E\\uDD40-\\uDD44\\uDD46\\uDD4A-\\uDD50\\uDD52-\\uDEA5\\uDEA8-\\uDEC0\\uDEC2-\\uDEDA\\uDEDC-\\uDEFA\\uDEFC-\\uDF14\\uDF16-\\uDF34\\uDF36-\\uDF4E\\uDF50-\\uDF6E\\uDF70-\\uDF88\\uDF8A-\\uDFA8\\uDFAA-\\uDFC2\\uDFC4-\\uDFCB]|\\uD837[\\uDF00-\\uDF1E]|\\uD838[\\uDD00-\\uDD2C\\uDD37-\\uDD3D\\uDD4E\\uDE90-\\uDEAD\\uDEC0-\\uDEEB]|\\uD839[\\uDFE0-\\uDFE6\\uDFE8-\\uDFEB\\uDFED\\uDFEE\\uDFF0-\\uDFFE]|\\uD83A[\\uDC00-\\uDCC4\\uDD00-\\uDD43\\uDD4B]|\\uD83B[\\uDE00-\\uDE03\\uDE05-\\uDE1F\\uDE21\\uDE22\\uDE24\\uDE27\\uDE29-\\uDE32\\uDE34-\\uDE37\\uDE39\\uDE3B\\uDE42\\uDE47\\uDE49\\uDE4B\\uDE4D-\\uDE4F\\uDE51\\uDE52\\uDE54\\uDE57\\uDE59\\uDE5B\\uDE5D\\uDE5F\\uDE61\\uDE62\\uDE64\\uDE67-\\uDE6A\\uDE6C-\\uDE72\\uDE74-\\uDE77\\uDE79-\\uDE7C\\uDE7E\\uDE80-\\uDE89\\uDE8B-\\uDE9B\\uDEA1-\\uDEA3\\uDEA5-\\uDEA9\\uDEAB-\\uDEBB]|\\uD869[\\uDC00-\\uDEDF\\uDF00-\\uDFFF]|\\uD86D[\\uDC00-\\uDF38\\uDF40-\\uDFFF]|\\uD86E[\\uDC00-\\uDC1D\\uDC20-\\uDFFF]|\\uD873[\\uDC00-\\uDEA1\\uDEB0-\\uDFFF]|\\uD87A[\\uDC00-\\uDFE0]|\\uD87E[\\uDC00-\\uDE1D]|\\uD884[\\uDC00-\\uDF4A])\\S*/g;\n\n/**\n * Transforms text to title case.\n * Capitalizes the first letter of each word and transforms the\n * rest of the word to lower case.\n * Words are delimited by any whitespace character, such as a space, tab, or line-feed character.\n *\n * @see {@link LowerCasePipe}\n * @see {@link UpperCasePipe}\n *\n * @usageNotes\n * The following example shows the result of transforming various strings into title case.\n *\n * <code-example path=\"common/pipes/ts/titlecase_pipe.ts\" region='TitleCasePipe'></code-example>\n *\n * @ngModule CommonModule\n * @publicApi\n */\n@Pipe({\n  name: 'titlecase',\n  standalone: true,\n})\nexport class TitleCasePipe implements PipeTransform {\n  /**\n   * @param value The string to transform to title case.\n   */\n  transform(value: string): string;\n  transform(value: null | undefined): null;\n  transform(value: string | null | undefined): string | null;\n  transform(value: string | null | undefined): string | null {\n    if (value == null) return null;\n    if (typeof value !== 'string') {\n      throw invalidPipeArgumentError(TitleCasePipe, value);\n    }\n\n    return value.replace(\n      unicodeWordMatch,\n      (txt) => txt[0].toUpperCase() + txt.slice(1).toLowerCase(),\n    );\n  }\n}\n\n/**\n * Transforms text to all upper case.\n * @see {@link LowerCasePipe}\n * @see {@link TitleCasePipe}\n *\n * @ngModule CommonModule\n * @publicApi\n */\n@Pipe({\n  name: 'uppercase',\n  standalone: true,\n})\nexport class UpperCasePipe implements PipeTransform {\n  /**\n   * @param value The string to transform to upper case.\n   */\n  transform(value: string): string;\n  transform(value: null | undefined): null;\n  transform(value: string | null | undefined): string | null;\n  transform(value: string | null | undefined): string | null {\n    if (value == null) return null;\n    if (typeof value !== 'string') {\n      throw invalidPipeArgumentError(UpperCasePipe, value);\n    }\n    return value.toUpperCase();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * An interface that describes the date pipe configuration, which can be provided using the\n * `DATE_PIPE_DEFAULT_OPTIONS` token.\n *\n * @see {@link DATE_PIPE_DEFAULT_OPTIONS}\n *\n * @publicApi\n */\nexport interface DatePipeConfig {\n  dateFormat?: string;\n  timezone?: string;\n}\n\n/**\n * The default date format of Angular date pipe, which corresponds to the following format:\n * `'MMM d,y'` (e.g. `Jun 15, 2015`)\n */\nexport const DEFAULT_DATE_FORMAT = 'mediumDate';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Inject, InjectionToken, LOCALE_ID, Optional, Pipe, PipeTransform} from '@angular/core';\n\nimport {formatDate} from '../i18n/format_date';\n\nimport {DatePipeConfig, DEFAULT_DATE_FORMAT} from './date_pipe_config';\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n\n/**\n * Optionally-provided default timezone to use for all instances of `DatePipe` (such as `'+0430'`).\n * If the value isn't provided, the `DatePipe` will use the end-user's local system timezone.\n *\n * @deprecated use DATE_PIPE_DEFAULT_OPTIONS token to configure DatePipe\n */\nexport const DATE_PIPE_DEFAULT_TIMEZONE = new InjectionToken<string>(\n  ngDevMode ? 'DATE_PIPE_DEFAULT_TIMEZONE' : '',\n);\n\n/**\n * DI token that allows to provide default configuration for the `DatePipe` instances in an\n * application. The value is an object which can include the following fields:\n * - `dateFormat`: configures the default date format. If not provided, the `DatePipe`\n * will use the 'mediumDate' as a value.\n * - `timezone`: configures the default timezone. If not provided, the `DatePipe` will\n * use the end-user's local system timezone.\n *\n * @see {@link DatePipeConfig}\n *\n * @usageNotes\n *\n * Various date pipe default values can be overwritten by providing this token with\n * the value that has this interface.\n *\n * For example:\n *\n * Override the default date format by providing a value using the token:\n * ```typescript\n * providers: [\n *   {provide: DATE_PIPE_DEFAULT_OPTIONS, useValue: {dateFormat: 'shortDate'}}\n * ]\n * ```\n *\n * Override the default timezone by providing a value using the token:\n * ```typescript\n * providers: [\n *   {provide: DATE_PIPE_DEFAULT_OPTIONS, useValue: {timezone: '-1200'}}\n * ]\n * ```\n */\nexport const DATE_PIPE_DEFAULT_OPTIONS = new InjectionToken<DatePipeConfig>(\n  ngDevMode ? 'DATE_PIPE_DEFAULT_OPTIONS' : '',\n);\n\n// clang-format off\n/**\n * @ngModule CommonModule\n * @description\n *\n * Formats a date value according to locale rules.\n *\n * `DatePipe` is executed only when it detects a pure change to the input value.\n * A pure change is either a change to a primitive input value\n * (such as `String`, `Number`, `Boolean`, or `Symbol`),\n * or a changed object reference (such as `Date`, `Array`, `Function`, or `Object`).\n *\n * Note that mutating a `Date` object does not cause the pipe to be rendered again.\n * To ensure that the pipe is executed, you must create a new `Date` object.\n *\n * Only the `en-US` locale data comes with Angular. To localize dates\n * in another language, you must import the corresponding locale data.\n * See the [I18n guide](guide/i18n-common-format-data-locale) for more information.\n *\n * The time zone of the formatted value can be specified either by passing it in as the second\n * parameter of the pipe, or by setting the default through the `DATE_PIPE_DEFAULT_OPTIONS`\n * injection token. The value that is passed in as the second parameter takes precedence over\n * the one defined using the injection token.\n *\n * @see {@link formatDate}\n *\n *\n * @usageNotes\n *\n * The result of this pipe is not reevaluated when the input is mutated. To avoid the need to\n * reformat the date on every change-detection cycle, treat the date as an immutable object\n * and change the reference when the pipe needs to run again.\n *\n * ### Pre-defined format options\n *\n * | Option        | Equivalent to                       | Examples (given in `en-US` locale)              |\n * |---------------|-------------------------------------|-------------------------------------------------|\n * | `'short'`     | `'M/d/yy, h:mm a'`                  | `6/15/15, 9:03 AM`                              |\n * | `'medium'`    | `'MMM d, y, h:mm:ss a'`             | `Jun 15, 2015, 9:03:01 AM`                      |\n * | `'long'`      | `'MMMM d, y, h:mm:ss a z'`          | `June 15, 2015 at 9:03:01 AM GMT+1`             |\n * | `'full'`      | `'EEEE, MMMM d, y, h:mm:ss a zzzz'` | `Monday, June 15, 2015 at 9:03:01 AM GMT+01:00` |\n * | `'shortDate'` | `'M/d/yy'`                          | `6/15/15`                                       |\n * | `'mediumDate'`| `'MMM d, y'`                        | `Jun 15, 2015`                                  |\n * | `'longDate'`  | `'MMMM d, y'`                       | `June 15, 2015`                                 |\n * | `'fullDate'`  | `'EEEE, MMMM d, y'`                 | `Monday, June 15, 2015`                         |\n * | `'shortTime'` | `'h:mm a'`                          | `9:03 AM`                                       |\n * | `'mediumTime'`| `'h:mm:ss a'`                       | `9:03:01 AM`                                    |\n * | `'longTime'`  | `'h:mm:ss a z'`                     | `9:03:01 AM GMT+1`                              |\n * | `'fullTime'`  | `'h:mm:ss a zzzz'`                  | `9:03:01 AM GMT+01:00`                          |\n *\n * ### Custom format options\n *\n * You can construct a format string using symbols to specify the components\n * of a date-time value, as described in the following table.\n * Format details depend on the locale.\n * Fields marked with (*) are only available in the extra data set for the given locale.\n *\n *  | Field type              | Format      | Description                                                   | Example Value                                              |\n *  |-------------------------|-------------|---------------------------------------------------------------|------------------------------------------------------------|\n *  | Era                     | G, GG & GGG | Abbreviated                                                   | AD                                                         |\n *  |                         | GGGG        | Wide                                                          | Anno Domini                                                |\n *  |                         | GGGGG       | Narrow                                                        | A                                                          |\n *  | Year                    | y           | Numeric: minimum digits                                       | 2, 20, 201, 2017, 20173                                    |\n *  |                         | yy          | Numeric: 2 digits + zero padded                               | 02, 20, 01, 17, 73                                         |\n *  |                         | yyy         | Numeric: 3 digits + zero padded                               | 002, 020, 201, 2017, 20173                                 |\n *  |                         | yyyy        | Numeric: 4 digits or more + zero padded                       | 0002, 0020, 0201, 2017, 20173                              |\n *  | ISO Week-numbering year | Y           | Numeric: minimum digits                                       | 2, 20, 201, 2017, 20173                                    |\n *  |                         | YY          | Numeric: 2 digits + zero padded                               | 02, 20, 01, 17, 73                                         |\n *  |                         | YYY         | Numeric: 3 digits + zero padded                               | 002, 020, 201, 2017, 20173                                 |\n *  |                         | YYYY        | Numeric: 4 digits or more + zero padded                       | 0002, 0020, 0201, 2017, 20173                              |\n *  | Month                   | M           | Numeric: 1 digit                                              | 9, 12                                                      |\n *  |                         | MM          | Numeric: 2 digits + zero padded                               | 09, 12                                                     |\n *  |                         | MMM         | Abbreviated                                                   | Sep                                                        |\n *  |                         | MMMM        | Wide                                                          | September                                                  |\n *  |                         | MMMMM       | Narrow                                                        | S                                                          |\n *  | Month standalone        | L           | Numeric: 1 digit                                              | 9, 12                                                      |\n *  |                         | LL          | Numeric: 2 digits + zero padded                               | 09, 12                                                     |\n *  |                         | LLL         | Abbreviated                                                   | Sep                                                        |\n *  |                         | LLLL        | Wide                                                          | September                                                  |\n *  |                         | LLLLL       | Narrow                                                        | S                                                          |\n *  | ISO Week of year        | w           | Numeric: minimum digits                                       | 1... 53                                                    |\n *  |                         | ww          | Numeric: 2 digits + zero padded                               | 01... 53                                                   |\n *  | Week of month           | W           | Numeric: 1 digit                                              | 1... 5                                                     |\n *  | Day of month            | d           | Numeric: minimum digits                                       | 1                                                          |\n *  |                         | dd          | Numeric: 2 digits + zero padded                               | 01                                                         |\n *  | Week day                | E, EE & EEE | Abbreviated                                                   | Tue                                                        |\n *  |                         | EEEE        | Wide                                                          | Tuesday                                                    |\n *  |                         | EEEEE       | Narrow                                                        | T                                                          |\n *  |                         | EEEEEE      | Short                                                         | Tu                                                         |\n *  | Week day standalone     | c, cc       | Numeric: 1 digit                                              | 2                                                          |\n *  |                         | ccc         | Abbreviated                                                   | Tue                                                        |\n *  |                         | cccc        | Wide                                                          | Tuesday                                                    |\n *  |                         | ccccc       | Narrow                                                        | T                                                          |\n *  |                         | cccccc      | Short                                                         | Tu                                                         |\n *  | Period                  | a, aa & aaa | Abbreviated                                                   | am/pm or AM/PM                                             |\n *  |                         | aaaa        | Wide (fallback to `a` when missing)                           | ante meridiem/post meridiem                                |\n *  |                         | aaaaa       | Narrow                                                        | a/p                                                        |\n *  | Period*                 | B, BB & BBB | Abbreviated                                                   | mid.                                                       |\n *  |                         | BBBB        | Wide                                                          | am, pm, midnight, noon, morning, afternoon, evening, night |\n *  |                         | BBBBB       | Narrow                                                        | md                                                         |\n *  | Period standalone*      | b, bb & bbb | Abbreviated                                                   | mid.                                                       |\n *  |                         | bbbb        | Wide                                                          | am, pm, midnight, noon, morning, afternoon, evening, night |\n *  |                         | bbbbb       | Narrow                                                        | md                                                         |\n *  | Hour 1-12               | h           | Numeric: minimum digits                                       | 1, 12                                                      |\n *  |                         | hh          | Numeric: 2 digits + zero padded                               | 01, 12                                                     |\n *  | Hour 0-23               | H           | Numeric: minimum digits                                       | 0, 23                                                      |\n *  |                         | HH          | Numeric: 2 digits + zero padded                               | 00, 23                                                     |\n *  | Minute                  | m           | Numeric: minimum digits                                       | 8, 59                                                      |\n *  |                         | mm          | Numeric: 2 digits + zero padded                               | 08, 59                                                     |\n *  | Second                  | s           | Numeric: minimum digits                                       | 0... 59                                                    |\n *  |                         | ss          | Numeric: 2 digits + zero padded                               | 00... 59                                                   |\n *  | Fractional seconds      | S           | Numeric: 1 digit                                              | 0... 9                                                     |\n *  |                         | SS          | Numeric: 2 digits + zero padded                               | 00... 99                                                   |\n *  |                         | SSS         | Numeric: 3 digits + zero padded (= milliseconds)              | 000... 999                                                 |\n *  | Zone                    | z, zz & zzz | Short specific non location format (fallback to O)            | GMT-8                                                      |\n *  |                         | zzzz        | Long specific non location format (fallback to OOOO)          | GMT-08:00                                                  |\n *  |                         | Z, ZZ & ZZZ | ISO8601 basic format                                          | -0800                                                      |\n *  |                         | ZZZZ        | Long localized GMT format                                     | GMT-8:00                                                   |\n *  |                         | ZZZZZ       | ISO8601 extended format + Z indicator for offset 0 (= XXXXX)  | -08:00                                                     |\n *  |                         | O, OO & OOO | Short localized GMT format                                    | GMT-8                                                      |\n *  |                         | OOOO        | Long localized GMT format                                     | GMT-08:00                                                  |\n *\n *\n * ### Format examples\n *\n * These examples transform a date into various formats,\n * assuming that `dateObj` is a JavaScript `Date` object for\n * year: 2015, month: 6, day: 15, hour: 21, minute: 43, second: 11,\n * given in the local time for the `en-US` locale.\n *\n * ```\n * {{ dateObj | date }}               // output is 'Jun 15, 2015'\n * {{ dateObj | date:'medium' }}      // output is 'Jun 15, 2015, 9:43:11 PM'\n * {{ dateObj | date:'shortTime' }}   // output is '9:43 PM'\n * {{ dateObj | date:'mm:ss' }}       // output is '43:11'\n * {{ dateObj | date:\"MMM dd, yyyy 'at' hh:mm a\" }}  // output is 'Jun 15, 2015 at 09:43 PM'\n * ```\n *\n * ### Usage example\n *\n * The following component uses a date pipe to display the current date in different formats.\n *\n * ```\n * @Component({\n *  selector: 'date-pipe',\n *  template: `<div>\n *    <p>Today is {{today | date}}</p>\n *    <p>Or if you prefer, {{today | date:'fullDate'}}</p>\n *    <p>The time is {{today | date:'h:mm a z'}}</p>\n *  </div>`\n * })\n * // Get the current date and time as a date-time value.\n * export class DatePipeComponent {\n *   today: number = Date.now();\n * }\n * ```\n *\n * @publicApi\n */\n// clang-format on\n@Pipe({\n  name: 'date',\n  standalone: true,\n})\nexport class DatePipe implements PipeTransform {\n  constructor(\n    @Inject(LOCALE_ID) private locale: string,\n    @Inject(DATE_PIPE_DEFAULT_TIMEZONE) @Optional() private defaultTimezone?: string | null,\n    @Inject(DATE_PIPE_DEFAULT_OPTIONS) @Optional() private defaultOptions?: DatePipeConfig | null,\n  ) {}\n\n  /**\n   * @param value The date expression: a `Date` object,  a number\n   * (milliseconds since UTC epoch), or an ISO string (https://www.w3.org/TR/NOTE-datetime).\n   * @param format The date/time components to include, using predefined options or a\n   * custom format string.  When not provided, the `DatePipe` looks for the value using the\n   * `DATE_PIPE_DEFAULT_OPTIONS` injection token (and reads the `dateFormat` property).\n   * If the token is not configured, the `mediumDate` is used as a value.\n   * @param timezone A timezone offset (such as `'+0430'`), or a standard UTC/GMT, or continental US\n   * timezone abbreviation. When not provided, the `DatePipe` looks for the value using the\n   * `DATE_PIPE_DEFAULT_OPTIONS` injection token (and reads the `timezone` property). If the token\n   * is not configured, the end-user's local system timezone is used as a value.\n   * @param locale A locale code for the locale format rules to use.\n   * When not supplied, uses the value of `LOCALE_ID`, which is `en-US` by default.\n   * See [Setting your app locale](guide/i18n-common-locale-id).\n   *\n   * @see {@link DATE_PIPE_DEFAULT_OPTIONS}\n   *\n   * @returns A date string in the desired format.\n   */\n  transform(\n    value: Date | string | number,\n    format?: string,\n    timezone?: string,\n    locale?: string,\n  ): string | null;\n  transform(value: null | undefined, format?: string, timezone?: string, locale?: string): null;\n  transform(\n    value: Date | string | number | null | undefined,\n    format?: string,\n    timezone?: string,\n    locale?: string,\n  ): string | null;\n  transform(\n    value: Date | string | number | null | undefined,\n    format?: string,\n    timezone?: string,\n    locale?: string,\n  ): string | null {\n    if (value == null || value === '' || value !== value) return null;\n\n    try {\n      const _format = format ?? this.defaultOptions?.dateFormat ?? DEFAULT_DATE_FORMAT;\n      const _timezone =\n        timezone ?? this.defaultOptions?.timezone ?? this.defaultTimezone ?? undefined;\n      return formatDate(value, _format, locale || this.locale, _timezone);\n    } catch (error) {\n      throw invalidPipeArgumentError(DatePipe, (error as Error).message);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Pipe, PipeTransform} from '@angular/core';\n\nimport {getPluralCategory, NgLocalization} from '../i18n/localization';\n\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n\nconst _INTERPOLATION_REGEXP: RegExp = /#/g;\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Maps a value to a string that pluralizes the value according to locale rules.\n *\n * @usageNotes\n *\n * ### Example\n *\n * {@example common/pipes/ts/i18n_pipe.ts region='I18nPluralPipeComponent'}\n *\n * @publicApi\n */\n@Pipe({\n  name: 'i18nPlural',\n  standalone: true,\n})\nexport class I18nPluralPipe implements PipeTransform {\n  constructor(private _localization: NgLocalization) {}\n\n  /**\n   * @param value the number to be formatted\n   * @param pluralMap an object that mimics the ICU format, see\n   * https://unicode-org.github.io/icu/userguide/format_parse/messages/.\n   * @param locale a `string` defining the locale to use (uses the current {@link LOCALE_ID} by\n   * default).\n   */\n  transform(\n    value: number | null | undefined,\n    pluralMap: {[count: string]: string},\n    locale?: string,\n  ): string {\n    if (value == null) return '';\n\n    if (typeof pluralMap !== 'object' || pluralMap === null) {\n      throw invalidPipeArgumentError(I18nPluralPipe, pluralMap);\n    }\n\n    const key = getPluralCategory(value, Object.keys(pluralMap), this._localization, locale);\n\n    return pluralMap[key].replace(_INTERPOLATION_REGEXP, value.toString());\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Pipe, PipeTransform} from '@angular/core';\n\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Generic selector that displays the string that matches the current value.\n *\n * If none of the keys of the `mapping` match the `value`, then the content\n * of the `other` key is returned when present, otherwise an empty string is returned.\n *\n * @usageNotes\n *\n * ### Example\n *\n * {@example common/pipes/ts/i18n_pipe.ts region='I18nSelectPipeComponent'}\n *\n * @publicApi\n */\n@Pipe({\n  name: 'i18nSelect',\n  standalone: true,\n})\nexport class I18nSelectPipe implements PipeTransform {\n  /**\n   * @param value a string to be internationalized.\n   * @param mapping an object that indicates the text that should be displayed\n   * for different values of the provided `value`.\n   */\n  transform(value: string | null | undefined, mapping: {[key: string]: string}): string {\n    if (value == null) return '';\n\n    if (typeof mapping !== 'object' || typeof value !== 'string') {\n      throw invalidPipeArgumentError(I18nSelectPipe, mapping);\n    }\n\n    if (mapping.hasOwnProperty(value)) {\n      return mapping[value];\n    }\n\n    if (mapping.hasOwnProperty('other')) {\n      return mapping['other'];\n    }\n\n    return '';\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Pipe, PipeTransform} from '@angular/core';\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Converts a value into its JSON-format representation.  Useful for debugging.\n *\n * @usageNotes\n *\n * The following component uses a JSON pipe to convert an object\n * to JSON format, and displays the string in both formats for comparison.\n *\n * {@example common/pipes/ts/json_pipe.ts region='JsonPipe'}\n *\n * @publicApi\n */\n@Pipe({\n  name: 'json',\n  pure: false,\n  standalone: true,\n})\nexport class JsonPipe implements PipeTransform {\n  /**\n   * @param value A value of any type to convert into a JSON-format string.\n   */\n  transform(value: any): string {\n    return JSON.stringify(value, null, 2);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  KeyValueChangeRecord,\n  KeyValueChanges,\n  KeyValueDiffer,\n  KeyValueDiffers,\n  Pipe,\n  PipeTransform,\n} from '@angular/core';\n\nfunction makeKeyValuePair<K, V>(key: K, value: V): KeyValue<K, V> {\n  return {key: key, value: value};\n}\n\n/**\n * A key value pair.\n * Usually used to represent the key value pairs from a Map or Object.\n *\n * @publicApi\n */\nexport interface KeyValue<K, V> {\n  key: K;\n  value: V;\n}\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Transforms Object or Map into an array of key value pairs.\n *\n * The output array will be ordered by keys.\n * By default the comparator will be by Unicode point value.\n * You can optionally pass a compareFn if your keys are complex types.\n *\n * @usageNotes\n * ### Examples\n *\n * This examples show how an Object or a Map can be iterated by ngFor with the use of this\n * keyvalue pipe.\n *\n * {@example common/pipes/ts/keyvalue_pipe.ts region='KeyValuePipe'}\n *\n * @publicApi\n */\n@Pipe({\n  name: 'keyvalue',\n  pure: false,\n  standalone: true,\n})\nexport class KeyValuePipe implements PipeTransform {\n  constructor(private readonly differs: KeyValueDiffers) {}\n\n  private differ!: KeyValueDiffer<any, any>;\n  private keyValues: Array<KeyValue<any, any>> = [];\n  private compareFn: (a: KeyValue<any, any>, b: KeyValue<any, any>) => number = defaultComparator;\n\n  /*\n   * NOTE: when the `input` value is a simple Record<K, V> object, the keys are extracted with\n   * Object.keys(). This means that even if the `input` type is Record<number, V> the keys are\n   * compared/returned as `string`s.\n   */\n  transform<K, V>(\n    input: ReadonlyMap<K, V>,\n    compareFn?: (a: KeyValue<K, V>, b: KeyValue<K, V>) => number,\n  ): Array<KeyValue<K, V>>;\n  transform<K extends number, V>(\n    input: Record<K, V>,\n    compareFn?: (a: KeyValue<string, V>, b: KeyValue<string, V>) => number,\n  ): Array<KeyValue<string, V>>;\n  transform<K extends string, V>(\n    input: Record<K, V> | ReadonlyMap<K, V>,\n    compareFn?: (a: KeyValue<K, V>, b: KeyValue<K, V>) => number,\n  ): Array<KeyValue<K, V>>;\n  transform(\n    input: null | undefined,\n    compareFn?: (a: KeyValue<unknown, unknown>, b: KeyValue<unknown, unknown>) => number,\n  ): null;\n  transform<K, V>(\n    input: ReadonlyMap<K, V> | null | undefined,\n    compareFn?: (a: KeyValue<K, V>, b: KeyValue<K, V>) => number,\n  ): Array<KeyValue<K, V>> | null;\n  transform<K extends number, V>(\n    input: Record<K, V> | null | undefined,\n    compareFn?: (a: KeyValue<string, V>, b: KeyValue<string, V>) => number,\n  ): Array<KeyValue<string, V>> | null;\n  transform<K extends string, V>(\n    input: Record<K, V> | ReadonlyMap<K, V> | null | undefined,\n    compareFn?: (a: KeyValue<K, V>, b: KeyValue<K, V>) => number,\n  ): Array<KeyValue<K, V>> | null;\n  transform<K, V>(\n    input: undefined | null | {[key: string]: V; [key: number]: V} | ReadonlyMap<K, V>,\n    compareFn: (a: KeyValue<K, V>, b: KeyValue<K, V>) => number = defaultComparator,\n  ): Array<KeyValue<K, V>> | null {\n    if (!input || (!(input instanceof Map) && typeof input !== 'object')) {\n      return null;\n    }\n\n    // make a differ for whatever type we've been passed in\n    this.differ ??= this.differs.find(input).create();\n\n    const differChanges: KeyValueChanges<K, V> | null = this.differ.diff(input as any);\n    const compareFnChanged = compareFn !== this.compareFn;\n\n    if (differChanges) {\n      this.keyValues = [];\n      differChanges.forEachItem((r: KeyValueChangeRecord<K, V>) => {\n        this.keyValues.push(makeKeyValuePair(r.key, r.currentValue!));\n      });\n    }\n    if (differChanges || compareFnChanged) {\n      this.keyValues.sort(compareFn);\n      this.compareFn = compareFn;\n    }\n    return this.keyValues;\n  }\n}\n\nexport function defaultComparator<K, V>(\n  keyValueA: KeyValue<K, V>,\n  keyValueB: KeyValue<K, V>,\n): number {\n  const a = keyValueA.key;\n  const b = keyValueB.key;\n  // if same exit with 0;\n  if (a === b) return 0;\n  // make sure that undefined are at the end of the sort.\n  if (a === undefined) return 1;\n  if (b === undefined) return -1;\n  // make sure that nulls are at the end of the sort.\n  if (a === null) return 1;\n  if (b === null) return -1;\n  if (typeof a == 'string' && typeof b == 'string') {\n    return a < b ? -1 : 1;\n  }\n  if (typeof a == 'number' && typeof b == 'number') {\n    return a - b;\n  }\n  if (typeof a == 'boolean' && typeof b == 'boolean') {\n    return a < b ? -1 : 1;\n  }\n  // `a` and `b` are of different types. Compare their string values.\n  const aString = String(a);\n  const bString = String(b);\n  return aString == bString ? 0 : aString < bString ? -1 : 1;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {DEFAULT_CURRENCY_CODE, Inject, LOCALE_ID, Pipe, PipeTransform} from '@angular/core';\n\nimport {formatCurrency, formatNumber, formatPercent} from '../i18n/format_number';\nimport {getCurrencySymbol} from '../i18n/locale_data_api';\n\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Formats a value according to digit options and locale rules.\n * Locale determines group sizing and separator,\n * decimal point character, and other locale-specific configurations.\n *\n * @see {@link formatNumber}\n *\n * @usageNotes\n *\n * ### digitsInfo\n *\n * The value's decimal representation is specified by the `digitsInfo`\n * parameter, written in the following format:<br>\n *\n * ```\n * {minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}\n * ```\n *\n *  - `minIntegerDigits`:\n * The minimum number of integer digits before the decimal point.\n * Default is 1.\n *\n * - `minFractionDigits`:\n * The minimum number of digits after the decimal point.\n * Default is 0.\n *\n *  - `maxFractionDigits`:\n * The maximum number of digits after the decimal point.\n * Default is 3.\n *\n * If the formatted value is truncated it will be rounded using the \"to-nearest\" method:\n *\n * ```\n * {{3.6 | number: '1.0-0'}}\n * <!--will output '4'-->\n *\n * {{-3.6 | number:'1.0-0'}}\n * <!--will output '-4'-->\n * ```\n *\n * ### locale\n *\n * `locale` will format a value according to locale rules.\n * Locale determines group sizing and separator,\n * decimal point character, and other locale-specific configurations.\n *\n * When not supplied, uses the value of `LOCALE_ID`, which is `en-US` by default.\n *\n * See [Setting your app locale](guide/i18n-common-locale-id).\n *\n * ### Example\n *\n * The following code shows how the pipe transforms values\n * according to various format specifications,\n * where the caller's default locale is `en-US`.\n *\n * <code-example path=\"common/pipes/ts/number_pipe.ts\" region='NumberPipe'></code-example>\n *\n * @publicApi\n */\n@Pipe({\n  name: 'number',\n  standalone: true,\n})\nexport class DecimalPipe implements PipeTransform {\n  constructor(@Inject(LOCALE_ID) private _locale: string) {}\n\n  transform(value: number | string, digitsInfo?: string, locale?: string): string | null;\n  transform(value: null | undefined, digitsInfo?: string, locale?: string): null;\n  transform(\n    value: number | string | null | undefined,\n    digitsInfo?: string,\n    locale?: string,\n  ): string | null;\n  /**\n   * @param value The value to be formatted.\n   * @param digitsInfo Sets digit and decimal representation.\n   * [See more](#digitsinfo).\n   * @param locale Specifies what locale format rules to use.\n   * [See more](#locale).\n   */\n  transform(\n    value: number | string | null | undefined,\n    digitsInfo?: string,\n    locale?: string,\n  ): string | null {\n    if (!isValue(value)) return null;\n\n    locale ||= this._locale;\n\n    try {\n      const num = strToNumber(value);\n      return formatNumber(num, locale, digitsInfo);\n    } catch (error) {\n      throw invalidPipeArgumentError(DecimalPipe, (error as Error).message);\n    }\n  }\n}\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Transforms a number to a percentage\n * string, formatted according to locale rules that determine group sizing and\n * separator, decimal-point character, and other locale-specific\n * configurations.\n *\n * @see {@link formatPercent}\n *\n * @usageNotes\n * The following code shows how the pipe transforms numbers\n * into text strings, according to various format specifications,\n * where the caller's default locale is `en-US`.\n *\n * <code-example path=\"common/pipes/ts/percent_pipe.ts\" region='PercentPipe'></code-example>\n *\n * @publicApi\n */\n@Pipe({\n  name: 'percent',\n  standalone: true,\n})\nexport class PercentPipe implements PipeTransform {\n  constructor(@Inject(LOCALE_ID) private _locale: string) {}\n\n  transform(value: number | string, digitsInfo?: string, locale?: string): string | null;\n  transform(value: null | undefined, digitsInfo?: string, locale?: string): null;\n  transform(\n    value: number | string | null | undefined,\n    digitsInfo?: string,\n    locale?: string,\n  ): string | null;\n  /**\n   *\n   * @param value The number to be formatted as a percentage.\n   * @param digitsInfo Decimal representation options, specified by a string\n   * in the following format:<br>\n   * <code>{minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}</code>.\n   *   - `minIntegerDigits`: The minimum number of integer digits before the decimal point.\n   * Default is `1`.\n   *   - `minFractionDigits`: The minimum number of digits after the decimal point.\n   * Default is `0`.\n   *   - `maxFractionDigits`: The maximum number of digits after the decimal point.\n   * Default is `0`.\n   * @param locale A locale code for the locale format rules to use.\n   * When not supplied, uses the value of `LOCALE_ID`, which is `en-US` by default.\n   * See [Setting your app locale](guide/i18n-common-locale-id).\n   */\n  transform(\n    value: number | string | null | undefined,\n    digitsInfo?: string,\n    locale?: string,\n  ): string | null {\n    if (!isValue(value)) return null;\n    locale ||= this._locale;\n    try {\n      const num = strToNumber(value);\n      return formatPercent(num, locale, digitsInfo);\n    } catch (error) {\n      throw invalidPipeArgumentError(PercentPipe, (error as Error).message);\n    }\n  }\n}\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Transforms a number to a currency string, formatted according to locale rules\n * that determine group sizing and separator, decimal-point character,\n * and other locale-specific configurations.\n *\n *\n * @see {@link getCurrencySymbol}\n * @see {@link formatCurrency}\n *\n * @usageNotes\n * The following code shows how the pipe transforms numbers\n * into text strings, according to various format specifications,\n * where the caller's default locale is `en-US`.\n *\n * <code-example path=\"common/pipes/ts/currency_pipe.ts\" region='CurrencyPipe'></code-example>\n *\n * @publicApi\n */\n@Pipe({\n  name: 'currency',\n  standalone: true,\n})\nexport class CurrencyPipe implements PipeTransform {\n  constructor(\n    @Inject(LOCALE_ID) private _locale: string,\n    @Inject(DEFAULT_CURRENCY_CODE) private _defaultCurrencyCode: string = 'USD',\n  ) {}\n\n  transform(\n    value: number | string,\n    currencyCode?: string,\n    display?: 'code' | 'symbol' | 'symbol-narrow' | string | boolean,\n    digitsInfo?: string,\n    locale?: string,\n  ): string | null;\n  transform(\n    value: null | undefined,\n    currencyCode?: string,\n    display?: 'code' | 'symbol' | 'symbol-narrow' | string | boolean,\n    digitsInfo?: string,\n    locale?: string,\n  ): null;\n  transform(\n    value: number | string | null | undefined,\n    currencyCode?: string,\n    display?: 'code' | 'symbol' | 'symbol-narrow' | string | boolean,\n    digitsInfo?: string,\n    locale?: string,\n  ): string | null;\n  /**\n   *\n   * @param value The number to be formatted as currency.\n   * @param currencyCode The [ISO 4217](https://en.wikipedia.org/wiki/ISO_4217) currency code,\n   * such as `USD` for the US dollar and `EUR` for the euro. The default currency code can be\n   * configured using the `DEFAULT_CURRENCY_CODE` injection token.\n   * @param display The format for the currency indicator. One of the following:\n   *   - `code`: Show the code (such as `USD`).\n   *   - `symbol`(default): Show the symbol (such as `$`).\n   *   - `symbol-narrow`: Use the narrow symbol for locales that have two symbols for their\n   * currency.\n   * For example, the Canadian dollar CAD has the symbol `CA$` and the symbol-narrow `$`. If the\n   * locale has no narrow symbol, uses the standard symbol for the locale.\n   *   - String: Use the given string value instead of a code or a symbol.\n   * For example, an empty string will suppress the currency & symbol.\n   *   - Boolean (marked deprecated in v5): `true` for symbol and false for `code`.\n   *\n   * @param digitsInfo Decimal representation options, specified by a string\n   * in the following format:<br>\n   * <code>{minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}</code>.\n   *   - `minIntegerDigits`: The minimum number of integer digits before the decimal point.\n   * Default is `1`.\n   *   - `minFractionDigits`: The minimum number of digits after the decimal point.\n   * Default is `2`.\n   *   - `maxFractionDigits`: The maximum number of digits after the decimal point.\n   * Default is `2`.\n   * If not provided, the number will be formatted with the proper amount of digits,\n   * depending on what the [ISO 4217](https://en.wikipedia.org/wiki/ISO_4217) specifies.\n   * For example, the Canadian dollar has 2 digits, whereas the Chilean peso has none.\n   * @param locale A locale code for the locale format rules to use.\n   * When not supplied, uses the value of `LOCALE_ID`, which is `en-US` by default.\n   * See [Setting your app locale](guide/i18n-common-locale-id).\n   */\n  transform(\n    value: number | string | null | undefined,\n    currencyCode: string = this._defaultCurrencyCode,\n    display: 'code' | 'symbol' | 'symbol-narrow' | string | boolean = 'symbol',\n    digitsInfo?: string,\n    locale?: string,\n  ): string | null {\n    if (!isValue(value)) return null;\n\n    locale ||= this._locale;\n\n    if (typeof display === 'boolean') {\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && <any>console && <any>console.warn) {\n        console.warn(\n          `Warning: the currency pipe has been changed in Angular v5. The symbolDisplay option (third parameter) is now a string instead of a boolean. The accepted values are \"code\", \"symbol\" or \"symbol-narrow\".`,\n        );\n      }\n      display = display ? 'symbol' : 'code';\n    }\n\n    let currency: string = currencyCode || this._defaultCurrencyCode;\n    if (display !== 'code') {\n      if (display === 'symbol' || display === 'symbol-narrow') {\n        currency = getCurrencySymbol(currency, display === 'symbol' ? 'wide' : 'narrow', locale);\n      } else {\n        currency = display;\n      }\n    }\n\n    try {\n      const num = strToNumber(value);\n      return formatCurrency(num, locale, currency, currencyCode, digitsInfo);\n    } catch (error) {\n      throw invalidPipeArgumentError(CurrencyPipe, (error as Error).message);\n    }\n  }\n}\n\nfunction isValue(value: number | string | null | undefined): value is number | string {\n  return !(value == null || value === '' || value !== value);\n}\n\n/**\n * Transforms a string into a number (if needed).\n */\nfunction strToNumber(value: number | string): number {\n  // Convert strings to numbers\n  if (typeof value === 'string' && !isNaN(Number(value) - parseFloat(value))) {\n    return Number(value);\n  }\n  if (typeof value !== 'number') {\n    throw new Error(`${value} is not a number`);\n  }\n  return value;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Pipe, PipeTransform} from '@angular/core';\n\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Creates a new `Array` or `String` containing a subset (slice) of the elements.\n *\n * @usageNotes\n *\n * All behavior is based on the expected behavior of the JavaScript API `Array.prototype.slice()`\n * and `String.prototype.slice()`.\n *\n * When operating on an `Array`, the returned `Array` is always a copy even when all\n * the elements are being returned.\n *\n * When operating on a blank value, the pipe returns the blank value.\n *\n * ### List Example\n *\n * This `ngFor` example:\n *\n * {@example common/pipes/ts/slice_pipe.ts region='SlicePipe_list'}\n *\n * produces the following:\n *\n * ```html\n * <li>b</li>\n * <li>c</li>\n * ```\n *\n * ### String Examples\n *\n * {@example common/pipes/ts/slice_pipe.ts region='SlicePipe_string'}\n *\n * @publicApi\n */\n@Pipe({\n  name: 'slice',\n  pure: false,\n  standalone: true,\n})\nexport class SlicePipe implements PipeTransform {\n  /**\n   * @param value a list or a string to be sliced.\n   * @param start the starting index of the subset to return:\n   *   - **a positive integer**: return the item at `start` index and all items after\n   *     in the list or string expression.\n   *   - **a negative integer**: return the item at `start` index from the end and all items after\n   *     in the list or string expression.\n   *   - **if positive and greater than the size of the expression**: return an empty list or\n   * string.\n   *   - **if negative and greater than the size of the expression**: return entire list or string.\n   * @param end the ending index of the subset to return:\n   *   - **omitted**: return all items until the end.\n   *   - **if positive**: return all items before `end` index of the list or string.\n   *   - **if negative**: return all items before `end` index from the end of the list or string.\n   */\n  transform<T>(value: ReadonlyArray<T>, start: number, end?: number): Array<T>;\n  transform(value: null | undefined, start: number, end?: number): null;\n  transform<T>(\n    value: ReadonlyArray<T> | null | undefined,\n    start: number,\n    end?: number,\n  ): Array<T> | null;\n  transform(value: string, start: number, end?: number): string;\n  transform(value: string | null | undefined, start: number, end?: number): string | null;\n  transform<T>(\n    value: ReadonlyArray<T> | string | null | undefined,\n    start: number,\n    end?: number,\n  ): Array<T> | string | null {\n    if (value == null) return null;\n\n    if (!this.supports(value)) {\n      throw invalidPipeArgumentError(SlicePipe, value);\n    }\n\n    return value.slice(start, end);\n  }\n\n  private supports(obj: any): boolean {\n    return typeof obj === 'string' || Array.isArray(obj);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * This module provides a set of common Pipes.\n */\nimport {AsyncPipe} from './async_pipe';\nimport {LowerCasePipe, TitleCasePipe, UpperCasePipe} from './case_conversion_pipes';\nimport {DATE_PIPE_DEFAULT_OPTIONS, DATE_PIPE_DEFAULT_TIMEZONE, DatePipe} from './date_pipe';\nimport {DatePipeConfig} from './date_pipe_config';\nimport {I18nPluralPipe} from './i18n_plural_pipe';\nimport {I18nSelectPipe} from './i18n_select_pipe';\nimport {JsonPipe} from './json_pipe';\nimport {KeyValue, KeyValuePipe} from './keyvalue_pipe';\nimport {<PERSON>urrencyPipe, DecimalPipe, PercentPipe} from './number_pipe';\nimport {SlicePipe} from './slice_pipe';\n\nexport {\n  AsyncPipe,\n  CurrencyPipe,\n  DATE_PIPE_DEFAULT_OPTIONS,\n  DATE_PIPE_DEFAULT_TIMEZONE,\n  DatePipe,\n  DatePipeConfig,\n  DecimalPipe,\n  I18nPluralPipe,\n  I18nSelectPipe,\n  JsonPipe,\n  KeyValue,\n  KeyValuePipe,\n  LowerCasePipe,\n  PercentPipe,\n  SlicePipe,\n  TitleCasePipe,\n  UpperCasePipe,\n};\n\n/**\n * A collection of Angular pipes that are likely to be used in each and every application.\n */\nexport const COMMON_PIPES = [\n  AsyncPipe,\n  UpperCasePipe,\n  LowerCasePipe,\n  JsonPipe,\n  SlicePipe,\n  DecimalPipe,\n  PercentPipe,\n  TitleCasePipe,\n  CurrencyPipe,\n  DatePipe,\n  I18nPluralPipe,\n  I18nSelectPipe,\n  KeyValuePipe,\n];\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\n\nimport {COMMON_DIRECTIVES} from './directives/index';\nimport {COMMON_PIPES} from './pipes/index';\n\n// Note: This does not contain the location providers,\n// as they need some platform specific implementations to work.\n/**\n * Exports all the basic Angular directives and pipes,\n * such as `NgIf`, `NgForOf`, `DecimalPipe`, and so on.\n * Re-exported by `BrowserModule`, which is included automatically in the root\n * `AppModule` when you create a new app with the CLI `new` command.\n *\n * @publicApi\n */\n@NgModule({\n  imports: [COMMON_DIRECTIVES, COMMON_PIPES],\n  exports: [COMMON_DIRECTIVES, COMMON_PIPES],\n})\nexport class CommonModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport const PLATFORM_BROWSER_ID = 'browser';\nexport const PLATFORM_SERVER_ID = 'server';\nexport const PLATFORM_WORKER_APP_ID = 'browserWorkerApp';\nexport const PLATFORM_WORKER_UI_ID = 'browserWorkerUi';\n\n/**\n * Returns whether a platform id represents a browser platform.\n * @publicApi\n */\nexport function isPlatformBrowser(platformId: Object): boolean {\n  return platformId === PLATFORM_BROWSER_ID;\n}\n\n/**\n * Returns whether a platform id represents a server platform.\n * @publicApi\n */\nexport function isPlatformServer(platformId: Object): boolean {\n  return platformId === PLATFORM_SERVER_ID;\n}\n\n/**\n * Returns whether a platform id represents a web worker app platform.\n * @publicApi\n * @deprecated This function serves no purpose since the removal of the Webworker platform. It will\n *     always return `false`.\n */\nexport function isPlatformWorkerApp(platformId: Object): boolean {\n  return platformId === PLATFORM_WORKER_APP_ID;\n}\n\n/**\n * Returns whether a platform id represents a web worker UI platform.\n * @publicApi\n * @deprecated This function serves no purpose since the removal of the Webworker platform. It will\n *     always return `false`.\n */\nexport function isPlatformWorkerUi(platformId: Object): boolean {\n  return platformId === PLATFORM_WORKER_UI_ID;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common package.\n */\n\nimport {Version} from '@angular/core';\n\n/**\n * @publicApi\n */\nexport const VERSION = new Version('17.3.12');\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {inject, PLATFORM_ID, ɵɵdefineInjectable} from '@angular/core';\n\nimport {DOCUMENT} from './dom_tokens';\nimport {isPlatformBrowser} from './platform_id';\n\n/**\n * Defines a scroll position manager. Implemented by `BrowserViewportScroller`.\n *\n * @publicApi\n */\nexport abstract class ViewportScroller {\n  // De-sugared tree-shakable injection\n  // See #23917\n  /** @nocollapse */\n  static ɵprov = /** @pureOrBreakMyCode */ ɵɵdefineInjectable({\n    token: ViewportScroller,\n    providedIn: 'root',\n    factory: () =>\n      isPlatformBrowser(inject(PLATFORM_ID))\n        ? new BrowserViewportScroller(inject(DOCUMENT), window)\n        : new NullViewportScroller(),\n  });\n\n  /**\n   * Configures the top offset used when scrolling to an anchor.\n   * @param offset A position in screen coordinates (a tuple with x and y values)\n   * or a function that returns the top offset position.\n   *\n   */\n  abstract setOffset(offset: [number, number] | (() => [number, number])): void;\n\n  /**\n   * Retrieves the current scroll position.\n   * @returns A position in screen coordinates (a tuple with x and y values).\n   */\n  abstract getScrollPosition(): [number, number];\n\n  /**\n   * Scrolls to a specified position.\n   * @param position A position in screen coordinates (a tuple with x and y values).\n   */\n  abstract scrollToPosition(position: [number, number]): void;\n\n  /**\n   * Scrolls to an anchor element.\n   * @param anchor The ID of the anchor element.\n   */\n  abstract scrollToAnchor(anchor: string): void;\n\n  /**\n   * Disables automatic scroll restoration provided by the browser.\n   * See also [window.history.scrollRestoration\n   * info](https://developers.google.com/web/updates/2015/09/history-api-scroll-restoration).\n   */\n  abstract setHistoryScrollRestoration(scrollRestoration: 'auto' | 'manual'): void;\n}\n\n/**\n * Manages the scroll position for a browser window.\n */\nexport class BrowserViewportScroller implements ViewportScroller {\n  private offset: () => [number, number] = () => [0, 0];\n\n  constructor(\n    private document: Document,\n    private window: Window,\n  ) {}\n\n  /**\n   * Configures the top offset used when scrolling to an anchor.\n   * @param offset A position in screen coordinates (a tuple with x and y values)\n   * or a function that returns the top offset position.\n   *\n   */\n  setOffset(offset: [number, number] | (() => [number, number])): void {\n    if (Array.isArray(offset)) {\n      this.offset = () => offset;\n    } else {\n      this.offset = offset;\n    }\n  }\n\n  /**\n   * Retrieves the current scroll position.\n   * @returns The position in screen coordinates.\n   */\n  getScrollPosition(): [number, number] {\n    return [this.window.scrollX, this.window.scrollY];\n  }\n\n  /**\n   * Sets the scroll position.\n   * @param position The new position in screen coordinates.\n   */\n  scrollToPosition(position: [number, number]): void {\n    this.window.scrollTo(position[0], position[1]);\n  }\n\n  /**\n   * Scrolls to an element and attempts to focus the element.\n   *\n   * Note that the function name here is misleading in that the target string may be an ID for a\n   * non-anchor element.\n   *\n   * @param target The ID of an element or name of the anchor.\n   *\n   * @see https://html.spec.whatwg.org/#the-indicated-part-of-the-document\n   * @see https://html.spec.whatwg.org/#scroll-to-fragid\n   */\n  scrollToAnchor(target: string): void {\n    const elSelected = findAnchorFromDocument(this.document, target);\n\n    if (elSelected) {\n      this.scrollToElement(elSelected);\n      // After scrolling to the element, the spec dictates that we follow the focus steps for the\n      // target. Rather than following the robust steps, simply attempt focus.\n      //\n      // @see https://html.spec.whatwg.org/#get-the-focusable-area\n      // @see https://developer.mozilla.org/en-US/docs/Web/API/HTMLOrForeignElement/focus\n      // @see https://html.spec.whatwg.org/#focusable-area\n      elSelected.focus();\n    }\n  }\n\n  /**\n   * Disables automatic scroll restoration provided by the browser.\n   */\n  setHistoryScrollRestoration(scrollRestoration: 'auto' | 'manual'): void {\n    this.window.history.scrollRestoration = scrollRestoration;\n  }\n\n  /**\n   * Scrolls to an element using the native offset and the specified offset set on this scroller.\n   *\n   * The offset can be used when we know that there is a floating header and scrolling naively to an\n   * element (ex: `scrollIntoView`) leaves the element hidden behind the floating header.\n   */\n  private scrollToElement(el: HTMLElement): void {\n    const rect = el.getBoundingClientRect();\n    const left = rect.left + this.window.pageXOffset;\n    const top = rect.top + this.window.pageYOffset;\n    const offset = this.offset();\n    this.window.scrollTo(left - offset[0], top - offset[1]);\n  }\n}\n\nfunction findAnchorFromDocument(document: Document, target: string): HTMLElement | null {\n  const documentResult = document.getElementById(target) || document.getElementsByName(target)[0];\n\n  if (documentResult) {\n    return documentResult;\n  }\n\n  // `getElementById` and `getElementsByName` won't pierce through the shadow DOM so we\n  // have to traverse the DOM manually and do the lookup through the shadow roots.\n  if (\n    typeof document.createTreeWalker === 'function' &&\n    document.body &&\n    typeof document.body.attachShadow === 'function'\n  ) {\n    const treeWalker = document.createTreeWalker(document.body, NodeFilter.SHOW_ELEMENT);\n    let currentNode = treeWalker.currentNode as HTMLElement | null;\n\n    while (currentNode) {\n      const shadowRoot = currentNode.shadowRoot;\n\n      if (shadowRoot) {\n        // Note that `ShadowRoot` doesn't support `getElementsByName`\n        // so we have to fall back to `querySelector`.\n        const result =\n          shadowRoot.getElementById(target) || shadowRoot.querySelector(`[name=\"${target}\"]`);\n        if (result) {\n          return result;\n        }\n      }\n\n      currentNode = treeWalker.nextNode() as HTMLElement | null;\n    }\n  }\n\n  return null;\n}\n\n/**\n * Provides an empty implementation of the viewport scroller.\n */\nexport class NullViewportScroller implements ViewportScroller {\n  /**\n   * Empty implementation\n   */\n  setOffset(offset: [number, number] | (() => [number, number])): void {}\n\n  /**\n   * Empty implementation\n   */\n  getScrollPosition(): [number, number] {\n    return [0, 0];\n  }\n\n  /**\n   * Empty implementation\n   */\n  scrollToPosition(position: [number, number]): void {}\n\n  /**\n   * Empty implementation\n   */\n  scrollToAnchor(anchor: string): void {}\n\n  /**\n   * Empty implementation\n   */\n  setHistoryScrollRestoration(scrollRestoration: 'auto' | 'manual'): void {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A wrapper around the `XMLHttpRequest` constructor.\n *\n * @publicApi\n */\nexport abstract class XhrFactory {\n  abstract build(): XMLHttpRequest;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Value (out of 100) of the requested quality for placeholder images.\n */\nexport const PLACEHOLDER_QUALITY = '20';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// Converts a string that represents a URL into a URL class instance.\nexport function getUrl(src: string, win: Window): URL {\n  // Don't use a base URL is the URL is absolute.\n  return isAbsoluteUrl(src) ? new URL(src) : new URL(src, win.location.href);\n}\n\n// Checks whether a URL is absolute (i.e. starts with `http://` or `https://`).\nexport function isAbsoluteUrl(src: string): boolean {\n  return /^https?:\\/\\//.test(src);\n}\n\n// Given a URL, extract the hostname part.\n// If a URL is a relative one - the URL is returned as is.\nexport function extractHostname(url: string): string {\n  return isAbsoluteUrl(url) ? new URL(url).hostname : url;\n}\n\nexport function isValidPath(path: unknown): boolean {\n  const isString = typeof path === 'string';\n\n  if (!isString || path.trim() === '') {\n    return false;\n  }\n\n  // Calling new URL() will throw if the path string is malformed\n  try {\n    const url = new URL(path);\n    return true;\n  } catch {\n    return false;\n  }\n}\n\nexport function normalizePath(path: string): string {\n  return path.endsWith('/') ? path.slice(0, -1) : path;\n}\n\nexport function normalizeSrc(src: string): string {\n  return src.startsWith('/') ? src.slice(1) : src;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {InjectionToken, Provider, ɵRuntimeError as RuntimeError} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../../../errors';\nimport {isAbsoluteUrl, isValidPath, normalizePath, normalizeSrc} from '../url';\n\n/**\n * Config options recognized by the image loader function.\n *\n * @see {@link ImageLoader}\n * @see {@link NgOptimizedImage}\n * @publicApi\n */\nexport interface ImageLoaderConfig {\n  /**\n   * Image file name to be added to the image request URL.\n   */\n  src: string;\n  /**\n   * Width of the requested image (to be used when generating srcset).\n   */\n  width?: number;\n  /**\n   * Whether the loader should generate a URL for a small image placeholder instead of a full-sized\n   * image.\n   */\n  isPlaceholder?: boolean;\n  /**\n   * Additional user-provided parameters for use by the ImageLoader.\n   */\n  loaderParams?: {[key: string]: any};\n}\n\n/**\n * Represents an image loader function. Image loader functions are used by the\n * NgOptimizedImage directive to produce full image URL based on the image name and its width.\n *\n * @publicApi\n */\nexport type ImageLoader = (config: ImageLoaderConfig) => string;\n\n/**\n * Noop image loader that does no transformation to the original src and just returns it as is.\n * This loader is used as a default one if more specific logic is not provided in an app config.\n *\n * @see {@link ImageLoader}\n * @see {@link NgOptimizedImage}\n */\nexport const noopImageLoader = (config: ImageLoaderConfig) => config.src;\n\n/**\n * Metadata about the image loader.\n */\nexport type ImageLoaderInfo = {\n  name: string;\n  testUrl: (url: string) => boolean;\n};\n\n/**\n * Injection token that configures the image loader function.\n *\n * @see {@link ImageLoader}\n * @see {@link NgOptimizedImage}\n * @publicApi\n */\nexport const IMAGE_LOADER = new InjectionToken<ImageLoader>(ngDevMode ? 'ImageLoader' : '', {\n  providedIn: 'root',\n  factory: () => noopImageLoader,\n});\n\n/**\n * Internal helper function that makes it easier to introduce custom image loaders for the\n * `NgOptimizedImage` directive. It is enough to specify a URL builder function to obtain full DI\n * configuration for a given loader: a DI token corresponding to the actual loader function, plus DI\n * tokens managing preconnect check functionality.\n * @param buildUrlFn a function returning a full URL based on loader's configuration\n * @param exampleUrls example of full URLs for a given loader (used in error messages)\n * @returns a set of DI providers corresponding to the configured image loader\n */\nexport function createImageLoader(\n  buildUrlFn: (path: string, config: ImageLoaderConfig) => string,\n  exampleUrls?: string[],\n) {\n  return function provideImageLoader(path: string) {\n    if (!isValidPath(path)) {\n      throwInvalidPathError(path, exampleUrls || []);\n    }\n\n    // The trailing / is stripped (if provided) to make URL construction (concatenation) easier in\n    // the individual loader functions.\n    path = normalizePath(path);\n\n    const loaderFn = (config: ImageLoaderConfig) => {\n      if (isAbsoluteUrl(config.src)) {\n        // Image loader functions expect an image file name (e.g. `my-image.png`)\n        // or a relative path + a file name (e.g. `/a/b/c/my-image.png`) as an input,\n        // so the final absolute URL can be constructed.\n        // When an absolute URL is provided instead - the loader can not\n        // build a final URL, thus the error is thrown to indicate that.\n        throwUnexpectedAbsoluteUrlError(path, config.src);\n      }\n\n      return buildUrlFn(path, {...config, src: normalizeSrc(config.src)});\n    };\n\n    const providers: Provider[] = [{provide: IMAGE_LOADER, useValue: loaderFn}];\n    return providers;\n  };\n}\n\nfunction throwInvalidPathError(path: unknown, exampleUrls: string[]): never {\n  throw new RuntimeError(\n    RuntimeErrorCode.INVALID_LOADER_ARGUMENTS,\n    ngDevMode &&\n      `Image loader has detected an invalid path (\\`${path}\\`). ` +\n        `To fix this, supply a path using one of the following formats: ${exampleUrls.join(\n          ' or ',\n        )}`,\n  );\n}\n\nfunction throwUnexpectedAbsoluteUrlError(path: string, url: string): never {\n  throw new RuntimeError(\n    RuntimeErrorCode.INVALID_LOADER_ARGUMENTS,\n    ngDevMode &&\n      `Image loader has detected a \\`<img>\\` tag with an invalid \\`ngSrc\\` attribute: ${url}. ` +\n        `This image loader expects \\`ngSrc\\` to be a relative URL - ` +\n        `however the provided value is an absolute URL. ` +\n        `To fix this, provide \\`ngSrc\\` as a path relative to the base URL ` +\n        `configured for this loader (\\`${path}\\`).`,\n  );\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {PLACEHOLDER_QUALITY} from './constants';\nimport {createImageLoader, ImageLoaderConfig} from './image_loader';\n\n/**\n * Function that generates an ImageLoader for [Cloudflare Image\n * Resizing](https://developers.cloudflare.com/images/image-resizing/) and turns it into an Angular\n * provider. Note: Cloudflare has multiple image products - this provider is specifically for\n * Cloudflare Image Resizing; it will not work with Cloudflare Images or Cloudflare Polish.\n *\n * @param path Your domain name, e.g. https://mysite.com\n * @returns Provider that provides an ImageLoader function\n *\n * @publicApi\n */\nexport const provideCloudflareLoader = createImageLoader(\n  createCloudflareUrl,\n  ngDevMode ? ['https://<ZONE>/cdn-cgi/image/<OPTIONS>/<SOURCE-IMAGE>'] : undefined,\n);\n\nfunction createCloudflareUrl(path: string, config: ImageLoaderConfig) {\n  let params = `format=auto`;\n  if (config.width) {\n    params += `,width=${config.width}`;\n  }\n\n  // When requesting a placeholder image we ask for a low quality image to reduce the load time.\n  if (config.isPlaceholder) {\n    params += `,quality=${PLACEHOLDER_QUALITY}`;\n  }\n\n  // Cloudflare image URLs format:\n  // https://developers.cloudflare.com/images/image-resizing/url-format/\n  return `${path}/cdn-cgi/image/${params}/${config.src}`;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {createImageLoader, ImageLoaderConfig, ImageLoaderInfo} from './image_loader';\n\n/**\n * Name and URL tester for Cloudinary.\n */\nexport const cloudinaryLoaderInfo: ImageLoaderInfo = {\n  name: 'Cloudinary',\n  testUrl: isCloudinaryUrl,\n};\n\nconst CLOUDINARY_LOADER_REGEX = /https?\\:\\/\\/[^\\/]+\\.cloudinary\\.com\\/.+/;\n/**\n * Tests whether a URL is from Cloudinary CDN.\n */\nfunction isCloudinaryUrl(url: string): boolean {\n  return CLOUDINARY_LOADER_REGEX.test(url);\n}\n\n/**\n * Function that generates an ImageLoader for Cloudinary and turns it into an Angular provider.\n *\n * @param path Base URL of your Cloudinary images\n * This URL should match one of the following formats:\n * https://res.cloudinary.com/mysite\n * https://mysite.cloudinary.com\n * https://subdomain.mysite.com\n * @returns Set of providers to configure the Cloudinary loader.\n *\n * @publicApi\n */\nexport const provideCloudinaryLoader = createImageLoader(\n  createCloudinaryUrl,\n  ngDevMode\n    ? [\n        'https://res.cloudinary.com/mysite',\n        'https://mysite.cloudinary.com',\n        'https://subdomain.mysite.com',\n      ]\n    : undefined,\n);\n\nfunction createCloudinaryUrl(path: string, config: ImageLoaderConfig) {\n  // Cloudinary image URLformat:\n  // https://cloudinary.com/documentation/image_transformations#transformation_url_structure\n  // Example of a Cloudinary image URL:\n  // https://res.cloudinary.com/mysite/image/upload/c_scale,f_auto,q_auto,w_600/marketing/tile-topics-m.png\n\n  // For a placeholder image, we use the lowest image setting available to reduce the load time\n  // else we use the auto size\n  const quality = config.isPlaceholder ? 'q_auto:low' : 'q_auto';\n\n  let params = `f_auto,${quality}`;\n  if (config.width) {\n    params += `,w_${config.width}`;\n  }\n\n  return `${path}/image/upload/${params}/${config.src}`;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {PLACEHOLDER_QUALITY} from './constants';\nimport {createImageLoader, ImageLoaderConfig, ImageLoaderInfo} from './image_loader';\n\n/**\n * Name and URL tester for ImageKit.\n */\nexport const imageKitLoaderInfo: ImageLoaderInfo = {\n  name: 'ImageKit',\n  testUrl: isImageKitUrl,\n};\n\nconst IMAGE_KIT_LOADER_REGEX = /https?\\:\\/\\/[^\\/]+\\.imagekit\\.io\\/.+/;\n/**\n * Tests whether a URL is from ImageKit CDN.\n */\nfunction isImageKitUrl(url: string): boolean {\n  return IMAGE_KIT_LOADER_REGEX.test(url);\n}\n\n/**\n * Function that generates an ImageLoader for ImageKit and turns it into an Angular provider.\n *\n * @param path Base URL of your ImageKit images\n * This URL should match one of the following formats:\n * https://ik.imagekit.io/myaccount\n * https://subdomain.mysite.com\n * @returns Set of providers to configure the ImageKit loader.\n *\n * @publicApi\n */\nexport const provideImageKitLoader = createImageLoader(\n  createImagekitUrl,\n  ngDevMode ? ['https://ik.imagekit.io/mysite', 'https://subdomain.mysite.com'] : undefined,\n);\n\nexport function createImagekitUrl(path: string, config: ImageLoaderConfig): string {\n  // Example of an ImageKit image URL:\n  // https://ik.imagekit.io/demo/tr:w-300,h-300/medium_cafe_B1iTdD0C.jpg\n  const {src, width} = config;\n  const params: string[] = [];\n\n  if (width) {\n    params.push(`w-${width}`);\n  }\n\n  // When requesting a placeholder image we ask for a low quality image to reduce the load time.\n  if (config.isPlaceholder) {\n    params.push(`q-${PLACEHOLDER_QUALITY}`);\n  }\n\n  const urlSegments = params.length ? [path, `tr:${params.join(',')}`, src] : [path, src];\n  const url = new URL(urlSegments.join('/'));\n  return url.href;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {PLACEHOLDER_QUALITY} from './constants';\nimport {createImageLoader, ImageLoaderConfig, ImageLoaderInfo} from './image_loader';\n\n/**\n * Name and URL tester for Imgix.\n */\nexport const imgixLoaderInfo: ImageLoaderInfo = {\n  name: 'Imgix',\n  testUrl: isImgixUrl,\n};\n\nconst IMGIX_LOADER_REGEX = /https?\\:\\/\\/[^\\/]+\\.imgix\\.net\\/.+/;\n/**\n * Tests whether a URL is from Imgix CDN.\n */\nfunction isImgixUrl(url: string): boolean {\n  return IMGIX_LOADER_REGEX.test(url);\n}\n\n/**\n * Function that generates an ImageLoader for Imgix and turns it into an Angular provider.\n *\n * @param path path to the desired Imgix origin,\n * e.g. https://somepath.imgix.net or https://images.mysite.com\n * @returns Set of providers to configure the Imgix loader.\n *\n * @publicApi\n */\nexport const provideImgixLoader = createImageLoader(\n  createImgixUrl,\n  ngDevMode ? ['https://somepath.imgix.net/'] : undefined,\n);\n\nfunction createImgixUrl(path: string, config: ImageLoaderConfig) {\n  const url = new URL(`${path}/${config.src}`);\n  // This setting ensures the smallest allowable format is set.\n  url.searchParams.set('auto', 'format');\n  if (config.width) {\n    url.searchParams.set('w', config.width.toString());\n  }\n\n  // When requesting a placeholder image we ask a low quality image to reduce the load time.\n  if (config.isPlaceholder) {\n    url.searchParams.set('q', PLACEHOLDER_QUALITY);\n  }\n  return url.href;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Provider,\n  ɵformatRuntimeError as formatRuntimeError,\n  ɵRuntimeError as RuntimeError,\n} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../../../errors';\nimport {isAbsoluteUrl, isValidPath} from '../url';\n\nimport {IMAGE_LOADER, ImageLoaderConfig, ImageLoaderInfo} from './image_loader';\nimport {PLACEHOLDER_QUALITY} from './constants';\n\n/**\n * Name and URL tester for Netlify.\n */\nexport const netlifyLoaderInfo: ImageLoaderInfo = {\n  name: 'Netlify',\n  testUrl: isNetlifyUrl,\n};\n\nconst NETLIFY_LOADER_REGEX = /https?\\:\\/\\/[^\\/]+\\.netlify\\.app\\/.+/;\n\n/**\n * Tests whether a URL is from a Netlify site. This won't catch sites with a custom domain,\n * but it's a good start for sites in development. This is only used to warn users who haven't\n * configured an image loader.\n */\nfunction isNetlifyUrl(url: string): boolean {\n  return NETLIFY_LOADER_REGEX.test(url);\n}\n\n/**\n * Function that generates an ImageLoader for Netlify and turns it into an Angular provider.\n *\n * @param path optional URL of the desired Netlify site. Defaults to the current site.\n * @returns Set of providers to configure the Netlify loader.\n *\n * @publicApi\n */\nexport function provideNetlifyLoader(path?: string) {\n  if (path && !isValidPath(path)) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_LOADER_ARGUMENTS,\n      ngDevMode &&\n        `Image loader has detected an invalid path (\\`${path}\\`). ` +\n          `To fix this, supply either the full URL to the Netlify site, or leave it empty to use the current site.`,\n    );\n  }\n\n  if (path) {\n    const url = new URL(path);\n    path = url.origin;\n  }\n\n  const loaderFn = (config: ImageLoaderConfig) => {\n    return createNetlifyUrl(config, path);\n  };\n\n  const providers: Provider[] = [{provide: IMAGE_LOADER, useValue: loaderFn}];\n  return providers;\n}\n\nconst validParams = new Map<string, string>([\n  ['height', 'h'],\n  ['fit', 'fit'],\n  ['quality', 'q'],\n  ['q', 'q'],\n  ['position', 'position'],\n]);\n\nfunction createNetlifyUrl(config: ImageLoaderConfig, path?: string) {\n  // Note: `path` can be undefined, in which case we use a fake one to construct a `URL` instance.\n  const url = new URL(path ?? 'https://a/');\n  url.pathname = '/.netlify/images';\n\n  if (!isAbsoluteUrl(config.src) && !config.src.startsWith('/')) {\n    config.src = '/' + config.src;\n  }\n\n  url.searchParams.set('url', config.src);\n\n  if (config.width) {\n    url.searchParams.set('w', config.width.toString());\n  }\n\n  // When requesting a placeholder image we ask for a low quality image to reduce the load time.\n  // If the quality is specified in the loader config - always use provided value.\n  const configQuality = config.loaderParams?.['quality'] ?? config.loaderParams?.['q'];\n  if (config.isPlaceholder && !configQuality) {\n    url.searchParams.set('q', PLACEHOLDER_QUALITY);\n  }\n\n  for (const [param, value] of Object.entries(config.loaderParams ?? {})) {\n    if (validParams.has(param)) {\n      url.searchParams.set(validParams.get(param)!, value.toString());\n    } else {\n      if (ngDevMode) {\n        console.warn(\n          formatRuntimeError(\n            RuntimeErrorCode.INVALID_LOADER_ARGUMENTS,\n            `The Netlify image loader has detected an \\`<img>\\` tag with the unsupported attribute \"\\`${param}\\`\".`,\n          ),\n        );\n      }\n    }\n  }\n  // The \"a\" hostname is used for relative URLs, so we can remove it from the final URL.\n  return url.hostname === 'a' ? url.href.replace(url.origin, '') : url.href;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// Assembles directive details string, useful for error messages.\nexport function imgDirectiveDetails(ngSrc: string, includeNgSrc = true) {\n  const ngSrcInfo = includeNgSrc\n    ? `(activated on an <img> element with the \\`ngSrc=\"${ngSrc}\"\\`) `\n    : '';\n  return `The NgOptimizedImage directive ${ngSrcInfo}has detected that`;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵRuntimeError as RuntimeError} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../../errors';\n\n/**\n * Asserts that the application is in development mode. Throws an error if the application is in\n * production mode. This assert can be used to make sure that there is no dev-mode code invoked in\n * the prod mode accidentally.\n */\nexport function assertDevMode(checkName: string) {\n  if (!ngDevMode) {\n    throw new RuntimeError(\n      RuntimeErrorCode.UNEXPECTED_DEV_MODE_CHECK_IN_PROD_MODE,\n      `Unexpected invocation of the ${checkName} in the prod mode. ` +\n        `Please make sure that the prod mode is enabled for production builds.`,\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  inject,\n  Injectable,\n  OnDestroy,\n  ɵformatRuntimeError as formatRuntimeError,\n} from '@angular/core';\n\nimport {DOCUMENT} from '../../dom_tokens';\nimport {RuntimeErrorCode} from '../../errors';\n\nimport {assertDevMode} from './asserts';\nimport {imgDirectiveDetails} from './error_helper';\nimport {getUrl} from './url';\n\ninterface ObservedImageState {\n  priority: boolean;\n  modified: boolean;\n  alreadyWarnedPriority: boolean;\n  alreadyWarnedModified: boolean;\n}\n\n/**\n * Observer that detects whether an image with `NgOptimizedImage`\n * is treated as a Largest Contentful Paint (LCP) element. If so,\n * asserts that the image has the `priority` attribute.\n *\n * Note: this is a dev-mode only class and it does not appear in prod bundles,\n * thus there is no `ngDevMode` use in the code.\n *\n * Based on https://web.dev/lcp/#measure-lcp-in-javascript.\n */\n@Injectable({providedIn: 'root'})\nexport class LCPImageObserver implements OnDestroy {\n  // Map of full image URLs -> original `ngSrc` values.\n  private images = new Map<string, ObservedImageState>();\n\n  private window: Window | null = null;\n  private observer: PerformanceObserver | null = null;\n\n  constructor() {\n    assertDevMode('LCP checker');\n    const win = inject(DOCUMENT).defaultView;\n    if (typeof win !== 'undefined' && typeof PerformanceObserver !== 'undefined') {\n      this.window = win;\n      this.observer = this.initPerformanceObserver();\n    }\n  }\n\n  /**\n   * Inits PerformanceObserver and subscribes to LCP events.\n   * Based on https://web.dev/lcp/#measure-lcp-in-javascript\n   */\n  private initPerformanceObserver(): PerformanceObserver {\n    const observer = new PerformanceObserver((entryList) => {\n      const entries = entryList.getEntries();\n      if (entries.length === 0) return;\n      // We use the latest entry produced by the `PerformanceObserver` as the best\n      // signal on which element is actually an LCP one. As an example, the first image to load on\n      // a page, by virtue of being the only thing on the page so far, is often a LCP candidate\n      // and gets reported by PerformanceObserver, but isn't necessarily the LCP element.\n      const lcpElement = entries[entries.length - 1];\n\n      // Cast to `any` due to missing `element` on the `LargestContentfulPaint` type of entry.\n      // See https://developer.mozilla.org/en-US/docs/Web/API/LargestContentfulPaint\n      const imgSrc = (lcpElement as any).element?.src ?? '';\n\n      // Exclude `data:` and `blob:` URLs, since they are not supported by the directive.\n      if (imgSrc.startsWith('data:') || imgSrc.startsWith('blob:')) return;\n\n      const img = this.images.get(imgSrc);\n      if (!img) return;\n      if (!img.priority && !img.alreadyWarnedPriority) {\n        img.alreadyWarnedPriority = true;\n        logMissingPriorityError(imgSrc);\n      }\n      if (img.modified && !img.alreadyWarnedModified) {\n        img.alreadyWarnedModified = true;\n        logModifiedWarning(imgSrc);\n      }\n    });\n    observer.observe({type: 'largest-contentful-paint', buffered: true});\n    return observer;\n  }\n\n  registerImage(rewrittenSrc: string, originalNgSrc: string, isPriority: boolean) {\n    if (!this.observer) return;\n    const newObservedImageState: ObservedImageState = {\n      priority: isPriority,\n      modified: false,\n      alreadyWarnedModified: false,\n      alreadyWarnedPriority: false,\n    };\n    this.images.set(getUrl(rewrittenSrc, this.window!).href, newObservedImageState);\n  }\n\n  unregisterImage(rewrittenSrc: string) {\n    if (!this.observer) return;\n    this.images.delete(getUrl(rewrittenSrc, this.window!).href);\n  }\n\n  updateImage(originalSrc: string, newSrc: string) {\n    const originalUrl = getUrl(originalSrc, this.window!).href;\n    const img = this.images.get(originalUrl);\n    if (img) {\n      img.modified = true;\n      this.images.set(getUrl(newSrc, this.window!).href, img);\n      this.images.delete(originalUrl);\n    }\n  }\n\n  ngOnDestroy() {\n    if (!this.observer) return;\n    this.observer.disconnect();\n    this.images.clear();\n  }\n}\n\nfunction logMissingPriorityError(ngSrc: string) {\n  const directiveDetails = imgDirectiveDetails(ngSrc);\n  console.error(\n    formatRuntimeError(\n      RuntimeErrorCode.LCP_IMG_MISSING_PRIORITY,\n      `${directiveDetails} this image is the Largest Contentful Paint (LCP) ` +\n        `element but was not marked \"priority\". This image should be marked ` +\n        `\"priority\" in order to prioritize its loading. ` +\n        `To fix this, add the \"priority\" attribute.`,\n    ),\n  );\n}\n\nfunction logModifiedWarning(ngSrc: string) {\n  const directiveDetails = imgDirectiveDetails(ngSrc);\n  console.warn(\n    formatRuntimeError(\n      RuntimeErrorCode.LCP_IMG_NGSRC_MODIFIED,\n      `${directiveDetails} this image is the Largest Contentful Paint (LCP) ` +\n        `element and has had its \"ngSrc\" attribute modified. This can cause ` +\n        `slower loading performance. It is recommended not to modify the \"ngSrc\" ` +\n        `property on any image which could be the LCP element.`,\n    ),\n  );\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  inject,\n  Injectable,\n  InjectionToken,\n  ɵformatRuntimeError as formatRuntimeError,\n  ɵRuntimeError as RuntimeError,\n} from '@angular/core';\n\nimport {DOCUMENT} from '../../dom_tokens';\nimport {RuntimeErrorCode} from '../../errors';\n\nimport {assertDevMode} from './asserts';\nimport {imgDirectiveDetails} from './error_helper';\nimport {extractHostname, getUrl} from './url';\n\n// Set of origins that are always excluded from the preconnect checks.\nconst INTERNAL_PRECONNECT_CHECK_BLOCKLIST = new Set(['localhost', '127.0.0.1', '0.0.0.0']);\n\n/**\n * Injection token to configure which origins should be excluded\n * from the preconnect checks. It can either be a single string or an array of strings\n * to represent a group of origins, for example:\n *\n * ```typescript\n *  {provide: PRECONNECT_CHECK_BLOCKLIST, useValue: 'https://your-domain.com'}\n * ```\n *\n * or:\n *\n * ```typescript\n *  {provide: PRECONNECT_CHECK_BLOCKLIST,\n *   useValue: ['https://your-domain-1.com', 'https://your-domain-2.com']}\n * ```\n *\n * @publicApi\n */\nexport const PRECONNECT_CHECK_BLOCKLIST = new InjectionToken<Array<string | string[]>>(\n  ngDevMode ? 'PRECONNECT_CHECK_BLOCKLIST' : '',\n);\n\n/**\n * Contains the logic to detect whether an image, marked with the \"priority\" attribute\n * has a corresponding `<link rel=\"preconnect\">` tag in the `document.head`.\n *\n * Note: this is a dev-mode only class, which should not appear in prod bundles,\n * thus there is no `ngDevMode` use in the code.\n */\n@Injectable({providedIn: 'root'})\nexport class PreconnectLinkChecker {\n  private document = inject(DOCUMENT);\n\n  /**\n   * Set of <link rel=\"preconnect\"> tags found on this page.\n   * The `null` value indicates that there was no DOM query operation performed.\n   */\n  private preconnectLinks: Set<string> | null = null;\n\n  /*\n   * Keep track of all already seen origin URLs to avoid repeating the same check.\n   */\n  private alreadySeen = new Set<string>();\n\n  private window: Window | null = null;\n\n  private blocklist = new Set<string>(INTERNAL_PRECONNECT_CHECK_BLOCKLIST);\n\n  constructor() {\n    assertDevMode('preconnect link checker');\n    const win = this.document.defaultView;\n    if (typeof win !== 'undefined') {\n      this.window = win;\n    }\n    const blocklist = inject(PRECONNECT_CHECK_BLOCKLIST, {optional: true});\n    if (blocklist) {\n      this.populateBlocklist(blocklist);\n    }\n  }\n\n  private populateBlocklist(origins: Array<string | string[]> | string) {\n    if (Array.isArray(origins)) {\n      deepForEach(origins, (origin) => {\n        this.blocklist.add(extractHostname(origin));\n      });\n    } else {\n      this.blocklist.add(extractHostname(origins));\n    }\n  }\n\n  /**\n   * Checks that a preconnect resource hint exists in the head for the\n   * given src.\n   *\n   * @param rewrittenSrc src formatted with loader\n   * @param originalNgSrc ngSrc value\n   */\n  assertPreconnect(rewrittenSrc: string, originalNgSrc: string): void {\n    if (!this.window) return;\n\n    const imgUrl = getUrl(rewrittenSrc, this.window);\n    if (this.blocklist.has(imgUrl.hostname) || this.alreadySeen.has(imgUrl.origin)) return;\n\n    // Register this origin as seen, so we don't check it again later.\n    this.alreadySeen.add(imgUrl.origin);\n\n    // Note: we query for preconnect links only *once* and cache the results\n    // for the entire lifespan of an application, since it's unlikely that the\n    // list would change frequently. This allows to make sure there are no\n    // performance implications of making extra DOM lookups for each image.\n    this.preconnectLinks ??= this.queryPreconnectLinks();\n\n    if (!this.preconnectLinks.has(imgUrl.origin)) {\n      console.warn(\n        formatRuntimeError(\n          RuntimeErrorCode.PRIORITY_IMG_MISSING_PRECONNECT_TAG,\n          `${imgDirectiveDetails(originalNgSrc)} there is no preconnect tag present for this ` +\n            `image. Preconnecting to the origin(s) that serve priority images ensures that these ` +\n            `images are delivered as soon as possible. To fix this, please add the following ` +\n            `element into the <head> of the document:\\n` +\n            `  <link rel=\"preconnect\" href=\"${imgUrl.origin}\">`,\n        ),\n      );\n    }\n  }\n\n  private queryPreconnectLinks(): Set<string> {\n    const preconnectUrls = new Set<string>();\n    const selector = 'link[rel=preconnect]';\n    const links: HTMLLinkElement[] = Array.from(this.document.querySelectorAll(selector));\n    for (let link of links) {\n      const url = getUrl(link.href, this.window!);\n      preconnectUrls.add(url.origin);\n    }\n    return preconnectUrls;\n  }\n\n  ngOnDestroy() {\n    this.preconnectLinks?.clear();\n    this.alreadySeen.clear();\n  }\n}\n\n/**\n * Invokes a callback for each element in the array. Also invokes a callback\n * recursively for each nested array.\n */\nfunction deepForEach<T>(input: (T | any[])[], fn: (value: T) => void): void {\n  for (let value of input) {\n    Array.isArray(value) ? deepForEach(value, fn) : fn(value);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {InjectionToken} from '@angular/core';\n\n/**\n * In SSR scenarios, a preload `<link>` element is generated for priority images.\n * Having a large number of preload tags may negatively affect the performance,\n * so we warn developers (by throwing an error) if the number of preloaded images\n * is above a certain threshold. This const specifies this threshold.\n */\nexport const DEFAULT_PRELOADED_IMAGES_LIMIT = 5;\n\n/**\n * Helps to keep track of priority images that already have a corresponding\n * preload tag (to avoid generating multiple preload tags with the same URL).\n *\n * This Set tracks the original src passed into the `ngSrc` input not the src after it has been\n * run through the specified `IMAGE_LOADER`.\n */\nexport const PRELOADED_IMAGES = new InjectionToken<Set<string>>('NG_OPTIMIZED_PRELOADED_IMAGES', {\n  providedIn: 'root',\n  factory: () => new Set<string>(),\n});\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {inject, Injectable, Renderer2, ɵRuntimeError as RuntimeError} from '@angular/core';\n\nimport {DOCUMENT} from '../../dom_tokens';\nimport {RuntimeErrorCode} from '../../errors';\n\nimport {DEFAULT_PRELOADED_IMAGES_LIMIT, PRELOADED_IMAGES} from './tokens';\n\n/**\n * @description Contains the logic needed to track and add preload link tags to the `<head>` tag. It\n * will also track what images have already had preload link tags added so as to not duplicate link\n * tags.\n *\n * In dev mode this service will validate that the number of preloaded images does not exceed the\n * configured default preloaded images limit: {@link DEFAULT_PRELOADED_IMAGES_LIMIT}.\n */\n@Injectable({providedIn: 'root'})\nexport class PreloadLinkCreator {\n  private readonly preloadedImages = inject(PRELOADED_IMAGES);\n  private readonly document = inject(DOCUMENT);\n\n  /**\n   * @description Add a preload `<link>` to the `<head>` of the `index.html` that is served from the\n   * server while using Angular Universal and SSR to kick off image loads for high priority images.\n   *\n   * The `sizes` (passed in from the user) and `srcset` (parsed and formatted from `ngSrcset`)\n   * properties used to set the corresponding attributes, `imagesizes` and `imagesrcset`\n   * respectively, on the preload `<link>` tag so that the correctly sized image is preloaded from\n   * the CDN.\n   *\n   * {@link https://web.dev/preload-responsive-images/#imagesrcset-and-imagesizes}\n   *\n   * @param renderer The `Renderer2` passed in from the directive\n   * @param src The original src of the image that is set on the `ngSrc` input.\n   * @param srcset The parsed and formatted srcset created from the `ngSrcset` input\n   * @param sizes The value of the `sizes` attribute passed in to the `<img>` tag\n   */\n  createPreloadLinkTag(renderer: Renderer2, src: string, srcset?: string, sizes?: string): void {\n    if (ngDevMode) {\n      if (this.preloadedImages.size >= DEFAULT_PRELOADED_IMAGES_LIMIT) {\n        throw new RuntimeError(\n          RuntimeErrorCode.TOO_MANY_PRELOADED_IMAGES,\n          ngDevMode &&\n            `The \\`NgOptimizedImage\\` directive has detected that more than ` +\n              `${DEFAULT_PRELOADED_IMAGES_LIMIT} images were marked as priority. ` +\n              `This might negatively affect an overall performance of the page. ` +\n              `To fix this, remove the \"priority\" attribute from images with less priority.`,\n        );\n      }\n    }\n\n    if (this.preloadedImages.has(src)) {\n      return;\n    }\n\n    this.preloadedImages.add(src);\n\n    const preload = renderer.createElement('link');\n    renderer.setAttribute(preload, 'as', 'image');\n    renderer.setAttribute(preload, 'href', src);\n    renderer.setAttribute(preload, 'rel', 'preload');\n    renderer.setAttribute(preload, 'fetchpriority', 'high');\n\n    if (sizes) {\n      renderer.setAttribute(preload, 'imageSizes', sizes);\n    }\n\n    if (srcset) {\n      renderer.setAttribute(preload, 'imageSrcset', srcset);\n    }\n\n    renderer.appendChild(this.document.head, preload);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  booleanAttribute,\n  Directive,\n  ElementRef,\n  inject,\n  Injector,\n  Input,\n  NgZone,\n  numberAttribute,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  PLATFORM_ID,\n  Renderer2,\n  SimpleChanges,\n  ɵformatRuntimeError as formatRuntimeError,\n  ɵIMAGE_CONFIG as IMAGE_CONFIG,\n  ɵIMAGE_CONFIG_DEFAULTS as IMAGE_CONFIG_DEFAULTS,\n  ɵImageConfig as ImageConfig,\n  ɵperformanceMarkFeature as performanceMarkFeature,\n  ɵRuntimeError as RuntimeError,\n  ɵSafeValue as SafeValue,\n  ɵunwrapSafeValue as unwrapSafeValue,\n  ChangeDetectorRef,\n} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../../errors';\nimport {isPlatformServer} from '../../platform_id';\n\nimport {imgDirectiveDetails} from './error_helper';\nimport {cloudinaryLoaderInfo} from './image_loaders/cloudinary_loader';\nimport {\n  IMAGE_LOADER,\n  ImageLoader,\n  ImageLoaderConfig,\n  noopImageLoader,\n} from './image_loaders/image_loader';\nimport {imageKitLoaderInfo} from './image_loaders/imagekit_loader';\nimport {imgixLoaderInfo} from './image_loaders/imgix_loader';\nimport {netlifyLoaderInfo} from './image_loaders/netlify_loader';\nimport {LCPImageObserver} from './lcp_image_observer';\nimport {PreconnectLinkChecker} from './preconnect_link_checker';\nimport {PreloadLinkCreator} from './preload-link-creator';\n\n/**\n * When a Base64-encoded image is passed as an input to the `NgOptimizedImage` directive,\n * an error is thrown. The image content (as a string) might be very long, thus making\n * it hard to read an error message if the entire string is included. This const defines\n * the number of characters that should be included into the error message. The rest\n * of the content is truncated.\n */\nconst BASE64_IMG_MAX_LENGTH_IN_ERROR = 50;\n\n/**\n * RegExpr to determine whether a src in a srcset is using width descriptors.\n * Should match something like: \"100w, 200w\".\n */\nconst VALID_WIDTH_DESCRIPTOR_SRCSET = /^((\\s*\\d+w\\s*(,|$)){1,})$/;\n\n/**\n * RegExpr to determine whether a src in a srcset is using density descriptors.\n * Should match something like: \"1x, 2x, 50x\". Also supports decimals like \"1.5x, 1.50x\".\n */\nconst VALID_DENSITY_DESCRIPTOR_SRCSET = /^((\\s*\\d+(\\.\\d+)?x\\s*(,|$)){1,})$/;\n\n/**\n * Srcset values with a density descriptor higher than this value will actively\n * throw an error. Such densities are not permitted as they cause image sizes\n * to be unreasonably large and slow down LCP.\n */\nexport const ABSOLUTE_SRCSET_DENSITY_CAP = 3;\n\n/**\n * Used only in error message text to communicate best practices, as we will\n * only throw based on the slightly more conservative ABSOLUTE_SRCSET_DENSITY_CAP.\n */\nexport const RECOMMENDED_SRCSET_DENSITY_CAP = 2;\n\n/**\n * Used in generating automatic density-based srcsets\n */\nconst DENSITY_SRCSET_MULTIPLIERS = [1, 2];\n\n/**\n * Used to determine which breakpoints to use on full-width images\n */\nconst VIEWPORT_BREAKPOINT_CUTOFF = 640;\n/**\n * Used to determine whether two aspect ratios are similar in value.\n */\nconst ASPECT_RATIO_TOLERANCE = 0.1;\n\n/**\n * Used to determine whether the image has been requested at an overly\n * large size compared to the actual rendered image size (after taking\n * into account a typical device pixel ratio). In pixels.\n */\nconst OVERSIZED_IMAGE_TOLERANCE = 1000;\n\n/**\n * Used to limit automatic srcset generation of very large sources for\n * fixed-size images. In pixels.\n */\nconst FIXED_SRCSET_WIDTH_LIMIT = 1920;\nconst FIXED_SRCSET_HEIGHT_LIMIT = 1080;\n\n/**\n * Default blur radius of the CSS filter used on placeholder images, in pixels\n */\nexport const PLACEHOLDER_BLUR_AMOUNT = 15;\n\n/**\n * Used to warn or error when the user provides an overly large dataURL for the placeholder\n * attribute.\n * Character count of Base64 images is 1 character per byte, and base64 encoding is approximately\n * 33% larger than base images, so 4000 characters is around 3KB on disk and 10000 characters is\n * around 7.7KB. Experimentally, 4000 characters is about 20x20px in PNG or medium-quality JPEG\n * format, and 10,000 is around 50x50px, but there's quite a bit of variation depending on how the\n * image is saved.\n */\nexport const DATA_URL_WARN_LIMIT = 4000;\nexport const DATA_URL_ERROR_LIMIT = 10000;\n\n/** Info about built-in loaders we can test for. */\nexport const BUILT_IN_LOADERS = [\n  imgixLoaderInfo,\n  imageKitLoaderInfo,\n  cloudinaryLoaderInfo,\n  netlifyLoaderInfo,\n];\n\n/**\n * Config options used in rendering placeholder images.\n *\n * @see {@link NgOptimizedImage}\n * @publicApi\n */\nexport interface ImagePlaceholderConfig {\n  blur?: boolean;\n}\n\n/**\n * Directive that improves image loading performance by enforcing best practices.\n *\n * `NgOptimizedImage` ensures that the loading of the Largest Contentful Paint (LCP) image is\n * prioritized by:\n * - Automatically setting the `fetchpriority` attribute on the `<img>` tag\n * - Lazy loading non-priority images by default\n * - Automatically generating a preconnect link tag in the document head\n *\n * In addition, the directive:\n * - Generates appropriate asset URLs if a corresponding `ImageLoader` function is provided\n * - Automatically generates a srcset\n * - Requires that `width` and `height` are set\n * - Warns if `width` or `height` have been set incorrectly\n * - Warns if the image will be visually distorted when rendered\n *\n * @usageNotes\n * The `NgOptimizedImage` directive is marked as [standalone](guide/standalone-components) and can\n * be imported directly.\n *\n * Follow the steps below to enable and use the directive:\n * 1. Import it into the necessary NgModule or a standalone Component.\n * 2. Optionally provide an `ImageLoader` if you use an image hosting service.\n * 3. Update the necessary `<img>` tags in templates and replace `src` attributes with `ngSrc`.\n * Using a `ngSrc` allows the directive to control when the `src` gets set, which triggers an image\n * download.\n *\n * Step 1: import the `NgOptimizedImage` directive.\n *\n * ```typescript\n * import { NgOptimizedImage } from '@angular/common';\n *\n * // Include it into the necessary NgModule\n * @NgModule({\n *   imports: [NgOptimizedImage],\n * })\n * class AppModule {}\n *\n * // ... or a standalone Component\n * @Component({\n *   standalone: true\n *   imports: [NgOptimizedImage],\n * })\n * class MyStandaloneComponent {}\n * ```\n *\n * Step 2: configure a loader.\n *\n * To use the **default loader**: no additional code changes are necessary. The URL returned by the\n * generic loader will always match the value of \"src\". In other words, this loader applies no\n * transformations to the resource URL and the value of the `ngSrc` attribute will be used as is.\n *\n * To use an existing loader for a **third-party image service**: add the provider factory for your\n * chosen service to the `providers` array. In the example below, the Imgix loader is used:\n *\n * ```typescript\n * import {provideImgixLoader} from '@angular/common';\n *\n * // Call the function and add the result to the `providers` array:\n * providers: [\n *   provideImgixLoader(\"https://my.base.url/\"),\n * ],\n * ```\n *\n * The `NgOptimizedImage` directive provides the following functions:\n * - `provideCloudflareLoader`\n * - `provideCloudinaryLoader`\n * - `provideImageKitLoader`\n * - `provideImgixLoader`\n *\n * If you use a different image provider, you can create a custom loader function as described\n * below.\n *\n * To use a **custom loader**: provide your loader function as a value for the `IMAGE_LOADER` DI\n * token.\n *\n * ```typescript\n * import {IMAGE_LOADER, ImageLoaderConfig} from '@angular/common';\n *\n * // Configure the loader using the `IMAGE_LOADER` token.\n * providers: [\n *   {\n *      provide: IMAGE_LOADER,\n *      useValue: (config: ImageLoaderConfig) => {\n *        return `https://example.com/${config.src}-${config.width}.jpg}`;\n *      }\n *   },\n * ],\n * ```\n *\n * Step 3: update `<img>` tags in templates to use `ngSrc` instead of `src`.\n *\n * ```\n * <img ngSrc=\"logo.png\" width=\"200\" height=\"100\">\n * ```\n *\n * @publicApi\n */\n@Directive({\n  standalone: true,\n  selector: 'img[ngSrc]',\n  host: {\n    '[style.position]': 'fill ? \"absolute\" : null',\n    '[style.width]': 'fill ? \"100%\" : null',\n    '[style.height]': 'fill ? \"100%\" : null',\n    '[style.inset]': 'fill ? \"0\" : null',\n    '[style.background-size]': 'placeholder ? \"cover\" : null',\n    '[style.background-position]': 'placeholder ? \"50% 50%\" : null',\n    '[style.background-repeat]': 'placeholder ? \"no-repeat\" : null',\n    '[style.background-image]': 'placeholder ? generatePlaceholder(placeholder) : null',\n    '[style.filter]': `placeholder && shouldBlurPlaceholder(placeholderConfig) ? \"blur(${PLACEHOLDER_BLUR_AMOUNT}px)\" : null`,\n  },\n})\nexport class NgOptimizedImage implements OnInit, OnChanges, OnDestroy {\n  private imageLoader = inject(IMAGE_LOADER);\n  private config: ImageConfig = processConfig(inject(IMAGE_CONFIG));\n  private renderer = inject(Renderer2);\n  private imgElement: HTMLImageElement = inject(ElementRef).nativeElement;\n  private injector = inject(Injector);\n  private readonly isServer = isPlatformServer(inject(PLATFORM_ID));\n  private readonly preloadLinkCreator = inject(PreloadLinkCreator);\n\n  // a LCP image observer - should be injected only in the dev mode\n  private lcpObserver = ngDevMode ? this.injector.get(LCPImageObserver) : null;\n\n  /**\n   * Calculate the rewritten `src` once and store it.\n   * This is needed to avoid repetitive calculations and make sure the directive cleanup in the\n   * `ngOnDestroy` does not rely on the `IMAGE_LOADER` logic (which in turn can rely on some other\n   * instance that might be already destroyed).\n   */\n  private _renderedSrc: string | null = null;\n\n  /**\n   * Name of the source image.\n   * Image name will be processed by the image loader and the final URL will be applied as the `src`\n   * property of the image.\n   */\n  @Input({required: true, transform: unwrapSafeUrl}) ngSrc!: string;\n\n  /**\n   * A comma separated list of width or density descriptors.\n   * The image name will be taken from `ngSrc` and combined with the list of width or density\n   * descriptors to generate the final `srcset` property of the image.\n   *\n   * Example:\n   * ```\n   * <img ngSrc=\"hello.jpg\" ngSrcset=\"100w, 200w\" />  =>\n   * <img src=\"path/hello.jpg\" srcset=\"path/hello.jpg?w=100 100w, path/hello.jpg?w=200 200w\" />\n   * ```\n   */\n  @Input() ngSrcset!: string;\n\n  /**\n   * The base `sizes` attribute passed through to the `<img>` element.\n   * Providing sizes causes the image to create an automatic responsive srcset.\n   */\n  @Input() sizes?: string;\n\n  /**\n   * For responsive images: the intrinsic width of the image in pixels.\n   * For fixed size images: the desired rendered width of the image in pixels.\n   */\n  @Input({transform: numberAttribute}) width: number | undefined;\n\n  /**\n   * For responsive images: the intrinsic height of the image in pixels.\n   * For fixed size images: the desired rendered height of the image in pixels.\n   */\n  @Input({transform: numberAttribute}) height: number | undefined;\n\n  /**\n   * The desired loading behavior (lazy, eager, or auto). Defaults to `lazy`,\n   * which is recommended for most images.\n   *\n   * Warning: Setting images as loading=\"eager\" or loading=\"auto\" marks them\n   * as non-priority images and can hurt loading performance. For images which\n   * may be the LCP element, use the `priority` attribute instead of `loading`.\n   */\n  @Input() loading?: 'lazy' | 'eager' | 'auto';\n\n  /**\n   * Indicates whether this image should have a high priority.\n   */\n  @Input({transform: booleanAttribute}) priority = false;\n\n  /**\n   * Data to pass through to custom loaders.\n   */\n  @Input() loaderParams?: {[key: string]: any};\n\n  /**\n   * Disables automatic srcset generation for this image.\n   */\n  @Input({transform: booleanAttribute}) disableOptimizedSrcset = false;\n\n  /**\n   * Sets the image to \"fill mode\", which eliminates the height/width requirement and adds\n   * styles such that the image fills its containing element.\n   */\n  @Input({transform: booleanAttribute}) fill = false;\n\n  /**\n   * A URL or data URL for an image to be used as a placeholder while this image loads.\n   */\n  @Input({transform: booleanOrDataUrlAttribute}) placeholder?: string | boolean;\n\n  /**\n   * Configuration object for placeholder settings. Options:\n   *   * blur: Setting this to false disables the automatic CSS blur.\n   */\n  @Input() placeholderConfig?: ImagePlaceholderConfig;\n\n  /**\n   * Value of the `src` attribute if set on the host `<img>` element.\n   * This input is exclusively read to assert that `src` is not set in conflict\n   * with `ngSrc` and that images don't start to load until a lazy loading strategy is set.\n   * @internal\n   */\n  @Input() src?: string;\n\n  /**\n   * Value of the `srcset` attribute if set on the host `<img>` element.\n   * This input is exclusively read to assert that `srcset` is not set in conflict\n   * with `ngSrcset` and that images don't start to load until a lazy loading strategy is set.\n   * @internal\n   */\n  @Input() srcset?: string;\n\n  /** @nodoc */\n  ngOnInit() {\n    performanceMarkFeature('NgOptimizedImage');\n\n    if (ngDevMode) {\n      const ngZone = this.injector.get(NgZone);\n      assertNonEmptyInput(this, 'ngSrc', this.ngSrc);\n      assertValidNgSrcset(this, this.ngSrcset);\n      assertNoConflictingSrc(this);\n      if (this.ngSrcset) {\n        assertNoConflictingSrcset(this);\n      }\n      assertNotBase64Image(this);\n      assertNotBlobUrl(this);\n      if (this.fill) {\n        assertEmptyWidthAndHeight(this);\n        // This leaves the Angular zone to avoid triggering unnecessary change detection cycles when\n        // `load` tasks are invoked on images.\n        ngZone.runOutsideAngular(() =>\n          assertNonZeroRenderedHeight(this, this.imgElement, this.renderer),\n        );\n      } else {\n        assertNonEmptyWidthAndHeight(this);\n        if (this.height !== undefined) {\n          assertGreaterThanZero(this, this.height, 'height');\n        }\n        if (this.width !== undefined) {\n          assertGreaterThanZero(this, this.width, 'width');\n        }\n        // Only check for distorted images when not in fill mode, where\n        // images may be intentionally stretched, cropped or letterboxed.\n        ngZone.runOutsideAngular(() =>\n          assertNoImageDistortion(this, this.imgElement, this.renderer),\n        );\n      }\n      assertValidLoadingInput(this);\n      if (!this.ngSrcset) {\n        assertNoComplexSizes(this);\n      }\n      assertValidPlaceholder(this, this.imageLoader);\n      assertNotMissingBuiltInLoader(this.ngSrc, this.imageLoader);\n      assertNoNgSrcsetWithoutLoader(this, this.imageLoader);\n      assertNoLoaderParamsWithoutLoader(this, this.imageLoader);\n\n      if (this.lcpObserver !== null) {\n        const ngZone = this.injector.get(NgZone);\n        ngZone.runOutsideAngular(() => {\n          this.lcpObserver!.registerImage(this.getRewrittenSrc(), this.ngSrc, this.priority);\n        });\n      }\n\n      if (this.priority) {\n        const checker = this.injector.get(PreconnectLinkChecker);\n        checker.assertPreconnect(this.getRewrittenSrc(), this.ngSrc);\n      }\n    }\n    if (this.placeholder) {\n      this.removePlaceholderOnLoad(this.imgElement);\n    }\n    this.setHostAttributes();\n  }\n\n  private setHostAttributes() {\n    // Must set width/height explicitly in case they are bound (in which case they will\n    // only be reflected and not found by the browser)\n    if (this.fill) {\n      this.sizes ||= '100vw';\n    } else {\n      this.setHostAttribute('width', this.width!.toString());\n      this.setHostAttribute('height', this.height!.toString());\n    }\n\n    this.setHostAttribute('loading', this.getLoadingBehavior());\n    this.setHostAttribute('fetchpriority', this.getFetchPriority());\n\n    // The `data-ng-img` attribute flags an image as using the directive, to allow\n    // for analysis of the directive's performance.\n    this.setHostAttribute('ng-img', 'true');\n\n    // The `src` and `srcset` attributes should be set last since other attributes\n    // could affect the image's loading behavior.\n    const rewrittenSrcset = this.updateSrcAndSrcset();\n\n    if (this.sizes) {\n      this.setHostAttribute('sizes', this.sizes);\n    }\n    if (this.isServer && this.priority) {\n      this.preloadLinkCreator.createPreloadLinkTag(\n        this.renderer,\n        this.getRewrittenSrc(),\n        rewrittenSrcset,\n        this.sizes,\n      );\n    }\n  }\n\n  /** @nodoc */\n  ngOnChanges(changes: SimpleChanges) {\n    if (ngDevMode) {\n      assertNoPostInitInputChange(this, changes, [\n        'ngSrcset',\n        'width',\n        'height',\n        'priority',\n        'fill',\n        'loading',\n        'sizes',\n        'loaderParams',\n        'disableOptimizedSrcset',\n      ]);\n    }\n    if (changes['ngSrc'] && !changes['ngSrc'].isFirstChange()) {\n      const oldSrc = this._renderedSrc;\n      this.updateSrcAndSrcset(true);\n      const newSrc = this._renderedSrc;\n      if (this.lcpObserver !== null && oldSrc && newSrc && oldSrc !== newSrc) {\n        const ngZone = this.injector.get(NgZone);\n        ngZone.runOutsideAngular(() => {\n          this.lcpObserver?.updateImage(oldSrc, newSrc);\n        });\n      }\n    }\n  }\n\n  private callImageLoader(\n    configWithoutCustomParams: Omit<ImageLoaderConfig, 'loaderParams'>,\n  ): string {\n    let augmentedConfig: ImageLoaderConfig = configWithoutCustomParams;\n    if (this.loaderParams) {\n      augmentedConfig.loaderParams = this.loaderParams;\n    }\n    return this.imageLoader(augmentedConfig);\n  }\n\n  private getLoadingBehavior(): string {\n    if (!this.priority && this.loading !== undefined) {\n      return this.loading;\n    }\n    return this.priority ? 'eager' : 'lazy';\n  }\n\n  private getFetchPriority(): string {\n    return this.priority ? 'high' : 'auto';\n  }\n\n  private getRewrittenSrc(): string {\n    // ImageLoaderConfig supports setting a width property. However, we're not setting width here\n    // because if the developer uses rendered width instead of intrinsic width in the HTML width\n    // attribute, the image requested may be too small for 2x+ screens.\n    if (!this._renderedSrc) {\n      const imgConfig = {src: this.ngSrc};\n      // Cache calculated image src to reuse it later in the code.\n      this._renderedSrc = this.callImageLoader(imgConfig);\n    }\n    return this._renderedSrc;\n  }\n\n  private getRewrittenSrcset(): string {\n    const widthSrcSet = VALID_WIDTH_DESCRIPTOR_SRCSET.test(this.ngSrcset);\n    const finalSrcs = this.ngSrcset\n      .split(',')\n      .filter((src) => src !== '')\n      .map((srcStr) => {\n        srcStr = srcStr.trim();\n        const width = widthSrcSet ? parseFloat(srcStr) : parseFloat(srcStr) * this.width!;\n        return `${this.callImageLoader({src: this.ngSrc, width})} ${srcStr}`;\n      });\n    return finalSrcs.join(', ');\n  }\n\n  private getAutomaticSrcset(): string {\n    if (this.sizes) {\n      return this.getResponsiveSrcset();\n    } else {\n      return this.getFixedSrcset();\n    }\n  }\n\n  private getResponsiveSrcset(): string {\n    const {breakpoints} = this.config;\n\n    let filteredBreakpoints = breakpoints!;\n    if (this.sizes?.trim() === '100vw') {\n      // Since this is a full-screen-width image, our srcset only needs to include\n      // breakpoints with full viewport widths.\n      filteredBreakpoints = breakpoints!.filter((bp) => bp >= VIEWPORT_BREAKPOINT_CUTOFF);\n    }\n\n    const finalSrcs = filteredBreakpoints.map(\n      (bp) => `${this.callImageLoader({src: this.ngSrc, width: bp})} ${bp}w`,\n    );\n    return finalSrcs.join(', ');\n  }\n\n  private updateSrcAndSrcset(forceSrcRecalc = false): string | undefined {\n    if (forceSrcRecalc) {\n      // Reset cached value, so that the followup `getRewrittenSrc()` call\n      // will recalculate it and update the cache.\n      this._renderedSrc = null;\n    }\n\n    const rewrittenSrc = this.getRewrittenSrc();\n    this.setHostAttribute('src', rewrittenSrc);\n\n    let rewrittenSrcset: string | undefined = undefined;\n    if (this.ngSrcset) {\n      rewrittenSrcset = this.getRewrittenSrcset();\n    } else if (this.shouldGenerateAutomaticSrcset()) {\n      rewrittenSrcset = this.getAutomaticSrcset();\n    }\n\n    if (rewrittenSrcset) {\n      this.setHostAttribute('srcset', rewrittenSrcset);\n    }\n    return rewrittenSrcset;\n  }\n\n  private getFixedSrcset(): string {\n    const finalSrcs = DENSITY_SRCSET_MULTIPLIERS.map(\n      (multiplier) =>\n        `${this.callImageLoader({\n          src: this.ngSrc,\n          width: this.width! * multiplier,\n        })} ${multiplier}x`,\n    );\n    return finalSrcs.join(', ');\n  }\n\n  private shouldGenerateAutomaticSrcset(): boolean {\n    let oversizedImage = false;\n    if (!this.sizes) {\n      oversizedImage =\n        this.width! > FIXED_SRCSET_WIDTH_LIMIT || this.height! > FIXED_SRCSET_HEIGHT_LIMIT;\n    }\n    return (\n      !this.disableOptimizedSrcset &&\n      !this.srcset &&\n      this.imageLoader !== noopImageLoader &&\n      !oversizedImage\n    );\n  }\n\n  /**\n   * Returns an image url formatted for use with the CSS background-image property. Expects one of:\n   * * A base64 encoded image, which is wrapped and passed through.\n   * * A boolean. If true, calls the image loader to generate a small placeholder url.\n   */\n  private generatePlaceholder(placeholderInput: string | boolean): string | boolean | null {\n    const {placeholderResolution} = this.config;\n    if (placeholderInput === true) {\n      return `url(${this.callImageLoader({\n        src: this.ngSrc,\n        width: placeholderResolution,\n        isPlaceholder: true,\n      })})`;\n    } else if (typeof placeholderInput === 'string' && placeholderInput.startsWith('data:')) {\n      return `url(${placeholderInput})`;\n    }\n    return null;\n  }\n\n  /**\n   * Determines if blur should be applied, based on an optional boolean\n   * property `blur` within the optional configuration object `placeholderConfig`.\n   */\n  private shouldBlurPlaceholder(placeholderConfig?: ImagePlaceholderConfig): boolean {\n    if (!placeholderConfig || !placeholderConfig.hasOwnProperty('blur')) {\n      return true;\n    }\n    return Boolean(placeholderConfig.blur);\n  }\n\n  private removePlaceholderOnLoad(img: HTMLImageElement): void {\n    const callback = () => {\n      const changeDetectorRef = this.injector.get(ChangeDetectorRef);\n      removeLoadListenerFn();\n      removeErrorListenerFn();\n      this.placeholder = false;\n      changeDetectorRef.markForCheck();\n    };\n\n    const removeLoadListenerFn = this.renderer.listen(img, 'load', callback);\n    const removeErrorListenerFn = this.renderer.listen(img, 'error', callback);\n  }\n\n  /** @nodoc */\n  ngOnDestroy() {\n    if (ngDevMode) {\n      if (!this.priority && this._renderedSrc !== null && this.lcpObserver !== null) {\n        this.lcpObserver.unregisterImage(this._renderedSrc);\n      }\n    }\n  }\n\n  private setHostAttribute(name: string, value: string): void {\n    this.renderer.setAttribute(this.imgElement, name, value);\n  }\n}\n\n/***** Helpers *****/\n\n/**\n * Sorts provided config breakpoints and uses defaults.\n */\nfunction processConfig(config: ImageConfig): ImageConfig {\n  let sortedBreakpoints: {breakpoints?: number[]} = {};\n  if (config.breakpoints) {\n    sortedBreakpoints.breakpoints = config.breakpoints.sort((a, b) => a - b);\n  }\n  return Object.assign({}, IMAGE_CONFIG_DEFAULTS, config, sortedBreakpoints);\n}\n\n/***** Assert functions *****/\n\n/**\n * Verifies that there is no `src` set on a host element.\n */\nfunction assertNoConflictingSrc(dir: NgOptimizedImage) {\n  if (dir.src) {\n    throw new RuntimeError(\n      RuntimeErrorCode.UNEXPECTED_SRC_ATTR,\n      `${imgDirectiveDetails(dir.ngSrc)} both \\`src\\` and \\`ngSrc\\` have been set. ` +\n        `Supplying both of these attributes breaks lazy loading. ` +\n        `The NgOptimizedImage directive sets \\`src\\` itself based on the value of \\`ngSrc\\`. ` +\n        `To fix this, please remove the \\`src\\` attribute.`,\n    );\n  }\n}\n\n/**\n * Verifies that there is no `srcset` set on a host element.\n */\nfunction assertNoConflictingSrcset(dir: NgOptimizedImage) {\n  if (dir.srcset) {\n    throw new RuntimeError(\n      RuntimeErrorCode.UNEXPECTED_SRCSET_ATTR,\n      `${imgDirectiveDetails(dir.ngSrc)} both \\`srcset\\` and \\`ngSrcset\\` have been set. ` +\n        `Supplying both of these attributes breaks lazy loading. ` +\n        `The NgOptimizedImage directive sets \\`srcset\\` itself based on the value of ` +\n        `\\`ngSrcset\\`. To fix this, please remove the \\`srcset\\` attribute.`,\n    );\n  }\n}\n\n/**\n * Verifies that the `ngSrc` is not a Base64-encoded image.\n */\nfunction assertNotBase64Image(dir: NgOptimizedImage) {\n  let ngSrc = dir.ngSrc.trim();\n  if (ngSrc.startsWith('data:')) {\n    if (ngSrc.length > BASE64_IMG_MAX_LENGTH_IN_ERROR) {\n      ngSrc = ngSrc.substring(0, BASE64_IMG_MAX_LENGTH_IN_ERROR) + '...';\n    }\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_INPUT,\n      `${imgDirectiveDetails(dir.ngSrc, false)} \\`ngSrc\\` is a Base64-encoded string ` +\n        `(${ngSrc}). NgOptimizedImage does not support Base64-encoded strings. ` +\n        `To fix this, disable the NgOptimizedImage directive for this element ` +\n        `by removing \\`ngSrc\\` and using a standard \\`src\\` attribute instead.`,\n    );\n  }\n}\n\n/**\n * Verifies that the 'sizes' only includes responsive values.\n */\nfunction assertNoComplexSizes(dir: NgOptimizedImage) {\n  let sizes = dir.sizes;\n  if (sizes?.match(/((\\)|,)\\s|^)\\d+px/)) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_INPUT,\n      `${imgDirectiveDetails(dir.ngSrc, false)} \\`sizes\\` was set to a string including ` +\n        `pixel values. For automatic \\`srcset\\` generation, \\`sizes\\` must only include responsive ` +\n        `values, such as \\`sizes=\"50vw\"\\` or \\`sizes=\"(min-width: 768px) 50vw, 100vw\"\\`. ` +\n        `To fix this, modify the \\`sizes\\` attribute, or provide your own \\`ngSrcset\\` value directly.`,\n    );\n  }\n}\n\nfunction assertValidPlaceholder(dir: NgOptimizedImage, imageLoader: ImageLoader) {\n  assertNoPlaceholderConfigWithoutPlaceholder(dir);\n  assertNoRelativePlaceholderWithoutLoader(dir, imageLoader);\n  assertNoOversizedDataUrl(dir);\n}\n\n/**\n * Verifies that placeholderConfig isn't being used without placeholder\n */\nfunction assertNoPlaceholderConfigWithoutPlaceholder(dir: NgOptimizedImage) {\n  if (dir.placeholderConfig && !dir.placeholder) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_INPUT,\n      `${imgDirectiveDetails(\n        dir.ngSrc,\n        false,\n      )} \\`placeholderConfig\\` options were provided for an ` +\n        `image that does not use the \\`placeholder\\` attribute, and will have no effect.`,\n    );\n  }\n}\n\n/**\n * Warns if a relative URL placeholder is specified, but no loader is present to provide the small\n * image.\n */\nfunction assertNoRelativePlaceholderWithoutLoader(dir: NgOptimizedImage, imageLoader: ImageLoader) {\n  if (dir.placeholder === true && imageLoader === noopImageLoader) {\n    throw new RuntimeError(\n      RuntimeErrorCode.MISSING_NECESSARY_LOADER,\n      `${imgDirectiveDetails(dir.ngSrc)} the \\`placeholder\\` attribute is set to true but ` +\n        `no image loader is configured (i.e. the default one is being used), ` +\n        `which would result in the same image being used for the primary image and its placeholder. ` +\n        `To fix this, provide a loader or remove the \\`placeholder\\` attribute from the image.`,\n    );\n  }\n}\n\n/**\n * Warns or throws an error if an oversized dataURL placeholder is provided.\n */\nfunction assertNoOversizedDataUrl(dir: NgOptimizedImage) {\n  if (\n    dir.placeholder &&\n    typeof dir.placeholder === 'string' &&\n    dir.placeholder.startsWith('data:')\n  ) {\n    if (dir.placeholder.length > DATA_URL_ERROR_LIMIT) {\n      throw new RuntimeError(\n        RuntimeErrorCode.OVERSIZED_PLACEHOLDER,\n        `${imgDirectiveDetails(\n          dir.ngSrc,\n        )} the \\`placeholder\\` attribute is set to a data URL which is longer ` +\n          `than ${DATA_URL_ERROR_LIMIT} characters. This is strongly discouraged, as large inline placeholders ` +\n          `directly increase the bundle size of Angular and hurt page load performance. To fix this, generate ` +\n          `a smaller data URL placeholder.`,\n      );\n    }\n    if (dir.placeholder.length > DATA_URL_WARN_LIMIT) {\n      console.warn(\n        formatRuntimeError(\n          RuntimeErrorCode.OVERSIZED_PLACEHOLDER,\n          `${imgDirectiveDetails(\n            dir.ngSrc,\n          )} the \\`placeholder\\` attribute is set to a data URL which is longer ` +\n            `than ${DATA_URL_WARN_LIMIT} characters. This is discouraged, as large inline placeholders ` +\n            `directly increase the bundle size of Angular and hurt page load performance. For better loading performance, ` +\n            `generate a smaller data URL placeholder.`,\n        ),\n      );\n    }\n  }\n}\n\n/**\n * Verifies that the `ngSrc` is not a Blob URL.\n */\nfunction assertNotBlobUrl(dir: NgOptimizedImage) {\n  const ngSrc = dir.ngSrc.trim();\n  if (ngSrc.startsWith('blob:')) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_INPUT,\n      `${imgDirectiveDetails(dir.ngSrc)} \\`ngSrc\\` was set to a blob URL (${ngSrc}). ` +\n        `Blob URLs are not supported by the NgOptimizedImage directive. ` +\n        `To fix this, disable the NgOptimizedImage directive for this element ` +\n        `by removing \\`ngSrc\\` and using a regular \\`src\\` attribute instead.`,\n    );\n  }\n}\n\n/**\n * Verifies that the input is set to a non-empty string.\n */\nfunction assertNonEmptyInput(dir: NgOptimizedImage, name: string, value: unknown) {\n  const isString = typeof value === 'string';\n  const isEmptyString = isString && value.trim() === '';\n  if (!isString || isEmptyString) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_INPUT,\n      `${imgDirectiveDetails(dir.ngSrc)} \\`${name}\\` has an invalid value ` +\n        `(\\`${value}\\`). To fix this, change the value to a non-empty string.`,\n    );\n  }\n}\n\n/**\n * Verifies that the `ngSrcset` is in a valid format, e.g. \"100w, 200w\" or \"1x, 2x\".\n */\nexport function assertValidNgSrcset(dir: NgOptimizedImage, value: unknown) {\n  if (value == null) return;\n  assertNonEmptyInput(dir, 'ngSrcset', value);\n  const stringVal = value as string;\n  const isValidWidthDescriptor = VALID_WIDTH_DESCRIPTOR_SRCSET.test(stringVal);\n  const isValidDensityDescriptor = VALID_DENSITY_DESCRIPTOR_SRCSET.test(stringVal);\n\n  if (isValidDensityDescriptor) {\n    assertUnderDensityCap(dir, stringVal);\n  }\n\n  const isValidSrcset = isValidWidthDescriptor || isValidDensityDescriptor;\n  if (!isValidSrcset) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_INPUT,\n      `${imgDirectiveDetails(dir.ngSrc)} \\`ngSrcset\\` has an invalid value (\\`${value}\\`). ` +\n        `To fix this, supply \\`ngSrcset\\` using a comma-separated list of one or more width ` +\n        `descriptors (e.g. \"100w, 200w\") or density descriptors (e.g. \"1x, 2x\").`,\n    );\n  }\n}\n\nfunction assertUnderDensityCap(dir: NgOptimizedImage, value: string) {\n  const underDensityCap = value\n    .split(',')\n    .every((num) => num === '' || parseFloat(num) <= ABSOLUTE_SRCSET_DENSITY_CAP);\n  if (!underDensityCap) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_INPUT,\n      `${imgDirectiveDetails(dir.ngSrc)} the \\`ngSrcset\\` contains an unsupported image density:` +\n        `\\`${value}\\`. NgOptimizedImage generally recommends a max image density of ` +\n        `${RECOMMENDED_SRCSET_DENSITY_CAP}x but supports image densities up to ` +\n        `${ABSOLUTE_SRCSET_DENSITY_CAP}x. The human eye cannot distinguish between image densities ` +\n        `greater than ${RECOMMENDED_SRCSET_DENSITY_CAP}x - which makes them unnecessary for ` +\n        `most use cases. Images that will be pinch-zoomed are typically the primary use case for ` +\n        `${ABSOLUTE_SRCSET_DENSITY_CAP}x images. Please remove the high density descriptor and try again.`,\n    );\n  }\n}\n\n/**\n * Creates a `RuntimeError` instance to represent a situation when an input is set after\n * the directive has initialized.\n */\nfunction postInitInputChangeError(dir: NgOptimizedImage, inputName: string): {} {\n  let reason!: string;\n  if (inputName === 'width' || inputName === 'height') {\n    reason =\n      `Changing \\`${inputName}\\` may result in different attribute value ` +\n      `applied to the underlying image element and cause layout shifts on a page.`;\n  } else {\n    reason =\n      `Changing the \\`${inputName}\\` would have no effect on the underlying ` +\n      `image element, because the resource loading has already occurred.`;\n  }\n  return new RuntimeError(\n    RuntimeErrorCode.UNEXPECTED_INPUT_CHANGE,\n    `${imgDirectiveDetails(dir.ngSrc)} \\`${inputName}\\` was updated after initialization. ` +\n      `The NgOptimizedImage directive will not react to this input change. ${reason} ` +\n      `To fix this, either switch \\`${inputName}\\` to a static value ` +\n      `or wrap the image element in an *ngIf that is gated on the necessary value.`,\n  );\n}\n\n/**\n * Verify that none of the listed inputs has changed.\n */\nfunction assertNoPostInitInputChange(\n  dir: NgOptimizedImage,\n  changes: SimpleChanges,\n  inputs: string[],\n) {\n  inputs.forEach((input) => {\n    const isUpdated = changes.hasOwnProperty(input);\n    if (isUpdated && !changes[input].isFirstChange()) {\n      if (input === 'ngSrc') {\n        // When the `ngSrc` input changes, we detect that only in the\n        // `ngOnChanges` hook, thus the `ngSrc` is already set. We use\n        // `ngSrc` in the error message, so we use a previous value, but\n        // not the updated one in it.\n        dir = {ngSrc: changes[input].previousValue} as NgOptimizedImage;\n      }\n      throw postInitInputChangeError(dir, input);\n    }\n  });\n}\n\n/**\n * Verifies that a specified input is a number greater than 0.\n */\nfunction assertGreaterThanZero(dir: NgOptimizedImage, inputValue: unknown, inputName: string) {\n  const validNumber = typeof inputValue === 'number' && inputValue > 0;\n  const validString =\n    typeof inputValue === 'string' && /^\\d+$/.test(inputValue.trim()) && parseInt(inputValue) > 0;\n  if (!validNumber && !validString) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_INPUT,\n      `${imgDirectiveDetails(dir.ngSrc)} \\`${inputName}\\` has an invalid value. ` +\n        `To fix this, provide \\`${inputName}\\` as a number greater than 0.`,\n    );\n  }\n}\n\n/**\n * Verifies that the rendered image is not visually distorted. Effectively this is checking:\n * - Whether the \"width\" and \"height\" attributes reflect the actual dimensions of the image.\n * - Whether image styling is \"correct\" (see below for a longer explanation).\n */\nfunction assertNoImageDistortion(\n  dir: NgOptimizedImage,\n  img: HTMLImageElement,\n  renderer: Renderer2,\n) {\n  const removeLoadListenerFn = renderer.listen(img, 'load', () => {\n    removeLoadListenerFn();\n    removeErrorListenerFn();\n    const computedStyle = window.getComputedStyle(img);\n    let renderedWidth = parseFloat(computedStyle.getPropertyValue('width'));\n    let renderedHeight = parseFloat(computedStyle.getPropertyValue('height'));\n    const boxSizing = computedStyle.getPropertyValue('box-sizing');\n\n    if (boxSizing === 'border-box') {\n      const paddingTop = computedStyle.getPropertyValue('padding-top');\n      const paddingRight = computedStyle.getPropertyValue('padding-right');\n      const paddingBottom = computedStyle.getPropertyValue('padding-bottom');\n      const paddingLeft = computedStyle.getPropertyValue('padding-left');\n      renderedWidth -= parseFloat(paddingRight) + parseFloat(paddingLeft);\n      renderedHeight -= parseFloat(paddingTop) + parseFloat(paddingBottom);\n    }\n\n    const renderedAspectRatio = renderedWidth / renderedHeight;\n    const nonZeroRenderedDimensions = renderedWidth !== 0 && renderedHeight !== 0;\n\n    const intrinsicWidth = img.naturalWidth;\n    const intrinsicHeight = img.naturalHeight;\n    const intrinsicAspectRatio = intrinsicWidth / intrinsicHeight;\n\n    const suppliedWidth = dir.width!;\n    const suppliedHeight = dir.height!;\n    const suppliedAspectRatio = suppliedWidth / suppliedHeight;\n\n    // Tolerance is used to account for the impact of subpixel rendering.\n    // Due to subpixel rendering, the rendered, intrinsic, and supplied\n    // aspect ratios of a correctly configured image may not exactly match.\n    // For example, a `width=4030 height=3020` image might have a rendered\n    // size of \"1062w, 796.48h\". (An aspect ratio of 1.334... vs. 1.333...)\n    const inaccurateDimensions =\n      Math.abs(suppliedAspectRatio - intrinsicAspectRatio) > ASPECT_RATIO_TOLERANCE;\n    const stylingDistortion =\n      nonZeroRenderedDimensions &&\n      Math.abs(intrinsicAspectRatio - renderedAspectRatio) > ASPECT_RATIO_TOLERANCE;\n\n    if (inaccurateDimensions) {\n      console.warn(\n        formatRuntimeError(\n          RuntimeErrorCode.INVALID_INPUT,\n          `${imgDirectiveDetails(dir.ngSrc)} the aspect ratio of the image does not match ` +\n            `the aspect ratio indicated by the width and height attributes. ` +\n            `\\nIntrinsic image size: ${intrinsicWidth}w x ${intrinsicHeight}h ` +\n            `(aspect-ratio: ${round(\n              intrinsicAspectRatio,\n            )}). \\nSupplied width and height attributes: ` +\n            `${suppliedWidth}w x ${suppliedHeight}h (aspect-ratio: ${round(\n              suppliedAspectRatio,\n            )}). ` +\n            `\\nTo fix this, update the width and height attributes.`,\n        ),\n      );\n    } else if (stylingDistortion) {\n      console.warn(\n        formatRuntimeError(\n          RuntimeErrorCode.INVALID_INPUT,\n          `${imgDirectiveDetails(dir.ngSrc)} the aspect ratio of the rendered image ` +\n            `does not match the image's intrinsic aspect ratio. ` +\n            `\\nIntrinsic image size: ${intrinsicWidth}w x ${intrinsicHeight}h ` +\n            `(aspect-ratio: ${round(intrinsicAspectRatio)}). \\nRendered image size: ` +\n            `${renderedWidth}w x ${renderedHeight}h (aspect-ratio: ` +\n            `${round(renderedAspectRatio)}). \\nThis issue can occur if \"width\" and \"height\" ` +\n            `attributes are added to an image without updating the corresponding ` +\n            `image styling. To fix this, adjust image styling. In most cases, ` +\n            `adding \"height: auto\" or \"width: auto\" to the image styling will fix ` +\n            `this issue.`,\n        ),\n      );\n    } else if (!dir.ngSrcset && nonZeroRenderedDimensions) {\n      // If `ngSrcset` hasn't been set, sanity check the intrinsic size.\n      const recommendedWidth = RECOMMENDED_SRCSET_DENSITY_CAP * renderedWidth;\n      const recommendedHeight = RECOMMENDED_SRCSET_DENSITY_CAP * renderedHeight;\n      const oversizedWidth = intrinsicWidth - recommendedWidth >= OVERSIZED_IMAGE_TOLERANCE;\n      const oversizedHeight = intrinsicHeight - recommendedHeight >= OVERSIZED_IMAGE_TOLERANCE;\n      if (oversizedWidth || oversizedHeight) {\n        console.warn(\n          formatRuntimeError(\n            RuntimeErrorCode.OVERSIZED_IMAGE,\n            `${imgDirectiveDetails(dir.ngSrc)} the intrinsic image is significantly ` +\n              `larger than necessary. ` +\n              `\\nRendered image size: ${renderedWidth}w x ${renderedHeight}h. ` +\n              `\\nIntrinsic image size: ${intrinsicWidth}w x ${intrinsicHeight}h. ` +\n              `\\nRecommended intrinsic image size: ${recommendedWidth}w x ${recommendedHeight}h. ` +\n              `\\nNote: Recommended intrinsic image size is calculated assuming a maximum DPR of ` +\n              `${RECOMMENDED_SRCSET_DENSITY_CAP}. To improve loading time, resize the image ` +\n              `or consider using the \"ngSrcset\" and \"sizes\" attributes.`,\n          ),\n        );\n      }\n    }\n  });\n\n  // We only listen to the `error` event to remove the `load` event listener because it will not be\n  // fired if the image fails to load. This is done to prevent memory leaks in development mode\n  // because image elements aren't garbage-collected properly. It happens because zone.js stores the\n  // event listener directly on the element and closures capture `dir`.\n  const removeErrorListenerFn = renderer.listen(img, 'error', () => {\n    removeLoadListenerFn();\n    removeErrorListenerFn();\n  });\n}\n\n/**\n * Verifies that a specified input is set.\n */\nfunction assertNonEmptyWidthAndHeight(dir: NgOptimizedImage) {\n  let missingAttributes = [];\n  if (dir.width === undefined) missingAttributes.push('width');\n  if (dir.height === undefined) missingAttributes.push('height');\n  if (missingAttributes.length > 0) {\n    throw new RuntimeError(\n      RuntimeErrorCode.REQUIRED_INPUT_MISSING,\n      `${imgDirectiveDetails(dir.ngSrc)} these required attributes ` +\n        `are missing: ${missingAttributes.map((attr) => `\"${attr}\"`).join(', ')}. ` +\n        `Including \"width\" and \"height\" attributes will prevent image-related layout shifts. ` +\n        `To fix this, include \"width\" and \"height\" attributes on the image tag or turn on ` +\n        `\"fill\" mode with the \\`fill\\` attribute.`,\n    );\n  }\n}\n\n/**\n * Verifies that width and height are not set. Used in fill mode, where those attributes don't make\n * sense.\n */\nfunction assertEmptyWidthAndHeight(dir: NgOptimizedImage) {\n  if (dir.width || dir.height) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_INPUT,\n      `${imgDirectiveDetails(dir.ngSrc)} the attributes \\`height\\` and/or \\`width\\` are present ` +\n        `along with the \\`fill\\` attribute. Because \\`fill\\` mode causes an image to fill its containing ` +\n        `element, the size attributes have no effect and should be removed.`,\n    );\n  }\n}\n\n/**\n * Verifies that the rendered image has a nonzero height. If the image is in fill mode, provides\n * guidance that this can be caused by the containing element's CSS position property.\n */\nfunction assertNonZeroRenderedHeight(\n  dir: NgOptimizedImage,\n  img: HTMLImageElement,\n  renderer: Renderer2,\n) {\n  const removeLoadListenerFn = renderer.listen(img, 'load', () => {\n    removeLoadListenerFn();\n    removeErrorListenerFn();\n    const renderedHeight = img.clientHeight;\n    if (dir.fill && renderedHeight === 0) {\n      console.warn(\n        formatRuntimeError(\n          RuntimeErrorCode.INVALID_INPUT,\n          `${imgDirectiveDetails(dir.ngSrc)} the height of the fill-mode image is zero. ` +\n            `This is likely because the containing element does not have the CSS 'position' ` +\n            `property set to one of the following: \"relative\", \"fixed\", or \"absolute\". ` +\n            `To fix this problem, make sure the container element has the CSS 'position' ` +\n            `property defined and the height of the element is not zero.`,\n        ),\n      );\n    }\n  });\n\n  // See comments in the `assertNoImageDistortion`.\n  const removeErrorListenerFn = renderer.listen(img, 'error', () => {\n    removeLoadListenerFn();\n    removeErrorListenerFn();\n  });\n}\n\n/**\n * Verifies that the `loading` attribute is set to a valid input &\n * is not used on priority images.\n */\nfunction assertValidLoadingInput(dir: NgOptimizedImage) {\n  if (dir.loading && dir.priority) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_INPUT,\n      `${imgDirectiveDetails(dir.ngSrc)} the \\`loading\\` attribute ` +\n        `was used on an image that was marked \"priority\". ` +\n        `Setting \\`loading\\` on priority images is not allowed ` +\n        `because these images will always be eagerly loaded. ` +\n        `To fix this, remove the “loading” attribute from the priority image.`,\n    );\n  }\n  const validInputs = ['auto', 'eager', 'lazy'];\n  if (typeof dir.loading === 'string' && !validInputs.includes(dir.loading)) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_INPUT,\n      `${imgDirectiveDetails(dir.ngSrc)} the \\`loading\\` attribute ` +\n        `has an invalid value (\\`${dir.loading}\\`). ` +\n        `To fix this, provide a valid value (\"lazy\", \"eager\", or \"auto\").`,\n    );\n  }\n}\n\n/**\n * Warns if NOT using a loader (falling back to the generic loader) and\n * the image appears to be hosted on one of the image CDNs for which\n * we do have a built-in image loader. Suggests switching to the\n * built-in loader.\n *\n * @param ngSrc Value of the ngSrc attribute\n * @param imageLoader ImageLoader provided\n */\nfunction assertNotMissingBuiltInLoader(ngSrc: string, imageLoader: ImageLoader) {\n  if (imageLoader === noopImageLoader) {\n    let builtInLoaderName = '';\n    for (const loader of BUILT_IN_LOADERS) {\n      if (loader.testUrl(ngSrc)) {\n        builtInLoaderName = loader.name;\n        break;\n      }\n    }\n    if (builtInLoaderName) {\n      console.warn(\n        formatRuntimeError(\n          RuntimeErrorCode.MISSING_BUILTIN_LOADER,\n          `NgOptimizedImage: It looks like your images may be hosted on the ` +\n            `${builtInLoaderName} CDN, but your app is not using Angular's ` +\n            `built-in loader for that CDN. We recommend switching to use ` +\n            `the built-in by calling \\`provide${builtInLoaderName}Loader()\\` ` +\n            `in your \\`providers\\` and passing it your instance's base URL. ` +\n            `If you don't want to use the built-in loader, define a custom ` +\n            `loader function using IMAGE_LOADER to silence this warning.`,\n        ),\n      );\n    }\n  }\n}\n\n/**\n * Warns if ngSrcset is present and no loader is configured (i.e. the default one is being used).\n */\nfunction assertNoNgSrcsetWithoutLoader(dir: NgOptimizedImage, imageLoader: ImageLoader) {\n  if (dir.ngSrcset && imageLoader === noopImageLoader) {\n    console.warn(\n      formatRuntimeError(\n        RuntimeErrorCode.MISSING_NECESSARY_LOADER,\n        `${imgDirectiveDetails(dir.ngSrc)} the \\`ngSrcset\\` attribute is present but ` +\n          `no image loader is configured (i.e. the default one is being used), ` +\n          `which would result in the same image being used for all configured sizes. ` +\n          `To fix this, provide a loader or remove the \\`ngSrcset\\` attribute from the image.`,\n      ),\n    );\n  }\n}\n\n/**\n * Warns if loaderParams is present and no loader is configured (i.e. the default one is being\n * used).\n */\nfunction assertNoLoaderParamsWithoutLoader(dir: NgOptimizedImage, imageLoader: ImageLoader) {\n  if (dir.loaderParams && imageLoader === noopImageLoader) {\n    console.warn(\n      formatRuntimeError(\n        RuntimeErrorCode.MISSING_NECESSARY_LOADER,\n        `${imgDirectiveDetails(dir.ngSrc)} the \\`loaderParams\\` attribute is present but ` +\n          `no image loader is configured (i.e. the default one is being used), ` +\n          `which means that the loaderParams data will not be consumed and will not affect the URL. ` +\n          `To fix this, provide a custom loader or remove the \\`loaderParams\\` attribute from the image.`,\n      ),\n    );\n  }\n}\n\nfunction round(input: number): number | string {\n  return Number.isInteger(input) ? input : input.toFixed(2);\n}\n\n// Transform function to handle SafeValue input for ngSrc. This doesn't do any sanitization,\n// as that is not needed for img.src and img.srcset. This transform is purely for compatibility.\nfunction unwrapSafeUrl(value: string | SafeValue): string {\n  if (typeof value === 'string') {\n    return value;\n  }\n  return unwrapSafeValue(value);\n}\n\n// Transform function to handle inputs which may be booleans, strings, or string representations\n// of boolean values. Used for the placeholder attribute.\nexport function booleanOrDataUrlAttribute(value: boolean | string): boolean | string {\n  if (typeof value === 'string' && value.startsWith(`data:`)) {\n    return value;\n  }\n  return booleanAttribute(value);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common package.\n */\nexport * from './private_export';\nexport * from './location/index';\nexport {formatDate} from './i18n/format_date';\nexport {formatCurrency, formatNumber, formatPercent} from './i18n/format_number';\nexport {NgLocaleLocalization, NgLocalization} from './i18n/localization';\nexport {registerLocaleData} from './i18n/locale_data';\nexport {\n  Plural,\n  NumberFormatStyle,\n  FormStyle,\n  Time,\n  TranslationWidth,\n  FormatWidth,\n  NumberSymbol,\n  WeekDay,\n  getNumberOfCurrencyDigits,\n  getCurrencySymbol,\n  getLocaleDayPeriods,\n  getLocaleDayNames,\n  getLocaleMonthNames,\n  getLocaleId,\n  getLocaleEraNames,\n  getLocaleWeekEndRange,\n  getLocaleFirstDayOfWeek,\n  getLocaleDateFormat,\n  getLocaleDateTimeFormat,\n  getLocaleExtraDayPeriodRules,\n  getLocaleExtraDayPeriods,\n  getLocalePluralCase,\n  getLocaleTimeFormat,\n  getLocaleNumberSymbol,\n  getLocaleNumberFormat,\n  getLocaleCurrencyCode,\n  getLocaleCurrencyName,\n  getLocaleCurrencySymbol,\n  getLocaleDirection,\n} from './i18n/locale_data_api';\nexport {parseCookieValue as ɵparseCookieValue} from './cookie';\nexport {CommonModule} from './common_module';\nexport {\n  NgClass,\n  NgFor,\n  NgForOf,\n  NgForOfContext,\n  NgIf,\n  NgIfContext,\n  NgPlural,\n  NgPluralCase,\n  NgStyle,\n  NgSwitch,\n  NgSwitchCase,\n  NgSwitchDefault,\n  NgTemplateOutlet,\n  NgComponentOutlet,\n} from './directives/index';\nexport {DOCUMENT} from './dom_tokens';\nexport {\n  AsyncPipe,\n  DatePipe,\n  DatePipeConfig,\n  DATE_PIPE_DEFAULT_TIMEZONE,\n  DATE_PIPE_DEFAULT_OPTIONS,\n  I18nPluralPipe,\n  I18nSelectPipe,\n  JsonPipe,\n  LowerCasePipe,\n  CurrencyPipe,\n  DecimalPipe,\n  PercentPipe,\n  SlicePipe,\n  UpperCasePipe,\n  TitleCasePipe,\n  KeyValuePipe,\n  KeyValue,\n} from './pipes/index';\nexport {\n  PLATFORM_BROWSER_ID as ɵPLATFORM_BROWSER_ID,\n  PLATFORM_SERVER_ID as ɵPLATFORM_SERVER_ID,\n  PLATFORM_WORKER_APP_ID as ɵPLATFORM_WORKER_APP_ID,\n  PLATFORM_WORKER_UI_ID as ɵPLATFORM_WORKER_UI_ID,\n  isPlatformBrowser,\n  isPlatformServer,\n  isPlatformWorkerApp,\n  isPlatformWorkerUi,\n} from './platform_id';\nexport {VERSION} from './version';\nexport {ViewportScroller, NullViewportScroller as ɵNullViewportScroller} from './viewport_scroller';\nexport {XhrFactory} from './xhr';\nexport {\n  IMAGE_CONFIG,\n  ImageConfig,\n  IMAGE_LOADER,\n  ImageLoader,\n  ImageLoaderConfig,\n  NgOptimizedImage,\n  ImagePlaceholderConfig,\n  PRECONNECT_CHECK_BLOCKLIST,\n  provideCloudflareLoader,\n  provideCloudinaryLoader,\n  provideImageKitLoader,\n  provideImgixLoader,\n  provideNetlifyLoader,\n} from './directives/ng_optimized_image';\nexport {normalizeQueryParams as ɵnormalizeQueryParams} from './location/util';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\nexport * from './src/common';\n\n// This file only reexports content of the `src` folder. Keep it that way.\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// This file is not used to build this module. It is only used during editing\n// by the TypeScript language service and during build for verification. `ngc`\n// replaces this file with production index.ts when it rewrites private symbol\n// names.\n\nexport * from './public_api';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["i1.LocationStrategy", "stringify", "RuntimeError", "formatRuntimeError", "i1.NgLocalization", "i1.<PERSON><PERSON><PERSON>", "i2.NgComponentOutlet", "i3.<PERSON><PERSON><PERSON><PERSON><PERSON>", "i4.Ng<PERSON>f", "i5.NgTemplateOutlet", "i6.<PERSON><PERSON><PERSON><PERSON>", "i7.Ng<PERSON><PERSON>", "i7.NgSwitchCase", "i7.NgSwitchDefault", "i8.<PERSON><PERSON><PERSON><PERSON>", "i8.NgPluralCase", "i9.AsyncPipe", "i10.UpperCasePipe", "i10.LowerCasePipe", "i11.JsonPipe", "i12.SlicePipe", "i13.DecimalPipe", "i13.Percent<PERSON><PERSON><PERSON>", "i10.TitleCasePipe", "i13.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i14.DatePipe", "i15.I18nPluralPipe", "i16.I18nSelectPipe", "i17.KeyValuePipe", "IMAGE_CONFIG", "performanceMarkFeature", "IMAGE_CONFIG_DEFAULTS", "unwrapSafeValue"], "mappings": ";;;;;;;;;;AAQA,IAAI,IAAI,GAAe,IAAK,CAAC;SAEb,MAAM,GAAA;AACpB,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAEK,SAAU,iBAAiB,CAAC,OAAmB,EAAA;IACnD,IAAI,KAAK,OAAO,CAAC;AACnB,CAAC;AAED;AACA;;;;;AAKG;MACmB,UAAU,CAAA;AAgC/B;;AClCD;;;AAGG;MAEmB,kBAAkB,CAAA;yHAAlB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAAlB,kBAAkB,EAAA,UAAA,EADf,UAAU,EAAc,UAAA,EAAA,MAAO,MAAc,CAAC,UAAU,EAAA,CAAA,CAAA,EAAA;;sGAC3D,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBADvC,UAAU;AAAC,YAAA,IAAA,EAAA,CAAA,EAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,MAAO,MAAc,CAAC,UAAU,EAAC,CAAA;;;ACjBlF;;;;;;AAMG;AACU,MAAA,QAAQ,GAAG,IAAI,cAAc,CAAW,SAAS,GAAG,eAAe,GAAG,EAAE;;ACJrF;;;;;;;;;;;;;;;;;;;;;AAqBG;MAEmB,gBAAgB,CAAA;AA4BpC,IAAA,SAAS,CAAE,gBAAwB,EAAA;AACjC,QAAA,MAAM,IAAI,KAAK,CAAC,SAAS,GAAG,iBAAiB,GAAG,EAAE,CAAC,CAAC;KACrD;yHA9BmB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAAhB,gBAAgB,EAAA,UAAA,EADb,UAAU,EAAc,UAAA,EAAA,MAAM,MAAM,CAAC,uBAAuB,CAAC,EAAA,CAAA,CAAA,EAAA;;sGAChE,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBADrC,UAAU;AAAC,YAAA,IAAA,EAAA,CAAA,EAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,MAAM,CAAC,uBAAuB,CAAC,EAAC,CAAA;;AAkCvF;;;;;AAKG;AACU,MAAA,oBAAoB,GAAG,IAAI,cAAc,CACpD,SAAS,GAAG,sBAAsB,GAAG,EAAE,EACvC;AAoBF;;;;;;AAMG;AAKG,MAAO,uBAAwB,SAAQ,gBAAgB,CAAA;AAK3D,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE,CAAC;AAHF,QAAA,IAAA,CAAA,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AAI9B,QAAA,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AACjC,QAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC;KAChC;IAEQ,kBAAkB,GAAA;QACzB,OAAO,MAAM,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAE,CAAC;KACzC;AAEQ,IAAA,UAAU,CAAC,EAA0B,EAAA;AAC5C,QAAA,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAClE,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;QAC/C,OAAO,MAAM,MAAM,CAAC,mBAAmB,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;KACzD;AAEQ,IAAA,YAAY,CAAC,EAA0B,EAAA;AAC9C,QAAA,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAClE,MAAM,CAAC,gBAAgB,CAAC,YAAY,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;QACjD,OAAO,MAAM,MAAM,CAAC,mBAAmB,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;KAC3D;AAED,IAAA,IAAa,IAAI,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;KAC5B;AACD,IAAA,IAAa,QAAQ,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;KAChC;AACD,IAAA,IAAa,QAAQ,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;KAChC;AACD,IAAA,IAAa,IAAI,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;KAC5B;AACD,IAAA,IAAa,QAAQ,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;KAChC;AACD,IAAA,IAAa,MAAM,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;KAC9B;AACD,IAAA,IAAa,IAAI,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;KAC5B;IACD,IAAa,QAAQ,CAAC,OAAe,EAAA;AACnC,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,OAAO,CAAC;KACnC;AAEQ,IAAA,SAAS,CAAC,KAAU,EAAE,KAAa,EAAE,GAAW,EAAA;QACvD,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;KAC5C;AAEQ,IAAA,YAAY,CAAC,KAAU,EAAE,KAAa,EAAE,GAAW,EAAA;QAC1D,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;KAC/C;IAEQ,OAAO,GAAA;AACd,QAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;KACzB;IAEQ,IAAI,GAAA;AACX,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;KACtB;IAEQ,SAAS,CAAC,mBAA2B,CAAC,EAAA;AAC7C,QAAA,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC;KACpC;IAEQ,QAAQ,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;KAC5B;yHA1EU,uBAAuB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAAvB,uBAAuB,EAAA,UAAA,EAHtB,UAAU,EACV,UAAA,EAAA,MAAM,IAAI,uBAAuB,EAAE,EAAA,CAAA,CAAA,EAAA;;sGAEpC,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBAJnC,UAAU;AAAC,YAAA,IAAA,EAAA,CAAA;AACV,oBAAA,UAAU,EAAE,UAAU;AACtB,oBAAA,UAAU,EAAE,MAAM,IAA6B,uBAAA,EAAA;AAChD,iBAAA,CAAA;;;ACnGD;;;;;;;;AAQG;AACa,SAAA,aAAa,CAAC,KAAa,EAAE,GAAW,EAAA;AACtD,IAAA,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;AACrB,QAAA,OAAO,GAAG,CAAC;KACZ;AACD,IAAA,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,EAAE;AACnB,QAAA,OAAO,KAAK,CAAC;KACd;IACD,IAAI,OAAO,GAAG,CAAC,CAAC;AAChB,IAAA,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AACvB,QAAA,OAAO,EAAE,CAAC;KACX;AACD,IAAA,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;AACvB,QAAA,OAAO,EAAE,CAAC;KACX;AACD,IAAA,IAAI,OAAO,IAAI,CAAC,EAAE;QAChB,OAAO,KAAK,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;KACjC;AACD,IAAA,IAAI,OAAO,IAAI,CAAC,EAAE;QAChB,OAAO,KAAK,GAAG,GAAG,CAAC;KACpB;AACD,IAAA,OAAO,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC;AAC3B,CAAC;AAED;;;;;;;;AAQG;AACG,SAAU,kBAAkB,CAAC,GAAW,EAAA;IAC5C,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AAClC,IAAA,MAAM,UAAU,GAAG,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,KAAK,GAAG,CAAC,MAAM,CAAC;IACxD,MAAM,eAAe,GAAG,UAAU,IAAI,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC3E,IAAA,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,eAAe,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AAC/D,CAAC;AAED;;;;;;AAMG;AACG,SAAU,oBAAoB,CAAC,MAAc,EAAA;AACjD,IAAA,OAAO,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,GAAG,GAAG,MAAM,GAAG,MAAM,CAAC;AAC7D;;AClDA;;;;;;;;;;;;;;;;AAgBG;MAEmB,gBAAgB,CAAA;AAQpC,IAAA,SAAS,CAAE,gBAAwB,EAAA;AACjC,QAAA,MAAM,IAAI,KAAK,CAAC,SAAS,GAAG,iBAAiB,GAAG,EAAE,CAAC,CAAC;KACrD;yHAVmB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAAhB,gBAAgB,EAAA,UAAA,EADb,MAAM,EAAc,UAAA,EAAA,MAAM,MAAM,CAAC,oBAAoB,CAAC,EAAA,CAAA,CAAA,EAAA;;sGACzD,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBADrC,UAAU;AAAC,YAAA,IAAA,EAAA,CAAA,EAAC,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,MAAM,CAAC,oBAAoB,CAAC,EAAC,CAAA;;AAgBhF;;;;;;;;;;;;;;;;;;;;;;AAsBG;AACU,MAAA,aAAa,GAAG,IAAI,cAAc,CAAS,SAAS,GAAG,aAAa,GAAG,EAAE,EAAE;AAExF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BG;AAEG,MAAO,oBAAqB,SAAQ,gBAAgB,CAAA;IAIxD,WACU,CAAA,iBAAmC,EACR,IAAa,EAAA;AAEhD,QAAA,KAAK,EAAE,CAAC;QAHA,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAkB;QAHrC,IAAkB,CAAA,kBAAA,GAAmB,EAAE,CAAC;AAQ9C,QAAA,IAAI,CAAC,SAAS;YACZ,IAAI;AACJ,gBAAA,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,EAAE;AAC3C,gBAAA,MAAM,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,MAAM;AACjC,gBAAA,EAAE,CAAC;KACN;;IAGD,WAAW,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE;AACrC,YAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAG,EAAE,CAAC;SAClC;KACF;AAEQ,IAAA,UAAU,CAAC,EAA0B,EAAA;QAC5C,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAC1B,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE,CAAC,EACrC,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,EAAE,CAAC,CACxC,CAAC;KACH;IAEQ,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;AAEQ,IAAA,kBAAkB,CAAC,QAAgB,EAAA;QAC1C,OAAO,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;KAChD;IAEQ,IAAI,CAAC,cAAuB,KAAK,EAAA;AACxC,QAAA,MAAM,QAAQ,GACZ,IAAI,CAAC,iBAAiB,CAAC,QAAQ,GAAG,oBAAoB,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;AACxF,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;AACzC,QAAA,OAAO,IAAI,IAAI,WAAW,GAAG,CAAA,EAAG,QAAQ,CAAA,EAAG,IAAI,CAAE,CAAA,GAAG,QAAQ,CAAC;KAC9D;AAEQ,IAAA,SAAS,CAAC,KAAU,EAAE,KAAa,EAAE,GAAW,EAAE,WAAmB,EAAA;AAC5E,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,GAAG,oBAAoB,CAAC,WAAW,CAAC,CAAC,CAAC;QACrF,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;KAC7D;AAEQ,IAAA,YAAY,CAAC,KAAU,EAAE,KAAa,EAAE,GAAW,EAAE,WAAmB,EAAA;AAC/E,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,GAAG,oBAAoB,CAAC,WAAW,CAAC,CAAC,CAAC;QACrF,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;KAChE;IAEQ,OAAO,GAAA;AACd,QAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;KAClC;IAEQ,IAAI,GAAA;AACX,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;KAC/B;IAEQ,QAAQ,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,CAAC;KAC1C;IAEQ,SAAS,CAAC,mBAA2B,CAAC,EAAA;QAC7C,IAAI,CAAC,iBAAiB,CAAC,SAAS,GAAG,gBAAgB,CAAC,CAAC;KACtD;AAtEU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,+CAMT,aAAa,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AANxB,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,cADR,MAAM,EAAA,CAAA,CAAA,EAAA;;sGAClB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBADhC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;0BAO3B,QAAQ;;0BAAI,MAAM;2BAAC,aAAa,CAAA;;;ACjGrC;;;;;;;;;;;;;;;;;AAiBG;AAEG,MAAO,oBAAqB,SAAQ,gBAAgB,CAAA;IAIxD,WACU,CAAA,iBAAmC,EACR,SAAkB,EAAA;AAErD,QAAA,KAAK,EAAE,CAAC;QAHA,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAkB;QAJrC,IAAS,CAAA,SAAA,GAAW,EAAE,CAAC;QACvB,IAAkB,CAAA,kBAAA,GAAmB,EAAE,CAAC;AAO9C,QAAA,IAAI,SAAS,IAAI,IAAI,EAAE;AACrB,YAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;SAC5B;KACF;;IAGD,WAAW,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE;AACrC,YAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAG,EAAE,CAAC;SAClC;KACF;AAEQ,IAAA,UAAU,CAAC,EAA0B,EAAA;QAC5C,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAC1B,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE,CAAC,EACrC,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,EAAE,CAAC,CACxC,CAAC;KACH;IAEQ,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IAEQ,IAAI,CAAC,cAAuB,KAAK,EAAA;;;QAGxC,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,IAAI,GAAG,CAAC;AAEhD,QAAA,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;KACnD;AAEQ,IAAA,kBAAkB,CAAC,QAAgB,EAAA;QAC1C,MAAM,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AACpD,QAAA,OAAO,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;KACzC;AAEQ,IAAA,SAAS,CAAC,KAAU,EAAE,KAAa,EAAE,IAAY,EAAE,WAAmB,EAAA;AAC7E,QAAA,IAAI,GAAG,GAAkB,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,oBAAoB,CAAC,WAAW,CAAC,CAAC,CAAC;AAC3F,QAAA,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,EAAE;AACnB,YAAA,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC;SACvC;QACD,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;KACrD;AAEQ,IAAA,YAAY,CAAC,KAAU,EAAE,KAAa,EAAE,IAAY,EAAE,WAAmB,EAAA;AAChF,QAAA,IAAI,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,oBAAoB,CAAC,WAAW,CAAC,CAAC,CAAC;AAC5E,QAAA,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,EAAE;AACnB,YAAA,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC;SACvC;QACD,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;KACxD;IAEQ,OAAO,GAAA;AACd,QAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;KAClC;IAEQ,IAAI,GAAA;AACX,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;KAC/B;IAEQ,QAAQ,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,CAAC;KAC1C;IAEQ,SAAS,CAAC,mBAA2B,CAAC,EAAA;QAC7C,IAAI,CAAC,iBAAiB,CAAC,SAAS,GAAG,gBAAgB,CAAC,CAAC;KACtD;AA3EU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,+CAMT,aAAa,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HANxB,oBAAoB,EAAA,CAAA,CAAA,EAAA;;sGAApB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBADhC,UAAU;;0BAON,QAAQ;;0BAAI,MAAM;2BAAC,aAAa,CAAA;;;ACjBrC;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BG;MAMU,QAAQ,CAAA;AAYnB,IAAA,WAAA,CAAY,gBAAkC,EAAA;;AAV9C,QAAA,IAAA,CAAA,QAAQ,GAAsB,IAAI,YAAY,EAAE,CAAC;;QAMjD,IAAmB,CAAA,mBAAA,GAA8C,EAAE,CAAC;;QAEpE,IAAsB,CAAA,sBAAA,GAA4B,IAAI,CAAC;AAGrD,QAAA,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,CAAC;;;;;;;AAOtD,QAAA,IAAI,CAAC,SAAS,GAAG,YAAY,CAAC,kBAAkB,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC7E,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,EAAE,KAAI;AACvC,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACjB,gBAAA,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AACtB,gBAAA,KAAK,EAAE,IAAI;gBACX,OAAO,EAAE,EAAE,CAAC,KAAK;gBACjB,MAAM,EAAE,EAAE,CAAC,IAAI;AAChB,aAAA,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;KACJ;;IAGD,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,sBAAsB,EAAE,WAAW,EAAE,CAAC;AAC3C,QAAA,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;KAC/B;AAED;;;;;;AAMG;;;IAGH,IAAI,CAAC,cAAuB,KAAK,EAAA;AAC/B,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;KACjE;AAED;;;AAGG;IACH,QAAQ,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,CAAC;KAC1C;AAED;;;;;;;;AAQG;AACH,IAAA,oBAAoB,CAAC,IAAY,EAAE,KAAA,GAAgB,EAAE,EAAA;AACnD,QAAA,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC;KAC1E;AAED;;;;;;AAMG;AACH,IAAA,SAAS,CAAC,GAAW,EAAA;AACnB,QAAA,OAAO,QAAQ,CAAC,kBAAkB,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;KAC1F;AAED;;;;;;;;;AASG;AACH,IAAA,kBAAkB,CAAC,GAAW,EAAA;QAC5B,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AACzB,YAAA,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;SACjB;QACD,OAAO,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;KACvD;;AAGD;;;;;;;;AAQG;AACH,IAAA,EAAE,CAAC,IAAY,EAAE,QAAgB,EAAE,EAAE,QAAa,IAAI,EAAA;AACpD,QAAA,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AACzD,QAAA,IAAI,CAAC,yBAAyB,CAC5B,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,oBAAoB,CAAC,KAAK,CAAC,CAAC,EAC3D,KAAK,CACN,CAAC;KACH;AAED;;;;;;;AAOG;AACH,IAAA,YAAY,CAAC,IAAY,EAAE,QAAgB,EAAE,EAAE,QAAa,IAAI,EAAA;AAC9D,QAAA,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AAC5D,QAAA,IAAI,CAAC,yBAAyB,CAC5B,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,oBAAoB,CAAC,KAAK,CAAC,CAAC,EAC3D,KAAK,CACN,CAAC;KACH;AAED;;AAEG;IACH,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;KAClC;AAED;;AAEG;IACH,IAAI,GAAA;AACF,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;KAC/B;AAED;;;;;;;;;;;AAWG;IACH,SAAS,CAAC,mBAA2B,CAAC,EAAA;QACpC,IAAI,CAAC,iBAAiB,CAAC,SAAS,GAAG,gBAAgB,CAAC,CAAC;KACtD;AAED;;;;;;AAMG;AACH,IAAA,WAAW,CAAC,EAAyC,EAAA;AACnD,QAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAElC,IAAI,CAAC,sBAAsB,KAAK,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,KAAI;YACnD,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;AACjD,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,MAAK;YACV,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACrD,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAE5C,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE;AACzC,gBAAA,IAAI,CAAC,sBAAsB,EAAE,WAAW,EAAE,CAAC;AAC3C,gBAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;aACpC;AACH,SAAC,CAAC;KACH;;AAGD,IAAA,yBAAyB,CAAC,GAAA,GAAc,EAAE,EAAE,KAAc,EAAA;AACxD,QAAA,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;KAC1D;AAED;;;;;;;;;;;;AAYG;AACH,IAAA,SAAS,CACP,MAAsC,EACtC,OAA2C,EAC3C,QAA8B,EAAA;QAE9B,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAC,CAAC,CAAC;KACpF;AAED;;;;;;AAMG;aACW,IAAoB,CAAA,oBAAA,GAA+B,oBAA/B,CAAoD,EAAA;AAEtF;;;;;;;;AAQG;aACW,IAAa,CAAA,aAAA,GAA2C,aAA3C,CAAyD,EAAA;AAEpF;;;;;;;;AAQG;aACW,IAAkB,CAAA,kBAAA,GAA4B,kBAA5B,CAA+C,EAAA;yHApPpE,QAAQ,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAA,gBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAAR,QAAQ,EAAA,UAAA,EAJP,MAAM,EAAA,UAAA,EAEN,cAAc,EAAA,CAAA,CAAA,EAAA;;sGAEf,QAAQ,EAAA,UAAA,EAAA,CAAA;kBALpB,UAAU;AAAC,YAAA,IAAA,EAAA,CAAA;AACV,oBAAA,UAAU,EAAE,MAAM;;AAElB,oBAAA,UAAU,EAAE,cAAc;AAC3B,iBAAA,CAAA;;SAwPe,cAAc,GAAA;IAC5B,OAAO,IAAI,QAAQ,CAAC,QAAQ,CAAC,gBAAuB,CAAC,CAAC,CAAC;AACzD,CAAC;AAED,SAAS,cAAc,CAAC,QAAgB,EAAE,GAAW,EAAA;IACnD,IAAI,CAAC,QAAQ,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;AAC1C,QAAA,OAAO,GAAG,CAAC;KACZ;IACD,MAAM,WAAW,GAAG,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACnD,IAAI,WAAW,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;AACvE,QAAA,OAAO,WAAW,CAAC;KACpB;AACD,IAAA,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,eAAe,CAAC,GAAW,EAAA;IAClC,OAAO,GAAG,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;AAC1C,CAAC;AAED,SAAS,YAAY,CAAC,QAAgB,EAAA;;;;;;AAMpC,IAAA,MAAM,aAAa,GAAG,IAAI,MAAM,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACjE,IAAI,aAAa,EAAE;QACjB,MAAM,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;AAClD,QAAA,OAAO,QAAQ,CAAC;KACjB;AACD,IAAA,OAAO,QAAQ,CAAC;AAClB;;AClUA;AACO,MAAM,aAAa,GAA2F,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,IAAI,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,MAAM,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,OAAO,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,MAAM,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC;;ACOl6G;;;;;;AAMG;IACS,kBAKX;AALD,CAAA,UAAY,iBAAiB,EAAA;AAC3B,IAAA,iBAAA,CAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO,CAAA;AACP,IAAA,iBAAA,CAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO,CAAA;AACP,IAAA,iBAAA,CAAA,iBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ,CAAA;AACR,IAAA,iBAAA,CAAA,iBAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAU,CAAA;AACZ,CAAC,EALW,iBAAiB,KAAjB,iBAAiB,GAK5B,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;;;AAQG;IACS,OAOX;AAPD,CAAA,UAAY,MAAM,EAAA;AAChB,IAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ,CAAA;AACR,IAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAO,CAAA;AACP,IAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAO,CAAA;AACP,IAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAO,CAAA;AACP,IAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ,CAAA;AACR,IAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS,CAAA;AACX,CAAC,EAPW,MAAM,KAAN,MAAM,GAOjB,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;;;AAQG;IACS,UAGX;AAHD,CAAA,UAAY,SAAS,EAAA;AACnB,IAAA,SAAA,CAAA,SAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM,CAAA;AACN,IAAA,SAAA,CAAA,SAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAU,CAAA;AACZ,CAAC,EAHW,SAAS,KAAT,SAAS,GAGpB,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;AAMG;IACS,iBASX;AATD,CAAA,UAAY,gBAAgB,EAAA;;AAE1B,IAAA,gBAAA,CAAA,gBAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM,CAAA;;AAEN,IAAA,gBAAA,CAAA,gBAAA,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA,GAAA,aAAW,CAAA;;AAEX,IAAA,gBAAA,CAAA,gBAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;;AAEJ,IAAA,gBAAA,CAAA,gBAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK,CAAA;AACP,CAAC,EATW,gBAAgB,KAAhB,gBAAgB,GAS3B,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;;;;;AAUG;IACS,YAqBX;AArBD,CAAA,UAAY,WAAW,EAAA;AACrB;;;AAGG;AACH,IAAA,WAAA,CAAA,WAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK,CAAA;AACL;;;AAGG;AACH,IAAA,WAAA,CAAA,WAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM,CAAA;AACN;;;AAGG;AACH,IAAA,WAAA,CAAA,WAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;AACJ;;;AAGG;AACH,IAAA,WAAA,CAAA,WAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;AACN,CAAC,EArBW,WAAW,KAAX,WAAW,GAqBtB,EAAA,CAAA,CAAA,CAAA;AAED;AACA;AACA;;;;;;;;;AASG;AACU,MAAA,YAAY,GAAG;AAC1B;;;;AAIG;AACH,IAAA,OAAO,EAAE,CAAC;AACV;;;;AAIG;AACH,IAAA,KAAK,EAAE,CAAC;AACR;;;AAGG;AACH,IAAA,IAAI,EAAE,CAAC;AACP;;;AAGG;AACH,IAAA,WAAW,EAAE,CAAC;AACd;;;AAGG;AACH,IAAA,QAAQ,EAAE,CAAC;AACX;;;AAGG;AACH,IAAA,SAAS,EAAE,CAAC;AACZ;;;AAGG;AACH,IAAA,WAAW,EAAE,CAAC;AACd;;;AAGG;AACH,IAAA,sBAAsB,EAAE,CAAC;AACzB;;;AAGG;AACH,IAAA,QAAQ,EAAE,CAAC;AACX;;;AAGG;AACH,IAAA,QAAQ,EAAE,CAAC;AACX;;;AAGG;AACH,IAAA,GAAG,EAAE,EAAE;AACP;;;AAGG;AACH,IAAA,aAAa,EAAE,EAAE;AACjB;;;AAGG;AACH,IAAA,eAAe,EAAE,EAAE;AACnB;;;AAGG;AACH,IAAA,aAAa,EAAE,EAAE;EACR;AAIX;;;;AAIG;IACS,QAQX;AARD,CAAA,UAAY,OAAO,EAAA;AACjB,IAAA,OAAA,CAAA,OAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU,CAAA;AACV,IAAA,OAAA,CAAA,OAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM,CAAA;AACN,IAAA,OAAA,CAAA,OAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO,CAAA;AACP,IAAA,OAAA,CAAA,OAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAS,CAAA;AACT,IAAA,OAAA,CAAA,OAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ,CAAA;AACR,IAAA,OAAA,CAAA,OAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM,CAAA;AACN,IAAA,OAAA,CAAA,OAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ,CAAA;AACV,CAAC,EARW,OAAO,KAAP,OAAO,GAQlB,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;;;AAQG;AACG,SAAU,WAAW,CAAC,MAAc,EAAA;IACxC,OAAO,eAAe,CAAC,MAAM,CAAC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AAC5D,CAAC;AAED;;;;;;;;;;AAUG;SACa,mBAAmB,CACjC,MAAc,EACd,SAAoB,EACpB,KAAuB,EAAA;AAEvB,IAAA,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;AACrC,IAAA,MAAM,QAAQ,GAAyB;AACrC,QAAA,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC;AACvC,QAAA,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,CAAC;KAC5C,CAAC;IACF,MAAM,IAAI,GAAG,mBAAmB,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;AACtD,IAAA,OAAO,mBAAmB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAC1C,CAAC;AAED;;;;;;;;;;;AAWG;SACa,iBAAiB,CAC/B,MAAc,EACd,SAAoB,EACpB,KAAuB,EAAA;AAEvB,IAAA,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;AACrC,IAAA,MAAM,QAAQ,GAAiB;AAC7B,QAAA,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC;AACjC,QAAA,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC;KACtC,CAAC;IACF,MAAM,IAAI,GAAG,mBAAmB,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;AACtD,IAAA,OAAO,mBAAmB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAC1C,CAAC;AAED;;;;;;;;;;;AAWG;SACa,mBAAmB,CACjC,MAAc,EACd,SAAoB,EACpB,KAAuB,EAAA;AAEvB,IAAA,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;AACrC,IAAA,MAAM,UAAU,GAAiB;AAC/B,QAAA,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC;AACnC,QAAA,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC;KACxC,CAAC;IACF,MAAM,MAAM,GAAG,mBAAmB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;AAC1D,IAAA,OAAO,mBAAmB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAC5C,CAAC;AAED;;;;;;;;;;AAUG;AACa,SAAA,iBAAiB,CAC/B,MAAc,EACd,KAAuB,EAAA;AAEvB,IAAA,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IACrC,MAAM,QAAQ,GAAuB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;AACjE,IAAA,OAAO,mBAAmB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AAC9C,CAAC;AAED;;;;;;;;;;AAUG;AACG,SAAU,uBAAuB,CAAC,MAAc,EAAA;AACpD,IAAA,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;AACrC,IAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;AAC/C,CAAC;AAED;;;;;;;;AAQG;AACG,SAAU,qBAAqB,CAAC,MAAc,EAAA;AAClD,IAAA,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;AACrC,IAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;AAC7C,CAAC;AAED;;;;;;;;;;AAUG;AACa,SAAA,mBAAmB,CAAC,MAAc,EAAE,KAAkB,EAAA;AACpE,IAAA,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IACrC,OAAO,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC,CAAC;AACvE,CAAC;AAED;;;;;;;;;;AAUG;AACa,SAAA,mBAAmB,CAAC,MAAc,EAAE,KAAkB,EAAA;AACpE,IAAA,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IACrC,OAAO,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC,CAAC;AACvE,CAAC;AAED;;;;;;;;;;AAUG;AACa,SAAA,uBAAuB,CAAC,MAAc,EAAE,KAAkB,EAAA;AACxE,IAAA,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IACrC,MAAM,kBAAkB,GAAa,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;AAC3E,IAAA,OAAO,mBAAmB,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;AACxD,CAAC;AAED;;;;;;;;;AASG;AACa,SAAA,qBAAqB,CAAC,MAAc,EAAE,MAAoB,EAAA;AACxE,IAAA,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IACrC,MAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC;AACzD,IAAA,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;AAC9B,QAAA,IAAI,MAAM,KAAK,YAAY,CAAC,eAAe,EAAE;YAC3C,OAAO,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;SACnE;AAAM,aAAA,IAAI,MAAM,KAAK,YAAY,CAAC,aAAa,EAAE;YAChD,OAAO,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;SACjE;KACF;AACD,IAAA,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCG;AACa,SAAA,qBAAqB,CAAC,MAAc,EAAE,IAAuB,EAAA;AAC3E,IAAA,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IACrC,OAAO,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC;AACpD,CAAC;AAED;;;;;;;;;;AAUG;AACG,SAAU,uBAAuB,CAAC,MAAc,EAAA;AACpD,IAAA,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IACrC,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;AACvD,CAAC;AAED;;;;;;;;;AASG;AACG,SAAU,qBAAqB,CAAC,MAAc,EAAA;AAClD,IAAA,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IACrC,OAAO,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC;AACrD,CAAC;AAED;;;;;;;;;AASG;AACG,SAAU,qBAAqB,CAAC,MAAc,EAAA;AAClD,IAAA,OAAO,sBAAsB,CAAC,MAAM,CAAC,CAAC;AACxC,CAAC;AAED;;;;;AAKG;AACH,SAAS,mBAAmB,CAAC,MAAc,EAAA;AACzC,IAAA,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;AACrC,IAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;AAC3C,CAAC;AAED;;;AAGG;AACI,MAAM,mBAAmB,GAC9B,qBAAqB;AAEvB,SAAS,aAAa,CAAC,IAAS,EAAA;IAC9B,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,EAAE;AACrC,QAAA,MAAM,IAAI,KAAK,CACb,CAAA,0CAAA,EACE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAChC,CAAgG,8FAAA,CAAA,CACjG,CAAC;KACH;AACH,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;AAqBG;AACG,SAAU,4BAA4B,CAAC,MAAc,EAAA;AACzD,IAAA,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IACrC,aAAa,CAAC,IAAI,CAAC,CAAC;IACpB,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAA,CAAA,kDAA4C,IAAI,EAAE,CAAC;AACjG,IAAA,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,IAA+B,KAAI;AACnD,QAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC5B,YAAA,OAAO,WAAW,CAAC,IAAI,CAAC,CAAC;SAC1B;AACD,QAAA,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD,KAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;;;;;;;;;;;AAgBG;SACa,wBAAwB,CACtC,MAAc,EACd,SAAoB,EACpB,KAAuB,EAAA;AAEvB,IAAA,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IACrC,aAAa,CAAC,IAAI,CAAC,CAAC;AACpB,IAAA,MAAM,cAAc,GAAiB;AACnC,QAAA,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAA6C,CAAA,mDAAA;AAC7E,QAAA,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAgD,CAAA,sDAAA;KACjF,CAAC;IACF,MAAM,UAAU,GAAG,mBAAmB,CAAC,cAAc,EAAE,SAAS,CAAC,IAAI,EAAE,CAAC;IACxE,OAAO,mBAAmB,CAAC,UAAU,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC;AACtD,CAAC;AAED;;;;;;AAMG;AACG,SAAU,kBAAkB,CAAC,MAAc,EAAA;AAC/C,IAAA,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;AACrC,IAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;AAC/C,CAAC;AAED;;;;;;;;;;;;AAYG;AACH,SAAS,mBAAmB,CAAI,IAAS,EAAE,KAAa,EAAA;AACtD,IAAA,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAC/B,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE;AAClC,YAAA,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;SAChB;KACF;AACD,IAAA,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;AAC5D,CAAC;AAYD;;AAEG;AACH,SAAS,WAAW,CAAC,IAAY,EAAA;AAC/B,IAAA,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC/B,OAAO,EAAC,KAAK,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,EAAC,CAAC;AAClC,CAAC;AAED;;;;;;;;;;;;;;AAcG;AACG,SAAU,iBAAiB,CAAC,IAAY,EAAE,MAAyB,EAAE,MAAM,GAAG,IAAI,EAAA;AACtF,IAAA,MAAM,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;AAChF,IAAA,MAAM,YAAY,GAAG,QAAQ,CAAA,CAAA,mCAA6B,CAAC;IAE3D,IAAI,MAAM,KAAK,QAAQ,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;AAC3D,QAAA,OAAO,YAAY,CAAC;KACrB;AAED,IAAA,OAAO,QAAQ,CAAA,CAAA,6BAAuB,IAAI,IAAI,CAAC;AACjD,CAAC;AAED;AACA,MAAM,6BAA6B,GAAG,CAAC,CAAC;AAExC;;;;;;;;;AASG;AACG,SAAU,yBAAyB,CAAC,IAAY,EAAA;AACpD,IAAA,IAAI,MAAM,CAAC;AACX,IAAA,MAAM,QAAQ,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;IACrC,IAAI,QAAQ,EAAE;QACZ,MAAM,GAAG,QAAQ,CAAA,CAAA,iCAA2B,CAAC;KAC9C;AACD,IAAA,OAAO,OAAO,MAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,6BAA6B,CAAC;AAC7E;;AC3qBO,MAAM,kBAAkB,GAC7B,uGAAuG,CAAC;AAC1G;AACA,MAAM,aAAa,GAAqD,EAAE,CAAC;AAC3E,MAAM,kBAAkB,GACtB,mNAAmN,CAAC;AAEtN,IAAK,SAKJ,CAAA;AALD,CAAA,UAAK,SAAS,EAAA;AACZ,IAAA,SAAA,CAAA,SAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK,CAAA;AACL,IAAA,SAAA,CAAA,SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ,CAAA;AACR,IAAA,SAAA,CAAA,SAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;AACJ,IAAA,SAAA,CAAA,SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ,CAAA;AACV,CAAC,EALI,SAAS,KAAT,SAAS,GAKb,EAAA,CAAA,CAAA,CAAA;AAED,IAAK,QASJ,CAAA;AATD,CAAA,UAAK,QAAQ,EAAA;AACX,IAAA,QAAA,CAAA,QAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ,CAAA;AACR,IAAA,QAAA,CAAA,QAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK,CAAA;AACL,IAAA,QAAA,CAAA,QAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;AACJ,IAAA,QAAA,CAAA,QAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK,CAAA;AACL,IAAA,QAAA,CAAA,QAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO,CAAA;AACP,IAAA,QAAA,CAAA,QAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO,CAAA;AACP,IAAA,QAAA,CAAA,QAAA,CAAA,mBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,mBAAiB,CAAA;AACjB,IAAA,QAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAG,CAAA;AACL,CAAC,EATI,QAAQ,KAAR,QAAQ,GASZ,EAAA,CAAA,CAAA,CAAA;AAED,IAAK,eAKJ,CAAA;AALD,CAAA,UAAK,eAAe,EAAA;AAClB,IAAA,eAAA,CAAA,eAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAU,CAAA;AACV,IAAA,eAAA,CAAA,eAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;AACJ,IAAA,eAAA,CAAA,eAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM,CAAA;AACN,IAAA,eAAA,CAAA,eAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;AACN,CAAC,EALI,eAAe,KAAf,eAAe,GAKnB,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;;;;;;;;;;;;;;;AAoBG;AACG,SAAU,UAAU,CACxB,KAA6B,EAC7B,MAAc,EACd,MAAc,EACd,QAAiB,EAAA;AAEjB,IAAA,IAAI,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IACzB,MAAM,WAAW,GAAG,cAAc,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACnD,IAAA,MAAM,GAAG,WAAW,IAAI,MAAM,CAAC;IAE/B,IAAI,KAAK,GAAa,EAAE,CAAC;AACzB,IAAA,IAAI,KAAK,CAAC;IACV,OAAO,MAAM,EAAE;AACb,QAAA,KAAK,GAAG,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACxC,IAAI,KAAK,EAAE;AACT,YAAA,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACrC,YAAA,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;YACzB,IAAI,CAAC,IAAI,EAAE;gBACT,MAAM;aACP;YACD,MAAM,GAAG,IAAI,CAAC;SACf;aAAM;AACL,YAAA,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACnB,MAAM;SACP;KACF;AAED,IAAA,IAAI,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAClD,IAAI,QAAQ,EAAE;AACZ,QAAA,kBAAkB,GAAG,gBAAgB,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC;QACpE,IAAI,GAAG,sBAAsB,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;KACrD;IAED,IAAI,IAAI,GAAG,EAAE,CAAC;AACd,IAAA,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;AACtB,QAAA,MAAM,aAAa,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAC9C,QAAA,IAAI,IAAI,aAAa;cACjB,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,kBAAkB,CAAC;cAC/C,KAAK,KAAK,IAAI;AAChB,kBAAE,GAAG;AACL,kBAAE,KAAK,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AACxD,KAAC,CAAC,CAAC;AAEH,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;;;;AASG;AACH,SAAS,UAAU,CAAC,IAAY,EAAE,KAAa,EAAE,IAAY,EAAA;;;;;AAK3D,IAAA,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;;;;;;IAQ5B,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;;;;IAIvC,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAE1B,IAAA,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,cAAc,CAAC,MAAc,EAAE,MAAc,EAAA;AACpD,IAAA,MAAM,QAAQ,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;AACrC,IAAA,aAAa,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;IAE/B,IAAI,aAAa,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,EAAE;AACnC,QAAA,OAAO,aAAa,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC;KACxC;IAED,IAAI,WAAW,GAAG,EAAE,CAAC;IACrB,QAAQ,MAAM;AACZ,QAAA,KAAK,WAAW;YACd,WAAW,GAAG,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;YAC7D,MAAM;AACR,QAAA,KAAK,YAAY;YACf,WAAW,GAAG,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;YAC9D,MAAM;AACR,QAAA,KAAK,UAAU;YACb,WAAW,GAAG,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM;AACR,QAAA,KAAK,UAAU;YACb,WAAW,GAAG,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM;AACR,QAAA,KAAK,WAAW;YACd,WAAW,GAAG,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;YAC7D,MAAM;AACR,QAAA,KAAK,YAAY;YACf,WAAW,GAAG,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;YAC9D,MAAM;AACR,QAAA,KAAK,UAAU;YACb,WAAW,GAAG,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM;AACR,QAAA,KAAK,UAAU;YACb,WAAW,GAAG,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM;AACR,QAAA,KAAK,OAAO;YACV,MAAM,SAAS,GAAG,cAAc,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;YACtD,MAAM,SAAS,GAAG,cAAc,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;YACtD,WAAW,GAAG,cAAc,CAAC,uBAAuB,CAAC,MAAM,EAAE,WAAW,CAAC,KAAK,CAAC,EAAE;gBAC/E,SAAS;gBACT,SAAS;AACV,aAAA,CAAC,CAAC;YACH,MAAM;AACR,QAAA,KAAK,QAAQ;YACX,MAAM,UAAU,GAAG,cAAc,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;YACxD,MAAM,UAAU,GAAG,cAAc,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;YACxD,WAAW,GAAG,cAAc,CAAC,uBAAuB,CAAC,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,EAAE;gBAChF,UAAU;gBACV,UAAU;AACX,aAAA,CAAC,CAAC;YACH,MAAM;AACR,QAAA,KAAK,MAAM;YACT,MAAM,QAAQ,GAAG,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YACpD,MAAM,QAAQ,GAAG,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YACpD,WAAW,GAAG,cAAc,CAAC,uBAAuB,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE;gBAC9E,QAAQ;gBACR,QAAQ;AACT,aAAA,CAAC,CAAC;YACH,MAAM;AACR,QAAA,KAAK,MAAM;YACT,MAAM,QAAQ,GAAG,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YACpD,MAAM,QAAQ,GAAG,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YACpD,WAAW,GAAG,cAAc,CAAC,uBAAuB,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE;gBAC9E,QAAQ;gBACR,QAAQ;AACT,aAAA,CAAC,CAAC;YACH,MAAM;KACT;IACD,IAAI,WAAW,EAAE;QACf,aAAa,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,GAAG,WAAW,CAAC;KAC/C;AACD,IAAA,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,SAAS,cAAc,CAAC,GAAW,EAAE,UAAoB,EAAA;IACvD,IAAI,UAAU,EAAE;QACd,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,EAAE,UAAU,KAAK,EAAE,GAAG,EAAA;AACnD,YAAA,OAAO,UAAU,IAAI,IAAI,IAAI,GAAG,IAAI,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAC3E,SAAC,CAAC,CAAC;KACJ;AACD,IAAA,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,SAAS,CAChB,GAAW,EACX,MAAc,EACd,SAAS,GAAG,GAAG,EACf,IAAc,EACd,OAAiB,EAAA;IAEjB,IAAI,GAAG,GAAG,EAAE,CAAC;AACb,IAAA,IAAI,GAAG,GAAG,CAAC,KAAK,OAAO,IAAI,GAAG,IAAI,CAAC,CAAC,EAAE;QACpC,IAAI,OAAO,EAAE;AACX,YAAA,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;SAChB;aAAM;YACL,GAAG,GAAG,CAAC,GAAG,CAAC;YACX,GAAG,GAAG,SAAS,CAAC;SACjB;KACF;AACD,IAAA,IAAI,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;AACzB,IAAA,OAAO,MAAM,CAAC,MAAM,GAAG,MAAM,EAAE;AAC7B,QAAA,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC;KACvB;IACD,IAAI,IAAI,EAAE;QACR,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;KAC/C;IACD,OAAO,GAAG,GAAG,MAAM,CAAC;AACtB,CAAC;AAED,SAAS,uBAAuB,CAAC,YAAoB,EAAE,MAAc,EAAA;IACnE,MAAM,KAAK,GAAG,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;IACzC,OAAO,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACpC,CAAC;AAED;;AAEG;AACH,SAAS,UAAU,CACjB,IAAc,EACd,IAAY,EACZ,MAAA,GAAiB,CAAC,EAClB,IAAI,GAAG,KAAK,EACZ,OAAO,GAAG,KAAK,EAAA;IAEf,OAAO,UAAU,IAAU,EAAE,MAAc,EAAA;QACzC,IAAI,IAAI,GAAG,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACnC,IAAI,MAAM,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,MAAM,EAAE;YAChC,IAAI,IAAI,MAAM,CAAC;SAChB;AAED,QAAA,IAAI,IAAI,KAAK,QAAQ,CAAC,KAAK,EAAE;YAC3B,IAAI,IAAI,KAAK,CAAC,IAAI,MAAM,KAAK,CAAC,EAAE,EAAE;gBAChC,IAAI,GAAG,EAAE,CAAC;aACX;SACF;AAAM,aAAA,IAAI,IAAI,KAAK,QAAQ,CAAC,iBAAiB,EAAE;AAC9C,YAAA,OAAO,uBAAuB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SAC5C;QAED,MAAM,WAAW,GAAG,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC;AAC1E,QAAA,OAAO,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAC3D,KAAC,CAAC;AACJ,CAAC;AAED,SAAS,WAAW,CAAC,IAAc,EAAE,IAAU,EAAA;IAC7C,QAAQ,IAAI;QACV,KAAK,QAAQ,CAAC,QAAQ;AACpB,YAAA,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;QAC5B,KAAK,QAAQ,CAAC,KAAK;AACjB,YAAA,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;QACzB,KAAK,QAAQ,CAAC,IAAI;AAChB,YAAA,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;QACxB,KAAK,QAAQ,CAAC,KAAK;AACjB,YAAA,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;QACzB,KAAK,QAAQ,CAAC,OAAO;AACnB,YAAA,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;QAC3B,KAAK,QAAQ,CAAC,OAAO;AACnB,YAAA,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;QAC3B,KAAK,QAAQ,CAAC,iBAAiB;AAC7B,YAAA,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC;QAChC,KAAK,QAAQ,CAAC,GAAG;AACf,YAAA,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;AACvB,QAAA;AACE,YAAA,MAAM,IAAI,KAAK,CAAC,2BAA2B,IAAI,CAAA,EAAA,CAAI,CAAC,CAAC;KACxD;AACH,CAAC;AAED;;AAEG;AACH,SAAS,aAAa,CACpB,IAAqB,EACrB,KAAuB,EACvB,IAAkB,GAAA,SAAS,CAAC,MAAM,EAClC,QAAQ,GAAG,KAAK,EAAA;IAEhB,OAAO,UAAU,IAAU,EAAE,MAAc,EAAA;AACzC,QAAA,OAAO,kBAAkB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;AACvE,KAAC,CAAC;AACJ,CAAC;AAED;;AAEG;AACH,SAAS,kBAAkB,CACzB,IAAU,EACV,MAAc,EACd,IAAqB,EACrB,KAAuB,EACvB,IAAe,EACf,QAAiB,EAAA;IAEjB,QAAQ,IAAI;QACV,KAAK,eAAe,CAAC,MAAM;AACzB,YAAA,OAAO,mBAAmB,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QACnE,KAAK,eAAe,CAAC,IAAI;AACvB,YAAA,OAAO,iBAAiB,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;QAC/D,KAAK,eAAe,CAAC,UAAU;AAC7B,YAAA,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AACrC,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YACzC,IAAI,QAAQ,EAAE;AACZ,gBAAA,MAAM,KAAK,GAAG,4BAA4B,CAAC,MAAM,CAAC,CAAC;gBACnD,MAAM,UAAU,GAAG,wBAAwB,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;gBACjE,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,KAAI;AACrC,oBAAA,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;;AAEvB,wBAAA,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC;AACxB,wBAAA,MAAM,SAAS,GAAG,YAAY,IAAI,IAAI,CAAC,KAAK,IAAI,cAAc,IAAI,IAAI,CAAC,OAAO,CAAC;wBAC/E,MAAM,QAAQ,GACZ,YAAY,GAAG,EAAE,CAAC,KAAK,KAAK,YAAY,KAAK,EAAE,CAAC,KAAK,IAAI,cAAc,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC;;;;;;;;;;;wBAWxF,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE;AACzB,4BAAA,IAAI,SAAS,IAAI,QAAQ,EAAE;AACzB,gCAAA,OAAO,IAAI,CAAC;6BACb;yBACF;AAAM,6BAAA,IAAI,SAAS,IAAI,QAAQ,EAAE;AAChC,4BAAA,OAAO,IAAI,CAAC;yBACb;qBACF;yBAAM;;AAEL,wBAAA,IAAI,IAAI,CAAC,KAAK,KAAK,YAAY,IAAI,IAAI,CAAC,OAAO,KAAK,cAAc,EAAE;AAClE,4BAAA,OAAO,IAAI,CAAC;yBACb;qBACF;AACD,oBAAA,OAAO,KAAK,CAAC;AACf,iBAAC,CAAC,CAAC;AACH,gBAAA,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;AAChB,oBAAA,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC;iBAC1B;aACF;;YAED,OAAO,mBAAmB,CAAC,MAAM,EAAE,IAAI,EAAoB,KAAK,CAAC,CAAC,YAAY,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/F,KAAK,eAAe,CAAC,IAAI;YACvB,OAAO,iBAAiB,CAAC,MAAM,EAAoB,KAAK,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC7F,QAAA;;;;;YAKE,MAAM,UAAU,GAAU,IAAI,CAAC;AAC/B,YAAA,MAAM,IAAI,KAAK,CAAC,+BAA+B,UAAU,CAAA,CAAE,CAAC,CAAC;KAChE;AACH,CAAC;AAED;;;;AAIG;AACH,SAAS,cAAc,CAAC,KAAgB,EAAA;AACtC,IAAA,OAAO,UAAU,IAAU,EAAE,MAAc,EAAE,MAAc,EAAA;AACzD,QAAA,MAAM,IAAI,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;QACzB,MAAM,SAAS,GAAG,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC;QACxE,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;QACtE,QAAQ,KAAK;YACX,KAAK,SAAS,CAAC,KAAK;AAClB,gBAAA,QACE,CAAC,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE;AACrB,oBAAA,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,SAAS,CAAC;AAC9B,oBAAA,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,EAC5C;YACJ,KAAK,SAAS,CAAC,QAAQ;gBACrB,OAAO,KAAK,IAAI,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;YACzE,KAAK,SAAS,CAAC,IAAI;AACjB,gBAAA,QACE,KAAK;qBACJ,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;AACtB,oBAAA,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,SAAS,CAAC;oBAC9B,GAAG;AACH,oBAAA,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,EAC5C;YACJ,KAAK,SAAS,CAAC,QAAQ;AACrB,gBAAA,IAAI,MAAM,KAAK,CAAC,EAAE;AAChB,oBAAA,OAAO,GAAG,CAAC;iBACZ;qBAAM;AACL,oBAAA,QACE,CAAC,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE;AACrB,wBAAA,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,SAAS,CAAC;wBAC9B,GAAG;AACH,wBAAA,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,EAC5C;iBACH;AACH,YAAA;AACE,gBAAA,MAAM,IAAI,KAAK,CAAC,uBAAuB,KAAK,CAAA,CAAA,CAAG,CAAC,CAAC;SACpD;AACH,KAAC,CAAC;AACJ,CAAC;AAED,MAAM,OAAO,GAAG,CAAC,CAAC;AAClB,MAAM,QAAQ,GAAG,CAAC,CAAC;AACnB,SAAS,sBAAsB,CAAC,IAAY,EAAA;AAC1C,IAAA,MAAM,cAAc,GAAG,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;IAC7D,OAAO,UAAU,CACf,IAAI,EACJ,CAAC,EACD,CAAC,IAAI,cAAc,IAAI,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,CAAC,CAAC,GAAG,cAAc,CAC5E,CAAC;AACJ,CAAC;AAED;;AAEG;AACG,SAAU,sBAAsB,CAAC,QAAc,EAAA;;AAEnD,IAAA,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;;AAGrC,IAAA,MAAM,eAAe,GAAG,UAAU,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,GAAG,UAAU,CAAC;AAEtE,IAAA,OAAO,UAAU,CACf,QAAQ,CAAC,WAAW,EAAE,EACtB,QAAQ,CAAC,QAAQ,EAAE,EACnB,QAAQ,CAAC,OAAO,EAAE,GAAG,eAAe,CACrC,CAAC;AACJ,CAAC;AAED,SAAS,UAAU,CAAC,IAAY,EAAE,UAAU,GAAG,KAAK,EAAA;IAClD,OAAO,UAAU,IAAU,EAAE,MAAc,EAAA;AACzC,QAAA,IAAI,MAAM,CAAC;QACX,IAAI,UAAU,EAAE;YACd,MAAM,yBAAyB,GAC7B,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AAChE,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;AAC7B,YAAA,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,yBAAyB,IAAI,CAAC,CAAC,CAAC;SAClE;aAAM;AACL,YAAA,MAAM,SAAS,GAAG,sBAAsB,CAAC,IAAI,CAAC,CAAC;;;YAG/C,MAAM,UAAU,GAAG,sBAAsB,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC;YACnE,MAAM,IAAI,GAAG,SAAS,CAAC,OAAO,EAAE,GAAG,UAAU,CAAC,OAAO,EAAE,CAAC;AACxD,YAAA,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC;SACzC;AAED,QAAA,OAAO,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC;AACxF,KAAC,CAAC;AACJ,CAAC;AAED;;AAEG;AACH,SAAS,uBAAuB,CAAC,IAAY,EAAE,IAAI,GAAG,KAAK,EAAA;IACzD,OAAO,UAAU,IAAU,EAAE,MAAc,EAAA;AACzC,QAAA,MAAM,SAAS,GAAG,sBAAsB,CAAC,IAAI,CAAC,CAAC;AAC/C,QAAA,MAAM,iBAAiB,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;AAClD,QAAA,OAAO,SAAS,CACd,iBAAiB,EACjB,IAAI,EACJ,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,SAAS,CAAC,EACrD,IAAI,CACL,CAAC;AACJ,KAAC,CAAC;AACJ,CAAC;AAID,MAAM,YAAY,GAAsC,EAAE,CAAC;AAE3D;AACA;AACA;AACA;AACA,SAAS,gBAAgB,CAAC,MAAc,EAAA;AACtC,IAAA,IAAI,YAAY,CAAC,MAAM,CAAC,EAAE;AACxB,QAAA,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC;KAC7B;AACD,IAAA,IAAI,SAAS,CAAC;IACd,QAAQ,MAAM;;AAEZ,QAAA,KAAK,GAAG,CAAC;AACT,QAAA,KAAK,IAAI,CAAC;AACV,QAAA,KAAK,KAAK;YACR,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,IAAI,EAAE,gBAAgB,CAAC,WAAW,CAAC,CAAC;YAC9E,MAAM;AACR,QAAA,KAAK,MAAM;YACT,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,IAAI,EAAE,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACvE,MAAM;AACR,QAAA,KAAK,OAAO;YACV,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,IAAI,EAAE,gBAAgB,CAAC,MAAM,CAAC,CAAC;YACzE,MAAM;;AAGR,QAAA,KAAK,GAAG;AACN,YAAA,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YAC7D,MAAM;;AAER,QAAA,KAAK,IAAI;AACP,YAAA,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAC5D,MAAM;;AAER,QAAA,KAAK,KAAK;AACR,YAAA,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YAC7D,MAAM;;AAER,QAAA,KAAK,MAAM;AACT,YAAA,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YAC7D,MAAM;;AAGR,QAAA,KAAK,GAAG;AACN,YAAA,SAAS,GAAG,uBAAuB,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM;;;AAGR,QAAA,KAAK,IAAI;AACP,YAAA,SAAS,GAAG,uBAAuB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;YAC7C,MAAM;;;AAGR,QAAA,KAAK,KAAK;AACR,YAAA,SAAS,GAAG,uBAAuB,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM;;AAER,QAAA,KAAK,MAAM;AACT,YAAA,SAAS,GAAG,uBAAuB,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM;;AAGR,QAAA,KAAK,GAAG,CAAC;AACT,QAAA,KAAK,GAAG;YACN,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC7C,MAAM;AACR,QAAA,KAAK,IAAI,CAAC;AACV,QAAA,KAAK,IAAI;YACP,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC7C,MAAM;;AAGR,QAAA,KAAK,KAAK;YACR,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,MAAM,EAAE,gBAAgB,CAAC,WAAW,CAAC,CAAC;YAChF,MAAM;AACR,QAAA,KAAK,MAAM;YACT,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,MAAM,EAAE,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACzE,MAAM;AACR,QAAA,KAAK,OAAO;YACV,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,MAAM,EAAE,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAC3E,MAAM;;AAGR,QAAA,KAAK,KAAK;AACR,YAAA,SAAS,GAAG,aAAa,CACvB,eAAe,CAAC,MAAM,EACtB,gBAAgB,CAAC,WAAW,EAC5B,SAAS,CAAC,UAAU,CACrB,CAAC;YACF,MAAM;AACR,QAAA,KAAK,MAAM;AACT,YAAA,SAAS,GAAG,aAAa,CACvB,eAAe,CAAC,MAAM,EACtB,gBAAgB,CAAC,IAAI,EACrB,SAAS,CAAC,UAAU,CACrB,CAAC;YACF,MAAM;AACR,QAAA,KAAK,OAAO;AACV,YAAA,SAAS,GAAG,aAAa,CACvB,eAAe,CAAC,MAAM,EACtB,gBAAgB,CAAC,MAAM,EACvB,SAAS,CAAC,UAAU,CACrB,CAAC;YACF,MAAM;;AAGR,QAAA,KAAK,GAAG;AACN,YAAA,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM;AACR,QAAA,KAAK,IAAI;AACP,YAAA,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM;;AAGR,QAAA,KAAK,GAAG;AACN,YAAA,SAAS,GAAG,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;YAChC,MAAM;;AAGR,QAAA,KAAK,GAAG;YACN,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YACzC,MAAM;AACR,QAAA,KAAK,IAAI;YACP,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YACzC,MAAM;;AAGR,QAAA,KAAK,GAAG,CAAC;AACT,QAAA,KAAK,IAAI;YACP,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACxC,MAAM;AACR,QAAA,KAAK,KAAK;AACR,YAAA,SAAS,GAAG,aAAa,CACvB,eAAe,CAAC,IAAI,EACpB,gBAAgB,CAAC,WAAW,EAC5B,SAAS,CAAC,UAAU,CACrB,CAAC;YACF,MAAM;AACR,QAAA,KAAK,MAAM;AACT,YAAA,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,IAAI,EAAE,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC;YAC7F,MAAM;AACR,QAAA,KAAK,OAAO;AACV,YAAA,SAAS,GAAG,aAAa,CACvB,eAAe,CAAC,IAAI,EACpB,gBAAgB,CAAC,MAAM,EACvB,SAAS,CAAC,UAAU,CACrB,CAAC;YACF,MAAM;AACR,QAAA,KAAK,QAAQ;AACX,YAAA,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,IAAI,EAAE,gBAAgB,CAAC,KAAK,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC;YAC9F,MAAM;;AAGR,QAAA,KAAK,GAAG,CAAC;AACT,QAAA,KAAK,IAAI,CAAC;AACV,QAAA,KAAK,KAAK;YACR,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,IAAI,EAAE,gBAAgB,CAAC,WAAW,CAAC,CAAC;YAC9E,MAAM;AACR,QAAA,KAAK,MAAM;YACT,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,IAAI,EAAE,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACvE,MAAM;AACR,QAAA,KAAK,OAAO;YACV,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,IAAI,EAAE,gBAAgB,CAAC,MAAM,CAAC,CAAC;YACzE,MAAM;AACR,QAAA,KAAK,QAAQ;YACX,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,IAAI,EAAE,gBAAgB,CAAC,KAAK,CAAC,CAAC;YACxE,MAAM;;AAGR,QAAA,KAAK,GAAG,CAAC;AACT,QAAA,KAAK,IAAI,CAAC;AACV,QAAA,KAAK,KAAK;YACR,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,UAAU,EAAE,gBAAgB,CAAC,WAAW,CAAC,CAAC;YACpF,MAAM;AACR,QAAA,KAAK,MAAM;YACT,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,UAAU,EAAE,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAC7E,MAAM;AACR,QAAA,KAAK,OAAO;YACV,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,UAAU,EAAE,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAC/E,MAAM;;AAGR,QAAA,KAAK,GAAG,CAAC;AACT,QAAA,KAAK,IAAI,CAAC;AACV,QAAA,KAAK,KAAK;AACR,YAAA,SAAS,GAAG,aAAa,CACvB,eAAe,CAAC,UAAU,EAC1B,gBAAgB,CAAC,WAAW,EAC5B,SAAS,CAAC,UAAU,EACpB,IAAI,CACL,CAAC;YACF,MAAM;AACR,QAAA,KAAK,MAAM;AACT,YAAA,SAAS,GAAG,aAAa,CACvB,eAAe,CAAC,UAAU,EAC1B,gBAAgB,CAAC,IAAI,EACrB,SAAS,CAAC,UAAU,EACpB,IAAI,CACL,CAAC;YACF,MAAM;AACR,QAAA,KAAK,OAAO;AACV,YAAA,SAAS,GAAG,aAAa,CACvB,eAAe,CAAC,UAAU,EAC1B,gBAAgB,CAAC,MAAM,EACvB,SAAS,CAAC,UAAU,EACpB,IAAI,CACL,CAAC;YACF,MAAM;;AAGR,QAAA,KAAK,GAAG,CAAC;AACT,QAAA,KAAK,IAAI,CAAC;AACV,QAAA,KAAK,KAAK;AACR,YAAA,SAAS,GAAG,aAAa,CACvB,eAAe,CAAC,UAAU,EAC1B,gBAAgB,CAAC,WAAW,EAC5B,SAAS,CAAC,MAAM,EAChB,IAAI,CACL,CAAC;YACF,MAAM;AACR,QAAA,KAAK,MAAM;AACT,YAAA,SAAS,GAAG,aAAa,CACvB,eAAe,CAAC,UAAU,EAC1B,gBAAgB,CAAC,IAAI,EACrB,SAAS,CAAC,MAAM,EAChB,IAAI,CACL,CAAC;YACF,MAAM;AACR,QAAA,KAAK,OAAO;AACV,YAAA,SAAS,GAAG,aAAa,CACvB,eAAe,CAAC,UAAU,EAC1B,gBAAgB,CAAC,MAAM,EACvB,SAAS,CAAC,MAAM,EAChB,IAAI,CACL,CAAC;YACF,MAAM;;AAGR,QAAA,KAAK,GAAG;AACN,YAAA,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAC/C,MAAM;AACR,QAAA,KAAK,IAAI;AACP,YAAA,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAC/C,MAAM;;AAGR,QAAA,KAAK,GAAG;YACN,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC1C,MAAM;;AAER,QAAA,KAAK,IAAI;YACP,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC1C,MAAM;;AAGR,QAAA,KAAK,GAAG;YACN,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAC5C,MAAM;AACR,QAAA,KAAK,IAAI;YACP,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAC5C,MAAM;;AAGR,QAAA,KAAK,GAAG;YACN,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAC5C,MAAM;AACR,QAAA,KAAK,IAAI;YACP,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAC5C,MAAM;;AAGR,QAAA,KAAK,GAAG;YACN,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;YACtD,MAAM;AACR,QAAA,KAAK,IAAI;YACP,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;YACtD,MAAM;AACR,QAAA,KAAK,KAAK;YACR,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;YACtD,MAAM;;AAGR,QAAA,KAAK,GAAG,CAAC;AACT,QAAA,KAAK,IAAI,CAAC;AACV,QAAA,KAAK,KAAK;AACR,YAAA,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM;;AAER,QAAA,KAAK,OAAO;AACV,YAAA,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAC/C,MAAM;;AAGR,QAAA,KAAK,GAAG,CAAC;AACT,QAAA,KAAK,IAAI,CAAC;AACV,QAAA,KAAK,KAAK,CAAC;;AAEX,QAAA,KAAK,GAAG,CAAC;AACT,QAAA,KAAK,IAAI,CAAC;AACV,QAAA,KAAK,KAAK;AACR,YAAA,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAC/C,MAAM;;AAER,QAAA,KAAK,MAAM,CAAC;AACZ,QAAA,KAAK,MAAM,CAAC;;AAEZ,QAAA,KAAK,MAAM;AACT,YAAA,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM;AACR,QAAA;AACE,YAAA,OAAO,IAAI,CAAC;KACf;AACD,IAAA,YAAY,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC;AACjC,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,gBAAgB,CAAC,QAAgB,EAAE,QAAgB,EAAA;;;IAG1D,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AACtC,IAAA,MAAM,uBAAuB,GAAG,IAAI,CAAC,KAAK,CAAC,wBAAwB,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC;AACxF,IAAA,OAAO,KAAK,CAAC,uBAAuB,CAAC,GAAG,QAAQ,GAAG,uBAAuB,CAAC;AAC7E,CAAC;AAED,SAAS,cAAc,CAAC,IAAU,EAAE,OAAe,EAAA;IACjD,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;IAChC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,OAAO,CAAC,CAAC;AAC7C,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,sBAAsB,CAAC,IAAU,EAAE,QAAgB,EAAE,OAAgB,EAAA;AAC5E,IAAA,MAAM,YAAY,GAAG,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACtC,IAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;IACpD,MAAM,cAAc,GAAG,gBAAgB,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC;AACtE,IAAA,OAAO,cAAc,CAAC,IAAI,EAAE,YAAY,IAAI,cAAc,GAAG,kBAAkB,CAAC,CAAC,CAAC;AACpF,CAAC;AAED;;;;;;;;;;;AAWG;AACG,SAAU,MAAM,CAAC,KAA6B,EAAA;AAClD,IAAA,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE;AACjB,QAAA,OAAO,KAAK,CAAC;KACd;IAED,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;AAC9C,QAAA,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;KACxB;AAED,IAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,QAAA,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;AAErB,QAAA,IAAI,iCAAiC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AACjD;;;;;;AAM0D;AAC1D,YAAA,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAW,KAAK,CAAC,GAAG,CAAC,CAAC;YACtE,OAAO,UAAU,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;SAChC;AAED,QAAA,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;;QAGnC,IAAI,CAAC,KAAK,CAAE,KAAa,GAAG,QAAQ,CAAC,EAAE;AACrC,YAAA,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC3B;AAED,QAAA,IAAI,KAA8B,CAAC;QACnC,KAAK,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,kBAAkB,CAAC,GAAG;AAC7C,YAAA,OAAO,eAAe,CAAC,KAAK,CAAC,CAAC;SAC/B;KACF;AAED,IAAA,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,KAAY,CAAC,CAAC;AACpC,IAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;AACjB,QAAA,MAAM,IAAI,KAAK,CAAC,sBAAsB,KAAK,CAAA,aAAA,CAAe,CAAC,CAAC;KAC7D;AACD,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;AAGG;AACG,SAAU,eAAe,CAAC,KAAuB,EAAA;AACrD,IAAA,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;IACzB,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,IAAI,KAAK,GAAG,CAAC,CAAC;;AAGd,IAAA,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC;AACrE,IAAA,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC;;AAG/D,IAAA,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;AACZ,QAAA,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;AACtC,QAAA,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;KACtC;AACD,IAAA,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChF,IAAA,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC;AACzC,IAAA,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC;IACxC,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;;;;IAIhC,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AACjE,IAAA,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;AACnC,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAEK,SAAU,MAAM,CAAC,KAAU,EAAA;AAC/B,IAAA,OAAO,KAAK,YAAY,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;AAC1D;;ACj6BO,MAAM,oBAAoB,GAAG,6BAA6B,CAAC;AAClE,MAAM,UAAU,GAAG,EAAE,CAAC;AACtB,MAAM,WAAW,GAAG,GAAG,CAAC;AACxB,MAAM,SAAS,GAAG,GAAG,CAAC;AACtB,MAAM,WAAW,GAAG,GAAG,CAAC;AACxB,MAAM,SAAS,GAAG,GAAG,CAAC;AACtB,MAAM,UAAU,GAAG,GAAG,CAAC;AACvB,MAAM,aAAa,GAAG,GAAG,CAAC;AAC1B,MAAM,YAAY,GAAG,GAAG,CAAC;AAEzB;;AAEG;AACH,SAAS,0BAA0B,CACjC,KAAa,EACb,OAA2B,EAC3B,MAAc,EACd,WAAyB,EACzB,aAA2B,EAC3B,UAAmB,EACnB,SAAS,GAAG,KAAK,EAAA;IAEjB,IAAI,aAAa,GAAG,EAAE,CAAC;IACvB,IAAI,MAAM,GAAG,KAAK,CAAC;AAEnB,IAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;QACpB,aAAa,GAAG,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC;KACtE;SAAM;AACL,QAAA,IAAI,YAAY,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;QAEtC,IAAI,SAAS,EAAE;AACb,YAAA,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC,CAAC;SACxC;AAED,QAAA,IAAI,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;AAC5B,QAAA,IAAI,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC;AAClC,QAAA,IAAI,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC;QAElC,IAAI,UAAU,EAAE;YACd,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;AACrD,YAAA,IAAI,KAAK,KAAK,IAAI,EAAE;AAClB,gBAAA,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,CAAA,0BAAA,CAA4B,CAAC,CAAC;aAC5D;AACD,YAAA,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAC5B,YAAA,MAAM,eAAe,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACjC,YAAA,MAAM,eAAe,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACjC,YAAA,IAAI,UAAU,IAAI,IAAI,EAAE;AACtB,gBAAA,MAAM,GAAG,iBAAiB,CAAC,UAAU,CAAC,CAAC;aACxC;AACD,YAAA,IAAI,eAAe,IAAI,IAAI,EAAE;AAC3B,gBAAA,WAAW,GAAG,iBAAiB,CAAC,eAAe,CAAC,CAAC;aAClD;AACD,YAAA,IAAI,eAAe,IAAI,IAAI,EAAE;AAC3B,gBAAA,WAAW,GAAG,iBAAiB,CAAC,eAAe,CAAC,CAAC;aAClD;iBAAM,IAAI,eAAe,IAAI,IAAI,IAAI,WAAW,GAAG,WAAW,EAAE;gBAC/D,WAAW,GAAG,WAAW,CAAC;aAC3B;SACF;AAED,QAAA,WAAW,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;AAEpD,QAAA,IAAI,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;AACjC,QAAA,IAAI,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC;AACzC,QAAA,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC;QACvC,IAAI,QAAQ,GAAG,EAAE,CAAC;AAClB,QAAA,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;;AAGjC,QAAA,OAAO,UAAU,GAAG,MAAM,EAAE,UAAU,EAAE,EAAE;AACxC,YAAA,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;SACnB;;AAGD,QAAA,OAAO,UAAU,GAAG,CAAC,EAAE,UAAU,EAAE,EAAE;AACnC,YAAA,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;SACnB;;AAGD,QAAA,IAAI,UAAU,GAAG,CAAC,EAAE;YAClB,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;SACrD;aAAM;YACL,QAAQ,GAAG,MAAM,CAAC;AAClB,YAAA,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;SACd;;QAGD,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,IAAI,MAAM,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,EAAE;YACnC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;SACxE;QAED,OAAO,MAAM,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,EAAE;YACpC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;SACvE;AAED,QAAA,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;SACjC;AAED,QAAA,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC;;AAGxE,QAAA,IAAI,QAAQ,CAAC,MAAM,EAAE;AACnB,YAAA,aAAa,IAAI,qBAAqB,CAAC,MAAM,EAAE,aAAa,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SACnF;QAED,IAAI,QAAQ,EAAE;AACZ,YAAA,aAAa,IAAI,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,WAAW,CAAC,GAAG,GAAG,GAAG,QAAQ,CAAC;SAC3F;KACF;AAED,IAAA,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE;QACxB,aAAa,GAAG,OAAO,CAAC,MAAM,GAAG,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC;KACjE;SAAM;QACL,aAAa,GAAG,OAAO,CAAC,MAAM,GAAG,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC;KACjE;AAED,IAAA,OAAO,aAAa,CAAC;AACvB,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;AAwBG;AACG,SAAU,cAAc,CAC5B,KAAa,EACb,MAAc,EACd,QAAgB,EAChB,YAAqB,EACrB,UAAmB,EAAA;IAEnB,MAAM,MAAM,GAAG,qBAAqB,CAAC,MAAM,EAAE,iBAAiB,CAAC,QAAQ,CAAC,CAAC;AACzE,IAAA,MAAM,OAAO,GAAG,iBAAiB,CAAC,MAAM,EAAE,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC;AAEjG,IAAA,OAAO,CAAC,OAAO,GAAG,yBAAyB,CAAC,YAAa,CAAC,CAAC;AAC3D,IAAA,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;IAElC,MAAM,GAAG,GAAG,0BAA0B,CACpC,KAAK,EACL,OAAO,EACP,MAAM,EACN,YAAY,CAAC,aAAa,EAC1B,YAAY,CAAC,eAAe,EAC5B,UAAU,CACX,CAAC;AACF,IAAA,QACE,GAAG;AACA,SAAA,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC;;AAEhC,SAAA,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;;;;;SAK1B,IAAI,EAAE,EACT;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;;;AAkBG;SACa,aAAa,CAAC,KAAa,EAAE,MAAc,EAAE,UAAmB,EAAA;IAC9E,MAAM,MAAM,GAAG,qBAAqB,CAAC,MAAM,EAAE,iBAAiB,CAAC,OAAO,CAAC,CAAC;AACxE,IAAA,MAAM,OAAO,GAAG,iBAAiB,CAAC,MAAM,EAAE,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC;IACjG,MAAM,GAAG,GAAG,0BAA0B,CACpC,KAAK,EACL,OAAO,EACP,MAAM,EACN,YAAY,CAAC,KAAK,EAClB,YAAY,CAAC,OAAO,EACpB,UAAU,EACV,IAAI,CACL,CAAC;IACF,OAAO,GAAG,CAAC,OAAO,CAChB,IAAI,MAAM,CAAC,YAAY,EAAE,GAAG,CAAC,EAC7B,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,WAAW,CAAC,CACxD,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;AAgBG;SACa,YAAY,CAAC,KAAa,EAAE,MAAc,EAAE,UAAmB,EAAA;IAC7E,MAAM,MAAM,GAAG,qBAAqB,CAAC,MAAM,EAAE,iBAAiB,CAAC,OAAO,CAAC,CAAC;AACxE,IAAA,MAAM,OAAO,GAAG,iBAAiB,CAAC,MAAM,EAAE,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC;AACjG,IAAA,OAAO,0BAA0B,CAC/B,KAAK,EACL,OAAO,EACP,MAAM,EACN,YAAY,CAAC,KAAK,EAClB,YAAY,CAAC,OAAO,EACpB,UAAU,CACX,CAAC;AACJ,CAAC;AAsBD,SAAS,iBAAiB,CAAC,MAAc,EAAE,SAAS,GAAG,GAAG,EAAA;AACxD,IAAA,MAAM,CAAC,GAAG;AACR,QAAA,MAAM,EAAE,CAAC;AACT,QAAA,OAAO,EAAE,CAAC;AACV,QAAA,OAAO,EAAE,CAAC;AACV,QAAA,MAAM,EAAE,EAAE;AACV,QAAA,MAAM,EAAE,EAAE;AACV,QAAA,MAAM,EAAE,EAAE;AACV,QAAA,MAAM,EAAE,EAAE;AACV,QAAA,KAAK,EAAE,CAAC;AACR,QAAA,MAAM,EAAE,CAAC;KACV,CAAC;IAEF,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;AAC/C,IAAA,MAAM,QAAQ,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;AACjC,IAAA,MAAM,QAAQ,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;IAEjC,MAAM,aAAa,GACf,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAClC,UAAE,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC;AAC7B,UAAE;AACE,YAAA,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YAC1D,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AACxD,SAAA,EACP,OAAO,GAAG,aAAa,CAAC,CAAC,CAAC,EAC1B,QAAQ,GAAG,aAAa,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;AAEpC,IAAA,CAAC,CAAC,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;AAE7D,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACxC,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC9B,QAAA,IAAI,EAAE,KAAK,SAAS,EAAE;YACpB,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;SAC/B;AAAM,aAAA,IAAI,EAAE,KAAK,UAAU,EAAE;AAC5B,YAAA,CAAC,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;SACnB;aAAM;AACL,YAAA,CAAC,CAAC,MAAM,IAAI,EAAE,CAAC;SAChB;KACF;IAED,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;IACxC,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;AAC3C,IAAA,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC;IAExE,IAAI,QAAQ,EAAE;QACZ,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,EAClE,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAErC,QAAA,CAAC,CAAC,MAAM,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AACxD,QAAA,CAAC,CAAC,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,QAAQ,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;KAC7D;SAAM;QACL,CAAC,CAAC,MAAM,GAAG,SAAS,GAAG,CAAC,CAAC,MAAM,CAAC;AAChC,QAAA,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;KACrB;AAED,IAAA,OAAO,CAAC,CAAC;AACX,CAAC;AAWD;AACA,SAAS,SAAS,CAAC,YAA0B,EAAA;;IAE3C,IAAI,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;AAChC,QAAA,OAAO,YAAY,CAAC;KACrB;;IAGD,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,MAAM,GAAG,YAAY,CAAC,UAAU,CAAC;AACzE,IAAA,IAAI,YAAY,CAAC,QAAQ,EAAE;AACzB,QAAA,YAAY,CAAC,QAAQ,IAAI,CAAC,CAAC;KAC5B;SAAM;AACL,QAAA,IAAI,WAAW,KAAK,CAAC,EAAE;YACrB,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SAChC;AAAM,aAAA,IAAI,WAAW,KAAK,CAAC,EAAE;AAC5B,YAAA,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SAC7B;AACD,QAAA,YAAY,CAAC,UAAU,IAAI,CAAC,CAAC;KAC9B;AAED,IAAA,OAAO,YAAY,CAAC;AACtB,CAAC;AAED;;;AAGG;AACH,SAAS,WAAW,CAAC,GAAW,EAAA;IAC9B,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;AAChC,IAAA,IAAI,QAAQ,GAAG,CAAC,EACd,MAAM,EACN,UAAU,CAAC;AACb,IAAA,IAAI,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC;;AAGhB,IAAA,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE;QACnD,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;KAC1C;;AAGD,IAAA,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;;QAEjC,IAAI,UAAU,GAAG,CAAC;YAAE,UAAU,GAAG,CAAC,CAAC;QACnC,UAAU,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACnC,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KACjC;AAAM,SAAA,IAAI,UAAU,GAAG,CAAC,EAAE;;AAEzB,QAAA,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC;KAC5B;;AAGD,IAAA,KAAK,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC,EAAE,EAAE;;KAEhD;IAED,IAAI,CAAC,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE;;AAEjC,QAAA,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QACb,UAAU,GAAG,CAAC,CAAC;KAChB;SAAM;;AAEL,QAAA,KAAK,EAAE,CAAC;AACR,QAAA,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,SAAS;AAAE,YAAA,KAAK,EAAE,CAAC;;QAGnD,UAAU,IAAI,CAAC,CAAC;QAChB,MAAM,GAAG,EAAE,CAAC;;AAEZ,QAAA,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;AAChC,YAAA,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SACtC;KACF;;AAGD,IAAA,IAAI,UAAU,GAAG,UAAU,EAAE;QAC3B,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC;AAC1C,QAAA,QAAQ,GAAG,UAAU,GAAG,CAAC,CAAC;QAC1B,UAAU,GAAG,CAAC,CAAC;KAChB;AAED,IAAA,OAAO,EAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAC,CAAC;AACxC,CAAC;AAED;;;AAGG;AACH,SAAS,WAAW,CAAC,YAA0B,EAAE,OAAe,EAAE,OAAe,EAAA;AAC/E,IAAA,IAAI,OAAO,GAAG,OAAO,EAAE;QACrB,MAAM,IAAI,KAAK,CACb,CAAA,6CAAA,EAAgD,OAAO,CAAiC,8BAAA,EAAA,OAAO,CAAI,EAAA,CAAA,CACpG,CAAC;KACH;AAED,IAAA,IAAI,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;IACjC,IAAI,WAAW,GAAG,MAAM,CAAC,MAAM,GAAG,YAAY,CAAC,UAAU,CAAC;AAC1D,IAAA,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,EAAE,OAAO,CAAC,CAAC;;AAGvE,IAAA,IAAI,OAAO,GAAG,YAAY,GAAG,YAAY,CAAC,UAAU,CAAC;AACrD,IAAA,IAAI,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;AAE5B,IAAA,IAAI,OAAO,GAAG,CAAC,EAAE;;AAEf,QAAA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC;;AAG1D,QAAA,KAAK,IAAI,CAAC,GAAG,OAAO,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC5C,YAAA,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;SACf;KACF;SAAM;;QAEL,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;AACvC,QAAA,YAAY,CAAC,UAAU,GAAG,CAAC,CAAC;AAC5B,QAAA,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,GAAG,YAAY,GAAG,CAAC,EAAE,CAAC;AAC1D,QAAA,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE;AAAE,YAAA,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;KACjD;AAED,IAAA,IAAI,KAAK,IAAI,CAAC,EAAE;AACd,QAAA,IAAI,OAAO,GAAG,CAAC,GAAG,CAAC,EAAE;AACnB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE;AAChC,gBAAA,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAClB,YAAY,CAAC,UAAU,EAAE,CAAC;aAC3B;AACD,YAAA,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAClB,YAAY,CAAC,UAAU,EAAE,CAAC;SAC3B;aAAM;AACL,YAAA,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC;SACvB;KACF;;AAGD,IAAA,OAAO,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,YAAY,CAAC,EAAE,WAAW,EAAE;AAAE,QAAA,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAE9E,IAAA,IAAI,iBAAiB,GAAG,YAAY,KAAK,CAAC,CAAC;;;AAG3C,IAAA,MAAM,MAAM,GAAG,OAAO,GAAG,YAAY,CAAC,UAAU,CAAC;;AAEjD,IAAA,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAA;AAC5D,QAAA,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AACd,QAAA,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;QAChC,IAAI,iBAAiB,EAAE;;YAErB,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,MAAM,EAAE;gBAClC,MAAM,CAAC,GAAG,EAAE,CAAC;aACd;iBAAM;gBACL,iBAAiB,GAAG,KAAK,CAAC;aAC3B;SACF;AACD,QAAA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;KACxB,EAAE,CAAC,CAAC,CAAC;IACN,IAAI,KAAK,EAAE;AACT,QAAA,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACtB,YAAY,CAAC,UAAU,EAAE,CAAC;KAC3B;AACH,CAAC;AAEK,SAAU,iBAAiB,CAAC,IAAY,EAAA;AAC5C,IAAA,MAAM,MAAM,GAAW,QAAQ,CAAC,IAAI,CAAC,CAAC;AACtC,IAAA,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE;AACjB,QAAA,MAAM,IAAI,KAAK,CAAC,uCAAuC,GAAG,IAAI,CAAC,CAAC;KACjE;AACD,IAAA,OAAO,MAAM,CAAC;AAChB;;ACtfA;;AAEG;MAMmB,cAAc,CAAA;yHAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAAd,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,EAJtB,UAAA,EAAA,MAAM,EACN,UAAA,EAAA,CAAC,MAAc,KAAK,IAAI,oBAAoB,CAAC,MAAM,CAAC,kBACzD,SAAS,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA;;sGAEI,cAAc,EAAA,UAAA,EAAA,CAAA;kBALnC,UAAU;AAAC,YAAA,IAAA,EAAA,CAAA;AACV,oBAAA,UAAU,EAAE,MAAM;oBAClB,UAAU,EAAE,CAAC,MAAc,KAAK,IAAI,oBAAoB,CAAC,MAAM,CAAC;oBAChE,IAAI,EAAE,CAAC,SAAS,CAAC;AAClB,iBAAA,CAAA;;AAKD;;;;AAIG;AACG,SAAU,iBAAiB,CAC/B,KAAa,EACb,KAAe,EACf,cAA8B,EAC9B,MAAe,EAAA;AAEf,IAAA,IAAI,GAAG,GAAG,CAAI,CAAA,EAAA,KAAK,EAAE,CAAC;IAEtB,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;AAC3B,QAAA,OAAO,GAAG,CAAC;KACZ;IAED,GAAG,GAAG,cAAc,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IAEtD,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;AAC3B,QAAA,OAAO,GAAG,CAAC;KACZ;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;AAC/B,QAAA,OAAO,OAAO,CAAC;KAChB;AAED,IAAA,MAAM,IAAI,KAAK,CAAC,sCAAsC,KAAK,CAAA,CAAA,CAAG,CAAC,CAAC;AAClE,CAAC;AAED;;;;AAIG;AAEG,MAAO,oBAAqB,SAAQ,cAAc,CAAA;AACtD,IAAA,WAAA,CAAyC,MAAc,EAAA;AACrD,QAAA,KAAK,EAAE,CAAC;QAD+B,IAAM,CAAA,MAAA,GAAN,MAAM,CAAQ;KAEtD;IAEQ,iBAAiB,CAAC,KAAU,EAAE,MAAe,EAAA;AACpD,QAAA,MAAM,MAAM,GAAG,mBAAmB,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC;QAEjE,QAAQ,MAAM;YACZ,KAAK,MAAM,CAAC,IAAI;AACd,gBAAA,OAAO,MAAM,CAAC;YAChB,KAAK,MAAM,CAAC,GAAG;AACb,gBAAA,OAAO,KAAK,CAAC;YACf,KAAK,MAAM,CAAC,GAAG;AACb,gBAAA,OAAO,KAAK,CAAC;YACf,KAAK,MAAM,CAAC,GAAG;AACb,gBAAA,OAAO,KAAK,CAAC;YACf,KAAK,MAAM,CAAC,IAAI;AACd,gBAAA,OAAO,MAAM,CAAC;AAChB,YAAA;AACE,gBAAA,OAAO,OAAO,CAAC;SAClB;KACF;AAtBU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,kBACX,SAAS,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HADlB,oBAAoB,EAAA,CAAA,CAAA,EAAA;;sGAApB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBADhC,UAAU;;0BAEI,MAAM;2BAAC,SAAS,CAAA;;;ACnD/B;;;;;;;;AAQG;SACa,kBAAkB,CAAC,IAAS,EAAE,QAAuB,EAAE,SAAe,EAAA;IACpF,OAAO,mBAAmB,CAAC,IAAI,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;AACxD;;ACbgB,SAAA,gBAAgB,CAAC,SAAiB,EAAE,IAAY,EAAA;AAC9D,IAAA,IAAI,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAChC,KAAK,MAAM,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;QACzC,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACpC,QAAA,MAAM,CAAC,UAAU,EAAE,WAAW,CAAC,GAC7B,OAAO,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;AACvF,QAAA,IAAI,UAAU,CAAC,IAAI,EAAE,KAAK,IAAI,EAAE;AAC9B,YAAA,OAAO,kBAAkB,CAAC,WAAW,CAAC,CAAC;SACxC;KACF;AACD,IAAA,OAAO,IAAI,CAAC;AACd;;ACCA,MAAM,SAAS,GAAG,KAAK,CAAC;AAExB,MAAM,WAAW,GAAa,EAAE,CAAC;AAkBjC;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BG;MAKU,OAAO,CAAA;IAMlB,WACU,CAAA,KAAiB,EACjB,SAAoB,EAAA;QADpB,IAAK,CAAA,KAAA,GAAL,KAAK,CAAY;QACjB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAW;QAPtB,IAAc,CAAA,cAAA,GAAG,WAAW,CAAC;AAG7B,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,GAAG,EAAyB,CAAC;KAKhD;IAEJ,IACI,KAAK,CAAC,KAAa,EAAA;QACrB,IAAI,CAAC,cAAc,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,WAAW,CAAC;KACnF;IAED,IACI,OAAO,CAAC,KAAkF,EAAA;QAC5F,IAAI,CAAC,QAAQ,GAAG,OAAO,KAAK,KAAK,QAAQ,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;KACnF;AAED;;;;;;;;;;;;;;;;;;;;;;;;AAwBG;IACH,SAAS,GAAA;;AAEP,QAAA,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,EAAE;AACvC,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;SAChC;;AAGD,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,QAAQ,YAAY,GAAG,EAAE;AACtD,YAAA,KAAK,MAAM,KAAK,IAAI,QAAQ,EAAE;AAC5B,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;aAChC;SACF;AAAM,aAAA,IAAI,QAAQ,IAAI,IAAI,EAAE;YAC3B,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;AACzC,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;aACpD;SACF;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;KACxB;IAEO,YAAY,CAAC,KAAa,EAAE,WAAoB,EAAA;QACtD,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACvC,QAAA,IAAI,KAAK,KAAK,SAAS,EAAE;AACvB,YAAA,IAAI,KAAK,CAAC,OAAO,KAAK,WAAW,EAAE;AACjC,gBAAA,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC;AACrB,gBAAA,KAAK,CAAC,OAAO,GAAG,WAAW,CAAC;aAC7B;AACD,YAAA,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC;SACtB;aAAM;YACL,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,EAAC,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC;SAChF;KACF;IAEO,eAAe,GAAA;AACrB,QAAA,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,QAAQ,EAAE;AACtC,YAAA,MAAM,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AAC5B,YAAA,MAAM,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AAE5B,YAAA,IAAI,KAAK,CAAC,OAAO,EAAE;gBACjB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;AACxC,gBAAA,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC;aACvB;AAAM,iBAAA,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;;;AAGzB,gBAAA,IAAI,KAAK,CAAC,OAAO,EAAE;AACjB,oBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;iBACjC;AACD,gBAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAC7B;AAED,YAAA,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC;SACvB;KACF;IAEO,YAAY,CAAC,KAAa,EAAE,OAAgB,EAAA;QAClD,IAAI,SAAS,EAAE;AACb,YAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC7B,MAAM,IAAI,KAAK,CACb,CAAiE,8DAAA,EAAAC,UAAS,CAAC,KAAK,CAAC,CAAE,CAAA,CACpF,CAAC;aACH;SACF;AACD,QAAA,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;AACrB,QAAA,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YACpB,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;gBACvC,IAAI,OAAO,EAAE;AACX,oBAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;iBAC1D;qBAAM;AACL,oBAAA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;iBAC7D;AACH,aAAC,CAAC,CAAC;SACJ;KACF;yHAvHU,OAAO,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAAP,OAAO,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,CAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAAP,OAAO,EAAA,UAAA,EAAA,CAAA;kBAJnB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,WAAW;AACrB,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;uGAaK,KAAK,EAAA,CAAA;sBADR,KAAK;uBAAC,OAAO,CAAA;gBAMV,OAAO,EAAA,CAAA;sBADV,KAAK;uBAAC,SAAS,CAAA;;;AChElB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoEG;MAKU,iBAAiB,CAAA;AAuB5B,IAAA,WAAA,CAAoB,iBAAmC,EAAA;QAAnC,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAkB;QAtB9C,IAAiB,CAAA,iBAAA,GAAqB,IAAI,CAAC;AAepD;;;;AAIG;AACK,QAAA,IAAA,CAAA,WAAW,GAAG,IAAI,GAAG,EAAmB,CAAC;KAEU;AAEnD,IAAA,+BAA+B,CAAC,OAAsB,EAAA;;;;AAI5D,QAAA,QACE,OAAO,CAAC,2BAA2B,CAAC,KAAK,SAAS;AAClD,YAAA,OAAO,CAAC,kCAAkC,CAAC,KAAK,SAAS,EACzD;KACH;AAEO,IAAA,gCAAgC,CAAC,OAAsB,EAAA;;;;AAI7D,QAAA,QACE,OAAO,CAAC,mBAAmB,CAAC,KAAK,SAAS;AAC1C,YAAA,OAAO,CAAC,0BAA0B,CAAC,KAAK,SAAS;AACjD,YAAA,OAAO,CAAC,2BAA2B,CAAC,KAAK,SAAS;AAClD,YAAA,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,EAC7C;KACH;;AAGD,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,IAAI,IAAI,CAAC,gCAAgC,CAAC,OAAO,CAAC,EAAE;AAClD,YAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;AAC/B,YAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;AACzB,YAAA,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;AAE/B,YAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBAC1B,MAAM,QAAQ,GAAG,IAAI,CAAC,yBAAyB,IAAI,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC;AAEzF,gBAAA,IAAI,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,EAAE;AACjD,oBAAA,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,CAAC;AAE3B,oBAAA,IAAI,IAAI,CAAC,yBAAyB,EAAE;AAClC,wBAAA,IAAI,CAAC,UAAU,GAAG,cAAc,CAC9B,IAAI,CAAC,yBAAyB,EAC9B,iBAAiB,CAAC,QAAQ,CAAC,CAC5B,CAAC;qBACH;AAAM,yBAAA,IAAI,IAAI,CAAC,gCAAgC,EAAE;AAChD,wBAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,gCAAgC,CAAC,MAAM,CAC5D,iBAAiB,CAAC,QAAQ,CAAC,CAC5B,CAAC;qBACH;yBAAM;AACL,wBAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;qBAC7B;iBACF;AAED,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,iBAAiB,EAAE;oBAClF,QAAQ;oBACR,WAAW,EAAE,IAAI,CAAC,UAAU;oBAC5B,gBAAgB,EAAE,IAAI,CAAC,wBAAwB;AAChD,iBAAA,CAAC,CAAC;aACJ;SACF;KACF;;IAGD,SAAS,GAAA;AACP,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,YAAA,IAAI,IAAI,CAAC,uBAAuB,EAAE;AAChC,gBAAA,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,EAAE;oBACjE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;iBACvC;aACF;AAED,YAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SAC/C;KACF;;IAGD,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,CAAC;KAC5B;AAEO,IAAA,oBAAoB,CAAC,YAAmC,EAAA;QAC9D,KAAK,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE;YACnD,IAAI,CAAC,OAAO,EAAE;;AAEZ,gBAAA,YAAY,CAAC,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AAC5C,gBAAA,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;aACpC;iBAAM;;AAEL,gBAAA,YAAY,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,uBAAwB,CAAC,SAAS,CAAC,CAAC,CAAC;gBAC3E,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;aACxC;SACF;KACF;yHAjHU,iBAAiB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAAjB,iBAAiB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,uBAAA,EAAA,yBAAA,EAAA,yBAAA,EAAA,2BAAA,EAAA,wBAAA,EAAA,0BAAA,EAAA,yBAAA,EAAA,2BAAA,EAAA,gCAAA,EAAA,kCAAA,EAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAAjB,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAJ7B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,qBAAqB;AAC/B,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;qFAEU,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAEG,uBAAuB,EAAA,CAAA;sBAA/B,KAAK;gBACG,yBAAyB,EAAA,CAAA;sBAAjC,KAAK;gBACG,wBAAwB,EAAA,CAAA;sBAAhC,KAAK;gBAEG,yBAAyB,EAAA,CAAA;sBAAjC,KAAK;gBAIG,gCAAgC,EAAA,CAAA;sBAAxC,KAAK;;AAyGR;AACA,SAAS,iBAAiB,CAAC,QAAkB,EAAA;IAC3C,MAAM,cAAc,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IACjD,OAAO,cAAc,CAAC,QAAQ,CAAC;AACjC;;AC/LA;;AAEG;MACU,cAAc,CAAA;AACzB,IAAA,WAAA,CACS,SAAY,EACZ,OAAU,EACV,KAAa,EACb,KAAa,EAAA;QAHb,IAAS,CAAA,SAAA,GAAT,SAAS,CAAG;QACZ,IAAO,CAAA,OAAA,GAAP,OAAO,CAAG;QACV,IAAK,CAAA,KAAA,GAAL,KAAK,CAAQ;QACb,IAAK,CAAA,KAAA,GAAL,KAAK,CAAQ;KAClB;AAEJ,IAAA,IAAI,KAAK,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC;KACzB;AAED,IAAA,IAAI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;KACtC;AAED,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC;KAC7B;AAED,IAAA,IAAI,GAAG,GAAA;AACL,QAAA,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;KACnB;AACF,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiGG;MAKU,OAAO,CAAA;AAClB;;;AAGG;IACH,IACI,OAAO,CAAC,OAA+C,EAAA;AACzD,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;AACxB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;KAC3B;AACD;;;;;;;;;;;;;;;;;AAiBG;IACH,IACI,YAAY,CAAC,EAAsB,EAAA;AACrC,QAAA,IAAI,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,EAAE,IAAI,IAAI,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE;YAC7F,OAAO,CAAC,IAAI,CACV,CAA4C,yCAAA,EAAA,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAI,EAAA,CAAA;AAChE,gBAAA,CAAA,kFAAA,CAAoF,CACvF,CAAC;SACH;AACD,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;KACtB;AAED,IAAA,IAAI,YAAY,GAAA;QACd,OAAO,IAAI,CAAC,UAAU,CAAC;KACxB;AAUD,IAAA,WAAA,CACU,cAAgC,EAChC,SAA4C,EAC5C,QAAyB,EAAA;QAFzB,IAAc,CAAA,cAAA,GAAd,cAAc,CAAkB;QAChC,IAAS,CAAA,SAAA,GAAT,SAAS,CAAmC;QAC5C,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;QAX3B,IAAQ,CAAA,QAAA,GAAyB,IAAI,CAAC;QACtC,IAAa,CAAA,aAAA,GAAY,IAAI,CAAC;QAC9B,IAAO,CAAA,OAAA,GAA6B,IAAI,CAAC;KAU7C;AAEJ;;;AAGG;IACH,IACI,aAAa,CAAC,KAAwC,EAAA;;;;QAIxD,IAAI,KAAK,EAAE;AACT,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;SACxB;KACF;AAED;;;AAGG;IACH,SAAS,GAAA;AACP,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,YAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;;AAE3B,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC5B,YAAA,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,KAAK,EAAE;AAC1B,gBAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,oBAAA,IAAI;;;AAGF,wBAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;qBACpE;AAAC,oBAAA,MAAM;AACN,wBAAA,IAAI,YAAY,GACd,CAA2C,wCAAA,EAAA,KAAK,CAAa,WAAA,CAAA;AAC7D,4BAAA,CAAA,EAAG,WAAW,CAAC,KAAK,CAAC,8DAA8D,CAAC;AACtF,wBAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;4BAC7B,YAAY,IAAI,yCAAyC,CAAC;yBAC3D;AACD,wBAAA,MAAM,IAAIC,aAAY,CAAyC,CAAA,IAAA,+CAAA,YAAY,CAAC,CAAC;qBAC9E;iBACF;qBAAM;;;AAGL,oBAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;iBACpE;aACF;SACF;AACD,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACjD,YAAA,IAAI,OAAO;AAAE,gBAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;SAC1C;KACF;AAEO,IAAA,aAAa,CAAC,OAA2B,EAAA;AAC/C,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;QAC1C,OAAO,CAAC,gBAAgB,CACtB,CACE,IAA6B,EAC7B,qBAAoC,EACpC,YAA2B,KACzB;AACF,YAAA,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,EAAE;;;;AAI9B,gBAAA,aAAa,CAAC,kBAAkB,CAC9B,IAAI,CAAC,SAAS,EACd,IAAI,cAAc,CAAO,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAC3D,YAAY,KAAK,IAAI,GAAG,SAAS,GAAG,YAAY,CACjD,CAAC;aACH;AAAM,iBAAA,IAAI,YAAY,IAAI,IAAI,EAAE;AAC/B,gBAAA,aAAa,CAAC,MAAM,CAAC,qBAAqB,KAAK,IAAI,GAAG,SAAS,GAAG,qBAAqB,CAAC,CAAC;aAC1F;AAAM,iBAAA,IAAI,qBAAqB,KAAK,IAAI,EAAE;gBACzC,MAAM,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC,qBAAqB,CAAE,CAAC;AACvD,gBAAA,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;AACvC,gBAAA,eAAe,CAAC,IAA6C,EAAE,IAAI,CAAC,CAAC;aACtE;AACH,SAAC,CACF,CAAC;AAEF,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;YAC1D,MAAM,OAAO,GAA0C,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC5E,YAAA,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;AAChC,YAAA,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC;AAClB,YAAA,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;AACrB,YAAA,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,QAAS,CAAC;SAClC;AAED,QAAA,OAAO,CAAC,qBAAqB,CAAC,CAAC,MAAW,KAAI;YAC5C,MAAM,OAAO,GAA0C,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AAC9F,YAAA,eAAe,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AACnC,SAAC,CAAC,CAAC;KACJ;AAED;;;;;AAKG;AACH,IAAA,OAAO,sBAAsB,CAC3B,GAAkB,EAClB,GAAQ,EAAA;AAER,QAAA,OAAO,IAAI,CAAC;KACb;yHAhKU,OAAO,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,WAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,eAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAAP,OAAO,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,YAAA,EAAA,cAAA,EAAA,aAAA,EAAA,eAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAAP,OAAO,EAAA,UAAA,EAAA,CAAA;kBAJnB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,kBAAkB;AAC5B,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;6IAOK,OAAO,EAAA,CAAA;sBADV,KAAK;gBAwBF,YAAY,EAAA,CAAA;sBADf,KAAK;gBAkCF,aAAa,EAAA,CAAA;sBADhB,KAAK;;AA2GR,SAAS,eAAe,CACtB,IAAwC,EACxC,MAA+B,EAAA;IAE/B,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC;AACvC,CAAC;AAED,SAAS,WAAW,CAAC,IAAS,EAAA;AAC5B,IAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAI,CAAC;AACrC;;AC5TA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0IG;MAKU,IAAI,CAAA;IAOf,WACU,CAAA,cAAgC,EACxC,WAAwC,EAAA;QADhC,IAAc,CAAA,cAAA,GAAd,cAAc,CAAkB;AAPlC,QAAA,IAAA,CAAA,QAAQ,GAAmB,IAAI,WAAW,EAAK,CAAC;QAChD,IAAgB,CAAA,gBAAA,GAAuC,IAAI,CAAC;QAC5D,IAAgB,CAAA,gBAAA,GAAuC,IAAI,CAAC;QAC5D,IAAY,CAAA,YAAA,GAA2C,IAAI,CAAC;QAC5D,IAAY,CAAA,YAAA,GAA2C,IAAI,CAAC;AAMlE,QAAA,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC;KACrC;AAED;;AAEG;IACH,IACI,IAAI,CAAC,SAAY,EAAA;AACnB,QAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,SAAS,CAAC;QACzD,IAAI,CAAC,WAAW,EAAE,CAAC;KACpB;AAED;;AAEG;IACH,IACI,QAAQ,CAAC,WAA+C,EAAA;AAC1D,QAAA,cAAc,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;AACxC,QAAA,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC;AACpC,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,WAAW,EAAE,CAAC;KACpB;AAED;;AAEG;IACH,IACI,QAAQ,CAAC,WAA+C,EAAA;AAC1D,QAAA,cAAc,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;AACxC,QAAA,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC;AACpC,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,WAAW,EAAE,CAAC;KACpB;IAEO,WAAW,GAAA;AACjB,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE;AAC3B,YAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;AAC5B,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AACzB,gBAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACzB,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,kBAAkB,CACxD,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,QAAQ,CACd,CAAC;iBACH;aACF;SACF;aAAM;AACL,YAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;AAC5B,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AACzB,gBAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACzB,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,kBAAkB,CACxD,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,QAAQ,CACd,CAAC;iBACH;aACF;SACF;KACF;AAeD;;;;;AAKG;AACH,IAAA,OAAO,sBAAsB,CAC3B,GAAY,EACZ,GAAQ,EAAA;AAER,QAAA,OAAO,IAAI,CAAC;KACb;yHA/FU,IAAI,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,WAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAAJ,IAAI,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAAJ,IAAI,EAAA,UAAA,EAAA,CAAA;kBAJhB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,QAAQ;AAClB,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;+GAmBK,IAAI,EAAA,CAAA;sBADP,KAAK;gBAUF,QAAQ,EAAA,CAAA;sBADX,KAAK;gBAYF,QAAQ,EAAA,CAAA;sBADX,KAAK;;AA6DR;;AAEG;MACU,WAAW,CAAA;AAAxB,IAAA,WAAA,GAAA;QACS,IAAS,CAAA,SAAA,GAAM,IAAK,CAAC;QACrB,IAAI,CAAA,IAAA,GAAM,IAAK,CAAC;KACxB;AAAA,CAAA;AAED,SAAS,cAAc,CAAC,QAAgB,EAAE,WAAoC,EAAA;AAC5E,IAAA,MAAM,mBAAmB,GAAG,CAAC,EAAE,CAAC,WAAW,IAAI,WAAW,CAAC,kBAAkB,CAAC,CAAC;IAC/E,IAAI,CAAC,mBAAmB,EAAE;AACxB,QAAA,MAAM,IAAI,KAAK,CAAC,CAAA,EAAG,QAAQ,CAAA,sCAAA,EAAyCD,UAAS,CAAC,WAAW,CAAC,CAAI,EAAA,CAAA,CAAC,CAAC;KACjG;AACH;;ACvQA;;;AAGG;AACI,MAAM,2BAA2B,GAAG,IAAI;;MCYlC,UAAU,CAAA;IAGrB,WACU,CAAA,iBAAmC,EACnC,YAAiC,EAAA;QADjC,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAkB;QACnC,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAqB;QAJnC,IAAQ,CAAA,QAAA,GAAG,KAAK,CAAC;KAKrB;IAEJ,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;KAC9D;IAED,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AACtB,QAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;KAChC;AAED,IAAA,YAAY,CAAC,OAAgB,EAAA;AAC3B,QAAA,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAC7B,IAAI,CAAC,MAAM,EAAE,CAAC;SACf;AAAM,aAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;YACpC,IAAI,CAAC,OAAO,EAAE,CAAC;SAChB;KACF;AACF,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiEG;MAKU,QAAQ,CAAA;AAJrB,IAAA,WAAA,GAAA;QAKU,IAAa,CAAA,aAAA,GAAiB,EAAE,CAAC;QACjC,IAAY,CAAA,YAAA,GAAG,KAAK,CAAC;QACrB,IAAU,CAAA,UAAA,GAAG,CAAC,CAAC;QACf,IAAmB,CAAA,mBAAA,GAAG,CAAC,CAAC;QACxB,IAAiB,CAAA,iBAAA,GAAG,KAAK,CAAC;AA0DnC,KAAA;IAvDC,IACI,QAAQ,CAAC,QAAa,EAAA;AACxB,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC1B,QAAA,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE;AACzB,YAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;SAChC;KACF;;IAGD,QAAQ,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;KAC1B;;AAGD,IAAA,WAAW,CAAC,IAAgB,EAAA;AAC1B,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC/B;;AAGD,IAAA,UAAU,CAAC,KAAU,EAAA;QACnB,MAAM,OAAO,GAAG,2BAA2B;AACzC,cAAE,KAAK,KAAK,IAAI,CAAC,SAAS;AAC1B,cAAE,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC;AAC5B,QAAA,IAAI,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,OAAO,MAAM,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE;AAC5F,YAAA,OAAO,CAAC,IAAI,CACVE,mBAAkB,4DAEhB,uHAAuH;gBACrH,CAA8B,2BAAA,EAAA,cAAc,CAC1C,KAAK,CACN,CAAA,mCAAA,EAAsC,cAAc,CACnD,IAAI,CAAC,SAAS,CACf,CAAsE,oEAAA,CAAA;gBACvE,oLAAoL,CACvL,CACF,CAAC;SACH;AACD,QAAA,IAAI,CAAC,iBAAiB,KAAK,OAAO,CAAC;QACnC,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,IAAI,CAAC,mBAAmB,KAAK,IAAI,CAAC,UAAU,EAAE;YAChD,IAAI,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AAClD,YAAA,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;AAC7B,YAAA,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;SAChC;AACD,QAAA,OAAO,OAAO,CAAC;KAChB;AAEO,IAAA,mBAAmB,CAAC,UAAmB,EAAA;AAC7C,QAAA,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,UAAU,KAAK,IAAI,CAAC,YAAY,EAAE;AACrE,YAAA,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC;AAC/B,YAAA,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,aAAa,EAAE;AAC5C,gBAAA,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;aACtC;SACF;KACF;yHA9DU,QAAQ,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAAR,QAAQ,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAAR,QAAQ,EAAA,UAAA,EAAA,CAAA;kBAJpB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,YAAY;AACtB,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;8BAUK,QAAQ,EAAA,CAAA;sBADX,KAAK;;AAyDR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCG;MAKU,YAAY,CAAA;AAOvB,IAAA,WAAA,CACE,aAA+B,EAC/B,WAAgC,EACJ,QAAkB,EAAA;QAAlB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;AAE9C,QAAA,IAAI,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,CAAC,QAAQ,EAAE;AAChE,YAAA,kCAAkC,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;SACpE;QAED,QAAQ,CAAC,QAAQ,EAAE,CAAC;QACpB,IAAI,CAAC,KAAK,GAAG,IAAI,UAAU,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;KACzD;AAED;;;AAGG;IACH,SAAS,GAAA;AACP,QAAA,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;KACtE;yHA1BU,YAAY,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,WAAA,EAAA,EAAA,EAAA,KAAA,EAAA,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAAZ,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBAJxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BAWI,QAAQ;;0BAAI,IAAI;yCALV,YAAY,EAAA,CAAA;sBAApB,KAAK;;AAwBR;;;;;;;;;;;;;AAaG;MAKU,eAAe,CAAA;AAC1B,IAAA,WAAA,CACE,aAA+B,EAC/B,WAAgC,EACZ,QAAkB,EAAA;AAEtC,QAAA,IAAI,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,CAAC,QAAQ,EAAE;AAChE,YAAA,kCAAkC,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;SAC1E;QAED,QAAQ,CAAC,WAAW,CAAC,IAAI,UAAU,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC;KAClE;yHAXU,eAAe,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,WAAA,EAAA,EAAA,EAAA,KAAA,EAAA,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAAf,eAAe,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAJ3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,mBAAmB;AAC7B,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BAKI,QAAQ;;0BAAI,IAAI;;AAUrB,SAAS,kCAAkC,CAAC,QAAgB,EAAE,aAAqB,EAAA;AACjF,IAAA,MAAM,IAAID,aAAY,CAEpB,IAAA,oDAAA,CAAA,qBAAA,EAAwB,QAAQ,CAAc,YAAA,CAAA;AAC5C,QAAA,CAAA,eAAA,EAAkB,aAAa,CAA+E,6EAAA,CAAA;AAC9G,QAAA,CAAA,+BAAA,CAAiC,CACpC,CAAC;AACJ,CAAC;AAED,SAAS,cAAc,CAAC,KAAc,EAAA;AACpC,IAAA,OAAO,OAAO,KAAK,KAAK,QAAQ,GAAG,CAAA,CAAA,EAAI,KAAK,CAAA,CAAA,CAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AAClE;;ACzRA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BG;MAKU,QAAQ,CAAA;AAInB,IAAA,WAAA,CAAoB,aAA6B,EAAA;QAA7B,IAAa,CAAA,aAAA,GAAb,aAAa,CAAgB;QAFzC,IAAU,CAAA,UAAA,GAA8B,EAAE,CAAC;KAEE;IAErD,IACI,QAAQ,CAAC,KAAa,EAAA;AACxB,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;KACzB;IAED,OAAO,CAAC,KAAa,EAAE,UAAsB,EAAA;AAC3C,QAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,UAAU,CAAC;KACrC;AAEO,IAAA,WAAW,CAAC,WAAmB,EAAA;QACrC,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnB,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC3C,QAAA,MAAM,GAAG,GAAG,iBAAiB,CAAC,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QACtE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;KAC1C;IAEO,WAAW,GAAA;QACjB,IAAI,IAAI,CAAC,WAAW;AAAE,YAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;KAClD;AAEO,IAAA,aAAa,CAAC,IAAgB,EAAA;QACpC,IAAI,IAAI,EAAE;AACR,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AACxB,YAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;SAC3B;KACF;yHAhCU,QAAQ,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAE,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAAR,QAAQ,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAAR,QAAQ,EAAA,UAAA,EAAA,CAAA;kBAJpB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,YAAY;AACtB,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;gFAQK,QAAQ,EAAA,CAAA;sBADX,KAAK;;AA6BR;;;;;;;;;;;;;;;;;;;AAmBG;MAKU,YAAY,CAAA;AACvB,IAAA,WAAA,CACoC,KAAa,EAC/C,QAA6B,EAC7B,aAA+B,EACvB,QAAkB,EAAA;QAHQ,IAAK,CAAA,KAAA,GAAL,KAAK,CAAQ;QAK/C,MAAM,SAAS,GAAY,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QACjD,QAAQ,CAAC,OAAO,CAAC,SAAS,GAAG,CAAI,CAAA,EAAA,KAAK,CAAE,CAAA,GAAG,KAAK,EAAE,IAAI,UAAU,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,CAAC;KAC5F;AATU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,kBAEV,cAAc,EAAA,SAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,WAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAFhB,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBAJxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BAGI,SAAS;2BAAC,cAAc,CAAA;;0BAGxB,IAAI;;;AC9FT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCG;MAKU,OAAO,CAAA;AAIlB,IAAA,WAAA,CACU,KAAiB,EACjB,QAAyB,EACzB,SAAoB,EAAA;QAFpB,IAAK,CAAA,KAAA,GAAL,KAAK,CAAY;QACjB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;QACzB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAW;QANtB,IAAQ,CAAA,QAAA,GAA+C,IAAI,CAAC;QAC5D,IAAO,CAAA,OAAA,GAAmD,IAAI,CAAC;KAMnE;IAEJ,IACI,OAAO,CAAC,MAAiD,EAAA;AAC3D,QAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC;AACvB,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,MAAM,EAAE;AAC3B,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,CAAC;SACpD;KACF;IAED,SAAS,GAAA;AACP,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAS,CAAC,CAAC;YAClD,IAAI,OAAO,EAAE;AACX,gBAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;aAC7B;SACF;KACF;IAEO,SAAS,CAAC,WAAmB,EAAE,KAAyC,EAAA;AAC9E,QAAA,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC5C,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,SAAS,GAAI,mBAAmB,CAAC,QAAmB,CAAC;AAE9F,QAAA,IAAI,KAAK,IAAI,IAAI,EAAE;AACjB,YAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,CACrB,IAAI,CAAC,KAAK,CAAC,aAAa,EACxB,IAAI,EACJ,IAAI,GAAG,CAAA,EAAG,KAAK,CAAA,EAAG,IAAI,CAAA,CAAE,GAAG,KAAK,EAChC,KAAK,CACN,CAAC;SACH;aAAM;AACL,YAAA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;SACnE;KACF;AAEO,IAAA,aAAa,CAAC,OAAiD,EAAA;AACrE,QAAA,OAAO,CAAC,kBAAkB,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;QACzE,OAAO,CAAC,gBAAgB,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;QACtF,OAAO,CAAC,kBAAkB,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;KACzF;yHA/CU,OAAO,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,eAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAAP,OAAO,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAAP,OAAO,EAAA,UAAA,EAAA,CAAA;kBAJnB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,WAAW;AACrB,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;qIAYK,OAAO,EAAA,CAAA;sBADV,KAAK;uBAAC,SAAS,CAAA;;;ACjDlB;;;;;;;;;;;;;;;;;;;;;;;AAuBG;MAKU,gBAAgB,CAAA;AAmB3B,IAAA,WAAA,CAAoB,iBAAmC,EAAA;QAAnC,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAkB;QAlB/C,IAAQ,CAAA,QAAA,GAA8B,IAAI,CAAC;AAEnD;;;;;AAKG;QACa,IAAuB,CAAA,uBAAA,GAAa,IAAI,CAAC;AAEzD;;AAEG;QACa,IAAgB,CAAA,gBAAA,GAA0B,IAAI,CAAC;;QAG/C,IAAwB,CAAA,wBAAA,GAAoB,IAAI,CAAC;KAEN;AAE3D,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,IAAI,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE;AACrC,YAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC;AAEhD,YAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,gBAAA,gBAAgB,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;aAClE;;AAGD,YAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;AAC1B,gBAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACrB,OAAO;aACR;;;AAID,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;AACtD,YAAA,IAAI,CAAC,QAAQ,GAAG,gBAAgB,CAAC,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,EAAE,WAAW,EAAE;AACtF,gBAAA,QAAQ,EAAE,IAAI,CAAC,wBAAwB,IAAI,SAAS;AACrD,aAAA,CAAC,CAAC;SACJ;KACF;AAED;;;;AAIG;AACK,IAAA,mBAAmB,CAAC,OAAsB,EAAA;AAChD,QAAA,OAAO,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;KAC/E;AAED;;;;AAIG;IACK,0BAA0B,GAAA;AAChC,QAAA,OAAU,IAAI,KAAK,CACjB,EAAE,EACF;YACE,GAAG,EAAE,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,KAAI;AAC/B,gBAAA,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;AACjC,oBAAA,OAAO,KAAK,CAAC;iBACd;AACD,gBAAA,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,uBAAuB,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;aAClE;YACD,GAAG,EAAE,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,KAAI;AAC/B,gBAAA,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;AACjC,oBAAA,OAAO,SAAS,CAAC;iBAClB;AACD,gBAAA,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,uBAAuB,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;aAClE;AACF,SAAA,CACF,CAAC;KACH;yHA5EU,gBAAgB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;6GAAhB,gBAAgB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,EAAA,uBAAA,EAAA,yBAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,wBAAA,EAAA,0BAAA,EAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAAhB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAJ5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oBAAoB;AAC9B,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;qFAUiB,uBAAuB,EAAA,CAAA;sBAAtC,KAAK;gBAKU,gBAAgB,EAAA,CAAA;sBAA/B,KAAK;gBAGU,wBAAwB,EAAA,CAAA;sBAAvC,KAAK;;;AC7BR;;;AAGG;AACI,MAAM,iBAAiB,GAAe;IAC3C,OAAO;IACP,iBAAiB;IACjB,OAAO;IACP,IAAI;IACJ,gBAAgB;IAChB,OAAO;IACP,QAAQ;IACR,YAAY;IACZ,eAAe;IACf,QAAQ;IACR,YAAY;CACb;;ACxCe,SAAA,wBAAwB,CAAC,IAAe,EAAE,KAAa,EAAA;AACrE,IAAA,OAAO,IAAIF,aAAY,CAErB,IAAA,+CAAA,SAAS,IAAI,CAAyB,sBAAA,EAAA,KAAK,CAAe,YAAA,EAAAD,UAAS,CAAC,IAAI,CAAC,CAAA,CAAA,CAAG,CAC7E,CAAC;AACJ;;ACaA,MAAM,oBAAoB,CAAA;IACxB,kBAAkB,CAAC,KAAwB,EAAE,iBAAsB,EAAA;;;;;;;;QAQjE,OAAO,SAAS,CAAC,MACf,KAAK,CAAC,SAAS,CAAC;AACd,YAAA,IAAI,EAAE,iBAAiB;AACvB,YAAA,KAAK,EAAE,CAAC,CAAM,KAAI;AAChB,gBAAA,MAAM,CAAC,CAAC;aACT;AACF,SAAA,CAAC,CACH,CAAC;KACH;AAED,IAAA,OAAO,CAAC,YAA4B,EAAA;;QAElC,SAAS,CAAC,MAAM,YAAY,CAAC,WAAW,EAAE,CAAC,CAAC;KAC7C;AACF,CAAA;AAED,MAAM,eAAe,CAAA;IACnB,kBAAkB,CAAC,KAAmB,EAAE,iBAAkC,EAAA;QACxE,OAAO,KAAK,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,KAAI;AACzC,YAAA,MAAM,CAAC,CAAC;AACV,SAAC,CAAC,CAAC;KACJ;IAED,OAAO,CAAC,YAA0B,EAAA,GAAU;AAC7C,CAAA;AAED,MAAM,gBAAgB,GAAG,IAAI,eAAe,EAAE,CAAC;AAC/C,MAAM,qBAAqB,GAAG,IAAI,oBAAoB,EAAE,CAAC;AAEzD;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BG;MAMU,SAAS,CAAA;AASpB,IAAA,WAAA,CAAY,GAAsB,EAAA;QAP1B,IAAY,CAAA,YAAA,GAAQ,IAAI,CAAC;QACzB,IAAyB,CAAA,yBAAA,GAAG,IAAI,CAAC;QAEjC,IAAa,CAAA,aAAA,GAAyC,IAAI,CAAC;QAC3D,IAAI,CAAA,IAAA,GAAgE,IAAI,CAAC;QACzE,IAAS,CAAA,SAAA,GAAgC,IAAI,CAAC;;;AAKpD,QAAA,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;KACjB;IAED,WAAW,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,QAAQ,EAAE,CAAC;SACjB;;;;;AAKD,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;KAClB;AASD,IAAA,SAAS,CAAI,GAAoE,EAAA;AAC/E,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACd,IAAI,GAAG,EAAE;AACP,gBAAA,IAAI;;;;AAIF,oBAAA,IAAI,CAAC,yBAAyB,GAAG,KAAK,CAAC;AACvC,oBAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;iBACtB;wBAAS;AACR,oBAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;iBACvC;aACF;YACD,OAAO,IAAI,CAAC,YAAY,CAAC;SAC1B;AAED,QAAA,IAAI,GAAG,KAAK,IAAI,CAAC,IAAI,EAAE;YACrB,IAAI,CAAC,QAAQ,EAAE,CAAC;AAChB,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;SAC5B;QAED,OAAO,IAAI,CAAC,YAAY,CAAC;KAC1B;AAEO,IAAA,UAAU,CAAC,GAAyD,EAAA;AAC1E,QAAA,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAChB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAC,KAAa,KACxE,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,KAAK,CAAC,CACpC,CAAC;KACH;AAEO,IAAA,eAAe,CACrB,GAAyD,EAAA;AAEzD,QAAA,IAAI,UAAU,CAAC,GAAG,CAAC,EAAE;AACnB,YAAA,OAAO,gBAAgB,CAAC;SACzB;AAED,QAAA,IAAI,eAAe,CAAC,GAAG,CAAC,EAAE;AACxB,YAAA,OAAO,qBAAqB,CAAC;SAC9B;AAED,QAAA,MAAM,wBAAwB,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;KAChD;IAEO,QAAQ,GAAA;;;QAGd,IAAI,CAAC,SAAU,CAAC,OAAO,CAAC,IAAI,CAAC,aAAc,CAAC,CAAC;AAC7C,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AACzB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;KAClB;IAEO,kBAAkB,CAAC,KAAU,EAAE,KAAa,EAAA;AAClD,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,IAAI,EAAE;AACvB,YAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;AAC1B,YAAA,IAAI,IAAI,CAAC,yBAAyB,EAAE;AAClC,gBAAA,IAAI,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC;aAC3B;SACF;KACF;yHA/FU,SAAS,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;uHAAT,SAAS,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,KAAA,EAAA,CAAA,CAAA,EAAA;;sGAAT,SAAS,EAAA,UAAA,EAAA,CAAA;kBALrB,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACJ,oBAAA,IAAI,EAAE,OAAO;AACb,oBAAA,IAAI,EAAE,KAAK;AACX,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;;ACxFD;;;;;;;;;;;;;;AAcG;MAKU,aAAa,CAAA;AAOxB,IAAA,SAAS,CAAC,KAAgC,EAAA;QACxC,IAAI,KAAK,IAAI,IAAI;AAAE,YAAA,OAAO,IAAI,CAAC;AAC/B,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,YAAA,MAAM,wBAAwB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;SACtD;AACD,QAAA,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC;KAC5B;yHAbU,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;uHAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,WAAA,EAAA,CAAA,CAAA,EAAA;;sGAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBAJzB,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACJ,oBAAA,IAAI,EAAE,WAAW;AACjB,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;AAiBD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,MAAM,gBAAgB,GACpB,orPAAorP,CAAC;AAEvrP;;;;;;;;;;;;;;;;AAgBG;MAKU,aAAa,CAAA;AAOxB,IAAA,SAAS,CAAC,KAAgC,EAAA;QACxC,IAAI,KAAK,IAAI,IAAI;AAAE,YAAA,OAAO,IAAI,CAAC;AAC/B,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,YAAA,MAAM,wBAAwB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;SACtD;AAED,QAAA,OAAO,KAAK,CAAC,OAAO,CAClB,gBAAgB,EAChB,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAC3D,CAAC;KACH;yHAjBU,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;uHAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,WAAA,EAAA,CAAA,CAAA,EAAA;;sGAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBAJzB,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACJ,oBAAA,IAAI,EAAE,WAAW;AACjB,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;AAqBD;;;;;;;AAOG;MAKU,aAAa,CAAA;AAOxB,IAAA,SAAS,CAAC,KAAgC,EAAA;QACxC,IAAI,KAAK,IAAI,IAAI;AAAE,YAAA,OAAO,IAAI,CAAC;AAC/B,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,YAAA,MAAM,wBAAwB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;SACtD;AACD,QAAA,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC;KAC5B;yHAbU,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;uHAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,WAAA,EAAA,CAAA,CAAA,EAAA;;sGAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBAJzB,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACJ,oBAAA,IAAI,EAAE,WAAW;AACjB,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;;AC1FD;;;AAGG;AACI,MAAM,mBAAmB,GAAG,YAAY;;ACV/C;;;;;AAKG;AACU,MAAA,0BAA0B,GAAG,IAAI,cAAc,CAC1D,SAAS,GAAG,4BAA4B,GAAG,EAAE,EAC7C;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BG;AACU,MAAA,yBAAyB,GAAG,IAAI,cAAc,CACzD,SAAS,GAAG,2BAA2B,GAAG,EAAE,EAC5C;AAEF;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6JG;AACH;MAKa,QAAQ,CAAA;AACnB,IAAA,WAAA,CAC6B,MAAc,EACe,eAA+B,EAChC,cAAsC,EAAA;QAFlE,IAAM,CAAA,MAAA,GAAN,MAAM,CAAQ;QACe,IAAe,CAAA,eAAA,GAAf,eAAe,CAAgB;QAChC,IAAc,CAAA,cAAA,GAAd,cAAc,CAAwB;KAC3F;AAkCJ,IAAA,SAAS,CACP,KAAgD,EAChD,MAAe,EACf,QAAiB,EACjB,MAAe,EAAA;QAEf,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK;AAAE,YAAA,OAAO,IAAI,CAAC;AAElE,QAAA,IAAI;YACF,MAAM,OAAO,GAAG,MAAM,IAAI,IAAI,CAAC,cAAc,EAAE,UAAU,IAAI,mBAAmB,CAAC;AACjF,YAAA,MAAM,SAAS,GACb,QAAQ,IAAI,IAAI,CAAC,cAAc,EAAE,QAAQ,IAAI,IAAI,CAAC,eAAe,IAAI,SAAS,CAAC;AACjF,YAAA,OAAO,UAAU,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;SACrE;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,wBAAwB,CAAC,QAAQ,EAAG,KAAe,CAAC,OAAO,CAAC,CAAC;SACpE;KACF;AAvDU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,QAAQ,EAET,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,SAAS,EACT,EAAA,EAAA,KAAA,EAAA,0BAA0B,6BAC1B,yBAAyB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;uHAJxB,QAAQ,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,CAAA,EAAA;;sGAAR,QAAQ,EAAA,UAAA,EAAA,CAAA;kBAJpB,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACJ,oBAAA,IAAI,EAAE,MAAM;AACZ,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BAGI,MAAM;2BAAC,SAAS,CAAA;;0BAChB,MAAM;2BAAC,0BAA0B,CAAA;;0BAAG,QAAQ;;0BAC5C,MAAM;2BAAC,yBAAyB,CAAA;;0BAAG,QAAQ;;;ACtNhD,MAAM,qBAAqB,GAAW,IAAI,CAAC;AAE3C;;;;;;;;;;;;;AAaG;MAKU,cAAc,CAAA;AACzB,IAAA,WAAA,CAAoB,aAA6B,EAAA;QAA7B,IAAa,CAAA,aAAA,GAAb,aAAa,CAAgB;KAAI;AAErD;;;;;;AAMG;AACH,IAAA,SAAS,CACP,KAAgC,EAChC,SAAoC,EACpC,MAAe,EAAA;QAEf,IAAI,KAAK,IAAI,IAAI;AAAE,YAAA,OAAO,EAAE,CAAC;QAE7B,IAAI,OAAO,SAAS,KAAK,QAAQ,IAAI,SAAS,KAAK,IAAI,EAAE;AACvD,YAAA,MAAM,wBAAwB,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;SAC3D;AAED,QAAA,MAAM,GAAG,GAAG,iBAAiB,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;AAEzF,QAAA,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,qBAAqB,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;KACxE;yHAxBU,cAAc,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAG,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;uHAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,CAAA,EAAA;;sGAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAJ1B,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACJ,oBAAA,IAAI,EAAE,YAAY;AAClB,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;;ACrBD;;;;;;;;;;;;;;;;AAgBG;MAKU,cAAc,CAAA;AACzB;;;;AAIG;IACH,SAAS,CAAC,KAAgC,EAAE,OAAgC,EAAA;QAC1E,IAAI,KAAK,IAAI,IAAI;AAAE,YAAA,OAAO,EAAE,CAAC;QAE7B,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC5D,YAAA,MAAM,wBAAwB,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;SACzD;AAED,QAAA,IAAI,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;AACjC,YAAA,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC;SACvB;AAED,QAAA,IAAI,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;AACnC,YAAA,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC;SACzB;AAED,QAAA,OAAO,EAAE,CAAC;KACX;yHAtBU,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;uHAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,CAAA,EAAA;;sGAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAJ1B,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACJ,oBAAA,IAAI,EAAE,YAAY;AAClB,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;;ACtBD;;;;;;;;;;;;;;AAcG;MAMU,QAAQ,CAAA;AACnB;;AAEG;AACH,IAAA,SAAS,CAAC,KAAU,EAAA;QAClB,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;KACvC;yHANU,QAAQ,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;uHAAR,QAAQ,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,KAAA,EAAA,CAAA,CAAA,EAAA;;sGAAR,QAAQ,EAAA,UAAA,EAAA,CAAA;kBALpB,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACJ,oBAAA,IAAI,EAAE,MAAM;AACZ,oBAAA,IAAI,EAAE,KAAK;AACX,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;;ACZD,SAAS,gBAAgB,CAAO,GAAM,EAAE,KAAQ,EAAA;IAC9C,OAAO,EAAC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAC,CAAC;AAClC,CAAC;AAaD;;;;;;;;;;;;;;;;;;;AAmBG;MAMU,YAAY,CAAA;AACvB,IAAA,WAAA,CAA6B,OAAwB,EAAA;QAAxB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAiB;QAG7C,IAAS,CAAA,SAAA,GAA8B,EAAE,CAAC;QAC1C,IAAS,CAAA,SAAA,GAA6D,iBAAiB,CAAC;KAJvC;AAuCzD,IAAA,SAAS,CACP,KAAkF,EAClF,SAAA,GAA8D,iBAAiB,EAAA;AAE/E,QAAA,IAAI,CAAC,KAAK,KAAK,EAAE,KAAK,YAAY,GAAG,CAAC,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC,EAAE;AACpE,YAAA,OAAO,IAAI,CAAC;SACb;;AAGD,QAAA,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;QAElD,MAAM,aAAa,GAAiC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAY,CAAC,CAAC;AACnF,QAAA,MAAM,gBAAgB,GAAG,SAAS,KAAK,IAAI,CAAC,SAAS,CAAC;QAEtD,IAAI,aAAa,EAAE;AACjB,YAAA,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;AACpB,YAAA,aAAa,CAAC,WAAW,CAAC,CAAC,CAA6B,KAAI;AAC1D,gBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,YAAa,CAAC,CAAC,CAAC;AAChE,aAAC,CAAC,CAAC;SACJ;AACD,QAAA,IAAI,aAAa,IAAI,gBAAgB,EAAE;AACrC,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC/B,YAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;SAC5B;QACD,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;yHAjEU,YAAY,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,eAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;uHAAZ,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,KAAA,EAAA,CAAA,CAAA,EAAA;;sGAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBALxB,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACJ,oBAAA,IAAI,EAAE,UAAU;AAChB,oBAAA,IAAI,EAAE,KAAK;AACX,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;AAqEe,SAAA,iBAAiB,CAC/B,SAAyB,EACzB,SAAyB,EAAA;AAEzB,IAAA,MAAM,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC;AACxB,IAAA,MAAM,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC;;IAExB,IAAI,CAAC,KAAK,CAAC;AAAE,QAAA,OAAO,CAAC,CAAC;;IAEtB,IAAI,CAAC,KAAK,SAAS;AAAE,QAAA,OAAO,CAAC,CAAC;IAC9B,IAAI,CAAC,KAAK,SAAS;QAAE,OAAO,CAAC,CAAC,CAAC;;IAE/B,IAAI,CAAC,KAAK,IAAI;AAAE,QAAA,OAAO,CAAC,CAAC;IACzB,IAAI,CAAC,KAAK,IAAI;QAAE,OAAO,CAAC,CAAC,CAAC;IAC1B,IAAI,OAAO,CAAC,IAAI,QAAQ,IAAI,OAAO,CAAC,IAAI,QAAQ,EAAE;AAChD,QAAA,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;KACvB;IACD,IAAI,OAAO,CAAC,IAAI,QAAQ,IAAI,OAAO,CAAC,IAAI,QAAQ,EAAE;QAChD,OAAO,CAAC,GAAG,CAAC,CAAC;KACd;IACD,IAAI,OAAO,CAAC,IAAI,SAAS,IAAI,OAAO,CAAC,IAAI,SAAS,EAAE;AAClD,QAAA,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;KACvB;;AAED,IAAA,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC1B,IAAA,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAC1B,OAAO,OAAO,IAAI,OAAO,GAAG,CAAC,GAAG,OAAO,GAAG,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC7D;;ACzIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DG;MAKU,WAAW,CAAA;AACtB,IAAA,WAAA,CAAuC,OAAe,EAAA;QAAf,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;KAAI;AAS1D;;;;;;AAMG;AACH,IAAA,SAAS,CACP,KAAyC,EACzC,UAAmB,EACnB,MAAe,EAAA;AAEf,QAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;AAAE,YAAA,OAAO,IAAI,CAAC;AAEjC,QAAA,MAAM,KAAK,IAAI,CAAC,OAAO,CAAC;AAExB,QAAA,IAAI;AACF,YAAA,MAAM,GAAG,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;YAC/B,OAAO,YAAY,CAAC,GAAG,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;SAC9C;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,wBAAwB,CAAC,WAAW,EAAG,KAAe,CAAC,OAAO,CAAC,CAAC;SACvE;KACF;AAhCU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAW,kBACF,SAAS,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;uHADlB,WAAW,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,QAAA,EAAA,CAAA,CAAA,EAAA;;sGAAX,WAAW,EAAA,UAAA,EAAA,CAAA;kBAJvB,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACd,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BAEc,MAAM;2BAAC,SAAS,CAAA;;AAkC/B;;;;;;;;;;;;;;;;;;;AAmBG;MAKU,WAAW,CAAA;AACtB,IAAA,WAAA,CAAuC,OAAe,EAAA;QAAf,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;KAAI;AAS1D;;;;;;;;;;;;;;;AAeG;AACH,IAAA,SAAS,CACP,KAAyC,EACzC,UAAmB,EACnB,MAAe,EAAA;AAEf,QAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;AAAE,YAAA,OAAO,IAAI,CAAC;AACjC,QAAA,MAAM,KAAK,IAAI,CAAC,OAAO,CAAC;AACxB,QAAA,IAAI;AACF,YAAA,MAAM,GAAG,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;YAC/B,OAAO,aAAa,CAAC,GAAG,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;SAC/C;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,wBAAwB,CAAC,WAAW,EAAG,KAAe,CAAC,OAAO,CAAC,CAAC;SACvE;KACF;AAvCU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAW,kBACF,SAAS,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;uHADlB,WAAW,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,CAAA,EAAA;;sGAAX,WAAW,EAAA,UAAA,EAAA,CAAA;kBAJvB,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACJ,oBAAA,IAAI,EAAE,SAAS;AACf,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BAEc,MAAM;2BAAC,SAAS,CAAA;;AAyC/B;;;;;;;;;;;;;;;;;;;;AAoBG;MAKU,YAAY,CAAA;IACvB,WAC6B,CAAA,OAAe,EACH,oBAAA,GAA+B,KAAK,EAAA;QADhD,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QACH,IAAoB,CAAA,oBAAA,GAApB,oBAAoB,CAAgB;KACzE;AAuBJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCG;AACH,IAAA,SAAS,CACP,KAAyC,EACzC,YAAA,GAAuB,IAAI,CAAC,oBAAoB,EAChD,OAAkE,GAAA,QAAQ,EAC1E,UAAmB,EACnB,MAAe,EAAA;AAEf,QAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;AAAE,YAAA,OAAO,IAAI,CAAC;AAEjC,QAAA,MAAM,KAAK,IAAI,CAAC,OAAO,CAAC;AAExB,QAAA,IAAI,OAAO,OAAO,KAAK,SAAS,EAAE;AAChC,YAAA,IAAI,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAU,OAAO,IAAS,OAAO,CAAC,IAAI,EAAE;AACxF,gBAAA,OAAO,CAAC,IAAI,CACV,CAAA,wMAAA,CAA0M,CAC3M,CAAC;aACH;YACD,OAAO,GAAG,OAAO,GAAG,QAAQ,GAAG,MAAM,CAAC;SACvC;AAED,QAAA,IAAI,QAAQ,GAAW,YAAY,IAAI,IAAI,CAAC,oBAAoB,CAAC;AACjE,QAAA,IAAI,OAAO,KAAK,MAAM,EAAE;YACtB,IAAI,OAAO,KAAK,QAAQ,IAAI,OAAO,KAAK,eAAe,EAAE;AACvD,gBAAA,QAAQ,GAAG,iBAAiB,CAAC,QAAQ,EAAE,OAAO,KAAK,QAAQ,GAAG,MAAM,GAAG,QAAQ,EAAE,MAAM,CAAC,CAAC;aAC1F;iBAAM;gBACL,QAAQ,GAAG,OAAO,CAAC;aACpB;SACF;AAED,QAAA,IAAI;AACF,YAAA,MAAM,GAAG,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;AAC/B,YAAA,OAAO,cAAc,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;SACxE;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,wBAAwB,CAAC,YAAY,EAAG,KAAe,CAAC,OAAO,CAAC,CAAC;SACxE;KACF;yHA/FU,YAAY,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAEb,SAAS,EAAA,EAAA,EAAA,KAAA,EACT,qBAAqB,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;uHAHpB,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,UAAA,EAAA,CAAA,CAAA,EAAA;;sGAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBAJxB,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACJ,oBAAA,IAAI,EAAE,UAAU;AAChB,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BAGI,MAAM;2BAAC,SAAS,CAAA;;0BAChB,MAAM;2BAAC,qBAAqB,CAAA;;AA+FjC,SAAS,OAAO,CAAC,KAAyC,EAAA;AACxD,IAAA,OAAO,EAAE,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK,CAAC,CAAC;AAC7D,CAAC;AAED;;AAEG;AACH,SAAS,WAAW,CAAC,KAAsB,EAAA;;AAEzC,IAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;AAC1E,QAAA,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;KACtB;AACD,IAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,QAAA,MAAM,IAAI,KAAK,CAAC,GAAG,KAAK,CAAA,gBAAA,CAAkB,CAAC,CAAC;KAC7C;AACD,IAAA,OAAO,KAAK,CAAC;AACf;;ACtTA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCG;MAMU,SAAS,CAAA;AAyBpB,IAAA,SAAS,CACP,KAAmD,EACnD,KAAa,EACb,GAAY,EAAA;QAEZ,IAAI,KAAK,IAAI,IAAI;AAAE,YAAA,OAAO,IAAI,CAAC;QAE/B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AACzB,YAAA,MAAM,wBAAwB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;SAClD;QAED,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;KAChC;AAEO,IAAA,QAAQ,CAAC,GAAQ,EAAA;QACvB,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;KACtD;yHAzCU,SAAS,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;uHAAT,SAAS,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,KAAA,EAAA,CAAA,CAAA,EAAA;;sGAAT,SAAS,EAAA,UAAA,EAAA,CAAA;kBALrB,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACJ,oBAAA,IAAI,EAAE,OAAO;AACb,oBAAA,IAAI,EAAE,KAAK;AACX,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;;AC3CD;;;;AAIG;AAgCH;;AAEG;AACI,MAAM,YAAY,GAAG;IAC1B,SAAS;IACT,aAAa;IACb,aAAa;IACb,QAAQ;IACR,SAAS;IACT,WAAW;IACX,WAAW;IACX,aAAa;IACb,YAAY;IACZ,QAAQ;IACR,cAAc;IACd,cAAc;IACd,YAAY;CACb;;AChDD;AACA;AACA;;;;;;;AAOG;MAKU,YAAY,CAAA;yHAAZ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;0HAAZ,YAAY,EAAA,OAAA,EAAA,CAAAC,OAAA,EAAAC,iBAAA,EAAAC,OAAA,EAAAC,IAAA,EAAAC,gBAAA,EAAAC,OAAA,EAAAC,QAAA,EAAAC,YAAA,EAAAC,eAAA,EAAAC,QAAA,EAAAC,YAAA,EAAAC,SAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,QAAA,EAAAC,SAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,aAAA,EAAAC,YAAA,EAAAC,QAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,YAAA,CAAA,EAAA,OAAA,EAAA,CAAAvB,OAAA,EAAAC,iBAAA,EAAAC,OAAA,EAAAC,IAAA,EAAAC,gBAAA,EAAAC,OAAA,EAAAC,QAAA,EAAAC,YAAA,EAAAC,eAAA,EAAAC,QAAA,EAAAC,YAAA,EAAAC,SAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,QAAA,EAAAC,SAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,aAAA,EAAAC,YAAA,EAAAC,QAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,YAAA,CAAA,EAAA,CAAA,CAAA,EAAA;0HAAZ,YAAY,EAAA,CAAA,CAAA,EAAA;;sGAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBAJxB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,iBAAiB,EAAE,YAAY,CAAC;AAC1C,oBAAA,OAAO,EAAE,CAAC,iBAAiB,EAAE,YAAY,CAAC;AAC3C,iBAAA,CAAA;;;AClBM,MAAM,mBAAmB,GAAG,UAAU;AACtC,MAAM,kBAAkB,GAAG,SAAS;AACpC,MAAM,sBAAsB,GAAG,mBAAmB;AAClD,MAAM,qBAAqB,GAAG,kBAAkB;AAEvD;;;AAGG;AACG,SAAU,iBAAiB,CAAC,UAAkB,EAAA;IAClD,OAAO,UAAU,KAAK,mBAAmB,CAAC;AAC5C,CAAC;AAED;;;AAGG;AACG,SAAU,gBAAgB,CAAC,UAAkB,EAAA;IACjD,OAAO,UAAU,KAAK,kBAAkB,CAAC;AAC3C,CAAC;AAED;;;;;AAKG;AACG,SAAU,mBAAmB,CAAC,UAAkB,EAAA;IACpD,OAAO,UAAU,KAAK,sBAAsB,CAAC;AAC/C,CAAC;AAED;;;;;AAKG;AACG,SAAU,kBAAkB,CAAC,UAAkB,EAAA;IACnD,OAAO,UAAU,KAAK,qBAAqB,CAAC;AAC9C;;ACvCA;;;;AAIG;AAIH;;AAEG;MACU,OAAO,GAAG,IAAI,OAAO,CAAC,mBAAmB;;ACNtD;;;;AAIG;MACmB,gBAAgB,CAAA;;;;aAI7B,IAAK,CAAA,KAAA,GAA6B,kBAAkB,CAAC;AAC1D,QAAA,KAAK,EAAE,gBAAgB;AACvB,QAAA,UAAU,EAAE,MAAM;QAClB,OAAO,EAAE,MACP,iBAAiB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;cAClC,IAAI,uBAAuB,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC;cACrD,IAAI,oBAAoB,EAAE;AACjC,KAAA,CAAC,CAAC,EAAA;;AAoCL;;AAEG;MACU,uBAAuB,CAAA;IAGlC,WACU,CAAA,QAAkB,EAClB,MAAc,EAAA;QADd,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAClB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAQ;QAJhB,IAAM,CAAA,MAAA,GAA2B,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KAKlD;AAEJ;;;;;AAKG;AACH,IAAA,SAAS,CAAC,MAAmD,EAAA;AAC3D,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AACzB,YAAA,IAAI,CAAC,MAAM,GAAG,MAAM,MAAM,CAAC;SAC5B;aAAM;AACL,YAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;SACtB;KACF;AAED;;;AAGG;IACH,iBAAiB,GAAA;AACf,QAAA,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;KACnD;AAED;;;AAGG;AACH,IAAA,gBAAgB,CAAC,QAA0B,EAAA;AACzC,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;KAChD;AAED;;;;;;;;;;AAUG;AACH,IAAA,cAAc,CAAC,MAAc,EAAA;QAC3B,MAAM,UAAU,GAAG,sBAAsB,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAEjE,IAAI,UAAU,EAAE;AACd,YAAA,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;;;;;;;YAOjC,UAAU,CAAC,KAAK,EAAE,CAAC;SACpB;KACF;AAED;;AAEG;AACH,IAAA,2BAA2B,CAAC,iBAAoC,EAAA;QAC9D,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;KAC3D;AAED;;;;;AAKG;AACK,IAAA,eAAe,CAAC,EAAe,EAAA;AACrC,QAAA,MAAM,IAAI,GAAG,EAAE,CAAC,qBAAqB,EAAE,CAAC;QACxC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;QACjD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;AAC/C,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AAC7B,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;KACzD;AACF,CAAA;AAED,SAAS,sBAAsB,CAAC,QAAkB,EAAE,MAAc,EAAA;AAChE,IAAA,MAAM,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAEhG,IAAI,cAAc,EAAE;AAClB,QAAA,OAAO,cAAc,CAAC;KACvB;;;AAID,IAAA,IACE,OAAO,QAAQ,CAAC,gBAAgB,KAAK,UAAU;AAC/C,QAAA,QAAQ,CAAC,IAAI;QACb,OAAO,QAAQ,CAAC,IAAI,CAAC,YAAY,KAAK,UAAU,EAChD;AACA,QAAA,MAAM,UAAU,GAAG,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC,YAAY,CAAC,CAAC;AACrF,QAAA,IAAI,WAAW,GAAG,UAAU,CAAC,WAAiC,CAAC;QAE/D,OAAO,WAAW,EAAE;AAClB,YAAA,MAAM,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC;YAE1C,IAAI,UAAU,EAAE;;;AAGd,gBAAA,MAAM,MAAM,GACV,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,aAAa,CAAC,UAAU,MAAM,CAAA,EAAA,CAAI,CAAC,CAAC;gBACtF,IAAI,MAAM,EAAE;AACV,oBAAA,OAAO,MAAM,CAAC;iBACf;aACF;AAED,YAAA,WAAW,GAAG,UAAU,CAAC,QAAQ,EAAwB,CAAC;SAC3D;KACF;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;AAEG;MACU,oBAAoB,CAAA;AAC/B;;AAEG;IACH,SAAS,CAAC,MAAmD,EAAA,GAAU;AAEvE;;AAEG;IACH,iBAAiB,GAAA;AACf,QAAA,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KACf;AAED;;AAEG;IACH,gBAAgB,CAAC,QAA0B,EAAA,GAAU;AAErD;;AAEG;IACH,cAAc,CAAC,MAAc,EAAA,GAAU;AAEvC;;AAEG;IACH,2BAA2B,CAAC,iBAAoC,EAAA,GAAU;AAC3E;;ACrND;;;;AAIG;MACmB,UAAU,CAAA;AAE/B;;ACPD;;AAEG;AACI,MAAM,mBAAmB,GAAG,IAAI;;ACHvC;AACgB,SAAA,MAAM,CAAC,GAAW,EAAE,GAAW,EAAA;;IAE7C,OAAO,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC7E,CAAC;AAED;AACM,SAAU,aAAa,CAAC,GAAW,EAAA;AACvC,IAAA,OAAO,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAClC,CAAC;AAED;AACA;AACM,SAAU,eAAe,CAAC,GAAW,EAAA;AACzC,IAAA,OAAO,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,GAAG,GAAG,CAAC;AAC1D,CAAC;AAEK,SAAU,WAAW,CAAC,IAAa,EAAA;AACvC,IAAA,MAAM,QAAQ,GAAG,OAAO,IAAI,KAAK,QAAQ,CAAC;IAE1C,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;AACnC,QAAA,OAAO,KAAK,CAAC;KACd;;AAGD,IAAA,IAAI;AACF,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;AAC1B,QAAA,OAAO,IAAI,CAAC;KACb;AAAC,IAAA,MAAM;AACN,QAAA,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAEK,SAAU,aAAa,CAAC,IAAY,EAAA;IACxC,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AACvD,CAAC;AAEK,SAAU,YAAY,CAAC,GAAW,EAAA;AACtC,IAAA,OAAO,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AAClD;;ACCA;;;;;;AAMG;AACI,MAAM,eAAe,GAAG,CAAC,MAAyB,KAAK,MAAM,CAAC,GAAG,CAAC;AAUzE;;;;;;AAMG;AACU,MAAA,YAAY,GAAG,IAAI,cAAc,CAAc,SAAS,GAAG,aAAa,GAAG,EAAE,EAAE;AAC1F,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,MAAM,eAAe;AAC/B,CAAA,EAAE;AAEH;;;;;;;;AAQG;AACa,SAAA,iBAAiB,CAC/B,UAA+D,EAC/D,WAAsB,EAAA;IAEtB,OAAO,SAAS,kBAAkB,CAAC,IAAY,EAAA;AAC7C,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;AACtB,YAAA,qBAAqB,CAAC,IAAI,EAAE,WAAW,IAAI,EAAE,CAAC,CAAC;SAChD;;;AAID,QAAA,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;AAE3B,QAAA,MAAM,QAAQ,GAAG,CAAC,MAAyB,KAAI;AAC7C,YAAA,IAAI,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;;;;;;AAM7B,gBAAA,+BAA+B,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;aACnD;AAED,YAAA,OAAO,UAAU,CAAC,IAAI,EAAE,EAAC,GAAG,MAAM,EAAE,GAAG,EAAE,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,EAAC,CAAC,CAAC;AACtE,SAAC,CAAC;AAEF,QAAA,MAAM,SAAS,GAAe,CAAC,EAAC,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAC,CAAC,CAAC;AAC5E,QAAA,OAAO,SAAS,CAAC;AACnB,KAAC,CAAC;AACJ,CAAC;AAED,SAAS,qBAAqB,CAAC,IAAa,EAAE,WAAqB,EAAA;IACjE,MAAM,IAAI1B,aAAY,CAAA,IAAA,kDAEpB,SAAS;AACP,QAAA,CAAA,6CAAA,EAAgD,IAAI,CAAO,KAAA,CAAA;YACzD,CAAkE,+DAAA,EAAA,WAAW,CAAC,IAAI,CAChF,MAAM,CACP,CAAA,CAAE,CACR,CAAC;AACJ,CAAC;AAED,SAAS,+BAA+B,CAAC,IAAY,EAAE,GAAW,EAAA;IAChE,MAAM,IAAIA,aAAY,CAAA,IAAA,kDAEpB,SAAS;AACP,QAAA,CAAA,+EAAA,EAAkF,GAAG,CAAI,EAAA,CAAA;YACvF,CAA6D,2DAAA,CAAA;YAC7D,CAAiD,+CAAA,CAAA;YACjD,CAAoE,kEAAA,CAAA;YACpE,CAAiC,8BAAA,EAAA,IAAI,CAAM,IAAA,CAAA,CAChD,CAAC;AACJ;;AC/HA;;;;;;;;;;AAUG;MACU,uBAAuB,GAAG,iBAAiB,CACtD,mBAAmB,EACnB,SAAS,GAAG,CAAC,uDAAuD,CAAC,GAAG,SAAS,EACjF;AAEF,SAAS,mBAAmB,CAAC,IAAY,EAAE,MAAyB,EAAA;IAClE,IAAI,MAAM,GAAG,CAAA,WAAA,CAAa,CAAC;AAC3B,IAAA,IAAI,MAAM,CAAC,KAAK,EAAE;AAChB,QAAA,MAAM,IAAI,CAAU,OAAA,EAAA,MAAM,CAAC,KAAK,EAAE,CAAC;KACpC;;AAGD,IAAA,IAAI,MAAM,CAAC,aAAa,EAAE;AACxB,QAAA,MAAM,IAAI,CAAA,SAAA,EAAY,mBAAmB,CAAA,CAAE,CAAC;KAC7C;;;IAID,OAAO,CAAA,EAAG,IAAI,CAAkB,eAAA,EAAA,MAAM,IAAI,MAAM,CAAC,GAAG,CAAA,CAAE,CAAC;AACzD;;AC/BA;;AAEG;AACI,MAAM,oBAAoB,GAAoB;AACnD,IAAA,IAAI,EAAE,YAAY;AAClB,IAAA,OAAO,EAAE,eAAe;CACzB,CAAC;AAEF,MAAM,uBAAuB,GAAG,yCAAyC,CAAC;AAC1E;;AAEG;AACH,SAAS,eAAe,CAAC,GAAW,EAAA;AAClC,IAAA,OAAO,uBAAuB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3C,CAAC;AAED;;;;;;;;;;;AAWG;MACU,uBAAuB,GAAG,iBAAiB,CACtD,mBAAmB,EACnB,SAAS;AACP,MAAE;QACE,mCAAmC;QACnC,+BAA+B;QAC/B,8BAA8B;AAC/B,KAAA;MACD,SAAS,EACb;AAEF,SAAS,mBAAmB,CAAC,IAAY,EAAE,MAAyB,EAAA;;;;;;;AAQlE,IAAA,MAAM,OAAO,GAAG,MAAM,CAAC,aAAa,GAAG,YAAY,GAAG,QAAQ,CAAC;AAE/D,IAAA,IAAI,MAAM,GAAG,CAAU,OAAA,EAAA,OAAO,EAAE,CAAC;AACjC,IAAA,IAAI,MAAM,CAAC,KAAK,EAAE;AAChB,QAAA,MAAM,IAAI,CAAM,GAAA,EAAA,MAAM,CAAC,KAAK,EAAE,CAAC;KAChC;IAED,OAAO,CAAA,EAAG,IAAI,CAAiB,cAAA,EAAA,MAAM,IAAI,MAAM,CAAC,GAAG,CAAA,CAAE,CAAC;AACxD;;ACtDA;;AAEG;AACI,MAAM,kBAAkB,GAAoB;AACjD,IAAA,IAAI,EAAE,UAAU;AAChB,IAAA,OAAO,EAAE,aAAa;CACvB,CAAC;AAEF,MAAM,sBAAsB,GAAG,sCAAsC,CAAC;AACtE;;AAEG;AACH,SAAS,aAAa,CAAC,GAAW,EAAA;AAChC,IAAA,OAAO,sBAAsB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC1C,CAAC;AAED;;;;;;;;;;AAUG;MACU,qBAAqB,GAAG,iBAAiB,CACpD,iBAAiB,EACjB,SAAS,GAAG,CAAC,+BAA+B,EAAE,8BAA8B,CAAC,GAAG,SAAS,EACzF;AAEc,SAAA,iBAAiB,CAAC,IAAY,EAAE,MAAyB,EAAA;;;AAGvE,IAAA,MAAM,EAAC,GAAG,EAAE,KAAK,EAAC,GAAG,MAAM,CAAC;IAC5B,MAAM,MAAM,GAAa,EAAE,CAAC;IAE5B,IAAI,KAAK,EAAE;AACT,QAAA,MAAM,CAAC,IAAI,CAAC,KAAK,KAAK,CAAA,CAAE,CAAC,CAAC;KAC3B;;AAGD,IAAA,IAAI,MAAM,CAAC,aAAa,EAAE;AACxB,QAAA,MAAM,CAAC,IAAI,CAAC,KAAK,mBAAmB,CAAA,CAAE,CAAC,CAAC;KACzC;AAED,IAAA,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE,CAAA,GAAA,EAAM,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,CAAE,EAAE,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AACxF,IAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IAC3C,OAAO,GAAG,CAAC,IAAI,CAAC;AAClB;;AClDA;;AAEG;AACI,MAAM,eAAe,GAAoB;AAC9C,IAAA,IAAI,EAAE,OAAO;AACb,IAAA,OAAO,EAAE,UAAU;CACpB,CAAC;AAEF,MAAM,kBAAkB,GAAG,oCAAoC,CAAC;AAChE;;AAEG;AACH,SAAS,UAAU,CAAC,GAAW,EAAA;AAC7B,IAAA,OAAO,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACtC,CAAC;AAED;;;;;;;;AAQG;MACU,kBAAkB,GAAG,iBAAiB,CACjD,cAAc,EACd,SAAS,GAAG,CAAC,6BAA6B,CAAC,GAAG,SAAS,EACvD;AAEF,SAAS,cAAc,CAAC,IAAY,EAAE,MAAyB,EAAA;AAC7D,IAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,CAAA,EAAG,IAAI,CAAA,CAAA,EAAI,MAAM,CAAC,GAAG,CAAA,CAAE,CAAC,CAAC;;IAE7C,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AACvC,IAAA,IAAI,MAAM,CAAC,KAAK,EAAE;AAChB,QAAA,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;KACpD;;AAGD,IAAA,IAAI,MAAM,CAAC,aAAa,EAAE;QACxB,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC;KAChD;IACD,OAAO,GAAG,CAAC,IAAI,CAAC;AAClB;;AClCA;;AAEG;AACI,MAAM,iBAAiB,GAAoB;AAChD,IAAA,IAAI,EAAE,SAAS;AACf,IAAA,OAAO,EAAE,YAAY;CACtB,CAAC;AAEF,MAAM,oBAAoB,GAAG,sCAAsC,CAAC;AAEpE;;;;AAIG;AACH,SAAS,YAAY,CAAC,GAAW,EAAA;AAC/B,IAAA,OAAO,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACxC,CAAC;AAED;;;;;;;AAOG;AACG,SAAU,oBAAoB,CAAC,IAAa,EAAA;IAChD,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;QAC9B,MAAM,IAAIA,aAAY,CAAA,IAAA,kDAEpB,SAAS;AACP,YAAA,CAAA,6CAAA,EAAgD,IAAI,CAAO,KAAA,CAAA;AACzD,gBAAA,CAAA,uGAAA,CAAyG,CAC9G,CAAC;KACH;IAED,IAAI,IAAI,EAAE;AACR,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;AAC1B,QAAA,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC;KACnB;AAED,IAAA,MAAM,QAAQ,GAAG,CAAC,MAAyB,KAAI;AAC7C,QAAA,OAAO,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AACxC,KAAC,CAAC;AAEF,IAAA,MAAM,SAAS,GAAe,CAAC,EAAC,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAC,CAAC,CAAC;AAC5E,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,MAAM,WAAW,GAAG,IAAI,GAAG,CAAiB;IAC1C,CAAC,QAAQ,EAAE,GAAG,CAAC;IACf,CAAC,KAAK,EAAE,KAAK,CAAC;IACd,CAAC,SAAS,EAAE,GAAG,CAAC;IAChB,CAAC,GAAG,EAAE,GAAG,CAAC;IACV,CAAC,UAAU,EAAE,UAAU,CAAC;AACzB,CAAA,CAAC,CAAC;AAEH,SAAS,gBAAgB,CAAC,MAAyB,EAAE,IAAa,EAAA;;IAEhE,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,IAAI,YAAY,CAAC,CAAC;AAC1C,IAAA,GAAG,CAAC,QAAQ,GAAG,kBAAkB,CAAC;AAElC,IAAA,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;QAC7D,MAAM,CAAC,GAAG,GAAG,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC;KAC/B;IAED,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;AAExC,IAAA,IAAI,MAAM,CAAC,KAAK,EAAE;AAChB,QAAA,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;KACpD;;;AAID,IAAA,MAAM,aAAa,GAAG,MAAM,CAAC,YAAY,GAAG,SAAS,CAAC,IAAI,MAAM,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC;AACrF,IAAA,IAAI,MAAM,CAAC,aAAa,IAAI,CAAC,aAAa,EAAE;QAC1C,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC;KAChD;AAED,IAAA,KAAK,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,IAAI,EAAE,CAAC,EAAE;AACtE,QAAA,IAAI,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AAC1B,YAAA,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAE,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;SACjE;aAAM;YACL,IAAI,SAAS,EAAE;gBACb,OAAO,CAAC,IAAI,CACVC,mBAAkB,CAAA,IAAA,kDAEhB,4FAA4F,KAAK,CAAA,IAAA,CAAM,CACxG,CACF,CAAC;aACH;SACF;KACF;;IAED,OAAO,GAAG,CAAC,QAAQ,KAAK,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC;AAC5E;;AC5GA;SACgB,mBAAmB,CAAC,KAAa,EAAE,YAAY,GAAG,IAAI,EAAA;IACpE,MAAM,SAAS,GAAG,YAAY;UAC1B,CAAoD,iDAAA,EAAA,KAAK,CAAO,KAAA,CAAA;UAChE,EAAE,CAAC;IACP,OAAO,CAAA,+BAAA,EAAkC,SAAS,CAAA,iBAAA,CAAmB,CAAC;AACxE;;ACFA;;;;AAIG;AACG,SAAU,aAAa,CAAC,SAAiB,EAAA;IAC7C,IAAI,CAAC,SAAS,EAAE;AACd,QAAA,MAAM,IAAID,aAAY,CAEpB,IAAA,gEAAA,CAAA,6BAAA,EAAgC,SAAS,CAAqB,mBAAA,CAAA;AAC5D,YAAA,CAAA,qEAAA,CAAuE,CAC1E,CAAC;KACH;AACH;;ACIA;;;;;;;;;AASG;MAEU,gBAAgB,CAAA;AAO3B,IAAA,WAAA,GAAA;;AALQ,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,GAAG,EAA8B,CAAC;QAE/C,IAAM,CAAA,MAAA,GAAkB,IAAI,CAAC;QAC7B,IAAQ,CAAA,QAAA,GAA+B,IAAI,CAAC;QAGlD,aAAa,CAAC,aAAa,CAAC,CAAC;QAC7B,MAAM,GAAG,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC;QACzC,IAAI,OAAO,GAAG,KAAK,WAAW,IAAI,OAAO,mBAAmB,KAAK,WAAW,EAAE;AAC5E,YAAA,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;AAClB,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;SAChD;KACF;AAED;;;AAGG;IACK,uBAAuB,GAAA;QAC7B,MAAM,QAAQ,GAAG,IAAI,mBAAmB,CAAC,CAAC,SAAS,KAAI;AACrD,YAAA,MAAM,OAAO,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;AACvC,YAAA,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;gBAAE,OAAO;;;;;YAKjC,MAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;;;YAI/C,MAAM,MAAM,GAAI,UAAkB,CAAC,OAAO,EAAE,GAAG,IAAI,EAAE,CAAC;;AAGtD,YAAA,IAAI,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC;gBAAE,OAAO;YAErE,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AACpC,YAAA,IAAI,CAAC,GAAG;gBAAE,OAAO;YACjB,IAAI,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,GAAG,CAAC,qBAAqB,EAAE;AAC/C,gBAAA,GAAG,CAAC,qBAAqB,GAAG,IAAI,CAAC;gBACjC,uBAAuB,CAAC,MAAM,CAAC,CAAC;aACjC;YACD,IAAI,GAAG,CAAC,QAAQ,IAAI,CAAC,GAAG,CAAC,qBAAqB,EAAE;AAC9C,gBAAA,GAAG,CAAC,qBAAqB,GAAG,IAAI,CAAC;gBACjC,kBAAkB,CAAC,MAAM,CAAC,CAAC;aAC5B;AACH,SAAC,CAAC,CAAC;AACH,QAAA,QAAQ,CAAC,OAAO,CAAC,EAAC,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;AACrE,QAAA,OAAO,QAAQ,CAAC;KACjB;AAED,IAAA,aAAa,CAAC,YAAoB,EAAE,aAAqB,EAAE,UAAmB,EAAA;QAC5E,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO;AAC3B,QAAA,MAAM,qBAAqB,GAAuB;AAChD,YAAA,QAAQ,EAAE,UAAU;AACpB,YAAA,QAAQ,EAAE,KAAK;AACf,YAAA,qBAAqB,EAAE,KAAK;AAC5B,YAAA,qBAAqB,EAAE,KAAK;SAC7B,CAAC;AACF,QAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,MAAO,CAAC,CAAC,IAAI,EAAE,qBAAqB,CAAC,CAAC;KACjF;AAED,IAAA,eAAe,CAAC,YAAoB,EAAA;QAClC,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO;AAC3B,QAAA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,MAAO,CAAC,CAAC,IAAI,CAAC,CAAC;KAC7D;IAED,WAAW,CAAC,WAAmB,EAAE,MAAc,EAAA;AAC7C,QAAA,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,MAAO,CAAC,CAAC,IAAI,CAAC;QAC3D,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACzC,IAAI,GAAG,EAAE;AACP,YAAA,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC;AACpB,YAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,MAAO,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AACxD,YAAA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;SACjC;KACF;IAED,WAAW,GAAA;QACT,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO;AAC3B,QAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;AAC3B,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;KACrB;yHAlFU,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAAhB,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,cADJ,MAAM,EAAA,CAAA,CAAA,EAAA;;sGAClB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAD5B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;AAsFhC,SAAS,uBAAuB,CAAC,KAAa,EAAA;AAC5C,IAAA,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC;AACpD,IAAA,OAAO,CAAC,KAAK,CACXC,mBAAkB,CAEhB,IAAA,kDAAA,CAAA,EAAG,gBAAgB,CAAoD,kDAAA,CAAA;QACrE,CAAqE,mEAAA,CAAA;QACrE,CAAiD,+CAAA,CAAA;QACjD,CAA4C,0CAAA,CAAA,CAC/C,CACF,CAAC;AACJ,CAAC;AAED,SAAS,kBAAkB,CAAC,KAAa,EAAA;AACvC,IAAA,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC;AACpD,IAAA,OAAO,CAAC,IAAI,CACVA,mBAAkB,CAEhB,IAAA,gDAAA,CAAA,EAAG,gBAAgB,CAAoD,kDAAA,CAAA;QACrE,CAAqE,mEAAA,CAAA;QACrE,CAA0E,wEAAA,CAAA;QAC1E,CAAuD,qDAAA,CAAA,CAC1D,CACF,CAAC;AACJ;;AC9HA;AACA,MAAM,mCAAmC,GAAG,IAAI,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC,CAAC;AAE3F;;;;;;;;;;;;;;;;;AAiBG;AACU,MAAA,0BAA0B,GAAG,IAAI,cAAc,CAC1D,SAAS,GAAG,4BAA4B,GAAG,EAAE,EAC7C;AAEF;;;;;;AAMG;MAEU,qBAAqB,CAAA;AAkBhC,IAAA,WAAA,GAAA;AAjBQ,QAAA,IAAA,CAAA,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AAEpC;;;AAGG;QACK,IAAe,CAAA,eAAA,GAAuB,IAAI,CAAC;AAEnD;;AAEG;AACK,QAAA,IAAA,CAAA,WAAW,GAAG,IAAI,GAAG,EAAU,CAAC;QAEhC,IAAM,CAAA,MAAA,GAAkB,IAAI,CAAC;AAE7B,QAAA,IAAA,CAAA,SAAS,GAAG,IAAI,GAAG,CAAS,mCAAmC,CAAC,CAAC;QAGvE,aAAa,CAAC,yBAAyB,CAAC,CAAC;AACzC,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;AACtC,QAAA,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;AAC9B,YAAA,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;SACnB;AACD,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,0BAA0B,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;QACvE,IAAI,SAAS,EAAE;AACb,YAAA,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;SACnC;KACF;AAEO,IAAA,iBAAiB,CAAC,OAA0C,EAAA;AAClE,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AAC1B,YAAA,WAAW,CAAC,OAAO,EAAE,CAAC,MAAM,KAAI;gBAC9B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC;AAC9C,aAAC,CAAC,CAAC;SACJ;aAAM;YACL,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC;SAC9C;KACF;AAED;;;;;;AAMG;IACH,gBAAgB,CAAC,YAAoB,EAAE,aAAqB,EAAA;QAC1D,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE,OAAO;QAEzB,MAAM,MAAM,GAAG,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACjD,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC;YAAE,OAAO;;QAGvF,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;;;;;AAMpC,QAAA,IAAI,CAAC,eAAe,KAAK,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAErD,QAAA,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAC5C,OAAO,CAAC,IAAI,CACVA,mBAAkB,CAAA,IAAA,6DAEhB,GAAG,mBAAmB,CAAC,aAAa,CAAC,CAA+C,6CAAA,CAAA;gBAClF,CAAsF,oFAAA,CAAA;gBACtF,CAAkF,gFAAA,CAAA;gBAClF,CAA4C,0CAAA,CAAA;AAC5C,gBAAA,CAAA,+BAAA,EAAkC,MAAM,CAAC,MAAM,CAAI,EAAA,CAAA,CACtD,CACF,CAAC;SACH;KACF;IAEO,oBAAoB,GAAA;AAC1B,QAAA,MAAM,cAAc,GAAG,IAAI,GAAG,EAAU,CAAC;QACzC,MAAM,QAAQ,GAAG,sBAAsB,CAAC;AACxC,QAAA,MAAM,KAAK,GAAsB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC;AACtF,QAAA,KAAK,IAAI,IAAI,IAAI,KAAK,EAAE;AACtB,YAAA,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAO,CAAC,CAAC;AAC5C,YAAA,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;SAChC;AACD,QAAA,OAAO,cAAc,CAAC;KACvB;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,eAAe,EAAE,KAAK,EAAE,CAAC;AAC9B,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;KAC1B;yHA1FU,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAArB,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,cADT,MAAM,EAAA,CAAA,CAAA,EAAA;;sGAClB,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBADjC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;AA8FhC;;;AAGG;AACH,SAAS,WAAW,CAAI,KAAoB,EAAE,EAAsB,EAAA;AAClE,IAAA,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE;QACvB,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;KAC3D;AACH;;ACnJA;;;;;AAKG;AACI,MAAM,8BAA8B,GAAG,CAAC,CAAC;AAEhD;;;;;;AAMG;AACI,MAAM,gBAAgB,GAAG,IAAI,cAAc,CAAc,+BAA+B,EAAE;AAC/F,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,MAAM,IAAI,GAAG,EAAU;AACjC,CAAA,CAAC;;ACbF;;;;;;;AAOG;MAEU,kBAAkB,CAAA;AAD/B,IAAA,WAAA,GAAA;AAEmB,QAAA,IAAA,CAAA,eAAe,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC;AAC3C,QAAA,IAAA,CAAA,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AAsD9C,KAAA;AApDC;;;;;;;;;;;;;;;AAeG;AACH,IAAA,oBAAoB,CAAC,QAAmB,EAAE,GAAW,EAAE,MAAe,EAAE,KAAc,EAAA;QACpF,IAAI,SAAS,EAAE;YACb,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,IAAI,8BAA8B,EAAE;gBAC/D,MAAM,IAAID,aAAY,CAAA,IAAA,mDAEpB,SAAS;oBACP,CAAiE,+DAAA,CAAA;AAC/D,wBAAA,CAAA,EAAG,8BAA8B,CAAmC,iCAAA,CAAA;wBACpE,CAAmE,iEAAA,CAAA;AACnE,wBAAA,CAAA,4EAAA,CAA8E,CACnF,CAAC;aACH;SACF;QAED,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YACjC,OAAO;SACR;AAED,QAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAE9B,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAC/C,QAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAC9C,QAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;QAC5C,QAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;QACjD,QAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,eAAe,EAAE,MAAM,CAAC,CAAC;QAExD,IAAI,KAAK,EAAE;YACT,QAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;SACrD;QAED,IAAI,MAAM,EAAE;YACV,QAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC;SACvD;QAED,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KACnD;yHAvDU,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAAlB,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,cADN,MAAM,EAAA,CAAA,CAAA,EAAA;;sGAClB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAD9B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;;AC6BhC;;;;;;AAMG;AACH,MAAM,8BAA8B,GAAG,EAAE,CAAC;AAE1C;;;AAGG;AACH,MAAM,6BAA6B,GAAG,2BAA2B,CAAC;AAElE;;;AAGG;AACH,MAAM,+BAA+B,GAAG,mCAAmC,CAAC;AAE5E;;;;AAIG;AACI,MAAM,2BAA2B,GAAG,CAAC,CAAC;AAE7C;;;AAGG;AACI,MAAM,8BAA8B,GAAG,CAAC,CAAC;AAEhD;;AAEG;AACH,MAAM,0BAA0B,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAE1C;;AAEG;AACH,MAAM,0BAA0B,GAAG,GAAG,CAAC;AACvC;;AAEG;AACH,MAAM,sBAAsB,GAAG,GAAG,CAAC;AAEnC;;;;AAIG;AACH,MAAM,yBAAyB,GAAG,IAAI,CAAC;AAEvC;;;AAGG;AACH,MAAM,wBAAwB,GAAG,IAAI,CAAC;AACtC,MAAM,yBAAyB,GAAG,IAAI,CAAC;AAEvC;;AAEG;AACI,MAAM,uBAAuB,GAAG,EAAE,CAAC;AAE1C;;;;;;;;AAQG;AACI,MAAM,mBAAmB,GAAG,IAAI,CAAC;AACjC,MAAM,oBAAoB,GAAG,KAAK,CAAC;AAE1C;AACO,MAAM,gBAAgB,GAAG;IAC9B,eAAe;IACf,kBAAkB;IAClB,oBAAoB;IACpB,iBAAiB;CAClB,CAAC;AAYF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiGG;MAgBU,gBAAgB,CAAA;AAf7B,IAAA,WAAA,GAAA;AAgBU,QAAA,IAAA,CAAA,WAAW,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC;QACnC,IAAM,CAAA,MAAA,GAAgB,aAAa,CAAC,MAAM,CAAC2B,aAAY,CAAC,CAAC,CAAC;AAC1D,QAAA,IAAA,CAAA,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;AAC7B,QAAA,IAAA,CAAA,UAAU,GAAqB,MAAM,CAAC,UAAU,CAAC,CAAC,aAAa,CAAC;AAChE,QAAA,IAAA,CAAA,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;QACnB,IAAQ,CAAA,QAAA,GAAG,gBAAgB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;AACjD,QAAA,IAAA,CAAA,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,CAAC,CAAC;;AAGzD,QAAA,IAAA,CAAA,WAAW,GAAG,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC;AAE7E;;;;;AAKG;QACK,IAAY,CAAA,YAAA,GAAkB,IAAI,CAAC;AAkD3C;;AAEG;QACmC,IAAQ,CAAA,QAAA,GAAG,KAAK,CAAC;AAOvD;;AAEG;QACmC,IAAsB,CAAA,sBAAA,GAAG,KAAK,CAAC;AAErE;;;AAGG;QACmC,IAAI,CAAA,IAAA,GAAG,KAAK,CAAC;AAsUpD,KAAA;;IAxSC,QAAQ,GAAA;QACNC,uBAAsB,CAAC,kBAAkB,CAAC,CAAC;QAE3C,IAAI,SAAS,EAAE;YACb,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YACzC,mBAAmB,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AAC/C,YAAA,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YACzC,sBAAsB,CAAC,IAAI,CAAC,CAAC;AAC7B,YAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,yBAAyB,CAAC,IAAI,CAAC,CAAC;aACjC;YACD,oBAAoB,CAAC,IAAI,CAAC,CAAC;YAC3B,gBAAgB,CAAC,IAAI,CAAC,CAAC;AACvB,YAAA,IAAI,IAAI,CAAC,IAAI,EAAE;gBACb,yBAAyB,CAAC,IAAI,CAAC,CAAC;;;AAGhC,gBAAA,MAAM,CAAC,iBAAiB,CAAC,MACvB,2BAA2B,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,CAClE,CAAC;aACH;iBAAM;gBACL,4BAA4B,CAAC,IAAI,CAAC,CAAC;AACnC,gBAAA,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;oBAC7B,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;iBACpD;AACD,gBAAA,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;oBAC5B,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;iBAClD;;;AAGD,gBAAA,MAAM,CAAC,iBAAiB,CAAC,MACvB,uBAAuB,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,CAC9D,CAAC;aACH;YACD,uBAAuB,CAAC,IAAI,CAAC,CAAC;AAC9B,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAClB,oBAAoB,CAAC,IAAI,CAAC,CAAC;aAC5B;AACD,YAAA,sBAAsB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YAC/C,6BAA6B,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AAC5D,YAAA,6BAA6B,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AACtD,YAAA,iCAAiC,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AAE1D,YAAA,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,EAAE;gBAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AACzC,gBAAA,MAAM,CAAC,iBAAiB,CAAC,MAAK;AAC5B,oBAAA,IAAI,CAAC,WAAY,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AACrF,iBAAC,CAAC,CAAC;aACJ;AAED,YAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;AACzD,gBAAA,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;aAC9D;SACF;AACD,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SAC/C;QACD,IAAI,CAAC,iBAAiB,EAAE,CAAC;KAC1B;IAEO,iBAAiB,GAAA;;;AAGvB,QAAA,IAAI,IAAI,CAAC,IAAI,EAAE;AACb,YAAA,IAAI,CAAC,KAAK,KAAK,OAAO,CAAC;SACxB;aAAM;AACL,YAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,KAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;AACvD,YAAA,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;SAC1D;QAED,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAC5D,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;;;AAIhE,QAAA,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;;;AAIxC,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAElD,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;SAC5C;QACD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE;YAClC,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,CAC1C,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,eAAe,EAAE,EACtB,eAAe,EACf,IAAI,CAAC,KAAK,CACX,CAAC;SACH;KACF;;AAGD,IAAA,WAAW,CAAC,OAAsB,EAAA;QAChC,IAAI,SAAS,EAAE;AACb,YAAA,2BAA2B,CAAC,IAAI,EAAE,OAAO,EAAE;gBACzC,UAAU;gBACV,OAAO;gBACP,QAAQ;gBACR,UAAU;gBACV,MAAM;gBACN,SAAS;gBACT,OAAO;gBACP,cAAc;gBACd,wBAAwB;AACzB,aAAA,CAAC,CAAC;SACJ;AACD,QAAA,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,EAAE;AACzD,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;AACjC,YAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;AAC9B,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;AACjC,YAAA,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,KAAK,MAAM,EAAE;gBACtE,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AACzC,gBAAA,MAAM,CAAC,iBAAiB,CAAC,MAAK;oBAC5B,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAChD,iBAAC,CAAC,CAAC;aACJ;SACF;KACF;AAEO,IAAA,eAAe,CACrB,yBAAkE,EAAA;QAElE,IAAI,eAAe,GAAsB,yBAAyB,CAAC;AACnE,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,eAAe,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;SAClD;AACD,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;KAC1C;IAEO,kBAAkB,GAAA;QACxB,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,EAAE;YAChD,OAAO,IAAI,CAAC,OAAO,CAAC;SACrB;QACD,OAAO,IAAI,CAAC,QAAQ,GAAG,OAAO,GAAG,MAAM,CAAC;KACzC;IAEO,gBAAgB,GAAA;QACtB,OAAO,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,MAAM,CAAC;KACxC;IAEO,eAAe,GAAA;;;;AAIrB,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,MAAM,SAAS,GAAG,EAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAC,CAAC;;YAEpC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;SACrD;QACD,OAAO,IAAI,CAAC,YAAY,CAAC;KAC1B;IAEO,kBAAkB,GAAA;QACxB,MAAM,WAAW,GAAG,6BAA6B,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACtE,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ;aAC5B,KAAK,CAAC,GAAG,CAAC;aACV,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,EAAE,CAAC;AAC3B,aAAA,GAAG,CAAC,CAAC,MAAM,KAAI;AACd,YAAA,MAAM,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;YACvB,MAAM,KAAK,GAAG,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAM,CAAC;AAClF,YAAA,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,EAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,EAAC,CAAC,CAAI,CAAA,EAAA,MAAM,EAAE,CAAC;AACvE,SAAC,CAAC,CAAC;AACL,QAAA,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC7B;IAEO,kBAAkB,GAAA;AACxB,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,YAAA,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC;SACnC;aAAM;AACL,YAAA,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC;SAC9B;KACF;IAEO,mBAAmB,GAAA;AACzB,QAAA,MAAM,EAAC,WAAW,EAAC,GAAG,IAAI,CAAC,MAAM,CAAC;QAElC,IAAI,mBAAmB,GAAG,WAAY,CAAC;QACvC,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,OAAO,EAAE;;;AAGlC,YAAA,mBAAmB,GAAG,WAAY,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,0BAA0B,CAAC,CAAC;SACrF;AAED,QAAA,MAAM,SAAS,GAAG,mBAAmB,CAAC,GAAG,CACvC,CAAC,EAAE,KAAK,CAAG,EAAA,IAAI,CAAC,eAAe,CAAC,EAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,EAAC,CAAC,CAAA,CAAA,EAAI,EAAE,CAAA,CAAA,CAAG,CACvE,CAAC;AACF,QAAA,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC7B;IAEO,kBAAkB,CAAC,cAAc,GAAG,KAAK,EAAA;QAC/C,IAAI,cAAc,EAAE;;;AAGlB,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;SAC1B;AAED,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;AAC5C,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;QAE3C,IAAI,eAAe,GAAuB,SAAS,CAAC;AACpD,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;SAC7C;AAAM,aAAA,IAAI,IAAI,CAAC,6BAA6B,EAAE,EAAE;AAC/C,YAAA,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;SAC7C;QAED,IAAI,eAAe,EAAE;AACnB,YAAA,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;SAClD;AACD,QAAA,OAAO,eAAe,CAAC;KACxB;IAEO,cAAc,GAAA;AACpB,QAAA,MAAM,SAAS,GAAG,0BAA0B,CAAC,GAAG,CAC9C,CAAC,UAAU,KACT,CAAA,EAAG,IAAI,CAAC,eAAe,CAAC;YACtB,GAAG,EAAE,IAAI,CAAC,KAAK;AACf,YAAA,KAAK,EAAE,IAAI,CAAC,KAAM,GAAG,UAAU;AAChC,SAAA,CAAC,CAAI,CAAA,EAAA,UAAU,CAAG,CAAA,CAAA,CACtB,CAAC;AACF,QAAA,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC7B;IAEO,6BAA6B,GAAA;QACnC,IAAI,cAAc,GAAG,KAAK,CAAC;AAC3B,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACf,cAAc;gBACZ,IAAI,CAAC,KAAM,GAAG,wBAAwB,IAAI,IAAI,CAAC,MAAO,GAAG,yBAAyB,CAAC;SACtF;AACD,QAAA,QACE,CAAC,IAAI,CAAC,sBAAsB;YAC5B,CAAC,IAAI,CAAC,MAAM;YACZ,IAAI,CAAC,WAAW,KAAK,eAAe;YACpC,CAAC,cAAc,EACf;KACH;AAED;;;;AAIG;AACK,IAAA,mBAAmB,CAAC,gBAAkC,EAAA;AAC5D,QAAA,MAAM,EAAC,qBAAqB,EAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AAC5C,QAAA,IAAI,gBAAgB,KAAK,IAAI,EAAE;AAC7B,YAAA,OAAO,CAAO,IAAA,EAAA,IAAI,CAAC,eAAe,CAAC;gBACjC,GAAG,EAAE,IAAI,CAAC,KAAK;AACf,gBAAA,KAAK,EAAE,qBAAqB;AAC5B,gBAAA,aAAa,EAAE,IAAI;AACpB,aAAA,CAAC,GAAG,CAAC;SACP;AAAM,aAAA,IAAI,OAAO,gBAAgB,KAAK,QAAQ,IAAI,gBAAgB,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;YACvF,OAAO,CAAA,IAAA,EAAO,gBAAgB,CAAA,CAAA,CAAG,CAAC;SACnC;AACD,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;AAGG;AACK,IAAA,qBAAqB,CAAC,iBAA0C,EAAA;QACtE,IAAI,CAAC,iBAAiB,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE;AACnE,YAAA,OAAO,IAAI,CAAC;SACb;AACD,QAAA,OAAO,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;KACxC;AAEO,IAAA,uBAAuB,CAAC,GAAqB,EAAA;QACnD,MAAM,QAAQ,GAAG,MAAK;YACpB,MAAM,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;AAC/D,YAAA,oBAAoB,EAAE,CAAC;AACvB,YAAA,qBAAqB,EAAE,CAAC;AACxB,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YACzB,iBAAiB,CAAC,YAAY,EAAE,CAAC;AACnC,SAAC,CAAC;AAEF,QAAA,MAAM,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;AACzE,QAAA,MAAM,qBAAqB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;KAC5E;;IAGD,WAAW,GAAA;QACT,IAAI,SAAS,EAAE;AACb,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,EAAE;gBAC7E,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;aACrD;SACF;KACF;IAEO,gBAAgB,CAAC,IAAY,EAAE,KAAa,EAAA;AAClD,QAAA,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;KAC1D;yHA5ZU,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAhB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,IAAA,EAAA,gBAAgB,EA+9BpB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAAA,aAAa,CA76BD,EAAA,QAAA,EAAA,UAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAAA,eAAe,CAMf,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAAA,eAAe,CAef,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAUhB,EAAA,YAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,CAAA,wBAAA,EAAA,wBAAA,EAAA,gBAAgB,CAMhB,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,gBAAgB,+CAi5BrB,yBAAyB,CAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,GAAA,EAAA,KAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,gBAAA,EAAA,4BAAA,EAAA,aAAA,EAAA,wBAAA,EAAA,cAAA,EAAA,wBAAA,EAAA,aAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,gCAAA,EAAA,2BAAA,EAAA,kCAAA,EAAA,yBAAA,EAAA,oCAAA,EAAA,wBAAA,EAAA,uDAAA,EAAA,cAAA,EAAA,iFAAA,EAAA,EAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;sGAx+B5B,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAf5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,UAAU,EAAE,IAAI;AAChB,oBAAA,QAAQ,EAAE,YAAY;AACtB,oBAAA,IAAI,EAAE;AACJ,wBAAA,kBAAkB,EAAE,0BAA0B;AAC9C,wBAAA,eAAe,EAAE,sBAAsB;AACvC,wBAAA,gBAAgB,EAAE,sBAAsB;AACxC,wBAAA,eAAe,EAAE,mBAAmB;AACpC,wBAAA,yBAAyB,EAAE,8BAA8B;AACzD,wBAAA,6BAA6B,EAAE,gCAAgC;AAC/D,wBAAA,2BAA2B,EAAE,kCAAkC;AAC/D,wBAAA,0BAA0B,EAAE,uDAAuD;wBACnF,gBAAgB,EAAE,CAAmE,gEAAA,EAAA,uBAAuB,CAAa,WAAA,CAAA;AAC1H,qBAAA;AACF,iBAAA,CAAA;8BA0BoD,KAAK,EAAA,CAAA;sBAAvD,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,aAAa,EAAC,CAAA;gBAaxC,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAMG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAM+B,KAAK,EAAA,CAAA;sBAAzC,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAA;gBAME,MAAM,EAAA,CAAA;sBAA1C,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAA;gBAU1B,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKgC,QAAQ,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAK3B,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKgC,sBAAsB,EAAA,CAAA;sBAA3D,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAME,IAAI,EAAA,CAAA;sBAAzC,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAKW,WAAW,EAAA,CAAA;sBAAzD,KAAK;uBAAC,EAAC,SAAS,EAAE,yBAAyB,EAAC,CAAA;gBAMpC,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAQG,GAAG,EAAA,CAAA;sBAAX,KAAK;gBAQG,MAAM,EAAA,CAAA;sBAAd,KAAK;;AA6SR;AAEA;;AAEG;AACH,SAAS,aAAa,CAAC,MAAmB,EAAA;IACxC,IAAI,iBAAiB,GAA6B,EAAE,CAAC;AACrD,IAAA,IAAI,MAAM,CAAC,WAAW,EAAE;QACtB,iBAAiB,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;KAC1E;AACD,IAAA,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAEC,sBAAqB,EAAE,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7E,CAAC;AAED;AAEA;;AAEG;AACH,SAAS,sBAAsB,CAAC,GAAqB,EAAA;AACnD,IAAA,IAAI,GAAG,CAAC,GAAG,EAAE;QACX,MAAM,IAAI7B,aAAY,CAAA,IAAA,6CAEpB,CAAG,EAAA,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAA6C,2CAAA,CAAA;YAC5E,CAA0D,wDAAA,CAAA;YAC1D,CAAsF,oFAAA,CAAA;AACtF,YAAA,CAAA,iDAAA,CAAmD,CACtD,CAAC;KACH;AACH,CAAC;AAED;;AAEG;AACH,SAAS,yBAAyB,CAAC,GAAqB,EAAA;AACtD,IAAA,IAAI,GAAG,CAAC,MAAM,EAAE;QACd,MAAM,IAAIA,aAAY,CAAA,IAAA,gDAEpB,CAAG,EAAA,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAmD,iDAAA,CAAA;YAClF,CAA0D,wDAAA,CAAA;YAC1D,CAA8E,4EAAA,CAAA;AAC9E,YAAA,CAAA,kEAAA,CAAoE,CACvE,CAAC;KACH;AACH,CAAC;AAED;;AAEG;AACH,SAAS,oBAAoB,CAAC,GAAqB,EAAA;IACjD,IAAI,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;AAC7B,IAAA,IAAI,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;AAC7B,QAAA,IAAI,KAAK,CAAC,MAAM,GAAG,8BAA8B,EAAE;YACjD,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,8BAA8B,CAAC,GAAG,KAAK,CAAC;SACpE;AACD,QAAA,MAAM,IAAIA,aAAY,CAEpB,IAAA,uCAAA,CAAA,EAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAwC,sCAAA,CAAA;AAC9E,YAAA,CAAA,CAAA,EAAI,KAAK,CAA+D,6DAAA,CAAA;YACxE,CAAuE,qEAAA,CAAA;AACvE,YAAA,CAAA,qEAAA,CAAuE,CAC1E,CAAC;KACH;AACH,CAAC;AAED;;AAEG;AACH,SAAS,oBAAoB,CAAC,GAAqB,EAAA;AACjD,IAAA,IAAI,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;AACtB,IAAA,IAAI,KAAK,EAAE,KAAK,CAAC,mBAAmB,CAAC,EAAE;AACrC,QAAA,MAAM,IAAIA,aAAY,CAEpB,IAAA,uCAAA,CAAA,EAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAA2C,yCAAA,CAAA;YACjF,CAA4F,0FAAA,CAAA;YAC5F,CAAkF,gFAAA,CAAA;AAClF,YAAA,CAAA,6FAAA,CAA+F,CAClG,CAAC;KACH;AACH,CAAC;AAED,SAAS,sBAAsB,CAAC,GAAqB,EAAE,WAAwB,EAAA;IAC7E,2CAA2C,CAAC,GAAG,CAAC,CAAC;AACjD,IAAA,wCAAwC,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;IAC3D,wBAAwB,CAAC,GAAG,CAAC,CAAC;AAChC,CAAC;AAED;;AAEG;AACH,SAAS,2CAA2C,CAAC,GAAqB,EAAA;IACxE,IAAI,GAAG,CAAC,iBAAiB,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE;AAC7C,QAAA,MAAM,IAAIA,aAAY,CAEpB,IAAA,uCAAA,CAAA,EAAG,mBAAmB,CACpB,GAAG,CAAC,KAAK,EACT,KAAK,CACN,CAAsD,oDAAA,CAAA;AACrD,YAAA,CAAA,+EAAA,CAAiF,CACpF,CAAC;KACH;AACH,CAAC;AAED;;;AAGG;AACH,SAAS,wCAAwC,CAAC,GAAqB,EAAE,WAAwB,EAAA;IAC/F,IAAI,GAAG,CAAC,WAAW,KAAK,IAAI,IAAI,WAAW,KAAK,eAAe,EAAE;QAC/D,MAAM,IAAIA,aAAY,CAAA,IAAA,kDAEpB,CAAG,EAAA,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAoD,kDAAA,CAAA;YACnF,CAAsE,oEAAA,CAAA;YACtE,CAA6F,2FAAA,CAAA;AAC7F,YAAA,CAAA,qFAAA,CAAuF,CAC1F,CAAC;KACH;AACH,CAAC;AAED;;AAEG;AACH,SAAS,wBAAwB,CAAC,GAAqB,EAAA;IACrD,IACE,GAAG,CAAC,WAAW;AACf,QAAA,OAAO,GAAG,CAAC,WAAW,KAAK,QAAQ;QACnC,GAAG,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,EACnC;QACA,IAAI,GAAG,CAAC,WAAW,CAAC,MAAM,GAAG,oBAAoB,EAAE;YACjD,MAAM,IAAIA,aAAY,CAAA,IAAA,+CAEpB,CAAG,EAAA,mBAAmB,CACpB,GAAG,CAAC,KAAK,CACV,CAAsE,oEAAA,CAAA;AACrE,gBAAA,CAAA,KAAA,EAAQ,oBAAoB,CAA0E,wEAAA,CAAA;gBACtG,CAAqG,mGAAA,CAAA;AACrG,gBAAA,CAAA,+BAAA,CAAiC,CACpC,CAAC;SACH;QACD,IAAI,GAAG,CAAC,WAAW,CAAC,MAAM,GAAG,mBAAmB,EAAE;AAChD,YAAA,OAAO,CAAC,IAAI,CACVC,mBAAkB,CAEhB,IAAA,+CAAA,CAAA,EAAG,mBAAmB,CACpB,GAAG,CAAC,KAAK,CACV,CAAsE,oEAAA,CAAA;AACrE,gBAAA,CAAA,KAAA,EAAQ,mBAAmB,CAAiE,+DAAA,CAAA;gBAC5F,CAA+G,6GAAA,CAAA;gBAC/G,CAA0C,wCAAA,CAAA,CAC7C,CACF,CAAC;SACH;KACF;AACH,CAAC;AAED;;AAEG;AACH,SAAS,gBAAgB,CAAC,GAAqB,EAAA;IAC7C,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;AAC/B,IAAA,IAAI,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;AAC7B,QAAA,MAAM,IAAID,aAAY,CAEpB,IAAA,uCAAA,CAAA,EAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAqC,kCAAA,EAAA,KAAK,CAAK,GAAA,CAAA;YAC9E,CAAiE,+DAAA,CAAA;YACjE,CAAuE,qEAAA,CAAA;AACvE,YAAA,CAAA,oEAAA,CAAsE,CACzE,CAAC;KACH;AACH,CAAC;AAED;;AAEG;AACH,SAAS,mBAAmB,CAAC,GAAqB,EAAE,IAAY,EAAE,KAAc,EAAA;AAC9E,IAAA,MAAM,QAAQ,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC;IAC3C,MAAM,aAAa,GAAG,QAAQ,IAAI,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;AACtD,IAAA,IAAI,CAAC,QAAQ,IAAI,aAAa,EAAE;AAC9B,QAAA,MAAM,IAAIA,aAAY,CAEpB,IAAA,uCAAA,CAAA,EAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAM,GAAA,EAAA,IAAI,CAA0B,wBAAA,CAAA;YACnE,CAAM,GAAA,EAAA,KAAK,CAA2D,yDAAA,CAAA,CACzE,CAAC;KACH;AACH,CAAC;AAED;;AAEG;AACa,SAAA,mBAAmB,CAAC,GAAqB,EAAE,KAAc,EAAA;IACvE,IAAI,KAAK,IAAI,IAAI;QAAE,OAAO;AAC1B,IAAA,mBAAmB,CAAC,GAAG,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;IAC5C,MAAM,SAAS,GAAG,KAAe,CAAC;IAClC,MAAM,sBAAsB,GAAG,6BAA6B,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC7E,MAAM,wBAAwB,GAAG,+BAA+B,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAEjF,IAAI,wBAAwB,EAAE;AAC5B,QAAA,qBAAqB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;KACvC;AAED,IAAA,MAAM,aAAa,GAAG,sBAAsB,IAAI,wBAAwB,CAAC;IACzE,IAAI,CAAC,aAAa,EAAE;AAClB,QAAA,MAAM,IAAIA,aAAY,CAEpB,IAAA,uCAAA,CAAA,EAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAyC,sCAAA,EAAA,KAAK,CAAO,KAAA,CAAA;YACpF,CAAqF,mFAAA,CAAA;AACrF,YAAA,CAAA,uEAAA,CAAyE,CAC5E,CAAC;KACH;AACH,CAAC;AAED,SAAS,qBAAqB,CAAC,GAAqB,EAAE,KAAa,EAAA;IACjE,MAAM,eAAe,GAAG,KAAK;SAC1B,KAAK,CAAC,GAAG,CAAC;AACV,SAAA,KAAK,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,EAAE,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,2BAA2B,CAAC,CAAC;IAChF,IAAI,CAAC,eAAe,EAAE;QACpB,MAAM,IAAIA,aAAY,CAAA,IAAA,uCAEpB,CAAG,EAAA,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAA0D,wDAAA,CAAA;AACzF,YAAA,CAAA,EAAA,EAAK,KAAK,CAAmE,iEAAA,CAAA;AAC7E,YAAA,CAAA,EAAG,8BAA8B,CAAuC,qCAAA,CAAA;AACxE,YAAA,CAAA,EAAG,2BAA2B,CAA8D,4DAAA,CAAA;AAC5F,YAAA,CAAA,aAAA,EAAgB,8BAA8B,CAAuC,qCAAA,CAAA;YACrF,CAA0F,wFAAA,CAAA;YAC1F,CAAG,EAAA,2BAA2B,CAAoE,kEAAA,CAAA,CACrG,CAAC;KACH;AACH,CAAC;AAED;;;AAGG;AACH,SAAS,wBAAwB,CAAC,GAAqB,EAAE,SAAiB,EAAA;AACxE,IAAA,IAAI,MAAe,CAAC;IACpB,IAAI,SAAS,KAAK,OAAO,IAAI,SAAS,KAAK,QAAQ,EAAE;QACnD,MAAM;AACJ,YAAA,CAAA,WAAA,EAAc,SAAS,CAA6C,2CAAA,CAAA;AACpE,gBAAA,CAAA,0EAAA,CAA4E,CAAC;KAChF;SAAM;QACL,MAAM;AACJ,YAAA,CAAA,eAAA,EAAkB,SAAS,CAA4C,0CAAA,CAAA;AACvE,gBAAA,CAAA,iEAAA,CAAmE,CAAC;KACvE;AACD,IAAA,OAAO,IAAIA,aAAY,CAErB,IAAA,iDAAA,CAAA,EAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAM,GAAA,EAAA,SAAS,CAAuC,qCAAA,CAAA;AACrF,QAAA,CAAA,oEAAA,EAAuE,MAAM,CAAG,CAAA,CAAA;AAChF,QAAA,CAAA,6BAAA,EAAgC,SAAS,CAAuB,qBAAA,CAAA;AAChE,QAAA,CAAA,2EAAA,CAA6E,CAChF,CAAC;AACJ,CAAC;AAED;;AAEG;AACH,SAAS,2BAA2B,CAClC,GAAqB,EACrB,OAAsB,EACtB,MAAgB,EAAA;AAEhB,IAAA,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;QACvB,MAAM,SAAS,GAAG,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAChD,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,EAAE;AAChD,YAAA,IAAI,KAAK,KAAK,OAAO,EAAE;;;;;gBAKrB,GAAG,GAAG,EAAC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,aAAa,EAAqB,CAAC;aACjE;AACD,YAAA,MAAM,wBAAwB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;SAC5C;AACH,KAAC,CAAC,CAAC;AACL,CAAC;AAED;;AAEG;AACH,SAAS,qBAAqB,CAAC,GAAqB,EAAE,UAAmB,EAAE,SAAiB,EAAA;IAC1F,MAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,IAAI,UAAU,GAAG,CAAC,CAAC;IACrE,MAAM,WAAW,GACf,OAAO,UAAU,KAAK,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,IAAI,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAChG,IAAA,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,EAAE;AAChC,QAAA,MAAM,IAAIA,aAAY,CAEpB,IAAA,uCAAA,CAAA,EAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAM,GAAA,EAAA,SAAS,CAA2B,yBAAA,CAAA;YACzE,CAA0B,uBAAA,EAAA,SAAS,CAAgC,8BAAA,CAAA,CACtE,CAAC;KACH;AACH,CAAC;AAED;;;;AAIG;AACH,SAAS,uBAAuB,CAC9B,GAAqB,EACrB,GAAqB,EACrB,QAAmB,EAAA;IAEnB,MAAM,oBAAoB,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,MAAK;AAC7D,QAAA,oBAAoB,EAAE,CAAC;AACvB,QAAA,qBAAqB,EAAE,CAAC;QACxB,MAAM,aAAa,GAAG,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;QACnD,IAAI,aAAa,GAAG,UAAU,CAAC,aAAa,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;QACxE,IAAI,cAAc,GAAG,UAAU,CAAC,aAAa,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC1E,MAAM,SAAS,GAAG,aAAa,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;AAE/D,QAAA,IAAI,SAAS,KAAK,YAAY,EAAE;YAC9B,MAAM,UAAU,GAAG,aAAa,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;YACjE,MAAM,YAAY,GAAG,aAAa,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;YACrE,MAAM,aAAa,GAAG,aAAa,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YACvE,MAAM,WAAW,GAAG,aAAa,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;YACnE,aAAa,IAAI,UAAU,CAAC,YAAY,CAAC,GAAG,UAAU,CAAC,WAAW,CAAC,CAAC;YACpE,cAAc,IAAI,UAAU,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,aAAa,CAAC,CAAC;SACtE;AAED,QAAA,MAAM,mBAAmB,GAAG,aAAa,GAAG,cAAc,CAAC;QAC3D,MAAM,yBAAyB,GAAG,aAAa,KAAK,CAAC,IAAI,cAAc,KAAK,CAAC,CAAC;AAE9E,QAAA,MAAM,cAAc,GAAG,GAAG,CAAC,YAAY,CAAC;AACxC,QAAA,MAAM,eAAe,GAAG,GAAG,CAAC,aAAa,CAAC;AAC1C,QAAA,MAAM,oBAAoB,GAAG,cAAc,GAAG,eAAe,CAAC;AAE9D,QAAA,MAAM,aAAa,GAAG,GAAG,CAAC,KAAM,CAAC;AACjC,QAAA,MAAM,cAAc,GAAG,GAAG,CAAC,MAAO,CAAC;AACnC,QAAA,MAAM,mBAAmB,GAAG,aAAa,GAAG,cAAc,CAAC;;;;;;AAO3D,QAAA,MAAM,oBAAoB,GACxB,IAAI,CAAC,GAAG,CAAC,mBAAmB,GAAG,oBAAoB,CAAC,GAAG,sBAAsB,CAAC;QAChF,MAAM,iBAAiB,GACrB,yBAAyB;YACzB,IAAI,CAAC,GAAG,CAAC,oBAAoB,GAAG,mBAAmB,CAAC,GAAG,sBAAsB,CAAC;QAEhF,IAAI,oBAAoB,EAAE;AACxB,YAAA,OAAO,CAAC,IAAI,CACVC,mBAAkB,CAEhB,IAAA,uCAAA,CAAA,EAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAgD,8CAAA,CAAA;gBAC/E,CAAiE,+DAAA,CAAA;gBACjE,CAA2B,wBAAA,EAAA,cAAc,CAAO,IAAA,EAAA,eAAe,CAAI,EAAA,CAAA;AACnE,gBAAA,CAAA,eAAA,EAAkB,KAAK,CACrB,oBAAoB,CACrB,CAA6C,2CAAA,CAAA;gBAC9C,CAAG,EAAA,aAAa,OAAO,cAAc,CAAA,iBAAA,EAAoB,KAAK,CAC5D,mBAAmB,CACpB,CAAK,GAAA,CAAA;gBACN,CAAwD,sDAAA,CAAA,CAC3D,CACF,CAAC;SACH;aAAM,IAAI,iBAAiB,EAAE;AAC5B,YAAA,OAAO,CAAC,IAAI,CACVA,mBAAkB,CAEhB,IAAA,uCAAA,CAAA,EAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAA0C,wCAAA,CAAA;gBACzE,CAAqD,mDAAA,CAAA;gBACrD,CAA2B,wBAAA,EAAA,cAAc,CAAO,IAAA,EAAA,eAAe,CAAI,EAAA,CAAA;AACnE,gBAAA,CAAA,eAAA,EAAkB,KAAK,CAAC,oBAAoB,CAAC,CAA4B,0BAAA,CAAA;gBACzE,CAAG,EAAA,aAAa,CAAO,IAAA,EAAA,cAAc,CAAmB,iBAAA,CAAA;AACxD,gBAAA,CAAA,EAAG,KAAK,CAAC,mBAAmB,CAAC,CAAoD,kDAAA,CAAA;gBACjF,CAAsE,oEAAA,CAAA;gBACtE,CAAmE,iEAAA,CAAA;gBACnE,CAAuE,qEAAA,CAAA;gBACvE,CAAa,WAAA,CAAA,CAChB,CACF,CAAC;SACH;AAAM,aAAA,IAAI,CAAC,GAAG,CAAC,QAAQ,IAAI,yBAAyB,EAAE;;AAErD,YAAA,MAAM,gBAAgB,GAAG,8BAA8B,GAAG,aAAa,CAAC;AACxE,YAAA,MAAM,iBAAiB,GAAG,8BAA8B,GAAG,cAAc,CAAC;AAC1E,YAAA,MAAM,cAAc,GAAG,cAAc,GAAG,gBAAgB,IAAI,yBAAyB,CAAC;AACtF,YAAA,MAAM,eAAe,GAAG,eAAe,GAAG,iBAAiB,IAAI,yBAAyB,CAAC;AACzF,YAAA,IAAI,cAAc,IAAI,eAAe,EAAE;AACrC,gBAAA,OAAO,CAAC,IAAI,CACVA,mBAAkB,CAEhB,IAAA,yCAAA,CAAA,EAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAwC,sCAAA,CAAA;oBACvE,CAAyB,uBAAA,CAAA;oBACzB,CAA0B,uBAAA,EAAA,aAAa,CAAO,IAAA,EAAA,cAAc,CAAK,GAAA,CAAA;oBACjE,CAA2B,wBAAA,EAAA,cAAc,CAAO,IAAA,EAAA,eAAe,CAAK,GAAA,CAAA;oBACpE,CAAuC,oCAAA,EAAA,gBAAgB,CAAO,IAAA,EAAA,iBAAiB,CAAK,GAAA,CAAA;oBACpF,CAAmF,iFAAA,CAAA;AACnF,oBAAA,CAAA,EAAG,8BAA8B,CAA8C,4CAAA,CAAA;oBAC/E,CAA0D,wDAAA,CAAA,CAC7D,CACF,CAAC;aACH;SACF;AACH,KAAC,CAAC,CAAC;;;;;IAMH,MAAM,qBAAqB,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,EAAE,MAAK;AAC/D,QAAA,oBAAoB,EAAE,CAAC;AACvB,QAAA,qBAAqB,EAAE,CAAC;AAC1B,KAAC,CAAC,CAAC;AACL,CAAC;AAED;;AAEG;AACH,SAAS,4BAA4B,CAAC,GAAqB,EAAA;IACzD,IAAI,iBAAiB,GAAG,EAAE,CAAC;AAC3B,IAAA,IAAI,GAAG,CAAC,KAAK,KAAK,SAAS;AAAE,QAAA,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC7D,IAAA,IAAI,GAAG,CAAC,MAAM,KAAK,SAAS;AAAE,QAAA,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC/D,IAAA,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;QAChC,MAAM,IAAID,aAAY,CAAA,IAAA,gDAEpB,CAAG,EAAA,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAA6B,2BAAA,CAAA;AAC5D,YAAA,CAAA,aAAA,EAAgB,iBAAiB,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAI,CAAA,EAAA,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAI,EAAA,CAAA;YAC3E,CAAsF,oFAAA,CAAA;YACtF,CAAmF,iFAAA,CAAA;AACnF,YAAA,CAAA,wCAAA,CAA0C,CAC7C,CAAC;KACH;AACH,CAAC;AAED;;;AAGG;AACH,SAAS,yBAAyB,CAAC,GAAqB,EAAA;IACtD,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,MAAM,EAAE;QAC3B,MAAM,IAAIA,aAAY,CAAA,IAAA,uCAEpB,CAAG,EAAA,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAA0D,wDAAA,CAAA;YACzF,CAAkG,gGAAA,CAAA;AAClG,YAAA,CAAA,kEAAA,CAAoE,CACvE,CAAC;KACH;AACH,CAAC;AAED;;;AAGG;AACH,SAAS,2BAA2B,CAClC,GAAqB,EACrB,GAAqB,EACrB,QAAmB,EAAA;IAEnB,MAAM,oBAAoB,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,MAAK;AAC7D,QAAA,oBAAoB,EAAE,CAAC;AACvB,QAAA,qBAAqB,EAAE,CAAC;AACxB,QAAA,MAAM,cAAc,GAAG,GAAG,CAAC,YAAY,CAAC;QACxC,IAAI,GAAG,CAAC,IAAI,IAAI,cAAc,KAAK,CAAC,EAAE;AACpC,YAAA,OAAO,CAAC,IAAI,CACVC,mBAAkB,CAEhB,IAAA,uCAAA,CAAA,EAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAA8C,4CAAA,CAAA;gBAC7E,CAAiF,+EAAA,CAAA;gBACjF,CAA4E,0EAAA,CAAA;gBAC5E,CAA8E,4EAAA,CAAA;gBAC9E,CAA6D,2DAAA,CAAA,CAChE,CACF,CAAC;SACH;AACH,KAAC,CAAC,CAAC;;IAGH,MAAM,qBAAqB,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,EAAE,MAAK;AAC/D,QAAA,oBAAoB,EAAE,CAAC;AACvB,QAAA,qBAAqB,EAAE,CAAC;AAC1B,KAAC,CAAC,CAAC;AACL,CAAC;AAED;;;AAGG;AACH,SAAS,uBAAuB,CAAC,GAAqB,EAAA;IACpD,IAAI,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,QAAQ,EAAE;QAC/B,MAAM,IAAID,aAAY,CAAA,IAAA,uCAEpB,CAAG,EAAA,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAA6B,2BAAA,CAAA;YAC5D,CAAmD,iDAAA,CAAA;YACnD,CAAwD,sDAAA,CAAA;YACxD,CAAsD,oDAAA,CAAA;AACtD,YAAA,CAAA,oEAAA,CAAsE,CACzE,CAAC;KACH;IACD,MAAM,WAAW,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;AAC9C,IAAA,IAAI,OAAO,GAAG,CAAC,OAAO,KAAK,QAAQ,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;QACzE,MAAM,IAAIA,aAAY,CAAA,IAAA,uCAEpB,CAAG,EAAA,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAA6B,2BAAA,CAAA;YAC5D,CAA2B,wBAAA,EAAA,GAAG,CAAC,OAAO,CAAO,KAAA,CAAA;AAC7C,YAAA,CAAA,gEAAA,CAAkE,CACrE,CAAC;KACH;AACH,CAAC;AAED;;;;;;;;AAQG;AACH,SAAS,6BAA6B,CAAC,KAAa,EAAE,WAAwB,EAAA;AAC5E,IAAA,IAAI,WAAW,KAAK,eAAe,EAAE;QACnC,IAAI,iBAAiB,GAAG,EAAE,CAAC;AAC3B,QAAA,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE;AACrC,YAAA,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACzB,gBAAA,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC;gBAChC,MAAM;aACP;SACF;QACD,IAAI,iBAAiB,EAAE;AACrB,YAAA,OAAO,CAAC,IAAI,CACVC,mBAAkB,qDAEhB,CAAmE,iEAAA,CAAA;AACjE,gBAAA,CAAA,EAAG,iBAAiB,CAA4C,0CAAA,CAAA;gBAChE,CAA8D,4DAAA,CAAA;AAC9D,gBAAA,CAAA,iCAAA,EAAoC,iBAAiB,CAAa,WAAA,CAAA;gBAClE,CAAiE,+DAAA,CAAA;gBACjE,CAAgE,8DAAA,CAAA;gBAChE,CAA6D,2DAAA,CAAA,CAChE,CACF,CAAC;SACH;KACF;AACH,CAAC;AAED;;AAEG;AACH,SAAS,6BAA6B,CAAC,GAAqB,EAAE,WAAwB,EAAA;IACpF,IAAI,GAAG,CAAC,QAAQ,IAAI,WAAW,KAAK,eAAe,EAAE;AACnD,QAAA,OAAO,CAAC,IAAI,CACVA,mBAAkB,CAEhB,IAAA,kDAAA,CAAA,EAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAA6C,2CAAA,CAAA;YAC5E,CAAsE,oEAAA,CAAA;YACtE,CAA4E,0EAAA,CAAA;YAC5E,CAAoF,kFAAA,CAAA,CACvF,CACF,CAAC;KACH;AACH,CAAC;AAED;;;AAGG;AACH,SAAS,iCAAiC,CAAC,GAAqB,EAAE,WAAwB,EAAA;IACxF,IAAI,GAAG,CAAC,YAAY,IAAI,WAAW,KAAK,eAAe,EAAE;AACvD,QAAA,OAAO,CAAC,IAAI,CACVA,mBAAkB,CAEhB,IAAA,kDAAA,CAAA,EAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAiD,+CAAA,CAAA;YAChF,CAAsE,oEAAA,CAAA;YACtE,CAA2F,yFAAA,CAAA;YAC3F,CAA+F,6FAAA,CAAA,CAClG,CACF,CAAC;KACH;AACH,CAAC;AAED,SAAS,KAAK,CAAC,KAAa,EAAA;AAC1B,IAAA,OAAO,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC;AAED;AACA;AACA,SAAS,aAAa,CAAC,KAAyB,EAAA;AAC9C,IAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,QAAA,OAAO,KAAK,CAAC;KACd;AACD,IAAA,OAAO6B,gBAAe,CAAC,KAAK,CAAC,CAAC;AAChC,CAAC;AAED;AACA;AACM,SAAU,yBAAyB,CAAC,KAAuB,EAAA;AAC/D,IAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,UAAU,CAAC,CAAA,KAAA,CAAO,CAAC,EAAE;AAC1D,QAAA,OAAO,KAAK,CAAC;KACd;AACD,IAAA,OAAO,gBAAgB,CAAC,KAAK,CAAC,CAAC;AACjC;;AC3uCA;;;;AAIG;;ACJH;;;;AAIG;AAGH;;ACPA;;ACRA;;AAEG;;;;"}