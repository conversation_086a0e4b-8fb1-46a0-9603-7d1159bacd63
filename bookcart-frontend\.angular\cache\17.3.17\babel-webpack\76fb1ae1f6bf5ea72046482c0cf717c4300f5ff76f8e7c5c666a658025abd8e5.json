{"ast": null, "code": "import { BehaviorSubject, tap } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@auth0/angular-jwt\";\nimport * as i3 from \"@angular/router\";\nexport class AuthService {\n  constructor(http, jwtHelper, router) {\n    this.http = http;\n    this.jwtHelper = jwtHelper;\n    this.router = router;\n    this.API_URL = 'http://localhost:5001/api/auth';\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    this.loadCurrentUser();\n  }\n  login(credentials) {\n    return this.http.post(`${this.API_URL}/login`, credentials).pipe(tap(response => {\n      this.setSession(response);\n    }));\n  }\n  register(userData) {\n    return this.http.post(`${this.API_URL}/register`, userData).pipe(tap(response => {\n      this.setSession(response);\n    }));\n  }\n  logout() {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    this.currentUserSubject.next(null);\n    this.router.navigate(['/login']);\n  }\n  getProfile() {\n    return this.http.get(`${this.API_URL}/profile`).pipe(tap(profile => {\n      this.currentUserSubject.next(profile);\n      localStorage.setItem('user', JSON.stringify(profile));\n    }));\n  }\n  updateProfile(profile) {\n    return this.http.put(`${this.API_URL}/profile`, profile).pipe(tap(updatedProfile => {\n      this.currentUserSubject.next(updatedProfile);\n      localStorage.setItem('user', JSON.stringify(updatedProfile));\n    }));\n  }\n  isAuthenticated() {\n    const token = localStorage.getItem('token');\n    return token ? !this.jwtHelper.isTokenExpired(token) : false;\n  }\n  hasRole(role) {\n    const user = this.currentUserSubject.value;\n    return user ? user.roles.includes(role) : false;\n  }\n  isAdmin() {\n    return this.hasRole('Admin');\n  }\n  getCurrentUser() {\n    return this.currentUserSubject.value;\n  }\n  getToken() {\n    return localStorage.getItem('token');\n  }\n  setSession(authResponse) {\n    localStorage.setItem('token', authResponse.token);\n    const userProfile = {\n      id: authResponse.userId,\n      firstName: authResponse.firstName,\n      lastName: authResponse.lastName,\n      email: authResponse.email,\n      phoneNumber: '',\n      address: '',\n      createdDate: new Date().toISOString(),\n      isActive: true,\n      roles: authResponse.roles\n    };\n    localStorage.setItem('user', JSON.stringify(userProfile));\n    this.currentUserSubject.next(userProfile);\n  }\n  loadCurrentUser() {\n    const token = localStorage.getItem('token');\n    const userStr = localStorage.getItem('user');\n    if (token && !this.jwtHelper.isTokenExpired(token) && userStr) {\n      const user = JSON.parse(userStr);\n      this.currentUserSubject.next(user);\n    } else {\n      this.logout();\n    }\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.JwtHelperService), i0.ɵɵinject(i3.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "tap", "AuthService", "constructor", "http", "jwtHelper", "router", "API_URL", "currentUserSubject", "currentUser$", "asObservable", "loadCurrentUser", "login", "credentials", "post", "pipe", "response", "setSession", "register", "userData", "logout", "localStorage", "removeItem", "next", "navigate", "getProfile", "get", "profile", "setItem", "JSON", "stringify", "updateProfile", "put", "updatedProfile", "isAuthenticated", "token", "getItem", "isTokenExpired", "hasRole", "role", "user", "value", "roles", "includes", "isAdmin", "getCurrentUser", "getToken", "authResponse", "userProfile", "id", "userId", "firstName", "lastName", "email", "phoneNumber", "address", "createdDate", "Date", "toISOString", "isActive", "userStr", "parse", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "JwtHelperService", "i3", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\BookCart\\bookcart-frontend\\src\\app\\shared\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { BehaviorSubject, Observable, tap } from 'rxjs';\nimport { JwtHelperService } from '@auth0/angular-jwt';\nimport { Router } from '@angular/router';\n\nexport interface LoginRequest {\n  email: string;\n  password: string;\n}\n\nexport interface RegisterRequest {\n  firstName: string;\n  lastName: string;\n  email: string;\n  password: string;\n  confirmPassword: string;\n  phoneNumber?: string;\n  address?: string;\n}\n\nexport interface AuthResponse {\n  token: string;\n  userId: string;\n  email: string;\n  firstName: string;\n  lastName: string;\n  roles: string[];\n  expiration: string;\n}\n\nexport interface UserProfile {\n  id: string;\n  firstName: string;\n  lastName: string;\n  email: string;\n  phoneNumber?: string;\n  address?: string;\n  createdDate: string;\n  isActive: boolean;\n  roles: string[];\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private readonly API_URL = 'http://localhost:5001/api/auth';\n  private currentUserSubject = new BehaviorSubject<UserProfile | null>(null);\n  public currentUser$ = this.currentUserSubject.asObservable();\n\n  constructor(\n    private http: HttpClient,\n    private jwtHelper: JwtHelperService,\n    private router: Router\n  ) {\n    this.loadCurrentUser();\n  }\n\n  login(credentials: LoginRequest): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.API_URL}/login`, credentials)\n      .pipe(\n        tap(response => {\n          this.setSession(response);\n        })\n      );\n  }\n\n  register(userData: RegisterRequest): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.API_URL}/register`, userData)\n      .pipe(\n        tap(response => {\n          this.setSession(response);\n        })\n      );\n  }\n\n  logout(): void {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    this.currentUserSubject.next(null);\n    this.router.navigate(['/login']);\n  }\n\n  getProfile(): Observable<UserProfile> {\n    return this.http.get<UserProfile>(`${this.API_URL}/profile`)\n      .pipe(\n        tap(profile => {\n          this.currentUserSubject.next(profile);\n          localStorage.setItem('user', JSON.stringify(profile));\n        })\n      );\n  }\n\n  updateProfile(profile: Partial<UserProfile>): Observable<UserProfile> {\n    return this.http.put<UserProfile>(`${this.API_URL}/profile`, profile)\n      .pipe(\n        tap(updatedProfile => {\n          this.currentUserSubject.next(updatedProfile);\n          localStorage.setItem('user', JSON.stringify(updatedProfile));\n        })\n      );\n  }\n\n  isAuthenticated(): boolean {\n    const token = localStorage.getItem('token');\n    return token ? !this.jwtHelper.isTokenExpired(token) : false;\n  }\n\n  hasRole(role: string): boolean {\n    const user = this.currentUserSubject.value;\n    return user ? user.roles.includes(role) : false;\n  }\n\n  isAdmin(): boolean {\n    return this.hasRole('Admin');\n  }\n\n  getCurrentUser(): UserProfile | null {\n    return this.currentUserSubject.value;\n  }\n\n  getToken(): string | null {\n    return localStorage.getItem('token');\n  }\n\n  private setSession(authResponse: AuthResponse): void {\n    localStorage.setItem('token', authResponse.token);\n    \n    const userProfile: UserProfile = {\n      id: authResponse.userId,\n      firstName: authResponse.firstName,\n      lastName: authResponse.lastName,\n      email: authResponse.email,\n      phoneNumber: '',\n      address: '',\n      createdDate: new Date().toISOString(),\n      isActive: true,\n      roles: authResponse.roles\n    };\n    \n    localStorage.setItem('user', JSON.stringify(userProfile));\n    this.currentUserSubject.next(userProfile);\n  }\n\n  private loadCurrentUser(): void {\n    const token = localStorage.getItem('token');\n    const userStr = localStorage.getItem('user');\n    \n    if (token && !this.jwtHelper.isTokenExpired(token) && userStr) {\n      const user = JSON.parse(userStr);\n      this.currentUserSubject.next(user);\n    } else {\n      this.logout();\n    }\n  }\n}\n"], "mappings": "AAEA,SAASA,eAAe,EAAcC,GAAG,QAAQ,MAAM;;;;;AA4CvD,OAAM,MAAOC,WAAW;EAKtBC,YACUC,IAAgB,EAChBC,SAA2B,EAC3BC,MAAc;IAFd,KAAAF,IAAI,GAAJA,IAAI;IACJ,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,MAAM,GAANA,MAAM;IAPC,KAAAC,OAAO,GAAG,gCAAgC;IACnD,KAAAC,kBAAkB,GAAG,IAAIR,eAAe,CAAqB,IAAI,CAAC;IACnE,KAAAS,YAAY,GAAG,IAAI,CAACD,kBAAkB,CAACE,YAAY,EAAE;IAO1D,IAAI,CAACC,eAAe,EAAE;EACxB;EAEAC,KAAKA,CAACC,WAAyB;IAC7B,OAAO,IAAI,CAACT,IAAI,CAACU,IAAI,CAAe,GAAG,IAAI,CAACP,OAAO,QAAQ,EAAEM,WAAW,CAAC,CACtEE,IAAI,CACHd,GAAG,CAACe,QAAQ,IAAG;MACb,IAAI,CAACC,UAAU,CAACD,QAAQ,CAAC;IAC3B,CAAC,CAAC,CACH;EACL;EAEAE,QAAQA,CAACC,QAAyB;IAChC,OAAO,IAAI,CAACf,IAAI,CAACU,IAAI,CAAe,GAAG,IAAI,CAACP,OAAO,WAAW,EAAEY,QAAQ,CAAC,CACtEJ,IAAI,CACHd,GAAG,CAACe,QAAQ,IAAG;MACb,IAAI,CAACC,UAAU,CAACD,QAAQ,CAAC;IAC3B,CAAC,CAAC,CACH;EACL;EAEAI,MAAMA,CAAA;IACJC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/B,IAAI,CAACd,kBAAkB,CAACe,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACjB,MAAM,CAACkB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAC,UAAUA,CAAA;IACR,OAAO,IAAI,CAACrB,IAAI,CAACsB,GAAG,CAAc,GAAG,IAAI,CAACnB,OAAO,UAAU,CAAC,CACzDQ,IAAI,CACHd,GAAG,CAAC0B,OAAO,IAAG;MACZ,IAAI,CAACnB,kBAAkB,CAACe,IAAI,CAACI,OAAO,CAAC;MACrCN,YAAY,CAACO,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACH,OAAO,CAAC,CAAC;IACvD,CAAC,CAAC,CACH;EACL;EAEAI,aAAaA,CAACJ,OAA6B;IACzC,OAAO,IAAI,CAACvB,IAAI,CAAC4B,GAAG,CAAc,GAAG,IAAI,CAACzB,OAAO,UAAU,EAAEoB,OAAO,CAAC,CAClEZ,IAAI,CACHd,GAAG,CAACgC,cAAc,IAAG;MACnB,IAAI,CAACzB,kBAAkB,CAACe,IAAI,CAACU,cAAc,CAAC;MAC5CZ,YAAY,CAACO,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACG,cAAc,CAAC,CAAC;IAC9D,CAAC,CAAC,CACH;EACL;EAEAC,eAAeA,CAAA;IACb,MAAMC,KAAK,GAAGd,YAAY,CAACe,OAAO,CAAC,OAAO,CAAC;IAC3C,OAAOD,KAAK,GAAG,CAAC,IAAI,CAAC9B,SAAS,CAACgC,cAAc,CAACF,KAAK,CAAC,GAAG,KAAK;EAC9D;EAEAG,OAAOA,CAACC,IAAY;IAClB,MAAMC,IAAI,GAAG,IAAI,CAAChC,kBAAkB,CAACiC,KAAK;IAC1C,OAAOD,IAAI,GAAGA,IAAI,CAACE,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAAC,GAAG,KAAK;EACjD;EAEAK,OAAOA,CAAA;IACL,OAAO,IAAI,CAACN,OAAO,CAAC,OAAO,CAAC;EAC9B;EAEAO,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACrC,kBAAkB,CAACiC,KAAK;EACtC;EAEAK,QAAQA,CAAA;IACN,OAAOzB,YAAY,CAACe,OAAO,CAAC,OAAO,CAAC;EACtC;EAEQnB,UAAUA,CAAC8B,YAA0B;IAC3C1B,YAAY,CAACO,OAAO,CAAC,OAAO,EAAEmB,YAAY,CAACZ,KAAK,CAAC;IAEjD,MAAMa,WAAW,GAAgB;MAC/BC,EAAE,EAAEF,YAAY,CAACG,MAAM;MACvBC,SAAS,EAAEJ,YAAY,CAACI,SAAS;MACjCC,QAAQ,EAAEL,YAAY,CAACK,QAAQ;MAC/BC,KAAK,EAAEN,YAAY,CAACM,KAAK;MACzBC,WAAW,EAAE,EAAE;MACfC,OAAO,EAAE,EAAE;MACXC,WAAW,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;MACrCC,QAAQ,EAAE,IAAI;MACdjB,KAAK,EAAEK,YAAY,CAACL;KACrB;IAEDrB,YAAY,CAACO,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACkB,WAAW,CAAC,CAAC;IACzD,IAAI,CAACxC,kBAAkB,CAACe,IAAI,CAACyB,WAAW,CAAC;EAC3C;EAEQrC,eAAeA,CAAA;IACrB,MAAMwB,KAAK,GAAGd,YAAY,CAACe,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMwB,OAAO,GAAGvC,YAAY,CAACe,OAAO,CAAC,MAAM,CAAC;IAE5C,IAAID,KAAK,IAAI,CAAC,IAAI,CAAC9B,SAAS,CAACgC,cAAc,CAACF,KAAK,CAAC,IAAIyB,OAAO,EAAE;MAC7D,MAAMpB,IAAI,GAAGX,IAAI,CAACgC,KAAK,CAACD,OAAO,CAAC;MAChC,IAAI,CAACpD,kBAAkB,CAACe,IAAI,CAACiB,IAAI,CAAC;KACnC,MAAM;MACL,IAAI,CAACpB,MAAM,EAAE;;EAEjB;;;uBA7GWlB,WAAW,EAAA4D,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAXnE,WAAW;MAAAoE,OAAA,EAAXpE,WAAW,CAAAqE,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}