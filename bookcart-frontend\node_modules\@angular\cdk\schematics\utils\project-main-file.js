"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getProjectMainFile = void 0;
const schematics_1 = require("@angular-devkit/schematics");
const project_targets_1 = require("./project-targets");
/** Looks for the main TypeScript file in the given project and returns its path. */
function getProjectMainFile(project) {
    const buildOptions = (0, project_targets_1.getProjectTargetOptions)(project, 'build');
    // `browser` is for the `@angular-devkit/build-angular:application` builder while
    // `main` is for the `@angular-devkit/build-angular:browser` builder.
    const mainPath = (buildOptions['browser'] || buildOptions['main']);
    if (!mainPath) {
        throw new schematics_1.SchematicsException(`Could not find the project main file inside of the ` +
            `workspace config (${project.sourceRoot})`);
    }
    return mainPath;
}
exports.getProjectMainFile = getProjectMainFile;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicHJvamVjdC1tYWluLWZpbGUuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi9zcmMvY2RrL3NjaGVtYXRpY3MvdXRpbHMvcHJvamVjdC1tYWluLWZpbGUudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUFBOzs7Ozs7R0FNRzs7O0FBR0gsMkRBQStEO0FBQy9ELHVEQUEwRDtBQUUxRCxvRkFBb0Y7QUFDcEYsU0FBZ0Isa0JBQWtCLENBQUMsT0FBcUM7SUFDdEUsTUFBTSxZQUFZLEdBQUcsSUFBQSx5Q0FBdUIsRUFBQyxPQUFPLEVBQUUsT0FBTyxDQUFDLENBQUM7SUFFL0QsaUZBQWlGO0lBQ2pGLHFFQUFxRTtJQUNyRSxNQUFNLFFBQVEsR0FBRyxDQUFDLFlBQVksQ0FBQyxTQUFTLENBQUMsSUFBSSxZQUFZLENBQUMsTUFBTSxDQUFDLENBQXFCLENBQUM7SUFFdkYsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFDO1FBQ2QsTUFBTSxJQUFJLGdDQUFtQixDQUMzQixxREFBcUQ7WUFDbkQscUJBQXFCLE9BQU8sQ0FBQyxVQUFVLEdBQUcsQ0FDN0MsQ0FBQztJQUNKLENBQUM7SUFFRCxPQUFPLFFBQVEsQ0FBQztBQUNsQixDQUFDO0FBZkQsZ0RBZUMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgR29vZ2xlIExMQyBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICpcbiAqIFVzZSBvZiB0aGlzIHNvdXJjZSBjb2RlIGlzIGdvdmVybmVkIGJ5IGFuIE1JVC1zdHlsZSBsaWNlbnNlIHRoYXQgY2FuIGJlXG4gKiBmb3VuZCBpbiB0aGUgTElDRU5TRSBmaWxlIGF0IGh0dHBzOi8vYW5ndWxhci5pby9saWNlbnNlXG4gKi9cblxuaW1wb3J0IHtQYXRoLCB3b3Jrc3BhY2VzfSBmcm9tICdAYW5ndWxhci1kZXZraXQvY29yZSc7XG5pbXBvcnQge1NjaGVtYXRpY3NFeGNlcHRpb259IGZyb20gJ0Bhbmd1bGFyLWRldmtpdC9zY2hlbWF0aWNzJztcbmltcG9ydCB7Z2V0UHJvamVjdFRhcmdldE9wdGlvbnN9IGZyb20gJy4vcHJvamVjdC10YXJnZXRzJztcblxuLyoqIExvb2tzIGZvciB0aGUgbWFpbiBUeXBlU2NyaXB0IGZpbGUgaW4gdGhlIGdpdmVuIHByb2plY3QgYW5kIHJldHVybnMgaXRzIHBhdGguICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0UHJvamVjdE1haW5GaWxlKHByb2plY3Q6IHdvcmtzcGFjZXMuUHJvamVjdERlZmluaXRpb24pOiBQYXRoIHtcbiAgY29uc3QgYnVpbGRPcHRpb25zID0gZ2V0UHJvamVjdFRhcmdldE9wdGlvbnMocHJvamVjdCwgJ2J1aWxkJyk7XG5cbiAgLy8gYGJyb3dzZXJgIGlzIGZvciB0aGUgYEBhbmd1bGFyLWRldmtpdC9idWlsZC1hbmd1bGFyOmFwcGxpY2F0aW9uYCBidWlsZGVyIHdoaWxlXG4gIC8vIGBtYWluYCBpcyBmb3IgdGhlIGBAYW5ndWxhci1kZXZraXQvYnVpbGQtYW5ndWxhcjpicm93c2VyYCBidWlsZGVyLlxuICBjb25zdCBtYWluUGF0aCA9IChidWlsZE9wdGlvbnNbJ2Jyb3dzZXInXSB8fCBidWlsZE9wdGlvbnNbJ21haW4nXSkgYXMgUGF0aCB8IHVuZGVmaW5lZDtcblxuICBpZiAoIW1haW5QYXRoKSB7XG4gICAgdGhyb3cgbmV3IFNjaGVtYXRpY3NFeGNlcHRpb24oXG4gICAgICBgQ291bGQgbm90IGZpbmQgdGhlIHByb2plY3QgbWFpbiBmaWxlIGluc2lkZSBvZiB0aGUgYCArXG4gICAgICAgIGB3b3Jrc3BhY2UgY29uZmlnICgke3Byb2plY3Quc291cmNlUm9vdH0pYCxcbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIG1haW5QYXRoO1xufVxuIl19