/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["ky", [["тң", "тк"], u, ["таңкы", "түштөн кийинки"]], u, [["Ж", "Д", "Ш", "Ш", "Б", "Ж", "И"], ["жек.", "дүй.", "шейш.", "шарш.", "бейш.", "жума", "ишм."], ["жекшемби", "дүйшөмбү", "шейшемби", "шаршемби", "бейшемби", "жума", "ишемби"], ["жш.", "дш.", "шш.", "шр.", "бш.", "жм.", "иш."]], u, [["Я", "Ф", "М", "А", "М", "И", "И", "А", "С", "О", "Н", "Д"], ["янв.", "фев.", "мар.", "апр.", "май", "июн.", "июл.", "авг.", "сен.", "окт.", "ноя.", "дек."], ["январь", "февраль", "март", "апрель", "май", "июнь", "июль", "август", "сентябрь", "октябрь", "ноябрь", "декабрь"]], [["Я", "Ф", "М", "А", "М", "И", "И", "А", "С", "О", "Н", "Д"], ["Янв", "Фев", "Мар", "Апр", "Май", "Июн", "Июл", "Авг", "Сен", "Окт", "Ноя", "Дек"], ["Январь", "Февраль", "Март", "Апрель", "Май", "Июнь", "Июль", "Август", "Сентябрь", "Октябрь", "Ноябрь", "Декабрь"]], [["б.з.ч.", "б.з."], u, ["биздин заманга чейин", "биздин заман"]], 1, [6, 0], ["d/M/yy", "y-'ж'., d-MMM", "y-'ж'., d-MMMM", "y-'ж'., d-MMMM, EEEE"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", " ", ";", "%", "+", "-", "E", "×", "‰", "∞", "сан эмес", ":"], ["#,##0.###", "#,##0%", "#,##0.00 ¤", "#E0"], "KGS", "сом", "Кыргызстан сому", { "AUD": [u, "$"], "BMD": [u, "BD$"], "BRL": [u, "R$"], "BSD": [u, "B$"], "BYN": [u, "р."], "BZD": [u, "BZ$"], "CAD": [u, "C$"], "DOP": [u, "RD$"], "EGP": [u, "LE"], "GBP": [u, "£"], "HKD": [u, "HK$"], "HRK": [u, "Kn"], "ILS": [u, "₪"], "INR": [u, "₹"], "JMD": [u, "J$"], "JPY": ["JP¥", "¥"], "KGS": ["сом"], "KRW": [u, "₩"], "MXN": [u, "$"], "NZD": [u, "$"], "PHP": [u, "₱"], "THB": ["฿"], "TTD": [u, "TT$"], "TWD": [u, "NT$"], "USD": [u, "$"], "XCD": [u, "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoia3kuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9jb21tb24vbG9jYWxlcy9reS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7O0dBTUc7QUFFSCwwQ0FBMEM7QUFDMUMsTUFBTSxDQUFDLEdBQUcsU0FBUyxDQUFDO0FBRXBCLFNBQVMsTUFBTSxDQUFDLEdBQVc7SUFDM0IsTUFBTSxDQUFDLEdBQUcsR0FBRyxDQUFDO0lBRWQsSUFBSSxDQUFDLEtBQUssQ0FBQztRQUNQLE9BQU8sQ0FBQyxDQUFDO0lBQ2IsT0FBTyxDQUFDLENBQUM7QUFDVCxDQUFDO0FBRUQsZUFBZSxDQUFDLElBQUksRUFBQyxDQUFDLENBQUMsSUFBSSxFQUFDLElBQUksQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLE9BQU8sRUFBQyxnQkFBZ0IsQ0FBQyxDQUFDLEVBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLENBQUMsRUFBQyxDQUFDLE1BQU0sRUFBQyxNQUFNLEVBQUMsT0FBTyxFQUFDLE9BQU8sRUFBQyxPQUFPLEVBQUMsTUFBTSxFQUFDLE1BQU0sQ0FBQyxFQUFDLENBQUMsVUFBVSxFQUFDLFVBQVUsRUFBQyxVQUFVLEVBQUMsVUFBVSxFQUFDLFVBQVUsRUFBQyxNQUFNLEVBQUMsUUFBUSxDQUFDLEVBQUMsQ0FBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLENBQUMsQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLENBQUMsRUFBQyxDQUFDLE1BQU0sRUFBQyxNQUFNLEVBQUMsTUFBTSxFQUFDLE1BQU0sRUFBQyxLQUFLLEVBQUMsTUFBTSxFQUFDLE1BQU0sRUFBQyxNQUFNLEVBQUMsTUFBTSxFQUFDLE1BQU0sRUFBQyxNQUFNLEVBQUMsTUFBTSxDQUFDLEVBQUMsQ0FBQyxRQUFRLEVBQUMsU0FBUyxFQUFDLE1BQU0sRUFBQyxRQUFRLEVBQUMsS0FBSyxFQUFDLE1BQU0sRUFBQyxNQUFNLEVBQUMsUUFBUSxFQUFDLFVBQVUsRUFBQyxTQUFTLEVBQUMsUUFBUSxFQUFDLFNBQVMsQ0FBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxDQUFDLEVBQUMsQ0FBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssQ0FBQyxFQUFDLENBQUMsUUFBUSxFQUFDLFNBQVMsRUFBQyxNQUFNLEVBQUMsUUFBUSxFQUFDLEtBQUssRUFBQyxNQUFNLEVBQUMsTUFBTSxFQUFDLFFBQVEsRUFBQyxVQUFVLEVBQUMsU0FBUyxFQUFDLFFBQVEsRUFBQyxTQUFTLENBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQyxRQUFRLEVBQUMsTUFBTSxDQUFDLEVBQUMsQ0FBQyxFQUFDLENBQUMsc0JBQXNCLEVBQUMsY0FBYyxDQUFDLENBQUMsRUFBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDLEVBQUMsQ0FBQyxRQUFRLEVBQUMsZUFBZSxFQUFDLGdCQUFnQixFQUFDLHNCQUFzQixDQUFDLEVBQUMsQ0FBQyxPQUFPLEVBQUMsVUFBVSxFQUFDLFlBQVksRUFBQyxlQUFlLENBQUMsRUFBQyxDQUFDLFNBQVMsRUFBQyxDQUFDLEVBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQyxFQUFDLENBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLFVBQVUsRUFBQyxHQUFHLENBQUMsRUFBQyxDQUFDLFdBQVcsRUFBQyxRQUFRLEVBQUMsWUFBWSxFQUFDLEtBQUssQ0FBQyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsaUJBQWlCLEVBQUMsRUFBQyxLQUFLLEVBQUMsQ0FBQyxDQUFDLEVBQUMsR0FBRyxDQUFDLEVBQUMsS0FBSyxFQUFDLENBQUMsQ0FBQyxFQUFDLEtBQUssQ0FBQyxFQUFDLEtBQUssRUFBQyxDQUFDLENBQUMsRUFBQyxJQUFJLENBQUMsRUFBQyxLQUFLLEVBQUMsQ0FBQyxDQUFDLEVBQUMsSUFBSSxDQUFDLEVBQUMsS0FBSyxFQUFDLENBQUMsQ0FBQyxFQUFDLElBQUksQ0FBQyxFQUFDLEtBQUssRUFBQyxDQUFDLENBQUMsRUFBQyxLQUFLLENBQUMsRUFBQyxLQUFLLEVBQUMsQ0FBQyxDQUFDLEVBQUMsSUFBSSxDQUFDLEVBQUMsS0FBSyxFQUFDLENBQUMsQ0FBQyxFQUFDLEtBQUssQ0FBQyxFQUFDLEtBQUssRUFBQyxDQUFDLENBQUMsRUFBQyxJQUFJLENBQUMsRUFBQyxLQUFLLEVBQUMsQ0FBQyxDQUFDLEVBQUMsR0FBRyxDQUFDLEVBQUMsS0FBSyxFQUFDLENBQUMsQ0FBQyxFQUFDLEtBQUssQ0FBQyxFQUFDLEtBQUssRUFBQyxDQUFDLENBQUMsRUFBQyxJQUFJLENBQUMsRUFBQyxLQUFLLEVBQUMsQ0FBQyxDQUFDLEVBQUMsR0FBRyxDQUFDLEVBQUMsS0FBSyxFQUFDLENBQUMsQ0FBQyxFQUFDLEdBQUcsQ0FBQyxFQUFDLEtBQUssRUFBQyxDQUFDLENBQUMsRUFBQyxJQUFJLENBQUMsRUFBQyxLQUFLLEVBQUMsQ0FBQyxLQUFLLEVBQUMsR0FBRyxDQUFDLEVBQUMsS0FBSyxFQUFDLENBQUMsS0FBSyxDQUFDLEVBQUMsS0FBSyxFQUFDLENBQUMsQ0FBQyxFQUFDLEdBQUcsQ0FBQyxFQUFDLEtBQUssRUFBQyxDQUFDLENBQUMsRUFBQyxHQUFHLENBQUMsRUFBQyxLQUFLLEVBQUMsQ0FBQyxDQUFDLEVBQUMsR0FBRyxDQUFDLEVBQUMsS0FBSyxFQUFDLENBQUMsQ0FBQyxFQUFDLEdBQUcsQ0FBQyxFQUFDLEtBQUssRUFBQyxDQUFDLEdBQUcsQ0FBQyxFQUFDLEtBQUssRUFBQyxDQUFDLENBQUMsRUFBQyxLQUFLLENBQUMsRUFBQyxLQUFLLEVBQUMsQ0FBQyxDQUFDLEVBQUMsS0FBSyxDQUFDLEVBQUMsS0FBSyxFQUFDLENBQUMsQ0FBQyxFQUFDLEdBQUcsQ0FBQyxFQUFDLEtBQUssRUFBQyxDQUFDLENBQUMsRUFBQyxHQUFHLENBQUMsRUFBQyxFQUFDLEtBQUssRUFBRSxNQUFNLENBQUMsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuXG4vLyBUSElTIENPREUgSVMgR0VORVJBVEVEIC0gRE8gTk9UIE1PRElGWS5cbmNvbnN0IHUgPSB1bmRlZmluZWQ7XG5cbmZ1bmN0aW9uIHBsdXJhbCh2YWw6IG51bWJlcik6IG51bWJlciB7XG5jb25zdCBuID0gdmFsO1xuXG5pZiAobiA9PT0gMSlcbiAgICByZXR1cm4gMTtcbnJldHVybiA1O1xufVxuXG5leHBvcnQgZGVmYXVsdCBbXCJreVwiLFtbXCLRgtKjXCIsXCLRgtC6XCJdLHUsW1wi0YLQsNKj0LrRi1wiLFwi0YLSr9GI0YLTqdC9INC60LjQudC40L3QutC4XCJdXSx1LFtbXCLQllwiLFwi0JRcIixcItCoXCIsXCLQqFwiLFwi0JFcIixcItCWXCIsXCLQmFwiXSxbXCLQttC10LouXCIsXCLQtNKv0LkuXCIsXCLRiNC10LnRiC5cIixcItGI0LDRgNGILlwiLFwi0LHQtdC50YguXCIsXCLQttGD0LzQsFwiLFwi0LjRiNC8LlwiXSxbXCLQttC10LrRiNC10LzQsdC4XCIsXCLQtNKv0LnRiNOp0LzQsdKvXCIsXCLRiNC10LnRiNC10LzQsdC4XCIsXCLRiNCw0YDRiNC10LzQsdC4XCIsXCLQsdC10LnRiNC10LzQsdC4XCIsXCLQttGD0LzQsFwiLFwi0LjRiNC10LzQsdC4XCJdLFtcItC20YguXCIsXCLQtNGILlwiLFwi0YjRiC5cIixcItGI0YAuXCIsXCLQsdGILlwiLFwi0LbQvC5cIixcItC40YguXCJdXSx1LFtbXCLQr1wiLFwi0KRcIixcItCcXCIsXCLQkFwiLFwi0JxcIixcItCYXCIsXCLQmFwiLFwi0JBcIixcItChXCIsXCLQnlwiLFwi0J1cIixcItCUXCJdLFtcItGP0L3Qsi5cIixcItGE0LXQsi5cIixcItC80LDRgC5cIixcItCw0L/RgC5cIixcItC80LDQuVwiLFwi0LjRjtC9LlwiLFwi0LjRjtC7LlwiLFwi0LDQstCzLlwiLFwi0YHQtdC9LlwiLFwi0L7QutGCLlwiLFwi0L3QvtGPLlwiLFwi0LTQtdC6LlwiXSxbXCLRj9C90LLQsNGA0YxcIixcItGE0LXQstGA0LDQu9GMXCIsXCLQvNCw0YDRglwiLFwi0LDQv9GA0LXQu9GMXCIsXCLQvNCw0LlcIixcItC40Y7QvdGMXCIsXCLQuNGO0LvRjFwiLFwi0LDQstCz0YPRgdGCXCIsXCLRgdC10L3RgtGP0LHRgNGMXCIsXCLQvtC60YLRj9Cx0YDRjFwiLFwi0L3QvtGP0LHRgNGMXCIsXCLQtNC10LrQsNCx0YDRjFwiXV0sW1tcItCvXCIsXCLQpFwiLFwi0JxcIixcItCQXCIsXCLQnFwiLFwi0JhcIixcItCYXCIsXCLQkFwiLFwi0KFcIixcItCeXCIsXCLQnVwiLFwi0JRcIl0sW1wi0K/QvdCyXCIsXCLQpNC10LJcIixcItCc0LDRgFwiLFwi0JDQv9GAXCIsXCLQnNCw0LlcIixcItCY0Y7QvVwiLFwi0JjRjtC7XCIsXCLQkNCy0LNcIixcItCh0LXQvVwiLFwi0J7QutGCXCIsXCLQndC+0Y9cIixcItCU0LXQulwiXSxbXCLQr9C90LLQsNGA0YxcIixcItCk0LXQstGA0LDQu9GMXCIsXCLQnNCw0YDRglwiLFwi0JDQv9GA0LXQu9GMXCIsXCLQnNCw0LlcIixcItCY0Y7QvdGMXCIsXCLQmNGO0LvRjFwiLFwi0JDQstCz0YPRgdGCXCIsXCLQodC10L3RgtGP0LHRgNGMXCIsXCLQntC60YLRj9Cx0YDRjFwiLFwi0J3QvtGP0LHRgNGMXCIsXCLQlNC10LrQsNCx0YDRjFwiXV0sW1tcItCxLtC3LtGHLlwiLFwi0LEu0LcuXCJdLHUsW1wi0LHQuNC30LTQuNC9INC30LDQvNCw0L3Qs9CwINGH0LXQudC40L1cIixcItCx0LjQt9C00LjQvSDQt9Cw0LzQsNC9XCJdXSwxLFs2LDBdLFtcImQvTS95eVwiLFwieS0n0LYnLiwgZC1NTU1cIixcInktJ9C2Jy4sIGQtTU1NTVwiLFwieS0n0LYnLiwgZC1NTU1NLCBFRUVFXCJdLFtcIkhIOm1tXCIsXCJISDptbTpzc1wiLFwiSEg6bW06c3MgelwiLFwiSEg6bW06c3Mgenp6elwiXSxbXCJ7MX0gezB9XCIsdSx1LHVdLFtcIixcIixcIsKgXCIsXCI7XCIsXCIlXCIsXCIrXCIsXCItXCIsXCJFXCIsXCLDl1wiLFwi4oCwXCIsXCLiiJ5cIixcItGB0LDQvcKg0Y3QvNC10YFcIixcIjpcIl0sW1wiIywjIzAuIyMjXCIsXCIjLCMjMCVcIixcIiMsIyMwLjAwwqDCpFwiLFwiI0UwXCJdLFwiS0dTXCIsXCLRgdC+0LxcIixcItCa0YvRgNCz0YvQt9GB0YLQsNC9INGB0L7QvNGDXCIse1wiQVVEXCI6W3UsXCIkXCJdLFwiQk1EXCI6W3UsXCJCRCRcIl0sXCJCUkxcIjpbdSxcIlIkXCJdLFwiQlNEXCI6W3UsXCJCJFwiXSxcIkJZTlwiOlt1LFwi0YAuXCJdLFwiQlpEXCI6W3UsXCJCWiRcIl0sXCJDQURcIjpbdSxcIkMkXCJdLFwiRE9QXCI6W3UsXCJSRCRcIl0sXCJFR1BcIjpbdSxcIkxFXCJdLFwiR0JQXCI6W3UsXCLCo1wiXSxcIkhLRFwiOlt1LFwiSEskXCJdLFwiSFJLXCI6W3UsXCJLblwiXSxcIklMU1wiOlt1LFwi4oKqXCJdLFwiSU5SXCI6W3UsXCLigrlcIl0sXCJKTURcIjpbdSxcIkokXCJdLFwiSlBZXCI6W1wiSlDCpVwiLFwiwqVcIl0sXCJLR1NcIjpbXCLRgdC+0LxcIl0sXCJLUldcIjpbdSxcIuKCqVwiXSxcIk1YTlwiOlt1LFwiJFwiXSxcIk5aRFwiOlt1LFwiJFwiXSxcIlBIUFwiOlt1LFwi4oKxXCJdLFwiVEhCXCI6W1wi4Li/XCJdLFwiVFREXCI6W3UsXCJUVCRcIl0sXCJUV0RcIjpbdSxcIk5UJFwiXSxcIlVTRFwiOlt1LFwiJFwiXSxcIlhDRFwiOlt1LFwiJFwiXX0sXCJsdHJcIiwgcGx1cmFsXTtcbiJdfQ==