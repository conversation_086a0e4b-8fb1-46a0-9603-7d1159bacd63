/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { Parser } from './parser';
import { getXmlTagDefinition } from './xml_tags';
export class XmlParser extends Parser {
    constructor() {
        super(getXmlTagDefinition);
    }
    parse(source, url, options = {}) {
        // Blocks aren't supported in an XML context.
        return super.parse(source, url, { ...options, tokenizeBlocks: false });
    }
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoieG1sX3BhcnNlci5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uLy4uL3BhY2thZ2VzL2NvbXBpbGVyL3NyYy9tbF9wYXJzZXIveG1sX3BhcnNlci50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7O0dBTUc7QUFHSCxPQUFPLEVBQUMsTUFBTSxFQUFrQixNQUFNLFVBQVUsQ0FBQztBQUNqRCxPQUFPLEVBQUMsbUJBQW1CLEVBQUMsTUFBTSxZQUFZLENBQUM7QUFFL0MsTUFBTSxPQUFPLFNBQVUsU0FBUSxNQUFNO0lBQ25DO1FBQ0UsS0FBSyxDQUFDLG1CQUFtQixDQUFDLENBQUM7SUFDN0IsQ0FBQztJQUVRLEtBQUssQ0FBQyxNQUFjLEVBQUUsR0FBVyxFQUFFLFVBQTJCLEVBQUU7UUFDdkUsNkNBQTZDO1FBQzdDLE9BQU8sS0FBSyxDQUFDLEtBQUssQ0FBQyxNQUFNLEVBQUUsR0FBRyxFQUFFLEVBQUMsR0FBRyxPQUFPLEVBQUUsY0FBYyxFQUFFLEtBQUssRUFBQyxDQUFDLENBQUM7SUFDdkUsQ0FBQztDQUNGIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5cbmltcG9ydCB7VG9rZW5pemVPcHRpb25zfSBmcm9tICcuL2xleGVyJztcbmltcG9ydCB7UGFyc2VyLCBQYXJzZVRyZWVSZXN1bHR9IGZyb20gJy4vcGFyc2VyJztcbmltcG9ydCB7Z2V0WG1sVGFnRGVmaW5pdGlvbn0gZnJvbSAnLi94bWxfdGFncyc7XG5cbmV4cG9ydCBjbGFzcyBYbWxQYXJzZXIgZXh0ZW5kcyBQYXJzZXIge1xuICBjb25zdHJ1Y3RvcigpIHtcbiAgICBzdXBlcihnZXRYbWxUYWdEZWZpbml0aW9uKTtcbiAgfVxuXG4gIG92ZXJyaWRlIHBhcnNlKHNvdXJjZTogc3RyaW5nLCB1cmw6IHN0cmluZywgb3B0aW9uczogVG9rZW5pemVPcHRpb25zID0ge30pOiBQYXJzZVRyZWVSZXN1bHQge1xuICAgIC8vIEJsb2NrcyBhcmVuJ3Qgc3VwcG9ydGVkIGluIGFuIFhNTCBjb250ZXh0LlxuICAgIHJldHVybiBzdXBlci5wYXJzZShzb3VyY2UsIHVybCwgey4uLm9wdGlvbnMsIHRva2VuaXplQmxvY2tzOiBmYWxzZX0pO1xuICB9XG59XG4iXX0=