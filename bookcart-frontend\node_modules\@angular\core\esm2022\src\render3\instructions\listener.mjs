/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { setActiveConsumer } from '@angular/core/primitives/signals';
import { assertIndexInRange } from '../../util/assert';
import { isDirectiveHost } from '../interfaces/type_checks';
import { CLEANUP, CONTEXT, RENDERER } from '../interfaces/view';
import { assertTNodeType } from '../node_assert';
import { profiler } from '../profiler';
import { getCurrentDirectiveDef, getCurrentTNode, getLView, getTView } from '../state';
import { getComponentLViewByIndex, getNativeByTNode, unwrapRNode } from '../util/view_utils';
import { markViewDirty } from './mark_view_dirty';
import { getOrCreateLViewCleanup, getOrCreateTViewCleanup, handleError, loadComponentRenderer } from './shared';
/**
 * Adds an event listener to the current node.
 *
 * If an output exists on one of the node's directives, it also subscribes to the output
 * and saves the subscription for later cleanup.
 *
 * @param eventName Name of the event
 * @param listenerFn The function to be called when event emits
 * @param useCapture Whether or not to use capture in event listener - this argument is a reminder
 *     from the Renderer3 infrastructure and should be removed from the instruction arguments
 * @param eventTargetResolver Function that returns global target information in case this listener
 * should be attached to a global object like window, document or body
 *
 * @codeGenApi
 */
export function ɵɵlistener(eventName, listenerFn, useCapture, eventTargetResolver) {
    const lView = getLView();
    const tView = getTView();
    const tNode = getCurrentTNode();
    listenerInternal(tView, lView, lView[RENDERER], tNode, eventName, listenerFn, eventTargetResolver);
    return ɵɵlistener;
}
/**
 * Registers a synthetic host listener (e.g. `(@foo.start)`) on a component or directive.
 *
 * This instruction is for compatibility purposes and is designed to ensure that a
 * synthetic host listener (e.g. `@HostListener('@foo.start')`) properly gets rendered
 * in the component's renderer. Normally all host listeners are evaluated with the
 * parent component's renderer, but, in the case of animation @triggers, they need
 * to be evaluated with the sub component's renderer (because that's where the
 * animation triggers are defined).
 *
 * Do not use this instruction as a replacement for `listener`. This instruction
 * only exists to ensure compatibility with the ViewEngine's host binding behavior.
 *
 * @param eventName Name of the event
 * @param listenerFn The function to be called when event emits
 * @param useCapture Whether or not to use capture in event listener
 * @param eventTargetResolver Function that returns global target information in case this listener
 * should be attached to a global object like window, document or body
 *
 * @codeGenApi
 */
export function ɵɵsyntheticHostListener(eventName, listenerFn) {
    const tNode = getCurrentTNode();
    const lView = getLView();
    const tView = getTView();
    const currentDef = getCurrentDirectiveDef(tView.data);
    const renderer = loadComponentRenderer(currentDef, tNode, lView);
    listenerInternal(tView, lView, renderer, tNode, eventName, listenerFn);
    return ɵɵsyntheticHostListener;
}
/**
 * A utility function that checks if a given element has already an event handler registered for an
 * event with a specified name. The TView.cleanup data structure is used to find out which events
 * are registered for a given element.
 */
function findExistingListener(tView, lView, eventName, tNodeIdx) {
    const tCleanup = tView.cleanup;
    if (tCleanup != null) {
        for (let i = 0; i < tCleanup.length - 1; i += 2) {
            const cleanupEventName = tCleanup[i];
            if (cleanupEventName === eventName && tCleanup[i + 1] === tNodeIdx) {
                // We have found a matching event name on the same node but it might not have been
                // registered yet, so we must explicitly verify entries in the LView cleanup data
                // structures.
                const lCleanup = lView[CLEANUP];
                const listenerIdxInLCleanup = tCleanup[i + 2];
                return lCleanup.length > listenerIdxInLCleanup ? lCleanup[listenerIdxInLCleanup] : null;
            }
            // TView.cleanup can have a mix of 4-elements entries (for event handler cleanups) or
            // 2-element entries (for directive and queries destroy hooks). As such we can encounter
            // blocks of 4 or 2 items in the tView.cleanup and this is why we iterate over 2 elements
            // first and jump another 2 elements if we detect listeners cleanup (4 elements). Also check
            // documentation of TView.cleanup for more details of this data structure layout.
            if (typeof cleanupEventName === 'string') {
                i += 2;
            }
        }
    }
    return null;
}
export function listenerInternal(tView, lView, renderer, tNode, eventName, listenerFn, eventTargetResolver) {
    const isTNodeDirectiveHost = isDirectiveHost(tNode);
    const firstCreatePass = tView.firstCreatePass;
    const tCleanup = firstCreatePass && getOrCreateTViewCleanup(tView);
    const context = lView[CONTEXT];
    // When the ɵɵlistener instruction was generated and is executed we know that there is either a
    // native listener or a directive output on this element. As such we we know that we will have to
    // register a listener and store its cleanup function on LView.
    const lCleanup = getOrCreateLViewCleanup(lView);
    ngDevMode && assertTNodeType(tNode, 3 /* TNodeType.AnyRNode */ | 12 /* TNodeType.AnyContainer */);
    let processOutputs = true;
    // Adding a native event listener is applicable when:
    // - The corresponding TNode represents a DOM element.
    // - The event target has a resolver (usually resulting in a global object,
    //   such as `window` or `document`).
    if ((tNode.type & 3 /* TNodeType.AnyRNode */) || eventTargetResolver) {
        const native = getNativeByTNode(tNode, lView);
        const target = eventTargetResolver ? eventTargetResolver(native) : native;
        const lCleanupIndex = lCleanup.length;
        const idxOrTargetGetter = eventTargetResolver ?
            (_lView) => eventTargetResolver(unwrapRNode(_lView[tNode.index])) :
            tNode.index;
        // In order to match current behavior, native DOM event listeners must be added for all
        // events (including outputs).
        // There might be cases where multiple directives on the same element try to register an event
        // handler function for the same event. In this situation we want to avoid registration of
        // several native listeners as each registration would be intercepted by NgZone and
        // trigger change detection. This would mean that a single user action would result in several
        // change detections being invoked. To avoid this situation we want to have only one call to
        // native handler registration (for the same element and same type of event).
        //
        // In order to have just one native event handler in presence of multiple handler functions,
        // we just register a first handler function as a native event listener and then chain
        // (coalesce) other handler functions on top of the first native handler function.
        let existingListener = null;
        // Please note that the coalescing described here doesn't happen for events specifying an
        // alternative target (ex. (document:click)) - this is to keep backward compatibility with the
        // view engine.
        // Also, we don't have to search for existing listeners is there are no directives
        // matching on a given node as we can't register multiple event handlers for the same event in
        // a template (this would mean having duplicate attributes).
        if (!eventTargetResolver && isTNodeDirectiveHost) {
            existingListener = findExistingListener(tView, lView, eventName, tNode.index);
        }
        if (existingListener !== null) {
            // Attach a new listener to coalesced listeners list, maintaining the order in which
            // listeners are registered. For performance reasons, we keep a reference to the last
            // listener in that list (in `__ngLastListenerFn__` field), so we can avoid going through
            // the entire set each time we need to add a new listener.
            const lastListenerFn = existingListener.__ngLastListenerFn__ || existingListener;
            lastListenerFn.__ngNextListenerFn__ = listenerFn;
            existingListener.__ngLastListenerFn__ = listenerFn;
            processOutputs = false;
        }
        else {
            listenerFn = wrapListener(tNode, lView, context, listenerFn, false /** preventDefault */);
            const cleanupFn = renderer.listen(target, eventName, listenerFn);
            ngDevMode && ngDevMode.rendererAddEventListener++;
            lCleanup.push(listenerFn, cleanupFn);
            tCleanup && tCleanup.push(eventName, idxOrTargetGetter, lCleanupIndex, lCleanupIndex + 1);
        }
    }
    else {
        // Even if there is no native listener to add, we still need to wrap the listener so that OnPush
        // ancestors are marked dirty when an event occurs.
        listenerFn = wrapListener(tNode, lView, context, listenerFn, false /** preventDefault */);
    }
    // subscribe to directive outputs
    const outputs = tNode.outputs;
    let props;
    if (processOutputs && outputs !== null && (props = outputs[eventName])) {
        const propsLength = props.length;
        if (propsLength) {
            for (let i = 0; i < propsLength; i += 2) {
                const index = props[i];
                ngDevMode && assertIndexInRange(lView, index);
                const minifiedName = props[i + 1];
                const directiveInstance = lView[index];
                const output = directiveInstance[minifiedName];
                if (ngDevMode && !isOutputSubscribable(output)) {
                    throw new Error(`@Output ${minifiedName} not initialized in '${directiveInstance.constructor.name}'.`);
                }
                const subscription = output.subscribe(listenerFn);
                const idx = lCleanup.length;
                lCleanup.push(listenerFn, subscription);
                tCleanup && tCleanup.push(eventName, tNode.index, idx, -(idx + 1));
            }
        }
    }
}
function executeListenerWithErrorHandling(lView, context, listenerFn, e) {
    const prevConsumer = setActiveConsumer(null);
    try {
        profiler(6 /* ProfilerEvent.OutputStart */, context, listenerFn);
        // Only explicitly returning false from a listener should preventDefault
        return listenerFn(e) !== false;
    }
    catch (error) {
        handleError(lView, error);
        return false;
    }
    finally {
        profiler(7 /* ProfilerEvent.OutputEnd */, context, listenerFn);
        setActiveConsumer(prevConsumer);
    }
}
/**
 * Wraps an event listener with a function that marks ancestors dirty and prevents default behavior,
 * if applicable.
 *
 * @param tNode The TNode associated with this listener
 * @param lView The LView that contains this listener
 * @param listenerFn The listener function to call
 * @param wrapWithPreventDefault Whether or not to prevent default behavior
 * (the procedural renderer does this already, so in those cases, we should skip)
 */
function wrapListener(tNode, lView, context, listenerFn, wrapWithPreventDefault) {
    // Note: we are performing most of the work in the listener function itself
    // to optimize listener registration.
    return function wrapListenerIn_markDirtyAndPreventDefault(e) {
        // Ivy uses `Function` as a special token that allows us to unwrap the function
        // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`.
        if (e === Function) {
            return listenerFn;
        }
        // In order to be backwards compatible with View Engine, events on component host nodes
        // must also mark the component view itself dirty (i.e. the view that it owns).
        const startView = tNode.componentOffset > -1 ? getComponentLViewByIndex(tNode.index, lView) : lView;
        markViewDirty(startView);
        let result = executeListenerWithErrorHandling(lView, context, listenerFn, e);
        // A just-invoked listener function might have coalesced listeners so we need to check for
        // their presence and invoke as needed.
        let nextListenerFn = wrapListenerIn_markDirtyAndPreventDefault.__ngNextListenerFn__;
        while (nextListenerFn) {
            // We should prevent default if any of the listeners explicitly return false
            result = executeListenerWithErrorHandling(lView, context, nextListenerFn, e) && result;
            nextListenerFn = nextListenerFn.__ngNextListenerFn__;
        }
        if (wrapWithPreventDefault && result === false) {
            e.preventDefault();
        }
        return result;
    };
}
/**
 * Whether the given value represents a subscribable output.
 *
 * For example, an `EventEmitter, a `Subject`, an `Observable` or an
 * `OutputEmitter`.
 */
function isOutputSubscribable(value) {
    return value != null &&
        typeof value.subscribe === 'function';
}
//# sourceMappingURL=data:application/json;base64,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