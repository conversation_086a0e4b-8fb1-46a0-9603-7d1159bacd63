/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

if (n === 1)
    return 1;
return 5;
}
    global.ng.common.locales['sn'] = ["sn",[["a","p"],["AM","PM"],u],[["AM","PM"],u,u],[["S","M","C","C","C","C","M"],["Svo","Muv","Chp","Cht","Chn","Chs","Mug"],["<PERSON>von<PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","China","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>"],["Sv","Mu","Cp","Ct","Cn","Cs","Mg"]],u,[["N","K","K","K","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","M","Z"],["Ndi","<PERSON>k","<PERSON>r","<PERSON>b","Chv","Chk","Chg","Nya","<PERSON>","<PERSON>um","<PERSON>bu","<PERSON>vi"],["<PERSON><PERSON>","<PERSON>kadzi","<PERSON>rume","<PERSON>bvumbi","<PERSON>vabvu","Chikumi","Chikunguru","Nyamavhuvhu","Gunyana","Gumiguru","Mbudzi","Zvita"]],u,[["BC","AD"],u,["Kristo asati auya","mugore ramambo vedu"]],0,[6,0],["y-MM-dd","y MMM d","y MMMM d","y MMMM d, EEEE"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤#,##0.00","#E0"],"USD","US$","Dora re Amerika",{"JPY":["JP¥","¥"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    