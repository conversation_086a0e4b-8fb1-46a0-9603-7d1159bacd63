{"version": 3, "file": "menu.mjs", "sources": ["../../../../../../src/cdk/menu/menu-group.ts", "../../../../../../src/cdk/menu/menu-interface.ts", "../../../../../../src/cdk/menu/menu-stack.ts", "../../../../../../src/cdk/menu/menu-trigger-base.ts", "../../../../../../src/cdk/menu/menu-errors.ts", "../../../../../../src/cdk/menu/menu-aim.ts", "../../../../../../src/cdk/menu/event-detection.ts", "../../../../../../src/cdk/menu/menu-trigger.ts", "../../../../../../src/cdk/menu/menu-item.ts", "../../../../../../src/cdk/menu/pointer-focus-tracker.ts", "../../../../../../src/cdk/menu/menu-base.ts", "../../../../../../src/cdk/menu/menu.ts", "../../../../../../src/cdk/menu/menu-bar.ts", "../../../../../../src/cdk/menu/menu-item-selectable.ts", "../../../../../../src/cdk/menu/menu-item-radio.ts", "../../../../../../src/cdk/menu/menu-item-checkbox.ts", "../../../../../../src/cdk/menu/context-menu-trigger.ts", "../../../../../../src/cdk/menu/menu-module.ts", "../../../../../../src/cdk/menu/menu_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive} from '@angular/core';\nimport {UniqueSelectionDispatcher} from '@angular/cdk/collections';\n\n/**\n * A grouping container for `CdkMenuItemRadio` instances, similar to a `role=\"radiogroup\"` element.\n */\n@Directive({\n  selector: '[cdkMenuGroup]',\n  exportAs: 'cdkMenuGroup',\n  standalone: true,\n  host: {\n    'role': 'group',\n    'class': 'cdk-menu-group',\n  },\n  providers: [{provide: UniqueSelectionDispatcher, useClass: UniqueSelectionDispatcher}],\n})\nexport class CdkMenuGroup {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {InjectionToken} from '@angular/core';\nimport {MenuStackItem} from './menu-stack';\nimport {FocusOrigin} from '@angular/cdk/a11y';\n\n/** Injection token used to return classes implementing the Menu interface */\nexport const CDK_MENU = new InjectionToken<Menu>('cdk-menu');\n\n/** Interface which specifies Menu operations and used to break circular dependency issues */\nexport interface Menu extends MenuStackItem {\n  /** The id of the menu's host element. */\n  id: string;\n\n  /** The menu's native DOM host element. */\n  nativeElement: HTMLElement;\n\n  /** The direction items in the menu flow. */\n  readonly orientation: 'horizontal' | 'vertical';\n\n  /** Place focus on the first MenuItem in the menu. */\n  focusFirstItem(focusOrigin: FocusOrigin): void;\n\n  /** Place focus on the last MenuItem in the menu. */\n  focusLastItem(focusOrigin: FocusOrigin): void;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Inject, Injectable, InjectionToken, Optional, SkipSelf} from '@angular/core';\nimport {Observable, Subject} from 'rxjs';\nimport {debounceTime, distinctUntilChanged, startWith} from 'rxjs/operators';\n\n/** The relative item in the inline menu to focus after closing all popup menus. */\nexport enum FocusNext {\n  nextItem,\n  previousItem,\n  currentItem,\n}\n\n/** A single item (menu) in the menu stack. */\nexport interface MenuStackItem {\n  /** A reference to the menu stack this menu stack item belongs to. */\n  menuStack?: MenuStack;\n}\n\n/** Injection token used for an implementation of MenuStack. */\nexport const MENU_STACK = new InjectionToken<MenuStack>('cdk-menu-stack');\n\n/** Provider that provides the parent menu stack, or a new menu stack if there is no parent one. */\nexport const PARENT_OR_NEW_MENU_STACK_PROVIDER = {\n  provide: MENU_STACK,\n  deps: [[new Optional(), new SkipSelf(), new Inject(MENU_STACK)]],\n  useFactory: (parentMenuStack?: MenuStack) => parentMenuStack || new MenuStack(),\n};\n\n/** Provider that provides the parent menu stack, or a new inline menu stack if there is no parent one. */\nexport const PARENT_OR_NEW_INLINE_MENU_STACK_PROVIDER = (\n  orientation: 'vertical' | 'horizontal',\n) => ({\n  provide: MENU_STACK,\n  deps: [[new Optional(), new SkipSelf(), new Inject(MENU_STACK)]],\n  useFactory: (parentMenuStack?: MenuStack) => parentMenuStack || MenuStack.inline(orientation),\n});\n\n/** Options that can be provided to the close or closeAll methods. */\nexport interface CloseOptions {\n  /** The element to focus next if the close operation causes the menu stack to become empty. */\n  focusNextOnEmpty?: FocusNext;\n  /** Whether to focus the parent trigger after closing the menu. */\n  focusParentTrigger?: boolean;\n}\n\n/** Event dispatched when a menu is closed. */\nexport interface MenuStackCloseEvent {\n  /** The menu being closed. */\n  item: MenuStackItem;\n  /** Whether to focus the parent trigger after closing the menu. */\n  focusParentTrigger?: boolean;\n}\n\n/** The next available menu stack ID. */\nlet nextId = 0;\n\n/**\n * MenuStack allows subscribers to listen for close events (when a MenuStackItem is popped off\n * of the stack) in order to perform closing actions. Upon the MenuStack being empty it emits\n * from the `empty` observable specifying the next focus action which the listener should perform\n * as requested by the closer.\n */\n@Injectable()\nexport class MenuStack {\n  /** The ID of this menu stack. */\n  readonly id = `${nextId++}`;\n\n  /** All MenuStackItems tracked by this MenuStack. */\n  private readonly _elements: MenuStackItem[] = [];\n\n  /** Emits the element which was popped off of the stack when requested by a closer. */\n  private readonly _close = new Subject<MenuStackCloseEvent>();\n\n  /** Emits once the MenuStack has become empty after popping off elements. */\n  private readonly _empty = new Subject<FocusNext | undefined>();\n\n  /** Emits whether any menu in the menu stack has focus. */\n  private readonly _hasFocus = new Subject<boolean>();\n\n  /** Observable which emits the MenuStackItem which has been requested to close. */\n  readonly closed: Observable<MenuStackCloseEvent> = this._close;\n\n  /** Observable which emits whether any menu in the menu stack has focus. */\n  readonly hasFocus: Observable<boolean> = this._hasFocus.pipe(\n    startWith(false),\n    debounceTime(0),\n    distinctUntilChanged(),\n  );\n\n  /**\n   * Observable which emits when the MenuStack is empty after popping off the last element. It\n   * emits a FocusNext event which specifies the action the closer has requested the listener\n   * perform.\n   */\n  readonly emptied: Observable<FocusNext | undefined> = this._empty;\n\n  /**\n   * Whether the inline menu associated with this menu stack is vertical or horizontal.\n   * `null` indicates there is no inline menu associated with this menu stack.\n   */\n  private _inlineMenuOrientation: 'vertical' | 'horizontal' | null = null;\n\n  /** Creates a menu stack that originates from an inline menu. */\n  static inline(orientation: 'vertical' | 'horizontal') {\n    const stack = new MenuStack();\n    stack._inlineMenuOrientation = orientation;\n    return stack;\n  }\n\n  /**\n   * Adds an item to the menu stack.\n   * @param menu the MenuStackItem to put on the stack.\n   */\n  push(menu: MenuStackItem) {\n    this._elements.push(menu);\n  }\n\n  /**\n   * Pop items off of the stack up to and including `lastItem` and emit each on the close\n   * observable. If the stack is empty or `lastItem` is not on the stack it does nothing.\n   * @param lastItem the last item to pop off the stack.\n   * @param options Options that configure behavior on close.\n   */\n  close(lastItem: MenuStackItem, options?: CloseOptions) {\n    const {focusNextOnEmpty, focusParentTrigger} = {...options};\n    if (this._elements.indexOf(lastItem) >= 0) {\n      let poppedElement;\n      do {\n        poppedElement = this._elements.pop()!;\n        this._close.next({item: poppedElement, focusParentTrigger});\n      } while (poppedElement !== lastItem);\n\n      if (this.isEmpty()) {\n        this._empty.next(focusNextOnEmpty);\n      }\n    }\n  }\n\n  /**\n   * Pop items off of the stack up to but excluding `lastItem` and emit each on the close\n   * observable. If the stack is empty or `lastItem` is not on the stack it does nothing.\n   * @param lastItem the element which should be left on the stack\n   * @return whether or not an item was removed from the stack\n   */\n  closeSubMenuOf(lastItem: MenuStackItem) {\n    let removed = false;\n    if (this._elements.indexOf(lastItem) >= 0) {\n      removed = this.peek() !== lastItem;\n      while (this.peek() !== lastItem) {\n        this._close.next({item: this._elements.pop()!});\n      }\n    }\n    return removed;\n  }\n\n  /**\n   * Pop off all MenuStackItems and emit each one on the `close` observable one by one.\n   * @param options Options that configure behavior on close.\n   */\n  closeAll(options?: CloseOptions) {\n    const {focusNextOnEmpty, focusParentTrigger} = {...options};\n    if (!this.isEmpty()) {\n      while (!this.isEmpty()) {\n        const menuStackItem = this._elements.pop();\n        if (menuStackItem) {\n          this._close.next({item: menuStackItem, focusParentTrigger});\n        }\n      }\n      this._empty.next(focusNextOnEmpty);\n    }\n  }\n\n  /** Return true if this stack is empty. */\n  isEmpty() {\n    return !this._elements.length;\n  }\n\n  /** Return the length of the stack. */\n  length() {\n    return this._elements.length;\n  }\n\n  /** Get the top most element on the stack. */\n  peek(): MenuStackItem | undefined {\n    return this._elements[this._elements.length - 1];\n  }\n\n  /** Whether the menu stack is associated with an inline menu. */\n  hasInlineMenu() {\n    return this._inlineMenuOrientation != null;\n  }\n\n  /** The orientation of the associated inline menu. */\n  inlineMenuOrientation() {\n    return this._inlineMenuOrientation;\n  }\n\n  /** Sets whether the menu stack contains the focused element. */\n  setHasFocus(hasFocus: boolean) {\n    this._hasFocus.next(hasFocus);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Directive,\n  EventEmitter,\n  inject,\n  InjectionToken,\n  Injector,\n  OnDestroy,\n  TemplateRef,\n  ViewContainerRef,\n} from '@angular/core';\nimport {Menu} from './menu-interface';\nimport {MENU_STACK, MenuStack} from './menu-stack';\nimport {ConnectedPosition, Overlay, OverlayRef, ScrollStrategy} from '@angular/cdk/overlay';\nimport {TemplatePortal} from '@angular/cdk/portal';\nimport {merge, Subject} from 'rxjs';\n\n/** Injection token used for an implementation of MenuStack. */\nexport const MENU_TRIGGER = new InjectionToken<CdkMenuTriggerBase>('cdk-menu-trigger');\n\n/** Injection token used to configure the behavior of the menu when the page is scrolled. */\nexport const MENU_SCROLL_STRATEGY = new InjectionToken<() => ScrollStrategy>(\n  'cdk-menu-scroll-strategy',\n  {\n    providedIn: 'root',\n    factory: () => {\n      const overlay = inject(Overlay);\n      return () => overlay.scrollStrategies.reposition();\n    },\n  },\n);\n\n/**\n * Abstract directive that implements shared logic common to all menu triggers.\n * This class can be extended to create custom menu trigger types.\n */\n@Directive({\n  host: {\n    '[attr.aria-controls]': 'childMenu?.id',\n    '[attr.data-cdk-menu-stack-id]': 'menuStack.id',\n  },\n  standalone: true,\n})\nexport abstract class CdkMenuTriggerBase implements OnDestroy {\n  /** The DI injector for this component. */\n  readonly injector = inject(Injector);\n\n  /** The view container ref for this component */\n  protected readonly viewContainerRef = inject(ViewContainerRef);\n\n  /** The menu stack in which this menu resides. */\n  protected readonly menuStack: MenuStack = inject(MENU_STACK);\n\n  /** Function used to configure the scroll strategy for the menu. */\n  protected readonly menuScrollStrategy = inject(MENU_SCROLL_STRATEGY);\n\n  /**\n   * A list of preferred menu positions to be used when constructing the\n   * `FlexibleConnectedPositionStrategy` for this trigger's menu.\n   */\n  menuPosition: ConnectedPosition[];\n\n  /** Emits when the attached menu is requested to open */\n  readonly opened: EventEmitter<void> = new EventEmitter();\n\n  /** Emits when the attached menu is requested to close */\n  readonly closed: EventEmitter<void> = new EventEmitter();\n\n  /** Template reference variable to the menu this trigger opens */\n  menuTemplateRef: TemplateRef<unknown> | null;\n\n  /** Context data to be passed along to the menu template */\n  menuData: unknown;\n\n  /** A reference to the overlay which manages the triggered menu */\n  protected overlayRef: OverlayRef | null = null;\n\n  /** Emits when this trigger is destroyed. */\n  protected readonly destroyed: Subject<void> = new Subject();\n\n  /** Emits when the outside pointer events listener on the overlay should be stopped. */\n  protected readonly stopOutsideClicksListener = merge(this.closed, this.destroyed);\n\n  /** The child menu opened by this trigger. */\n  protected childMenu?: Menu;\n\n  /** The content of the menu panel opened by this trigger. */\n  private _menuPortal: TemplatePortal;\n\n  /** The injector to use for the child menu opened by this trigger. */\n  private _childMenuInjector?: Injector;\n\n  ngOnDestroy() {\n    this._destroyOverlay();\n\n    this.destroyed.next();\n    this.destroyed.complete();\n  }\n\n  /** Whether the attached menu is open. */\n  isOpen() {\n    return !!this.overlayRef?.hasAttached();\n  }\n\n  /** Registers a child menu as having been opened by this trigger. */\n  registerChildMenu(child: Menu) {\n    this.childMenu = child;\n  }\n\n  /**\n   * Get the portal to be attached to the overlay which contains the menu. Allows for the menu\n   * content to change dynamically and be reflected in the application.\n   */\n  protected getMenuContentPortal() {\n    const hasMenuContentChanged = this.menuTemplateRef !== this._menuPortal?.templateRef;\n    if (this.menuTemplateRef && (!this._menuPortal || hasMenuContentChanged)) {\n      this._menuPortal = new TemplatePortal(\n        this.menuTemplateRef,\n        this.viewContainerRef,\n        this.menuData,\n        this._getChildMenuInjector(),\n      );\n    }\n\n    return this._menuPortal;\n  }\n\n  /**\n   * Whether the given element is inside the scope of this trigger's menu stack.\n   * @param element The element to check.\n   * @return Whether the element is inside the scope of this trigger's menu stack.\n   */\n  protected isElementInsideMenuStack(element: Element) {\n    for (let el: Element | null = element; el; el = el?.parentElement ?? null) {\n      if (el.getAttribute('data-cdk-menu-stack-id') === this.menuStack.id) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  /** Destroy and unset the overlay reference it if exists */\n  private _destroyOverlay() {\n    if (this.overlayRef) {\n      this.overlayRef.dispose();\n      this.overlayRef = null;\n    }\n  }\n\n  /** Gets the injector to use when creating a child menu. */\n  private _getChildMenuInjector() {\n    this._childMenuInjector =\n      this._childMenuInjector ||\n      Injector.create({\n        providers: [\n          {provide: MENU_TRIGGER, useValue: this},\n          {provide: MENU_STACK, useValue: this.menuStack},\n        ],\n        parent: this.injector,\n      });\n    return this._childMenuInjector;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Throws an exception when an instance of the PointerFocusTracker is not provided.\n * @docs-private\n */\nexport function throwMissingPointerFocusTracker() {\n  throw Error('expected an instance of PointerFocusTracker to be provided');\n}\n\n/**\n * Throws an exception when a reference to the parent menu is not provided.\n * @docs-private\n */\nexport function throwMissingMenuReference() {\n  throw Error('expected a reference to the parent menu');\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, inject, Injectable, InjectionToken, NgZone, OnDestroy} from '@angular/core';\nimport {fromEvent, Subject} from 'rxjs';\nimport {filter, takeUntil} from 'rxjs/operators';\nimport {FocusableElement, PointerFocusTracker} from './pointer-focus-tracker';\nimport {Menu} from './menu-interface';\nimport {throwMissingMenuReference, throwMissingPointerFocusTracker} from './menu-errors';\n\n/**\n * MenuAim is responsible for determining if a sibling menuitem's menu should be closed when a\n * Toggler item is hovered into. It is up to the hovered in item to call the MenuAim service in\n * order to determine if it may perform its close actions.\n */\nexport interface MenuAim {\n  /**\n   * Set the Menu and its PointerFocusTracker.\n   * @param menu The menu that this menu aim service controls.\n   * @param pointerTracker The `PointerFocusTracker` for the given menu.\n   */\n  initialize(menu: Menu, pointerTracker: PointerFocusTracker<FocusableElement & Toggler>): void;\n\n  /**\n   * Calls the `doToggle` callback when it is deemed that the user is not moving towards\n   * the submenu.\n   * @param doToggle the function called when the user is not moving towards the submenu.\n   */\n  toggle(doToggle: () => void): void;\n}\n\n/** Injection token used for an implementation of MenuAim. */\nexport const MENU_AIM = new InjectionToken<MenuAim>('cdk-menu-aim');\n\n/** Capture every nth mouse move event. */\nconst MOUSE_MOVE_SAMPLE_FREQUENCY = 3;\n\n/** The number of mouse move events to track. */\nconst NUM_POINTS = 5;\n\n/**\n * How long to wait before closing a sibling menu if a user stops short of the submenu they were\n * predicted to go into.\n */\nconst CLOSE_DELAY = 300;\n\n/** An element which when hovered over may open or close a menu. */\nexport interface Toggler {\n  /** Gets the open menu, or undefined if no menu is open. */\n  getMenu(): Menu | undefined;\n}\n\n/** Calculate the slope between point a and b. */\nfunction getSlope(a: Point, b: Point) {\n  return (b.y - a.y) / (b.x - a.x);\n}\n\n/** Calculate the y intercept for the given point and slope. */\nfunction getYIntercept(point: Point, slope: number) {\n  return point.y - slope * point.x;\n}\n\n/** Represents a coordinate of mouse travel. */\ntype Point = {x: number; y: number};\n\n/**\n * Whether the given mouse trajectory line defined by the slope and y intercept falls within the\n * submenu as defined by `submenuPoints`\n * @param submenuPoints the submenu DOMRect points.\n * @param m the slope of the trajectory line.\n * @param b the y intercept of the trajectory line.\n * @return true if any point on the line falls within the submenu.\n */\nfunction isWithinSubmenu(submenuPoints: DOMRect, m: number, b: number) {\n  const {left, right, top, bottom} = submenuPoints;\n\n  // Check for intersection with each edge of the submenu (left, right, top, bottom)\n  // by fixing one coordinate to that edge's coordinate (either x or y) and checking if the\n  // other coordinate is within bounds.\n  return (\n    (m * left + b >= top && m * left + b <= bottom) ||\n    (m * right + b >= top && m * right + b <= bottom) ||\n    ((top - b) / m >= left && (top - b) / m <= right) ||\n    ((bottom - b) / m >= left && (bottom - b) / m <= right)\n  );\n}\n\n/**\n * TargetMenuAim predicts if a user is moving into a submenu. It calculates the\n * trajectory of the user's mouse movement in the current menu to determine if the\n * mouse is moving towards an open submenu.\n *\n * The determination is made by calculating the slope of the users last NUM_POINTS moves where each\n * pair of points determines if the trajectory line points into the submenu. It uses consensus\n * approach by checking if at least NUM_POINTS / 2 pairs determine that the user is moving towards\n * to submenu.\n */\n@Injectable()\nexport class TargetMenuAim implements MenuAim, OnDestroy {\n  /** The Angular zone. */\n  private readonly _ngZone = inject(NgZone);\n\n  /** The last NUM_POINTS mouse move events. */\n  private readonly _points: Point[] = [];\n\n  /** Reference to the root menu in which we are tracking mouse moves. */\n  private _menu: Menu;\n\n  /** Reference to the root menu's mouse manager. */\n  private _pointerTracker: PointerFocusTracker<Toggler & FocusableElement>;\n\n  /** The id associated with the current timeout call waiting to resolve. */\n  private _timeoutId: number | null;\n\n  /** Emits when this service is destroyed. */\n  private readonly _destroyed: Subject<void> = new Subject();\n\n  ngOnDestroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n\n  /**\n   * Set the Menu and its PointerFocusTracker.\n   * @param menu The menu that this menu aim service controls.\n   * @param pointerTracker The `PointerFocusTracker` for the given menu.\n   */\n  initialize(menu: Menu, pointerTracker: PointerFocusTracker<FocusableElement & Toggler>) {\n    this._menu = menu;\n    this._pointerTracker = pointerTracker;\n    this._subscribeToMouseMoves();\n  }\n\n  /**\n   * Calls the `doToggle` callback when it is deemed that the user is not moving towards\n   * the submenu.\n   * @param doToggle the function called when the user is not moving towards the submenu.\n   */\n  toggle(doToggle: () => void) {\n    // If the menu is horizontal the sub-menus open below and there is no risk of premature\n    // closing of any sub-menus therefore we automatically resolve the callback.\n    if (this._menu.orientation === 'horizontal') {\n      doToggle();\n    }\n\n    this._checkConfigured();\n\n    const siblingItemIsWaiting = !!this._timeoutId;\n    const hasPoints = this._points.length > 1;\n\n    if (hasPoints && !siblingItemIsWaiting) {\n      if (this._isMovingToSubmenu()) {\n        this._startTimeout(doToggle);\n      } else {\n        doToggle();\n      }\n    } else if (!siblingItemIsWaiting) {\n      doToggle();\n    }\n  }\n\n  /**\n   * Start the delayed toggle handler if one isn't running already.\n   *\n   * The delayed toggle handler executes the `doToggle` callback after some period of time iff the\n   * users mouse is on an item in the current menu.\n   *\n   * @param doToggle the function called when the user is not moving towards the submenu.\n   */\n  private _startTimeout(doToggle: () => void) {\n    // If the users mouse is moving towards a submenu we don't want to immediately resolve.\n    // Wait for some period of time before determining if the previous menu should close in\n    // cases where the user may have moved towards the submenu but stopped on a sibling menu\n    // item intentionally.\n    const timeoutId = setTimeout(() => {\n      // Resolve if the user is currently moused over some element in the root menu\n      if (this._pointerTracker!.activeElement && timeoutId === this._timeoutId) {\n        doToggle();\n      }\n      this._timeoutId = null;\n    }, CLOSE_DELAY) as any as number;\n\n    this._timeoutId = timeoutId;\n  }\n\n  /** Whether the user is heading towards the open submenu. */\n  private _isMovingToSubmenu() {\n    const submenuPoints = this._getSubmenuBounds();\n    if (!submenuPoints) {\n      return false;\n    }\n\n    let numMoving = 0;\n    const currPoint = this._points[this._points.length - 1];\n    // start from the second last point and calculate the slope between each point and the last\n    // point.\n    for (let i = this._points.length - 2; i >= 0; i--) {\n      const previous = this._points[i];\n      const slope = getSlope(currPoint, previous);\n      if (isWithinSubmenu(submenuPoints, slope, getYIntercept(currPoint, slope))) {\n        numMoving++;\n      }\n    }\n    return numMoving >= Math.floor(NUM_POINTS / 2);\n  }\n\n  /** Get the bounding DOMRect for the open submenu. */\n  private _getSubmenuBounds(): DOMRect | undefined {\n    return this._pointerTracker?.previousElement?.getMenu()?.nativeElement.getBoundingClientRect();\n  }\n\n  /**\n   * Check if a reference to the PointerFocusTracker and menu element is provided.\n   * @throws an error if neither reference is provided.\n   */\n  private _checkConfigured() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this._pointerTracker) {\n        throwMissingPointerFocusTracker();\n      }\n      if (!this._menu) {\n        throwMissingMenuReference();\n      }\n    }\n  }\n\n  /** Subscribe to the root menus mouse move events and update the tracked mouse points. */\n  private _subscribeToMouseMoves() {\n    this._ngZone.runOutsideAngular(() => {\n      fromEvent<MouseEvent>(this._menu.nativeElement, 'mousemove')\n        .pipe(\n          filter((_: MouseEvent, index: number) => index % MOUSE_MOVE_SAMPLE_FREQUENCY === 0),\n          takeUntil(this._destroyed),\n        )\n        .subscribe((event: MouseEvent) => {\n          this._points.push({x: event.clientX, y: event.clientY});\n          if (this._points.length > NUM_POINTS) {\n            this._points.shift();\n          }\n        });\n    });\n  }\n}\n\n/**\n * CdkTargetMenuAim is a provider for the TargetMenuAim service. It can be added to an\n * element with either the `cdkMenu` or `cdkMenuBar` directive and child menu items.\n */\n@Directive({\n  selector: '[cdkTargetMenuAim]',\n  exportAs: 'cdkTargetMenuAim',\n  standalone: true,\n  providers: [{provide: MENU_AIM, useClass: TargetMenuAim}],\n})\nexport class CdkTargetMenuAim {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ElementRef} from '@angular/core';\nimport {ENTER, SPACE} from '@angular/cdk/keycodes';\n\n/** Checks whether a keyboard event will trigger a native `click` event on an element. */\nexport function eventDispatchesNativeClick(\n  elementRef: ElementRef<HTMLElement>,\n  event: KeyboardEvent,\n): boolean {\n  // Synthetic events won't trigger clicks.\n  if (!event.isTrusted) {\n    return false;\n  }\n\n  const el = elementRef.nativeElement;\n  const keyCode = event.keyCode;\n\n  // Buttons trigger clicks both on space and enter events.\n  if (el.nodeName === 'BUTTON' && !(el as HTMLButtonElement).disabled) {\n    return keyCode === ENTER || keyCode === SPACE;\n  }\n\n  // Links only trigger clicks on enter.\n  if (el.nodeName === 'A') {\n    return keyCode === ENTER;\n  }\n\n  // Any other elements won't dispatch clicks from keyboard events.\n  return false;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ChangeDetectorRef, Directive, ElementRef, inject, Ng<PERSON>one, OnDestroy} from '@angular/core';\nimport {InputModalityDetector} from '@angular/cdk/a11y';\nimport {Directionality} from '@angular/cdk/bidi';\nimport {\n  ConnectedPosition,\n  FlexibleConnectedPositionStrategy,\n  Overlay,\n  OverlayConfig,\n  STANDARD_DROPDOWN_ADJACENT_POSITIONS,\n  STANDARD_DROPDOWN_BELOW_POSITIONS,\n} from '@angular/cdk/overlay';\nimport {\n  DOWN_ARROW,\n  ENTER,\n  hasModifierKey,\n  LEFT_ARROW,\n  RIGHT_ARROW,\n  SPACE,\n  UP_ARROW,\n} from '@angular/cdk/keycodes';\nimport {_getEventTarget} from '@angular/cdk/platform';\nimport {fromEvent} from 'rxjs';\nimport {filter, takeUntil} from 'rxjs/operators';\nimport {CDK_MENU, Menu} from './menu-interface';\nimport {PARENT_OR_NEW_MENU_STACK_PROVIDER} from './menu-stack';\nimport {MENU_AIM} from './menu-aim';\nimport {CdkMenuTriggerBase, MENU_TRIGGER} from './menu-trigger-base';\nimport {eventDispatchesNativeClick} from './event-detection';\n\n/**\n * A directive that turns its host element into a trigger for a popup menu.\n * It can be combined with cdkMenuItem to create sub-menus. If the element is in a top level\n * MenuBar it will open the menu on click, or if a sibling is already opened it will open on hover.\n * If it is inside of a Menu it will open the attached Submenu on hover regardless of its sibling\n * state.\n */\n@Directive({\n  selector: '[cdkMenuTriggerFor]',\n  exportAs: 'cdkMenuTriggerFor',\n  standalone: true,\n  host: {\n    'class': 'cdk-menu-trigger',\n    '[attr.aria-haspopup]': 'menuTemplateRef ? \"menu\" : null',\n    '[attr.aria-expanded]': 'menuTemplateRef == null ? null : isOpen()',\n    '(focusin)': '_setHasFocus(true)',\n    '(focusout)': '_setHasFocus(false)',\n    '(keydown)': '_toggleOnKeydown($event)',\n    '(click)': '_handleClick()',\n  },\n  inputs: [\n    {name: 'menuTemplateRef', alias: 'cdkMenuTriggerFor'},\n    {name: 'menuPosition', alias: 'cdkMenuPosition'},\n    {name: 'menuData', alias: 'cdkMenuTriggerData'},\n  ],\n  outputs: ['opened: cdkMenuOpened', 'closed: cdkMenuClosed'],\n  providers: [\n    {provide: MENU_TRIGGER, useExisting: CdkMenuTrigger},\n    PARENT_OR_NEW_MENU_STACK_PROVIDER,\n  ],\n})\nexport class CdkMenuTrigger extends CdkMenuTriggerBase implements OnDestroy {\n  private readonly _elementRef: ElementRef<HTMLElement> = inject(ElementRef);\n  private readonly _overlay = inject(Overlay);\n  private readonly _ngZone = inject(NgZone);\n  private readonly _changeDetectorRef = inject(ChangeDetectorRef);\n  private readonly _inputModalityDetector = inject(InputModalityDetector);\n  private readonly _directionality = inject(Directionality, {optional: true});\n\n  /** The parent menu this trigger belongs to. */\n  private readonly _parentMenu = inject(CDK_MENU, {optional: true});\n\n  /** The menu aim service used by this menu. */\n  private readonly _menuAim = inject(MENU_AIM, {optional: true});\n\n  constructor() {\n    super();\n    this._setRole();\n    this._registerCloseHandler();\n    this._subscribeToMenuStackClosed();\n    this._subscribeToMouseEnter();\n    this._subscribeToMenuStackHasFocus();\n    this._setType();\n  }\n\n  /** Toggle the attached menu. */\n  toggle() {\n    this.isOpen() ? this.close() : this.open();\n  }\n\n  /** Open the attached menu. */\n  open() {\n    if (!this.isOpen() && this.menuTemplateRef != null) {\n      this.opened.next();\n\n      this.overlayRef = this.overlayRef || this._overlay.create(this._getOverlayConfig());\n      this.overlayRef.attach(this.getMenuContentPortal());\n      this._changeDetectorRef.markForCheck();\n      this._subscribeToOutsideClicks();\n    }\n  }\n\n  /** Close the opened menu. */\n  close() {\n    if (this.isOpen()) {\n      this.closed.next();\n\n      this.overlayRef!.detach();\n      this._changeDetectorRef.markForCheck();\n    }\n    this._closeSiblingTriggers();\n  }\n\n  /**\n   * Get a reference to the rendered Menu if the Menu is open and rendered in the DOM.\n   */\n  getMenu(): Menu | undefined {\n    return this.childMenu;\n  }\n\n  /**\n   * Handles keyboard events for the menu item.\n   * @param event The keyboard event to handle\n   */\n  _toggleOnKeydown(event: KeyboardEvent) {\n    const isParentVertical = this._parentMenu?.orientation === 'vertical';\n    switch (event.keyCode) {\n      case SPACE:\n      case ENTER:\n        // Skip events that will trigger clicks so the handler doesn't get triggered twice.\n        if (!hasModifierKey(event) && !eventDispatchesNativeClick(this._elementRef, event)) {\n          this.toggle();\n          this.childMenu?.focusFirstItem('keyboard');\n        }\n        break;\n\n      case RIGHT_ARROW:\n        if (!hasModifierKey(event)) {\n          if (this._parentMenu && isParentVertical && this._directionality?.value !== 'rtl') {\n            event.preventDefault();\n            this.open();\n            this.childMenu?.focusFirstItem('keyboard');\n          }\n        }\n        break;\n\n      case LEFT_ARROW:\n        if (!hasModifierKey(event)) {\n          if (this._parentMenu && isParentVertical && this._directionality?.value === 'rtl') {\n            event.preventDefault();\n            this.open();\n            this.childMenu?.focusFirstItem('keyboard');\n          }\n        }\n        break;\n\n      case DOWN_ARROW:\n      case UP_ARROW:\n        if (!hasModifierKey(event)) {\n          if (!isParentVertical) {\n            event.preventDefault();\n            this.open();\n            event.keyCode === DOWN_ARROW\n              ? this.childMenu?.focusFirstItem('keyboard')\n              : this.childMenu?.focusLastItem('keyboard');\n          }\n        }\n        break;\n    }\n  }\n\n  /** Handles clicks on the menu trigger. */\n  _handleClick() {\n    this.toggle();\n    this.childMenu?.focusFirstItem('mouse');\n  }\n\n  /**\n   * Sets whether the trigger's menu stack has focus.\n   * @param hasFocus Whether the menu stack has focus.\n   */\n  _setHasFocus(hasFocus: boolean) {\n    if (!this._parentMenu) {\n      this.menuStack.setHasFocus(hasFocus);\n    }\n  }\n\n  /**\n   * Subscribe to the mouseenter events and close any sibling menu items if this element is moused\n   * into.\n   */\n  private _subscribeToMouseEnter() {\n    this._ngZone.runOutsideAngular(() => {\n      fromEvent(this._elementRef.nativeElement, 'mouseenter')\n        .pipe(\n          filter(() => {\n            return (\n              // Skip fake `mouseenter` events dispatched by touch devices.\n              this._inputModalityDetector.mostRecentModality !== 'touch' &&\n              !this.menuStack.isEmpty() &&\n              !this.isOpen()\n            );\n          }),\n          takeUntil(this.destroyed),\n        )\n        .subscribe(() => {\n          // Closes any sibling menu items and opens the menu associated with this trigger.\n          const toggleMenus = () =>\n            this._ngZone.run(() => {\n              this._closeSiblingTriggers();\n              this.open();\n            });\n\n          if (this._menuAim) {\n            this._menuAim.toggle(toggleMenus);\n          } else {\n            toggleMenus();\n          }\n        });\n    });\n  }\n\n  /** Close out any sibling menu trigger menus. */\n  private _closeSiblingTriggers() {\n    if (this._parentMenu) {\n      // If nothing was removed from the stack and the last element is not the parent item\n      // that means that the parent menu is a menu bar since we don't put the menu bar on the\n      // stack\n      const isParentMenuBar =\n        !this.menuStack.closeSubMenuOf(this._parentMenu) &&\n        this.menuStack.peek() !== this._parentMenu;\n\n      if (isParentMenuBar) {\n        this.menuStack.closeAll();\n      }\n    } else {\n      this.menuStack.closeAll();\n    }\n  }\n\n  /** Get the configuration object used to create the overlay. */\n  private _getOverlayConfig() {\n    return new OverlayConfig({\n      positionStrategy: this._getOverlayPositionStrategy(),\n      scrollStrategy: this.menuScrollStrategy(),\n      direction: this._directionality || undefined,\n    });\n  }\n\n  /** Build the position strategy for the overlay which specifies where to place the menu. */\n  private _getOverlayPositionStrategy(): FlexibleConnectedPositionStrategy {\n    return this._overlay\n      .position()\n      .flexibleConnectedTo(this._elementRef)\n      .withLockedPosition()\n      .withGrowAfterOpen()\n      .withPositions(this._getOverlayPositions());\n  }\n\n  /** Get the preferred positions for the opened menu relative to the menu item. */\n  private _getOverlayPositions(): ConnectedPosition[] {\n    return (\n      this.menuPosition ??\n      (!this._parentMenu || this._parentMenu.orientation === 'horizontal'\n        ? STANDARD_DROPDOWN_BELOW_POSITIONS\n        : STANDARD_DROPDOWN_ADJACENT_POSITIONS)\n    );\n  }\n\n  /**\n   * Subscribe to the MenuStack close events if this is a standalone trigger and close out the menu\n   * this triggers when requested.\n   */\n  private _registerCloseHandler() {\n    if (!this._parentMenu) {\n      this.menuStack.closed.pipe(takeUntil(this.destroyed)).subscribe(({item}) => {\n        if (item === this.childMenu) {\n          this.close();\n        }\n      });\n    }\n  }\n\n  /**\n   * Subscribe to the overlays outside pointer events stream and handle closing out the stack if a\n   * click occurs outside the menus.\n   */\n  private _subscribeToOutsideClicks() {\n    if (this.overlayRef) {\n      this.overlayRef\n        .outsidePointerEvents()\n        .pipe(takeUntil(this.stopOutsideClicksListener))\n        .subscribe(event => {\n          const target = _getEventTarget(event) as Element;\n          const element = this._elementRef.nativeElement;\n\n          if (target !== element && !element.contains(target)) {\n            if (!this.isElementInsideMenuStack(target)) {\n              this.menuStack.closeAll();\n            } else {\n              this._closeSiblingTriggers();\n            }\n          }\n        });\n    }\n  }\n\n  /** Subscribe to the MenuStack hasFocus events. */\n  private _subscribeToMenuStackHasFocus() {\n    if (!this._parentMenu) {\n      this.menuStack.hasFocus.pipe(takeUntil(this.destroyed)).subscribe(hasFocus => {\n        if (!hasFocus) {\n          this.menuStack.closeAll();\n        }\n      });\n    }\n  }\n\n  /** Subscribe to the MenuStack closed events. */\n  private _subscribeToMenuStackClosed() {\n    if (!this._parentMenu) {\n      this.menuStack.closed.subscribe(({focusParentTrigger}) => {\n        if (focusParentTrigger && !this.menuStack.length()) {\n          this._elementRef.nativeElement.focus();\n        }\n      });\n    }\n  }\n\n  /** Sets the role attribute for this trigger if needed. */\n  private _setRole() {\n    // If this trigger is part of another menu, the cdkMenuItem directive will handle setting the\n    // role, otherwise this is a standalone trigger, and we should ensure it has role=\"button\".\n    if (!this._parentMenu) {\n      this._elementRef.nativeElement.setAttribute('role', 'button');\n    }\n  }\n\n  /** Sets thte `type` attribute of the trigger. */\n  private _setType() {\n    const element = this._elementRef.nativeElement;\n\n    if (element.nodeName === 'BUTTON' && !element.getAttribute('type')) {\n      // Prevents form submissions.\n      element.setAttribute('type', 'button');\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  booleanAttribute,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  inject,\n  Input,\n  Ng<PERSON><PERSON>,\n  OnDestroy,\n  Output,\n} from '@angular/core';\nimport {FocusableOption, InputModalityDetector} from '@angular/cdk/a11y';\nimport {ENTER, hasModifierKey, LEFT_ARROW, RIGHT_ARROW, SPACE} from '@angular/cdk/keycodes';\nimport {Directionality} from '@angular/cdk/bidi';\nimport {fromEvent, Subject} from 'rxjs';\nimport {filter, takeUntil} from 'rxjs/operators';\nimport {CdkMenuTrigger} from './menu-trigger';\nimport {CDK_MENU, Menu} from './menu-interface';\nimport {FocusNext, MENU_STACK} from './menu-stack';\nimport {FocusableElement} from './pointer-focus-tracker';\nimport {ME<PERSON>_AIM, Toggler} from './menu-aim';\nimport {eventDispatchesNativeClick} from './event-detection';\n\n/**\n * Directive which provides the ability for an element to be focused and navigated to using the\n * keyboard when residing in a CdkMenu, CdkMenuBar, or CdkMenuGroup. It performs user defined\n * behavior when clicked.\n */\n@Directive({\n  selector: '[cdkMenuItem]',\n  exportAs: 'cdkMenuItem',\n  standalone: true,\n  host: {\n    'role': 'menuitem',\n    'class': 'cdk-menu-item',\n    '[tabindex]': '_tabindex',\n    '[attr.aria-disabled]': 'disabled || null',\n    '(blur)': '_resetTabIndex()',\n    '(focus)': '_setTabIndex()',\n    '(click)': 'trigger()',\n    '(keydown)': '_onKeydown($event)',\n  },\n})\nexport class CdkMenuItem implements FocusableOption, FocusableElement, Toggler, OnDestroy {\n  protected readonly _dir = inject(Directionality, {optional: true});\n  readonly _elementRef: ElementRef<HTMLElement> = inject(ElementRef);\n  protected _ngZone = inject(NgZone);\n  private readonly _inputModalityDetector = inject(InputModalityDetector);\n\n  /** The menu aim service used by this menu. */\n  private readonly _menuAim = inject(MENU_AIM, {optional: true});\n\n  /** The stack of menus this menu belongs to. */\n  private readonly _menuStack = inject(MENU_STACK);\n\n  /** The parent menu in which this menuitem resides. */\n  private readonly _parentMenu = inject(CDK_MENU, {optional: true});\n\n  /** Reference to the CdkMenuItemTrigger directive if one is added to the same element */\n  private readonly _menuTrigger = inject(CdkMenuTrigger, {optional: true, self: true});\n\n  /**  Whether the CdkMenuItem is disabled - defaults to false */\n  @Input({alias: 'cdkMenuItemDisabled', transform: booleanAttribute}) disabled: boolean = false;\n\n  /**\n   * The text used to locate this item during menu typeahead. If not specified,\n   * the `textContent` of the item will be used.\n   */\n  @Input('cdkMenuitemTypeaheadLabel') typeaheadLabel: string | null;\n\n  /**\n   * If this MenuItem is a regular MenuItem, outputs when it is triggered by a keyboard or mouse\n   * event.\n   */\n  @Output('cdkMenuItemTriggered') readonly triggered: EventEmitter<void> = new EventEmitter();\n\n  /** Whether the menu item opens a menu. */\n  get hasMenu() {\n    return this._menuTrigger?.menuTemplateRef != null;\n  }\n\n  /**\n   * The tabindex for this menu item managed internally and used for implementing roving a\n   * tab index.\n   */\n  _tabindex: 0 | -1 = -1;\n\n  /** Whether the item should close the menu if triggered by the spacebar. */\n  protected closeOnSpacebarTrigger = true;\n\n  /** Emits when the menu item is destroyed. */\n  protected readonly destroyed = new Subject<void>();\n\n  constructor() {\n    this._setupMouseEnter();\n    this._setType();\n\n    if (this._isStandaloneItem()) {\n      this._tabindex = 0;\n    }\n  }\n\n  ngOnDestroy() {\n    this.destroyed.next();\n    this.destroyed.complete();\n  }\n\n  /** Place focus on the element. */\n  focus() {\n    this._elementRef.nativeElement.focus();\n  }\n\n  /**\n   * If the menu item is not disabled and the element does not have a menu trigger attached, emit\n   * on the cdkMenuItemTriggered emitter and close all open menus.\n   * @param options Options the configure how the item is triggered\n   *   - keepOpen: specifies that the menu should be kept open after triggering the item.\n   */\n  trigger(options?: {keepOpen: boolean}) {\n    const {keepOpen} = {...options};\n    if (!this.disabled && !this.hasMenu) {\n      this.triggered.next();\n      if (!keepOpen) {\n        this._menuStack.closeAll({focusParentTrigger: true});\n      }\n    }\n  }\n\n  /** Return true if this MenuItem has an attached menu and it is open. */\n  isMenuOpen() {\n    return !!this._menuTrigger?.isOpen();\n  }\n\n  /**\n   * Get a reference to the rendered Menu if the Menu is open and it is visible in the DOM.\n   * @return the menu if it is open, otherwise undefined.\n   */\n  getMenu(): Menu | undefined {\n    return this._menuTrigger?.getMenu();\n  }\n\n  /** Get the CdkMenuTrigger associated with this element. */\n  getMenuTrigger(): CdkMenuTrigger | null {\n    return this._menuTrigger;\n  }\n\n  /** Get the label for this element which is required by the FocusableOption interface. */\n  getLabel(): string {\n    return this.typeaheadLabel || this._elementRef.nativeElement.textContent?.trim() || '';\n  }\n\n  /** Reset the tabindex to -1. */\n  _resetTabIndex() {\n    if (!this._isStandaloneItem()) {\n      this._tabindex = -1;\n    }\n  }\n\n  /**\n   * Set the tab index to 0 if not disabled and it's a focus event, or a mouse enter if this element\n   * is not in a menu bar.\n   */\n  _setTabIndex(event?: MouseEvent) {\n    if (this.disabled) {\n      return;\n    }\n\n    // don't set the tabindex if there are no open sibling or parent menus\n    if (!event || !this._menuStack.isEmpty()) {\n      this._tabindex = 0;\n    }\n  }\n\n  /**\n   * Handles keyboard events for the menu item, specifically either triggering the user defined\n   * callback or opening/closing the current menu based on whether the left or right arrow key was\n   * pressed.\n   * @param event the keyboard event to handle\n   */\n  _onKeydown(event: KeyboardEvent) {\n    switch (event.keyCode) {\n      case SPACE:\n      case ENTER:\n        // Skip events that will trigger clicks so the handler doesn't get triggered twice.\n        if (!hasModifierKey(event) && !eventDispatchesNativeClick(this._elementRef, event)) {\n          this.trigger({keepOpen: event.keyCode === SPACE && !this.closeOnSpacebarTrigger});\n        }\n        break;\n\n      case RIGHT_ARROW:\n        if (!hasModifierKey(event)) {\n          if (this._parentMenu && this._isParentVertical()) {\n            if (this._dir?.value !== 'rtl') {\n              this._forwardArrowPressed(event);\n            } else {\n              this._backArrowPressed(event);\n            }\n          }\n        }\n        break;\n\n      case LEFT_ARROW:\n        if (!hasModifierKey(event)) {\n          if (this._parentMenu && this._isParentVertical()) {\n            if (this._dir?.value !== 'rtl') {\n              this._backArrowPressed(event);\n            } else {\n              this._forwardArrowPressed(event);\n            }\n          }\n        }\n        break;\n    }\n  }\n\n  /** Whether this menu item is standalone or within a menu or menu bar. */\n  private _isStandaloneItem() {\n    return !this._parentMenu;\n  }\n\n  /**\n   * Handles the user pressing the back arrow key.\n   * @param event The keyboard event.\n   */\n  private _backArrowPressed(event: KeyboardEvent) {\n    const parentMenu = this._parentMenu!;\n    if (this._menuStack.hasInlineMenu() || this._menuStack.length() > 1) {\n      event.preventDefault();\n      this._menuStack.close(parentMenu, {\n        focusNextOnEmpty:\n          this._menuStack.inlineMenuOrientation() === 'horizontal'\n            ? FocusNext.previousItem\n            : FocusNext.currentItem,\n        focusParentTrigger: true,\n      });\n    }\n  }\n\n  /**\n   * Handles the user pressing the forward arrow key.\n   * @param event The keyboard event.\n   */\n  private _forwardArrowPressed(event: KeyboardEvent) {\n    if (!this.hasMenu && this._menuStack.inlineMenuOrientation() === 'horizontal') {\n      event.preventDefault();\n      this._menuStack.closeAll({\n        focusNextOnEmpty: FocusNext.nextItem,\n        focusParentTrigger: true,\n      });\n    }\n  }\n\n  /**\n   * Subscribe to the mouseenter events and close any sibling menu items if this element is moused\n   * into.\n   */\n  private _setupMouseEnter() {\n    if (!this._isStandaloneItem()) {\n      const closeOpenSiblings = () =>\n        this._ngZone.run(() => this._menuStack.closeSubMenuOf(this._parentMenu!));\n\n      this._ngZone.runOutsideAngular(() =>\n        fromEvent(this._elementRef.nativeElement, 'mouseenter')\n          .pipe(\n            filter(() => {\n              return (\n                // Skip fake `mouseenter` events dispatched by touch devices.\n                this._inputModalityDetector.mostRecentModality !== 'touch' &&\n                !this._menuStack.isEmpty() &&\n                !this.hasMenu\n              );\n            }),\n            takeUntil(this.destroyed),\n          )\n          .subscribe(() => {\n            if (this._menuAim) {\n              this._menuAim.toggle(closeOpenSiblings);\n            } else {\n              closeOpenSiblings();\n            }\n          }),\n      );\n    }\n  }\n\n  /**\n   * Return true if the enclosing parent menu is configured in a horizontal orientation, false\n   * otherwise or if no parent.\n   */\n  private _isParentVertical() {\n    return this._parentMenu?.orientation === 'vertical';\n  }\n\n  /** Sets the `type` attribute of the menu item. */\n  private _setType() {\n    const element = this._elementRef.nativeElement;\n\n    if (element.nodeName === 'BUTTON' && !element.getAttribute('type')) {\n      // Prevent form submissions.\n      element.setAttribute('type', 'button');\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ElementRef, QueryList} from '@angular/core';\nimport {defer, fromEvent, Observable, Subject} from 'rxjs';\nimport {mapTo, mergeAll, mergeMap, startWith, takeUntil} from 'rxjs/operators';\n\n/** Item to track for mouse focus events. */\nexport interface FocusableElement {\n  /** A reference to the element to be tracked. */\n  _elementRef: ElementRef<HTMLElement>;\n}\n\n/**\n * PointerFocusTracker keeps track of the currently active item under mouse focus. It also has\n * observables which emit when the users mouse enters and leaves a tracked element.\n */\nexport class PointerFocusTracker<T extends FocusableElement> {\n  /** Emits when an element is moused into. */\n  readonly entered: Observable<T> = this._getItemPointerEntries();\n\n  /** Emits when an element is moused out. */\n  readonly exited: Observable<T> = this._getItemPointerExits();\n\n  /** The element currently under mouse focus. */\n  activeElement?: T;\n\n  /** The element previously under mouse focus. */\n  previousElement?: T;\n\n  /** Emits when this is destroyed. */\n  private readonly _destroyed: Subject<void> = new Subject();\n\n  constructor(\n    /** The list of items being tracked. */\n    private readonly _items: QueryList<T>,\n  ) {\n    this.entered.subscribe(element => (this.activeElement = element));\n    this.exited.subscribe(() => {\n      this.previousElement = this.activeElement;\n      this.activeElement = undefined;\n    });\n  }\n\n  /** Stop the managers listeners. */\n  destroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n\n  /**\n   * Gets a stream of pointer (mouse) entries into the given items.\n   * This should typically run outside the Angular zone.\n   */\n  private _getItemPointerEntries(): Observable<T> {\n    return defer(() =>\n      this._items.changes.pipe(\n        startWith(this._items),\n        mergeMap((list: QueryList<T>) =>\n          list.map(element =>\n            fromEvent(element._elementRef.nativeElement, 'mouseenter').pipe(\n              mapTo(element),\n              takeUntil(this._items.changes),\n            ),\n          ),\n        ),\n        mergeAll(),\n      ),\n    );\n  }\n\n  /**\n   * Gets a stream of pointer (mouse) exits out of the given items.\n   * This should typically run outside the Angular zone.\n   */\n  private _getItemPointerExits() {\n    return defer(() =>\n      this._items.changes.pipe(\n        startWith(this._items),\n        mergeMap((list: QueryList<T>) =>\n          list.map(element =>\n            fromEvent(element._elementRef.nativeElement, 'mouseout').pipe(\n              mapTo(element),\n              takeUntil(this._items.changes),\n            ),\n          ),\n        ),\n        mergeAll(),\n      ),\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {CdkMenuGroup} from './menu-group';\nimport {\n  AfterContentInit,\n  ContentChildren,\n  Directive,\n  ElementRef,\n  inject,\n  Input,\n  NgZone,\n  OnDestroy,\n  QueryList,\n} from '@angular/core';\nimport {FocusKeyManager, FocusOrigin} from '@angular/cdk/a11y';\nimport {CdkMenuItem} from './menu-item';\nimport {merge, Subject} from 'rxjs';\nimport {Directionality} from '@angular/cdk/bidi';\nimport {mapTo, mergeAll, mergeMap, startWith, switchMap, takeUntil} from 'rxjs/operators';\nimport {MENU_STACK, MenuStack, MenuStackItem} from './menu-stack';\nimport {Menu} from './menu-interface';\nimport {PointerFocusTracker} from './pointer-focus-tracker';\nimport {MENU_AIM} from './menu-aim';\n\n/** Counter used to create unique IDs for menus. */\nlet nextId = 0;\n\n/**\n * Abstract directive that implements shared logic common to all menus.\n * This class can be extended to create custom menu types.\n */\n@Directive({\n  host: {\n    'role': 'menu',\n    'class': '', // reset the css class added by the super-class\n    '[tabindex]': '_getTabIndex()',\n    '[id]': 'id',\n    '[attr.aria-orientation]': 'orientation',\n    '[attr.data-cdk-menu-stack-id]': 'menuStack.id',\n    '(focus)': 'focusFirstItem()',\n    '(focusin)': 'menuStack.setHasFocus(true)',\n    '(focusout)': 'menuStack.setHasFocus(false)',\n  },\n  standalone: true,\n})\nexport abstract class CdkMenuBase\n  extends CdkMenuGroup\n  implements Menu, AfterContentInit, OnDestroy\n{\n  /** The menu's native DOM host element. */\n  readonly nativeElement: HTMLElement = inject(ElementRef).nativeElement;\n\n  /** The Angular zone. */\n  protected ngZone = inject(NgZone);\n\n  /** The stack of menus this menu belongs to. */\n  readonly menuStack: MenuStack = inject(MENU_STACK);\n\n  /** The menu aim service used by this menu. */\n  protected readonly menuAim = inject(MENU_AIM, {optional: true, self: true});\n\n  /** The directionality (text direction) of the current page. */\n  protected readonly dir = inject(Directionality, {optional: true});\n\n  /** The id of the menu's host element. */\n  @Input() id = `cdk-menu-${nextId++}`;\n\n  /** All child MenuItem elements nested in this Menu. */\n  @ContentChildren(CdkMenuItem, {descendants: true})\n  readonly items: QueryList<CdkMenuItem>;\n\n  /** The direction items in the menu flow. */\n  orientation: 'horizontal' | 'vertical' = 'vertical';\n\n  /**\n   * Whether the menu is displayed inline (i.e. always present vs a conditional popup that the\n   * user triggers with a trigger element).\n   */\n  isInline = false;\n\n  /** Handles keyboard events for the menu. */\n  protected keyManager: FocusKeyManager<CdkMenuItem>;\n\n  /** Emits when the MenuBar is destroyed. */\n  protected readonly destroyed: Subject<void> = new Subject();\n\n  /** The Menu Item which triggered the open submenu. */\n  protected triggerItem?: CdkMenuItem;\n\n  /** Tracks the users mouse movements over the menu. */\n  protected pointerTracker?: PointerFocusTracker<CdkMenuItem>;\n\n  /** Whether this menu's menu stack has focus. */\n  private _menuStackHasFocus = false;\n\n  ngAfterContentInit() {\n    if (!this.isInline) {\n      this.menuStack.push(this);\n    }\n    this._setKeyManager();\n    this._subscribeToMenuStackHasFocus();\n    this._subscribeToMenuOpen();\n    this._subscribeToMenuStackClosed();\n    this._setUpPointerTracker();\n  }\n\n  ngOnDestroy() {\n    this.keyManager?.destroy();\n    this.destroyed.next();\n    this.destroyed.complete();\n    this.pointerTracker?.destroy();\n  }\n\n  /**\n   * Place focus on the first MenuItem in the menu and set the focus origin.\n   * @param focusOrigin The origin input mode of the focus event.\n   */\n  focusFirstItem(focusOrigin: FocusOrigin = 'program') {\n    this.keyManager.setFocusOrigin(focusOrigin);\n    this.keyManager.setFirstItemActive();\n  }\n\n  /**\n   * Place focus on the last MenuItem in the menu and set the focus origin.\n   * @param focusOrigin The origin input mode of the focus event.\n   */\n  focusLastItem(focusOrigin: FocusOrigin = 'program') {\n    this.keyManager.setFocusOrigin(focusOrigin);\n    this.keyManager.setLastItemActive();\n  }\n\n  /** Gets the tabindex for this menu. */\n  _getTabIndex() {\n    const tabindexIfInline = this._menuStackHasFocus ? -1 : 0;\n    return this.isInline ? tabindexIfInline : null;\n  }\n\n  /**\n   * Close the open menu if the current active item opened the requested MenuStackItem.\n   * @param menu The menu requested to be closed.\n   * @param options Options to configure the behavior on close.\n   *   - `focusParentTrigger` Whether to focus the parent trigger after closing the menu.\n   */\n  protected closeOpenMenu(menu: MenuStackItem, options?: {focusParentTrigger?: boolean}) {\n    const {focusParentTrigger} = {...options};\n    const keyManager = this.keyManager;\n    const trigger = this.triggerItem;\n    if (menu === trigger?.getMenuTrigger()?.getMenu()) {\n      trigger?.getMenuTrigger()?.close();\n      // If the user has moused over a sibling item we want to focus the element under mouse focus\n      // not the trigger which previously opened the now closed menu.\n      if (focusParentTrigger) {\n        if (trigger) {\n          keyManager.setActiveItem(trigger);\n        } else {\n          keyManager.setFirstItemActive();\n        }\n      }\n    }\n  }\n\n  /** Setup the FocusKeyManager with the correct orientation for the menu. */\n  private _setKeyManager() {\n    this.keyManager = new FocusKeyManager(this.items).withWrap().withTypeAhead().withHomeAndEnd();\n\n    if (this.orientation === 'horizontal') {\n      this.keyManager.withHorizontalOrientation(this.dir?.value || 'ltr');\n    } else {\n      this.keyManager.withVerticalOrientation();\n    }\n  }\n\n  /**\n   * Subscribe to the menu trigger's open events in order to track the trigger which opened the menu\n   * and stop tracking it when the menu is closed.\n   */\n  private _subscribeToMenuOpen() {\n    const exitCondition = merge(this.items.changes, this.destroyed);\n    this.items.changes\n      .pipe(\n        startWith(this.items),\n        mergeMap((list: QueryList<CdkMenuItem>) =>\n          list\n            .filter(item => item.hasMenu)\n            .map(item => item.getMenuTrigger()!.opened.pipe(mapTo(item), takeUntil(exitCondition))),\n        ),\n        mergeAll(),\n        switchMap((item: CdkMenuItem) => {\n          this.triggerItem = item;\n          return item.getMenuTrigger()!.closed;\n        }),\n        takeUntil(this.destroyed),\n      )\n      .subscribe(() => (this.triggerItem = undefined));\n  }\n\n  /** Subscribe to the MenuStack close events. */\n  private _subscribeToMenuStackClosed() {\n    this.menuStack.closed\n      .pipe(takeUntil(this.destroyed))\n      .subscribe(({item, focusParentTrigger}) => this.closeOpenMenu(item, {focusParentTrigger}));\n  }\n\n  /** Subscribe to the MenuStack hasFocus events. */\n  private _subscribeToMenuStackHasFocus() {\n    if (this.isInline) {\n      this.menuStack.hasFocus.pipe(takeUntil(this.destroyed)).subscribe(hasFocus => {\n        this._menuStackHasFocus = hasFocus;\n      });\n    }\n  }\n\n  /**\n   * Set the PointerFocusTracker and ensure that when mouse focus changes the key manager is updated\n   * with the latest menu item under mouse focus.\n   */\n  private _setUpPointerTracker() {\n    if (this.menuAim) {\n      this.ngZone.runOutsideAngular(() => {\n        this.pointerTracker = new PointerFocusTracker(this.items);\n      });\n      this.menuAim.initialize(this, this.pointerTracker!);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AfterContentInit, Directive, EventEmitter, inject, OnDestroy, Output} from '@angular/core';\nimport {ESCAPE, hasModifier<PERSON>ey, LEFT_ARROW, RIGHT_ARROW, TAB} from '@angular/cdk/keycodes';\nimport {takeUntil} from 'rxjs/operators';\nimport {CdkMenuGroup} from './menu-group';\nimport {CDK_MENU} from './menu-interface';\nimport {FocusNext, PARENT_OR_NEW_INLINE_MENU_STACK_PROVIDER} from './menu-stack';\nimport {MENU_TRIGGER} from './menu-trigger-base';\nimport {CdkMenuBase} from './menu-base';\n\n/**\n * Directive which configures the element as a Menu which should contain child elements marked as\n * CdkMenuItem or CdkMenuGroup. Sets the appropriate role and aria-attributes for a menu and\n * contains accessible keyboard and mouse handling logic.\n *\n * It also acts as a RadioGroup for elements marked with role `menuitemradio`.\n */\n@Directive({\n  selector: '[cdkMenu]',\n  exportAs: 'cdkMenu',\n  standalone: true,\n  host: {\n    'role': 'menu',\n    'class': 'cdk-menu',\n    '[class.cdk-menu-inline]': 'isInline',\n    '(keydown)': '_handleKeyEvent($event)',\n  },\n  providers: [\n    {provide: CdkMenuGroup, useExisting: CdkMenu},\n    {provide: CDK_MENU, useExisting: CdkMenu},\n    PARENT_OR_NEW_INLINE_MENU_STACK_PROVIDER('vertical'),\n  ],\n})\nexport class CdkMenu extends CdkMenuBase implements AfterContentInit, OnDestroy {\n  private _parentTrigger = inject(MENU_TRIGGER, {optional: true});\n\n  /** Event emitted when the menu is closed. */\n  @Output() readonly closed: EventEmitter<void> = new EventEmitter();\n\n  /** The direction items in the menu flow. */\n  override readonly orientation = 'vertical';\n\n  /** Whether the menu is displayed inline (i.e. always present vs a conditional popup that the user triggers with a trigger element). */\n  override readonly isInline = !this._parentTrigger;\n\n  constructor() {\n    super();\n    this.destroyed.subscribe(this.closed);\n    this._parentTrigger?.registerChildMenu(this);\n  }\n\n  override ngAfterContentInit() {\n    super.ngAfterContentInit();\n    this._subscribeToMenuStackEmptied();\n  }\n\n  override ngOnDestroy() {\n    super.ngOnDestroy();\n    this.closed.complete();\n  }\n\n  /**\n   * Handle keyboard events for the Menu.\n   * @param event The keyboard event to be handled.\n   */\n  _handleKeyEvent(event: KeyboardEvent) {\n    const keyManager = this.keyManager;\n    switch (event.keyCode) {\n      case LEFT_ARROW:\n      case RIGHT_ARROW:\n        if (!hasModifierKey(event)) {\n          event.preventDefault();\n          keyManager.setFocusOrigin('keyboard');\n          keyManager.onKeydown(event);\n        }\n        break;\n\n      case ESCAPE:\n        if (!hasModifierKey(event)) {\n          event.preventDefault();\n          this.menuStack.close(this, {\n            focusNextOnEmpty: FocusNext.currentItem,\n            focusParentTrigger: true,\n          });\n        }\n        break;\n\n      case TAB:\n        if (!hasModifierKey(event, 'altKey', 'metaKey', 'ctrlKey')) {\n          this.menuStack.closeAll({focusParentTrigger: true});\n        }\n        break;\n\n      default:\n        keyManager.onKeydown(event);\n    }\n  }\n\n  /**\n   * Set focus the either the current, previous or next item based on the FocusNext event.\n   * @param focusNext The element to focus.\n   */\n  private _toggleMenuFocus(focusNext: FocusNext | undefined) {\n    const keyManager = this.keyManager;\n    switch (focusNext) {\n      case FocusNext.nextItem:\n        keyManager.setFocusOrigin('keyboard');\n        keyManager.setNextItemActive();\n        break;\n\n      case FocusNext.previousItem:\n        keyManager.setFocusOrigin('keyboard');\n        keyManager.setPreviousItemActive();\n        break;\n\n      case FocusNext.currentItem:\n        if (keyManager.activeItem) {\n          keyManager.setFocusOrigin('keyboard');\n          keyManager.setActiveItem(keyManager.activeItem);\n        }\n        break;\n    }\n  }\n\n  /** Subscribe to the MenuStack emptied events. */\n  private _subscribeToMenuStackEmptied() {\n    this.menuStack.emptied\n      .pipe(takeUntil(this.destroyed))\n      .subscribe(event => this._toggleMenuFocus(event));\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AfterContentInit, Directive} from '@angular/core';\nimport {\n  DOWN_ARROW,\n  ESCAPE,\n  hasModifier<PERSON>ey,\n  LEFT_ARROW,\n  RIGHT_ARROW,\n  TAB,\n  UP_ARROW,\n} from '@angular/cdk/keycodes';\nimport {takeUntil} from 'rxjs/operators';\nimport {CdkMenuGroup} from './menu-group';\nimport {CDK_MENU} from './menu-interface';\nimport {FocusNext, MENU_STACK, MenuStack} from './menu-stack';\nimport {CdkMenuBase} from './menu-base';\n\n/**\n * Directive applied to an element which configures it as a MenuBar by setting the appropriate\n * role, aria attributes, and accessible keyboard and mouse handling logic. The component that\n * this directive is applied to should contain components marked with CdkMenuItem.\n *\n */\n@Directive({\n  selector: '[cdkMenuBar]',\n  exportAs: 'cdkMenuBar',\n  standalone: true,\n  host: {\n    'role': 'menubar',\n    'class': 'cdk-menu-bar',\n    '(keydown)': '_handleKeyEvent($event)',\n  },\n  providers: [\n    {provide: CdkMenuGroup, useExisting: CdkMenuBar},\n    {provide: CDK_MENU, useExisting: CdkMenuBar},\n    {provide: MENU_STACK, useFactory: () => MenuStack.inline('horizontal')},\n  ],\n})\nexport class CdkMenuBar extends CdkMenuBase implements AfterContentInit {\n  /** The direction items in the menu flow. */\n  override readonly orientation = 'horizontal';\n\n  /** Whether the menu is displayed inline (i.e. always present vs a conditional popup that the user triggers with a trigger element). */\n  override readonly isInline = true;\n\n  override ngAfterContentInit() {\n    super.ngAfterContentInit();\n    this._subscribeToMenuStackEmptied();\n  }\n\n  /**\n   * Handle keyboard events for the Menu.\n   * @param event The keyboard event to be handled.\n   */\n  _handleKeyEvent(event: KeyboardEvent) {\n    const keyManager = this.keyManager;\n    switch (event.keyCode) {\n      case UP_ARROW:\n      case DOWN_ARROW:\n      case LEFT_ARROW:\n      case RIGHT_ARROW:\n        if (!hasModifierKey(event)) {\n          const horizontalArrows = event.keyCode === LEFT_ARROW || event.keyCode === RIGHT_ARROW;\n          // For a horizontal menu if the left/right keys were clicked, or a vertical menu if the\n          // up/down keys were clicked: if the current menu is open, close it then focus and open the\n          // next  menu.\n          if (horizontalArrows) {\n            event.preventDefault();\n\n            const prevIsOpen = keyManager.activeItem?.isMenuOpen();\n            keyManager.activeItem?.getMenuTrigger()?.close();\n\n            keyManager.setFocusOrigin('keyboard');\n            keyManager.onKeydown(event);\n            if (prevIsOpen) {\n              keyManager.activeItem?.getMenuTrigger()?.open();\n            }\n          }\n        }\n        break;\n\n      case ESCAPE:\n        if (!hasModifierKey(event)) {\n          event.preventDefault();\n          keyManager.activeItem?.getMenuTrigger()?.close();\n        }\n        break;\n\n      case TAB:\n        if (!hasModifierKey(event, 'altKey', 'metaKey', 'ctrlKey')) {\n          keyManager.activeItem?.getMenuTrigger()?.close();\n        }\n        break;\n\n      default:\n        keyManager.onKeydown(event);\n    }\n  }\n\n  /**\n   * Set focus to either the current, previous or next item based on the FocusNext event, then\n   * open the previous or next item.\n   * @param focusNext The element to focus.\n   */\n  private _toggleOpenMenu(focusNext: FocusNext | undefined) {\n    const keyManager = this.keyManager;\n    switch (focusNext) {\n      case FocusNext.nextItem:\n        keyManager.setFocusOrigin('keyboard');\n        keyManager.setNextItemActive();\n        keyManager.activeItem?.getMenuTrigger()?.open();\n        break;\n\n      case FocusNext.previousItem:\n        keyManager.setFocusOrigin('keyboard');\n        keyManager.setPreviousItemActive();\n        keyManager.activeItem?.getMenuTrigger()?.open();\n        break;\n\n      case FocusNext.currentItem:\n        if (keyManager.activeItem) {\n          keyManager.setFocusOrigin('keyboard');\n          keyManager.setActiveItem(keyManager.activeItem);\n        }\n        break;\n    }\n  }\n\n  /** Subscribe to the MenuStack emptied events. */\n  private _subscribeToMenuStackEmptied() {\n    this.menuStack?.emptied\n      .pipe(takeUntil(this.destroyed))\n      .subscribe(event => this._toggleOpenMenu(event));\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, Input, booleanAttribute} from '@angular/core';\nimport {CdkMenuItem} from './menu-item';\n\n/** Base class providing checked state for selectable MenuItems. */\n@Directive({\n  host: {\n    '[attr.aria-checked]': '!!checked',\n    '[attr.aria-disabled]': 'disabled || null',\n  },\n  standalone: true,\n})\nexport abstract class CdkMenuItemSelectable extends CdkMenuItem {\n  /** Whether the element is checked */\n  @Input({alias: 'cdkMenuItemChecked', transform: booleanAttribute}) checked: boolean = false;\n\n  /** Whether the item should close the menu if triggered by the spacebar. */\n  protected override closeOnSpacebarTrigger = false;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {UniqueSelectionDispatcher} from '@angular/cdk/collections';\nimport {Directive, inject, OnDestroy} from '@angular/core';\nimport {CdkMenuItemSelectable} from './menu-item-selectable';\nimport {CdkMenuItem} from './menu-item';\n\n/** Counter used to set a unique id and name for a selectable item */\nlet nextId = 0;\n\n/**\n * A directive providing behavior for the \"menuitemradio\" ARIA role, which behaves similarly to\n * a conventional radio-button. Any sibling `CdkMenuItemRadio` instances within the same `CdkMenu`\n * or `CdkMenuGroup` comprise a radio group with unique selection enforced.\n */\n@Directive({\n  selector: '[cdkMenuItemRadio]',\n  exportAs: 'cdkMenuItemRadio',\n  standalone: true,\n  host: {\n    'role': 'menuitemradio',\n    '[class.cdk-menu-item-radio]': 'true',\n  },\n  providers: [\n    {provide: CdkMenuItemSelectable, useExisting: CdkMenuItemRadio},\n    {provide: CdkMenuItem, useExisting: CdkMenuItemSelectable},\n  ],\n})\nexport class CdkMenuItemRadio extends CdkMenuItemSelectable implements OnDestroy {\n  /** The unique selection dispatcher for this radio's `CdkMenuGroup`. */\n  private readonly _selectionDispatcher = inject(UniqueSelectionDispatcher);\n\n  /** An ID to identify this radio item to the `UniqueSelectionDispatcher`. */\n  private _id = `${nextId++}`;\n\n  /** Function to unregister the selection dispatcher */\n  private _removeDispatcherListener: () => void;\n\n  constructor() {\n    super();\n    this._registerDispatcherListener();\n  }\n\n  override ngOnDestroy() {\n    super.ngOnDestroy();\n\n    this._removeDispatcherListener();\n  }\n\n  /**\n   * Toggles the checked state of the radio-button.\n   * @param options Options the configure how the item is triggered\n   *   - keepOpen: specifies that the menu should be kept open after triggering the item.\n   */\n  override trigger(options?: {keepOpen: boolean}) {\n    super.trigger(options);\n\n    if (!this.disabled) {\n      this._selectionDispatcher.notify(this._id, '');\n    }\n  }\n\n  /** Configure the unique selection dispatcher listener in order to toggle the checked state  */\n  private _registerDispatcherListener() {\n    this._removeDispatcherListener = this._selectionDispatcher.listen((id: string) => {\n      this.checked = this._id === id;\n    });\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive} from '@angular/core';\nimport {CdkMenuItemSelectable} from './menu-item-selectable';\nimport {CdkMenuItem} from './menu-item';\n\n/**\n * A directive providing behavior for the \"menuitemcheckbox\" ARIA role, which behaves similarly to a\n * conventional checkbox.\n */\n@Directive({\n  selector: '[cdkMenuItemCheckbox]',\n  exportAs: 'cdkMenuItemCheckbox',\n  standalone: true,\n  host: {\n    'role': 'menuitemcheckbox',\n    '[class.cdk-menu-item-checkbox]': 'true',\n  },\n  providers: [\n    {provide: CdkMenuItemSelectable, useExisting: CdkMenuItemCheckbox},\n    {provide: CdkMenuItem, useExisting: CdkMenuItemSelectable},\n  ],\n})\nexport class CdkMenuItemCheckbox extends CdkMenuItemSelectable {\n  /**\n   * Toggle the checked state of the checkbox.\n   * @param options Options the configure how the item is triggered\n   *   - keepOpen: specifies that the menu should be kept open after triggering the item.\n   */\n  override trigger(options?: {keepOpen: boolean}) {\n    super.trigger(options);\n\n    if (!this.disabled) {\n      this.checked = !this.checked;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {booleanAttribute, Directive, inject, Injectable, Input, OnDestroy} from '@angular/core';\nimport {Directionality} from '@angular/cdk/bidi';\nimport {\n  FlexibleConnectedPositionStrategy,\n  Overlay,\n  OverlayConfig,\n  STANDARD_DROPDOWN_BELOW_POSITIONS,\n} from '@angular/cdk/overlay';\nimport {_getEventTarget} from '@angular/cdk/platform';\nimport {merge, partition} from 'rxjs';\nimport {skip, takeUntil, skipWhile} from 'rxjs/operators';\nimport {MENU_STACK, MenuStack} from './menu-stack';\nimport {CdkMenuTriggerBase, MENU_TRIGGER} from './menu-trigger-base';\n\n/** The preferred menu positions for the context menu. */\nconst CONTEXT_MENU_POSITIONS = STANDARD_DROPDOWN_BELOW_POSITIONS.map(position => {\n  // In cases where the first menu item in the context menu is a trigger the submenu opens on a\n  // hover event. We offset the context menu 2px by default to prevent this from occurring.\n  const offsetX = position.overlayX === 'start' ? 2 : -2;\n  const offsetY = position.overlayY === 'top' ? 2 : -2;\n  return {...position, offsetX, offsetY};\n});\n\n/** Tracks the last open context menu trigger across the entire application. */\n@Injectable({providedIn: 'root'})\nexport class ContextMenuTracker {\n  /** The last open context menu trigger. */\n  private static _openContextMenuTrigger?: CdkContextMenuTrigger;\n\n  /**\n   * Close the previous open context menu and set the given one as being open.\n   * @param trigger The trigger for the currently open Context Menu.\n   */\n  update(trigger: CdkContextMenuTrigger) {\n    if (ContextMenuTracker._openContextMenuTrigger !== trigger) {\n      ContextMenuTracker._openContextMenuTrigger?.close();\n      ContextMenuTracker._openContextMenuTrigger = trigger;\n    }\n  }\n}\n\n/** The coordinates where the context menu should open. */\nexport type ContextMenuCoordinates = {x: number; y: number};\n\n/**\n * A directive that opens a menu when a user right-clicks within its host element.\n * It is aware of nested context menus and will trigger only the lowest level non-disabled context menu.\n */\n@Directive({\n  selector: '[cdkContextMenuTriggerFor]',\n  exportAs: 'cdkContextMenuTriggerFor',\n  standalone: true,\n  host: {\n    '[attr.data-cdk-menu-stack-id]': 'null',\n    '(contextmenu)': '_openOnContextMenu($event)',\n  },\n  inputs: [\n    {name: 'menuTemplateRef', alias: 'cdkContextMenuTriggerFor'},\n    {name: 'menuPosition', alias: 'cdkContextMenuPosition'},\n    {name: 'menuData', alias: 'cdkContextMenuTriggerData'},\n  ],\n  outputs: ['opened: cdkContextMenuOpened', 'closed: cdkContextMenuClosed'],\n  providers: [\n    {provide: MENU_TRIGGER, useExisting: CdkContextMenuTrigger},\n    {provide: MENU_STACK, useClass: MenuStack},\n  ],\n})\nexport class CdkContextMenuTrigger extends CdkMenuTriggerBase implements OnDestroy {\n  /** The CDK overlay service. */\n  private readonly _overlay = inject(Overlay);\n\n  /** The directionality of the page. */\n  private readonly _directionality = inject(Directionality, {optional: true});\n\n  /** The app's context menu tracking registry */\n  private readonly _contextMenuTracker = inject(ContextMenuTracker);\n\n  /** Whether the context menu is disabled. */\n  @Input({alias: 'cdkContextMenuDisabled', transform: booleanAttribute}) disabled: boolean = false;\n\n  constructor() {\n    super();\n    this._setMenuStackCloseListener();\n  }\n\n  /**\n   * Open the attached menu at the specified location.\n   * @param coordinates where to open the context menu\n   */\n  open(coordinates: ContextMenuCoordinates) {\n    this._open(null, coordinates);\n  }\n\n  /** Close the currently opened context menu. */\n  close() {\n    this.menuStack.closeAll();\n  }\n\n  /**\n   * Open the context menu and closes any previously open menus.\n   * @param event the mouse event which opens the context menu.\n   */\n  _openOnContextMenu(event: MouseEvent) {\n    if (!this.disabled) {\n      // Prevent the native context menu from opening because we're opening a custom one.\n      event.preventDefault();\n\n      // Stop event propagation to ensure that only the closest enabled context menu opens.\n      // Otherwise, any context menus attached to containing elements would *also* open,\n      // resulting in multiple stacked context menus being displayed.\n      event.stopPropagation();\n\n      this._contextMenuTracker.update(this);\n      this._open(event, {x: event.clientX, y: event.clientY});\n\n      // A context menu can be triggered via a mouse right click or a keyboard shortcut.\n      if (event.button === 2) {\n        this.childMenu?.focusFirstItem('mouse');\n      } else if (event.button === 0) {\n        this.childMenu?.focusFirstItem('keyboard');\n      } else {\n        this.childMenu?.focusFirstItem('program');\n      }\n    }\n  }\n\n  /**\n   * Get the configuration object used to create the overlay.\n   * @param coordinates the location to place the opened menu\n   */\n  private _getOverlayConfig(coordinates: ContextMenuCoordinates) {\n    return new OverlayConfig({\n      positionStrategy: this._getOverlayPositionStrategy(coordinates),\n      scrollStrategy: this.menuScrollStrategy(),\n      direction: this._directionality || undefined,\n    });\n  }\n\n  /**\n   * Get the position strategy for the overlay which specifies where to place the menu.\n   * @param coordinates the location to place the opened menu\n   */\n  private _getOverlayPositionStrategy(\n    coordinates: ContextMenuCoordinates,\n  ): FlexibleConnectedPositionStrategy {\n    return this._overlay\n      .position()\n      .flexibleConnectedTo(coordinates)\n      .withLockedPosition()\n      .withGrowAfterOpen()\n      .withPositions(this.menuPosition ?? CONTEXT_MENU_POSITIONS);\n  }\n\n  /** Subscribe to the menu stack close events and close this menu when requested. */\n  private _setMenuStackCloseListener() {\n    this.menuStack.closed.pipe(takeUntil(this.destroyed)).subscribe(({item}) => {\n      if (item === this.childMenu && this.isOpen()) {\n        this.closed.next();\n        this.overlayRef!.detach();\n      }\n    });\n  }\n\n  /**\n   * Subscribe to the overlays outside pointer events stream and handle closing out the stack if a\n   * click occurs outside the menus.\n   * @param userEvent User-generated event that opened the menu.\n   */\n  private _subscribeToOutsideClicks(userEvent: MouseEvent | null) {\n    if (this.overlayRef) {\n      let outsideClicks = this.overlayRef.outsidePointerEvents();\n\n      if (userEvent) {\n        const [auxClicks, nonAuxClicks] = partition(outsideClicks, ({type}) => type === 'auxclick');\n        outsideClicks = merge(\n          // Using a mouse, the `contextmenu` event can fire either when pressing the right button\n          // or left button + control. Most browsers won't dispatch a `click` event right after\n          // a `contextmenu` event triggered by left button + control, but Safari will (see #27832).\n          // This closes the menu immediately. To work around it, we check that both the triggering\n          // event and the current outside click event both had the control key pressed, and that\n          // that this is the first outside click event.\n          nonAuxClicks.pipe(\n            skipWhile((event, index) => userEvent.ctrlKey && index === 0 && event.ctrlKey),\n          ),\n\n          // If the menu was triggered by the `contextmenu` event, skip the first `auxclick` event\n          // because it fires when the mouse is released on the same click that opened the menu.\n          auxClicks.pipe(skip(1)),\n        );\n      }\n\n      outsideClicks.pipe(takeUntil(this.stopOutsideClicksListener)).subscribe(event => {\n        if (!this.isElementInsideMenuStack(_getEventTarget(event)!)) {\n          this.menuStack.closeAll();\n        }\n      });\n    }\n  }\n\n  /**\n   * Open the attached menu at the specified location.\n   * @param userEvent User-generated event that opened the menu\n   * @param coordinates where to open the context menu\n   */\n  private _open(userEvent: MouseEvent | null, coordinates: ContextMenuCoordinates) {\n    if (this.disabled) {\n      return;\n    }\n    if (this.isOpen()) {\n      // since we're moving this menu we need to close any submenus first otherwise they end up\n      // disconnected from this one.\n      this.menuStack.closeSubMenuOf(this.childMenu!);\n\n      (\n        this.overlayRef!.getConfig().positionStrategy as FlexibleConnectedPositionStrategy\n      ).setOrigin(coordinates);\n      this.overlayRef!.updatePosition();\n    } else {\n      this.opened.next();\n\n      if (this.overlayRef) {\n        (\n          this.overlayRef.getConfig().positionStrategy as FlexibleConnectedPositionStrategy\n        ).setOrigin(coordinates);\n        this.overlayRef.updatePosition();\n      } else {\n        this.overlayRef = this._overlay.create(this._getOverlayConfig(coordinates));\n      }\n\n      this.overlayRef.attach(this.getMenuContentPortal());\n      this._subscribeToOutsideClicks(userEvent);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {OverlayModule} from '@angular/cdk/overlay';\nimport {CdkMenu} from './menu';\nimport {CdkMenuBar} from './menu-bar';\nimport {CdkMenuItem} from './menu-item';\nimport {CdkMenuGroup} from './menu-group';\nimport {CdkMenuItemRadio} from './menu-item-radio';\nimport {CdkMenuItemCheckbox} from './menu-item-checkbox';\nimport {CdkMenuTrigger} from './menu-trigger';\nimport {CdkContextMenuTrigger} from './context-menu-trigger';\nimport {CdkTargetMenuAim} from './menu-aim';\n\nconst MENU_DIRECTIVES = [\n  CdkMenuBar,\n  CdkMenu,\n  CdkMenuItem,\n  CdkMenuItemRadio,\n  CdkMenuItemCheckbox,\n  CdkMenuTrigger,\n  CdkMenuGroup,\n  CdkContextMenuTrigger,\n  CdkTargetMenuAim,\n];\n\n/** Module that declares components and directives for the CDK menu. */\n@NgModule({\n  imports: [OverlayModule, ...MENU_DIRECTIVES],\n  exports: MENU_DIRECTIVES,\n})\nexport class CdkMenuModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["nextId"], "mappings": ";;;;;;;;;;;;AAWA;;AAEG;MAWU,YAAY,CAAA;8GAAZ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAZ,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,OAAA,EAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,SAAA,EAFZ,CAAC,EAAC,OAAO,EAAE,yBAAyB,EAAE,QAAQ,EAAE,yBAAyB,EAAC,CAAC,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAE3E,YAAY,EAAA,UAAA,EAAA,CAAA;kBAVxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,UAAU,EAAE,IAAI;AAChB,oBAAA,IAAI,EAAE;AACJ,wBAAA,MAAM,EAAE,OAAO;AACf,wBAAA,OAAO,EAAE,gBAAgB;AAC1B,qBAAA;oBACD,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,yBAAyB,EAAE,QAAQ,EAAE,yBAAyB,EAAC,CAAC;AACvF,iBAAA,CAAA;;;ACXD;MACa,QAAQ,GAAG,IAAI,cAAc,CAAO,UAAU;;ACD3D;IACY,UAIX;AAJD,CAAA,UAAY,SAAS,EAAA;AACnB,IAAA,SAAA,CAAA,SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ,CAAA;AACR,IAAA,SAAA,CAAA,SAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,GAAA,cAAY,CAAA;AACZ,IAAA,SAAA,CAAA,SAAA,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA,GAAA,aAAW,CAAA;AACb,CAAC,EAJW,SAAS,KAAT,SAAS,GAIpB,EAAA,CAAA,CAAA,CAAA;AAQD;MACa,UAAU,GAAG,IAAI,cAAc,CAAY,gBAAgB,EAAE;AAE1E;AACa,MAAA,iCAAiC,GAAG;AAC/C,IAAA,OAAO,EAAE,UAAU;AACnB,IAAA,IAAI,EAAE,CAAC,CAAC,IAAI,QAAQ,EAAE,EAAE,IAAI,QAAQ,EAAE,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;IAChE,UAAU,EAAE,CAAC,eAA2B,KAAK,eAAe,IAAI,IAAI,SAAS,EAAE;EAC/E;AAEF;MACa,wCAAwC,GAAG,CACtD,WAAsC,MAClC;AACJ,IAAA,OAAO,EAAE,UAAU;AACnB,IAAA,IAAI,EAAE,CAAC,CAAC,IAAI,QAAQ,EAAE,EAAE,IAAI,QAAQ,EAAE,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;AAChE,IAAA,UAAU,EAAE,CAAC,eAA2B,KAAK,eAAe,IAAI,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC;AAC9F,CAAA,EAAE;AAkBH;AACA,IAAIA,QAAM,GAAG,CAAC,CAAC;AAEf;;;;;AAKG;MAEU,SAAS,CAAA;AADtB,IAAA,WAAA,GAAA;;AAGW,QAAA,IAAA,CAAA,EAAE,GAAG,CAAA,EAAGA,QAAM,EAAE,EAAE,CAAC;;QAGX,IAAS,CAAA,SAAA,GAAoB,EAAE,CAAC;;AAGhC,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,OAAO,EAAuB,CAAC;;AAG5C,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,OAAO,EAAyB,CAAC;;AAG9C,QAAA,IAAA,CAAA,SAAS,GAAG,IAAI,OAAO,EAAW,CAAC;;AAG3C,QAAA,IAAA,CAAA,MAAM,GAAoC,IAAI,CAAC,MAAM,CAAC;;QAGtD,IAAQ,CAAA,QAAA,GAAwB,IAAI,CAAC,SAAS,CAAC,IAAI,CAC1D,SAAS,CAAC,KAAK,CAAC,EAChB,YAAY,CAAC,CAAC,CAAC,EACf,oBAAoB,EAAE,CACvB,CAAC;AAEF;;;;AAIG;AACM,QAAA,IAAA,CAAA,OAAO,GAAsC,IAAI,CAAC,MAAM,CAAC;AAElE;;;AAGG;QACK,IAAsB,CAAA,sBAAA,GAAqC,IAAI,CAAC;AAqGzE,KAAA;;IAlGC,OAAO,MAAM,CAAC,WAAsC,EAAA;AAClD,QAAA,MAAM,KAAK,GAAG,IAAI,SAAS,EAAE,CAAC;AAC9B,QAAA,KAAK,CAAC,sBAAsB,GAAG,WAAW,CAAC;AAC3C,QAAA,OAAO,KAAK,CAAC;KACd;AAED;;;AAGG;AACH,IAAA,IAAI,CAAC,IAAmB,EAAA;AACtB,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC3B;AAED;;;;;AAKG;IACH,KAAK,CAAC,QAAuB,EAAE,OAAsB,EAAA;QACnD,MAAM,EAAC,gBAAgB,EAAE,kBAAkB,EAAC,GAAG,EAAC,GAAG,OAAO,EAAC,CAAC;QAC5D,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AACzC,YAAA,IAAI,aAAa,CAAC;AAClB,YAAA,GAAG;AACD,gBAAA,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,EAAG,CAAC;AACtC,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,aAAa,EAAE,kBAAkB,EAAC,CAAC,CAAC;AAC9D,aAAC,QAAQ,aAAa,KAAK,QAAQ,EAAE;AAErC,YAAA,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;AAClB,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;aACpC;SACF;KACF;AAED;;;;;AAKG;AACH,IAAA,cAAc,CAAC,QAAuB,EAAA;QACpC,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AACzC,YAAA,OAAO,GAAG,IAAI,CAAC,IAAI,EAAE,KAAK,QAAQ,CAAC;AACnC,YAAA,OAAO,IAAI,CAAC,IAAI,EAAE,KAAK,QAAQ,EAAE;AAC/B,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,EAAG,EAAC,CAAC,CAAC;aACjD;SACF;AACD,QAAA,OAAO,OAAO,CAAC;KAChB;AAED;;;AAGG;AACH,IAAA,QAAQ,CAAC,OAAsB,EAAA;QAC7B,MAAM,EAAC,gBAAgB,EAAE,kBAAkB,EAAC,GAAG,EAAC,GAAG,OAAO,EAAC,CAAC;AAC5D,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE;AACnB,YAAA,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE;gBACtB,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;gBAC3C,IAAI,aAAa,EAAE;AACjB,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,aAAa,EAAE,kBAAkB,EAAC,CAAC,CAAC;iBAC7D;aACF;AACD,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SACpC;KACF;;IAGD,OAAO,GAAA;AACL,QAAA,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;KAC/B;;IAGD,MAAM,GAAA;AACJ,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;KAC9B;;IAGD,IAAI,GAAA;AACF,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;KAClD;;IAGD,aAAa,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC;KAC5C;;IAGD,qBAAqB,GAAA;QACnB,OAAO,IAAI,CAAC,sBAAsB,CAAC;KACpC;;AAGD,IAAA,WAAW,CAAC,QAAiB,EAAA;AAC3B,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KAC/B;8GAzIU,SAAS,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;kHAAT,SAAS,EAAA,CAAA,CAAA,EAAA;;2FAAT,SAAS,EAAA,UAAA,EAAA,CAAA;kBADrB,UAAU;;;AC7CX;MACa,YAAY,GAAG,IAAI,cAAc,CAAqB,kBAAkB,EAAE;AAEvF;MACa,oBAAoB,GAAG,IAAI,cAAc,CACpD,0BAA0B,EAC1B;AACE,IAAA,UAAU,EAAE,MAAM;IAClB,OAAO,EAAE,MAAK;AACZ,QAAA,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;QAChC,OAAO,MAAM,OAAO,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;KACpD;AACF,CAAA,EACD;AAEF;;;AAGG;MAQmB,kBAAkB,CAAA;AAPxC,IAAA,WAAA,GAAA;;AASW,QAAA,IAAA,CAAA,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;;AAGlB,QAAA,IAAA,CAAA,gBAAgB,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC;;AAG5C,QAAA,IAAA,CAAA,SAAS,GAAc,MAAM,CAAC,UAAU,CAAC,CAAC;;AAG1C,QAAA,IAAA,CAAA,kBAAkB,GAAG,MAAM,CAAC,oBAAoB,CAAC,CAAC;;AAS5D,QAAA,IAAA,CAAA,MAAM,GAAuB,IAAI,YAAY,EAAE,CAAC;;AAGhD,QAAA,IAAA,CAAA,MAAM,GAAuB,IAAI,YAAY,EAAE,CAAC;;QAS/C,IAAU,CAAA,UAAA,GAAsB,IAAI,CAAC;;AAG5B,QAAA,IAAA,CAAA,SAAS,GAAkB,IAAI,OAAO,EAAE,CAAC;;QAGzC,IAAyB,CAAA,yBAAA,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AAiFnF,KAAA;IAtEC,WAAW,GAAA;QACT,IAAI,CAAC,eAAe,EAAE,CAAC;AAEvB,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;KAC3B;;IAGD,MAAM,GAAA;QACJ,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,EAAE,CAAC;KACzC;;AAGD,IAAA,iBAAiB,CAAC,KAAW,EAAA;AAC3B,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;KACxB;AAED;;;AAGG;IACO,oBAAoB,GAAA;QAC5B,MAAM,qBAAqB,GAAG,IAAI,CAAC,eAAe,KAAK,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC;AACrF,QAAA,IAAI,IAAI,CAAC,eAAe,KAAK,CAAC,IAAI,CAAC,WAAW,IAAI,qBAAqB,CAAC,EAAE;YACxE,IAAI,CAAC,WAAW,GAAG,IAAI,cAAc,CACnC,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,qBAAqB,EAAE,CAC7B,CAAC;SACH;QAED,OAAO,IAAI,CAAC,WAAW,CAAC;KACzB;AAED;;;;AAIG;AACO,IAAA,wBAAwB,CAAC,OAAgB,EAAA;AACjD,QAAA,KAAK,IAAI,EAAE,GAAmB,OAAO,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,aAAa,IAAI,IAAI,EAAE;AACzE,YAAA,IAAI,EAAE,CAAC,YAAY,CAAC,wBAAwB,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE;AACnE,gBAAA,OAAO,IAAI,CAAC;aACb;SACF;AACD,QAAA,OAAO,KAAK,CAAC;KACd;;IAGO,eAAe,GAAA;AACrB,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;AAC1B,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;SACxB;KACF;;IAGO,qBAAqB,GAAA;AAC3B,QAAA,IAAI,CAAC,kBAAkB;AACrB,YAAA,IAAI,CAAC,kBAAkB;gBACvB,QAAQ,CAAC,MAAM,CAAC;AACd,oBAAA,SAAS,EAAE;AACT,wBAAA,EAAC,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAC;wBACvC,EAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAC;AAChD,qBAAA;oBACD,MAAM,EAAE,IAAI,CAAC,QAAQ;AACtB,iBAAA,CAAC,CAAC;QACL,OAAO,IAAI,CAAC,kBAAkB,CAAC;KAChC;8GAtHmB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAlB,kBAAkB,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,oBAAA,EAAA,eAAA,EAAA,6BAAA,EAAA,cAAA,EAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAlB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAPvC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,IAAI,EAAE;AACJ,wBAAA,sBAAsB,EAAE,eAAe;AACvC,wBAAA,+BAA+B,EAAE,cAAc;AAChD,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;;ACzCD;;;AAGG;SACa,+BAA+B,GAAA;AAC7C,IAAA,MAAM,KAAK,CAAC,4DAA4D,CAAC,CAAC;AAC5E,CAAC;AAED;;;AAGG;SACa,yBAAyB,GAAA;AACvC,IAAA,MAAM,KAAK,CAAC,yCAAyC,CAAC,CAAC;AACzD;;ACcA;MACa,QAAQ,GAAG,IAAI,cAAc,CAAU,cAAc,EAAE;AAEpE;AACA,MAAM,2BAA2B,GAAG,CAAC,CAAC;AAEtC;AACA,MAAM,UAAU,GAAG,CAAC,CAAC;AAErB;;;AAGG;AACH,MAAM,WAAW,GAAG,GAAG,CAAC;AAQxB;AACA,SAAS,QAAQ,CAAC,CAAQ,EAAE,CAAQ,EAAA;AAClC,IAAA,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,CAAC;AAED;AACA,SAAS,aAAa,CAAC,KAAY,EAAE,KAAa,EAAA;IAChD,OAAO,KAAK,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC;AACnC,CAAC;AAKD;;;;;;;AAOG;AACH,SAAS,eAAe,CAAC,aAAsB,EAAE,CAAS,EAAE,CAAS,EAAA;IACnE,MAAM,EAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAC,GAAG,aAAa,CAAC;;;;AAKjD,IAAA,QACE,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,MAAM;AAC9C,SAAC,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,MAAM,CAAC;AACjD,SAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC;SAChD,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,EACvD;AACJ,CAAC;AAED;;;;;;;;;AASG;MAEU,aAAa,CAAA;AAD1B,IAAA,WAAA,GAAA;;AAGmB,QAAA,IAAA,CAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;;QAGzB,IAAO,CAAA,OAAA,GAAY,EAAE,CAAC;;AAYtB,QAAA,IAAA,CAAA,UAAU,GAAkB,IAAI,OAAO,EAAE,CAAC;AA+H5D,KAAA;IA7HC,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;KAC5B;AAED;;;;AAIG;IACH,UAAU,CAAC,IAAU,EAAE,cAA+D,EAAA;AACpF,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,sBAAsB,EAAE,CAAC;KAC/B;AAED;;;;AAIG;AACH,IAAA,MAAM,CAAC,QAAoB,EAAA;;;QAGzB,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,KAAK,YAAY,EAAE;AAC3C,YAAA,QAAQ,EAAE,CAAC;SACZ;QAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAExB,QAAA,MAAM,oBAAoB,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC;QAC/C,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;AAE1C,QAAA,IAAI,SAAS,IAAI,CAAC,oBAAoB,EAAE;AACtC,YAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE;AAC7B,gBAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;aAC9B;iBAAM;AACL,gBAAA,QAAQ,EAAE,CAAC;aACZ;SACF;aAAM,IAAI,CAAC,oBAAoB,EAAE;AAChC,YAAA,QAAQ,EAAE,CAAC;SACZ;KACF;AAED;;;;;;;AAOG;AACK,IAAA,aAAa,CAAC,QAAoB,EAAA;;;;;AAKxC,QAAA,MAAM,SAAS,GAAG,UAAU,CAAC,MAAK;;AAEhC,YAAA,IAAI,IAAI,CAAC,eAAgB,CAAC,aAAa,IAAI,SAAS,KAAK,IAAI,CAAC,UAAU,EAAE;AACxE,gBAAA,QAAQ,EAAE,CAAC;aACZ;AACD,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;SACxB,EAAE,WAAW,CAAkB,CAAC;AAEjC,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;KAC7B;;IAGO,kBAAkB,GAAA;AACxB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC/C,IAAI,CAAC,aAAa,EAAE;AAClB,YAAA,OAAO,KAAK,CAAC;SACd;QAED,IAAI,SAAS,GAAG,CAAC,CAAC;AAClB,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;;;AAGxD,QAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YACjD,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACjC,MAAM,KAAK,GAAG,QAAQ,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AAC5C,YAAA,IAAI,eAAe,CAAC,aAAa,EAAE,KAAK,EAAE,aAAa,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,EAAE;AAC1E,gBAAA,SAAS,EAAE,CAAC;aACb;SACF;QACD,OAAO,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;KAChD;;IAGO,iBAAiB,GAAA;AACvB,QAAA,OAAO,IAAI,CAAC,eAAe,EAAE,eAAe,EAAE,OAAO,EAAE,EAAE,aAAa,CAAC,qBAAqB,EAAE,CAAC;KAChG;AAED;;;AAGG;IACK,gBAAgB,GAAA;AACtB,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;AACzB,gBAAA,+BAA+B,EAAE,CAAC;aACnC;AACD,YAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACf,gBAAA,yBAAyB,EAAE,CAAC;aAC7B;SACF;KACF;;IAGO,sBAAsB,GAAA;AAC5B,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;YAClC,SAAS,CAAa,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,WAAW,CAAC;iBACzD,IAAI,CACH,MAAM,CAAC,CAAC,CAAa,EAAE,KAAa,KAAK,KAAK,GAAG,2BAA2B,KAAK,CAAC,CAAC,EACnF,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAC3B;AACA,iBAAA,SAAS,CAAC,CAAC,KAAiB,KAAI;AAC/B,gBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAC,CAAC,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,EAAE,KAAK,CAAC,OAAO,EAAC,CAAC,CAAC;gBACxD,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,UAAU,EAAE;AACpC,oBAAA,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;iBACtB;AACH,aAAC,CAAC,CAAC;AACP,SAAC,CAAC,CAAC;KACJ;8GA/IU,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;kHAAb,aAAa,EAAA,CAAA,CAAA,EAAA;;2FAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBADzB,UAAU;;AAmJX;;;AAGG;MAOU,gBAAgB,CAAA;8GAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAhB,gBAAgB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,SAAA,EAFhB,CAAC,EAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,EAAC,CAAC,EAAA,QAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAE9C,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAN5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oBAAoB;AAC9B,oBAAA,QAAQ,EAAE,kBAAkB;AAC5B,oBAAA,UAAU,EAAE,IAAI;oBAChB,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,EAAC,CAAC;AAC1D,iBAAA,CAAA;;;ACvPD;AACgB,SAAA,0BAA0B,CACxC,UAAmC,EACnC,KAAoB,EAAA;;AAGpB,IAAA,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;AACpB,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,MAAM,EAAE,GAAG,UAAU,CAAC,aAAa,CAAC;AACpC,IAAA,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;;IAG9B,IAAI,EAAE,CAAC,QAAQ,KAAK,QAAQ,IAAI,CAAE,EAAwB,CAAC,QAAQ,EAAE;AACnE,QAAA,OAAO,OAAO,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK,CAAC;KAC/C;;AAGD,IAAA,IAAI,EAAE,CAAC,QAAQ,KAAK,GAAG,EAAE;QACvB,OAAO,OAAO,KAAK,KAAK,CAAC;KAC1B;;AAGD,IAAA,OAAO,KAAK,CAAC;AACf;;ACCA;;;;;;AAMG;AAyBG,MAAO,cAAe,SAAQ,kBAAkB,CAAA;AAcpD,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE,CAAC;AAdO,QAAA,IAAA,CAAA,WAAW,GAA4B,MAAM,CAAC,UAAU,CAAC,CAAC;AAC1D,QAAA,IAAA,CAAA,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;AAC3B,QAAA,IAAA,CAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AACzB,QAAA,IAAA,CAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC;AAC/C,QAAA,IAAA,CAAA,sBAAsB,GAAG,MAAM,CAAC,qBAAqB,CAAC,CAAC;QACvD,IAAe,CAAA,eAAA,GAAG,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;;QAG3D,IAAW,CAAA,WAAA,GAAG,MAAM,CAAC,QAAQ,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;;QAGjD,IAAQ,CAAA,QAAA,GAAG,MAAM,CAAC,QAAQ,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;QAI7D,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACnC,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,6BAA6B,EAAE,CAAC;QACrC,IAAI,CAAC,QAAQ,EAAE,CAAC;KACjB;;IAGD,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;KAC5C;;IAGD,IAAI,GAAA;AACF,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,EAAE;AAClD,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;AAEnB,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;YACpF,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC;AACpD,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;YACvC,IAAI,CAAC,yBAAyB,EAAE,CAAC;SAClC;KACF;;IAGD,KAAK,GAAA;AACH,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;AACjB,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;AAEnB,YAAA,IAAI,CAAC,UAAW,CAAC,MAAM,EAAE,CAAC;AAC1B,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;SACxC;QACD,IAAI,CAAC,qBAAqB,EAAE,CAAC;KAC9B;AAED;;AAEG;IACH,OAAO,GAAA;QACL,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;AAED;;;AAGG;AACH,IAAA,gBAAgB,CAAC,KAAoB,EAAA;QACnC,MAAM,gBAAgB,GAAG,IAAI,CAAC,WAAW,EAAE,WAAW,KAAK,UAAU,CAAC;AACtE,QAAA,QAAQ,KAAK,CAAC,OAAO;AACnB,YAAA,KAAK,KAAK,CAAC;AACX,YAAA,KAAK,KAAK;;AAER,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,EAAE;oBAClF,IAAI,CAAC,MAAM,EAAE,CAAC;AACd,oBAAA,IAAI,CAAC,SAAS,EAAE,cAAc,CAAC,UAAU,CAAC,CAAC;iBAC5C;gBACD,MAAM;AAER,YAAA,KAAK,WAAW;AACd,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;AAC1B,oBAAA,IAAI,IAAI,CAAC,WAAW,IAAI,gBAAgB,IAAI,IAAI,CAAC,eAAe,EAAE,KAAK,KAAK,KAAK,EAAE;wBACjF,KAAK,CAAC,cAAc,EAAE,CAAC;wBACvB,IAAI,CAAC,IAAI,EAAE,CAAC;AACZ,wBAAA,IAAI,CAAC,SAAS,EAAE,cAAc,CAAC,UAAU,CAAC,CAAC;qBAC5C;iBACF;gBACD,MAAM;AAER,YAAA,KAAK,UAAU;AACb,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;AAC1B,oBAAA,IAAI,IAAI,CAAC,WAAW,IAAI,gBAAgB,IAAI,IAAI,CAAC,eAAe,EAAE,KAAK,KAAK,KAAK,EAAE;wBACjF,KAAK,CAAC,cAAc,EAAE,CAAC;wBACvB,IAAI,CAAC,IAAI,EAAE,CAAC;AACZ,wBAAA,IAAI,CAAC,SAAS,EAAE,cAAc,CAAC,UAAU,CAAC,CAAC;qBAC5C;iBACF;gBACD,MAAM;AAER,YAAA,KAAK,UAAU,CAAC;AAChB,YAAA,KAAK,QAAQ;AACX,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;oBAC1B,IAAI,CAAC,gBAAgB,EAAE;wBACrB,KAAK,CAAC,cAAc,EAAE,CAAC;wBACvB,IAAI,CAAC,IAAI,EAAE,CAAC;wBACZ,KAAK,CAAC,OAAO,KAAK,UAAU;8BACxB,IAAI,CAAC,SAAS,EAAE,cAAc,CAAC,UAAU,CAAC;8BAC1C,IAAI,CAAC,SAAS,EAAE,aAAa,CAAC,UAAU,CAAC,CAAC;qBAC/C;iBACF;gBACD,MAAM;SACT;KACF;;IAGD,YAAY,GAAA;QACV,IAAI,CAAC,MAAM,EAAE,CAAC;AACd,QAAA,IAAI,CAAC,SAAS,EAAE,cAAc,CAAC,OAAO,CAAC,CAAC;KACzC;AAED;;;AAGG;AACH,IAAA,YAAY,CAAC,QAAiB,EAAA;AAC5B,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AACrB,YAAA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;SACtC;KACF;AAED;;;AAGG;IACK,sBAAsB,GAAA;AAC5B,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;YAClC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,YAAY,CAAC;AACpD,iBAAA,IAAI,CACH,MAAM,CAAC,MAAK;gBACV;;AAEE,gBAAA,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,KAAK,OAAO;AAC1D,oBAAA,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;AACzB,oBAAA,CAAC,IAAI,CAAC,MAAM,EAAE,EACd;aACH,CAAC,EACF,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAC1B;iBACA,SAAS,CAAC,MAAK;;AAEd,gBAAA,MAAM,WAAW,GAAG,MAClB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAK;oBACpB,IAAI,CAAC,qBAAqB,EAAE,CAAC;oBAC7B,IAAI,CAAC,IAAI,EAAE,CAAC;AACd,iBAAC,CAAC,CAAC;AAEL,gBAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,oBAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;iBACnC;qBAAM;AACL,oBAAA,WAAW,EAAE,CAAC;iBACf;AACH,aAAC,CAAC,CAAC;AACP,SAAC,CAAC,CAAC;KACJ;;IAGO,qBAAqB,GAAA;AAC3B,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;;;;AAIpB,YAAA,MAAM,eAAe,GACnB,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC;gBAChD,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC;YAE7C,IAAI,eAAe,EAAE;AACnB,gBAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;aAC3B;SACF;aAAM;AACL,YAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;SAC3B;KACF;;IAGO,iBAAiB,GAAA;QACvB,OAAO,IAAI,aAAa,CAAC;AACvB,YAAA,gBAAgB,EAAE,IAAI,CAAC,2BAA2B,EAAE;AACpD,YAAA,cAAc,EAAE,IAAI,CAAC,kBAAkB,EAAE;AACzC,YAAA,SAAS,EAAE,IAAI,CAAC,eAAe,IAAI,SAAS;AAC7C,SAAA,CAAC,CAAC;KACJ;;IAGO,2BAA2B,GAAA;QACjC,OAAO,IAAI,CAAC,QAAQ;AACjB,aAAA,QAAQ,EAAE;AACV,aAAA,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,aAAA,kBAAkB,EAAE;AACpB,aAAA,iBAAiB,EAAE;AACnB,aAAA,aAAa,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC;KAC/C;;IAGO,oBAAoB,GAAA;QAC1B,QACE,IAAI,CAAC,YAAY;aAChB,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,KAAK,YAAY;AACjE,kBAAE,iCAAiC;AACnC,kBAAE,oCAAoC,CAAC,EACzC;KACH;AAED;;;AAGG;IACK,qBAAqB,GAAA;AAC3B,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAC,IAAI,EAAC,KAAI;AACzE,gBAAA,IAAI,IAAI,KAAK,IAAI,CAAC,SAAS,EAAE;oBAC3B,IAAI,CAAC,KAAK,EAAE,CAAC;iBACd;AACH,aAAC,CAAC,CAAC;SACJ;KACF;AAED;;;AAGG;IACK,yBAAyB,GAAA;AAC/B,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,IAAI,CAAC,UAAU;AACZ,iBAAA,oBAAoB,EAAE;AACtB,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;iBAC/C,SAAS,CAAC,KAAK,IAAG;AACjB,gBAAA,MAAM,MAAM,GAAG,eAAe,CAAC,KAAK,CAAY,CAAC;AACjD,gBAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;AAE/C,gBAAA,IAAI,MAAM,KAAK,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;oBACnD,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,EAAE;AAC1C,wBAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;qBAC3B;yBAAM;wBACL,IAAI,CAAC,qBAAqB,EAAE,CAAC;qBAC9B;iBACF;AACH,aAAC,CAAC,CAAC;SACN;KACF;;IAGO,6BAA6B,GAAA;AACnC,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AACrB,YAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,IAAG;gBAC3E,IAAI,CAAC,QAAQ,EAAE;AACb,oBAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;iBAC3B;AACH,aAAC,CAAC,CAAC;SACJ;KACF;;IAGO,2BAA2B,GAAA;AACjC,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AACrB,YAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAC,kBAAkB,EAAC,KAAI;gBACvD,IAAI,kBAAkB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE;AAClD,oBAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;iBACxC;AACH,aAAC,CAAC,CAAC;SACJ;KACF;;IAGO,QAAQ,GAAA;;;AAGd,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SAC/D;KACF;;IAGO,QAAQ,GAAA;AACd,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;AAE/C,QAAA,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE;;AAElE,YAAA,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SACxC;KACF;8GA7RU,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAd,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,cAAc,EALd,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,EAAA,eAAA,EAAA,CAAA,mBAAA,EAAA,iBAAA,CAAA,EAAA,YAAA,EAAA,CAAA,iBAAA,EAAA,cAAA,CAAA,EAAA,QAAA,EAAA,CAAA,oBAAA,EAAA,UAAA,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,eAAA,EAAA,MAAA,EAAA,eAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,SAAA,EAAA,oBAAA,EAAA,UAAA,EAAA,qBAAA,EAAA,SAAA,EAAA,0BAAA,EAAA,OAAA,EAAA,gBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,oBAAA,EAAA,mCAAA,EAAA,oBAAA,EAAA,2CAAA,EAAA,EAAA,cAAA,EAAA,kBAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA,EAAC,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,cAAc,EAAC;YACpD,iCAAiC;AAClC,SAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAEU,cAAc,EAAA,UAAA,EAAA,CAAA;kBAxB1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,qBAAqB;AAC/B,oBAAA,QAAQ,EAAE,mBAAmB;AAC7B,oBAAA,UAAU,EAAE,IAAI;AAChB,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,kBAAkB;AAC3B,wBAAA,sBAAsB,EAAE,iCAAiC;AACzD,wBAAA,sBAAsB,EAAE,2CAA2C;AACnE,wBAAA,WAAW,EAAE,oBAAoB;AACjC,wBAAA,YAAY,EAAE,qBAAqB;AACnC,wBAAA,WAAW,EAAE,0BAA0B;AACvC,wBAAA,SAAS,EAAE,gBAAgB;AAC5B,qBAAA;AACD,oBAAA,MAAM,EAAE;AACN,wBAAA,EAAC,IAAI,EAAE,iBAAiB,EAAE,KAAK,EAAE,mBAAmB,EAAC;AACrD,wBAAA,EAAC,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,iBAAiB,EAAC;AAChD,wBAAA,EAAC,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,oBAAoB,EAAC;AAChD,qBAAA;AACD,oBAAA,OAAO,EAAE,CAAC,uBAAuB,EAAE,uBAAuB,CAAC;AAC3D,oBAAA,SAAS,EAAE;AACT,wBAAA,EAAC,OAAO,EAAE,YAAY,EAAE,WAAW,gBAAgB,EAAC;wBACpD,iCAAiC;AAClC,qBAAA;AACF,iBAAA,CAAA;;;ACpCD;;;;AAIG;MAgBU,WAAW,CAAA;;AAkCtB,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,YAAY,EAAE,eAAe,IAAI,IAAI,CAAC;KACnD;AAcD,IAAA,WAAA,GAAA;QAjDmB,IAAI,CAAA,IAAA,GAAG,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;AAC1D,QAAA,IAAA,CAAA,WAAW,GAA4B,MAAM,CAAC,UAAU,CAAC,CAAC;AACzD,QAAA,IAAA,CAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AAClB,QAAA,IAAA,CAAA,sBAAsB,GAAG,MAAM,CAAC,qBAAqB,CAAC,CAAC;;QAGvD,IAAQ,CAAA,QAAA,GAAG,MAAM,CAAC,QAAQ,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;;AAG9C,QAAA,IAAA,CAAA,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;;QAGhC,IAAW,CAAA,WAAA,GAAG,MAAM,CAAC,QAAQ,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;;AAGjD,QAAA,IAAA,CAAA,YAAY,GAAG,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC,CAAC;;QAGjB,IAAQ,CAAA,QAAA,GAAY,KAAK,CAAC;AAQ9F;;;AAGG;AACsC,QAAA,IAAA,CAAA,SAAS,GAAuB,IAAI,YAAY,EAAE,CAAC;AAO5F;;;AAGG;QACH,IAAS,CAAA,SAAA,GAAW,CAAC,CAAC,CAAC;;QAGb,IAAsB,CAAA,sBAAA,GAAG,IAAI,CAAC;;AAGrB,QAAA,IAAA,CAAA,SAAS,GAAG,IAAI,OAAO,EAAQ,CAAC;QAGjD,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,QAAQ,EAAE,CAAC;AAEhB,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;AAC5B,YAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;SACpB;KACF;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;KAC3B;;IAGD,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;KACxC;AAED;;;;;AAKG;AACH,IAAA,OAAO,CAAC,OAA6B,EAAA;QACnC,MAAM,EAAC,QAAQ,EAAC,GAAG,EAAC,GAAG,OAAO,EAAC,CAAC;QAChC,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACnC,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;YACtB,IAAI,CAAC,QAAQ,EAAE;gBACb,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAC,kBAAkB,EAAE,IAAI,EAAC,CAAC,CAAC;aACtD;SACF;KACF;;IAGD,UAAU,GAAA;QACR,OAAO,CAAC,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,EAAE,CAAC;KACtC;AAED;;;AAGG;IACH,OAAO,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,CAAC;KACrC;;IAGD,cAAc,GAAA;QACZ,OAAO,IAAI,CAAC,YAAY,CAAC;KAC1B;;IAGD,QAAQ,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;KACxF;;IAGD,cAAc,GAAA;AACZ,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE;AAC7B,YAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;SACrB;KACF;AAED;;;AAGG;AACH,IAAA,YAAY,CAAC,KAAkB,EAAA;AAC7B,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,OAAO;SACR;;QAGD,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE;AACxC,YAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;SACpB;KACF;AAED;;;;;AAKG;AACH,IAAA,UAAU,CAAC,KAAoB,EAAA;AAC7B,QAAA,QAAQ,KAAK,CAAC,OAAO;AACnB,YAAA,KAAK,KAAK,CAAC;AACX,YAAA,KAAK,KAAK;;AAER,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,EAAE;AAClF,oBAAA,IAAI,CAAC,OAAO,CAAC,EAAC,QAAQ,EAAE,KAAK,CAAC,OAAO,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAC,CAAC,CAAC;iBACnF;gBACD,MAAM;AAER,YAAA,KAAK,WAAW;AACd,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;oBAC1B,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;wBAChD,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,KAAK,KAAK,EAAE;AAC9B,4BAAA,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;yBAClC;6BAAM;AACL,4BAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;yBAC/B;qBACF;iBACF;gBACD,MAAM;AAER,YAAA,KAAK,UAAU;AACb,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;oBAC1B,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;wBAChD,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,KAAK,KAAK,EAAE;AAC9B,4BAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;yBAC/B;6BAAM;AACL,4BAAA,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;yBAClC;qBACF;iBACF;gBACD,MAAM;SACT;KACF;;IAGO,iBAAiB,GAAA;AACvB,QAAA,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC;KAC1B;AAED;;;AAGG;AACK,IAAA,iBAAiB,CAAC,KAAoB,EAAA;AAC5C,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,WAAY,CAAC;AACrC,QAAA,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;YACnE,KAAK,CAAC,cAAc,EAAE,CAAC;AACvB,YAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,EAAE;gBAChC,gBAAgB,EACd,IAAI,CAAC,UAAU,CAAC,qBAAqB,EAAE,KAAK,YAAY;sBACpD,SAAS,CAAC,YAAY;sBACtB,SAAS,CAAC,WAAW;AAC3B,gBAAA,kBAAkB,EAAE,IAAI;AACzB,aAAA,CAAC,CAAC;SACJ;KACF;AAED;;;AAGG;AACK,IAAA,oBAAoB,CAAC,KAAoB,EAAA;AAC/C,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,CAAC,qBAAqB,EAAE,KAAK,YAAY,EAAE;YAC7E,KAAK,CAAC,cAAc,EAAE,CAAC;AACvB,YAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;gBACvB,gBAAgB,EAAE,SAAS,CAAC,QAAQ;AACpC,gBAAA,kBAAkB,EAAE,IAAI;AACzB,aAAA,CAAC,CAAC;SACJ;KACF;AAED;;;AAGG;IACK,gBAAgB,GAAA;AACtB,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE;YAC7B,MAAM,iBAAiB,GAAG,MACxB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,WAAY,CAAC,CAAC,CAAC;AAE5E,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAC7B,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,YAAY,CAAC;AACpD,iBAAA,IAAI,CACH,MAAM,CAAC,MAAK;gBACV;;AAEE,gBAAA,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,KAAK,OAAO;AAC1D,oBAAA,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;AAC1B,oBAAA,CAAC,IAAI,CAAC,OAAO,EACb;aACH,CAAC,EACF,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAC1B;iBACA,SAAS,CAAC,MAAK;AACd,gBAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,oBAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;iBACzC;qBAAM;AACL,oBAAA,iBAAiB,EAAE,CAAC;iBACrB;aACF,CAAC,CACL,CAAC;SACH;KACF;AAED;;;AAGG;IACK,iBAAiB,GAAA;AACvB,QAAA,OAAO,IAAI,CAAC,WAAW,EAAE,WAAW,KAAK,UAAU,CAAC;KACrD;;IAGO,QAAQ,GAAA;AACd,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;AAE/C,QAAA,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE;;AAElE,YAAA,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SACxC;KACF;8GAlQU,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAX,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,WAAW,yGAmB2B,gBAAgB,CAAA,EAAA,cAAA,EAAA,CAAA,2BAAA,EAAA,gBAAA,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,SAAA,EAAA,sBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,UAAA,EAAA,EAAA,SAAA,EAAA,EAAA,MAAA,EAAA,kBAAA,EAAA,OAAA,EAAA,gBAAA,EAAA,OAAA,EAAA,WAAA,EAAA,SAAA,EAAA,oBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,UAAA,EAAA,WAAA,EAAA,oBAAA,EAAA,kBAAA,EAAA,EAAA,cAAA,EAAA,eAAA,EAAA,EAAA,QAAA,EAAA,CAAA,aAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAnBtD,WAAW,EAAA,UAAA,EAAA,CAAA;kBAfvB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,eAAe;AACzB,oBAAA,QAAQ,EAAE,aAAa;AACvB,oBAAA,UAAU,EAAE,IAAI;AAChB,oBAAA,IAAI,EAAE;AACJ,wBAAA,MAAM,EAAE,UAAU;AAClB,wBAAA,OAAO,EAAE,eAAe;AACxB,wBAAA,YAAY,EAAE,WAAW;AACzB,wBAAA,sBAAsB,EAAE,kBAAkB;AAC1C,wBAAA,QAAQ,EAAE,kBAAkB;AAC5B,wBAAA,SAAS,EAAE,gBAAgB;AAC3B,wBAAA,SAAS,EAAE,WAAW;AACtB,wBAAA,WAAW,EAAE,oBAAoB;AAClC,qBAAA;AACF,iBAAA,CAAA;wDAoBqE,QAAQ,EAAA,CAAA;sBAA3E,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,qBAAqB,EAAE,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAM9B,cAAc,EAAA,CAAA;sBAAjD,KAAK;uBAAC,2BAA2B,CAAA;gBAMO,SAAS,EAAA,CAAA;sBAAjD,MAAM;uBAAC,sBAAsB,CAAA;;;AChEhC;;;AAGG;MACU,mBAAmB,CAAA;AAgB9B,IAAA,WAAA;;IAEmB,MAAoB,EAAA;QAApB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAc;;AAhB9B,QAAA,IAAA,CAAA,OAAO,GAAkB,IAAI,CAAC,sBAAsB,EAAE,CAAC;;AAGvD,QAAA,IAAA,CAAA,MAAM,GAAkB,IAAI,CAAC,oBAAoB,EAAE,CAAC;;AAS5C,QAAA,IAAA,CAAA,UAAU,GAAkB,IAAI,OAAO,EAAE,CAAC;AAMzD,QAAA,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,KAAK,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,CAAC,CAAC;AAClE,QAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAK;AACzB,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC;AAC1C,YAAA,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;AACjC,SAAC,CAAC,CAAC;KACJ;;IAGD,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;KAC5B;AAED;;;AAGG;IACK,sBAAsB,GAAA;AAC5B,QAAA,OAAO,KAAK,CAAC,MACX,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CACtB,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,EACtB,QAAQ,CAAC,CAAC,IAAkB,KAC1B,IAAI,CAAC,GAAG,CAAC,OAAO,IACd,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC,IAAI,CAC7D,KAAK,CAAC,OAAO,CAAC,EACd,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAC/B,CACF,CACF,EACD,QAAQ,EAAE,CACX,CACF,CAAC;KACH;AAED;;;AAGG;IACK,oBAAoB,GAAA;AAC1B,QAAA,OAAO,KAAK,CAAC,MACX,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CACtB,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,EACtB,QAAQ,CAAC,CAAC,IAAkB,KAC1B,IAAI,CAAC,GAAG,CAAC,OAAO,IACd,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,IAAI,CAC3D,KAAK,CAAC,OAAO,CAAC,EACd,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAC/B,CACF,CACF,EACD,QAAQ,EAAE,CACX,CACF,CAAC;KACH;AACF;;AClED;AACA,IAAIA,QAAM,GAAG,CAAC,CAAC;AAEf;;;AAGG;AAeG,MAAgB,WACpB,SAAQ,YAAY,CAAA;AAftB,IAAA,WAAA,GAAA;;;AAmBW,QAAA,IAAA,CAAA,aAAa,GAAgB,MAAM,CAAC,UAAU,CAAC,CAAC,aAAa,CAAC;;AAG7D,QAAA,IAAA,CAAA,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;;AAGzB,QAAA,IAAA,CAAA,SAAS,GAAc,MAAM,CAAC,UAAU,CAAC,CAAC;;AAGhC,QAAA,IAAA,CAAA,OAAO,GAAG,MAAM,CAAC,QAAQ,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC,CAAC;;QAGzD,IAAG,CAAA,GAAA,GAAG,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;;AAGzD,QAAA,IAAA,CAAA,EAAE,GAAG,CAAA,SAAA,EAAYA,QAAM,EAAE,EAAE,CAAC;;QAOrC,IAAW,CAAA,WAAA,GAA8B,UAAU,CAAC;AAEpD;;;AAGG;QACH,IAAQ,CAAA,QAAA,GAAG,KAAK,CAAC;;AAME,QAAA,IAAA,CAAA,SAAS,GAAkB,IAAI,OAAO,EAAE,CAAC;;QASpD,IAAkB,CAAA,kBAAA,GAAG,KAAK,CAAC;AAmIpC,KAAA;IAjIC,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC3B;QACD,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,6BAA6B,EAAE,CAAC;QACrC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACnC,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC7B;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,CAAC;AAC3B,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC1B,QAAA,IAAI,CAAC,cAAc,EAAE,OAAO,EAAE,CAAC;KAChC;AAED;;;AAGG;IACH,cAAc,CAAC,cAA2B,SAAS,EAAA;AACjD,QAAA,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;AAC5C,QAAA,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC;KACtC;AAED;;;AAGG;IACH,aAAa,CAAC,cAA2B,SAAS,EAAA;AAChD,QAAA,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;AAC5C,QAAA,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;KACrC;;IAGD,YAAY,GAAA;AACV,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1D,OAAO,IAAI,CAAC,QAAQ,GAAG,gBAAgB,GAAG,IAAI,CAAC;KAChD;AAED;;;;;AAKG;IACO,aAAa,CAAC,IAAmB,EAAE,OAAwC,EAAA;QACnF,MAAM,EAAC,kBAAkB,EAAC,GAAG,EAAC,GAAG,OAAO,EAAC,CAAC;AAC1C,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;AACnC,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC;QACjC,IAAI,IAAI,KAAK,OAAO,EAAE,cAAc,EAAE,EAAE,OAAO,EAAE,EAAE;AACjD,YAAA,OAAO,EAAE,cAAc,EAAE,EAAE,KAAK,EAAE,CAAC;;;YAGnC,IAAI,kBAAkB,EAAE;gBACtB,IAAI,OAAO,EAAE;AACX,oBAAA,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;iBACnC;qBAAM;oBACL,UAAU,CAAC,kBAAkB,EAAE,CAAC;iBACjC;aACF;SACF;KACF;;IAGO,cAAc,GAAA;AACpB,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC,aAAa,EAAE,CAAC,cAAc,EAAE,CAAC;AAE9F,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,YAAY,EAAE;AACrC,YAAA,IAAI,CAAC,UAAU,CAAC,yBAAyB,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,KAAK,CAAC,CAAC;SACrE;aAAM;AACL,YAAA,IAAI,CAAC,UAAU,CAAC,uBAAuB,EAAE,CAAC;SAC3C;KACF;AAED;;;AAGG;IACK,oBAAoB,GAAA;AAC1B,QAAA,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAChE,IAAI,CAAC,KAAK,CAAC,OAAO;AACf,aAAA,IAAI,CACH,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,EACrB,QAAQ,CAAC,CAAC,IAA4B,KACpC,IAAI;aACD,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC;AAC5B,aAAA,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,cAAc,EAAG,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,CAC1F,EACD,QAAQ,EAAE,EACV,SAAS,CAAC,CAAC,IAAiB,KAAI;AAC9B,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AACxB,YAAA,OAAO,IAAI,CAAC,cAAc,EAAG,CAAC,MAAM,CAAC;SACtC,CAAC,EACF,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAC1B;AACA,aAAA,SAAS,CAAC,OAAO,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC;KACpD;;IAGO,2BAA2B,GAAA;QACjC,IAAI,CAAC,SAAS,CAAC,MAAM;AAClB,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aAC/B,SAAS,CAAC,CAAC,EAAC,IAAI,EAAE,kBAAkB,EAAC,KAAK,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,EAAC,kBAAkB,EAAC,CAAC,CAAC,CAAC;KAC9F;;IAGO,6BAA6B,GAAA;AACnC,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,IAAG;AAC3E,gBAAA,IAAI,CAAC,kBAAkB,GAAG,QAAQ,CAAC;AACrC,aAAC,CAAC,CAAC;SACJ;KACF;AAED;;;AAGG;IACK,oBAAoB,GAAA;AAC1B,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAK;gBACjC,IAAI,CAAC,cAAc,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5D,aAAC,CAAC,CAAC;YACH,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,cAAe,CAAC,CAAC;SACrD;KACF;8GAlLmB,WAAW,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAX,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,WAAW,uZAuBd,WAAW,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAvBR,WAAW,EAAA,UAAA,EAAA,CAAA;kBAdhC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,IAAI,EAAE;AACJ,wBAAA,MAAM,EAAE,MAAM;wBACd,OAAO,EAAE,EAAE;AACX,wBAAA,YAAY,EAAE,gBAAgB;AAC9B,wBAAA,MAAM,EAAE,IAAI;AACZ,wBAAA,yBAAyB,EAAE,aAAa;AACxC,wBAAA,+BAA+B,EAAE,cAAc;AAC/C,wBAAA,SAAS,EAAE,kBAAkB;AAC7B,wBAAA,WAAW,EAAE,6BAA6B;AAC1C,wBAAA,YAAY,EAAE,8BAA8B;AAC7C,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;8BAqBU,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAIG,KAAK,EAAA,CAAA;sBADb,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,WAAW,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC,CAAA;;;ACzDnD;;;;;;AAMG;AAiBG,MAAO,OAAQ,SAAQ,WAAW,CAAA;AAYtC,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE,CAAC;QAZF,IAAc,CAAA,cAAA,GAAG,MAAM,CAAC,YAAY,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;;AAG7C,QAAA,IAAA,CAAA,MAAM,GAAuB,IAAI,YAAY,EAAE,CAAC;;QAGjD,IAAW,CAAA,WAAA,GAAG,UAAU,CAAC;;AAGzB,QAAA,IAAA,CAAA,QAAQ,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC;QAIhD,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACtC,QAAA,IAAI,CAAC,cAAc,EAAE,iBAAiB,CAAC,IAAI,CAAC,CAAC;KAC9C;IAEQ,kBAAkB,GAAA;QACzB,KAAK,CAAC,kBAAkB,EAAE,CAAC;QAC3B,IAAI,CAAC,4BAA4B,EAAE,CAAC;KACrC;IAEQ,WAAW,GAAA;QAClB,KAAK,CAAC,WAAW,EAAE,CAAC;AACpB,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;KACxB;AAED;;;AAGG;AACH,IAAA,eAAe,CAAC,KAAoB,EAAA;AAClC,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;AACnC,QAAA,QAAQ,KAAK,CAAC,OAAO;AACnB,YAAA,KAAK,UAAU,CAAC;AAChB,YAAA,KAAK,WAAW;AACd,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;oBAC1B,KAAK,CAAC,cAAc,EAAE,CAAC;AACvB,oBAAA,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;AACtC,oBAAA,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;iBAC7B;gBACD,MAAM;AAER,YAAA,KAAK,MAAM;AACT,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;oBAC1B,KAAK,CAAC,cAAc,EAAE,CAAC;AACvB,oBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE;wBACzB,gBAAgB,EAAE,SAAS,CAAC,WAAW;AACvC,wBAAA,kBAAkB,EAAE,IAAI;AACzB,qBAAA,CAAC,CAAC;iBACJ;gBACD,MAAM;AAER,YAAA,KAAK,GAAG;AACN,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC,EAAE;oBAC1D,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAC,kBAAkB,EAAE,IAAI,EAAC,CAAC,CAAC;iBACrD;gBACD,MAAM;AAER,YAAA;AACE,gBAAA,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;SAC/B;KACF;AAED;;;AAGG;AACK,IAAA,gBAAgB,CAAC,SAAgC,EAAA;AACvD,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACnC,QAAQ,SAAS;YACf,KAAK,SAAS,CAAC,QAAQ;AACrB,gBAAA,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;gBACtC,UAAU,CAAC,iBAAiB,EAAE,CAAC;gBAC/B,MAAM;YAER,KAAK,SAAS,CAAC,YAAY;AACzB,gBAAA,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;gBACtC,UAAU,CAAC,qBAAqB,EAAE,CAAC;gBACnC,MAAM;YAER,KAAK,SAAS,CAAC,WAAW;AACxB,gBAAA,IAAI,UAAU,CAAC,UAAU,EAAE;AACzB,oBAAA,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;AACtC,oBAAA,UAAU,CAAC,aAAa,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;iBACjD;gBACD,MAAM;SACT;KACF;;IAGO,4BAA4B,GAAA;QAClC,IAAI,CAAC,SAAS,CAAC,OAAO;AACnB,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC/B,aAAA,SAAS,CAAC,KAAK,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;KACrD;8GAhGU,OAAO,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAP,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,OAAO,EANP,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,WAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,MAAA,EAAA,EAAA,SAAA,EAAA,EAAA,SAAA,EAAA,yBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,uBAAA,EAAA,UAAA,EAAA,EAAA,cAAA,EAAA,UAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA,EAAC,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,OAAO,EAAC;AAC7C,YAAA,EAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAC;YACzC,wCAAwC,CAAC,UAAU,CAAC;AACrD,SAAA,EAAA,QAAA,EAAA,CAAA,SAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAEU,OAAO,EAAA,UAAA,EAAA,CAAA;kBAhBnB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,WAAW;AACrB,oBAAA,QAAQ,EAAE,SAAS;AACnB,oBAAA,UAAU,EAAE,IAAI;AAChB,oBAAA,IAAI,EAAE;AACJ,wBAAA,MAAM,EAAE,MAAM;AACd,wBAAA,OAAO,EAAE,UAAU;AACnB,wBAAA,yBAAyB,EAAE,UAAU;AACrC,wBAAA,WAAW,EAAE,yBAAyB;AACvC,qBAAA;AACD,oBAAA,SAAS,EAAE;AACT,wBAAA,EAAC,OAAO,EAAE,YAAY,EAAE,WAAW,SAAS,EAAC;AAC7C,wBAAA,EAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,SAAS,EAAC;wBACzC,wCAAwC,CAAC,UAAU,CAAC;AACrD,qBAAA;AACF,iBAAA,CAAA;wDAKoB,MAAM,EAAA,CAAA;sBAAxB,MAAM;;;ACpBT;;;;;AAKG;AAgBG,MAAO,UAAW,SAAQ,WAAW,CAAA;AAf3C,IAAA,WAAA,GAAA;;;QAiBoB,IAAW,CAAA,WAAA,GAAG,YAAY,CAAC;;QAG3B,IAAQ,CAAA,QAAA,GAAG,IAAI,CAAC;AA2FnC,KAAA;IAzFU,kBAAkB,GAAA;QACzB,KAAK,CAAC,kBAAkB,EAAE,CAAC;QAC3B,IAAI,CAAC,4BAA4B,EAAE,CAAC;KACrC;AAED;;;AAGG;AACH,IAAA,eAAe,CAAC,KAAoB,EAAA;AAClC,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;AACnC,QAAA,QAAQ,KAAK,CAAC,OAAO;AACnB,YAAA,KAAK,QAAQ,CAAC;AACd,YAAA,KAAK,UAAU,CAAC;AAChB,YAAA,KAAK,UAAU,CAAC;AAChB,YAAA,KAAK,WAAW;AACd,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;AAC1B,oBAAA,MAAM,gBAAgB,GAAG,KAAK,CAAC,OAAO,KAAK,UAAU,IAAI,KAAK,CAAC,OAAO,KAAK,WAAW,CAAC;;;;oBAIvF,IAAI,gBAAgB,EAAE;wBACpB,KAAK,CAAC,cAAc,EAAE,CAAC;wBAEvB,MAAM,UAAU,GAAG,UAAU,CAAC,UAAU,EAAE,UAAU,EAAE,CAAC;wBACvD,UAAU,CAAC,UAAU,EAAE,cAAc,EAAE,EAAE,KAAK,EAAE,CAAC;AAEjD,wBAAA,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;AACtC,wBAAA,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;wBAC5B,IAAI,UAAU,EAAE;4BACd,UAAU,CAAC,UAAU,EAAE,cAAc,EAAE,EAAE,IAAI,EAAE,CAAC;yBACjD;qBACF;iBACF;gBACD,MAAM;AAER,YAAA,KAAK,MAAM;AACT,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;oBAC1B,KAAK,CAAC,cAAc,EAAE,CAAC;oBACvB,UAAU,CAAC,UAAU,EAAE,cAAc,EAAE,EAAE,KAAK,EAAE,CAAC;iBAClD;gBACD,MAAM;AAER,YAAA,KAAK,GAAG;AACN,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC,EAAE;oBAC1D,UAAU,CAAC,UAAU,EAAE,cAAc,EAAE,EAAE,KAAK,EAAE,CAAC;iBAClD;gBACD,MAAM;AAER,YAAA;AACE,gBAAA,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;SAC/B;KACF;AAED;;;;AAIG;AACK,IAAA,eAAe,CAAC,SAAgC,EAAA;AACtD,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACnC,QAAQ,SAAS;YACf,KAAK,SAAS,CAAC,QAAQ;AACrB,gBAAA,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;gBACtC,UAAU,CAAC,iBAAiB,EAAE,CAAC;gBAC/B,UAAU,CAAC,UAAU,EAAE,cAAc,EAAE,EAAE,IAAI,EAAE,CAAC;gBAChD,MAAM;YAER,KAAK,SAAS,CAAC,YAAY;AACzB,gBAAA,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;gBACtC,UAAU,CAAC,qBAAqB,EAAE,CAAC;gBACnC,UAAU,CAAC,UAAU,EAAE,cAAc,EAAE,EAAE,IAAI,EAAE,CAAC;gBAChD,MAAM;YAER,KAAK,SAAS,CAAC,WAAW;AACxB,gBAAA,IAAI,UAAU,CAAC,UAAU,EAAE;AACzB,oBAAA,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;AACtC,oBAAA,UAAU,CAAC,aAAa,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;iBACjD;gBACD,MAAM;SACT;KACF;;IAGO,4BAA4B,GAAA;QAClC,IAAI,CAAC,SAAS,EAAE,OAAO;AACpB,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC/B,aAAA,SAAS,CAAC,KAAK,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;KACpD;8GA/FU,UAAU,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAV,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAU,EANV,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,SAAA,EAAA,EAAA,SAAA,EAAA,EAAA,SAAA,EAAA,yBAAA,EAAA,EAAA,cAAA,EAAA,cAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA,EAAC,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,UAAU,EAAC;AAChD,YAAA,EAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAC;AAC5C,YAAA,EAAC,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,EAAC;AACxE,SAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAEU,UAAU,EAAA,UAAA,EAAA,CAAA;kBAftB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,QAAQ,EAAE,YAAY;AACtB,oBAAA,UAAU,EAAE,IAAI;AAChB,oBAAA,IAAI,EAAE;AACJ,wBAAA,MAAM,EAAE,SAAS;AACjB,wBAAA,OAAO,EAAE,cAAc;AACvB,wBAAA,WAAW,EAAE,yBAAyB;AACvC,qBAAA;AACD,oBAAA,SAAS,EAAE;AACT,wBAAA,EAAC,OAAO,EAAE,YAAY,EAAE,WAAW,YAAY,EAAC;AAChD,wBAAA,EAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,YAAY,EAAC;AAC5C,wBAAA,EAAC,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,EAAC;AACxE,qBAAA;AACF,iBAAA,CAAA;;;ACjCD;AAQM,MAAgB,qBAAsB,SAAQ,WAAW,CAAA;AAP/D,IAAA,WAAA,GAAA;;;QASqE,IAAO,CAAA,OAAA,GAAY,KAAK,CAAC;;QAGzE,IAAsB,CAAA,sBAAA,GAAG,KAAK,CAAC;AACnD,KAAA;8GANqB,qBAAqB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAArB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,qBAAqB,2EAEO,gBAAgB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,mBAAA,EAAA,WAAA,EAAA,oBAAA,EAAA,kBAAA,EAAA,EAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAF5C,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBAP1C,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,IAAI,EAAE;AACJ,wBAAA,qBAAqB,EAAE,WAAW;AAClC,wBAAA,sBAAsB,EAAE,kBAAkB;AAC3C,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;8BAGoE,OAAO,EAAA,CAAA;sBAAzE,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,oBAAoB,EAAE,SAAS,EAAE,gBAAgB,EAAC,CAAA;;;ACRnE;AACA,IAAI,MAAM,GAAG,CAAC,CAAC;AAEf;;;;AAIG;AAcG,MAAO,gBAAiB,SAAQ,qBAAqB,CAAA;AAUzD,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE,CAAC;;AATO,QAAA,IAAA,CAAA,oBAAoB,GAAG,MAAM,CAAC,yBAAyB,CAAC,CAAC;;AAGlE,QAAA,IAAA,CAAA,GAAG,GAAG,CAAA,EAAG,MAAM,EAAE,EAAE,CAAC;QAO1B,IAAI,CAAC,2BAA2B,EAAE,CAAC;KACpC;IAEQ,WAAW,GAAA;QAClB,KAAK,CAAC,WAAW,EAAE,CAAC;QAEpB,IAAI,CAAC,yBAAyB,EAAE,CAAC;KAClC;AAED;;;;AAIG;AACM,IAAA,OAAO,CAAC,OAA6B,EAAA;AAC5C,QAAA,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAEvB,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;SAChD;KACF;;IAGO,2BAA2B,GAAA;AACjC,QAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,EAAU,KAAI;YAC/E,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC;AACjC,SAAC,CAAC,CAAC;KACJ;8GAvCU,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAhB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,gBAAgB,EALhB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,eAAA,EAAA,EAAA,UAAA,EAAA,EAAA,2BAAA,EAAA,MAAA,EAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA,EAAC,OAAO,EAAE,qBAAqB,EAAE,WAAW,EAAE,gBAAgB,EAAC;AAC/D,YAAA,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,qBAAqB,EAAC;AAC3D,SAAA,EAAA,QAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAEU,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAb5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oBAAoB;AAC9B,oBAAA,QAAQ,EAAE,kBAAkB;AAC5B,oBAAA,UAAU,EAAE,IAAI;AAChB,oBAAA,IAAI,EAAE;AACJ,wBAAA,MAAM,EAAE,eAAe;AACvB,wBAAA,6BAA6B,EAAE,MAAM;AACtC,qBAAA;AACD,oBAAA,SAAS,EAAE;AACT,wBAAA,EAAC,OAAO,EAAE,qBAAqB,EAAE,WAAW,kBAAkB,EAAC;AAC/D,wBAAA,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,qBAAqB,EAAC;AAC3D,qBAAA;AACF,iBAAA,CAAA;;;ACrBD;;;AAGG;AAcG,MAAO,mBAAoB,SAAQ,qBAAqB,CAAA;AAC5D;;;;AAIG;AACM,IAAA,OAAO,CAAC,OAA6B,EAAA;AAC5C,QAAA,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAEvB,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC;SAC9B;KACF;8GAZU,mBAAmB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAnB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,mBAAmB,EALnB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,uBAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,kBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,8BAAA,EAAA,MAAA,EAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA,EAAC,OAAO,EAAE,qBAAqB,EAAE,WAAW,EAAE,mBAAmB,EAAC;AAClE,YAAA,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,qBAAqB,EAAC;AAC3D,SAAA,EAAA,QAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAEU,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAb/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,uBAAuB;AACjC,oBAAA,QAAQ,EAAE,qBAAqB;AAC/B,oBAAA,UAAU,EAAE,IAAI;AAChB,oBAAA,IAAI,EAAE;AACJ,wBAAA,MAAM,EAAE,kBAAkB;AAC1B,wBAAA,gCAAgC,EAAE,MAAM;AACzC,qBAAA;AACD,oBAAA,SAAS,EAAE;AACT,wBAAA,EAAC,OAAO,EAAE,qBAAqB,EAAE,WAAW,qBAAqB,EAAC;AAClE,wBAAA,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,qBAAqB,EAAC;AAC3D,qBAAA;AACF,iBAAA,CAAA;;;ACND;AACA,MAAM,sBAAsB,GAAG,iCAAiC,CAAC,GAAG,CAAC,QAAQ,IAAG;;;AAG9E,IAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,QAAQ,KAAK,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACvD,IAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,QAAQ,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACrD,OAAO,EAAC,GAAG,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAC,CAAC;AACzC,CAAC,CAAC,CAAC;AAEH;MAEa,kBAAkB,CAAA;AAI7B;;;AAGG;AACH,IAAA,MAAM,CAAC,OAA8B,EAAA;AACnC,QAAA,IAAI,kBAAkB,CAAC,uBAAuB,KAAK,OAAO,EAAE;AAC1D,YAAA,kBAAkB,CAAC,uBAAuB,EAAE,KAAK,EAAE,CAAC;AACpD,YAAA,kBAAkB,CAAC,uBAAuB,GAAG,OAAO,CAAC;SACtD;KACF;8GAbU,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAAlB,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,cADN,MAAM,EAAA,CAAA,CAAA,EAAA;;2FAClB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAD9B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;AAoBhC;;;AAGG;AAoBG,MAAO,qBAAsB,SAAQ,kBAAkB,CAAA;AAa3D,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE,CAAC;;AAZO,QAAA,IAAA,CAAA,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;;QAG3B,IAAe,CAAA,eAAA,GAAG,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;;AAG3D,QAAA,IAAA,CAAA,mBAAmB,GAAG,MAAM,CAAC,kBAAkB,CAAC,CAAC;;QAGK,IAAQ,CAAA,QAAA,GAAY,KAAK,CAAC;QAI/F,IAAI,CAAC,0BAA0B,EAAE,CAAC;KACnC;AAED;;;AAGG;AACH,IAAA,IAAI,CAAC,WAAmC,EAAA;AACtC,QAAA,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;KAC/B;;IAGD,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;KAC3B;AAED;;;AAGG;AACH,IAAA,kBAAkB,CAAC,KAAiB,EAAA;AAClC,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;;YAElB,KAAK,CAAC,cAAc,EAAE,CAAC;;;;YAKvB,KAAK,CAAC,eAAe,EAAE,CAAC;AAExB,YAAA,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACtC,YAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,EAAC,CAAC,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,EAAE,KAAK,CAAC,OAAO,EAAC,CAAC,CAAC;;AAGxD,YAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AACtB,gBAAA,IAAI,CAAC,SAAS,EAAE,cAAc,CAAC,OAAO,CAAC,CAAC;aACzC;AAAM,iBAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AAC7B,gBAAA,IAAI,CAAC,SAAS,EAAE,cAAc,CAAC,UAAU,CAAC,CAAC;aAC5C;iBAAM;AACL,gBAAA,IAAI,CAAC,SAAS,EAAE,cAAc,CAAC,SAAS,CAAC,CAAC;aAC3C;SACF;KACF;AAED;;;AAGG;AACK,IAAA,iBAAiB,CAAC,WAAmC,EAAA;QAC3D,OAAO,IAAI,aAAa,CAAC;AACvB,YAAA,gBAAgB,EAAE,IAAI,CAAC,2BAA2B,CAAC,WAAW,CAAC;AAC/D,YAAA,cAAc,EAAE,IAAI,CAAC,kBAAkB,EAAE;AACzC,YAAA,SAAS,EAAE,IAAI,CAAC,eAAe,IAAI,SAAS;AAC7C,SAAA,CAAC,CAAC;KACJ;AAED;;;AAGG;AACK,IAAA,2BAA2B,CACjC,WAAmC,EAAA;QAEnC,OAAO,IAAI,CAAC,QAAQ;AACjB,aAAA,QAAQ,EAAE;aACV,mBAAmB,CAAC,WAAW,CAAC;AAChC,aAAA,kBAAkB,EAAE;AACpB,aAAA,iBAAiB,EAAE;AACnB,aAAA,aAAa,CAAC,IAAI,CAAC,YAAY,IAAI,sBAAsB,CAAC,CAAC;KAC/D;;IAGO,0BAA0B,GAAA;QAChC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAC,IAAI,EAAC,KAAI;YACzE,IAAI,IAAI,KAAK,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;AAC5C,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;AACnB,gBAAA,IAAI,CAAC,UAAW,CAAC,MAAM,EAAE,CAAC;aAC3B;AACH,SAAC,CAAC,CAAC;KACJ;AAED;;;;AAIG;AACK,IAAA,yBAAyB,CAAC,SAA4B,EAAA;AAC5D,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,oBAAoB,EAAE,CAAC;YAE3D,IAAI,SAAS,EAAE;gBACb,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC,EAAC,IAAI,EAAC,KAAK,IAAI,KAAK,UAAU,CAAC,CAAC;AAC5F,gBAAA,aAAa,GAAG,KAAK;;;;;;;gBAOnB,YAAY,CAAC,IAAI,CACf,SAAS,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK,SAAS,CAAC,OAAO,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAC/E;;;gBAID,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CACxB,CAAC;aACH;AAED,YAAA,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,IAAG;gBAC9E,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,KAAK,CAAE,CAAC,EAAE;AAC3D,oBAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;iBAC3B;AACH,aAAC,CAAC,CAAC;SACJ;KACF;AAED;;;;AAIG;IACK,KAAK,CAAC,SAA4B,EAAE,WAAmC,EAAA;AAC7E,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,OAAO;SACR;AACD,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;;;YAGjB,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,SAAU,CAAC,CAAC;AAG7C,YAAA,IAAI,CAAC,UAAW,CAAC,SAAS,EAAE,CAAC,gBAC9B,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;AACzB,YAAA,IAAI,CAAC,UAAW,CAAC,cAAc,EAAE,CAAC;SACnC;aAAM;AACL,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;AAEnB,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AAEjB,gBAAA,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC,gBAC7B,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;AACzB,gBAAA,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC;aAClC;iBAAM;AACL,gBAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC,CAAC;aAC7E;YAED,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC;AACpD,YAAA,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC;SAC3C;KACF;8GArKU,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAArB,qBAAqB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,4BAAA,EAAA,MAAA,EAAA,EAAA,eAAA,EAAA,CAAA,0BAAA,EAAA,iBAAA,CAAA,EAAA,YAAA,EAAA,CAAA,wBAAA,EAAA,cAAA,CAAA,EAAA,QAAA,EAAA,CAAA,2BAAA,EAAA,UAAA,CAAA,EAAA,QAAA,EAAA,CAAA,wBAAA,EAAA,UAAA,EAWoB,gBAAgB,CAhBzD,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,sBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,aAAA,EAAA,4BAAA,EAAA,EAAA,UAAA,EAAA,EAAA,6BAAA,EAAA,MAAA,EAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA,EAAC,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,qBAAqB,EAAC;AAC3D,YAAA,EAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAC;AAC3C,SAAA,EAAA,QAAA,EAAA,CAAA,0BAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAEU,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBAnBjC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,4BAA4B;AACtC,oBAAA,QAAQ,EAAE,0BAA0B;AACpC,oBAAA,UAAU,EAAE,IAAI;AAChB,oBAAA,IAAI,EAAE;AACJ,wBAAA,+BAA+B,EAAE,MAAM;AACvC,wBAAA,eAAe,EAAE,4BAA4B;AAC9C,qBAAA;AACD,oBAAA,MAAM,EAAE;AACN,wBAAA,EAAC,IAAI,EAAE,iBAAiB,EAAE,KAAK,EAAE,0BAA0B,EAAC;AAC5D,wBAAA,EAAC,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,wBAAwB,EAAC;AACvD,wBAAA,EAAC,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,2BAA2B,EAAC;AACvD,qBAAA;AACD,oBAAA,OAAO,EAAE,CAAC,8BAA8B,EAAE,8BAA8B,CAAC;AACzE,oBAAA,SAAS,EAAE;AACT,wBAAA,EAAC,OAAO,EAAE,YAAY,EAAE,WAAW,uBAAuB,EAAC;AAC3D,wBAAA,EAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAC;AAC3C,qBAAA;AACF,iBAAA,CAAA;wDAYwE,QAAQ,EAAA,CAAA;sBAA9E,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,wBAAwB,EAAE,SAAS,EAAE,gBAAgB,EAAC,CAAA;;;AClEvE,MAAM,eAAe,GAAG;IACtB,UAAU;IACV,OAAO;IACP,WAAW;IACX,gBAAgB;IAChB,mBAAmB;IACnB,cAAc;IACd,YAAY;IACZ,qBAAqB;IACrB,gBAAgB;CACjB,CAAC;AAEF;MAKa,aAAa,CAAA;8GAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;+GAAb,aAAa,EAAA,OAAA,EAAA,CAHd,aAAa,EAbvB,UAAU;YACV,OAAO;YACP,WAAW;YACX,gBAAgB;YAChB,mBAAmB;YACnB,cAAc;YACd,YAAY;YACZ,qBAAqB;AACrB,YAAA,gBAAgB,aARhB,UAAU;YACV,OAAO;YACP,WAAW;YACX,gBAAgB;YAChB,mBAAmB;YACnB,cAAc;YACd,YAAY;YACZ,qBAAqB;YACrB,gBAAgB,CAAA,EAAA,CAAA,CAAA,EAAA;AAQL,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,YAHd,aAAa,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAGZ,aAAa,EAAA,UAAA,EAAA,CAAA;kBAJzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,aAAa,EAAE,GAAG,eAAe,CAAC;AAC5C,oBAAA,OAAO,EAAE,eAAe;AACzB,iBAAA,CAAA;;;ACpCD;;AAEG;;;;"}