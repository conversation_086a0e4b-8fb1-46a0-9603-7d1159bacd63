/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { TileCoordinator } from './tile-coordinator';
export * from './grid-list-module';
export * from './grid-list';
export * from './grid-tile';
// Privately exported for the grid-list harness.
export const ɵTileCoordinator = TileCoordinator;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicHVibGljLWFwaS5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uL3NyYy9tYXRlcmlhbC9ncmlkLWxpc3QvcHVibGljLWFwaS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7O0dBTUc7QUFDSCxPQUFPLEVBQUMsZUFBZSxFQUFDLE1BQU0sb0JBQW9CLENBQUM7QUFFbkQsY0FBYyxvQkFBb0IsQ0FBQztBQUNuQyxjQUFjLGFBQWEsQ0FBQztBQUM1QixjQUFjLGFBQWEsQ0FBQztBQUU1QixnREFBZ0Q7QUFDaEQsTUFBTSxDQUFDLE1BQU0sZ0JBQWdCLEdBQUcsZUFBZSxDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5pbXBvcnQge1RpbGVDb29yZGluYXRvcn0gZnJvbSAnLi90aWxlLWNvb3JkaW5hdG9yJztcblxuZXhwb3J0ICogZnJvbSAnLi9ncmlkLWxpc3QtbW9kdWxlJztcbmV4cG9ydCAqIGZyb20gJy4vZ3JpZC1saXN0JztcbmV4cG9ydCAqIGZyb20gJy4vZ3JpZC10aWxlJztcblxuLy8gUHJpdmF0ZWx5IGV4cG9ydGVkIGZvciB0aGUgZ3JpZC1saXN0IGhhcm5lc3MuXG5leHBvcnQgY29uc3QgybVUaWxlQ29vcmRpbmF0b3IgPSBUaWxlQ29vcmRpbmF0b3I7XG4iXX0=