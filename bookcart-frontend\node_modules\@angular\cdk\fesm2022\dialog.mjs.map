{"version": 3, "file": "dialog.mjs", "sources": ["../../../../../../src/cdk/dialog/dialog-config.ts", "../../../../../../src/cdk/dialog/dialog-container.ts", "../../../../../../src/cdk/dialog/dialog-container.html", "../../../../../../src/cdk/dialog/dialog-ref.ts", "../../../../../../src/cdk/dialog/dialog-injectors.ts", "../../../../../../src/cdk/dialog/dialog.ts", "../../../../../../src/cdk/dialog/dialog-module.ts", "../../../../../../src/cdk/dialog/dialog_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ViewContainerRef,\n  ComponentFactoryResolver,\n  Injector,\n  StaticProvider,\n  Type,\n} from '@angular/core';\nimport {Direction} from '@angular/cdk/bidi';\nimport {PositionStrategy, ScrollStrategy} from '@angular/cdk/overlay';\nimport {BasePortalOutlet} from '@angular/cdk/portal';\n\n/** Options for where to set focus to automatically on dialog open */\nexport type AutoFocusTarget = 'dialog' | 'first-tabbable' | 'first-heading';\n\n/** Valid ARIA roles for a dialog. */\nexport type DialogRole = 'dialog' | 'alertdialog';\n\n/** Configuration for opening a modal dialog. */\nexport class DialogConfig<D = unknown, R = unknown, C extends BasePortalOutlet = BasePortalOutlet> {\n  /**\n   * Where the attached component should live in Angular's *logical* component tree.\n   * This affects what is available for injection and the change detection order for the\n   * component instantiated inside of the dialog. This does not affect where the dialog\n   * content will be rendered.\n   */\n  viewContainerRef?: ViewContainerRef;\n\n  /**\n   * Injector used for the instantiation of the component to be attached. If provided,\n   * takes precedence over the injector indirectly provided by `ViewContainerRef`.\n   */\n  injector?: Injector;\n\n  /** ID for the dialog. If omitted, a unique one will be generated. */\n  id?: string;\n\n  /** The ARIA role of the dialog element. */\n  role?: DialogRole = 'dialog';\n\n  /** Optional CSS class or classes applied to the overlay panel. */\n  panelClass?: string | string[] = '';\n\n  /** Whether the dialog has a backdrop. */\n  hasBackdrop?: boolean = true;\n\n  /** Optional CSS class or classes applied to the overlay backdrop. */\n  backdropClass?: string | string[] = '';\n\n  /** Whether the dialog closes with the escape key or pointer events outside the panel element. */\n  disableClose?: boolean = false;\n\n  /** Width of the dialog. */\n  width?: string = '';\n\n  /** Height of the dialog. */\n  height?: string = '';\n\n  /** Min-width of the dialog. If a number is provided, assumes pixel units. */\n  minWidth?: number | string;\n\n  /** Min-height of the dialog. If a number is provided, assumes pixel units. */\n  minHeight?: number | string;\n\n  /** Max-width of the dialog. If a number is provided, assumes pixel units. Defaults to 80vw. */\n  maxWidth?: number | string;\n\n  /** Max-height of the dialog. If a number is provided, assumes pixel units. */\n  maxHeight?: number | string;\n\n  /** Strategy to use when positioning the dialog. Defaults to centering it on the page. */\n  positionStrategy?: PositionStrategy;\n\n  /** Data being injected into the child component. */\n  data?: D | null = null;\n\n  /** Layout direction for the dialog's content. */\n  direction?: Direction;\n\n  /** ID of the element that describes the dialog. */\n  ariaDescribedBy?: string | null = null;\n\n  /** ID of the element that labels the dialog. */\n  ariaLabelledBy?: string | null = null;\n\n  /** Dialog label applied via `aria-label` */\n  ariaLabel?: string | null = null;\n\n  /** Whether this is a modal dialog. Used to set the `aria-modal` attribute. */\n  ariaModal?: boolean = true;\n\n  /**\n   * Where the dialog should focus on open.\n   * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n   * AutoFocusTarget instead.\n   */\n  autoFocus?: AutoFocusTarget | string | boolean = 'first-tabbable';\n\n  /**\n   * Whether the dialog should restore focus to the previously-focused element upon closing.\n   * Has the following behavior based on the type that is passed in:\n   * - `boolean` - when true, will return focus to the element that was focused before the dialog\n   *    was opened, otherwise won't restore focus at all.\n   * - `string` - focus will be restored to the first element that matches the CSS selector.\n   * - `HTMLElement` - focus will be restored to the specific element.\n   */\n  restoreFocus?: boolean | string | HTMLElement = true;\n\n  /**\n   * Scroll strategy to be used for the dialog. This determines how\n   * the dialog responds to scrolling underneath the panel element.\n   */\n  scrollStrategy?: ScrollStrategy;\n\n  /**\n   * Whether the dialog should close when the user navigates backwards or forwards through browser\n   * history. This does not apply to navigation via anchor element unless using URL-hash based\n   * routing (`HashLocationStrategy` in the Angular router).\n   */\n  closeOnNavigation?: boolean = true;\n\n  /**\n   * Whether the dialog should close when the dialog service is destroyed. This is useful if\n   * another service is wrapping the dialog and is managing the destruction instead.\n   */\n  closeOnDestroy?: boolean = true;\n\n  /**\n   * Whether the dialog should close when the underlying overlay is detached. This is useful if\n   * another service is wrapping the dialog and is managing the destruction instead. E.g. an\n   * external detachment can happen as a result of a scroll strategy triggering it or when the\n   * browser location changes.\n   */\n  closeOnOverlayDetachments?: boolean = true;\n\n  /** Alternate `ComponentFactoryResolver` to use when resolving the associated component. */\n  componentFactoryResolver?: ComponentFactoryResolver;\n\n  /**\n   * Providers that will be exposed to the contents of the dialog. Can also\n   * be provided as a function in order to generate the providers lazily.\n   */\n  providers?:\n    | StaticProvider[]\n    | ((dialogRef: R, config: DialogConfig<D, R, C>, container: C) => StaticProvider[]);\n\n  /**\n   * Component into which the dialog content will be rendered. Defaults to `CdkDialogContainer`.\n   * A configuration object can be passed in to customize the providers that will be exposed\n   * to the dialog container.\n   */\n  container?:\n    | Type<C>\n    | {\n        type: Type<C>;\n        providers: (config: DialogConfig<D, R, C>) => StaticProvider[];\n      };\n\n  /**\n   * Context that will be passed to template-based dialogs.\n   * A function can be passed in to resolve the context lazily.\n   */\n  templateContext?: Record<string, any> | (() => Record<string, any>);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  FocusMonitor,\n  FocusOrigin,\n  FocusTrap,\n  FocusTrapFactory,\n  InteractivityChecker,\n} from '@angular/cdk/a11y';\nimport {OverlayRef} from '@angular/cdk/overlay';\nimport {Platform, _getFocusedElementPierceShadowDom} from '@angular/cdk/platform';\nimport {\n  BasePortalOutlet,\n  CdkPortalOutlet,\n  ComponentPortal,\n  DomPortal,\n  TemplatePortal,\n} from '@angular/cdk/portal';\nimport {DOCUMENT} from '@angular/common';\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ComponentRef,\n  ElementRef,\n  EmbeddedViewRef,\n  Inject,\n  NgZone,\n  OnDestroy,\n  Optional,\n  ViewChild,\n  ViewEncapsulation,\n  inject,\n} from '@angular/core';\nimport {DialogConfig} from './dialog-config';\n\nexport function throwDialogContentAlreadyAttachedError() {\n  throw Error('Attempting to attach dialog content after content is already attached');\n}\n\n/**\n * Internal component that wraps user-provided dialog content.\n * @docs-private\n */\n@Component({\n  selector: 'cdk-dialog-container',\n  templateUrl: './dialog-container.html',\n  styleUrl: 'dialog-container.css',\n  encapsulation: ViewEncapsulation.None,\n  // Using OnPush for dialogs caused some G3 sync issues. Disabled until we can track them down.\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  standalone: true,\n  imports: [CdkPortalOutlet],\n  host: {\n    'class': 'cdk-dialog-container',\n    'tabindex': '-1',\n    '[attr.id]': '_config.id || null',\n    '[attr.role]': '_config.role',\n    '[attr.aria-modal]': '_config.ariaModal',\n    '[attr.aria-labelledby]': '_config.ariaLabel ? null : _ariaLabelledByQueue[0]',\n    '[attr.aria-label]': '_config.ariaLabel',\n    '[attr.aria-describedby]': '_config.ariaDescribedBy || null',\n  },\n})\nexport class CdkDialogContainer<C extends DialogConfig = DialogConfig>\n  extends BasePortalOutlet\n  implements OnDestroy\n{\n  private _platform = inject(Platform);\n  protected _document: Document;\n\n  /** The portal outlet inside of this container into which the dialog content will be loaded. */\n  @ViewChild(CdkPortalOutlet, {static: true}) _portalOutlet: CdkPortalOutlet;\n\n  /** The class that traps and manages focus within the dialog. */\n  private _focusTrap: FocusTrap | null = null;\n\n  /** Element that was focused before the dialog was opened. Save this to restore upon close. */\n  private _elementFocusedBeforeDialogWasOpened: HTMLElement | null = null;\n\n  /**\n   * Type of interaction that led to the dialog being closed. This is used to determine\n   * whether the focus style will be applied when returning focus to its original location\n   * after the dialog is closed.\n   */\n  _closeInteractionType: FocusOrigin | null = null;\n\n  /**\n   * Queue of the IDs of the dialog's label element, based on their definition order. The first\n   * ID will be used as the `aria-labelledby` value. We use a queue here to handle the case\n   * where there are two or more titles in the DOM at a time and the first one is destroyed while\n   * the rest are present.\n   */\n  _ariaLabelledByQueue: string[] = [];\n\n  protected readonly _changeDetectorRef = inject(ChangeDetectorRef);\n\n  constructor(\n    protected _elementRef: ElementRef,\n    protected _focusTrapFactory: FocusTrapFactory,\n    @Optional() @Inject(DOCUMENT) _document: any,\n    @Inject(DialogConfig) readonly _config: C,\n    private _interactivityChecker: InteractivityChecker,\n    protected _ngZone: NgZone,\n    private _overlayRef: OverlayRef,\n    private _focusMonitor?: FocusMonitor,\n  ) {\n    super();\n\n    this._document = _document;\n\n    if (this._config.ariaLabelledBy) {\n      this._ariaLabelledByQueue.push(this._config.ariaLabelledBy);\n    }\n  }\n\n  _addAriaLabelledBy(id: string) {\n    this._ariaLabelledByQueue.push(id);\n    this._changeDetectorRef.markForCheck();\n  }\n\n  _removeAriaLabelledBy(id: string) {\n    const index = this._ariaLabelledByQueue.indexOf(id);\n\n    if (index > -1) {\n      this._ariaLabelledByQueue.splice(index, 1);\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n\n  protected _contentAttached() {\n    this._initializeFocusTrap();\n    this._handleBackdropClicks();\n    this._captureInitialFocus();\n  }\n\n  /**\n   * Can be used by child classes to customize the initial focus\n   * capturing behavior (e.g. if it's tied to an animation).\n   */\n  protected _captureInitialFocus() {\n    this._trapFocus();\n  }\n\n  ngOnDestroy() {\n    this._restoreFocus();\n  }\n\n  /**\n   * Attach a ComponentPortal as content to this dialog container.\n   * @param portal Portal to be attached as the dialog content.\n   */\n  attachComponentPortal<T>(portal: ComponentPortal<T>): ComponentRef<T> {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwDialogContentAlreadyAttachedError();\n    }\n\n    const result = this._portalOutlet.attachComponentPortal(portal);\n    this._contentAttached();\n    return result;\n  }\n\n  /**\n   * Attach a TemplatePortal as content to this dialog container.\n   * @param portal Portal to be attached as the dialog content.\n   */\n  attachTemplatePortal<T>(portal: TemplatePortal<T>): EmbeddedViewRef<T> {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwDialogContentAlreadyAttachedError();\n    }\n\n    const result = this._portalOutlet.attachTemplatePortal(portal);\n    this._contentAttached();\n    return result;\n  }\n\n  /**\n   * Attaches a DOM portal to the dialog container.\n   * @param portal Portal to be attached.\n   * @deprecated To be turned into a method.\n   * @breaking-change 10.0.0\n   */\n  override attachDomPortal = (portal: DomPortal) => {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwDialogContentAlreadyAttachedError();\n    }\n\n    const result = this._portalOutlet.attachDomPortal(portal);\n    this._contentAttached();\n    return result;\n  };\n\n  // TODO(crisbeto): this shouldn't be exposed, but there are internal references to it.\n  /** Captures focus if it isn't already inside the dialog. */\n  _recaptureFocus() {\n    if (!this._containsFocus()) {\n      this._trapFocus();\n    }\n  }\n\n  /**\n   * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n   * attribute to forcefully focus it. The attribute is removed after focus is moved.\n   * @param element The element to focus.\n   */\n  private _forceFocus(element: HTMLElement, options?: FocusOptions) {\n    if (!this._interactivityChecker.isFocusable(element)) {\n      element.tabIndex = -1;\n      // The tabindex attribute should be removed to avoid navigating to that element again\n      this._ngZone.runOutsideAngular(() => {\n        const callback = () => {\n          element.removeEventListener('blur', callback);\n          element.removeEventListener('mousedown', callback);\n          element.removeAttribute('tabindex');\n        };\n\n        element.addEventListener('blur', callback);\n        element.addEventListener('mousedown', callback);\n      });\n    }\n    element.focus(options);\n  }\n\n  /**\n   * Focuses the first element that matches the given selector within the focus trap.\n   * @param selector The CSS selector for the element to set focus to.\n   */\n  private _focusByCssSelector(selector: string, options?: FocusOptions) {\n    let elementToFocus = this._elementRef.nativeElement.querySelector(\n      selector,\n    ) as HTMLElement | null;\n    if (elementToFocus) {\n      this._forceFocus(elementToFocus, options);\n    }\n  }\n\n  /**\n   * Moves the focus inside the focus trap. When autoFocus is not set to 'dialog', if focus\n   * cannot be moved then focus will go to the dialog container.\n   */\n  protected _trapFocus() {\n    const element = this._elementRef.nativeElement;\n    // If were to attempt to focus immediately, then the content of the dialog would not yet be\n    // ready in instances where change detection has to run first. To deal with this, we simply\n    // wait for the microtask queue to be empty when setting focus when autoFocus isn't set to\n    // dialog. If the element inside the dialog can't be focused, then the container is focused\n    // so the user can't tab into other elements behind it.\n    switch (this._config.autoFocus) {\n      case false:\n      case 'dialog':\n        // Ensure that focus is on the dialog container. It's possible that a different\n        // component tried to move focus while the open animation was running. See:\n        // https://github.com/angular/components/issues/16215. Note that we only want to do this\n        // if the focus isn't inside the dialog already, because it's possible that the consumer\n        // turned off `autoFocus` in order to move focus themselves.\n        if (!this._containsFocus()) {\n          element.focus();\n        }\n        break;\n      case true:\n      case 'first-tabbable':\n        this._focusTrap?.focusInitialElementWhenReady().then(focusedSuccessfully => {\n          // If we weren't able to find a focusable element in the dialog, then focus the dialog\n          // container instead.\n          if (!focusedSuccessfully) {\n            this._focusDialogContainer();\n          }\n        });\n        break;\n      case 'first-heading':\n        this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]');\n        break;\n      default:\n        this._focusByCssSelector(this._config.autoFocus!);\n        break;\n    }\n  }\n\n  /** Restores focus to the element that was focused before the dialog opened. */\n  private _restoreFocus() {\n    const focusConfig = this._config.restoreFocus;\n    let focusTargetElement: HTMLElement | null = null;\n\n    if (typeof focusConfig === 'string') {\n      focusTargetElement = this._document.querySelector(focusConfig);\n    } else if (typeof focusConfig === 'boolean') {\n      focusTargetElement = focusConfig ? this._elementFocusedBeforeDialogWasOpened : null;\n    } else if (focusConfig) {\n      focusTargetElement = focusConfig;\n    }\n\n    // We need the extra check, because IE can set the `activeElement` to null in some cases.\n    if (\n      this._config.restoreFocus &&\n      focusTargetElement &&\n      typeof focusTargetElement.focus === 'function'\n    ) {\n      const activeElement = _getFocusedElementPierceShadowDom();\n      const element = this._elementRef.nativeElement;\n\n      // Make sure that focus is still inside the dialog or is on the body (usually because a\n      // non-focusable element like the backdrop was clicked) before moving it. It's possible that\n      // the consumer moved it themselves before the animation was done, in which case we shouldn't\n      // do anything.\n      if (\n        !activeElement ||\n        activeElement === this._document.body ||\n        activeElement === element ||\n        element.contains(activeElement)\n      ) {\n        if (this._focusMonitor) {\n          this._focusMonitor.focusVia(focusTargetElement, this._closeInteractionType);\n          this._closeInteractionType = null;\n        } else {\n          focusTargetElement.focus();\n        }\n      }\n    }\n\n    if (this._focusTrap) {\n      this._focusTrap.destroy();\n    }\n  }\n\n  /** Focuses the dialog container. */\n  private _focusDialogContainer() {\n    // Note that there is no focus method when rendering on the server.\n    if (this._elementRef.nativeElement.focus) {\n      this._elementRef.nativeElement.focus();\n    }\n  }\n\n  /** Returns whether focus is inside the dialog. */\n  private _containsFocus() {\n    const element = this._elementRef.nativeElement;\n    const activeElement = _getFocusedElementPierceShadowDom();\n    return element === activeElement || element.contains(activeElement);\n  }\n\n  /** Sets up the focus trap. */\n  private _initializeFocusTrap() {\n    if (this._platform.isBrowser) {\n      this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n\n      // Save the previously focused element. This element will be re-focused\n      // when the dialog closes.\n      if (this._document) {\n        this._elementFocusedBeforeDialogWasOpened = _getFocusedElementPierceShadowDom();\n      }\n    }\n  }\n\n  /** Sets up the listener that handles clicks on the dialog backdrop. */\n  private _handleBackdropClicks() {\n    // Clicking on the backdrop will move focus out of dialog.\n    // Recapture it if closing via the backdrop is disabled.\n    this._overlayRef.backdropClick().subscribe(() => {\n      if (this._config.disableClose) {\n        this._recaptureFocus();\n      }\n    });\n  }\n}\n", "<ng-template cdkPortalOutlet />\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {OverlayRef} from '@angular/cdk/overlay';\nimport {ESCAPE, hasModifierKey} from '@angular/cdk/keycodes';\nimport {Observable, Subject, Subscription} from 'rxjs';\nimport {DialogConfig} from './dialog-config';\nimport {FocusOrigin} from '@angular/cdk/a11y';\nimport {BasePortalOutlet} from '@angular/cdk/portal';\nimport {ComponentRef} from '@angular/core';\n\n/** Additional options that can be passed in when closing a dialog. */\nexport interface DialogCloseOptions {\n  /** Focus original to use when restoring focus. */\n  focusOrigin?: FocusOrigin;\n}\n\n/**\n * Reference to a dialog opened via the Dialog service.\n */\nexport class DialogRef<R = unknown, C = unknown> {\n  /**\n   * Instance of component opened into the dialog. Will be\n   * null when the dialog is opened using a `TemplateRef`.\n   */\n  readonly componentInstance: C | null;\n\n  /**\n   * `ComponentRef` of the component opened into the dialog. Will be\n   * null when the dialog is opened using a `TemplateRef`.\n   */\n  readonly componentRef: ComponentRef<C> | null;\n\n  /** Instance of the container that is rendering out the dialog content. */\n  readonly containerInstance: BasePortalOutlet & {_closeInteractionType?: FocusOrigin};\n\n  /** Whether the user is allowed to close the dialog. */\n  disableClose: boolean | undefined;\n\n  /** Emits when the dialog has been closed. */\n  readonly closed: Observable<R | undefined> = new Subject<R | undefined>();\n\n  /** Emits when the backdrop of the dialog is clicked. */\n  readonly backdropClick: Observable<MouseEvent>;\n\n  /** Emits when on keyboard events within the dialog. */\n  readonly keydownEvents: Observable<KeyboardEvent>;\n\n  /** Emits on pointer events that happen outside of the dialog. */\n  readonly outsidePointerEvents: Observable<MouseEvent>;\n\n  /** Unique ID for the dialog. */\n  readonly id: string;\n\n  /** Subscription to external detachments of the dialog. */\n  private _detachSubscription: Subscription;\n\n  constructor(\n    readonly overlayRef: OverlayRef,\n    readonly config: DialogConfig<any, DialogRef<R, C>, BasePortalOutlet>,\n  ) {\n    this.disableClose = config.disableClose;\n    this.backdropClick = overlayRef.backdropClick();\n    this.keydownEvents = overlayRef.keydownEvents();\n    this.outsidePointerEvents = overlayRef.outsidePointerEvents();\n    this.id = config.id!; // By the time the dialog is created we are guaranteed to have an ID.\n\n    this.keydownEvents.subscribe(event => {\n      if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n        event.preventDefault();\n        this.close(undefined, {focusOrigin: 'keyboard'});\n      }\n    });\n\n    this.backdropClick.subscribe(() => {\n      if (!this.disableClose) {\n        this.close(undefined, {focusOrigin: 'mouse'});\n      }\n    });\n\n    this._detachSubscription = overlayRef.detachments().subscribe(() => {\n      // Check specifically for `false`, because we want `undefined` to be treated like `true`.\n      if (config.closeOnOverlayDetachments !== false) {\n        this.close();\n      }\n    });\n  }\n\n  /**\n   * Close the dialog.\n   * @param result Optional result to return to the dialog opener.\n   * @param options Additional options to customize the closing behavior.\n   */\n  close(result?: R, options?: DialogCloseOptions): void {\n    if (this.containerInstance) {\n      const closedSubject = this.closed as Subject<R | undefined>;\n      this.containerInstance._closeInteractionType = options?.focusOrigin || 'program';\n      // Drop the detach subscription first since it can be triggered by the\n      // `dispose` call and override the result of this closing sequence.\n      this._detachSubscription.unsubscribe();\n      this.overlayRef.dispose();\n      closedSubject.next(result);\n      closedSubject.complete();\n      (this as {componentInstance: C}).componentInstance = (\n        this as {containerInstance: BasePortalOutlet}\n      ).containerInstance = null!;\n    }\n  }\n\n  /** Updates the position of the dialog based on the current position strategy. */\n  updatePosition(): this {\n    this.overlayRef.updatePosition();\n    return this;\n  }\n\n  /**\n   * Updates the dialog's width and height.\n   * @param width New width of the dialog.\n   * @param height New height of the dialog.\n   */\n  updateSize(width: string | number = '', height: string | number = ''): this {\n    this.overlayRef.updateSize({width, height});\n    return this;\n  }\n\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes: string | string[]): this {\n    this.overlayRef.addPanelClass(classes);\n    return this;\n  }\n\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes: string | string[]): this {\n    this.overlayRef.removePanelClass(classes);\n    return this;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {InjectionToken, inject} from '@angular/core';\nimport {Overlay, ScrollStrategy} from '@angular/cdk/overlay';\nimport {DialogConfig} from './dialog-config';\n\n/** Injection token for the Dialog's ScrollStrategy. */\nexport const DIALOG_SCROLL_STRATEGY = new InjectionToken<() => ScrollStrategy>(\n  'DialogScrollStrategy',\n  {\n    providedIn: 'root',\n    factory: () => {\n      const overlay = inject(Overlay);\n      return () => overlay.scrollStrategies.block();\n    },\n  },\n);\n\n/** Injection token for the Dialog's Data. */\nexport const DIALOG_DATA = new InjectionToken<any>('DialogData');\n\n/** Injection token that can be used to provide default options for the dialog module. */\nexport const DEFAULT_DIALOG_CONFIG = new InjectionToken<DialogConfig>('DefaultDialogConfig');\n\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nexport function DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay: Overlay): () => ScrollStrategy {\n  return () => overlay.scrollStrategies.block();\n}\n\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nexport const DIALOG_SCROLL_STRATEGY_PROVIDER = {\n  provide: DIALOG_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  TemplateRef,\n  Injectable,\n  Injector,\n  OnDestroy,\n  Type,\n  StaticProvider,\n  Inject,\n  Optional,\n  SkipSelf,\n  ComponentRef,\n} from '@angular/core';\nimport {BasePortalOutlet, ComponentPortal, TemplatePortal} from '@angular/cdk/portal';\nimport {of as observableOf, Observable, Subject, defer} from 'rxjs';\nimport {DialogRef} from './dialog-ref';\nimport {DialogConfig} from './dialog-config';\nimport {Directionality} from '@angular/cdk/bidi';\nimport {\n  ComponentType,\n  Overlay,\n  OverlayRef,\n  OverlayConfig,\n  ScrollStrategy,\n  OverlayContainer,\n} from '@angular/cdk/overlay';\nimport {startWith} from 'rxjs/operators';\n\nimport {DEFAULT_DIALOG_CONFIG, DIALOG_DATA, DIALOG_SCROLL_STRATEGY} from './dialog-injectors';\nimport {CdkDialogContainer} from './dialog-container';\n\n/** Unique id for the created dialog. */\nlet uniqueId = 0;\n\n@Injectable({providedIn: 'root'})\nexport class Dialog implements OnDestroy {\n  private _openDialogsAtThisLevel: DialogRef<any, any>[] = [];\n  private readonly _afterAllClosedAtThisLevel = new Subject<void>();\n  private readonly _afterOpenedAtThisLevel = new Subject<DialogRef>();\n  private _ariaHiddenElements = new Map<Element, string | null>();\n  private _scrollStrategy: () => ScrollStrategy;\n\n  /** Keeps track of the currently-open dialogs. */\n  get openDialogs(): readonly DialogRef<any, any>[] {\n    return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n  }\n\n  /** Stream that emits when a dialog has been opened. */\n  get afterOpened(): Subject<DialogRef<any, any>> {\n    return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n  }\n\n  /**\n   * Stream that emits when all open dialog have finished closing.\n   * Will emit on subscribe if there are no open dialogs to begin with.\n   */\n  readonly afterAllClosed: Observable<void> = defer(() =>\n    this.openDialogs.length\n      ? this._getAfterAllClosed()\n      : this._getAfterAllClosed().pipe(startWith(undefined)),\n  );\n\n  constructor(\n    private _overlay: Overlay,\n    private _injector: Injector,\n    @Optional() @Inject(DEFAULT_DIALOG_CONFIG) private _defaultOptions: DialogConfig,\n    @Optional() @SkipSelf() private _parentDialog: Dialog,\n    private _overlayContainer: OverlayContainer,\n    @Inject(DIALOG_SCROLL_STRATEGY) scrollStrategy: any,\n  ) {\n    this._scrollStrategy = scrollStrategy;\n  }\n\n  /**\n   * Opens a modal dialog containing the given component.\n   * @param component Type of the component to load into the dialog.\n   * @param config Extra configuration options.\n   * @returns Reference to the newly-opened dialog.\n   */\n  open<R = unknown, D = unknown, C = unknown>(\n    component: ComponentType<C>,\n    config?: DialogConfig<D, DialogRef<R, C>>,\n  ): DialogRef<R, C>;\n\n  /**\n   * Opens a modal dialog containing the given template.\n   * @param template TemplateRef to instantiate as the dialog content.\n   * @param config Extra configuration options.\n   * @returns Reference to the newly-opened dialog.\n   */\n  open<R = unknown, D = unknown, C = unknown>(\n    template: TemplateRef<C>,\n    config?: DialogConfig<D, DialogRef<R, C>>,\n  ): DialogRef<R, C>;\n\n  open<R = unknown, D = unknown, C = unknown>(\n    componentOrTemplateRef: ComponentType<C> | TemplateRef<C>,\n    config?: DialogConfig<D, DialogRef<R, C>>,\n  ): DialogRef<R, C>;\n\n  open<R = unknown, D = unknown, C = unknown>(\n    componentOrTemplateRef: ComponentType<C> | TemplateRef<C>,\n    config?: DialogConfig<D, DialogRef<R, C>>,\n  ): DialogRef<R, C> {\n    const defaults = (this._defaultOptions || new DialogConfig()) as DialogConfig<\n      D,\n      DialogRef<R, C>\n    >;\n    config = {...defaults, ...config};\n    config.id = config.id || `cdk-dialog-${uniqueId++}`;\n\n    if (\n      config.id &&\n      this.getDialogById(config.id) &&\n      (typeof ngDevMode === 'undefined' || ngDevMode)\n    ) {\n      throw Error(`Dialog with id \"${config.id}\" exists already. The dialog id must be unique.`);\n    }\n\n    const overlayConfig = this._getOverlayConfig(config);\n    const overlayRef = this._overlay.create(overlayConfig);\n    const dialogRef = new DialogRef(overlayRef, config);\n    const dialogContainer = this._attachContainer(overlayRef, dialogRef, config);\n\n    (dialogRef as {containerInstance: BasePortalOutlet}).containerInstance = dialogContainer;\n    this._attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config);\n\n    // If this is the first dialog that we're opening, hide all the non-overlay content.\n    if (!this.openDialogs.length) {\n      this._hideNonDialogContentFromAssistiveTechnology();\n    }\n\n    (this.openDialogs as DialogRef<R, C>[]).push(dialogRef);\n    dialogRef.closed.subscribe(() => this._removeOpenDialog(dialogRef, true));\n    this.afterOpened.next(dialogRef);\n\n    return dialogRef;\n  }\n\n  /**\n   * Closes all of the currently-open dialogs.\n   */\n  closeAll(): void {\n    reverseForEach(this.openDialogs, dialog => dialog.close());\n  }\n\n  /**\n   * Finds an open dialog by its id.\n   * @param id ID to use when looking up the dialog.\n   */\n  getDialogById<R, C>(id: string): DialogRef<R, C> | undefined {\n    return this.openDialogs.find(dialog => dialog.id === id);\n  }\n\n  ngOnDestroy() {\n    // Make one pass over all the dialogs that need to be untracked, but should not be closed. We\n    // want to stop tracking the open dialog even if it hasn't been closed, because the tracking\n    // determines when `aria-hidden` is removed from elements outside the dialog.\n    reverseForEach(this._openDialogsAtThisLevel, dialog => {\n      // Check for `false` specifically since we want `undefined` to be interpreted as `true`.\n      if (dialog.config.closeOnDestroy === false) {\n        this._removeOpenDialog(dialog, false);\n      }\n    });\n\n    // Make a second pass and close the remaining dialogs. We do this second pass in order to\n    // correctly dispatch the `afterAllClosed` event in case we have a mixed array of dialogs\n    // that should be closed and dialogs that should not.\n    reverseForEach(this._openDialogsAtThisLevel, dialog => dialog.close());\n\n    this._afterAllClosedAtThisLevel.complete();\n    this._afterOpenedAtThisLevel.complete();\n    this._openDialogsAtThisLevel = [];\n  }\n\n  /**\n   * Creates an overlay config from a dialog config.\n   * @param config The dialog configuration.\n   * @returns The overlay configuration.\n   */\n  private _getOverlayConfig<D, R>(config: DialogConfig<D, R>): OverlayConfig {\n    const state = new OverlayConfig({\n      positionStrategy:\n        config.positionStrategy ||\n        this._overlay.position().global().centerHorizontally().centerVertically(),\n      scrollStrategy: config.scrollStrategy || this._scrollStrategy(),\n      panelClass: config.panelClass,\n      hasBackdrop: config.hasBackdrop,\n      direction: config.direction,\n      minWidth: config.minWidth,\n      minHeight: config.minHeight,\n      maxWidth: config.maxWidth,\n      maxHeight: config.maxHeight,\n      width: config.width,\n      height: config.height,\n      disposeOnNavigation: config.closeOnNavigation,\n    });\n\n    if (config.backdropClass) {\n      state.backdropClass = config.backdropClass;\n    }\n\n    return state;\n  }\n\n  /**\n   * Attaches a dialog container to a dialog's already-created overlay.\n   * @param overlay Reference to the dialog's underlying overlay.\n   * @param config The dialog configuration.\n   * @returns A promise resolving to a ComponentRef for the attached container.\n   */\n  private _attachContainer<R, D, C>(\n    overlay: OverlayRef,\n    dialogRef: DialogRef<R, C>,\n    config: DialogConfig<D, DialogRef<R, C>>,\n  ): BasePortalOutlet {\n    const userInjector = config.injector || config.viewContainerRef?.injector;\n    const providers: StaticProvider[] = [\n      {provide: DialogConfig, useValue: config},\n      {provide: DialogRef, useValue: dialogRef},\n      {provide: OverlayRef, useValue: overlay},\n    ];\n    let containerType: Type<BasePortalOutlet>;\n\n    if (config.container) {\n      if (typeof config.container === 'function') {\n        containerType = config.container;\n      } else {\n        containerType = config.container.type;\n        providers.push(...config.container.providers(config));\n      }\n    } else {\n      containerType = CdkDialogContainer;\n    }\n\n    const containerPortal = new ComponentPortal(\n      containerType,\n      config.viewContainerRef,\n      Injector.create({parent: userInjector || this._injector, providers}),\n      config.componentFactoryResolver,\n    );\n    const containerRef = overlay.attach(containerPortal);\n\n    return containerRef.instance;\n  }\n\n  /**\n   * Attaches the user-provided component to the already-created dialog container.\n   * @param componentOrTemplateRef The type of component being loaded into the dialog,\n   *     or a TemplateRef to instantiate as the content.\n   * @param dialogRef Reference to the dialog being opened.\n   * @param dialogContainer Component that is going to wrap the dialog content.\n   * @param config Configuration used to open the dialog.\n   */\n  private _attachDialogContent<R, D, C>(\n    componentOrTemplateRef: ComponentType<C> | TemplateRef<C>,\n    dialogRef: DialogRef<R, C>,\n    dialogContainer: BasePortalOutlet,\n    config: DialogConfig<D, DialogRef<R, C>>,\n  ) {\n    if (componentOrTemplateRef instanceof TemplateRef) {\n      const injector = this._createInjector(config, dialogRef, dialogContainer, undefined);\n      let context: any = {$implicit: config.data, dialogRef};\n\n      if (config.templateContext) {\n        context = {\n          ...context,\n          ...(typeof config.templateContext === 'function'\n            ? config.templateContext()\n            : config.templateContext),\n        };\n      }\n\n      dialogContainer.attachTemplatePortal(\n        new TemplatePortal<C>(componentOrTemplateRef, null!, context, injector),\n      );\n    } else {\n      const injector = this._createInjector(config, dialogRef, dialogContainer, this._injector);\n      const contentRef = dialogContainer.attachComponentPortal<C>(\n        new ComponentPortal(\n          componentOrTemplateRef,\n          config.viewContainerRef,\n          injector,\n          config.componentFactoryResolver,\n        ),\n      );\n      (dialogRef as {componentRef: ComponentRef<C>}).componentRef = contentRef;\n      (dialogRef as {componentInstance: C}).componentInstance = contentRef.instance;\n    }\n  }\n\n  /**\n   * Creates a custom injector to be used inside the dialog. This allows a component loaded inside\n   * of a dialog to close itself and, optionally, to return a value.\n   * @param config Config object that is used to construct the dialog.\n   * @param dialogRef Reference to the dialog being opened.\n   * @param dialogContainer Component that is going to wrap the dialog content.\n   * @param fallbackInjector Injector to use as a fallback when a lookup fails in the custom\n   * dialog injector, if the user didn't provide a custom one.\n   * @returns The custom injector that can be used inside the dialog.\n   */\n  private _createInjector<R, D, C>(\n    config: DialogConfig<D, DialogRef<R, C>>,\n    dialogRef: DialogRef<R, C>,\n    dialogContainer: BasePortalOutlet,\n    fallbackInjector: Injector | undefined,\n  ): Injector {\n    const userInjector = config.injector || config.viewContainerRef?.injector;\n    const providers: StaticProvider[] = [\n      {provide: DIALOG_DATA, useValue: config.data},\n      {provide: DialogRef, useValue: dialogRef},\n    ];\n\n    if (config.providers) {\n      if (typeof config.providers === 'function') {\n        providers.push(...config.providers(dialogRef, config, dialogContainer));\n      } else {\n        providers.push(...config.providers);\n      }\n    }\n\n    if (\n      config.direction &&\n      (!userInjector ||\n        !userInjector.get<Directionality | null>(Directionality, null, {optional: true}))\n    ) {\n      providers.push({\n        provide: Directionality,\n        useValue: {value: config.direction, change: observableOf()},\n      });\n    }\n\n    return Injector.create({parent: userInjector || fallbackInjector, providers});\n  }\n\n  /**\n   * Removes a dialog from the array of open dialogs.\n   * @param dialogRef Dialog to be removed.\n   * @param emitEvent Whether to emit an event if this is the last dialog.\n   */\n  private _removeOpenDialog<R, C>(dialogRef: DialogRef<R, C>, emitEvent: boolean) {\n    const index = this.openDialogs.indexOf(dialogRef);\n\n    if (index > -1) {\n      (this.openDialogs as DialogRef<R, C>[]).splice(index, 1);\n\n      // If all the dialogs were closed, remove/restore the `aria-hidden`\n      // to a the siblings and emit to the `afterAllClosed` stream.\n      if (!this.openDialogs.length) {\n        this._ariaHiddenElements.forEach((previousValue, element) => {\n          if (previousValue) {\n            element.setAttribute('aria-hidden', previousValue);\n          } else {\n            element.removeAttribute('aria-hidden');\n          }\n        });\n\n        this._ariaHiddenElements.clear();\n\n        if (emitEvent) {\n          this._getAfterAllClosed().next();\n        }\n      }\n    }\n  }\n\n  /** Hides all of the content that isn't an overlay from assistive technology. */\n  private _hideNonDialogContentFromAssistiveTechnology() {\n    const overlayContainer = this._overlayContainer.getContainerElement();\n\n    // Ensure that the overlay container is attached to the DOM.\n    if (overlayContainer.parentElement) {\n      const siblings = overlayContainer.parentElement.children;\n\n      for (let i = siblings.length - 1; i > -1; i--) {\n        const sibling = siblings[i];\n\n        if (\n          sibling !== overlayContainer &&\n          sibling.nodeName !== 'SCRIPT' &&\n          sibling.nodeName !== 'STYLE' &&\n          !sibling.hasAttribute('aria-live')\n        ) {\n          this._ariaHiddenElements.set(sibling, sibling.getAttribute('aria-hidden'));\n          sibling.setAttribute('aria-hidden', 'true');\n        }\n      }\n    }\n  }\n\n  private _getAfterAllClosed(): Subject<void> {\n    const parent = this._parentDialog;\n    return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n  }\n}\n\n/**\n * Executes a callback against all elements in an array while iterating in reverse.\n * Useful if the array is being modified as it is being iterated.\n */\nfunction reverseForEach<T>(items: T[] | readonly T[], callback: (current: T) => void) {\n  let i = items.length;\n\n  while (i--) {\n    callback(items[i]);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {OverlayModule} from '@angular/cdk/overlay';\nimport {PortalModule} from '@angular/cdk/portal';\nimport {A11yModule} from '@angular/cdk/a11y';\nimport {Dialog} from './dialog';\nimport {CdkDialogContainer} from './dialog-container';\n\n@NgModule({\n  imports: [OverlayModule, PortalModule, A11yModule, CdkDialogContainer],\n  exports: [\n    // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n    // don't have to remember to import it or be faced with an unhelpful error.\n    PortalModule,\n    CdkDialogContainer,\n  ],\n  providers: [Dialog],\n})\nexport class DialogModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["i2", "observableOf", "i1"], "mappings": ";;;;;;;;;;;;;;AAyBA;MACa,YAAY,CAAA;AAAzB,IAAA,WAAA,GAAA;;QAmBE,IAAI,CAAA,IAAA,GAAgB,QAAQ,CAAC;;QAG7B,IAAU,CAAA,UAAA,GAAuB,EAAE,CAAC;;QAGpC,IAAW,CAAA,WAAA,GAAa,IAAI,CAAC;;QAG7B,IAAa,CAAA,aAAA,GAAuB,EAAE,CAAC;;QAGvC,IAAY,CAAA,YAAA,GAAa,KAAK,CAAC;;QAG/B,IAAK,CAAA,KAAA,GAAY,EAAE,CAAC;;QAGpB,IAAM,CAAA,MAAA,GAAY,EAAE,CAAC;;QAkBrB,IAAI,CAAA,IAAA,GAAc,IAAI,CAAC;;QAMvB,IAAe,CAAA,eAAA,GAAmB,IAAI,CAAC;;QAGvC,IAAc,CAAA,cAAA,GAAmB,IAAI,CAAC;;QAGtC,IAAS,CAAA,SAAA,GAAmB,IAAI,CAAC;;QAGjC,IAAS,CAAA,SAAA,GAAa,IAAI,CAAC;AAE3B;;;;AAIG;QACH,IAAS,CAAA,SAAA,GAAwC,gBAAgB,CAAC;AAElE;;;;;;;AAOG;QACH,IAAY,CAAA,YAAA,GAAoC,IAAI,CAAC;AAQrD;;;;AAIG;QACH,IAAiB,CAAA,iBAAA,GAAa,IAAI,CAAC;AAEnC;;;AAGG;QACH,IAAc,CAAA,cAAA,GAAa,IAAI,CAAC;AAEhC;;;;;AAKG;QACH,IAAyB,CAAA,yBAAA,GAAa,IAAI,CAAC;KA8B5C;AAAA;;SChIe,sCAAsC,GAAA;AACpD,IAAA,MAAM,KAAK,CAAC,uEAAuE,CAAC,CAAC;AACvF,CAAC;AAED;;;AAGG;AAsBG,MAAO,kBACX,SAAQ,gBAAgB,CAAA;AAgCxB,IAAA,WAAA,CACY,WAAuB,EACvB,iBAAmC,EACf,SAAc,EACb,OAAU,EACjC,qBAA2C,EACzC,OAAe,EACjB,WAAuB,EACvB,aAA4B,EAAA;AAEpC,QAAA,KAAK,EAAE,CAAC;QATE,IAAW,CAAA,WAAA,GAAX,WAAW,CAAY;QACvB,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAkB;QAEd,IAAO,CAAA,OAAA,GAAP,OAAO,CAAG;QACjC,IAAqB,CAAA,qBAAA,GAArB,qBAAqB,CAAsB;QACzC,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QACjB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAY;QACvB,IAAa,CAAA,aAAA,GAAb,aAAa,CAAe;AArC9B,QAAA,IAAA,CAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;;QAO7B,IAAU,CAAA,UAAA,GAAqB,IAAI,CAAC;;QAGpC,IAAoC,CAAA,oCAAA,GAAuB,IAAI,CAAC;AAExE;;;;AAIG;QACH,IAAqB,CAAA,qBAAA,GAAuB,IAAI,CAAC;AAEjD;;;;;AAKG;QACH,IAAoB,CAAA,oBAAA,GAAa,EAAE,CAAC;AAEjB,QAAA,IAAA,CAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC;AAiFlE;;;;;AAKG;AACM,QAAA,IAAA,CAAA,eAAe,GAAG,CAAC,MAAiB,KAAI;AAC/C,YAAA,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AACvF,gBAAA,sCAAsC,EAAE,CAAC;aAC1C;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAC1D,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxB,YAAA,OAAO,MAAM,CAAC;AAChB,SAAC,CAAC;AAjFA,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAE3B,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;YAC/B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;SAC7D;KACF;AAED,IAAA,kBAAkB,CAAC,EAAU,EAAA;AAC3B,QAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACnC,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;KACxC;AAED,IAAA,qBAAqB,CAAC,EAAU,EAAA;QAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AAEpD,QAAA,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAC3C,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;SACxC;KACF;IAES,gBAAgB,GAAA;QACxB,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC7B;AAED;;;AAGG;IACO,oBAAoB,GAAA;QAC5B,IAAI,CAAC,UAAU,EAAE,CAAC;KACnB;IAED,WAAW,GAAA;QACT,IAAI,CAAC,aAAa,EAAE,CAAC;KACtB;AAED;;;AAGG;AACH,IAAA,qBAAqB,CAAI,MAA0B,EAAA;AACjD,QAAA,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AACvF,YAAA,sCAAsC,EAAE,CAAC;SAC1C;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAChE,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxB,QAAA,OAAO,MAAM,CAAC;KACf;AAED;;;AAGG;AACH,IAAA,oBAAoB,CAAI,MAAyB,EAAA;AAC/C,QAAA,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AACvF,YAAA,sCAAsC,EAAE,CAAC;SAC1C;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;QAC/D,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxB,QAAA,OAAO,MAAM,CAAC;KACf;;;IAoBD,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE;YAC1B,IAAI,CAAC,UAAU,EAAE,CAAC;SACnB;KACF;AAED;;;;AAIG;IACK,WAAW,CAAC,OAAoB,EAAE,OAAsB,EAAA;QAC9D,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE;AACpD,YAAA,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;;AAEtB,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;gBAClC,MAAM,QAAQ,GAAG,MAAK;AACpB,oBAAA,OAAO,CAAC,mBAAmB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC9C,oBAAA,OAAO,CAAC,mBAAmB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;AACnD,oBAAA,OAAO,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;AACtC,iBAAC,CAAC;AAEF,gBAAA,OAAO,CAAC,gBAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC3C,gBAAA,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;AAClD,aAAC,CAAC,CAAC;SACJ;AACD,QAAA,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;KACxB;AAED;;;AAGG;IACK,mBAAmB,CAAC,QAAgB,EAAE,OAAsB,EAAA;AAClE,QAAA,IAAI,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,aAAa,CAC/D,QAAQ,CACa,CAAC;QACxB,IAAI,cAAc,EAAE;AAClB,YAAA,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;SAC3C;KACF;AAED;;;AAGG;IACO,UAAU,GAAA;AAClB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;;;;;;AAM/C,QAAA,QAAQ,IAAI,CAAC,OAAO,CAAC,SAAS;AAC5B,YAAA,KAAK,KAAK,CAAC;AACX,YAAA,KAAK,QAAQ;;;;;;AAMX,gBAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE;oBAC1B,OAAO,CAAC,KAAK,EAAE,CAAC;iBACjB;gBACD,MAAM;AACR,YAAA,KAAK,IAAI,CAAC;AACV,YAAA,KAAK,gBAAgB;gBACnB,IAAI,CAAC,UAAU,EAAE,4BAA4B,EAAE,CAAC,IAAI,CAAC,mBAAmB,IAAG;;;oBAGzE,IAAI,CAAC,mBAAmB,EAAE;wBACxB,IAAI,CAAC,qBAAqB,EAAE,CAAC;qBAC9B;AACH,iBAAC,CAAC,CAAC;gBACH,MAAM;AACR,YAAA,KAAK,eAAe;AAClB,gBAAA,IAAI,CAAC,mBAAmB,CAAC,0CAA0C,CAAC,CAAC;gBACrE,MAAM;AACR,YAAA;gBACE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,SAAU,CAAC,CAAC;gBAClD,MAAM;SACT;KACF;;IAGO,aAAa,GAAA;AACnB,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;QAC9C,IAAI,kBAAkB,GAAuB,IAAI,CAAC;AAElD,QAAA,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;YACnC,kBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;SAChE;AAAM,aAAA,IAAI,OAAO,WAAW,KAAK,SAAS,EAAE;AAC3C,YAAA,kBAAkB,GAAG,WAAW,GAAG,IAAI,CAAC,oCAAoC,GAAG,IAAI,CAAC;SACrF;aAAM,IAAI,WAAW,EAAE;YACtB,kBAAkB,GAAG,WAAW,CAAC;SAClC;;AAGD,QAAA,IACE,IAAI,CAAC,OAAO,CAAC,YAAY;YACzB,kBAAkB;AAClB,YAAA,OAAO,kBAAkB,CAAC,KAAK,KAAK,UAAU,EAC9C;AACA,YAAA,MAAM,aAAa,GAAG,iCAAiC,EAAE,CAAC;AAC1D,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;;;;;AAM/C,YAAA,IACE,CAAC,aAAa;AACd,gBAAA,aAAa,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI;AACrC,gBAAA,aAAa,KAAK,OAAO;AACzB,gBAAA,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,EAC/B;AACA,gBAAA,IAAI,IAAI,CAAC,aAAa,EAAE;oBACtB,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAC5E,oBAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;iBACnC;qBAAM;oBACL,kBAAkB,CAAC,KAAK,EAAE,CAAC;iBAC5B;aACF;SACF;AAED,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;SAC3B;KACF;;IAGO,qBAAqB,GAAA;;QAE3B,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,EAAE;AACxC,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;SACxC;KACF;;IAGO,cAAc,GAAA;AACpB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;AAC/C,QAAA,MAAM,aAAa,GAAG,iCAAiC,EAAE,CAAC;QAC1D,OAAO,OAAO,KAAK,aAAa,IAAI,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;KACrE;;IAGO,oBAAoB,GAAA;AAC1B,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;AAC5B,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;;;AAIhF,YAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,gBAAA,IAAI,CAAC,oCAAoC,GAAG,iCAAiC,EAAE,CAAC;aACjF;SACF;KACF;;IAGO,qBAAqB,GAAA;;;QAG3B,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC,SAAS,CAAC,MAAK;AAC9C,YAAA,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;gBAC7B,IAAI,CAAC,eAAe,EAAE,CAAC;aACxB;AACH,SAAC,CAAC,CAAC;KACJ;8GAzSU,kBAAkB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAoCP,QAAQ,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EACpB,YAAY,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,oBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAAA,IAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,YAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AArCX,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,kBAAkB,EAQlB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sBAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,UAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,SAAA,EAAA,oBAAA,EAAA,WAAA,EAAA,cAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,sBAAA,EAAA,oDAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,uBAAA,EAAA,iCAAA,EAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,eAAe,EC/E5B,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,mCACA,2JD0DY,eAAe,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,UAAA,CAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAYd,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBArB9B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,sBAAsB,EAGjB,aAAA,EAAA,iBAAiB,CAAC,IAAI,mBAGpB,uBAAuB,CAAC,OAAO,EAAA,UAAA,EACpC,IAAI,EAAA,OAAA,EACP,CAAC,eAAe,CAAC,EACpB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,sBAAsB;AAC/B,wBAAA,UAAU,EAAE,IAAI;AAChB,wBAAA,WAAW,EAAE,oBAAoB;AACjC,wBAAA,aAAa,EAAE,cAAc;AAC7B,wBAAA,mBAAmB,EAAE,mBAAmB;AACxC,wBAAA,wBAAwB,EAAE,oDAAoD;AAC9E,wBAAA,mBAAmB,EAAE,mBAAmB;AACxC,wBAAA,yBAAyB,EAAE,iCAAiC;AAC7D,qBAAA,EAAA,QAAA,EAAA,mCAAA,EAAA,MAAA,EAAA,CAAA,mGAAA,CAAA,EAAA,CAAA;;0BAsCE,QAAQ;;0BAAI,MAAM;2BAAC,QAAQ,CAAA;;0BAC3B,MAAM;2BAAC,YAAY,CAAA;uJA7BsB,aAAa,EAAA,CAAA;sBAAxD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,eAAe,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC,CAAA;;;AEzD5C;;AAEG;MACU,SAAS,CAAA;IAqCpB,WACW,CAAA,UAAsB,EACtB,MAA4D,EAAA;QAD5D,IAAU,CAAA,UAAA,GAAV,UAAU,CAAY;QACtB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAsD;;AAnB9D,QAAA,IAAA,CAAA,MAAM,GAA8B,IAAI,OAAO,EAAiB,CAAC;AAqBxE,QAAA,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;AACxC,QAAA,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,aAAa,EAAE,CAAC;AAChD,QAAA,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,aAAa,EAAE,CAAC;AAChD,QAAA,IAAI,CAAC,oBAAoB,GAAG,UAAU,CAAC,oBAAoB,EAAE,CAAC;QAC9D,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,EAAG,CAAC;AAErB,QAAA,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,KAAK,IAAG;AACnC,YAAA,IAAI,KAAK,CAAC,OAAO,KAAK,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;gBAC5E,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,EAAC,WAAW,EAAE,UAAU,EAAC,CAAC,CAAC;aAClD;AACH,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,MAAK;AAChC,YAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;gBACtB,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,EAAC,WAAW,EAAE,OAAO,EAAC,CAAC,CAAC;aAC/C;AACH,SAAC,CAAC,CAAC;QAEH,IAAI,CAAC,mBAAmB,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,MAAK;;AAEjE,YAAA,IAAI,MAAM,CAAC,yBAAyB,KAAK,KAAK,EAAE;gBAC9C,IAAI,CAAC,KAAK,EAAE,CAAC;aACd;AACH,SAAC,CAAC,CAAC;KACJ;AAED;;;;AAIG;IACH,KAAK,CAAC,MAAU,EAAE,OAA4B,EAAA;AAC5C,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAC1B,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,MAAgC,CAAC;YAC5D,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,GAAG,OAAO,EAAE,WAAW,IAAI,SAAS,CAAC;;;AAGjF,YAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;AACvC,YAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;AAC1B,YAAA,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC3B,aAAa,CAAC,QAAQ,EAAE,CAAC;YACxB,IAA+B,CAAC,iBAAiB,GAChD,IACD,CAAC,iBAAiB,GAAG,IAAK,CAAC;SAC7B;KACF;;IAGD,cAAc,GAAA;AACZ,QAAA,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC;AACjC,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;AAIG;AACH,IAAA,UAAU,CAAC,KAAA,GAAyB,EAAE,EAAE,SAA0B,EAAE,EAAA;QAClE,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,EAAC,KAAK,EAAE,MAAM,EAAC,CAAC,CAAC;AAC5C,QAAA,OAAO,IAAI,CAAC;KACb;;AAGD,IAAA,aAAa,CAAC,OAA0B,EAAA;AACtC,QAAA,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AACvC,QAAA,OAAO,IAAI,CAAC;KACb;;AAGD,IAAA,gBAAgB,CAAC,OAA0B,EAAA;AACzC,QAAA,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;AAC1C,QAAA,OAAO,IAAI,CAAC;KACb;AACF;;ACjID;MACa,sBAAsB,GAAG,IAAI,cAAc,CACtD,sBAAsB,EACtB;AACE,IAAA,UAAU,EAAE,MAAM;IAClB,OAAO,EAAE,MAAK;AACZ,QAAA,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;QAChC,OAAO,MAAM,OAAO,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;KAC/C;AACF,CAAA,EACD;AAEF;MACa,WAAW,GAAG,IAAI,cAAc,CAAM,YAAY,EAAE;AAEjE;MACa,qBAAqB,GAAG,IAAI,cAAc,CAAe,qBAAqB,EAAE;AAE7F;;;;AAIG;AACG,SAAU,uCAAuC,CAAC,OAAgB,EAAA;IACtE,OAAO,MAAM,OAAO,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;AAChD,CAAC;AAED;;;;AAIG;AACU,MAAA,+BAA+B,GAAG;AAC7C,IAAA,OAAO,EAAE,sBAAsB;IAC/B,IAAI,EAAE,CAAC,OAAO,CAAC;AACf,IAAA,UAAU,EAAE,uCAAuC;;;ACTrD;AACA,IAAI,QAAQ,GAAG,CAAC,CAAC;MAGJ,MAAM,CAAA;;AAQjB,IAAA,IAAI,WAAW,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC;KAC3F;;AAGD,IAAA,IAAI,WAAW,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC;KAC3F;IAYD,WACU,CAAA,QAAiB,EACjB,SAAmB,EACwB,eAA6B,EAChD,aAAqB,EAC7C,iBAAmC,EACX,cAAmB,EAAA;QAL3C,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAS;QACjB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QACwB,IAAe,CAAA,eAAA,GAAf,eAAe,CAAc;QAChD,IAAa,CAAA,aAAA,GAAb,aAAa,CAAQ;QAC7C,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAkB;QA/BrC,IAAuB,CAAA,uBAAA,GAA0B,EAAE,CAAC;AAC3C,QAAA,IAAA,CAAA,0BAA0B,GAAG,IAAI,OAAO,EAAQ,CAAC;AACjD,QAAA,IAAA,CAAA,uBAAuB,GAAG,IAAI,OAAO,EAAa,CAAC;AAC5D,QAAA,IAAA,CAAA,mBAAmB,GAAG,IAAI,GAAG,EAA0B,CAAC;AAahE;;;AAGG;QACM,IAAc,CAAA,cAAA,GAAqB,KAAK,CAAC,MAChD,IAAI,CAAC,WAAW,CAAC,MAAM;AACrB,cAAE,IAAI,CAAC,kBAAkB,EAAE;AAC3B,cAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CACzD,CAAC;AAUA,QAAA,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;KACvC;IA6BD,IAAI,CACF,sBAAyD,EACzD,MAAyC,EAAA;QAEzC,MAAM,QAAQ,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,YAAY,EAAE,CAG3D,CAAC;QACF,MAAM,GAAG,EAAC,GAAG,QAAQ,EAAE,GAAG,MAAM,EAAC,CAAC;QAClC,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,IAAI,CAAc,WAAA,EAAA,QAAQ,EAAE,CAAA,CAAE,CAAC;QAEpD,IACE,MAAM,CAAC,EAAE;AACT,YAAA,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC;aAC5B,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAC/C;YACA,MAAM,KAAK,CAAC,CAAmB,gBAAA,EAAA,MAAM,CAAC,EAAE,CAAA,+CAAA,CAAiD,CAAC,CAAC;SAC5F;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QACrD,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QACvD,MAAM,SAAS,GAAG,IAAI,SAAS,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;AACpD,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;AAE5E,QAAA,SAAmD,CAAC,iBAAiB,GAAG,eAAe,CAAC;QACzF,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,EAAE,SAAS,EAAE,eAAe,EAAE,MAAM,CAAC,CAAC;;AAGtF,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;YAC5B,IAAI,CAAC,4CAA4C,EAAE,CAAC;SACrD;AAEA,QAAA,IAAI,CAAC,WAAiC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACxD,QAAA,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;AAC1E,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAEjC,QAAA,OAAO,SAAS,CAAC;KAClB;AAED;;AAEG;IACH,QAAQ,GAAA;AACN,QAAA,cAAc,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;KAC5D;AAED;;;AAGG;AACH,IAAA,aAAa,CAAO,EAAU,EAAA;AAC5B,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;KAC1D;IAED,WAAW,GAAA;;;;AAIT,QAAA,cAAc,CAAC,IAAI,CAAC,uBAAuB,EAAE,MAAM,IAAG;;YAEpD,IAAI,MAAM,CAAC,MAAM,CAAC,cAAc,KAAK,KAAK,EAAE;AAC1C,gBAAA,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;aACvC;AACH,SAAC,CAAC,CAAC;;;;AAKH,QAAA,cAAc,CAAC,IAAI,CAAC,uBAAuB,EAAE,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;AAEvE,QAAA,IAAI,CAAC,0BAA0B,CAAC,QAAQ,EAAE,CAAC;AAC3C,QAAA,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,CAAC;AACxC,QAAA,IAAI,CAAC,uBAAuB,GAAG,EAAE,CAAC;KACnC;AAED;;;;AAIG;AACK,IAAA,iBAAiB,CAAO,MAA0B,EAAA;AACxD,QAAA,MAAM,KAAK,GAAG,IAAI,aAAa,CAAC;YAC9B,gBAAgB,EACd,MAAM,CAAC,gBAAgB;AACvB,gBAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,CAAC,kBAAkB,EAAE,CAAC,gBAAgB,EAAE;YAC3E,cAAc,EAAE,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC,eAAe,EAAE;YAC/D,UAAU,EAAE,MAAM,CAAC,UAAU;YAC7B,WAAW,EAAE,MAAM,CAAC,WAAW;YAC/B,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,mBAAmB,EAAE,MAAM,CAAC,iBAAiB;AAC9C,SAAA,CAAC,CAAC;AAEH,QAAA,IAAI,MAAM,CAAC,aAAa,EAAE;AACxB,YAAA,KAAK,CAAC,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;SAC5C;AAED,QAAA,OAAO,KAAK,CAAC;KACd;AAED;;;;;AAKG;AACK,IAAA,gBAAgB,CACtB,OAAmB,EACnB,SAA0B,EAC1B,MAAwC,EAAA;QAExC,MAAM,YAAY,GAAG,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,gBAAgB,EAAE,QAAQ,CAAC;AAC1E,QAAA,MAAM,SAAS,GAAqB;AAClC,YAAA,EAAC,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAC;AACzC,YAAA,EAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAC;AACzC,YAAA,EAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAC;SACzC,CAAC;AACF,QAAA,IAAI,aAAqC,CAAC;AAE1C,QAAA,IAAI,MAAM,CAAC,SAAS,EAAE;AACpB,YAAA,IAAI,OAAO,MAAM,CAAC,SAAS,KAAK,UAAU,EAAE;AAC1C,gBAAA,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC;aAClC;iBAAM;AACL,gBAAA,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC;AACtC,gBAAA,SAAS,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;aACvD;SACF;aAAM;YACL,aAAa,GAAG,kBAAkB,CAAC;SACpC;AAED,QAAA,MAAM,eAAe,GAAG,IAAI,eAAe,CACzC,aAAa,EACb,MAAM,CAAC,gBAAgB,EACvB,QAAQ,CAAC,MAAM,CAAC,EAAC,MAAM,EAAE,YAAY,IAAI,IAAI,CAAC,SAAS,EAAE,SAAS,EAAC,CAAC,EACpE,MAAM,CAAC,wBAAwB,CAChC,CAAC;QACF,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;QAErD,OAAO,YAAY,CAAC,QAAQ,CAAC;KAC9B;AAED;;;;;;;AAOG;AACK,IAAA,oBAAoB,CAC1B,sBAAyD,EACzD,SAA0B,EAC1B,eAAiC,EACjC,MAAwC,EAAA;AAExC,QAAA,IAAI,sBAAsB,YAAY,WAAW,EAAE;AACjD,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,SAAS,CAAC,CAAC;YACrF,IAAI,OAAO,GAAQ,EAAC,SAAS,EAAE,MAAM,CAAC,IAAI,EAAE,SAAS,EAAC,CAAC;AAEvD,YAAA,IAAI,MAAM,CAAC,eAAe,EAAE;AAC1B,gBAAA,OAAO,GAAG;AACR,oBAAA,GAAG,OAAO;AACV,oBAAA,IAAI,OAAO,MAAM,CAAC,eAAe,KAAK,UAAU;AAC9C,0BAAE,MAAM,CAAC,eAAe,EAAE;AAC1B,0BAAE,MAAM,CAAC,eAAe,CAAC;iBAC5B,CAAC;aACH;AAED,YAAA,eAAe,CAAC,oBAAoB,CAClC,IAAI,cAAc,CAAI,sBAAsB,EAAE,IAAK,EAAE,OAAO,EAAE,QAAQ,CAAC,CACxE,CAAC;SACH;aAAM;AACL,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YAC1F,MAAM,UAAU,GAAG,eAAe,CAAC,qBAAqB,CACtD,IAAI,eAAe,CACjB,sBAAsB,EACtB,MAAM,CAAC,gBAAgB,EACvB,QAAQ,EACR,MAAM,CAAC,wBAAwB,CAChC,CACF,CAAC;AACD,YAAA,SAA6C,CAAC,YAAY,GAAG,UAAU,CAAC;AACxE,YAAA,SAAoC,CAAC,iBAAiB,GAAG,UAAU,CAAC,QAAQ,CAAC;SAC/E;KACF;AAED;;;;;;;;;AASG;AACK,IAAA,eAAe,CACrB,MAAwC,EACxC,SAA0B,EAC1B,eAAiC,EACjC,gBAAsC,EAAA;QAEtC,MAAM,YAAY,GAAG,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,gBAAgB,EAAE,QAAQ,CAAC;AAC1E,QAAA,MAAM,SAAS,GAAqB;YAClC,EAAC,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,CAAC,IAAI,EAAC;AAC7C,YAAA,EAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAC;SAC1C,CAAC;AAEF,QAAA,IAAI,MAAM,CAAC,SAAS,EAAE;AACpB,YAAA,IAAI,OAAO,MAAM,CAAC,SAAS,KAAK,UAAU,EAAE;AAC1C,gBAAA,SAAS,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC,CAAC;aACzE;iBAAM;gBACL,SAAS,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;aACrC;SACF;QAED,IACE,MAAM,CAAC,SAAS;AAChB,aAAC,CAAC,YAAY;AACZ,gBAAA,CAAC,YAAY,CAAC,GAAG,CAAwB,cAAc,EAAE,IAAI,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC,EACnF;YACA,SAAS,CAAC,IAAI,CAAC;AACb,gBAAA,OAAO,EAAE,cAAc;AACvB,gBAAA,QAAQ,EAAE,EAAC,KAAK,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,EAAEC,EAAY,EAAE,EAAC;AAC5D,aAAA,CAAC,CAAC;SACJ;AAED,QAAA,OAAO,QAAQ,CAAC,MAAM,CAAC,EAAC,MAAM,EAAE,YAAY,IAAI,gBAAgB,EAAE,SAAS,EAAC,CAAC,CAAC;KAC/E;AAED;;;;AAIG;IACK,iBAAiB,CAAO,SAA0B,EAAE,SAAkB,EAAA;QAC5E,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AAElD,QAAA,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACb,IAAI,CAAC,WAAiC,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;;;AAIzD,YAAA,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;gBAC5B,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,OAAO,KAAI;oBAC1D,IAAI,aAAa,EAAE;AACjB,wBAAA,OAAO,CAAC,YAAY,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;qBACpD;yBAAM;AACL,wBAAA,OAAO,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;qBACxC;AACH,iBAAC,CAAC,CAAC;AAEH,gBAAA,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;gBAEjC,IAAI,SAAS,EAAE;AACb,oBAAA,IAAI,CAAC,kBAAkB,EAAE,CAAC,IAAI,EAAE,CAAC;iBAClC;aACF;SACF;KACF;;IAGO,4CAA4C,GAAA;QAClD,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,CAAC;;AAGtE,QAAA,IAAI,gBAAgB,CAAC,aAAa,EAAE;AAClC,YAAA,MAAM,QAAQ,GAAG,gBAAgB,CAAC,aAAa,CAAC,QAAQ,CAAC;AAEzD,YAAA,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;AAC7C,gBAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAE5B,IACE,OAAO,KAAK,gBAAgB;oBAC5B,OAAO,CAAC,QAAQ,KAAK,QAAQ;oBAC7B,OAAO,CAAC,QAAQ,KAAK,OAAO;AAC5B,oBAAA,CAAC,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,EAClC;AACA,oBAAA,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC;AAC3E,oBAAA,OAAO,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;iBAC7C;aACF;SACF;KACF;IAEO,kBAAkB,GAAA;AACxB,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC;AAClC,QAAA,OAAO,MAAM,GAAG,MAAM,CAAC,kBAAkB,EAAE,GAAG,IAAI,CAAC,0BAA0B,CAAC;KAC/E;8GAtWU,MAAM,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAC,IAAA,CAAA,OAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EA8BK,qBAAqB,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,MAAA,EAAA,QAAA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAAA,IAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAGjC,sBAAsB,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAjCrB,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,MAAM,cADM,MAAM,EAAA,CAAA,CAAA,EAAA;;2FAClB,MAAM,EAAA,UAAA,EAAA,CAAA;kBADlB,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;0BA+B3B,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;;0BACxC,QAAQ;;0BAAI,QAAQ;;0BAEpB,MAAM;2BAAC,sBAAsB,CAAA;;AAwUlC;;;AAGG;AACH,SAAS,cAAc,CAAI,KAAyB,EAAE,QAA8B,EAAA;AAClF,IAAA,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;IAErB,OAAO,CAAC,EAAE,EAAE;AACV,QAAA,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;KACpB;AACH;;MCpYa,YAAY,CAAA;8GAAZ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAZ,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,YATb,aAAa,EAAE,YAAY,EAAE,UAAU,EAAE,kBAAkB,CAAA,EAAA,OAAA,EAAA;;;YAInE,YAAY;YACZ,kBAAkB,CAAA,EAAA,CAAA,CAAA,EAAA;+GAIT,YAAY,EAAA,SAAA,EAFZ,CAAC,MAAM,CAAC,YAPT,aAAa,EAAE,YAAY,EAAE,UAAU;;;YAI/C,YAAY,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAKH,YAAY,EAAA,UAAA,EAAA,CAAA;kBAVxB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,aAAa,EAAE,YAAY,EAAE,UAAU,EAAE,kBAAkB,CAAC;AACtE,oBAAA,OAAO,EAAE;;;wBAGP,YAAY;wBACZ,kBAAkB;AACnB,qBAAA;oBACD,SAAS,EAAE,CAAC,MAAM,CAAC;AACpB,iBAAA,CAAA;;;ACxBD;;AAEG;;;;"}