"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getProjectIndexFiles = void 0;
const project_targets_1 = require("./project-targets");
/** Gets the path of the index file in the given project. */
function getProjectIndexFiles(project) {
    const paths = (0, project_targets_1.getProjectBuildTargets)(project)
        .filter(t => t.options?.['index'])
        .map(t => t.options['index']);
    // Use a set to remove duplicate index files referenced in multiple build targets of a project.
    return Array.from(new Set(paths));
}
exports.getProjectIndexFiles = getProjectIndexFiles;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicHJvamVjdC1pbmRleC1maWxlLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vLi4vLi4vc3JjL2Nkay9zY2hlbWF0aWNzL3V0aWxzL3Byb2plY3QtaW5kZXgtZmlsZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQUE7Ozs7OztHQU1HOzs7QUFHSCx1REFBeUQ7QUFFekQsNERBQTREO0FBQzVELFNBQWdCLG9CQUFvQixDQUFDLE9BQXFDO0lBQ3hFLE1BQU0sS0FBSyxHQUFHLElBQUEsd0NBQXNCLEVBQUMsT0FBTyxDQUFDO1NBQzFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxPQUFPLENBQUMsQ0FBQztTQUNqQyxHQUFHLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsT0FBUSxDQUFDLE9BQU8sQ0FBUyxDQUFDLENBQUM7SUFFekMsK0ZBQStGO0lBQy9GLE9BQU8sS0FBSyxDQUFDLElBQUksQ0FBQyxJQUFJLEdBQUcsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDO0FBQ3BDLENBQUM7QUFQRCxvREFPQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuXG5pbXBvcnQge1BhdGgsIHdvcmtzcGFjZXN9IGZyb20gJ0Bhbmd1bGFyLWRldmtpdC9jb3JlJztcbmltcG9ydCB7Z2V0UHJvamVjdEJ1aWxkVGFyZ2V0c30gZnJvbSAnLi9wcm9qZWN0LXRhcmdldHMnO1xuXG4vKiogR2V0cyB0aGUgcGF0aCBvZiB0aGUgaW5kZXggZmlsZSBpbiB0aGUgZ2l2ZW4gcHJvamVjdC4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRQcm9qZWN0SW5kZXhGaWxlcyhwcm9qZWN0OiB3b3Jrc3BhY2VzLlByb2plY3REZWZpbml0aW9uKTogUGF0aFtdIHtcbiAgY29uc3QgcGF0aHMgPSBnZXRQcm9qZWN0QnVpbGRUYXJnZXRzKHByb2plY3QpXG4gICAgLmZpbHRlcih0ID0+IHQub3B0aW9ucz8uWydpbmRleCddKVxuICAgIC5tYXAodCA9PiB0Lm9wdGlvbnMhWydpbmRleCddIGFzIFBhdGgpO1xuXG4gIC8vIFVzZSBhIHNldCB0byByZW1vdmUgZHVwbGljYXRlIGluZGV4IGZpbGVzIHJlZmVyZW5jZWQgaW4gbXVsdGlwbGUgYnVpbGQgdGFyZ2V0cyBvZiBhIHByb2plY3QuXG4gIHJldHVybiBBcnJheS5mcm9tKG5ldyBTZXQocGF0aHMpKTtcbn1cbiJdfQ==