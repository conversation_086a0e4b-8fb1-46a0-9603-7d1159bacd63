/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    AFN: never[];
    ALL: string[];
    AMD: never[];
    AOA: never[];
    ARS: never[];
    AUD: string[];
    AZN: never[];
    BAM: never[];
    BBD: never[];
    BDT: never[];
    BMD: never[];
    BND: never[];
    BOB: never[];
    BRL: never[];
    BSD: never[];
    BWP: never[];
    BZD: never[];
    CAD: string[];
    CLP: never[];
    CNY: string[];
    COP: never[];
    CRC: never[];
    CUC: never[];
    CUP: never[];
    CZK: never[];
    DKK: never[];
    DOP: never[];
    EGP: never[];
    FJD: never[];
    FKP: never[];
    GBP: string[];
    GEL: never[];
    GIP: never[];
    GNF: never[];
    GTQ: never[];
    GYD: never[];
    HKD: string[];
    HNL: never[];
    HRK: never[];
    HUF: never[];
    IDR: never[];
    ILS: string[];
    INR: string[];
    ISK: never[];
    JMD: never[];
    JPY: string[];
    KHR: never[];
    KMF: never[];
    KPW: never[];
    KRW: string[];
    KYD: never[];
    KZT: never[];
    LAK: never[];
    LBP: never[];
    LKR: never[];
    LRD: never[];
    MGA: never[];
    MMK: never[];
    MNT: never[];
    MUR: never[];
    MXN: string[];
    MYR: never[];
    NAD: never[];
    NGN: never[];
    NIO: never[];
    NOK: never[];
    NPR: never[];
    NZD: string[];
    PHP: never[];
    PKR: never[];
    PLN: never[];
    PYG: never[];
    RON: never[];
    RUB: never[];
    RWF: never[];
    SBD: never[];
    SEK: never[];
    SGD: never[];
    SHP: never[];
    SRD: never[];
    SSP: never[];
    STN: never[];
    SYP: never[];
    THB: string[];
    TOP: never[];
    TRY: never[];
    TTD: never[];
    TWD: string[];
    UAH: never[];
    USD: string[];
    UYU: never[];
    VND: string[];
    XCD: string[];
    ZAR: never[];
    ZMW: never[];
} | undefined)[];
export default _default;
