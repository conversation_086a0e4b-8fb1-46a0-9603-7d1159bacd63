/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val, i = Math.floor(Math.abs(val));
    if (i === 0 || n === 1)
        return 1;
    return 5;
}
export default ["am", [["ጠ", "ከ"], ["ጥዋት", "ከሰዓት"], u], u, [["እ", "ሰ", "ማ", "ረ", "ሐ", "ዓ", "ቅ"], ["እሑድ", "ሰኞ", "ማክሰ", "ረቡዕ", "ሐሙስ", "ዓርብ", "ቅዳሜ"], ["እሑድ", "ሰኞ", "ማክሰኞ", "ረቡዕ", "ሐሙስ", "ዓርብ", "ቅዳሜ"], ["እ", "ሰ", "ማ", "ረ", "ሐ", "ዓ", "ቅ"]], u, [["ጃ", "ፌ", "ማ", "ኤ", "ሜ", "ጁ", "ጁ", "ኦ", "ሴ", "ኦ", "ኖ", "ዲ"], ["ጃንዩ", "ፌብሩ", "ማርች", "ኤፕሪ", "ሜይ", "ጁን", "ጁላይ", "ኦገስ", "ሴፕቴ", "ኦክቶ", "ኖቬም", "ዲሴም"], ["ጃንዩወሪ", "ፌብሩወሪ", "ማርች", "ኤፕሪል", "ሜይ", "ጁን", "ጁላይ", "ኦገስት", "ሴፕቴምበር", "ኦክቶበር", "ኖቬምበር", "ዲሴምበር"]], u, [["ዓ/ዓ", "ዓ/ም"], u, ["ዓመተ ዓለም", "ዓመተ ምሕረት"]], 0, [6, 0], ["dd/MM/y", "d MMM y", "d MMMM y", "y MMMM d, EEEE"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "ETB", "ብር", "የኢትዮጵያ ብር", { "AUD": ["AU$", "$"], "BYN": [u, "р."], "CNH": ["የቻይና ዩዋን"], "ETB": ["ብር"], "JPY": ["JP¥", "¥"], "PHP": [u, "₱"], "THB": ["฿"], "TWD": ["NT$"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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