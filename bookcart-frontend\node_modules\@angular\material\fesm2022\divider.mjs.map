{"version": 3, "file": "divider.mjs", "sources": ["../../../../../../src/material/divider/divider.ts", "../../../../../../src/material/divider/divider-module.ts", "../../../../../../src/material/divider/divider_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ChangeDetectionStrategy, Component, Input, ViewEncapsulation} from '@angular/core';\nimport {BooleanInput, coerceBooleanProperty} from '@angular/cdk/coercion';\n\n@Component({\n  selector: 'mat-divider',\n  host: {\n    'role': 'separator',\n    '[attr.aria-orientation]': 'vertical ? \"vertical\" : \"horizontal\"',\n    '[class.mat-divider-vertical]': 'vertical',\n    '[class.mat-divider-horizontal]': '!vertical',\n    '[class.mat-divider-inset]': 'inset',\n    'class': 'mat-divider',\n  },\n  template: '',\n  styleUrl: 'divider.css',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: true,\n})\nexport class MatDivider {\n  /** Whether the divider is vertically aligned. */\n  @Input()\n  get vertical(): boolean {\n    return this._vertical;\n  }\n  set vertical(value: BooleanInput) {\n    this._vertical = coerceBooleanProperty(value);\n  }\n  private _vertical: boolean = false;\n\n  /** Whether the divider is an inset divider. */\n  @Input()\n  get inset(): boolean {\n    return this._inset;\n  }\n  set inset(value: BooleanInput) {\n    this._inset = coerceBooleanProperty(value);\n  }\n  private _inset: boolean = false;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '@angular/material/core';\nimport {MatDivider} from './divider';\n\n@NgModule({\n  imports: [MatCommonModule, MatDivider],\n  exports: [MatDivider, MatCommonModule],\n})\nexport class MatDividerModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;MA2Ba,UAAU,CAAA;AAhBvB,IAAA,WAAA,GAAA;QAyBU,IAAS,CAAA,SAAA,GAAY,KAAK,CAAC;QAU3B,IAAM,CAAA,MAAA,GAAY,KAAK,CAAC;AACjC,KAAA;;AAlBC,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,KAAmB,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;KAC/C;;AAID,IAAA,IACI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;KACpB;IACD,IAAI,KAAK,CAAC,KAAmB,EAAA;AAC3B,QAAA,IAAI,CAAC,MAAM,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;KAC5C;8GAlBU,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAV,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAU,uZANX,EAAE,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,6aAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAMD,UAAU,EAAA,UAAA,EAAA,CAAA;kBAhBtB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,aAAa,EACjB,IAAA,EAAA;AACJ,wBAAA,MAAM,EAAE,WAAW;AACnB,wBAAA,yBAAyB,EAAE,sCAAsC;AACjE,wBAAA,8BAA8B,EAAE,UAAU;AAC1C,wBAAA,gCAAgC,EAAE,WAAW;AAC7C,wBAAA,2BAA2B,EAAE,OAAO;AACpC,wBAAA,OAAO,EAAE,aAAa;qBACvB,EACS,QAAA,EAAA,EAAE,EAEG,aAAA,EAAA,iBAAiB,CAAC,IAAI,mBACpB,uBAAuB,CAAC,MAAM,EAAA,UAAA,EACnC,IAAI,EAAA,MAAA,EAAA,CAAA,6aAAA,CAAA,EAAA,CAAA;8BAKZ,QAAQ,EAAA,CAAA;sBADX,KAAK;gBAWF,KAAK,EAAA,CAAA;sBADR,KAAK;;;MCvBK,gBAAgB,CAAA;8GAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAhB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,YAHjB,eAAe,EAAE,UAAU,CAC3B,EAAA,OAAA,EAAA,CAAA,UAAU,EAAE,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;+GAE1B,gBAAgB,EAAA,OAAA,EAAA,CAHjB,eAAe,EACH,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAE1B,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAJ5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,UAAU,CAAC;AACtC,oBAAA,OAAO,EAAE,CAAC,UAAU,EAAE,eAAe,CAAC;AACvC,iBAAA,CAAA;;;ACfD;;AAEG;;;;"}