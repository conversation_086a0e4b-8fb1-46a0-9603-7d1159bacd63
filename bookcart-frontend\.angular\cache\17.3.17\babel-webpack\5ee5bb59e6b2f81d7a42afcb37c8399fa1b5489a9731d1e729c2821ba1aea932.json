{"ast": null, "code": "import { Scheduler } from '../Scheduler';\nexport class AsyncScheduler extends Scheduler {\n  constructor(SchedulerAction, now = Scheduler.now) {\n    super(SchedulerAction, now);\n    this.actions = [];\n    this._active = false;\n  }\n  flush(action) {\n    const {\n      actions\n    } = this;\n    if (this._active) {\n      actions.push(action);\n      return;\n    }\n    let error;\n    this._active = true;\n    do {\n      if (error = action.execute(action.state, action.delay)) {\n        break;\n      }\n    } while (action = actions.shift());\n    this._active = false;\n    if (error) {\n      while (action = actions.shift()) {\n        action.unsubscribe();\n      }\n      throw error;\n    }\n  }\n}", "map": {"version": 3, "names": ["Scheduler", "AsyncScheduler", "constructor", "SchedulerAction", "now", "actions", "_active", "flush", "action", "push", "error", "execute", "state", "delay", "shift", "unsubscribe"], "sources": ["C:/Users/<USER>/Desktop/BookCart/bookcart-frontend/node_modules/rxjs/dist/esm/internal/scheduler/AsyncScheduler.js"], "sourcesContent": ["import { Scheduler } from '../Scheduler';\nexport class AsyncScheduler extends Scheduler {\n    constructor(SchedulerAction, now = Scheduler.now) {\n        super(SchedulerAction, now);\n        this.actions = [];\n        this._active = false;\n    }\n    flush(action) {\n        const { actions } = this;\n        if (this._active) {\n            actions.push(action);\n            return;\n        }\n        let error;\n        this._active = true;\n        do {\n            if ((error = action.execute(action.state, action.delay))) {\n                break;\n            }\n        } while ((action = actions.shift()));\n        this._active = false;\n        if (error) {\n            while ((action = actions.shift())) {\n                action.unsubscribe();\n            }\n            throw error;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,cAAc;AACxC,OAAO,MAAMC,cAAc,SAASD,SAAS,CAAC;EAC1CE,WAAWA,CAACC,eAAe,EAAEC,GAAG,GAAGJ,SAAS,CAACI,GAAG,EAAE;IAC9C,KAAK,CAACD,eAAe,EAAEC,GAAG,CAAC;IAC3B,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,OAAO,GAAG,KAAK;EACxB;EACAC,KAAKA,CAACC,MAAM,EAAE;IACV,MAAM;MAAEH;IAAQ,CAAC,GAAG,IAAI;IACxB,IAAI,IAAI,CAACC,OAAO,EAAE;MACdD,OAAO,CAACI,IAAI,CAACD,MAAM,CAAC;MACpB;IACJ;IACA,IAAIE,KAAK;IACT,IAAI,CAACJ,OAAO,GAAG,IAAI;IACnB,GAAG;MACC,IAAKI,KAAK,GAAGF,MAAM,CAACG,OAAO,CAACH,MAAM,CAACI,KAAK,EAAEJ,MAAM,CAACK,KAAK,CAAC,EAAG;QACtD;MACJ;IACJ,CAAC,QAASL,MAAM,GAAGH,OAAO,CAACS,KAAK,CAAC,CAAC;IAClC,IAAI,CAACR,OAAO,GAAG,KAAK;IACpB,IAAII,KAAK,EAAE;MACP,OAAQF,MAAM,GAAGH,OAAO,CAACS,KAAK,CAAC,CAAC,EAAG;QAC/BN,MAAM,CAACO,WAAW,CAAC,CAAC;MACxB;MACA,MAAML,KAAK;IACf;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}