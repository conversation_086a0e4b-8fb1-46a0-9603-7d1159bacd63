/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { noSideEffects } from '../util/closure';
/**
 * The name of a field that Angular monkey-patches onto a component
 * class to store a function that loads defer-loadable dependencies
 * and applies metadata to a class.
 */
const ASYNC_COMPONENT_METADATA_FN = '__ngAsyncComponentMetadataFn__';
/**
 * If a given component has unresolved async metadata - returns a reference
 * to a function that applies component metadata after resolving defer-loadable
 * dependencies. Otherwise - this function returns `null`.
 */
export function getAsyncClassMetadataFn(type) {
    const componentClass = type; // cast to `any`, so that we can read a monkey-patched field
    return componentClass[ASYNC_COMPONENT_METADATA_FN] ?? null;
}
/**
 * Handles the process of applying metadata info to a component class in case
 * component template has defer blocks (thus some dependencies became deferrable).
 *
 * @param type Component class where metadata should be added
 * @param dependencyLoaderFn Function that loads dependencies
 * @param metadataSetterFn Function that forms a scope in which the `setClassMetadata` is invoked
 */
export function setClassMetadataAsync(type, dependencyLoaderFn, metadataSetterFn) {
    const componentClass = type; // cast to `any`, so that we can monkey-patch it
    componentClass[ASYNC_COMPONENT_METADATA_FN] = () => Promise.all(dependencyLoaderFn()).then(dependencies => {
        metadataSetterFn(...dependencies);
        // Metadata is now set, reset field value to indicate that this component
        // can by used/compiled synchronously.
        componentClass[ASYNC_COMPONENT_METADATA_FN] = null;
        return dependencies;
    });
    return componentClass[ASYNC_COMPONENT_METADATA_FN];
}
/**
 * Adds decorator, constructor, and property metadata to a given type via static metadata fields
 * on the type.
 *
 * These metadata fields can later be read with Angular's `ReflectionCapabilities` API.
 *
 * Calls to `setClassMetadata` can be guarded by ngDevMode, resulting in the metadata assignments
 * being tree-shaken away during production builds.
 */
export function setClassMetadata(type, decorators, ctorParameters, propDecorators) {
    return noSideEffects(() => {
        const clazz = type;
        if (decorators !== null) {
            if (clazz.hasOwnProperty('decorators') && clazz.decorators !== undefined) {
                clazz.decorators.push(...decorators);
            }
            else {
                clazz.decorators = decorators;
            }
        }
        if (ctorParameters !== null) {
            // Rather than merging, clobber the existing parameters. If other projects exist which
            // use tsickle-style annotations and reflect over them in the same way, this could
            // cause issues, but that is vanishingly unlikely.
            clazz.ctorParameters = ctorParameters;
        }
        if (propDecorators !== null) {
            // The property decorator objects are merged as it is possible different fields have
            // different decorator types. Decorators on individual fields are not merged, as it's
            // also incredibly unlikely that a field will be decorated both with an Angular
            // decorator and a non-Angular decorator that's also been downleveled.
            if (clazz.hasOwnProperty('propDecorators') && clazz.propDecorators !== undefined) {
                clazz.propDecorators = { ...clazz.propDecorators, ...propDecorators };
            }
            else {
                clazz.propDecorators = propDecorators;
            }
        }
    });
}
//# sourceMappingURL=data:application/json;base64,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