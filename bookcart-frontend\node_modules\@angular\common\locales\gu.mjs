/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val, i = Math.floor(Math.abs(val));
    if (i === 0 || n === 1)
        return 1;
    return 5;
}
export default ["gu", [["AM", "PM"], u, u], u, [["ર", "સો", "મં", "બુ", "ગુ", "શુ", "શ"], ["રવિ", "સોમ", "મંગળ", "બુધ", "ગુરુ", "શુક્ર", "શનિ"], ["રવિવાર", "સોમવાર", "મંગળવાર", "બુધવાર", "ગુરુવાર", "શુક્રવાર", "શનિવાર"], ["ર", "સો", "મં", "બુ", "ગુ", "શુ", "શ"]], u, [["જા", "ફે", "મા", "એ", "મે", "જૂ", "જુ", "ઑ", "સ", "ઑ", "ન", "ડિ"], ["જાન્યુ", "ફેબ્રુ", "માર્ચ", "એપ્રિલ", "મે", "જૂન", "જુલાઈ", "ઑગસ્ટ", "સપ્ટે", "ઑક્ટો", "નવે", "ડિસે"], ["જાન્યુઆરી", "ફેબ્રુઆરી", "માર્ચ", "એપ્રિલ", "મે", "જૂન", "જુલાઈ", "ઑગસ્ટ", "સપ્ટેમ્બર", "ઑક્ટોબર", "નવેમ્બર", "ડિસેમ્બર"]], u, [["ઇ સ પુ", "ઇસ"], ["ઈ.સ.પૂર્વે", "ઈ.સ."], ["ઈસવીસન પૂર્વે", "ઇસવીસન"]], 0, [0, 0], ["d/M/yy", "d MMM, y", "d MMMM, y", "EEEE, d MMMM, y"], ["hh:mm a", "hh:mm:ss a", "hh:mm:ss a z", "hh:mm:ss a zzzz"], ["{1} {0}", u, "{1} એ {0} વાગ્યે", u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##,##0.###", "#,##,##0%", "¤#,##,##0.00", "[#E0]"], "INR", "₹", "ભારતીય રૂપિયા", { "BYN": [u, "р."], "JPY": ["JP¥", "¥"], "MUR": [u, "રૂ."], "PHP": [u, "₱"], "THB": ["฿"], "TWD": ["NT$"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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