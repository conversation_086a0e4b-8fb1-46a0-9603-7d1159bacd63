/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val, i = Math.floor(Math.abs(val)), f = parseInt(val.toString().replace(/^[^.]*\.?/, ''), 10) || 0;
    if (n === 0 || n === 1 || i === 0 && f === 1)
        return 1;
    return 5;
}
export default ["si", [["පෙ", "ප"], ["පෙ.ව.", "ප.ව."], u], [["පෙ.ව.", "ප.ව."], u, u], [["ඉ", "ස", "අ", "බ", "බ්‍ර", "සි", "සෙ"], ["ඉරිදා", "සඳුදා", "අඟහ", "බදාදා", "බ්‍රහස්", "සිකු", "සෙන"], ["ඉරිදා", "සඳුදා", "අඟහරුවාදා", "බදාදා", "බ්‍රහස්පතින්දා", "සිකුරාදා", "සෙනසුරාදා"], ["ඉරි", "සඳු", "අඟ", "බදා", "බ්‍රහ", "සිකු", "සෙන"]], u, [["ජ", "පෙ", "මා", "අ", "මැ", "ජූ", "ජූ", "අ", "සැ", "ඔ", "නෙ", "දෙ"], ["ජන", "පෙබ", "මාර්තු", "අප්‍රේල්", "මැයි", "ජූනි", "ජූලි", "අගෝ", "සැප්", "ඔක්", "නොවැ", "දෙසැ"], ["ජනවාරි", "පෙබරවාරි", "මාර්තු", "අප්‍රේල්", "මැයි", "ජූනි", "ජූලි", "අගෝස්තු", "සැප්තැම්බර්", "ඔක්තෝබර්", "නොවැම්බර්", "දෙසැම්බර්"]], [["ජ", "පෙ", "මා", "අ", "මැ", "ජූ", "ජූ", "අ", "සැ", "ඔ", "නෙ", "දෙ"], ["ජන", "පෙබ", "මාර්", "අප්‍රේල්", "මැයි", "ජූනි", "ජූලි", "අගෝ", "සැප්", "ඔක්", "නොවැ", "දෙසැ"], ["ජනවාරි", "පෙබරවාරි", "මාර්තු", "අප්‍රේල්", "මැයි", "ජූනි", "ජූලි", "අගෝස්තු", "සැප්තැම්බර්", "ඔක්තෝබර්", "නොවැම්බර්", "දෙසැම්බර්"]], [["ක්‍රි.පූ.", "ක්‍රි.ව."], u, ["ක්‍රිස්තු පූර්ව", "ක්‍රිස්තු වර්ෂ"]], 1, [6, 0], ["y-MM-dd", "y MMM d", "y MMMM d", "y MMMM d, EEEE"], ["HH.mm", "HH.mm.ss", "HH.mm.ss z", "HH.mm.ss zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", "."], ["#,##0.###", "#,##0%", "¤#,##0.00", "#"], "LKR", "රු.", "ශ්‍රී ලංකා රුපියල", { "BYN": [u, "р."], "JPY": ["JP¥", "¥"], "LKR": ["රු."], "PHP": [u, "₱"], "THB": ["฿"], "TWD": ["NT$"], "USD": ["US$", "$"], "XOF": ["සිෆ්එ"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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