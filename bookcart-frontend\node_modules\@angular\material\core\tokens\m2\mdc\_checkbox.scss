@use 'sass:map';
@use '../../../theming/palette';
@use '../../../theming/theming';
@use '../../../theming/inspection';
@use '../../../style/sass-utils';
@use '../../../mdc-helpers/mdc-helpers';
@use '../../token-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mdc, checkbox);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
//
// Tokens that are available in MDC, but not used in Angular Material should be mapped to `null`.
// `null` indicates that we are intentionally choosing not to emit a slot or value for the token in
// our CSS.
@function get-unthemable-tokens() {
  @return (
    // The color of the checkmark when the checkbox is selected and disabled.
    disabled-selected-checkmark-color: #fff,
    // The opacity of the ripple when the checkbox is selected and focused.
    selected-focus-state-layer-opacity: 0.16,
    // The opacity of the ripple when the checkbox is selected and hovered.
    selected-hover-state-layer-opacity: 0.04,
    // The opacity of the ripple when the checkbox is selected and pressed.
    selected-pressed-state-layer-opacity: 0.16,
    // The opacity of the ripple when the checkbox is unselected and focused.
    unselected-focus-state-layer-opacity: 0.16,
    // The opacity of the ripple when the checkbox is unselected and hovered.
    unselected-hover-state-layer-opacity: 0.04,
    // The opacity of the ripple when the checkbox is unselected and pressed.
    unselected-pressed-state-layer-opacity: 0.16,
    // =============================================================================================
    // = TOKENS NOT USED IN ANGULAR MATERIAL                                                       =
    // =============================================================================================
    // MDC currently doesn't output a slot for these tokens.
    disabled-selected-icon-opacity: null,
    disabled-unselected-icon-opacity: null,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme, $palette-name: accent) {
  $is-dark: inspection.get-theme-type($theme) == dark;
  $foreground-base: inspection.get-theme-color($theme, foreground, base);
  $palette-default: inspection.get-theme-color($theme, $palette-name, default);
  $disabled-color: sass-utils.safe-color-change(
      inspection.get-theme-color($theme, foreground, base), $alpha: 0.38);
  $palette-selected: inspection.get-theme-color($theme, $palette-name);
  $border-color: sass-utils.safe-color-change(
      inspection.get-theme-color($theme, foreground, base), $alpha: 0.54);
  $active-border-color:
    theming.get-color-from-palette(palette.$gray-palette, if($is-dark, 200, 900));
  $selected-checkmark-color: null;

  // Ideally we would derive all values directly from the theme, but it causes a lot of regressions
  // internally. For now we fall back to the old hardcoded behavior only for internal apps.
  @if (token-utils.$private-is-internal-build) {
    $contrast-tone: mdc-helpers.variable-safe-contrast-tone($palette-selected, $is-dark);
    $selected-checkmark-color: if($contrast-tone == 'dark', #000, #fff);
  }
  @else {
    $selected-checkmark-color:
      inspection.get-theme-color($theme, $palette-name, default-contrast, 1);
  }

  @return (
    // The color of the checkbox fill when the checkbox is selected and disabled.
    disabled-selected-icon-color: $disabled-color,
    // The color of the checkbox border when the checkbox is unselected and disabled.
    disabled-unselected-icon-color: $disabled-color,
    // The color of the checkmark when the checkbox is selected.
    selected-checkmark-color: $selected-checkmark-color,
    // The color of the checkbox fill when the checkbox is selected and focused.
    selected-focus-icon-color: $palette-selected,
    // The color of the checkbox fill when the checkbox is selected and hovered.
    selected-hover-icon-color: $palette-selected,
    // The color of the checkbox fill when the checkbox is selected.
    selected-icon-color: $palette-selected,
    // The color of the checkbox fill when the checkbox is selected an pressed.
    selected-pressed-icon-color: $palette-selected,
    // The color of the checkbox border when the checkbox is unselected and focused.
    unselected-focus-icon-color: $active-border-color,
    // The color of the checkbox border when the checkbox is unselected and hovered.
    unselected-hover-icon-color: $active-border-color,
    // The color of the checkbox border when the checkbox is unselected.
    unselected-icon-color: $border-color,
    // The color of the checkbox border when the checkbox is unselected and pressed.
    unselected-pressed-icon-color: $border-color,
    // The color of the ripple when the checkbox is selected and focused.
    selected-focus-state-layer-color: $palette-default,
    // The color of the ripple when the checkbox is selected and hovered.
    selected-hover-state-layer-color: $palette-default,
    // The color of the ripple when the checkbox is selected and pressed.
    selected-pressed-state-layer-color: $palette-default,
    // The color of the ripple when the checkbox is unselected and focused.
    unselected-focus-state-layer-color: $foreground-base,
    // The color of the ripple when the checkbox is unselected and hovered.
    unselected-hover-state-layer-color: $foreground-base,
    // The color of the ripple when the checkbox is unselected and pressed.
    unselected-pressed-state-layer-color: $foreground-base,
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return ();
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $scale: theming.clamp-density(inspection.get-theme-density($theme), -3);

  @return (
    // The diameter of the checkbox's ripple.
    state-layer-size: map.get((
      0: 40px,
      -1: 36px,
      -2: 32px,
      -3: 28px,
    ), $scale)
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-utils.$placeholder-color-config),
      get-typography-tokens(token-utils.$placeholder-typography-config),
      get-density-tokens(token-utils.$placeholder-density-config)
  );
}
