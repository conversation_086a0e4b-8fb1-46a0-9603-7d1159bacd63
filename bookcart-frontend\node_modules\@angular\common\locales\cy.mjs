/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 0)
        return 0;
    if (n === 1)
        return 1;
    if (n === 2)
        return 2;
    if (n === 3)
        return 3;
    if (n === 6)
        return 4;
    return 5;
}
export default ["cy", [["b", "h"], ["AM", "PM"], ["yb", "yh"]], [["AM", "PM"], u, u], [["S", "Ll", "M", "M", "I", "G", "S"], ["<PERSON>", "<PERSON>lun", "Maw", "Mer", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], ["<PERSON>yd<PERSON>", "<PERSON>yd<PERSON>lun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>yd<PERSON>rn"], ["<PERSON>", "<PERSON>l", "<PERSON>", "<PERSON>", "Ia", "Gw", "Sa"]], [["S", "Ll", "<PERSON>", "<PERSON>", "I", "G", "S"], ["<PERSON>", "Llun", "Maw", "Mer", "Iau", "Gwe", "Sad"], ["Dydd <PERSON>", "<PERSON>ydd Llun", "<PERSON>ydd <PERSON>wrth", "<PERSON>ydd Mercher", "Dydd Iau", "Dydd Gwener", "<PERSON>ydd <PERSON>wrn"], ["Su", "Ll", "Ma", "Me", "Ia", "Gw", "Sa"]], [["I", "Ch", "M", "E", "M", "M", "G", "A", "M", "H", "T", "Rh"], ["Ion", "Chwef", "Maw", "Ebr", "Mai", "Meh", "Gorff", "Awst", "Medi", "Hyd", "Tach", "Rhag"], ["Ionawr", "Chwefror", "Mawrth", "Ebrill", "Mai", "Mehefin", "Gorffennaf", "Awst", "Medi", "Hydref", "Tachwedd", "Rhagfyr"]], [["I", "Ch", "M", "E", "M", "M", "G", "A", "M", "H", "T", "Rh"], ["Ion", "Chw", "Maw", "Ebr", "Mai", "Meh", "Gor", "Awst", "Medi", "Hyd", "Tach", "Rhag"], ["Ionawr", "Chwefror", "Mawrth", "Ebrill", "Mai", "Mehefin", "Gorffennaf", "Awst", "Medi", "Hydref", "Tachwedd", "Rhagfyr"]], [["C", "O"], ["CC", "OC"], ["Cyn Crist", "Oed Crist"]], 1, [6, 0], ["dd/MM/yy", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, "{1} 'am' {0}", u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "GBP", "£", "Punt Prydain", { "BDT": [u, "TK"], "BWP": [], "BYN": [u, "р."], "HKD": ["HK$"], "JPY": ["JP¥", "¥"], "KRW": [u, "₩"], "PHP": [u, "₱"], "THB": ["฿"], "TWD": ["NT$"], "USD": ["US$", "$"], "XXX": [], "ZAR": [], "ZMW": [] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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