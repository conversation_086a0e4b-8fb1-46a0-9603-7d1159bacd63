/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// These are private APIs that are used both by the public APIs inside of this package, as well
// as in unit tests of other entry-points, hence why we need to re-export them through here.
export * from './dispatch-events';
export * from './event-objects';
export * from './element-focus';
export * from './type-in-element';
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi8uLi9zcmMvY2RrL3Rlc3RpbmcvdGVzdGJlZC9mYWtlLWV2ZW50cy9pbmRleC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7O0dBTUc7QUFFSCwrRkFBK0Y7QUFDL0YsNEZBQTRGO0FBQzVGLGNBQWMsbUJBQW1CLENBQUM7QUFDbEMsY0FBYyxpQkFBaUIsQ0FBQztBQUNoQyxjQUFjLGlCQUFpQixDQUFDO0FBQ2hDLGNBQWMsbUJBQW1CLENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgR29vZ2xlIExMQyBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICpcbiAqIFVzZSBvZiB0aGlzIHNvdXJjZSBjb2RlIGlzIGdvdmVybmVkIGJ5IGFuIE1JVC1zdHlsZSBsaWNlbnNlIHRoYXQgY2FuIGJlXG4gKiBmb3VuZCBpbiB0aGUgTElDRU5TRSBmaWxlIGF0IGh0dHBzOi8vYW5ndWxhci5pby9saWNlbnNlXG4gKi9cblxuLy8gVGhlc2UgYXJlIHByaXZhdGUgQVBJcyB0aGF0IGFyZSB1c2VkIGJvdGggYnkgdGhlIHB1YmxpYyBBUElzIGluc2lkZSBvZiB0aGlzIHBhY2thZ2UsIGFzIHdlbGxcbi8vIGFzIGluIHVuaXQgdGVzdHMgb2Ygb3RoZXIgZW50cnktcG9pbnRzLCBoZW5jZSB3aHkgd2UgbmVlZCB0byByZS1leHBvcnQgdGhlbSB0aHJvdWdoIGhlcmUuXG5leHBvcnQgKiBmcm9tICcuL2Rpc3BhdGNoLWV2ZW50cyc7XG5leHBvcnQgKiBmcm9tICcuL2V2ZW50LW9iamVjdHMnO1xuZXhwb3J0ICogZnJvbSAnLi9lbGVtZW50LWZvY3VzJztcbmV4cG9ydCAqIGZyb20gJy4vdHlwZS1pbi1lbGVtZW50JztcbiJdfQ==