{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/icon\";\nexport class OrderListComponent {\n  static {\n    this.ɵfac = function OrderListComponent_Factory(t) {\n      return new (t || OrderListComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OrderListComponent,\n      selectors: [[\"app-order-list\"]],\n      decls: 23,\n      vars: 0,\n      consts: [[1, \"order-list-container\"], [1, \"header\"], [1, \"coming-soon-card\"], [1, \"coming-soon-content\"], [1, \"coming-soon-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/books\"]],\n      template: function OrderListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\")(3, \"mat-icon\");\n          i0.ɵɵtext(4, \"receipt_long\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(5, \" My Orders \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\");\n          i0.ɵɵtext(7, \"View your order history\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"mat-card\", 2)(9, \"mat-card-content\")(10, \"div\", 3)(11, \"mat-icon\", 4);\n          i0.ɵɵtext(12, \"construction\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"h2\");\n          i0.ɵɵtext(14, \"Coming Soon!\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"p\");\n          i0.ɵɵtext(16, \"Order management functionality is under development.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"p\");\n          i0.ɵɵtext(18, \"You'll be able to view and track your orders here soon.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"button\", 5)(20, \"mat-icon\");\n          i0.ɵɵtext(21, \"library_books\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(22, \" Continue Shopping \");\n          i0.ɵɵelementEnd()()()()();\n        }\n      },\n      dependencies: [i1.RouterLink, i2.MatButton, i3.MatCard, i3.MatCardContent, i4.MatIcon],\n      styles: [\".order-list-container[_ngcontent-%COMP%] {\\n      max-width: 800px;\\n      margin: 0 auto;\\n      padding: 20px;\\n    }\\n    .header[_ngcontent-%COMP%] {\\n      margin-bottom: 30px;\\n    }\\n    .header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      gap: 12px;\\n      margin: 0 0 8px 0;\\n      color: #333;\\n    }\\n    .header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n      margin: 0;\\n      color: #666;\\n    }\\n    .coming-soon-card[_ngcontent-%COMP%] {\\n      padding: 20px;\\n    }\\n    .coming-soon-content[_ngcontent-%COMP%] {\\n      display: flex;\\n      flex-direction: column;\\n      align-items: center;\\n      text-align: center;\\n      padding: 40px 20px;\\n    }\\n    .coming-soon-icon[_ngcontent-%COMP%] {\\n      font-size: 80px;\\n      height: 80px;\\n      width: 80px;\\n      color: #ff9800;\\n      margin-bottom: 20px;\\n    }\\n    h2[_ngcontent-%COMP%] {\\n      margin: 0 0 16px 0;\\n      color: #333;\\n    }\\n    p[_ngcontent-%COMP%] {\\n      margin: 0 0 12px 0;\\n      color: #666;\\n    }\\n    button[_ngcontent-%COMP%] {\\n      margin-top: 24px;\\n    }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvb3JkZXJzL29yZGVyLWxpc3Qvb3JkZXItbGlzdC5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtJQUNJO01BQ0UsZ0JBQWdCO01BQ2hCLGNBQWM7TUFDZCxhQUFhO0lBQ2Y7SUFDQTtNQUNFLG1CQUFtQjtJQUNyQjtJQUNBO01BQ0UsYUFBYTtNQUNiLG1CQUFtQjtNQUNuQixTQUFTO01BQ1QsaUJBQWlCO01BQ2pCLFdBQVc7SUFDYjtJQUNBO01BQ0UsU0FBUztNQUNULFdBQVc7SUFDYjtJQUNBO01BQ0UsYUFBYTtJQUNmO0lBQ0E7TUFDRSxhQUFhO01BQ2Isc0JBQXNCO01BQ3RCLG1CQUFtQjtNQUNuQixrQkFBa0I7TUFDbEIsa0JBQWtCO0lBQ3BCO0lBQ0E7TUFDRSxlQUFlO01BQ2YsWUFBWTtNQUNaLFdBQVc7TUFDWCxjQUFjO01BQ2QsbUJBQW1CO0lBQ3JCO0lBQ0E7TUFDRSxrQkFBa0I7TUFDbEIsV0FBVztJQUNiO0lBQ0E7TUFDRSxrQkFBa0I7TUFDbEIsV0FBVztJQUNiO0lBQ0E7TUFDRSxnQkFBZ0I7SUFDbEIiLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAub3JkZXItbGlzdC1jb250YWluZXIge1xuICAgICAgbWF4LXdpZHRoOiA4MDBweDtcbiAgICAgIG1hcmdpbjogMCBhdXRvO1xuICAgICAgcGFkZGluZzogMjBweDtcbiAgICB9XG4gICAgLmhlYWRlciB7XG4gICAgICBtYXJnaW4tYm90dG9tOiAzMHB4O1xuICAgIH1cbiAgICAuaGVhZGVyIGgxIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgZ2FwOiAxMnB4O1xuICAgICAgbWFyZ2luOiAwIDAgOHB4IDA7XG4gICAgICBjb2xvcjogIzMzMztcbiAgICB9XG4gICAgLmhlYWRlciBwIHtcbiAgICAgIG1hcmdpbjogMDtcbiAgICAgIGNvbG9yOiAjNjY2O1xuICAgIH1cbiAgICAuY29taW5nLXNvb24tY2FyZCB7XG4gICAgICBwYWRkaW5nOiAyMHB4O1xuICAgIH1cbiAgICAuY29taW5nLXNvb24tY29udGVudCB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gICAgICBwYWRkaW5nOiA0MHB4IDIwcHg7XG4gICAgfVxuICAgIC5jb21pbmctc29vbi1pY29uIHtcbiAgICAgIGZvbnQtc2l6ZTogODBweDtcbiAgICAgIGhlaWdodDogODBweDtcbiAgICAgIHdpZHRoOiA4MHB4O1xuICAgICAgY29sb3I6ICNmZjk4MDA7XG4gICAgICBtYXJnaW4tYm90dG9tOiAyMHB4O1xuICAgIH1cbiAgICBoMiB7XG4gICAgICBtYXJnaW46IDAgMCAxNnB4IDA7XG4gICAgICBjb2xvcjogIzMzMztcbiAgICB9XG4gICAgcCB7XG4gICAgICBtYXJnaW46IDAgMCAxMnB4IDA7XG4gICAgICBjb2xvcjogIzY2NjtcbiAgICB9XG4gICAgYnV0dG9uIHtcbiAgICAgIG1hcmdpbi10b3A6IDI0cHg7XG4gICAgfVxuICAiXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["OrderListComponent", "selectors", "decls", "vars", "consts", "template", "OrderListComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\BookCart\\bookcart-frontend\\src\\app\\orders\\order-list\\order-list.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-order-list',\n  template: `\n    <div class=\"order-list-container\">\n      <div class=\"header\">\n        <h1>\n          <mat-icon>receipt_long</mat-icon>\n          My Orders\n        </h1>\n        <p>View your order history</p>\n      </div>\n\n      <mat-card class=\"coming-soon-card\">\n        <mat-card-content>\n          <div class=\"coming-soon-content\">\n            <mat-icon class=\"coming-soon-icon\">construction</mat-icon>\n            <h2>Coming Soon!</h2>\n            <p>Order management functionality is under development.</p>\n            <p>You'll be able to view and track your orders here soon.</p>\n            \n            <button mat-raised-button color=\"primary\" routerLink=\"/books\">\n              <mat-icon>library_books</mat-icon>\n              Continue Shopping\n            </button>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  `,\n  styles: [`\n    .order-list-container {\n      max-width: 800px;\n      margin: 0 auto;\n      padding: 20px;\n    }\n    .header {\n      margin-bottom: 30px;\n    }\n    .header h1 {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n      margin: 0 0 8px 0;\n      color: #333;\n    }\n    .header p {\n      margin: 0;\n      color: #666;\n    }\n    .coming-soon-card {\n      padding: 20px;\n    }\n    .coming-soon-content {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      text-align: center;\n      padding: 40px 20px;\n    }\n    .coming-soon-icon {\n      font-size: 80px;\n      height: 80px;\n      width: 80px;\n      color: #ff9800;\n      margin-bottom: 20px;\n    }\n    h2 {\n      margin: 0 0 16px 0;\n      color: #333;\n    }\n    p {\n      margin: 0 0 12px 0;\n      color: #666;\n    }\n    button {\n      margin-top: 24px;\n    }\n  `]\n})\nexport class OrderListComponent {}\n"], "mappings": ";;;;;AAiFA,OAAM,MAAOA,kBAAkB;;;uBAAlBA,kBAAkB;IAAA;EAAA;;;YAAlBA,kBAAkB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzErBE,EAHN,CAAAC,cAAA,aAAkC,aACZ,SACd,eACQ;UAAAD,EAAA,CAAAE,MAAA,mBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACjCH,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,8BAAuB;UAC5BF,EAD4B,CAAAG,YAAA,EAAI,EAC1B;UAKAH,EAHN,CAAAC,cAAA,kBAAmC,uBACf,cACiB,mBACI;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1DH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,4DAAoD;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC3DH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,+DAAuD;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAG5DH,EADF,CAAAC,cAAA,iBAA8D,gBAClD;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAClCH,EAAA,CAAAE,MAAA,2BACF;UAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACW,EACV,EACP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}