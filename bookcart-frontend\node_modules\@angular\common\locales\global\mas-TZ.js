/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

if (n === 1)
    return 1;
return 5;
}
    global.ng.common.locales['mas-tz'] = ["mas-TZ",[["Ɛnkakɛnyá","Ɛndámâ"],u,u],u,[["2","3","4","5","6","7","1"],["Jpi","Jtt","Jnn","Jtn","Alh","Iju","J<PERSON>"],["<PERSON><PERSON>p<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>"],["<PERSON><PERSON>","Jtt","Jnn","Jtn","<PERSON>h","<PERSON><PERSON>","J<PERSON>"]],u,[["1","2","3","4","5","6","7","8","9","10","11","12"],["<PERSON>","Ará","Ɔɛn","Doy","Lép","Rok","Sás","Bɔ́r","Kús","Gís","Shʉ́","Ntʉ́"],["Oladalʉ́","Ar<PERSON>t","Ɔɛnɨ́ɔɨŋɔk","Olodoyíóríê inkókúâ","Oloilépūnyīē inkókúâ","Kújúɔrɔk","Mórusásin","Ɔlɔ́ɨ́bɔ́rárɛ","Kúshîn","Olgísan","Pʉshʉ́ka","Ntʉ́ŋʉ́s"]],u,[["MY","EY"],u,["Meínō Yɛ́sʉ","Eínō Yɛ́sʉ"]],1,[6,0],["dd/MM/y","d MMM y","d MMMM y","EEEE, d MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤#,##0.00","#E0"],"TZS","TSh","Iropiyianí e Tanzania",{"JPY":["JP¥","¥"],"KES":["Ksh"],"TZS":["TSh"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    