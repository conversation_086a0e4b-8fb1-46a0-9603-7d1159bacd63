/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { ChangeDetectionStrategy } from '../change_detection/constants';
import { formatRuntimeError } from '../errors';
import { ViewEncapsulation } from '../metadata/view';
import { noSideEffects } from '../util/closure';
import { EMPTY_ARRAY, EMPTY_OBJ } from '../util/empty';
import { initNgDevMode } from '../util/ng_dev_mode';
import { stringify } from '../util/stringify';
import { NG_COMP_DEF, NG_DIR_DEF, NG_MOD_DEF, NG_PIPE_DEF } from './fields';
import { InputFlags } from './interfaces/input_flags';
import { stringifyCSSSelectorList } from './node_selector_matcher';
/**
 * Create a component definition object.
 *
 *
 * # Example
 * ```
 * class MyComponent {
 *   // Generated by Angular Template Compiler
 *   // [Symbol] syntax will not be supported by TypeScript until v2.7
 *   static ɵcmp = defineComponent({
 *     ...
 *   });
 * }
 * ```
 * @codeGenApi
 */
export function ɵɵdefineComponent(componentDefinition) {
    return noSideEffects(() => {
        // Initialize ngDevMode. This must be the first statement in ɵɵdefineComponent.
        // See the `initNgDevMode` docstring for more information.
        (typeof ngDevMode === 'undefined' || ngDevMode) && initNgDevMode();
        const baseDef = getNgDirectiveDef(componentDefinition);
        const def = {
            ...baseDef,
            decls: componentDefinition.decls,
            vars: componentDefinition.vars,
            template: componentDefinition.template,
            consts: componentDefinition.consts || null,
            ngContentSelectors: componentDefinition.ngContentSelectors,
            onPush: componentDefinition.changeDetection === ChangeDetectionStrategy.OnPush,
            directiveDefs: null, // assigned in noSideEffects
            pipeDefs: null, // assigned in noSideEffects
            dependencies: baseDef.standalone && componentDefinition.dependencies || null,
            getStandaloneInjector: null,
            signals: componentDefinition.signals ?? false,
            data: componentDefinition.data || {},
            encapsulation: componentDefinition.encapsulation || ViewEncapsulation.Emulated,
            styles: componentDefinition.styles || EMPTY_ARRAY,
            _: null,
            schemas: componentDefinition.schemas || null,
            tView: null,
            id: '',
        };
        initFeatures(def);
        const dependencies = componentDefinition.dependencies;
        def.directiveDefs = extractDefListOrFactory(dependencies, /* pipeDef */ false);
        def.pipeDefs = extractDefListOrFactory(dependencies, /* pipeDef */ true);
        def.id = getComponentId(def);
        return def;
    });
}
export function extractDirectiveDef(type) {
    return getComponentDef(type) || getDirectiveDef(type);
}
function nonNull(value) {
    return value !== null;
}
/**
 * @codeGenApi
 */
export function ɵɵdefineNgModule(def) {
    return noSideEffects(() => {
        const res = {
            type: def.type,
            bootstrap: def.bootstrap || EMPTY_ARRAY,
            declarations: def.declarations || EMPTY_ARRAY,
            imports: def.imports || EMPTY_ARRAY,
            exports: def.exports || EMPTY_ARRAY,
            transitiveCompileScopes: null,
            schemas: def.schemas || null,
            id: def.id || null,
        };
        return res;
    });
}
function parseAndConvertBindingsForDefinition(obj, declaredInputs) {
    if (obj == null)
        return EMPTY_OBJ;
    const newLookup = {};
    for (const minifiedKey in obj) {
        if (obj.hasOwnProperty(minifiedKey)) {
            const value = obj[minifiedKey];
            let publicName;
            let declaredName;
            let inputFlags = InputFlags.None;
            if (Array.isArray(value)) {
                inputFlags = value[0];
                publicName = value[1];
                declaredName = value[2] ?? publicName; // declared name might not be set to save bytes.
            }
            else {
                publicName = value;
                declaredName = value;
            }
            // For inputs, capture the declared name, or if some flags are set.
            if (declaredInputs) {
                // Perf note: An array is only allocated for the input if there are flags.
                newLookup[publicName] =
                    inputFlags !== InputFlags.None ? [minifiedKey, inputFlags] : minifiedKey;
                declaredInputs[publicName] = declaredName;
            }
            else {
                newLookup[publicName] = minifiedKey;
            }
        }
    }
    return newLookup;
}
/**
 * Create a directive definition object.
 *
 * # Example
 * ```ts
 * class MyDirective {
 *   // Generated by Angular Template Compiler
 *   // [Symbol] syntax will not be supported by TypeScript until v2.7
 *   static ɵdir = ɵɵdefineDirective({
 *     ...
 *   });
 * }
 * ```
 *
 * @codeGenApi
 */
export function ɵɵdefineDirective(directiveDefinition) {
    return noSideEffects(() => {
        const def = getNgDirectiveDef(directiveDefinition);
        initFeatures(def);
        return def;
    });
}
/**
 * Create a pipe definition object.
 *
 * # Example
 * ```
 * class MyPipe implements PipeTransform {
 *   // Generated by Angular Template Compiler
 *   static ɵpipe = definePipe({
 *     ...
 *   });
 * }
 * ```
 * @param pipeDef Pipe definition generated by the compiler
 *
 * @codeGenApi
 */
export function ɵɵdefinePipe(pipeDef) {
    return {
        type: pipeDef.type,
        name: pipeDef.name,
        factory: null,
        pure: pipeDef.pure !== false,
        standalone: pipeDef.standalone === true,
        onDestroy: pipeDef.type.prototype.ngOnDestroy || null
    };
}
/**
 * The following getter methods retrieve the definition from the type. Currently the retrieval
 * honors inheritance, but in the future we may change the rule to require that definitions are
 * explicit. This would require some sort of migration strategy.
 */
export function getComponentDef(type) {
    return type[NG_COMP_DEF] || null;
}
export function getDirectiveDef(type) {
    return type[NG_DIR_DEF] || null;
}
export function getPipeDef(type) {
    return type[NG_PIPE_DEF] || null;
}
/**
 * Checks whether a given Component, Directive or Pipe is marked as standalone.
 * This will return false if passed anything other than a Component, Directive, or Pipe class
 * See [this guide](/guide/standalone-components) for additional information:
 *
 * @param type A reference to a Component, Directive or Pipe.
 * @publicApi
 */
export function isStandalone(type) {
    const def = getComponentDef(type) || getDirectiveDef(type) || getPipeDef(type);
    return def !== null ? def.standalone : false;
}
export function getNgModuleDef(type, throwNotFound) {
    const ngModuleDef = type[NG_MOD_DEF] || null;
    if (!ngModuleDef && throwNotFound === true) {
        throw new Error(`Type ${stringify(type)} does not have 'ɵmod' property.`);
    }
    return ngModuleDef;
}
function getNgDirectiveDef(directiveDefinition) {
    const declaredInputs = {};
    return {
        type: directiveDefinition.type,
        providersResolver: null,
        factory: null,
        hostBindings: directiveDefinition.hostBindings || null,
        hostVars: directiveDefinition.hostVars || 0,
        hostAttrs: directiveDefinition.hostAttrs || null,
        contentQueries: directiveDefinition.contentQueries || null,
        declaredInputs: declaredInputs,
        inputTransforms: null,
        inputConfig: directiveDefinition.inputs || EMPTY_OBJ,
        exportAs: directiveDefinition.exportAs || null,
        standalone: directiveDefinition.standalone === true,
        signals: directiveDefinition.signals === true,
        selectors: directiveDefinition.selectors || EMPTY_ARRAY,
        viewQuery: directiveDefinition.viewQuery || null,
        features: directiveDefinition.features || null,
        setInput: null,
        findHostDirectiveDefs: null,
        hostDirectives: null,
        inputs: parseAndConvertBindingsForDefinition(directiveDefinition.inputs, declaredInputs),
        outputs: parseAndConvertBindingsForDefinition(directiveDefinition.outputs),
        debugInfo: null,
    };
}
function initFeatures(definition) {
    definition.features?.forEach((fn) => fn(definition));
}
export function extractDefListOrFactory(dependencies, pipeDef) {
    if (!dependencies) {
        return null;
    }
    const defExtractor = pipeDef ? getPipeDef : extractDirectiveDef;
    return () => (typeof dependencies === 'function' ? dependencies() : dependencies)
        .map(dep => defExtractor(dep))
        .filter(nonNull);
}
/**
 * A map that contains the generated component IDs and type.
 */
export const GENERATED_COMP_IDS = new Map();
/**
 * A method can returns a component ID from the component definition using a variant of DJB2 hash
 * algorithm.
 */
function getComponentId(componentDef) {
    let hash = 0;
    // We cannot rely solely on the component selector as the same selector can be used in different
    // modules.
    //
    // `componentDef.style` is not used, due to it causing inconsistencies. Ex: when server
    // component styles has no sourcemaps and browsers do.
    //
    // Example:
    // https://github.com/angular/components/blob/d9f82c8f95309e77a6d82fd574c65871e91354c2/src/material/core/option/option.ts#L248
    // https://github.com/angular/components/blob/285f46dc2b4c5b127d356cb7c4714b221f03ce50/src/material/legacy-core/option/option.ts#L32
    const hashSelectors = [
        componentDef.selectors,
        componentDef.ngContentSelectors,
        componentDef.hostVars,
        componentDef.hostAttrs,
        componentDef.consts,
        componentDef.vars,
        componentDef.decls,
        componentDef.encapsulation,
        componentDef.standalone,
        componentDef.signals,
        componentDef.exportAs,
        JSON.stringify(componentDef.inputs),
        JSON.stringify(componentDef.outputs),
        // We cannot use 'componentDef.type.name' as the name of the symbol will change and will not
        // match in the server and browser bundles.
        Object.getOwnPropertyNames(componentDef.type.prototype),
        !!componentDef.contentQueries,
        !!componentDef.viewQuery,
    ].join('|');
    for (const char of hashSelectors) {
        hash = Math.imul(31, hash) + char.charCodeAt(0) << 0;
    }
    // Force positive number hash.
    // 2147483647 = equivalent of Integer.MAX_VALUE.
    hash += 2147483647 + 1;
    const compId = 'c' + hash;
    if (typeof ngDevMode === 'undefined' || ngDevMode) {
        if (GENERATED_COMP_IDS.has(compId)) {
            const previousCompDefType = GENERATED_COMP_IDS.get(compId);
            if (previousCompDefType !== componentDef.type) {
                console.warn(formatRuntimeError(-912 /* RuntimeErrorCode.COMPONENT_ID_COLLISION */, `Component ID generation collision detected. Components '${previousCompDefType.name}' and '${componentDef.type.name}' with selector '${stringifyCSSSelectorList(componentDef
                    .selectors)}' generated the same component ID. To fix this, you can change the selector of one of those components or add an extra host attribute to force a different ID.`));
            }
        }
        else {
            GENERATED_COMP_IDS.set(compId, componentDef.type);
        }
    }
    return compId;
}
//# sourceMappingURL=data:application/json;base64,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