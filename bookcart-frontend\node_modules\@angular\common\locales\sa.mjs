/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["sa", [["AM", "PM"], u, ["पूर्वाह्न", "अपराह्न"]], [["AM", "PM"], u, u], [["र", "सो", "मं", "बु", "गु", "शु", "श"], ["रवि", "सोम", "मंगल", "बुध", "गुरु", "शुक्र", "शनि"], ["रविवासरः", "सोमवासरः", "मंगलवासरः", "बुधवासरः", "गुरुवासर:", "शुक्रवासरः", "शनिवासरः"], ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]], u, [["ज", "फ", "मा", "अ", "म", "जू", "जु", "अ", "सि", "अ", "न", "दि"], ["जनवरी:", "फरवरी:", "मार्च:", "अप्रैल:", "मई", "जून:", "जुलाई:", "अगस्त:", "सितंबर:", "अक्तूबर:", "नवंबर:", "दिसंबर:"], ["जनवरीमासः", "फरवरीमासः", "मार्चमासः", "अप्रैलमासः", "मईमासः", "जूनमासः", "जुलाईमासः", "अगस्तमासः", "सितंबरमासः", "अक्तूबरमासः", "नवंबरमासः", "दिसंबरमासः"]], [["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"], ["जनवरी:", "फरवरी:", "मार्च:", "अप्रैल:", "मई", "जून:", "जुलाई:", "अगस्त:", "सितंबर:", "अक्तूबर:", "नवंबर:", "दिसंबर:"], ["जनवरीमासः", "फरवरीमासः", "मार्चमासः", "अप्रैलमासः", "मईमासः", "जूनमासः", "जुलाईमासः", "अगस्तमासः", "सितंबरमासः", "अक्तूबरमासः", "नवंबरमासः", "दिसंबरमासः"]], [["BCE", "CE"], u, u], 0, [0, 0], ["d/M/yy", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1}, {0}", u, "{1} तदा {0}", u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##,##0.###", "#,##,##0%", "¤#,##,##0.00", "[#E0]"], "INR", "₹", "भारतीय रूप्यकम्", { "JPY": ["JP¥", "¥"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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