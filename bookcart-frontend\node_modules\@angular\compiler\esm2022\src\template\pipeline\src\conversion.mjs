/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import * as o from '../../../output/output_ast';
import * as ir from '../ir';
export const BINARY_OPERATORS = new Map([
    ['&&', o.BinaryOperator.And],
    ['>', o.BinaryOperator.Bigger],
    ['>=', o.BinaryOperator.BiggerEquals],
    ['|', o.BinaryOperator.BitwiseOr],
    ['&', o.BinaryOperator.BitwiseAnd],
    ['/', o.BinaryOperator.Divide],
    ['==', o.BinaryOperator.Equals],
    ['===', o.BinaryOperator.Identical],
    ['<', o.BinaryOperator.Lower],
    ['<=', o.BinaryOperator.LowerEquals],
    ['-', o.BinaryOperator.Minus],
    ['%', o.BinaryOperator.Modulo],
    ['*', o.BinaryOperator.Multiply],
    ['!=', o.BinaryOperator.NotEquals],
    ['!==', o.BinaryOperator.NotIdentical],
    ['??', o.BinaryOperator.NullishCoalesce],
    ['||', o.BinaryOperator.Or],
    ['+', o.BinaryOperator.Plus],
]);
export function namespaceForKey(namespacePrefixKey) {
    const NAMESPACES = new Map([['svg', ir.Namespace.SVG], ['math', ir.Namespace.Math]]);
    if (namespacePrefixKey === null) {
        return ir.Namespace.HTML;
    }
    return NAMESPACES.get(namespacePrefixKey) ?? ir.Namespace.HTML;
}
export function keyForNamespace(namespace) {
    const NAMESPACES = new Map([['svg', ir.Namespace.SVG], ['math', ir.Namespace.Math]]);
    for (const [k, n] of NAMESPACES.entries()) {
        if (n === namespace) {
            return k;
        }
    }
    return null; // No namespace prefix for HTML
}
export function prefixWithNamespace(strippedTag, namespace) {
    if (namespace === ir.Namespace.HTML) {
        return strippedTag;
    }
    return `:${keyForNamespace(namespace)}:${strippedTag}`;
}
export function literalOrArrayLiteral(value) {
    if (Array.isArray(value)) {
        return o.literalArr(value.map(literalOrArrayLiteral));
    }
    return o.literal(value);
}
//# sourceMappingURL=data:application/json;base64,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