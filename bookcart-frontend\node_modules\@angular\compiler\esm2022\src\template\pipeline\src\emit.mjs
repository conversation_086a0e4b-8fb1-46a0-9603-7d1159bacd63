/**
 *
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import * as o from '../../../../src/output/output_ast';
import * as ir from '../ir';
import { CompilationJobKind as Kind } from './compilation';
import { deleteAnyCasts } from './phases/any_cast';
import { applyI18nExpressions } from './phases/apply_i18n_expressions';
import { assignI18nSlotDependencies } from './phases/assign_i18n_slot_dependencies';
import { extractAttributes } from './phases/attribute_extraction';
import { specializeBindings } from './phases/binding_specialization';
import { chain } from './phases/chaining';
import { collapseSingletonInterpolations } from './phases/collapse_singleton_interpolations';
import { generateConditionalExpressions } from './phases/conditionals';
import { collectElementConsts } from './phases/const_collection';
import { convertI18nBindings } from './phases/convert_i18n_bindings';
import { createDeferDepsFns } from './phases/create_defer_deps_fns';
import { createI18nContexts } from './phases/create_i18n_contexts';
import { deduplicateTextBindings } from './phases/deduplicate_text_bindings';
import { configureDeferInstructions } from './phases/defer_configs';
import { resolveDeferTargetNames } from './phases/defer_resolve_targets';
import { collapseEmptyInstructions } from './phases/empty_elements';
import { expandSafeReads } from './phases/expand_safe_reads';
import { extractI18nMessages } from './phases/extract_i18n_messages';
import { generateAdvance } from './phases/generate_advance';
import { generateProjectionDefs } from './phases/generate_projection_def';
import { generateVariables } from './phases/generate_variables';
import { collectConstExpressions } from './phases/has_const_expression_collection';
import { parseHostStyleProperties } from './phases/host_style_property_parsing';
import { collectI18nConsts } from './phases/i18n_const_collection';
import { convertI18nText } from './phases/i18n_text_extraction';
import { liftLocalRefs } from './phases/local_refs';
import { emitNamespaceChanges } from './phases/namespace';
import { nameFunctionsAndVariables } from './phases/naming';
import { mergeNextContextExpressions } from './phases/next_context_merging';
import { generateNgContainerOps } from './phases/ng_container';
import { disableBindings } from './phases/nonbindable';
import { generateNullishCoalesceExpressions } from './phases/nullish_coalescing';
import { orderOps } from './phases/ordering';
import { parseExtractedStyles } from './phases/parse_extracted_styles';
import { removeContentSelectors } from './phases/phase_remove_content_selectors';
import { createPipes } from './phases/pipe_creation';
import { createVariadicPipes } from './phases/pipe_variadic';
import { propagateI18nBlocks } from './phases/propagate_i18n_blocks';
import { extractPureFunctions } from './phases/pure_function_extraction';
import { generatePureLiteralStructures } from './phases/pure_literal_structures';
import { reify } from './phases/reify';
import { removeEmptyBindings } from './phases/remove_empty_bindings';
import { removeI18nContexts } from './phases/remove_i18n_contexts';
import { removeUnusedI18nAttributesOps } from './phases/remove_unused_i18n_attrs';
import { resolveContexts } from './phases/resolve_contexts';
import { resolveDollarEvent } from './phases/resolve_dollar_event';
import { resolveI18nElementPlaceholders } from './phases/resolve_i18n_element_placeholders';
import { resolveI18nExpressionPlaceholders } from './phases/resolve_i18n_expression_placeholders';
import { resolveNames } from './phases/resolve_names';
import { resolveSanitizers } from './phases/resolve_sanitizers';
import { transformTwoWayBindingSet } from './phases/transform_two_way_binding_set';
import { saveAndRestoreView } from './phases/save_restore_view';
import { allocateSlots } from './phases/slot_allocation';
import { specializeStyleBindings } from './phases/style_binding_specialization';
import { generateTemporaryVariables } from './phases/temporary_variables';
import { generateTrackFns } from './phases/track_fn_generation';
import { optimizeTrackFns } from './phases/track_fn_optimization';
import { generateTrackVariables } from './phases/track_variables';
import { countVariables } from './phases/var_counting';
import { optimizeVariables } from './phases/variable_optimization';
import { wrapI18nIcus } from './phases/wrap_icus';
const phases = [
    { kind: Kind.Tmpl, fn: removeContentSelectors },
    { kind: Kind.Host, fn: parseHostStyleProperties },
    { kind: Kind.Tmpl, fn: emitNamespaceChanges },
    { kind: Kind.Tmpl, fn: propagateI18nBlocks },
    { kind: Kind.Tmpl, fn: wrapI18nIcus },
    { kind: Kind.Both, fn: deduplicateTextBindings },
    { kind: Kind.Both, fn: specializeStyleBindings },
    { kind: Kind.Both, fn: specializeBindings },
    { kind: Kind.Both, fn: extractAttributes },
    { kind: Kind.Tmpl, fn: createI18nContexts },
    { kind: Kind.Both, fn: parseExtractedStyles },
    { kind: Kind.Tmpl, fn: removeEmptyBindings },
    { kind: Kind.Both, fn: collapseSingletonInterpolations },
    { kind: Kind.Both, fn: orderOps },
    { kind: Kind.Tmpl, fn: generateConditionalExpressions },
    { kind: Kind.Tmpl, fn: createPipes },
    { kind: Kind.Tmpl, fn: configureDeferInstructions },
    { kind: Kind.Tmpl, fn: convertI18nText },
    { kind: Kind.Tmpl, fn: convertI18nBindings },
    { kind: Kind.Tmpl, fn: removeUnusedI18nAttributesOps },
    { kind: Kind.Tmpl, fn: assignI18nSlotDependencies },
    { kind: Kind.Tmpl, fn: applyI18nExpressions },
    { kind: Kind.Tmpl, fn: createVariadicPipes },
    { kind: Kind.Both, fn: generatePureLiteralStructures },
    { kind: Kind.Tmpl, fn: generateProjectionDefs },
    { kind: Kind.Tmpl, fn: generateVariables },
    { kind: Kind.Tmpl, fn: saveAndRestoreView },
    { kind: Kind.Both, fn: deleteAnyCasts },
    { kind: Kind.Both, fn: resolveDollarEvent },
    { kind: Kind.Tmpl, fn: generateTrackVariables },
    { kind: Kind.Both, fn: resolveNames },
    { kind: Kind.Tmpl, fn: resolveDeferTargetNames },
    { kind: Kind.Tmpl, fn: transformTwoWayBindingSet },
    { kind: Kind.Tmpl, fn: optimizeTrackFns },
    { kind: Kind.Both, fn: resolveContexts },
    { kind: Kind.Both, fn: resolveSanitizers },
    { kind: Kind.Tmpl, fn: liftLocalRefs },
    { kind: Kind.Both, fn: generateNullishCoalesceExpressions },
    { kind: Kind.Both, fn: expandSafeReads },
    { kind: Kind.Both, fn: generateTemporaryVariables },
    { kind: Kind.Tmpl, fn: allocateSlots },
    { kind: Kind.Tmpl, fn: resolveI18nElementPlaceholders },
    { kind: Kind.Tmpl, fn: resolveI18nExpressionPlaceholders },
    { kind: Kind.Tmpl, fn: extractI18nMessages },
    { kind: Kind.Tmpl, fn: generateTrackFns },
    { kind: Kind.Tmpl, fn: collectI18nConsts },
    { kind: Kind.Tmpl, fn: collectConstExpressions },
    { kind: Kind.Both, fn: collectElementConsts },
    { kind: Kind.Tmpl, fn: removeI18nContexts },
    { kind: Kind.Both, fn: countVariables },
    { kind: Kind.Tmpl, fn: generateAdvance },
    { kind: Kind.Both, fn: optimizeVariables },
    { kind: Kind.Both, fn: nameFunctionsAndVariables },
    { kind: Kind.Tmpl, fn: createDeferDepsFns },
    { kind: Kind.Tmpl, fn: mergeNextContextExpressions },
    { kind: Kind.Tmpl, fn: generateNgContainerOps },
    { kind: Kind.Tmpl, fn: collapseEmptyInstructions },
    { kind: Kind.Tmpl, fn: disableBindings },
    { kind: Kind.Both, fn: extractPureFunctions },
    { kind: Kind.Both, fn: reify },
    { kind: Kind.Both, fn: chain },
];
/**
 * Run all transformation phases in the correct order against a compilation job. After this
 * processing, the compilation should be in a state where it can be emitted.
 */
export function transform(job, kind) {
    for (const phase of phases) {
        if (phase.kind === kind || phase.kind === Kind.Both) {
            // The type of `Phase` above ensures it is impossible to call a phase that doesn't support the
            // job kind.
            phase.fn(job);
        }
    }
}
/**
 * Compile all views in the given `ComponentCompilation` into the final template function, which may
 * reference constants defined in a `ConstantPool`.
 */
export function emitTemplateFn(tpl, pool) {
    const rootFn = emitView(tpl.root);
    emitChildViews(tpl.root, pool);
    return rootFn;
}
function emitChildViews(parent, pool) {
    for (const unit of parent.job.units) {
        if (unit.parent !== parent.xref) {
            continue;
        }
        // Child views are emitted depth-first.
        emitChildViews(unit, pool);
        const viewFn = emitView(unit);
        pool.statements.push(viewFn.toDeclStmt(viewFn.name));
    }
}
/**
 * Emit a template function for an individual `ViewCompilation` (which may be either the root view
 * or an embedded view).
 */
function emitView(view) {
    if (view.fnName === null) {
        throw new Error(`AssertionError: view ${view.xref} is unnamed`);
    }
    const createStatements = [];
    for (const op of view.create) {
        if (op.kind !== ir.OpKind.Statement) {
            throw new Error(`AssertionError: expected all create ops to have been compiled, but got ${ir.OpKind[op.kind]}`);
        }
        createStatements.push(op.statement);
    }
    const updateStatements = [];
    for (const op of view.update) {
        if (op.kind !== ir.OpKind.Statement) {
            throw new Error(`AssertionError: expected all update ops to have been compiled, but got ${ir.OpKind[op.kind]}`);
        }
        updateStatements.push(op.statement);
    }
    const createCond = maybeGenerateRfBlock(1, createStatements);
    const updateCond = maybeGenerateRfBlock(2, updateStatements);
    return o.fn([
        new o.FnParam('rf'),
        new o.FnParam('ctx'),
    ], [
        ...createCond,
        ...updateCond,
    ], 
    /* type */ undefined, /* sourceSpan */ undefined, view.fnName);
}
function maybeGenerateRfBlock(flag, statements) {
    if (statements.length === 0) {
        return [];
    }
    return [
        o.ifStmt(new o.BinaryOperatorExpr(o.BinaryOperator.BitwiseAnd, o.variable('rf'), o.literal(flag)), statements),
    ];
}
export function emitHostBindingFunction(job) {
    if (job.root.fnName === null) {
        throw new Error(`AssertionError: host binding function is unnamed`);
    }
    const createStatements = [];
    for (const op of job.root.create) {
        if (op.kind !== ir.OpKind.Statement) {
            throw new Error(`AssertionError: expected all create ops to have been compiled, but got ${ir.OpKind[op.kind]}`);
        }
        createStatements.push(op.statement);
    }
    const updateStatements = [];
    for (const op of job.root.update) {
        if (op.kind !== ir.OpKind.Statement) {
            throw new Error(`AssertionError: expected all update ops to have been compiled, but got ${ir.OpKind[op.kind]}`);
        }
        updateStatements.push(op.statement);
    }
    if (createStatements.length === 0 && updateStatements.length === 0) {
        return null;
    }
    const createCond = maybeGenerateRfBlock(1, createStatements);
    const updateCond = maybeGenerateRfBlock(2, updateStatements);
    return o.fn([
        new o.FnParam('rf'),
        new o.FnParam('ctx'),
    ], [
        ...createCond,
        ...updateCond,
    ], 
    /* type */ undefined, /* sourceSpan */ undefined, job.root.fnName);
}
//# sourceMappingURL=data:application/json;base64,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