/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\.?/, '').length;
    if (i === 1 && v === 0)
        return 1;
    if (i === Math.floor(i) && (i >= 2 && i <= 4) && v === 0)
        return 3;
    if (!(v === 0))
        return 4;
    return 5;
}
export default ["sk", [["AM", "PM"], u, u], u, [["n", "p", "u", "s", "š", "p", "s"], ["ne", "po", "ut", "st", "št", "pi", "so"], ["nedeľa", "pondelok", "utorok", "streda", "štvrtok", "piatok", "sobota"], ["ne", "po", "ut", "st", "št", "pi", "so"]], u, [["j", "f", "m", "a", "m", "j", "j", "a", "s", "o", "n", "d"], ["jan", "feb", "mar", "apr", "máj", "jún", "júl", "aug", "sep", "okt", "nov", "dec"], ["januára", "februára", "marca", "apríla", "mája", "júna", "júla", "augusta", "septembra", "októbra", "novembra", "decembra"]], [["j", "f", "m", "a", "m", "j", "j", "a", "s", "o", "n", "d"], ["jan", "feb", "mar", "apr", "máj", "jún", "júl", "aug", "sep", "okt", "nov", "dec"], ["január", "február", "marec", "apríl", "máj", "jún", "júl", "august", "september", "október", "november", "december"]], [["pred Kr.", "po Kr."], u, ["pred Kristom", "po Kristovi"]], 1, [6, 0], ["d. M. y", u, "d. MMMM y", "EEEE d. MMMM y"], ["H:mm", "H:mm:ss", "H:mm:ss z", "H:mm:ss zzzz"], ["{1} {0}", "{1}, {0}", u, u], [",", " ", ";", "%", "+", "-", "e", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0 %", "#,##0.00 ¤", "#E0"], "EUR", "€", "euro", { "AUD": [u, "$"], "BRL": [u, "R$"], "BYN": [u, "р."], "CAD": [u, "$"], "CNY": [u, "¥"], "GBP": [u, "£"], "HKD": [u, "$"], "ILS": ["NIS", "₪"], "INR": [u, "₹"], "JPY": [u, "¥"], "KRW": [u, "₩"], "NZD": [u, "$"], "PHP": [u, "₱"], "RUR": [u, "р."], "TWD": [u, "NT$"], "USD": [u, "$"], "VND": [u, "₫"], "XXX": [] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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