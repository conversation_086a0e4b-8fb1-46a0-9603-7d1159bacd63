/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export * from './aria-describer/aria-describer';
export * from './aria-describer/aria-reference';
export * from './key-manager/activedescendant-key-manager';
export * from './key-manager/focus-key-manager';
export * from './key-manager/list-key-manager';
export * from './focus-trap/configurable-focus-trap';
export * from './focus-trap/configurable-focus-trap-config';
export * from './focus-trap/configurable-focus-trap-factory';
export * from './focus-trap/event-listener-inert-strategy';
export * from './focus-trap/focus-trap';
export * from './focus-trap/focus-trap-inert-strategy';
export * from './interactivity-checker/interactivity-checker';
export { InputModalityDetector, INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS, INPUT_MODALITY_DETECTOR_OPTIONS, } from './input-modality/input-modality-detector';
export * from './live-announcer/live-announcer';
export * from './live-announcer/live-announcer-tokens';
export * from './focus-monitor/focus-monitor';
export * from './fake-event-detection';
export * from './a11y-module';
export { HighContrastModeDetector, HighContrastMode, } from './high-contrast-mode/high-contrast-mode-detector';
//# sourceMappingURL=data:application/json;base64,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