{"version": 3, "sources": ["../../../../../../packages/compiler-cli/src/ngtsc/sourcemaps/src/source_file.ts", "../../../../../../packages/compiler-cli/src/ngtsc/sourcemaps/src/segment_marker.ts", "../../../../../../packages/compiler-cli/src/ngtsc/sourcemaps/src/source_file_loader.ts", "../../../../../../packages/compiler-cli/src/ngtsc/sourcemaps/src/content_origin.ts"], "mappings": ";;;;;;AAOA,SAAQ,QAAQ,cAAkD;AAClE,OAAO,gBAAgB;;;ACoBjB,SAAU,gBAAgB,GAAkB,GAAgB;AAChE,SAAO,EAAE,WAAW,EAAE;AACxB;AAUM,SAAU,cACZ,sBAAgC,QAAuB,QAAc;AACvE,MAAI,WAAW,GAAG;AAChB,WAAO;EACT;AAEA,MAAI,OAAO,OAAO;AAClB,QAAM,WAAW,OAAO,WAAW;AACnC,SAAO,OAAO,qBAAqB,SAAS,KAAK,qBAAqB,OAAO,MAAM,UAAU;AAC3F;EACF;AACA,SAAO,OAAO,KAAK,qBAAqB,QAAQ,UAAU;AACxD;EACF;AACA,QAAM,SAAS,WAAW,qBAAqB;AAC/C,SAAO,EAAC,MAAM,QAAQ,UAAU,MAAM,OAAS;AACjD;;;ADzCM,SAAU,wBAAwB,UAAgB;AACtD,SAAO,WAAW,sBAAsB,WAAW,eAAe,QAAQ,CAAC,EACtE,QAAQ,SAAS,IAAI;AAC5B;AAEM,IAAO,aAAP,MAAiB;EAWrB,YAEa,YAEA,UAEA,QAEA,SACD,IAAoB;AAPnB,SAAA,aAAA;AAEA,SAAA,WAAA;AAEA,SAAA,SAAA;AAEA,SAAA,UAAA;AACD,SAAA,KAAA;AAEV,SAAK,WAAW,wBAAwB,QAAQ;AAChD,SAAK,uBAAuB,4BAA4B,KAAK,QAAQ;AACrE,SAAK,oBAAoB,KAAK,gBAAe;EAC/C;EAKA,2BAAwB;AACtB,UAAM,UAAU,IAAI,WAAU;AAC9B,UAAM,QAAQ,IAAI,WAAU;AAC5B,UAAM,WAA8B,CAAA;AACpC,UAAM,gBAAgB,KAAK,GAAG,QAAQ,KAAK,UAAU;AAGrD,UAAM,0BACF,IAAI,MAAsB,WAAS,KAAK,GAAG,SAAS,eAAe,KAAK,CAAC;AAE7E,eAAW,WAAW,KAAK,mBAAmB;AAC5C,YAAM,cAAc,QAAQ,IACxB,wBAAwB,IAAI,QAAQ,eAAe,UAAU,GAC7D,QAAQ,eAAe,QAAQ;AACnC,YAAM,eAAiC;QACrC,QAAQ,iBAAiB;QACzB;QACA,QAAQ,gBAAgB;QACxB,QAAQ,gBAAgB;;AAE1B,UAAI,QAAQ,SAAS,QAAW;AAC9B,cAAM,YAAY,MAAM,IAAI,QAAQ,IAAI;AACxC,qBAAa,KAAK,SAAS;MAC7B;AAGA,YAAM,OAAO,QAAQ,iBAAiB;AACtC,aAAO,QAAQ,SAAS,QAAQ;AAC9B,iBAAS,KAAK,CAAA,CAAE;MAClB;AAEA,eAAS,MAAM,KAAK,YAAY;IAClC;AAEA,UAAM,YAA0B;MAC9B,SAAS;MACT,MAAM,KAAK,GAAG,SAAS,eAAe,KAAK,UAAU;MACrD,SAAS,QAAQ;MACjB,OAAO,MAAM;MACb,UAAU,OAAO,QAAQ;MACzB,gBAAgB,QAAQ;;AAE1B,WAAO;EACT;EAUA,oBAAoB,MAAc,QAAc;AAE9C,QAAI,KAAK,kBAAkB,WAAW,GAAG;AACvC,aAAO;IACT;AAEA,QAAI;AACJ,QAAI,OAAO,KAAK,qBAAqB,QAAQ;AAC3C,iBAAW,KAAK,qBAAqB,QAAQ;IAC/C,OAAO;AAEL,iBAAW,KAAK,SAAS;IAC3B;AAEA,UAAM,kBAAiC,EAAC,MAAM,QAAQ,UAAU,MAAM,OAAS;AAE/E,QAAI,eACA,2BAA2B,KAAK,mBAAmB,iBAAiB,OAAO,CAAC;AAChF,QAAI,eAAe,GAAG;AACpB,qBAAe;IACjB;AACA,UAAM,EAAC,iBAAiB,gBAAgB,iBAAgB,IACpD,KAAK,kBAAkB;AAC3B,UAAM,SAAS,gBAAgB,WAAW,iBAAiB;AAC3D,UAAM,wBACF,cAAc,eAAe,sBAAsB,iBAAiB,MAAM;AAE9E,WAAO;MACL,MAAM,eAAe;MACrB,MAAM,sBAAsB;MAC5B,QAAQ,sBAAsB;;EAElC;EAMQ,kBAAe;AACrB,UAAM,WACF,cAAc,KAAK,UAAU,KAAK,OAAO,KAAK,KAAK,SAAS,KAAK,oBAAoB;AACzF,+BAA2B,QAAQ;AACnC,UAAM,oBAA+B,CAAA;AACrC,aAAS,eAAe,GAAG,eAAe,SAAS,QAAQ,gBAAgB;AACzE,YAAM,cAAc,SAAS;AAC7B,YAAM,UAAU,YAAY;AAC5B,UAAI,QAAQ,kBAAkB,WAAW,GAAG;AAG1C,0BAAkB,KAAK,WAAW;AAClC;MACF;AAoBA,YAAM,gBAAgB,YAAY;AAClC,YAAM,cAAc,cAAc;AAuBlC,UAAI,qBACA,2BAA2B,QAAQ,mBAAmB,eAAe,OAAO,CAAC;AACjF,UAAI,qBAAqB,GAAG;AAC1B,6BAAqB;MACvB;AACA,YAAM,mBAAmB,gBAAgB,SACrC,2BACI,QAAQ,mBAAmB,aAAa,MAAM,kBAAkB,IACpE,QAAQ,kBAAkB,SAAS;AAEvC,eAAS,mBAAmB,oBAAoB,oBAAoB,kBAC/D,oBAAoB;AACvB,cAAM,cAAuB,QAAQ,kBAAkB;AACvD,0BAAkB,KAAK,cAAc,MAAM,aAAa,WAAW,CAAC;MACtE;IACF;AACA,WAAO;EACT;;AAcI,SAAU,2BACZ,UAAqB,QAAuB,WAAoB,YAAkB;AACpF,MAAI,aAAa,SAAS,SAAS;AACnC,QAAM,OAAO,YAAY,KAAK;AAE9B,MAAI,gBAAgB,SAAS,YAAY,kBAAkB,MAAM,IAAI,MAAM;AAEzE,WAAO;EACT;AAEA,MAAI,gBAAgB;AACpB,SAAO,cAAc,YAAY;AAC/B,UAAM,QAAS,aAAa,cAAe;AAC3C,QAAI,gBAAgB,SAAS,OAAO,kBAAkB,MAAM,KAAK,MAAM;AACrE,sBAAgB;AAChB,mBAAa,QAAQ;IACvB,OAAO;AACL,mBAAa,QAAQ;IACvB;EACF;AACA,SAAO;AACT;AAqBM,SAAU,cAAc,iBAA6B,IAAa,IAAW;AACjF,QAAM,OAAO,GAAG,QAAQ,GAAG;AA0C3B,QAAM,OAAO,gBAAgB,GAAG,kBAAkB,GAAG,eAAe;AACpE,MAAI,OAAO,GAAG;AACZ,WAAO;MACL;MACA,kBACI,cAAc,gBAAgB,sBAAsB,GAAG,kBAAkB,IAAI;MACjF,gBAAgB,GAAG;MACnB,iBAAiB,GAAG;;EAExB,OAAO;AACL,WAAO;MACL;MACA,kBAAkB,GAAG;MACrB,gBAAgB,GAAG;MACnB,iBACI,cAAc,GAAG,eAAe,sBAAsB,GAAG,iBAAiB,CAAC,IAAI;;EAEvF;AACF;AAMM,SAAU,cACZ,QAA2B,SAC3B,qCAA6C;AAC/C,MAAI,WAAW,MAAM;AACnB,WAAO,CAAA;EACT;AAEA,QAAM,cAAc,OAAO,OAAO,QAAQ;AAC1C,MAAI,gBAAgB,MAAM;AACxB,WAAO,CAAA;EACT;AAEA,QAAM,WAAsB,CAAA;AAC5B,WAAS,gBAAgB,GAAG,gBAAgB,YAAY,QAAQ,iBAAiB;AAC/E,UAAM,wBAAwB,YAAY;AAC1C,eAAW,cAAc,uBAAuB;AAC9C,UAAI,WAAW,UAAU,GAAG;AAC1B,cAAM,iBAAiB,QAAQ,WAAW;AAC1C,YAAI,mBAAmB,QAAQ,mBAAmB,QAAW;AAE3D;QACF;AACA,cAAM,kBAAkB,WAAW;AACnC,cAAM,OAAO,WAAW,WAAW,IAAI,OAAO,MAAM,WAAW,MAAM;AACrE,cAAM,OAAO,WAAW;AACxB,cAAM,SAAS,WAAW;AAC1B,cAAM,mBAAkC;UACtC,MAAM;UACN,QAAQ;UACR,UAAU,oCAAoC,iBAAiB;UAC/D,MAAM;;AAER,cAAM,kBAAiC;UACrC;UACA;UACA,UAAU,eAAe,qBAAqB,QAAQ;UACtD,MAAM;;AAER,iBAAS,KAAK,EAAC,MAAM,kBAAkB,iBAAiB,eAAc,CAAC;MACzE;IACF;EACF;AACA,SAAO;AACT;AAUM,SAAU,wBAAwB,UAAmB;AACzD,QAAM,mBAAmB,oBAAI,IAAG;AAChC,aAAW,WAAW,UAAU;AAC9B,UAAM,iBAAiB,QAAQ;AAC/B,QAAI,CAAC,iBAAiB,IAAI,cAAc,GAAG;AACzC,uBAAiB,IAAI,gBAAgB,CAAA,CAAE;IACzC;AACA,UAAM,WAAW,iBAAiB,IAAI,cAAc;AACpD,aAAS,KAAK,QAAQ,eAAe;EACvC;AACA,mBAAiB,QAAQ,oBAAkB,eAAe,KAAK,eAAe,CAAC;AAC/E,SAAO;AACT;AAQM,SAAU,2BAA2B,UAAmB;AAC5D,QAAM,mBAAmB,wBAAwB,QAAQ;AACzD,mBAAiB,QAAQ,aAAU;AACjC,aAAS,IAAI,GAAG,IAAI,QAAQ,SAAS,GAAG,KAAK;AAC3C,cAAQ,GAAG,OAAO,QAAQ,IAAI;IAChC;EACF,CAAC;AACH;AAEM,SAAU,4BAA4B,KAAW;AAMrD,QAAM,wBAAwB;AAC9B,QAAM,cAAc,mBAAmB,GAAG;AAC1C,QAAM,iBAAiB,CAAC,CAAC;AACzB,WAAS,IAAI,GAAG,IAAI,YAAY,SAAS,GAAG,KAAK;AAC/C,mBAAe,KAAK,eAAe,KAAK,YAAY,KAAK,qBAAqB;EAChF;AACA,SAAO;AACT;AAEA,SAAS,mBAAmB,KAAW;AACrC,SAAQ,IAAI,MAAM,IAAI,EAAG,IAAI,OAAK,EAAE,MAAM;AAC5C;AASA,IAAM,aAAN,MAAgB;EAAhB,cAAA;AACU,SAAA,MAAM,oBAAI,IAAG;AAOZ,SAAA,OAAY,CAAA;AAQZ,SAAA,SAAc,CAAA;EAsBzB;EATE,IAAI,KAAQ,OAAQ;AAClB,QAAI,KAAK,IAAI,IAAI,GAAG,GAAG;AACrB,aAAO,KAAK,IAAI,IAAI,GAAG;IACzB;AACA,UAAM,QAAQ,KAAK,OAAO,KAAK,KAAK,IAAI;AACxC,SAAK,KAAK,KAAK,GAAG;AAClB,SAAK,IAAI,IAAI,KAAK,KAAK;AACvB,WAAO;EACT;;AASF,IAAM,aAAN,MAAgB;EAAhB,cAAA;AACU,SAAA,MAAM,oBAAI,IAAG;AAMZ,SAAA,SAAc,CAAA;EAoBzB;EARE,IAAI,OAAQ;AACV,QAAI,KAAK,IAAI,IAAI,KAAK,GAAG;AACvB,aAAO,KAAK,IAAI,IAAI,KAAK;IAC3B;AACA,UAAM,QAAQ,KAAK,OAAO,KAAK,KAAK,IAAI;AACxC,SAAK,IAAI,IAAI,OAAO,KAAK;AACzB,WAAO;EACT;;AAGF,IAAM,QAAN,MAAW;EAET,YAAoB,WAAmC;AAAnC,SAAA,YAAA;AADZ,SAAA,MAAM,oBAAI,IAAG;EACqC;EAC1D,IAAI,OAAY;AACd,QAAI,CAAC,KAAK,IAAI,IAAI,KAAK,GAAG;AACxB,WAAK,IAAI,IAAI,OAAO,KAAK,UAAU,KAAK,CAAC;IAC3C;AACA,WAAO,KAAK,IAAI,IAAI,KAAK;EAC3B;;;;AE1gBF,OAAOA,iBAAgB;;;ACYvB,IAAY;CAAZ,SAAYC,gBAAa;AAIvB,EAAAA,eAAAA,eAAA,cAAA,KAAA;AAIA,EAAAA,eAAAA,eAAA,YAAA,KAAA;AAKA,EAAAA,eAAAA,eAAA,gBAAA,KAAA;AACF,GAdY,kBAAA,gBAAa,CAAA,EAAA;;;ADHzB,IAAM,iBAAiB;AAWjB,IAAO,mBAAP,MAAuB;EAG3B,YACY,IAAgC,QAEhC,WAAyC;AAFzC,SAAA,KAAA;AAAgC,SAAA,SAAA;AAEhC,SAAA,YAAA;AALJ,SAAA,eAAiC,CAAA;EAKe;EA6BxD,eACI,YAA4B,WAAwB,MACpD,aAA8B,MAAI;AACpC,UAAM,iBAAiB,aAAa,OAAO,cAAc,WAAW,cAAc;AAClF,UAAM,gBACF,cAAc,EAAC,QAAQ,cAAc,UAAU,GAAG,WAAU;AAChE,WAAO,KAAK,uBAAuB,YAAY,UAAU,gBAAgB,aAAa;EACxF;EAkBQ,uBACJ,YAA4B,UAAuB,cACnD,eAAiC;AACnC,UAAM,gBAAgB,KAAK,aAAa,MAAK;AAC7C,QAAI;AACF,UAAI,aAAa,MAAM;AACrB,YAAI,CAAC,KAAK,GAAG,OAAO,UAAU,GAAG;AAC/B,iBAAO;QACT;AACA,mBAAW,KAAK,eAAe,UAAU;MAC3C;AAGA,UAAI,kBAAkB,MAAM;AAC1B,wBAAgB,KAAK,cAAc,YAAY,UAAU,YAAY;MACvE;AAEA,UAAI,UAA+B,CAAA;AACnC,UAAI,kBAAkB,MAAM;AAC1B,cAAM,WAAW,cAAc,WAAW;AAC1C,kBAAU,KAAK,eAAe,UAAU,aAAa;MACvD;AAEA,aAAO,IAAI,WAAW,YAAY,UAAU,eAAe,SAAS,KAAK,EAAE;IAC7E,SAAS,GAAP;AACA,WAAK,OAAO,KACR,wBAAwB,yCAA0C,EAAY,SAAS;AAC3F,aAAO;IACT;AAEE,WAAK,eAAe;IACtB;EACF;EAeQ,cACJ,YAA4B,gBAC5B,cAA2B;AAI7B,UAAM,WAAW,KAAK,oBAAoB,cAAc;AACxD,UAAM,SAASC,YAAW,aAAa,KAAK,QAAQ;AACpD,QAAI,WAAW,MAAM;AACnB,aAAO;QACL,KAAKA,YAAW,YAAY,OAAO,IAAG,CAAG,EAAE;QAC3C,SAAS;QACT,QAAQ,cAAc;;IAE1B;AAEA,QAAI,iBAAiB,cAAc,QAAQ;AAIzC,aAAO;IACT;AAEA,UAAM,WAAWA,YAAW,oBAAoB,KAAK,QAAQ;AAC7D,QAAI,UAAU;AACZ,UAAI;AACF,cAAM,WAAW,SAAS,MAAM,SAAS;AACzC,cAAM,kBAAkB,KAAK,GAAG,QAAQ,KAAK,GAAG,QAAQ,UAAU,GAAG,QAAQ;AAC7E,eAAO;UACL,KAAK,KAAK,iBAAiB,eAAe;UAC1C,SAAS;UACT,QAAQ,cAAc;;MAE1B,SAAS,GAAP;AACA,aAAK,OAAO,KAAK,wBAAwB,yCACpC,EAAY,SAAS;AAC1B,eAAO;MACT;IACF;AAEA,UAAM,iBAAiB,KAAK,GAAG,QAAQ,aAAa,MAAM;AAC1D,QAAI,KAAK,GAAG,OAAO,cAAc,GAAG;AAClC,aAAO;QACL,KAAK,KAAK,iBAAiB,cAAc;QACzC,SAAS;QACT,QAAQ,cAAc;;IAE1B;AAEA,WAAO;EACT;EAMQ,eAAe,UAA0B,EAAC,KAAK,QAAQ,gBAAe,GAAgB;AAE5F,UAAM,aAAa,KAAK,GAAG,QACvB,KAAK,GAAG,QAAQ,QAAQ,GAAG,KAAK,sBAAsB,IAAI,cAAc,EAAE,CAAC;AAC/E,WAAO,IAAI,QAAQ,IAAI,CAAC,QAAQ,UAAS;AACvC,YAAM,OAAO,KAAK,GAAG,QAAQ,YAAY,KAAK,sBAAsB,MAAM,CAAC;AAC3E,YAAM,UAAU,IAAI,kBAAkB,IAAI,eAAe,UAAU;AAOnE,YAAM,eAAe,YAAY,QAAQ,oBAAoB,cAAc,WACvE,cAAc,SACd,cAAc;AAClB,aAAO,KAAK,uBAAuB,MAAM,SAAS,cAAc,IAAI;IACtE,CAAC;EACH;EAOQ,eAAe,YAA0B;AAC/C,SAAK,UAAU,UAAU;AACzB,WAAO,KAAK,GAAG,SAAS,UAAU;EACpC;EAQQ,iBAAiB,SAAuB;AAC9C,SAAK,UAAU,OAAO;AACtB,WAAO,KAAK,MAAM,KAAK,GAAG,SAAS,OAAO,CAAC;EAC7C;EAMQ,UAAU,MAAoB;AACpC,QAAI,KAAK,aAAa,SAAS,IAAI,GAAG;AACpC,YAAM,IAAI,MACN,4CAA4C,KAAK,aAAa,KAAK,MAAM,QAAQ,MAAM;IAC7F;AACA,SAAK,aAAa,KAAK,IAAI;EAC7B;EAEQ,oBAAoB,UAAgB;AAC1C,QAAI,0BAA0B,SAAS,SAAS;AAChD,WAAO,0BAA0B,MACzB,SAAS,6BAA6B,QACtC,SAAS,6BAA6B,OAAO;AACnD;IACF;AACA,QAAI,oBAAoB,SAAS,YAAY,MAAM,0BAA0B,CAAC;AAC9E,QAAI,sBAAsB,IAAI;AAC5B,0BAAoB;IACtB;AACA,WAAO,SAAS,MAAM,oBAAoB,CAAC;EAC7C;EAUQ,sBAAsB,MAAY;AACxC,WAAO,KAAK,QACR,gBAAgB,CAAC,GAAW,WAAmB,KAAK,UAAU,OAAO,YAAW,MAAO,EAAE;EAC/F;;", "names": ["mapHelpers", "ContentOrigin", "mapHelpers"]}