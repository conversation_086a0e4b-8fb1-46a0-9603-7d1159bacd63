/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["sbp", [["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], u, u], u, [["M", "J", "J", "J", "A", "<PERSON>", "J"], ["Mul", "Jtt", "Jnn", "Jtn", "<PERSON>h", "<PERSON><PERSON>", "<PERSON><PERSON>"], ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ju<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>ju<PERSON><PERSON>", "Ju<PERSON><PERSON><PERSON>"], ["Mul", "Jtt", "Jnn", "Jtn", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"]], u, [["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"], ["Mup", "<PERSON><PERSON>", "<PERSON>h", "<PERSON>n", "Mag", "<PERSON>j", "<PERSON><PERSON>", "<PERSON>pg", "<PERSON>e", "<PERSON>k", "<PERSON>s", "<PERSON>h"], ["Mupalangulwa", "<PERSON>wi<PERSON>e", "<PERSON><PERSON>nde", "<PERSON>nyi", "<PERSON><PERSON>nde <PERSON>gali", "<PERSON><PERSON>mbi", "<PERSON>ship<PERSON><PERSON>", "<PERSON>pu<PERSON><PERSON>", "<PERSON>ny<PERSON>", "<PERSON>khu", "<PERSON><PERSON><PERSON><PERSON>mbwe", "<PERSON>ha<PERSON>"]], u, [["<PERSON>", "<PERSON><PERSON>"], u, ["Ashanali uKilisito", "Pamwandi ya Kilisto"]], 1, [6, 0], ["dd/MM/y", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "#,##0.00¤", "#E0"], "TZS", "TSh", "Ihela ya Tansaniya", { "JPY": ["JP¥", "¥"], "TZS": ["TSh"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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