/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val, i = Math.floor(Math.abs(val));
    if (i === 0 || n === 1)
        return 1;
    return 5;
}
export default ["doi", [["सवेर", "स’ञ"], u, ["सवेर", "बाद दपैहर"]], [["सवेर", "स’ञ"], u, u], [["ऐ.", "सो.", "म.", "बु.", "बी.", "शु.", "श."], ["ऐत", "सोम", "मंगल", "बुध", "बीर", "शुक्र", "शनि"], ["ऐतबार", "सोमबार", "मंगलबार", "बुधबार", "बीरबार", "शुक्रबार", "शनीबार"], ["ऐत", "सोम", "मंगल", "बुध", "बीर", "शुक्र", "शनि"]], [["ऐ", "सो", "म.", "बु.", "बी.", "शु.", "श."], ["ऐत", "सोम", "मंगल", "बुध", "बीर", "शुक्र", "शनि"], ["ऐतबार", "सोमबार", "मंगलबार", "बुधबार", "बीरबार", "शुक्रबार", "शनिबार"], ["ऐत", "सोम", "मंगल", "बुध", "बीर", "शुक्र", "शनि"]], [["ज", "फ", "मा", "अ", "मे", "जू", "जु", "अ", "सि", "अ", "न", "दि"], ["जन.", "फर.", "मार्च", "अप्रैल", "मेई", "जून", "जुलाई", "अग.", "सित.", "अक्तू.", "नव.", "दिस."], ["जनवरी", "फरवरी", "मार्च", "अप्रैल", "मेई", "जून", "जुलाई", "अगस्त", "सितंबर", "अत्तूबर", "नवंबर", "दिसंबर"]], [["ज", "फ", "मा", "अ", "मे", "जू", "जु", "अ", "सि", "अ", "न", "दि"], ["जन.", "फर.", "मार्च", "अप्रैल", "मेई", "जून", "जुलाई", "अग.", "सित.", "अक्तू.", "नव.", "दिस."], ["जनवरी", "फरवरी", "मार्च", "अप्रैल", "मेई", "जून", "जुलाई", "अगस्त", "सितंबर", "अक्तूबर", "नवंबर", "दिसंबर"]], [["ई.पू.", "ईसवी"], u, ["ई.पू.", "ई. सन्"]], 0, [0, 0], ["d/M/yy", "d, MMM y", "d, MMMM y", "EEEE, d, MMMM y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1}, {0}", u, "{1} गी {0}", u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "INR", "₹", "भारती रपेऽ", {}, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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