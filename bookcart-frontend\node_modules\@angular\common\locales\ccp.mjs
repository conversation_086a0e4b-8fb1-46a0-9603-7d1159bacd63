/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["ccp", [["AM", "PM"], u, u], u, [["𑄢𑄧", "𑄥𑄧", "𑄟𑄧", "𑄝𑄪", "𑄝𑄳𑄢𑄨", "𑄥𑄪", "𑄥𑄧"], ["𑄢𑄧𑄝𑄨", "𑄥𑄧𑄟𑄴", "𑄟𑄧𑄁𑄉𑄧𑄣𑄴", "𑄝𑄪𑄖𑄴", "𑄝𑄳𑄢𑄨𑄥𑄪𑄛𑄴", "𑄥𑄪𑄇𑄴𑄇𑄮𑄢𑄴", "𑄥𑄧𑄚𑄨"], ["𑄢𑄧𑄝𑄨𑄝𑄢𑄴", "𑄥𑄧𑄟𑄴𑄝𑄢𑄴", "𑄟𑄧𑄁𑄉𑄧𑄣𑄴𑄝𑄢𑄴", "𑄝𑄪𑄖𑄴𑄝𑄢𑄴", "𑄝𑄳𑄢𑄨𑄥𑄪𑄛𑄴𑄝𑄢𑄴", "𑄥𑄪𑄇𑄴𑄇𑄮𑄢𑄴𑄝𑄢𑄴", "𑄥𑄧𑄚𑄨𑄝𑄢𑄴"], ["𑄢𑄧𑄝𑄨", "𑄥𑄧𑄟𑄴", "𑄟𑄧𑄁𑄉𑄧𑄣𑄴", "𑄝𑄪𑄖𑄴", "𑄝𑄳𑄢𑄨𑄥𑄪𑄛𑄴", "𑄥𑄪𑄇𑄴𑄇𑄮𑄢𑄴", "𑄥𑄧𑄚𑄨"]], u, [["𑄎", "𑄜𑄬", "𑄟", "𑄃𑄬", "𑄟𑄬", "𑄎𑄪𑄚𑄴", "𑄎𑄪", "𑄃", "𑄥𑄬", "𑄃𑄧", "𑄚𑄧", "𑄓𑄨"], ["𑄎𑄚𑄪", "𑄜𑄬𑄛𑄴", "𑄟𑄢𑄴𑄌𑄧", "𑄃𑄬𑄛𑄳𑄢𑄨𑄣𑄴", "𑄟𑄬", "𑄎𑄪𑄚𑄴", "𑄎𑄪𑄣𑄭", "𑄃𑄉𑄧𑄌𑄴𑄑𑄴", "𑄥𑄬𑄛𑄴𑄑𑄬𑄟𑄴𑄝𑄧𑄢𑄴", "𑄃𑄧𑄇𑄴𑄑𑄮𑄝𑄧𑄢𑄴", "𑄚𑄧𑄞𑄬𑄟𑄴𑄝𑄧𑄢𑄴", "𑄓𑄨𑄥𑄬𑄟𑄴𑄝𑄢𑄴"], ["𑄎𑄚𑄪𑄠𑄢𑄨", "𑄜𑄬𑄛𑄴𑄝𑄳𑄢𑄪𑄠𑄢𑄨", "𑄟𑄢𑄴𑄌𑄧", "𑄃𑄬𑄛𑄳𑄢𑄨𑄣𑄴", "𑄟𑄬", "𑄎𑄪𑄚𑄴", "𑄎𑄪𑄣𑄭", "𑄃𑄉𑄧𑄌𑄴𑄑𑄴", "𑄥𑄬𑄛𑄴𑄑𑄬𑄟𑄴𑄝𑄧𑄢𑄴", "𑄃𑄧𑄇𑄴𑄑𑄬𑄝𑄧𑄢𑄴", "𑄚𑄧𑄞𑄬𑄟𑄴𑄝𑄧𑄢𑄴", "𑄓𑄨𑄥𑄬𑄟𑄴𑄝𑄧𑄢𑄴"]], [["𑄎", "𑄜𑄬", "𑄟", "𑄃𑄬", "𑄟𑄬", "𑄎𑄪𑄚𑄴", "𑄎𑄪", "𑄃", "𑄥𑄬", "𑄃𑄧", "𑄚𑄧", "𑄓𑄨"], ["𑄎𑄚𑄪𑄠𑄢𑄨", "𑄜𑄬𑄛𑄴𑄝𑄳𑄢𑄪𑄠𑄢𑄨", "𑄟𑄢𑄴𑄌𑄧", "𑄃𑄬𑄛𑄳𑄢𑄨𑄣𑄴", "𑄟𑄬", "𑄎𑄪𑄚𑄴", "𑄎𑄪𑄣𑄭", "𑄃𑄉𑄧𑄌𑄴𑄑𑄴", "𑄥𑄬𑄛𑄴𑄑𑄬𑄟𑄴𑄝𑄧𑄢𑄴", "𑄃𑄧𑄇𑄴𑄑𑄮𑄝𑄧𑄢𑄴", "𑄚𑄧𑄞𑄬𑄟𑄴𑄝𑄧𑄢𑄴", "𑄓𑄨𑄥𑄬𑄟𑄴𑄝𑄧𑄢𑄴"], u], [["𑄈𑄳𑄢𑄨𑄌𑄴𑄑𑄴𑄛𑄫𑄢𑄴𑄝𑄧", "𑄈𑄳𑄢𑄨𑄌𑄴𑄑𑄛𑄴𑄘𑄧"], u, u], 0, [6, 0], ["d/M/yy", "d MMM, y", "d MMMM, y", "EEEE, d MMMM, y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##,##0.###", "#,##,##0%", "#,##,##0.00¤", "#E0"], "BDT", "৳", "𑄝𑄁𑄣𑄘𑄬𑄥𑄨 𑄑𑄬𑄋", { "BDT": ["৳"], "BYN": [u, "р."], "JPY": ["JP¥", "¥"], "PHP": [u, "₱"], "STD": [u, "Db"], "THB": ["฿"], "TWD": ["NT$"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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