/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["af-NA", [["v", "n"], ["vm.", "nm."], u], u, [["S", "M", "D", "W", "D", "V", "S"], ["So.", "Ma.", "Di.", "Wo.", "Do.", "Vr.", "Sa."], ["Sondag", "Maandag", "Dinsdag", "Woensdag", "Donderdag", "Vrydag", "Saterdag"], ["So.", "Ma.", "Di.", "Wo.", "<PERSON>.", "Vr.", "<PERSON>."]], u, [["<PERSON>", "<PERSON>", "<PERSON>", "A", "M", "J", "J", "A", "S", "O", "N", "D"], ["Jan.", "Feb.", "Mrt.", "Apr.", "<PERSON>", "Jun.", "Jul.", "Aug.", "Sep.", "Okt.", "Nov.", "<PERSON>."], ["<PERSON>uarie", "Februarie", "Maart", "April", "Mei", "Junie", "<PERSON>", "<PERSON>", "September", "Oktober", "November", "<PERSON>ember"]], u, [["v.C.", "n.C."], u, ["voor Christus", "na Christus"]], 1, [6, 0], ["y-MM-dd", "dd MMM y", "dd MMMM y", "EEEE dd MMMM y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1} {0}", u, u, u], [",", " ", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "ZAR", "R", "Suid-Afrikaanse rand", { "BYN": [u, "р."], "CAD": [u, "$"], "JPY": ["JP¥", "¥"], "MXN": [u, "$"], "NAD": ["$"], "PHP": [u, "₱"], "RON": [u, "leu"], "THB": ["฿"], "TWD": ["NT$"], "USD": [u, "$"], "ZAR": ["R"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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