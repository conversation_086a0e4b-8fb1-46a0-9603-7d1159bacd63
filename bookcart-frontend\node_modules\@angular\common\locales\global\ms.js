/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['ms'] = ["ms",[["a","p"],["PG","PTG"],u],u,[["A","I","S","R","K","J","S"],["Ahd","Isn","Sel","Rab","Kha","Jum","Sab"],["Ahad","Isnin","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","Sabtu"],["<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>h","<PERSON>","<PERSON>"]],u,[["J","<PERSON>","M","A","M","J","J","O","S","O","N","<PERSON>"],["<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON>t","<PERSON>","<PERSON>s"],["<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON>","April","<PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","September","<PERSON>to<PERSON>","November","<PERSON><PERSON>mber"]],u,[["<PERSON>.<PERSON>.","TM"],u,u],1,[6,0],["d/MM/yy","d MMM y","d MMMM y","EEEE, d MMMM y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",u,"{1} {0}",u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤#,##0.00","#E0"],"MYR","RM","Ringgit Malaysia",{"BYN":[u,"р."],"CAD":[u,"$"],"JPY":["JP¥","¥"],"MXN":[u,"$"],"MYR":["RM"],"PHP":[u,"₱"],"TWD":["NT$"],"USD":[u,"$"]},"ltr", plural, [[["pagi","pagi","tengah hari","petang","malam"],u,["tengah malam","pagi","tengah hari","petang","malam"]],[["pagi","pagi","tengah hari","petang","malam"],["tengah malam","pagi","tengah hari","petang","malam"],u],[["00:00","01:00"],["01:00","12:00"],["12:00","14:00"],["14:00","19:00"],["19:00","24:00"]]]];
  })(globalThis);
    