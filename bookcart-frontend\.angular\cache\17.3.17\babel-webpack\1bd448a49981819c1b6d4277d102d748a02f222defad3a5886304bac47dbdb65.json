{"ast": null, "code": "/**\n * @license Angular v17.3.12\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { ɵglobal, ɵRuntimeError, Injectable, InjectionToken, Inject, APP_ID, CSP_NONCE, PLATFORM_ID, Optional, ViewEncapsulation, RendererStyleFlags2, ɵinternalCreateApplication, ErrorHandler, ɵsetDocument, PLATFORM_INITIALIZER, createPlatformFactory, platformCore, ɵTESTABILITY_GETTER, ɵTESTABILITY, Testability, NgZone, TestabilityRegistry, ɵINJECTOR_SCOPE, RendererFactory2, ApplicationModule, NgModule, SkipSelf, ApplicationRef, ɵConsole, forwardRef, ɵXSS_SECURITY_URL, SecurityContext, ɵallowSanitizationBypassAndThrow, ɵunwrapSafeValue, ɵ_sanitizeUrl, ɵ_sanitizeHtml, ɵbypassSanitizationTrustHtml, ɵbypassSanitizationTrustStyle, ɵbypassSanitizationTrustScript, ɵbypassSanitizationTrustUrl, ɵbypassSanitizationTrustResourceUrl, ENVIRONMENT_INITIALIZER, inject, ɵformatRuntimeError, makeEnvironmentProviders, ɵwithDomHydration, Version, makeStateKey as makeStateKey$1, TransferState as TransferState$1 } from '@angular/core';\nimport { ɵDomAdapter, ɵsetRootDomAdapter, ɵparseCookieValue, ɵgetDOM, isPlatformServer, DOCUMENT, ɵPLATFORM_BROWSER_ID, XhrFactory, CommonModule } from '@angular/common';\nexport { ɵgetDOM } from '@angular/common';\nimport { ɵwithHttpTransferCache } from '@angular/common/http';\n\n/**\n * Provides DOM operations in any browser environment.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nclass GenericBrowserDomAdapter extends ɵDomAdapter {\n  constructor() {\n    super(...arguments);\n    this.supportsDOMEvents = true;\n  }\n}\n\n/**\n * A `DomAdapter` powered by full browser DOM APIs.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\n/* tslint:disable:requireParameterType no-console */\nclass BrowserDomAdapter extends GenericBrowserDomAdapter {\n  static makeCurrent() {\n    ɵsetRootDomAdapter(new BrowserDomAdapter());\n  }\n  onAndCancel(el, evt, listener) {\n    el.addEventListener(evt, listener);\n    return () => {\n      el.removeEventListener(evt, listener);\n    };\n  }\n  dispatchEvent(el, evt) {\n    el.dispatchEvent(evt);\n  }\n  remove(node) {\n    if (node.parentNode) {\n      node.parentNode.removeChild(node);\n    }\n  }\n  createElement(tagName, doc) {\n    doc = doc || this.getDefaultDocument();\n    return doc.createElement(tagName);\n  }\n  createHtmlDocument() {\n    return document.implementation.createHTMLDocument('fakeTitle');\n  }\n  getDefaultDocument() {\n    return document;\n  }\n  isElementNode(node) {\n    return node.nodeType === Node.ELEMENT_NODE;\n  }\n  isShadowRoot(node) {\n    return node instanceof DocumentFragment;\n  }\n  /** @deprecated No longer being used in Ivy code. To be removed in version 14. */\n  getGlobalEventTarget(doc, target) {\n    if (target === 'window') {\n      return window;\n    }\n    if (target === 'document') {\n      return doc;\n    }\n    if (target === 'body') {\n      return doc.body;\n    }\n    return null;\n  }\n  getBaseHref(doc) {\n    const href = getBaseElementHref();\n    return href == null ? null : relativePath(href);\n  }\n  resetBaseElement() {\n    baseElement = null;\n  }\n  getUserAgent() {\n    return window.navigator.userAgent;\n  }\n  getCookie(name) {\n    return ɵparseCookieValue(document.cookie, name);\n  }\n}\nlet baseElement = null;\nfunction getBaseElementHref() {\n  baseElement = baseElement || document.querySelector('base');\n  return baseElement ? baseElement.getAttribute('href') : null;\n}\nfunction relativePath(url) {\n  // The base URL doesn't really matter, we just need it so relative paths have something\n  // to resolve against. In the browser `HTMLBaseElement.href` is always absolute.\n  return new URL(url, document.baseURI).pathname;\n}\nclass BrowserGetTestability {\n  addToWindow(registry) {\n    ɵglobal['getAngularTestability'] = (elem, findInAncestors = true) => {\n      const testability = registry.findTestabilityInTree(elem, findInAncestors);\n      if (testability == null) {\n        throw new ɵRuntimeError(5103 /* RuntimeErrorCode.TESTABILITY_NOT_FOUND */, (typeof ngDevMode === 'undefined' || ngDevMode) && 'Could not find testability for element.');\n      }\n      return testability;\n    };\n    ɵglobal['getAllAngularTestabilities'] = () => registry.getAllTestabilities();\n    ɵglobal['getAllAngularRootElements'] = () => registry.getAllRootElements();\n    const whenAllStable = callback => {\n      const testabilities = ɵglobal['getAllAngularTestabilities']();\n      let count = testabilities.length;\n      const decrement = function () {\n        count--;\n        if (count == 0) {\n          callback();\n        }\n      };\n      testabilities.forEach(testability => {\n        testability.whenStable(decrement);\n      });\n    };\n    if (!ɵglobal['frameworkStabilizers']) {\n      ɵglobal['frameworkStabilizers'] = [];\n    }\n    ɵglobal['frameworkStabilizers'].push(whenAllStable);\n  }\n  findTestabilityInTree(registry, elem, findInAncestors) {\n    if (elem == null) {\n      return null;\n    }\n    const t = registry.getTestability(elem);\n    if (t != null) {\n      return t;\n    } else if (!findInAncestors) {\n      return null;\n    }\n    if (ɵgetDOM().isShadowRoot(elem)) {\n      return this.findTestabilityInTree(registry, elem.host, true);\n    }\n    return this.findTestabilityInTree(registry, elem.parentElement, true);\n  }\n}\n\n/**\n * A factory for `HttpXhrBackend` that uses the `XMLHttpRequest` browser API.\n */\nclass BrowserXhr {\n  build() {\n    return new XMLHttpRequest();\n  }\n  static {\n    this.ɵfac = function BrowserXhr_Factory(t) {\n      return new (t || BrowserXhr)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: BrowserXhr,\n      factory: BrowserXhr.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserXhr, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * The injection token for plugins of the `EventManager` service.\n *\n * @publicApi\n */\nconst EVENT_MANAGER_PLUGINS = new InjectionToken(ngDevMode ? 'EventManagerPlugins' : '');\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\nclass EventManager {\n  /**\n   * Initializes an instance of the event-manager service.\n   */\n  constructor(plugins, _zone) {\n    this._zone = _zone;\n    this._eventNameToPlugin = new Map();\n    plugins.forEach(plugin => {\n      plugin.manager = this;\n    });\n    this._plugins = plugins.slice().reverse();\n  }\n  /**\n   * Registers a handler for a specific element and event.\n   *\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @returns  A callback function that can be used to remove the handler.\n   */\n  addEventListener(element, eventName, handler) {\n    const plugin = this._findPluginFor(eventName);\n    return plugin.addEventListener(element, eventName, handler);\n  }\n  /**\n   * Retrieves the compilation zone in which event listeners are registered.\n   */\n  getZone() {\n    return this._zone;\n  }\n  /** @internal */\n  _findPluginFor(eventName) {\n    let plugin = this._eventNameToPlugin.get(eventName);\n    if (plugin) {\n      return plugin;\n    }\n    const plugins = this._plugins;\n    plugin = plugins.find(plugin => plugin.supports(eventName));\n    if (!plugin) {\n      throw new ɵRuntimeError(5101 /* RuntimeErrorCode.NO_PLUGIN_FOR_EVENT */, (typeof ngDevMode === 'undefined' || ngDevMode) && `No event manager plugin found for event ${eventName}`);\n    }\n    this._eventNameToPlugin.set(eventName, plugin);\n    return plugin;\n  }\n  static {\n    this.ɵfac = function EventManager_Factory(t) {\n      return new (t || EventManager)(i0.ɵɵinject(EVENT_MANAGER_PLUGINS), i0.ɵɵinject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: EventManager,\n      factory: EventManager.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EventManager, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [EVENT_MANAGER_PLUGINS]\n    }]\n  }, {\n    type: i0.NgZone\n  }], null);\n})();\n/**\n * The plugin definition for the `EventManager` class\n *\n * It can be used as a base class to create custom manager plugins, i.e. you can create your own\n * class that extends the `EventManagerPlugin` one.\n *\n * @publicApi\n */\nclass EventManagerPlugin {\n  // TODO: remove (has some usage in G3)\n  constructor(_doc) {\n    this._doc = _doc;\n  }\n}\n\n/** The style elements attribute name used to set value of `APP_ID` token. */\nconst APP_ID_ATTRIBUTE_NAME = 'ng-app-id';\nclass SharedStylesHost {\n  constructor(doc, appId, nonce, platformId = {}) {\n    this.doc = doc;\n    this.appId = appId;\n    this.nonce = nonce;\n    this.platformId = platformId;\n    // Maps all registered host nodes to a list of style nodes that have been added to the host node.\n    this.styleRef = new Map();\n    this.hostNodes = new Set();\n    this.styleNodesInDOM = this.collectServerRenderedStyles();\n    this.platformIsServer = isPlatformServer(platformId);\n    this.resetHostNodes();\n  }\n  addStyles(styles) {\n    for (const style of styles) {\n      const usageCount = this.changeUsageCount(style, 1);\n      if (usageCount === 1) {\n        this.onStyleAdded(style);\n      }\n    }\n  }\n  removeStyles(styles) {\n    for (const style of styles) {\n      const usageCount = this.changeUsageCount(style, -1);\n      if (usageCount <= 0) {\n        this.onStyleRemoved(style);\n      }\n    }\n  }\n  ngOnDestroy() {\n    const styleNodesInDOM = this.styleNodesInDOM;\n    if (styleNodesInDOM) {\n      styleNodesInDOM.forEach(node => node.remove());\n      styleNodesInDOM.clear();\n    }\n    for (const style of this.getAllStyles()) {\n      this.onStyleRemoved(style);\n    }\n    this.resetHostNodes();\n  }\n  addHost(hostNode) {\n    this.hostNodes.add(hostNode);\n    for (const style of this.getAllStyles()) {\n      this.addStyleToHost(hostNode, style);\n    }\n  }\n  removeHost(hostNode) {\n    this.hostNodes.delete(hostNode);\n  }\n  getAllStyles() {\n    return this.styleRef.keys();\n  }\n  onStyleAdded(style) {\n    for (const host of this.hostNodes) {\n      this.addStyleToHost(host, style);\n    }\n  }\n  onStyleRemoved(style) {\n    const styleRef = this.styleRef;\n    styleRef.get(style)?.elements?.forEach(node => node.remove());\n    styleRef.delete(style);\n  }\n  collectServerRenderedStyles() {\n    const styles = this.doc.head?.querySelectorAll(`style[${APP_ID_ATTRIBUTE_NAME}=\"${this.appId}\"]`);\n    if (styles?.length) {\n      const styleMap = new Map();\n      styles.forEach(style => {\n        if (style.textContent != null) {\n          styleMap.set(style.textContent, style);\n        }\n      });\n      return styleMap;\n    }\n    return null;\n  }\n  changeUsageCount(style, delta) {\n    const map = this.styleRef;\n    if (map.has(style)) {\n      const styleRefValue = map.get(style);\n      styleRefValue.usage += delta;\n      return styleRefValue.usage;\n    }\n    map.set(style, {\n      usage: delta,\n      elements: []\n    });\n    return delta;\n  }\n  getStyleElement(host, style) {\n    const styleNodesInDOM = this.styleNodesInDOM;\n    const styleEl = styleNodesInDOM?.get(style);\n    if (styleEl?.parentNode === host) {\n      // `styleNodesInDOM` cannot be undefined due to the above `styleNodesInDOM?.get`.\n      styleNodesInDOM.delete(style);\n      styleEl.removeAttribute(APP_ID_ATTRIBUTE_NAME);\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        // This attribute is solely used for debugging purposes.\n        styleEl.setAttribute('ng-style-reused', '');\n      }\n      return styleEl;\n    } else {\n      const styleEl = this.doc.createElement('style');\n      if (this.nonce) {\n        styleEl.setAttribute('nonce', this.nonce);\n      }\n      styleEl.textContent = style;\n      if (this.platformIsServer) {\n        styleEl.setAttribute(APP_ID_ATTRIBUTE_NAME, this.appId);\n      }\n      host.appendChild(styleEl);\n      return styleEl;\n    }\n  }\n  addStyleToHost(host, style) {\n    const styleEl = this.getStyleElement(host, style);\n    const styleRef = this.styleRef;\n    const styleElRef = styleRef.get(style)?.elements;\n    if (styleElRef) {\n      styleElRef.push(styleEl);\n    } else {\n      styleRef.set(style, {\n        elements: [styleEl],\n        usage: 1\n      });\n    }\n  }\n  resetHostNodes() {\n    const hostNodes = this.hostNodes;\n    hostNodes.clear();\n    // Re-add the head element back since this is the default host.\n    hostNodes.add(this.doc.head);\n  }\n  static {\n    this.ɵfac = function SharedStylesHost_Factory(t) {\n      return new (t || SharedStylesHost)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(APP_ID), i0.ɵɵinject(CSP_NONCE, 8), i0.ɵɵinject(PLATFORM_ID));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: SharedStylesHost,\n      factory: SharedStylesHost.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SharedStylesHost, [{\n    type: Injectable\n  }], () => [{\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [APP_ID]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CSP_NONCE]\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }], null);\n})();\nconst NAMESPACE_URIS = {\n  'svg': 'http://www.w3.org/2000/svg',\n  'xhtml': 'http://www.w3.org/1999/xhtml',\n  'xlink': 'http://www.w3.org/1999/xlink',\n  'xml': 'http://www.w3.org/XML/1998/namespace',\n  'xmlns': 'http://www.w3.org/2000/xmlns/',\n  'math': 'http://www.w3.org/1998/MathML/'\n};\nconst COMPONENT_REGEX = /%COMP%/g;\nconst COMPONENT_VARIABLE = '%COMP%';\nconst HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nconst CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n/**\n * The default value for the `REMOVE_STYLES_ON_COMPONENT_DESTROY` DI token.\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT = true;\n/**\n * A [DI token](guide/glossary#di-token \"DI token definition\") that indicates whether styles\n * of destroyed components should be removed from DOM.\n *\n * By default, the value is set to `true`.\n * @publicApi\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY = new InjectionToken(ngDevMode ? 'RemoveStylesOnCompDestroy' : '', {\n  providedIn: 'root',\n  factory: () => REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT\n});\nfunction shimContentAttribute(componentShortId) {\n  return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimHostAttribute(componentShortId) {\n  return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimStylesContent(compId, styles) {\n  return styles.map(s => s.replace(COMPONENT_REGEX, compId));\n}\nclass DomRendererFactory2 {\n  constructor(eventManager, sharedStylesHost, appId, removeStylesOnCompDestroy, doc, platformId, ngZone, nonce = null) {\n    this.eventManager = eventManager;\n    this.sharedStylesHost = sharedStylesHost;\n    this.appId = appId;\n    this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n    this.doc = doc;\n    this.platformId = platformId;\n    this.ngZone = ngZone;\n    this.nonce = nonce;\n    this.rendererByCompId = new Map();\n    this.platformIsServer = isPlatformServer(platformId);\n    this.defaultRenderer = new DefaultDomRenderer2(eventManager, doc, ngZone, this.platformIsServer);\n  }\n  createRenderer(element, type) {\n    if (!element || !type) {\n      return this.defaultRenderer;\n    }\n    if (this.platformIsServer && type.encapsulation === ViewEncapsulation.ShadowDom) {\n      // Domino does not support shadow DOM.\n      type = {\n        ...type,\n        encapsulation: ViewEncapsulation.Emulated\n      };\n    }\n    const renderer = this.getOrCreateRenderer(element, type);\n    // Renderers have different logic due to different encapsulation behaviours.\n    // Ex: for emulated, an attribute is added to the element.\n    if (renderer instanceof EmulatedEncapsulationDomRenderer2) {\n      renderer.applyToHost(element);\n    } else if (renderer instanceof NoneEncapsulationDomRenderer) {\n      renderer.applyStyles();\n    }\n    return renderer;\n  }\n  getOrCreateRenderer(element, type) {\n    const rendererByCompId = this.rendererByCompId;\n    let renderer = rendererByCompId.get(type.id);\n    if (!renderer) {\n      const doc = this.doc;\n      const ngZone = this.ngZone;\n      const eventManager = this.eventManager;\n      const sharedStylesHost = this.sharedStylesHost;\n      const removeStylesOnCompDestroy = this.removeStylesOnCompDestroy;\n      const platformIsServer = this.platformIsServer;\n      switch (type.encapsulation) {\n        case ViewEncapsulation.Emulated:\n          renderer = new EmulatedEncapsulationDomRenderer2(eventManager, sharedStylesHost, type, this.appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer);\n          break;\n        case ViewEncapsulation.ShadowDom:\n          return new ShadowDomRenderer(eventManager, sharedStylesHost, element, type, doc, ngZone, this.nonce, platformIsServer);\n        default:\n          renderer = new NoneEncapsulationDomRenderer(eventManager, sharedStylesHost, type, removeStylesOnCompDestroy, doc, ngZone, platformIsServer);\n          break;\n      }\n      rendererByCompId.set(type.id, renderer);\n    }\n    return renderer;\n  }\n  ngOnDestroy() {\n    this.rendererByCompId.clear();\n  }\n  static {\n    this.ɵfac = function DomRendererFactory2_Factory(t) {\n      return new (t || DomRendererFactory2)(i0.ɵɵinject(EventManager), i0.ɵɵinject(SharedStylesHost), i0.ɵɵinject(APP_ID), i0.ɵɵinject(REMOVE_STYLES_ON_COMPONENT_DESTROY), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(CSP_NONCE));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DomRendererFactory2,\n      factory: DomRendererFactory2.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomRendererFactory2, [{\n    type: Injectable\n  }], () => [{\n    type: EventManager\n  }, {\n    type: SharedStylesHost\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [APP_ID]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [REMOVE_STYLES_ON_COMPONENT_DESTROY]\n    }]\n  }, {\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: Object,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CSP_NONCE]\n    }]\n  }], null);\n})();\nclass DefaultDomRenderer2 {\n  constructor(eventManager, doc, ngZone, platformIsServer) {\n    this.eventManager = eventManager;\n    this.doc = doc;\n    this.ngZone = ngZone;\n    this.platformIsServer = platformIsServer;\n    this.data = Object.create(null);\n    /**\n     * By default this renderer throws when encountering synthetic properties\n     * This can be disabled for example by the AsyncAnimationRendererFactory\n     */\n    this.throwOnSyntheticProps = true;\n    this.destroyNode = null;\n  }\n  destroy() {}\n  createElement(name, namespace) {\n    if (namespace) {\n      // TODO: `|| namespace` was added in\n      // https://github.com/angular/angular/commit/2b9cc8503d48173492c29f5a271b61126104fbdb to\n      // support how Ivy passed around the namespace URI rather than short name at the time. It did\n      // not, however extend the support to other parts of the system (setAttribute, setAttribute,\n      // and the ServerRenderer). We should decide what exactly the semantics for dealing with\n      // namespaces should be and make it consistent.\n      // Related issues:\n      // https://github.com/angular/angular/issues/44028\n      // https://github.com/angular/angular/issues/44883\n      return this.doc.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n    }\n    return this.doc.createElement(name);\n  }\n  createComment(value) {\n    return this.doc.createComment(value);\n  }\n  createText(value) {\n    return this.doc.createTextNode(value);\n  }\n  appendChild(parent, newChild) {\n    const targetParent = isTemplateNode(parent) ? parent.content : parent;\n    targetParent.appendChild(newChild);\n  }\n  insertBefore(parent, newChild, refChild) {\n    if (parent) {\n      const targetParent = isTemplateNode(parent) ? parent.content : parent;\n      targetParent.insertBefore(newChild, refChild);\n    }\n  }\n  removeChild(parent, oldChild) {\n    if (parent) {\n      parent.removeChild(oldChild);\n    }\n  }\n  selectRootElement(selectorOrNode, preserveContent) {\n    let el = typeof selectorOrNode === 'string' ? this.doc.querySelector(selectorOrNode) : selectorOrNode;\n    if (!el) {\n      throw new ɵRuntimeError(-5104 /* RuntimeErrorCode.ROOT_NODE_NOT_FOUND */, (typeof ngDevMode === 'undefined' || ngDevMode) && `The selector \"${selectorOrNode}\" did not match any elements`);\n    }\n    if (!preserveContent) {\n      el.textContent = '';\n    }\n    return el;\n  }\n  parentNode(node) {\n    return node.parentNode;\n  }\n  nextSibling(node) {\n    return node.nextSibling;\n  }\n  setAttribute(el, name, value, namespace) {\n    if (namespace) {\n      name = namespace + ':' + name;\n      const namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.setAttributeNS(namespaceUri, name, value);\n      } else {\n        el.setAttribute(name, value);\n      }\n    } else {\n      el.setAttribute(name, value);\n    }\n  }\n  removeAttribute(el, name, namespace) {\n    if (namespace) {\n      const namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.removeAttributeNS(namespaceUri, name);\n      } else {\n        el.removeAttribute(`${namespace}:${name}`);\n      }\n    } else {\n      el.removeAttribute(name);\n    }\n  }\n  addClass(el, name) {\n    el.classList.add(name);\n  }\n  removeClass(el, name) {\n    el.classList.remove(name);\n  }\n  setStyle(el, style, value, flags) {\n    if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n      el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n    } else {\n      el.style[style] = value;\n    }\n  }\n  removeStyle(el, style, flags) {\n    if (flags & RendererStyleFlags2.DashCase) {\n      // removeProperty has no effect when used on camelCased properties.\n      el.style.removeProperty(style);\n    } else {\n      el.style[style] = '';\n    }\n  }\n  setProperty(el, name, value) {\n    if (el == null) {\n      return;\n    }\n    (typeof ngDevMode === 'undefined' || ngDevMode) && this.throwOnSyntheticProps && checkNoSyntheticProp(name, 'property');\n    el[name] = value;\n  }\n  setValue(node, value) {\n    node.nodeValue = value;\n  }\n  listen(target, event, callback) {\n    (typeof ngDevMode === 'undefined' || ngDevMode) && this.throwOnSyntheticProps && checkNoSyntheticProp(event, 'listener');\n    if (typeof target === 'string') {\n      target = ɵgetDOM().getGlobalEventTarget(this.doc, target);\n      if (!target) {\n        throw new Error(`Unsupported event target ${target} for event ${event}`);\n      }\n    }\n    return this.eventManager.addEventListener(target, event, this.decoratePreventDefault(callback));\n  }\n  decoratePreventDefault(eventHandler) {\n    // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n    // decoratePreventDefault or is a listener added outside the Angular context so it can handle\n    // the two differently. In the first case, the special '__ngUnwrap__' token is passed to the\n    // unwrap the listener (see below).\n    return event => {\n      // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n      // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The\n      // debug_node can inspect the listener toString contents for the existence of this special\n      // token. Because the token is a string literal, it is ensured to not be modified by compiled\n      // code.\n      if (event === '__ngUnwrap__') {\n        return eventHandler;\n      }\n      // Run the event handler inside the ngZone because event handlers are not patched\n      // by Zone on the server. This is required only for tests.\n      const allowDefaultBehavior = this.platformIsServer ? this.ngZone.runGuarded(() => eventHandler(event)) : eventHandler(event);\n      if (allowDefaultBehavior === false) {\n        event.preventDefault();\n      }\n      return undefined;\n    };\n  }\n}\nconst AT_CHARCODE = (() => '@'.charCodeAt(0))();\nfunction checkNoSyntheticProp(name, nameKind) {\n  if (name.charCodeAt(0) === AT_CHARCODE) {\n    throw new ɵRuntimeError(5105 /* RuntimeErrorCode.UNEXPECTED_SYNTHETIC_PROPERTY */, `Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Either \\`BrowserAnimationsModule\\` or \\`NoopAnimationsModule\\` are imported in your application.\n  - There is corresponding configuration for the animation named \\`${name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.io/api/core/Component#animations).`);\n  }\n}\nfunction isTemplateNode(node) {\n  return node.tagName === 'TEMPLATE' && node.content !== undefined;\n}\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n  constructor(eventManager, sharedStylesHost, hostEl, component, doc, ngZone, nonce, platformIsServer) {\n    super(eventManager, doc, ngZone, platformIsServer);\n    this.sharedStylesHost = sharedStylesHost;\n    this.hostEl = hostEl;\n    this.shadowRoot = hostEl.attachShadow({\n      mode: 'open'\n    });\n    this.sharedStylesHost.addHost(this.shadowRoot);\n    const styles = shimStylesContent(component.id, component.styles);\n    for (const style of styles) {\n      const styleEl = document.createElement('style');\n      if (nonce) {\n        styleEl.setAttribute('nonce', nonce);\n      }\n      styleEl.textContent = style;\n      this.shadowRoot.appendChild(styleEl);\n    }\n  }\n  nodeOrShadowRoot(node) {\n    return node === this.hostEl ? this.shadowRoot : node;\n  }\n  appendChild(parent, newChild) {\n    return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n  }\n  insertBefore(parent, newChild, refChild) {\n    return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n  }\n  removeChild(parent, oldChild) {\n    return super.removeChild(this.nodeOrShadowRoot(parent), oldChild);\n  }\n  parentNode(node) {\n    return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n  }\n  destroy() {\n    this.sharedStylesHost.removeHost(this.shadowRoot);\n  }\n}\nclass NoneEncapsulationDomRenderer extends DefaultDomRenderer2 {\n  constructor(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, compId) {\n    super(eventManager, doc, ngZone, platformIsServer);\n    this.sharedStylesHost = sharedStylesHost;\n    this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n    this.styles = compId ? shimStylesContent(compId, component.styles) : component.styles;\n  }\n  applyStyles() {\n    this.sharedStylesHost.addStyles(this.styles);\n  }\n  destroy() {\n    if (!this.removeStylesOnCompDestroy) {\n      return;\n    }\n    this.sharedStylesHost.removeStyles(this.styles);\n  }\n}\nclass EmulatedEncapsulationDomRenderer2 extends NoneEncapsulationDomRenderer {\n  constructor(eventManager, sharedStylesHost, component, appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer) {\n    const compId = appId + '-' + component.id;\n    super(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, compId);\n    this.contentAttr = shimContentAttribute(compId);\n    this.hostAttr = shimHostAttribute(compId);\n  }\n  applyToHost(element) {\n    this.applyStyles();\n    this.setAttribute(element, this.hostAttr, '');\n  }\n  createElement(parent, name) {\n    const el = super.createElement(parent, name);\n    super.setAttribute(el, this.contentAttr, '');\n    return el;\n  }\n}\nclass DomEventsPlugin extends EventManagerPlugin {\n  constructor(doc) {\n    super(doc);\n  }\n  // This plugin should come last in the list of plugins, because it accepts all\n  // events.\n  supports(eventName) {\n    return true;\n  }\n  addEventListener(element, eventName, handler) {\n    element.addEventListener(eventName, handler, false);\n    return () => this.removeEventListener(element, eventName, handler);\n  }\n  removeEventListener(target, eventName, callback) {\n    return target.removeEventListener(eventName, callback);\n  }\n  static {\n    this.ɵfac = function DomEventsPlugin_Factory(t) {\n      return new (t || DomEventsPlugin)(i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DomEventsPlugin,\n      factory: DomEventsPlugin.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomEventsPlugin, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * Defines supported modifiers for key events.\n */\nconst MODIFIER_KEYS = ['alt', 'control', 'meta', 'shift'];\n// The following values are here for cross-browser compatibility and to match the W3C standard\n// cf https://www.w3.org/TR/DOM-Level-3-Events-key/\nconst _keyMap = {\n  '\\b': 'Backspace',\n  '\\t': 'Tab',\n  '\\x7F': 'Delete',\n  '\\x1B': 'Escape',\n  'Del': 'Delete',\n  'Esc': 'Escape',\n  'Left': 'ArrowLeft',\n  'Right': 'ArrowRight',\n  'Up': 'ArrowUp',\n  'Down': 'ArrowDown',\n  'Menu': 'ContextMenu',\n  'Scroll': 'ScrollLock',\n  'Win': 'OS'\n};\n/**\n * Retrieves modifiers from key-event objects.\n */\nconst MODIFIER_KEY_GETTERS = {\n  'alt': event => event.altKey,\n  'control': event => event.ctrlKey,\n  'meta': event => event.metaKey,\n  'shift': event => event.shiftKey\n};\n/**\n * A browser plug-in that provides support for handling of key events in Angular.\n */\nclass KeyEventsPlugin extends EventManagerPlugin {\n  /**\n   * Initializes an instance of the browser plug-in.\n   * @param doc The document in which key events will be detected.\n   */\n  constructor(doc) {\n    super(doc);\n  }\n  /**\n   * Reports whether a named key event is supported.\n   * @param eventName The event name to query.\n   * @return True if the named key event is supported.\n   */\n  supports(eventName) {\n    return KeyEventsPlugin.parseEventName(eventName) != null;\n  }\n  /**\n   * Registers a handler for a specific element and key event.\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the key event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @returns The key event that was registered.\n   */\n  addEventListener(element, eventName, handler) {\n    const parsedEvent = KeyEventsPlugin.parseEventName(eventName);\n    const outsideHandler = KeyEventsPlugin.eventCallback(parsedEvent['fullKey'], handler, this.manager.getZone());\n    return this.manager.getZone().runOutsideAngular(() => {\n      return ɵgetDOM().onAndCancel(element, parsedEvent['domEventName'], outsideHandler);\n    });\n  }\n  /**\n   * Parses the user provided full keyboard event definition and normalizes it for\n   * later internal use. It ensures the string is all lowercase, converts special\n   * characters to a standard spelling, and orders all the values consistently.\n   *\n   * @param eventName The name of the key event to listen for.\n   * @returns an object with the full, normalized string, and the dom event name\n   * or null in the case when the event doesn't match a keyboard event.\n   */\n  static parseEventName(eventName) {\n    const parts = eventName.toLowerCase().split('.');\n    const domEventName = parts.shift();\n    if (parts.length === 0 || !(domEventName === 'keydown' || domEventName === 'keyup')) {\n      return null;\n    }\n    const key = KeyEventsPlugin._normalizeKey(parts.pop());\n    let fullKey = '';\n    let codeIX = parts.indexOf('code');\n    if (codeIX > -1) {\n      parts.splice(codeIX, 1);\n      fullKey = 'code.';\n    }\n    MODIFIER_KEYS.forEach(modifierName => {\n      const index = parts.indexOf(modifierName);\n      if (index > -1) {\n        parts.splice(index, 1);\n        fullKey += modifierName + '.';\n      }\n    });\n    fullKey += key;\n    if (parts.length != 0 || key.length === 0) {\n      // returning null instead of throwing to let another plugin process the event\n      return null;\n    }\n    // NOTE: Please don't rewrite this as so, as it will break JSCompiler property renaming.\n    //       The code must remain in the `result['domEventName']` form.\n    // return {domEventName, fullKey};\n    const result = {};\n    result['domEventName'] = domEventName;\n    result['fullKey'] = fullKey;\n    return result;\n  }\n  /**\n   * Determines whether the actual keys pressed match the configured key code string.\n   * The `fullKeyCode` event is normalized in the `parseEventName` method when the\n   * event is attached to the DOM during the `addEventListener` call. This is unseen\n   * by the end user and is normalized for internal consistency and parsing.\n   *\n   * @param event The keyboard event.\n   * @param fullKeyCode The normalized user defined expected key event string\n   * @returns boolean.\n   */\n  static matchEventFullKeyCode(event, fullKeyCode) {\n    let keycode = _keyMap[event.key] || event.key;\n    let key = '';\n    if (fullKeyCode.indexOf('code.') > -1) {\n      keycode = event.code;\n      key = 'code.';\n    }\n    // the keycode could be unidentified so we have to check here\n    if (keycode == null || !keycode) return false;\n    keycode = keycode.toLowerCase();\n    if (keycode === ' ') {\n      keycode = 'space'; // for readability\n    } else if (keycode === '.') {\n      keycode = 'dot'; // because '.' is used as a separator in event names\n    }\n    MODIFIER_KEYS.forEach(modifierName => {\n      if (modifierName !== keycode) {\n        const modifierGetter = MODIFIER_KEY_GETTERS[modifierName];\n        if (modifierGetter(event)) {\n          key += modifierName + '.';\n        }\n      }\n    });\n    key += keycode;\n    return key === fullKeyCode;\n  }\n  /**\n   * Configures a handler callback for a key event.\n   * @param fullKey The event name that combines all simultaneous keystrokes.\n   * @param handler The function that responds to the key event.\n   * @param zone The zone in which the event occurred.\n   * @returns A callback function.\n   */\n  static eventCallback(fullKey, handler, zone) {\n    return event => {\n      if (KeyEventsPlugin.matchEventFullKeyCode(event, fullKey)) {\n        zone.runGuarded(() => handler(event));\n      }\n    };\n  }\n  /** @internal */\n  static _normalizeKey(keyName) {\n    return keyName === 'esc' ? 'escape' : keyName;\n  }\n  static {\n    this.ɵfac = function KeyEventsPlugin_Factory(t) {\n      return new (t || KeyEventsPlugin)(i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: KeyEventsPlugin,\n      factory: KeyEventsPlugin.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(KeyEventsPlugin, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * Bootstraps an instance of an Angular application and renders a standalone component as the\n * application's root component. More information about standalone components can be found in [this\n * guide](guide/standalone-components).\n *\n * @usageNotes\n * The root component passed into this function *must* be a standalone one (should have the\n * `standalone: true` flag in the `@Component` decorator config).\n *\n * ```typescript\n * @Component({\n *   standalone: true,\n *   template: 'Hello world!'\n * })\n * class RootComponent {}\n *\n * const appRef: ApplicationRef = await bootstrapApplication(RootComponent);\n * ```\n *\n * You can add the list of providers that should be available in the application injector by\n * specifying the `providers` field in an object passed as the second argument:\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     {provide: BACKEND_URL, useValue: 'https://yourdomain.com/api'}\n *   ]\n * });\n * ```\n *\n * The `importProvidersFrom` helper method can be used to collect all providers from any\n * existing NgModule (and transitively from all NgModules that it imports):\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     importProvidersFrom(SomeNgModule)\n *   ]\n * });\n * ```\n *\n * Note: the `bootstrapApplication` method doesn't include [Testability](api/core/Testability) by\n * default. You can add [Testability](api/core/Testability) by getting the list of necessary\n * providers using `provideProtractorTestingSupport()` function and adding them into the `providers`\n * array, for example:\n *\n * ```typescript\n * import {provideProtractorTestingSupport} from '@angular/platform-browser';\n *\n * await bootstrapApplication(RootComponent, {providers: [provideProtractorTestingSupport()]});\n * ```\n *\n * @param rootComponent A reference to a standalone component that should be rendered.\n * @param options Extra configuration for the bootstrap operation, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction bootstrapApplication(rootComponent, options) {\n  return ɵinternalCreateApplication({\n    rootComponent,\n    ...createProvidersConfig(options)\n  });\n}\n/**\n * Create an instance of an Angular application without bootstrapping any components. This is useful\n * for the situation where one wants to decouple application environment creation (a platform and\n * associated injectors) from rendering components on a screen. Components can be subsequently\n * bootstrapped on the returned `ApplicationRef`.\n *\n * @param options Extra configuration for the application environment, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction createApplication(options) {\n  return ɵinternalCreateApplication(createProvidersConfig(options));\n}\nfunction createProvidersConfig(options) {\n  return {\n    appProviders: [...BROWSER_MODULE_PROVIDERS, ...(options?.providers ?? [])],\n    platformProviders: INTERNAL_BROWSER_PLATFORM_PROVIDERS\n  };\n}\n/**\n * Returns a set of providers required to setup [Testability](api/core/Testability) for an\n * application bootstrapped using the `bootstrapApplication` function. The set of providers is\n * needed to support testing an application with Protractor (which relies on the Testability APIs\n * to be present).\n *\n * @returns An array of providers required to setup Testability for an application and make it\n *     available for testing using Protractor.\n *\n * @publicApi\n */\nfunction provideProtractorTestingSupport() {\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideProtractorTestingSupport` call results in app\n  // code.\n  return [...TESTABILITY_PROVIDERS];\n}\nfunction initDomAdapter() {\n  BrowserDomAdapter.makeCurrent();\n}\nfunction errorHandler() {\n  return new ErrorHandler();\n}\nfunction _document() {\n  // Tell ivy about the global document\n  ɵsetDocument(document);\n  return document;\n}\nconst INTERNAL_BROWSER_PLATFORM_PROVIDERS = [{\n  provide: PLATFORM_ID,\n  useValue: ɵPLATFORM_BROWSER_ID\n}, {\n  provide: PLATFORM_INITIALIZER,\n  useValue: initDomAdapter,\n  multi: true\n}, {\n  provide: DOCUMENT,\n  useFactory: _document,\n  deps: []\n}];\n/**\n * A factory function that returns a `PlatformRef` instance associated with browser service\n * providers.\n *\n * @publicApi\n */\nconst platformBrowser = createPlatformFactory(platformCore, 'browser', INTERNAL_BROWSER_PLATFORM_PROVIDERS);\n/**\n * Internal marker to signal whether providers from the `BrowserModule` are already present in DI.\n * This is needed to avoid loading `BrowserModule` providers twice. We can't rely on the\n * `BrowserModule` presence itself, since the standalone-based bootstrap just imports\n * `BrowserModule` providers without referencing the module itself.\n */\nconst BROWSER_MODULE_PROVIDERS_MARKER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'BrowserModule Providers Marker' : '');\nconst TESTABILITY_PROVIDERS = [{\n  provide: ɵTESTABILITY_GETTER,\n  useClass: BrowserGetTestability,\n  deps: []\n}, {\n  provide: ɵTESTABILITY,\n  useClass: Testability,\n  deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER]\n}, {\n  provide: Testability,\n  // Also provide as `Testability` for backwards-compatibility.\n  useClass: Testability,\n  deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER]\n}];\nconst BROWSER_MODULE_PROVIDERS = [{\n  provide: ɵINJECTOR_SCOPE,\n  useValue: 'root'\n}, {\n  provide: ErrorHandler,\n  useFactory: errorHandler,\n  deps: []\n}, {\n  provide: EVENT_MANAGER_PLUGINS,\n  useClass: DomEventsPlugin,\n  multi: true,\n  deps: [DOCUMENT, NgZone, PLATFORM_ID]\n}, {\n  provide: EVENT_MANAGER_PLUGINS,\n  useClass: KeyEventsPlugin,\n  multi: true,\n  deps: [DOCUMENT]\n}, DomRendererFactory2, SharedStylesHost, EventManager, {\n  provide: RendererFactory2,\n  useExisting: DomRendererFactory2\n}, {\n  provide: XhrFactory,\n  useClass: BrowserXhr,\n  deps: []\n}, typeof ngDevMode === 'undefined' || ngDevMode ? {\n  provide: BROWSER_MODULE_PROVIDERS_MARKER,\n  useValue: true\n} : []];\n/**\n * Exports required infrastructure for all Angular apps.\n * Included by default in all Angular apps created with the CLI\n * `new` command.\n * Re-exports `CommonModule` and `ApplicationModule`, making their\n * exports and providers available to all apps.\n *\n * @publicApi\n */\nclass BrowserModule {\n  constructor(providersAlreadyPresent) {\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && providersAlreadyPresent) {\n      throw new ɵRuntimeError(5100 /* RuntimeErrorCode.BROWSER_MODULE_ALREADY_LOADED */, `Providers from the \\`BrowserModule\\` have already been loaded. If you need access ` + `to common directives such as NgIf and NgFor, import the \\`CommonModule\\` instead.`);\n    }\n  }\n  /**\n   * Configures a browser-based app to transition from a server-rendered app, if\n   * one is present on the page.\n   *\n   * @param params An object containing an identifier for the app to transition.\n   * The ID must match between the client and server versions of the app.\n   * @returns The reconfigured `BrowserModule` to import into the app's root `AppModule`.\n   *\n   * @deprecated Use {@link APP_ID} instead to set the application ID.\n   */\n  static withServerTransition(params) {\n    return {\n      ngModule: BrowserModule,\n      providers: [{\n        provide: APP_ID,\n        useValue: params.appId\n      }]\n    };\n  }\n  static {\n    this.ɵfac = function BrowserModule_Factory(t) {\n      return new (t || BrowserModule)(i0.ɵɵinject(BROWSER_MODULE_PROVIDERS_MARKER, 12));\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: BrowserModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n      imports: [CommonModule, ApplicationModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserModule, [{\n    type: NgModule,\n    args: [{\n      providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n      exports: [CommonModule, ApplicationModule]\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: SkipSelf\n    }, {\n      type: Inject,\n      args: [BROWSER_MODULE_PROVIDERS_MARKER]\n    }]\n  }], null);\n})();\n\n/**\n * A service for managing HTML `<meta>` tags.\n *\n * Properties of the `MetaDefinition` object match the attributes of the\n * HTML `<meta>` tag. These tags define document metadata that is important for\n * things like configuring a Content Security Policy, defining browser compatibility\n * and security settings, setting HTTP Headers, defining rich content for social sharing,\n * and Search Engine Optimization (SEO).\n *\n * To identify specific `<meta>` tags in a document, use an attribute selection\n * string in the format `\"tag_attribute='value string'\"`.\n * For example, an `attrSelector` value of `\"name='description'\"` matches a tag\n * whose `name` attribute has the value `\"description\"`.\n * Selectors are used with the `querySelector()` Document method,\n * in the format `meta[{attrSelector}]`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see [Document.querySelector()](https://developer.mozilla.org/docs/Web/API/Document/querySelector)\n *\n *\n * @publicApi\n */\nclass Meta {\n  constructor(_doc) {\n    this._doc = _doc;\n    this._dom = ɵgetDOM();\n  }\n  /**\n   * Retrieves or creates a specific `<meta>` tag element in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * If an existing element is found, it is returned and is not modified in any way.\n   * @param tag The definition of a `<meta>` element to match or create.\n   * @param forceCreation True to create a new element without checking whether one already exists.\n   * @returns The existing element with the same attributes and values if found,\n   * the new element if no match is found, or `null` if the tag parameter is not defined.\n   */\n  addTag(tag, forceCreation = false) {\n    if (!tag) return null;\n    return this._getOrCreateElement(tag, forceCreation);\n  }\n  /**\n   * Retrieves or creates a set of `<meta>` tag elements in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * @param tags An array of tag definitions to match or create.\n   * @param forceCreation True to create new elements without checking whether they already exist.\n   * @returns The matching elements if found, or the new elements.\n   */\n  addTags(tags, forceCreation = false) {\n    if (!tags) return [];\n    return tags.reduce((result, tag) => {\n      if (tag) {\n        result.push(this._getOrCreateElement(tag, forceCreation));\n      }\n      return result;\n    }, []);\n  }\n  /**\n   * Retrieves a `<meta>` tag element in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching element, if any.\n   */\n  getTag(attrSelector) {\n    if (!attrSelector) return null;\n    return this._doc.querySelector(`meta[${attrSelector}]`) || null;\n  }\n  /**\n   * Retrieves a set of `<meta>` tag elements in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching elements, if any.\n   */\n  getTags(attrSelector) {\n    if (!attrSelector) return [];\n    const list /*NodeList*/ = this._doc.querySelectorAll(`meta[${attrSelector}]`);\n    return list ? [].slice.call(list) : [];\n  }\n  /**\n   * Modifies an existing `<meta>` tag element in the current HTML document.\n   * @param tag The tag description with which to replace the existing tag content.\n   * @param selector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   * If not supplied, matches a tag with the same `name` or `property` attribute value as the\n   * replacement tag.\n   * @return The modified element.\n   */\n  updateTag(tag, selector) {\n    if (!tag) return null;\n    selector = selector || this._parseSelector(tag);\n    const meta = this.getTag(selector);\n    if (meta) {\n      return this._setMetaElementAttributes(tag, meta);\n    }\n    return this._getOrCreateElement(tag, true);\n  }\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param attrSelector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   */\n  removeTag(attrSelector) {\n    this.removeTagElement(this.getTag(attrSelector));\n  }\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param meta The tag definition to match against to identify an existing tag.\n   */\n  removeTagElement(meta) {\n    if (meta) {\n      this._dom.remove(meta);\n    }\n  }\n  _getOrCreateElement(meta, forceCreation = false) {\n    if (!forceCreation) {\n      const selector = this._parseSelector(meta);\n      // It's allowed to have multiple elements with the same name so it's not enough to\n      // just check that element with the same name already present on the page. We also need to\n      // check if element has tag attributes\n      const elem = this.getTags(selector).filter(elem => this._containsAttributes(meta, elem))[0];\n      if (elem !== undefined) return elem;\n    }\n    const element = this._dom.createElement('meta');\n    this._setMetaElementAttributes(meta, element);\n    const head = this._doc.getElementsByTagName('head')[0];\n    head.appendChild(element);\n    return element;\n  }\n  _setMetaElementAttributes(tag, el) {\n    Object.keys(tag).forEach(prop => el.setAttribute(this._getMetaKeyMap(prop), tag[prop]));\n    return el;\n  }\n  _parseSelector(tag) {\n    const attr = tag.name ? 'name' : 'property';\n    return `${attr}=\"${tag[attr]}\"`;\n  }\n  _containsAttributes(tag, elem) {\n    return Object.keys(tag).every(key => elem.getAttribute(this._getMetaKeyMap(key)) === tag[key]);\n  }\n  _getMetaKeyMap(prop) {\n    return META_KEYS_MAP[prop] || prop;\n  }\n  static {\n    this.ɵfac = function Meta_Factory(t) {\n      return new (t || Meta)(i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: Meta,\n      factory: Meta.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Meta, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n/**\n * Mapping for MetaDefinition properties with their correct meta attribute names\n */\nconst META_KEYS_MAP = {\n  httpEquiv: 'http-equiv'\n};\n\n/**\n * A service that can be used to get and set the title of a current HTML document.\n *\n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n *\n * @publicApi\n */\nclass Title {\n  constructor(_doc) {\n    this._doc = _doc;\n  }\n  /**\n   * Get the title of the current HTML document.\n   */\n  getTitle() {\n    return this._doc.title;\n  }\n  /**\n   * Set the title of the current HTML document.\n   * @param newTitle\n   */\n  setTitle(newTitle) {\n    this._doc.title = newTitle || '';\n  }\n  static {\n    this.ɵfac = function Title_Factory(t) {\n      return new (t || Title)(i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: Title,\n      factory: Title.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Title, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param value The value to export.\n */\nfunction exportNgVar(name, value) {\n  if (typeof COMPILED === 'undefined' || !COMPILED) {\n    // Note: we can't export `ng` when using closure enhanced optimization as:\n    // - closure declares globals itself for minified names, which sometimes clobber our `ng` global\n    // - we can't declare a closure extern as the namespace `ng` is already used within Google\n    //   for typings for angularJS (via `goog.provide('ng....')`).\n    const ng = ɵglobal['ng'] = ɵglobal['ng'] || {};\n    ng[name] = value;\n  }\n}\nclass ChangeDetectionPerfRecord {\n  constructor(msPerTick, numTicks) {\n    this.msPerTick = msPerTick;\n    this.numTicks = numTicks;\n  }\n}\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\nclass AngularProfiler {\n  constructor(ref) {\n    this.appRef = ref.injector.get(ApplicationRef);\n  }\n  // tslint:disable:no-console\n  /**\n   * Exercises change detection in a loop and then prints the average amount of\n   * time in milliseconds how long a single round of change detection takes for\n   * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n   * of 500 milliseconds.\n   *\n   * Optionally, a user may pass a `config` parameter containing a map of\n   * options. Supported options are:\n   *\n   * `record` (boolean) - causes the profiler to record a CPU profile while\n   * it exercises the change detector. Example:\n   *\n   * ```\n   * ng.profiler.timeChangeDetection({record: true})\n   * ```\n   */\n  timeChangeDetection(config) {\n    const record = config && config['record'];\n    const profileName = 'Change Detection';\n    // Profiler is not available in Android browsers without dev tools opened\n    if (record && 'profile' in console && typeof console.profile === 'function') {\n      console.profile(profileName);\n    }\n    const start = performance.now();\n    let numTicks = 0;\n    while (numTicks < 5 || performance.now() - start < 500) {\n      this.appRef.tick();\n      numTicks++;\n    }\n    const end = performance.now();\n    if (record && 'profileEnd' in console && typeof console.profileEnd === 'function') {\n      console.profileEnd(profileName);\n    }\n    const msPerTick = (end - start) / numTicks;\n    console.log(`ran ${numTicks} change detection cycles`);\n    console.log(`${msPerTick.toFixed(2)} ms per check`);\n    return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n  }\n}\nconst PROFILER_GLOBAL_NAME = 'profiler';\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n *\n * Usage:\n *\n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n *\n * @publicApi\n */\nfunction enableDebugTools(ref) {\n  exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n  return ref;\n}\n/**\n * Disables Angular tools.\n *\n * @publicApi\n */\nfunction disableDebugTools() {\n  exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\n\n/**\n * Predicates for use with {@link DebugElement}'s query functions.\n *\n * @publicApi\n */\nclass By {\n  /**\n   * Match all nodes.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n   */\n  static all() {\n    return () => true;\n  }\n  /**\n   * Match elements by the given CSS selector.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n   */\n  static css(selector) {\n    return debugElement => {\n      return debugElement.nativeElement != null ? elementMatches(debugElement.nativeElement, selector) : false;\n    };\n  }\n  /**\n   * Match nodes that have the given directive present.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n   */\n  static directive(type) {\n    return debugNode => debugNode.providerTokens.indexOf(type) !== -1;\n  }\n}\nfunction elementMatches(n, selector) {\n  if (ɵgetDOM().isElementNode(n)) {\n    return n.matches && n.matches(selector) || n.msMatchesSelector && n.msMatchesSelector(selector) || n.webkitMatchesSelector && n.webkitMatchesSelector(selector);\n  }\n  return false;\n}\n\n/**\n * Supported HammerJS recognizer event names.\n */\nconst EVENT_NAMES = {\n  // pan\n  'pan': true,\n  'panstart': true,\n  'panmove': true,\n  'panend': true,\n  'pancancel': true,\n  'panleft': true,\n  'panright': true,\n  'panup': true,\n  'pandown': true,\n  // pinch\n  'pinch': true,\n  'pinchstart': true,\n  'pinchmove': true,\n  'pinchend': true,\n  'pinchcancel': true,\n  'pinchin': true,\n  'pinchout': true,\n  // press\n  'press': true,\n  'pressup': true,\n  // rotate\n  'rotate': true,\n  'rotatestart': true,\n  'rotatemove': true,\n  'rotateend': true,\n  'rotatecancel': true,\n  // swipe\n  'swipe': true,\n  'swipeleft': true,\n  'swiperight': true,\n  'swipeup': true,\n  'swipedown': true,\n  // tap\n  'tap': true,\n  'doubletap': true\n};\n/**\n * DI token for providing [HammerJS](https://hammerjs.github.io/) support to Angular.\n * @see {@link HammerGestureConfig}\n *\n * @ngModule HammerModule\n * @publicApi\n */\nconst HAMMER_GESTURE_CONFIG = new InjectionToken('HammerGestureConfig');\n/**\n * Injection token used to provide a {@link HammerLoader} to Angular.\n *\n * @publicApi\n */\nconst HAMMER_LOADER = new InjectionToken('HammerLoader');\n/**\n * An injectable [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n * for gesture recognition. Configures specific event recognition.\n * @publicApi\n */\nclass HammerGestureConfig {\n  constructor() {\n    /**\n     * A set of supported event names for gestures to be used in Angular.\n     * Angular supports all built-in recognizers, as listed in\n     * [HammerJS documentation](https://hammerjs.github.io/).\n     */\n    this.events = [];\n    /**\n     * Maps gesture event names to a set of configuration options\n     * that specify overrides to the default values for specific properties.\n     *\n     * The key is a supported event name to be configured,\n     * and the options object contains a set of properties, with override values\n     * to be applied to the named recognizer event.\n     * For example, to disable recognition of the rotate event, specify\n     *  `{\"rotate\": {\"enable\": false}}`.\n     *\n     * Properties that are not present take the HammerJS default values.\n     * For information about which properties are supported for which events,\n     * and their allowed and default values, see\n     * [HammerJS documentation](https://hammerjs.github.io/).\n     *\n     */\n    this.overrides = {};\n  }\n  /**\n   * Creates a [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n   * and attaches it to a given HTML element.\n   * @param element The element that will recognize gestures.\n   * @returns A HammerJS event-manager object.\n   */\n  buildHammer(element) {\n    const mc = new Hammer(element, this.options);\n    mc.get('pinch').set({\n      enable: true\n    });\n    mc.get('rotate').set({\n      enable: true\n    });\n    for (const eventName in this.overrides) {\n      mc.get(eventName).set(this.overrides[eventName]);\n    }\n    return mc;\n  }\n  static {\n    this.ɵfac = function HammerGestureConfig_Factory(t) {\n      return new (t || HammerGestureConfig)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: HammerGestureConfig,\n      factory: HammerGestureConfig.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerGestureConfig, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n * Event plugin that adds Hammer support to an application.\n *\n * @ngModule HammerModule\n */\nclass HammerGesturesPlugin extends EventManagerPlugin {\n  constructor(doc, _config, console, loader) {\n    super(doc);\n    this._config = _config;\n    this.console = console;\n    this.loader = loader;\n    this._loaderPromise = null;\n  }\n  supports(eventName) {\n    if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n      return false;\n    }\n    if (!window.Hammer && !this.loader) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        this.console.warn(`The \"${eventName}\" event cannot be bound because Hammer.JS is not ` + `loaded and no custom loader has been specified.`);\n      }\n      return false;\n    }\n    return true;\n  }\n  addEventListener(element, eventName, handler) {\n    const zone = this.manager.getZone();\n    eventName = eventName.toLowerCase();\n    // If Hammer is not present but a loader is specified, we defer adding the event listener\n    // until Hammer is loaded.\n    if (!window.Hammer && this.loader) {\n      this._loaderPromise = this._loaderPromise || zone.runOutsideAngular(() => this.loader());\n      // This `addEventListener` method returns a function to remove the added listener.\n      // Until Hammer is loaded, the returned function needs to *cancel* the registration rather\n      // than remove anything.\n      let cancelRegistration = false;\n      let deregister = () => {\n        cancelRegistration = true;\n      };\n      zone.runOutsideAngular(() => this._loaderPromise.then(() => {\n        // If Hammer isn't actually loaded when the custom loader resolves, give up.\n        if (!window.Hammer) {\n          if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            this.console.warn(`The custom HAMMER_LOADER completed, but Hammer.JS is not present.`);\n          }\n          deregister = () => {};\n          return;\n        }\n        if (!cancelRegistration) {\n          // Now that Hammer is loaded and the listener is being loaded for real,\n          // the deregistration function changes from canceling registration to\n          // removal.\n          deregister = this.addEventListener(element, eventName, handler);\n        }\n      }).catch(() => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          this.console.warn(`The \"${eventName}\" event cannot be bound because the custom ` + `Hammer.JS loader failed.`);\n        }\n        deregister = () => {};\n      }));\n      // Return a function that *executes* `deregister` (and not `deregister` itself) so that we\n      // can change the behavior of `deregister` once the listener is added. Using a closure in\n      // this way allows us to avoid any additional data structures to track listener removal.\n      return () => {\n        deregister();\n      };\n    }\n    return zone.runOutsideAngular(() => {\n      // Creating the manager bind events, must be done outside of angular\n      const mc = this._config.buildHammer(element);\n      const callback = function (eventObj) {\n        zone.runGuarded(function () {\n          handler(eventObj);\n        });\n      };\n      mc.on(eventName, callback);\n      return () => {\n        mc.off(eventName, callback);\n        // destroy mc to prevent memory leak\n        if (typeof mc.destroy === 'function') {\n          mc.destroy();\n        }\n      };\n    });\n  }\n  isCustomEvent(eventName) {\n    return this._config.events.indexOf(eventName) > -1;\n  }\n  static {\n    this.ɵfac = function HammerGesturesPlugin_Factory(t) {\n      return new (t || HammerGesturesPlugin)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(HAMMER_GESTURE_CONFIG), i0.ɵɵinject(i0.ɵConsole), i0.ɵɵinject(HAMMER_LOADER, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: HammerGesturesPlugin,\n      factory: HammerGesturesPlugin.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerGesturesPlugin, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: HammerGestureConfig,\n    decorators: [{\n      type: Inject,\n      args: [HAMMER_GESTURE_CONFIG]\n    }]\n  }, {\n    type: i0.ɵConsole\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [HAMMER_LOADER]\n    }]\n  }], null);\n})();\n/**\n * Adds support for HammerJS.\n *\n * Import this module at the root of your application so that Angular can work with\n * HammerJS to detect gesture events.\n *\n * Note that applications still need to include the HammerJS script itself. This module\n * simply sets up the coordination layer between HammerJS and Angular's `EventManager`.\n *\n * @publicApi\n */\nclass HammerModule {\n  static {\n    this.ɵfac = function HammerModule_Factory(t) {\n      return new (t || HammerModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: HammerModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [{\n        provide: EVENT_MANAGER_PLUGINS,\n        useClass: HammerGesturesPlugin,\n        multi: true,\n        deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n      }, {\n        provide: HAMMER_GESTURE_CONFIG,\n        useClass: HammerGestureConfig,\n        deps: []\n      }]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerModule, [{\n    type: NgModule,\n    args: [{\n      providers: [{\n        provide: EVENT_MANAGER_PLUGINS,\n        useClass: HammerGesturesPlugin,\n        multi: true,\n        deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n      }, {\n        provide: HAMMER_GESTURE_CONFIG,\n        useClass: HammerGestureConfig,\n        deps: []\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n *\n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n *\n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n *\n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n *\n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n *\n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n *\n * @security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](https://g.co/ng/security).\n *\n * @publicApi\n */\nclass DomSanitizer {\n  static {\n    this.ɵfac = function DomSanitizer_Factory(t) {\n      return new (t || DomSanitizer)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DomSanitizer,\n      factory: function DomSanitizer_Factory(t) {\n        let r = null;\n        if (t) {\n          r = new (t || DomSanitizer)();\n        } else {\n          r = i0.ɵɵinject(DomSanitizerImpl);\n        }\n        return r;\n      },\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSanitizer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useExisting: forwardRef(() => DomSanitizerImpl)\n    }]\n  }], null, null);\n})();\nclass DomSanitizerImpl extends DomSanitizer {\n  constructor(_doc) {\n    super();\n    this._doc = _doc;\n  }\n  sanitize(ctx, value) {\n    if (value == null) return null;\n    switch (ctx) {\n      case SecurityContext.NONE:\n        return value;\n      case SecurityContext.HTML:\n        if (ɵallowSanitizationBypassAndThrow(value, \"HTML\" /* BypassType.Html */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        return ɵ_sanitizeHtml(this._doc, String(value)).toString();\n      case SecurityContext.STYLE:\n        if (ɵallowSanitizationBypassAndThrow(value, \"Style\" /* BypassType.Style */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        return value;\n      case SecurityContext.SCRIPT:\n        if (ɵallowSanitizationBypassAndThrow(value, \"Script\" /* BypassType.Script */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        throw new ɵRuntimeError(5200 /* RuntimeErrorCode.SANITIZATION_UNSAFE_SCRIPT */, (typeof ngDevMode === 'undefined' || ngDevMode) && 'unsafe value used in a script context');\n      case SecurityContext.URL:\n        if (ɵallowSanitizationBypassAndThrow(value, \"URL\" /* BypassType.Url */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        return ɵ_sanitizeUrl(String(value));\n      case SecurityContext.RESOURCE_URL:\n        if (ɵallowSanitizationBypassAndThrow(value, \"ResourceURL\" /* BypassType.ResourceUrl */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        throw new ɵRuntimeError(5201 /* RuntimeErrorCode.SANITIZATION_UNSAFE_RESOURCE_URL */, (typeof ngDevMode === 'undefined' || ngDevMode) && `unsafe value used in a resource URL context (see ${ɵXSS_SECURITY_URL})`);\n      default:\n        throw new ɵRuntimeError(5202 /* RuntimeErrorCode.SANITIZATION_UNEXPECTED_CTX */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Unexpected SecurityContext ${ctx} (see ${ɵXSS_SECURITY_URL})`);\n    }\n  }\n  bypassSecurityTrustHtml(value) {\n    return ɵbypassSanitizationTrustHtml(value);\n  }\n  bypassSecurityTrustStyle(value) {\n    return ɵbypassSanitizationTrustStyle(value);\n  }\n  bypassSecurityTrustScript(value) {\n    return ɵbypassSanitizationTrustScript(value);\n  }\n  bypassSecurityTrustUrl(value) {\n    return ɵbypassSanitizationTrustUrl(value);\n  }\n  bypassSecurityTrustResourceUrl(value) {\n    return ɵbypassSanitizationTrustResourceUrl(value);\n  }\n  static {\n    this.ɵfac = function DomSanitizerImpl_Factory(t) {\n      return new (t || DomSanitizerImpl)(i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DomSanitizerImpl,\n      factory: DomSanitizerImpl.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSanitizerImpl, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * The list of features as an enum to uniquely type each `HydrationFeature`.\n * @see {@link HydrationFeature}\n *\n * @publicApi\n */\nvar HydrationFeatureKind;\n(function (HydrationFeatureKind) {\n  HydrationFeatureKind[HydrationFeatureKind[\"NoHttpTransferCache\"] = 0] = \"NoHttpTransferCache\";\n  HydrationFeatureKind[HydrationFeatureKind[\"HttpTransferCacheOptions\"] = 1] = \"HttpTransferCacheOptions\";\n})(HydrationFeatureKind || (HydrationFeatureKind = {}));\n/**\n * Helper function to create an object that represents a Hydration feature.\n */\nfunction hydrationFeature(ɵkind, ɵproviders = [], ɵoptions = {}) {\n  return {\n    ɵkind,\n    ɵproviders\n  };\n}\n/**\n * Disables HTTP transfer cache. Effectively causes HTTP requests to be performed twice: once on the\n * server and other one on the browser.\n *\n * @publicApi\n */\nfunction withNoHttpTransferCache() {\n  // This feature has no providers and acts as a flag that turns off\n  // HTTP transfer cache (which otherwise is turned on by default).\n  return hydrationFeature(HydrationFeatureKind.NoHttpTransferCache);\n}\n/**\n * The function accepts a an object, which allows to configure cache parameters,\n * such as which headers should be included (no headers are included by default),\n * wether POST requests should be cached or a callback function to determine if a\n * particular request should be cached.\n *\n * @publicApi\n */\nfunction withHttpTransferCacheOptions(options) {\n  // This feature has no providers and acts as a flag to pass options to the HTTP transfer cache.\n  return hydrationFeature(HydrationFeatureKind.HttpTransferCacheOptions, ɵwithHttpTransferCache(options));\n}\n/**\n * Returns an `ENVIRONMENT_INITIALIZER` token setup with a function\n * that verifies whether compatible ZoneJS was used in an application\n * and logs a warning in a console if it's not the case.\n */\nfunction provideZoneJsCompatibilityDetector() {\n  return [{\n    provide: ENVIRONMENT_INITIALIZER,\n    useValue: () => {\n      const ngZone = inject(NgZone);\n      // Checking `ngZone instanceof NgZone` would be insufficient here,\n      // because custom implementations might use NgZone as a base class.\n      if (ngZone.constructor !== NgZone) {\n        const console = inject(ɵConsole);\n        const message = ɵformatRuntimeError(-5000 /* RuntimeErrorCode.UNSUPPORTED_ZONEJS_INSTANCE */, 'Angular detected that hydration was enabled for an application ' + 'that uses a custom or a noop Zone.js implementation. ' + 'This is not yet a fully supported configuration.');\n        // tslint:disable-next-line:no-console\n        console.warn(message);\n      }\n    },\n    multi: true\n  }];\n}\n/**\n * Sets up providers necessary to enable hydration functionality for the application.\n *\n * By default, the function enables the recommended set of features for the optimal\n * performance for most of the applications. It includes the following features:\n *\n * * Reconciling DOM hydration. Learn more about it [here](guide/hydration).\n * * [`HttpClient`](api/common/http/HttpClient) response caching while running on the server and\n * transferring this cache to the client to avoid extra HTTP requests. Learn more about data caching\n * [here](/guide/ssr#caching-data-when-using-httpclient).\n *\n * These functions allow you to disable some of the default features or configure features\n * * {@link withNoHttpTransferCache} to disable HTTP transfer cache\n * * {@link withHttpTransferCacheOptions} to configure some HTTP transfer cache options\n *\n * @usageNotes\n *\n * Basic example of how you can enable hydration in your application when\n * `bootstrapApplication` function is used:\n * ```\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration()]\n * });\n * ```\n *\n * Alternatively if you are using NgModules, you would add `provideClientHydration`\n * to your root app module's provider list.\n * ```\n * @NgModule({\n *   declarations: [RootCmp],\n *   bootstrap: [RootCmp],\n *   providers: [provideClientHydration()],\n * })\n * export class AppModule {}\n * ```\n *\n * @see {@link withNoHttpTransferCache}\n * @see {@link withHttpTransferCacheOptions}\n *\n * @param features Optional features to configure additional router behaviors.\n * @returns A set of providers to enable hydration.\n *\n * @publicApi\n */\nfunction provideClientHydration(...features) {\n  const providers = [];\n  const featuresKind = new Set();\n  const hasHttpTransferCacheOptions = featuresKind.has(HydrationFeatureKind.HttpTransferCacheOptions);\n  for (const {\n    ɵproviders,\n    ɵkind\n  } of features) {\n    featuresKind.add(ɵkind);\n    if (ɵproviders.length) {\n      providers.push(ɵproviders);\n    }\n  }\n  if (typeof ngDevMode !== 'undefined' && ngDevMode && featuresKind.has(HydrationFeatureKind.NoHttpTransferCache) && hasHttpTransferCacheOptions) {\n    // TODO: Make this a runtime error\n    throw new Error('Configuration error: found both withHttpTransferCacheOptions() and withNoHttpTransferCache() in the same call to provideClientHydration(), which is a contradiction.');\n  }\n  return makeEnvironmentProviders([typeof ngDevMode !== 'undefined' && ngDevMode ? provideZoneJsCompatibilityDetector() : [], ɵwithDomHydration(), featuresKind.has(HydrationFeatureKind.NoHttpTransferCache) || hasHttpTransferCacheOptions ? [] : ɵwithHttpTransferCache({}), providers]);\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('17.3.12');\n\n// Re-export TransferState to the public API of the `platform-browser` for backwards-compatibility.\n/**\n * Create a `StateKey<T>` that can be used to store value of type T with `TransferState`.\n *\n * Example:\n *\n * ```\n * const COUNTER_KEY = makeStateKey<number>('counter');\n * let value = 10;\n *\n * transferState.set(COUNTER_KEY, value);\n * ```\n *\n * @publicApi\n * @deprecated `makeStateKey` has moved, please import `makeStateKey` from `@angular/core` instead.\n */\n// The below is a workaround to add a deprecated message.\nconst makeStateKey = makeStateKey$1;\n// The below type is needed for G3 due to JSC_CONFORMANCE_VIOLATION.\nconst TransferState = TransferState$1;\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserModule, By, DomSanitizer, EVENT_MANAGER_PLUGINS, EventManager, EventManagerPlugin, HAMMER_GESTURE_CONFIG, HAMMER_LOADER, HammerGestureConfig, HammerModule, HydrationFeatureKind, Meta, REMOVE_STYLES_ON_COMPONENT_DESTROY, Title, TransferState, VERSION, bootstrapApplication, createApplication, disableDebugTools, enableDebugTools, makeStateKey, platformBrowser, provideClientHydration, provideProtractorTestingSupport, withHttpTransferCacheOptions, withNoHttpTransferCache, BrowserDomAdapter as ɵBrowserDomAdapter, BrowserGetTestability as ɵBrowserGetTestability, DomEventsPlugin as ɵDomEventsPlugin, DomRendererFactory2 as ɵDomRendererFactory2, DomSanitizerImpl as ɵDomSanitizerImpl, HammerGesturesPlugin as ɵHammerGesturesPlugin, INTERNAL_BROWSER_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS, KeyEventsPlugin as ɵKeyEventsPlugin, SharedStylesHost as ɵSharedStylesHost, initDomAdapter as ɵinitDomAdapter };", "map": {"version": 3, "names": ["i0", "ɵglobal", "ɵRuntimeError", "Injectable", "InjectionToken", "Inject", "APP_ID", "CSP_NONCE", "PLATFORM_ID", "Optional", "ViewEncapsulation", "RendererStyleFlags2", "ɵinternalCreateApplication", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵsetDocument", "PLATFORM_INITIALIZER", "createPlatformFactory", "platformCore", "ɵTESTABILITY_GETTER", "ɵTESTABILITY", "Testability", "NgZone", "TestabilityRegistry", "ɵINJECTOR_SCOPE", "RendererFactory2", "ApplicationModule", "NgModule", "SkipSelf", "ApplicationRef", "ɵConsole", "forwardRef", "ɵXSS_SECURITY_URL", "SecurityContext", "ɵallowSanitizationBypassAndThrow", "ɵunwrapSafeValue", "ɵ_sanitizeUrl", "ɵ_sanitizeHtml", "ɵbypassSanitizationTrustHtml", "ɵbypassSanitizationTrustStyle", "ɵbypassSanitizationTrustScript", "ɵbypassSanitizationTrustUrl", "ɵbypassSanitizationTrustResourceUrl", "ENVIRONMENT_INITIALIZER", "inject", "ɵformatRuntimeError", "makeEnvironmentProviders", "ɵwithDomHydration", "Version", "makeStateKey", "makeStateKey$1", "TransferState", "TransferState$1", "ɵDomAdapter", "ɵsetRootDomAdapter", "ɵparseCookieValue", "ɵgetDOM", "isPlatformServer", "DOCUMENT", "ɵPLATFORM_BROWSER_ID", "XhrFactory", "CommonModule", "ɵwithHttpTransferCache", "GenericBrowserDomAdapter", "constructor", "arguments", "supportsDOMEvents", "BrowserDomAdapter", "makeCurrent", "onAndCancel", "el", "evt", "listener", "addEventListener", "removeEventListener", "dispatchEvent", "remove", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "tagName", "doc", "getDefaultDocument", "createHtmlDocument", "document", "implementation", "createHTMLDocument", "isElementNode", "nodeType", "Node", "ELEMENT_NODE", "isShadowRoot", "DocumentFragment", "getGlobalEventTarget", "target", "window", "body", "getBaseHref", "href", "getBaseElementHref", "relativePath", "resetBaseElement", "baseElement", "getUserAgent", "navigator", "userAgent", "<PERSON><PERSON><PERSON><PERSON>", "name", "cookie", "querySelector", "getAttribute", "url", "URL", "baseURI", "pathname", "BrowserGetTestability", "addToWindow", "registry", "elem", "findInAncestors", "testability", "findTestabilityInTree", "ngDevMode", "getAllTestabilities", "getAllRootElements", "whenAllStable", "callback", "testabilities", "count", "length", "decrement", "for<PERSON>ach", "whenStable", "push", "t", "getTestability", "host", "parentElement", "BrowserXhr", "build", "XMLHttpRequest", "ɵfac", "BrowserXhr_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ɵsetClassMetadata", "type", "EVENT_MANAGER_PLUGINS", "EventManager", "plugins", "_zone", "_eventNameToPlugin", "Map", "plugin", "manager", "_plugins", "slice", "reverse", "element", "eventName", "handler", "_findPluginFor", "getZone", "get", "find", "supports", "set", "EventManager_Factory", "ɵɵinject", "undefined", "decorators", "args", "EventManagerPlugin", "_doc", "APP_ID_ATTRIBUTE_NAME", "SharedStylesHost", "appId", "nonce", "platformId", "styleRef", "hostNodes", "Set", "styleNodesInDOM", "collectServerRenderedStyles", "platformIsServer", "resetHostNodes", "addStyles", "styles", "style", "usageCount", "changeUsageCount", "onStyleAdded", "removeStyles", "onStyleRemoved", "ngOnDestroy", "clear", "getAllStyles", "addHost", "hostNode", "add", "addStyleToHost", "removeHost", "delete", "keys", "elements", "head", "querySelectorAll", "styleMap", "textContent", "delta", "map", "has", "styleRefValue", "usage", "getStyleElement", "styleEl", "removeAttribute", "setAttribute", "append<PERSON><PERSON><PERSON>", "styleElRef", "SharedStylesHost_Factory", "Document", "NAMESPACE_URIS", "COMPONENT_REGEX", "COMPONENT_VARIABLE", "HOST_ATTR", "CONTENT_ATTR", "REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT", "REMOVE_STYLES_ON_COMPONENT_DESTROY", "providedIn", "shimContentAttribute", "componentShortId", "replace", "shimHostAttribute", "shim<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "compId", "s", "DomRendererFactory2", "eventManager", "sharedStylesHost", "removeStylesOnCompDestroy", "ngZone", "rendererByCompId", "defaultRenderer", "DefaultDomRenderer2", "<PERSON><PERSON><PERSON><PERSON>", "encapsulation", "ShadowDom", "Emulated", "renderer", "getOr<PERSON><PERSON><PERSON><PERSON><PERSON>", "EmulatedEncapsulationDomRenderer2", "applyToHost", "NoneEncapsulationDomRenderer", "applyStyles", "id", "ShadowDom<PERSON><PERSON><PERSON>", "DomRendererFactory2_Factory", "Object", "data", "create", "throwOnSyntheticProps", "destroyNode", "destroy", "namespace", "createElementNS", "createComment", "value", "createText", "createTextNode", "parent", "<PERSON><PERSON><PERSON><PERSON>", "targetParent", "isTemplateNode", "content", "insertBefore", "refChild", "<PERSON><PERSON><PERSON><PERSON>", "selectRootElement", "selectorOrNode", "preserve<PERSON><PERSON>nt", "nextS<PERSON>ling", "namespaceUri", "setAttributeNS", "removeAttributeNS", "addClass", "classList", "removeClass", "setStyle", "flags", "DashCase", "Important", "setProperty", "removeStyle", "removeProperty", "checkNoSyntheticProp", "setValue", "nodeValue", "listen", "event", "Error", "decoratePreventDefault", "<PERSON><PERSON><PERSON><PERSON>", "allowDefaultBehavior", "runGuarded", "preventDefault", "AT_CHARCODE", "charCodeAt", "<PERSON><PERSON><PERSON>", "hostEl", "component", "shadowRoot", "attachShadow", "mode", "nodeOrShadowRoot", "contentAttr", "hostAttr", "DomEventsPlugin", "DomEventsPlugin_Factory", "MODIFIER_KEYS", "_keyMap", "MODIFIER_KEY_GETTERS", "altKey", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "KeyEventsPlugin", "parseEventName", "parsedEvent", "outsideH<PERSON>ler", "eventCallback", "runOutsideAngular", "parts", "toLowerCase", "split", "domEventName", "shift", "key", "_normalizeKey", "pop", "<PERSON><PERSON><PERSON>", "codeIX", "indexOf", "splice", "modifierName", "index", "result", "matchEventFullKeyCode", "fullKeyCode", "keycode", "code", "modifierGetter", "zone", "keyName", "KeyEventsPlugin_Factory", "bootstrapApplication", "rootComponent", "options", "createProvidersConfig", "createApplication", "appProviders", "BROWSER_MODULE_PROVIDERS", "providers", "platformProviders", "INTERNAL_BROWSER_PLATFORM_PROVIDERS", "provideProtractorTestingSupport", "TESTABILITY_PROVIDERS", "initDomAdapter", "<PERSON><PERSON><PERSON><PERSON>", "_document", "provide", "useValue", "multi", "useFactory", "deps", "platformBrowser", "BROWSER_MODULE_PROVIDERS_MARKER", "useClass", "useExisting", "BrowserModule", "providersAlreadyPresent", "withServerTransition", "params", "ngModule", "BrowserModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "Meta", "_dom", "addTag", "tag", "forceCreation", "_getOrCreateElement", "addTags", "tags", "reduce", "getTag", "attrSelector", "getTags", "list", "call", "updateTag", "selector", "_parseSelector", "meta", "_setMetaElementAttributes", "removeTag", "removeTagElement", "filter", "_containsAttributes", "getElementsByTagName", "prop", "_getMetaKeyMap", "attr", "every", "META_KEYS_MAP", "Meta_Factory", "httpEquiv", "Title", "getTitle", "title", "setTitle", "newTitle", "Title_Factory", "exportNgVar", "COMPILED", "ng", "ChangeDetectionPerfRecord", "msPerTick", "numTicks", "AngularProfiler", "ref", "appRef", "injector", "timeChangeDetection", "config", "record", "profileName", "console", "profile", "start", "performance", "now", "tick", "end", "profileEnd", "log", "toFixed", "PROFILER_GLOBAL_NAME", "enableDebugTools", "disableDebugTools", "By", "all", "css", "debugElement", "nativeElement", "elementMatches", "directive", "debugNode", "providerTokens", "n", "matches", "msMatchesSelector", "webkitMatchesSelector", "EVENT_NAMES", "HAMMER_GESTURE_CONFIG", "HAMMER_LOADER", "HammerGestureConfig", "events", "overrides", "buildHammer", "mc", "Hammer", "enable", "HammerGestureConfig_Factory", "HammerGesturesPlugin", "_config", "loader", "_loaderPromise", "hasOwnProperty", "isCustomEvent", "warn", "cancelRegistration", "deregister", "then", "catch", "eventObj", "on", "off", "HammerGesturesPlugin_Factory", "HammerModule", "HammerModule_Factory", "Dom<PERSON><PERSON><PERSON>zer", "DomSanitizer_Factory", "r", "DomSanitizerImpl", "sanitize", "ctx", "NONE", "HTML", "String", "toString", "STYLE", "SCRIPT", "RESOURCE_URL", "bypassSecurityTrustHtml", "bypassSecurityTrustStyle", "bypassSecurityTrustScript", "bypassSecurityTrustUrl", "bypassSecurityTrustResourceUrl", "DomSanitizerImpl_Factory", "HydrationFeatureKind", "hydrationFeature", "ɵkind", "ɵproviders", "ɵoptions", "withNoHttpTransferCache", "NoHttpTransferCache", "withHttpTransferCacheOptions", "HttpTransferCacheOptions", "provideZoneJsCompatibilityDetector", "message", "provideClientHydration", "features", "featuresKind", "hasHttpTransferCacheOptions", "VERSION", "ɵBrowserDomAdapter", "ɵBrowserGetTestability", "ɵDomEventsPlugin", "ɵDomRendererFactory2", "ɵDomSanitizerImpl", "ɵHammerGesturesPlugin", "ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS", "ɵKeyEventsPlugin", "ɵSharedStylesHost", "ɵinitDomAdapter"], "sources": ["C:/Users/<USER>/Desktop/BookCart/bookcart-frontend/node_modules/@angular/platform-browser/fesm2022/platform-browser.mjs"], "sourcesContent": ["/**\n * @license Angular v17.3.12\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { ɵglobal, ɵRuntimeError, Injectable, InjectionToken, Inject, APP_ID, CSP_NONCE, PLATFORM_ID, Optional, ViewEncapsulation, RendererStyleFlags2, ɵinternalCreateApplication, ErrorHandler, ɵsetDocument, PLATFORM_INITIALIZER, createPlatformFactory, platformCore, ɵTESTABILITY_GETTER, ɵTESTABILITY, Testability, NgZone, TestabilityRegistry, ɵINJECTOR_SCOPE, RendererFactory2, ApplicationModule, NgModule, SkipSelf, ApplicationRef, ɵConsole, forwardRef, ɵXSS_SECURITY_URL, SecurityContext, ɵallowSanitizationBypassAndThrow, ɵunwrapSafeValue, ɵ_sanitizeUrl, ɵ_sanitizeHtml, ɵbypassSanitizationTrustHtml, ɵbypassSanitizationTrustStyle, ɵbypassSanitizationTrustScript, ɵbypassSanitizationTrustUrl, ɵbypassSanitizationTrustResourceUrl, ENVIRONMENT_INITIALIZER, inject, ɵformatRuntimeError, makeEnvironmentProviders, ɵwithDomHydration, Version, makeStateKey as makeStateKey$1, TransferState as TransferState$1 } from '@angular/core';\nimport { ɵDomAdapter, ɵsetRootDomAdapter, ɵparseCookieValue, ɵgetDOM, isPlatformServer, DOCUMENT, ɵPLATFORM_BROWSER_ID, XhrFactory, CommonModule } from '@angular/common';\nexport { ɵgetDOM } from '@angular/common';\nimport { ɵwithHttpTransferCache } from '@angular/common/http';\n\n/**\n * Provides DOM operations in any browser environment.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nclass GenericBrowserDomAdapter extends ɵDomAdapter {\n    constructor() {\n        super(...arguments);\n        this.supportsDOMEvents = true;\n    }\n}\n\n/**\n * A `DomAdapter` powered by full browser DOM APIs.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\n/* tslint:disable:requireParameterType no-console */\nclass BrowserDomAdapter extends GenericBrowserDomAdapter {\n    static makeCurrent() {\n        ɵsetRootDomAdapter(new BrowserDomAdapter());\n    }\n    onAndCancel(el, evt, listener) {\n        el.addEventListener(evt, listener);\n        return () => {\n            el.removeEventListener(evt, listener);\n        };\n    }\n    dispatchEvent(el, evt) {\n        el.dispatchEvent(evt);\n    }\n    remove(node) {\n        if (node.parentNode) {\n            node.parentNode.removeChild(node);\n        }\n    }\n    createElement(tagName, doc) {\n        doc = doc || this.getDefaultDocument();\n        return doc.createElement(tagName);\n    }\n    createHtmlDocument() {\n        return document.implementation.createHTMLDocument('fakeTitle');\n    }\n    getDefaultDocument() {\n        return document;\n    }\n    isElementNode(node) {\n        return node.nodeType === Node.ELEMENT_NODE;\n    }\n    isShadowRoot(node) {\n        return node instanceof DocumentFragment;\n    }\n    /** @deprecated No longer being used in Ivy code. To be removed in version 14. */\n    getGlobalEventTarget(doc, target) {\n        if (target === 'window') {\n            return window;\n        }\n        if (target === 'document') {\n            return doc;\n        }\n        if (target === 'body') {\n            return doc.body;\n        }\n        return null;\n    }\n    getBaseHref(doc) {\n        const href = getBaseElementHref();\n        return href == null ? null : relativePath(href);\n    }\n    resetBaseElement() {\n        baseElement = null;\n    }\n    getUserAgent() {\n        return window.navigator.userAgent;\n    }\n    getCookie(name) {\n        return ɵparseCookieValue(document.cookie, name);\n    }\n}\nlet baseElement = null;\nfunction getBaseElementHref() {\n    baseElement = baseElement || document.querySelector('base');\n    return baseElement ? baseElement.getAttribute('href') : null;\n}\nfunction relativePath(url) {\n    // The base URL doesn't really matter, we just need it so relative paths have something\n    // to resolve against. In the browser `HTMLBaseElement.href` is always absolute.\n    return new URL(url, document.baseURI).pathname;\n}\n\nclass BrowserGetTestability {\n    addToWindow(registry) {\n        ɵglobal['getAngularTestability'] = (elem, findInAncestors = true) => {\n            const testability = registry.findTestabilityInTree(elem, findInAncestors);\n            if (testability == null) {\n                throw new ɵRuntimeError(5103 /* RuntimeErrorCode.TESTABILITY_NOT_FOUND */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    'Could not find testability for element.');\n            }\n            return testability;\n        };\n        ɵglobal['getAllAngularTestabilities'] = () => registry.getAllTestabilities();\n        ɵglobal['getAllAngularRootElements'] = () => registry.getAllRootElements();\n        const whenAllStable = (callback) => {\n            const testabilities = ɵglobal['getAllAngularTestabilities']();\n            let count = testabilities.length;\n            const decrement = function () {\n                count--;\n                if (count == 0) {\n                    callback();\n                }\n            };\n            testabilities.forEach((testability) => {\n                testability.whenStable(decrement);\n            });\n        };\n        if (!ɵglobal['frameworkStabilizers']) {\n            ɵglobal['frameworkStabilizers'] = [];\n        }\n        ɵglobal['frameworkStabilizers'].push(whenAllStable);\n    }\n    findTestabilityInTree(registry, elem, findInAncestors) {\n        if (elem == null) {\n            return null;\n        }\n        const t = registry.getTestability(elem);\n        if (t != null) {\n            return t;\n        }\n        else if (!findInAncestors) {\n            return null;\n        }\n        if (ɵgetDOM().isShadowRoot(elem)) {\n            return this.findTestabilityInTree(registry, elem.host, true);\n        }\n        return this.findTestabilityInTree(registry, elem.parentElement, true);\n    }\n}\n\n/**\n * A factory for `HttpXhrBackend` that uses the `XMLHttpRequest` browser API.\n */\nclass BrowserXhr {\n    build() {\n        return new XMLHttpRequest();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: BrowserXhr, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: BrowserXhr }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: BrowserXhr, decorators: [{\n            type: Injectable\n        }] });\n\n/**\n * The injection token for plugins of the `EventManager` service.\n *\n * @publicApi\n */\nconst EVENT_MANAGER_PLUGINS = new InjectionToken(ngDevMode ? 'EventManagerPlugins' : '');\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\nclass EventManager {\n    /**\n     * Initializes an instance of the event-manager service.\n     */\n    constructor(plugins, _zone) {\n        this._zone = _zone;\n        this._eventNameToPlugin = new Map();\n        plugins.forEach((plugin) => {\n            plugin.manager = this;\n        });\n        this._plugins = plugins.slice().reverse();\n    }\n    /**\n     * Registers a handler for a specific element and event.\n     *\n     * @param element The HTML element to receive event notifications.\n     * @param eventName The name of the event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @returns  A callback function that can be used to remove the handler.\n     */\n    addEventListener(element, eventName, handler) {\n        const plugin = this._findPluginFor(eventName);\n        return plugin.addEventListener(element, eventName, handler);\n    }\n    /**\n     * Retrieves the compilation zone in which event listeners are registered.\n     */\n    getZone() {\n        return this._zone;\n    }\n    /** @internal */\n    _findPluginFor(eventName) {\n        let plugin = this._eventNameToPlugin.get(eventName);\n        if (plugin) {\n            return plugin;\n        }\n        const plugins = this._plugins;\n        plugin = plugins.find((plugin) => plugin.supports(eventName));\n        if (!plugin) {\n            throw new ɵRuntimeError(5101 /* RuntimeErrorCode.NO_PLUGIN_FOR_EVENT */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                `No event manager plugin found for event ${eventName}`);\n        }\n        this._eventNameToPlugin.set(eventName, plugin);\n        return plugin;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: EventManager, deps: [{ token: EVENT_MANAGER_PLUGINS }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: EventManager }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: EventManager, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [EVENT_MANAGER_PLUGINS]\n                }] }, { type: i0.NgZone }] });\n/**\n * The plugin definition for the `EventManager` class\n *\n * It can be used as a base class to create custom manager plugins, i.e. you can create your own\n * class that extends the `EventManagerPlugin` one.\n *\n * @publicApi\n */\nclass EventManagerPlugin {\n    // TODO: remove (has some usage in G3)\n    constructor(_doc) {\n        this._doc = _doc;\n    }\n}\n\n/** The style elements attribute name used to set value of `APP_ID` token. */\nconst APP_ID_ATTRIBUTE_NAME = 'ng-app-id';\nclass SharedStylesHost {\n    constructor(doc, appId, nonce, platformId = {}) {\n        this.doc = doc;\n        this.appId = appId;\n        this.nonce = nonce;\n        this.platformId = platformId;\n        // Maps all registered host nodes to a list of style nodes that have been added to the host node.\n        this.styleRef = new Map();\n        this.hostNodes = new Set();\n        this.styleNodesInDOM = this.collectServerRenderedStyles();\n        this.platformIsServer = isPlatformServer(platformId);\n        this.resetHostNodes();\n    }\n    addStyles(styles) {\n        for (const style of styles) {\n            const usageCount = this.changeUsageCount(style, 1);\n            if (usageCount === 1) {\n                this.onStyleAdded(style);\n            }\n        }\n    }\n    removeStyles(styles) {\n        for (const style of styles) {\n            const usageCount = this.changeUsageCount(style, -1);\n            if (usageCount <= 0) {\n                this.onStyleRemoved(style);\n            }\n        }\n    }\n    ngOnDestroy() {\n        const styleNodesInDOM = this.styleNodesInDOM;\n        if (styleNodesInDOM) {\n            styleNodesInDOM.forEach((node) => node.remove());\n            styleNodesInDOM.clear();\n        }\n        for (const style of this.getAllStyles()) {\n            this.onStyleRemoved(style);\n        }\n        this.resetHostNodes();\n    }\n    addHost(hostNode) {\n        this.hostNodes.add(hostNode);\n        for (const style of this.getAllStyles()) {\n            this.addStyleToHost(hostNode, style);\n        }\n    }\n    removeHost(hostNode) {\n        this.hostNodes.delete(hostNode);\n    }\n    getAllStyles() {\n        return this.styleRef.keys();\n    }\n    onStyleAdded(style) {\n        for (const host of this.hostNodes) {\n            this.addStyleToHost(host, style);\n        }\n    }\n    onStyleRemoved(style) {\n        const styleRef = this.styleRef;\n        styleRef.get(style)?.elements?.forEach((node) => node.remove());\n        styleRef.delete(style);\n    }\n    collectServerRenderedStyles() {\n        const styles = this.doc.head?.querySelectorAll(`style[${APP_ID_ATTRIBUTE_NAME}=\"${this.appId}\"]`);\n        if (styles?.length) {\n            const styleMap = new Map();\n            styles.forEach((style) => {\n                if (style.textContent != null) {\n                    styleMap.set(style.textContent, style);\n                }\n            });\n            return styleMap;\n        }\n        return null;\n    }\n    changeUsageCount(style, delta) {\n        const map = this.styleRef;\n        if (map.has(style)) {\n            const styleRefValue = map.get(style);\n            styleRefValue.usage += delta;\n            return styleRefValue.usage;\n        }\n        map.set(style, { usage: delta, elements: [] });\n        return delta;\n    }\n    getStyleElement(host, style) {\n        const styleNodesInDOM = this.styleNodesInDOM;\n        const styleEl = styleNodesInDOM?.get(style);\n        if (styleEl?.parentNode === host) {\n            // `styleNodesInDOM` cannot be undefined due to the above `styleNodesInDOM?.get`.\n            styleNodesInDOM.delete(style);\n            styleEl.removeAttribute(APP_ID_ATTRIBUTE_NAME);\n            if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                // This attribute is solely used for debugging purposes.\n                styleEl.setAttribute('ng-style-reused', '');\n            }\n            return styleEl;\n        }\n        else {\n            const styleEl = this.doc.createElement('style');\n            if (this.nonce) {\n                styleEl.setAttribute('nonce', this.nonce);\n            }\n            styleEl.textContent = style;\n            if (this.platformIsServer) {\n                styleEl.setAttribute(APP_ID_ATTRIBUTE_NAME, this.appId);\n            }\n            host.appendChild(styleEl);\n            return styleEl;\n        }\n    }\n    addStyleToHost(host, style) {\n        const styleEl = this.getStyleElement(host, style);\n        const styleRef = this.styleRef;\n        const styleElRef = styleRef.get(style)?.elements;\n        if (styleElRef) {\n            styleElRef.push(styleEl);\n        }\n        else {\n            styleRef.set(style, { elements: [styleEl], usage: 1 });\n        }\n    }\n    resetHostNodes() {\n        const hostNodes = this.hostNodes;\n        hostNodes.clear();\n        // Re-add the head element back since this is the default host.\n        hostNodes.add(this.doc.head);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: SharedStylesHost, deps: [{ token: DOCUMENT }, { token: APP_ID }, { token: CSP_NONCE, optional: true }, { token: PLATFORM_ID }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: SharedStylesHost }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: SharedStylesHost, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [APP_ID]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CSP_NONCE]\n                }, {\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }] });\n\nconst NAMESPACE_URIS = {\n    'svg': 'http://www.w3.org/2000/svg',\n    'xhtml': 'http://www.w3.org/1999/xhtml',\n    'xlink': 'http://www.w3.org/1999/xlink',\n    'xml': 'http://www.w3.org/XML/1998/namespace',\n    'xmlns': 'http://www.w3.org/2000/xmlns/',\n    'math': 'http://www.w3.org/1998/MathML/',\n};\nconst COMPONENT_REGEX = /%COMP%/g;\nconst COMPONENT_VARIABLE = '%COMP%';\nconst HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nconst CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n/**\n * The default value for the `REMOVE_STYLES_ON_COMPONENT_DESTROY` DI token.\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT = true;\n/**\n * A [DI token](guide/glossary#di-token \"DI token definition\") that indicates whether styles\n * of destroyed components should be removed from DOM.\n *\n * By default, the value is set to `true`.\n * @publicApi\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY = new InjectionToken(ngDevMode ? 'RemoveStylesOnCompDestroy' : '', {\n    providedIn: 'root',\n    factory: () => REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT,\n});\nfunction shimContentAttribute(componentShortId) {\n    return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimHostAttribute(componentShortId) {\n    return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimStylesContent(compId, styles) {\n    return styles.map(s => s.replace(COMPONENT_REGEX, compId));\n}\nclass DomRendererFactory2 {\n    constructor(eventManager, sharedStylesHost, appId, removeStylesOnCompDestroy, doc, platformId, ngZone, nonce = null) {\n        this.eventManager = eventManager;\n        this.sharedStylesHost = sharedStylesHost;\n        this.appId = appId;\n        this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n        this.doc = doc;\n        this.platformId = platformId;\n        this.ngZone = ngZone;\n        this.nonce = nonce;\n        this.rendererByCompId = new Map();\n        this.platformIsServer = isPlatformServer(platformId);\n        this.defaultRenderer =\n            new DefaultDomRenderer2(eventManager, doc, ngZone, this.platformIsServer);\n    }\n    createRenderer(element, type) {\n        if (!element || !type) {\n            return this.defaultRenderer;\n        }\n        if (this.platformIsServer && type.encapsulation === ViewEncapsulation.ShadowDom) {\n            // Domino does not support shadow DOM.\n            type = { ...type, encapsulation: ViewEncapsulation.Emulated };\n        }\n        const renderer = this.getOrCreateRenderer(element, type);\n        // Renderers have different logic due to different encapsulation behaviours.\n        // Ex: for emulated, an attribute is added to the element.\n        if (renderer instanceof EmulatedEncapsulationDomRenderer2) {\n            renderer.applyToHost(element);\n        }\n        else if (renderer instanceof NoneEncapsulationDomRenderer) {\n            renderer.applyStyles();\n        }\n        return renderer;\n    }\n    getOrCreateRenderer(element, type) {\n        const rendererByCompId = this.rendererByCompId;\n        let renderer = rendererByCompId.get(type.id);\n        if (!renderer) {\n            const doc = this.doc;\n            const ngZone = this.ngZone;\n            const eventManager = this.eventManager;\n            const sharedStylesHost = this.sharedStylesHost;\n            const removeStylesOnCompDestroy = this.removeStylesOnCompDestroy;\n            const platformIsServer = this.platformIsServer;\n            switch (type.encapsulation) {\n                case ViewEncapsulation.Emulated:\n                    renderer = new EmulatedEncapsulationDomRenderer2(eventManager, sharedStylesHost, type, this.appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer);\n                    break;\n                case ViewEncapsulation.ShadowDom:\n                    return new ShadowDomRenderer(eventManager, sharedStylesHost, element, type, doc, ngZone, this.nonce, platformIsServer);\n                default:\n                    renderer = new NoneEncapsulationDomRenderer(eventManager, sharedStylesHost, type, removeStylesOnCompDestroy, doc, ngZone, platformIsServer);\n                    break;\n            }\n            rendererByCompId.set(type.id, renderer);\n        }\n        return renderer;\n    }\n    ngOnDestroy() {\n        this.rendererByCompId.clear();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: DomRendererFactory2, deps: [{ token: EventManager }, { token: SharedStylesHost }, { token: APP_ID }, { token: REMOVE_STYLES_ON_COMPONENT_DESTROY }, { token: DOCUMENT }, { token: PLATFORM_ID }, { token: i0.NgZone }, { token: CSP_NONCE }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: DomRendererFactory2 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: DomRendererFactory2, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: EventManager }, { type: SharedStylesHost }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [APP_ID]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [REMOVE_STYLES_ON_COMPONENT_DESTROY]\n                }] }, { type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: Object, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CSP_NONCE]\n                }] }] });\nclass DefaultDomRenderer2 {\n    constructor(eventManager, doc, ngZone, platformIsServer) {\n        this.eventManager = eventManager;\n        this.doc = doc;\n        this.ngZone = ngZone;\n        this.platformIsServer = platformIsServer;\n        this.data = Object.create(null);\n        /**\n         * By default this renderer throws when encountering synthetic properties\n         * This can be disabled for example by the AsyncAnimationRendererFactory\n         */\n        this.throwOnSyntheticProps = true;\n        this.destroyNode = null;\n    }\n    destroy() { }\n    createElement(name, namespace) {\n        if (namespace) {\n            // TODO: `|| namespace` was added in\n            // https://github.com/angular/angular/commit/2b9cc8503d48173492c29f5a271b61126104fbdb to\n            // support how Ivy passed around the namespace URI rather than short name at the time. It did\n            // not, however extend the support to other parts of the system (setAttribute, setAttribute,\n            // and the ServerRenderer). We should decide what exactly the semantics for dealing with\n            // namespaces should be and make it consistent.\n            // Related issues:\n            // https://github.com/angular/angular/issues/44028\n            // https://github.com/angular/angular/issues/44883\n            return this.doc.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n        }\n        return this.doc.createElement(name);\n    }\n    createComment(value) {\n        return this.doc.createComment(value);\n    }\n    createText(value) {\n        return this.doc.createTextNode(value);\n    }\n    appendChild(parent, newChild) {\n        const targetParent = isTemplateNode(parent) ? parent.content : parent;\n        targetParent.appendChild(newChild);\n    }\n    insertBefore(parent, newChild, refChild) {\n        if (parent) {\n            const targetParent = isTemplateNode(parent) ? parent.content : parent;\n            targetParent.insertBefore(newChild, refChild);\n        }\n    }\n    removeChild(parent, oldChild) {\n        if (parent) {\n            parent.removeChild(oldChild);\n        }\n    }\n    selectRootElement(selectorOrNode, preserveContent) {\n        let el = typeof selectorOrNode === 'string' ? this.doc.querySelector(selectorOrNode) :\n            selectorOrNode;\n        if (!el) {\n            throw new ɵRuntimeError(-5104 /* RuntimeErrorCode.ROOT_NODE_NOT_FOUND */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                `The selector \"${selectorOrNode}\" did not match any elements`);\n        }\n        if (!preserveContent) {\n            el.textContent = '';\n        }\n        return el;\n    }\n    parentNode(node) {\n        return node.parentNode;\n    }\n    nextSibling(node) {\n        return node.nextSibling;\n    }\n    setAttribute(el, name, value, namespace) {\n        if (namespace) {\n            name = namespace + ':' + name;\n            const namespaceUri = NAMESPACE_URIS[namespace];\n            if (namespaceUri) {\n                el.setAttributeNS(namespaceUri, name, value);\n            }\n            else {\n                el.setAttribute(name, value);\n            }\n        }\n        else {\n            el.setAttribute(name, value);\n        }\n    }\n    removeAttribute(el, name, namespace) {\n        if (namespace) {\n            const namespaceUri = NAMESPACE_URIS[namespace];\n            if (namespaceUri) {\n                el.removeAttributeNS(namespaceUri, name);\n            }\n            else {\n                el.removeAttribute(`${namespace}:${name}`);\n            }\n        }\n        else {\n            el.removeAttribute(name);\n        }\n    }\n    addClass(el, name) {\n        el.classList.add(name);\n    }\n    removeClass(el, name) {\n        el.classList.remove(name);\n    }\n    setStyle(el, style, value, flags) {\n        if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n            el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n        }\n        else {\n            el.style[style] = value;\n        }\n    }\n    removeStyle(el, style, flags) {\n        if (flags & RendererStyleFlags2.DashCase) {\n            // removeProperty has no effect when used on camelCased properties.\n            el.style.removeProperty(style);\n        }\n        else {\n            el.style[style] = '';\n        }\n    }\n    setProperty(el, name, value) {\n        if (el == null) {\n            return;\n        }\n        (typeof ngDevMode === 'undefined' || ngDevMode) && this.throwOnSyntheticProps &&\n            checkNoSyntheticProp(name, 'property');\n        el[name] = value;\n    }\n    setValue(node, value) {\n        node.nodeValue = value;\n    }\n    listen(target, event, callback) {\n        (typeof ngDevMode === 'undefined' || ngDevMode) && this.throwOnSyntheticProps &&\n            checkNoSyntheticProp(event, 'listener');\n        if (typeof target === 'string') {\n            target = ɵgetDOM().getGlobalEventTarget(this.doc, target);\n            if (!target) {\n                throw new Error(`Unsupported event target ${target} for event ${event}`);\n            }\n        }\n        return this.eventManager.addEventListener(target, event, this.decoratePreventDefault(callback));\n    }\n    decoratePreventDefault(eventHandler) {\n        // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n        // decoratePreventDefault or is a listener added outside the Angular context so it can handle\n        // the two differently. In the first case, the special '__ngUnwrap__' token is passed to the\n        // unwrap the listener (see below).\n        return (event) => {\n            // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n            // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The\n            // debug_node can inspect the listener toString contents for the existence of this special\n            // token. Because the token is a string literal, it is ensured to not be modified by compiled\n            // code.\n            if (event === '__ngUnwrap__') {\n                return eventHandler;\n            }\n            // Run the event handler inside the ngZone because event handlers are not patched\n            // by Zone on the server. This is required only for tests.\n            const allowDefaultBehavior = this.platformIsServer ?\n                this.ngZone.runGuarded(() => eventHandler(event)) :\n                eventHandler(event);\n            if (allowDefaultBehavior === false) {\n                event.preventDefault();\n            }\n            return undefined;\n        };\n    }\n}\nconst AT_CHARCODE = (() => '@'.charCodeAt(0))();\nfunction checkNoSyntheticProp(name, nameKind) {\n    if (name.charCodeAt(0) === AT_CHARCODE) {\n        throw new ɵRuntimeError(5105 /* RuntimeErrorCode.UNEXPECTED_SYNTHETIC_PROPERTY */, `Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Either \\`BrowserAnimationsModule\\` or \\`NoopAnimationsModule\\` are imported in your application.\n  - There is corresponding configuration for the animation named \\`${name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.io/api/core/Component#animations).`);\n    }\n}\nfunction isTemplateNode(node) {\n    return node.tagName === 'TEMPLATE' && node.content !== undefined;\n}\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n    constructor(eventManager, sharedStylesHost, hostEl, component, doc, ngZone, nonce, platformIsServer) {\n        super(eventManager, doc, ngZone, platformIsServer);\n        this.sharedStylesHost = sharedStylesHost;\n        this.hostEl = hostEl;\n        this.shadowRoot = hostEl.attachShadow({ mode: 'open' });\n        this.sharedStylesHost.addHost(this.shadowRoot);\n        const styles = shimStylesContent(component.id, component.styles);\n        for (const style of styles) {\n            const styleEl = document.createElement('style');\n            if (nonce) {\n                styleEl.setAttribute('nonce', nonce);\n            }\n            styleEl.textContent = style;\n            this.shadowRoot.appendChild(styleEl);\n        }\n    }\n    nodeOrShadowRoot(node) {\n        return node === this.hostEl ? this.shadowRoot : node;\n    }\n    appendChild(parent, newChild) {\n        return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n    }\n    insertBefore(parent, newChild, refChild) {\n        return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n    }\n    removeChild(parent, oldChild) {\n        return super.removeChild(this.nodeOrShadowRoot(parent), oldChild);\n    }\n    parentNode(node) {\n        return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n    }\n    destroy() {\n        this.sharedStylesHost.removeHost(this.shadowRoot);\n    }\n}\nclass NoneEncapsulationDomRenderer extends DefaultDomRenderer2 {\n    constructor(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, compId) {\n        super(eventManager, doc, ngZone, platformIsServer);\n        this.sharedStylesHost = sharedStylesHost;\n        this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n        this.styles = compId ? shimStylesContent(compId, component.styles) : component.styles;\n    }\n    applyStyles() {\n        this.sharedStylesHost.addStyles(this.styles);\n    }\n    destroy() {\n        if (!this.removeStylesOnCompDestroy) {\n            return;\n        }\n        this.sharedStylesHost.removeStyles(this.styles);\n    }\n}\nclass EmulatedEncapsulationDomRenderer2 extends NoneEncapsulationDomRenderer {\n    constructor(eventManager, sharedStylesHost, component, appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer) {\n        const compId = appId + '-' + component.id;\n        super(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, compId);\n        this.contentAttr = shimContentAttribute(compId);\n        this.hostAttr = shimHostAttribute(compId);\n    }\n    applyToHost(element) {\n        this.applyStyles();\n        this.setAttribute(element, this.hostAttr, '');\n    }\n    createElement(parent, name) {\n        const el = super.createElement(parent, name);\n        super.setAttribute(el, this.contentAttr, '');\n        return el;\n    }\n}\n\nclass DomEventsPlugin extends EventManagerPlugin {\n    constructor(doc) {\n        super(doc);\n    }\n    // This plugin should come last in the list of plugins, because it accepts all\n    // events.\n    supports(eventName) {\n        return true;\n    }\n    addEventListener(element, eventName, handler) {\n        element.addEventListener(eventName, handler, false);\n        return () => this.removeEventListener(element, eventName, handler);\n    }\n    removeEventListener(target, eventName, callback) {\n        return target.removeEventListener(eventName, callback);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: DomEventsPlugin, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: DomEventsPlugin }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: DomEventsPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\n/**\n * Defines supported modifiers for key events.\n */\nconst MODIFIER_KEYS = ['alt', 'control', 'meta', 'shift'];\n// The following values are here for cross-browser compatibility and to match the W3C standard\n// cf https://www.w3.org/TR/DOM-Level-3-Events-key/\nconst _keyMap = {\n    '\\b': 'Backspace',\n    '\\t': 'Tab',\n    '\\x7F': 'Delete',\n    '\\x1B': 'Escape',\n    'Del': 'Delete',\n    'Esc': 'Escape',\n    'Left': 'ArrowLeft',\n    'Right': 'ArrowRight',\n    'Up': 'ArrowUp',\n    'Down': 'ArrowDown',\n    'Menu': 'ContextMenu',\n    'Scroll': 'ScrollLock',\n    'Win': 'OS'\n};\n/**\n * Retrieves modifiers from key-event objects.\n */\nconst MODIFIER_KEY_GETTERS = {\n    'alt': (event) => event.altKey,\n    'control': (event) => event.ctrlKey,\n    'meta': (event) => event.metaKey,\n    'shift': (event) => event.shiftKey\n};\n/**\n * A browser plug-in that provides support for handling of key events in Angular.\n */\nclass KeyEventsPlugin extends EventManagerPlugin {\n    /**\n     * Initializes an instance of the browser plug-in.\n     * @param doc The document in which key events will be detected.\n     */\n    constructor(doc) {\n        super(doc);\n    }\n    /**\n     * Reports whether a named key event is supported.\n     * @param eventName The event name to query.\n     * @return True if the named key event is supported.\n     */\n    supports(eventName) {\n        return KeyEventsPlugin.parseEventName(eventName) != null;\n    }\n    /**\n     * Registers a handler for a specific element and key event.\n     * @param element The HTML element to receive event notifications.\n     * @param eventName The name of the key event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @returns The key event that was registered.\n     */\n    addEventListener(element, eventName, handler) {\n        const parsedEvent = KeyEventsPlugin.parseEventName(eventName);\n        const outsideHandler = KeyEventsPlugin.eventCallback(parsedEvent['fullKey'], handler, this.manager.getZone());\n        return this.manager.getZone().runOutsideAngular(() => {\n            return ɵgetDOM().onAndCancel(element, parsedEvent['domEventName'], outsideHandler);\n        });\n    }\n    /**\n     * Parses the user provided full keyboard event definition and normalizes it for\n     * later internal use. It ensures the string is all lowercase, converts special\n     * characters to a standard spelling, and orders all the values consistently.\n     *\n     * @param eventName The name of the key event to listen for.\n     * @returns an object with the full, normalized string, and the dom event name\n     * or null in the case when the event doesn't match a keyboard event.\n     */\n    static parseEventName(eventName) {\n        const parts = eventName.toLowerCase().split('.');\n        const domEventName = parts.shift();\n        if ((parts.length === 0) || !(domEventName === 'keydown' || domEventName === 'keyup')) {\n            return null;\n        }\n        const key = KeyEventsPlugin._normalizeKey(parts.pop());\n        let fullKey = '';\n        let codeIX = parts.indexOf('code');\n        if (codeIX > -1) {\n            parts.splice(codeIX, 1);\n            fullKey = 'code.';\n        }\n        MODIFIER_KEYS.forEach(modifierName => {\n            const index = parts.indexOf(modifierName);\n            if (index > -1) {\n                parts.splice(index, 1);\n                fullKey += modifierName + '.';\n            }\n        });\n        fullKey += key;\n        if (parts.length != 0 || key.length === 0) {\n            // returning null instead of throwing to let another plugin process the event\n            return null;\n        }\n        // NOTE: Please don't rewrite this as so, as it will break JSCompiler property renaming.\n        //       The code must remain in the `result['domEventName']` form.\n        // return {domEventName, fullKey};\n        const result = {};\n        result['domEventName'] = domEventName;\n        result['fullKey'] = fullKey;\n        return result;\n    }\n    /**\n     * Determines whether the actual keys pressed match the configured key code string.\n     * The `fullKeyCode` event is normalized in the `parseEventName` method when the\n     * event is attached to the DOM during the `addEventListener` call. This is unseen\n     * by the end user and is normalized for internal consistency and parsing.\n     *\n     * @param event The keyboard event.\n     * @param fullKeyCode The normalized user defined expected key event string\n     * @returns boolean.\n     */\n    static matchEventFullKeyCode(event, fullKeyCode) {\n        let keycode = _keyMap[event.key] || event.key;\n        let key = '';\n        if (fullKeyCode.indexOf('code.') > -1) {\n            keycode = event.code;\n            key = 'code.';\n        }\n        // the keycode could be unidentified so we have to check here\n        if (keycode == null || !keycode)\n            return false;\n        keycode = keycode.toLowerCase();\n        if (keycode === ' ') {\n            keycode = 'space'; // for readability\n        }\n        else if (keycode === '.') {\n            keycode = 'dot'; // because '.' is used as a separator in event names\n        }\n        MODIFIER_KEYS.forEach(modifierName => {\n            if (modifierName !== keycode) {\n                const modifierGetter = MODIFIER_KEY_GETTERS[modifierName];\n                if (modifierGetter(event)) {\n                    key += modifierName + '.';\n                }\n            }\n        });\n        key += keycode;\n        return key === fullKeyCode;\n    }\n    /**\n     * Configures a handler callback for a key event.\n     * @param fullKey The event name that combines all simultaneous keystrokes.\n     * @param handler The function that responds to the key event.\n     * @param zone The zone in which the event occurred.\n     * @returns A callback function.\n     */\n    static eventCallback(fullKey, handler, zone) {\n        return (event) => {\n            if (KeyEventsPlugin.matchEventFullKeyCode(event, fullKey)) {\n                zone.runGuarded(() => handler(event));\n            }\n        };\n    }\n    /** @internal */\n    static _normalizeKey(keyName) {\n        return keyName === 'esc' ? 'escape' : keyName;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: KeyEventsPlugin, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: KeyEventsPlugin }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: KeyEventsPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\n/**\n * Bootstraps an instance of an Angular application and renders a standalone component as the\n * application's root component. More information about standalone components can be found in [this\n * guide](guide/standalone-components).\n *\n * @usageNotes\n * The root component passed into this function *must* be a standalone one (should have the\n * `standalone: true` flag in the `@Component` decorator config).\n *\n * ```typescript\n * @Component({\n *   standalone: true,\n *   template: 'Hello world!'\n * })\n * class RootComponent {}\n *\n * const appRef: ApplicationRef = await bootstrapApplication(RootComponent);\n * ```\n *\n * You can add the list of providers that should be available in the application injector by\n * specifying the `providers` field in an object passed as the second argument:\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     {provide: BACKEND_URL, useValue: 'https://yourdomain.com/api'}\n *   ]\n * });\n * ```\n *\n * The `importProvidersFrom` helper method can be used to collect all providers from any\n * existing NgModule (and transitively from all NgModules that it imports):\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     importProvidersFrom(SomeNgModule)\n *   ]\n * });\n * ```\n *\n * Note: the `bootstrapApplication` method doesn't include [Testability](api/core/Testability) by\n * default. You can add [Testability](api/core/Testability) by getting the list of necessary\n * providers using `provideProtractorTestingSupport()` function and adding them into the `providers`\n * array, for example:\n *\n * ```typescript\n * import {provideProtractorTestingSupport} from '@angular/platform-browser';\n *\n * await bootstrapApplication(RootComponent, {providers: [provideProtractorTestingSupport()]});\n * ```\n *\n * @param rootComponent A reference to a standalone component that should be rendered.\n * @param options Extra configuration for the bootstrap operation, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction bootstrapApplication(rootComponent, options) {\n    return ɵinternalCreateApplication({ rootComponent, ...createProvidersConfig(options) });\n}\n/**\n * Create an instance of an Angular application without bootstrapping any components. This is useful\n * for the situation where one wants to decouple application environment creation (a platform and\n * associated injectors) from rendering components on a screen. Components can be subsequently\n * bootstrapped on the returned `ApplicationRef`.\n *\n * @param options Extra configuration for the application environment, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction createApplication(options) {\n    return ɵinternalCreateApplication(createProvidersConfig(options));\n}\nfunction createProvidersConfig(options) {\n    return {\n        appProviders: [\n            ...BROWSER_MODULE_PROVIDERS,\n            ...(options?.providers ?? []),\n        ],\n        platformProviders: INTERNAL_BROWSER_PLATFORM_PROVIDERS\n    };\n}\n/**\n * Returns a set of providers required to setup [Testability](api/core/Testability) for an\n * application bootstrapped using the `bootstrapApplication` function. The set of providers is\n * needed to support testing an application with Protractor (which relies on the Testability APIs\n * to be present).\n *\n * @returns An array of providers required to setup Testability for an application and make it\n *     available for testing using Protractor.\n *\n * @publicApi\n */\nfunction provideProtractorTestingSupport() {\n    // Return a copy to prevent changes to the original array in case any in-place\n    // alterations are performed to the `provideProtractorTestingSupport` call results in app\n    // code.\n    return [...TESTABILITY_PROVIDERS];\n}\nfunction initDomAdapter() {\n    BrowserDomAdapter.makeCurrent();\n}\nfunction errorHandler() {\n    return new ErrorHandler();\n}\nfunction _document() {\n    // Tell ivy about the global document\n    ɵsetDocument(document);\n    return document;\n}\nconst INTERNAL_BROWSER_PLATFORM_PROVIDERS = [\n    { provide: PLATFORM_ID, useValue: ɵPLATFORM_BROWSER_ID },\n    { provide: PLATFORM_INITIALIZER, useValue: initDomAdapter, multi: true },\n    { provide: DOCUMENT, useFactory: _document, deps: [] },\n];\n/**\n * A factory function that returns a `PlatformRef` instance associated with browser service\n * providers.\n *\n * @publicApi\n */\nconst platformBrowser = createPlatformFactory(platformCore, 'browser', INTERNAL_BROWSER_PLATFORM_PROVIDERS);\n/**\n * Internal marker to signal whether providers from the `BrowserModule` are already present in DI.\n * This is needed to avoid loading `BrowserModule` providers twice. We can't rely on the\n * `BrowserModule` presence itself, since the standalone-based bootstrap just imports\n * `BrowserModule` providers without referencing the module itself.\n */\nconst BROWSER_MODULE_PROVIDERS_MARKER = new InjectionToken((typeof ngDevMode === 'undefined' || ngDevMode) ? 'BrowserModule Providers Marker' : '');\nconst TESTABILITY_PROVIDERS = [\n    {\n        provide: ɵTESTABILITY_GETTER,\n        useClass: BrowserGetTestability,\n        deps: [],\n    },\n    {\n        provide: ɵTESTABILITY,\n        useClass: Testability,\n        deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER]\n    },\n    {\n        provide: Testability, // Also provide as `Testability` for backwards-compatibility.\n        useClass: Testability,\n        deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER]\n    }\n];\nconst BROWSER_MODULE_PROVIDERS = [\n    { provide: ɵINJECTOR_SCOPE, useValue: 'root' },\n    { provide: ErrorHandler, useFactory: errorHandler, deps: [] }, {\n        provide: EVENT_MANAGER_PLUGINS,\n        useClass: DomEventsPlugin,\n        multi: true,\n        deps: [DOCUMENT, NgZone, PLATFORM_ID]\n    },\n    { provide: EVENT_MANAGER_PLUGINS, useClass: KeyEventsPlugin, multi: true, deps: [DOCUMENT] },\n    DomRendererFactory2, SharedStylesHost, EventManager,\n    { provide: RendererFactory2, useExisting: DomRendererFactory2 },\n    { provide: XhrFactory, useClass: BrowserXhr, deps: [] },\n    (typeof ngDevMode === 'undefined' || ngDevMode) ?\n        { provide: BROWSER_MODULE_PROVIDERS_MARKER, useValue: true } :\n        []\n];\n/**\n * Exports required infrastructure for all Angular apps.\n * Included by default in all Angular apps created with the CLI\n * `new` command.\n * Re-exports `CommonModule` and `ApplicationModule`, making their\n * exports and providers available to all apps.\n *\n * @publicApi\n */\nclass BrowserModule {\n    constructor(providersAlreadyPresent) {\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) && providersAlreadyPresent) {\n            throw new ɵRuntimeError(5100 /* RuntimeErrorCode.BROWSER_MODULE_ALREADY_LOADED */, `Providers from the \\`BrowserModule\\` have already been loaded. If you need access ` +\n                `to common directives such as NgIf and NgFor, import the \\`CommonModule\\` instead.`);\n        }\n    }\n    /**\n     * Configures a browser-based app to transition from a server-rendered app, if\n     * one is present on the page.\n     *\n     * @param params An object containing an identifier for the app to transition.\n     * The ID must match between the client and server versions of the app.\n     * @returns The reconfigured `BrowserModule` to import into the app's root `AppModule`.\n     *\n     * @deprecated Use {@link APP_ID} instead to set the application ID.\n     */\n    static withServerTransition(params) {\n        return {\n            ngModule: BrowserModule,\n            providers: [\n                { provide: APP_ID, useValue: params.appId },\n            ],\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: BrowserModule, deps: [{ token: BROWSER_MODULE_PROVIDERS_MARKER, optional: true, skipSelf: true }], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.12\", ngImport: i0, type: BrowserModule, exports: [CommonModule, ApplicationModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: BrowserModule, providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS], imports: [CommonModule, ApplicationModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: BrowserModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n                    exports: [CommonModule, ApplicationModule],\n                }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }, {\n                    type: Inject,\n                    args: [BROWSER_MODULE_PROVIDERS_MARKER]\n                }] }] });\n\n/**\n * A service for managing HTML `<meta>` tags.\n *\n * Properties of the `MetaDefinition` object match the attributes of the\n * HTML `<meta>` tag. These tags define document metadata that is important for\n * things like configuring a Content Security Policy, defining browser compatibility\n * and security settings, setting HTTP Headers, defining rich content for social sharing,\n * and Search Engine Optimization (SEO).\n *\n * To identify specific `<meta>` tags in a document, use an attribute selection\n * string in the format `\"tag_attribute='value string'\"`.\n * For example, an `attrSelector` value of `\"name='description'\"` matches a tag\n * whose `name` attribute has the value `\"description\"`.\n * Selectors are used with the `querySelector()` Document method,\n * in the format `meta[{attrSelector}]`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see [Document.querySelector()](https://developer.mozilla.org/docs/Web/API/Document/querySelector)\n *\n *\n * @publicApi\n */\nclass Meta {\n    constructor(_doc) {\n        this._doc = _doc;\n        this._dom = ɵgetDOM();\n    }\n    /**\n     * Retrieves or creates a specific `<meta>` tag element in the current HTML document.\n     * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n     * values in the provided tag definition, and verifies that all other attribute values are equal.\n     * If an existing element is found, it is returned and is not modified in any way.\n     * @param tag The definition of a `<meta>` element to match or create.\n     * @param forceCreation True to create a new element without checking whether one already exists.\n     * @returns The existing element with the same attributes and values if found,\n     * the new element if no match is found, or `null` if the tag parameter is not defined.\n     */\n    addTag(tag, forceCreation = false) {\n        if (!tag)\n            return null;\n        return this._getOrCreateElement(tag, forceCreation);\n    }\n    /**\n     * Retrieves or creates a set of `<meta>` tag elements in the current HTML document.\n     * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n     * values in the provided tag definition, and verifies that all other attribute values are equal.\n     * @param tags An array of tag definitions to match or create.\n     * @param forceCreation True to create new elements without checking whether they already exist.\n     * @returns The matching elements if found, or the new elements.\n     */\n    addTags(tags, forceCreation = false) {\n        if (!tags)\n            return [];\n        return tags.reduce((result, tag) => {\n            if (tag) {\n                result.push(this._getOrCreateElement(tag, forceCreation));\n            }\n            return result;\n        }, []);\n    }\n    /**\n     * Retrieves a `<meta>` tag element in the current HTML document.\n     * @param attrSelector The tag attribute and value to match against, in the format\n     * `\"tag_attribute='value string'\"`.\n     * @returns The matching element, if any.\n     */\n    getTag(attrSelector) {\n        if (!attrSelector)\n            return null;\n        return this._doc.querySelector(`meta[${attrSelector}]`) || null;\n    }\n    /**\n     * Retrieves a set of `<meta>` tag elements in the current HTML document.\n     * @param attrSelector The tag attribute and value to match against, in the format\n     * `\"tag_attribute='value string'\"`.\n     * @returns The matching elements, if any.\n     */\n    getTags(attrSelector) {\n        if (!attrSelector)\n            return [];\n        const list /*NodeList*/ = this._doc.querySelectorAll(`meta[${attrSelector}]`);\n        return list ? [].slice.call(list) : [];\n    }\n    /**\n     * Modifies an existing `<meta>` tag element in the current HTML document.\n     * @param tag The tag description with which to replace the existing tag content.\n     * @param selector A tag attribute and value to match against, to identify\n     * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n     * If not supplied, matches a tag with the same `name` or `property` attribute value as the\n     * replacement tag.\n     * @return The modified element.\n     */\n    updateTag(tag, selector) {\n        if (!tag)\n            return null;\n        selector = selector || this._parseSelector(tag);\n        const meta = this.getTag(selector);\n        if (meta) {\n            return this._setMetaElementAttributes(tag, meta);\n        }\n        return this._getOrCreateElement(tag, true);\n    }\n    /**\n     * Removes an existing `<meta>` tag element from the current HTML document.\n     * @param attrSelector A tag attribute and value to match against, to identify\n     * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n     */\n    removeTag(attrSelector) {\n        this.removeTagElement(this.getTag(attrSelector));\n    }\n    /**\n     * Removes an existing `<meta>` tag element from the current HTML document.\n     * @param meta The tag definition to match against to identify an existing tag.\n     */\n    removeTagElement(meta) {\n        if (meta) {\n            this._dom.remove(meta);\n        }\n    }\n    _getOrCreateElement(meta, forceCreation = false) {\n        if (!forceCreation) {\n            const selector = this._parseSelector(meta);\n            // It's allowed to have multiple elements with the same name so it's not enough to\n            // just check that element with the same name already present on the page. We also need to\n            // check if element has tag attributes\n            const elem = this.getTags(selector).filter(elem => this._containsAttributes(meta, elem))[0];\n            if (elem !== undefined)\n                return elem;\n        }\n        const element = this._dom.createElement('meta');\n        this._setMetaElementAttributes(meta, element);\n        const head = this._doc.getElementsByTagName('head')[0];\n        head.appendChild(element);\n        return element;\n    }\n    _setMetaElementAttributes(tag, el) {\n        Object.keys(tag).forEach((prop) => el.setAttribute(this._getMetaKeyMap(prop), tag[prop]));\n        return el;\n    }\n    _parseSelector(tag) {\n        const attr = tag.name ? 'name' : 'property';\n        return `${attr}=\"${tag[attr]}\"`;\n    }\n    _containsAttributes(tag, elem) {\n        return Object.keys(tag).every((key) => elem.getAttribute(this._getMetaKeyMap(key)) === tag[key]);\n    }\n    _getMetaKeyMap(prop) {\n        return META_KEYS_MAP[prop] || prop;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: Meta, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: Meta, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: Meta, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n/**\n * Mapping for MetaDefinition properties with their correct meta attribute names\n */\nconst META_KEYS_MAP = {\n    httpEquiv: 'http-equiv'\n};\n\n/**\n * A service that can be used to get and set the title of a current HTML document.\n *\n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n *\n * @publicApi\n */\nclass Title {\n    constructor(_doc) {\n        this._doc = _doc;\n    }\n    /**\n     * Get the title of the current HTML document.\n     */\n    getTitle() {\n        return this._doc.title;\n    }\n    /**\n     * Set the title of the current HTML document.\n     * @param newTitle\n     */\n    setTitle(newTitle) {\n        this._doc.title = newTitle || '';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: Title, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: Title, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: Title, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param value The value to export.\n */\nfunction exportNgVar(name, value) {\n    if (typeof COMPILED === 'undefined' || !COMPILED) {\n        // Note: we can't export `ng` when using closure enhanced optimization as:\n        // - closure declares globals itself for minified names, which sometimes clobber our `ng` global\n        // - we can't declare a closure extern as the namespace `ng` is already used within Google\n        //   for typings for angularJS (via `goog.provide('ng....')`).\n        const ng = ɵglobal['ng'] = ɵglobal['ng'] || {};\n        ng[name] = value;\n    }\n}\n\nclass ChangeDetectionPerfRecord {\n    constructor(msPerTick, numTicks) {\n        this.msPerTick = msPerTick;\n        this.numTicks = numTicks;\n    }\n}\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\nclass AngularProfiler {\n    constructor(ref) {\n        this.appRef = ref.injector.get(ApplicationRef);\n    }\n    // tslint:disable:no-console\n    /**\n     * Exercises change detection in a loop and then prints the average amount of\n     * time in milliseconds how long a single round of change detection takes for\n     * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n     * of 500 milliseconds.\n     *\n     * Optionally, a user may pass a `config` parameter containing a map of\n     * options. Supported options are:\n     *\n     * `record` (boolean) - causes the profiler to record a CPU profile while\n     * it exercises the change detector. Example:\n     *\n     * ```\n     * ng.profiler.timeChangeDetection({record: true})\n     * ```\n     */\n    timeChangeDetection(config) {\n        const record = config && config['record'];\n        const profileName = 'Change Detection';\n        // Profiler is not available in Android browsers without dev tools opened\n        if (record && 'profile' in console && typeof console.profile === 'function') {\n            console.profile(profileName);\n        }\n        const start = performance.now();\n        let numTicks = 0;\n        while (numTicks < 5 || (performance.now() - start) < 500) {\n            this.appRef.tick();\n            numTicks++;\n        }\n        const end = performance.now();\n        if (record && 'profileEnd' in console && typeof console.profileEnd === 'function') {\n            console.profileEnd(profileName);\n        }\n        const msPerTick = (end - start) / numTicks;\n        console.log(`ran ${numTicks} change detection cycles`);\n        console.log(`${msPerTick.toFixed(2)} ms per check`);\n        return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n    }\n}\n\nconst PROFILER_GLOBAL_NAME = 'profiler';\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n *\n * Usage:\n *\n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n *\n * @publicApi\n */\nfunction enableDebugTools(ref) {\n    exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n    return ref;\n}\n/**\n * Disables Angular tools.\n *\n * @publicApi\n */\nfunction disableDebugTools() {\n    exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\n\n/**\n * Predicates for use with {@link DebugElement}'s query functions.\n *\n * @publicApi\n */\nclass By {\n    /**\n     * Match all nodes.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n     */\n    static all() {\n        return () => true;\n    }\n    /**\n     * Match elements by the given CSS selector.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n     */\n    static css(selector) {\n        return (debugElement) => {\n            return debugElement.nativeElement != null ?\n                elementMatches(debugElement.nativeElement, selector) :\n                false;\n        };\n    }\n    /**\n     * Match nodes that have the given directive present.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n     */\n    static directive(type) {\n        return (debugNode) => debugNode.providerTokens.indexOf(type) !== -1;\n    }\n}\nfunction elementMatches(n, selector) {\n    if (ɵgetDOM().isElementNode(n)) {\n        return n.matches && n.matches(selector) ||\n            n.msMatchesSelector && n.msMatchesSelector(selector) ||\n            n.webkitMatchesSelector && n.webkitMatchesSelector(selector);\n    }\n    return false;\n}\n\n/**\n * Supported HammerJS recognizer event names.\n */\nconst EVENT_NAMES = {\n    // pan\n    'pan': true,\n    'panstart': true,\n    'panmove': true,\n    'panend': true,\n    'pancancel': true,\n    'panleft': true,\n    'panright': true,\n    'panup': true,\n    'pandown': true,\n    // pinch\n    'pinch': true,\n    'pinchstart': true,\n    'pinchmove': true,\n    'pinchend': true,\n    'pinchcancel': true,\n    'pinchin': true,\n    'pinchout': true,\n    // press\n    'press': true,\n    'pressup': true,\n    // rotate\n    'rotate': true,\n    'rotatestart': true,\n    'rotatemove': true,\n    'rotateend': true,\n    'rotatecancel': true,\n    // swipe\n    'swipe': true,\n    'swipeleft': true,\n    'swiperight': true,\n    'swipeup': true,\n    'swipedown': true,\n    // tap\n    'tap': true,\n    'doubletap': true\n};\n/**\n * DI token for providing [HammerJS](https://hammerjs.github.io/) support to Angular.\n * @see {@link HammerGestureConfig}\n *\n * @ngModule HammerModule\n * @publicApi\n */\nconst HAMMER_GESTURE_CONFIG = new InjectionToken('HammerGestureConfig');\n/**\n * Injection token used to provide a {@link HammerLoader} to Angular.\n *\n * @publicApi\n */\nconst HAMMER_LOADER = new InjectionToken('HammerLoader');\n/**\n * An injectable [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n * for gesture recognition. Configures specific event recognition.\n * @publicApi\n */\nclass HammerGestureConfig {\n    constructor() {\n        /**\n         * A set of supported event names for gestures to be used in Angular.\n         * Angular supports all built-in recognizers, as listed in\n         * [HammerJS documentation](https://hammerjs.github.io/).\n         */\n        this.events = [];\n        /**\n         * Maps gesture event names to a set of configuration options\n         * that specify overrides to the default values for specific properties.\n         *\n         * The key is a supported event name to be configured,\n         * and the options object contains a set of properties, with override values\n         * to be applied to the named recognizer event.\n         * For example, to disable recognition of the rotate event, specify\n         *  `{\"rotate\": {\"enable\": false}}`.\n         *\n         * Properties that are not present take the HammerJS default values.\n         * For information about which properties are supported for which events,\n         * and their allowed and default values, see\n         * [HammerJS documentation](https://hammerjs.github.io/).\n         *\n         */\n        this.overrides = {};\n    }\n    /**\n     * Creates a [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n     * and attaches it to a given HTML element.\n     * @param element The element that will recognize gestures.\n     * @returns A HammerJS event-manager object.\n     */\n    buildHammer(element) {\n        const mc = new Hammer(element, this.options);\n        mc.get('pinch').set({ enable: true });\n        mc.get('rotate').set({ enable: true });\n        for (const eventName in this.overrides) {\n            mc.get(eventName).set(this.overrides[eventName]);\n        }\n        return mc;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: HammerGestureConfig, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: HammerGestureConfig }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: HammerGestureConfig, decorators: [{\n            type: Injectable\n        }] });\n/**\n * Event plugin that adds Hammer support to an application.\n *\n * @ngModule HammerModule\n */\nclass HammerGesturesPlugin extends EventManagerPlugin {\n    constructor(doc, _config, console, loader) {\n        super(doc);\n        this._config = _config;\n        this.console = console;\n        this.loader = loader;\n        this._loaderPromise = null;\n    }\n    supports(eventName) {\n        if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n            return false;\n        }\n        if (!window.Hammer && !this.loader) {\n            if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                this.console.warn(`The \"${eventName}\" event cannot be bound because Hammer.JS is not ` +\n                    `loaded and no custom loader has been specified.`);\n            }\n            return false;\n        }\n        return true;\n    }\n    addEventListener(element, eventName, handler) {\n        const zone = this.manager.getZone();\n        eventName = eventName.toLowerCase();\n        // If Hammer is not present but a loader is specified, we defer adding the event listener\n        // until Hammer is loaded.\n        if (!window.Hammer && this.loader) {\n            this._loaderPromise = this._loaderPromise || zone.runOutsideAngular(() => this.loader());\n            // This `addEventListener` method returns a function to remove the added listener.\n            // Until Hammer is loaded, the returned function needs to *cancel* the registration rather\n            // than remove anything.\n            let cancelRegistration = false;\n            let deregister = () => {\n                cancelRegistration = true;\n            };\n            zone.runOutsideAngular(() => this._loaderPromise\n                .then(() => {\n                // If Hammer isn't actually loaded when the custom loader resolves, give up.\n                if (!window.Hammer) {\n                    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                        this.console.warn(`The custom HAMMER_LOADER completed, but Hammer.JS is not present.`);\n                    }\n                    deregister = () => { };\n                    return;\n                }\n                if (!cancelRegistration) {\n                    // Now that Hammer is loaded and the listener is being loaded for real,\n                    // the deregistration function changes from canceling registration to\n                    // removal.\n                    deregister = this.addEventListener(element, eventName, handler);\n                }\n            })\n                .catch(() => {\n                if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                    this.console.warn(`The \"${eventName}\" event cannot be bound because the custom ` +\n                        `Hammer.JS loader failed.`);\n                }\n                deregister = () => { };\n            }));\n            // Return a function that *executes* `deregister` (and not `deregister` itself) so that we\n            // can change the behavior of `deregister` once the listener is added. Using a closure in\n            // this way allows us to avoid any additional data structures to track listener removal.\n            return () => {\n                deregister();\n            };\n        }\n        return zone.runOutsideAngular(() => {\n            // Creating the manager bind events, must be done outside of angular\n            const mc = this._config.buildHammer(element);\n            const callback = function (eventObj) {\n                zone.runGuarded(function () {\n                    handler(eventObj);\n                });\n            };\n            mc.on(eventName, callback);\n            return () => {\n                mc.off(eventName, callback);\n                // destroy mc to prevent memory leak\n                if (typeof mc.destroy === 'function') {\n                    mc.destroy();\n                }\n            };\n        });\n    }\n    isCustomEvent(eventName) {\n        return this._config.events.indexOf(eventName) > -1;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: HammerGesturesPlugin, deps: [{ token: DOCUMENT }, { token: HAMMER_GESTURE_CONFIG }, { token: i0.ɵConsole }, { token: HAMMER_LOADER, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: HammerGesturesPlugin }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: HammerGesturesPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: HammerGestureConfig, decorators: [{\n                    type: Inject,\n                    args: [HAMMER_GESTURE_CONFIG]\n                }] }, { type: i0.ɵConsole }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [HAMMER_LOADER]\n                }] }] });\n/**\n * Adds support for HammerJS.\n *\n * Import this module at the root of your application so that Angular can work with\n * HammerJS to detect gesture events.\n *\n * Note that applications still need to include the HammerJS script itself. This module\n * simply sets up the coordination layer between HammerJS and Angular's `EventManager`.\n *\n * @publicApi\n */\nclass HammerModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: HammerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.12\", ngImport: i0, type: HammerModule }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: HammerModule, providers: [\n            {\n                provide: EVENT_MANAGER_PLUGINS,\n                useClass: HammerGesturesPlugin,\n                multi: true,\n                deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n            },\n            { provide: HAMMER_GESTURE_CONFIG, useClass: HammerGestureConfig, deps: [] },\n        ] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: HammerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        {\n                            provide: EVENT_MANAGER_PLUGINS,\n                            useClass: HammerGesturesPlugin,\n                            multi: true,\n                            deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n                        },\n                        { provide: HAMMER_GESTURE_CONFIG, useClass: HammerGestureConfig, deps: [] },\n                    ]\n                }]\n        }] });\n\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n *\n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n *\n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n *\n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n *\n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n *\n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n *\n * @security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](https://g.co/ng/security).\n *\n * @publicApi\n */\nclass DomSanitizer {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: DomSanitizer, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: DomSanitizer, providedIn: 'root', useExisting: i0.forwardRef(() => DomSanitizerImpl) }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: DomSanitizer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useExisting: forwardRef(() => DomSanitizerImpl) }]\n        }] });\nclass DomSanitizerImpl extends DomSanitizer {\n    constructor(_doc) {\n        super();\n        this._doc = _doc;\n    }\n    sanitize(ctx, value) {\n        if (value == null)\n            return null;\n        switch (ctx) {\n            case SecurityContext.NONE:\n                return value;\n            case SecurityContext.HTML:\n                if (ɵallowSanitizationBypassAndThrow(value, \"HTML\" /* BypassType.Html */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                return ɵ_sanitizeHtml(this._doc, String(value)).toString();\n            case SecurityContext.STYLE:\n                if (ɵallowSanitizationBypassAndThrow(value, \"Style\" /* BypassType.Style */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                return value;\n            case SecurityContext.SCRIPT:\n                if (ɵallowSanitizationBypassAndThrow(value, \"Script\" /* BypassType.Script */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                throw new ɵRuntimeError(5200 /* RuntimeErrorCode.SANITIZATION_UNSAFE_SCRIPT */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    'unsafe value used in a script context');\n            case SecurityContext.URL:\n                if (ɵallowSanitizationBypassAndThrow(value, \"URL\" /* BypassType.Url */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                return ɵ_sanitizeUrl(String(value));\n            case SecurityContext.RESOURCE_URL:\n                if (ɵallowSanitizationBypassAndThrow(value, \"ResourceURL\" /* BypassType.ResourceUrl */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                throw new ɵRuntimeError(5201 /* RuntimeErrorCode.SANITIZATION_UNSAFE_RESOURCE_URL */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    `unsafe value used in a resource URL context (see ${ɵXSS_SECURITY_URL})`);\n            default:\n                throw new ɵRuntimeError(5202 /* RuntimeErrorCode.SANITIZATION_UNEXPECTED_CTX */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    `Unexpected SecurityContext ${ctx} (see ${ɵXSS_SECURITY_URL})`);\n        }\n    }\n    bypassSecurityTrustHtml(value) {\n        return ɵbypassSanitizationTrustHtml(value);\n    }\n    bypassSecurityTrustStyle(value) {\n        return ɵbypassSanitizationTrustStyle(value);\n    }\n    bypassSecurityTrustScript(value) {\n        return ɵbypassSanitizationTrustScript(value);\n    }\n    bypassSecurityTrustUrl(value) {\n        return ɵbypassSanitizationTrustUrl(value);\n    }\n    bypassSecurityTrustResourceUrl(value) {\n        return ɵbypassSanitizationTrustResourceUrl(value);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: DomSanitizerImpl, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: DomSanitizerImpl, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: DomSanitizerImpl, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\n/**\n * The list of features as an enum to uniquely type each `HydrationFeature`.\n * @see {@link HydrationFeature}\n *\n * @publicApi\n */\nvar HydrationFeatureKind;\n(function (HydrationFeatureKind) {\n    HydrationFeatureKind[HydrationFeatureKind[\"NoHttpTransferCache\"] = 0] = \"NoHttpTransferCache\";\n    HydrationFeatureKind[HydrationFeatureKind[\"HttpTransferCacheOptions\"] = 1] = \"HttpTransferCacheOptions\";\n})(HydrationFeatureKind || (HydrationFeatureKind = {}));\n/**\n * Helper function to create an object that represents a Hydration feature.\n */\nfunction hydrationFeature(ɵkind, ɵproviders = [], ɵoptions = {}) {\n    return { ɵkind, ɵproviders };\n}\n/**\n * Disables HTTP transfer cache. Effectively causes HTTP requests to be performed twice: once on the\n * server and other one on the browser.\n *\n * @publicApi\n */\nfunction withNoHttpTransferCache() {\n    // This feature has no providers and acts as a flag that turns off\n    // HTTP transfer cache (which otherwise is turned on by default).\n    return hydrationFeature(HydrationFeatureKind.NoHttpTransferCache);\n}\n/**\n * The function accepts a an object, which allows to configure cache parameters,\n * such as which headers should be included (no headers are included by default),\n * wether POST requests should be cached or a callback function to determine if a\n * particular request should be cached.\n *\n * @publicApi\n */\nfunction withHttpTransferCacheOptions(options) {\n    // This feature has no providers and acts as a flag to pass options to the HTTP transfer cache.\n    return hydrationFeature(HydrationFeatureKind.HttpTransferCacheOptions, ɵwithHttpTransferCache(options));\n}\n/**\n * Returns an `ENVIRONMENT_INITIALIZER` token setup with a function\n * that verifies whether compatible ZoneJS was used in an application\n * and logs a warning in a console if it's not the case.\n */\nfunction provideZoneJsCompatibilityDetector() {\n    return [{\n            provide: ENVIRONMENT_INITIALIZER,\n            useValue: () => {\n                const ngZone = inject(NgZone);\n                // Checking `ngZone instanceof NgZone` would be insufficient here,\n                // because custom implementations might use NgZone as a base class.\n                if (ngZone.constructor !== NgZone) {\n                    const console = inject(ɵConsole);\n                    const message = ɵformatRuntimeError(-5000 /* RuntimeErrorCode.UNSUPPORTED_ZONEJS_INSTANCE */, 'Angular detected that hydration was enabled for an application ' +\n                        'that uses a custom or a noop Zone.js implementation. ' +\n                        'This is not yet a fully supported configuration.');\n                    // tslint:disable-next-line:no-console\n                    console.warn(message);\n                }\n            },\n            multi: true,\n        }];\n}\n/**\n * Sets up providers necessary to enable hydration functionality for the application.\n *\n * By default, the function enables the recommended set of features for the optimal\n * performance for most of the applications. It includes the following features:\n *\n * * Reconciling DOM hydration. Learn more about it [here](guide/hydration).\n * * [`HttpClient`](api/common/http/HttpClient) response caching while running on the server and\n * transferring this cache to the client to avoid extra HTTP requests. Learn more about data caching\n * [here](/guide/ssr#caching-data-when-using-httpclient).\n *\n * These functions allow you to disable some of the default features or configure features\n * * {@link withNoHttpTransferCache} to disable HTTP transfer cache\n * * {@link withHttpTransferCacheOptions} to configure some HTTP transfer cache options\n *\n * @usageNotes\n *\n * Basic example of how you can enable hydration in your application when\n * `bootstrapApplication` function is used:\n * ```\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration()]\n * });\n * ```\n *\n * Alternatively if you are using NgModules, you would add `provideClientHydration`\n * to your root app module's provider list.\n * ```\n * @NgModule({\n *   declarations: [RootCmp],\n *   bootstrap: [RootCmp],\n *   providers: [provideClientHydration()],\n * })\n * export class AppModule {}\n * ```\n *\n * @see {@link withNoHttpTransferCache}\n * @see {@link withHttpTransferCacheOptions}\n *\n * @param features Optional features to configure additional router behaviors.\n * @returns A set of providers to enable hydration.\n *\n * @publicApi\n */\nfunction provideClientHydration(...features) {\n    const providers = [];\n    const featuresKind = new Set();\n    const hasHttpTransferCacheOptions = featuresKind.has(HydrationFeatureKind.HttpTransferCacheOptions);\n    for (const { ɵproviders, ɵkind } of features) {\n        featuresKind.add(ɵkind);\n        if (ɵproviders.length) {\n            providers.push(ɵproviders);\n        }\n    }\n    if (typeof ngDevMode !== 'undefined' && ngDevMode &&\n        featuresKind.has(HydrationFeatureKind.NoHttpTransferCache) && hasHttpTransferCacheOptions) {\n        // TODO: Make this a runtime error\n        throw new Error('Configuration error: found both withHttpTransferCacheOptions() and withNoHttpTransferCache() in the same call to provideClientHydration(), which is a contradiction.');\n    }\n    return makeEnvironmentProviders([\n        (typeof ngDevMode !== 'undefined' && ngDevMode) ? provideZoneJsCompatibilityDetector() : [],\n        ɵwithDomHydration(),\n        ((featuresKind.has(HydrationFeatureKind.NoHttpTransferCache) || hasHttpTransferCacheOptions) ?\n            [] :\n            ɵwithHttpTransferCache({})),\n        providers,\n    ]);\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('17.3.12');\n\n// Re-export TransferState to the public API of the `platform-browser` for backwards-compatibility.\n/**\n * Create a `StateKey<T>` that can be used to store value of type T with `TransferState`.\n *\n * Example:\n *\n * ```\n * const COUNTER_KEY = makeStateKey<number>('counter');\n * let value = 10;\n *\n * transferState.set(COUNTER_KEY, value);\n * ```\n *\n * @publicApi\n * @deprecated `makeStateKey` has moved, please import `makeStateKey` from `@angular/core` instead.\n */\n// The below is a workaround to add a deprecated message.\nconst makeStateKey = makeStateKey$1;\n// The below type is needed for G3 due to JSC_CONFORMANCE_VIOLATION.\nconst TransferState = TransferState$1;\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserModule, By, DomSanitizer, EVENT_MANAGER_PLUGINS, EventManager, EventManagerPlugin, HAMMER_GESTURE_CONFIG, HAMMER_LOADER, HammerGestureConfig, HammerModule, HydrationFeatureKind, Meta, REMOVE_STYLES_ON_COMPONENT_DESTROY, Title, TransferState, VERSION, bootstrapApplication, createApplication, disableDebugTools, enableDebugTools, makeStateKey, platformBrowser, provideClientHydration, provideProtractorTestingSupport, withHttpTransferCacheOptions, withNoHttpTransferCache, BrowserDomAdapter as ɵBrowserDomAdapter, BrowserGetTestability as ɵBrowserGetTestability, DomEventsPlugin as ɵDomEventsPlugin, DomRendererFactory2 as ɵDomRendererFactory2, DomSanitizerImpl as ɵDomSanitizerImpl, HammerGesturesPlugin as ɵHammerGesturesPlugin, INTERNAL_BROWSER_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS, KeyEventsPlugin as ɵKeyEventsPlugin, SharedStylesHost as ɵSharedStylesHost, initDomAdapter as ɵinitDomAdapter };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,OAAO,EAAEC,aAAa,EAAEC,UAAU,EAAEC,cAAc,EAAEC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,mBAAmB,EAAEC,0BAA0B,EAAEC,YAAY,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,qBAAqB,EAAEC,YAAY,EAAEC,mBAAmB,EAAEC,YAAY,EAAEC,WAAW,EAAEC,MAAM,EAAEC,mBAAmB,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,gCAAgC,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,cAAc,EAAEC,4BAA4B,EAAEC,6BAA6B,EAAEC,8BAA8B,EAAEC,2BAA2B,EAAEC,mCAAmC,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,mBAAmB,EAAEC,wBAAwB,EAAEC,iBAAiB,EAAEC,OAAO,EAAEC,YAAY,IAAIC,cAAc,EAAEC,aAAa,IAAIC,eAAe,QAAQ,eAAe;AACh6B,SAASC,WAAW,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,OAAO,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,oBAAoB,EAAEC,UAAU,EAAEC,YAAY,QAAQ,iBAAiB;AACzK,SAASL,OAAO,QAAQ,iBAAiB;AACzC,SAASM,sBAAsB,QAAQ,sBAAsB;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,SAASV,WAAW,CAAC;EAC/CW,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,iBAAiB,GAAG,IAAI;EACjC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,SAASJ,wBAAwB,CAAC;EACrD,OAAOK,WAAWA,CAAA,EAAG;IACjBd,kBAAkB,CAAC,IAAIa,iBAAiB,CAAC,CAAC,CAAC;EAC/C;EACAE,WAAWA,CAACC,EAAE,EAAEC,GAAG,EAAEC,QAAQ,EAAE;IAC3BF,EAAE,CAACG,gBAAgB,CAACF,GAAG,EAAEC,QAAQ,CAAC;IAClC,OAAO,MAAM;MACTF,EAAE,CAACI,mBAAmB,CAACH,GAAG,EAAEC,QAAQ,CAAC;IACzC,CAAC;EACL;EACAG,aAAaA,CAACL,EAAE,EAAEC,GAAG,EAAE;IACnBD,EAAE,CAACK,aAAa,CAACJ,GAAG,CAAC;EACzB;EACAK,MAAMA,CAACC,IAAI,EAAE;IACT,IAAIA,IAAI,CAACC,UAAU,EAAE;MACjBD,IAAI,CAACC,UAAU,CAACC,WAAW,CAACF,IAAI,CAAC;IACrC;EACJ;EACAG,aAAaA,CAACC,OAAO,EAAEC,GAAG,EAAE;IACxBA,GAAG,GAAGA,GAAG,IAAI,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACtC,OAAOD,GAAG,CAACF,aAAa,CAACC,OAAO,CAAC;EACrC;EACAG,kBAAkBA,CAAA,EAAG;IACjB,OAAOC,QAAQ,CAACC,cAAc,CAACC,kBAAkB,CAAC,WAAW,CAAC;EAClE;EACAJ,kBAAkBA,CAAA,EAAG;IACjB,OAAOE,QAAQ;EACnB;EACAG,aAAaA,CAACX,IAAI,EAAE;IAChB,OAAOA,IAAI,CAACY,QAAQ,KAAKC,IAAI,CAACC,YAAY;EAC9C;EACAC,YAAYA,CAACf,IAAI,EAAE;IACf,OAAOA,IAAI,YAAYgB,gBAAgB;EAC3C;EACA;EACAC,oBAAoBA,CAACZ,GAAG,EAAEa,MAAM,EAAE;IAC9B,IAAIA,MAAM,KAAK,QAAQ,EAAE;MACrB,OAAOC,MAAM;IACjB;IACA,IAAID,MAAM,KAAK,UAAU,EAAE;MACvB,OAAOb,GAAG;IACd;IACA,IAAIa,MAAM,KAAK,MAAM,EAAE;MACnB,OAAOb,GAAG,CAACe,IAAI;IACnB;IACA,OAAO,IAAI;EACf;EACAC,WAAWA,CAAChB,GAAG,EAAE;IACb,MAAMiB,IAAI,GAAGC,kBAAkB,CAAC,CAAC;IACjC,OAAOD,IAAI,IAAI,IAAI,GAAG,IAAI,GAAGE,YAAY,CAACF,IAAI,CAAC;EACnD;EACAG,gBAAgBA,CAAA,EAAG;IACfC,WAAW,GAAG,IAAI;EACtB;EACAC,YAAYA,CAAA,EAAG;IACX,OAAOR,MAAM,CAACS,SAAS,CAACC,SAAS;EACrC;EACAC,SAASA,CAACC,IAAI,EAAE;IACZ,OAAOrD,iBAAiB,CAAC8B,QAAQ,CAACwB,MAAM,EAAED,IAAI,CAAC;EACnD;AACJ;AACA,IAAIL,WAAW,GAAG,IAAI;AACtB,SAASH,kBAAkBA,CAAA,EAAG;EAC1BG,WAAW,GAAGA,WAAW,IAAIlB,QAAQ,CAACyB,aAAa,CAAC,MAAM,CAAC;EAC3D,OAAOP,WAAW,GAAGA,WAAW,CAACQ,YAAY,CAAC,MAAM,CAAC,GAAG,IAAI;AAChE;AACA,SAASV,YAAYA,CAACW,GAAG,EAAE;EACvB;EACA;EACA,OAAO,IAAIC,GAAG,CAACD,GAAG,EAAE3B,QAAQ,CAAC6B,OAAO,CAAC,CAACC,QAAQ;AAClD;AAEA,MAAMC,qBAAqB,CAAC;EACxBC,WAAWA,CAACC,QAAQ,EAAE;IAClBpH,OAAO,CAAC,uBAAuB,CAAC,GAAG,CAACqH,IAAI,EAAEC,eAAe,GAAG,IAAI,KAAK;MACjE,MAAMC,WAAW,GAAGH,QAAQ,CAACI,qBAAqB,CAACH,IAAI,EAAEC,eAAe,CAAC;MACzE,IAAIC,WAAW,IAAI,IAAI,EAAE;QACrB,MAAM,IAAItH,aAAa,CAAC,IAAI,CAAC,8CAA8C,CAAC,OAAOwH,SAAS,KAAK,WAAW,IAAIA,SAAS,KACrH,yCAAyC,CAAC;MAClD;MACA,OAAOF,WAAW;IACtB,CAAC;IACDvH,OAAO,CAAC,4BAA4B,CAAC,GAAG,MAAMoH,QAAQ,CAACM,mBAAmB,CAAC,CAAC;IAC5E1H,OAAO,CAAC,2BAA2B,CAAC,GAAG,MAAMoH,QAAQ,CAACO,kBAAkB,CAAC,CAAC;IAC1E,MAAMC,aAAa,GAAIC,QAAQ,IAAK;MAChC,MAAMC,aAAa,GAAG9H,OAAO,CAAC,4BAA4B,CAAC,CAAC,CAAC;MAC7D,IAAI+H,KAAK,GAAGD,aAAa,CAACE,MAAM;MAChC,MAAMC,SAAS,GAAG,SAAAA,CAAA,EAAY;QAC1BF,KAAK,EAAE;QACP,IAAIA,KAAK,IAAI,CAAC,EAAE;UACZF,QAAQ,CAAC,CAAC;QACd;MACJ,CAAC;MACDC,aAAa,CAACI,OAAO,CAAEX,WAAW,IAAK;QACnCA,WAAW,CAACY,UAAU,CAACF,SAAS,CAAC;MACrC,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACjI,OAAO,CAAC,sBAAsB,CAAC,EAAE;MAClCA,OAAO,CAAC,sBAAsB,CAAC,GAAG,EAAE;IACxC;IACAA,OAAO,CAAC,sBAAsB,CAAC,CAACoI,IAAI,CAACR,aAAa,CAAC;EACvD;EACAJ,qBAAqBA,CAACJ,QAAQ,EAAEC,IAAI,EAAEC,eAAe,EAAE;IACnD,IAAID,IAAI,IAAI,IAAI,EAAE;MACd,OAAO,IAAI;IACf;IACA,MAAMgB,CAAC,GAAGjB,QAAQ,CAACkB,cAAc,CAACjB,IAAI,CAAC;IACvC,IAAIgB,CAAC,IAAI,IAAI,EAAE;MACX,OAAOA,CAAC;IACZ,CAAC,MACI,IAAI,CAACf,eAAe,EAAE;MACvB,OAAO,IAAI;IACf;IACA,IAAIhE,OAAO,CAAC,CAAC,CAACoC,YAAY,CAAC2B,IAAI,CAAC,EAAE;MAC9B,OAAO,IAAI,CAACG,qBAAqB,CAACJ,QAAQ,EAAEC,IAAI,CAACkB,IAAI,EAAE,IAAI,CAAC;IAChE;IACA,OAAO,IAAI,CAACf,qBAAqB,CAACJ,QAAQ,EAAEC,IAAI,CAACmB,aAAa,EAAE,IAAI,CAAC;EACzE;AACJ;;AAEA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACbC,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAIC,cAAc,CAAC,CAAC;EAC/B;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,mBAAAR,CAAA;MAAA,YAAAA,CAAA,IAAyFI,UAAU;IAAA,CAAoD;EAAE;EAC3K;IAAS,IAAI,CAACK,KAAK,kBAD8E/I,EAAE,CAAAgJ,kBAAA;MAAAC,KAAA,EACYP,UAAU;MAAAQ,OAAA,EAAVR,UAAU,CAAAG;IAAA,EAAG;EAAE;AAClI;AACA;EAAA,QAAAnB,SAAA,oBAAAA,SAAA,KAHqG1H,EAAE,CAAAmJ,iBAAA,CAGXT,UAAU,EAAc,CAAC;IACzGU,IAAI,EAAEjJ;EACV,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA,MAAMkJ,qBAAqB,GAAG,IAAIjJ,cAAc,CAACsH,SAAS,GAAG,qBAAqB,GAAG,EAAE,CAAC;AACxF;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4B,YAAY,CAAC;EACf;AACJ;AACA;EACIvF,WAAWA,CAACwF,OAAO,EAAEC,KAAK,EAAE;IACxB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,kBAAkB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACnCH,OAAO,CAACpB,OAAO,CAAEwB,MAAM,IAAK;MACxBA,MAAM,CAACC,OAAO,GAAG,IAAI;IACzB,CAAC,CAAC;IACF,IAAI,CAACC,QAAQ,GAAGN,OAAO,CAACO,KAAK,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIvF,gBAAgBA,CAACwF,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAE;IAC1C,MAAMP,MAAM,GAAG,IAAI,CAACQ,cAAc,CAACF,SAAS,CAAC;IAC7C,OAAON,MAAM,CAACnF,gBAAgB,CAACwF,OAAO,EAAEC,SAAS,EAAEC,OAAO,CAAC;EAC/D;EACA;AACJ;AACA;EACIE,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACZ,KAAK;EACrB;EACA;EACAW,cAAcA,CAACF,SAAS,EAAE;IACtB,IAAIN,MAAM,GAAG,IAAI,CAACF,kBAAkB,CAACY,GAAG,CAACJ,SAAS,CAAC;IACnD,IAAIN,MAAM,EAAE;MACR,OAAOA,MAAM;IACjB;IACA,MAAMJ,OAAO,GAAG,IAAI,CAACM,QAAQ;IAC7BF,MAAM,GAAGJ,OAAO,CAACe,IAAI,CAAEX,MAAM,IAAKA,MAAM,CAACY,QAAQ,CAACN,SAAS,CAAC,CAAC;IAC7D,IAAI,CAACN,MAAM,EAAE;MACT,MAAM,IAAIzJ,aAAa,CAAC,IAAI,CAAC,4CAA4C,CAAC,OAAOwH,SAAS,KAAK,WAAW,IAAIA,SAAS,KACnH,2CAA2CuC,SAAS,EAAE,CAAC;IAC/D;IACA,IAAI,CAACR,kBAAkB,CAACe,GAAG,CAACP,SAAS,EAAEN,MAAM,CAAC;IAC9C,OAAOA,MAAM;EACjB;EACA;IAAS,IAAI,CAACd,IAAI,YAAA4B,qBAAAnC,CAAA;MAAA,YAAAA,CAAA,IAAyFgB,YAAY,EAjEtBtJ,EAAE,CAAA0K,QAAA,CAiEsCrB,qBAAqB,GAjE7DrJ,EAAE,CAAA0K,QAAA,CAiEwE1K,EAAE,CAACqB,MAAM;IAAA,CAA6C;EAAE;EACnO;IAAS,IAAI,CAAC0H,KAAK,kBAlE8E/I,EAAE,CAAAgJ,kBAAA;MAAAC,KAAA,EAkEYK,YAAY;MAAAJ,OAAA,EAAZI,YAAY,CAAAT;IAAA,EAAG;EAAE;AACpI;AACA;EAAA,QAAAnB,SAAA,oBAAAA,SAAA,KApEqG1H,EAAE,CAAAmJ,iBAAA,CAoEXG,YAAY,EAAc,CAAC;IAC3GF,IAAI,EAAEjJ;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEiJ,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CxB,IAAI,EAAE/I,MAAM;MACZwK,IAAI,EAAE,CAACxB,qBAAqB;IAChC,CAAC;EAAE,CAAC,EAAE;IAAED,IAAI,EAAEpJ,EAAE,CAACqB;EAAO,CAAC,CAAC;AAAA;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyJ,kBAAkB,CAAC;EACrB;EACA/G,WAAWA,CAACgH,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;AACJ;;AAEA;AACA,MAAMC,qBAAqB,GAAG,WAAW;AACzC,MAAMC,gBAAgB,CAAC;EACnBlH,WAAWA,CAACkB,GAAG,EAAEiG,KAAK,EAAEC,KAAK,EAAEC,UAAU,GAAG,CAAC,CAAC,EAAE;IAC5C,IAAI,CAACnG,GAAG,GAAGA,GAAG;IACd,IAAI,CAACiG,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B;IACA,IAAI,CAACC,QAAQ,GAAG,IAAI3B,GAAG,CAAC,CAAC;IACzB,IAAI,CAAC4B,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,eAAe,GAAG,IAAI,CAACC,2BAA2B,CAAC,CAAC;IACzD,IAAI,CAACC,gBAAgB,GAAGlI,gBAAgB,CAAC4H,UAAU,CAAC;IACpD,IAAI,CAACO,cAAc,CAAC,CAAC;EACzB;EACAC,SAASA,CAACC,MAAM,EAAE;IACd,KAAK,MAAMC,KAAK,IAAID,MAAM,EAAE;MACxB,MAAME,UAAU,GAAG,IAAI,CAACC,gBAAgB,CAACF,KAAK,EAAE,CAAC,CAAC;MAClD,IAAIC,UAAU,KAAK,CAAC,EAAE;QAClB,IAAI,CAACE,YAAY,CAACH,KAAK,CAAC;MAC5B;IACJ;EACJ;EACAI,YAAYA,CAACL,MAAM,EAAE;IACjB,KAAK,MAAMC,KAAK,IAAID,MAAM,EAAE;MACxB,MAAME,UAAU,GAAG,IAAI,CAACC,gBAAgB,CAACF,KAAK,EAAE,CAAC,CAAC,CAAC;MACnD,IAAIC,UAAU,IAAI,CAAC,EAAE;QACjB,IAAI,CAACI,cAAc,CAACL,KAAK,CAAC;MAC9B;IACJ;EACJ;EACAM,WAAWA,CAAA,EAAG;IACV,MAAMZ,eAAe,GAAG,IAAI,CAACA,eAAe;IAC5C,IAAIA,eAAe,EAAE;MACjBA,eAAe,CAACrD,OAAO,CAAEvD,IAAI,IAAKA,IAAI,CAACD,MAAM,CAAC,CAAC,CAAC;MAChD6G,eAAe,CAACa,KAAK,CAAC,CAAC;IAC3B;IACA,KAAK,MAAMP,KAAK,IAAI,IAAI,CAACQ,YAAY,CAAC,CAAC,EAAE;MACrC,IAAI,CAACH,cAAc,CAACL,KAAK,CAAC;IAC9B;IACA,IAAI,CAACH,cAAc,CAAC,CAAC;EACzB;EACAY,OAAOA,CAACC,QAAQ,EAAE;IACd,IAAI,CAAClB,SAAS,CAACmB,GAAG,CAACD,QAAQ,CAAC;IAC5B,KAAK,MAAMV,KAAK,IAAI,IAAI,CAACQ,YAAY,CAAC,CAAC,EAAE;MACrC,IAAI,CAACI,cAAc,CAACF,QAAQ,EAAEV,KAAK,CAAC;IACxC;EACJ;EACAa,UAAUA,CAACH,QAAQ,EAAE;IACjB,IAAI,CAAClB,SAAS,CAACsB,MAAM,CAACJ,QAAQ,CAAC;EACnC;EACAF,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACjB,QAAQ,CAACwB,IAAI,CAAC,CAAC;EAC/B;EACAZ,YAAYA,CAACH,KAAK,EAAE;IAChB,KAAK,MAAMtD,IAAI,IAAI,IAAI,CAAC8C,SAAS,EAAE;MAC/B,IAAI,CAACoB,cAAc,CAAClE,IAAI,EAAEsD,KAAK,CAAC;IACpC;EACJ;EACAK,cAAcA,CAACL,KAAK,EAAE;IAClB,MAAMT,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9BA,QAAQ,CAAChB,GAAG,CAACyB,KAAK,CAAC,EAAEgB,QAAQ,EAAE3E,OAAO,CAAEvD,IAAI,IAAKA,IAAI,CAACD,MAAM,CAAC,CAAC,CAAC;IAC/D0G,QAAQ,CAACuB,MAAM,CAACd,KAAK,CAAC;EAC1B;EACAL,2BAA2BA,CAAA,EAAG;IAC1B,MAAMI,MAAM,GAAG,IAAI,CAAC5G,GAAG,CAAC8H,IAAI,EAAEC,gBAAgB,CAAC,SAAShC,qBAAqB,KAAK,IAAI,CAACE,KAAK,IAAI,CAAC;IACjG,IAAIW,MAAM,EAAE5D,MAAM,EAAE;MAChB,MAAMgF,QAAQ,GAAG,IAAIvD,GAAG,CAAC,CAAC;MAC1BmC,MAAM,CAAC1D,OAAO,CAAE2D,KAAK,IAAK;QACtB,IAAIA,KAAK,CAACoB,WAAW,IAAI,IAAI,EAAE;UAC3BD,QAAQ,CAACzC,GAAG,CAACsB,KAAK,CAACoB,WAAW,EAAEpB,KAAK,CAAC;QAC1C;MACJ,CAAC,CAAC;MACF,OAAOmB,QAAQ;IACnB;IACA,OAAO,IAAI;EACf;EACAjB,gBAAgBA,CAACF,KAAK,EAAEqB,KAAK,EAAE;IAC3B,MAAMC,GAAG,GAAG,IAAI,CAAC/B,QAAQ;IACzB,IAAI+B,GAAG,CAACC,GAAG,CAACvB,KAAK,CAAC,EAAE;MAChB,MAAMwB,aAAa,GAAGF,GAAG,CAAC/C,GAAG,CAACyB,KAAK,CAAC;MACpCwB,aAAa,CAACC,KAAK,IAAIJ,KAAK;MAC5B,OAAOG,aAAa,CAACC,KAAK;IAC9B;IACAH,GAAG,CAAC5C,GAAG,CAACsB,KAAK,EAAE;MAAEyB,KAAK,EAAEJ,KAAK;MAAEL,QAAQ,EAAE;IAAG,CAAC,CAAC;IAC9C,OAAOK,KAAK;EAChB;EACAK,eAAeA,CAAChF,IAAI,EAAEsD,KAAK,EAAE;IACzB,MAAMN,eAAe,GAAG,IAAI,CAACA,eAAe;IAC5C,MAAMiC,OAAO,GAAGjC,eAAe,EAAEnB,GAAG,CAACyB,KAAK,CAAC;IAC3C,IAAI2B,OAAO,EAAE5I,UAAU,KAAK2D,IAAI,EAAE;MAC9B;MACAgD,eAAe,CAACoB,MAAM,CAACd,KAAK,CAAC;MAC7B2B,OAAO,CAACC,eAAe,CAAC1C,qBAAqB,CAAC;MAC9C,IAAI,OAAOtD,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;QAC/C;QACA+F,OAAO,CAACE,YAAY,CAAC,iBAAiB,EAAE,EAAE,CAAC;MAC/C;MACA,OAAOF,OAAO;IAClB,CAAC,MACI;MACD,MAAMA,OAAO,GAAG,IAAI,CAACxI,GAAG,CAACF,aAAa,CAAC,OAAO,CAAC;MAC/C,IAAI,IAAI,CAACoG,KAAK,EAAE;QACZsC,OAAO,CAACE,YAAY,CAAC,OAAO,EAAE,IAAI,CAACxC,KAAK,CAAC;MAC7C;MACAsC,OAAO,CAACP,WAAW,GAAGpB,KAAK;MAC3B,IAAI,IAAI,CAACJ,gBAAgB,EAAE;QACvB+B,OAAO,CAACE,YAAY,CAAC3C,qBAAqB,EAAE,IAAI,CAACE,KAAK,CAAC;MAC3D;MACA1C,IAAI,CAACoF,WAAW,CAACH,OAAO,CAAC;MACzB,OAAOA,OAAO;IAClB;EACJ;EACAf,cAAcA,CAAClE,IAAI,EAAEsD,KAAK,EAAE;IACxB,MAAM2B,OAAO,GAAG,IAAI,CAACD,eAAe,CAAChF,IAAI,EAAEsD,KAAK,CAAC;IACjD,MAAMT,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,MAAMwC,UAAU,GAAGxC,QAAQ,CAAChB,GAAG,CAACyB,KAAK,CAAC,EAAEgB,QAAQ;IAChD,IAAIe,UAAU,EAAE;MACZA,UAAU,CAACxF,IAAI,CAACoF,OAAO,CAAC;IAC5B,CAAC,MACI;MACDpC,QAAQ,CAACb,GAAG,CAACsB,KAAK,EAAE;QAAEgB,QAAQ,EAAE,CAACW,OAAO,CAAC;QAAEF,KAAK,EAAE;MAAE,CAAC,CAAC;IAC1D;EACJ;EACA5B,cAAcA,CAAA,EAAG;IACb,MAAML,SAAS,GAAG,IAAI,CAACA,SAAS;IAChCA,SAAS,CAACe,KAAK,CAAC,CAAC;IACjB;IACAf,SAAS,CAACmB,GAAG,CAAC,IAAI,CAACxH,GAAG,CAAC8H,IAAI,CAAC;EAChC;EACA;IAAS,IAAI,CAAClE,IAAI,YAAAiF,yBAAAxF,CAAA;MAAA,YAAAA,CAAA,IAAyF2C,gBAAgB,EA3N1BjL,EAAE,CAAA0K,QAAA,CA2N0CjH,QAAQ,GA3NpDzD,EAAE,CAAA0K,QAAA,CA2N+DpK,MAAM,GA3NvEN,EAAE,CAAA0K,QAAA,CA2NkFnK,SAAS,MA3N7FP,EAAE,CAAA0K,QAAA,CA2NwHlK,WAAW;IAAA,CAA6C;EAAE;EACrR;IAAS,IAAI,CAACuI,KAAK,kBA5N8E/I,EAAE,CAAAgJ,kBAAA;MAAAC,KAAA,EA4NYgC,gBAAgB;MAAA/B,OAAA,EAAhB+B,gBAAgB,CAAApC;IAAA,EAAG;EAAE;AACxI;AACA;EAAA,QAAAnB,SAAA,oBAAAA,SAAA,KA9NqG1H,EAAE,CAAAmJ,iBAAA,CA8NX8B,gBAAgB,EAAc,CAAC;IAC/G7B,IAAI,EAAEjJ;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEiJ,IAAI,EAAE2E,QAAQ;IAAEnD,UAAU,EAAE,CAAC;MAC9CxB,IAAI,EAAE/I,MAAM;MACZwK,IAAI,EAAE,CAACpH,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAE2F,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCxB,IAAI,EAAE/I,MAAM;MACZwK,IAAI,EAAE,CAACvK,MAAM;IACjB,CAAC;EAAE,CAAC,EAAE;IAAE8I,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCxB,IAAI,EAAE/I,MAAM;MACZwK,IAAI,EAAE,CAACtK,SAAS;IACpB,CAAC,EAAE;MACC6I,IAAI,EAAE3I;IACV,CAAC;EAAE,CAAC,EAAE;IAAE2I,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCxB,IAAI,EAAE/I,MAAM;MACZwK,IAAI,EAAE,CAACrK,WAAW;IACtB,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,MAAMwN,cAAc,GAAG;EACnB,KAAK,EAAE,4BAA4B;EACnC,OAAO,EAAE,8BAA8B;EACvC,OAAO,EAAE,8BAA8B;EACvC,KAAK,EAAE,sCAAsC;EAC7C,OAAO,EAAE,+BAA+B;EACxC,MAAM,EAAE;AACZ,CAAC;AACD,MAAMC,eAAe,GAAG,SAAS;AACjC,MAAMC,kBAAkB,GAAG,QAAQ;AACnC,MAAMC,SAAS,GAAG,WAAWD,kBAAkB,EAAE;AACjD,MAAME,YAAY,GAAG,cAAcF,kBAAkB,EAAE;AACvD;AACA;AACA;AACA,MAAMG,0CAA0C,GAAG,IAAI;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kCAAkC,GAAG,IAAIlO,cAAc,CAACsH,SAAS,GAAG,2BAA2B,GAAG,EAAE,EAAE;EACxG6G,UAAU,EAAE,MAAM;EAClBrF,OAAO,EAAEA,CAAA,KAAMmF;AACnB,CAAC,CAAC;AACF,SAASG,oBAAoBA,CAACC,gBAAgB,EAAE;EAC5C,OAAOL,YAAY,CAACM,OAAO,CAACT,eAAe,EAAEQ,gBAAgB,CAAC;AAClE;AACA,SAASE,iBAAiBA,CAACF,gBAAgB,EAAE;EACzC,OAAON,SAAS,CAACO,OAAO,CAACT,eAAe,EAAEQ,gBAAgB,CAAC;AAC/D;AACA,SAASG,iBAAiBA,CAACC,MAAM,EAAEhD,MAAM,EAAE;EACvC,OAAOA,MAAM,CAACuB,GAAG,CAAC0B,CAAC,IAAIA,CAAC,CAACJ,OAAO,CAACT,eAAe,EAAEY,MAAM,CAAC,CAAC;AAC9D;AACA,MAAME,mBAAmB,CAAC;EACtBhL,WAAWA,CAACiL,YAAY,EAAEC,gBAAgB,EAAE/D,KAAK,EAAEgE,yBAAyB,EAAEjK,GAAG,EAAEmG,UAAU,EAAE+D,MAAM,EAAEhE,KAAK,GAAG,IAAI,EAAE;IACjH,IAAI,CAAC6D,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAAC/D,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACgE,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACjK,GAAG,GAAGA,GAAG;IACd,IAAI,CAACmG,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAC+D,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAChE,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACiE,gBAAgB,GAAG,IAAI1F,GAAG,CAAC,CAAC;IACjC,IAAI,CAACgC,gBAAgB,GAAGlI,gBAAgB,CAAC4H,UAAU,CAAC;IACpD,IAAI,CAACiE,eAAe,GAChB,IAAIC,mBAAmB,CAACN,YAAY,EAAE/J,GAAG,EAAEkK,MAAM,EAAE,IAAI,CAACzD,gBAAgB,CAAC;EACjF;EACA6D,cAAcA,CAACvF,OAAO,EAAEZ,IAAI,EAAE;IAC1B,IAAI,CAACY,OAAO,IAAI,CAACZ,IAAI,EAAE;MACnB,OAAO,IAAI,CAACiG,eAAe;IAC/B;IACA,IAAI,IAAI,CAAC3D,gBAAgB,IAAItC,IAAI,CAACoG,aAAa,KAAK9O,iBAAiB,CAAC+O,SAAS,EAAE;MAC7E;MACArG,IAAI,GAAG;QAAE,GAAGA,IAAI;QAAEoG,aAAa,EAAE9O,iBAAiB,CAACgP;MAAS,CAAC;IACjE;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACC,mBAAmB,CAAC5F,OAAO,EAAEZ,IAAI,CAAC;IACxD;IACA;IACA,IAAIuG,QAAQ,YAAYE,iCAAiC,EAAE;MACvDF,QAAQ,CAACG,WAAW,CAAC9F,OAAO,CAAC;IACjC,CAAC,MACI,IAAI2F,QAAQ,YAAYI,4BAA4B,EAAE;MACvDJ,QAAQ,CAACK,WAAW,CAAC,CAAC;IAC1B;IACA,OAAOL,QAAQ;EACnB;EACAC,mBAAmBA,CAAC5F,OAAO,EAAEZ,IAAI,EAAE;IAC/B,MAAMgG,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAC9C,IAAIO,QAAQ,GAAGP,gBAAgB,CAAC/E,GAAG,CAACjB,IAAI,CAAC6G,EAAE,CAAC;IAC5C,IAAI,CAACN,QAAQ,EAAE;MACX,MAAM1K,GAAG,GAAG,IAAI,CAACA,GAAG;MACpB,MAAMkK,MAAM,GAAG,IAAI,CAACA,MAAM;MAC1B,MAAMH,YAAY,GAAG,IAAI,CAACA,YAAY;MACtC,MAAMC,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;MAC9C,MAAMC,yBAAyB,GAAG,IAAI,CAACA,yBAAyB;MAChE,MAAMxD,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;MAC9C,QAAQtC,IAAI,CAACoG,aAAa;QACtB,KAAK9O,iBAAiB,CAACgP,QAAQ;UAC3BC,QAAQ,GAAG,IAAIE,iCAAiC,CAACb,YAAY,EAAEC,gBAAgB,EAAE7F,IAAI,EAAE,IAAI,CAAC8B,KAAK,EAAEgE,yBAAyB,EAAEjK,GAAG,EAAEkK,MAAM,EAAEzD,gBAAgB,CAAC;UAC5J;QACJ,KAAKhL,iBAAiB,CAAC+O,SAAS;UAC5B,OAAO,IAAIS,iBAAiB,CAAClB,YAAY,EAAEC,gBAAgB,EAAEjF,OAAO,EAAEZ,IAAI,EAAEnE,GAAG,EAAEkK,MAAM,EAAE,IAAI,CAAChE,KAAK,EAAEO,gBAAgB,CAAC;QAC1H;UACIiE,QAAQ,GAAG,IAAII,4BAA4B,CAACf,YAAY,EAAEC,gBAAgB,EAAE7F,IAAI,EAAE8F,yBAAyB,EAAEjK,GAAG,EAAEkK,MAAM,EAAEzD,gBAAgB,CAAC;UAC3I;MACR;MACA0D,gBAAgB,CAAC5E,GAAG,CAACpB,IAAI,CAAC6G,EAAE,EAAEN,QAAQ,CAAC;IAC3C;IACA,OAAOA,QAAQ;EACnB;EACAvD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACgD,gBAAgB,CAAC/C,KAAK,CAAC,CAAC;EACjC;EACA;IAAS,IAAI,CAACxD,IAAI,YAAAsH,4BAAA7H,CAAA;MAAA,YAAAA,CAAA,IAAyFyG,mBAAmB,EAjV7B/O,EAAE,CAAA0K,QAAA,CAiV6CpB,YAAY,GAjV3DtJ,EAAE,CAAA0K,QAAA,CAiVsEO,gBAAgB,GAjVxFjL,EAAE,CAAA0K,QAAA,CAiVmGpK,MAAM,GAjV3GN,EAAE,CAAA0K,QAAA,CAiVsH4D,kCAAkC,GAjV1JtO,EAAE,CAAA0K,QAAA,CAiVqKjH,QAAQ,GAjV/KzD,EAAE,CAAA0K,QAAA,CAiV0LlK,WAAW,GAjVvMR,EAAE,CAAA0K,QAAA,CAiVkN1K,EAAE,CAACqB,MAAM,GAjV7NrB,EAAE,CAAA0K,QAAA,CAiVwOnK,SAAS;IAAA,CAA6C;EAAE;EACnY;IAAS,IAAI,CAACwI,KAAK,kBAlV8E/I,EAAE,CAAAgJ,kBAAA;MAAAC,KAAA,EAkVY8F,mBAAmB;MAAA7F,OAAA,EAAnB6F,mBAAmB,CAAAlG;IAAA,EAAG;EAAE;AAC3I;AACA;EAAA,QAAAnB,SAAA,oBAAAA,SAAA,KApVqG1H,EAAE,CAAAmJ,iBAAA,CAoVX4F,mBAAmB,EAAc,CAAC;IAClH3F,IAAI,EAAEjJ;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEiJ,IAAI,EAAEE;EAAa,CAAC,EAAE;IAAEF,IAAI,EAAE6B;EAAiB,CAAC,EAAE;IAAE7B,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MACnGxB,IAAI,EAAE/I,MAAM;MACZwK,IAAI,EAAE,CAACvK,MAAM;IACjB,CAAC;EAAE,CAAC,EAAE;IAAE8I,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCxB,IAAI,EAAE/I,MAAM;MACZwK,IAAI,EAAE,CAACyD,kCAAkC;IAC7C,CAAC;EAAE,CAAC,EAAE;IAAElF,IAAI,EAAE2E,QAAQ;IAAEnD,UAAU,EAAE,CAAC;MACjCxB,IAAI,EAAE/I,MAAM;MACZwK,IAAI,EAAE,CAACpH,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAE2F,IAAI,EAAEgH,MAAM;IAAExF,UAAU,EAAE,CAAC;MAC/BxB,IAAI,EAAE/I,MAAM;MACZwK,IAAI,EAAE,CAACrK,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAE4I,IAAI,EAAEpJ,EAAE,CAACqB;EAAO,CAAC,EAAE;IAAE+H,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MACvDxB,IAAI,EAAE/I,MAAM;MACZwK,IAAI,EAAE,CAACtK,SAAS;IACpB,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB,MAAM+O,mBAAmB,CAAC;EACtBvL,WAAWA,CAACiL,YAAY,EAAE/J,GAAG,EAAEkK,MAAM,EAAEzD,gBAAgB,EAAE;IACrD,IAAI,CAACsD,YAAY,GAAGA,YAAY;IAChC,IAAI,CAAC/J,GAAG,GAAGA,GAAG;IACd,IAAI,CAACkK,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACzD,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAAC2E,IAAI,GAAGD,MAAM,CAACE,MAAM,CAAC,IAAI,CAAC;IAC/B;AACR;AACA;AACA;IACQ,IAAI,CAACC,qBAAqB,GAAG,IAAI;IACjC,IAAI,CAACC,WAAW,GAAG,IAAI;EAC3B;EACAC,OAAOA,CAAA,EAAG,CAAE;EACZ1L,aAAaA,CAAC4B,IAAI,EAAE+J,SAAS,EAAE;IAC3B,IAAIA,SAAS,EAAE;MACX;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,OAAO,IAAI,CAACzL,GAAG,CAAC0L,eAAe,CAAC3C,cAAc,CAAC0C,SAAS,CAAC,IAAIA,SAAS,EAAE/J,IAAI,CAAC;IACjF;IACA,OAAO,IAAI,CAAC1B,GAAG,CAACF,aAAa,CAAC4B,IAAI,CAAC;EACvC;EACAiK,aAAaA,CAACC,KAAK,EAAE;IACjB,OAAO,IAAI,CAAC5L,GAAG,CAAC2L,aAAa,CAACC,KAAK,CAAC;EACxC;EACAC,UAAUA,CAACD,KAAK,EAAE;IACd,OAAO,IAAI,CAAC5L,GAAG,CAAC8L,cAAc,CAACF,KAAK,CAAC;EACzC;EACAjD,WAAWA,CAACoD,MAAM,EAAEC,QAAQ,EAAE;IAC1B,MAAMC,YAAY,GAAGC,cAAc,CAACH,MAAM,CAAC,GAAGA,MAAM,CAACI,OAAO,GAAGJ,MAAM;IACrEE,YAAY,CAACtD,WAAW,CAACqD,QAAQ,CAAC;EACtC;EACAI,YAAYA,CAACL,MAAM,EAAEC,QAAQ,EAAEK,QAAQ,EAAE;IACrC,IAAIN,MAAM,EAAE;MACR,MAAME,YAAY,GAAGC,cAAc,CAACH,MAAM,CAAC,GAAGA,MAAM,CAACI,OAAO,GAAGJ,MAAM;MACrEE,YAAY,CAACG,YAAY,CAACJ,QAAQ,EAAEK,QAAQ,CAAC;IACjD;EACJ;EACAxM,WAAWA,CAACkM,MAAM,EAAEO,QAAQ,EAAE;IAC1B,IAAIP,MAAM,EAAE;MACRA,MAAM,CAAClM,WAAW,CAACyM,QAAQ,CAAC;IAChC;EACJ;EACAC,iBAAiBA,CAACC,cAAc,EAAEC,eAAe,EAAE;IAC/C,IAAIrN,EAAE,GAAG,OAAOoN,cAAc,KAAK,QAAQ,GAAG,IAAI,CAACxM,GAAG,CAAC4B,aAAa,CAAC4K,cAAc,CAAC,GAChFA,cAAc;IAClB,IAAI,CAACpN,EAAE,EAAE;MACL,MAAM,IAAInE,aAAa,CAAC,CAAC,IAAI,CAAC,4CAA4C,CAAC,OAAOwH,SAAS,KAAK,WAAW,IAAIA,SAAS,KACpH,iBAAiB+J,cAAc,8BAA8B,CAAC;IACtE;IACA,IAAI,CAACC,eAAe,EAAE;MAClBrN,EAAE,CAAC6I,WAAW,GAAG,EAAE;IACvB;IACA,OAAO7I,EAAE;EACb;EACAQ,UAAUA,CAACD,IAAI,EAAE;IACb,OAAOA,IAAI,CAACC,UAAU;EAC1B;EACA8M,WAAWA,CAAC/M,IAAI,EAAE;IACd,OAAOA,IAAI,CAAC+M,WAAW;EAC3B;EACAhE,YAAYA,CAACtJ,EAAE,EAAEsC,IAAI,EAAEkK,KAAK,EAAEH,SAAS,EAAE;IACrC,IAAIA,SAAS,EAAE;MACX/J,IAAI,GAAG+J,SAAS,GAAG,GAAG,GAAG/J,IAAI;MAC7B,MAAMiL,YAAY,GAAG5D,cAAc,CAAC0C,SAAS,CAAC;MAC9C,IAAIkB,YAAY,EAAE;QACdvN,EAAE,CAACwN,cAAc,CAACD,YAAY,EAAEjL,IAAI,EAAEkK,KAAK,CAAC;MAChD,CAAC,MACI;QACDxM,EAAE,CAACsJ,YAAY,CAAChH,IAAI,EAAEkK,KAAK,CAAC;MAChC;IACJ,CAAC,MACI;MACDxM,EAAE,CAACsJ,YAAY,CAAChH,IAAI,EAAEkK,KAAK,CAAC;IAChC;EACJ;EACAnD,eAAeA,CAACrJ,EAAE,EAAEsC,IAAI,EAAE+J,SAAS,EAAE;IACjC,IAAIA,SAAS,EAAE;MACX,MAAMkB,YAAY,GAAG5D,cAAc,CAAC0C,SAAS,CAAC;MAC9C,IAAIkB,YAAY,EAAE;QACdvN,EAAE,CAACyN,iBAAiB,CAACF,YAAY,EAAEjL,IAAI,CAAC;MAC5C,CAAC,MACI;QACDtC,EAAE,CAACqJ,eAAe,CAAC,GAAGgD,SAAS,IAAI/J,IAAI,EAAE,CAAC;MAC9C;IACJ,CAAC,MACI;MACDtC,EAAE,CAACqJ,eAAe,CAAC/G,IAAI,CAAC;IAC5B;EACJ;EACAoL,QAAQA,CAAC1N,EAAE,EAAEsC,IAAI,EAAE;IACftC,EAAE,CAAC2N,SAAS,CAACvF,GAAG,CAAC9F,IAAI,CAAC;EAC1B;EACAsL,WAAWA,CAAC5N,EAAE,EAAEsC,IAAI,EAAE;IAClBtC,EAAE,CAAC2N,SAAS,CAACrN,MAAM,CAACgC,IAAI,CAAC;EAC7B;EACAuL,QAAQA,CAAC7N,EAAE,EAAEyH,KAAK,EAAE+E,KAAK,EAAEsB,KAAK,EAAE;IAC9B,IAAIA,KAAK,IAAIxR,mBAAmB,CAACyR,QAAQ,GAAGzR,mBAAmB,CAAC0R,SAAS,CAAC,EAAE;MACxEhO,EAAE,CAACyH,KAAK,CAACwG,WAAW,CAACxG,KAAK,EAAE+E,KAAK,EAAEsB,KAAK,GAAGxR,mBAAmB,CAAC0R,SAAS,GAAG,WAAW,GAAG,EAAE,CAAC;IAChG,CAAC,MACI;MACDhO,EAAE,CAACyH,KAAK,CAACA,KAAK,CAAC,GAAG+E,KAAK;IAC3B;EACJ;EACA0B,WAAWA,CAAClO,EAAE,EAAEyH,KAAK,EAAEqG,KAAK,EAAE;IAC1B,IAAIA,KAAK,GAAGxR,mBAAmB,CAACyR,QAAQ,EAAE;MACtC;MACA/N,EAAE,CAACyH,KAAK,CAAC0G,cAAc,CAAC1G,KAAK,CAAC;IAClC,CAAC,MACI;MACDzH,EAAE,CAACyH,KAAK,CAACA,KAAK,CAAC,GAAG,EAAE;IACxB;EACJ;EACAwG,WAAWA,CAACjO,EAAE,EAAEsC,IAAI,EAAEkK,KAAK,EAAE;IACzB,IAAIxM,EAAE,IAAI,IAAI,EAAE;MACZ;IACJ;IACA,CAAC,OAAOqD,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAK,IAAI,CAAC6I,qBAAqB,IACzEkC,oBAAoB,CAAC9L,IAAI,EAAE,UAAU,CAAC;IAC1CtC,EAAE,CAACsC,IAAI,CAAC,GAAGkK,KAAK;EACpB;EACA6B,QAAQA,CAAC9N,IAAI,EAAEiM,KAAK,EAAE;IAClBjM,IAAI,CAAC+N,SAAS,GAAG9B,KAAK;EAC1B;EACA+B,MAAMA,CAAC9M,MAAM,EAAE+M,KAAK,EAAE/K,QAAQ,EAAE;IAC5B,CAAC,OAAOJ,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAK,IAAI,CAAC6I,qBAAqB,IACzEkC,oBAAoB,CAACI,KAAK,EAAE,UAAU,CAAC;IAC3C,IAAI,OAAO/M,MAAM,KAAK,QAAQ,EAAE;MAC5BA,MAAM,GAAGvC,OAAO,CAAC,CAAC,CAACsC,oBAAoB,CAAC,IAAI,CAACZ,GAAG,EAAEa,MAAM,CAAC;MACzD,IAAI,CAACA,MAAM,EAAE;QACT,MAAM,IAAIgN,KAAK,CAAC,4BAA4BhN,MAAM,cAAc+M,KAAK,EAAE,CAAC;MAC5E;IACJ;IACA,OAAO,IAAI,CAAC7D,YAAY,CAACxK,gBAAgB,CAACsB,MAAM,EAAE+M,KAAK,EAAE,IAAI,CAACE,sBAAsB,CAACjL,QAAQ,CAAC,CAAC;EACnG;EACAiL,sBAAsBA,CAACC,YAAY,EAAE;IACjC;IACA;IACA;IACA;IACA,OAAQH,KAAK,IAAK;MACd;MACA;MACA;MACA;MACA;MACA,IAAIA,KAAK,KAAK,cAAc,EAAE;QAC1B,OAAOG,YAAY;MACvB;MACA;MACA;MACA,MAAMC,oBAAoB,GAAG,IAAI,CAACvH,gBAAgB,GAC9C,IAAI,CAACyD,MAAM,CAAC+D,UAAU,CAAC,MAAMF,YAAY,CAACH,KAAK,CAAC,CAAC,GACjDG,YAAY,CAACH,KAAK,CAAC;MACvB,IAAII,oBAAoB,KAAK,KAAK,EAAE;QAChCJ,KAAK,CAACM,cAAc,CAAC,CAAC;MAC1B;MACA,OAAOxI,SAAS;IACpB,CAAC;EACL;AACJ;AACA,MAAMyI,WAAW,GAAG,CAAC,MAAM,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;AAC/C,SAASZ,oBAAoBA,CAAC9L,IAAI,EAAE2M,QAAQ,EAAE;EAC1C,IAAI3M,IAAI,CAAC0M,UAAU,CAAC,CAAC,CAAC,KAAKD,WAAW,EAAE;IACpC,MAAM,IAAIlT,aAAa,CAAC,IAAI,CAAC,sDAAsD,wBAAwBoT,QAAQ,IAAI3M,IAAI;AACnI;AACA,qEAAqEA,IAAI,gIAAgI,CAAC;EACtM;AACJ;AACA,SAASwK,cAAcA,CAACvM,IAAI,EAAE;EAC1B,OAAOA,IAAI,CAACI,OAAO,KAAK,UAAU,IAAIJ,IAAI,CAACwM,OAAO,KAAKzG,SAAS;AACpE;AACA,MAAMuF,iBAAiB,SAASZ,mBAAmB,CAAC;EAChDvL,WAAWA,CAACiL,YAAY,EAAEC,gBAAgB,EAAEsE,MAAM,EAAEC,SAAS,EAAEvO,GAAG,EAAEkK,MAAM,EAAEhE,KAAK,EAAEO,gBAAgB,EAAE;IACjG,KAAK,CAACsD,YAAY,EAAE/J,GAAG,EAAEkK,MAAM,EAAEzD,gBAAgB,CAAC;IAClD,IAAI,CAACuD,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACsE,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACE,UAAU,GAAGF,MAAM,CAACG,YAAY,CAAC;MAAEC,IAAI,EAAE;IAAO,CAAC,CAAC;IACvD,IAAI,CAAC1E,gBAAgB,CAAC1C,OAAO,CAAC,IAAI,CAACkH,UAAU,CAAC;IAC9C,MAAM5H,MAAM,GAAG+C,iBAAiB,CAAC4E,SAAS,CAACvD,EAAE,EAAEuD,SAAS,CAAC3H,MAAM,CAAC;IAChE,KAAK,MAAMC,KAAK,IAAID,MAAM,EAAE;MACxB,MAAM4B,OAAO,GAAGrI,QAAQ,CAACL,aAAa,CAAC,OAAO,CAAC;MAC/C,IAAIoG,KAAK,EAAE;QACPsC,OAAO,CAACE,YAAY,CAAC,OAAO,EAAExC,KAAK,CAAC;MACxC;MACAsC,OAAO,CAACP,WAAW,GAAGpB,KAAK;MAC3B,IAAI,CAAC2H,UAAU,CAAC7F,WAAW,CAACH,OAAO,CAAC;IACxC;EACJ;EACAmG,gBAAgBA,CAAChP,IAAI,EAAE;IACnB,OAAOA,IAAI,KAAK,IAAI,CAAC2O,MAAM,GAAG,IAAI,CAACE,UAAU,GAAG7O,IAAI;EACxD;EACAgJ,WAAWA,CAACoD,MAAM,EAAEC,QAAQ,EAAE;IAC1B,OAAO,KAAK,CAACrD,WAAW,CAAC,IAAI,CAACgG,gBAAgB,CAAC5C,MAAM,CAAC,EAAEC,QAAQ,CAAC;EACrE;EACAI,YAAYA,CAACL,MAAM,EAAEC,QAAQ,EAAEK,QAAQ,EAAE;IACrC,OAAO,KAAK,CAACD,YAAY,CAAC,IAAI,CAACuC,gBAAgB,CAAC5C,MAAM,CAAC,EAAEC,QAAQ,EAAEK,QAAQ,CAAC;EAChF;EACAxM,WAAWA,CAACkM,MAAM,EAAEO,QAAQ,EAAE;IAC1B,OAAO,KAAK,CAACzM,WAAW,CAAC,IAAI,CAAC8O,gBAAgB,CAAC5C,MAAM,CAAC,EAAEO,QAAQ,CAAC;EACrE;EACA1M,UAAUA,CAACD,IAAI,EAAE;IACb,OAAO,IAAI,CAACgP,gBAAgB,CAAC,KAAK,CAAC/O,UAAU,CAAC,IAAI,CAAC+O,gBAAgB,CAAChP,IAAI,CAAC,CAAC,CAAC;EAC/E;EACA6L,OAAOA,CAAA,EAAG;IACN,IAAI,CAACxB,gBAAgB,CAACtC,UAAU,CAAC,IAAI,CAAC8G,UAAU,CAAC;EACrD;AACJ;AACA,MAAM1D,4BAA4B,SAAST,mBAAmB,CAAC;EAC3DvL,WAAWA,CAACiL,YAAY,EAAEC,gBAAgB,EAAEuE,SAAS,EAAEtE,yBAAyB,EAAEjK,GAAG,EAAEkK,MAAM,EAAEzD,gBAAgB,EAAEmD,MAAM,EAAE;IACrH,KAAK,CAACG,YAAY,EAAE/J,GAAG,EAAEkK,MAAM,EAAEzD,gBAAgB,CAAC;IAClD,IAAI,CAACuD,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACrD,MAAM,GAAGgD,MAAM,GAAGD,iBAAiB,CAACC,MAAM,EAAE2E,SAAS,CAAC3H,MAAM,CAAC,GAAG2H,SAAS,CAAC3H,MAAM;EACzF;EACAmE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACf,gBAAgB,CAACrD,SAAS,CAAC,IAAI,CAACC,MAAM,CAAC;EAChD;EACA4E,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACvB,yBAAyB,EAAE;MACjC;IACJ;IACA,IAAI,CAACD,gBAAgB,CAAC/C,YAAY,CAAC,IAAI,CAACL,MAAM,CAAC;EACnD;AACJ;AACA,MAAMgE,iCAAiC,SAASE,4BAA4B,CAAC;EACzEhM,WAAWA,CAACiL,YAAY,EAAEC,gBAAgB,EAAEuE,SAAS,EAAEtI,KAAK,EAAEgE,yBAAyB,EAAEjK,GAAG,EAAEkK,MAAM,EAAEzD,gBAAgB,EAAE;IACpH,MAAMmD,MAAM,GAAG3D,KAAK,GAAG,GAAG,GAAGsI,SAAS,CAACvD,EAAE;IACzC,KAAK,CAACjB,YAAY,EAAEC,gBAAgB,EAAEuE,SAAS,EAAEtE,yBAAyB,EAAEjK,GAAG,EAAEkK,MAAM,EAAEzD,gBAAgB,EAAEmD,MAAM,CAAC;IAClH,IAAI,CAACgF,WAAW,GAAGrF,oBAAoB,CAACK,MAAM,CAAC;IAC/C,IAAI,CAACiF,QAAQ,GAAGnF,iBAAiB,CAACE,MAAM,CAAC;EAC7C;EACAiB,WAAWA,CAAC9F,OAAO,EAAE;IACjB,IAAI,CAACgG,WAAW,CAAC,CAAC;IAClB,IAAI,CAACrC,YAAY,CAAC3D,OAAO,EAAE,IAAI,CAAC8J,QAAQ,EAAE,EAAE,CAAC;EACjD;EACA/O,aAAaA,CAACiM,MAAM,EAAErK,IAAI,EAAE;IACxB,MAAMtC,EAAE,GAAG,KAAK,CAACU,aAAa,CAACiM,MAAM,EAAErK,IAAI,CAAC;IAC5C,KAAK,CAACgH,YAAY,CAACtJ,EAAE,EAAE,IAAI,CAACwP,WAAW,EAAE,EAAE,CAAC;IAC5C,OAAOxP,EAAE;EACb;AACJ;AAEA,MAAM0P,eAAe,SAASjJ,kBAAkB,CAAC;EAC7C/G,WAAWA,CAACkB,GAAG,EAAE;IACb,KAAK,CAACA,GAAG,CAAC;EACd;EACA;EACA;EACAsF,QAAQA,CAACN,SAAS,EAAE;IAChB,OAAO,IAAI;EACf;EACAzF,gBAAgBA,CAACwF,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAE;IAC1CF,OAAO,CAACxF,gBAAgB,CAACyF,SAAS,EAAEC,OAAO,EAAE,KAAK,CAAC;IACnD,OAAO,MAAM,IAAI,CAACzF,mBAAmB,CAACuF,OAAO,EAAEC,SAAS,EAAEC,OAAO,CAAC;EACtE;EACAzF,mBAAmBA,CAACqB,MAAM,EAAEmE,SAAS,EAAEnC,QAAQ,EAAE;IAC7C,OAAOhC,MAAM,CAACrB,mBAAmB,CAACwF,SAAS,EAAEnC,QAAQ,CAAC;EAC1D;EACA;IAAS,IAAI,CAACe,IAAI,YAAAmL,wBAAA1L,CAAA;MAAA,YAAAA,CAAA,IAAyFyL,eAAe,EAjnBzB/T,EAAE,CAAA0K,QAAA,CAinByCjH,QAAQ;IAAA,CAA6C;EAAE;EACnM;IAAS,IAAI,CAACsF,KAAK,kBAlnB8E/I,EAAE,CAAAgJ,kBAAA;MAAAC,KAAA,EAknBY8K,eAAe;MAAA7K,OAAA,EAAf6K,eAAe,CAAAlL;IAAA,EAAG;EAAE;AACvI;AACA;EAAA,QAAAnB,SAAA,oBAAAA,SAAA,KApnBqG1H,EAAE,CAAAmJ,iBAAA,CAonBX4K,eAAe,EAAc,CAAC;IAC9G3K,IAAI,EAAEjJ;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEiJ,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CxB,IAAI,EAAE/I,MAAM;MACZwK,IAAI,EAAE,CAACpH,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA,MAAMwQ,aAAa,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC;AACzD;AACA;AACA,MAAMC,OAAO,GAAG;EACZ,IAAI,EAAE,WAAW;EACjB,IAAI,EAAE,KAAK;EACX,MAAM,EAAE,QAAQ;EAChB,MAAM,EAAE,QAAQ;EAChB,KAAK,EAAE,QAAQ;EACf,KAAK,EAAE,QAAQ;EACf,MAAM,EAAE,WAAW;EACnB,OAAO,EAAE,YAAY;EACrB,IAAI,EAAE,SAAS;EACf,MAAM,EAAE,WAAW;EACnB,MAAM,EAAE,aAAa;EACrB,QAAQ,EAAE,YAAY;EACtB,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA,MAAMC,oBAAoB,GAAG;EACzB,KAAK,EAAGtB,KAAK,IAAKA,KAAK,CAACuB,MAAM;EAC9B,SAAS,EAAGvB,KAAK,IAAKA,KAAK,CAACwB,OAAO;EACnC,MAAM,EAAGxB,KAAK,IAAKA,KAAK,CAACyB,OAAO;EAChC,OAAO,EAAGzB,KAAK,IAAKA,KAAK,CAAC0B;AAC9B,CAAC;AACD;AACA;AACA;AACA,MAAMC,eAAe,SAAS1J,kBAAkB,CAAC;EAC7C;AACJ;AACA;AACA;EACI/G,WAAWA,CAACkB,GAAG,EAAE;IACb,KAAK,CAACA,GAAG,CAAC;EACd;EACA;AACJ;AACA;AACA;AACA;EACIsF,QAAQA,CAACN,SAAS,EAAE;IAChB,OAAOuK,eAAe,CAACC,cAAc,CAACxK,SAAS,CAAC,IAAI,IAAI;EAC5D;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIzF,gBAAgBA,CAACwF,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAE;IAC1C,MAAMwK,WAAW,GAAGF,eAAe,CAACC,cAAc,CAACxK,SAAS,CAAC;IAC7D,MAAM0K,cAAc,GAAGH,eAAe,CAACI,aAAa,CAACF,WAAW,CAAC,SAAS,CAAC,EAAExK,OAAO,EAAE,IAAI,CAACN,OAAO,CAACQ,OAAO,CAAC,CAAC,CAAC;IAC7G,OAAO,IAAI,CAACR,OAAO,CAACQ,OAAO,CAAC,CAAC,CAACyK,iBAAiB,CAAC,MAAM;MAClD,OAAOtR,OAAO,CAAC,CAAC,CAACa,WAAW,CAAC4F,OAAO,EAAE0K,WAAW,CAAC,cAAc,CAAC,EAAEC,cAAc,CAAC;IACtF,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOF,cAAcA,CAACxK,SAAS,EAAE;IAC7B,MAAM6K,KAAK,GAAG7K,SAAS,CAAC8K,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;IAChD,MAAMC,YAAY,GAAGH,KAAK,CAACI,KAAK,CAAC,CAAC;IAClC,IAAKJ,KAAK,CAAC7M,MAAM,KAAK,CAAC,IAAK,EAAEgN,YAAY,KAAK,SAAS,IAAIA,YAAY,KAAK,OAAO,CAAC,EAAE;MACnF,OAAO,IAAI;IACf;IACA,MAAME,GAAG,GAAGX,eAAe,CAACY,aAAa,CAACN,KAAK,CAACO,GAAG,CAAC,CAAC,CAAC;IACtD,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAIC,MAAM,GAAGT,KAAK,CAACU,OAAO,CAAC,MAAM,CAAC;IAClC,IAAID,MAAM,GAAG,CAAC,CAAC,EAAE;MACbT,KAAK,CAACW,MAAM,CAACF,MAAM,EAAE,CAAC,CAAC;MACvBD,OAAO,GAAG,OAAO;IACrB;IACArB,aAAa,CAAC9L,OAAO,CAACuN,YAAY,IAAI;MAClC,MAAMC,KAAK,GAAGb,KAAK,CAACU,OAAO,CAACE,YAAY,CAAC;MACzC,IAAIC,KAAK,GAAG,CAAC,CAAC,EAAE;QACZb,KAAK,CAACW,MAAM,CAACE,KAAK,EAAE,CAAC,CAAC;QACtBL,OAAO,IAAII,YAAY,GAAG,GAAG;MACjC;IACJ,CAAC,CAAC;IACFJ,OAAO,IAAIH,GAAG;IACd,IAAIL,KAAK,CAAC7M,MAAM,IAAI,CAAC,IAAIkN,GAAG,CAAClN,MAAM,KAAK,CAAC,EAAE;MACvC;MACA,OAAO,IAAI;IACf;IACA;IACA;IACA;IACA,MAAM2N,MAAM,GAAG,CAAC,CAAC;IACjBA,MAAM,CAAC,cAAc,CAAC,GAAGX,YAAY;IACrCW,MAAM,CAAC,SAAS,CAAC,GAAGN,OAAO;IAC3B,OAAOM,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,qBAAqBA,CAAChD,KAAK,EAAEiD,WAAW,EAAE;IAC7C,IAAIC,OAAO,GAAG7B,OAAO,CAACrB,KAAK,CAACsC,GAAG,CAAC,IAAItC,KAAK,CAACsC,GAAG;IAC7C,IAAIA,GAAG,GAAG,EAAE;IACZ,IAAIW,WAAW,CAACN,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;MACnCO,OAAO,GAAGlD,KAAK,CAACmD,IAAI;MACpBb,GAAG,GAAG,OAAO;IACjB;IACA;IACA,IAAIY,OAAO,IAAI,IAAI,IAAI,CAACA,OAAO,EAC3B,OAAO,KAAK;IAChBA,OAAO,GAAGA,OAAO,CAAChB,WAAW,CAAC,CAAC;IAC/B,IAAIgB,OAAO,KAAK,GAAG,EAAE;MACjBA,OAAO,GAAG,OAAO,CAAC,CAAC;IACvB,CAAC,MACI,IAAIA,OAAO,KAAK,GAAG,EAAE;MACtBA,OAAO,GAAG,KAAK,CAAC,CAAC;IACrB;IACA9B,aAAa,CAAC9L,OAAO,CAACuN,YAAY,IAAI;MAClC,IAAIA,YAAY,KAAKK,OAAO,EAAE;QAC1B,MAAME,cAAc,GAAG9B,oBAAoB,CAACuB,YAAY,CAAC;QACzD,IAAIO,cAAc,CAACpD,KAAK,CAAC,EAAE;UACvBsC,GAAG,IAAIO,YAAY,GAAG,GAAG;QAC7B;MACJ;IACJ,CAAC,CAAC;IACFP,GAAG,IAAIY,OAAO;IACd,OAAOZ,GAAG,KAAKW,WAAW;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,OAAOlB,aAAaA,CAACU,OAAO,EAAEpL,OAAO,EAAEgM,IAAI,EAAE;IACzC,OAAQrD,KAAK,IAAK;MACd,IAAI2B,eAAe,CAACqB,qBAAqB,CAAChD,KAAK,EAAEyC,OAAO,CAAC,EAAE;QACvDY,IAAI,CAAChD,UAAU,CAAC,MAAMhJ,OAAO,CAAC2I,KAAK,CAAC,CAAC;MACzC;IACJ,CAAC;EACL;EACA;EACA,OAAOuC,aAAaA,CAACe,OAAO,EAAE;IAC1B,OAAOA,OAAO,KAAK,KAAK,GAAG,QAAQ,GAAGA,OAAO;EACjD;EACA;IAAS,IAAI,CAACtN,IAAI,YAAAuN,wBAAA9N,CAAA;MAAA,YAAAA,CAAA,IAAyFkM,eAAe,EA7xBzBxU,EAAE,CAAA0K,QAAA,CA6xByCjH,QAAQ;IAAA,CAA6C;EAAE;EACnM;IAAS,IAAI,CAACsF,KAAK,kBA9xB8E/I,EAAE,CAAAgJ,kBAAA;MAAAC,KAAA,EA8xBYuL,eAAe;MAAAtL,OAAA,EAAfsL,eAAe,CAAA3L;IAAA,EAAG;EAAE;AACvI;AACA;EAAA,QAAAnB,SAAA,oBAAAA,SAAA,KAhyBqG1H,EAAE,CAAAmJ,iBAAA,CAgyBXqL,eAAe,EAAc,CAAC;IAC9GpL,IAAI,EAAEjJ;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEiJ,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CxB,IAAI,EAAE/I,MAAM;MACZwK,IAAI,EAAE,CAACpH,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS4S,oBAAoBA,CAACC,aAAa,EAAEC,OAAO,EAAE;EAClD,OAAO3V,0BAA0B,CAAC;IAAE0V,aAAa;IAAE,GAAGE,qBAAqB,CAACD,OAAO;EAAE,CAAC,CAAC;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,iBAAiBA,CAACF,OAAO,EAAE;EAChC,OAAO3V,0BAA0B,CAAC4V,qBAAqB,CAACD,OAAO,CAAC,CAAC;AACrE;AACA,SAASC,qBAAqBA,CAACD,OAAO,EAAE;EACpC,OAAO;IACHG,YAAY,EAAE,CACV,GAAGC,wBAAwB,EAC3B,IAAIJ,OAAO,EAAEK,SAAS,IAAI,EAAE,CAAC,CAChC;IACDC,iBAAiB,EAAEC;EACvB,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,+BAA+BA,CAAA,EAAG;EACvC;EACA;EACA;EACA,OAAO,CAAC,GAAGC,qBAAqB,CAAC;AACrC;AACA,SAASC,cAAcA,CAAA,EAAG;EACtB/S,iBAAiB,CAACC,WAAW,CAAC,CAAC;AACnC;AACA,SAAS+S,YAAYA,CAAA,EAAG;EACpB,OAAO,IAAIrW,YAAY,CAAC,CAAC;AAC7B;AACA,SAASsW,SAASA,CAAA,EAAG;EACjB;EACArW,YAAY,CAACsE,QAAQ,CAAC;EACtB,OAAOA,QAAQ;AACnB;AACA,MAAM0R,mCAAmC,GAAG,CACxC;EAAEM,OAAO,EAAE5W,WAAW;EAAE6W,QAAQ,EAAE3T;AAAqB,CAAC,EACxD;EAAE0T,OAAO,EAAErW,oBAAoB;EAAEsW,QAAQ,EAAEJ,cAAc;EAAEK,KAAK,EAAE;AAAK,CAAC,EACxE;EAAEF,OAAO,EAAE3T,QAAQ;EAAE8T,UAAU,EAAEJ,SAAS;EAAEK,IAAI,EAAE;AAAG,CAAC,CACzD;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAGzW,qBAAqB,CAACC,YAAY,EAAE,SAAS,EAAE6V,mCAAmC,CAAC;AAC3G;AACA;AACA;AACA;AACA;AACA;AACA,MAAMY,+BAA+B,GAAG,IAAItX,cAAc,CAAE,OAAOsH,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAI,gCAAgC,GAAG,EAAE,CAAC;AACnJ,MAAMsP,qBAAqB,GAAG,CAC1B;EACII,OAAO,EAAElW,mBAAmB;EAC5ByW,QAAQ,EAAExQ,qBAAqB;EAC/BqQ,IAAI,EAAE;AACV,CAAC,EACD;EACIJ,OAAO,EAAEjW,YAAY;EACrBwW,QAAQ,EAAEvW,WAAW;EACrBoW,IAAI,EAAE,CAACnW,MAAM,EAAEC,mBAAmB,EAAEJ,mBAAmB;AAC3D,CAAC,EACD;EACIkW,OAAO,EAAEhW,WAAW;EAAE;EACtBuW,QAAQ,EAAEvW,WAAW;EACrBoW,IAAI,EAAE,CAACnW,MAAM,EAAEC,mBAAmB,EAAEJ,mBAAmB;AAC3D,CAAC,CACJ;AACD,MAAMyV,wBAAwB,GAAG,CAC7B;EAAES,OAAO,EAAE7V,eAAe;EAAE8V,QAAQ,EAAE;AAAO,CAAC,EAC9C;EAAED,OAAO,EAAEvW,YAAY;EAAE0W,UAAU,EAAEL,YAAY;EAAEM,IAAI,EAAE;AAAG,CAAC,EAAE;EAC3DJ,OAAO,EAAE/N,qBAAqB;EAC9BsO,QAAQ,EAAE5D,eAAe;EACzBuD,KAAK,EAAE,IAAI;EACXE,IAAI,EAAE,CAAC/T,QAAQ,EAAEpC,MAAM,EAAEb,WAAW;AACxC,CAAC,EACD;EAAE4W,OAAO,EAAE/N,qBAAqB;EAAEsO,QAAQ,EAAEnD,eAAe;EAAE8C,KAAK,EAAE,IAAI;EAAEE,IAAI,EAAE,CAAC/T,QAAQ;AAAE,CAAC,EAC5FsL,mBAAmB,EAAE9D,gBAAgB,EAAE3B,YAAY,EACnD;EAAE8N,OAAO,EAAE5V,gBAAgB;EAAEoW,WAAW,EAAE7I;AAAoB,CAAC,EAC/D;EAAEqI,OAAO,EAAEzT,UAAU;EAAEgU,QAAQ,EAAEjP,UAAU;EAAE8O,IAAI,EAAE;AAAG,CAAC,EACtD,OAAO9P,SAAS,KAAK,WAAW,IAAIA,SAAS,GAC1C;EAAE0P,OAAO,EAAEM,+BAA+B;EAAEL,QAAQ,EAAE;AAAK,CAAC,GAC5D,EAAE,CACT;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMQ,aAAa,CAAC;EAChB9T,WAAWA,CAAC+T,uBAAuB,EAAE;IACjC,IAAI,CAAC,OAAOpQ,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKoQ,uBAAuB,EAAE;MAC5E,MAAM,IAAI5X,aAAa,CAAC,IAAI,CAAC,sDAAsD,oFAAoF,GACnK,mFAAmF,CAAC;IAC5F;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAO6X,oBAAoBA,CAACC,MAAM,EAAE;IAChC,OAAO;MACHC,QAAQ,EAAEJ,aAAa;MACvBjB,SAAS,EAAE,CACP;QAAEQ,OAAO,EAAE9W,MAAM;QAAE+W,QAAQ,EAAEW,MAAM,CAAC9M;MAAM,CAAC;IAEnD,CAAC;EACL;EACA;IAAS,IAAI,CAACrC,IAAI,YAAAqP,sBAAA5P,CAAA;MAAA,YAAAA,CAAA,IAAyFuP,aAAa,EA/+BvB7X,EAAE,CAAA0K,QAAA,CA++BuCgN,+BAA+B;IAAA,CAA2E;EAAE;EACtP;IAAS,IAAI,CAACS,IAAI,kBAh/B+EnY,EAAE,CAAAoY,gBAAA;MAAAhP,IAAA,EAg/BSyO;IAAa,EAA+C;EAAE;EAC1K;IAAS,IAAI,CAACQ,IAAI,kBAj/B+ErY,EAAE,CAAAsY,gBAAA;MAAA1B,SAAA,EAi/BmC,CAAC,GAAGD,wBAAwB,EAAE,GAAGK,qBAAqB,CAAC;MAAAuB,OAAA,GAAY3U,YAAY,EAAEnC,iBAAiB;IAAA,EAAI;EAAE;AAClP;AACA;EAAA,QAAAiG,SAAA,oBAAAA,SAAA,KAn/BqG1H,EAAE,CAAAmJ,iBAAA,CAm/BX0O,aAAa,EAAc,CAAC;IAC5GzO,IAAI,EAAE1H,QAAQ;IACdmJ,IAAI,EAAE,CAAC;MACC+L,SAAS,EAAE,CAAC,GAAGD,wBAAwB,EAAE,GAAGK,qBAAqB,CAAC;MAClEwB,OAAO,EAAE,CAAC5U,YAAY,EAAEnC,iBAAiB;IAC7C,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE2H,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CxB,IAAI,EAAE3I;IACV,CAAC,EAAE;MACC2I,IAAI,EAAEzH;IACV,CAAC,EAAE;MACCyH,IAAI,EAAE/I,MAAM;MACZwK,IAAI,EAAE,CAAC6M,+BAA+B;IAC1C,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMe,IAAI,CAAC;EACP1U,WAAWA,CAACgH,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC2N,IAAI,GAAGnV,OAAO,CAAC,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIoV,MAAMA,CAACC,GAAG,EAAEC,aAAa,GAAG,KAAK,EAAE;IAC/B,IAAI,CAACD,GAAG,EACJ,OAAO,IAAI;IACf,OAAO,IAAI,CAACE,mBAAmB,CAACF,GAAG,EAAEC,aAAa,CAAC;EACvD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,OAAOA,CAACC,IAAI,EAAEH,aAAa,GAAG,KAAK,EAAE;IACjC,IAAI,CAACG,IAAI,EACL,OAAO,EAAE;IACb,OAAOA,IAAI,CAACC,MAAM,CAAC,CAACrD,MAAM,EAAEgD,GAAG,KAAK;MAChC,IAAIA,GAAG,EAAE;QACLhD,MAAM,CAACvN,IAAI,CAAC,IAAI,CAACyQ,mBAAmB,CAACF,GAAG,EAAEC,aAAa,CAAC,CAAC;MAC7D;MACA,OAAOjD,MAAM;IACjB,CAAC,EAAE,EAAE,CAAC;EACV;EACA;AACJ;AACA;AACA;AACA;AACA;EACIsD,MAAMA,CAACC,YAAY,EAAE;IACjB,IAAI,CAACA,YAAY,EACb,OAAO,IAAI;IACf,OAAO,IAAI,CAACpO,IAAI,CAAClE,aAAa,CAAC,QAAQsS,YAAY,GAAG,CAAC,IAAI,IAAI;EACnE;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,OAAOA,CAACD,YAAY,EAAE;IAClB,IAAI,CAACA,YAAY,EACb,OAAO,EAAE;IACb,MAAME,IAAI,CAAC,eAAe,IAAI,CAACtO,IAAI,CAACiC,gBAAgB,CAAC,QAAQmM,YAAY,GAAG,CAAC;IAC7E,OAAOE,IAAI,GAAG,EAAE,CAACvP,KAAK,CAACwP,IAAI,CAACD,IAAI,CAAC,GAAG,EAAE;EAC1C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,SAASA,CAACX,GAAG,EAAEY,QAAQ,EAAE;IACrB,IAAI,CAACZ,GAAG,EACJ,OAAO,IAAI;IACfY,QAAQ,GAAGA,QAAQ,IAAI,IAAI,CAACC,cAAc,CAACb,GAAG,CAAC;IAC/C,MAAMc,IAAI,GAAG,IAAI,CAACR,MAAM,CAACM,QAAQ,CAAC;IAClC,IAAIE,IAAI,EAAE;MACN,OAAO,IAAI,CAACC,yBAAyB,CAACf,GAAG,EAAEc,IAAI,CAAC;IACpD;IACA,OAAO,IAAI,CAACZ,mBAAmB,CAACF,GAAG,EAAE,IAAI,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;EACIgB,SAASA,CAACT,YAAY,EAAE;IACpB,IAAI,CAACU,gBAAgB,CAAC,IAAI,CAACX,MAAM,CAACC,YAAY,CAAC,CAAC;EACpD;EACA;AACJ;AACA;AACA;EACIU,gBAAgBA,CAACH,IAAI,EAAE;IACnB,IAAIA,IAAI,EAAE;MACN,IAAI,CAAChB,IAAI,CAAC/T,MAAM,CAAC+U,IAAI,CAAC;IAC1B;EACJ;EACAZ,mBAAmBA,CAACY,IAAI,EAAEb,aAAa,GAAG,KAAK,EAAE;IAC7C,IAAI,CAACA,aAAa,EAAE;MAChB,MAAMW,QAAQ,GAAG,IAAI,CAACC,cAAc,CAACC,IAAI,CAAC;MAC1C;MACA;MACA;MACA,MAAMpS,IAAI,GAAG,IAAI,CAAC8R,OAAO,CAACI,QAAQ,CAAC,CAACM,MAAM,CAACxS,IAAI,IAAI,IAAI,CAACyS,mBAAmB,CAACL,IAAI,EAAEpS,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3F,IAAIA,IAAI,KAAKqD,SAAS,EAClB,OAAOrD,IAAI;IACnB;IACA,MAAM0C,OAAO,GAAG,IAAI,CAAC0O,IAAI,CAAC3T,aAAa,CAAC,MAAM,CAAC;IAC/C,IAAI,CAAC4U,yBAAyB,CAACD,IAAI,EAAE1P,OAAO,CAAC;IAC7C,MAAM+C,IAAI,GAAG,IAAI,CAAChC,IAAI,CAACiP,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACtDjN,IAAI,CAACa,WAAW,CAAC5D,OAAO,CAAC;IACzB,OAAOA,OAAO;EAClB;EACA2P,yBAAyBA,CAACf,GAAG,EAAEvU,EAAE,EAAE;IAC/B+L,MAAM,CAACvD,IAAI,CAAC+L,GAAG,CAAC,CAACzQ,OAAO,CAAE8R,IAAI,IAAK5V,EAAE,CAACsJ,YAAY,CAAC,IAAI,CAACuM,cAAc,CAACD,IAAI,CAAC,EAAErB,GAAG,CAACqB,IAAI,CAAC,CAAC,CAAC;IACzF,OAAO5V,EAAE;EACb;EACAoV,cAAcA,CAACb,GAAG,EAAE;IAChB,MAAMuB,IAAI,GAAGvB,GAAG,CAACjS,IAAI,GAAG,MAAM,GAAG,UAAU;IAC3C,OAAO,GAAGwT,IAAI,KAAKvB,GAAG,CAACuB,IAAI,CAAC,GAAG;EACnC;EACAJ,mBAAmBA,CAACnB,GAAG,EAAEtR,IAAI,EAAE;IAC3B,OAAO8I,MAAM,CAACvD,IAAI,CAAC+L,GAAG,CAAC,CAACwB,KAAK,CAAEjF,GAAG,IAAK7N,IAAI,CAACR,YAAY,CAAC,IAAI,CAACoT,cAAc,CAAC/E,GAAG,CAAC,CAAC,KAAKyD,GAAG,CAACzD,GAAG,CAAC,CAAC;EACpG;EACA+E,cAAcA,CAACD,IAAI,EAAE;IACjB,OAAOI,aAAa,CAACJ,IAAI,CAAC,IAAIA,IAAI;EACtC;EACA;IAAS,IAAI,CAACpR,IAAI,YAAAyR,aAAAhS,CAAA;MAAA,YAAAA,CAAA,IAAyFmQ,IAAI,EAvpCdzY,EAAE,CAAA0K,QAAA,CAupC8BjH,QAAQ;IAAA,CAA6C;EAAE;EACxL;IAAS,IAAI,CAACsF,KAAK,kBAxpC8E/I,EAAE,CAAAgJ,kBAAA;MAAAC,KAAA,EAwpCYwP,IAAI;MAAAvP,OAAA,EAAJuP,IAAI,CAAA5P,IAAA;MAAA0F,UAAA,EAAc;IAAM,EAAG;EAAE;AAChJ;AACA;EAAA,QAAA7G,SAAA,oBAAAA,SAAA,KA1pCqG1H,EAAE,CAAAmJ,iBAAA,CA0pCXsP,IAAI,EAAc,CAAC;IACnGrP,IAAI,EAAEjJ,UAAU;IAChB0K,IAAI,EAAE,CAAC;MAAE0D,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEnF,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CxB,IAAI,EAAE/I,MAAM;MACZwK,IAAI,EAAE,CAACpH,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB;AACA;AACA;AACA,MAAM4W,aAAa,GAAG;EAClBE,SAAS,EAAE;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,KAAK,CAAC;EACRzW,WAAWA,CAACgH,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACA;AACJ;AACA;EACI0P,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC1P,IAAI,CAAC2P,KAAK;EAC1B;EACA;AACJ;AACA;AACA;EACIC,QAAQA,CAACC,QAAQ,EAAE;IACf,IAAI,CAAC7P,IAAI,CAAC2P,KAAK,GAAGE,QAAQ,IAAI,EAAE;EACpC;EACA;IAAS,IAAI,CAAC/R,IAAI,YAAAgS,cAAAvS,CAAA;MAAA,YAAAA,CAAA,IAAyFkS,KAAK,EAnsCfxa,EAAE,CAAA0K,QAAA,CAmsC+BjH,QAAQ;IAAA,CAA6C;EAAE;EACzL;IAAS,IAAI,CAACsF,KAAK,kBApsC8E/I,EAAE,CAAAgJ,kBAAA;MAAAC,KAAA,EAosCYuR,KAAK;MAAAtR,OAAA,EAALsR,KAAK,CAAA3R,IAAA;MAAA0F,UAAA,EAAc;IAAM,EAAG;EAAE;AACjJ;AACA;EAAA,QAAA7G,SAAA,oBAAAA,SAAA,KAtsCqG1H,EAAE,CAAAmJ,iBAAA,CAssCXqR,KAAK,EAAc,CAAC;IACpGpR,IAAI,EAAEjJ,UAAU;IAChB0K,IAAI,EAAE,CAAC;MAAE0D,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEnF,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CxB,IAAI,EAAE/I,MAAM;MACZwK,IAAI,EAAE,CAACpH,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqX,WAAWA,CAACnU,IAAI,EAAEkK,KAAK,EAAE;EAC9B,IAAI,OAAOkK,QAAQ,KAAK,WAAW,IAAI,CAACA,QAAQ,EAAE;IAC9C;IACA;IACA;IACA;IACA,MAAMC,EAAE,GAAG/a,OAAO,CAAC,IAAI,CAAC,GAAGA,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9C+a,EAAE,CAACrU,IAAI,CAAC,GAAGkK,KAAK;EACpB;AACJ;AAEA,MAAMoK,yBAAyB,CAAC;EAC5BlX,WAAWA,CAACmX,SAAS,EAAEC,QAAQ,EAAE;IAC7B,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC5B;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAClBrX,WAAWA,CAACsX,GAAG,EAAE;IACb,IAAI,CAACC,MAAM,GAAGD,GAAG,CAACE,QAAQ,CAAClR,GAAG,CAACzI,cAAc,CAAC;EAClD;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI4Z,mBAAmBA,CAACC,MAAM,EAAE;IACxB,MAAMC,MAAM,GAAGD,MAAM,IAAIA,MAAM,CAAC,QAAQ,CAAC;IACzC,MAAME,WAAW,GAAG,kBAAkB;IACtC;IACA,IAAID,MAAM,IAAI,SAAS,IAAIE,OAAO,IAAI,OAAOA,OAAO,CAACC,OAAO,KAAK,UAAU,EAAE;MACzED,OAAO,CAACC,OAAO,CAACF,WAAW,CAAC;IAChC;IACA,MAAMG,KAAK,GAAGC,WAAW,CAACC,GAAG,CAAC,CAAC;IAC/B,IAAIb,QAAQ,GAAG,CAAC;IAChB,OAAOA,QAAQ,GAAG,CAAC,IAAKY,WAAW,CAACC,GAAG,CAAC,CAAC,GAAGF,KAAK,GAAI,GAAG,EAAE;MACtD,IAAI,CAACR,MAAM,CAACW,IAAI,CAAC,CAAC;MAClBd,QAAQ,EAAE;IACd;IACA,MAAMe,GAAG,GAAGH,WAAW,CAACC,GAAG,CAAC,CAAC;IAC7B,IAAIN,MAAM,IAAI,YAAY,IAAIE,OAAO,IAAI,OAAOA,OAAO,CAACO,UAAU,KAAK,UAAU,EAAE;MAC/EP,OAAO,CAACO,UAAU,CAACR,WAAW,CAAC;IACnC;IACA,MAAMT,SAAS,GAAG,CAACgB,GAAG,GAAGJ,KAAK,IAAIX,QAAQ;IAC1CS,OAAO,CAACQ,GAAG,CAAC,OAAOjB,QAAQ,0BAA0B,CAAC;IACtDS,OAAO,CAACQ,GAAG,CAAC,GAAGlB,SAAS,CAACmB,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;IACnD,OAAO,IAAIpB,yBAAyB,CAACC,SAAS,EAAEC,QAAQ,CAAC;EAC7D;AACJ;AAEA,MAAMmB,oBAAoB,GAAG,UAAU;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAAClB,GAAG,EAAE;EAC3BP,WAAW,CAACwB,oBAAoB,EAAE,IAAIlB,eAAe,CAACC,GAAG,CAAC,CAAC;EAC3D,OAAOA,GAAG;AACd;AACA;AACA;AACA;AACA;AACA;AACA,SAASmB,iBAAiBA,CAAA,EAAG;EACzB1B,WAAW,CAACwB,oBAAoB,EAAE,IAAI,CAAC;AAC3C;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMG,EAAE,CAAC;EACL;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,GAAGA,CAAA,EAAG;IACT,OAAO,MAAM,IAAI;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,GAAGA,CAACnD,QAAQ,EAAE;IACjB,OAAQoD,YAAY,IAAK;MACrB,OAAOA,YAAY,CAACC,aAAa,IAAI,IAAI,GACrCC,cAAc,CAACF,YAAY,CAACC,aAAa,EAAErD,QAAQ,CAAC,GACpD,KAAK;IACb,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOuD,SAASA,CAAC3T,IAAI,EAAE;IACnB,OAAQ4T,SAAS,IAAKA,SAAS,CAACC,cAAc,CAACzH,OAAO,CAACpM,IAAI,CAAC,KAAK,CAAC,CAAC;EACvE;AACJ;AACA,SAAS0T,cAAcA,CAACI,CAAC,EAAE1D,QAAQ,EAAE;EACjC,IAAIjW,OAAO,CAAC,CAAC,CAACgC,aAAa,CAAC2X,CAAC,CAAC,EAAE;IAC5B,OAAOA,CAAC,CAACC,OAAO,IAAID,CAAC,CAACC,OAAO,CAAC3D,QAAQ,CAAC,IACnC0D,CAAC,CAACE,iBAAiB,IAAIF,CAAC,CAACE,iBAAiB,CAAC5D,QAAQ,CAAC,IACpD0D,CAAC,CAACG,qBAAqB,IAAIH,CAAC,CAACG,qBAAqB,CAAC7D,QAAQ,CAAC;EACpE;EACA,OAAO,KAAK;AAChB;;AAEA;AACA;AACA;AACA,MAAM8D,WAAW,GAAG;EAChB;EACA,KAAK,EAAE,IAAI;EACX,UAAU,EAAE,IAAI;EAChB,SAAS,EAAE,IAAI;EACf,QAAQ,EAAE,IAAI;EACd,WAAW,EAAE,IAAI;EACjB,SAAS,EAAE,IAAI;EACf,UAAU,EAAE,IAAI;EAChB,OAAO,EAAE,IAAI;EACb,SAAS,EAAE,IAAI;EACf;EACA,OAAO,EAAE,IAAI;EACb,YAAY,EAAE,IAAI;EAClB,WAAW,EAAE,IAAI;EACjB,UAAU,EAAE,IAAI;EAChB,aAAa,EAAE,IAAI;EACnB,SAAS,EAAE,IAAI;EACf,UAAU,EAAE,IAAI;EAChB;EACA,OAAO,EAAE,IAAI;EACb,SAAS,EAAE,IAAI;EACf;EACA,QAAQ,EAAE,IAAI;EACd,aAAa,EAAE,IAAI;EACnB,YAAY,EAAE,IAAI;EAClB,WAAW,EAAE,IAAI;EACjB,cAAc,EAAE,IAAI;EACpB;EACA,OAAO,EAAE,IAAI;EACb,WAAW,EAAE,IAAI;EACjB,YAAY,EAAE,IAAI;EAClB,SAAS,EAAE,IAAI;EACf,WAAW,EAAE,IAAI;EACjB;EACA,KAAK,EAAE,IAAI;EACX,WAAW,EAAE;AACjB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAG,IAAInd,cAAc,CAAC,qBAAqB,CAAC;AACvE;AACA;AACA;AACA;AACA;AACA,MAAMod,aAAa,GAAG,IAAIpd,cAAc,CAAC,cAAc,CAAC;AACxD;AACA;AACA;AACA;AACA;AACA,MAAMqd,mBAAmB,CAAC;EACtB1Z,WAAWA,CAAA,EAAG;IACV;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC2Z,MAAM,GAAG,EAAE;IAChB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;EACvB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAAC5T,OAAO,EAAE;IACjB,MAAM6T,EAAE,GAAG,IAAIC,MAAM,CAAC9T,OAAO,EAAE,IAAI,CAACuM,OAAO,CAAC;IAC5CsH,EAAE,CAACxT,GAAG,CAAC,OAAO,CAAC,CAACG,GAAG,CAAC;MAAEuT,MAAM,EAAE;IAAK,CAAC,CAAC;IACrCF,EAAE,CAACxT,GAAG,CAAC,QAAQ,CAAC,CAACG,GAAG,CAAC;MAAEuT,MAAM,EAAE;IAAK,CAAC,CAAC;IACtC,KAAK,MAAM9T,SAAS,IAAI,IAAI,CAAC0T,SAAS,EAAE;MACpCE,EAAE,CAACxT,GAAG,CAACJ,SAAS,CAAC,CAACO,GAAG,CAAC,IAAI,CAACmT,SAAS,CAAC1T,SAAS,CAAC,CAAC;IACpD;IACA,OAAO4T,EAAE;EACb;EACA;IAAS,IAAI,CAAChV,IAAI,YAAAmV,4BAAA1V,CAAA;MAAA,YAAAA,CAAA,IAAyFmV,mBAAmB;IAAA,CAAoD;EAAE;EACpL;IAAS,IAAI,CAAC1U,KAAK,kBA78C8E/I,EAAE,CAAAgJ,kBAAA;MAAAC,KAAA,EA68CYwU,mBAAmB;MAAAvU,OAAA,EAAnBuU,mBAAmB,CAAA5U;IAAA,EAAG;EAAE;AAC3I;AACA;EAAA,QAAAnB,SAAA,oBAAAA,SAAA,KA/8CqG1H,EAAE,CAAAmJ,iBAAA,CA+8CXsU,mBAAmB,EAAc,CAAC;IAClHrU,IAAI,EAAEjJ;EACV,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA,MAAM8d,oBAAoB,SAASnT,kBAAkB,CAAC;EAClD/G,WAAWA,CAACkB,GAAG,EAAEiZ,OAAO,EAAEtC,OAAO,EAAEuC,MAAM,EAAE;IACvC,KAAK,CAAClZ,GAAG,CAAC;IACV,IAAI,CAACiZ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACtC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACuC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,cAAc,GAAG,IAAI;EAC9B;EACA7T,QAAQA,CAACN,SAAS,EAAE;IAChB,IAAI,CAACqT,WAAW,CAACe,cAAc,CAACpU,SAAS,CAAC8K,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAACuJ,aAAa,CAACrU,SAAS,CAAC,EAAE;MACxF,OAAO,KAAK;IAChB;IACA,IAAI,CAAClE,MAAM,CAAC+X,MAAM,IAAI,CAAC,IAAI,CAACK,MAAM,EAAE;MAChC,IAAI,OAAOzW,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;QAC/C,IAAI,CAACkU,OAAO,CAAC2C,IAAI,CAAC,QAAQtU,SAAS,mDAAmD,GAClF,iDAAiD,CAAC;MAC1D;MACA,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf;EACAzF,gBAAgBA,CAACwF,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAE;IAC1C,MAAMgM,IAAI,GAAG,IAAI,CAACtM,OAAO,CAACQ,OAAO,CAAC,CAAC;IACnCH,SAAS,GAAGA,SAAS,CAAC8K,WAAW,CAAC,CAAC;IACnC;IACA;IACA,IAAI,CAAChP,MAAM,CAAC+X,MAAM,IAAI,IAAI,CAACK,MAAM,EAAE;MAC/B,IAAI,CAACC,cAAc,GAAG,IAAI,CAACA,cAAc,IAAIlI,IAAI,CAACrB,iBAAiB,CAAC,MAAM,IAAI,CAACsJ,MAAM,CAAC,CAAC,CAAC;MACxF;MACA;MACA;MACA,IAAIK,kBAAkB,GAAG,KAAK;MAC9B,IAAIC,UAAU,GAAGA,CAAA,KAAM;QACnBD,kBAAkB,GAAG,IAAI;MAC7B,CAAC;MACDtI,IAAI,CAACrB,iBAAiB,CAAC,MAAM,IAAI,CAACuJ,cAAc,CAC3CM,IAAI,CAAC,MAAM;QACZ;QACA,IAAI,CAAC3Y,MAAM,CAAC+X,MAAM,EAAE;UAChB,IAAI,OAAOpW,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;YAC/C,IAAI,CAACkU,OAAO,CAAC2C,IAAI,CAAC,mEAAmE,CAAC;UAC1F;UACAE,UAAU,GAAGA,CAAA,KAAM,CAAE,CAAC;UACtB;QACJ;QACA,IAAI,CAACD,kBAAkB,EAAE;UACrB;UACA;UACA;UACAC,UAAU,GAAG,IAAI,CAACja,gBAAgB,CAACwF,OAAO,EAAEC,SAAS,EAAEC,OAAO,CAAC;QACnE;MACJ,CAAC,CAAC,CACGyU,KAAK,CAAC,MAAM;QACb,IAAI,OAAOjX,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;UAC/C,IAAI,CAACkU,OAAO,CAAC2C,IAAI,CAAC,QAAQtU,SAAS,6CAA6C,GAC5E,0BAA0B,CAAC;QACnC;QACAwU,UAAU,GAAGA,CAAA,KAAM,CAAE,CAAC;MAC1B,CAAC,CAAC,CAAC;MACH;MACA;MACA;MACA,OAAO,MAAM;QACTA,UAAU,CAAC,CAAC;MAChB,CAAC;IACL;IACA,OAAOvI,IAAI,CAACrB,iBAAiB,CAAC,MAAM;MAChC;MACA,MAAMgJ,EAAE,GAAG,IAAI,CAACK,OAAO,CAACN,WAAW,CAAC5T,OAAO,CAAC;MAC5C,MAAMlC,QAAQ,GAAG,SAAAA,CAAU8W,QAAQ,EAAE;QACjC1I,IAAI,CAAChD,UAAU,CAAC,YAAY;UACxBhJ,OAAO,CAAC0U,QAAQ,CAAC;QACrB,CAAC,CAAC;MACN,CAAC;MACDf,EAAE,CAACgB,EAAE,CAAC5U,SAAS,EAAEnC,QAAQ,CAAC;MAC1B,OAAO,MAAM;QACT+V,EAAE,CAACiB,GAAG,CAAC7U,SAAS,EAAEnC,QAAQ,CAAC;QAC3B;QACA,IAAI,OAAO+V,EAAE,CAACpN,OAAO,KAAK,UAAU,EAAE;UAClCoN,EAAE,CAACpN,OAAO,CAAC,CAAC;QAChB;MACJ,CAAC;IACL,CAAC,CAAC;EACN;EACA6N,aAAaA,CAACrU,SAAS,EAAE;IACrB,OAAO,IAAI,CAACiU,OAAO,CAACR,MAAM,CAAClI,OAAO,CAACvL,SAAS,CAAC,GAAG,CAAC,CAAC;EACtD;EACA;IAAS,IAAI,CAACpB,IAAI,YAAAkW,6BAAAzW,CAAA;MAAA,YAAAA,CAAA,IAAyF2V,oBAAoB,EA9iD9Bje,EAAE,CAAA0K,QAAA,CA8iD8CjH,QAAQ,GA9iDxDzD,EAAE,CAAA0K,QAAA,CA8iDmE6S,qBAAqB,GA9iD1Fvd,EAAE,CAAA0K,QAAA,CA8iDqG1K,EAAE,CAAC6B,QAAQ,GA9iDlH7B,EAAE,CAAA0K,QAAA,CA8iD6H8S,aAAa;IAAA,CAA6D;EAAE;EAC5S;IAAS,IAAI,CAACzU,KAAK,kBA/iD8E/I,EAAE,CAAAgJ,kBAAA;MAAAC,KAAA,EA+iDYgV,oBAAoB;MAAA/U,OAAA,EAApB+U,oBAAoB,CAAApV;IAAA,EAAG;EAAE;AAC5I;AACA;EAAA,QAAAnB,SAAA,oBAAAA,SAAA,KAjjDqG1H,EAAE,CAAAmJ,iBAAA,CAijDX8U,oBAAoB,EAAc,CAAC;IACnH7U,IAAI,EAAEjJ;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEiJ,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CxB,IAAI,EAAE/I,MAAM;MACZwK,IAAI,EAAE,CAACpH,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAE2F,IAAI,EAAEqU,mBAAmB;IAAE7S,UAAU,EAAE,CAAC;MAC5CxB,IAAI,EAAE/I,MAAM;MACZwK,IAAI,EAAE,CAAC0S,qBAAqB;IAChC,CAAC;EAAE,CAAC,EAAE;IAAEnU,IAAI,EAAEpJ,EAAE,CAAC6B;EAAS,CAAC,EAAE;IAAEuH,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MACzDxB,IAAI,EAAE3I;IACV,CAAC,EAAE;MACC2I,IAAI,EAAE/I,MAAM;MACZwK,IAAI,EAAE,CAAC2S,aAAa;IACxB,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwB,YAAY,CAAC;EACf;IAAS,IAAI,CAACnW,IAAI,YAAAoW,qBAAA3W,CAAA;MAAA,YAAAA,CAAA,IAAyF0W,YAAY;IAAA,CAAkD;EAAE;EAC3K;IAAS,IAAI,CAAC7G,IAAI,kBA5kD+EnY,EAAE,CAAAoY,gBAAA;MAAAhP,IAAA,EA4kDS4V;IAAY,EAAG;EAAE;EAC7H;IAAS,IAAI,CAAC3G,IAAI,kBA7kD+ErY,EAAE,CAAAsY,gBAAA;MAAA1B,SAAA,EA6kDkC,CAC7H;QACIQ,OAAO,EAAE/N,qBAAqB;QAC9BsO,QAAQ,EAAEsG,oBAAoB;QAC9B3G,KAAK,EAAE,IAAI;QACXE,IAAI,EAAE,CAAC/T,QAAQ,EAAE8Z,qBAAqB,EAAE1b,QAAQ,EAAE,CAAC,IAAIpB,QAAQ,CAAC,CAAC,EAAE+c,aAAa,CAAC;MACrF,CAAC,EACD;QAAEpG,OAAO,EAAEmG,qBAAqB;QAAE5F,QAAQ,EAAE8F,mBAAmB;QAAEjG,IAAI,EAAE;MAAG,CAAC;IAC9E,EAAG;EAAE;AACd;AACA;EAAA,QAAA9P,SAAA,oBAAAA,SAAA,KAvlDqG1H,EAAE,CAAAmJ,iBAAA,CAulDX6V,YAAY,EAAc,CAAC;IAC3G5V,IAAI,EAAE1H,QAAQ;IACdmJ,IAAI,EAAE,CAAC;MACC+L,SAAS,EAAE,CACP;QACIQ,OAAO,EAAE/N,qBAAqB;QAC9BsO,QAAQ,EAAEsG,oBAAoB;QAC9B3G,KAAK,EAAE,IAAI;QACXE,IAAI,EAAE,CAAC/T,QAAQ,EAAE8Z,qBAAqB,EAAE1b,QAAQ,EAAE,CAAC,IAAIpB,QAAQ,CAAC,CAAC,EAAE+c,aAAa,CAAC;MACrF,CAAC,EACD;QAAEpG,OAAO,EAAEmG,qBAAqB;QAAE5F,QAAQ,EAAE8F,mBAAmB;QAAEjG,IAAI,EAAE;MAAG,CAAC;IAEnF,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0H,YAAY,CAAC;EACf;IAAS,IAAI,CAACrW,IAAI,YAAAsW,qBAAA7W,CAAA;MAAA,YAAAA,CAAA,IAAyF4W,YAAY;IAAA,CAAoD;EAAE;EAC7K;IAAS,IAAI,CAACnW,KAAK,kBAvoD8E/I,EAAE,CAAAgJ,kBAAA;MAAAC,KAAA,EAuoDYiW,YAAY;MAAAhW,OAAA,WAAAiW,qBAAA7W,CAAA;QAAA,IAAA8W,CAAA;QAAA,IAAA9W,CAAA;UAAA8W,CAAA,QAAA9W,CAAA,IAAZ4W,YAAY;QAAA;UAAAE,CAAA,GAvoD1Bpf,EAAE,CAAA0K,QAAA,CAuoD+E2U,gBAAgB;QAAA;QAAA,OAAAD,CAAA;MAAA;MAAA7Q,UAAA,EAAzD;IAAM,EAAuD;EAAE;AAC5M;AACA;EAAA,QAAA7G,SAAA,oBAAAA,SAAA,KAzoDqG1H,EAAE,CAAAmJ,iBAAA,CAyoDX+V,YAAY,EAAc,CAAC;IAC3G9V,IAAI,EAAEjJ,UAAU;IAChB0K,IAAI,EAAE,CAAC;MAAE0D,UAAU,EAAE,MAAM;MAAEqJ,WAAW,EAAE9V,UAAU,CAAC,MAAMud,gBAAgB;IAAE,CAAC;EAClF,CAAC,CAAC;AAAA;AACV,MAAMA,gBAAgB,SAASH,YAAY,CAAC;EACxCnb,WAAWA,CAACgH,IAAI,EAAE;IACd,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACAuU,QAAQA,CAACC,GAAG,EAAE1O,KAAK,EAAE;IACjB,IAAIA,KAAK,IAAI,IAAI,EACb,OAAO,IAAI;IACf,QAAQ0O,GAAG;MACP,KAAKvd,eAAe,CAACwd,IAAI;QACrB,OAAO3O,KAAK;MAChB,KAAK7O,eAAe,CAACyd,IAAI;QACrB,IAAIxd,gCAAgC,CAAC4O,KAAK,EAAE,MAAM,CAAC,qBAAqB,CAAC,EAAE;UACvE,OAAO3O,gBAAgB,CAAC2O,KAAK,CAAC;QAClC;QACA,OAAOzO,cAAc,CAAC,IAAI,CAAC2I,IAAI,EAAE2U,MAAM,CAAC7O,KAAK,CAAC,CAAC,CAAC8O,QAAQ,CAAC,CAAC;MAC9D,KAAK3d,eAAe,CAAC4d,KAAK;QACtB,IAAI3d,gCAAgC,CAAC4O,KAAK,EAAE,OAAO,CAAC,sBAAsB,CAAC,EAAE;UACzE,OAAO3O,gBAAgB,CAAC2O,KAAK,CAAC;QAClC;QACA,OAAOA,KAAK;MAChB,KAAK7O,eAAe,CAAC6d,MAAM;QACvB,IAAI5d,gCAAgC,CAAC4O,KAAK,EAAE,QAAQ,CAAC,uBAAuB,CAAC,EAAE;UAC3E,OAAO3O,gBAAgB,CAAC2O,KAAK,CAAC;QAClC;QACA,MAAM,IAAI3Q,aAAa,CAAC,IAAI,CAAC,mDAAmD,CAAC,OAAOwH,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC1H,uCAAuC,CAAC;MAChD,KAAK1F,eAAe,CAACgF,GAAG;QACpB,IAAI/E,gCAAgC,CAAC4O,KAAK,EAAE,KAAK,CAAC,oBAAoB,CAAC,EAAE;UACrE,OAAO3O,gBAAgB,CAAC2O,KAAK,CAAC;QAClC;QACA,OAAO1O,aAAa,CAACud,MAAM,CAAC7O,KAAK,CAAC,CAAC;MACvC,KAAK7O,eAAe,CAAC8d,YAAY;QAC7B,IAAI7d,gCAAgC,CAAC4O,KAAK,EAAE,aAAa,CAAC,4BAA4B,CAAC,EAAE;UACrF,OAAO3O,gBAAgB,CAAC2O,KAAK,CAAC;QAClC;QACA,MAAM,IAAI3Q,aAAa,CAAC,IAAI,CAAC,yDAAyD,CAAC,OAAOwH,SAAS,KAAK,WAAW,IAAIA,SAAS,KAChI,oDAAoD3F,iBAAiB,GAAG,CAAC;MACjF;QACI,MAAM,IAAI7B,aAAa,CAAC,IAAI,CAAC,oDAAoD,CAAC,OAAOwH,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC3H,8BAA8B6X,GAAG,SAASxd,iBAAiB,GAAG,CAAC;IAC3E;EACJ;EACAge,uBAAuBA,CAAClP,KAAK,EAAE;IAC3B,OAAOxO,4BAA4B,CAACwO,KAAK,CAAC;EAC9C;EACAmP,wBAAwBA,CAACnP,KAAK,EAAE;IAC5B,OAAOvO,6BAA6B,CAACuO,KAAK,CAAC;EAC/C;EACAoP,yBAAyBA,CAACpP,KAAK,EAAE;IAC7B,OAAOtO,8BAA8B,CAACsO,KAAK,CAAC;EAChD;EACAqP,sBAAsBA,CAACrP,KAAK,EAAE;IAC1B,OAAOrO,2BAA2B,CAACqO,KAAK,CAAC;EAC7C;EACAsP,8BAA8BA,CAACtP,KAAK,EAAE;IAClC,OAAOpO,mCAAmC,CAACoO,KAAK,CAAC;EACrD;EACA;IAAS,IAAI,CAAChI,IAAI,YAAAuX,yBAAA9X,CAAA;MAAA,YAAAA,CAAA,IAAyF+W,gBAAgB,EAvsD1Brf,EAAE,CAAA0K,QAAA,CAusD0CjH,QAAQ;IAAA,CAA6C;EAAE;EACpM;IAAS,IAAI,CAACsF,KAAK,kBAxsD8E/I,EAAE,CAAAgJ,kBAAA;MAAAC,KAAA,EAwsDYoW,gBAAgB;MAAAnW,OAAA,EAAhBmW,gBAAgB,CAAAxW,IAAA;MAAA0F,UAAA,EAAc;IAAM,EAAG;EAAE;AAC5J;AACA;EAAA,QAAA7G,SAAA,oBAAAA,SAAA,KA1sDqG1H,EAAE,CAAAmJ,iBAAA,CA0sDXkW,gBAAgB,EAAc,CAAC;IAC/GjW,IAAI,EAAEjJ,UAAU;IAChB0K,IAAI,EAAE,CAAC;MAAE0D,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEnF,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CxB,IAAI,EAAE/I,MAAM;MACZwK,IAAI,EAAE,CAACpH,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA,IAAI4c,oBAAoB;AACxB,CAAC,UAAUA,oBAAoB,EAAE;EAC7BA,oBAAoB,CAACA,oBAAoB,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,GAAG,qBAAqB;EAC7FA,oBAAoB,CAACA,oBAAoB,CAAC,0BAA0B,CAAC,GAAG,CAAC,CAAC,GAAG,0BAA0B;AAC3G,CAAC,EAAEA,oBAAoB,KAAKA,oBAAoB,GAAG,CAAC,CAAC,CAAC,CAAC;AACvD;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,KAAK,EAAEC,UAAU,GAAG,EAAE,EAAEC,QAAQ,GAAG,CAAC,CAAC,EAAE;EAC7D,OAAO;IAAEF,KAAK;IAAEC;EAAW,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,uBAAuBA,CAAA,EAAG;EAC/B;EACA;EACA,OAAOJ,gBAAgB,CAACD,oBAAoB,CAACM,mBAAmB,CAAC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,4BAA4BA,CAACrK,OAAO,EAAE;EAC3C;EACA,OAAO+J,gBAAgB,CAACD,oBAAoB,CAACQ,wBAAwB,EAAEhd,sBAAsB,CAAC0S,OAAO,CAAC,CAAC;AAC3G;AACA;AACA;AACA;AACA;AACA;AACA,SAASuK,kCAAkCA,CAAA,EAAG;EAC1C,OAAO,CAAC;IACA1J,OAAO,EAAE1U,uBAAuB;IAChC2U,QAAQ,EAAEA,CAAA,KAAM;MACZ,MAAMlI,MAAM,GAAGxM,MAAM,CAACtB,MAAM,CAAC;MAC7B;MACA;MACA,IAAI8N,MAAM,CAACpL,WAAW,KAAK1C,MAAM,EAAE;QAC/B,MAAMua,OAAO,GAAGjZ,MAAM,CAACd,QAAQ,CAAC;QAChC,MAAMkf,OAAO,GAAGne,mBAAmB,CAAC,CAAC,IAAI,CAAC,oDAAoD,iEAAiE,GAC3J,uDAAuD,GACvD,kDAAkD,CAAC;QACvD;QACAgZ,OAAO,CAAC2C,IAAI,CAACwC,OAAO,CAAC;MACzB;IACJ,CAAC;IACDzJ,KAAK,EAAE;EACX,CAAC,CAAC;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0J,sBAAsBA,CAAC,GAAGC,QAAQ,EAAE;EACzC,MAAMrK,SAAS,GAAG,EAAE;EACpB,MAAMsK,YAAY,GAAG,IAAI3V,GAAG,CAAC,CAAC;EAC9B,MAAM4V,2BAA2B,GAAGD,YAAY,CAAC7T,GAAG,CAACgT,oBAAoB,CAACQ,wBAAwB,CAAC;EACnG,KAAK,MAAM;IAAEL,UAAU;IAAED;EAAM,CAAC,IAAIU,QAAQ,EAAE;IAC1CC,YAAY,CAACzU,GAAG,CAAC8T,KAAK,CAAC;IACvB,IAAIC,UAAU,CAACvY,MAAM,EAAE;MACnB2O,SAAS,CAACvO,IAAI,CAACmY,UAAU,CAAC;IAC9B;EACJ;EACA,IAAI,OAAO9Y,SAAS,KAAK,WAAW,IAAIA,SAAS,IAC7CwZ,YAAY,CAAC7T,GAAG,CAACgT,oBAAoB,CAACM,mBAAmB,CAAC,IAAIQ,2BAA2B,EAAE;IAC3F;IACA,MAAM,IAAIrO,KAAK,CAAC,sKAAsK,CAAC;EAC3L;EACA,OAAOjQ,wBAAwB,CAAC,CAC3B,OAAO6E,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAIoZ,kCAAkC,CAAC,CAAC,GAAG,EAAE,EAC3Fhe,iBAAiB,CAAC,CAAC,EACjBoe,YAAY,CAAC7T,GAAG,CAACgT,oBAAoB,CAACM,mBAAmB,CAAC,IAAIQ,2BAA2B,GACvF,EAAE,GACFtd,sBAAsB,CAAC,CAAC,CAAC,CAAC,EAC9B+S,SAAS,CACZ,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwK,OAAO,GAAG,IAAIre,OAAO,CAAC,SAAS,CAAC;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAGC,cAAc;AACnC;AACA,MAAMC,aAAa,GAAGC,eAAe;;AAErC;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA,SAAS0U,aAAa,EAAE4E,EAAE,EAAEyC,YAAY,EAAE7V,qBAAqB,EAAEC,YAAY,EAAEwB,kBAAkB,EAAEyS,qBAAqB,EAAEC,aAAa,EAAEC,mBAAmB,EAAEuB,YAAY,EAAEqB,oBAAoB,EAAE5H,IAAI,EAAEnK,kCAAkC,EAAEkM,KAAK,EAAEtX,aAAa,EAAEke,OAAO,EAAE/K,oBAAoB,EAAEI,iBAAiB,EAAE+F,iBAAiB,EAAED,gBAAgB,EAAEvZ,YAAY,EAAEyU,eAAe,EAAEuJ,sBAAsB,EAAEjK,+BAA+B,EAAE6J,4BAA4B,EAAEF,uBAAuB,EAAExc,iBAAiB,IAAImd,kBAAkB,EAAEla,qBAAqB,IAAIma,sBAAsB,EAAEvN,eAAe,IAAIwN,gBAAgB,EAAExS,mBAAmB,IAAIyS,oBAAoB,EAAEnC,gBAAgB,IAAIoC,iBAAiB,EAAExD,oBAAoB,IAAIyD,qBAAqB,EAAE5K,mCAAmC,IAAI6K,oCAAoC,EAAEnN,eAAe,IAAIoN,gBAAgB,EAAE3W,gBAAgB,IAAI4W,iBAAiB,EAAE5K,cAAc,IAAI6K,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}