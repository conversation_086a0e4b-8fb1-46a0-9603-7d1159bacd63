@use 'sass:math';
@use 'sass:map';
@use '@material/textfield' as mdc-textfield;
@use '@material/density' as mdc-density;
@use '../../token-utils';
@use '../../../theming/theming';
@use '../../../theming/inspection';
@use '../../../style/sass-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, paginator);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return ();
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  $foreground-base: inspection.get-theme-color($theme, foreground, base);

  @return (
    container-text-color: sass-utils.safe-color-change($foreground-base, $alpha: 0.87),
    container-background-color: inspection.get-theme-color($theme, background, card),
    enabled-icon-color: sass-utils.safe-color-change($foreground-base, $alpha: 0.54),
    disabled-icon-color: sass-utils.safe-color-change($foreground-base, $alpha: 0.12),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    container-text-font: inspection.get-theme-typography($theme, caption, font-family),
    container-text-line-height: inspection.get-theme-typography($theme, caption, line-height),
    container-text-size: inspection.get-theme-typography($theme, caption, font-size),
    container-text-tracking: inspection.get-theme-typography($theme, caption, letter-spacing),
    container-text-weight: inspection.get-theme-typography($theme, caption, font-weight),
    select-trigger-text-size: inspection.get-theme-typography($theme, caption, font-size),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $density-scale: theming.clamp-density(inspection.get-theme-density($theme), -5);
  $size-scale: (
    0: 56px,
    -1: 52px,
    -2: 48px,
    -3: 40px,
    -4: 40px,
    -5: 40px,
  );
  $form-field-density-scale: if($density-scale > -4, -4, $density-scale);
  $form-field-height: mdc-density.prop-value(
    $density-config: mdc-textfield.$density-config,
    $density-scale: $form-field-density-scale,
    $property-name: height,
  );
  // We computed the desired height of the form-field using the density configuration. The
  // spec only describes vertical spacing/alignment in non-dense mode. This means that we
  // cannot update the spacing to explicit numbers based on the density scale. Instead, we
  // determine the height reduction and equally subtract it from the default `top` and `bottom`
  // padding that is provided by the Material Design specification.
  $form-field-vertical-deduction: math.div(mdc-textfield.$height - $form-field-height, 2);
  $form-field-vertical-padding: 16px - $form-field-vertical-deduction;

  @return (
    container-size: map.get($size-scale, $density-scale),
    form-field-container-height: $form-field-height,
    form-field-container-vertical-padding: $form-field-vertical-padding,
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-utils.$placeholder-color-config),
      get-typography-tokens(token-utils.$placeholder-typography-config),
      get-density-tokens(token-utils.$placeholder-density-config)
  );
}
