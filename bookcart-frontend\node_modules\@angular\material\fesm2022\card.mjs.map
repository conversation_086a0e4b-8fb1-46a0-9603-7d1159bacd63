{"version": 3, "file": "card.mjs", "sources": ["../../../../../../src/material/card/card.ts", "../../../../../../src/material/card/card.html", "../../../../../../src/material/card/card-title-group.html", "../../../../../../src/material/card/card-header.html", "../../../../../../src/material/card/module.ts", "../../../../../../src/material/card/card_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  Directive,\n  Inject,\n  InjectionToken,\n  Input,\n  Optional,\n  ViewEncapsulation,\n} from '@angular/core';\n\nexport type MatCardAppearance = 'outlined' | 'raised';\n\n/** Object that can be used to configure the default options for the card module. */\nexport interface MatCardConfig {\n  /** Default appearance for cards. */\n  appearance?: MatCardAppearance;\n}\n\n/** Injection token that can be used to provide the default options the card module. */\nexport const MAT_CARD_CONFIG = new InjectionToken<MatCardConfig>('MAT_CARD_CONFIG');\n\n/**\n * Material Design card component. Cards contain content and actions about a single subject.\n * See https://material.io/design/components/cards.html\n *\n * MatCard provides no behaviors, instead serving as a purely visual treatment.\n */\n@Component({\n  selector: 'mat-card',\n  templateUrl: 'card.html',\n  styleUrl: 'card.css',\n  host: {\n    'class': 'mat-mdc-card mdc-card',\n    '[class.mat-mdc-card-outlined]': 'appearance === \"outlined\"',\n    '[class.mdc-card--outlined]': 'appearance === \"outlined\"',\n  },\n  exportAs: 'matCard',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: true,\n})\nexport class MatCard {\n  @Input() appearance: MatCardAppearance;\n\n  constructor(@Inject(MAT_CARD_CONFIG) @Optional() config?: MatCardConfig) {\n    this.appearance = config?.appearance || 'raised';\n  }\n}\n\n// TODO(jelbourn): add `MatActionCard`, which is a card that acts like a button (and has a ripple).\n// Supported in MDC with `.mdc-card__primary-action`. Will require additional a11y docs for users.\n\n/**\n * Title of a card, intended for use within `<mat-card>`. This component is an optional\n * convenience for one variety of card title; any custom title element may be used in its place.\n *\n * MatCardTitle provides no behaviors, instead serving as a purely visual treatment.\n */\n@Directive({\n  selector: `mat-card-title, [mat-card-title], [matCardTitle]`,\n  host: {'class': 'mat-mdc-card-title'},\n  standalone: true,\n})\nexport class MatCardTitle {}\n\n/**\n * Container intended to be used within the `<mat-card>` component. Can contain exactly one\n * `<mat-card-title>`, one `<mat-card-subtitle>` and one content image of any size\n * (e.g. `<img matCardLgImage>`).\n */\n@Component({\n  selector: 'mat-card-title-group',\n  templateUrl: 'card-title-group.html',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  host: {'class': 'mat-mdc-card-title-group'},\n  standalone: true,\n})\nexport class MatCardTitleGroup {}\n\n/**\n * Content of a card, intended for use within `<mat-card>`. This component is an optional\n * convenience for use with other convenience elements, such as `<mat-card-title>`; any custom\n * content block element may be used in its place.\n *\n * MatCardContent provides no behaviors, instead serving as a purely visual treatment.\n */\n@Directive({\n  selector: 'mat-card-content',\n  host: {'class': 'mat-mdc-card-content'},\n  standalone: true,\n})\nexport class MatCardContent {}\n\n/**\n * Sub-title of a card, intended for use within `<mat-card>` beneath a `<mat-card-title>`. This\n * component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-title>`.\n *\n * MatCardSubtitle provides no behaviors, instead serving as a purely visual treatment.\n */\n@Directive({\n  selector: `mat-card-subtitle, [mat-card-subtitle], [matCardSubtitle]`,\n  host: {'class': 'mat-mdc-card-subtitle'},\n  standalone: true,\n})\nexport class MatCardSubtitle {}\n\n/**\n * Bottom area of a card that contains action buttons, intended for use within `<mat-card>`.\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom action block element may be used in its place.\n *\n * MatCardActions provides no behaviors, instead serving as a purely visual treatment.\n */\n@Directive({\n  selector: 'mat-card-actions',\n  exportAs: 'matCardActions',\n  host: {\n    'class': 'mat-mdc-card-actions mdc-card__actions',\n    '[class.mat-mdc-card-actions-align-end]': 'align === \"end\"',\n  },\n  standalone: true,\n})\nexport class MatCardActions {\n  // TODO(jelbourn): deprecate `align` in favor of `actionPosition` or `actionAlignment`\n  // as to not conflict with the native `align` attribute.\n\n  /** Position of the actions inside the card. */\n  @Input() align: 'start' | 'end' = 'start';\n\n  // TODO(jelbourn): support `.mdc-card__actions--full-bleed`.\n\n  // TODO(jelbourn): support  `.mdc-card__action-buttons` and `.mdc-card__action-icons`.\n\n  // TODO(jelbourn): figure out how to use `.mdc-card__action`, `.mdc-card__action--button`, and\n  // `mdc-card__action--icon`. They're used primarily for positioning, which we might be able to\n  // do implicitly.\n}\n\n/**\n * Header region of a card, intended for use within `<mat-card>`. This header captures\n * a card title, subtitle, and avatar.  This component is an optional convenience for use with\n * other convenience elements, such as `<mat-card-footer>`; any custom header block element may be\n * used in its place.\n *\n * MatCardHeader provides no behaviors, instead serving as a purely visual treatment.\n */\n@Component({\n  selector: 'mat-card-header',\n  templateUrl: 'card-header.html',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  host: {'class': 'mat-mdc-card-header'},\n  standalone: true,\n})\nexport class MatCardHeader {}\n\n/**\n * Footer area a card, intended for use within `<mat-card>`.\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom footer block element may be used in its place.\n *\n * MatCardFooter provides no behaviors, instead serving as a purely visual treatment.\n */\n@Directive({\n  selector: 'mat-card-footer',\n  host: {'class': 'mat-mdc-card-footer'},\n  standalone: true,\n})\nexport class MatCardFooter {}\n\n// TODO(jelbourn): deprecate the \"image\" selectors to replace with \"media\".\n\n// TODO(jelbourn): support `.mdc-card__media-content`.\n\n/**\n * Primary image content for a card, intended for use within `<mat-card>`. Can be applied to\n * any media element, such as `<img>` or `<picture>`.\n *\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom media element may be used in its place.\n *\n * MatCardImage provides no behaviors, instead serving as a purely visual treatment.\n */\n@Directive({\n  selector: '[mat-card-image], [matCardImage]',\n  host: {'class': 'mat-mdc-card-image mdc-card__media'},\n  standalone: true,\n})\nexport class MatCardImage {\n  // TODO(jelbourn): support `.mdc-card__media--square` and `.mdc-card__media--16-9`.\n}\n\n/** Same as `MatCardImage`, but small. */\n@Directive({\n  selector: '[mat-card-sm-image], [matCardImageSmall]',\n  host: {'class': 'mat-mdc-card-sm-image mdc-card__media'},\n  standalone: true,\n})\nexport class MatCardSmImage {}\n\n/** Same as `MatCardImage`, but medium. */\n@Directive({\n  selector: '[mat-card-md-image], [matCardImageMedium]',\n  host: {'class': 'mat-mdc-card-md-image mdc-card__media'},\n  standalone: true,\n})\nexport class MatCardMdImage {}\n\n/** Same as `MatCardImage`, but large. */\n@Directive({\n  selector: '[mat-card-lg-image], [matCardImageLarge]',\n  host: {'class': 'mat-mdc-card-lg-image mdc-card__media'},\n  standalone: true,\n})\nexport class MatCardLgImage {}\n\n/** Same as `MatCardImage`, but extra-large. */\n@Directive({\n  selector: '[mat-card-xl-image], [matCardImageXLarge]',\n  host: {'class': 'mat-mdc-card-xl-image mdc-card__media'},\n  standalone: true,\n})\nexport class MatCardXlImage {}\n\n/**\n * Avatar image content for a card, intended for use within `<mat-card>`. Can be applied to\n * any media element, such as `<img>` or `<picture>`.\n *\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-title>`; any custom media element may be used in its place.\n *\n * MatCardAvatar provides no behaviors, instead serving as a purely visual treatment.\n */\n@Directive({\n  selector: '[mat-card-avatar], [matCardAvatar]',\n  host: {'class': 'mat-mdc-card-avatar'},\n  standalone: true,\n})\nexport class MatCardAvatar {}\n", "<ng-content></ng-content>\n", "<div>\n  <ng-content\n      select=\"mat-card-title, mat-card-subtitle,\n      [mat-card-title], [mat-card-subtitle],\n      [matCardTitle], [matCardSubtitle]\"></ng-content>\n</div>\n<ng-content select=\"[mat-card-image], [matCardImage],\n                    [mat-card-sm-image], [matCardImageSmall],\n                    [mat-card-md-image], [matCardImageMedium],\n                    [mat-card-lg-image], [matCardImageLarge],\n                    [mat-card-xl-image], [matCardImageXLarge]\"></ng-content>\n<ng-content></ng-content>\n", "<ng-content select=\"[mat-card-avatar], [matCardAvatar]\"></ng-content>\n<div class=\"mat-mdc-card-header-text\">\n  <ng-content\n      select=\"mat-card-title, mat-card-subtitle,\n      [mat-card-title], [mat-card-subtitle],\n      [matCardTitle], [matCardSubtitle]\"></ng-content>\n</div>\n<ng-content></ng-content>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {CommonModule} from '@angular/common';\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '@angular/material/core';\nimport {\n  MatCard,\n  MatCardActions,\n  MatCardAvatar,\n  MatCardContent,\n  MatCardFooter,\n  MatCardHeader,\n  MatCardImage,\n  MatCardLgImage,\n  MatCardMdImage,\n  MatCardSmImage,\n  MatCardSubtitle,\n  MatCardTitle,\n  MatCardTitleGroup,\n  MatCardXlImage,\n} from './card';\n\nconst CARD_DIRECTIVES = [\n  MatC<PERSON>,\n  Mat<PERSON>ardA<PERSON>,\n  <PERSON><PERSON>ardAvatar,\n  MatCardContent,\n  MatCardFooter,\n  MatCardHeader,\n  MatCardImage,\n  MatCardLgImage,\n  MatCardMdImage,\n  MatCardSmImage,\n  MatCardSubtitle,\n  MatCardTitle,\n  MatCardTitleGroup,\n  MatCardXlImage,\n];\n\n@NgModule({\n  imports: [MatCommonModule, CommonModule, ...CARD_DIRECTIVES],\n  exports: [CARD_DIRECTIVES, MatCommonModule],\n})\nexport class MatCardModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;AA2BA;MACa,eAAe,GAAG,IAAI,cAAc,CAAgB,iBAAiB,EAAE;AAEpF;;;;;AAKG;MAeU,OAAO,CAAA;AAGlB,IAAA,WAAA,CAAiD,MAAsB,EAAA;QACrE,IAAI,CAAC,UAAU,GAAG,MAAM,EAAE,UAAU,IAAI,QAAQ,CAAC;KAClD;AALU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,OAAO,kBAGE,eAAe,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAHxB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,OAAO,6TClDpB,6BACA,EAAA,MAAA,EAAA,CAAA,4pLAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FDiDa,OAAO,EAAA,UAAA,EAAA,CAAA;kBAdnB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,UAAU,EAGd,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,uBAAuB;AAChC,wBAAA,+BAA+B,EAAE,2BAA2B;AAC5D,wBAAA,4BAA4B,EAAE,2BAA2B;qBAC1D,EACS,QAAA,EAAA,SAAS,EACJ,aAAA,EAAA,iBAAiB,CAAC,IAAI,mBACpB,uBAAuB,CAAC,MAAM,EAAA,UAAA,EACnC,IAAI,EAAA,QAAA,EAAA,6BAAA,EAAA,MAAA,EAAA,CAAA,4pLAAA,CAAA,EAAA,CAAA;;0BAKH,MAAM;2BAAC,eAAe,CAAA;;0BAAG,QAAQ;yCAFrC,UAAU,EAAA,CAAA;sBAAlB,KAAK;;AAOR;AACA;AAEA;;;;;AAKG;MAMU,YAAY,CAAA;8GAAZ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAZ,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kDAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,oBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBALxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,CAAkD,gDAAA,CAAA;AAC5D,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,oBAAoB,EAAC;AACrC,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;AAGD;;;;AAIG;MASU,iBAAiB,CAAA;8GAAjB,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAjB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,iBAAiB,sIEvF9B,0hBAYA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FF2Ea,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAR7B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,sBAAsB,EAEjB,aAAA,EAAA,iBAAiB,CAAC,IAAI,mBACpB,uBAAuB,CAAC,MAAM,EAAA,IAAA,EACzC,EAAC,OAAO,EAAE,0BAA0B,EAAC,cAC/B,IAAI,EAAA,QAAA,EAAA,0hBAAA,EAAA,CAAA;;AAIlB;;;;;;AAMG;MAMU,cAAc,CAAA;8GAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAL1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,kBAAkB;AAC5B,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,sBAAsB,EAAC;AACvC,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;AAGD;;;;;;AAMG;MAMU,eAAe,CAAA;8GAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAf,eAAe,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,2DAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,uBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAL3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,CAA2D,yDAAA,CAAA;AACrE,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,uBAAuB,EAAC;AACxC,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;AAGD;;;;;;AAMG;MAUU,cAAc,CAAA;AAT3B,IAAA,WAAA,GAAA;;;;QAcW,IAAK,CAAA,KAAA,GAAoB,OAAO,CAAC;AAS3C,KAAA;8GAdY,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,sCAAA,EAAA,mBAAA,EAAA,EAAA,cAAA,EAAA,wCAAA,EAAA,EAAA,QAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAT1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,kBAAkB;AAC5B,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,wCAAwC;AACjD,wBAAA,wCAAwC,EAAE,iBAAiB;AAC5D,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;8BAMU,KAAK,EAAA,CAAA;sBAAb,KAAK;;AAWR;;;;;;;AAOG;MASU,aAAa,CAAA;8GAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAb,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,4HGrK1B,iUAQA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FH6Ja,aAAa,EAAA,UAAA,EAAA,CAAA;kBARzB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,iBAAiB,EAEZ,aAAA,EAAA,iBAAiB,CAAC,IAAI,mBACpB,uBAAuB,CAAC,MAAM,EAAA,IAAA,EACzC,EAAC,OAAO,EAAE,qBAAqB,EAAC,cAC1B,IAAI,EAAA,QAAA,EAAA,iUAAA,EAAA,CAAA;;AAIlB;;;;;;AAMG;MAMU,aAAa,CAAA;8GAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,qBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBALzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iBAAiB;AAC3B,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,qBAAqB,EAAC;AACtC,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;AAGD;AAEA;AAEA;;;;;;;;AAQG;MAMU,YAAY,CAAA;8GAAZ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAZ,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kCAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,oCAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBALxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,kCAAkC;AAC5C,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,oCAAoC,EAAC;AACrD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;AAKD;MAMa,cAAc,CAAA;8GAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,0CAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,uCAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAL1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,0CAA0C;AACpD,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,uCAAuC,EAAC;AACxD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;AAGD;MAMa,cAAc,CAAA;8GAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,2CAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,uCAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAL1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,2CAA2C;AACrD,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,uCAAuC,EAAC;AACxD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;AAGD;MAMa,cAAc,CAAA;8GAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,0CAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,uCAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAL1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,0CAA0C;AACpD,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,uCAAuC,EAAC;AACxD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;AAGD;MAMa,cAAc,CAAA;8GAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,2CAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,uCAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAL1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,2CAA2C;AACrD,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,uCAAuC,EAAC;AACxD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;AAGD;;;;;;;;AAQG;MAMU,aAAa,CAAA;8GAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,oCAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,qBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBALzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oCAAoC;AAC9C,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,qBAAqB,EAAC;AACtC,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;;AI5ND,MAAM,eAAe,GAAG;IACtB,OAAO;IACP,cAAc;IACd,aAAa;IACb,cAAc;IACd,aAAa;IACb,aAAa;IACb,YAAY;IACZ,cAAc;IACd,cAAc;IACd,cAAc;IACd,eAAe;IACf,YAAY;IACZ,iBAAiB;IACjB,cAAc;CACf,CAAC;MAMW,aAAa,CAAA;8GAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAb,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,EAHd,OAAA,EAAA,CAAA,eAAe,EAAE,YAAY,EAjBvC,OAAO;YACP,cAAc;YACd,aAAa;YACb,cAAc;YACd,aAAa;YACb,aAAa;YACb,YAAY;YACZ,cAAc;YACd,cAAc;YACd,cAAc;YACd,eAAe;YACf,YAAY;YACZ,iBAAiB;AACjB,YAAA,cAAc,aAbd,OAAO;YACP,cAAc;YACd,aAAa;YACb,cAAc;YACd,aAAa;YACb,aAAa;YACb,YAAY;YACZ,cAAc;YACd,cAAc;YACd,cAAc;YACd,eAAe;YACf,YAAY;YACZ,iBAAiB;AACjB,YAAA,cAAc,EAKa,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;AAE/B,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,EAHd,OAAA,EAAA,CAAA,eAAe,EAAE,YAAY,EACZ,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAE/B,aAAa,EAAA,UAAA,EAAA,CAAA;kBAJzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,GAAG,eAAe,CAAC;AAC5D,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,eAAe,CAAC;AAC5C,iBAAA,CAAA;;;AChDD;;AAEG;;;;"}