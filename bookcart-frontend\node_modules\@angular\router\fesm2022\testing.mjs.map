{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../packages/router/testing/src/router_testing_module.ts", "../../../../../../packages/router/testing/src/router_testing_harness.ts", "../../../../../../packages/router/testing/src/testing.ts", "../../../../../../packages/router/testing/public_api.ts", "../../../../../../packages/router/testing/index.ts", "../../../../../../packages/router/testing/testing.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Location} from '@angular/common';\nimport {provideLocationMocks} from '@angular/common/testing';\nimport {Compiler, inject, Injector, ModuleWithProviders, NgModule} from '@angular/core';\nimport {\n  ChildrenOutletContexts,\n  ExtraOptions,\n  NoPreloading,\n  Route,\n  Router,\n  ROUTER_CONFIGURATION,\n  RouteReuseStrategy,\n  RouterModule,\n  ROUTES,\n  Routes,\n  TitleStrategy,\n  UrlHandlingStrategy,\n  UrlSerializer,\n  withPreloading,\n  ɵROUTER_PROVIDERS as ROUTER_PROVIDERS,\n} from '@angular/router';\n\nfunction isUrlHandlingStrategy(\n  opts: ExtraOptions | UrlHandlingStrategy,\n): opts is UrlHandlingStrategy {\n  // This property check is needed because UrlHandlingStrategy is an interface and doesn't exist at\n  // runtime.\n  return 'shouldProcessUrl' in opts;\n}\n\nfunction throwInvalidConfigError(parameter: string): never {\n  throw new Error(\n    `Parameter ${parameter} does not match the one available in the injector. ` +\n      '`setupTestingRouter` is meant to be used as a factory function with dependencies coming from DI.',\n  );\n}\n\n/**\n * @description\n *\n * Sets up the router to be used for testing.\n *\n * The modules sets up the router to be used for testing.\n * It provides spy implementations of `Location` and `LocationStrategy`.\n *\n * @usageNotes\n * ### Example\n *\n * ```\n * beforeEach(() => {\n *   TestBed.configureTestingModule({\n *     imports: [\n *       RouterModule.forRoot(\n *         [{path: '', component: BlankCmp}, {path: 'simple', component: SimpleCmp}]\n *       )\n *     ]\n *   });\n * });\n * ```\n *\n * @publicApi\n * @deprecated Use `provideRouter` or `RouterModule`/`RouterModule.forRoot` instead.\n * This module was previously used to provide a helpful collection of test fakes,\n * most notably those for `Location` and `LocationStrategy`.  These are generally not\n * required anymore, as `MockPlatformLocation` is provided in `TestBed` by default.\n * However, you can use them directly with `provideLocationMocks`.\n */\n@NgModule({\n  exports: [RouterModule],\n  providers: [\n    ROUTER_PROVIDERS,\n    provideLocationMocks(),\n    withPreloading(NoPreloading).ɵproviders,\n    {provide: ROUTES, multi: true, useValue: []},\n  ],\n})\nexport class RouterTestingModule {\n  static withRoutes(\n    routes: Routes,\n    config?: ExtraOptions,\n  ): ModuleWithProviders<RouterTestingModule> {\n    return {\n      ngModule: RouterTestingModule,\n      providers: [\n        {provide: ROUTES, multi: true, useValue: routes},\n        {provide: ROUTER_CONFIGURATION, useValue: config ? config : {}},\n      ],\n    };\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Component, DebugElement, Injectable, Type, ViewChild} from '@angular/core';\nimport {ComponentFixture, TestBed} from '@angular/core/testing';\nimport {Router, RouterOutlet, ɵafterNextNavigation as afterNextNavigation} from '@angular/router';\n\n@Injectable({providedIn: 'root'})\nexport class RootFixtureService {\n  private fixture?: ComponentFixture<RootCmp>;\n  private harness?: RouterTestingHarness;\n\n  createHarness(): RouterTestingHarness {\n    if (this.harness) {\n      throw new Error('Only one harness should be created per test.');\n    }\n    this.harness = new RouterTestingHarness(this.getRootFixture());\n    return this.harness;\n  }\n\n  private getRootFixture(): ComponentFixture<RootCmp> {\n    if (this.fixture !== undefined) {\n      return this.fixture;\n    }\n    this.fixture = TestBed.createComponent(RootCmp);\n    this.fixture.detectChanges();\n    return this.fixture;\n  }\n}\n\n@Component({\n  standalone: true,\n  template: '<router-outlet></router-outlet>',\n  imports: [RouterOutlet],\n})\nexport class RootCmp {\n  @ViewChild(RouterOutlet) outlet?: RouterOutlet;\n}\n\n/**\n * A testing harness for the `Router` to reduce the boilerplate needed to test routes and routed\n * components.\n *\n * @publicApi\n */\nexport class RouterTestingHarness {\n  /**\n   * Creates a `RouterTestingHarness` instance.\n   *\n   * The `RouterTestingHarness` also creates its own root component with a `RouterOutlet` for the\n   * purposes of rendering route components.\n   *\n   * Throws an error if an instance has already been created.\n   * Use of this harness also requires `destroyAfterEach: true` in the `ModuleTeardownOptions`\n   *\n   * @param initialUrl The target of navigation to trigger before returning the harness.\n   */\n  static async create(initialUrl?: string): Promise<RouterTestingHarness> {\n    const harness = TestBed.inject(RootFixtureService).createHarness();\n    if (initialUrl !== undefined) {\n      await harness.navigateByUrl(initialUrl);\n    }\n    return harness;\n  }\n\n  /**\n   * Fixture of the root component of the RouterTestingHarness\n   */\n  public readonly fixture: ComponentFixture<unknown>;\n\n  /** @internal */\n  constructor(fixture: ComponentFixture<unknown>) {\n    this.fixture = fixture;\n  }\n\n  /** Instructs the root fixture to run change detection. */\n  detectChanges(): void {\n    this.fixture.detectChanges();\n  }\n  /** The `DebugElement` of the `RouterOutlet` component. `null` if the outlet is not activated. */\n  get routeDebugElement(): DebugElement | null {\n    const outlet = (this.fixture.componentInstance as RootCmp).outlet;\n    if (!outlet || !outlet.isActivated) {\n      return null;\n    }\n    return this.fixture.debugElement.query((v) => v.componentInstance === outlet.component);\n  }\n  /** The native element of the `RouterOutlet` component. `null` if the outlet is not activated. */\n  get routeNativeElement(): HTMLElement | null {\n    return this.routeDebugElement?.nativeElement ?? null;\n  }\n\n  /**\n   * Triggers a `Router` navigation and waits for it to complete.\n   *\n   * The root component with a `RouterOutlet` created for the harness is used to render `Route`\n   * components. The root component is reused within the same test in subsequent calls to\n   * `navigateForTest`.\n   *\n   * When testing `Routes` with a guards that reject the navigation, the `RouterOutlet` might not be\n   * activated and the `activatedComponent` may be `null`.\n   *\n   * {@example router/testing/test/router_testing_harness_examples.spec.ts region='Guard'}\n   *\n   * @param url The target of the navigation. Passed to `Router.navigateByUrl`.\n   * @returns The activated component instance of the `RouterOutlet` after navigation completes\n   *     (`null` if the outlet does not get activated).\n   */\n  async navigateByUrl(url: string): Promise<null | {}>;\n  /**\n   * Triggers a router navigation and waits for it to complete.\n   *\n   * The root component with a `RouterOutlet` created for the harness is used to render `Route`\n   * components.\n   *\n   * {@example router/testing/test/router_testing_harness_examples.spec.ts region='RoutedComponent'}\n   *\n   * The root component is reused within the same test in subsequent calls to `navigateByUrl`.\n   *\n   * This function also makes it easier to test components that depend on `ActivatedRoute` data.\n   *\n   * {@example router/testing/test/router_testing_harness_examples.spec.ts region='ActivatedRoute'}\n   *\n   * @param url The target of the navigation. Passed to `Router.navigateByUrl`.\n   * @param requiredRoutedComponentType After navigation completes, the required type for the\n   *     activated component of the `RouterOutlet`. If the outlet is not activated or a different\n   *     component is activated, this function will throw an error.\n   * @returns The activated component instance of the `RouterOutlet` after navigation completes.\n   */\n  async navigateByUrl<T>(url: string, requiredRoutedComponentType: Type<T>): Promise<T>;\n  async navigateByUrl<T>(url: string, requiredRoutedComponentType?: Type<T>): Promise<T | null> {\n    const router = TestBed.inject(Router);\n    let resolveFn!: () => void;\n    const redirectTrackingPromise = new Promise<void>((resolve) => {\n      resolveFn = resolve;\n    });\n    afterNextNavigation(TestBed.inject(Router), resolveFn);\n    await router.navigateByUrl(url);\n    await redirectTrackingPromise;\n    this.fixture.detectChanges();\n    const outlet = (this.fixture.componentInstance as RootCmp).outlet;\n    // The outlet might not be activated if the user is testing a navigation for a guard that\n    // rejects\n    if (outlet && outlet.isActivated && outlet.activatedRoute.component) {\n      const activatedComponent = outlet.component;\n      if (\n        requiredRoutedComponentType !== undefined &&\n        !(activatedComponent instanceof requiredRoutedComponentType)\n      ) {\n        throw new Error(\n          `Unexpected routed component type. Expected ${requiredRoutedComponentType.name} but got ${activatedComponent.constructor.name}`,\n        );\n      }\n      return activatedComponent as T;\n    } else {\n      if (requiredRoutedComponentType !== undefined) {\n        throw new Error(\n          `Unexpected routed component type. Expected ${requiredRoutedComponentType.name} but the navigation did not activate any component.`,\n        );\n      }\n      return null;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the router/testing package.\n */\nexport * from './router_testing_module';\nexport {RouterTestingHarness} from './router_testing_harness';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\nexport * from './src/testing';\n\n// This file only reexports content of the `src` folder. Keep it that way.\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// This file is not used to build this module. It is only used during editing\n// by the TypeScript language service and during build for verification. `ngc`\n// replaces this file with production index.ts when it rewrites private symbol\n// names.\n\nexport * from './public_api';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["ROUTER_PROVIDERS", "afterNextNavigation"], "mappings": ";;;;;;;;;;;;AA6BA,SAAS,qBAAqB,CAC5B,IAAwC,EAAA;;;IAIxC,OAAO,kBAAkB,IAAI,IAAI,CAAC;AACpC,CAAC;AAED,SAAS,uBAAuB,CAAC,SAAiB,EAAA;AAChD,IAAA,MAAM,IAAI,KAAK,CACb,CAAA,UAAA,EAAa,SAAS,CAAqD,mDAAA,CAAA;AACzE,QAAA,kGAAkG,CACrG,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;MAUU,mBAAmB,CAAA;AAC9B,IAAA,OAAO,UAAU,CACf,MAAc,EACd,MAAqB,EAAA;QAErB,OAAO;AACL,YAAA,QAAQ,EAAE,mBAAmB;AAC7B,YAAA,SAAS,EAAE;gBACT,EAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAC;AAChD,gBAAA,EAAC,OAAO,EAAE,oBAAoB,EAAE,QAAQ,EAAE,MAAM,GAAG,MAAM,GAAG,EAAE,EAAC;AAChE,aAAA;SACF,CAAC;KACH;yHAZU,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAnB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,mBAAmB,YARpB,YAAY,CAAA,EAAA,CAAA,CAAA,EAAA;AAQX,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,mBAAmB,EAPnB,SAAA,EAAA;YACTA,iBAAgB;AAChB,YAAA,oBAAoB,EAAE;AACtB,YAAA,cAAc,CAAC,YAAY,CAAC,CAAC,UAAU;YACvC,EAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAC;AAC7C,SAAA,EAAA,OAAA,EAAA,CANS,YAAY,CAAA,EAAA,CAAA,CAAA,EAAA;;sGAQX,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAT/B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,YAAY,CAAC;AACvB,oBAAA,SAAS,EAAE;wBACTA,iBAAgB;AAChB,wBAAA,oBAAoB,EAAE;AACtB,wBAAA,cAAc,CAAC,YAAY,CAAC,CAAC,UAAU;wBACvC,EAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAC;AAC7C,qBAAA;AACF,iBAAA,CAAA;;;MCrEY,kBAAkB,CAAA;IAI7B,aAAa,GAAA;AACX,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;SACjE;QACD,IAAI,CAAC,OAAO,GAAG,IAAI,oBAAoB,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;QAC/D,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;IAEO,cAAc,GAAA;AACpB,QAAA,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,EAAE;YAC9B,OAAO,IAAI,CAAC,OAAO,CAAC;SACrB;QACD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;AAChD,QAAA,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;yHAnBU,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAAlB,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,cADN,MAAM,EAAA,CAAA,CAAA,EAAA;;sGAClB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAD9B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;MA4BnB,OAAO,CAAA;yHAAP,OAAO,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAP,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,IAAA,EAAA,OAAO,EACP,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,QAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,YAAY,EAJb,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,iCAAiC,4DACjC,YAAY,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,CAAA,MAAA,CAAA,EAAA,OAAA,EAAA,CAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,QAAA,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA;;sGAEX,OAAO,EAAA,UAAA,EAAA,CAAA;kBALnB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,UAAU,EAAE,IAAI;AAChB,oBAAA,QAAQ,EAAE,iCAAiC;oBAC3C,OAAO,EAAE,CAAC,YAAY,CAAC;AACxB,iBAAA,CAAA;8BAE0B,MAAM,EAAA,CAAA;sBAA9B,SAAS;uBAAC,YAAY,CAAA;;AAGzB;;;;;AAKG;MACU,oBAAoB,CAAA;AAC/B;;;;;;;;;;AAUG;AACH,IAAA,aAAa,MAAM,CAAC,UAAmB,EAAA;QACrC,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,aAAa,EAAE,CAAC;AACnE,QAAA,IAAI,UAAU,KAAK,SAAS,EAAE;AAC5B,YAAA,MAAM,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;SACzC;AACD,QAAA,OAAO,OAAO,CAAC;KAChB;;AAQD,IAAA,WAAA,CAAY,OAAkC,EAAA;AAC5C,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;KACxB;;IAGD,aAAa,GAAA;AACX,QAAA,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;KAC9B;;AAED,IAAA,IAAI,iBAAiB,GAAA;QACnB,MAAM,MAAM,GAAI,IAAI,CAAC,OAAO,CAAC,iBAA6B,CAAC,MAAM,CAAC;QAClE,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;AAClC,YAAA,OAAO,IAAI,CAAC;SACb;QACD,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,iBAAiB,KAAK,MAAM,CAAC,SAAS,CAAC,CAAC;KACzF;;AAED,IAAA,IAAI,kBAAkB,GAAA;AACpB,QAAA,OAAO,IAAI,CAAC,iBAAiB,EAAE,aAAa,IAAI,IAAI,CAAC;KACtD;AAwCD,IAAA,MAAM,aAAa,CAAI,GAAW,EAAE,2BAAqC,EAAA;QACvE,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACtC,QAAA,IAAI,SAAsB,CAAC;QAC3B,MAAM,uBAAuB,GAAG,IAAI,OAAO,CAAO,CAAC,OAAO,KAAI;YAC5D,SAAS,GAAG,OAAO,CAAC;AACtB,SAAC,CAAC,CAAC;QACHC,oBAAmB,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,SAAS,CAAC,CAAC;AACvD,QAAA,MAAM,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AAChC,QAAA,MAAM,uBAAuB,CAAC;AAC9B,QAAA,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;QAC7B,MAAM,MAAM,GAAI,IAAI,CAAC,OAAO,CAAC,iBAA6B,CAAC,MAAM,CAAC;;;AAGlE,QAAA,IAAI,MAAM,IAAI,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE;AACnE,YAAA,MAAM,kBAAkB,GAAG,MAAM,CAAC,SAAS,CAAC;YAC5C,IACE,2BAA2B,KAAK,SAAS;AACzC,gBAAA,EAAE,kBAAkB,YAAY,2BAA2B,CAAC,EAC5D;AACA,gBAAA,MAAM,IAAI,KAAK,CACb,CAAA,2CAAA,EAA8C,2BAA2B,CAAC,IAAI,CAAY,SAAA,EAAA,kBAAkB,CAAC,WAAW,CAAC,IAAI,CAAA,CAAE,CAChI,CAAC;aACH;AACD,YAAA,OAAO,kBAAuB,CAAC;SAChC;aAAM;AACL,YAAA,IAAI,2BAA2B,KAAK,SAAS,EAAE;gBAC7C,MAAM,IAAI,KAAK,CACb,CAAA,2CAAA,EAA8C,2BAA2B,CAAC,IAAI,CAAqD,mDAAA,CAAA,CACpI,CAAC;aACH;AACD,YAAA,OAAO,IAAI,CAAC;SACb;KACF;AACF;;AChKD;;;;AAIG;;ACJH;;;;AAIG;AAGH;;ACPA;;ACRA;;AAEG;;;;"}