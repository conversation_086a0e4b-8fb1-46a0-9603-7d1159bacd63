{"version": 3, "file": "mergeInternals.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/mergeInternals.ts"], "names": [], "mappings": ";;;AACA,qDAAoD;AAGpD,2DAA0D;AAC1D,2DAAgE;AAehE,SAAgB,cAAc,CAC5B,MAAqB,EACrB,UAAyB,EACzB,OAAwD,EACxD,UAAkB,EAClB,YAAsC,EACtC,MAAgB,EAChB,iBAAiC,EACjC,mBAAgC;IAGhC,IAAM,MAAM,GAAQ,EAAE,CAAC;IAEvB,IAAI,MAAM,GAAG,CAAC,CAAC;IAEf,IAAI,KAAK,GAAG,CAAC,CAAC;IAEd,IAAI,UAAU,GAAG,KAAK,CAAC;IAKvB,IAAM,aAAa,GAAG;QAIpB,IAAI,UAAU,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE;YAC3C,UAAU,CAAC,QAAQ,EAAE,CAAC;SACvB;IACH,CAAC,CAAC;IAGF,IAAM,SAAS,GAAG,UAAC,KAAQ,IAAK,OAAA,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAA9D,CAA8D,CAAC;IAE/F,IAAM,UAAU,GAAG,UAAC,KAAQ;QAI1B,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,KAAY,CAAC,CAAC;QAIxC,MAAM,EAAE,CAAC;QAKT,IAAI,aAAa,GAAG,KAAK,CAAC;QAG1B,qBAAS,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS,CAC1C,6CAAwB,CACtB,UAAU,EACV,UAAC,UAAU;YAGT,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAG,UAAU,CAAC,CAAC;YAE3B,IAAI,MAAM,EAAE;gBAGV,SAAS,CAAC,UAAiB,CAAC,CAAC;aAC9B;iBAAM;gBAEL,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAC7B;QACH,CAAC,EACD;YAGE,aAAa,GAAG,IAAI,CAAC;QACvB,CAAC,EAED,SAAS,EACT;YAIE,IAAI,aAAa,EAAE;gBAKjB,IAAI;oBAIF,MAAM,EAAE,CAAC;;wBAMP,IAAM,aAAa,GAAG,MAAM,CAAC,KAAK,EAAG,CAAC;wBAItC,IAAI,iBAAiB,EAAE;4BACrB,iCAAe,CAAC,UAAU,EAAE,iBAAiB,EAAE,cAAM,OAAA,UAAU,CAAC,aAAa,CAAC,EAAzB,CAAyB,CAAC,CAAC;yBACjF;6BAAM;4BACL,UAAU,CAAC,aAAa,CAAC,CAAC;yBAC3B;;oBATH,OAAO,MAAM,CAAC,MAAM,IAAI,MAAM,GAAG,UAAU;;qBAU1C;oBAED,aAAa,EAAE,CAAC;iBACjB;gBAAC,OAAO,GAAG,EAAE;oBACZ,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;iBACvB;aACF;QACH,CAAC,CACF,CACF,CAAC;IACJ,CAAC,CAAC;IAGF,MAAM,CAAC,SAAS,CACd,6CAAwB,CAAC,UAAU,EAAE,SAAS,EAAE;QAE9C,UAAU,GAAG,IAAI,CAAC;QAClB,aAAa,EAAE,CAAC;IAClB,CAAC,CAAC,CACH,CAAC;IAIF,OAAO;QACL,mBAAmB,aAAnB,mBAAmB,uBAAnB,mBAAmB,EAAI,CAAC;IAC1B,CAAC,CAAC;AACJ,CAAC;AAhID,wCAgIC"}