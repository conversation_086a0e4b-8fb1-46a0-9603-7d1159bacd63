{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"../../services/cart.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/material/toolbar\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/form-field\";\nimport * as i9 from \"@angular/material/input\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/menu\";\nimport * as i12 from \"@angular/material/badge\";\nimport * as i13 from \"@angular/material/divider\";\nfunction NavbarComponent_ng_container_19_mat_divider_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-divider\");\n  }\n}\nfunction NavbarComponent_ng_container_19_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_ng_container_19_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.goToAdminPanel());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"admin_panel_settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Admin Panel\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NavbarComponent_ng_container_19_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 20)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Add Book\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NavbarComponent_ng_container_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_ng_container_19_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.goToCart());\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\", 14);\n    i0.ɵɵtext(3, \"shopping_cart\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"button\", 15)(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"arrow_drop_down\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"mat-menu\", null, 1)(13, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_ng_container_19_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.goToProfile());\n    });\n    i0.ɵɵelementStart(14, \"mat-icon\");\n    i0.ɵɵtext(15, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17, \"Profile\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_ng_container_19_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.goToOrders());\n    });\n    i0.ɵɵelementStart(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"receipt_long\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22, \"My Orders\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(23, NavbarComponent_ng_container_19_mat_divider_23_Template, 1, 0, \"mat-divider\", 17)(24, NavbarComponent_ng_container_19_button_24_Template, 5, 0, \"button\", 18)(25, NavbarComponent_ng_container_19_button_25_Template, 5, 0, \"button\", 19);\n    i0.ɵɵelement(26, \"mat-divider\");\n    i0.ɵɵelementStart(27, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_ng_container_19_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.logout());\n    });\n    i0.ɵɵelementStart(28, \"mat-icon\");\n    i0.ɵɵtext(29, \"logout\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"span\");\n    i0.ɵɵtext(31, \"Logout\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const userMenu_r5 = i0.ɵɵreference(12);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"matBadge\", ctx_r2.cartItemCount)(\"matBadgeHidden\", ctx_r2.cartItemCount === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", userMenu_r5);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.currentUser == null ? null : ctx_r2.currentUser.firstName);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isAdmin());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isAdmin());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isAdmin());\n  }\n}\nfunction NavbarComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 21)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"login\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Login \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 22)(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"person_add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7, \" Register \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class NavbarComponent {\n  constructor(authService, cartService, router) {\n    this.authService = authService;\n    this.cartService = cartService;\n    this.router = router;\n    this.currentUser = null;\n    this.cartItemCount = 0;\n    this.searchTerm = '';\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    // Subscribe to current user\n    this.authService.currentUser$.pipe(takeUntil(this.destroy$)).subscribe(user => {\n      this.currentUser = user;\n      if (user) {\n        // Load cart when user logs in\n        this.cartService.getMyCart().subscribe();\n      }\n    });\n    // Subscribe to cart changes\n    this.cartService.cart$.pipe(takeUntil(this.destroy$)).subscribe(cart => {\n      this.cartItemCount = cart ? cart.cartItems.reduce((total, item) => total + item.quantity, 0) : 0;\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  isAuthenticated() {\n    return this.authService.isAuthenticated();\n  }\n  isAdmin() {\n    return this.authService.isAdmin();\n  }\n  onSearch() {\n    if (this.searchTerm.trim()) {\n      this.router.navigate(['/books'], {\n        queryParams: {\n          search: this.searchTerm.trim()\n        }\n      });\n    } else {\n      this.router.navigate(['/books']);\n    }\n  }\n  logout() {\n    this.authService.logout();\n  }\n  goToCart() {\n    this.router.navigate(['/cart']);\n  }\n  goToProfile() {\n    // Navigate to profile page (to be implemented)\n    console.log('Navigate to profile');\n  }\n  goToOrders() {\n    this.router.navigate(['/orders']);\n  }\n  goToAdminPanel() {\n    this.router.navigate(['/admin/users']);\n  }\n  static {\n    this.ɵfac = function NavbarComponent_Factory(t) {\n      return new (t || NavbarComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.CartService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NavbarComponent,\n      selectors: [[\"app-navbar\"]],\n      decls: 22,\n      vars: 3,\n      consts: [[\"authButtons\", \"\"], [\"userMenu\", \"matMenu\"], [\"color\", \"primary\", 1, \"navbar\"], [1, \"navbar-content\"], [\"routerLink\", \"/books\", 1, \"brand\"], [1, \"brand-text\"], [1, \"search-container\"], [\"appearance\", \"outline\", 1, \"search-field\"], [\"matInput\", \"\", \"placeholder\", \"Search by title, author, or description\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"matSuffix\", \"\", 1, \"search-icon\", 3, \"click\"], [1, \"nav-links\"], [\"mat-button\", \"\", \"routerLink\", \"/books\", \"routerLinkActive\", \"active\"], [4, \"ngIf\", \"ngIfElse\"], [\"mat-icon-button\", \"\", 1, \"cart-button\", 3, \"click\"], [\"matBadgeColor\", \"accent\", 3, \"matBadge\", \"matBadgeHidden\"], [\"mat-button\", \"\", 1, \"user-menu\", 3, \"matMenuTriggerFor\"], [\"mat-menu-item\", \"\", 3, \"click\"], [4, \"ngIf\"], [\"mat-menu-item\", \"\", 3, \"click\", 4, \"ngIf\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/admin/books/add\", 4, \"ngIf\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/admin/books/add\"], [\"mat-button\", \"\", \"routerLink\", \"/login\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/register\"]],\n      template: function NavbarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"mat-toolbar\", 2)(1, \"div\", 3)(2, \"div\", 4)(3, \"mat-icon\");\n          i0.ɵɵtext(4, \"book\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"span\", 5);\n          i0.ɵɵtext(6, \"BookCart\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"mat-form-field\", 7)(9, \"mat-label\");\n          i0.ɵɵtext(10, \"Search books...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"input\", 8);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function NavbarComponent_Template_input_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchTerm, $event) || (ctx.searchTerm = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"keyup.enter\", function NavbarComponent_Template_input_keyup_enter_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"mat-icon\", 9);\n          i0.ɵɵlistener(\"click\", function NavbarComponent_Template_mat_icon_click_12_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵtext(13, \"search\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 10)(15, \"button\", 11)(16, \"mat-icon\");\n          i0.ɵɵtext(17, \"library_books\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(18, \" Books \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(19, NavbarComponent_ng_container_19_Template, 32, 7, \"ng-container\", 12)(20, NavbarComponent_ng_template_20_Template, 8, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          const authButtons_r6 = i0.ɵɵreference(21);\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchTerm);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isAuthenticated())(\"ngIfElse\", authButtons_r6);\n        }\n      },\n      dependencies: [i4.NgIf, i3.RouterLink, i3.RouterLinkActive, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel, i6.MatToolbar, i7.MatButton, i7.MatIconButton, i8.MatFormField, i8.MatLabel, i8.MatSuffix, i9.MatInput, i10.MatIcon, i11.MatMenu, i11.MatMenuItem, i11.MatMenuTrigger, i12.MatBadge, i13.MatDivider],\n      styles: [\".navbar[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 1000;\\n  height: 64px;\\n}\\n\\n.navbar-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  width: 100%;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 16px;\\n}\\n\\n.brand[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  cursor: pointer;\\n  color: white;\\n  text-decoration: none;\\n  font-size: 1.2rem;\\n  font-weight: 500;\\n}\\n\\n.brand[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 28px;\\n  height: 28px;\\n  width: 28px;\\n}\\n\\n.brand-text[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n}\\n\\n.search-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  max-width: 400px;\\n  margin: 0 24px;\\n}\\n\\n.search-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.search-field[_ngcontent-%COMP%]     .mat-form-field-wrapper {\\n  padding-bottom: 0;\\n}\\n\\n.search-field[_ngcontent-%COMP%]     .mat-form-field-infix {\\n  border-top: none;\\n}\\n\\n.search-field[_ngcontent-%COMP%]     .mat-form-field-outline {\\n  background-color: rgba(255, 255, 255, 0.1);\\n  border-radius: 4px;\\n}\\n\\n.search-field[_ngcontent-%COMP%]     .mat-form-field-outline-thick {\\n  color: white;\\n}\\n\\n.search-field[_ngcontent-%COMP%]     .mat-form-field-label {\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.search-field[_ngcontent-%COMP%]     input {\\n  color: white;\\n}\\n\\n.search-icon[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.search-icon[_ngcontent-%COMP%]:hover {\\n  color: white;\\n}\\n\\n.nav-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.nav-links[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n\\n.nav-links[_ngcontent-%COMP%]   button.active[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n\\n.cart-button[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.user-menu[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n\\n.user-menu[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  margin: 0 4px;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .navbar-content[_ngcontent-%COMP%] {\\n    padding: 0 8px;\\n  }\\n  \\n  .search-container[_ngcontent-%COMP%] {\\n    max-width: 200px;\\n    margin: 0 12px;\\n  }\\n  \\n  .brand-text[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  \\n  .nav-links[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  .search-container[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  \\n  .nav-links[_ngcontent-%COMP%] {\\n    gap: 4px;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵlistener", "NavbarComponent_ng_container_19_button_24_Template_button_click_0_listener", "ɵɵrestoreView", "_r4", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "goToAdminPanel", "ɵɵtext", "ɵɵelementEnd", "ɵɵelementContainerStart", "NavbarComponent_ng_container_19_Template_button_click_1_listener", "_r2", "goToCart", "NavbarComponent_ng_container_19_Template_button_click_13_listener", "goToProfile", "NavbarComponent_ng_container_19_Template_button_click_18_listener", "goToOrders", "ɵɵtemplate", "NavbarComponent_ng_container_19_mat_divider_23_Template", "NavbarComponent_ng_container_19_button_24_Template", "NavbarComponent_ng_container_19_button_25_Template", "NavbarComponent_ng_container_19_Template_button_click_27_listener", "logout", "ɵɵadvance", "ɵɵproperty", "cartItemCount", "userMenu_r5", "ɵɵtextInterpolate", "currentUser", "firstName", "isAdmin", "NavbarComponent", "constructor", "authService", "cartService", "router", "searchTerm", "destroy$", "ngOnInit", "currentUser$", "pipe", "subscribe", "user", "getMyCart", "cart$", "cart", "cartItems", "reduce", "total", "item", "quantity", "ngOnDestroy", "next", "complete", "isAuthenticated", "onSearch", "trim", "navigate", "queryParams", "search", "console", "log", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "CartService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "NavbarComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "NavbarComponent_Template_input_ngModelChange_11_listener", "$event", "_r1", "ɵɵtwoWayBindingSet", "NavbarComponent_Template_input_keyup_enter_11_listener", "NavbarComponent_Template_mat_icon_click_12_listener", "NavbarComponent_ng_container_19_Template", "NavbarComponent_ng_template_20_Template", "ɵɵtemplateRefExtractor", "ɵɵtwoWayProperty", "authButtons_r6"], "sources": ["C:\\Users\\<USER>\\Desktop\\BookCart\\bookcart-frontend\\src\\app\\shared\\components\\navbar\\navbar.component.ts", "C:\\Users\\<USER>\\Desktop\\BookCart\\bookcart-frontend\\src\\app\\shared\\components\\navbar\\navbar.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { Subject, takeUntil } from 'rxjs';\nimport { AuthService, UserProfile } from '../../services/auth.service';\nimport { CartService } from '../../services/cart.service';\n\n@Component({\n  selector: 'app-navbar',\n  templateUrl: './navbar.component.html',\n  styleUrls: ['./navbar.component.css']\n})\nexport class NavbarComponent implements OnInit, OnDestroy {\n  currentUser: UserProfile | null = null;\n  cartItemCount = 0;\n  searchTerm = '';\n  private destroy$ = new Subject<void>();\n\n  constructor(\n    private authService: AuthService,\n    private cartService: CartService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    // Subscribe to current user\n    this.authService.currentUser$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(user => {\n        this.currentUser = user;\n        if (user) {\n          // Load cart when user logs in\n          this.cartService.getMyCart().subscribe();\n        }\n      });\n\n    // Subscribe to cart changes\n    this.cartService.cart$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(cart => {\n        this.cartItemCount = cart ? cart.cartItems.reduce((total, item) => total + item.quantity, 0) : 0;\n      });\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  isAuthenticated(): boolean {\n    return this.authService.isAuthenticated();\n  }\n\n  isAdmin(): boolean {\n    return this.authService.isAdmin();\n  }\n\n  onSearch(): void {\n    if (this.searchTerm.trim()) {\n      this.router.navigate(['/books'], { queryParams: { search: this.searchTerm.trim() } });\n    } else {\n      this.router.navigate(['/books']);\n    }\n  }\n\n  logout(): void {\n    this.authService.logout();\n  }\n\n  goToCart(): void {\n    this.router.navigate(['/cart']);\n  }\n\n  goToProfile(): void {\n    // Navigate to profile page (to be implemented)\n    console.log('Navigate to profile');\n  }\n\n  goToOrders(): void {\n    this.router.navigate(['/orders']);\n  }\n\n  goToAdminPanel(): void {\n    this.router.navigate(['/admin/users']);\n  }\n}\n", "<mat-toolbar color=\"primary\" class=\"navbar\">\n  <div class=\"navbar-content\">\n    <!-- Logo/Brand -->\n    <div class=\"brand\" routerLink=\"/books\">\n      <mat-icon>book</mat-icon>\n      <span class=\"brand-text\">BookCart</span>\n    </div>\n\n    <!-- Search Bar -->\n    <div class=\"search-container\">\n      <mat-form-field appearance=\"outline\" class=\"search-field\">\n        <mat-label>Search books...</mat-label>\n        <input matInput \n               [(ngModel)]=\"searchTerm\" \n               (keyup.enter)=\"onSearch()\"\n               placeholder=\"Search by title, author, or description\">\n        <mat-icon matSuffix (click)=\"onSearch()\" class=\"search-icon\">search</mat-icon>\n      </mat-form-field>\n    </div>\n\n    <!-- Navigation Links -->\n    <div class=\"nav-links\">\n      <button mat-button routerLink=\"/books\" routerLinkActive=\"active\">\n        <mat-icon>library_books</mat-icon>\n        Books\n      </button>\n\n      <!-- User Menu -->\n      <ng-container *ngIf=\"isAuthenticated(); else authButtons\">\n        <!-- Cart Button -->\n        <button mat-icon-button (click)=\"goToCart()\" class=\"cart-button\">\n          <mat-icon [matBadge]=\"cartItemCount\" \n                    [matBadgeHidden]=\"cartItemCount === 0\" \n                    matBadgeColor=\"accent\">shopping_cart</mat-icon>\n        </button>\n\n        <!-- User Menu -->\n        <button mat-button [matMenuTriggerFor]=\"userMenu\" class=\"user-menu\">\n          <mat-icon>account_circle</mat-icon>\n          <span>{{ currentUser?.firstName }}</span>\n          <mat-icon>arrow_drop_down</mat-icon>\n        </button>\n\n        <mat-menu #userMenu=\"matMenu\">\n          <button mat-menu-item (click)=\"goToProfile()\">\n            <mat-icon>person</mat-icon>\n            <span>Profile</span>\n          </button>\n          <button mat-menu-item (click)=\"goToOrders()\">\n            <mat-icon>receipt_long</mat-icon>\n            <span>My Orders</span>\n          </button>\n          <mat-divider *ngIf=\"isAdmin()\"></mat-divider>\n          <button mat-menu-item (click)=\"goToAdminPanel()\" *ngIf=\"isAdmin()\">\n            <mat-icon>admin_panel_settings</mat-icon>\n            <span>Admin Panel</span>\n          </button>\n          <button mat-menu-item routerLink=\"/admin/books/add\" *ngIf=\"isAdmin()\">\n            <mat-icon>add</mat-icon>\n            <span>Add Book</span>\n          </button>\n          <mat-divider></mat-divider>\n          <button mat-menu-item (click)=\"logout()\">\n            <mat-icon>logout</mat-icon>\n            <span>Logout</span>\n          </button>\n        </mat-menu>\n      </ng-container>\n\n      <!-- Auth Buttons (when not logged in) -->\n      <ng-template #authButtons>\n        <button mat-button routerLink=\"/login\">\n          <mat-icon>login</mat-icon>\n          Login\n        </button>\n        <button mat-raised-button color=\"accent\" routerLink=\"/register\">\n          <mat-icon>person_add</mat-icon>\n          Register\n        </button>\n      </ng-template>\n    </div>\n  </div>\n</mat-toolbar>\n"], "mappings": "AAEA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;;;ICkD/BC,EAAA,CAAAC,SAAA,kBAA6C;;;;;;IAC7CD,EAAA,CAAAE,cAAA,iBAAmE;IAA7CF,EAAA,CAAAG,UAAA,mBAAAC,2EAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAG,cAAA,EAAgB;IAAA,EAAC;IAC9CV,EAAA,CAAAE,cAAA,eAAU;IAAAF,EAAA,CAAAW,MAAA,2BAAoB;IAAAX,EAAA,CAAAY,YAAA,EAAW;IACzCZ,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAW,MAAA,kBAAW;IACnBX,EADmB,CAAAY,YAAA,EAAO,EACjB;;;;;IAEPZ,EADF,CAAAE,cAAA,iBAAsE,eAC1D;IAAAF,EAAA,CAAAW,MAAA,UAAG;IAAAX,EAAA,CAAAY,YAAA,EAAW;IACxBZ,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAW,MAAA,eAAQ;IAChBX,EADgB,CAAAY,YAAA,EAAO,EACd;;;;;;IAhCbZ,EAAA,CAAAa,uBAAA,GAA0D;IAExDb,EAAA,CAAAE,cAAA,iBAAiE;IAAzCF,EAAA,CAAAG,UAAA,mBAAAW,iEAAA;MAAAd,EAAA,CAAAK,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAS,QAAA,EAAU;IAAA,EAAC;IAC1ChB,EAAA,CAAAE,cAAA,mBAEiC;IAAAF,EAAA,CAAAW,MAAA,oBAAa;IAChDX,EADgD,CAAAY,YAAA,EAAW,EAClD;IAIPZ,EADF,CAAAE,cAAA,iBAAoE,eACxD;IAAAF,EAAA,CAAAW,MAAA,qBAAc;IAAAX,EAAA,CAAAY,YAAA,EAAW;IACnCZ,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAW,MAAA,GAA4B;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACzCZ,EAAA,CAAAE,cAAA,eAAU;IAAAF,EAAA,CAAAW,MAAA,uBAAe;IAC3BX,EAD2B,CAAAY,YAAA,EAAW,EAC7B;IAGPZ,EADF,CAAAE,cAAA,yBAA8B,kBACkB;IAAxBF,EAAA,CAAAG,UAAA,mBAAAc,kEAAA;MAAAjB,EAAA,CAAAK,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAW,WAAA,EAAa;IAAA,EAAC;IAC3ClB,EAAA,CAAAE,cAAA,gBAAU;IAAAF,EAAA,CAAAW,MAAA,cAAM;IAAAX,EAAA,CAAAY,YAAA,EAAW;IAC3BZ,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAW,MAAA,eAAO;IACfX,EADe,CAAAY,YAAA,EAAO,EACb;IACTZ,EAAA,CAAAE,cAAA,kBAA6C;IAAvBF,EAAA,CAAAG,UAAA,mBAAAgB,kEAAA;MAAAnB,EAAA,CAAAK,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAa,UAAA,EAAY;IAAA,EAAC;IAC1CpB,EAAA,CAAAE,cAAA,gBAAU;IAAAF,EAAA,CAAAW,MAAA,oBAAY;IAAAX,EAAA,CAAAY,YAAA,EAAW;IACjCZ,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAW,MAAA,iBAAS;IACjBX,EADiB,CAAAY,YAAA,EAAO,EACf;IAMTZ,EALA,CAAAqB,UAAA,KAAAC,uDAAA,0BAA+B,KAAAC,kDAAA,qBACoC,KAAAC,kDAAA,qBAIG;IAItExB,EAAA,CAAAC,SAAA,mBAA2B;IAC3BD,EAAA,CAAAE,cAAA,kBAAyC;IAAnBF,EAAA,CAAAG,UAAA,mBAAAsB,kEAAA;MAAAzB,EAAA,CAAAK,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAmB,MAAA,EAAQ;IAAA,EAAC;IACtC1B,EAAA,CAAAE,cAAA,gBAAU;IAAAF,EAAA,CAAAW,MAAA,cAAM;IAAAX,EAAA,CAAAY,YAAA,EAAW;IAC3BZ,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAW,MAAA,cAAM;IAEhBX,EAFgB,CAAAY,YAAA,EAAO,EACZ,EACA;;;;;;IAnCCZ,EAAA,CAAA2B,SAAA,GAA0B;IAC1B3B,EADA,CAAA4B,UAAA,aAAArB,MAAA,CAAAsB,aAAA,CAA0B,mBAAAtB,MAAA,CAAAsB,aAAA,OACY;IAK/B7B,EAAA,CAAA2B,SAAA,GAA8B;IAA9B3B,EAAA,CAAA4B,UAAA,sBAAAE,WAAA,CAA8B;IAEzC9B,EAAA,CAAA2B,SAAA,GAA4B;IAA5B3B,EAAA,CAAA+B,iBAAA,CAAAxB,MAAA,CAAAyB,WAAA,kBAAAzB,MAAA,CAAAyB,WAAA,CAAAC,SAAA,CAA4B;IAapBjC,EAAA,CAAA2B,SAAA,IAAe;IAAf3B,EAAA,CAAA4B,UAAA,SAAArB,MAAA,CAAA2B,OAAA,GAAe;IACqBlC,EAAA,CAAA2B,SAAA,EAAe;IAAf3B,EAAA,CAAA4B,UAAA,SAAArB,MAAA,CAAA2B,OAAA,GAAe;IAIZlC,EAAA,CAAA2B,SAAA,EAAe;IAAf3B,EAAA,CAAA4B,UAAA,SAAArB,MAAA,CAAA2B,OAAA,GAAe;;;;;IAepElC,EADF,CAAAE,cAAA,iBAAuC,eAC3B;IAAAF,EAAA,CAAAW,MAAA,YAAK;IAAAX,EAAA,CAAAY,YAAA,EAAW;IAC1BZ,EAAA,CAAAW,MAAA,cACF;IAAAX,EAAA,CAAAY,YAAA,EAAS;IAEPZ,EADF,CAAAE,cAAA,iBAAgE,eACpD;IAAAF,EAAA,CAAAW,MAAA,iBAAU;IAAAX,EAAA,CAAAY,YAAA,EAAW;IAC/BZ,EAAA,CAAAW,MAAA,iBACF;IAAAX,EAAA,CAAAY,YAAA,EAAS;;;ADnEjB,OAAM,MAAOuB,eAAe;EAM1BC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IARhB,KAAAP,WAAW,GAAuB,IAAI;IACtC,KAAAH,aAAa,GAAG,CAAC;IACjB,KAAAW,UAAU,GAAG,EAAE;IACP,KAAAC,QAAQ,GAAG,IAAI3C,OAAO,EAAQ;EAMnC;EAEH4C,QAAQA,CAAA;IACN;IACA,IAAI,CAACL,WAAW,CAACM,YAAY,CAC1BC,IAAI,CAAC7C,SAAS,CAAC,IAAI,CAAC0C,QAAQ,CAAC,CAAC,CAC9BI,SAAS,CAACC,IAAI,IAAG;MAChB,IAAI,CAACd,WAAW,GAAGc,IAAI;MACvB,IAAIA,IAAI,EAAE;QACR;QACA,IAAI,CAACR,WAAW,CAACS,SAAS,EAAE,CAACF,SAAS,EAAE;;IAE5C,CAAC,CAAC;IAEJ;IACA,IAAI,CAACP,WAAW,CAACU,KAAK,CACnBJ,IAAI,CAAC7C,SAAS,CAAC,IAAI,CAAC0C,QAAQ,CAAC,CAAC,CAC9BI,SAAS,CAACI,IAAI,IAAG;MAChB,IAAI,CAACpB,aAAa,GAAGoB,IAAI,GAAGA,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAKD,KAAK,GAAGC,IAAI,CAACC,QAAQ,EAAE,CAAC,CAAC,GAAG,CAAC;IAClG,CAAC,CAAC;EACN;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACd,QAAQ,CAACe,IAAI,EAAE;IACpB,IAAI,CAACf,QAAQ,CAACgB,QAAQ,EAAE;EAC1B;EAEAC,eAAeA,CAAA;IACb,OAAO,IAAI,CAACrB,WAAW,CAACqB,eAAe,EAAE;EAC3C;EAEAxB,OAAOA,CAAA;IACL,OAAO,IAAI,CAACG,WAAW,CAACH,OAAO,EAAE;EACnC;EAEAyB,QAAQA,CAAA;IACN,IAAI,IAAI,CAACnB,UAAU,CAACoB,IAAI,EAAE,EAAE;MAC1B,IAAI,CAACrB,MAAM,CAACsB,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;QAAEC,WAAW,EAAE;UAAEC,MAAM,EAAE,IAAI,CAACvB,UAAU,CAACoB,IAAI;QAAE;MAAE,CAAE,CAAC;KACtF,MAAM;MACL,IAAI,CAACrB,MAAM,CAACsB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;;EAEpC;EAEAnC,MAAMA,CAAA;IACJ,IAAI,CAACW,WAAW,CAACX,MAAM,EAAE;EAC3B;EAEAV,QAAQA,CAAA;IACN,IAAI,CAACuB,MAAM,CAACsB,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;EAEA3C,WAAWA,CAAA;IACT;IACA8C,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;EACpC;EAEA7C,UAAUA,CAAA;IACR,IAAI,CAACmB,MAAM,CAACsB,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;EACnC;EAEAnD,cAAcA,CAAA;IACZ,IAAI,CAAC6B,MAAM,CAACsB,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;EACxC;;;uBAxEW1B,eAAe,EAAAnC,EAAA,CAAAkE,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAApE,EAAA,CAAAkE,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAtE,EAAA,CAAAkE,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAfrC,eAAe;MAAAsC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCPtB/E,EAJN,CAAAE,cAAA,qBAA4C,aACd,aAEa,eAC3B;UAAAF,EAAA,CAAAW,MAAA,WAAI;UAAAX,EAAA,CAAAY,YAAA,EAAW;UACzBZ,EAAA,CAAAE,cAAA,cAAyB;UAAAF,EAAA,CAAAW,MAAA,eAAQ;UACnCX,EADmC,CAAAY,YAAA,EAAO,EACpC;UAKFZ,EAFJ,CAAAE,cAAA,aAA8B,wBAC8B,gBAC7C;UAAAF,EAAA,CAAAW,MAAA,uBAAe;UAAAX,EAAA,CAAAY,YAAA,EAAY;UACtCZ,EAAA,CAAAE,cAAA,gBAG6D;UAFtDF,EAAA,CAAAiF,gBAAA,2BAAAC,yDAAAC,MAAA;YAAAnF,EAAA,CAAAK,aAAA,CAAA+E,GAAA;YAAApF,EAAA,CAAAqF,kBAAA,CAAAL,GAAA,CAAAxC,UAAA,EAAA2C,MAAA,MAAAH,GAAA,CAAAxC,UAAA,GAAA2C,MAAA;YAAA,OAAAnF,EAAA,CAAAS,WAAA,CAAA0E,MAAA;UAAA,EAAwB;UACxBnF,EAAA,CAAAG,UAAA,yBAAAmF,uDAAA;YAAAtF,EAAA,CAAAK,aAAA,CAAA+E,GAAA;YAAA,OAAApF,EAAA,CAAAS,WAAA,CAAeuE,GAAA,CAAArB,QAAA,EAAU;UAAA,EAAC;UAFjC3D,EAAA,CAAAY,YAAA,EAG6D;UAC7DZ,EAAA,CAAAE,cAAA,mBAA6D;UAAzCF,EAAA,CAAAG,UAAA,mBAAAoF,oDAAA;YAAAvF,EAAA,CAAAK,aAAA,CAAA+E,GAAA;YAAA,OAAApF,EAAA,CAAAS,WAAA,CAASuE,GAAA,CAAArB,QAAA,EAAU;UAAA,EAAC;UAAqB3D,EAAA,CAAAW,MAAA,cAAM;UAEvEX,EAFuE,CAAAY,YAAA,EAAW,EAC/D,EACb;UAKFZ,EAFJ,CAAAE,cAAA,eAAuB,kBAC4C,gBACrD;UAAAF,EAAA,CAAAW,MAAA,qBAAa;UAAAX,EAAA,CAAAY,YAAA,EAAW;UAClCZ,EAAA,CAAAW,MAAA,eACF;UAAAX,EAAA,CAAAY,YAAA,EAAS;UA6CTZ,EA1CA,CAAAqB,UAAA,KAAAmE,wCAAA,4BAA0D,KAAAC,uCAAA,gCAAAzF,EAAA,CAAA0F,sBAAA,CA0ChC;UAYhC1F,EAFI,CAAAY,YAAA,EAAM,EACF,EACM;;;;UArECZ,EAAA,CAAA2B,SAAA,IAAwB;UAAxB3B,EAAA,CAAA2F,gBAAA,YAAAX,GAAA,CAAAxC,UAAA,CAAwB;UAelBxC,EAAA,CAAA2B,SAAA,GAAyB;UAAA3B,EAAzB,CAAA4B,UAAA,SAAAoD,GAAA,CAAAtB,eAAA,GAAyB,aAAAkC,cAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}