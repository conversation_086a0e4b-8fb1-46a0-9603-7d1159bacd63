/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 0)
        return 0;
    if (n === 1)
        return 1;
    if (n === 2)
        return 2;
    if (n % 100 === Math.floor(n % 100) && (n % 100 >= 3 && n % 100 <= 10))
        return 3;
    if (n % 100 === Math.floor(n % 100) && (n % 100 >= 11 && n % 100 <= 99))
        return 4;
    return 5;
}
export default ["ar-ER", [["ص", "م"], u, u], [["ص", "م"], u, ["صباحًا", "مساءً"]], [["ح", "ن", "ث", "ر", "خ", "ج", "س"], ["الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"], u, ["أحد", "إثنين", "ثلاثاء", "أربعاء", "خميس", "جمعة", "سبت"]], u, [["ي", "ف", "م", "أ", "و", "ن", "ل", "غ", "س", "ك", "ب", "د"], ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"], u], u, [["ق.م", "م"], u, ["قبل الميلاد", "ميلادي"]], 1, [6, 0], ["d‏/M‏/y", "dd‏/MM‏/y", "d MMMM y", "EEEE، d MMMM y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1}, {0}", u, "{1} في {0}", u], [".", ",", ";", "‎%‎", "‎+", "‎-", "E", "×", "‰", "∞", "ليس رقمًا", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "ERN", "Nfk", "ناكفا أريتري", { "AED": ["د.إ.‏"], "ARS": [u, "AR$"], "AUD": ["AU$"], "BBD": [u, "BB$"], "BHD": ["د.ب.‏"], "BMD": [u, "BM$"], "BND": [u, "BN$"], "BSD": [u, "BS$"], "BYN": [u, "р."], "BZD": [u, "BZ$"], "CAD": ["CA$"], "CLP": [u, "CL$"], "CNY": ["CN¥"], "COP": [u, "CO$"], "CUP": [u, "CU$"], "DOP": [u, "DO$"], "DZD": ["د.ج.‏"], "EGP": ["ج.م.‏", "E£"], "ERN": ["Nfk"], "FJD": [u, "FJ$"], "GBP": ["UK£"], "GYD": [u, "GY$"], "HKD": ["HK$"], "IQD": ["د.ع.‏"], "IRR": ["ر.إ."], "JMD": [u, "JM$"], "JOD": ["د.أ.‏"], "JPY": ["JP¥"], "KWD": ["د.ك.‏"], "KYD": [u, "KY$"], "LBP": ["ل.ل.‏", "L£"], "LRD": [u, "$LR"], "LYD": ["د.ل.‏"], "MAD": ["د.م.‏"], "MRU": ["أ.م."], "MXN": ["MX$"], "NZD": ["NZ$"], "OMR": ["ر.ع.‏"], "PHP": [u, "₱"], "QAR": ["ر.ق.‏"], "SAR": ["ر.س.‏"], "SBD": [u, "SB$"], "SDD": ["د.س.‏"], "SDG": ["ج.س."], "SRD": [u, "SR$"], "SYP": ["ل.س.‏", "£"], "THB": ["฿"], "TND": ["د.ت.‏"], "TTD": [u, "TT$"], "TWD": ["NT$"], "USD": ["US$"], "UYU": [u, "UY$"], "YER": ["ر.ي.‏"] }, "rtl", plural];
//# sourceMappingURL=data:application/json;base64,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