/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { inject, InjectionToken, LOCALE_ID } from '@angular/core';
import { Subject } from 'rxjs';
/** InjectionToken for datepicker that can be used to override default locale code. */
export const MAT_DATE_LOCALE = new InjectionToken('MAT_DATE_LOCALE', {
    providedIn: 'root',
    factory: MAT_DATE_LOCALE_FACTORY,
});
/** @docs-private */
export function MAT_DATE_LOCALE_FACTORY() {
    return inject(LOCALE_ID);
}
/** Adapts type `D` to be usable as a date by cdk-based components that work with dates. */
export class DateAdapter {
    constructor() {
        this._localeChanges = new Subject();
        /** A stream that emits when the locale changes. */
        this.localeChanges = this._localeChanges;
    }
    /**
     * Given a potential date object, returns that same date object if it is
     * a valid date, or `null` if it's not a valid date.
     * @param obj The object to check.
     * @returns A date or `null`.
     */
    getValidDateOrNull(obj) {
        return this.isDateInstance(obj) && this.isValid(obj) ? obj : null;
    }
    /**
     * Attempts to deserialize a value to a valid date object. This is different from parsing in that
     * deserialize should only accept non-ambiguous, locale-independent formats (e.g. a ISO 8601
     * string). The default implementation does not allow any deserialization, it simply checks that
     * the given value is already a valid date object or null. The `<mat-datepicker>` will call this
     * method on all of its `@Input()` properties that accept dates. It is therefore possible to
     * support passing values from your backend directly to these properties by overriding this method
     * to also deserialize the format used by your backend.
     * @param value The value to be deserialized into a date object.
     * @returns The deserialized date object, either a valid date, null if the value can be
     *     deserialized into a null date (e.g. the empty string), or an invalid date.
     */
    deserialize(value) {
        if (value == null || (this.isDateInstance(value) && this.isValid(value))) {
            return value;
        }
        return this.invalid();
    }
    /**
     * Sets the locale used for all dates.
     * @param locale The new locale.
     */
    setLocale(locale) {
        this.locale = locale;
        this._localeChanges.next();
    }
    /**
     * Compares two dates.
     * @param first The first date to compare.
     * @param second The second date to compare.
     * @returns 0 if the dates are equal, a number less than 0 if the first date is earlier,
     *     a number greater than 0 if the first date is later.
     */
    compareDate(first, second) {
        return (this.getYear(first) - this.getYear(second) ||
            this.getMonth(first) - this.getMonth(second) ||
            this.getDate(first) - this.getDate(second));
    }
    /**
     * Checks if two dates are equal.
     * @param first The first date to check.
     * @param second The second date to check.
     * @returns Whether the two dates are equal.
     *     Null dates are considered equal to other null dates.
     */
    sameDate(first, second) {
        if (first && second) {
            let firstValid = this.isValid(first);
            let secondValid = this.isValid(second);
            if (firstValid && secondValid) {
                return !this.compareDate(first, second);
            }
            return firstValid == secondValid;
        }
        return first == second;
    }
    /**
     * Clamp the given date between min and max dates.
     * @param date The date to clamp.
     * @param min The minimum value to allow. If null or omitted no min is enforced.
     * @param max The maximum value to allow. If null or omitted no max is enforced.
     * @returns `min` if `date` is less than `min`, `max` if date is greater than `max`,
     *     otherwise `date`.
     */
    clampDate(date, min, max) {
        if (min && this.compareDate(date, min) < 0) {
            return min;
        }
        if (max && this.compareDate(date, max) > 0) {
            return max;
        }
        return date;
    }
}
//# sourceMappingURL=data:application/json;base64,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