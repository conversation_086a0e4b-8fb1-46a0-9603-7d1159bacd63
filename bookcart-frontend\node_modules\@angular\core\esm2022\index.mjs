/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/* This file is not used to build this module. It is only used during editing
 * by the TypeScript language service and during build for verification. `ngc`
 * replaces this file with production index.ts when it rewrites private symbol
 * names.
 */
export * from './public_api';
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9jb3JlL2luZGV4LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7R0FNRztBQUVIOzs7O0dBSUc7QUFFSCxjQUFjLGNBQWMsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuXG4vKiBUaGlzIGZpbGUgaXMgbm90IHVzZWQgdG8gYnVpbGQgdGhpcyBtb2R1bGUuIEl0IGlzIG9ubHkgdXNlZCBkdXJpbmcgZWRpdGluZ1xuICogYnkgdGhlIFR5cGVTY3JpcHQgbGFuZ3VhZ2Ugc2VydmljZSBhbmQgZHVyaW5nIGJ1aWxkIGZvciB2ZXJpZmljYXRpb24uIGBuZ2NgXG4gKiByZXBsYWNlcyB0aGlzIGZpbGUgd2l0aCBwcm9kdWN0aW9uIGluZGV4LnRzIHdoZW4gaXQgcmV3cml0ZXMgcHJpdmF0ZSBzeW1ib2xcbiAqIG5hbWVzLlxuICovXG5cbmV4cG9ydCAqIGZyb20gJy4vcHVibGljX2FwaSc7XG4iXX0=