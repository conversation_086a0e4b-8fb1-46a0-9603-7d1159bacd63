/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["bo-IN", [["སྔ་དྲོ་", "ཕྱི་དྲོ་"], u, u], u, [["ཉི", "ཟླ", "མིག", "ལྷག", "ཕུར", "སངས", "སྤེན"], ["ཉི་མ་", "ཟླ་བ་", "མིག་དམར་", "ལྷག་པ་", "ཕུར་བུ་", "པ་སངས་", "སྤེན་པ་"], ["གཟའ་ཉི་མ་", "གཟའ་ཟླ་བ་", "གཟའ་མིག་དམར་", "གཟའ་ལྷག་པ་", "གཟའ་ཕུར་བུ་", "གཟའ་པ་སངས་", "གཟའ་སྤེན་པ་"], ["ཉི་མ་", "ཟླ་བ་", "མིག་དམར་", "ལྷག་པ་", "ཕུར་བུ་", "པ་སངས་", "སྤེན་པ་"]], u, [["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"], ["ཟླ་༡", "ཟླ་༢", "ཟླ་༣", "ཟླ་༤", "ཟླ་༥", "ཟླ་༦", "ཟླ་༧", "ཟླ་༨", "ཟླ་༩", "ཟླ་༡༠", "ཟླ་༡༡", "ཟླ་༡༢"], ["ཟླ་བ་དང་པོ", "ཟླ་བ་གཉིས་པ", "ཟླ་བ་གསུམ་པ", "ཟླ་བ་བཞི་པ", "ཟླ་བ་ལྔ་པ", "ཟླ་བ་དྲུག་པ", "ཟླ་བ་བདུན་པ", "ཟླ་བ་བརྒྱད་པ", "ཟླ་བ་དགུ་པ", "ཟླ་བ་བཅུ་པ", "ཟླ་བ་བཅུ་གཅིག་པ", "ཟླ་བ་བཅུ་གཉིས་པ"]], [["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"], ["ཟླ་༡", "ཟླ་༢", "ཟླ་༣", "ཟླ་༤", "ཟླ་༥", "ཟླ་༦", "ཟླ་༧", "ཟླ་༨", "ཟླ་༩", "ཟླ་༡༠", "ཟླ་༡༡", "ཟླ་༡༢"], ["ཟླ་བ་དང་པོ་", "ཟླ་བ་གཉིས་པ་", "ཟླ་བ་གསུམ་པ་", "ཟླ་བ་བཞི་པ་", "ཟླ་བ་ལྔ་པ་", "ཟླ་བ་དྲུག་པ་", "ཟླ་བ་བདུན་པ་", "ཟླ་བ་བརྒྱད་པ་", "ཟླ་བ་དགུ་པ་", "ཟླ་བ་བཅུ་པ་", "ཟླ་བ་བཅུ་གཅིག་པ་", "ཟླ་བ་བཅུ་གཉིས་པ་"]], [["སྤྱི་ལོ་སྔོན་", "སྤྱི་ལོ་"], u, u], 0, [0, 0], ["y-MM-dd", "y ལོའི་MMMཚེས་d", "སྤྱི་ལོ་y MMMMའི་ཚེས་d", "y MMMMའི་ཚེས་d, EEEE"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "INR", "₹", "རྒྱ་གར་སྒོར་", { "JPY": ["JP¥", "¥"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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