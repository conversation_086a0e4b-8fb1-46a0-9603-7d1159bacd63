/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
export default [[["o poln.", "nap.", "ráno", "dop.", "pop.", "več.", "v n."], ["o poln.", "napol.", "ráno", "dopol.", "popol.", "večer", "v noci"], ["o polnoci", "napoludnie", "ráno", "dopoludnia", "popoludní", "večer", "v noci"]], [["poln.", "pol.", "ráno", "dop.", "pop.", "več.", "noc"], ["poln.", "pol.", "ráno", "dopol.", "popol.", "večer", "noc"], ["polnoc", "poludnie", "ráno", "dopoludnie", "popoludnie", "večer", "noc"]], ["00:00", "12:00", ["04:00", "09:00"], ["09:00", "12:00"], ["12:00", "18:00"], ["18:00", "22:00"], ["22:00", "04:00"]]];
//# sourceMappingURL=data:application/json;base64,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