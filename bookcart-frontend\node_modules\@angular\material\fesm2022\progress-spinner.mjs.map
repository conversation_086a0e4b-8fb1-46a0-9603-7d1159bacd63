{"version": 3, "file": "progress-spinner.mjs", "sources": ["../../../../../../src/material/progress-spinner/progress-spinner.ts", "../../../../../../src/material/progress-spinner/progress-spinner.html", "../../../../../../src/material/progress-spinner/module.ts", "../../../../../../src/material/progress-spinner/progress-spinner_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  ElementRef,\n  Inject,\n  InjectionToken,\n  Input,\n  Optional,\n  ViewChild,\n  ViewEncapsulation,\n  numberAttribute,\n  ANIMATION_MODULE_TYPE,\n} from '@angular/core';\nimport {ThemePalette} from '@angular/material/core';\nimport {NgTemplateOutlet} from '@angular/common';\n\n/** Possible mode for a progress spinner. */\nexport type ProgressSpinnerMode = 'determinate' | 'indeterminate';\n\n/** Default `mat-progress-spinner` options that can be overridden. */\nexport interface MatProgressSpinnerDefaultOptions {\n  /** Default color of the spinner. */\n  color?: ThemePalette;\n  /** Diameter of the spinner. */\n  diameter?: number;\n  /** Width of the spinner's stroke. */\n  strokeWidth?: number;\n  /**\n   * Whether the animations should be force to be enabled, ignoring if the current environment is\n   * using NoopAnimationsModule.\n   */\n  _forceAnimations?: boolean;\n}\n\n/** Injection token to be used to override the default options for `mat-progress-spinner`. */\nexport const MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS =\n  new InjectionToken<MatProgressSpinnerDefaultOptions>('mat-progress-spinner-default-options', {\n    providedIn: 'root',\n    factory: MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY,\n  });\n\n/** @docs-private */\nexport function MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY(): MatProgressSpinnerDefaultOptions {\n  return {diameter: BASE_SIZE};\n}\n\n/**\n * Base reference size of the spinner.\n */\nconst BASE_SIZE = 100;\n\n/**\n * Base reference stroke width of the spinner.\n */\nconst BASE_STROKE_WIDTH = 10;\n\n@Component({\n  selector: 'mat-progress-spinner, mat-spinner',\n  exportAs: 'matProgressSpinner',\n  host: {\n    'role': 'progressbar',\n    'class': 'mat-mdc-progress-spinner mdc-circular-progress',\n    // set tab index to -1 so screen readers will read the aria-label\n    // Note: there is a known issue with JAWS that does not read progressbar aria labels on FireFox\n    'tabindex': '-1',\n    '[class]': '\"mat-\" + color',\n    '[class._mat-animation-noopable]': `_noopAnimations`,\n    '[class.mdc-circular-progress--indeterminate]': 'mode === \"indeterminate\"',\n    '[style.width.px]': 'diameter',\n    '[style.height.px]': 'diameter',\n    '[style.--mdc-circular-progress-size]': 'diameter + \"px\"',\n    '[style.--mdc-circular-progress-active-indicator-width]': 'diameter + \"px\"',\n    '[attr.aria-valuemin]': '0',\n    '[attr.aria-valuemax]': '100',\n    '[attr.aria-valuenow]': 'mode === \"determinate\" ? value : null',\n    '[attr.mode]': 'mode',\n  },\n  templateUrl: 'progress-spinner.html',\n  styleUrl: 'progress-spinner.css',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  standalone: true,\n  imports: [NgTemplateOutlet],\n})\nexport class MatProgressSpinner {\n  /** Whether the _mat-animation-noopable class should be applied, disabling animations.  */\n  _noopAnimations: boolean;\n\n  // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n  /** Theme palette color of the progress spinner. */\n  @Input()\n  get color() {\n    return this._color || this._defaultColor;\n  }\n  set color(value: string | null | undefined) {\n    this._color = value;\n  }\n  private _color: string | null | undefined;\n  private _defaultColor: ThemePalette = 'primary';\n\n  /** The element of the determinate spinner. */\n  @ViewChild('determinateSpinner') _determinateCircle: ElementRef<HTMLElement>;\n\n  constructor(\n    readonly _elementRef: ElementRef<HTMLElement>,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) animationMode: string,\n    @Inject(MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS)\n    defaults?: MatProgressSpinnerDefaultOptions,\n  ) {\n    this._noopAnimations =\n      animationMode === 'NoopAnimations' && !!defaults && !defaults._forceAnimations;\n    this.mode =\n      _elementRef.nativeElement.nodeName.toLowerCase() === 'mat-spinner'\n        ? 'indeterminate'\n        : 'determinate';\n\n    if (defaults) {\n      if (defaults.color) {\n        this.color = this._defaultColor = defaults.color;\n      }\n\n      if (defaults.diameter) {\n        this.diameter = defaults.diameter;\n      }\n\n      if (defaults.strokeWidth) {\n        this.strokeWidth = defaults.strokeWidth;\n      }\n    }\n  }\n\n  /**\n   * Mode of the progress bar.\n   *\n   * Input must be one of these values: determinate, indeterminate, buffer, query, defaults to\n   * 'determinate'.\n   * Mirrored to mode attribute.\n   */\n  @Input() mode: ProgressSpinnerMode;\n\n  /** Value of the progress bar. Defaults to zero. Mirrored to aria-valuenow. */\n  @Input({transform: numberAttribute})\n  get value(): number {\n    return this.mode === 'determinate' ? this._value : 0;\n  }\n  set value(v: number) {\n    this._value = Math.max(0, Math.min(100, v || 0));\n  }\n  private _value = 0;\n\n  /** The diameter of the progress spinner (will set width and height of svg). */\n  @Input({transform: numberAttribute})\n  get diameter(): number {\n    return this._diameter;\n  }\n  set diameter(size: number) {\n    this._diameter = size || 0;\n  }\n  private _diameter = BASE_SIZE;\n\n  /** Stroke width of the progress spinner. */\n  @Input({transform: numberAttribute})\n  get strokeWidth(): number {\n    return this._strokeWidth ?? this.diameter / 10;\n  }\n  set strokeWidth(value: number) {\n    this._strokeWidth = value || 0;\n  }\n  private _strokeWidth: number;\n\n  /** The radius of the spinner, adjusted for stroke width. */\n  _circleRadius(): number {\n    return (this.diameter - BASE_STROKE_WIDTH) / 2;\n  }\n\n  /** The view box of the spinner's svg element. */\n  _viewBox() {\n    const viewBox = this._circleRadius() * 2 + this.strokeWidth;\n    return `0 0 ${viewBox} ${viewBox}`;\n  }\n\n  /** The stroke circumference of the svg circle. */\n  _strokeCircumference(): number {\n    return 2 * Math.PI * this._circleRadius();\n  }\n\n  /** The dash offset of the svg circle. */\n  _strokeDashOffset() {\n    if (this.mode === 'determinate') {\n      return (this._strokeCircumference() * (100 - this._value)) / 100;\n    }\n    return null;\n  }\n\n  /** Stroke width of the circle in percent. */\n  _circleStrokeWidth() {\n    return (this.strokeWidth / this.diameter) * 100;\n  }\n}\n\n/**\n * @deprecated Import Progress Spinner instead. Note that the\n *    `mat-spinner` selector isn't deprecated.\n * @breaking-change 16.0.0\n */\n// tslint:disable-next-line:variable-name\nexport const MatSpinner = MatProgressSpinner;\n", "<ng-template #circle>\n  <svg [attr.viewBox]=\"_viewBox()\" class=\"mdc-circular-progress__indeterminate-circle-graphic\"\n       xmlns=\"http://www.w3.org/2000/svg\" focusable=\"false\">\n    <circle [attr.r]=\"_circleRadius()\"\n            [style.stroke-dasharray.px]=\"_strokeCircumference()\"\n            [style.stroke-dashoffset.px]=\"_strokeCircumference() / 2\"\n            [style.stroke-width.%]=\"_circleStrokeWidth()\"\n            cx=\"50%\" cy=\"50%\"/>\n  </svg>\n</ng-template>\n\n<!--\n  All children need to be hidden for screen readers in order to support ChromeVox.\n  More context in the issue: https://github.com/angular/components/issues/22165.\n-->\n<div class=\"mdc-circular-progress__determinate-container\" aria-hidden=\"true\" #determinateSpinner>\n  <svg [attr.viewBox]=\"_viewBox()\" class=\"mdc-circular-progress__determinate-circle-graphic\"\n       xmlns=\"http://www.w3.org/2000/svg\" focusable=\"false\">\n    <circle [attr.r]=\"_circleRadius()\"\n            [style.stroke-dasharray.px]=\"_strokeCircumference()\"\n            [style.stroke-dashoffset.px]=\"_strokeDashOffset()\"\n            [style.stroke-width.%]=\"_circleStrokeWidth()\"\n            class=\"mdc-circular-progress__determinate-circle\"\n            cx=\"50%\" cy=\"50%\"/>\n  </svg>\n</div>\n<!--TODO: figure out why there are 3 separate svgs-->\n<div class=\"mdc-circular-progress__indeterminate-container\" aria-hidden=\"true\">\n  <div class=\"mdc-circular-progress__spinner-layer\">\n    <div class=\"mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left\">\n      <ng-container [ngTemplateOutlet]=\"circle\"></ng-container>\n    </div>\n    <div class=\"mdc-circular-progress__gap-patch\">\n      <ng-container [ngTemplateOutlet]=\"circle\"></ng-container>\n    </div>\n    <div class=\"mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right\">\n      <ng-container [ngTemplateOutlet]=\"circle\"></ng-container>\n    </div>\n  </div>\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '@angular/material/core';\nimport {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MatSpinner} from './progress-spinner';\nimport {CommonModule} from '@angular/common';\n\n@NgModule({\n  imports: [CommonModule, MatProgressSpinner, MatSpinner],\n  exports: [MatProgressSpinner, MatSpinner, MatCommonModule],\n})\nexport class MatProgressSpinnerModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;AA0CA;MACa,oCAAoC,GAC/C,IAAI,cAAc,CAAmC,sCAAsC,EAAE;AAC3F,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,4CAA4C;AACtD,CAAA,EAAE;AAEL;SACgB,4CAA4C,GAAA;AAC1D,IAAA,OAAO,EAAC,QAAQ,EAAE,SAAS,EAAC,CAAC;AAC/B,CAAC;AAED;;AAEG;AACH,MAAM,SAAS,GAAG,GAAG,CAAC;AAEtB;;AAEG;AACH,MAAM,iBAAiB,GAAG,EAAE,CAAC;MA8BhB,kBAAkB,CAAA;;;AAM7B,IAAA,IACI,KAAK,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC;KAC1C;IACD,IAAI,KAAK,CAAC,KAAgC,EAAA;AACxC,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;KACrB;AAOD,IAAA,WAAA,CACW,WAAoC,EACF,aAAqB,EAEhE,QAA2C,EAAA;QAHlC,IAAW,CAAA,WAAA,GAAX,WAAW,CAAyB;QANvC,IAAa,CAAA,aAAA,GAAiB,SAAS,CAAC;QAkDxC,IAAM,CAAA,MAAA,GAAG,CAAC,CAAC;QAUX,IAAS,CAAA,SAAA,GAAG,SAAS,CAAC;AAjD5B,QAAA,IAAI,CAAC,eAAe;YAClB,aAAa,KAAK,gBAAgB,IAAI,CAAC,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;AACjF,QAAA,IAAI,CAAC,IAAI;YACP,WAAW,CAAC,aAAa,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,aAAa;AAChE,kBAAE,eAAe;kBACf,aAAa,CAAC;QAEpB,IAAI,QAAQ,EAAE;AACZ,YAAA,IAAI,QAAQ,CAAC,KAAK,EAAE;gBAClB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,KAAK,CAAC;aAClD;AAED,YAAA,IAAI,QAAQ,CAAC,QAAQ,EAAE;AACrB,gBAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;aACnC;AAED,YAAA,IAAI,QAAQ,CAAC,WAAW,EAAE;AACxB,gBAAA,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC;aACzC;SACF;KACF;;AAYD,IAAA,IACI,KAAK,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,IAAI,KAAK,aAAa,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;KACtD;IACD,IAAI,KAAK,CAAC,CAAS,EAAA;QACjB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KAClD;;AAID,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,IAAY,EAAA;AACvB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,CAAC;KAC5B;;AAID,IAAA,IACI,WAAW,GAAA;QACb,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;KAChD;IACD,IAAI,WAAW,CAAC,KAAa,EAAA;AAC3B,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK,IAAI,CAAC,CAAC;KAChC;;IAID,aAAa,GAAA;QACX,OAAO,CAAC,IAAI,CAAC,QAAQ,GAAG,iBAAiB,IAAI,CAAC,CAAC;KAChD;;IAGD,QAAQ,GAAA;AACN,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;AAC5D,QAAA,OAAO,CAAO,IAAA,EAAA,OAAO,CAAI,CAAA,EAAA,OAAO,EAAE,CAAC;KACpC;;IAGD,oBAAoB,GAAA;QAClB,OAAO,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;KAC3C;;IAGD,iBAAiB,GAAA;AACf,QAAA,IAAI,IAAI,CAAC,IAAI,KAAK,aAAa,EAAE;AAC/B,YAAA,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC;SAClE;AACD,QAAA,OAAO,IAAI,CAAC;KACb;;IAGD,kBAAkB,GAAA;QAChB,OAAO,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,IAAI,GAAG,CAAC;KACjD;8GAjHU,kBAAkB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAqBP,qBAAqB,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EACjC,oCAAoC,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAtBnC,kBAAkB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mCAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,IAAA,EAAA,MAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAyDV,eAAe,CAUf,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,eAAe,+CAUf,eAAe,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,aAAA,EAAA,UAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,kBAAA,EAAA,+BAAA,EAAA,iBAAA,EAAA,4CAAA,EAAA,4BAAA,EAAA,gBAAA,EAAA,UAAA,EAAA,iBAAA,EAAA,UAAA,EAAA,oCAAA,EAAA,mBAAA,EAAA,sDAAA,EAAA,mBAAA,EAAA,oBAAA,EAAA,GAAA,EAAA,oBAAA,EAAA,KAAA,EAAA,oBAAA,EAAA,yCAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,cAAA,EAAA,gDAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,oBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECzKpC,28DAwCA,EAAA,MAAA,EAAA,CAAA,ihOAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EDkDY,gBAAgB,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAEf,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBA5B9B,SAAS;+BACE,mCAAmC,EAAA,QAAA,EACnC,oBAAoB,EACxB,IAAA,EAAA;AACJ,wBAAA,MAAM,EAAE,aAAa;AACrB,wBAAA,OAAO,EAAE,gDAAgD;;;AAGzD,wBAAA,UAAU,EAAE,IAAI;AAChB,wBAAA,SAAS,EAAE,gBAAgB;AAC3B,wBAAA,iCAAiC,EAAE,CAAiB,eAAA,CAAA;AACpD,wBAAA,8CAA8C,EAAE,0BAA0B;AAC1E,wBAAA,kBAAkB,EAAE,UAAU;AAC9B,wBAAA,mBAAmB,EAAE,UAAU;AAC/B,wBAAA,sCAAsC,EAAE,iBAAiB;AACzD,wBAAA,wDAAwD,EAAE,iBAAiB;AAC3E,wBAAA,sBAAsB,EAAE,GAAG;AAC3B,wBAAA,sBAAsB,EAAE,KAAK;AAC7B,wBAAA,sBAAsB,EAAE,uCAAuC;AAC/D,wBAAA,aAAa,EAAE,MAAM;AACtB,qBAAA,EAAA,eAAA,EAGgB,uBAAuB,CAAC,MAAM,EAAA,aAAA,EAChC,iBAAiB,CAAC,IAAI,EAAA,UAAA,EACzB,IAAI,EAAA,OAAA,EACP,CAAC,gBAAgB,CAAC,EAAA,QAAA,EAAA,28DAAA,EAAA,MAAA,EAAA,CAAA,ihOAAA,CAAA,EAAA,CAAA;;0BAuBxB,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;;0BACxC,MAAM;2BAAC,oCAAoC,CAAA;yCAf1C,KAAK,EAAA,CAAA;sBADR,KAAK;gBAW2B,kBAAkB,EAAA,CAAA;sBAAlD,SAAS;uBAAC,oBAAoB,CAAA;gBAqCtB,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAIF,KAAK,EAAA,CAAA;sBADR,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAA;gBAW/B,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAA;gBAW/B,WAAW,EAAA,CAAA;sBADd,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAA;;AAuCrC;;;;AAIG;AACH;AACO,MAAM,UAAU,GAAG;;MErMb,wBAAwB,CAAA;8GAAxB,wBAAwB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;+GAAxB,wBAAwB,EAAA,OAAA,EAAA,CAHzB,YAAY,EAAE,kBAAkB,EAAE,UAAU,CAAA,EAAA,OAAA,EAAA,CAC5C,kBAAkB,EAAE,UAAU,EAAE,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;+GAE9C,wBAAwB,EAAA,OAAA,EAAA,CAHzB,YAAY,EACoB,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAE9C,wBAAwB,EAAA,UAAA,EAAA,CAAA;kBAJpC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,kBAAkB,EAAE,UAAU,CAAC;AACvD,oBAAA,OAAO,EAAE,CAAC,kBAAkB,EAAE,UAAU,EAAE,eAAe,CAAC;AAC3D,iBAAA,CAAA;;;AChBD;;AAEG;;;;"}