{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../shared/services/books.service\";\nimport * as i2 from \"../../shared/services/cart.service\";\nimport * as i3 from \"../../shared/services/auth.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/material/snack-bar\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/card\";\nimport * as i9 from \"@angular/material/form-field\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/chips\";\nimport * as i12 from \"@angular/material/select\";\nimport * as i13 from \"@angular/material/core\";\nimport * as i14 from \"@angular/material/progress-spinner\";\nfunction BookListComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"button\", 22)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Add New Book \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BookListComponent_mat_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", category_r1.categoryId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", category_r1.categoryName, \" \");\n  }\n}\nfunction BookListComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵelement(1, \"mat-spinner\", 24);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading books...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BookListComponent_div_51_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"search_off\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No books found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Try adjusting your search criteria or browse all books.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function BookListComponent_div_51_div_1_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clearFilters());\n    });\n    i0.ɵɵtext(8, \" Browse All Books \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BookListComponent_div_51_mat_card_2_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function BookListComponent_div_51_mat_card_2_div_25_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const book_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.editBook(book_r5));\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function BookListComponent_div_51_mat_card_2_div_25_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const book_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.deleteBook(book_r5));\n    });\n    i0.ɵɵelementStart(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"delete\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction BookListComponent_div_51_mat_card_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 30)(1, \"div\", 31)(2, \"img\", 32);\n    i0.ɵɵlistener(\"error\", function BookListComponent_div_51_mat_card_2_Template_img_error_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onImageError($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 33);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"mat-card-content\", 34)(6, \"div\", 35)(7, \"mat-chip\", 36);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"h3\", 37);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\", 38);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\", 39);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 40)(16, \"span\", 41);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"number\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"mat-card-actions\", 42)(20, \"div\", 43)(21, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function BookListComponent_div_51_mat_card_2_Template_button_click_21_listener() {\n      const book_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.addToCart(book_r5));\n    });\n    i0.ɵɵelementStart(22, \"mat-icon\");\n    i0.ɵɵtext(23, \"add_shopping_cart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(25, BookListComponent_div_51_mat_card_2_div_25_Template, 7, 0, \"div\", 3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const book_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", book_r5.imageUrl || \"assets/images/book-placeholder.jpg\", i0.ɵɵsanitizeUrl)(\"alt\", book_r5.title);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"low-stock\", book_r5.stockQuantity <= 5);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", book_r5.stockQuantity, \" in stock \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(book_r5.categoryName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(book_r5.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"by \", book_r5.author, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(book_r5.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"$\", i0.ɵɵpipeBind2(18, 13, book_r5.price, \"1.2-2\"), \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", book_r5.stockQuantity === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", book_r5.stockQuantity === 0 ? \"Out of Stock\" : \"Add to Cart\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isAdmin());\n  }\n}\nfunction BookListComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtemplate(1, BookListComponent_div_51_div_1_Template, 9, 0, \"div\", 26)(2, BookListComponent_div_51_mat_card_2_Template, 26, 16, \"mat-card\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.filteredBooks.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.filteredBooks);\n  }\n}\nexport class BookListComponent {\n  constructor(booksService, cartService, authService, route, router, snackBar) {\n    this.booksService = booksService;\n    this.cartService = cartService;\n    this.authService = authService;\n    this.route = route;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.books = [];\n    this.categories = [];\n    this.filteredBooks = [];\n    this.isLoading = true;\n    this.searchTerm = '';\n    this.selectedCategoryId = null;\n    this.sortBy = 'title';\n    this.sortDirection = 'asc';\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.loadCategories();\n    this.loadBooks();\n    // Listen to query parameters for search\n    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(params => {\n      if (params['search']) {\n        this.searchTerm = params['search'];\n        this.searchBooks();\n      } else if (params['category']) {\n        this.selectedCategoryId = +params['category'];\n        this.filterByCategory();\n      } else {\n        this.loadBooks();\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  loadBooks() {\n    this.isLoading = true;\n    this.booksService.getAllBooks().subscribe({\n      next: books => {\n        this.books = books;\n        this.filteredBooks = [...books];\n        this.applySorting();\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error loading books:', error);\n        this.snackBar.open('Error loading books. Please try again.', 'Close', {\n          duration: 3000,\n          panelClass: ['error-snackbar']\n        });\n        this.isLoading = false;\n      }\n    });\n  }\n  loadCategories() {\n    this.booksService.getCategories().subscribe({\n      next: categories => {\n        this.categories = categories;\n      },\n      error: error => {\n        console.error('Error loading categories:', error);\n      }\n    });\n  }\n  searchBooks() {\n    if (this.searchTerm.trim()) {\n      this.isLoading = true;\n      this.booksService.searchBooks(this.searchTerm.trim()).subscribe({\n        next: books => {\n          this.filteredBooks = books;\n          this.applySorting();\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error searching books:', error);\n          this.snackBar.open('Error searching books. Please try again.', 'Close', {\n            duration: 3000,\n            panelClass: ['error-snackbar']\n          });\n          this.isLoading = false;\n        }\n      });\n    } else {\n      this.filteredBooks = [...this.books];\n      this.applySorting();\n    }\n  }\n  filterByCategory() {\n    if (this.selectedCategoryId) {\n      this.isLoading = true;\n      this.booksService.getBooksByCategory(this.selectedCategoryId).subscribe({\n        next: books => {\n          this.filteredBooks = books;\n          this.applySorting();\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error filtering books by category:', error);\n          this.snackBar.open('Error filtering books. Please try again.', 'Close', {\n            duration: 3000,\n            panelClass: ['error-snackbar']\n          });\n          this.isLoading = false;\n        }\n      });\n    } else {\n      this.filteredBooks = [...this.books];\n      this.applySorting();\n    }\n  }\n  onCategoryChange() {\n    if (this.selectedCategoryId) {\n      this.router.navigate(['/books'], {\n        queryParams: {\n          category: this.selectedCategoryId\n        }\n      });\n    } else {\n      this.router.navigate(['/books']);\n    }\n  }\n  onSortChange() {\n    this.applySorting();\n  }\n  applySorting() {\n    this.filteredBooks.sort((a, b) => {\n      let aValue = a[this.sortBy];\n      let bValue = b[this.sortBy];\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n      if (this.sortDirection === 'asc') {\n        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\n      } else {\n        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;\n      }\n    });\n  }\n  addToCart(book) {\n    if (!this.authService.isAuthenticated()) {\n      this.router.navigate(['/login'], {\n        queryParams: {\n          returnUrl: '/books'\n        }\n      });\n      return;\n    }\n    const request = {\n      bookId: book.bookId,\n      quantity: 1\n    };\n    this.cartService.addToCart(request).subscribe({\n      next: () => {\n        this.snackBar.open(`\"${book.title}\" added to cart!`, 'Close', {\n          duration: 3000,\n          panelClass: ['success-snackbar']\n        });\n      },\n      error: error => {\n        console.error('Error adding to cart:', error);\n        this.snackBar.open('Error adding to cart. Please try again.', 'Close', {\n          duration: 3000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n  editBook(book) {\n    this.router.navigate(['/admin/books/edit', book.bookId]);\n  }\n  deleteBook(book) {\n    if (confirm(`Are you sure you want to delete \"${book.title}\"?`)) {\n      this.booksService.deleteBook(book.bookId).subscribe({\n        next: () => {\n          this.snackBar.open(`\"${book.title}\" deleted successfully!`, 'Close', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n          this.loadBooks();\n        },\n        error: error => {\n          console.error('Error deleting book:', error);\n          this.snackBar.open('Error deleting book. Please try again.', 'Close', {\n            duration: 3000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    }\n  }\n  clearFilters() {\n    this.searchTerm = '';\n    this.selectedCategoryId = null;\n    this.router.navigate(['/books']);\n  }\n  isAuthenticated() {\n    return this.authService.isAuthenticated();\n  }\n  isAdmin() {\n    return this.authService.isAdmin();\n  }\n  onImageError(event) {\n    event.target.src = 'assets/images/book-placeholder.jpg';\n  }\n  static {\n    this.ɵfac = function BookListComponent_Factory(t) {\n      return new (t || BookListComponent)(i0.ɵɵdirectiveInject(i1.BooksService), i0.ɵɵdirectiveInject(i2.CartService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BookListComponent,\n      selectors: [[\"app-book-list\"]],\n      decls: 52,\n      vars: 8,\n      consts: [[1, \"book-list-container\"], [1, \"header-section\"], [1, \"page-title\"], [\"class\", \"admin-actions\", 4, \"ngIf\"], [1, \"filters-section\"], [1, \"filters-card\"], [1, \"filters-content\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [3, \"valueChange\", \"selectionChange\", \"value\"], [3, \"value\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"matSuffix\", \"\"], [\"value\", \"title\"], [\"value\", \"author\"], [\"value\", \"price\"], [\"value\", \"createdDate\"], [\"value\", \"asc\"], [\"value\", \"desc\"], [\"mat-stroked-button\", \"\", 1, \"clear-filters-btn\", 3, \"click\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"books-grid\", 4, \"ngIf\"], [1, \"admin-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/admin/books/add\"], [1, \"loading-container\"], [\"diameter\", \"50\"], [1, \"books-grid\"], [\"class\", \"no-books-message\", 4, \"ngIf\"], [\"class\", \"book-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"no-books-message\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"book-card\"], [1, \"book-image-container\"], [1, \"book-image\", 3, \"error\", \"src\", \"alt\"], [1, \"stock-badge\"], [1, \"book-content\"], [1, \"book-category\"], [\"color\", \"primary\", \"selected\", \"\"], [1, \"book-title\"], [1, \"book-author\"], [1, \"book-description\"], [1, \"book-price\"], [1, \"price\"], [1, \"book-actions\"], [1, \"user-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\", \"disabled\"], [\"mat-icon-button\", \"\", \"color\", \"accent\", \"matTooltip\", \"Edit Book\", 3, \"click\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", \"matTooltip\", \"Delete Book\", 3, \"click\"]],\n      template: function BookListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\")(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"library_books\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6, \" Discover Amazing Books \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"p\");\n          i0.ɵɵtext(8, \"Find your next favorite read from our extensive collection\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(9, BookListComponent_div_9_Template, 5, 0, \"div\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 4)(11, \"mat-card\", 5)(12, \"div\", 6)(13, \"mat-form-field\", 7)(14, \"mat-label\");\n          i0.ɵɵtext(15, \"Category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"mat-select\", 8);\n          i0.ɵɵtwoWayListener(\"valueChange\", function BookListComponent_Template_mat_select_valueChange_16_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedCategoryId, $event) || (ctx.selectedCategoryId = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function BookListComponent_Template_mat_select_selectionChange_16_listener() {\n            return ctx.onCategoryChange();\n          });\n          i0.ɵɵelementStart(17, \"mat-option\", 9);\n          i0.ɵɵtext(18, \"All Categories\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(19, BookListComponent_mat_option_19_Template, 2, 2, \"mat-option\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"mat-icon\", 11);\n          i0.ɵɵtext(21, \"category\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"mat-form-field\", 7)(23, \"mat-label\");\n          i0.ɵɵtext(24, \"Sort By\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"mat-select\", 8);\n          i0.ɵɵtwoWayListener(\"valueChange\", function BookListComponent_Template_mat_select_valueChange_25_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.sortBy, $event) || (ctx.sortBy = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function BookListComponent_Template_mat_select_selectionChange_25_listener() {\n            return ctx.onSortChange();\n          });\n          i0.ɵɵelementStart(26, \"mat-option\", 12);\n          i0.ɵɵtext(27, \"Title\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"mat-option\", 13);\n          i0.ɵɵtext(29, \"Author\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"mat-option\", 14);\n          i0.ɵɵtext(31, \"Price\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"mat-option\", 15);\n          i0.ɵɵtext(33, \"Date Added\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"mat-icon\", 11);\n          i0.ɵɵtext(35, \"sort\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"mat-form-field\", 7)(37, \"mat-label\");\n          i0.ɵɵtext(38, \"Order\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"mat-select\", 8);\n          i0.ɵɵtwoWayListener(\"valueChange\", function BookListComponent_Template_mat_select_valueChange_39_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.sortDirection, $event) || (ctx.sortDirection = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function BookListComponent_Template_mat_select_selectionChange_39_listener() {\n            return ctx.onSortChange();\n          });\n          i0.ɵɵelementStart(40, \"mat-option\", 16);\n          i0.ɵɵtext(41, \"Ascending\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"mat-option\", 17);\n          i0.ɵɵtext(43, \"Descending\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"mat-icon\", 11);\n          i0.ɵɵtext(45, \"swap_vert\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function BookListComponent_Template_button_click_46_listener() {\n            return ctx.clearFilters();\n          });\n          i0.ɵɵelementStart(47, \"mat-icon\");\n          i0.ɵɵtext(48, \"clear\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(49, \" Clear Filters \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(50, BookListComponent_div_50_Template, 4, 0, \"div\", 19)(51, BookListComponent_div_51_Template, 3, 2, \"div\", 20);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.isAdmin());\n          i0.ɵɵadvance(7);\n          i0.ɵɵtwoWayProperty(\"value\", ctx.selectedCategoryId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"value\", ctx.sortBy);\n          i0.ɵɵadvance(14);\n          i0.ɵɵtwoWayProperty(\"value\", ctx.sortDirection);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i4.RouterLink, i7.MatButton, i7.MatIconButton, i8.MatCard, i8.MatCardActions, i8.MatCardContent, i9.MatFormField, i9.MatLabel, i9.MatSuffix, i10.MatIcon, i11.MatChip, i12.MatSelect, i13.MatOption, i14.MatProgressSpinner, i6.DecimalPipe],\n      styles: [\".book-list-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 20px;\\n}\\n\\n.header-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 30px;\\n}\\n\\n.page-title[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  margin: 0 0 8px 0;\\n  color: #333;\\n  font-size: 2rem;\\n  font-weight: 600;\\n}\\n\\n.page-title[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 1.1rem;\\n}\\n\\n.admin-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  height: 48px;\\n  font-size: 16px;\\n}\\n\\n.filters-section[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.filters-card[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n\\n.filters-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  align-items: center;\\n  flex-wrap: wrap;\\n}\\n\\n.filter-field[_ngcontent-%COMP%] {\\n  min-width: 150px;\\n}\\n\\n.clear-filters-btn[_ngcontent-%COMP%] {\\n  height: 56px;\\n  margin-left: auto;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 60px 20px;\\n  text-align: center;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  color: #666;\\n  font-size: 1.1rem;\\n}\\n\\n.books-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\\n  gap: 24px;\\n  margin-top: 20px;\\n}\\n\\n.no-books-message[_ngcontent-%COMP%] {\\n  grid-column: 1 / -1;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 60px 20px;\\n  text-align: center;\\n  color: #666;\\n}\\n\\n.no-books-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  height: 64px;\\n  width: 64px;\\n  margin-bottom: 20px;\\n  opacity: 0.5;\\n}\\n\\n.no-books-message[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  font-size: 1.5rem;\\n}\\n\\n.no-books-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 24px 0;\\n  font-size: 1.1rem;\\n}\\n\\n.book-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\\n  border-radius: 12px;\\n  overflow: hidden;\\n}\\n\\n.book-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\\n}\\n\\n.book-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 200px;\\n  overflow: hidden;\\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\\n}\\n\\n.book-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n\\n.book-card[_ngcontent-%COMP%]:hover   .book-image[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n\\n.stock-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  background: rgba(76, 175, 80, 0.9);\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n}\\n\\n.stock-badge.low-stock[_ngcontent-%COMP%] {\\n  background: rgba(255, 152, 0, 0.9);\\n}\\n\\n.book-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 20px;\\n}\\n\\n.book-category[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n}\\n\\n.book-title[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #333;\\n  line-height: 1.3;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n}\\n\\n.book-author[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  color: #666;\\n  font-size: 0.95rem;\\n  font-style: italic;\\n}\\n\\n.book-description[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  color: #555;\\n  font-size: 0.9rem;\\n  line-height: 1.4;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 3;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n}\\n\\n.book-price[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n\\n.price[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  color: #1976d2;\\n}\\n\\n.book-actions[_ngcontent-%COMP%] {\\n  padding: 16px 20px;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  border-top: 1px solid #eee;\\n}\\n\\n.user-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 40px;\\n}\\n\\n.admin-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  margin-left: 12px;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .book-list-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  \\n  .header-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 16px;\\n  }\\n  \\n  .page-title[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  \\n  .filters-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n    gap: 16px;\\n  }\\n  \\n  .filter-field[_ngcontent-%COMP%] {\\n    min-width: unset;\\n  }\\n  \\n  .clear-filters-btn[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n  }\\n  \\n  .books-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\\n    gap: 16px;\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  .books-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .book-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 12px;\\n    align-items: stretch;\\n  }\\n  \\n  .admin-actions[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n    justify-content: center;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "category_r1", "categoryId", "ɵɵadvance", "ɵɵtextInterpolate1", "categoryName", "ɵɵelement", "ɵɵlistener", "BookListComponent_div_51_div_1_Template_button_click_7_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "clearFilters", "BookListComponent_div_51_mat_card_2_div_25_Template_button_click_1_listener", "_r6", "book_r5", "$implicit", "editBook", "BookListComponent_div_51_mat_card_2_div_25_Template_button_click_4_listener", "deleteBook", "BookListComponent_div_51_mat_card_2_Template_img_error_2_listener", "$event", "_r4", "onImageError", "BookListComponent_div_51_mat_card_2_Template_button_click_21_listener", "addToCart", "ɵɵtemplate", "BookListComponent_div_51_mat_card_2_div_25_Template", "imageUrl", "ɵɵsanitizeUrl", "title", "ɵɵclassProp", "stockQuantity", "ɵɵtextInterpolate", "author", "description", "ɵɵpipeBind2", "price", "isAdmin", "BookListComponent_div_51_div_1_Template", "BookListComponent_div_51_mat_card_2_Template", "filteredBooks", "length", "BookListComponent", "constructor", "booksService", "cartService", "authService", "route", "router", "snackBar", "books", "categories", "isLoading", "searchTerm", "selectedCategoryId", "sortBy", "sortDirection", "destroy$", "ngOnInit", "loadCategories", "loadBooks", "queryParams", "pipe", "subscribe", "params", "searchBooks", "filterByCategory", "ngOnDestroy", "next", "complete", "getAllBooks", "applySorting", "error", "console", "open", "duration", "panelClass", "getCategories", "trim", "getBooksByCategory", "onCategoryChange", "navigate", "category", "onSortChange", "sort", "a", "b", "aValue", "bValue", "toLowerCase", "book", "isAuthenticated", "returnUrl", "request", "bookId", "quantity", "confirm", "event", "target", "src", "ɵɵdirectiveInject", "i1", "BooksService", "i2", "CartService", "i3", "AuthService", "i4", "ActivatedRoute", "Router", "i5", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "BookListComponent_Template", "rf", "ctx", "BookListComponent_div_9_Template", "ɵɵtwoWayListener", "BookListComponent_Template_mat_select_valueChange_16_listener", "ɵɵtwoWayBindingSet", "BookListComponent_Template_mat_select_selectionChange_16_listener", "BookListComponent_mat_option_19_Template", "BookListComponent_Template_mat_select_valueChange_25_listener", "BookListComponent_Template_mat_select_selectionChange_25_listener", "BookListComponent_Template_mat_select_valueChange_39_listener", "BookListComponent_Template_mat_select_selectionChange_39_listener", "BookListComponent_Template_button_click_46_listener", "BookListComponent_div_50_Template", "BookListComponent_div_51_Template", "ɵɵtwoWayProperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\BookCart\\bookcart-frontend\\src\\app\\books\\book-list\\book-list.component.ts", "C:\\Users\\<USER>\\Desktop\\BookCart\\bookcart-frontend\\src\\app\\books\\book-list\\book-list.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';\nimport { BooksService, Book, Category } from '../../shared/services/books.service';\nimport { CartService } from '../../shared/services/cart.service';\nimport { AuthService } from '../../shared/services/auth.service';\n\n@Component({\n  selector: 'app-book-list',\n  templateUrl: './book-list.component.html',\n  styleUrls: ['./book-list.component.css']\n})\nexport class BookListComponent implements OnInit, OnDestroy {\n  books: Book[] = [];\n  categories: Category[] = [];\n  filteredBooks: Book[] = [];\n  isLoading = true;\n  searchTerm = '';\n  selectedCategoryId: number | null = null;\n  sortBy = 'title';\n  sortDirection = 'asc';\n  \n  private destroy$ = new Subject<void>();\n\n  constructor(\n    private booksService: BooksService,\n    private cartService: CartService,\n    private authService: AuthService,\n    private route: ActivatedRoute,\n    private router: Router,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnInit(): void {\n    this.loadCategories();\n    this.loadBooks();\n    \n    // Listen to query parameters for search\n    this.route.queryParams\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(params => {\n        if (params['search']) {\n          this.searchTerm = params['search'];\n          this.searchBooks();\n        } else if (params['category']) {\n          this.selectedCategoryId = +params['category'];\n          this.filterByCategory();\n        } else {\n          this.loadBooks();\n        }\n      });\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  loadBooks(): void {\n    this.isLoading = true;\n    this.booksService.getAllBooks().subscribe({\n      next: (books) => {\n        this.books = books;\n        this.filteredBooks = [...books];\n        this.applySorting();\n        this.isLoading = false;\n      },\n      error: (error) => {\n        console.error('Error loading books:', error);\n        this.snackBar.open('Error loading books. Please try again.', 'Close', {\n          duration: 3000,\n          panelClass: ['error-snackbar']\n        });\n        this.isLoading = false;\n      }\n    });\n  }\n\n  loadCategories(): void {\n    this.booksService.getCategories().subscribe({\n      next: (categories) => {\n        this.categories = categories;\n      },\n      error: (error) => {\n        console.error('Error loading categories:', error);\n      }\n    });\n  }\n\n  searchBooks(): void {\n    if (this.searchTerm.trim()) {\n      this.isLoading = true;\n      this.booksService.searchBooks(this.searchTerm.trim()).subscribe({\n        next: (books) => {\n          this.filteredBooks = books;\n          this.applySorting();\n          this.isLoading = false;\n        },\n        error: (error) => {\n          console.error('Error searching books:', error);\n          this.snackBar.open('Error searching books. Please try again.', 'Close', {\n            duration: 3000,\n            panelClass: ['error-snackbar']\n          });\n          this.isLoading = false;\n        }\n      });\n    } else {\n      this.filteredBooks = [...this.books];\n      this.applySorting();\n    }\n  }\n\n  filterByCategory(): void {\n    if (this.selectedCategoryId) {\n      this.isLoading = true;\n      this.booksService.getBooksByCategory(this.selectedCategoryId).subscribe({\n        next: (books) => {\n          this.filteredBooks = books;\n          this.applySorting();\n          this.isLoading = false;\n        },\n        error: (error) => {\n          console.error('Error filtering books by category:', error);\n          this.snackBar.open('Error filtering books. Please try again.', 'Close', {\n            duration: 3000,\n            panelClass: ['error-snackbar']\n          });\n          this.isLoading = false;\n        }\n      });\n    } else {\n      this.filteredBooks = [...this.books];\n      this.applySorting();\n    }\n  }\n\n  onCategoryChange(): void {\n    if (this.selectedCategoryId) {\n      this.router.navigate(['/books'], { \n        queryParams: { category: this.selectedCategoryId } \n      });\n    } else {\n      this.router.navigate(['/books']);\n    }\n  }\n\n  onSortChange(): void {\n    this.applySorting();\n  }\n\n  applySorting(): void {\n    this.filteredBooks.sort((a, b) => {\n      let aValue: any = a[this.sortBy as keyof Book];\n      let bValue: any = b[this.sortBy as keyof Book];\n      \n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n      \n      if (this.sortDirection === 'asc') {\n        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\n      } else {\n        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;\n      }\n    });\n  }\n\n  addToCart(book: Book): void {\n    if (!this.authService.isAuthenticated()) {\n      this.router.navigate(['/login'], { \n        queryParams: { returnUrl: '/books' } \n      });\n      return;\n    }\n\n    const request = {\n      bookId: book.bookId,\n      quantity: 1\n    };\n\n    this.cartService.addToCart(request).subscribe({\n      next: () => {\n        this.snackBar.open(`\"${book.title}\" added to cart!`, 'Close', {\n          duration: 3000,\n          panelClass: ['success-snackbar']\n        });\n      },\n      error: (error) => {\n        console.error('Error adding to cart:', error);\n        this.snackBar.open('Error adding to cart. Please try again.', 'Close', {\n          duration: 3000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n\n  editBook(book: Book): void {\n    this.router.navigate(['/admin/books/edit', book.bookId]);\n  }\n\n  deleteBook(book: Book): void {\n    if (confirm(`Are you sure you want to delete \"${book.title}\"?`)) {\n      this.booksService.deleteBook(book.bookId).subscribe({\n        next: () => {\n          this.snackBar.open(`\"${book.title}\" deleted successfully!`, 'Close', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n          this.loadBooks();\n        },\n        error: (error) => {\n          console.error('Error deleting book:', error);\n          this.snackBar.open('Error deleting book. Please try again.', 'Close', {\n            duration: 3000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    }\n  }\n\n  clearFilters(): void {\n    this.searchTerm = '';\n    this.selectedCategoryId = null;\n    this.router.navigate(['/books']);\n  }\n\n  isAuthenticated(): boolean {\n    return this.authService.isAuthenticated();\n  }\n\n  isAdmin(): boolean {\n    return this.authService.isAdmin();\n  }\n\n  onImageError(event: any): void {\n    event.target.src = 'assets/images/book-placeholder.jpg';\n  }\n}\n", "<div class=\"book-list-container\">\n  <!-- Header Section -->\n  <div class=\"header-section\">\n    <div class=\"page-title\">\n      <h1>\n        <mat-icon>library_books</mat-icon>\n        Discover Amazing Books\n      </h1>\n      <p>Find your next favorite read from our extensive collection</p>\n    </div>\n\n    <!-- Admin Actions -->\n    <div class=\"admin-actions\" *ngIf=\"isAdmin()\">\n      <button mat-raised-button color=\"primary\" routerLink=\"/admin/books/add\">\n        <mat-icon>add</mat-icon>\n        Add New Book\n      </button>\n    </div>\n  </div>\n\n  <!-- Filters Section -->\n  <div class=\"filters-section\">\n    <mat-card class=\"filters-card\">\n      <div class=\"filters-content\">\n        <!-- Category Filter -->\n        <mat-form-field appearance=\"outline\" class=\"filter-field\">\n          <mat-label>Category</mat-label>\n          <mat-select [(value)]=\"selectedCategoryId\" (selectionChange)=\"onCategoryChange()\">\n            <mat-option [value]=\"null\">All Categories</mat-option>\n            <mat-option *ngFor=\"let category of categories\" [value]=\"category.categoryId\">\n              {{ category.categoryName }}\n            </mat-option>\n          </mat-select>\n          <mat-icon matSuffix>category</mat-icon>\n        </mat-form-field>\n\n        <!-- Sort Options -->\n        <mat-form-field appearance=\"outline\" class=\"filter-field\">\n          <mat-label>Sort By</mat-label>\n          <mat-select [(value)]=\"sortBy\" (selectionChange)=\"onSortChange()\">\n            <mat-option value=\"title\">Title</mat-option>\n            <mat-option value=\"author\">Author</mat-option>\n            <mat-option value=\"price\">Price</mat-option>\n            <mat-option value=\"createdDate\">Date Added</mat-option>\n          </mat-select>\n          <mat-icon matSuffix>sort</mat-icon>\n        </mat-form-field>\n\n        <mat-form-field appearance=\"outline\" class=\"filter-field\">\n          <mat-label>Order</mat-label>\n          <mat-select [(value)]=\"sortDirection\" (selectionChange)=\"onSortChange()\">\n            <mat-option value=\"asc\">Ascending</mat-option>\n            <mat-option value=\"desc\">Descending</mat-option>\n          </mat-select>\n          <mat-icon matSuffix>swap_vert</mat-icon>\n        </mat-form-field>\n\n        <!-- Clear Filters -->\n        <button mat-stroked-button (click)=\"clearFilters()\" class=\"clear-filters-btn\">\n          <mat-icon>clear</mat-icon>\n          Clear Filters\n        </button>\n      </div>\n    </mat-card>\n  </div>\n\n  <!-- Loading Spinner -->\n  <div class=\"loading-container\" *ngIf=\"isLoading\">\n    <mat-spinner diameter=\"50\"></mat-spinner>\n    <p>Loading books...</p>\n  </div>\n\n  <!-- Books Grid -->\n  <div class=\"books-grid\" *ngIf=\"!isLoading\">\n    <!-- No Books Message -->\n    <div class=\"no-books-message\" *ngIf=\"filteredBooks.length === 0\">\n      <mat-icon>search_off</mat-icon>\n      <h3>No books found</h3>\n      <p>Try adjusting your search criteria or browse all books.</p>\n      <button mat-raised-button color=\"primary\" (click)=\"clearFilters()\">\n        Browse All Books\n      </button>\n    </div>\n\n    <!-- Book Cards -->\n    <mat-card class=\"book-card\" *ngFor=\"let book of filteredBooks\">\n      <!-- Book Image -->\n      <div class=\"book-image-container\">\n        <img [src]=\"book.imageUrl || 'assets/images/book-placeholder.jpg'\"\n             [alt]=\"book.title\"\n             class=\"book-image\"\n             (error)=\"onImageError($event)\">\n        \n        <!-- Stock Badge -->\n        <div class=\"stock-badge\" [class.low-stock]=\"book.stockQuantity <= 5\">\n          {{ book.stockQuantity }} in stock\n        </div>\n      </div>\n\n      <!-- Book Content -->\n      <mat-card-content class=\"book-content\">\n        <div class=\"book-category\">\n          <mat-chip color=\"primary\" selected>{{ book.categoryName }}</mat-chip>\n        </div>\n        \n        <h3 class=\"book-title\">{{ book.title }}</h3>\n        <p class=\"book-author\">by {{ book.author }}</p>\n        <p class=\"book-description\">{{ book.description }}</p>\n        \n        <div class=\"book-price\">\n          <span class=\"price\">${{ book.price | number:'1.2-2' }}</span>\n        </div>\n      </mat-card-content>\n\n      <!-- Book Actions -->\n      <mat-card-actions class=\"book-actions\">\n        <div class=\"user-actions\">\n          <button mat-raised-button \n                  color=\"primary\" \n                  (click)=\"addToCart(book)\"\n                  [disabled]=\"book.stockQuantity === 0\">\n            <mat-icon>add_shopping_cart</mat-icon>\n            {{ book.stockQuantity === 0 ? 'Out of Stock' : 'Add to Cart' }}\n          </button>\n        </div>\n\n        <!-- Admin Actions -->\n        <div class=\"admin-actions\" *ngIf=\"isAdmin()\">\n          <button mat-icon-button \n                  color=\"accent\" \n                  (click)=\"editBook(book)\"\n                  matTooltip=\"Edit Book\">\n            <mat-icon>edit</mat-icon>\n          </button>\n          <button mat-icon-button \n                  color=\"warn\" \n                  (click)=\"deleteBook(book)\"\n                  matTooltip=\"Delete Book\">\n            <mat-icon>delete</mat-icon>\n          </button>\n        </div>\n      </mat-card-actions>\n    </mat-card>\n  </div>\n</div>\n"], "mappings": "AAGA,SAASA,OAAO,EAAEC,SAAS,QAA4C,MAAM;;;;;;;;;;;;;;;;;;ICWrEC,EAFJ,CAAAC,cAAA,cAA6C,iBAC6B,eAC5D;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACxBH,EAAA,CAAAE,MAAA,qBACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;;IAYEH,EAAA,CAAAC,cAAA,oBAA8E;IAC5ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFmCH,EAAA,CAAAI,UAAA,UAAAC,WAAA,CAAAC,UAAA,CAA6B;IAC3EN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,WAAA,CAAAI,YAAA,MACF;;;;;IAoCVT,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAU,SAAA,sBAAyC;IACzCV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IACrBF,EADqB,CAAAG,YAAA,EAAI,EACnB;;;;;;IAMFH,EADF,CAAAC,cAAA,cAAiE,eACrD;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,8DAAuD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC9DH,EAAA,CAAAC,cAAA,iBAAmE;IAAzBD,EAAA,CAAAW,UAAA,mBAAAC,gEAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IAChElB,EAAA,CAAAE,MAAA,yBACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;;;IA8CAH,EADF,CAAAC,cAAA,cAA6C,iBAIZ;IADvBD,EAAA,CAAAW,UAAA,mBAAAQ,4EAAA;MAAAnB,EAAA,CAAAa,aAAA,CAAAO,GAAA;MAAA,MAAAC,OAAA,GAAArB,EAAA,CAAAgB,aAAA,GAAAM,SAAA;MAAA,MAAAP,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAQ,QAAA,CAAAF,OAAA,CAAc;IAAA,EAAC;IAE9BrB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAChBF,EADgB,CAAAG,YAAA,EAAW,EAClB;IACTH,EAAA,CAAAC,cAAA,iBAGiC;IADzBD,EAAA,CAAAW,UAAA,mBAAAa,4EAAA;MAAAxB,EAAA,CAAAa,aAAA,CAAAO,GAAA;MAAA,MAAAC,OAAA,GAAArB,EAAA,CAAAgB,aAAA,GAAAM,SAAA;MAAA,MAAAP,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAU,UAAA,CAAAJ,OAAA,CAAgB;IAAA,EAAC;IAEhCrB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAEpBF,EAFoB,CAAAG,YAAA,EAAW,EACpB,EACL;;;;;;IApDNH,EAHJ,CAAAC,cAAA,mBAA+D,cAE3B,cAII;IAA/BD,EAAA,CAAAW,UAAA,mBAAAe,kEAAAC,MAAA;MAAA3B,EAAA,CAAAa,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAc,YAAA,CAAAF,MAAA,CAAoB;IAAA,EAAC;IAHnC3B,EAAA,CAAAG,YAAA,EAGoC;IAGpCH,EAAA,CAAAC,cAAA,cAAqE;IACnED,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAKFH,EAFJ,CAAAC,cAAA,2BAAuC,cACV,mBACU;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAC5DF,EAD4D,CAAAG,YAAA,EAAW,EACjE;IAENH,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,IAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC/CH,EAAA,CAAAC,cAAA,aAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAGpDH,EADF,CAAAC,cAAA,eAAwB,gBACF;IAAAD,EAAA,CAAAE,MAAA,IAAkC;;IAE1DF,EAF0D,CAAAG,YAAA,EAAO,EACzD,EACW;IAKfH,EAFJ,CAAAC,cAAA,4BAAuC,eACX,kBAIsB;IADtCD,EAAA,CAAAW,UAAA,mBAAAmB,sEAAA;MAAA,MAAAT,OAAA,GAAArB,EAAA,CAAAa,aAAA,CAAAe,GAAA,EAAAN,SAAA;MAAA,MAAAP,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAgB,SAAA,CAAAV,OAAA,CAAe;IAAA,EAAC;IAE/BrB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtCH,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;IAGNH,EAAA,CAAAgC,UAAA,KAAAC,mDAAA,iBAA6C;IAejDjC,EADE,CAAAG,YAAA,EAAmB,EACV;;;;;IAtDFH,EAAA,CAAAO,SAAA,GAA6D;IAC7DP,EADA,CAAAI,UAAA,QAAAiB,OAAA,CAAAa,QAAA,0CAAAlC,EAAA,CAAAmC,aAAA,CAA6D,QAAAd,OAAA,CAAAe,KAAA,CAC3C;IAKEpC,EAAA,CAAAO,SAAA,EAA2C;IAA3CP,EAAA,CAAAqC,WAAA,cAAAhB,OAAA,CAAAiB,aAAA,MAA2C;IAClEtC,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAa,OAAA,CAAAiB,aAAA,eACF;IAMqCtC,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAuC,iBAAA,CAAAlB,OAAA,CAAAZ,YAAA,CAAuB;IAGrCT,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAuC,iBAAA,CAAAlB,OAAA,CAAAe,KAAA,CAAgB;IAChBpC,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAQ,kBAAA,QAAAa,OAAA,CAAAmB,MAAA,KAAoB;IACfxC,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAuC,iBAAA,CAAAlB,OAAA,CAAAoB,WAAA,CAAsB;IAG5BzC,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAA0C,WAAA,SAAArB,OAAA,CAAAsB,KAAA,eAAkC;IAU9C3C,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAI,UAAA,aAAAiB,OAAA,CAAAiB,aAAA,OAAqC;IAE3CtC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAa,OAAA,CAAAiB,aAAA,6CACF;IAI0BtC,EAAA,CAAAO,SAAA,EAAe;IAAfP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA6B,OAAA,GAAe;;;;;IAtDjD5C,EAAA,CAAAC,cAAA,cAA2C;IAYzCD,EAVA,CAAAgC,UAAA,IAAAa,uCAAA,kBAAiE,IAAAC,4CAAA,yBAUF;IA0DjE9C,EAAA,CAAAG,YAAA,EAAM;;;;IApE2BH,EAAA,CAAAO,SAAA,EAAgC;IAAhCP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAAgC,aAAA,CAAAC,MAAA,OAAgC;IAUlBhD,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAAgC,aAAA,CAAgB;;;ADxEjE,OAAM,MAAOE,iBAAiB;EAY5BC,YACUC,YAA0B,EAC1BC,WAAwB,EACxBC,WAAwB,EACxBC,KAAqB,EACrBC,MAAc,EACdC,QAAqB;IALrB,KAAAL,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IAjBlB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,UAAU,GAAe,EAAE;IAC3B,KAAAX,aAAa,GAAW,EAAE;IAC1B,KAAAY,SAAS,GAAG,IAAI;IAChB,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAC,kBAAkB,GAAkB,IAAI;IACxC,KAAAC,MAAM,GAAG,OAAO;IAChB,KAAAC,aAAa,GAAG,KAAK;IAEb,KAAAC,QAAQ,GAAG,IAAIlE,OAAO,EAAQ;EASnC;EAEHmE,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,SAAS,EAAE;IAEhB;IACA,IAAI,CAACb,KAAK,CAACc,WAAW,CACnBC,IAAI,CAACtE,SAAS,CAAC,IAAI,CAACiE,QAAQ,CAAC,CAAC,CAC9BM,SAAS,CAACC,MAAM,IAAG;MAClB,IAAIA,MAAM,CAAC,QAAQ,CAAC,EAAE;QACpB,IAAI,CAACX,UAAU,GAAGW,MAAM,CAAC,QAAQ,CAAC;QAClC,IAAI,CAACC,WAAW,EAAE;OACnB,MAAM,IAAID,MAAM,CAAC,UAAU,CAAC,EAAE;QAC7B,IAAI,CAACV,kBAAkB,GAAG,CAACU,MAAM,CAAC,UAAU,CAAC;QAC7C,IAAI,CAACE,gBAAgB,EAAE;OACxB,MAAM;QACL,IAAI,CAACN,SAAS,EAAE;;IAEpB,CAAC,CAAC;EACN;EAEAO,WAAWA,CAAA;IACT,IAAI,CAACV,QAAQ,CAACW,IAAI,EAAE;IACpB,IAAI,CAACX,QAAQ,CAACY,QAAQ,EAAE;EAC1B;EAEAT,SAASA,CAAA;IACP,IAAI,CAACR,SAAS,GAAG,IAAI;IACrB,IAAI,CAACR,YAAY,CAAC0B,WAAW,EAAE,CAACP,SAAS,CAAC;MACxCK,IAAI,EAAGlB,KAAK,IAAI;QACd,IAAI,CAACA,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACV,aAAa,GAAG,CAAC,GAAGU,KAAK,CAAC;QAC/B,IAAI,CAACqB,YAAY,EAAE;QACnB,IAAI,CAACnB,SAAS,GAAG,KAAK;MACxB,CAAC;MACDoB,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAACvB,QAAQ,CAACyB,IAAI,CAAC,wCAAwC,EAAE,OAAO,EAAE;UACpEC,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;QACF,IAAI,CAACxB,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEAO,cAAcA,CAAA;IACZ,IAAI,CAACf,YAAY,CAACiC,aAAa,EAAE,CAACd,SAAS,CAAC;MAC1CK,IAAI,EAAGjB,UAAU,IAAI;QACnB,IAAI,CAACA,UAAU,GAAGA,UAAU;MAC9B,CAAC;MACDqB,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;KACD,CAAC;EACJ;EAEAP,WAAWA,CAAA;IACT,IAAI,IAAI,CAACZ,UAAU,CAACyB,IAAI,EAAE,EAAE;MAC1B,IAAI,CAAC1B,SAAS,GAAG,IAAI;MACrB,IAAI,CAACR,YAAY,CAACqB,WAAW,CAAC,IAAI,CAACZ,UAAU,CAACyB,IAAI,EAAE,CAAC,CAACf,SAAS,CAAC;QAC9DK,IAAI,EAAGlB,KAAK,IAAI;UACd,IAAI,CAACV,aAAa,GAAGU,KAAK;UAC1B,IAAI,CAACqB,YAAY,EAAE;UACnB,IAAI,CAACnB,SAAS,GAAG,KAAK;QACxB,CAAC;QACDoB,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAC9C,IAAI,CAACvB,QAAQ,CAACyB,IAAI,CAAC,0CAA0C,EAAE,OAAO,EAAE;YACtEC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,gBAAgB;WAC9B,CAAC;UACF,IAAI,CAACxB,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAACZ,aAAa,GAAG,CAAC,GAAG,IAAI,CAACU,KAAK,CAAC;MACpC,IAAI,CAACqB,YAAY,EAAE;;EAEvB;EAEAL,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACZ,kBAAkB,EAAE;MAC3B,IAAI,CAACF,SAAS,GAAG,IAAI;MACrB,IAAI,CAACR,YAAY,CAACmC,kBAAkB,CAAC,IAAI,CAACzB,kBAAkB,CAAC,CAACS,SAAS,CAAC;QACtEK,IAAI,EAAGlB,KAAK,IAAI;UACd,IAAI,CAACV,aAAa,GAAGU,KAAK;UAC1B,IAAI,CAACqB,YAAY,EAAE;UACnB,IAAI,CAACnB,SAAS,GAAG,KAAK;QACxB,CAAC;QACDoB,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;UAC1D,IAAI,CAACvB,QAAQ,CAACyB,IAAI,CAAC,0CAA0C,EAAE,OAAO,EAAE;YACtEC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,gBAAgB;WAC9B,CAAC;UACF,IAAI,CAACxB,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAACZ,aAAa,GAAG,CAAC,GAAG,IAAI,CAACU,KAAK,CAAC;MACpC,IAAI,CAACqB,YAAY,EAAE;;EAEvB;EAEAS,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAC1B,kBAAkB,EAAE;MAC3B,IAAI,CAACN,MAAM,CAACiC,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;QAC/BpB,WAAW,EAAE;UAAEqB,QAAQ,EAAE,IAAI,CAAC5B;QAAkB;OACjD,CAAC;KACH,MAAM;MACL,IAAI,CAACN,MAAM,CAACiC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;;EAEpC;EAEAE,YAAYA,CAAA;IACV,IAAI,CAACZ,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV,IAAI,CAAC/B,aAAa,CAAC4C,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC/B,IAAIC,MAAM,GAAQF,CAAC,CAAC,IAAI,CAAC9B,MAAoB,CAAC;MAC9C,IAAIiC,MAAM,GAAQF,CAAC,CAAC,IAAI,CAAC/B,MAAoB,CAAC;MAE9C,IAAI,OAAOgC,MAAM,KAAK,QAAQ,EAAE;QAC9BA,MAAM,GAAGA,MAAM,CAACE,WAAW,EAAE;QAC7BD,MAAM,GAAGA,MAAM,CAACC,WAAW,EAAE;;MAG/B,IAAI,IAAI,CAACjC,aAAa,KAAK,KAAK,EAAE;QAChC,OAAO+B,MAAM,GAAGC,MAAM,GAAG,CAAC,CAAC,GAAGD,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC;OACtD,MAAM;QACL,OAAOD,MAAM,GAAGC,MAAM,GAAG,CAAC,CAAC,GAAGD,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC;;IAEzD,CAAC,CAAC;EACJ;EAEAhE,SAASA,CAACkE,IAAU;IAClB,IAAI,CAAC,IAAI,CAAC5C,WAAW,CAAC6C,eAAe,EAAE,EAAE;MACvC,IAAI,CAAC3C,MAAM,CAACiC,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;QAC/BpB,WAAW,EAAE;UAAE+B,SAAS,EAAE;QAAQ;OACnC,CAAC;MACF;;IAGF,MAAMC,OAAO,GAAG;MACdC,MAAM,EAAEJ,IAAI,CAACI,MAAM;MACnBC,QAAQ,EAAE;KACX;IAED,IAAI,CAAClD,WAAW,CAACrB,SAAS,CAACqE,OAAO,CAAC,CAAC9B,SAAS,CAAC;MAC5CK,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACnB,QAAQ,CAACyB,IAAI,CAAC,IAAIgB,IAAI,CAAC7D,KAAK,kBAAkB,EAAE,OAAO,EAAE;UAC5D8C,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,kBAAkB;SAChC,CAAC;MACJ,CAAC;MACDJ,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,IAAI,CAACvB,QAAQ,CAACyB,IAAI,CAAC,yCAAyC,EAAE,OAAO,EAAE;UACrEC,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;MACJ;KACD,CAAC;EACJ;EAEA5D,QAAQA,CAAC0E,IAAU;IACjB,IAAI,CAAC1C,MAAM,CAACiC,QAAQ,CAAC,CAAC,mBAAmB,EAAES,IAAI,CAACI,MAAM,CAAC,CAAC;EAC1D;EAEA5E,UAAUA,CAACwE,IAAU;IACnB,IAAIM,OAAO,CAAC,oCAAoCN,IAAI,CAAC7D,KAAK,IAAI,CAAC,EAAE;MAC/D,IAAI,CAACe,YAAY,CAAC1B,UAAU,CAACwE,IAAI,CAACI,MAAM,CAAC,CAAC/B,SAAS,CAAC;QAClDK,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACnB,QAAQ,CAACyB,IAAI,CAAC,IAAIgB,IAAI,CAAC7D,KAAK,yBAAyB,EAAE,OAAO,EAAE;YACnE8C,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,kBAAkB;WAChC,CAAC;UACF,IAAI,CAAChB,SAAS,EAAE;QAClB,CAAC;QACDY,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAI,CAACvB,QAAQ,CAACyB,IAAI,CAAC,wCAAwC,EAAE,OAAO,EAAE;YACpEC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,gBAAgB;WAC9B,CAAC;QACJ;OACD,CAAC;;EAEN;EAEAjE,YAAYA,CAAA;IACV,IAAI,CAAC0C,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACN,MAAM,CAACiC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAU,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC7C,WAAW,CAAC6C,eAAe,EAAE;EAC3C;EAEAtD,OAAOA,CAAA;IACL,OAAO,IAAI,CAACS,WAAW,CAACT,OAAO,EAAE;EACnC;EAEAf,YAAYA,CAAC2E,KAAU;IACrBA,KAAK,CAACC,MAAM,CAACC,GAAG,GAAG,oCAAoC;EACzD;;;uBApOWzD,iBAAiB,EAAAjD,EAAA,CAAA2G,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAA7G,EAAA,CAAA2G,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA/G,EAAA,CAAA2G,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAjH,EAAA,CAAA2G,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAnH,EAAA,CAAA2G,iBAAA,CAAAO,EAAA,CAAAE,MAAA,GAAApH,EAAA,CAAA2G,iBAAA,CAAAU,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAjBrE,iBAAiB;MAAAsE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCRtB7H,EALR,CAAAC,cAAA,aAAiC,aAEH,aACF,SAClB,eACQ;UAAAD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAClCH,EAAA,CAAAE,MAAA,+BACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,iEAA0D;UAC/DF,EAD+D,CAAAG,YAAA,EAAI,EAC7D;UAGNH,EAAA,CAAAgC,UAAA,IAAA+F,gCAAA,iBAA6C;UAM/C/H,EAAA,CAAAG,YAAA,EAAM;UAQEH,EALR,CAAAC,cAAA,cAA6B,mBACI,cACA,yBAE+B,iBAC7C;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAC,cAAA,qBAAkF;UAAtED,EAAA,CAAAgI,gBAAA,yBAAAC,8DAAAtG,MAAA;YAAA3B,EAAA,CAAAkI,kBAAA,CAAAJ,GAAA,CAAAjE,kBAAA,EAAAlC,MAAA,MAAAmG,GAAA,CAAAjE,kBAAA,GAAAlC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA8B;UAAC3B,EAAA,CAAAW,UAAA,6BAAAwH,kEAAA;YAAA,OAAmBL,GAAA,CAAAvC,gBAAA,EAAkB;UAAA,EAAC;UAC/EvF,EAAA,CAAAC,cAAA,qBAA2B;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAa;UACtDH,EAAA,CAAAgC,UAAA,KAAAoG,wCAAA,yBAA8E;UAGhFpI,EAAA,CAAAG,YAAA,EAAa;UACbH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAC9BF,EAD8B,CAAAG,YAAA,EAAW,EACxB;UAIfH,EADF,CAAAC,cAAA,yBAA0D,iBAC7C;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC9BH,EAAA,CAAAC,cAAA,qBAAkE;UAAtDD,EAAA,CAAAgI,gBAAA,yBAAAK,8DAAA1G,MAAA;YAAA3B,EAAA,CAAAkI,kBAAA,CAAAJ,GAAA,CAAAhE,MAAA,EAAAnC,MAAA,MAAAmG,GAAA,CAAAhE,MAAA,GAAAnC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAkB;UAAC3B,EAAA,CAAAW,UAAA,6BAAA2H,kEAAA;YAAA,OAAmBR,GAAA,CAAApC,YAAA,EAAc;UAAA,EAAC;UAC/D1F,EAAA,CAAAC,cAAA,sBAA0B;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC5CH,EAAA,CAAAC,cAAA,sBAA2B;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC9CH,EAAA,CAAAC,cAAA,sBAA0B;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC5CH,EAAA,CAAAC,cAAA,sBAAgC;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAC5CF,EAD4C,CAAAG,YAAA,EAAa,EAC5C;UACbH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAC1BF,EAD0B,CAAAG,YAAA,EAAW,EACpB;UAGfH,EADF,CAAAC,cAAA,yBAA0D,iBAC7C;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAAC,cAAA,qBAAyE;UAA7DD,EAAA,CAAAgI,gBAAA,yBAAAO,8DAAA5G,MAAA;YAAA3B,EAAA,CAAAkI,kBAAA,CAAAJ,GAAA,CAAA/D,aAAA,EAAApC,MAAA,MAAAmG,GAAA,CAAA/D,aAAA,GAAApC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAyB;UAAC3B,EAAA,CAAAW,UAAA,6BAAA6H,kEAAA;YAAA,OAAmBV,GAAA,CAAApC,YAAA,EAAc;UAAA,EAAC;UACtE1F,EAAA,CAAAC,cAAA,sBAAwB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC9CH,EAAA,CAAAC,cAAA,sBAAyB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UACrCF,EADqC,CAAAG,YAAA,EAAa,EACrC;UACbH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAC/BF,EAD+B,CAAAG,YAAA,EAAW,EACzB;UAGjBH,EAAA,CAAAC,cAAA,kBAA8E;UAAnDD,EAAA,CAAAW,UAAA,mBAAA8H,oDAAA;YAAA,OAASX,GAAA,CAAA5G,YAAA,EAAc;UAAA,EAAC;UACjDlB,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1BH,EAAA,CAAAE,MAAA,uBACF;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACG,EACP;UASNH,EANA,CAAAgC,UAAA,KAAA0G,iCAAA,kBAAiD,KAAAC,iCAAA,kBAMN;UAuE7C3I,EAAA,CAAAG,YAAA,EAAM;;;UApI0BH,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAI,UAAA,SAAA0H,GAAA,CAAAlF,OAAA,GAAe;UAezB5C,EAAA,CAAAO,SAAA,GAA8B;UAA9BP,EAAA,CAAA4I,gBAAA,UAAAd,GAAA,CAAAjE,kBAAA,CAA8B;UAC5B7D,EAAA,CAAAO,SAAA,EAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACOJ,EAAA,CAAAO,SAAA,GAAa;UAAbP,EAAA,CAAAI,UAAA,YAAA0H,GAAA,CAAApE,UAAA,CAAa;UAUpC1D,EAAA,CAAAO,SAAA,GAAkB;UAAlBP,EAAA,CAAA4I,gBAAA,UAAAd,GAAA,CAAAhE,MAAA,CAAkB;UAWlB9D,EAAA,CAAAO,SAAA,IAAyB;UAAzBP,EAAA,CAAA4I,gBAAA,UAAAd,GAAA,CAAA/D,aAAA,CAAyB;UAiBb/D,EAAA,CAAAO,SAAA,IAAe;UAAfP,EAAA,CAAAI,UAAA,SAAA0H,GAAA,CAAAnE,SAAA,CAAe;UAMtB3D,EAAA,CAAAO,SAAA,EAAgB;UAAhBP,EAAA,CAAAI,UAAA,UAAA0H,GAAA,CAAAnE,SAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}