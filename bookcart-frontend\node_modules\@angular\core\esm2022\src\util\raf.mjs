/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { global } from './global';
export function getNativeRequestAnimationFrame() {
    // Note: the `getNativeRequestAnimationFrame` is used in the `NgZone` class, but we cannot use the
    // `inject` function. The `NgZone` instance may be created manually, and thus the injection
    // context will be unavailable. This might be enough to check whether `requestAnimationFrame` is
    // available because otherwise, we'll fall back to `setTimeout`.
    const isBrowser = typeof global['requestAnimationFrame'] === 'function';
    // Note: `requestAnimationFrame` is unavailable when the code runs in the Node.js environment. We
    // use `setTimeout` because no changes are required other than checking if the current platform is
    // the browser. `setTimeout` is a well-established API that is available in both environments.
    // `requestAnimationFrame` is used in the browser to coalesce event tasks since event tasks are
    // usually executed within the same rendering frame (but this is more implementation details of
    // browsers).
    let nativeRequestAnimationFrame = global[isBrowser ? 'requestAnimationFrame' : 'setTimeout'];
    let nativeCancelAnimationFrame = global[isBrowser ? 'cancelAnimationFrame' : 'clearTimeout'];
    if (typeof Zone !== 'undefined' && nativeRequestAnimationFrame && nativeCancelAnimationFrame) {
        // Note: zone.js sets original implementations on patched APIs behind the
        // `__zone_symbol__OriginalDelegate` key (see `attachOriginToPatched`). Given the following
        // example: `window.requestAnimationFrame.__zone_symbol__OriginalDelegate`; this would return an
        // unpatched implementation of the `requestAnimationFrame`, which isn't intercepted by the
        // Angular zone. We use the unpatched implementation to avoid another change detection when
        // coalescing tasks.
        const unpatchedRequestAnimationFrame = nativeRequestAnimationFrame[Zone.__symbol__('OriginalDelegate')];
        if (unpatchedRequestAnimationFrame) {
            nativeRequestAnimationFrame = unpatchedRequestAnimationFrame;
        }
        const unpatchedCancelAnimationFrame = nativeCancelAnimationFrame[Zone.__symbol__('OriginalDelegate')];
        if (unpatchedCancelAnimationFrame) {
            nativeCancelAnimationFrame = unpatchedCancelAnimationFrame;
        }
    }
    return { nativeRequestAnimationFrame, nativeCancelAnimationFrame };
}
//# sourceMappingURL=data:application/json;base64,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