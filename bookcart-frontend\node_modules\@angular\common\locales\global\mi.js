/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['mi'] = ["mi",[["AM","PM"],u,u],u,[["T","H","T","A","P","M","H"],["Tap","Hin","Tū","<PERSON><PERSON>","Par","Mer","Hor"],["<PERSON>ā<PERSON>pu","<PERSON>āhina","<PERSON>ā<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>e","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>"],["Tap","<PERSON>n","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>r","Hor"]],u,[["K","H","P","P","H","P","H","H","M","N","R","<PERSON>"],["<PERSON>hi","<PERSON>","<PERSON>u","<PERSON>e","<PERSON>","<PERSON>pi","<PERSON><PERSON>ngo","<PERSON>","<PERSON>hu","<PERSON>uku","<PERSON>ngi","<PERSON>ki"],["<PERSON>hit<PERSON>tea","<PERSON>tanguru","<PERSON>ut<PERSON><PERSON>ngi","<PERSON>engawhāwhā","<PERSON>tua","<PERSON>piri","<PERSON><PERSON>ngongoi","<PERSON>turikōkā","Mahuru","Whiringa-ā-nuku","Whiringa-ā-rangi","Hakihea"]],u,[["BCE","CE"],u,u],1,[6,0],["dd-MM-y","d MMM y","d MMMM y","EEEE, d MMMM y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1} {0}",u,u,u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤ #,##0.00","#E0"],"NZD","$","Tāra o Aotearoa",{"NZD":["$"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    