/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
const TAG_TO_PLACEHOLDER_NAMES = {
    'A': 'LINK',
    'B': 'BOLD_TEXT',
    'BR': 'LINE_BREAK',
    'EM': 'EMPHASISED_TEXT',
    'H1': 'HEADING_LEVEL1',
    'H2': 'HEADING_LEVEL2',
    'H3': 'HEADING_LEVEL3',
    'H4': 'HEADING_LEVEL4',
    'H5': 'HEADING_LEVEL5',
    'H6': 'HEADING_LEVEL6',
    'HR': 'HORIZONTAL_RULE',
    'I': 'ITALIC_TEXT',
    'LI': 'LIST_ITEM',
    'LINK': 'MEDIA_LINK',
    'OL': 'ORDERED_LIST',
    'P': 'PARAGRAPH',
    'Q': 'QUOTATION',
    'S': 'STRIKETHROUGH_TEXT',
    'SMALL': 'SMALL_TEXT',
    'SUB': 'SUBSTRIPT',
    'SUP': 'SUPERSCRIPT',
    'TBODY': 'TABLE_BODY',
    'TD': 'TABLE_CELL',
    'TFOOT': 'TABLE_FOOTER',
    'TH': 'TABLE_HEADER_CELL',
    'THEAD': 'TABLE_HEADER',
    'TR': 'TABLE_ROW',
    'TT': 'MONOSPACED_TEXT',
    'U': 'UNDERLINED_TEXT',
    'UL': 'UNORDERED_LIST',
};
/**
 * Creates unique names for placeholder with different content.
 *
 * Returns the same placeholder name when the content is identical.
 */
export class PlaceholderRegistry {
    constructor() {
        // Count the occurrence of the base name top generate a unique name
        this._placeHolderNameCounts = {};
        // Maps signature to placeholder names
        this._signatureToName = {};
    }
    getStartTagPlaceholderName(tag, attrs, isVoid) {
        const signature = this._hashTag(tag, attrs, isVoid);
        if (this._signatureToName[signature]) {
            return this._signatureToName[signature];
        }
        const upperTag = tag.toUpperCase();
        const baseName = TAG_TO_PLACEHOLDER_NAMES[upperTag] || `TAG_${upperTag}`;
        const name = this._generateUniqueName(isVoid ? baseName : `START_${baseName}`);
        this._signatureToName[signature] = name;
        return name;
    }
    getCloseTagPlaceholderName(tag) {
        const signature = this._hashClosingTag(tag);
        if (this._signatureToName[signature]) {
            return this._signatureToName[signature];
        }
        const upperTag = tag.toUpperCase();
        const baseName = TAG_TO_PLACEHOLDER_NAMES[upperTag] || `TAG_${upperTag}`;
        const name = this._generateUniqueName(`CLOSE_${baseName}`);
        this._signatureToName[signature] = name;
        return name;
    }
    getPlaceholderName(name, content) {
        const upperName = name.toUpperCase();
        const signature = `PH: ${upperName}=${content}`;
        if (this._signatureToName[signature]) {
            return this._signatureToName[signature];
        }
        const uniqueName = this._generateUniqueName(upperName);
        this._signatureToName[signature] = uniqueName;
        return uniqueName;
    }
    getUniquePlaceholder(name) {
        return this._generateUniqueName(name.toUpperCase());
    }
    getStartBlockPlaceholderName(name, parameters) {
        const signature = this._hashBlock(name, parameters);
        if (this._signatureToName[signature]) {
            return this._signatureToName[signature];
        }
        const placeholder = this._generateUniqueName(`START_BLOCK_${this._toSnakeCase(name)}`);
        this._signatureToName[signature] = placeholder;
        return placeholder;
    }
    getCloseBlockPlaceholderName(name) {
        const signature = this._hashClosingBlock(name);
        if (this._signatureToName[signature]) {
            return this._signatureToName[signature];
        }
        const placeholder = this._generateUniqueName(`CLOSE_BLOCK_${this._toSnakeCase(name)}`);
        this._signatureToName[signature] = placeholder;
        return placeholder;
    }
    // Generate a hash for a tag - does not take attribute order into account
    _hashTag(tag, attrs, isVoid) {
        const start = `<${tag}`;
        const strAttrs = Object.keys(attrs).sort().map((name) => ` ${name}=${attrs[name]}`).join('');
        const end = isVoid ? '/>' : `></${tag}>`;
        return start + strAttrs + end;
    }
    _hashClosingTag(tag) {
        return this._hashTag(`/${tag}`, {}, false);
    }
    _hashBlock(name, parameters) {
        const params = parameters.length === 0 ? '' : ` (${parameters.sort().join('; ')})`;
        return `@${name}${params} {}`;
    }
    _hashClosingBlock(name) {
        return this._hashBlock(`close_${name}`, []);
    }
    _toSnakeCase(name) {
        return name.toUpperCase().replace(/[^A-Z0-9]/g, '_');
    }
    _generateUniqueName(base) {
        const seen = this._placeHolderNameCounts.hasOwnProperty(base);
        if (!seen) {
            this._placeHolderNameCounts[base] = 1;
            return base;
        }
        const id = this._placeHolderNameCounts[base];
        this._placeHolderNameCounts[base] = id + 1;
        return `${base}_${id}`;
    }
}
//# sourceMappingURL=data:application/json;base64,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