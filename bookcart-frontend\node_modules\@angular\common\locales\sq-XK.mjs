/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["sq-XK", [["p.d.", "m.d."], u, ["e paradites", "e pasdites"]], [["p.d.", "m.d."], u, ["paradite", "pasdite"]], [["d", "h", "m", "m", "e", "p", "sh"], ["Die", "Hën", "Mar", "Mër", "Enj", "Pre", "Sht"], ["e diel", "e hënë", "e martë", "e mërkurë", "e enjte", "e premte", "e shtunë"], ["die", "hën", "mar", "mër", "enj", "pre", "sht"]], [["d", "h", "m", "m", "e", "p", "sh"], ["die", "hën", "mar", "mër", "enj", "pre", "sht"], ["e diel", "e hënë", "e martë", "e mërkurë", "e enjte", "e premte", "e shtunë"], ["die", "hën", "mar", "mër", "enj", "pre", "sht"]], [["j", "sh", "m", "p", "m", "q", "k", "g", "sh", "t", "n", "dh"], ["jan", "shk", "mar", "pri", "maj", "qer", "korr", "gush", "sht", "tet", "nën", "dhj"], ["janar", "shkurt", "mars", "prill", "maj", "qershor", "korrik", "gusht", "shtator", "tetor", "nëntor", "dhjetor"]], u, [["p.K.", "mb.K."], u, ["para Krishtit", "mbas Krishtit"]], 1, [6, 0], ["d.M.yy", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1}, {0}", u, "{1} 'në' {0}", u], [",", " ", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "#,##0.00 ¤", "#E0"], "EUR", "€", "Euroja", { "AFN": [], "ALL": ["Lekë"], "AMD": [], "AOA": [], "ARS": [], "AUD": ["A$", "AUD"], "AZN": [], "BAM": [], "BBD": [], "BDT": [], "BMD": [], "BND": [], "BOB": [], "BRL": [], "BSD": [], "BWP": [], "BZD": [], "CAD": ["CA$", "CAD"], "CLP": [], "CNY": ["CN¥", "CNY"], "COP": [], "CRC": [], "CUC": [], "CUP": [], "CZK": [], "DKK": [], "DOP": [], "EGP": [], "FJD": [], "FKP": [], "GBP": ["£", "GBP"], "GEL": [], "GIP": [], "GNF": [], "GTQ": [], "GYD": [], "HKD": ["HK$", "HKS"], "HNL": [], "HRK": [], "HUF": [], "IDR": [], "ILS": ["₪", "ILS"], "INR": ["₹", "INR"], "ISK": [], "JMD": [], "JPY": ["JP¥", "JPY"], "KHR": [], "KMF": [], "KPW": [], "KRW": ["₩", "KRW"], "KYD": [], "KZT": [], "LAK": [], "LBP": [], "LKR": [], "LRD": [], "MGA": [], "MMK": [], "MNT": [], "MUR": [], "MXN": ["MX$", "MXN"], "MYR": [], "NAD": [], "NGN": [], "NIO": [], "NOK": [], "NPR": [], "NZD": ["NZ$", "NZD"], "PHP": [], "PKR": [], "PLN": [], "PYG": [], "RON": [], "RUB": [], "RWF": [], "SBD": [], "SEK": [], "SGD": [], "SHP": [], "SRD": [], "SSP": [], "STN": [], "SYP": [], "THB": ["฿", "THB"], "TOP": [], "TRY": [], "TTD": [], "TWD": ["NT$", "TWD"], "UAH": [], "USD": ["US$", "USD"], "UYU": [], "VND": ["₫", "VND"], "XCD": ["EC$", "XCD"], "ZAR": [], "ZMW": [] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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