/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["lu", [["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], u, u], u, [["L", "N", "N", "N", "N", "N", "L"], ["Lum", "Nko", "Ndy", "Ndg", "Njw", "Ngv", "Lub"], ["<PERSON><PERSON><PERSON>", "<PERSON>ko<PERSON>a", "Nd<PERSON><PERSON><PERSON>", "Ndang<PERSON>", "Nj<PERSON><PERSON>", "<PERSON><PERSON><PERSON>a", "<PERSON>bing<PERSON>"], ["Lum", "Nko", "Ndy", "Ndg", "Njw", "Ngv", "Lub"]], u, [["C", "L", "L", "M", "L", "L", "K", "L", "L", "L", "<PERSON>", "<PERSON>"], ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Lu<PERSON>", "Lu<PERSON>", "<PERSON><PERSON>", "<PERSON>sh", "<PERSON>t", "<PERSON>n", "Kas", "<PERSON>is"], ["<PERSON>iongo", "Lùishi", "Lusòlo", "<PERSON>ùuyà", "Lumùngùlù", "Lufuimi", "Kabàlàsh<PERSON>pù", "Lùsh<PERSON>k<PERSON>", "<PERSON>golo", "Lungùdi", "Kaswèk<PERSON>è", "Ciswà"]], u, [["kmp. Y.K.", "kny. Y. K."], u, ["Kumpala kwa Yezu Kli", "Kunyima kwa Yezu Kli"]], 1, [6, 0], ["d/M/y", "d MMM y", "d MMMM y", "EEEE d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "#,##0.00¤", "#E0"], "CDF", "FC", "Nfalanga wa Kongu", { "CDF": ["FC"], "JPY": ["JP¥", "¥"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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