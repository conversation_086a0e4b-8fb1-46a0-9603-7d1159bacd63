{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../../src/material/sidenav/testing/drawer-harness.ts", "../../../../../../../src/material/sidenav/testing/drawer-content-harness.ts", "../../../../../../../src/material/sidenav/testing/drawer-container-harness.ts", "../../../../../../../src/material/sidenav/testing/sidenav-content-harness.ts", "../../../../../../../src/material/sidenav/testing/sidenav-harness.ts", "../../../../../../../src/material/sidenav/testing/sidenav-container-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ContentContainerComponentHarness, HarnessPredicate} from '@angular/cdk/testing';\nimport {DrawerHarnessFilters} from './drawer-harness-filters';\n\n/**\n * Base class for the drawer harness functionality.\n * @docs-private\n */\nexport class MatDrawerHarnessBase extends ContentContainerComponentHarness<string> {\n  /** Whether the drawer is open. */\n  async isOpen(): Promise<boolean> {\n    return (await this.host()).hasClass('mat-drawer-opened');\n  }\n\n  /** Gets the position of the drawer inside its container. */\n  async getPosition(): Promise<'start' | 'end'> {\n    const host = await this.host();\n    return (await host.hasClass('mat-drawer-end')) ? 'end' : 'start';\n  }\n\n  /** Gets the mode that the drawer is in. */\n  async getMode(): Promise<'over' | 'push' | 'side'> {\n    const host = await this.host();\n\n    if (await host.hasClass('mat-drawer-push')) {\n      return 'push';\n    }\n\n    if (await host.hasClass('mat-drawer-side')) {\n      return 'side';\n    }\n\n    return 'over';\n  }\n}\n\n/** Harness for interacting with a standard mat-drawer in tests. */\nexport class MatDrawerHarness extends MatDrawerHarnessBase {\n  /** The selector for the host element of a `MatDrawer` instance. */\n  static hostSelector = '.mat-drawer';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatDrawerHarness` that meets\n   * certain criteria.\n   * @param options Options for filtering which drawer instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: DrawerHarnessFilters = {}): HarnessPredicate<MatDrawerHarness> {\n    return new HarnessPredicate(MatDrawerHarness, options).addOption(\n      'position',\n      options.position,\n      async (harness, position) => (await harness.getPosition()) === position,\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ContentContainerComponentHarness, HarnessPredicate} from '@angular/cdk/testing';\nimport {DrawerContentHarnessFilters} from './drawer-harness-filters';\n\n/** Harness for interacting with a standard mat-drawer-content in tests. */\nexport class MatDrawerContentHarness extends ContentContainerComponentHarness<string> {\n  /** The selector for the host element of a `MatDrawerContent` instance. */\n  static hostSelector = '.mat-drawer-content';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatDrawerContentHarness` that\n   * meets certain criteria.\n   * @param options Options for filtering which drawer content instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(\n    options: DrawerContentHarnessFilters = {},\n  ): HarnessPredicate<MatDrawerContentHarness> {\n    return new HarnessPredicate(MatDrawerContentHarness, options);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ContentContainerComponentHarness, HarnessPredicate} from '@angular/cdk/testing';\nimport {DrawerContainerHarnessFilters, DrawerHarnessFilters} from './drawer-harness-filters';\nimport {MatDrawerContentHarness} from './drawer-content-harness';\nimport {MatDrawerHarness} from './drawer-harness';\n\n/** Harness for interacting with a standard mat-drawer-container in tests. */\nexport class MatDrawerContainerHarness extends ContentContainerComponentHarness<string> {\n  /** The selector for the host element of a `MatDrawerContainer` instance. */\n  static hostSelector = '.mat-drawer-container';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatDrawerContainerHarness` that\n   * meets certain criteria.\n   * @param options Options for filtering which container instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(\n    options: DrawerContainerHarnessFilters = {},\n  ): HarnessPredicate<MatDrawerContainerHarness> {\n    return new HarnessPredicate(MatDrawerContainerHarness, options);\n  }\n\n  /**\n   * Gets drawers that match particular criteria within the container.\n   * @param filter Optionally filters which chips are included.\n   */\n  async getDrawers(filter: DrawerHarnessFilters = {}): Promise<MatDrawerHarness[]> {\n    return this.locatorForAll(MatDrawerHarness.with(filter))();\n  }\n\n  /** Gets the element that has the container's content. */\n  async getContent(): Promise<MatDrawerContentHarness> {\n    return this.locatorFor(MatDrawerContentHarness)();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ContentContainerComponentHarness, HarnessPredicate} from '@angular/cdk/testing';\nimport {DrawerContentHarnessFilters} from './drawer-harness-filters';\n\n/** Harness for interacting with a standard mat-sidenav-content in tests. */\nexport class MatSidenavContentHarness extends ContentContainerComponentHarness<string> {\n  /** The selector for the host element of a `MatSidenavContent` instance. */\n  static hostSelector = '.mat-sidenav-content';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatSidenavContentHarness` that\n   * meets certain criteria.\n   * @param options Options for filtering which sidenav content instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(\n    options: DrawerContentHarnessFilters = {},\n  ): HarnessPredicate<MatSidenavContentHarness> {\n    return new HarnessPredicate(MatSidenavContentHarness, options);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {HarnessPredicate} from '@angular/cdk/testing';\nimport {MatDrawerHarnessBase} from './drawer-harness';\nimport {DrawerHarnessFilters} from './drawer-harness-filters';\n\n/** <PERSON><PERSON><PERSON> for interacting with a standard mat-sidenav in tests. */\nexport class MatSidenavHarness extends MatDrawerHarnessBase {\n  /** The selector for the host element of a `MatSidenav` instance. */\n  static hostSelector = '.mat-sidenav';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatSidenavHarness` that meets\n   * certain criteria.\n   * @param options Options for filtering which sidenav instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: DrawerHarnessFilters = {}): HarnessPredicate<MatSidenavHarness> {\n    return new HarnessPredicate(MatSidenavHarness, options).addOption(\n      'position',\n      options.position,\n      async (harness, position) => (await harness.getPosition()) === position,\n    );\n  }\n\n  /** Whether the sidenav is fixed in the viewport. */\n  async isFixedInViewport(): Promise<boolean> {\n    return (await this.host()).hasClass('mat-sidenav-fixed');\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ContentContainerComponentHarness, HarnessPredicate} from '@angular/cdk/testing';\nimport {DrawerContainerHarnessFilters, DrawerHarnessFilters} from './drawer-harness-filters';\nimport {MatSidenavContentHarness} from './sidenav-content-harness';\nimport {MatSidenavHarness} from './sidenav-harness';\n\n/** Harness for interacting with a standard mat-sidenav-container in tests. */\nexport class MatSidenavContainerHarness extends ContentContainerComponentHarness<string> {\n  /** The selector for the host element of a `MatSidenavContainer` instance. */\n  static hostSelector = '.mat-sidenav-container';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatSidenavContainerHarness` that\n   * meets certain criteria.\n   * @param options Options for filtering which container instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(\n    options: DrawerContainerHarnessFilters = {},\n  ): HarnessPredicate<MatSidenavContainerHarness> {\n    return new HarnessPredicate(MatSidenavContainerHarness, options);\n  }\n\n  /**\n   * Gets sidenavs that match particular criteria within the container.\n   * @param filter Optionally filters which chips are included.\n   */\n  async getSidenavs(filter: DrawerHarnessFilters = {}): Promise<MatSidenavHarness[]> {\n    return this.locatorForAll(MatSidenavHarness.with(filter))();\n  }\n\n  /** Gets the element that has the container's content. */\n  async getContent(): Promise<MatSidenavContentHarness> {\n    return this.locatorFor(MatSidenavContentHarness)();\n  }\n}\n"], "names": [], "mappings": ";;AAWA;;;AAGG;AACG,MAAO,oBAAqB,SAAQ,gCAAwC,CAAA;;AAEhF,IAAA,MAAM,MAAM,GAAA;AACV,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,mBAAmB,CAAC,CAAC;KAC1D;;AAGD,IAAA,MAAM,WAAW,GAAA;AACf,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;AAC/B,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,KAAK,GAAG,OAAO,CAAC;KAClE;;AAGD,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAE/B,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;AAC1C,YAAA,OAAO,MAAM,CAAC;SACf;QAED,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;AAC1C,YAAA,OAAO,MAAM,CAAC;SACf;AAED,QAAA,OAAO,MAAM,CAAC;KACf;AACF,CAAA;AAED;AACM,MAAO,gBAAiB,SAAQ,oBAAoB,CAAA;;aAEjD,IAAY,CAAA,YAAA,GAAG,aAAa,CAAC,EAAA;AAEpC;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAAgC,EAAE,EAAA;AAC5C,QAAA,OAAO,IAAI,gBAAgB,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC,SAAS,CAC9D,UAAU,EACV,OAAO,CAAC,QAAQ,EAChB,OAAO,OAAO,EAAE,QAAQ,KAAK,CAAC,MAAM,OAAO,CAAC,WAAW,EAAE,MAAM,QAAQ,CACxE,CAAC;KACH;;;ACjDH;AACM,MAAO,uBAAwB,SAAQ,gCAAwC,CAAA;;aAE5E,IAAY,CAAA,YAAA,GAAG,qBAAqB,CAAC,EAAA;AAE5C;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CACT,OAAA,GAAuC,EAAE,EAAA;AAEzC,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,EAAE,OAAO,CAAC,CAAC;KAC/D;;;ACbH;AACM,MAAO,yBAA0B,SAAQ,gCAAwC,CAAA;;aAE9E,IAAY,CAAA,YAAA,GAAG,uBAAuB,CAAC,EAAA;AAE9C;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CACT,OAAA,GAAyC,EAAE,EAAA;AAE3C,QAAA,OAAO,IAAI,gBAAgB,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAAC;KACjE;AAED;;;AAGG;AACH,IAAA,MAAM,UAAU,CAAC,MAAA,GAA+B,EAAE,EAAA;AAChD,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;KAC5D;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,uBAAuB,CAAC,EAAE,CAAC;KACnD;;;AC9BH;AACM,MAAO,wBAAyB,SAAQ,gCAAwC,CAAA;;aAE7E,IAAY,CAAA,YAAA,GAAG,sBAAsB,CAAC,EAAA;AAE7C;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CACT,OAAA,GAAuC,EAAE,EAAA;AAEzC,QAAA,OAAO,IAAI,gBAAgB,CAAC,wBAAwB,EAAE,OAAO,CAAC,CAAC;KAChE;;;ACdH;AACM,MAAO,iBAAkB,SAAQ,oBAAoB,CAAA;;aAElD,IAAY,CAAA,YAAA,GAAG,cAAc,CAAC,EAAA;AAErC;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAAgC,EAAE,EAAA;AAC5C,QAAA,OAAO,IAAI,gBAAgB,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC,SAAS,CAC/D,UAAU,EACV,OAAO,CAAC,QAAQ,EAChB,OAAO,OAAO,EAAE,QAAQ,KAAK,CAAC,MAAM,OAAO,CAAC,WAAW,EAAE,MAAM,QAAQ,CACxE,CAAC;KACH;;AAGD,IAAA,MAAM,iBAAiB,GAAA;AACrB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,mBAAmB,CAAC,CAAC;KAC1D;;;ACrBH;AACM,MAAO,0BAA2B,SAAQ,gCAAwC,CAAA;;aAE/E,IAAY,CAAA,YAAA,GAAG,wBAAwB,CAAC,EAAA;AAE/C;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CACT,OAAA,GAAyC,EAAE,EAAA;AAE3C,QAAA,OAAO,IAAI,gBAAgB,CAAC,0BAA0B,EAAE,OAAO,CAAC,CAAC;KAClE;AAED;;;AAGG;AACH,IAAA,MAAM,WAAW,CAAC,MAAA,GAA+B,EAAE,EAAA;AACjD,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;KAC7D;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,wBAAwB,CAAC,EAAE,CAAC;KACpD;;;;;"}