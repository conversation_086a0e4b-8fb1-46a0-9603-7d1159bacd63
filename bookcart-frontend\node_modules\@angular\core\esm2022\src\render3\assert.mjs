/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { RuntimeError } from '../errors';
import { assertDefined, assertEqual, assertNumber, throwError } from '../util/assert';
import { getComponentDef, getNgModuleDef } from './definition';
import { isLContainer, isLView } from './interfaces/type_checks';
import { DECLARATION_COMPONENT_VIEW, HEADER_OFFSET, T_HOST, TVIEW } from './interfaces/view';
// [Assert functions do not constraint type when they are guarded by a truthy
// expression.](https://github.com/microsoft/TypeScript/issues/37295)
export function assertTNodeForLView(tNode, lView) {
    assertTNodeForTView(tNode, lView[TVIEW]);
}
export function assertTNodeForTView(tNode, tView) {
    assertTNode(tNode);
    const tData = tView.data;
    for (let i = HEADER_OFFSET; i < tData.length; i++) {
        if (tData[i] === tNode) {
            return;
        }
    }
    throwError('This TNode does not belong to this TView.');
}
export function assertTNode(tNode) {
    assertDefined(tNode, 'TNode must be defined');
    if (!(tNode && typeof tNode === 'object' && tNode.hasOwnProperty('directiveStylingLast'))) {
        throwError('Not of type TNode, got: ' + tNode);
    }
}
export function assertTIcu(tIcu) {
    assertDefined(tIcu, 'Expected TIcu to be defined');
    if (!(typeof tIcu.currentCaseLViewIndex === 'number')) {
        throwError('Object is not of TIcu type.');
    }
}
export function assertComponentType(actual, msg = 'Type passed in is not ComponentType, it does not have \'ɵcmp\' property.') {
    if (!getComponentDef(actual)) {
        throwError(msg);
    }
}
export function assertNgModuleType(actual, msg = 'Type passed in is not NgModuleType, it does not have \'ɵmod\' property.') {
    if (!getNgModuleDef(actual)) {
        throwError(msg);
    }
}
export function assertCurrentTNodeIsParent(isParent) {
    assertEqual(isParent, true, 'currentTNode should be a parent');
}
export function assertHasParent(tNode) {
    assertDefined(tNode, 'currentTNode should exist!');
    assertDefined(tNode.parent, 'currentTNode should have a parent');
}
export function assertLContainer(value) {
    assertDefined(value, 'LContainer must be defined');
    assertEqual(isLContainer(value), true, 'Expecting LContainer');
}
export function assertLViewOrUndefined(value) {
    value && assertEqual(isLView(value), true, 'Expecting LView or undefined or null');
}
export function assertLView(value) {
    assertDefined(value, 'LView must be defined');
    assertEqual(isLView(value), true, 'Expecting LView');
}
export function assertFirstCreatePass(tView, errMessage) {
    assertEqual(tView.firstCreatePass, true, errMessage || 'Should only be called in first create pass.');
}
export function assertFirstUpdatePass(tView, errMessage) {
    assertEqual(tView.firstUpdatePass, true, errMessage || 'Should only be called in first update pass.');
}
/**
 * This is a basic sanity check that an object is probably a directive def. DirectiveDef is
 * an interface, so we can't do a direct instanceof check.
 */
export function assertDirectiveDef(obj) {
    if (obj.type === undefined || obj.selectors == undefined || obj.inputs === undefined) {
        throwError(`Expected a DirectiveDef/ComponentDef and this object does not seem to have the expected shape.`);
    }
}
export function assertIndexInDeclRange(tView, index) {
    assertBetween(HEADER_OFFSET, tView.bindingStartIndex, index);
}
export function assertIndexInExpandoRange(lView, index) {
    const tView = lView[1];
    assertBetween(tView.expandoStartIndex, lView.length, index);
}
export function assertBetween(lower, upper, index) {
    if (!(lower <= index && index < upper)) {
        throwError(`Index out of range (expecting ${lower} <= ${index} < ${upper})`);
    }
}
export function assertProjectionSlots(lView, errMessage) {
    assertDefined(lView[DECLARATION_COMPONENT_VIEW], 'Component views should exist.');
    assertDefined(lView[DECLARATION_COMPONENT_VIEW][T_HOST].projection, errMessage ||
        'Components with projection nodes (<ng-content>) must have projection slots defined.');
}
export function assertParentView(lView, errMessage) {
    assertDefined(lView, errMessage || 'Component views should always have a parent view (component\'s host view)');
}
export function assertNoDuplicateDirectives(directives) {
    // The array needs at least two elements in order to have duplicates.
    if (directives.length < 2) {
        return;
    }
    const seenDirectives = new Set();
    for (const current of directives) {
        if (seenDirectives.has(current)) {
            throw new RuntimeError(309 /* RuntimeErrorCode.DUPLICATE_DIRECTIVE */, `Directive ${current.type.name} matches multiple times on the same element. ` +
                `Directives can only match an element once.`);
        }
        seenDirectives.add(current);
    }
}
/**
 * This is a basic sanity check that the `injectorIndex` seems to point to what looks like a
 * NodeInjector data structure.
 *
 * @param lView `LView` which should be checked.
 * @param injectorIndex index into the `LView` where the `NodeInjector` is expected.
 */
export function assertNodeInjector(lView, injectorIndex) {
    assertIndexInExpandoRange(lView, injectorIndex);
    assertIndexInExpandoRange(lView, injectorIndex + 8 /* NodeInjectorOffset.PARENT */);
    assertNumber(lView[injectorIndex + 0], 'injectorIndex should point to a bloom filter');
    assertNumber(lView[injectorIndex + 1], 'injectorIndex should point to a bloom filter');
    assertNumber(lView[injectorIndex + 2], 'injectorIndex should point to a bloom filter');
    assertNumber(lView[injectorIndex + 3], 'injectorIndex should point to a bloom filter');
    assertNumber(lView[injectorIndex + 4], 'injectorIndex should point to a bloom filter');
    assertNumber(lView[injectorIndex + 5], 'injectorIndex should point to a bloom filter');
    assertNumber(lView[injectorIndex + 6], 'injectorIndex should point to a bloom filter');
    assertNumber(lView[injectorIndex + 7], 'injectorIndex should point to a bloom filter');
    assertNumber(lView[injectorIndex + 8 /* NodeInjectorOffset.PARENT */], 'injectorIndex should point to parent injector');
}
//# sourceMappingURL=data:application/json;base64,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