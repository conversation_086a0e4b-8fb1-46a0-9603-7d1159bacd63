{"name": "@angular/core", "version": "17.3.12", "description": "Angular - the core framework", "author": "angular", "license": "MIT", "engines": {"node": "^18.13.0 || >=20.9.0"}, "exports": {"./schematics/*": {"default": "./schematics/*.js"}, "./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "esm2022": "./esm2022/core.mjs", "esm": "./esm2022/core.mjs", "default": "./fesm2022/core.mjs"}, "./primitives/signals": {"types": "./primitives/signals/index.d.ts", "esm2022": "./esm2022/primitives/signals/index.mjs", "esm": "./esm2022/primitives/signals/index.mjs", "default": "./fesm2022/primitives/signals.mjs"}, "./rxjs-interop": {"types": "./rxjs-interop/index.d.ts", "esm2022": "./esm2022/rxjs-interop/rxjs-interop.mjs", "esm": "./esm2022/rxjs-interop/rxjs-interop.mjs", "default": "./fesm2022/rxjs-interop.mjs"}, "./testing": {"types": "./testing/index.d.ts", "esm2022": "./esm2022/testing/testing.mjs", "esm": "./esm2022/testing/testing.mjs", "default": "./fesm2022/testing.mjs"}}, "dependencies": {"tslib": "^2.3.0"}, "peerDependencies": {"rxjs": "^6.5.3 || ^7.4.0", "zone.js": "~0.14.0"}, "repository": {"type": "git", "url": "https://github.com/angular/angular.git", "directory": "packages/core"}, "ng-update": {"migrations": "./schematics/migrations.json", "packageGroup": ["@angular/core", "@angular/bazel", "@angular/common", "@angular/compiler", "@angular/compiler-cli", "@angular/animations", "@angular/elements", "@angular/platform-browser", "@angular/platform-browser-dynamic", "@angular/forms", "@angular/platform-server", "@angular/upgrade", "@angular/router", "@angular/language-service", "@angular/localize", "@angular/service-worker"]}, "schematics": "./schematics/collection.json", "sideEffects": false, "module": "./fesm2022/core.mjs", "typings": "./index.d.ts", "type": "module"}