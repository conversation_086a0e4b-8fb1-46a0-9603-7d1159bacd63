"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileSystem = void 0;
/**
 * Abstraction of the file system that migrations can use to record and apply
 * changes. This is necessary to support virtual file systems as used in the CLI devkit.
 */
class FileSystem {
}
exports.FileSystem = FileSystem;
//# sourceMappingURL=data:application/json;base64,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