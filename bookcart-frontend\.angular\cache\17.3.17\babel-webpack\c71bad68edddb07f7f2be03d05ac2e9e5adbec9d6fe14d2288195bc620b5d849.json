{"ast": null, "code": "import { RouterModule } from '@angular/router';\n// Components\nimport { LoginComponent } from './auth/login/login.component';\nimport { RegisterComponent } from './auth/register/register.component';\nimport { BookListComponent } from './books/book-list/book-list.component';\nimport { BookAddComponent } from './books/book-add/book-add.component';\nimport { CartComponent } from './cart/cart.component';\nimport { OrderListComponent } from './orders/order-list/order-list.component';\nimport { OrderDetailComponent } from './orders/order-detail/order-detail.component';\nimport { UserManagementComponent } from './admin/user-management/user-management.component';\nimport { UnauthorizedComponent } from './shared/components/unauthorized/unauthorized.component';\n// Guards\nimport { AuthGuard } from './shared/guards/auth.guard';\nimport { RoleGuard } from './shared/guards/role.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: '/books',\n  pathMatch: 'full'\n},\n// Public Routes\n{\n  path: 'login',\n  component: LoginComponent\n}, {\n  path: 'register',\n  component: RegisterComponent\n}, {\n  path: 'books',\n  component: BookListComponent\n}, {\n  path: 'unauthorized',\n  component: UnauthorizedComponent\n},\n// Protected Routes (User)\n{\n  path: 'cart',\n  component: CartComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'orders',\n  component: OrderListComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'orders/:id',\n  component: OrderDetailComponent,\n  canActivate: [AuthGuard]\n},\n// Admin Routes\n{\n  path: 'admin/books/add',\n  component: BookAddComponent,\n  canActivate: [AuthGuard, RoleGuard],\n  data: {\n    expectedRole: 'Admin'\n  }\n}, {\n  path: 'admin/books/edit/:id',\n  component: BookAddComponent,\n  canActivate: [AuthGuard, RoleGuard],\n  data: {\n    expectedRole: 'Admin'\n  }\n}, {\n  path: 'admin/users',\n  component: UserManagementComponent,\n  canActivate: [AuthGuard, RoleGuard],\n  data: {\n    expectedRole: 'Admin'\n  }\n},\n// Wildcard route\n{\n  path: '**',\n  redirectTo: '/books'\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "LoginComponent", "RegisterComponent", "BookListComponent", "BookAddComponent", "CartComponent", "OrderListComponent", "OrderDetailComponent", "UserManagementComponent", "UnauthorizedComponent", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "routes", "path", "redirectTo", "pathMatch", "component", "canActivate", "data", "expectedRole", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\BookCart\\bookcart-frontend\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\n\n// Components\nimport { LoginComponent } from './auth/login/login.component';\nimport { RegisterComponent } from './auth/register/register.component';\nimport { BookListComponent } from './books/book-list/book-list.component';\nimport { BookAddComponent } from './books/book-add/book-add.component';\nimport { CartComponent } from './cart/cart.component';\nimport { OrderListComponent } from './orders/order-list/order-list.component';\nimport { OrderDetailComponent } from './orders/order-detail/order-detail.component';\nimport { UserManagementComponent } from './admin/user-management/user-management.component';\nimport { UnauthorizedComponent } from './shared/components/unauthorized/unauthorized.component';\n\n// Guards\nimport { AuthGuard } from './shared/guards/auth.guard';\nimport { RoleGuard } from './shared/guards/role.guard';\n\nconst routes: Routes = [\n  { path: '', redirectTo: '/books', pathMatch: 'full' },\n  \n  // Public Routes\n  { path: 'login', component: LoginComponent },\n  { path: 'register', component: RegisterComponent },\n  { path: 'books', component: BookListComponent },\n  { path: 'unauthorized', component: UnauthorizedComponent },\n  \n  // Protected Routes (User)\n  { \n    path: 'cart', \n    component: CartComponent, \n    canActivate: [AuthGuard] \n  },\n  { \n    path: 'orders', \n    component: OrderListComponent, \n    canActivate: [AuthGuard] \n  },\n  { \n    path: 'orders/:id', \n    component: OrderDetailComponent, \n    canActivate: [AuthGuard] \n  },\n  \n  // Admin Routes\n  { \n    path: 'admin/books/add', \n    component: BookAddComponent, \n    canActivate: [AuthGuard, RoleGuard],\n    data: { expectedRole: 'Admin' }\n  },\n  { \n    path: 'admin/books/edit/:id', \n    component: BookAddComponent, \n    canActivate: [AuthGuard, RoleGuard],\n    data: { expectedRole: 'Admin' }\n  },\n  { \n    path: 'admin/users', \n    component: UserManagementComponent, \n    canActivate: [AuthGuard, RoleGuard],\n    data: { expectedRole: 'Admin' }\n  },\n  \n  // Wildcard route\n  { path: '**', redirectTo: '/books' }\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AAEtD;AACA,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,iBAAiB,QAAQ,oCAAoC;AACtE,SAASC,iBAAiB,QAAQ,uCAAuC;AACzE,SAASC,gBAAgB,QAAQ,qCAAqC;AACtE,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,kBAAkB,QAAQ,0CAA0C;AAC7E,SAASC,oBAAoB,QAAQ,8CAA8C;AACnF,SAASC,uBAAuB,QAAQ,mDAAmD;AAC3F,SAASC,qBAAqB,QAAQ,yDAAyD;AAE/F;AACA,SAASC,SAAS,QAAQ,4BAA4B;AACtD,SAASC,SAAS,QAAQ,4BAA4B;;;AAEtD,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,UAAU,EAAE,QAAQ;EAAEC,SAAS,EAAE;AAAM,CAAE;AAErD;AACA;EAAEF,IAAI,EAAE,OAAO;EAAEG,SAAS,EAAEf;AAAc,CAAE,EAC5C;EAAEY,IAAI,EAAE,UAAU;EAAEG,SAAS,EAAEd;AAAiB,CAAE,EAClD;EAAEW,IAAI,EAAE,OAAO;EAAEG,SAAS,EAAEb;AAAiB,CAAE,EAC/C;EAAEU,IAAI,EAAE,cAAc;EAAEG,SAAS,EAAEP;AAAqB,CAAE;AAE1D;AACA;EACEI,IAAI,EAAE,MAAM;EACZG,SAAS,EAAEX,aAAa;EACxBY,WAAW,EAAE,CAACP,SAAS;CACxB,EACD;EACEG,IAAI,EAAE,QAAQ;EACdG,SAAS,EAAEV,kBAAkB;EAC7BW,WAAW,EAAE,CAACP,SAAS;CACxB,EACD;EACEG,IAAI,EAAE,YAAY;EAClBG,SAAS,EAAET,oBAAoB;EAC/BU,WAAW,EAAE,CAACP,SAAS;CACxB;AAED;AACA;EACEG,IAAI,EAAE,iBAAiB;EACvBG,SAAS,EAAEZ,gBAAgB;EAC3Ba,WAAW,EAAE,CAACP,SAAS,EAAEC,SAAS,CAAC;EACnCO,IAAI,EAAE;IAAEC,YAAY,EAAE;EAAO;CAC9B,EACD;EACEN,IAAI,EAAE,sBAAsB;EAC5BG,SAAS,EAAEZ,gBAAgB;EAC3Ba,WAAW,EAAE,CAACP,SAAS,EAAEC,SAAS,CAAC;EACnCO,IAAI,EAAE;IAAEC,YAAY,EAAE;EAAO;CAC9B,EACD;EACEN,IAAI,EAAE,aAAa;EACnBG,SAAS,EAAER,uBAAuB;EAClCS,WAAW,EAAE,CAACP,SAAS,EAAEC,SAAS,CAAC;EACnCO,IAAI,EAAE;IAAEC,YAAY,EAAE;EAAO;CAC9B;AAED;AACA;EAAEN,IAAI,EAAE,IAAI;EAAEC,UAAU,EAAE;AAAQ,CAAE,CACrC;AAMD,OAAM,MAAOM,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBpB,YAAY,CAACqB,OAAO,CAACT,MAAM,CAAC,EAC5BZ,YAAY;IAAA;EAAA;;;2EAEXoB,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAAvB,YAAA;IAAAwB,OAAA,GAFjBxB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}