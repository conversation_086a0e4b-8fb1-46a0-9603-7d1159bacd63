{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../shared/services/books.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/form-field\";\nimport * as i9 from \"@angular/material/input\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/select\";\nimport * as i12 from \"@angular/material/core\";\nfunction BookAddComponent_mat_error_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Title is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookAddComponent_mat_error_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Author is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookAddComponent_mat_error_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Valid price is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookAddComponent_mat_error_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Stock quantity is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookAddComponent_mat_option_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", category_r1.categoryId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", category_r1.categoryName, \" \");\n  }\n}\nfunction BookAddComponent_mat_error_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Category is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookAddComponent_mat_icon_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 21);\n    i0.ɵɵtext(1, \"refresh\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookAddComponent_mat_icon_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.isEditMode ? \"update\" : \"add\");\n  }\n}\nexport class BookAddComponent {\n  constructor(formBuilder, booksService, router, route, snackBar) {\n    this.formBuilder = formBuilder;\n    this.booksService = booksService;\n    this.router = router;\n    this.route = route;\n    this.snackBar = snackBar;\n    this.categories = [];\n    this.isLoading = false;\n    this.isEditMode = false;\n    this.bookId = null;\n    this.bookForm = this.formBuilder.group({\n      title: ['', Validators.required],\n      author: ['', Validators.required],\n      description: [''],\n      price: ['', [Validators.required, Validators.min(0)]],\n      stockQuantity: ['', [Validators.required, Validators.min(0)]],\n      categoryId: ['', Validators.required],\n      imageUrl: ['']\n    });\n  }\n  ngOnInit() {\n    this.loadCategories();\n    // Check if editing\n    this.bookId = +this.route.snapshot.params['id'];\n    if (this.bookId) {\n      this.isEditMode = true;\n      this.loadBook();\n    }\n  }\n  loadCategories() {\n    this.booksService.getCategories().subscribe({\n      next: categories => {\n        this.categories = categories;\n      },\n      error: error => {\n        console.error('Error loading categories:', error);\n      }\n    });\n  }\n  loadBook() {\n    if (this.bookId) {\n      this.booksService.getBookById(this.bookId).subscribe({\n        next: book => {\n          this.bookForm.patchValue(book);\n        },\n        error: error => {\n          console.error('Error loading book:', error);\n          this.snackBar.open('Error loading book details.', 'Close', {\n            duration: 3000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    }\n  }\n  onSubmit() {\n    if (this.bookForm.valid && !this.isLoading) {\n      this.isLoading = true;\n      const bookData = this.bookForm.value;\n      const operation = this.isEditMode ? this.booksService.updateBook(this.bookId, bookData) : this.booksService.createBook(bookData);\n      operation.subscribe({\n        next: () => {\n          this.isLoading = false;\n          this.snackBar.open(`Book ${this.isEditMode ? 'updated' : 'created'} successfully!`, 'Close', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n          this.router.navigate(['/books']);\n        },\n        error: error => {\n          this.isLoading = false;\n          console.error('Error saving book:', error);\n          this.snackBar.open('Error saving book. Please try again.', 'Close', {\n            duration: 3000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    }\n  }\n  goBack() {\n    this.router.navigate(['/books']);\n  }\n  static {\n    this.ɵfac = function BookAddComponent_Factory(t) {\n      return new (t || BookAddComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.BooksService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BookAddComponent,\n      selectors: [[\"app-book-add\"]],\n      decls: 53,\n      vars: 12,\n      consts: [[1, \"book-add-container\"], [1, \"book-form-card\"], [1, \"book-form\", 3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"title\", \"placeholder\", \"Enter book title\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"author\", \"placeholder\", \"Enter author name\"], [\"matInput\", \"\", \"formControlName\", \"description\", \"rows\", \"3\", \"placeholder\", \"Enter book description\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"half-width\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"price\", \"placeholder\", \"0.00\"], [\"matPrefix\", \"\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"stockQuantity\", \"placeholder\", \"0\"], [\"formControlName\", \"categoryId\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"matInput\", \"\", \"formControlName\", \"imageUrl\", \"placeholder\", \"Enter image URL\"], [1, \"form-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [\"class\", \"spinning\", 4, \"ngIf\"], [\"mat-button\", \"\", \"type\", \"button\", 3, \"click\"], [3, \"value\"], [1, \"spinning\"]],\n      template: function BookAddComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\");\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"mat-card-content\")(6, \"form\", 2);\n          i0.ɵɵlistener(\"ngSubmit\", function BookAddComponent_Template_form_ngSubmit_6_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(7, \"mat-form-field\", 3)(8, \"mat-label\");\n          i0.ɵɵtext(9, \"Title\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(10, \"input\", 4);\n          i0.ɵɵtemplate(11, BookAddComponent_mat_error_11_Template, 2, 0, \"mat-error\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"mat-form-field\", 3)(13, \"mat-label\");\n          i0.ɵɵtext(14, \"Author\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(15, \"input\", 6);\n          i0.ɵɵtemplate(16, BookAddComponent_mat_error_16_Template, 2, 0, \"mat-error\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"mat-form-field\", 3)(18, \"mat-label\");\n          i0.ɵɵtext(19, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(20, \"textarea\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 8)(22, \"mat-form-field\", 9)(23, \"mat-label\");\n          i0.ɵɵtext(24, \"Price\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(25, \"input\", 10);\n          i0.ɵɵelementStart(26, \"span\", 11);\n          i0.ɵɵtext(27, \"$\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(28, BookAddComponent_mat_error_28_Template, 2, 0, \"mat-error\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"mat-form-field\", 9)(30, \"mat-label\");\n          i0.ɵɵtext(31, \"Stock Quantity\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(32, \"input\", 12);\n          i0.ɵɵtemplate(33, BookAddComponent_mat_error_33_Template, 2, 0, \"mat-error\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"mat-form-field\", 3)(35, \"mat-label\");\n          i0.ɵɵtext(36, \"Category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"mat-select\", 13);\n          i0.ɵɵtemplate(38, BookAddComponent_mat_option_38_Template, 2, 2, \"mat-option\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(39, BookAddComponent_mat_error_39_Template, 2, 0, \"mat-error\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"mat-form-field\", 3)(41, \"mat-label\");\n          i0.ɵɵtext(42, \"Image URL (Optional)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(43, \"input\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"div\", 16)(45, \"button\", 17);\n          i0.ɵɵtemplate(46, BookAddComponent_mat_icon_46_Template, 2, 0, \"mat-icon\", 18)(47, BookAddComponent_mat_icon_47_Template, 2, 1, \"mat-icon\", 5);\n          i0.ɵɵtext(48);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"button\", 19);\n          i0.ɵɵlistener(\"click\", function BookAddComponent_Template_button_click_49_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵelementStart(50, \"mat-icon\");\n          i0.ɵɵtext(51, \"arrow_back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(52, \" Cancel \");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          let tmp_2_0;\n          let tmp_3_0;\n          let tmp_4_0;\n          let tmp_5_0;\n          let tmp_7_0;\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.isEditMode ? \"Edit Book\" : \"Add New Book\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.bookForm);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.bookForm.get(\"title\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.bookForm.get(\"title\")) == null ? null : tmp_2_0.touched));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.bookForm.get(\"author\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.bookForm.get(\"author\")) == null ? null : tmp_3_0.touched));\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.bookForm.get(\"price\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.bookForm.get(\"price\")) == null ? null : tmp_4_0.touched));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.bookForm.get(\"stockQuantity\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.bookForm.get(\"stockQuantity\")) == null ? null : tmp_5_0.touched));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx.bookForm.get(\"categoryId\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx.bookForm.get(\"categoryId\")) == null ? null : tmp_7_0.touched));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.bookForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.isLoading ? \"Saving...\" : ctx.isEditMode ? \"Update Book\" : \"Add Book\", \" \");\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.MatButton, i7.MatCard, i7.MatCardContent, i7.MatCardHeader, i7.MatCardTitle, i8.MatFormField, i8.MatLabel, i8.MatError, i8.MatPrefix, i9.MatInput, i10.MatIcon, i11.MatSelect, i12.MatOption],\n      styles: [\".book-add-container[_ngcontent-%COMP%] {\\n      max-width: 600px;\\n      margin: 20px auto;\\n      padding: 20px;\\n    }\\n    .book-form-card[_ngcontent-%COMP%] {\\n      padding: 20px;\\n    }\\n    .book-form[_ngcontent-%COMP%] {\\n      display: flex;\\n      flex-direction: column;\\n      gap: 16px;\\n    }\\n    .form-row[_ngcontent-%COMP%] {\\n      display: flex;\\n      gap: 16px;\\n    }\\n    .full-width[_ngcontent-%COMP%] {\\n      width: 100%;\\n    }\\n    .half-width[_ngcontent-%COMP%] {\\n      flex: 1;\\n    }\\n    .form-actions[_ngcontent-%COMP%] {\\n      display: flex;\\n      gap: 12px;\\n      margin-top: 20px;\\n    }\\n    .spinning[_ngcontent-%COMP%] {\\n      animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n    }\\n    @keyframes _ngcontent-%COMP%_spin {\\n      0% { transform: rotate(0deg); }\\n      100% { transform: rotate(360deg); }\\n    }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYm9va3MvYm9vay1hZGQvYm9vay1hZGQuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7SUFDSTtNQUNFLGdCQUFnQjtNQUNoQixpQkFBaUI7TUFDakIsYUFBYTtJQUNmO0lBQ0E7TUFDRSxhQUFhO0lBQ2Y7SUFDQTtNQUNFLGFBQWE7TUFDYixzQkFBc0I7TUFDdEIsU0FBUztJQUNYO0lBQ0E7TUFDRSxhQUFhO01BQ2IsU0FBUztJQUNYO0lBQ0E7TUFDRSxXQUFXO0lBQ2I7SUFDQTtNQUNFLE9BQU87SUFDVDtJQUNBO01BQ0UsYUFBYTtNQUNiLFNBQVM7TUFDVCxnQkFBZ0I7SUFDbEI7SUFDQTtNQUNFLGtDQUFrQztJQUNwQztJQUNBO01BQ0UsS0FBSyx1QkFBdUIsRUFBRTtNQUM5QixPQUFPLHlCQUF5QixFQUFFO0lBQ3BDIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLmJvb2stYWRkLWNvbnRhaW5lciB7XG4gICAgICBtYXgtd2lkdGg6IDYwMHB4O1xuICAgICAgbWFyZ2luOiAyMHB4IGF1dG87XG4gICAgICBwYWRkaW5nOiAyMHB4O1xuICAgIH1cbiAgICAuYm9vay1mb3JtLWNhcmQge1xuICAgICAgcGFkZGluZzogMjBweDtcbiAgICB9XG4gICAgLmJvb2stZm9ybSB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgIGdhcDogMTZweDtcbiAgICB9XG4gICAgLmZvcm0tcm93IHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBnYXA6IDE2cHg7XG4gICAgfVxuICAgIC5mdWxsLXdpZHRoIHtcbiAgICAgIHdpZHRoOiAxMDAlO1xuICAgIH1cbiAgICAuaGFsZi13aWR0aCB7XG4gICAgICBmbGV4OiAxO1xuICAgIH1cbiAgICAuZm9ybS1hY3Rpb25zIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBnYXA6IDEycHg7XG4gICAgICBtYXJnaW4tdG9wOiAyMHB4O1xuICAgIH1cbiAgICAuc3Bpbm5pbmcge1xuICAgICAgYW5pbWF0aW9uOiBzcGluIDFzIGxpbmVhciBpbmZpbml0ZTtcbiAgICB9XG4gICAgQGtleWZyYW1lcyBzcGluIHtcbiAgICAgIDAlIHsgdHJhbnNmb3JtOiByb3RhdGUoMGRlZyk7IH1cbiAgICAgIDEwMCUgeyB0cmFuc2Zvcm06IHJvdGF0ZSgzNjBkZWcpOyB9XG4gICAgfVxuICAiXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "category_r1", "categoryId", "ɵɵadvance", "ɵɵtextInterpolate1", "categoryName", "ɵɵtextInterpolate", "ctx_r1", "isEditMode", "BookAddComponent", "constructor", "formBuilder", "booksService", "router", "route", "snackBar", "categories", "isLoading", "bookId", "bookForm", "group", "title", "required", "author", "description", "price", "min", "stockQuantity", "imageUrl", "ngOnInit", "loadCategories", "snapshot", "params", "loadBook", "getCategories", "subscribe", "next", "error", "console", "getBookById", "book", "patchValue", "open", "duration", "panelClass", "onSubmit", "valid", "bookData", "value", "operation", "updateBook", "createBook", "navigate", "goBack", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "BooksService", "i3", "Router", "ActivatedRoute", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "BookAddComponent_Template", "rf", "ctx", "ɵɵlistener", "BookAddComponent_Template_form_ngSubmit_6_listener", "ɵɵelement", "ɵɵtemplate", "BookAddComponent_mat_error_11_Template", "BookAddComponent_mat_error_16_Template", "BookAddComponent_mat_error_28_Template", "BookAddComponent_mat_error_33_Template", "BookAddComponent_mat_option_38_Template", "BookAddComponent_mat_error_39_Template", "BookAddComponent_mat_icon_46_Template", "BookAddComponent_mat_icon_47_Template", "BookAddComponent_Template_button_click_49_listener", "tmp_2_0", "get", "invalid", "touched", "tmp_3_0", "tmp_4_0", "tmp_5_0", "tmp_7_0"], "sources": ["C:\\Users\\<USER>\\Desktop\\BookCart\\bookcart-frontend\\src\\app\\books\\book-add\\book-add.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { BooksService, Category } from '../../shared/services/books.service';\n\n@Component({\n  selector: 'app-book-add',\n  template: `\n    <div class=\"book-add-container\">\n      <mat-card class=\"book-form-card\">\n        <mat-card-header>\n          <mat-card-title>{{ isEditMode ? 'Edit Book' : 'Add New Book' }}</mat-card-title>\n        </mat-card-header>\n        \n        <mat-card-content>\n          <form [formGroup]=\"bookForm\" (ngSubmit)=\"onSubmit()\" class=\"book-form\">\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Title</mat-label>\n              <input matInput formControlName=\"title\" placeholder=\"Enter book title\">\n              <mat-error *ngIf=\"bookForm.get('title')?.invalid && bookForm.get('title')?.touched\">\n                Title is required\n              </mat-error>\n            </mat-form-field>\n\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Author</mat-label>\n              <input matInput formControlName=\"author\" placeholder=\"Enter author name\">\n              <mat-error *ngIf=\"bookForm.get('author')?.invalid && bookForm.get('author')?.touched\">\n                Author is required\n              </mat-error>\n            </mat-form-field>\n\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Description</mat-label>\n              <textarea matInput formControlName=\"description\" rows=\"3\" placeholder=\"Enter book description\"></textarea>\n            </mat-form-field>\n\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"half-width\">\n                <mat-label>Price</mat-label>\n                <input matInput type=\"number\" formControlName=\"price\" placeholder=\"0.00\">\n                <span matPrefix>$</span>\n                <mat-error *ngIf=\"bookForm.get('price')?.invalid && bookForm.get('price')?.touched\">\n                  Valid price is required\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"half-width\">\n                <mat-label>Stock Quantity</mat-label>\n                <input matInput type=\"number\" formControlName=\"stockQuantity\" placeholder=\"0\">\n                <mat-error *ngIf=\"bookForm.get('stockQuantity')?.invalid && bookForm.get('stockQuantity')?.touched\">\n                  Stock quantity is required\n                </mat-error>\n              </mat-form-field>\n            </div>\n\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Category</mat-label>\n              <mat-select formControlName=\"categoryId\">\n                <mat-option *ngFor=\"let category of categories\" [value]=\"category.categoryId\">\n                  {{ category.categoryName }}\n                </mat-option>\n              </mat-select>\n              <mat-error *ngIf=\"bookForm.get('categoryId')?.invalid && bookForm.get('categoryId')?.touched\">\n                Category is required\n              </mat-error>\n            </mat-form-field>\n\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Image URL (Optional)</mat-label>\n              <input matInput formControlName=\"imageUrl\" placeholder=\"Enter image URL\">\n            </mat-form-field>\n\n            <div class=\"form-actions\">\n              <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"bookForm.invalid || isLoading\">\n                <mat-icon *ngIf=\"isLoading\" class=\"spinning\">refresh</mat-icon>\n                <mat-icon *ngIf=\"!isLoading\">{{ isEditMode ? 'update' : 'add' }}</mat-icon>\n                {{ isLoading ? 'Saving...' : (isEditMode ? 'Update Book' : 'Add Book') }}\n              </button>\n              \n              <button mat-button type=\"button\" (click)=\"goBack()\">\n                <mat-icon>arrow_back</mat-icon>\n                Cancel\n              </button>\n            </div>\n          </form>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  `,\n  styles: [`\n    .book-add-container {\n      max-width: 600px;\n      margin: 20px auto;\n      padding: 20px;\n    }\n    .book-form-card {\n      padding: 20px;\n    }\n    .book-form {\n      display: flex;\n      flex-direction: column;\n      gap: 16px;\n    }\n    .form-row {\n      display: flex;\n      gap: 16px;\n    }\n    .full-width {\n      width: 100%;\n    }\n    .half-width {\n      flex: 1;\n    }\n    .form-actions {\n      display: flex;\n      gap: 12px;\n      margin-top: 20px;\n    }\n    .spinning {\n      animation: spin 1s linear infinite;\n    }\n    @keyframes spin {\n      0% { transform: rotate(0deg); }\n      100% { transform: rotate(360deg); }\n    }\n  `]\n})\nexport class BookAddComponent implements OnInit {\n  bookForm: FormGroup;\n  categories: Category[] = [];\n  isLoading = false;\n  isEditMode = false;\n  bookId: number | null = null;\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private booksService: BooksService,\n    private router: Router,\n    private route: ActivatedRoute,\n    private snackBar: MatSnackBar\n  ) {\n    this.bookForm = this.formBuilder.group({\n      title: ['', Validators.required],\n      author: ['', Validators.required],\n      description: [''],\n      price: ['', [Validators.required, Validators.min(0)]],\n      stockQuantity: ['', [Validators.required, Validators.min(0)]],\n      categoryId: ['', Validators.required],\n      imageUrl: ['']\n    });\n  }\n\n  ngOnInit(): void {\n    this.loadCategories();\n    \n    // Check if editing\n    this.bookId = +this.route.snapshot.params['id'];\n    if (this.bookId) {\n      this.isEditMode = true;\n      this.loadBook();\n    }\n  }\n\n  loadCategories(): void {\n    this.booksService.getCategories().subscribe({\n      next: (categories) => {\n        this.categories = categories;\n      },\n      error: (error) => {\n        console.error('Error loading categories:', error);\n      }\n    });\n  }\n\n  loadBook(): void {\n    if (this.bookId) {\n      this.booksService.getBookById(this.bookId).subscribe({\n        next: (book) => {\n          this.bookForm.patchValue(book);\n        },\n        error: (error) => {\n          console.error('Error loading book:', error);\n          this.snackBar.open('Error loading book details.', 'Close', {\n            duration: 3000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    }\n  }\n\n  onSubmit(): void {\n    if (this.bookForm.valid && !this.isLoading) {\n      this.isLoading = true;\n      const bookData = this.bookForm.value;\n\n      const operation = this.isEditMode \n        ? this.booksService.updateBook(this.bookId!, bookData)\n        : this.booksService.createBook(bookData);\n\n      operation.subscribe({\n        next: () => {\n          this.isLoading = false;\n          this.snackBar.open(\n            `Book ${this.isEditMode ? 'updated' : 'created'} successfully!`, \n            'Close', \n            {\n              duration: 3000,\n              panelClass: ['success-snackbar']\n            }\n          );\n          this.router.navigate(['/books']);\n        },\n        error: (error) => {\n          this.isLoading = false;\n          console.error('Error saving book:', error);\n          this.snackBar.open('Error saving book. Please try again.', 'Close', {\n            duration: 3000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    }\n  }\n\n  goBack(): void {\n    this.router.navigate(['/books']);\n  }\n}\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;IAmBrDC,EAAA,CAAAC,cAAA,gBAAoF;IAClFD,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAMZH,EAAA,CAAAC,cAAA,gBAAsF;IACpFD,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAaVH,EAAA,CAAAC,cAAA,gBAAoF;IAClFD,EAAA,CAAAE,MAAA,gCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAMZH,EAAA,CAAAC,cAAA,gBAAoG;IAClGD,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAOZH,EAAA,CAAAC,cAAA,qBAA8E;IAC5ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFmCH,EAAA,CAAAI,UAAA,UAAAC,WAAA,CAAAC,UAAA,CAA6B;IAC3EN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,WAAA,CAAAI,YAAA,MACF;;;;;IAEFT,EAAA,CAAAC,cAAA,gBAA8F;IAC5FD,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAUVH,EAAA,CAAAC,cAAA,mBAA6C;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAC/DH,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IAA9CH,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAU,iBAAA,CAAAC,MAAA,CAAAC,UAAA,oBAAmC;;;AAoDhF,OAAM,MAAOC,gBAAgB;EAO3BC,YACUC,WAAwB,EACxBC,YAA0B,EAC1BC,MAAc,EACdC,KAAqB,EACrBC,QAAqB;IAJrB,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IAVlB,KAAAC,UAAU,GAAe,EAAE;IAC3B,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAT,UAAU,GAAG,KAAK;IAClB,KAAAU,MAAM,GAAkB,IAAI;IAS1B,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACR,WAAW,CAACS,KAAK,CAAC;MACrCC,KAAK,EAAE,CAAC,EAAE,EAAE1B,UAAU,CAAC2B,QAAQ,CAAC;MAChCC,MAAM,EAAE,CAAC,EAAE,EAAE5B,UAAU,CAAC2B,QAAQ,CAAC;MACjCE,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC9B,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACrDC,aAAa,EAAE,CAAC,EAAE,EAAE,CAAChC,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7DxB,UAAU,EAAE,CAAC,EAAE,EAAEP,UAAU,CAAC2B,QAAQ,CAAC;MACrCM,QAAQ,EAAE,CAAC,EAAE;KACd,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;IAErB;IACA,IAAI,CAACZ,MAAM,GAAG,CAAC,IAAI,CAACJ,KAAK,CAACiB,QAAQ,CAACC,MAAM,CAAC,IAAI,CAAC;IAC/C,IAAI,IAAI,CAACd,MAAM,EAAE;MACf,IAAI,CAACV,UAAU,GAAG,IAAI;MACtB,IAAI,CAACyB,QAAQ,EAAE;;EAEnB;EAEAH,cAAcA,CAAA;IACZ,IAAI,CAAClB,YAAY,CAACsB,aAAa,EAAE,CAACC,SAAS,CAAC;MAC1CC,IAAI,EAAGpB,UAAU,IAAI;QACnB,IAAI,CAACA,UAAU,GAAGA,UAAU;MAC9B,CAAC;MACDqB,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;KACD,CAAC;EACJ;EAEAJ,QAAQA,CAAA;IACN,IAAI,IAAI,CAACf,MAAM,EAAE;MACf,IAAI,CAACN,YAAY,CAAC2B,WAAW,CAAC,IAAI,CAACrB,MAAM,CAAC,CAACiB,SAAS,CAAC;QACnDC,IAAI,EAAGI,IAAI,IAAI;UACb,IAAI,CAACrB,QAAQ,CAACsB,UAAU,CAACD,IAAI,CAAC;QAChC,CAAC;QACDH,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;UAC3C,IAAI,CAACtB,QAAQ,CAAC2B,IAAI,CAAC,6BAA6B,EAAE,OAAO,EAAE;YACzDC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,gBAAgB;WAC9B,CAAC;QACJ;OACD,CAAC;;EAEN;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC1B,QAAQ,CAAC2B,KAAK,IAAI,CAAC,IAAI,CAAC7B,SAAS,EAAE;MAC1C,IAAI,CAACA,SAAS,GAAG,IAAI;MACrB,MAAM8B,QAAQ,GAAG,IAAI,CAAC5B,QAAQ,CAAC6B,KAAK;MAEpC,MAAMC,SAAS,GAAG,IAAI,CAACzC,UAAU,GAC7B,IAAI,CAACI,YAAY,CAACsC,UAAU,CAAC,IAAI,CAAChC,MAAO,EAAE6B,QAAQ,CAAC,GACpD,IAAI,CAACnC,YAAY,CAACuC,UAAU,CAACJ,QAAQ,CAAC;MAE1CE,SAAS,CAACd,SAAS,CAAC;QAClBC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACnB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACF,QAAQ,CAAC2B,IAAI,CAChB,QAAQ,IAAI,CAAClC,UAAU,GAAG,SAAS,GAAG,SAAS,gBAAgB,EAC/D,OAAO,EACP;YACEmC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,kBAAkB;WAChC,CACF;UACD,IAAI,CAAC/B,MAAM,CAACuC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;QAClC,CAAC;QACDf,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACpB,SAAS,GAAG,KAAK;UACtBqB,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;UAC1C,IAAI,CAACtB,QAAQ,CAAC2B,IAAI,CAAC,sCAAsC,EAAE,OAAO,EAAE;YAClEC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,gBAAgB;WAC9B,CAAC;QACJ;OACD,CAAC;;EAEN;EAEAS,MAAMA,CAAA;IACJ,IAAI,CAACxC,MAAM,CAACuC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;;;uBApGW3C,gBAAgB,EAAAb,EAAA,CAAA0D,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA5D,EAAA,CAAA0D,iBAAA,CAAAG,EAAA,CAAAC,YAAA,GAAA9D,EAAA,CAAA0D,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAhE,EAAA,CAAA0D,iBAAA,CAAAK,EAAA,CAAAE,cAAA,GAAAjE,EAAA,CAAA0D,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhBtD,gBAAgB;MAAAuD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UArHnB1E,EAHN,CAAAC,cAAA,aAAgC,kBACG,sBACd,qBACC;UAAAD,EAAA,CAAAE,MAAA,GAA+C;UACjEF,EADiE,CAAAG,YAAA,EAAiB,EAChE;UAGhBH,EADF,CAAAC,cAAA,uBAAkB,cACuD;UAA1CD,EAAA,CAAA4E,UAAA,sBAAAC,mDAAA;YAAA,OAAYF,GAAA,CAAA1B,QAAA,EAAU;UAAA,EAAC;UAEhDjD,EADF,CAAAC,cAAA,wBAAwD,gBAC3C;UAAAD,EAAA,CAAAE,MAAA,YAAK;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAA8E,SAAA,gBAAuE;UACvE9E,EAAA,CAAA+E,UAAA,KAAAC,sCAAA,uBAAoF;UAGtFhF,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC7BH,EAAA,CAAA8E,SAAA,gBAAyE;UACzE9E,EAAA,CAAA+E,UAAA,KAAAE,sCAAA,uBAAsF;UAGxFjF,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAA8E,SAAA,mBAA0G;UAC5G9E,EAAA,CAAAG,YAAA,EAAiB;UAIbH,EAFJ,CAAAC,cAAA,cAAsB,yBACoC,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAA8E,SAAA,iBAAyE;UACzE9E,EAAA,CAAAC,cAAA,gBAAgB;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACxBH,EAAA,CAAA+E,UAAA,KAAAG,sCAAA,uBAAoF;UAGtFlF,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAA8E,SAAA,iBAA8E;UAC9E9E,EAAA,CAAA+E,UAAA,KAAAI,sCAAA,uBAAoG;UAIxGnF,EADE,CAAAG,YAAA,EAAiB,EACb;UAGJH,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAC,cAAA,sBAAyC;UACvCD,EAAA,CAAA+E,UAAA,KAAAK,uCAAA,yBAA8E;UAGhFpF,EAAA,CAAAG,YAAA,EAAa;UACbH,EAAA,CAAA+E,UAAA,KAAAM,sCAAA,uBAA8F;UAGhGrF,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,4BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC3CH,EAAA,CAAA8E,SAAA,iBAAyE;UAC3E9E,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,eAA0B,kBAC2E;UAEjGD,EADA,CAAA+E,UAAA,KAAAO,qCAAA,uBAA6C,KAAAC,qCAAA,sBAChB;UAC7BvF,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,kBAAoD;UAAnBD,EAAA,CAAA4E,UAAA,mBAAAY,mDAAA;YAAA,OAASb,GAAA,CAAAlB,MAAA,EAAQ;UAAA,EAAC;UACjDzD,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,gBACF;UAKVF,EALU,CAAAG,YAAA,EAAS,EACL,EACD,EACU,EACV,EACP;;;;;;;;UA7EgBH,EAAA,CAAAO,SAAA,GAA+C;UAA/CP,EAAA,CAAAU,iBAAA,CAAAiE,GAAA,CAAA/D,UAAA,gCAA+C;UAIzDZ,EAAA,CAAAO,SAAA,GAAsB;UAAtBP,EAAA,CAAAI,UAAA,cAAAuE,GAAA,CAAApD,QAAA,CAAsB;UAIZvB,EAAA,CAAAO,SAAA,GAAsE;UAAtEP,EAAA,CAAAI,UAAA,WAAAqF,OAAA,GAAAd,GAAA,CAAApD,QAAA,CAAAmE,GAAA,4BAAAD,OAAA,CAAAE,OAAA,OAAAF,OAAA,GAAAd,GAAA,CAAApD,QAAA,CAAAmE,GAAA,4BAAAD,OAAA,CAAAG,OAAA,EAAsE;UAQtE5F,EAAA,CAAAO,SAAA,GAAwE;UAAxEP,EAAA,CAAAI,UAAA,WAAAyF,OAAA,GAAAlB,GAAA,CAAApD,QAAA,CAAAmE,GAAA,6BAAAG,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAAlB,GAAA,CAAApD,QAAA,CAAAmE,GAAA,6BAAAG,OAAA,CAAAD,OAAA,EAAwE;UAetE5F,EAAA,CAAAO,SAAA,IAAsE;UAAtEP,EAAA,CAAAI,UAAA,WAAA0F,OAAA,GAAAnB,GAAA,CAAApD,QAAA,CAAAmE,GAAA,4BAAAI,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAAnB,GAAA,CAAApD,QAAA,CAAAmE,GAAA,4BAAAI,OAAA,CAAAF,OAAA,EAAsE;UAQtE5F,EAAA,CAAAO,SAAA,GAAsF;UAAtFP,EAAA,CAAAI,UAAA,WAAA2F,OAAA,GAAApB,GAAA,CAAApD,QAAA,CAAAmE,GAAA,oCAAAK,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAApB,GAAA,CAAApD,QAAA,CAAAmE,GAAA,oCAAAK,OAAA,CAAAH,OAAA,EAAsF;UASjE5F,EAAA,CAAAO,SAAA,GAAa;UAAbP,EAAA,CAAAI,UAAA,YAAAuE,GAAA,CAAAvD,UAAA,CAAa;UAIpCpB,EAAA,CAAAO,SAAA,EAAgF;UAAhFP,EAAA,CAAAI,UAAA,WAAA4F,OAAA,GAAArB,GAAA,CAAApD,QAAA,CAAAmE,GAAA,iCAAAM,OAAA,CAAAL,OAAA,OAAAK,OAAA,GAAArB,GAAA,CAAApD,QAAA,CAAAmE,GAAA,iCAAAM,OAAA,CAAAJ,OAAA,EAAgF;UAWpC5F,EAAA,CAAAO,SAAA,GAA0C;UAA1CP,EAAA,CAAAI,UAAA,aAAAuE,GAAA,CAAApD,QAAA,CAAAoE,OAAA,IAAAhB,GAAA,CAAAtD,SAAA,CAA0C;UACrFrB,EAAA,CAAAO,SAAA,EAAe;UAAfP,EAAA,CAAAI,UAAA,SAAAuE,GAAA,CAAAtD,SAAA,CAAe;UACfrB,EAAA,CAAAO,SAAA,EAAgB;UAAhBP,EAAA,CAAAI,UAAA,UAAAuE,GAAA,CAAAtD,SAAA,CAAgB;UAC3BrB,EAAA,CAAAO,SAAA,EACF;UADEP,EAAA,CAAAQ,kBAAA,MAAAmE,GAAA,CAAAtD,SAAA,iBAAAsD,GAAA,CAAA/D,UAAA,mCACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}