/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { DefaultIterableDifferFactory } from './differs/default_iterable_differ';
import { DefaultKeyValueDifferFactory } from './differs/default_keyvalue_differ';
import { IterableDiffers } from './differs/iterable_differs';
import { KeyValueDiffers } from './differs/keyvalue_differs';
export { SimpleChange } from '../interface/simple_change';
export { devModeEqual } from '../util/comparison';
export { ChangeDetectorRef } from './change_detector_ref';
export { ChangeDetectionStrategy } from './constants';
export { DefaultIterableDiffer, DefaultIterableDifferFactory } from './differs/default_iterable_differ';
export { DefaultKeyValueDifferFactory } from './differs/default_keyvalue_differ';
export { IterableDiffers } from './differs/iterable_differs';
export { KeyValueDiffers } from './differs/keyvalue_differs';
/**
 * Structural diffing for `Object`s and `Map`s.
 */
const keyValDiff = [new DefaultKeyValueDifferFactory()];
/**
 * Structural diffing for `Iterable` types such as `Array`s.
 */
const iterableDiff = [new DefaultIterableDifferFactory()];
export const defaultIterableDiffers = new IterableDiffers(iterableDiff);
export const defaultKeyValueDiffers = new KeyValueDiffers(keyValDiff);
//# sourceMappingURL=data:application/json;base64,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