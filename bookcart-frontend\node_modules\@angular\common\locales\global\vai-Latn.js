/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['vai-latn'] = ["vai-Latn",[["AM","PM"],u,u],u,[["S","M","T","W","T","F","S"],["lahadi","tɛɛnɛɛ","talata","alaba","aimisa","aijima","siɓiti"],u,u],u,[["1","2","3","4","5","6","7","8","9","10","11","12"],["M01","M02","M03","M04","M05","M06","M07","M08","M09","M10","M11","M12"],u],u,[["BCE","CE"],u,u],1,[6,0],["dd/MM/y","d MMM y","d MMMM y","EEEE, d MMMM y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1} {0}",u,u,u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤#,##0.00","#E0"],"LRD","$","Laibhiya Dala",{"JPY":["JP¥","¥"],"LRD":["$"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    