"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DevkitMigration = void 0;
const migration_1 = require("../update-tool/migration");
class DevkitMigration extends migration_1.Migration {
    /** Prints an informative message with context on the current target. */
    printInfo(text) {
        const targetName = this.context.isTestTarget ? 'test' : 'build';
        this.logger.info(`- ${this.context.projectName}@${targetName}: ${text}`);
    }
}
exports.DevkitMigration = DevkitMigration;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZGV2a2l0LW1pZ3JhdGlvbi5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uLy4uL3NyYy9jZGsvc2NoZW1hdGljcy9uZy11cGRhdGUvZGV2a2l0LW1pZ3JhdGlvbi50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQUE7Ozs7OztHQU1HOzs7QUFJSCx3REFBcUY7QUFjckYsTUFBc0IsZUFBc0IsU0FBUSxxQkFBOEI7SUFDaEYsd0VBQXdFO0lBQzlELFNBQVMsQ0FBQyxJQUFZO1FBQzlCLE1BQU0sVUFBVSxHQUFHLElBQUksQ0FBQyxPQUFPLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQztRQUNoRSxJQUFJLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxLQUFLLElBQUksQ0FBQyxPQUFPLENBQUMsV0FBVyxJQUFJLFVBQVUsS0FBSyxJQUFJLEVBQUUsQ0FBQyxDQUFDO0lBQzNFLENBQUM7Q0FhRjtBQWxCRCwwQ0FrQkMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgR29vZ2xlIExMQyBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICpcbiAqIFVzZSBvZiB0aGlzIHNvdXJjZSBjb2RlIGlzIGdvdmVybmVkIGJ5IGFuIE1JVC1zdHlsZSBsaWNlbnNlIHRoYXQgY2FuIGJlXG4gKiBmb3VuZCBpbiB0aGUgTElDRU5TRSBmaWxlIGF0IGh0dHBzOi8vYW5ndWxhci5pby9saWNlbnNlXG4gKi9cblxuaW1wb3J0IHtTY2hlbWF0aWNDb250ZXh0LCBUcmVlfSBmcm9tICdAYW5ndWxhci1kZXZraXQvc2NoZW1hdGljcyc7XG5pbXBvcnQge3dvcmtzcGFjZXN9IGZyb20gJ0Bhbmd1bGFyLWRldmtpdC9jb3JlJztcbmltcG9ydCB7Q29uc3RydWN0b3IsIE1pZ3JhdGlvbiwgUG9zdE1pZ3JhdGlvbkFjdGlvbn0gZnJvbSAnLi4vdXBkYXRlLXRvb2wvbWlncmF0aW9uJztcbmltcG9ydCB7VGFyZ2V0VmVyc2lvbn0gZnJvbSAnLi4vdXBkYXRlLXRvb2wvdGFyZ2V0LXZlcnNpb24nO1xuXG5leHBvcnQgdHlwZSBEZXZraXRDb250ZXh0ID0ge1xuICAvKiogRGV2a2l0IHRyZWUgZm9yIHRoZSBjdXJyZW50IG1pZ3JhdGlvbnMuIENhbiBiZSB1c2VkIHRvIGluc2VydC9yZW1vdmUgZmlsZXMuICovXG4gIHRyZWU6IFRyZWU7XG4gIC8qKiBOYW1lIG9mIHRoZSBwcm9qZWN0IHRoZSBtaWdyYXRpb25zIHJ1biBhZ2FpbnN0LiAqL1xuICBwcm9qZWN0TmFtZTogc3RyaW5nO1xuICAvKiogV29ya3NwYWNlIHByb2plY3QgdGhlIG1pZ3JhdGlvbnMgcnVuIGFnYWluc3QuICovXG4gIHByb2plY3Q6IHdvcmtzcGFjZXMuUHJvamVjdERlZmluaXRpb247XG4gIC8qKiBXaGV0aGVyIHRoZSBtaWdyYXRpb25zIHJ1biBmb3IgYSB0ZXN0IHRhcmdldC4gKi9cbiAgaXNUZXN0VGFyZ2V0OiBib29sZWFuO1xufTtcblxuZXhwb3J0IGFic3RyYWN0IGNsYXNzIERldmtpdE1pZ3JhdGlvbjxEYXRhPiBleHRlbmRzIE1pZ3JhdGlvbjxEYXRhLCBEZXZraXRDb250ZXh0PiB7XG4gIC8qKiBQcmludHMgYW4gaW5mb3JtYXRpdmUgbWVzc2FnZSB3aXRoIGNvbnRleHQgb24gdGhlIGN1cnJlbnQgdGFyZ2V0LiAqL1xuICBwcm90ZWN0ZWQgcHJpbnRJbmZvKHRleHQ6IHN0cmluZykge1xuICAgIGNvbnN0IHRhcmdldE5hbWUgPSB0aGlzLmNvbnRleHQuaXNUZXN0VGFyZ2V0ID8gJ3Rlc3QnIDogJ2J1aWxkJztcbiAgICB0aGlzLmxvZ2dlci5pbmZvKGAtICR7dGhpcy5jb250ZXh0LnByb2plY3ROYW1lfUAke3RhcmdldE5hbWV9OiAke3RleHR9YCk7XG4gIH1cblxuICAvKipcbiAgICogT3B0aW9uYWwgc3RhdGljIG1ldGhvZCB0aGF0IHdpbGwgYmUgY2FsbGVkIG9uY2UgdGhlIG1pZ3JhdGlvbiBvZiBhbGwgcHJvamVjdFxuICAgKiB0YXJnZXRzIGhhcyBiZWVuIHBlcmZvcm1lZC4gVGhpcyBtZXRob2QgY2FuIGJlIHVzZWQgdG8gbWFrZSBjaGFuZ2VzIHJlc3BlY3RpbmcgdGhlXG4gICAqIG1pZ3JhdGlvbiByZXN1bHQgb2YgYWxsIGluZGl2aWR1YWwgdGFyZ2V0cy4gZS5nLiByZW1vdmluZyBIYW1tZXJKUyBpZiBpdFxuICAgKiBpcyBub3QgbmVlZGVkIGluIGFueSBwcm9qZWN0IHRhcmdldC5cbiAgICovXG4gIHN0YXRpYyBnbG9iYWxQb3N0TWlncmF0aW9uPyhcbiAgICB0cmVlOiBUcmVlLFxuICAgIHRhcmdldFZlcnNpb246IFRhcmdldFZlcnNpb24sXG4gICAgY29udGV4dDogU2NoZW1hdGljQ29udGV4dCxcbiAgKTogUG9zdE1pZ3JhdGlvbkFjdGlvbjtcbn1cblxuZXhwb3J0IHR5cGUgRGV2a2l0TWlncmF0aW9uQ3RvcjxEYXRhPiA9IENvbnN0cnVjdG9yPERldmtpdE1pZ3JhdGlvbjxEYXRhPj4gJiB7XG4gIFttIGluIGtleW9mIHR5cGVvZiBEZXZraXRNaWdyYXRpb25dOiAodHlwZW9mIERldmtpdE1pZ3JhdGlvbilbbV07XG59O1xuIl19