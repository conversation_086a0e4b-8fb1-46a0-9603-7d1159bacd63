/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export const PLATFORM_BROWSER_ID = 'browser';
export const PLATFORM_SERVER_ID = 'server';
export const PLATFORM_WORKER_APP_ID = 'browserWorkerApp';
export const PLATFORM_WORKER_UI_ID = 'browserWorkerUi';
/**
 * Returns whether a platform id represents a browser platform.
 * @publicApi
 */
export function isPlatformBrowser(platformId) {
    return platformId === PLATFORM_BROWSER_ID;
}
/**
 * Returns whether a platform id represents a server platform.
 * @publicApi
 */
export function isPlatformServer(platformId) {
    return platformId === PLATFORM_SERVER_ID;
}
/**
 * Returns whether a platform id represents a web worker app platform.
 * @publicApi
 * @deprecated This function serves no purpose since the removal of the Webworker platform. It will
 *     always return `false`.
 */
export function isPlatformWorkerApp(platformId) {
    return platformId === PLATFORM_WORKER_APP_ID;
}
/**
 * Returns whether a platform id represents a web worker UI platform.
 * @publicApi
 * @deprecated This function serves no purpose since the removal of the Webworker platform. It will
 *     always return `false`.
 */
export function isPlatformWorkerUi(platformId) {
    return platformId === PLATFORM_WORKER_UI_ID;
}
//# sourceMappingURL=data:application/json;base64,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