/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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