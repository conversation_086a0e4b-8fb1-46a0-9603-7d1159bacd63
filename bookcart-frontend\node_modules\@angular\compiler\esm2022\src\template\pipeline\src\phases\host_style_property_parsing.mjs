/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import * as ir from '../../ir';
const STYLE_DOT = 'style.';
const CLASS_DOT = 'class.';
const STYLE_BANG = 'style!';
const CLASS_BANG = 'class!';
const BANG_IMPORTANT = '!important';
/**
 * Host bindings are compiled using a different parser entrypoint, and are parsed quite differently
 * as a result. Therefore, we need to do some extra parsing for host style properties, as compared
 * to non-host style properties.
 * TODO: Unify host bindings and non-host bindings in the parser.
 */
export function parseHostStyleProperties(job) {
    for (const op of job.root.update) {
        if (!(op.kind === ir.OpKind.Binding && op.bindingKind === ir.BindingKind.Property)) {
            continue;
        }
        if (op.name.endsWith(BANG_IMPORTANT)) {
            // Delete any `!important` suffixes from the binding name.
            op.name = op.name.substring(0, op.name.length - BANG_IMPORTANT.length);
        }
        if (op.name.startsWith(STYLE_DOT)) {
            op.bindingKind = ir.BindingKind.StyleProperty;
            op.name = op.name.substring(STYLE_DOT.length);
            if (!isCssCustomProperty(op.name)) {
                op.name = hyphenate(op.name);
            }
            const { property, suffix } = parseProperty(op.name);
            op.name = property;
            op.unit = suffix;
        }
        else if (op.name.startsWith(STYLE_BANG)) {
            op.bindingKind = ir.BindingKind.StyleProperty;
            op.name = 'style';
        }
        else if (op.name.startsWith(CLASS_DOT)) {
            op.bindingKind = ir.BindingKind.ClassName;
            op.name = parseProperty(op.name.substring(CLASS_DOT.length)).property;
        }
        else if (op.name.startsWith(CLASS_BANG)) {
            op.bindingKind = ir.BindingKind.ClassName;
            op.name = parseProperty(op.name.substring(CLASS_BANG.length)).property;
        }
    }
}
/**
 * Checks whether property name is a custom CSS property.
 * See: https://www.w3.org/TR/css-variables-1
 */
function isCssCustomProperty(name) {
    return name.startsWith('--');
}
function hyphenate(value) {
    return value
        .replace(/[a-z][A-Z]/g, v => {
        return v.charAt(0) + '-' + v.charAt(1);
    })
        .toLowerCase();
}
function parseProperty(name) {
    const overrideIndex = name.indexOf('!important');
    if (overrideIndex !== -1) {
        name = overrideIndex > 0 ? name.substring(0, overrideIndex) : '';
    }
    let suffix = null;
    let property = name;
    const unitIndex = name.lastIndexOf('.');
    if (unitIndex > 0) {
        suffix = name.slice(unitIndex + 1);
        property = name.substring(0, unitIndex);
    }
    return { property, suffix };
}
//# sourceMappingURL=data:application/json;base64,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