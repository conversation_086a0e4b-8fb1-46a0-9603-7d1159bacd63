/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { ɵɵinjectAttribute } from '../render3/instructions/di_attr';
import { makeParamDecorator } from '../util/decorators';
/**
 * Attribute decorator and metadata.
 *
 * @Annotation
 * @publicApi
 */
export const Attribute = makeParamDecorator('Attribute', (attributeName) => ({ attributeName, __NG_ELEMENT_ID__: () => ɵɵinjectAttribute(attributeName) }));
//# sourceMappingURL=data:application/json;base64,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