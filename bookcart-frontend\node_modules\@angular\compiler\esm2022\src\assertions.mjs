/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
const UNUSABLE_INTERPOLATION_REGEXPS = [
    /^\s*$/, // empty
    /[<>]/, // html tag
    /^[{}]$/, // i18n expansion
    /&(#|[a-z])/i, // character reference,
    /^\/\//, // comment
];
export function assertInterpolationSymbols(identifier, value) {
    if (value != null && !(Array.isArray(value) && value.length == 2)) {
        throw new Error(`Expected '${identifier}' to be an array, [start, end].`);
    }
    else if (value != null) {
        const start = value[0];
        const end = value[1];
        // Check for unusable interpolation symbols
        UNUSABLE_INTERPOLATION_REGEXPS.forEach(regexp => {
            if (regexp.test(start) || regexp.test(end)) {
                throw new Error(`['${start}', '${end}'] contains unusable interpolation symbol.`);
            }
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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