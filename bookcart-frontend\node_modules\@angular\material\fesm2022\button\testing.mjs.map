{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../../src/material/button/testing/button-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {booleanAttribute} from '@angular/core';\nimport {\n  ComponentHarnessConstructor,\n  ContentContainerComponentHarness,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {ButtonHarnessFilters, ButtonVariant} from './button-harness-filters';\n\n/** Harness for interacting with a MDC-based mat-button in tests. */\nexport class MatButtonHarness extends ContentContainerComponentHarness {\n  // TODO(jelbourn) use a single class, like `.mat-button-base`\n  static hostSelector = `[mat-button], [mat-raised-button], [mat-flat-button],\n                         [mat-icon-button], [mat-stroked-button], [mat-fab], [mat-mini-fab]`;\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a button with specific attributes.\n   * @param options Options for narrowing the search:\n   *   - `selector` finds a button whose host element matches the given selector.\n   *   - `text` finds a button with specific text content.\n   *   - `variant` finds buttons matching a specific variant.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatButtonHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: ButtonHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options)\n      .addOption('text', options.text, (harness, text) =>\n        HarnessPredicate.stringMatches(harness.getText(), text),\n      )\n      .addOption('variant', options.variant, (harness, variant) =>\n        HarnessPredicate.stringMatches(harness.getVariant(), variant),\n      )\n      .addOption('disabled', options.disabled, async (harness, disabled) => {\n        return (await harness.isDisabled()) === disabled;\n      });\n  }\n\n  /**\n   * Clicks the button at the given position relative to its top-left.\n   * @param relativeX The relative x position of the click.\n   * @param relativeY The relative y position of the click.\n   */\n  click(relativeX: number, relativeY: number): Promise<void>;\n  /** Clicks the button at its center. */\n  click(location: 'center'): Promise<void>;\n  /** Clicks the button. */\n  click(): Promise<void>;\n  async click(...args: [] | ['center'] | [number, number]): Promise<void> {\n    return (await this.host()).click(...(args as []));\n  }\n\n  /** Gets a boolean promise indicating if the button is disabled. */\n  async isDisabled(): Promise<boolean> {\n    const host = await this.host();\n    return (\n      booleanAttribute(await host.getAttribute('disabled')) ||\n      (await host.hasClass('mat-mdc-button-disabled'))\n    );\n  }\n\n  /** Gets a promise for the button's label text. */\n  async getText(): Promise<string> {\n    return (await this.host()).text();\n  }\n\n  /** Focuses the button and returns a void promise that indicates when the action is complete. */\n  async focus(): Promise<void> {\n    return (await this.host()).focus();\n  }\n\n  /** Blurs the button and returns a void promise that indicates when the action is complete. */\n  async blur(): Promise<void> {\n    return (await this.host()).blur();\n  }\n\n  /** Whether the button is focused. */\n  async isFocused(): Promise<boolean> {\n    return (await this.host()).isFocused();\n  }\n\n  /** Gets the variant of the button. */\n  async getVariant(): Promise<ButtonVariant> {\n    const host = await this.host();\n\n    if ((await host.getAttribute('mat-raised-button')) != null) {\n      return 'raised';\n    } else if ((await host.getAttribute('mat-flat-button')) != null) {\n      return 'flat';\n    } else if ((await host.getAttribute('mat-icon-button')) != null) {\n      return 'icon';\n    } else if ((await host.getAttribute('mat-stroked-button')) != null) {\n      return 'stroked';\n    } else if ((await host.getAttribute('mat-fab')) != null) {\n      return 'fab';\n    } else if ((await host.getAttribute('mat-mini-fab')) != null) {\n      return 'mini-fab';\n    }\n\n    return 'basic';\n  }\n}\n"], "names": [], "mappings": ";;;AAgBA;AACM,MAAO,gBAAiB,SAAQ,gCAAgC,CAAA;;AAE7D,IAAA,SAAA,IAAA,CAAA,YAAY,GAAG,CAAA;4FACoE,CAAC,EAAA;AAE3F;;;;;;;AAOG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAgC,EAAE,EAAA;AAElC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC;aACvC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,IAAI,KAC7C,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,CACxD;aACA,SAAS,CAAC,SAAS,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,KACtD,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,OAAO,CAAC,CAC9D;AACA,aAAA,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,OAAO,EAAE,QAAQ,KAAI;YACnE,OAAO,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,QAAQ,CAAC;AACnD,SAAC,CAAC,CAAC;KACN;AAYD,IAAA,MAAM,KAAK,CAAC,GAAG,IAAwC,EAAA;AACrD,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,GAAI,IAAW,CAAC,CAAC;KACnD;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAC/B,QACE,gBAAgB,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;aACpD,MAAM,IAAI,CAAC,QAAQ,CAAC,yBAAyB,CAAC,CAAC,EAChD;KACH;;AAGD,IAAA,MAAM,OAAO,GAAA;QACX,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC;KACnC;;AAGD,IAAA,MAAM,KAAK,GAAA;QACT,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC;KACpC;;AAGD,IAAA,MAAM,IAAI,GAAA;QACR,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC;KACnC;;AAGD,IAAA,MAAM,SAAS,GAAA;QACb,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,CAAC;KACxC;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;AAE/B,QAAA,IAAI,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,KAAK,IAAI,EAAE;AAC1D,YAAA,OAAO,QAAQ,CAAC;SACjB;AAAM,aAAA,IAAI,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,KAAK,IAAI,EAAE;AAC/D,YAAA,OAAO,MAAM,CAAC;SACf;AAAM,aAAA,IAAI,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,KAAK,IAAI,EAAE;AAC/D,YAAA,OAAO,MAAM,CAAC;SACf;AAAM,aAAA,IAAI,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,KAAK,IAAI,EAAE;AAClE,YAAA,OAAO,SAAS,CAAC;SAClB;AAAM,aAAA,IAAI,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;AACvD,YAAA,OAAO,KAAK,CAAC;SACd;AAAM,aAAA,IAAI,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;AAC5D,YAAA,OAAO,UAAU,CAAC;SACnB;AAED,QAAA,OAAO,OAAO,CAAC;KAChB;;;;;"}