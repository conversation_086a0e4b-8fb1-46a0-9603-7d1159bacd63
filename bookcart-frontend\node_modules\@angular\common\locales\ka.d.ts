/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    AUD: (string | undefined)[];
    BYN: (string | undefined)[];
    CNY: (string | undefined)[];
    GEL: string[];
    HKD: (string | undefined)[];
    ILS: (string | undefined)[];
    INR: (string | undefined)[];
    JPY: (string | undefined)[];
    KRW: (string | undefined)[];
    NZD: (string | undefined)[];
    PHP: (string | undefined)[];
    TWD: string[];
    USD: string[];
    VND: (string | undefined)[];
} | undefined)[];
export default _default;
