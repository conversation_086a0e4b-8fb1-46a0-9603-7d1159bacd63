@use '@material/form-field/form-field-theme' as mdc-form-field-theme;
@use '../tokens/m2/mdc/form-field' as tokens-mdc-form-field;
@use '../style/sass-utils';
@use '../theming/theming';
@use '../theming/inspection';
@use '../typography/typography';

@mixin base($theme) {}

@mixin color($theme) {
  @include sass-utils.current-selector-or-root() {
    @include mdc-form-field-theme.theme(tokens-mdc-form-field.get-color-tokens($theme));
  }
}

@mixin typography($theme) {
  @include sass-utils.current-selector-or-root() {
    @include mdc-form-field-theme.theme(tokens-mdc-form-field.get-typography-tokens($theme));
  }
}

@mixin density($theme) {}

@mixin theme($theme) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-internal-form-field') {
    @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
  }
}
