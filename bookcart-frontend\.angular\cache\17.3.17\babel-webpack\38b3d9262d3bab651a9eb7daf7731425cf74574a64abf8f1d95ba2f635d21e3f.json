{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../shared/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/form-field\";\nimport * as i9 from \"@angular/material/input\";\nimport * as i10 from \"@angular/material/icon\";\nfunction LoginComponent_mat_error_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getEmailErrorMessage(), \" \");\n  }\n}\nfunction LoginComponent_mat_error_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getPasswordErrorMessage(), \" \");\n  }\n}\nfunction LoginComponent_mat_icon_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 15);\n    i0.ɵɵtext(1, \"refresh\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_mat_icon_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"login\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(formBuilder, authService, router, route, snackBar) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.router = router;\n    this.route = route;\n    this.snackBar = snackBar;\n    this.isLoading = false;\n    this.hidePassword = true;\n    this.returnUrl = '/books';\n    this.loginForm = this.formBuilder.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n  ngOnInit() {\n    // Get return url from route parameters or default to '/books'\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/books';\n    // Redirect if already logged in\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate([this.returnUrl]);\n    }\n  }\n  onSubmit() {\n    if (this.loginForm.valid && !this.isLoading) {\n      this.isLoading = true;\n      const loginData = this.loginForm.value;\n      this.authService.login(loginData).subscribe({\n        next: response => {\n          this.isLoading = false;\n          this.snackBar.open('Login successful!', 'Close', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n          this.router.navigate([this.returnUrl]);\n        },\n        error: error => {\n          this.isLoading = false;\n          let errorMessage = 'Login failed. Please try again.';\n          if (error.status === 401) {\n            errorMessage = 'Invalid email or password.';\n          } else if (error.error?.message) {\n            errorMessage = error.error.message;\n          }\n          this.snackBar.open(errorMessage, 'Close', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    }\n  }\n  getEmailErrorMessage() {\n    const emailControl = this.loginForm.get('email');\n    if (emailControl?.hasError('required')) {\n      return 'Email is required';\n    }\n    if (emailControl?.hasError('email')) {\n      return 'Please enter a valid email address';\n    }\n    return '';\n  }\n  getPasswordErrorMessage() {\n    const passwordControl = this.loginForm.get('password');\n    if (passwordControl?.hasError('required')) {\n      return 'Password is required';\n    }\n    if (passwordControl?.hasError('minlength')) {\n      return 'Password must be at least 6 characters long';\n    }\n    return '';\n  }\n  goToRegister() {\n    this.router.navigate(['/register'], {\n      queryParams: {\n        returnUrl: this.returnUrl\n      }\n    });\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 37,\n      vars: 11,\n      consts: [[1, \"login-container\"], [1, \"login-card\"], [\"mat-card-avatar\", \"\", 1, \"login-header-image\"], [1, \"login-form\", 3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email\", \"autocomplete\", \"email\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"placeholder\", \"Enter your password\", \"autocomplete\", \"current-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"full-width\", \"login-button\", 3, \"disabled\"], [\"class\", \"spinning\", 4, \"ngIf\"], [1, \"login-actions\"], [1, \"register-link\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"spinning\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"div\", 2)(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"book\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"mat-card-title\");\n          i0.ɵɵtext(7, \"Welcome Back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"mat-card-subtitle\");\n          i0.ɵɵtext(9, \"Sign in to your BookCart account\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"mat-card-content\")(11, \"form\", 3);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_11_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(12, \"mat-form-field\", 4)(13, \"mat-label\");\n          i0.ɵɵtext(14, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(15, \"input\", 5);\n          i0.ɵɵelementStart(16, \"mat-icon\", 6);\n          i0.ɵɵtext(17, \"email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(18, LoginComponent_mat_error_18_Template, 2, 1, \"mat-error\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"mat-form-field\", 4)(20, \"mat-label\");\n          i0.ɵɵtext(21, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(22, \"input\", 8);\n          i0.ɵɵelementStart(23, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_23_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(24, \"mat-icon\");\n          i0.ɵɵtext(25);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(26, LoginComponent_mat_error_26_Template, 2, 1, \"mat-error\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"button\", 10);\n          i0.ɵɵtemplate(28, LoginComponent_mat_icon_28_Template, 2, 0, \"mat-icon\", 11)(29, LoginComponent_mat_icon_29_Template, 2, 0, \"mat-icon\", 7);\n          i0.ɵɵtext(30);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"mat-card-actions\", 12)(32, \"div\", 13)(33, \"span\");\n          i0.ɵɵtext(34, \"Don't have an account?\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_35_listener() {\n            return ctx.goToRegister();\n          });\n          i0.ɵɵtext(36, \" Create Account \");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_6_0;\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_1_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"aria-label\", \"Hide password\")(\"aria-pressed\", ctx.hidePassword);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_6_0.touched));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.loginForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.isLoading ? \"Signing In...\" : \"Sign In\", \" \");\n        }\n      },\n      dependencies: [i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.MatButton, i6.MatIconButton, i7.MatCard, i7.MatCardActions, i7.MatCardAvatar, i7.MatCardContent, i7.MatCardHeader, i7.MatCardSubtitle, i7.MatCardTitle, i8.MatFormField, i8.MatLabel, i8.MatError, i8.MatSuffix, i9.MatInput, i10.MatIcon],\n      styles: [\".login-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-height: calc(100vh - 64px);\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n\\n.login-card[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 400px;\\n  padding: 20px;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  border-radius: 16px;\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.login-header-image[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.login-header-image[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  height: 24px;\\n  width: 24px;\\n}\\n\\n.login-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n  margin-top: 20px;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.login-button[_ngcontent-%COMP%] {\\n  height: 48px;\\n  font-size: 16px;\\n  font-weight: 500;\\n  margin-top: 8px;\\n}\\n\\n.login-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.spinning[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% { transform: rotate(0deg); }\\n  100% { transform: rotate(360deg); }\\n}\\n\\n.login-actions[_ngcontent-%COMP%] {\\n  padding: 16px 0 0 0;\\n  justify-content: center;\\n}\\n\\n.register-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 8px;\\n  text-align: center;\\n}\\n\\n.register-link[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n}\\n\\n\\n\\n  .success-snackbar {\\n  background-color: #4caf50 !important;\\n  color: white !important;\\n}\\n\\n  .error-snackbar {\\n  background-color: #f44336 !important;\\n  color: white !important;\\n}\\n\\n\\n\\n@media (max-width: 480px) {\\n  .login-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  \\n  .login-card[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "getEmailErrorMessage", "getPasswordErrorMessage", "LoginComponent", "constructor", "formBuilder", "authService", "router", "route", "snackBar", "isLoading", "hidePassword", "returnUrl", "loginForm", "group", "email", "required", "password", "<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "snapshot", "queryParams", "isAuthenticated", "navigate", "onSubmit", "valid", "loginData", "value", "login", "subscribe", "next", "response", "open", "duration", "panelClass", "error", "errorMessage", "status", "message", "emailControl", "get", "<PERSON><PERSON><PERSON><PERSON>", "passwordControl", "goToRegister", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "ActivatedRoute", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_11_listener", "ɵɵelement", "ɵɵtemplate", "LoginComponent_mat_error_18_Template", "LoginComponent_Template_button_click_23_listener", "LoginComponent_mat_error_26_Template", "LoginComponent_mat_icon_28_Template", "LoginComponent_mat_icon_29_Template", "LoginComponent_Template_button_click_35_listener", "ɵɵproperty", "tmp_1_0", "invalid", "touched", "ɵɵtextInterpolate", "tmp_6_0"], "sources": ["C:\\Users\\<USER>\\Desktop\\BookCart\\bookcart-frontend\\src\\app\\auth\\login\\login.component.ts", "C:\\Users\\<USER>\\Desktop\\BookCart\\bookcart-frontend\\src\\app\\auth\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { AuthService } from '../../shared/services/auth.service';\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.css']\n})\nexport class LoginComponent implements OnInit {\n  loginForm: FormGroup;\n  isLoading = false;\n  hidePassword = true;\n  returnUrl = '/books';\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private route: ActivatedRoute,\n    private snackBar: MatSnackBar\n  ) {\n    this.loginForm = this.formBuilder.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n\n  ngOnInit(): void {\n    // Get return url from route parameters or default to '/books'\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/books';\n\n    // Redirect if already logged in\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate([this.returnUrl]);\n    }\n  }\n\n  onSubmit(): void {\n    if (this.loginForm.valid && !this.isLoading) {\n      this.isLoading = true;\n      \n      const loginData = this.loginForm.value;\n      \n      this.authService.login(loginData).subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          this.snackBar.open('Login successful!', 'Close', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n          this.router.navigate([this.returnUrl]);\n        },\n        error: (error) => {\n          this.isLoading = false;\n          let errorMessage = 'Login failed. Please try again.';\n          \n          if (error.status === 401) {\n            errorMessage = 'Invalid email or password.';\n          } else if (error.error?.message) {\n            errorMessage = error.error.message;\n          }\n          \n          this.snackBar.open(errorMessage, 'Close', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    }\n  }\n\n  getEmailErrorMessage(): string {\n    const emailControl = this.loginForm.get('email');\n    if (emailControl?.hasError('required')) {\n      return 'Email is required';\n    }\n    if (emailControl?.hasError('email')) {\n      return 'Please enter a valid email address';\n    }\n    return '';\n  }\n\n  getPasswordErrorMessage(): string {\n    const passwordControl = this.loginForm.get('password');\n    if (passwordControl?.hasError('required')) {\n      return 'Password is required';\n    }\n    if (passwordControl?.hasError('minlength')) {\n      return 'Password must be at least 6 characters long';\n    }\n    return '';\n  }\n\n  goToRegister(): void {\n    this.router.navigate(['/register'], { \n      queryParams: { returnUrl: this.returnUrl } \n    });\n  }\n}\n", "<div class=\"login-container\">\n  <mat-card class=\"login-card\">\n    <mat-card-header>\n      <div mat-card-avatar class=\"login-header-image\">\n        <mat-icon>book</mat-icon>\n      </div>\n      <mat-card-title>Welcome Back</mat-card-title>\n      <mat-card-subtitle>Sign in to your BookCart account</mat-card-subtitle>\n    </mat-card-header>\n\n    <mat-card-content>\n      <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\" class=\"login-form\">\n        <!-- Email Field -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Email</mat-label>\n          <input matInput \n                 type=\"email\" \n                 formControlName=\"email\"\n                 placeholder=\"Enter your email\"\n                 autocomplete=\"email\">\n          <mat-icon matSuffix>email</mat-icon>\n          <mat-error *ngIf=\"loginForm.get('email')?.invalid && loginForm.get('email')?.touched\">\n            {{ getEmailErrorMessage() }}\n          </mat-error>\n        </mat-form-field>\n\n        <!-- Password Field -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Password</mat-label>\n          <input matInput \n                 [type]=\"hidePassword ? 'password' : 'text'\"\n                 formControlName=\"password\"\n                 placeholder=\"Enter your password\"\n                 autocomplete=\"current-password\">\n          <button mat-icon-button \n                  matSuffix \n                  (click)=\"hidePassword = !hidePassword\"\n                  [attr.aria-label]=\"'Hide password'\"\n                  [attr.aria-pressed]=\"hidePassword\"\n                  type=\"button\">\n            <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>\n          </button>\n          <mat-error *ngIf=\"loginForm.get('password')?.invalid && loginForm.get('password')?.touched\">\n            {{ getPasswordErrorMessage() }}\n          </mat-error>\n        </mat-form-field>\n\n        <!-- Submit Button -->\n        <button mat-raised-button \n                color=\"primary\" \n                type=\"submit\"\n                class=\"full-width login-button\"\n                [disabled]=\"loginForm.invalid || isLoading\">\n          <mat-icon *ngIf=\"isLoading\" class=\"spinning\">refresh</mat-icon>\n          <mat-icon *ngIf=\"!isLoading\">login</mat-icon>\n          {{ isLoading ? 'Signing In...' : 'Sign In' }}\n        </button>\n      </form>\n    </mat-card-content>\n\n    <mat-card-actions class=\"login-actions\">\n      <div class=\"register-link\">\n        <span>Don't have an account?</span>\n        <button mat-button color=\"primary\" (click)=\"goToRegister()\">\n          Create Account\n        </button>\n      </div>\n    </mat-card-actions>\n  </mat-card>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;ICoBzDC,EAAA,CAAAC,cAAA,gBAAsF;IACpFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,oBAAA,QACF;;;;;IAmBAP,EAAA,CAAAC,cAAA,gBAA4F;IAC1FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAE,uBAAA,QACF;;;;;IASAR,EAAA,CAAAC,cAAA,mBAA6C;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAC/DH,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;AD3CvD,OAAM,MAAOM,cAAc;EAMzBC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc,EACdC,KAAqB,EACrBC,QAAqB;IAJrB,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IATlB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,SAAS,GAAG,QAAQ;IASlB,IAAI,CAACC,SAAS,GAAG,IAAI,CAACR,WAAW,CAACS,KAAK,CAAC;MACtCC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACtB,UAAU,CAACuB,QAAQ,EAAEvB,UAAU,CAACsB,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACxB,UAAU,CAACuB,QAAQ,EAAEvB,UAAU,CAACyB,SAAS,CAAC,CAAC,CAAC,CAAC;KAC9D,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACP,SAAS,GAAG,IAAI,CAACJ,KAAK,CAACY,QAAQ,CAACC,WAAW,CAAC,WAAW,CAAC,IAAI,QAAQ;IAEzE;IACA,IAAI,IAAI,CAACf,WAAW,CAACgB,eAAe,EAAE,EAAE;MACtC,IAAI,CAACf,MAAM,CAACgB,QAAQ,CAAC,CAAC,IAAI,CAACX,SAAS,CAAC,CAAC;;EAE1C;EAEAY,QAAQA,CAAA;IACN,IAAI,IAAI,CAACX,SAAS,CAACY,KAAK,IAAI,CAAC,IAAI,CAACf,SAAS,EAAE;MAC3C,IAAI,CAACA,SAAS,GAAG,IAAI;MAErB,MAAMgB,SAAS,GAAG,IAAI,CAACb,SAAS,CAACc,KAAK;MAEtC,IAAI,CAACrB,WAAW,CAACsB,KAAK,CAACF,SAAS,CAAC,CAACG,SAAS,CAAC;QAC1CC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACrB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACD,QAAQ,CAACuB,IAAI,CAAC,mBAAmB,EAAE,OAAO,EAAE;YAC/CC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,kBAAkB;WAChC,CAAC;UACF,IAAI,CAAC3B,MAAM,CAACgB,QAAQ,CAAC,CAAC,IAAI,CAACX,SAAS,CAAC,CAAC;QACxC,CAAC;QACDuB,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACzB,SAAS,GAAG,KAAK;UACtB,IAAI0B,YAAY,GAAG,iCAAiC;UAEpD,IAAID,KAAK,CAACE,MAAM,KAAK,GAAG,EAAE;YACxBD,YAAY,GAAG,4BAA4B;WAC5C,MAAM,IAAID,KAAK,CAACA,KAAK,EAAEG,OAAO,EAAE;YAC/BF,YAAY,GAAGD,KAAK,CAACA,KAAK,CAACG,OAAO;;UAGpC,IAAI,CAAC7B,QAAQ,CAACuB,IAAI,CAACI,YAAY,EAAE,OAAO,EAAE;YACxCH,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,gBAAgB;WAC9B,CAAC;QACJ;OACD,CAAC;;EAEN;EAEAjC,oBAAoBA,CAAA;IAClB,MAAMsC,YAAY,GAAG,IAAI,CAAC1B,SAAS,CAAC2B,GAAG,CAAC,OAAO,CAAC;IAChD,IAAID,YAAY,EAAEE,QAAQ,CAAC,UAAU,CAAC,EAAE;MACtC,OAAO,mBAAmB;;IAE5B,IAAIF,YAAY,EAAEE,QAAQ,CAAC,OAAO,CAAC,EAAE;MACnC,OAAO,oCAAoC;;IAE7C,OAAO,EAAE;EACX;EAEAvC,uBAAuBA,CAAA;IACrB,MAAMwC,eAAe,GAAG,IAAI,CAAC7B,SAAS,CAAC2B,GAAG,CAAC,UAAU,CAAC;IACtD,IAAIE,eAAe,EAAED,QAAQ,CAAC,UAAU,CAAC,EAAE;MACzC,OAAO,sBAAsB;;IAE/B,IAAIC,eAAe,EAAED,QAAQ,CAAC,WAAW,CAAC,EAAE;MAC1C,OAAO,6CAA6C;;IAEtD,OAAO,EAAE;EACX;EAEAE,YAAYA,CAAA;IACV,IAAI,CAACpC,MAAM,CAACgB,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;MAClCF,WAAW,EAAE;QAAET,SAAS,EAAE,IAAI,CAACA;MAAS;KACzC,CAAC;EACJ;;;uBAzFWT,cAAc,EAAAT,EAAA,CAAAkD,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAApD,EAAA,CAAAkD,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAtD,EAAA,CAAAkD,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAxD,EAAA,CAAAkD,iBAAA,CAAAK,EAAA,CAAAE,cAAA,GAAAzD,EAAA,CAAAkD,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAdlD,cAAc;MAAAmD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPnBlE,EAJR,CAAAC,cAAA,aAA6B,kBACE,sBACV,aACiC,eACpC;UAAAD,EAAA,CAAAE,MAAA,WAAI;UAChBF,EADgB,CAAAG,YAAA,EAAW,EACrB;UACNH,EAAA,CAAAC,cAAA,qBAAgB;UAAAD,EAAA,CAAAE,MAAA,mBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UAC7CH,EAAA,CAAAC,cAAA,wBAAmB;UAAAD,EAAA,CAAAE,MAAA,uCAAgC;UACrDF,EADqD,CAAAG,YAAA,EAAoB,EACvD;UAGhBH,EADF,CAAAC,cAAA,wBAAkB,eACyD;UAA3CD,EAAA,CAAAoE,UAAA,sBAAAC,kDAAA;YAAA,OAAYF,GAAA,CAAArC,QAAA,EAAU;UAAA,EAAC;UAGjD9B,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAAsE,SAAA,gBAI4B;UAC5BtE,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACpCH,EAAA,CAAAuE,UAAA,KAAAC,oCAAA,uBAAsF;UAGxFxE,EAAA,CAAAG,YAAA,EAAiB;UAIfH,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAsE,SAAA,gBAIuC;UACvCtE,EAAA,CAAAC,cAAA,iBAKsB;UAHdD,EAAA,CAAAoE,UAAA,mBAAAK,iDAAA;YAAA,OAAAN,GAAA,CAAAlD,YAAA,IAAAkD,GAAA,CAAAlD,YAAA;UAAA,EAAsC;UAI5CjB,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAoD;UAChEF,EADgE,CAAAG,YAAA,EAAW,EAClE;UACTH,EAAA,CAAAuE,UAAA,KAAAG,oCAAA,uBAA4F;UAG9F1E,EAAA,CAAAG,YAAA,EAAiB;UAGjBH,EAAA,CAAAC,cAAA,kBAIoD;UAElDD,EADA,CAAAuE,UAAA,KAAAI,mCAAA,uBAA6C,KAAAC,mCAAA,sBAChB;UAC7B5E,EAAA,CAAAE,MAAA,IACF;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACJ,EACU;UAIfH,EAFJ,CAAAC,cAAA,4BAAwC,eACX,YACnB;UAAAD,EAAA,CAAAE,MAAA,8BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACnCH,EAAA,CAAAC,cAAA,kBAA4D;UAAzBD,EAAA,CAAAoE,UAAA,mBAAAS,iDAAA;YAAA,OAASV,GAAA,CAAAlB,YAAA,EAAc;UAAA,EAAC;UACzDjD,EAAA,CAAAE,MAAA,wBACF;UAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACW,EACV,EACP;;;;;UA1DMH,EAAA,CAAAI,SAAA,IAAuB;UAAvBJ,EAAA,CAAA8E,UAAA,cAAAX,GAAA,CAAAhD,SAAA,CAAuB;UAUbnB,EAAA,CAAAI,SAAA,GAAwE;UAAxEJ,EAAA,CAAA8E,UAAA,WAAAC,OAAA,GAAAZ,GAAA,CAAAhD,SAAA,CAAA2B,GAAA,4BAAAiC,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAZ,GAAA,CAAAhD,SAAA,CAAA2B,GAAA,4BAAAiC,OAAA,CAAAE,OAAA,EAAwE;UAS7EjF,EAAA,CAAAI,SAAA,GAA2C;UAA3CJ,EAAA,CAAA8E,UAAA,SAAAX,GAAA,CAAAlD,YAAA,uBAA2C;UAO1CjB,EAAA,CAAAI,SAAA,EAAmC;;UAG/BJ,EAAA,CAAAI,SAAA,GAAoD;UAApDJ,EAAA,CAAAkF,iBAAA,CAAAf,GAAA,CAAAlD,YAAA,mCAAoD;UAEpDjB,EAAA,CAAAI,SAAA,EAA8E;UAA9EJ,EAAA,CAAA8E,UAAA,WAAAK,OAAA,GAAAhB,GAAA,CAAAhD,SAAA,CAAA2B,GAAA,+BAAAqC,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAAhB,GAAA,CAAAhD,SAAA,CAAA2B,GAAA,+BAAAqC,OAAA,CAAAF,OAAA,EAA8E;UAUpFjF,EAAA,CAAAI,SAAA,EAA2C;UAA3CJ,EAAA,CAAA8E,UAAA,aAAAX,GAAA,CAAAhD,SAAA,CAAA6D,OAAA,IAAAb,GAAA,CAAAnD,SAAA,CAA2C;UACtChB,EAAA,CAAAI,SAAA,EAAe;UAAfJ,EAAA,CAAA8E,UAAA,SAAAX,GAAA,CAAAnD,SAAA,CAAe;UACfhB,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAA8E,UAAA,UAAAX,GAAA,CAAAnD,SAAA,CAAgB;UAC3BhB,EAAA,CAAAI,SAAA,EACF;UADEJ,EAAA,CAAAK,kBAAA,MAAA8D,GAAA,CAAAnD,SAAA,oCACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}