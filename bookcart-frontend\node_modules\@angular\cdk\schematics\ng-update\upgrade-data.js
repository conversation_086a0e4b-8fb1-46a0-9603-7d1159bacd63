"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getVersionUpgradeData = exports.cdkUpgradeData = void 0;
const version_changes_1 = require("../update-tool/version-changes");
const data_1 = require("./data");
/** Upgrade data for the Angular CDK. */
exports.cdkUpgradeData = {
    attributeSelectors: data_1.attributeSelectors,
    classNames: data_1.classNames,
    constructorChecks: data_1.constructorChecks,
    cssSelectors: data_1.cssSelectors,
    elementSelectors: data_1.elementSelectors,
    inputNames: data_1.inputNames,
    methodCallChecks: data_1.methodCallChecks,
    outputNames: data_1.outputNames,
    propertyNames: data_1.propertyNames,
    symbolRemoval: data_1.symbolRemoval,
};
/**
 * Gets the reduced upgrade data for the specified data key. The function reads out the
 * target version and upgrade data object from the migration and resolves the specified
 * data portion that is specifically tied to the target version.
 */
function getVersionUpgradeData(migration, dataName) {
    if (migration.targetVersion === null) {
        return [];
    }
    // Note that below we need to cast to `unknown` first TS doesn't infer the type of T correctly.
    return (0, version_changes_1.getChangesForTarget)(migration.targetVersion, migration.upgradeData[dataName]);
}
exports.getVersionUpgradeData = getVersionUpgradeData;
//# sourceMappingURL=data:application/json;base64,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