/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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