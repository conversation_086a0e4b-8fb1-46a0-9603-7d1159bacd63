{"version": 3, "file": "bottom-sheet.mjs", "sources": ["../../../../../../src/material/bottom-sheet/bottom-sheet-animations.ts", "../../../../../../src/material/bottom-sheet/bottom-sheet-container.ts", "../../../../../../src/material/bottom-sheet/bottom-sheet-container.html", "../../../../../../src/material/bottom-sheet/bottom-sheet-config.ts", "../../../../../../src/material/bottom-sheet/bottom-sheet-ref.ts", "../../../../../../src/material/bottom-sheet/bottom-sheet.ts", "../../../../../../src/material/bottom-sheet/bottom-sheet-module.ts", "../../../../../../src/material/bottom-sheet/bottom-sheet_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {\n  animate,\n  state,\n  style,\n  transition,\n  trigger,\n  AnimationTriggerMetadata,\n  group,\n  query,\n  animateChild,\n} from '@angular/animations';\nimport {AnimationCurves, AnimationDurations} from '@angular/material/core';\n\n/** Animations used by the Material bottom sheet. */\nexport const matBottomSheetAnimations: {\n  readonly bottomSheetState: AnimationTriggerMetadata;\n} = {\n  /** Animation that shows and hides a bottom sheet. */\n  bottomSheetState: trigger('state', [\n    state('void, hidden', style({transform: 'translateY(100%)'})),\n    state('visible', style({transform: 'translateY(0%)'})),\n    transition(\n      'visible => void, visible => hidden',\n      group([\n        animate(`${AnimationDurations.COMPLEX} ${AnimationCurves.ACCELERATION_CURVE}`),\n        query('@*', animateChild(), {optional: true}),\n      ]),\n    ),\n    transition(\n      'void => visible',\n      group([\n        animate(`${AnimationDurations.EXITING} ${AnimationCurves.DECELERATION_CURVE}`),\n        query('@*', animateChild(), {optional: true}),\n      ]),\n    ),\n  ]),\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationEvent} from '@angular/animations';\nimport {CdkDialogContainer, DialogConfig} from '@angular/cdk/dialog';\nimport {FocusMonitor, FocusTrapFactory, InteractivityChecker} from '@angular/cdk/a11y';\nimport {BreakpointObserver, Breakpoints} from '@angular/cdk/layout';\nimport {OverlayRef} from '@angular/cdk/overlay';\nimport {DOCUMENT} from '@angular/common';\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  ElementRef,\n  EventEmitter,\n  Inject,\n  NgZone,\n  OnDestroy,\n  Optional,\n  ViewEncapsulation,\n} from '@angular/core';\nimport {Subscription} from 'rxjs';\nimport {matBottomSheetAnimations} from './bottom-sheet-animations';\nimport {CdkPortalOutlet} from '@angular/cdk/portal';\n\n/**\n * Internal component that wraps user-provided bottom sheet content.\n * @docs-private\n */\n@Component({\n  selector: 'mat-bottom-sheet-container',\n  templateUrl: 'bottom-sheet-container.html',\n  styleUrl: 'bottom-sheet-container.css',\n  // In Ivy embedded views will be change detected from their declaration place, rather than where\n  // they were stamped out. This means that we can't have the bottom sheet container be OnPush,\n  // because it might cause the sheets that were opened from a template not to be out of date.\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  encapsulation: ViewEncapsulation.None,\n  animations: [matBottomSheetAnimations.bottomSheetState],\n  host: {\n    'class': 'mat-bottom-sheet-container',\n    'tabindex': '-1',\n    '[attr.role]': '_config.role',\n    '[attr.aria-modal]': '_config.ariaModal',\n    '[attr.aria-label]': '_config.ariaLabel',\n    '[@state]': '_animationState',\n    '(@state.start)': '_onAnimationStart($event)',\n    '(@state.done)': '_onAnimationDone($event)',\n  },\n  standalone: true,\n  imports: [CdkPortalOutlet],\n})\nexport class MatBottomSheetContainer extends CdkDialogContainer implements OnDestroy {\n  private _breakpointSubscription: Subscription;\n\n  /** The state of the bottom sheet animations. */\n  _animationState: 'void' | 'visible' | 'hidden' = 'void';\n\n  /** Emits whenever the state of the animation changes. */\n  _animationStateChanged = new EventEmitter<AnimationEvent>();\n\n  /** Whether the component has been destroyed. */\n  private _destroyed: boolean;\n\n  constructor(\n    elementRef: ElementRef,\n    focusTrapFactory: FocusTrapFactory,\n    @Optional() @Inject(DOCUMENT) document: any,\n    config: DialogConfig,\n    checker: InteractivityChecker,\n    ngZone: NgZone,\n    overlayRef: OverlayRef,\n    breakpointObserver: BreakpointObserver,\n    focusMonitor?: FocusMonitor,\n  ) {\n    super(\n      elementRef,\n      focusTrapFactory,\n      document,\n      config,\n      checker,\n      ngZone,\n      overlayRef,\n      focusMonitor,\n    );\n\n    this._breakpointSubscription = breakpointObserver\n      .observe([Breakpoints.Medium, Breakpoints.Large, Breakpoints.XLarge])\n      .subscribe(() => {\n        this._toggleClass(\n          'mat-bottom-sheet-container-medium',\n          breakpointObserver.isMatched(Breakpoints.Medium),\n        );\n        this._toggleClass(\n          'mat-bottom-sheet-container-large',\n          breakpointObserver.isMatched(Breakpoints.Large),\n        );\n        this._toggleClass(\n          'mat-bottom-sheet-container-xlarge',\n          breakpointObserver.isMatched(Breakpoints.XLarge),\n        );\n      });\n  }\n\n  /** Begin animation of bottom sheet entrance into view. */\n  enter(): void {\n    if (!this._destroyed) {\n      this._animationState = 'visible';\n      this._changeDetectorRef.detectChanges();\n    }\n  }\n\n  /** Begin animation of the bottom sheet exiting from view. */\n  exit(): void {\n    if (!this._destroyed) {\n      this._animationState = 'hidden';\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n\n  override ngOnDestroy() {\n    super.ngOnDestroy();\n    this._breakpointSubscription.unsubscribe();\n    this._destroyed = true;\n  }\n\n  _onAnimationDone(event: AnimationEvent) {\n    if (event.toState === 'visible') {\n      this._trapFocus();\n    }\n\n    this._animationStateChanged.emit(event);\n  }\n\n  _onAnimationStart(event: AnimationEvent) {\n    this._animationStateChanged.emit(event);\n  }\n\n  protected override _captureInitialFocus(): void {}\n\n  private _toggleClass(cssClass: string, add: boolean) {\n    this._elementRef.nativeElement.classList.toggle(cssClass, add);\n  }\n}\n", "<ng-template cdkPortalOutlet></ng-template>\r\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Direction} from '@angular/cdk/bidi';\nimport {ScrollStrategy} from '@angular/cdk/overlay';\nimport {InjectionToken, ViewContainerRef} from '@angular/core';\n\n/** Options for where to set focus to automatically on dialog open */\nexport type AutoFocusTarget = 'dialog' | 'first-tabbable' | 'first-heading';\n\n/** Injection token that can be used to access the data that was passed in to a bottom sheet. */\nexport const MAT_BOTTOM_SHEET_DATA = new InjectionToken<any>('MatBottomSheetData');\n\n/**\n * Configuration used when opening a bottom sheet.\n */\nexport class MatBottomSheetConfig<D = any> {\n  /** The view container to place the overlay for the bottom sheet into. */\n  viewContainerRef?: ViewContainerRef;\n\n  /** Extra CSS classes to be added to the bottom sheet container. */\n  panelClass?: string | string[];\n\n  /** Text layout direction for the bottom sheet. */\n  direction?: Direction;\n\n  /** Data being injected into the child component. */\n  data?: D | null = null;\n\n  /** Whether the bottom sheet has a backdrop. */\n  hasBackdrop?: boolean = true;\n\n  /** Custom class for the backdrop. */\n  backdropClass?: string;\n\n  /** Whether the user can use escape or clicking outside to close the bottom sheet. */\n  disableClose?: boolean = false;\n\n  /** Aria label to assign to the bottom sheet element. */\n  ariaLabel?: string | null = null;\n\n  /** Whether this is a modal bottom sheet. Used to set the `aria-modal` attribute. */\n  ariaModal?: boolean = true;\n\n  /**\n   * Whether the bottom sheet should close when the user goes backwards/forwards in history.\n   * Note that this usually doesn't include clicking on links (unless the user is using\n   * the `HashLocationStrategy`).\n   */\n  closeOnNavigation?: boolean = true;\n\n  // Note that this is set to 'dialog' by default, because while the a11y recommendations\n  // are to focus the first focusable element, doing so prevents screen readers from reading out the\n  // rest of the bottom sheet content.\n  /**\n   * Where the bottom sheet should focus on open.\n   * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n   * AutoFocusTarget instead.\n   */\n  autoFocus?: AutoFocusTarget | string | boolean = 'dialog';\n\n  /**\n   * Whether the bottom sheet should restore focus to the\n   * previously-focused element, after it's closed.\n   */\n  restoreFocus?: boolean = true;\n\n  /** Scroll strategy to be used for the bottom sheet. */\n  scrollStrategy?: ScrollStrategy;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ComponentRef} from '@angular/core';\nimport {DialogRef} from '@angular/cdk/dialog';\nimport {ESCAPE, hasModifierKey} from '@angular/cdk/keycodes';\nimport {merge, Observable, Subject} from 'rxjs';\nimport {filter, take} from 'rxjs/operators';\nimport {MatBottomSheetConfig} from './bottom-sheet-config';\nimport {MatBottomSheetContainer} from './bottom-sheet-container';\n\n/**\n * Reference to a bottom sheet dispatched from the bottom sheet service.\n */\nexport class MatBottomSheetRef<T = any, R = any> {\n  /** Instance of the component making up the content of the bottom sheet. */\n  get instance(): T {\n    return this._ref.componentInstance!;\n  }\n\n  /**\n   * `ComponentRef` of the component opened into the bottom sheet. Will be\n   * null when the bottom sheet is opened using a `TemplateRef`.\n   */\n  get componentRef(): ComponentRef<T> | null {\n    return this._ref.componentRef;\n  }\n\n  /**\n   * Instance of the component into which the bottom sheet content is projected.\n   * @docs-private\n   */\n  containerInstance: MatBottomSheetContainer;\n\n  /** Whether the user is allowed to close the bottom sheet. */\n  disableClose: boolean | undefined;\n\n  /** Subject for notifying the user that the bottom sheet has opened and appeared. */\n  private readonly _afterOpened = new Subject<void>();\n\n  /** Result to be passed down to the `afterDismissed` stream. */\n  private _result: R | undefined;\n\n  /** Handle to the timeout that's running as a fallback in case the exit animation doesn't fire. */\n  private _closeFallbackTimeout: number;\n\n  constructor(\n    private _ref: DialogRef<R, T>,\n    config: MatBottomSheetConfig,\n    containerInstance: MatBottomSheetContainer,\n  ) {\n    this.containerInstance = containerInstance;\n    this.disableClose = config.disableClose;\n\n    // Emit when opening animation completes\n    containerInstance._animationStateChanged\n      .pipe(\n        filter(event => event.phaseName === 'done' && event.toState === 'visible'),\n        take(1),\n      )\n      .subscribe(() => {\n        this._afterOpened.next();\n        this._afterOpened.complete();\n      });\n\n    // Dispose overlay when closing animation is complete\n    containerInstance._animationStateChanged\n      .pipe(\n        filter(event => event.phaseName === 'done' && event.toState === 'hidden'),\n        take(1),\n      )\n      .subscribe(() => {\n        clearTimeout(this._closeFallbackTimeout);\n        this._ref.close(this._result);\n      });\n\n    _ref.overlayRef.detachments().subscribe(() => {\n      this._ref.close(this._result);\n    });\n\n    merge(\n      this.backdropClick(),\n      this.keydownEvents().pipe(filter(event => event.keyCode === ESCAPE)),\n    ).subscribe(event => {\n      if (\n        !this.disableClose &&\n        (event.type !== 'keydown' || !hasModifierKey(event as KeyboardEvent))\n      ) {\n        event.preventDefault();\n        this.dismiss();\n      }\n    });\n  }\n\n  /**\n   * Dismisses the bottom sheet.\n   * @param result Data to be passed back to the bottom sheet opener.\n   */\n  dismiss(result?: R): void {\n    if (!this.containerInstance) {\n      return;\n    }\n\n    // Transition the backdrop in parallel to the bottom sheet.\n    this.containerInstance._animationStateChanged\n      .pipe(\n        filter(event => event.phaseName === 'start'),\n        take(1),\n      )\n      .subscribe(event => {\n        // The logic that disposes of the overlay depends on the exit animation completing, however\n        // it isn't guaranteed if the parent view is destroyed while it's running. Add a fallback\n        // timeout which will clean everything up if the animation hasn't fired within the specified\n        // amount of time plus 100ms. We don't need to run this outside the NgZone, because for the\n        // vast majority of cases the timeout will have been cleared before it has fired.\n        this._closeFallbackTimeout = setTimeout(() => {\n          this._ref.close(this._result);\n        }, event.totalTime + 100);\n\n        this._ref.overlayRef.detachBackdrop();\n      });\n\n    this._result = result;\n    this.containerInstance.exit();\n    this.containerInstance = null!;\n  }\n\n  /** Gets an observable that is notified when the bottom sheet is finished closing. */\n  afterDismissed(): Observable<R | undefined> {\n    return this._ref.closed;\n  }\n\n  /** Gets an observable that is notified when the bottom sheet has opened and appeared. */\n  afterOpened(): Observable<void> {\n    return this._afterOpened;\n  }\n\n  /**\n   * Gets an observable that emits when the overlay's backdrop has been clicked.\n   */\n  backdropClick(): Observable<MouseEvent> {\n    return this._ref.backdropClick;\n  }\n\n  /**\n   * Gets an observable that emits when keydown events are targeted on the overlay.\n   */\n  keydownEvents(): Observable<KeyboardEvent> {\n    return this._ref.keydownEvents;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Dialog} from '@angular/cdk/dialog';\nimport {Overlay} from '@angular/cdk/overlay';\nimport {ComponentType} from '@angular/cdk/portal';\nimport {\n  Injectable,\n  Optional,\n  SkipSelf,\n  TemplateRef,\n  InjectionToken,\n  Inject,\n  OnDestroy,\n  Injector,\n} from '@angular/core';\nimport {MAT_BOTTOM_SHEET_DATA, MatBottomSheetConfig} from './bottom-sheet-config';\nimport {MatBottomSheetContainer} from './bottom-sheet-container';\nimport {MatBottomSheetRef} from './bottom-sheet-ref';\n\n/** Injection token that can be used to specify default bottom sheet options. */\nexport const MAT_BOTTOM_SHEET_DEFAULT_OPTIONS = new InjectionToken<MatBottomSheetConfig>(\n  'mat-bottom-sheet-default-options',\n);\n\n/**\n * Service to trigger Material Design bottom sheets.\n */\n@Injectable({providedIn: 'root'})\nexport class MatBottomSheet implements OnDestroy {\n  private _bottomSheetRefAtThisLevel: MatBottomSheetRef<any> | null = null;\n  private _dialog: Dialog;\n\n  /** Reference to the currently opened bottom sheet. */\n  get _openedBottomSheetRef(): MatBottomSheetRef<any> | null {\n    const parent = this._parentBottomSheet;\n    return parent ? parent._openedBottomSheetRef : this._bottomSheetRefAtThisLevel;\n  }\n\n  set _openedBottomSheetRef(value: MatBottomSheetRef<any> | null) {\n    if (this._parentBottomSheet) {\n      this._parentBottomSheet._openedBottomSheetRef = value;\n    } else {\n      this._bottomSheetRefAtThisLevel = value;\n    }\n  }\n\n  constructor(\n    private _overlay: Overlay,\n    injector: Injector,\n    @Optional() @SkipSelf() private _parentBottomSheet: MatBottomSheet,\n    @Optional()\n    @Inject(MAT_BOTTOM_SHEET_DEFAULT_OPTIONS)\n    private _defaultOptions?: MatBottomSheetConfig,\n  ) {\n    this._dialog = injector.get(Dialog);\n  }\n\n  /**\n   * Opens a bottom sheet containing the given component.\n   * @param component Type of the component to load into the bottom sheet.\n   * @param config Extra configuration options.\n   * @returns Reference to the newly-opened bottom sheet.\n   */\n  open<T, D = any, R = any>(\n    component: ComponentType<T>,\n    config?: MatBottomSheetConfig<D>,\n  ): MatBottomSheetRef<T, R>;\n\n  /**\n   * Opens a bottom sheet containing the given template.\n   * @param template TemplateRef to instantiate as the bottom sheet content.\n   * @param config Extra configuration options.\n   * @returns Reference to the newly-opened bottom sheet.\n   */\n  open<T, D = any, R = any>(\n    template: TemplateRef<T>,\n    config?: MatBottomSheetConfig<D>,\n  ): MatBottomSheetRef<T, R>;\n\n  open<T, D = any, R = any>(\n    componentOrTemplateRef: ComponentType<T> | TemplateRef<T>,\n    config?: MatBottomSheetConfig<D>,\n  ): MatBottomSheetRef<T, R> {\n    const _config = {...(this._defaultOptions || new MatBottomSheetConfig()), ...config};\n    let ref: MatBottomSheetRef<T, R>;\n\n    this._dialog.open<R, D, T>(componentOrTemplateRef, {\n      ..._config,\n      // Disable closing since we need to sync it up to the animation ourselves.\n      disableClose: true,\n      // Disable closing on detachments so that we can sync up the animation.\n      closeOnOverlayDetachments: false,\n      maxWidth: '100%',\n      container: MatBottomSheetContainer,\n      scrollStrategy: _config.scrollStrategy || this._overlay.scrollStrategies.block(),\n      positionStrategy: this._overlay.position().global().centerHorizontally().bottom('0'),\n      templateContext: () => ({bottomSheetRef: ref}),\n      providers: (cdkRef, _cdkConfig, container) => {\n        ref = new MatBottomSheetRef(cdkRef, _config, container as MatBottomSheetContainer);\n        return [\n          {provide: MatBottomSheetRef, useValue: ref},\n          {provide: MAT_BOTTOM_SHEET_DATA, useValue: _config.data},\n        ];\n      },\n    });\n\n    // When the bottom sheet is dismissed, clear the reference to it.\n    ref!.afterDismissed().subscribe(() => {\n      // Clear the bottom sheet ref if it hasn't already been replaced by a newer one.\n      if (this._openedBottomSheetRef === ref) {\n        this._openedBottomSheetRef = null;\n      }\n    });\n\n    if (this._openedBottomSheetRef) {\n      // If a bottom sheet is already in view, dismiss it and enter the\n      // new bottom sheet after exit animation is complete.\n      this._openedBottomSheetRef.afterDismissed().subscribe(() => ref.containerInstance?.enter());\n      this._openedBottomSheetRef.dismiss();\n    } else {\n      // If no bottom sheet is in view, enter the new bottom sheet.\n      ref!.containerInstance.enter();\n    }\n\n    this._openedBottomSheetRef = ref!;\n    return ref!;\n  }\n\n  /**\n   * Dismisses the currently-visible bottom sheet.\n   * @param result Data to pass to the bottom sheet instance.\n   */\n  dismiss<R = any>(result?: R): void {\n    if (this._openedBottomSheetRef) {\n      this._openedBottomSheetRef.dismiss(result);\n    }\n  }\n\n  ngOnDestroy() {\n    if (this._bottomSheetRefAtThisLevel) {\n      this._bottomSheetRefAtThisLevel.dismiss();\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {DialogModule} from '@angular/cdk/dialog';\nimport {PortalModule} from '@angular/cdk/portal';\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '@angular/material/core';\nimport {MatBottomSheetContainer} from './bottom-sheet-container';\nimport {MatBottomSheet} from './bottom-sheet';\n\n@NgModule({\n  imports: [DialogModule, MatCommonModule, PortalModule, MatBottomSheetContainer],\n  exports: [MatBottomSheetContainer, MatCommonModule],\n  providers: [MatBottomSheet],\n})\nexport class MatBottomSheetModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAoBA;AACa,MAAA,wBAAwB,GAEjC;;AAEF,IAAA,gBAAgB,EAAE,OAAO,CAAC,OAAO,EAAE;QACjC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,EAAC,SAAS,EAAE,kBAAkB,EAAC,CAAC,CAAC;QAC7D,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAC,CAAC;AACtD,QAAA,UAAU,CACR,oCAAoC,EACpC,KAAK,CAAC;YACJ,OAAO,CAAC,CAAG,EAAA,kBAAkB,CAAC,OAAO,IAAI,eAAe,CAAC,kBAAkB,CAAA,CAAE,CAAC;YAC9E,KAAK,CAAC,IAAI,EAAE,YAAY,EAAE,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAC9C,SAAA,CAAC,CACH;AACD,QAAA,UAAU,CACR,iBAAiB,EACjB,KAAK,CAAC;YACJ,OAAO,CAAC,CAAG,EAAA,kBAAkB,CAAC,OAAO,IAAI,eAAe,CAAC,kBAAkB,CAAA,CAAE,CAAC;YAC9E,KAAK,CAAC,IAAI,EAAE,YAAY,EAAE,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAC9C,SAAA,CAAC,CACH;KACF,CAAC;;;ACbJ;;;AAGG;AAyBG,MAAO,uBAAwB,SAAQ,kBAAkB,CAAA;AAY7D,IAAA,WAAA,CACE,UAAsB,EACtB,gBAAkC,EACJ,QAAa,EAC3C,MAAoB,EACpB,OAA6B,EAC7B,MAAc,EACd,UAAsB,EACtB,kBAAsC,EACtC,YAA2B,EAAA;AAE3B,QAAA,KAAK,CACH,UAAU,EACV,gBAAgB,EAChB,QAAQ,EACR,MAAM,EACN,OAAO,EACP,MAAM,EACN,UAAU,EACV,YAAY,CACb,CAAC;;QA5BJ,IAAe,CAAA,eAAA,GAAkC,MAAM,CAAC;;AAGxD,QAAA,IAAA,CAAA,sBAAsB,GAAG,IAAI,YAAY,EAAkB,CAAC;QA2B1D,IAAI,CAAC,uBAAuB,GAAG,kBAAkB;AAC9C,aAAA,OAAO,CAAC,CAAC,WAAW,CAAC,MAAM,EAAE,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;aACpE,SAAS,CAAC,MAAK;AACd,YAAA,IAAI,CAAC,YAAY,CACf,mCAAmC,EACnC,kBAAkB,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CACjD,CAAC;AACF,YAAA,IAAI,CAAC,YAAY,CACf,kCAAkC,EAClC,kBAAkB,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAChD,CAAC;AACF,YAAA,IAAI,CAAC,YAAY,CACf,mCAAmC,EACnC,kBAAkB,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CACjD,CAAC;AACJ,SAAC,CAAC,CAAC;KACN;;IAGD,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACpB,YAAA,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;AACjC,YAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC;SACzC;KACF;;IAGD,IAAI,GAAA;AACF,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACpB,YAAA,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;AAChC,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;SACxC;KACF;IAEQ,WAAW,GAAA;QAClB,KAAK,CAAC,WAAW,EAAE,CAAC;AACpB,QAAA,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,CAAC;AAC3C,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;KACxB;AAED,IAAA,gBAAgB,CAAC,KAAqB,EAAA;AACpC,QAAA,IAAI,KAAK,CAAC,OAAO,KAAK,SAAS,EAAE;YAC/B,IAAI,CAAC,UAAU,EAAE,CAAC;SACnB;AAED,QAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACzC;AAED,IAAA,iBAAiB,CAAC,KAAqB,EAAA;AACrC,QAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACzC;AAEkB,IAAA,oBAAoB,MAAW;IAE1C,YAAY,CAAC,QAAgB,EAAE,GAAY,EAAA;AACjD,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;KAChE;AA1FU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,uBAAuB,4EAeZ,QAAQ,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,YAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,oBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,kBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,YAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAfnB,uBAAuB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,4BAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,UAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,cAAA,EAAA,2BAAA,EAAA,aAAA,EAAA,0BAAA,EAAA,EAAA,UAAA,EAAA,EAAA,WAAA,EAAA,cAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,EAAA,cAAA,EAAA,4BAAA,EAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECzDpC,iDACA,EDsDY,MAAA,EAAA,CAAA,gsCAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,eAAe,mIAZb,CAAC,wBAAwB,CAAC,gBAAgB,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAc5C,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBAxBnC,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,4BAA4B,EAOrB,eAAA,EAAA,uBAAuB,CAAC,OAAO,iBACjC,iBAAiB,CAAC,IAAI,EAAA,UAAA,EACzB,CAAC,wBAAwB,CAAC,gBAAgB,CAAC,EACjD,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,4BAA4B;AACrC,wBAAA,UAAU,EAAE,IAAI;AAChB,wBAAA,aAAa,EAAE,cAAc;AAC7B,wBAAA,mBAAmB,EAAE,mBAAmB;AACxC,wBAAA,mBAAmB,EAAE,mBAAmB;AACxC,wBAAA,UAAU,EAAE,iBAAiB;AAC7B,wBAAA,gBAAgB,EAAE,2BAA2B;AAC7C,wBAAA,eAAe,EAAE,0BAA0B;AAC5C,qBAAA,EAAA,UAAA,EACW,IAAI,EAAA,OAAA,EACP,CAAC,eAAe,CAAC,EAAA,QAAA,EAAA,iDAAA,EAAA,MAAA,EAAA,CAAA,gsCAAA,CAAA,EAAA,CAAA;;0BAiBvB,QAAQ;;0BAAI,MAAM;2BAAC,QAAQ,CAAA;;;AEzDhC;MACa,qBAAqB,GAAG,IAAI,cAAc,CAAM,oBAAoB,EAAE;AAEnF;;AAEG;MACU,oBAAoB,CAAA;AAAjC,IAAA,WAAA,GAAA;;QAWE,IAAI,CAAA,IAAA,GAAc,IAAI,CAAC;;QAGvB,IAAW,CAAA,WAAA,GAAa,IAAI,CAAC;;QAM7B,IAAY,CAAA,YAAA,GAAa,KAAK,CAAC;;QAG/B,IAAS,CAAA,SAAA,GAAmB,IAAI,CAAC;;QAGjC,IAAS,CAAA,SAAA,GAAa,IAAI,CAAC;AAE3B;;;;AAIG;QACH,IAAiB,CAAA,iBAAA,GAAa,IAAI,CAAC;;;;AAKnC;;;;AAIG;QACH,IAAS,CAAA,SAAA,GAAwC,QAAQ,CAAC;AAE1D;;;AAGG;QACH,IAAY,CAAA,YAAA,GAAa,IAAI,CAAC;KAI/B;AAAA;;AC1DD;;AAEG;MACU,iBAAiB,CAAA;;AAE5B,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,iBAAkB,CAAC;KACrC;AAED;;;AAGG;AACH,IAAA,IAAI,YAAY,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC;KAC/B;AAoBD,IAAA,WAAA,CACU,IAAqB,EAC7B,MAA4B,EAC5B,iBAA0C,EAAA;QAFlC,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAiB;;AATd,QAAA,IAAA,CAAA,YAAY,GAAG,IAAI,OAAO,EAAQ,CAAC;AAalD,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAC3C,QAAA,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;;AAGxC,QAAA,iBAAiB,CAAC,sBAAsB;aACrC,IAAI,CACH,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,SAAS,KAAK,MAAM,IAAI,KAAK,CAAC,OAAO,KAAK,SAAS,CAAC,EAC1E,IAAI,CAAC,CAAC,CAAC,CACR;aACA,SAAS,CAAC,MAAK;AACd,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;AACzB,YAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;AAC/B,SAAC,CAAC,CAAC;;AAGL,QAAA,iBAAiB,CAAC,sBAAsB;aACrC,IAAI,CACH,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,SAAS,KAAK,MAAM,IAAI,KAAK,CAAC,OAAO,KAAK,QAAQ,CAAC,EACzE,IAAI,CAAC,CAAC,CAAC,CACR;aACA,SAAS,CAAC,MAAK;AACd,YAAA,YAAY,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACzC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAChC,SAAC,CAAC,CAAC;QAEL,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,MAAK;YAC3C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAChC,SAAC,CAAC,CAAC;AAEH,QAAA,KAAK,CACH,IAAI,CAAC,aAAa,EAAE,EACpB,IAAI,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,MAAM,CAAC,CAAC,CACrE,CAAC,SAAS,CAAC,KAAK,IAAG;YAClB,IACE,CAAC,IAAI,CAAC,YAAY;AAClB,iBAAC,KAAK,CAAC,IAAI,KAAK,SAAS,IAAI,CAAC,cAAc,CAAC,KAAsB,CAAC,CAAC,EACrE;gBACA,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,IAAI,CAAC,OAAO,EAAE,CAAC;aAChB;AACH,SAAC,CAAC,CAAC;KACJ;AAED;;;AAGG;AACH,IAAA,OAAO,CAAC,MAAU,EAAA;AAChB,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC3B,OAAO;SACR;;QAGD,IAAI,CAAC,iBAAiB,CAAC,sBAAsB;AAC1C,aAAA,IAAI,CACH,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,SAAS,KAAK,OAAO,CAAC,EAC5C,IAAI,CAAC,CAAC,CAAC,CACR;aACA,SAAS,CAAC,KAAK,IAAG;;;;;;AAMjB,YAAA,IAAI,CAAC,qBAAqB,GAAG,UAAU,CAAC,MAAK;gBAC3C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAChC,aAAC,EAAE,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC;AAE1B,YAAA,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC;AACxC,SAAC,CAAC,CAAC;AAEL,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;AACtB,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;AAC9B,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAK,CAAC;KAChC;;IAGD,cAAc,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;KACzB;;IAGD,WAAW,GAAA;QACT,OAAO,IAAI,CAAC,YAAY,CAAC;KAC1B;AAED;;AAEG;IACH,aAAa,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;KAChC;AAED;;AAEG;IACH,aAAa,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;KAChC;AACF;;AClID;MACa,gCAAgC,GAAG,IAAI,cAAc,CAChE,kCAAkC,EAClC;AAEF;;AAEG;MAEU,cAAc,CAAA;;AAKzB,IAAA,IAAI,qBAAqB,GAAA;AACvB,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC;AACvC,QAAA,OAAO,MAAM,GAAG,MAAM,CAAC,qBAAqB,GAAG,IAAI,CAAC,0BAA0B,CAAC;KAChF;IAED,IAAI,qBAAqB,CAAC,KAAoC,EAAA;AAC5D,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;AAC3B,YAAA,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,GAAG,KAAK,CAAC;SACvD;aAAM;AACL,YAAA,IAAI,CAAC,0BAA0B,GAAG,KAAK,CAAC;SACzC;KACF;AAED,IAAA,WAAA,CACU,QAAiB,EACzB,QAAkB,EACc,kBAAkC,EAG1D,eAAsC,EAAA;QALtC,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAS;QAEO,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAgB;QAG1D,IAAe,CAAA,eAAA,GAAf,eAAe,CAAuB;QAvBxC,IAA0B,CAAA,0BAAA,GAAkC,IAAI,CAAC;QAyBvE,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;KACrC;IAwBD,IAAI,CACF,sBAAyD,EACzD,MAAgC,EAAA;AAEhC,QAAA,MAAM,OAAO,GAAG,EAAC,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,oBAAoB,EAAE,CAAC,EAAE,GAAG,MAAM,EAAC,CAAC;AACrF,QAAA,IAAI,GAA4B,CAAC;AAEjC,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAU,sBAAsB,EAAE;AACjD,YAAA,GAAG,OAAO;;AAEV,YAAA,YAAY,EAAE,IAAI;;AAElB,YAAA,yBAAyB,EAAE,KAAK;AAChC,YAAA,QAAQ,EAAE,MAAM;AAChB,YAAA,SAAS,EAAE,uBAAuB;AAClC,YAAA,cAAc,EAAE,OAAO,CAAC,cAAc,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,EAAE;AAChF,YAAA,gBAAgB,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,CAAC,kBAAkB,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC;YACpF,eAAe,EAAE,OAAO,EAAC,cAAc,EAAE,GAAG,EAAC,CAAC;YAC9C,SAAS,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,SAAS,KAAI;gBAC3C,GAAG,GAAG,IAAI,iBAAiB,CAAC,MAAM,EAAE,OAAO,EAAE,SAAoC,CAAC,CAAC;gBACnF,OAAO;AACL,oBAAA,EAAC,OAAO,EAAE,iBAAiB,EAAE,QAAQ,EAAE,GAAG,EAAC;oBAC3C,EAAC,OAAO,EAAE,qBAAqB,EAAE,QAAQ,EAAE,OAAO,CAAC,IAAI,EAAC;iBACzD,CAAC;aACH;AACF,SAAA,CAAC,CAAC;;AAGH,QAAA,GAAI,CAAC,cAAc,EAAE,CAAC,SAAS,CAAC,MAAK;;AAEnC,YAAA,IAAI,IAAI,CAAC,qBAAqB,KAAK,GAAG,EAAE;AACtC,gBAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;aACnC;AACH,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,IAAI,CAAC,qBAAqB,EAAE;;;AAG9B,YAAA,IAAI,CAAC,qBAAqB,CAAC,cAAc,EAAE,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,iBAAiB,EAAE,KAAK,EAAE,CAAC,CAAC;AAC5F,YAAA,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,CAAC;SACtC;aAAM;;AAEL,YAAA,GAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;SAChC;AAED,QAAA,IAAI,CAAC,qBAAqB,GAAG,GAAI,CAAC;AAClC,QAAA,OAAO,GAAI,CAAC;KACb;AAED;;;AAGG;AACH,IAAA,OAAO,CAAU,MAAU,EAAA;AACzB,QAAA,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAC9B,YAAA,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SAC5C;KACF;IAED,WAAW,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,0BAA0B,EAAE;AACnC,YAAA,IAAI,CAAC,0BAA0B,CAAC,OAAO,EAAE,CAAC;SAC3C;KACF;AAlHU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,4HAuBf,gCAAgC,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAvB/B,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,cADF,MAAM,EAAA,CAAA,CAAA,EAAA;;2FAClB,cAAc,EAAA,UAAA,EAAA,CAAA;kBAD1B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;0BAsB3B,QAAQ;;0BAAI,QAAQ;;0BACpB,QAAQ;;0BACR,MAAM;2BAAC,gCAAgC,CAAA;;;MCrC/B,oBAAoB,CAAA;8GAApB,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;+GAApB,oBAAoB,EAAA,OAAA,EAAA,CAJrB,YAAY,EAAE,eAAe,EAAE,YAAY,EAAE,uBAAuB,CAAA,EAAA,OAAA,EAAA,CACpE,uBAAuB,EAAE,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;+GAGvC,oBAAoB,EAAA,SAAA,EAFpB,CAAC,cAAc,CAAC,EAAA,OAAA,EAAA,CAFjB,YAAY,EAAE,eAAe,EAAE,YAAY,EAClB,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAGvC,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBALhC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,YAAY,EAAE,eAAe,EAAE,YAAY,EAAE,uBAAuB,CAAC;AAC/E,oBAAA,OAAO,EAAE,CAAC,uBAAuB,EAAE,eAAe,CAAC;oBACnD,SAAS,EAAE,CAAC,cAAc,CAAC;AAC5B,iBAAA,CAAA;;;ACnBD;;AAEG;;;;"}