/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// This default value is when checking the hierarchy for a token.
//
// It means both:
// - the token is not provided by the current injector,
// - only the element injectors should be checked (ie do not check module injectors
//
//          mod1
//         /
//       el1   mod2
//         \  /
//         el2
//
// When requesting el2.injector.get(token), we should check in the following order and return the
// first found value:
// - el2.injector.get(token, default)
// - el1.injector.get(token, NOT_FOUND_CHECK_ONLY_ELEMENT_INJECTOR) -> do not check the module
// - mod2.injector.get(token, default)
export const NOT_FOUND_CHECK_ONLY_ELEMENT_INJECTOR = {};
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicHJvdmlkZXJfZmxhZ3MuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9jb3JlL3NyYy92aWV3L3Byb3ZpZGVyX2ZsYWdzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7R0FNRztBQUVILGlFQUFpRTtBQUNqRSxFQUFFO0FBQ0YsaUJBQWlCO0FBQ2pCLHVEQUF1RDtBQUN2RCxtRkFBbUY7QUFDbkYsRUFBRTtBQUNGLGdCQUFnQjtBQUNoQixZQUFZO0FBQ1osbUJBQW1CO0FBQ25CLGVBQWU7QUFDZixjQUFjO0FBQ2QsRUFBRTtBQUNGLGlHQUFpRztBQUNqRyxxQkFBcUI7QUFDckIscUNBQXFDO0FBQ3JDLDhGQUE4RjtBQUM5RixzQ0FBc0M7QUFDdEMsTUFBTSxDQUFDLE1BQU0scUNBQXFDLEdBQUcsRUFBRSxDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5cbi8vIFRoaXMgZGVmYXVsdCB2YWx1ZSBpcyB3aGVuIGNoZWNraW5nIHRoZSBoaWVyYXJjaHkgZm9yIGEgdG9rZW4uXG4vL1xuLy8gSXQgbWVhbnMgYm90aDpcbi8vIC0gdGhlIHRva2VuIGlzIG5vdCBwcm92aWRlZCBieSB0aGUgY3VycmVudCBpbmplY3Rvcixcbi8vIC0gb25seSB0aGUgZWxlbWVudCBpbmplY3RvcnMgc2hvdWxkIGJlIGNoZWNrZWQgKGllIGRvIG5vdCBjaGVjayBtb2R1bGUgaW5qZWN0b3JzXG4vL1xuLy8gICAgICAgICAgbW9kMVxuLy8gICAgICAgICAvXG4vLyAgICAgICBlbDEgICBtb2QyXG4vLyAgICAgICAgIFxcICAvXG4vLyAgICAgICAgIGVsMlxuLy9cbi8vIFdoZW4gcmVxdWVzdGluZyBlbDIuaW5qZWN0b3IuZ2V0KHRva2VuKSwgd2Ugc2hvdWxkIGNoZWNrIGluIHRoZSBmb2xsb3dpbmcgb3JkZXIgYW5kIHJldHVybiB0aGVcbi8vIGZpcnN0IGZvdW5kIHZhbHVlOlxuLy8gLSBlbDIuaW5qZWN0b3IuZ2V0KHRva2VuLCBkZWZhdWx0KVxuLy8gLSBlbDEuaW5qZWN0b3IuZ2V0KHRva2VuLCBOT1RfRk9VTkRfQ0hFQ0tfT05MWV9FTEVNRU5UX0lOSkVDVE9SKSAtPiBkbyBub3QgY2hlY2sgdGhlIG1vZHVsZVxuLy8gLSBtb2QyLmluamVjdG9yLmdldCh0b2tlbiwgZGVmYXVsdClcbmV4cG9ydCBjb25zdCBOT1RfRk9VTkRfQ0hFQ0tfT05MWV9FTEVNRU5UX0lOSkVDVE9SID0ge307XG4iXX0=