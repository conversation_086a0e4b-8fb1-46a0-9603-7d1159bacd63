/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { defaultEquals } from './equality';
import { throwInvalidWriteToSignalError } from './errors';
import { producerAccessed, producerIncrementEpoch, producerNotifyConsumers, producerUpdatesAllowed, REACTIVE_NODE, SIGNAL } from './graph';
/**
 * If set, called after `WritableSignal`s are updated.
 *
 * This hook can be used to achieve various effects, such as running effects synchronously as part
 * of setting a signal.
 */
let postSignalSetFn = null;
/**
 * Create a `Signal` that can be set or updated directly.
 */
export function createSignal(initialValue) {
    const node = Object.create(SIGNAL_NODE);
    node.value = initialValue;
    const getter = (() => {
        producerAccessed(node);
        return node.value;
    });
    getter[SIGNAL] = node;
    return getter;
}
export function setPostSignalSetFn(fn) {
    const prev = postSignalSetFn;
    postSignalSetFn = fn;
    return prev;
}
export function signalGetFn() {
    producerAccessed(this);
    return this.value;
}
export function signalSetFn(node, newValue) {
    if (!producerUpdatesAllowed()) {
        throwInvalidWriteToSignalError();
    }
    if (!node.equal(node.value, newValue)) {
        node.value = newValue;
        signalValueChanged(node);
    }
}
export function signalUpdateFn(node, updater) {
    if (!producerUpdatesAllowed()) {
        throwInvalidWriteToSignalError();
    }
    signalSetFn(node, updater(node.value));
}
// Note: Using an IIFE here to ensure that the spread assignment is not considered
// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.
// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.
export const SIGNAL_NODE = /* @__PURE__ */ (() => {
    return {
        ...REACTIVE_NODE,
        equal: defaultEquals,
        value: undefined,
    };
})();
function signalValueChanged(node) {
    node.version++;
    producerIncrementEpoch();
    producerNotifyConsumers(node);
    postSignalSetFn?.();
}
//# sourceMappingURL=data:application/json;base64,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