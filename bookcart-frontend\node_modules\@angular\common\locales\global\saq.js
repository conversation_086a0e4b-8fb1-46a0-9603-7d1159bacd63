/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

if (n === 1)
    return 1;
return 5;
}
    global.ng.common.locales['saq'] = ["saq",[["<PERSON>siran","<PERSON>ipa"],u,u],u,[["A","K","O","I","I","S","K"],["Are","Kun","Ong","Ine","Ile","Sap","Kwe"],["Mderot ee are","Mderot ee kuni","Mderot ee ong’wan","<PERSON>derot ee inet","<PERSON>derot ee ile","<PERSON><PERSON>ot ee sapa","<PERSON>derot ee kwe"],["Are","Kun","Ong","Ine","Ile","Sap","Kwe"]],u,[["O","W","O","O","I","I","S","I","S","T","T","T"],["Obo","Waa","Oku","Ong","Ime","Ile","Sap","Isi","Saa","Tom","Tob","Tow"],["Lapa le obo","Lapa le waare","Lapa le okuni","Lapa le ong’wan","Lapa le imet","Lapa le ile","Lapa le sapa","Lapa le isiet","Lapa le saal","Lapa le tomon","Lapa le tomon obo","Lapa le tomon waare"]],u,[["KK","BK"],u,["Kabla ya Christo","Baada ya Christo"]],0,[6,0],["dd/MM/y","d MMM y","d MMMM y","EEEE, d MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤#,##0.00","#E0"],"KES","Ksh","Njilingi eel Kenya",{"JPY":["JP¥","¥"],"KES":["Ksh"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    