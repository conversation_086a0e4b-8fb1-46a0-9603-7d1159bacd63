{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class BooksService {\n  constructor(http) {\n    this.http = http;\n    this.API_URL = 'http://localhost:5001/api';\n  }\n  // Get all books\n  getAllBooks() {\n    return this.http.get(`${this.API_URL}/books`);\n  }\n  // Get book by ID\n  getBookById(id) {\n    return this.http.get(`${this.API_URL}/books/${id}`);\n  }\n  // Search books\n  searchBooks(searchTerm) {\n    const params = new HttpParams().set('searchTerm', searchTerm);\n    return this.http.get(`${this.API_URL}/books/search`, {\n      params\n    });\n  }\n  // Get books by category\n  getBooksByCategory(categoryId) {\n    return this.http.get(`${this.API_URL}/books/category/${categoryId}`);\n  }\n  // Get all categories\n  getCategories() {\n    return this.http.get(`${this.API_URL}/categories`);\n  }\n  // Admin: Create book\n  createBook(book) {\n    return this.http.post(`${this.API_URL}/books`, book);\n  }\n  // Admin: Update book\n  updateBook(id, book) {\n    return this.http.put(`${this.API_URL}/books/${id}`, book);\n  }\n  // Admin: Delete book\n  deleteBook(id) {\n    return this.http.delete(`${this.API_URL}/books/${id}`);\n  }\n  static {\n    this.ɵfac = function BooksService_Factory(t) {\n      return new (t || BooksService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: BooksService,\n      factory: BooksService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BooksService", "constructor", "http", "API_URL", "getAllBooks", "get", "getBookById", "id", "searchBooks", "searchTerm", "params", "set", "getBooksByCategory", "categoryId", "getCategories", "createBook", "book", "post", "updateBook", "put", "deleteBook", "delete", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\BookCart\\bookcart-frontend\\src\\app\\shared\\services\\books.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable } from 'rxjs';\n\nexport interface Book {\n  bookId: number;\n  title: string;\n  author: string;\n  description: string;\n  price: number;\n  imageUrl?: string;\n  categoryId: number;\n  categoryName: string;\n  stockQuantity: number;\n  createdDate: string;\n  isActive: boolean;\n}\n\nexport interface Category {\n  categoryId: number;\n  categoryName: string;\n  description: string;\n}\n\nexport interface CreateBookRequest {\n  title: string;\n  author: string;\n  description: string;\n  price: number;\n  imageUrl?: string;\n  categoryId: number;\n  stockQuantity: number;\n}\n\nexport interface UpdateBookRequest {\n  title?: string;\n  author?: string;\n  description?: string;\n  price?: number;\n  imageUrl?: string;\n  categoryId?: number;\n  stockQuantity?: number;\n  isActive?: boolean;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class BooksService {\n  private readonly API_URL = 'http://localhost:5001/api';\n\n  constructor(private http: HttpClient) { }\n\n  // Get all books\n  getAllBooks(): Observable<Book[]> {\n    return this.http.get<Book[]>(`${this.API_URL}/books`);\n  }\n\n  // Get book by ID\n  getBookById(id: number): Observable<Book> {\n    return this.http.get<Book>(`${this.API_URL}/books/${id}`);\n  }\n\n  // Search books\n  searchBooks(searchTerm: string): Observable<Book[]> {\n    const params = new HttpParams().set('searchTerm', searchTerm);\n    return this.http.get<Book[]>(`${this.API_URL}/books/search`, { params });\n  }\n\n  // Get books by category\n  getBooksByCategory(categoryId: number): Observable<Book[]> {\n    return this.http.get<Book[]>(`${this.API_URL}/books/category/${categoryId}`);\n  }\n\n  // Get all categories\n  getCategories(): Observable<Category[]> {\n    return this.http.get<Category[]>(`${this.API_URL}/categories`);\n  }\n\n  // Admin: Create book\n  createBook(book: CreateBookRequest): Observable<Book> {\n    return this.http.post<Book>(`${this.API_URL}/books`, book);\n  }\n\n  // Admin: Update book\n  updateBook(id: number, book: UpdateBookRequest): Observable<Book> {\n    return this.http.put<Book>(`${this.API_URL}/books/${id}`, book);\n  }\n\n  // Admin: Delete book\n  deleteBook(id: number): Observable<void> {\n    return this.http.delete<void>(`${this.API_URL}/books/${id}`);\n  }\n}\n"], "mappings": "AACA,SAAqBA,UAAU,QAAQ,sBAAsB;;;AA+C7D,OAAM,MAAOC,YAAY;EAGvBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFP,KAAAC,OAAO,GAAG,2BAA2B;EAEd;EAExC;EACAC,WAAWA,CAAA;IACT,OAAO,IAAI,CAACF,IAAI,CAACG,GAAG,CAAS,GAAG,IAAI,CAACF,OAAO,QAAQ,CAAC;EACvD;EAEA;EACAG,WAAWA,CAACC,EAAU;IACpB,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAO,GAAG,IAAI,CAACF,OAAO,UAAUI,EAAE,EAAE,CAAC;EAC3D;EAEA;EACAC,WAAWA,CAACC,UAAkB;IAC5B,MAAMC,MAAM,GAAG,IAAIX,UAAU,EAAE,CAACY,GAAG,CAAC,YAAY,EAAEF,UAAU,CAAC;IAC7D,OAAO,IAAI,CAACP,IAAI,CAACG,GAAG,CAAS,GAAG,IAAI,CAACF,OAAO,eAAe,EAAE;MAAEO;IAAM,CAAE,CAAC;EAC1E;EAEA;EACAE,kBAAkBA,CAACC,UAAkB;IACnC,OAAO,IAAI,CAACX,IAAI,CAACG,GAAG,CAAS,GAAG,IAAI,CAACF,OAAO,mBAAmBU,UAAU,EAAE,CAAC;EAC9E;EAEA;EACAC,aAAaA,CAAA;IACX,OAAO,IAAI,CAACZ,IAAI,CAACG,GAAG,CAAa,GAAG,IAAI,CAACF,OAAO,aAAa,CAAC;EAChE;EAEA;EACAY,UAAUA,CAACC,IAAuB;IAChC,OAAO,IAAI,CAACd,IAAI,CAACe,IAAI,CAAO,GAAG,IAAI,CAACd,OAAO,QAAQ,EAAEa,IAAI,CAAC;EAC5D;EAEA;EACAE,UAAUA,CAACX,EAAU,EAAES,IAAuB;IAC5C,OAAO,IAAI,CAACd,IAAI,CAACiB,GAAG,CAAO,GAAG,IAAI,CAAChB,OAAO,UAAUI,EAAE,EAAE,EAAES,IAAI,CAAC;EACjE;EAEA;EACAI,UAAUA,CAACb,EAAU;IACnB,OAAO,IAAI,CAACL,IAAI,CAACmB,MAAM,CAAO,GAAG,IAAI,CAAClB,OAAO,UAAUI,EAAE,EAAE,CAAC;EAC9D;;;uBA5CWP,YAAY,EAAAsB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAZzB,YAAY;MAAA0B,OAAA,EAAZ1B,YAAY,CAAA2B,IAAA;MAAAC,UAAA,EAFX;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}