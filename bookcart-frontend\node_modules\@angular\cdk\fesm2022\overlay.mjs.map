{"version": 3, "file": "overlay.mjs", "sources": ["../../../../../../src/cdk/overlay/scroll/block-scroll-strategy.ts", "../../../../../../src/cdk/overlay/scroll/scroll-strategy.ts", "../../../../../../src/cdk/overlay/scroll/close-scroll-strategy.ts", "../../../../../../src/cdk/overlay/scroll/noop-scroll-strategy.ts", "../../../../../../src/cdk/overlay/position/scroll-clip.ts", "../../../../../../src/cdk/overlay/scroll/reposition-scroll-strategy.ts", "../../../../../../src/cdk/overlay/scroll/scroll-strategy-options.ts", "../../../../../../src/cdk/overlay/overlay-config.ts", "../../../../../../src/cdk/overlay/position/connected-position.ts", "../../../../../../src/cdk/overlay/dispatchers/base-overlay-dispatcher.ts", "../../../../../../src/cdk/overlay/dispatchers/overlay-keyboard-dispatcher.ts", "../../../../../../src/cdk/overlay/dispatchers/overlay-outside-click-dispatcher.ts", "../../../../../../src/cdk/overlay/overlay-container.ts", "../../../../../../src/cdk/overlay/overlay-ref.ts", "../../../../../../src/cdk/overlay/position/flexible-connected-position-strategy.ts", "../../../../../../src/cdk/overlay/position/global-position-strategy.ts", "../../../../../../src/cdk/overlay/position/overlay-position-builder.ts", "../../../../../../src/cdk/overlay/overlay.ts", "../../../../../../src/cdk/overlay/overlay-directives.ts", "../../../../../../src/cdk/overlay/overlay-module.ts", "../../../../../../src/cdk/overlay/fullscreen-overlay-container.ts", "../../../../../../src/cdk/overlay/overlay_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ScrollStrategy} from './scroll-strategy';\nimport {ViewportRuler} from '@angular/cdk/scrolling';\nimport {coerceCssPixelValue} from '@angular/cdk/coercion';\nimport {supportsScrollBehavior} from '@angular/cdk/platform';\n\nconst scrollBehaviorSupported = supportsScrollBehavior();\n\n/**\n * Strategy that will prevent the user from scrolling while the overlay is visible.\n */\nexport class BlockScrollStrategy implements ScrollStrategy {\n  private _previousHTMLStyles = {top: '', left: ''};\n  private _previousScrollPosition: {top: number; left: number};\n  private _isEnabled = false;\n  private _document: Document;\n\n  constructor(\n    private _viewportRuler: ViewportRuler,\n    document: any,\n  ) {\n    this._document = document;\n  }\n\n  /** Attaches this scroll strategy to an overlay. */\n  attach() {}\n\n  /** Blocks page-level scroll while the attached overlay is open. */\n  enable() {\n    if (this._canBeEnabled()) {\n      const root = this._document.documentElement!;\n\n      this._previousScrollPosition = this._viewportRuler.getViewportScrollPosition();\n\n      // Cache the previous inline styles in case the user had set them.\n      this._previousHTMLStyles.left = root.style.left || '';\n      this._previousHTMLStyles.top = root.style.top || '';\n\n      // Note: we're using the `html` node, instead of the `body`, because the `body` may\n      // have the user agent margin, whereas the `html` is guaranteed not to have one.\n      root.style.left = coerceCssPixelValue(-this._previousScrollPosition.left);\n      root.style.top = coerceCssPixelValue(-this._previousScrollPosition.top);\n      root.classList.add('cdk-global-scrollblock');\n      this._isEnabled = true;\n    }\n  }\n\n  /** Unblocks page-level scroll while the attached overlay is open. */\n  disable() {\n    if (this._isEnabled) {\n      const html = this._document.documentElement!;\n      const body = this._document.body!;\n      const htmlStyle = html.style;\n      const bodyStyle = body.style;\n      const previousHtmlScrollBehavior = htmlStyle.scrollBehavior || '';\n      const previousBodyScrollBehavior = bodyStyle.scrollBehavior || '';\n\n      this._isEnabled = false;\n\n      htmlStyle.left = this._previousHTMLStyles.left;\n      htmlStyle.top = this._previousHTMLStyles.top;\n      html.classList.remove('cdk-global-scrollblock');\n\n      // Disable user-defined smooth scrolling temporarily while we restore the scroll position.\n      // See https://developer.mozilla.org/en-US/docs/Web/CSS/scroll-behavior\n      // Note that we don't mutate the property if the browser doesn't support `scroll-behavior`,\n      // because it can throw off feature detections in `supportsScrollBehavior` which\n      // checks for `'scrollBehavior' in documentElement.style`.\n      if (scrollBehaviorSupported) {\n        htmlStyle.scrollBehavior = bodyStyle.scrollBehavior = 'auto';\n      }\n\n      window.scroll(this._previousScrollPosition.left, this._previousScrollPosition.top);\n\n      if (scrollBehaviorSupported) {\n        htmlStyle.scrollBehavior = previousHtmlScrollBehavior;\n        bodyStyle.scrollBehavior = previousBodyScrollBehavior;\n      }\n    }\n  }\n\n  private _canBeEnabled(): boolean {\n    // Since the scroll strategies can't be singletons, we have to use a global CSS class\n    // (`cdk-global-scrollblock`) to make sure that we don't try to disable global\n    // scrolling multiple times.\n    const html = this._document.documentElement!;\n\n    if (html.classList.contains('cdk-global-scrollblock') || this._isEnabled) {\n      return false;\n    }\n\n    const body = this._document.body;\n    const viewport = this._viewportRuler.getViewportSize();\n    return body.scrollHeight > viewport.height || body.scrollWidth > viewport.width;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport type {OverlayRef} from '../overlay-ref';\n\n/**\n * Describes a strategy that will be used by an overlay to handle scroll events while it is open.\n */\nexport interface ScrollStrategy {\n  /** Enable this scroll strategy (called when the attached overlay is attached to a portal). */\n  enable: () => void;\n\n  /** Disable this scroll strategy (called when the attached overlay is detached from a portal). */\n  disable: () => void;\n\n  /** Attaches this `ScrollStrategy` to an overlay. */\n  attach: (overlayRef: OverlayRef) => void;\n\n  /** Detaches the scroll strategy from the current overlay. */\n  detach?: () => void;\n}\n\n/**\n * Returns an error to be thrown when attempting to attach an already-attached scroll strategy.\n */\nexport function getMatScrollStrategyAlreadyAttachedError(): Error {\n  return Error(`Scroll strategy has already been attached.`);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {NgZone} from '@angular/core';\nimport {ScrollStrategy, getMatScrollStrategyAlreadyAttachedError} from './scroll-strategy';\nimport {Subscription} from 'rxjs';\nimport {ScrollDispatcher, ViewportRuler} from '@angular/cdk/scrolling';\nimport {filter} from 'rxjs/operators';\nimport type {OverlayRef} from '../overlay-ref';\n\n/**\n * Config options for the CloseScrollStrategy.\n */\nexport interface CloseScrollStrategyConfig {\n  /** Amount of pixels the user has to scroll before the overlay is closed. */\n  threshold?: number;\n}\n\n/**\n * Strategy that will close the overlay as soon as the user starts scrolling.\n */\nexport class CloseScrollStrategy implements ScrollStrategy {\n  private _scrollSubscription: Subscription | null = null;\n  private _overlayRef: OverlayRef;\n  private _initialScrollPosition: number;\n\n  constructor(\n    private _scrollDispatcher: ScrollDispatcher,\n    private _ngZone: NgZone,\n    private _viewportRuler: ViewportRuler,\n    private _config?: CloseScrollStrategyConfig,\n  ) {}\n\n  /** Attaches this scroll strategy to an overlay. */\n  attach(overlayRef: OverlayRef) {\n    if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatScrollStrategyAlreadyAttachedError();\n    }\n\n    this._overlayRef = overlayRef;\n  }\n\n  /** Enables the closing of the attached overlay on scroll. */\n  enable() {\n    if (this._scrollSubscription) {\n      return;\n    }\n\n    const stream = this._scrollDispatcher.scrolled(0).pipe(\n      filter(scrollable => {\n        return (\n          !scrollable ||\n          !this._overlayRef.overlayElement.contains(scrollable.getElementRef().nativeElement)\n        );\n      }),\n    );\n\n    if (this._config && this._config.threshold && this._config.threshold > 1) {\n      this._initialScrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n\n      this._scrollSubscription = stream.subscribe(() => {\n        const scrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n\n        if (Math.abs(scrollPosition - this._initialScrollPosition) > this._config!.threshold!) {\n          this._detach();\n        } else {\n          this._overlayRef.updatePosition();\n        }\n      });\n    } else {\n      this._scrollSubscription = stream.subscribe(this._detach);\n    }\n  }\n\n  /** Disables the closing the attached overlay on scroll. */\n  disable() {\n    if (this._scrollSubscription) {\n      this._scrollSubscription.unsubscribe();\n      this._scrollSubscription = null;\n    }\n  }\n\n  detach() {\n    this.disable();\n    this._overlayRef = null!;\n  }\n\n  /** Detaches the overlay ref and disables the scroll strategy. */\n  private _detach = () => {\n    this.disable();\n\n    if (this._overlayRef.hasAttached()) {\n      this._ngZone.run(() => this._overlayRef.detach());\n    }\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ScrollStrategy} from './scroll-strategy';\n\n/** Scroll strategy that doesn't do anything. */\nexport class NoopScrollStrategy implements ScrollStrategy {\n  /** Does nothing, as this scroll strategy is a no-op. */\n  enable() {}\n  /** Does nothing, as this scroll strategy is a no-op. */\n  disable() {}\n  /** Does nothing, as this scroll strategy is a no-op. */\n  attach() {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// TODO(jelbourn): move this to live with the rest of the scrolling code\n// TODO(jelbourn): someday replace this with IntersectionObservers\n\n/** Equivalent of `DOMRect` without some of the properties we don't care about. */\ntype Dimensions = Omit<DOMRect, 'x' | 'y' | 'toJSON'>;\n\n/**\n * Gets whether an element is scrolled outside of view by any of its parent scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is scrolled out of view\n * @docs-private\n */\nexport function isElementScrolledOutsideView(element: Dimensions, scrollContainers: Dimensions[]) {\n  return scrollContainers.some(containerBounds => {\n    const outsideAbove = element.bottom < containerBounds.top;\n    const outsideBelow = element.top > containerBounds.bottom;\n    const outsideLeft = element.right < containerBounds.left;\n    const outsideRight = element.left > containerBounds.right;\n\n    return outsideAbove || outsideBelow || outsideLeft || outsideRight;\n  });\n}\n\n/**\n * Gets whether an element is clipped by any of its scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is clipped\n * @docs-private\n */\nexport function isElementClippedByScrolling(element: Dimensions, scrollContainers: Dimensions[]) {\n  return scrollContainers.some(scrollContainerRect => {\n    const clippedAbove = element.top < scrollContainerRect.top;\n    const clippedBelow = element.bottom > scrollContainerRect.bottom;\n    const clippedLeft = element.left < scrollContainerRect.left;\n    const clippedRight = element.right > scrollContainerRect.right;\n\n    return clippedAbove || clippedBelow || clippedLeft || clippedRight;\n  });\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgZone} from '@angular/core';\nimport {Subscription} from 'rxjs';\nimport {ScrollStrategy, getMatScrollStrategyAlreadyAttachedError} from './scroll-strategy';\nimport {ScrollDispatcher, ViewportRuler} from '@angular/cdk/scrolling';\nimport {isElementScrolledOutsideView} from '../position/scroll-clip';\nimport type {OverlayRef} from '../overlay-ref';\n\n/**\n * Config options for the RepositionScrollStrategy.\n */\nexport interface RepositionScrollStrategyConfig {\n  /** Time in milliseconds to throttle the scroll events. */\n  scrollThrottle?: number;\n\n  /** Whether to close the overlay once the user has scrolled away completely. */\n  autoClose?: boolean;\n}\n\n/**\n * Strategy that will update the element position as the user is scrolling.\n */\nexport class RepositionScrollStrategy implements ScrollStrategy {\n  private _scrollSubscription: Subscription | null = null;\n  private _overlayRef: OverlayRef;\n\n  constructor(\n    private _scrollDispatcher: ScrollDispatcher,\n    private _viewportRuler: ViewportRuler,\n    private _ngZone: NgZone,\n    private _config?: RepositionScrollStrategyConfig,\n  ) {}\n\n  /** Attaches this scroll strategy to an overlay. */\n  attach(overlayRef: OverlayRef) {\n    if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatScrollStrategyAlreadyAttachedError();\n    }\n\n    this._overlayRef = overlayRef;\n  }\n\n  /** Enables repositioning of the attached overlay on scroll. */\n  enable() {\n    if (!this._scrollSubscription) {\n      const throttle = this._config ? this._config.scrollThrottle : 0;\n\n      this._scrollSubscription = this._scrollDispatcher.scrolled(throttle).subscribe(() => {\n        this._overlayRef.updatePosition();\n\n        // TODO(crisbeto): make `close` on by default once all components can handle it.\n        if (this._config && this._config.autoClose) {\n          const overlayRect = this._overlayRef.overlayElement.getBoundingClientRect();\n          const {width, height} = this._viewportRuler.getViewportSize();\n\n          // TODO(crisbeto): include all ancestor scroll containers here once\n          // we have a way of exposing the trigger element to the scroll strategy.\n          const parentRects = [{width, height, bottom: height, right: width, top: 0, left: 0}];\n\n          if (isElementScrolledOutsideView(overlayRect, parentRects)) {\n            this.disable();\n            this._ngZone.run(() => this._overlayRef.detach());\n          }\n        }\n      });\n    }\n  }\n\n  /** Disables repositioning of the attached overlay on scroll. */\n  disable() {\n    if (this._scrollSubscription) {\n      this._scrollSubscription.unsubscribe();\n      this._scrollSubscription = null;\n    }\n  }\n\n  detach() {\n    this.disable();\n    this._overlayRef = null!;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ScrollDispatcher, ViewportRuler} from '@angular/cdk/scrolling';\nimport {DOCUMENT} from '@angular/common';\nimport {Inject, Injectable, NgZone} from '@angular/core';\nimport {BlockScrollStrategy} from './block-scroll-strategy';\nimport {CloseScrollStrategy, CloseScrollStrategyConfig} from './close-scroll-strategy';\nimport {NoopScrollStrategy} from './noop-scroll-strategy';\nimport {\n  RepositionScrollStrategy,\n  RepositionScrollStrategyConfig,\n} from './reposition-scroll-strategy';\n\n/**\n * Options for how an overlay will handle scrolling.\n *\n * Users can provide a custom value for `ScrollStrategyOptions` to replace the default\n * behaviors. This class primarily acts as a factory for ScrollStrategy instances.\n */\n@Injectable({providedIn: 'root'})\nexport class ScrollStrategyOptions {\n  private _document: Document;\n\n  constructor(\n    private _scrollDispatcher: ScrollDispatcher,\n    private _viewportRuler: ViewportRuler,\n    private _ngZone: NgZone,\n    @Inject(DOCUMENT) document: any,\n  ) {\n    this._document = document;\n  }\n\n  /** Do nothing on scroll. */\n  noop = () => new NoopScrollStrategy();\n\n  /**\n   * Close the overlay as soon as the user scrolls.\n   * @param config Configuration to be used inside the scroll strategy.\n   */\n  close = (config?: CloseScrollStrategyConfig) =>\n    new CloseScrollStrategy(this._scrollDispatcher, this._ngZone, this._viewportRuler, config);\n\n  /** Block scrolling. */\n  block = () => new BlockScrollStrategy(this._viewportRuler, this._document);\n\n  /**\n   * Update the overlay's position on scroll.\n   * @param config Configuration to be used inside the scroll strategy.\n   * Allows debouncing the reposition calls.\n   */\n  reposition = (config?: RepositionScrollStrategyConfig) =>\n    new RepositionScrollStrategy(this._scrollDispatcher, this._viewportRuler, this._ngZone, config);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {PositionStrategy} from './position/position-strategy';\nimport {Direction, Directionality} from '@angular/cdk/bidi';\nimport {ScrollStrategy, NoopScrollStrategy} from './scroll/index';\n\n/** Initial configuration used when creating an overlay. */\nexport class OverlayConfig {\n  /** Strategy with which to position the overlay. */\n  positionStrategy?: PositionStrategy;\n\n  /** Strategy to be used when handling scroll events while the overlay is open. */\n  scrollStrategy?: ScrollStrategy = new NoopScrollStrategy();\n\n  /** Custom class to add to the overlay pane. */\n  panelClass?: string | string[] = '';\n\n  /** Whether the overlay has a backdrop. */\n  hasBackdrop?: boolean = false;\n\n  /** Custom class to add to the backdrop */\n  backdropClass?: string | string[] = 'cdk-overlay-dark-backdrop';\n\n  /** The width of the overlay panel. If a number is provided, pixel units are assumed. */\n  width?: number | string;\n\n  /** The height of the overlay panel. If a number is provided, pixel units are assumed. */\n  height?: number | string;\n\n  /** The min-width of the overlay panel. If a number is provided, pixel units are assumed. */\n  minWidth?: number | string;\n\n  /** The min-height of the overlay panel. If a number is provided, pixel units are assumed. */\n  minHeight?: number | string;\n\n  /** The max-width of the overlay panel. If a number is provided, pixel units are assumed. */\n  maxWidth?: number | string;\n\n  /** The max-height of the overlay panel. If a number is provided, pixel units are assumed. */\n  maxHeight?: number | string;\n\n  /**\n   * Direction of the text in the overlay panel. If a `Directionality` instance\n   * is passed in, the overlay will handle changes to its value automatically.\n   */\n  direction?: Direction | Directionality;\n\n  /**\n   * Whether the overlay should be disposed of when the user goes backwards/forwards in history.\n   * Note that this usually doesn't include clicking on links (unless the user is using\n   * the `HashLocationStrategy`).\n   */\n  disposeOnNavigation?: boolean = false;\n\n  constructor(config?: OverlayConfig) {\n    if (config) {\n      // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n      // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n      // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n      const configKeys = Object.keys(config) as Iterable<keyof OverlayConfig> &\n        (keyof OverlayConfig)[];\n      for (const key of configKeys) {\n        if (config[key] !== undefined) {\n          // TypeScript, as of version 3.5, sees the left-hand-side of this expression\n          // as \"I don't know *which* key this is, so the only valid value is the intersection\n          // of all the possible values.\" In this case, that happens to be `undefined`. TypeScript\n          // is not smart enough to see that the right-hand-side is actually an access of the same\n          // exact type with the same exact key, meaning that the value type must be identical.\n          // So we use `any` to work around this.\n          this[key] = config[key] as any;\n        }\n      }\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Horizontal dimension of a connection point on the perimeter of the origin or overlay element. */\nexport type HorizontalConnectionPos = 'start' | 'center' | 'end';\n\n/** Vertical dimension of a connection point on the perimeter of the origin or overlay element. */\nexport type VerticalConnectionPos = 'top' | 'center' | 'bottom';\n\n/** A connection point on the origin element. */\nexport interface OriginConnectionPosition {\n  originX: HorizontalConnectionPos;\n  originY: VerticalConnectionPos;\n}\n\n/** A connection point on the overlay element. */\nexport interface OverlayConnectionPosition {\n  overlayX: HorizontalConnectionPos;\n  overlayY: VerticalConnectionPos;\n}\n\n/** The points of the origin element and the overlay element to connect. */\nexport class ConnectionPositionPair {\n  /** X-axis attachment point for connected overlay origin. Can be 'start', 'end', or 'center'. */\n  originX: HorizontalConnectionPos;\n  /** Y-axis attachment point for connected overlay origin. Can be 'top', 'bottom', or 'center'. */\n  originY: VerticalConnectionPos;\n  /** X-axis attachment point for connected overlay. Can be 'start', 'end', or 'center'. */\n  overlayX: HorizontalConnectionPos;\n  /** Y-axis attachment point for connected overlay. Can be 'top', 'bottom', or 'center'. */\n  overlayY: VerticalConnectionPos;\n\n  constructor(\n    origin: OriginConnectionPosition,\n    overlay: OverlayConnectionPosition,\n    /** Offset along the X axis. */\n    public offsetX?: number,\n    /** Offset along the Y axis. */\n    public offsetY?: number,\n    /** Class(es) to be applied to the panel while this position is active. */\n    public panelClass?: string | string[],\n  ) {\n    this.originX = origin.originX;\n    this.originY = origin.originY;\n    this.overlayX = overlay.overlayX;\n    this.overlayY = overlay.overlayY;\n  }\n}\n\n/**\n * Set of properties regarding the position of the origin and overlay relative to the viewport\n * with respect to the containing Scrollable elements.\n *\n * The overlay and origin are clipped if any part of their bounding client rectangle exceeds the\n * bounds of any one of the strategy's Scrollable's bounding client rectangle.\n *\n * The overlay and origin are outside view if there is no overlap between their bounding client\n * rectangle and any one of the strategy's Scrollable's bounding client rectangle.\n *\n *       -----------                    -----------\n *       | outside |                    | clipped |\n *       |  view   |              --------------------------\n *       |         |              |     |         |        |\n *       ----------               |     -----------        |\n *  --------------------------    |                        |\n *  |                        |    |      Scrollable        |\n *  |                        |    |                        |\n *  |                        |     --------------------------\n *  |      Scrollable        |\n *  |                        |\n *  --------------------------\n *\n *  @docs-private\n */\nexport class ScrollingVisibility {\n  isOriginClipped: boolean;\n  isOriginOutsideView: boolean;\n  isOverlayClipped: boolean;\n  isOverlayOutsideView: boolean;\n}\n\n/** The change event emitted by the strategy when a fallback position is used. */\nexport class ConnectedOverlayPositionChange {\n  constructor(\n    /** The position used as a result of this change. */\n    public connectionPair: ConnectionPositionPair,\n    /** @docs-private */\n    public scrollableViewProperties: ScrollingVisibility,\n  ) {}\n}\n\n/**\n * Validates whether a vertical position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nexport function validateVerticalPosition(property: string, value: VerticalConnectionPos) {\n  if (value !== 'top' && value !== 'bottom' && value !== 'center') {\n    throw Error(\n      `ConnectedPosition: Invalid ${property} \"${value}\". ` +\n        `Expected \"top\", \"bottom\" or \"center\".`,\n    );\n  }\n}\n\n/**\n * Validates whether a horizontal position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nexport function validateHorizontalPosition(property: string, value: HorizontalConnectionPos) {\n  if (value !== 'start' && value !== 'end' && value !== 'center') {\n    throw Error(\n      `ConnectedPosition: Invalid ${property} \"${value}\". ` +\n        `Expected \"start\", \"end\" or \"center\".`,\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {DOCUMENT} from '@angular/common';\nimport {Inject, Injectable, OnDestroy} from '@angular/core';\nimport type {OverlayRef} from '../overlay-ref';\n\n/**\n * Service for dispatching events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\n@Injectable({providedIn: 'root'})\nexport abstract class BaseOverlayDispatcher implements OnDestroy {\n  /** Currently attached overlays in the order they were attached. */\n  _attachedOverlays: OverlayRef[] = [];\n\n  protected _document: Document;\n  protected _isAttached: boolean;\n\n  constructor(@Inject(DOCUMENT) document: any) {\n    this._document = document;\n  }\n\n  ngOnDestroy(): void {\n    this.detach();\n  }\n\n  /** Add a new overlay to the list of attached overlay refs. */\n  add(overlayRef: OverlayRef): void {\n    // Ensure that we don't get the same overlay multiple times.\n    this.remove(overlayRef);\n    this._attachedOverlays.push(overlayRef);\n  }\n\n  /** Remove an overlay from the list of attached overlay refs. */\n  remove(overlayRef: OverlayRef): void {\n    const index = this._attachedOverlays.indexOf(overlayRef);\n\n    if (index > -1) {\n      this._attachedOverlays.splice(index, 1);\n    }\n\n    // Remove the global listener once there are no more overlays.\n    if (this._attachedOverlays.length === 0) {\n      this.detach();\n    }\n  }\n\n  /** Detaches the global event listener. */\n  protected abstract detach(): void;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {DOCUMENT} from '@angular/common';\nimport {Inject, Injectable, NgZone, Optional} from '@angular/core';\nimport {BaseOverlayDispatcher} from './base-overlay-dispatcher';\nimport type {OverlayRef} from '../overlay-ref';\n\n/**\n * Service for dispatching keyboard events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\n@Injectable({providedIn: 'root'})\nexport class OverlayKeyboardDispatcher extends BaseOverlayDispatcher {\n  constructor(\n    @Inject(DOCUMENT) document: any,\n    /** @breaking-change 14.0.0 _ngZone will be required. */\n    @Optional() private _ngZone?: NgZone,\n  ) {\n    super(document);\n  }\n\n  /** Add a new overlay to the list of attached overlay refs. */\n  override add(overlayRef: OverlayRef): void {\n    super.add(overlayRef);\n\n    // Lazily start dispatcher once first overlay is added\n    if (!this._isAttached) {\n      /** @breaking-change 14.0.0 _ngZone will be required. */\n      if (this._ngZone) {\n        this._ngZone.runOutsideAngular(() =>\n          this._document.body.addEventListener('keydown', this._keydownListener),\n        );\n      } else {\n        this._document.body.addEventListener('keydown', this._keydownListener);\n      }\n      this._isAttached = true;\n    }\n  }\n\n  /** Detaches the global keyboard event listener. */\n  protected detach() {\n    if (this._isAttached) {\n      this._document.body.removeEventListener('keydown', this._keydownListener);\n      this._isAttached = false;\n    }\n  }\n\n  /** Keyboard event listener that will be attached to the body. */\n  private _keydownListener = (event: KeyboardEvent) => {\n    const overlays = this._attachedOverlays;\n\n    for (let i = overlays.length - 1; i > -1; i--) {\n      // Dispatch the keydown event to the top overlay which has subscribers to its keydown events.\n      // We want to target the most recent overlay, rather than trying to match where the event came\n      // from, because some components might open an overlay, but keep focus on a trigger element\n      // (e.g. for select and autocomplete). We skip overlays without keydown event subscriptions,\n      // because we don't want overlays that don't handle keyboard events to block the ones below\n      // them that do.\n      if (overlays[i]._keydownEvents.observers.length > 0) {\n        const keydownEvents = overlays[i]._keydownEvents;\n        /** @breaking-change 14.0.0 _ngZone will be required. */\n        if (this._ngZone) {\n          this._ngZone.run(() => keydownEvents.next(event));\n        } else {\n          keydownEvents.next(event);\n        }\n        break;\n      }\n    }\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {DOCUMENT} from '@angular/common';\nimport {Inject, Injectable, NgZone, Optional} from '@angular/core';\nimport {Platform, _getEventTarget} from '@angular/cdk/platform';\nimport {BaseOverlayDispatcher} from './base-overlay-dispatcher';\nimport type {OverlayRef} from '../overlay-ref';\n\n/**\n * Service for dispatching mouse click events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\n@Injectable({providedIn: 'root'})\nexport class OverlayOutsideClickDispatcher extends BaseOverlayDispatcher {\n  private _cursorOriginalValue: string;\n  private _cursorStyleIsSet = false;\n  private _pointerDownEventTarget: EventTarget | null;\n\n  constructor(\n    @Inject(DOCUMENT) document: any,\n    private _platform: Platform,\n    /** @breaking-change 14.0.0 _ngZone will be required. */\n    @Optional() private _ngZone?: NgZone,\n  ) {\n    super(document);\n  }\n\n  /** Add a new overlay to the list of attached overlay refs. */\n  override add(overlayRef: OverlayRef): void {\n    super.add(overlayRef);\n\n    // Safari on iOS does not generate click events for non-interactive\n    // elements. However, we want to receive a click for any element outside\n    // the overlay. We can force a \"clickable\" state by setting\n    // `cursor: pointer` on the document body. See:\n    // https://developer.mozilla.org/en-US/docs/Web/API/Element/click_event#Safari_Mobile\n    // https://developer.apple.com/library/archive/documentation/AppleApplications/Reference/SafariWebContent/HandlingEvents/HandlingEvents.html\n    if (!this._isAttached) {\n      const body = this._document.body;\n\n      /** @breaking-change 14.0.0 _ngZone will be required. */\n      if (this._ngZone) {\n        this._ngZone.runOutsideAngular(() => this._addEventListeners(body));\n      } else {\n        this._addEventListeners(body);\n      }\n\n      // click event is not fired on iOS. To make element \"clickable\" we are\n      // setting the cursor to pointer\n      if (this._platform.IOS && !this._cursorStyleIsSet) {\n        this._cursorOriginalValue = body.style.cursor;\n        body.style.cursor = 'pointer';\n        this._cursorStyleIsSet = true;\n      }\n\n      this._isAttached = true;\n    }\n  }\n\n  /** Detaches the global keyboard event listener. */\n  protected detach() {\n    if (this._isAttached) {\n      const body = this._document.body;\n      body.removeEventListener('pointerdown', this._pointerDownListener, true);\n      body.removeEventListener('click', this._clickListener, true);\n      body.removeEventListener('auxclick', this._clickListener, true);\n      body.removeEventListener('contextmenu', this._clickListener, true);\n      if (this._platform.IOS && this._cursorStyleIsSet) {\n        body.style.cursor = this._cursorOriginalValue;\n        this._cursorStyleIsSet = false;\n      }\n      this._isAttached = false;\n    }\n  }\n\n  private _addEventListeners(body: HTMLElement): void {\n    body.addEventListener('pointerdown', this._pointerDownListener, true);\n    body.addEventListener('click', this._clickListener, true);\n    body.addEventListener('auxclick', this._clickListener, true);\n    body.addEventListener('contextmenu', this._clickListener, true);\n  }\n\n  /** Store pointerdown event target to track origin of click. */\n  private _pointerDownListener = (event: PointerEvent) => {\n    this._pointerDownEventTarget = _getEventTarget(event);\n  };\n\n  /** Click event listener that will be attached to the body propagate phase. */\n  private _clickListener = (event: MouseEvent) => {\n    const target = _getEventTarget(event);\n    // In case of a click event, we want to check the origin of the click\n    // (e.g. in case where a user starts a click inside the overlay and\n    // releases the click outside of it).\n    // This is done by using the event target of the preceding pointerdown event.\n    // Every click event caused by a pointer device has a preceding pointerdown\n    // event, unless the click was programmatically triggered (e.g. in a unit test).\n    const origin =\n      event.type === 'click' && this._pointerDownEventTarget\n        ? this._pointerDownEventTarget\n        : target;\n    // Reset the stored pointerdown event target, to avoid having it interfere\n    // in subsequent events.\n    this._pointerDownEventTarget = null;\n\n    // We copy the array because the original may be modified asynchronously if the\n    // outsidePointerEvents listener decides to detach overlays resulting in index errors inside\n    // the for loop.\n    const overlays = this._attachedOverlays.slice();\n\n    // Dispatch the mouse event to the top overlay which has subscribers to its mouse events.\n    // We want to target all overlays for which the click could be considered as outside click.\n    // As soon as we reach an overlay for which the click is not outside click we break off\n    // the loop.\n    for (let i = overlays.length - 1; i > -1; i--) {\n      const overlayRef = overlays[i];\n      if (overlayRef._outsidePointerEvents.observers.length < 1 || !overlayRef.hasAttached()) {\n        continue;\n      }\n\n      // If it's a click inside the overlay, just break - we should do nothing\n      // If it's an outside click (both origin and target of the click) dispatch the mouse event,\n      // and proceed with the next overlay\n      if (\n        overlayRef.overlayElement.contains(target as Node) ||\n        overlayRef.overlayElement.contains(origin as Node)\n      ) {\n        break;\n      }\n\n      const outsidePointerEvents = overlayRef._outsidePointerEvents;\n      /** @breaking-change 14.0.0 _ngZone will be required. */\n      if (this._ngZone) {\n        this._ngZone.run(() => outsidePointerEvents.next(event));\n      } else {\n        outsidePointerEvents.next(event);\n      }\n    }\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {DOCUMENT} from '@angular/common';\nimport {Inject, Injectable, OnDestroy} from '@angular/core';\nimport {Platform, _isTestEnvironment} from '@angular/cdk/platform';\n\n/** Container inside which all overlays will render. */\n@Injectable({providedIn: 'root'})\nexport class OverlayContainer implements OnDestroy {\n  protected _containerElement: HTMLElement;\n  protected _document: Document;\n\n  constructor(\n    @Inject(DOCUMENT) document: any,\n    protected _platform: Platform,\n  ) {\n    this._document = document;\n  }\n\n  ngOnDestroy() {\n    this._containerElement?.remove();\n  }\n\n  /**\n   * This method returns the overlay container element. It will lazily\n   * create the element the first time it is called to facilitate using\n   * the container in non-browser environments.\n   * @returns the container element\n   */\n  getContainerElement(): HTMLElement {\n    if (!this._containerElement) {\n      this._createContainer();\n    }\n\n    return this._containerElement;\n  }\n\n  /**\n   * Create the overlay container element, which is simply a div\n   * with the 'cdk-overlay-container' class on the document body.\n   */\n  protected _createContainer(): void {\n    const containerClass = 'cdk-overlay-container';\n\n    // TODO(crisbeto): remove the testing check once we have an overlay testing\n    // module or Angular starts tearing down the testing `NgModule`. See:\n    // https://github.com/angular/angular/issues/18831\n    if (this._platform.isBrowser || _isTestEnvironment()) {\n      const oppositePlatformContainers = this._document.querySelectorAll(\n        `.${containerClass}[platform=\"server\"], ` + `.${containerClass}[platform=\"test\"]`,\n      );\n\n      // Remove any old containers from the opposite platform.\n      // This can happen when transitioning from the server to the client.\n      for (let i = 0; i < oppositePlatformContainers.length; i++) {\n        oppositePlatformContainers[i].remove();\n      }\n    }\n\n    const container = this._document.createElement('div');\n    container.classList.add(containerClass);\n\n    // A long time ago we kept adding new overlay containers whenever a new app was instantiated,\n    // but at some point we added logic which clears the duplicate ones in order to avoid leaks.\n    // The new logic was a little too aggressive since it was breaking some legitimate use cases.\n    // To mitigate the problem we made it so that only containers from a different platform are\n    // cleared, but the side-effect was that people started depending on the overly-aggressive\n    // logic to clean up their tests for them. Until we can introduce an overlay-specific testing\n    // module which does the cleanup, we try to detect that we're in a test environment and we\n    // always clear the container. See #17006.\n    // TODO(crisbeto): remove the test environment check once we have an overlay testing module.\n    if (_isTestEnvironment()) {\n      container.setAttribute('platform', 'test');\n    } else if (!this._platform.isBrowser) {\n      container.setAttribute('platform', 'server');\n    }\n\n    this._document.body.appendChild(container);\n    this._containerElement = container;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Direction, Directionality} from '@angular/cdk/bidi';\nimport {ComponentPortal, Portal, PortalOutlet, TemplatePortal} from '@angular/cdk/portal';\nimport {ComponentRef, EmbeddedViewRef, NgZone} from '@angular/core';\nimport {Location} from '@angular/common';\nimport {Observable, Subject, merge, SubscriptionLike, Subscription} from 'rxjs';\nimport {take, takeUntil} from 'rxjs/operators';\nimport {OverlayKeyboardDispatcher} from './dispatchers/overlay-keyboard-dispatcher';\nimport {OverlayOutsideClickDispatcher} from './dispatchers/overlay-outside-click-dispatcher';\nimport {OverlayConfig} from './overlay-config';\nimport {coerceCssPixelValue, coerceArray} from '@angular/cdk/coercion';\nimport {PositionStrategy} from './position/position-strategy';\nimport {ScrollStrategy} from './scroll';\n\n/** An object where all of its properties cannot be written. */\nexport type ImmutableObject<T> = {\n  readonly [P in keyof T]: T[P];\n};\n\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\nexport class OverlayRef implements PortalOutlet {\n  private _backdropElement: HTMLElement | null = null;\n  private _backdropTimeout: number | undefined;\n  private readonly _backdropClick = new Subject<MouseEvent>();\n  private readonly _attachments = new Subject<void>();\n  private readonly _detachments = new Subject<void>();\n  private _positionStrategy: PositionStrategy | undefined;\n  private _scrollStrategy: ScrollStrategy | undefined;\n  private _locationChanges: SubscriptionLike = Subscription.EMPTY;\n  private _backdropClickHandler = (event: MouseEvent) => this._backdropClick.next(event);\n  private _backdropTransitionendHandler = (event: TransitionEvent) => {\n    this._disposeBackdrop(event.target as HTMLElement | null);\n  };\n\n  /**\n   * Reference to the parent of the `_host` at the time it was detached. Used to restore\n   * the `_host` to its original position in the DOM when it gets re-attached.\n   */\n  private _previousHostParent: HTMLElement;\n\n  /** Stream of keydown events dispatched to this overlay. */\n  readonly _keydownEvents = new Subject<KeyboardEvent>();\n\n  /** Stream of mouse outside events dispatched to this overlay. */\n  readonly _outsidePointerEvents = new Subject<MouseEvent>();\n\n  constructor(\n    private _portalOutlet: PortalOutlet,\n    private _host: HTMLElement,\n    private _pane: HTMLElement,\n    private _config: ImmutableObject<OverlayConfig>,\n    private _ngZone: NgZone,\n    private _keyboardDispatcher: OverlayKeyboardDispatcher,\n    private _document: Document,\n    private _location: Location,\n    private _outsideClickDispatcher: OverlayOutsideClickDispatcher,\n    private _animationsDisabled = false,\n  ) {\n    if (_config.scrollStrategy) {\n      this._scrollStrategy = _config.scrollStrategy;\n      this._scrollStrategy.attach(this);\n    }\n\n    this._positionStrategy = _config.positionStrategy;\n  }\n\n  /** The overlay's HTML element */\n  get overlayElement(): HTMLElement {\n    return this._pane;\n  }\n\n  /** The overlay's backdrop HTML element. */\n  get backdropElement(): HTMLElement | null {\n    return this._backdropElement;\n  }\n\n  /**\n   * Wrapper around the panel element. Can be used for advanced\n   * positioning where a wrapper with specific styling is\n   * required around the overlay pane.\n   */\n  get hostElement(): HTMLElement {\n    return this._host;\n  }\n\n  attach<T>(portal: ComponentPortal<T>): ComponentRef<T>;\n  attach<T>(portal: TemplatePortal<T>): EmbeddedViewRef<T>;\n  attach(portal: any): any;\n\n  /**\n   * Attaches content, given via a Portal, to the overlay.\n   * If the overlay is configured to have a backdrop, it will be created.\n   *\n   * @param portal Portal instance to which to attach the overlay.\n   * @returns The portal attachment result.\n   */\n  attach(portal: Portal<any>): any {\n    // Insert the host into the DOM before attaching the portal, otherwise\n    // the animations module will skip animations on repeat attachments.\n    if (!this._host.parentElement && this._previousHostParent) {\n      this._previousHostParent.appendChild(this._host);\n    }\n\n    const attachResult = this._portalOutlet.attach(portal);\n\n    if (this._positionStrategy) {\n      this._positionStrategy.attach(this);\n    }\n\n    this._updateStackingOrder();\n    this._updateElementSize();\n    this._updateElementDirection();\n\n    if (this._scrollStrategy) {\n      this._scrollStrategy.enable();\n    }\n\n    // Update the position once the zone is stable so that the overlay will be fully rendered\n    // before attempting to position it, as the position may depend on the size of the rendered\n    // content.\n    this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n      // The overlay could've been detached before the zone has stabilized.\n      if (this.hasAttached()) {\n        this.updatePosition();\n      }\n    });\n\n    // Enable pointer events for the overlay pane element.\n    this._togglePointerEvents(true);\n\n    if (this._config.hasBackdrop) {\n      this._attachBackdrop();\n    }\n\n    if (this._config.panelClass) {\n      this._toggleClasses(this._pane, this._config.panelClass, true);\n    }\n\n    // Only emit the `attachments` event once all other setup is done.\n    this._attachments.next();\n\n    // Track this overlay by the keyboard dispatcher\n    this._keyboardDispatcher.add(this);\n\n    if (this._config.disposeOnNavigation) {\n      this._locationChanges = this._location.subscribe(() => this.dispose());\n    }\n\n    this._outsideClickDispatcher.add(this);\n\n    // TODO(crisbeto): the null check is here, because the portal outlet returns `any`.\n    // We should be guaranteed for the result to be `ComponentRef | EmbeddedViewRef`, but\n    // `instanceof EmbeddedViewRef` doesn't appear to work at the moment.\n    if (typeof attachResult?.onDestroy === 'function') {\n      // In most cases we control the portal and we know when it is being detached so that\n      // we can finish the disposal process. The exception is if the user passes in a custom\n      // `ViewContainerRef` that isn't destroyed through the overlay API. Note that we use\n      // `detach` here instead of `dispose`, because we don't know if the user intends to\n      // reattach the overlay at a later point. It also has the advantage of waiting for animations.\n      attachResult.onDestroy(() => {\n        if (this.hasAttached()) {\n          // We have to delay the `detach` call, because detaching immediately prevents\n          // other destroy hooks from running. This is likely a framework bug similar to\n          // https://github.com/angular/angular/issues/46119\n          this._ngZone.runOutsideAngular(() => Promise.resolve().then(() => this.detach()));\n        }\n      });\n    }\n\n    return attachResult;\n  }\n\n  /**\n   * Detaches an overlay from a portal.\n   * @returns The portal detachment result.\n   */\n  detach(): any {\n    if (!this.hasAttached()) {\n      return;\n    }\n\n    this.detachBackdrop();\n\n    // When the overlay is detached, the pane element should disable pointer events.\n    // This is necessary because otherwise the pane element will cover the page and disable\n    // pointer events therefore. Depends on the position strategy and the applied pane boundaries.\n    this._togglePointerEvents(false);\n\n    if (this._positionStrategy && this._positionStrategy.detach) {\n      this._positionStrategy.detach();\n    }\n\n    if (this._scrollStrategy) {\n      this._scrollStrategy.disable();\n    }\n\n    const detachmentResult = this._portalOutlet.detach();\n\n    // Only emit after everything is detached.\n    this._detachments.next();\n\n    // Remove this overlay from keyboard dispatcher tracking.\n    this._keyboardDispatcher.remove(this);\n\n    // Keeping the host element in the DOM can cause scroll jank, because it still gets\n    // rendered, even though it's transparent and unclickable which is why we remove it.\n    this._detachContentWhenStable();\n    this._locationChanges.unsubscribe();\n    this._outsideClickDispatcher.remove(this);\n    return detachmentResult;\n  }\n\n  /** Cleans up the overlay from the DOM. */\n  dispose(): void {\n    const isAttached = this.hasAttached();\n\n    if (this._positionStrategy) {\n      this._positionStrategy.dispose();\n    }\n\n    this._disposeScrollStrategy();\n    this._disposeBackdrop(this._backdropElement);\n    this._locationChanges.unsubscribe();\n    this._keyboardDispatcher.remove(this);\n    this._portalOutlet.dispose();\n    this._attachments.complete();\n    this._backdropClick.complete();\n    this._keydownEvents.complete();\n    this._outsidePointerEvents.complete();\n    this._outsideClickDispatcher.remove(this);\n    this._host?.remove();\n\n    this._previousHostParent = this._pane = this._host = null!;\n\n    if (isAttached) {\n      this._detachments.next();\n    }\n\n    this._detachments.complete();\n  }\n\n  /** Whether the overlay has attached content. */\n  hasAttached(): boolean {\n    return this._portalOutlet.hasAttached();\n  }\n\n  /** Gets an observable that emits when the backdrop has been clicked. */\n  backdropClick(): Observable<MouseEvent> {\n    return this._backdropClick;\n  }\n\n  /** Gets an observable that emits when the overlay has been attached. */\n  attachments(): Observable<void> {\n    return this._attachments;\n  }\n\n  /** Gets an observable that emits when the overlay has been detached. */\n  detachments(): Observable<void> {\n    return this._detachments;\n  }\n\n  /** Gets an observable of keydown events targeted to this overlay. */\n  keydownEvents(): Observable<KeyboardEvent> {\n    return this._keydownEvents;\n  }\n\n  /** Gets an observable of pointer events targeted outside this overlay. */\n  outsidePointerEvents(): Observable<MouseEvent> {\n    return this._outsidePointerEvents;\n  }\n\n  /** Gets the current overlay configuration, which is immutable. */\n  getConfig(): OverlayConfig {\n    return this._config;\n  }\n\n  /** Updates the position of the overlay based on the position strategy. */\n  updatePosition(): void {\n    if (this._positionStrategy) {\n      this._positionStrategy.apply();\n    }\n  }\n\n  /** Switches to a new position strategy and updates the overlay position. */\n  updatePositionStrategy(strategy: PositionStrategy): void {\n    if (strategy === this._positionStrategy) {\n      return;\n    }\n\n    if (this._positionStrategy) {\n      this._positionStrategy.dispose();\n    }\n\n    this._positionStrategy = strategy;\n\n    if (this.hasAttached()) {\n      strategy.attach(this);\n      this.updatePosition();\n    }\n  }\n\n  /** Update the size properties of the overlay. */\n  updateSize(sizeConfig: OverlaySizeConfig): void {\n    this._config = {...this._config, ...sizeConfig};\n    this._updateElementSize();\n  }\n\n  /** Sets the LTR/RTL direction for the overlay. */\n  setDirection(dir: Direction | Directionality): void {\n    this._config = {...this._config, direction: dir};\n    this._updateElementDirection();\n  }\n\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes: string | string[]): void {\n    if (this._pane) {\n      this._toggleClasses(this._pane, classes, true);\n    }\n  }\n\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes: string | string[]): void {\n    if (this._pane) {\n      this._toggleClasses(this._pane, classes, false);\n    }\n  }\n\n  /**\n   * Returns the layout direction of the overlay panel.\n   */\n  getDirection(): Direction {\n    const direction = this._config.direction;\n\n    if (!direction) {\n      return 'ltr';\n    }\n\n    return typeof direction === 'string' ? direction : direction.value;\n  }\n\n  /** Switches to a new scroll strategy. */\n  updateScrollStrategy(strategy: ScrollStrategy): void {\n    if (strategy === this._scrollStrategy) {\n      return;\n    }\n\n    this._disposeScrollStrategy();\n    this._scrollStrategy = strategy;\n\n    if (this.hasAttached()) {\n      strategy.attach(this);\n      strategy.enable();\n    }\n  }\n\n  /** Updates the text direction of the overlay panel. */\n  private _updateElementDirection() {\n    this._host.setAttribute('dir', this.getDirection());\n  }\n\n  /** Updates the size of the overlay element based on the overlay config. */\n  private _updateElementSize() {\n    if (!this._pane) {\n      return;\n    }\n\n    const style = this._pane.style;\n\n    style.width = coerceCssPixelValue(this._config.width);\n    style.height = coerceCssPixelValue(this._config.height);\n    style.minWidth = coerceCssPixelValue(this._config.minWidth);\n    style.minHeight = coerceCssPixelValue(this._config.minHeight);\n    style.maxWidth = coerceCssPixelValue(this._config.maxWidth);\n    style.maxHeight = coerceCssPixelValue(this._config.maxHeight);\n  }\n\n  /** Toggles the pointer events for the overlay pane element. */\n  private _togglePointerEvents(enablePointer: boolean) {\n    this._pane.style.pointerEvents = enablePointer ? '' : 'none';\n  }\n\n  /** Attaches a backdrop for this overlay. */\n  private _attachBackdrop() {\n    const showingClass = 'cdk-overlay-backdrop-showing';\n\n    this._backdropElement = this._document.createElement('div');\n    this._backdropElement.classList.add('cdk-overlay-backdrop');\n\n    if (this._animationsDisabled) {\n      this._backdropElement.classList.add('cdk-overlay-backdrop-noop-animation');\n    }\n\n    if (this._config.backdropClass) {\n      this._toggleClasses(this._backdropElement, this._config.backdropClass, true);\n    }\n\n    // Insert the backdrop before the pane in the DOM order,\n    // in order to handle stacked overlays properly.\n    this._host.parentElement!.insertBefore(this._backdropElement, this._host);\n\n    // Forward backdrop clicks such that the consumer of the overlay can perform whatever\n    // action desired when such a click occurs (usually closing the overlay).\n    this._backdropElement.addEventListener('click', this._backdropClickHandler);\n\n    // Add class to fade-in the backdrop after one frame.\n    if (!this._animationsDisabled && typeof requestAnimationFrame !== 'undefined') {\n      this._ngZone.runOutsideAngular(() => {\n        requestAnimationFrame(() => {\n          if (this._backdropElement) {\n            this._backdropElement.classList.add(showingClass);\n          }\n        });\n      });\n    } else {\n      this._backdropElement.classList.add(showingClass);\n    }\n  }\n\n  /**\n   * Updates the stacking order of the element, moving it to the top if necessary.\n   * This is required in cases where one overlay was detached, while another one,\n   * that should be behind it, was destroyed. The next time both of them are opened,\n   * the stacking will be wrong, because the detached element's pane will still be\n   * in its original DOM position.\n   */\n  private _updateStackingOrder() {\n    if (this._host.nextSibling) {\n      this._host.parentNode!.appendChild(this._host);\n    }\n  }\n\n  /** Detaches the backdrop (if any) associated with the overlay. */\n  detachBackdrop(): void {\n    const backdropToDetach = this._backdropElement;\n\n    if (!backdropToDetach) {\n      return;\n    }\n\n    if (this._animationsDisabled) {\n      this._disposeBackdrop(backdropToDetach);\n      return;\n    }\n\n    backdropToDetach.classList.remove('cdk-overlay-backdrop-showing');\n\n    this._ngZone.runOutsideAngular(() => {\n      backdropToDetach!.addEventListener('transitionend', this._backdropTransitionendHandler);\n    });\n\n    // If the backdrop doesn't have a transition, the `transitionend` event won't fire.\n    // In this case we make it unclickable and we try to remove it after a delay.\n    backdropToDetach.style.pointerEvents = 'none';\n\n    // Run this outside the Angular zone because there's nothing that Angular cares about.\n    // If it were to run inside the Angular zone, every test that used Overlay would have to be\n    // either async or fakeAsync.\n    this._backdropTimeout = this._ngZone.runOutsideAngular(() =>\n      setTimeout(() => {\n        this._disposeBackdrop(backdropToDetach);\n      }, 500),\n    );\n  }\n\n  /** Toggles a single CSS class or an array of classes on an element. */\n  private _toggleClasses(element: HTMLElement, cssClasses: string | string[], isAdd: boolean) {\n    const classes = coerceArray(cssClasses || []).filter(c => !!c);\n\n    if (classes.length) {\n      isAdd ? element.classList.add(...classes) : element.classList.remove(...classes);\n    }\n  }\n\n  /** Detaches the overlay content next time the zone stabilizes. */\n  private _detachContentWhenStable() {\n    // Normally we wouldn't have to explicitly run this outside the `NgZone`, however\n    // if the consumer is using `zone-patch-rxjs`, the `Subscription.unsubscribe` call will\n    // be patched to run inside the zone, which will throw us into an infinite loop.\n    this._ngZone.runOutsideAngular(() => {\n      // We can't remove the host here immediately, because the overlay pane's content\n      // might still be animating. This stream helps us avoid interrupting the animation\n      // by waiting for the pane to become empty.\n      const subscription = this._ngZone.onStable\n        .pipe(takeUntil(merge(this._attachments, this._detachments)))\n        .subscribe(() => {\n          // Needs a couple of checks for the pane and host, because\n          // they may have been removed by the time the zone stabilizes.\n          if (!this._pane || !this._host || this._pane.children.length === 0) {\n            if (this._pane && this._config.panelClass) {\n              this._toggleClasses(this._pane, this._config.panelClass, false);\n            }\n\n            if (this._host && this._host.parentElement) {\n              this._previousHostParent = this._host.parentElement;\n              this._host.remove();\n            }\n\n            subscription.unsubscribe();\n          }\n        });\n    });\n  }\n\n  /** Disposes of a scroll strategy. */\n  private _disposeScrollStrategy() {\n    const scrollStrategy = this._scrollStrategy;\n\n    if (scrollStrategy) {\n      scrollStrategy.disable();\n\n      if (scrollStrategy.detach) {\n        scrollStrategy.detach();\n      }\n    }\n  }\n\n  /** Removes a backdrop element from the DOM. */\n  private _disposeBackdrop(backdrop: HTMLElement | null) {\n    if (backdrop) {\n      backdrop.removeEventListener('click', this._backdropClickHandler);\n      backdrop.removeEventListener('transitionend', this._backdropTransitionendHandler);\n      backdrop.remove();\n\n      // It is possible that a new portal has been attached to this overlay since we started\n      // removing the backdrop. If that is the case, only clear the backdrop reference if it\n      // is still the same instance that we started to remove.\n      if (this._backdropElement === backdrop) {\n        this._backdropElement = null;\n      }\n    }\n\n    if (this._backdropTimeout) {\n      clearTimeout(this._backdropTimeout);\n      this._backdropTimeout = undefined;\n    }\n  }\n}\n\n/** Size properties for an overlay. */\nexport interface OverlaySizeConfig {\n  width?: number | string;\n  height?: number | string;\n  minWidth?: number | string;\n  minHeight?: number | string;\n  maxWidth?: number | string;\n  maxHeight?: number | string;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {PositionStrategy} from './position-strategy';\nimport {ElementRef} from '@angular/core';\nimport {ViewportRuler, CdkScrollable, ViewportScrollPosition} from '@angular/cdk/scrolling';\nimport {\n  ConnectedOverlayPositionChange,\n  ConnectionPositionPair,\n  ScrollingVisibility,\n  validateHorizontalPosition,\n  validateVerticalPosition,\n} from './connected-position';\nimport {Observable, Subscription, Subject} from 'rxjs';\nimport {isElementScrolledOutsideView, isElementClippedByScrolling} from './scroll-clip';\nimport {coerceCssPixelValue, coerceArray} from '@angular/cdk/coercion';\nimport {Platform} from '@angular/cdk/platform';\nimport {OverlayContainer} from '../overlay-container';\nimport {OverlayRef} from '../overlay-ref';\n\n// TODO: refactor clipping detection into a separate thing (part of scrolling module)\n// TODO: doesn't handle both flexible width and height when it has to scroll along both axis.\n\n/** Class to be added to the overlay bounding box. */\nconst boundingBoxClass = 'cdk-overlay-connected-position-bounding-box';\n\n/** Regex used to split a string on its CSS units. */\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n\n/** Possible values that can be set as the origin of a FlexibleConnectedPositionStrategy. */\nexport type FlexibleConnectedPositionStrategyOrigin =\n  | ElementRef\n  | Element\n  | (Point & {\n      width?: number;\n      height?: number;\n    });\n\n/** Equivalent of `DOMRect` without some of the properties we don't care about. */\ntype Dimensions = Omit<DOMRect, 'x' | 'y' | 'toJSON'>;\n\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * implicit position relative some origin element. The relative position is defined in terms of\n * a point on the origin element that is connected to a point on the overlay element. For example,\n * a basic dropdown is connecting the bottom-left corner of the origin to the top-left corner\n * of the overlay.\n */\nexport class FlexibleConnectedPositionStrategy implements PositionStrategy {\n  /** The overlay to which this strategy is attached. */\n  private _overlayRef: OverlayRef;\n\n  /** Whether we're performing the very first positioning of the overlay. */\n  private _isInitialRender: boolean;\n\n  /** Last size used for the bounding box. Used to avoid resizing the overlay after open. */\n  private _lastBoundingBoxSize = {width: 0, height: 0};\n\n  /** Whether the overlay was pushed in a previous positioning. */\n  private _isPushed = false;\n\n  /** Whether the overlay can be pushed on-screen on the initial open. */\n  private _canPush = true;\n\n  /** Whether the overlay can grow via flexible width/height after the initial open. */\n  private _growAfterOpen = false;\n\n  /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n  private _hasFlexibleDimensions = true;\n\n  /** Whether the overlay position is locked. */\n  private _positionLocked = false;\n\n  /** Cached origin dimensions */\n  private _originRect: Dimensions;\n\n  /** Cached overlay dimensions */\n  private _overlayRect: Dimensions;\n\n  /** Cached viewport dimensions */\n  private _viewportRect: Dimensions;\n\n  /** Cached container dimensions */\n  private _containerRect: Dimensions;\n\n  /** Amount of space that must be maintained between the overlay and the edge of the viewport. */\n  private _viewportMargin = 0;\n\n  /** The Scrollable containers used to check scrollable view properties on position change. */\n  private _scrollables: CdkScrollable[] = [];\n\n  /** Ordered list of preferred positions, from most to least desirable. */\n  _preferredPositions: ConnectionPositionPair[] = [];\n\n  /** The origin element against which the overlay will be positioned. */\n  _origin: FlexibleConnectedPositionStrategyOrigin;\n\n  /** The overlay pane element. */\n  private _pane: HTMLElement;\n\n  /** Whether the strategy has been disposed of already. */\n  private _isDisposed: boolean;\n\n  /**\n   * Parent element for the overlay panel used to constrain the overlay panel's size to fit\n   * within the viewport.\n   */\n  private _boundingBox: HTMLElement | null;\n\n  /** The last position to have been calculated as the best fit position. */\n  private _lastPosition: ConnectedPosition | null;\n\n  /** The last calculated scroll visibility. Only tracked  */\n  private _lastScrollVisibility: ScrollingVisibility | null;\n\n  /** Subject that emits whenever the position changes. */\n  private readonly _positionChanges = new Subject<ConnectedOverlayPositionChange>();\n\n  /** Subscription to viewport size changes. */\n  private _resizeSubscription = Subscription.EMPTY;\n\n  /** Default offset for the overlay along the x axis. */\n  private _offsetX = 0;\n\n  /** Default offset for the overlay along the y axis. */\n  private _offsetY = 0;\n\n  /** Selector to be used when finding the elements on which to set the transform origin. */\n  private _transformOriginSelector: string;\n\n  /** Keeps track of the CSS classes that the position strategy has applied on the overlay panel. */\n  private _appliedPanelClasses: string[] = [];\n\n  /** Amount by which the overlay was pushed in each axis during the last time it was positioned. */\n  private _previousPushAmount: {x: number; y: number} | null;\n\n  /** Observable sequence of position changes. */\n  positionChanges: Observable<ConnectedOverlayPositionChange> = this._positionChanges;\n\n  /** Ordered list of preferred positions, from most to least desirable. */\n  get positions(): ConnectionPositionPair[] {\n    return this._preferredPositions;\n  }\n\n  constructor(\n    connectedTo: FlexibleConnectedPositionStrategyOrigin,\n    private _viewportRuler: ViewportRuler,\n    private _document: Document,\n    private _platform: Platform,\n    private _overlayContainer: OverlayContainer,\n  ) {\n    this.setOrigin(connectedTo);\n  }\n\n  /** Attaches this position strategy to an overlay. */\n  attach(overlayRef: OverlayRef): void {\n    if (\n      this._overlayRef &&\n      overlayRef !== this._overlayRef &&\n      (typeof ngDevMode === 'undefined' || ngDevMode)\n    ) {\n      throw Error('This position strategy is already attached to an overlay');\n    }\n\n    this._validatePositions();\n\n    overlayRef.hostElement.classList.add(boundingBoxClass);\n\n    this._overlayRef = overlayRef;\n    this._boundingBox = overlayRef.hostElement;\n    this._pane = overlayRef.overlayElement;\n    this._isDisposed = false;\n    this._isInitialRender = true;\n    this._lastPosition = null;\n    this._resizeSubscription.unsubscribe();\n    this._resizeSubscription = this._viewportRuler.change().subscribe(() => {\n      // When the window is resized, we want to trigger the next reposition as if it\n      // was an initial render, in order for the strategy to pick a new optimal position,\n      // otherwise position locking will cause it to stay at the old one.\n      this._isInitialRender = true;\n      this.apply();\n    });\n  }\n\n  /**\n   * Updates the position of the overlay element, using whichever preferred position relative\n   * to the origin best fits on-screen.\n   *\n   * The selection of a position goes as follows:\n   *  - If any positions fit completely within the viewport as-is,\n   *      choose the first position that does so.\n   *  - If flexible dimensions are enabled and at least one satisfies the given minimum width/height,\n   *      choose the position with the greatest available size modified by the positions' weight.\n   *  - If pushing is enabled, take the position that went off-screen the least and push it\n   *      on-screen.\n   *  - If none of the previous criteria were met, use the position that goes off-screen the least.\n   * @docs-private\n   */\n  apply(): void {\n    // We shouldn't do anything if the strategy was disposed or we're on the server.\n    if (this._isDisposed || !this._platform.isBrowser) {\n      return;\n    }\n\n    // If the position has been applied already (e.g. when the overlay was opened) and the\n    // consumer opted into locking in the position, re-use the old position, in order to\n    // prevent the overlay from jumping around.\n    if (!this._isInitialRender && this._positionLocked && this._lastPosition) {\n      this.reapplyLastPosition();\n      return;\n    }\n\n    this._clearPanelClasses();\n    this._resetOverlayElementStyles();\n    this._resetBoundingBoxStyles();\n\n    // We need the bounding rects for the origin, the overlay and the container to determine how to position\n    // the overlay relative to the origin.\n    // We use the viewport rect to determine whether a position would go off-screen.\n    this._viewportRect = this._getNarrowedViewportRect();\n    this._originRect = this._getOriginRect();\n    this._overlayRect = this._pane.getBoundingClientRect();\n    this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n\n    const originRect = this._originRect;\n    const overlayRect = this._overlayRect;\n    const viewportRect = this._viewportRect;\n    const containerRect = this._containerRect;\n\n    // Positions where the overlay will fit with flexible dimensions.\n    const flexibleFits: FlexibleFit[] = [];\n\n    // Fallback if none of the preferred positions fit within the viewport.\n    let fallback: FallbackPosition | undefined;\n\n    // Go through each of the preferred positions looking for a good fit.\n    // If a good fit is found, it will be applied immediately.\n    for (let pos of this._preferredPositions) {\n      // Get the exact (x, y) coordinate for the point-of-origin on the origin element.\n      let originPoint = this._getOriginPoint(originRect, containerRect, pos);\n\n      // From that point-of-origin, get the exact (x, y) coordinate for the top-left corner of the\n      // overlay in this position. We use the top-left corner for calculations and later translate\n      // this into an appropriate (top, left, bottom, right) style.\n      let overlayPoint = this._getOverlayPoint(originPoint, overlayRect, pos);\n\n      // Calculate how well the overlay would fit into the viewport with this point.\n      let overlayFit = this._getOverlayFit(overlayPoint, overlayRect, viewportRect, pos);\n\n      // If the overlay, without any further work, fits into the viewport, use this position.\n      if (overlayFit.isCompletelyWithinViewport) {\n        this._isPushed = false;\n        this._applyPosition(pos, originPoint);\n        return;\n      }\n\n      // If the overlay has flexible dimensions, we can use this position\n      // so long as there's enough space for the minimum dimensions.\n      if (this._canFitWithFlexibleDimensions(overlayFit, overlayPoint, viewportRect)) {\n        // Save positions where the overlay will fit with flexible dimensions. We will use these\n        // if none of the positions fit *without* flexible dimensions.\n        flexibleFits.push({\n          position: pos,\n          origin: originPoint,\n          overlayRect,\n          boundingBoxRect: this._calculateBoundingBoxRect(originPoint, pos),\n        });\n\n        continue;\n      }\n\n      // If the current preferred position does not fit on the screen, remember the position\n      // if it has more visible area on-screen than we've seen and move onto the next preferred\n      // position.\n      if (!fallback || fallback.overlayFit.visibleArea < overlayFit.visibleArea) {\n        fallback = {overlayFit, overlayPoint, originPoint, position: pos, overlayRect};\n      }\n    }\n\n    // If there are any positions where the overlay would fit with flexible dimensions, choose the\n    // one that has the greatest area available modified by the position's weight\n    if (flexibleFits.length) {\n      let bestFit: FlexibleFit | null = null;\n      let bestScore = -1;\n      for (const fit of flexibleFits) {\n        const score =\n          fit.boundingBoxRect.width * fit.boundingBoxRect.height * (fit.position.weight || 1);\n        if (score > bestScore) {\n          bestScore = score;\n          bestFit = fit;\n        }\n      }\n\n      this._isPushed = false;\n      this._applyPosition(bestFit!.position, bestFit!.origin);\n      return;\n    }\n\n    // When none of the preferred positions fit within the viewport, take the position\n    // that went off-screen the least and attempt to push it on-screen.\n    if (this._canPush) {\n      // TODO(jelbourn): after pushing, the opening \"direction\" of the overlay might not make sense.\n      this._isPushed = true;\n      this._applyPosition(fallback!.position, fallback!.originPoint);\n      return;\n    }\n\n    // All options for getting the overlay within the viewport have been exhausted, so go with the\n    // position that went off-screen the least.\n    this._applyPosition(fallback!.position, fallback!.originPoint);\n  }\n\n  detach(): void {\n    this._clearPanelClasses();\n    this._lastPosition = null;\n    this._previousPushAmount = null;\n    this._resizeSubscription.unsubscribe();\n  }\n\n  /** Cleanup after the element gets destroyed. */\n  dispose(): void {\n    if (this._isDisposed) {\n      return;\n    }\n\n    // We can't use `_resetBoundingBoxStyles` here, because it resets\n    // some properties to zero, rather than removing them.\n    if (this._boundingBox) {\n      extendStyles(this._boundingBox.style, {\n        top: '',\n        left: '',\n        right: '',\n        bottom: '',\n        height: '',\n        width: '',\n        alignItems: '',\n        justifyContent: '',\n      } as CSSStyleDeclaration);\n    }\n\n    if (this._pane) {\n      this._resetOverlayElementStyles();\n    }\n\n    if (this._overlayRef) {\n      this._overlayRef.hostElement.classList.remove(boundingBoxClass);\n    }\n\n    this.detach();\n    this._positionChanges.complete();\n    this._overlayRef = this._boundingBox = null!;\n    this._isDisposed = true;\n  }\n\n  /**\n   * This re-aligns the overlay element with the trigger in its last calculated position,\n   * even if a position higher in the \"preferred positions\" list would now fit. This\n   * allows one to re-align the panel without changing the orientation of the panel.\n   */\n  reapplyLastPosition(): void {\n    if (this._isDisposed || !this._platform.isBrowser) {\n      return;\n    }\n\n    const lastPosition = this._lastPosition;\n\n    if (lastPosition) {\n      this._originRect = this._getOriginRect();\n      this._overlayRect = this._pane.getBoundingClientRect();\n      this._viewportRect = this._getNarrowedViewportRect();\n      this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n\n      const originPoint = this._getOriginPoint(this._originRect, this._containerRect, lastPosition);\n      this._applyPosition(lastPosition, originPoint);\n    } else {\n      this.apply();\n    }\n  }\n\n  /**\n   * Sets the list of Scrollable containers that host the origin element so that\n   * on reposition we can evaluate if it or the overlay has been clipped or outside view. Every\n   * Scrollable must be an ancestor element of the strategy's origin element.\n   */\n  withScrollableContainers(scrollables: CdkScrollable[]): this {\n    this._scrollables = scrollables;\n    return this;\n  }\n\n  /**\n   * Adds new preferred positions.\n   * @param positions List of positions options for this overlay.\n   */\n  withPositions(positions: ConnectedPosition[]): this {\n    this._preferredPositions = positions;\n\n    // If the last calculated position object isn't part of the positions anymore, clear\n    // it in order to avoid it being picked up if the consumer tries to re-apply.\n    if (positions.indexOf(this._lastPosition!) === -1) {\n      this._lastPosition = null;\n    }\n\n    this._validatePositions();\n\n    return this;\n  }\n\n  /**\n   * Sets a minimum distance the overlay may be positioned to the edge of the viewport.\n   * @param margin Required margin between the overlay and the viewport edge in pixels.\n   */\n  withViewportMargin(margin: number): this {\n    this._viewportMargin = margin;\n    return this;\n  }\n\n  /** Sets whether the overlay's width and height can be constrained to fit within the viewport. */\n  withFlexibleDimensions(flexibleDimensions = true): this {\n    this._hasFlexibleDimensions = flexibleDimensions;\n    return this;\n  }\n\n  /** Sets whether the overlay can grow after the initial open via flexible width/height. */\n  withGrowAfterOpen(growAfterOpen = true): this {\n    this._growAfterOpen = growAfterOpen;\n    return this;\n  }\n\n  /** Sets whether the overlay can be pushed on-screen if none of the provided positions fit. */\n  withPush(canPush = true): this {\n    this._canPush = canPush;\n    return this;\n  }\n\n  /**\n   * Sets whether the overlay's position should be locked in after it is positioned\n   * initially. When an overlay is locked in, it won't attempt to reposition itself\n   * when the position is re-applied (e.g. when the user scrolls away).\n   * @param isLocked Whether the overlay should locked in.\n   */\n  withLockedPosition(isLocked = true): this {\n    this._positionLocked = isLocked;\n    return this;\n  }\n\n  /**\n   * Sets the origin, relative to which to position the overlay.\n   * Using an element origin is useful for building components that need to be positioned\n   * relatively to a trigger (e.g. dropdown menus or tooltips), whereas using a point can be\n   * used for cases like contextual menus which open relative to the user's pointer.\n   * @param origin Reference to the new origin.\n   */\n  setOrigin(origin: FlexibleConnectedPositionStrategyOrigin): this {\n    this._origin = origin;\n    return this;\n  }\n\n  /**\n   * Sets the default offset for the overlay's connection point on the x-axis.\n   * @param offset New offset in the X axis.\n   */\n  withDefaultOffsetX(offset: number): this {\n    this._offsetX = offset;\n    return this;\n  }\n\n  /**\n   * Sets the default offset for the overlay's connection point on the y-axis.\n   * @param offset New offset in the Y axis.\n   */\n  withDefaultOffsetY(offset: number): this {\n    this._offsetY = offset;\n    return this;\n  }\n\n  /**\n   * Configures that the position strategy should set a `transform-origin` on some elements\n   * inside the overlay, depending on the current position that is being applied. This is\n   * useful for the cases where the origin of an animation can change depending on the\n   * alignment of the overlay.\n   * @param selector CSS selector that will be used to find the target\n   *    elements onto which to set the transform origin.\n   */\n  withTransformOriginOn(selector: string): this {\n    this._transformOriginSelector = selector;\n    return this;\n  }\n\n  /**\n   * Gets the (x, y) coordinate of a connection point on the origin based on a relative position.\n   */\n  private _getOriginPoint(\n    originRect: Dimensions,\n    containerRect: Dimensions,\n    pos: ConnectedPosition,\n  ): Point {\n    let x: number;\n    if (pos.originX == 'center') {\n      // Note: when centering we should always use the `left`\n      // offset, otherwise the position will be wrong in RTL.\n      x = originRect.left + originRect.width / 2;\n    } else {\n      const startX = this._isRtl() ? originRect.right : originRect.left;\n      const endX = this._isRtl() ? originRect.left : originRect.right;\n      x = pos.originX == 'start' ? startX : endX;\n    }\n\n    // When zooming in Safari the container rectangle contains negative values for the position\n    // and we need to re-add them to the calculated coordinates.\n    if (containerRect.left < 0) {\n      x -= containerRect.left;\n    }\n\n    let y: number;\n    if (pos.originY == 'center') {\n      y = originRect.top + originRect.height / 2;\n    } else {\n      y = pos.originY == 'top' ? originRect.top : originRect.bottom;\n    }\n\n    // Normally the containerRect's top value would be zero, however when the overlay is attached to an input\n    // (e.g. in an autocomplete), mobile browsers will shift everything in order to put the input in the middle\n    // of the screen and to make space for the virtual keyboard. We need to account for this offset,\n    // otherwise our positioning will be thrown off.\n    // Additionally, when zooming in Safari this fixes the vertical position.\n    if (containerRect.top < 0) {\n      y -= containerRect.top;\n    }\n\n    return {x, y};\n  }\n\n  /**\n   * Gets the (x, y) coordinate of the top-left corner of the overlay given a given position and\n   * origin point to which the overlay should be connected.\n   */\n  private _getOverlayPoint(\n    originPoint: Point,\n    overlayRect: Dimensions,\n    pos: ConnectedPosition,\n  ): Point {\n    // Calculate the (overlayStartX, overlayStartY), the start of the\n    // potential overlay position relative to the origin point.\n    let overlayStartX: number;\n    if (pos.overlayX == 'center') {\n      overlayStartX = -overlayRect.width / 2;\n    } else if (pos.overlayX === 'start') {\n      overlayStartX = this._isRtl() ? -overlayRect.width : 0;\n    } else {\n      overlayStartX = this._isRtl() ? 0 : -overlayRect.width;\n    }\n\n    let overlayStartY: number;\n    if (pos.overlayY == 'center') {\n      overlayStartY = -overlayRect.height / 2;\n    } else {\n      overlayStartY = pos.overlayY == 'top' ? 0 : -overlayRect.height;\n    }\n\n    // The (x, y) coordinates of the overlay.\n    return {\n      x: originPoint.x + overlayStartX,\n      y: originPoint.y + overlayStartY,\n    };\n  }\n\n  /** Gets how well an overlay at the given point will fit within the viewport. */\n  private _getOverlayFit(\n    point: Point,\n    rawOverlayRect: Dimensions,\n    viewport: Dimensions,\n    position: ConnectedPosition,\n  ): OverlayFit {\n    // Round the overlay rect when comparing against the\n    // viewport, because the viewport is always rounded.\n    const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n    let {x, y} = point;\n    let offsetX = this._getOffset(position, 'x');\n    let offsetY = this._getOffset(position, 'y');\n\n    // Account for the offsets since they could push the overlay out of the viewport.\n    if (offsetX) {\n      x += offsetX;\n    }\n\n    if (offsetY) {\n      y += offsetY;\n    }\n\n    // How much the overlay would overflow at this position, on each side.\n    let leftOverflow = 0 - x;\n    let rightOverflow = x + overlay.width - viewport.width;\n    let topOverflow = 0 - y;\n    let bottomOverflow = y + overlay.height - viewport.height;\n\n    // Visible parts of the element on each axis.\n    let visibleWidth = this._subtractOverflows(overlay.width, leftOverflow, rightOverflow);\n    let visibleHeight = this._subtractOverflows(overlay.height, topOverflow, bottomOverflow);\n    let visibleArea = visibleWidth * visibleHeight;\n\n    return {\n      visibleArea,\n      isCompletelyWithinViewport: overlay.width * overlay.height === visibleArea,\n      fitsInViewportVertically: visibleHeight === overlay.height,\n      fitsInViewportHorizontally: visibleWidth == overlay.width,\n    };\n  }\n\n  /**\n   * Whether the overlay can fit within the viewport when it may resize either its width or height.\n   * @param fit How well the overlay fits in the viewport at some position.\n   * @param point The (x, y) coordinates of the overlay at some position.\n   * @param viewport The geometry of the viewport.\n   */\n  private _canFitWithFlexibleDimensions(fit: OverlayFit, point: Point, viewport: Dimensions) {\n    if (this._hasFlexibleDimensions) {\n      const availableHeight = viewport.bottom - point.y;\n      const availableWidth = viewport.right - point.x;\n      const minHeight = getPixelValue(this._overlayRef.getConfig().minHeight);\n      const minWidth = getPixelValue(this._overlayRef.getConfig().minWidth);\n\n      const verticalFit =\n        fit.fitsInViewportVertically || (minHeight != null && minHeight <= availableHeight);\n      const horizontalFit =\n        fit.fitsInViewportHorizontally || (minWidth != null && minWidth <= availableWidth);\n\n      return verticalFit && horizontalFit;\n    }\n    return false;\n  }\n\n  /**\n   * Gets the point at which the overlay can be \"pushed\" on-screen. If the overlay is larger than\n   * the viewport, the top-left corner will be pushed on-screen (with overflow occurring on the\n   * right and bottom).\n   *\n   * @param start Starting point from which the overlay is pushed.\n   * @param rawOverlayRect Dimensions of the overlay.\n   * @param scrollPosition Current viewport scroll position.\n   * @returns The point at which to position the overlay after pushing. This is effectively a new\n   *     originPoint.\n   */\n  private _pushOverlayOnScreen(\n    start: Point,\n    rawOverlayRect: Dimensions,\n    scrollPosition: ViewportScrollPosition,\n  ): Point {\n    // If the position is locked and we've pushed the overlay already, reuse the previous push\n    // amount, rather than pushing it again. If we were to continue pushing, the element would\n    // remain in the viewport, which goes against the expectations when position locking is enabled.\n    if (this._previousPushAmount && this._positionLocked) {\n      return {\n        x: start.x + this._previousPushAmount.x,\n        y: start.y + this._previousPushAmount.y,\n      };\n    }\n\n    // Round the overlay rect when comparing against the\n    // viewport, because the viewport is always rounded.\n    const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n    const viewport = this._viewportRect;\n\n    // Determine how much the overlay goes outside the viewport on each\n    // side, which we'll use to decide which direction to push it.\n    const overflowRight = Math.max(start.x + overlay.width - viewport.width, 0);\n    const overflowBottom = Math.max(start.y + overlay.height - viewport.height, 0);\n    const overflowTop = Math.max(viewport.top - scrollPosition.top - start.y, 0);\n    const overflowLeft = Math.max(viewport.left - scrollPosition.left - start.x, 0);\n\n    // Amount by which to push the overlay in each axis such that it remains on-screen.\n    let pushX = 0;\n    let pushY = 0;\n\n    // If the overlay fits completely within the bounds of the viewport, push it from whichever\n    // direction is goes off-screen. Otherwise, push the top-left corner such that its in the\n    // viewport and allow for the trailing end of the overlay to go out of bounds.\n    if (overlay.width <= viewport.width) {\n      pushX = overflowLeft || -overflowRight;\n    } else {\n      pushX = start.x < this._viewportMargin ? viewport.left - scrollPosition.left - start.x : 0;\n    }\n\n    if (overlay.height <= viewport.height) {\n      pushY = overflowTop || -overflowBottom;\n    } else {\n      pushY = start.y < this._viewportMargin ? viewport.top - scrollPosition.top - start.y : 0;\n    }\n\n    this._previousPushAmount = {x: pushX, y: pushY};\n\n    return {\n      x: start.x + pushX,\n      y: start.y + pushY,\n    };\n  }\n\n  /**\n   * Applies a computed position to the overlay and emits a position change.\n   * @param position The position preference\n   * @param originPoint The point on the origin element where the overlay is connected.\n   */\n  private _applyPosition(position: ConnectedPosition, originPoint: Point) {\n    this._setTransformOrigin(position);\n    this._setOverlayElementStyles(originPoint, position);\n    this._setBoundingBoxStyles(originPoint, position);\n\n    if (position.panelClass) {\n      this._addPanelClasses(position.panelClass);\n    }\n\n    // Notify that the position has been changed along with its change properties.\n    // We only emit if we've got any subscriptions, because the scroll visibility\n    // calculations can be somewhat expensive.\n    if (this._positionChanges.observers.length) {\n      const scrollVisibility = this._getScrollVisibility();\n\n      // We're recalculating on scroll, but we only want to emit if anything\n      // changed since downstream code might be hitting the `NgZone`.\n      if (\n        position !== this._lastPosition ||\n        !this._lastScrollVisibility ||\n        !compareScrollVisibility(this._lastScrollVisibility, scrollVisibility)\n      ) {\n        const changeEvent = new ConnectedOverlayPositionChange(position, scrollVisibility);\n        this._positionChanges.next(changeEvent);\n      }\n\n      this._lastScrollVisibility = scrollVisibility;\n    }\n\n    // Save the last connected position in case the position needs to be re-calculated.\n    this._lastPosition = position;\n    this._isInitialRender = false;\n  }\n\n  /** Sets the transform origin based on the configured selector and the passed-in position.  */\n  private _setTransformOrigin(position: ConnectedPosition) {\n    if (!this._transformOriginSelector) {\n      return;\n    }\n\n    const elements: NodeListOf<HTMLElement> = this._boundingBox!.querySelectorAll(\n      this._transformOriginSelector,\n    );\n    let xOrigin: 'left' | 'right' | 'center';\n    let yOrigin: 'top' | 'bottom' | 'center' = position.overlayY;\n\n    if (position.overlayX === 'center') {\n      xOrigin = 'center';\n    } else if (this._isRtl()) {\n      xOrigin = position.overlayX === 'start' ? 'right' : 'left';\n    } else {\n      xOrigin = position.overlayX === 'start' ? 'left' : 'right';\n    }\n\n    for (let i = 0; i < elements.length; i++) {\n      elements[i].style.transformOrigin = `${xOrigin} ${yOrigin}`;\n    }\n  }\n\n  /**\n   * Gets the position and size of the overlay's sizing container.\n   *\n   * This method does no measuring and applies no styles so that we can cheaply compute the\n   * bounds for all positions and choose the best fit based on these results.\n   */\n  private _calculateBoundingBoxRect(origin: Point, position: ConnectedPosition): BoundingBoxRect {\n    const viewport = this._viewportRect;\n    const isRtl = this._isRtl();\n    let height: number, top: number, bottom: number;\n\n    if (position.overlayY === 'top') {\n      // Overlay is opening \"downward\" and thus is bound by the bottom viewport edge.\n      top = origin.y;\n      height = viewport.height - top + this._viewportMargin;\n    } else if (position.overlayY === 'bottom') {\n      // Overlay is opening \"upward\" and thus is bound by the top viewport edge. We need to add\n      // the viewport margin back in, because the viewport rect is narrowed down to remove the\n      // margin, whereas the `origin` position is calculated based on its `DOMRect`.\n      bottom = viewport.height - origin.y + this._viewportMargin * 2;\n      height = viewport.height - bottom + this._viewportMargin;\n    } else {\n      // If neither top nor bottom, it means that the overlay is vertically centered on the\n      // origin point. Note that we want the position relative to the viewport, rather than\n      // the page, which is why we don't use something like `viewport.bottom - origin.y` and\n      // `origin.y - viewport.top`.\n      const smallestDistanceToViewportEdge = Math.min(\n        viewport.bottom - origin.y + viewport.top,\n        origin.y,\n      );\n\n      const previousHeight = this._lastBoundingBoxSize.height;\n\n      height = smallestDistanceToViewportEdge * 2;\n      top = origin.y - smallestDistanceToViewportEdge;\n\n      if (height > previousHeight && !this._isInitialRender && !this._growAfterOpen) {\n        top = origin.y - previousHeight / 2;\n      }\n    }\n\n    // The overlay is opening 'right-ward' (the content flows to the right).\n    const isBoundedByRightViewportEdge =\n      (position.overlayX === 'start' && !isRtl) || (position.overlayX === 'end' && isRtl);\n\n    // The overlay is opening 'left-ward' (the content flows to the left).\n    const isBoundedByLeftViewportEdge =\n      (position.overlayX === 'end' && !isRtl) || (position.overlayX === 'start' && isRtl);\n\n    let width: number, left: number, right: number;\n\n    if (isBoundedByLeftViewportEdge) {\n      right = viewport.width - origin.x + this._viewportMargin * 2;\n      width = origin.x - this._viewportMargin;\n    } else if (isBoundedByRightViewportEdge) {\n      left = origin.x;\n      width = viewport.right - origin.x;\n    } else {\n      // If neither start nor end, it means that the overlay is horizontally centered on the\n      // origin point. Note that we want the position relative to the viewport, rather than\n      // the page, which is why we don't use something like `viewport.right - origin.x` and\n      // `origin.x - viewport.left`.\n      const smallestDistanceToViewportEdge = Math.min(\n        viewport.right - origin.x + viewport.left,\n        origin.x,\n      );\n      const previousWidth = this._lastBoundingBoxSize.width;\n\n      width = smallestDistanceToViewportEdge * 2;\n      left = origin.x - smallestDistanceToViewportEdge;\n\n      if (width > previousWidth && !this._isInitialRender && !this._growAfterOpen) {\n        left = origin.x - previousWidth / 2;\n      }\n    }\n\n    return {top: top!, left: left!, bottom: bottom!, right: right!, width, height};\n  }\n\n  /**\n   * Sets the position and size of the overlay's sizing wrapper. The wrapper is positioned on the\n   * origin's connection point and stretches to the bounds of the viewport.\n   *\n   * @param origin The point on the origin element where the overlay is connected.\n   * @param position The position preference\n   */\n  private _setBoundingBoxStyles(origin: Point, position: ConnectedPosition): void {\n    const boundingBoxRect = this._calculateBoundingBoxRect(origin, position);\n\n    // It's weird if the overlay *grows* while scrolling, so we take the last size into account\n    // when applying a new size.\n    if (!this._isInitialRender && !this._growAfterOpen) {\n      boundingBoxRect.height = Math.min(boundingBoxRect.height, this._lastBoundingBoxSize.height);\n      boundingBoxRect.width = Math.min(boundingBoxRect.width, this._lastBoundingBoxSize.width);\n    }\n\n    const styles = {} as CSSStyleDeclaration;\n\n    if (this._hasExactPosition()) {\n      styles.top = styles.left = '0';\n      styles.bottom = styles.right = styles.maxHeight = styles.maxWidth = '';\n      styles.width = styles.height = '100%';\n    } else {\n      const maxHeight = this._overlayRef.getConfig().maxHeight;\n      const maxWidth = this._overlayRef.getConfig().maxWidth;\n\n      styles.height = coerceCssPixelValue(boundingBoxRect.height);\n      styles.top = coerceCssPixelValue(boundingBoxRect.top);\n      styles.bottom = coerceCssPixelValue(boundingBoxRect.bottom);\n      styles.width = coerceCssPixelValue(boundingBoxRect.width);\n      styles.left = coerceCssPixelValue(boundingBoxRect.left);\n      styles.right = coerceCssPixelValue(boundingBoxRect.right);\n\n      // Push the pane content towards the proper direction.\n      if (position.overlayX === 'center') {\n        styles.alignItems = 'center';\n      } else {\n        styles.alignItems = position.overlayX === 'end' ? 'flex-end' : 'flex-start';\n      }\n\n      if (position.overlayY === 'center') {\n        styles.justifyContent = 'center';\n      } else {\n        styles.justifyContent = position.overlayY === 'bottom' ? 'flex-end' : 'flex-start';\n      }\n\n      if (maxHeight) {\n        styles.maxHeight = coerceCssPixelValue(maxHeight);\n      }\n\n      if (maxWidth) {\n        styles.maxWidth = coerceCssPixelValue(maxWidth);\n      }\n    }\n\n    this._lastBoundingBoxSize = boundingBoxRect;\n\n    extendStyles(this._boundingBox!.style, styles);\n  }\n\n  /** Resets the styles for the bounding box so that a new positioning can be computed. */\n  private _resetBoundingBoxStyles() {\n    extendStyles(this._boundingBox!.style, {\n      top: '0',\n      left: '0',\n      right: '0',\n      bottom: '0',\n      height: '',\n      width: '',\n      alignItems: '',\n      justifyContent: '',\n    } as CSSStyleDeclaration);\n  }\n\n  /** Resets the styles for the overlay pane so that a new positioning can be computed. */\n  private _resetOverlayElementStyles() {\n    extendStyles(this._pane.style, {\n      top: '',\n      left: '',\n      bottom: '',\n      right: '',\n      position: '',\n      transform: '',\n    } as CSSStyleDeclaration);\n  }\n\n  /** Sets positioning styles to the overlay element. */\n  private _setOverlayElementStyles(originPoint: Point, position: ConnectedPosition): void {\n    const styles = {} as CSSStyleDeclaration;\n    const hasExactPosition = this._hasExactPosition();\n    const hasFlexibleDimensions = this._hasFlexibleDimensions;\n    const config = this._overlayRef.getConfig();\n\n    if (hasExactPosition) {\n      const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n      extendStyles(styles, this._getExactOverlayY(position, originPoint, scrollPosition));\n      extendStyles(styles, this._getExactOverlayX(position, originPoint, scrollPosition));\n    } else {\n      styles.position = 'static';\n    }\n\n    // Use a transform to apply the offsets. We do this because the `center` positions rely on\n    // being in the normal flex flow and setting a `top` / `left` at all will completely throw\n    // off the position. We also can't use margins, because they won't have an effect in some\n    // cases where the element doesn't have anything to \"push off of\". Finally, this works\n    // better both with flexible and non-flexible positioning.\n    let transformString = '';\n    let offsetX = this._getOffset(position, 'x');\n    let offsetY = this._getOffset(position, 'y');\n\n    if (offsetX) {\n      transformString += `translateX(${offsetX}px) `;\n    }\n\n    if (offsetY) {\n      transformString += `translateY(${offsetY}px)`;\n    }\n\n    styles.transform = transformString.trim();\n\n    // If a maxWidth or maxHeight is specified on the overlay, we remove them. We do this because\n    // we need these values to both be set to \"100%\" for the automatic flexible sizing to work.\n    // The maxHeight and maxWidth are set on the boundingBox in order to enforce the constraint.\n    // Note that this doesn't apply when we have an exact position, in which case we do want to\n    // apply them because they'll be cleared from the bounding box.\n    if (config.maxHeight) {\n      if (hasExactPosition) {\n        styles.maxHeight = coerceCssPixelValue(config.maxHeight);\n      } else if (hasFlexibleDimensions) {\n        styles.maxHeight = '';\n      }\n    }\n\n    if (config.maxWidth) {\n      if (hasExactPosition) {\n        styles.maxWidth = coerceCssPixelValue(config.maxWidth);\n      } else if (hasFlexibleDimensions) {\n        styles.maxWidth = '';\n      }\n    }\n\n    extendStyles(this._pane.style, styles);\n  }\n\n  /** Gets the exact top/bottom for the overlay when not using flexible sizing or when pushing. */\n  private _getExactOverlayY(\n    position: ConnectedPosition,\n    originPoint: Point,\n    scrollPosition: ViewportScrollPosition,\n  ) {\n    // Reset any existing styles. This is necessary in case the\n    // preferred position has changed since the last `apply`.\n    let styles = {top: '', bottom: ''} as CSSStyleDeclaration;\n    let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n\n    if (this._isPushed) {\n      overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n    }\n\n    // We want to set either `top` or `bottom` based on whether the overlay wants to appear\n    // above or below the origin and the direction in which the element will expand.\n    if (position.overlayY === 'bottom') {\n      // When using `bottom`, we adjust the y position such that it is the distance\n      // from the bottom of the viewport rather than the top.\n      const documentHeight = this._document.documentElement!.clientHeight;\n      styles.bottom = `${documentHeight - (overlayPoint.y + this._overlayRect.height)}px`;\n    } else {\n      styles.top = coerceCssPixelValue(overlayPoint.y);\n    }\n\n    return styles;\n  }\n\n  /** Gets the exact left/right for the overlay when not using flexible sizing or when pushing. */\n  private _getExactOverlayX(\n    position: ConnectedPosition,\n    originPoint: Point,\n    scrollPosition: ViewportScrollPosition,\n  ) {\n    // Reset any existing styles. This is necessary in case the preferred position has\n    // changed since the last `apply`.\n    let styles = {left: '', right: ''} as CSSStyleDeclaration;\n    let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n\n    if (this._isPushed) {\n      overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n    }\n\n    // We want to set either `left` or `right` based on whether the overlay wants to appear \"before\"\n    // or \"after\" the origin, which determines the direction in which the element will expand.\n    // For the horizontal axis, the meaning of \"before\" and \"after\" change based on whether the\n    // page is in RTL or LTR.\n    let horizontalStyleProperty: 'left' | 'right';\n\n    if (this._isRtl()) {\n      horizontalStyleProperty = position.overlayX === 'end' ? 'left' : 'right';\n    } else {\n      horizontalStyleProperty = position.overlayX === 'end' ? 'right' : 'left';\n    }\n\n    // When we're setting `right`, we adjust the x position such that it is the distance\n    // from the right edge of the viewport rather than the left edge.\n    if (horizontalStyleProperty === 'right') {\n      const documentWidth = this._document.documentElement!.clientWidth;\n      styles.right = `${documentWidth - (overlayPoint.x + this._overlayRect.width)}px`;\n    } else {\n      styles.left = coerceCssPixelValue(overlayPoint.x);\n    }\n\n    return styles;\n  }\n\n  /**\n   * Gets the view properties of the trigger and overlay, including whether they are clipped\n   * or completely outside the view of any of the strategy's scrollables.\n   */\n  private _getScrollVisibility(): ScrollingVisibility {\n    // Note: needs fresh rects since the position could've changed.\n    const originBounds = this._getOriginRect();\n    const overlayBounds = this._pane.getBoundingClientRect();\n\n    // TODO(jelbourn): instead of needing all of the client rects for these scrolling containers\n    // every time, we should be able to use the scrollTop of the containers if the size of those\n    // containers hasn't changed.\n    const scrollContainerBounds = this._scrollables.map(scrollable => {\n      return scrollable.getElementRef().nativeElement.getBoundingClientRect();\n    });\n\n    return {\n      isOriginClipped: isElementClippedByScrolling(originBounds, scrollContainerBounds),\n      isOriginOutsideView: isElementScrolledOutsideView(originBounds, scrollContainerBounds),\n      isOverlayClipped: isElementClippedByScrolling(overlayBounds, scrollContainerBounds),\n      isOverlayOutsideView: isElementScrolledOutsideView(overlayBounds, scrollContainerBounds),\n    };\n  }\n\n  /** Subtracts the amount that an element is overflowing on an axis from its length. */\n  private _subtractOverflows(length: number, ...overflows: number[]): number {\n    return overflows.reduce((currentValue: number, currentOverflow: number) => {\n      return currentValue - Math.max(currentOverflow, 0);\n    }, length);\n  }\n\n  /** Narrows the given viewport rect by the current _viewportMargin. */\n  private _getNarrowedViewportRect(): Dimensions {\n    // We recalculate the viewport rect here ourselves, rather than using the ViewportRuler,\n    // because we want to use the `clientWidth` and `clientHeight` as the base. The difference\n    // being that the client properties don't include the scrollbar, as opposed to `innerWidth`\n    // and `innerHeight` that do. This is necessary, because the overlay container uses\n    // 100% `width` and `height` which don't include the scrollbar either.\n    const width = this._document.documentElement!.clientWidth;\n    const height = this._document.documentElement!.clientHeight;\n    const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n\n    return {\n      top: scrollPosition.top + this._viewportMargin,\n      left: scrollPosition.left + this._viewportMargin,\n      right: scrollPosition.left + width - this._viewportMargin,\n      bottom: scrollPosition.top + height - this._viewportMargin,\n      width: width - 2 * this._viewportMargin,\n      height: height - 2 * this._viewportMargin,\n    };\n  }\n\n  /** Whether the we're dealing with an RTL context */\n  private _isRtl() {\n    return this._overlayRef.getDirection() === 'rtl';\n  }\n\n  /** Determines whether the overlay uses exact or flexible positioning. */\n  private _hasExactPosition() {\n    return !this._hasFlexibleDimensions || this._isPushed;\n  }\n\n  /** Retrieves the offset of a position along the x or y axis. */\n  private _getOffset(position: ConnectedPosition, axis: 'x' | 'y') {\n    if (axis === 'x') {\n      // We don't do something like `position['offset' + axis]` in\n      // order to avoid breaking minifiers that rename properties.\n      return position.offsetX == null ? this._offsetX : position.offsetX;\n    }\n\n    return position.offsetY == null ? this._offsetY : position.offsetY;\n  }\n\n  /** Validates that the current position match the expected values. */\n  private _validatePositions(): void {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this._preferredPositions.length) {\n        throw Error('FlexibleConnectedPositionStrategy: At least one position is required.');\n      }\n\n      // TODO(crisbeto): remove these once Angular's template type\n      // checking is advanced enough to catch these cases.\n      this._preferredPositions.forEach(pair => {\n        validateHorizontalPosition('originX', pair.originX);\n        validateVerticalPosition('originY', pair.originY);\n        validateHorizontalPosition('overlayX', pair.overlayX);\n        validateVerticalPosition('overlayY', pair.overlayY);\n      });\n    }\n  }\n\n  /** Adds a single CSS class or an array of classes on the overlay panel. */\n  private _addPanelClasses(cssClasses: string | string[]) {\n    if (this._pane) {\n      coerceArray(cssClasses).forEach(cssClass => {\n        if (cssClass !== '' && this._appliedPanelClasses.indexOf(cssClass) === -1) {\n          this._appliedPanelClasses.push(cssClass);\n          this._pane.classList.add(cssClass);\n        }\n      });\n    }\n  }\n\n  /** Clears the classes that the position strategy has applied from the overlay panel. */\n  private _clearPanelClasses() {\n    if (this._pane) {\n      this._appliedPanelClasses.forEach(cssClass => {\n        this._pane.classList.remove(cssClass);\n      });\n      this._appliedPanelClasses = [];\n    }\n  }\n\n  /** Returns the DOMRect of the current origin. */\n  private _getOriginRect(): Dimensions {\n    const origin = this._origin;\n\n    if (origin instanceof ElementRef) {\n      return origin.nativeElement.getBoundingClientRect();\n    }\n\n    // Check for Element so SVG elements are also supported.\n    if (origin instanceof Element) {\n      return origin.getBoundingClientRect();\n    }\n\n    const width = origin.width || 0;\n    const height = origin.height || 0;\n\n    // If the origin is a point, return a client rect as if it was a 0x0 element at the point.\n    return {\n      top: origin.y,\n      bottom: origin.y + height,\n      left: origin.x,\n      right: origin.x + width,\n      height,\n      width,\n    };\n  }\n}\n\n/** A simple (x, y) coordinate. */\ninterface Point {\n  x: number;\n  y: number;\n}\n\n/** Record of measurements for how an overlay (at a given position) fits into the viewport. */\ninterface OverlayFit {\n  /** Whether the overlay fits completely in the viewport. */\n  isCompletelyWithinViewport: boolean;\n\n  /** Whether the overlay fits in the viewport on the y-axis. */\n  fitsInViewportVertically: boolean;\n\n  /** Whether the overlay fits in the viewport on the x-axis. */\n  fitsInViewportHorizontally: boolean;\n\n  /** The total visible area (in px^2) of the overlay inside the viewport. */\n  visibleArea: number;\n}\n\n/** Record of the measurements determining whether an overlay will fit in a specific position. */\ninterface FallbackPosition {\n  position: ConnectedPosition;\n  originPoint: Point;\n  overlayPoint: Point;\n  overlayFit: OverlayFit;\n  overlayRect: Dimensions;\n}\n\n/** Position and size of the overlay sizing wrapper for a specific position. */\ninterface BoundingBoxRect {\n  top: number;\n  left: number;\n  bottom: number;\n  right: number;\n  height: number;\n  width: number;\n}\n\n/** Record of measures determining how well a given position will fit with flexible dimensions. */\ninterface FlexibleFit {\n  position: ConnectedPosition;\n  origin: Point;\n  overlayRect: Dimensions;\n  boundingBoxRect: BoundingBoxRect;\n}\n\n/** A connected position as specified by the user. */\nexport interface ConnectedPosition {\n  originX: 'start' | 'center' | 'end';\n  originY: 'top' | 'center' | 'bottom';\n\n  overlayX: 'start' | 'center' | 'end';\n  overlayY: 'top' | 'center' | 'bottom';\n\n  weight?: number;\n  offsetX?: number;\n  offsetY?: number;\n  panelClass?: string | string[];\n}\n\n/** Shallow-extends a stylesheet object with another stylesheet object. */\nfunction extendStyles(\n  destination: CSSStyleDeclaration,\n  source: CSSStyleDeclaration,\n): CSSStyleDeclaration {\n  for (let key in source) {\n    if (source.hasOwnProperty(key)) {\n      destination[key] = source[key];\n    }\n  }\n\n  return destination;\n}\n\n/**\n * Extracts the pixel value as a number from a value, if it's a number\n * or a CSS pixel string (e.g. `1337px`). Otherwise returns null.\n */\nfunction getPixelValue(input: number | string | null | undefined): number | null {\n  if (typeof input !== 'number' && input != null) {\n    const [value, units] = input.split(cssUnitPattern);\n    return !units || units === 'px' ? parseFloat(value) : null;\n  }\n\n  return input || null;\n}\n\n/**\n * Gets a version of an element's bounding `DOMRect` where all the values are rounded down to\n * the nearest pixel. This allows us to account for the cases where there may be sub-pixel\n * deviations in the `DOMRect` returned by the browser (e.g. when zoomed in with a percentage\n * size, see #21350).\n */\nfunction getRoundedBoundingClientRect(clientRect: Dimensions): Dimensions {\n  return {\n    top: Math.floor(clientRect.top),\n    right: Math.floor(clientRect.right),\n    bottom: Math.floor(clientRect.bottom),\n    left: Math.floor(clientRect.left),\n    width: Math.floor(clientRect.width),\n    height: Math.floor(clientRect.height),\n  };\n}\n\n/** Returns whether two `ScrollingVisibility` objects are identical. */\nfunction compareScrollVisibility(a: ScrollingVisibility, b: ScrollingVisibility): boolean {\n  if (a === b) {\n    return true;\n  }\n\n  return (\n    a.isOriginClipped === b.isOriginClipped &&\n    a.isOriginOutsideView === b.isOriginOutsideView &&\n    a.isOverlayClipped === b.isOverlayClipped &&\n    a.isOverlayOutsideView === b.isOverlayOutsideView\n  );\n}\n\nexport const STANDARD_DROPDOWN_BELOW_POSITIONS: ConnectedPosition[] = [\n  {originX: 'start', originY: 'bottom', overlayX: 'start', overlayY: 'top'},\n  {originX: 'start', originY: 'top', overlayX: 'start', overlayY: 'bottom'},\n  {originX: 'end', originY: 'bottom', overlayX: 'end', overlayY: 'top'},\n  {originX: 'end', originY: 'top', overlayX: 'end', overlayY: 'bottom'},\n];\n\nexport const STANDARD_DROPDOWN_ADJACENT_POSITIONS: ConnectedPosition[] = [\n  {originX: 'end', originY: 'top', overlayX: 'start', overlayY: 'top'},\n  {originX: 'end', originY: 'bottom', overlayX: 'start', overlayY: 'bottom'},\n  {originX: 'start', originY: 'top', overlayX: 'end', overlayY: 'top'},\n  {originX: 'start', originY: 'bottom', overlayX: 'end', overlayY: 'bottom'},\n];\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {OverlayRef} from '../overlay-ref';\nimport {PositionStrategy} from './position-strategy';\n\n/** Class to be added to the overlay pane wrapper. */\nconst wrapperClass = 'cdk-global-overlay-wrapper';\n\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * explicit position relative to the browser's viewport. We use flexbox, instead of\n * transforms, in order to avoid issues with subpixel rendering which can cause the\n * element to become blurry.\n */\nexport class GlobalPositionStrategy implements PositionStrategy {\n  /** The overlay to which this strategy is attached. */\n  private _overlayRef: OverlayRef;\n  private _cssPosition = 'static';\n  private _topOffset = '';\n  private _bottomOffset = '';\n  private _alignItems = '';\n  private _xPosition = '';\n  private _xOffset = '';\n  private _width = '';\n  private _height = '';\n  private _isDisposed = false;\n\n  attach(overlayRef: OverlayRef): void {\n    const config = overlayRef.getConfig();\n\n    this._overlayRef = overlayRef;\n\n    if (this._width && !config.width) {\n      overlayRef.updateSize({width: this._width});\n    }\n\n    if (this._height && !config.height) {\n      overlayRef.updateSize({height: this._height});\n    }\n\n    overlayRef.hostElement.classList.add(wrapperClass);\n    this._isDisposed = false;\n  }\n\n  /**\n   * Sets the top position of the overlay. Clears any previously set vertical position.\n   * @param value New top offset.\n   */\n  top(value: string = ''): this {\n    this._bottomOffset = '';\n    this._topOffset = value;\n    this._alignItems = 'flex-start';\n    return this;\n  }\n\n  /**\n   * Sets the left position of the overlay. Clears any previously set horizontal position.\n   * @param value New left offset.\n   */\n  left(value: string = ''): this {\n    this._xOffset = value;\n    this._xPosition = 'left';\n    return this;\n  }\n\n  /**\n   * Sets the bottom position of the overlay. Clears any previously set vertical position.\n   * @param value New bottom offset.\n   */\n  bottom(value: string = ''): this {\n    this._topOffset = '';\n    this._bottomOffset = value;\n    this._alignItems = 'flex-end';\n    return this;\n  }\n\n  /**\n   * Sets the right position of the overlay. Clears any previously set horizontal position.\n   * @param value New right offset.\n   */\n  right(value: string = ''): this {\n    this._xOffset = value;\n    this._xPosition = 'right';\n    return this;\n  }\n\n  /**\n   * Sets the overlay to the start of the viewport, depending on the overlay direction.\n   * This will be to the left in LTR layouts and to the right in RTL.\n   * @param offset Offset from the edge of the screen.\n   */\n  start(value: string = ''): this {\n    this._xOffset = value;\n    this._xPosition = 'start';\n    return this;\n  }\n\n  /**\n   * Sets the overlay to the end of the viewport, depending on the overlay direction.\n   * This will be to the right in LTR layouts and to the left in RTL.\n   * @param offset Offset from the edge of the screen.\n   */\n  end(value: string = ''): this {\n    this._xOffset = value;\n    this._xPosition = 'end';\n    return this;\n  }\n\n  /**\n   * Sets the overlay width and clears any previously set width.\n   * @param value New width for the overlay\n   * @deprecated Pass the `width` through the `OverlayConfig`.\n   * @breaking-change 8.0.0\n   */\n  width(value: string = ''): this {\n    if (this._overlayRef) {\n      this._overlayRef.updateSize({width: value});\n    } else {\n      this._width = value;\n    }\n\n    return this;\n  }\n\n  /**\n   * Sets the overlay height and clears any previously set height.\n   * @param value New height for the overlay\n   * @deprecated Pass the `height` through the `OverlayConfig`.\n   * @breaking-change 8.0.0\n   */\n  height(value: string = ''): this {\n    if (this._overlayRef) {\n      this._overlayRef.updateSize({height: value});\n    } else {\n      this._height = value;\n    }\n\n    return this;\n  }\n\n  /**\n   * Centers the overlay horizontally with an optional offset.\n   * Clears any previously set horizontal position.\n   *\n   * @param offset Overlay offset from the horizontal center.\n   */\n  centerHorizontally(offset: string = ''): this {\n    this.left(offset);\n    this._xPosition = 'center';\n    return this;\n  }\n\n  /**\n   * Centers the overlay vertically with an optional offset.\n   * Clears any previously set vertical position.\n   *\n   * @param offset Overlay offset from the vertical center.\n   */\n  centerVertically(offset: string = ''): this {\n    this.top(offset);\n    this._alignItems = 'center';\n    return this;\n  }\n\n  /**\n   * Apply the position to the element.\n   * @docs-private\n   */\n  apply(): void {\n    // Since the overlay ref applies the strategy asynchronously, it could\n    // have been disposed before it ends up being applied. If that is the\n    // case, we shouldn't do anything.\n    if (!this._overlayRef || !this._overlayRef.hasAttached()) {\n      return;\n    }\n\n    const styles = this._overlayRef.overlayElement.style;\n    const parentStyles = this._overlayRef.hostElement.style;\n    const config = this._overlayRef.getConfig();\n    const {width, height, maxWidth, maxHeight} = config;\n    const shouldBeFlushHorizontally =\n      (width === '100%' || width === '100vw') &&\n      (!maxWidth || maxWidth === '100%' || maxWidth === '100vw');\n    const shouldBeFlushVertically =\n      (height === '100%' || height === '100vh') &&\n      (!maxHeight || maxHeight === '100%' || maxHeight === '100vh');\n    const xPosition = this._xPosition;\n    const xOffset = this._xOffset;\n    const isRtl = this._overlayRef.getConfig().direction === 'rtl';\n    let marginLeft = '';\n    let marginRight = '';\n    let justifyContent = '';\n\n    if (shouldBeFlushHorizontally) {\n      justifyContent = 'flex-start';\n    } else if (xPosition === 'center') {\n      justifyContent = 'center';\n\n      if (isRtl) {\n        marginRight = xOffset;\n      } else {\n        marginLeft = xOffset;\n      }\n    } else if (isRtl) {\n      if (xPosition === 'left' || xPosition === 'end') {\n        justifyContent = 'flex-end';\n        marginLeft = xOffset;\n      } else if (xPosition === 'right' || xPosition === 'start') {\n        justifyContent = 'flex-start';\n        marginRight = xOffset;\n      }\n    } else if (xPosition === 'left' || xPosition === 'start') {\n      justifyContent = 'flex-start';\n      marginLeft = xOffset;\n    } else if (xPosition === 'right' || xPosition === 'end') {\n      justifyContent = 'flex-end';\n      marginRight = xOffset;\n    }\n\n    styles.position = this._cssPosition;\n    styles.marginLeft = shouldBeFlushHorizontally ? '0' : marginLeft;\n    styles.marginTop = shouldBeFlushVertically ? '0' : this._topOffset;\n    styles.marginBottom = this._bottomOffset;\n    styles.marginRight = shouldBeFlushHorizontally ? '0' : marginRight;\n    parentStyles.justifyContent = justifyContent;\n    parentStyles.alignItems = shouldBeFlushVertically ? 'flex-start' : this._alignItems;\n  }\n\n  /**\n   * Cleans up the DOM changes from the position strategy.\n   * @docs-private\n   */\n  dispose(): void {\n    if (this._isDisposed || !this._overlayRef) {\n      return;\n    }\n\n    const styles = this._overlayRef.overlayElement.style;\n    const parent = this._overlayRef.hostElement;\n    const parentStyles = parent.style;\n\n    parent.classList.remove(wrapperClass);\n    parentStyles.justifyContent =\n      parentStyles.alignItems =\n      styles.marginTop =\n      styles.marginBottom =\n      styles.marginLeft =\n      styles.marginRight =\n      styles.position =\n        '';\n\n    this._overlayRef = null!;\n    this._isDisposed = true;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Platform} from '@angular/cdk/platform';\nimport {ViewportRuler} from '@angular/cdk/scrolling';\nimport {DOCUMENT} from '@angular/common';\nimport {Inject, Injectable} from '@angular/core';\nimport {OverlayContainer} from '../overlay-container';\nimport {\n  FlexibleConnectedPositionStrategy,\n  FlexibleConnectedPositionStrategyOrigin,\n} from './flexible-connected-position-strategy';\nimport {GlobalPositionStrategy} from './global-position-strategy';\n\n/** Builder for overlay position strategy. */\n@Injectable({providedIn: 'root'})\nexport class OverlayPositionBuilder {\n  constructor(\n    private _viewportRuler: ViewportRuler,\n    @Inject(DOCUMENT) private _document: any,\n    private _platform: Platform,\n    private _overlayContainer: OverlayContainer,\n  ) {}\n\n  /**\n   * Creates a global position strategy.\n   */\n  global(): GlobalPositionStrategy {\n    return new GlobalPositionStrategy();\n  }\n\n  /**\n   * Creates a flexible position strategy.\n   * @param origin Origin relative to which to position the overlay.\n   */\n  flexibleConnectedTo(\n    origin: FlexibleConnectedPositionStrategyOrigin,\n  ): FlexibleConnectedPositionStrategy {\n    return new FlexibleConnectedPositionStrategy(\n      origin,\n      this._viewportRuler,\n      this._document,\n      this._platform,\n      this._overlayContainer,\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directionality} from '@angular/cdk/bidi';\nimport {DomPortalOutlet} from '@angular/cdk/portal';\nimport {DOCUMENT, Location} from '@angular/common';\nimport {\n  ApplicationRef,\n  ComponentFactoryResolver,\n  Inject,\n  Injectable,\n  Injector,\n  NgZone,\n  ANIMATION_MODULE_TYPE,\n  Optional,\n} from '@angular/core';\nimport {OverlayKeyboardDispatcher} from './dispatchers/overlay-keyboard-dispatcher';\nimport {OverlayOutsideClickDispatcher} from './dispatchers/overlay-outside-click-dispatcher';\nimport {OverlayConfig} from './overlay-config';\nimport {OverlayContainer} from './overlay-container';\nimport {OverlayRef} from './overlay-ref';\nimport {OverlayPositionBuilder} from './position/overlay-position-builder';\nimport {ScrollStrategyOptions} from './scroll/index';\n\n/** Next overlay unique ID. */\nlet nextUniqueId = 0;\n\n// Note that Overlay is *not* scoped to the app root because of the ComponentFactoryResolver\n// which needs to be different depending on where OverlayModule is imported.\n\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalOutlet, so any kind of Portal can be loaded into one.\n */\n@Injectable({providedIn: 'root'})\nexport class Overlay {\n  private _appRef: ApplicationRef;\n\n  constructor(\n    /** Scrolling strategies that can be used when creating an overlay. */\n    public scrollStrategies: ScrollStrategyOptions,\n    private _overlayContainer: OverlayContainer,\n    private _componentFactoryResolver: ComponentFactoryResolver,\n    private _positionBuilder: OverlayPositionBuilder,\n    private _keyboardDispatcher: OverlayKeyboardDispatcher,\n    private _injector: Injector,\n    private _ngZone: NgZone,\n    @Inject(DOCUMENT) private _document: any,\n    private _directionality: Directionality,\n    private _location: Location,\n    private _outsideClickDispatcher: OverlayOutsideClickDispatcher,\n    @Inject(ANIMATION_MODULE_TYPE) @Optional() private _animationsModuleType?: string,\n  ) {}\n\n  /**\n   * Creates an overlay.\n   * @param config Configuration applied to the overlay.\n   * @returns Reference to the created overlay.\n   */\n  create(config?: OverlayConfig): OverlayRef {\n    const host = this._createHostElement();\n    const pane = this._createPaneElement(host);\n    const portalOutlet = this._createPortalOutlet(pane);\n    const overlayConfig = new OverlayConfig(config);\n\n    overlayConfig.direction = overlayConfig.direction || this._directionality.value;\n\n    return new OverlayRef(\n      portalOutlet,\n      host,\n      pane,\n      overlayConfig,\n      this._ngZone,\n      this._keyboardDispatcher,\n      this._document,\n      this._location,\n      this._outsideClickDispatcher,\n      this._animationsModuleType === 'NoopAnimations',\n    );\n  }\n\n  /**\n   * Gets a position builder that can be used, via fluent API,\n   * to construct and configure a position strategy.\n   * @returns An overlay position builder.\n   */\n  position(): OverlayPositionBuilder {\n    return this._positionBuilder;\n  }\n\n  /**\n   * Creates the DOM element for an overlay and appends it to the overlay container.\n   * @returns Newly-created pane element\n   */\n  private _createPaneElement(host: HTMLElement): HTMLElement {\n    const pane = this._document.createElement('div');\n\n    pane.id = `cdk-overlay-${nextUniqueId++}`;\n    pane.classList.add('cdk-overlay-pane');\n    host.appendChild(pane);\n\n    return pane;\n  }\n\n  /**\n   * Creates the host element that wraps around an overlay\n   * and can be used for advanced positioning.\n   * @returns Newly-create host element.\n   */\n  private _createHostElement(): HTMLElement {\n    const host = this._document.createElement('div');\n    this._overlayContainer.getContainerElement().appendChild(host);\n    return host;\n  }\n\n  /**\n   * Create a DomPortalOutlet into which the overlay content can be loaded.\n   * @param pane The DOM element to turn into a portal outlet.\n   * @returns A portal outlet for the given DOM element.\n   */\n  private _createPortalOutlet(pane: HTMLElement): DomPortalOutlet {\n    // We have to resolve the ApplicationRef later in order to allow people\n    // to use overlay-based providers during app initialization.\n    if (!this._appRef) {\n      this._appRef = this._injector.get<ApplicationRef>(ApplicationRef);\n    }\n\n    return new DomPortalOutlet(\n      pane,\n      this._componentFactoryResolver,\n      this._appRef,\n      this._injector,\n      this._document,\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Direction, Directionality} from '@angular/cdk/bidi';\nimport {ESCAPE, hasModifierKey} from '@angular/cdk/keycodes';\nimport {TemplatePortal} from '@angular/cdk/portal';\nimport {\n  Directive,\n  ElementRef,\n  EventEmitter,\n  Inject,\n  InjectionToken,\n  Input,\n  NgZone,\n  OnChanges,\n  OnDestroy,\n  Optional,\n  Output,\n  SimpleChanges,\n  TemplateRef,\n  ViewContainerRef,\n  booleanAttribute,\n  inject,\n} from '@angular/core';\nimport {_getEventTarget} from '@angular/cdk/platform';\nimport {Subscription} from 'rxjs';\nimport {takeWhile} from 'rxjs/operators';\nimport {Overlay} from './overlay';\nimport {OverlayConfig} from './overlay-config';\nimport {OverlayRef} from './overlay-ref';\nimport {ConnectedOverlayPositionChange} from './position/connected-position';\nimport {\n  ConnectedPosition,\n  FlexibleConnectedPositionStrategy,\n  FlexibleConnectedPositionStrategyOrigin,\n} from './position/flexible-connected-position-strategy';\nimport {RepositionScrollStrategy, ScrollStrategy} from './scroll/index';\n\n/** Default set of positions for the overlay. Follows the behavior of a dropdown. */\nconst defaultPositionList: ConnectedPosition[] = [\n  {\n    originX: 'start',\n    originY: 'bottom',\n    overlayX: 'start',\n    overlayY: 'top',\n  },\n  {\n    originX: 'start',\n    originY: 'top',\n    overlayX: 'start',\n    overlayY: 'bottom',\n  },\n  {\n    originX: 'end',\n    originY: 'top',\n    overlayX: 'end',\n    overlayY: 'bottom',\n  },\n  {\n    originX: 'end',\n    originY: 'bottom',\n    overlayX: 'end',\n    overlayY: 'top',\n  },\n];\n\n/** Injection token that determines the scroll handling while the connected overlay is open. */\nexport const CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY = new InjectionToken<() => ScrollStrategy>(\n  'cdk-connected-overlay-scroll-strategy',\n  {\n    providedIn: 'root',\n    factory: () => {\n      const overlay = inject(Overlay);\n      return () => overlay.scrollStrategies.reposition();\n    },\n  },\n);\n\n/**\n * Directive applied to an element to make it usable as an origin for an Overlay using a\n * ConnectedPositionStrategy.\n */\n@Directive({\n  selector: '[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]',\n  exportAs: 'cdkOverlayOrigin',\n  standalone: true,\n})\nexport class CdkOverlayOrigin {\n  constructor(\n    /** Reference to the element on which the directive is applied. */\n    public elementRef: ElementRef,\n  ) {}\n}\n\n/**\n * Directive to facilitate declarative creation of an\n * Overlay using a FlexibleConnectedPositionStrategy.\n */\n@Directive({\n  selector: '[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]',\n  exportAs: 'cdkConnectedOverlay',\n  standalone: true,\n})\nexport class CdkConnectedOverlay implements OnDestroy, OnChanges {\n  private _overlayRef: OverlayRef;\n  private _templatePortal: TemplatePortal;\n  private _backdropSubscription = Subscription.EMPTY;\n  private _attachSubscription = Subscription.EMPTY;\n  private _detachSubscription = Subscription.EMPTY;\n  private _positionSubscription = Subscription.EMPTY;\n  private _offsetX: number;\n  private _offsetY: number;\n  private _position: FlexibleConnectedPositionStrategy;\n  private _scrollStrategyFactory: () => ScrollStrategy;\n  private _disposeOnNavigation = false;\n  private _ngZone = inject(NgZone);\n\n  /** Origin for the connected overlay. */\n  @Input('cdkConnectedOverlayOrigin')\n  origin: CdkOverlayOrigin | FlexibleConnectedPositionStrategyOrigin;\n\n  /** Registered connected position pairs. */\n  @Input('cdkConnectedOverlayPositions') positions: ConnectedPosition[];\n\n  /**\n   * This input overrides the positions input if specified. It lets users pass\n   * in arbitrary positioning strategies.\n   */\n  @Input('cdkConnectedOverlayPositionStrategy') positionStrategy: FlexibleConnectedPositionStrategy;\n\n  /** The offset in pixels for the overlay connection point on the x-axis */\n  @Input('cdkConnectedOverlayOffsetX')\n  get offsetX(): number {\n    return this._offsetX;\n  }\n  set offsetX(offsetX: number) {\n    this._offsetX = offsetX;\n\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n    }\n  }\n\n  /** The offset in pixels for the overlay connection point on the y-axis */\n  @Input('cdkConnectedOverlayOffsetY')\n  get offsetY() {\n    return this._offsetY;\n  }\n  set offsetY(offsetY: number) {\n    this._offsetY = offsetY;\n\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n    }\n  }\n\n  /** The width of the overlay panel. */\n  @Input('cdkConnectedOverlayWidth') width: number | string;\n\n  /** The height of the overlay panel. */\n  @Input('cdkConnectedOverlayHeight') height: number | string;\n\n  /** The min width of the overlay panel. */\n  @Input('cdkConnectedOverlayMinWidth') minWidth: number | string;\n\n  /** The min height of the overlay panel. */\n  @Input('cdkConnectedOverlayMinHeight') minHeight: number | string;\n\n  /** The custom class to be set on the backdrop element. */\n  @Input('cdkConnectedOverlayBackdropClass') backdropClass: string | string[];\n\n  /** The custom class to add to the overlay pane element. */\n  @Input('cdkConnectedOverlayPanelClass') panelClass: string | string[];\n\n  /** Margin between the overlay and the viewport edges. */\n  @Input('cdkConnectedOverlayViewportMargin') viewportMargin: number = 0;\n\n  /** Strategy to be used when handling scroll events while the overlay is open. */\n  @Input('cdkConnectedOverlayScrollStrategy') scrollStrategy: ScrollStrategy;\n\n  /** Whether the overlay is open. */\n  @Input('cdkConnectedOverlayOpen') open: boolean = false;\n\n  /** Whether the overlay can be closed by user interaction. */\n  @Input('cdkConnectedOverlayDisableClose') disableClose: boolean = false;\n\n  /** CSS selector which to set the transform origin. */\n  @Input('cdkConnectedOverlayTransformOriginOn') transformOriginSelector: string;\n\n  /** Whether or not the overlay should attach a backdrop. */\n  @Input({alias: 'cdkConnectedOverlayHasBackdrop', transform: booleanAttribute})\n  hasBackdrop: boolean = false;\n\n  /** Whether or not the overlay should be locked when scrolling. */\n  @Input({alias: 'cdkConnectedOverlayLockPosition', transform: booleanAttribute})\n  lockPosition: boolean = false;\n\n  /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n  @Input({alias: 'cdkConnectedOverlayFlexibleDimensions', transform: booleanAttribute})\n  flexibleDimensions: boolean = false;\n\n  /** Whether the overlay can grow after the initial open when flexible positioning is turned on. */\n  @Input({alias: 'cdkConnectedOverlayGrowAfterOpen', transform: booleanAttribute})\n  growAfterOpen: boolean = false;\n\n  /** Whether the overlay can be pushed on-screen if none of the provided positions fit. */\n  @Input({alias: 'cdkConnectedOverlayPush', transform: booleanAttribute}) push: boolean = false;\n\n  /** Whether the overlay should be disposed of when the user goes backwards/forwards in history. */\n  @Input({alias: 'cdkConnectedOverlayDisposeOnNavigation', transform: booleanAttribute})\n  get disposeOnNavigation(): boolean {\n    return this._disposeOnNavigation;\n  }\n  set disposeOnNavigation(value: boolean) {\n    this._disposeOnNavigation = value;\n  }\n\n  /** Event emitted when the backdrop is clicked. */\n  @Output() readonly backdropClick = new EventEmitter<MouseEvent>();\n\n  /** Event emitted when the position has changed. */\n  @Output() readonly positionChange = new EventEmitter<ConnectedOverlayPositionChange>();\n\n  /** Event emitted when the overlay has been attached. */\n  @Output() readonly attach = new EventEmitter<void>();\n\n  /** Event emitted when the overlay has been detached. */\n  @Output() readonly detach = new EventEmitter<void>();\n\n  /** Emits when there are keyboard events that are targeted at the overlay. */\n  @Output() readonly overlayKeydown = new EventEmitter<KeyboardEvent>();\n\n  /** Emits when there are mouse outside click events that are targeted at the overlay. */\n  @Output() readonly overlayOutsideClick = new EventEmitter<MouseEvent>();\n\n  // TODO(jelbourn): inputs for size, scroll behavior, animation, etc.\n\n  constructor(\n    private _overlay: Overlay,\n    templateRef: TemplateRef<any>,\n    viewContainerRef: ViewContainerRef,\n    @Inject(CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY) scrollStrategyFactory: any,\n    @Optional() private _dir: Directionality,\n  ) {\n    this._templatePortal = new TemplatePortal(templateRef, viewContainerRef);\n    this._scrollStrategyFactory = scrollStrategyFactory;\n    this.scrollStrategy = this._scrollStrategyFactory();\n  }\n\n  /** The associated overlay reference. */\n  get overlayRef(): OverlayRef {\n    return this._overlayRef;\n  }\n\n  /** The element's layout direction. */\n  get dir(): Direction {\n    return this._dir ? this._dir.value : 'ltr';\n  }\n\n  ngOnDestroy() {\n    this._attachSubscription.unsubscribe();\n    this._detachSubscription.unsubscribe();\n    this._backdropSubscription.unsubscribe();\n    this._positionSubscription.unsubscribe();\n\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n    }\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n      this._overlayRef.updateSize({\n        width: this.width,\n        minWidth: this.minWidth,\n        height: this.height,\n        minHeight: this.minHeight,\n      });\n\n      if (changes['origin'] && this.open) {\n        this._position.apply();\n      }\n    }\n\n    if (changes['open']) {\n      this.open ? this._attachOverlay() : this._detachOverlay();\n    }\n  }\n\n  /** Creates an overlay */\n  private _createOverlay() {\n    if (!this.positions || !this.positions.length) {\n      this.positions = defaultPositionList;\n    }\n\n    const overlayRef = (this._overlayRef = this._overlay.create(this._buildConfig()));\n    this._attachSubscription = overlayRef.attachments().subscribe(() => this.attach.emit());\n    this._detachSubscription = overlayRef.detachments().subscribe(() => this.detach.emit());\n    overlayRef.keydownEvents().subscribe((event: KeyboardEvent) => {\n      this.overlayKeydown.next(event);\n\n      if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n        event.preventDefault();\n        this._detachOverlay();\n      }\n    });\n\n    this._overlayRef.outsidePointerEvents().subscribe((event: MouseEvent) => {\n      const origin = this._getOriginElement();\n      const target = _getEventTarget(event) as Element | null;\n\n      if (!origin || (origin !== target && !origin.contains(target))) {\n        this.overlayOutsideClick.next(event);\n      }\n    });\n  }\n\n  /** Builds the overlay config based on the directive's inputs */\n  private _buildConfig(): OverlayConfig {\n    const positionStrategy = (this._position =\n      this.positionStrategy || this._createPositionStrategy());\n    const overlayConfig = new OverlayConfig({\n      direction: this._dir,\n      positionStrategy,\n      scrollStrategy: this.scrollStrategy,\n      hasBackdrop: this.hasBackdrop,\n      disposeOnNavigation: this.disposeOnNavigation,\n    });\n\n    if (this.width || this.width === 0) {\n      overlayConfig.width = this.width;\n    }\n\n    if (this.height || this.height === 0) {\n      overlayConfig.height = this.height;\n    }\n\n    if (this.minWidth || this.minWidth === 0) {\n      overlayConfig.minWidth = this.minWidth;\n    }\n\n    if (this.minHeight || this.minHeight === 0) {\n      overlayConfig.minHeight = this.minHeight;\n    }\n\n    if (this.backdropClass) {\n      overlayConfig.backdropClass = this.backdropClass;\n    }\n\n    if (this.panelClass) {\n      overlayConfig.panelClass = this.panelClass;\n    }\n\n    return overlayConfig;\n  }\n\n  /** Updates the state of a position strategy, based on the values of the directive inputs. */\n  private _updatePositionStrategy(positionStrategy: FlexibleConnectedPositionStrategy) {\n    const positions: ConnectedPosition[] = this.positions.map(currentPosition => ({\n      originX: currentPosition.originX,\n      originY: currentPosition.originY,\n      overlayX: currentPosition.overlayX,\n      overlayY: currentPosition.overlayY,\n      offsetX: currentPosition.offsetX || this.offsetX,\n      offsetY: currentPosition.offsetY || this.offsetY,\n      panelClass: currentPosition.panelClass || undefined,\n    }));\n\n    return positionStrategy\n      .setOrigin(this._getOrigin())\n      .withPositions(positions)\n      .withFlexibleDimensions(this.flexibleDimensions)\n      .withPush(this.push)\n      .withGrowAfterOpen(this.growAfterOpen)\n      .withViewportMargin(this.viewportMargin)\n      .withLockedPosition(this.lockPosition)\n      .withTransformOriginOn(this.transformOriginSelector);\n  }\n\n  /** Returns the position strategy of the overlay to be set on the overlay config */\n  private _createPositionStrategy(): FlexibleConnectedPositionStrategy {\n    const strategy = this._overlay.position().flexibleConnectedTo(this._getOrigin());\n    this._updatePositionStrategy(strategy);\n    return strategy;\n  }\n\n  private _getOrigin(): FlexibleConnectedPositionStrategyOrigin {\n    if (this.origin instanceof CdkOverlayOrigin) {\n      return this.origin.elementRef;\n    } else {\n      return this.origin;\n    }\n  }\n\n  private _getOriginElement(): Element | null {\n    if (this.origin instanceof CdkOverlayOrigin) {\n      return this.origin.elementRef.nativeElement;\n    }\n\n    if (this.origin instanceof ElementRef) {\n      return this.origin.nativeElement;\n    }\n\n    if (typeof Element !== 'undefined' && this.origin instanceof Element) {\n      return this.origin;\n    }\n\n    return null;\n  }\n\n  /** Attaches the overlay and subscribes to backdrop clicks if backdrop exists */\n  private _attachOverlay() {\n    if (!this._overlayRef) {\n      this._createOverlay();\n    } else {\n      // Update the overlay size, in case the directive's inputs have changed\n      this._overlayRef.getConfig().hasBackdrop = this.hasBackdrop;\n    }\n\n    if (!this._overlayRef.hasAttached()) {\n      this._overlayRef.attach(this._templatePortal);\n    }\n\n    if (this.hasBackdrop) {\n      this._backdropSubscription = this._overlayRef.backdropClick().subscribe(event => {\n        this.backdropClick.emit(event);\n      });\n    } else {\n      this._backdropSubscription.unsubscribe();\n    }\n\n    this._positionSubscription.unsubscribe();\n\n    // Only subscribe to `positionChanges` if requested, because putting\n    // together all the information for it can be expensive.\n    if (this.positionChange.observers.length > 0) {\n      this._positionSubscription = this._position.positionChanges\n        .pipe(takeWhile(() => this.positionChange.observers.length > 0))\n        .subscribe(position => {\n          this._ngZone.run(() => this.positionChange.emit(position));\n\n          if (this.positionChange.observers.length === 0) {\n            this._positionSubscription.unsubscribe();\n          }\n        });\n    }\n  }\n\n  /** Detaches the overlay and unsubscribes to backdrop clicks if backdrop exists */\n  private _detachOverlay() {\n    if (this._overlayRef) {\n      this._overlayRef.detach();\n    }\n\n    this._backdropSubscription.unsubscribe();\n    this._positionSubscription.unsubscribe();\n  }\n}\n\n/** @docs-private */\nexport function CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY(\n  overlay: Overlay,\n): () => RepositionScrollStrategy {\n  return () => overlay.scrollStrategies.reposition();\n}\n\n/** @docs-private */\nexport const CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER = {\n  provide: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {BidiModule} from '@angular/cdk/bidi';\nimport {PortalModule} from '@angular/cdk/portal';\nimport {ScrollingModule} from '@angular/cdk/scrolling';\nimport {NgModule} from '@angular/core';\nimport {Overlay} from './overlay';\nimport {\n  CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER,\n  CdkConnectedOverlay,\n  CdkOverlayOrigin,\n} from './overlay-directives';\n\n@NgModule({\n  imports: [BidiModule, PortalModule, ScrollingModule, CdkConnectedOverlay, CdkOverlayOrigin],\n  exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule],\n  providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER],\n})\nexport class OverlayModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injectable, Inject, OnDestroy} from '@angular/core';\nimport {OverlayContainer} from './overlay-container';\nimport {DOCUMENT} from '@angular/common';\nimport {Platform} from '@angular/cdk/platform';\n\n/**\n * Alternative to OverlayContainer that supports correct displaying of overlay elements in\n * Fullscreen mode\n * https://developer.mozilla.org/en-US/docs/Web/API/Element/requestFullScreen\n *\n * Should be provided in the root component.\n */\n@Injectable({providedIn: 'root'})\nexport class FullscreenOverlayContainer extends OverlayContainer implements OnDestroy {\n  private _fullScreenEventName: string | undefined;\n  private _fullScreenListener: () => void;\n\n  constructor(@Inject(DOCUMENT) _document: any, platform: Platform) {\n    super(_document, platform);\n  }\n\n  override ngOnDestroy() {\n    super.ngOnDestroy();\n\n    if (this._fullScreenEventName && this._fullScreenListener) {\n      this._document.removeEventListener(this._fullScreenEventName, this._fullScreenListener);\n    }\n  }\n\n  protected override _createContainer(): void {\n    super._createContainer();\n    this._adjustParentForFullscreenChange();\n    this._addFullscreenChangeListener(() => this._adjustParentForFullscreenChange());\n  }\n\n  private _adjustParentForFullscreenChange(): void {\n    if (!this._containerElement) {\n      return;\n    }\n\n    const fullscreenElement = this.getFullscreenElement();\n    const parent = fullscreenElement || this._document.body;\n    parent.appendChild(this._containerElement);\n  }\n\n  private _addFullscreenChangeListener(fn: () => void) {\n    const eventName = this._getEventName();\n\n    if (eventName) {\n      if (this._fullScreenListener) {\n        this._document.removeEventListener(eventName, this._fullScreenListener);\n      }\n\n      this._document.addEventListener(eventName, fn);\n      this._fullScreenListener = fn;\n    }\n  }\n\n  private _getEventName(): string | undefined {\n    if (!this._fullScreenEventName) {\n      const _document = this._document as any;\n\n      if (_document.fullscreenEnabled) {\n        this._fullScreenEventName = 'fullscreenchange';\n      } else if (_document.webkitFullscreenEnabled) {\n        this._fullScreenEventName = 'webkitfullscreenchange';\n      } else if (_document.mozFullScreenEnabled) {\n        this._fullScreenEventName = 'mozfullscreenchange';\n      } else if (_document.msFullscreenEnabled) {\n        this._fullScreenEventName = 'MSFullscreenChange';\n      }\n    }\n\n    return this._fullScreenEventName;\n  }\n\n  /**\n   * When the page is put into fullscreen mode, a specific element is specified.\n   * Only that element and its children are visible when in fullscreen mode.\n   */\n  getFullscreenElement(): Element {\n    const _document = this._document as any;\n\n    return (\n      _document.fullscreenElement ||\n      _document.webkitFullscreenElement ||\n      _document.mozFullScreenElement ||\n      _document.msFullscreenElement ||\n      null\n    );\n  }\n}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["i1", "i2", "i3.OverlayContainer", "i1.ScrollStrategyOptions", "i2.OverlayContainer", "i3.OverlayPositionBuilder", "i4.OverlayKeyboardDispatcher", "i7.OverlayOutsideClickDispatcher"], "mappings": ";;;;;;;;;;;;;;;;;AAaA,MAAM,uBAAuB,GAAG,sBAAsB,EAAE,CAAC;AAEzD;;AAEG;MACU,mBAAmB,CAAA;IAM9B,WACU,CAAA,cAA6B,EACrC,QAAa,EAAA;QADL,IAAc,CAAA,cAAA,GAAd,cAAc,CAAe;QAN/B,IAAmB,CAAA,mBAAA,GAAG,EAAC,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAC,CAAC;QAE1C,IAAU,CAAA,UAAA,GAAG,KAAK,CAAC;AAOzB,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;KAC3B;;AAGD,IAAA,MAAM,MAAK;;IAGX,MAAM,GAAA;AACJ,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE;AACxB,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,eAAgB,CAAC;YAE7C,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,cAAc,CAAC,yBAAyB,EAAE,CAAC;;AAG/E,YAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC;AACtD,YAAA,IAAI,CAAC,mBAAmB,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,EAAE,CAAC;;;AAIpD,YAAA,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,mBAAmB,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;AAC1E,YAAA,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,mBAAmB,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;AACxE,YAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;AAC7C,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;SACxB;KACF;;IAGD,OAAO,GAAA;AACL,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,eAAgB,CAAC;AAC7C,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAK,CAAC;AAClC,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;AAC7B,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;AAC7B,YAAA,MAAM,0BAA0B,GAAG,SAAS,CAAC,cAAc,IAAI,EAAE,CAAC;AAClE,YAAA,MAAM,0BAA0B,GAAG,SAAS,CAAC,cAAc,IAAI,EAAE,CAAC;AAElE,YAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YAExB,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;YAC/C,SAAS,CAAC,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC;AAC7C,YAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC;;;;;;YAOhD,IAAI,uBAAuB,EAAE;gBAC3B,SAAS,CAAC,cAAc,GAAG,SAAS,CAAC,cAAc,GAAG,MAAM,CAAC;aAC9D;AAED,YAAA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;YAEnF,IAAI,uBAAuB,EAAE;AAC3B,gBAAA,SAAS,CAAC,cAAc,GAAG,0BAA0B,CAAC;AACtD,gBAAA,SAAS,CAAC,cAAc,GAAG,0BAA0B,CAAC;aACvD;SACF;KACF;IAEO,aAAa,GAAA;;;;AAInB,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,eAAgB,CAAC;AAE7C,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,wBAAwB,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE;AACxE,YAAA,OAAO,KAAK,CAAC;SACd;AAED,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QACjC,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC;AACvD,QAAA,OAAO,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC;KACjF;AACF;;AC3ED;;AAEG;SACa,wCAAwC,GAAA;AACtD,IAAA,OAAO,KAAK,CAAC,CAA4C,0CAAA,CAAA,CAAC,CAAC;AAC7D;;ACVA;;AAEG;MACU,mBAAmB,CAAA;AAK9B,IAAA,WAAA,CACU,iBAAmC,EACnC,OAAe,EACf,cAA6B,EAC7B,OAAmC,EAAA;QAHnC,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAkB;QACnC,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QACf,IAAc,CAAA,cAAA,GAAd,cAAc,CAAe;QAC7B,IAAO,CAAA,OAAA,GAAP,OAAO,CAA4B;QARrC,IAAmB,CAAA,mBAAA,GAAwB,IAAI,CAAC;;QAkEhD,IAAO,CAAA,OAAA,GAAG,MAAK;YACrB,IAAI,CAAC,OAAO,EAAE,CAAC;AAEf,YAAA,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE;AAClC,gBAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;aACnD;AACH,SAAC,CAAC;KA/DE;;AAGJ,IAAA,MAAM,CAAC,UAAsB,EAAA;AAC3B,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YACvE,MAAM,wCAAwC,EAAE,CAAC;SAClD;AAED,QAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;KAC/B;;IAGD,MAAM,GAAA;AACJ,QAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,OAAO;SACR;AAED,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CACpD,MAAM,CAAC,UAAU,IAAG;YAClB,QACE,CAAC,UAAU;AACX,gBAAA,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,EACnF;SACH,CAAC,CACH,CAAC;AAEF,QAAA,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,EAAE;YACxE,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,cAAc,CAAC,yBAAyB,EAAE,CAAC,GAAG,CAAC;YAElF,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC,SAAS,CAAC,MAAK;gBAC/C,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,yBAAyB,EAAE,CAAC,GAAG,CAAC;AAE3E,gBAAA,IAAI,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAG,IAAI,CAAC,OAAQ,CAAC,SAAU,EAAE;oBACrF,IAAI,CAAC,OAAO,EAAE,CAAC;iBAChB;qBAAM;AACL,oBAAA,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;iBACnC;AACH,aAAC,CAAC,CAAC;SACJ;aAAM;YACL,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC3D;KACF;;IAGD,OAAO,GAAA;AACL,QAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC5B,YAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;AACvC,YAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;SACjC;KACF;IAED,MAAM,GAAA;QACJ,IAAI,CAAC,OAAO,EAAE,CAAC;AACf,QAAA,IAAI,CAAC,WAAW,GAAG,IAAK,CAAC;KAC1B;AAUF;;ACzFD;MACa,kBAAkB,CAAA;;AAE7B,IAAA,MAAM,MAAK;;AAEX,IAAA,OAAO,MAAK;;AAEZ,IAAA,MAAM,MAAK;AACZ;;ACJD;;;;;;AAMG;AACa,SAAA,4BAA4B,CAAC,OAAmB,EAAE,gBAA8B,EAAA;AAC9F,IAAA,OAAO,gBAAgB,CAAC,IAAI,CAAC,eAAe,IAAG;QAC7C,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,GAAG,eAAe,CAAC,GAAG,CAAC;QAC1D,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,GAAG,eAAe,CAAC,MAAM,CAAC;QAC1D,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,GAAG,eAAe,CAAC,IAAI,CAAC;QACzD,MAAM,YAAY,GAAG,OAAO,CAAC,IAAI,GAAG,eAAe,CAAC,KAAK,CAAC;AAE1D,QAAA,OAAO,YAAY,IAAI,YAAY,IAAI,WAAW,IAAI,YAAY,CAAC;AACrE,KAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;AAMG;AACa,SAAA,2BAA2B,CAAC,OAAmB,EAAE,gBAA8B,EAAA;AAC7F,IAAA,OAAO,gBAAgB,CAAC,IAAI,CAAC,mBAAmB,IAAG;QACjD,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,GAAG,mBAAmB,CAAC,GAAG,CAAC;QAC3D,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,GAAG,mBAAmB,CAAC,MAAM,CAAC;QACjE,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,GAAG,mBAAmB,CAAC,IAAI,CAAC;QAC5D,MAAM,YAAY,GAAG,OAAO,CAAC,KAAK,GAAG,mBAAmB,CAAC,KAAK,CAAC;AAE/D,QAAA,OAAO,YAAY,IAAI,YAAY,IAAI,WAAW,IAAI,YAAY,CAAC;AACrE,KAAC,CAAC,CAAC;AACL;;ACtBA;;AAEG;MACU,wBAAwB,CAAA;AAInC,IAAA,WAAA,CACU,iBAAmC,EACnC,cAA6B,EAC7B,OAAe,EACf,OAAwC,EAAA;QAHxC,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAkB;QACnC,IAAc,CAAA,cAAA,GAAd,cAAc,CAAe;QAC7B,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QACf,IAAO,CAAA,OAAA,GAAP,OAAO,CAAiC;QAP1C,IAAmB,CAAA,mBAAA,GAAwB,IAAI,CAAC;KAQpD;;AAGJ,IAAA,MAAM,CAAC,UAAsB,EAAA;AAC3B,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YACvE,MAAM,wCAAwC,EAAE,CAAC;SAClD;AAED,QAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;KAC/B;;IAGD,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;AAC7B,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC;AAEhE,YAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,MAAK;AAClF,gBAAA,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;;gBAGlC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;oBAC1C,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,qBAAqB,EAAE,CAAC;AAC5E,oBAAA,MAAM,EAAC,KAAK,EAAE,MAAM,EAAC,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC;;;oBAI9D,MAAM,WAAW,GAAG,CAAC,EAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAC,CAAC,CAAC;AAErF,oBAAA,IAAI,4BAA4B,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE;wBAC1D,IAAI,CAAC,OAAO,EAAE,CAAC;AACf,wBAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;qBACnD;iBACF;AACH,aAAC,CAAC,CAAC;SACJ;KACF;;IAGD,OAAO,GAAA;AACL,QAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC5B,YAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;AACvC,YAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;SACjC;KACF;IAED,MAAM,GAAA;QACJ,IAAI,CAAC,OAAO,EAAE,CAAC;AACf,QAAA,IAAI,CAAC,WAAW,GAAG,IAAK,CAAC;KAC1B;AACF;;ACpED;;;;;AAKG;MAEU,qBAAqB,CAAA;AAGhC,IAAA,WAAA,CACU,iBAAmC,EACnC,cAA6B,EAC7B,OAAe,EACL,QAAa,EAAA;QAHvB,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAkB;QACnC,IAAc,CAAA,cAAA,GAAd,cAAc,CAAe;QAC7B,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;;AAOzB,QAAA,IAAA,CAAA,IAAI,GAAG,MAAM,IAAI,kBAAkB,EAAE,CAAC;AAEtC;;;AAGG;QACH,IAAK,CAAA,KAAA,GAAG,CAAC,MAAkC,KACzC,IAAI,mBAAmB,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;;AAG7F,QAAA,IAAA,CAAA,KAAK,GAAG,MAAM,IAAI,mBAAmB,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AAE3E;;;;AAIG;QACH,IAAU,CAAA,UAAA,GAAG,CAAC,MAAuC,KACnD,IAAI,wBAAwB,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AAtBhG,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;KAC3B;AAVU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,qGAOtB,QAAQ,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAPP,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,cADT,MAAM,EAAA,CAAA,CAAA,EAAA;;2FAClB,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBADjC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;0BAQ3B,MAAM;2BAAC,QAAQ,CAAA;;;ACrBpB;MACa,aAAa,CAAA;AA+CxB,IAAA,WAAA,CAAY,MAAsB,EAAA;;AA1ClC,QAAA,IAAA,CAAA,cAAc,GAAoB,IAAI,kBAAkB,EAAE,CAAC;;QAG3D,IAAU,CAAA,UAAA,GAAuB,EAAE,CAAC;;QAGpC,IAAW,CAAA,WAAA,GAAa,KAAK,CAAC;;QAG9B,IAAa,CAAA,aAAA,GAAuB,2BAA2B,CAAC;AA0BhE;;;;AAIG;QACH,IAAmB,CAAA,mBAAA,GAAa,KAAK,CAAC;QAGpC,IAAI,MAAM,EAAE;;;;YAIV,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CACZ,CAAC;AAC1B,YAAA,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE;AAC5B,gBAAA,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE;;;;;;;oBAO7B,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAQ,CAAC;iBAChC;aACF;SACF;KACF;AACF;;ACtDD;MACa,sBAAsB,CAAA;IAUjC,WACE,CAAA,MAAgC,EAChC,OAAkC;;IAE3B,OAAgB;;IAEhB,OAAgB;;IAEhB,UAA8B,EAAA;QAJ9B,IAAO,CAAA,OAAA,GAAP,OAAO,CAAS;QAEhB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAS;QAEhB,IAAU,CAAA,UAAA,GAAV,UAAU,CAAoB;AAErC,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;AAC9B,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;AAC9B,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;AACjC,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;KAClC;AACF,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;AAwBG;MACU,mBAAmB,CAAA;AAK/B,CAAA;AAED;MACa,8BAA8B,CAAA;AACzC,IAAA,WAAA;;IAES,cAAsC;;IAEtC,wBAA6C,EAAA;QAF7C,IAAc,CAAA,cAAA,GAAd,cAAc,CAAwB;QAEtC,IAAwB,CAAA,wBAAA,GAAxB,wBAAwB,CAAqB;KAClD;AACL,CAAA;AAED;;;;;AAKG;AACa,SAAA,wBAAwB,CAAC,QAAgB,EAAE,KAA4B,EAAA;AACrF,IAAA,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,QAAQ,EAAE;AAC/D,QAAA,MAAM,KAAK,CACT,CAAA,2BAAA,EAA8B,QAAQ,CAAA,EAAA,EAAK,KAAK,CAAK,GAAA,CAAA;AACnD,YAAA,CAAA,qCAAA,CAAuC,CAC1C,CAAC;KACH;AACH,CAAC;AAED;;;;;AAKG;AACa,SAAA,0BAA0B,CAAC,QAAgB,EAAE,KAA8B,EAAA;AACzF,IAAA,IAAI,KAAK,KAAK,OAAO,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,QAAQ,EAAE;AAC9D,QAAA,MAAM,KAAK,CACT,CAAA,2BAAA,EAA8B,QAAQ,CAAA,EAAA,EAAK,KAAK,CAAK,GAAA,CAAA;AACnD,YAAA,CAAA,oCAAA,CAAsC,CACzC,CAAC;KACH;AACH;;AChHA;;;;AAIG;MAEmB,qBAAqB,CAAA;AAOzC,IAAA,WAAA,CAA8B,QAAa,EAAA;;QAL3C,IAAiB,CAAA,iBAAA,GAAiB,EAAE,CAAC;AAMnC,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;KAC3B;IAED,WAAW,GAAA;QACT,IAAI,CAAC,MAAM,EAAE,CAAC;KACf;;AAGD,IAAA,GAAG,CAAC,UAAsB,EAAA;;AAExB,QAAA,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AACxB,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;KACzC;;AAGD,IAAA,MAAM,CAAC,UAAsB,EAAA;QAC3B,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAEzD,QAAA,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SACzC;;QAGD,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE;YACvC,IAAI,CAAC,MAAM,EAAE,CAAC;SACf;KACF;AAlCmB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,kBAOrB,QAAQ,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAPR,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,cADlB,MAAM,EAAA,CAAA,CAAA,EAAA;;2FACT,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBAD1C,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;0BAQjB,MAAM;2BAAC,QAAQ,CAAA;;;ACZ9B;;;;AAIG;AAEG,MAAO,yBAA0B,SAAQ,qBAAqB,CAAA;AAClE,IAAA,WAAA,CACoB,QAAa;;IAEX,OAAgB,EAAA;QAEpC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAFI,IAAO,CAAA,OAAA,GAAP,OAAO,CAAS;;AAgC9B,QAAA,IAAA,CAAA,gBAAgB,GAAG,CAAC,KAAoB,KAAI;AAClD,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC;AAExC,YAAA,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;;;;;;;AAO7C,gBAAA,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;oBACnD,MAAM,aAAa,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC;;AAEjD,oBAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,wBAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;qBACnD;yBAAM;AACL,wBAAA,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;qBAC3B;oBACD,MAAM;iBACP;aACF;AACH,SAAC,CAAC;KAlDD;;AAGQ,IAAA,GAAG,CAAC,UAAsB,EAAA;AACjC,QAAA,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;;AAGtB,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;;AAErB,YAAA,IAAI,IAAI,CAAC,OAAO,EAAE;gBAChB,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAC7B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,CACvE,CAAC;aACH;iBAAM;AACL,gBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;aACxE;AACD,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;SACzB;KACF;;IAGS,MAAM,GAAA;AACd,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;AAC1E,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;SAC1B;KACF;AAjCU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,yBAAyB,kBAE1B,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAFP,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,yBAAyB,cADb,MAAM,EAAA,CAAA,CAAA,EAAA;;2FAClB,yBAAyB,EAAA,UAAA,EAAA,CAAA;kBADrC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;0BAG3B,MAAM;2BAAC,QAAQ,CAAA;;0BAEf,QAAQ;;;ACTb;;;;AAIG;AAEG,MAAO,6BAA8B,SAAQ,qBAAqB,CAAA;IAKtE,WACoB,CAAA,QAAa,EACvB,SAAmB;;IAEP,OAAgB,EAAA;QAEpC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAJR,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QAEP,IAAO,CAAA,OAAA,GAAP,OAAO,CAAS;QAP9B,IAAiB,CAAA,iBAAA,GAAG,KAAK,CAAC;;AAoE1B,QAAA,IAAA,CAAA,oBAAoB,GAAG,CAAC,KAAmB,KAAI;AACrD,YAAA,IAAI,CAAC,uBAAuB,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;AACxD,SAAC,CAAC;;AAGM,QAAA,IAAA,CAAA,cAAc,GAAG,CAAC,KAAiB,KAAI;AAC7C,YAAA,MAAM,MAAM,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;;;;;;;YAOtC,MAAM,MAAM,GACV,KAAK,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,uBAAuB;kBAClD,IAAI,CAAC,uBAAuB;kBAC5B,MAAM,CAAC;;;AAGb,YAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;;;;YAKpC,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;;;;;AAMhD,YAAA,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;AAC7C,gBAAA,MAAM,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC/B,gBAAA,IAAI,UAAU,CAAC,qBAAqB,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE;oBACtF,SAAS;iBACV;;;;AAKD,gBAAA,IACE,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAc,CAAC;oBAClD,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAc,CAAC,EAClD;oBACA,MAAM;iBACP;AAED,gBAAA,MAAM,oBAAoB,GAAG,UAAU,CAAC,qBAAqB,CAAC;;AAE9D,gBAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,oBAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;iBAC1D;qBAAM;AACL,oBAAA,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBAClC;aACF;AACH,SAAC,CAAC;KAhHD;;AAGQ,IAAA,GAAG,CAAC,UAAsB,EAAA;AACjC,QAAA,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;;;;;;;AAQtB,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AACrB,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;;AAGjC,YAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,gBAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;aACrE;iBAAM;AACL,gBAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;aAC/B;;;YAID,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;gBACjD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;AAC9C,gBAAA,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC;AAC9B,gBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;aAC/B;AAED,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;SACzB;KACF;;IAGS,MAAM,GAAA;AACd,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YACjC,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;YACzE,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;YAC7D,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;YAChE,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;YACnE,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBAChD,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC;AAC9C,gBAAA,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;aAChC;AACD,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;SAC1B;KACF;AAEO,IAAA,kBAAkB,CAAC,IAAiB,EAAA;QAC1C,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;QACtE,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;QAC1D,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;QAC7D,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;KACjE;AAnEU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,6BAA6B,kBAM9B,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAAA,IAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AANP,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,6BAA6B,cADjB,MAAM,EAAA,CAAA,CAAA,EAAA;;2FAClB,6BAA6B,EAAA,UAAA,EAAA,CAAA;kBADzC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;0BAO3B,MAAM;2BAAC,QAAQ,CAAA;;0BAGf,QAAQ;;;ACjBb;MAEa,gBAAgB,CAAA;IAI3B,WACoB,CAAA,QAAa,EACrB,SAAmB,EAAA;QAAnB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;AAE7B,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;KAC3B;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC;KAClC;AAED;;;;;AAKG;IACH,mBAAmB,GAAA;AACjB,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC3B,IAAI,CAAC,gBAAgB,EAAE,CAAC;SACzB;QAED,OAAO,IAAI,CAAC,iBAAiB,CAAC;KAC/B;AAED;;;AAGG;IACO,gBAAgB,GAAA;QACxB,MAAM,cAAc,GAAG,uBAAuB,CAAC;;;;QAK/C,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,kBAAkB,EAAE,EAAE;AACpD,YAAA,MAAM,0BAA0B,GAAG,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAChE,CAAI,CAAA,EAAA,cAAc,uBAAuB,GAAG,CAAA,CAAA,EAAI,cAAc,CAAA,iBAAA,CAAmB,CAClF,CAAC;;;AAIF,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,0BAA0B,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1D,gBAAA,0BAA0B,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;aACxC;SACF;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACtD,QAAA,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;;;;;;;;;;QAWxC,IAAI,kBAAkB,EAAE,EAAE;AACxB,YAAA,SAAS,CAAC,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;SAC5C;AAAM,aAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;AACpC,YAAA,SAAS,CAAC,YAAY,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;SAC9C;QAED,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;AAC3C,QAAA,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;KACpC;AAvEU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,kBAKjB,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAAA,IAAA,CAAA,QAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AALP,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,cADJ,MAAM,EAAA,CAAA,CAAA,EAAA;;2FAClB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAD5B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;0BAM3B,MAAM;2BAAC,QAAQ,CAAA;;;ACOpB;;;AAGG;MACU,UAAU,CAAA;AA0BrB,IAAA,WAAA,CACU,aAA2B,EAC3B,KAAkB,EAClB,KAAkB,EAClB,OAAuC,EACvC,OAAe,EACf,mBAA8C,EAC9C,SAAmB,EACnB,SAAmB,EACnB,uBAAsD,EACtD,sBAAsB,KAAK,EAAA;QAT3B,IAAa,CAAA,aAAA,GAAb,aAAa,CAAc;QAC3B,IAAK,CAAA,KAAA,GAAL,KAAK,CAAa;QAClB,IAAK,CAAA,KAAA,GAAL,KAAK,CAAa;QAClB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAgC;QACvC,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QACf,IAAmB,CAAA,mBAAA,GAAnB,mBAAmB,CAA2B;QAC9C,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QACnB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QACnB,IAAuB,CAAA,uBAAA,GAAvB,uBAAuB,CAA+B;QACtD,IAAmB,CAAA,mBAAA,GAAnB,mBAAmB,CAAQ;QAnC7B,IAAgB,CAAA,gBAAA,GAAuB,IAAI,CAAC;AAEnC,QAAA,IAAA,CAAA,cAAc,GAAG,IAAI,OAAO,EAAc,CAAC;AAC3C,QAAA,IAAA,CAAA,YAAY,GAAG,IAAI,OAAO,EAAQ,CAAC;AACnC,QAAA,IAAA,CAAA,YAAY,GAAG,IAAI,OAAO,EAAQ,CAAC;AAG5C,QAAA,IAAA,CAAA,gBAAgB,GAAqB,YAAY,CAAC,KAAK,CAAC;AACxD,QAAA,IAAA,CAAA,qBAAqB,GAAG,CAAC,KAAiB,KAAK,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC/E,QAAA,IAAA,CAAA,6BAA6B,GAAG,CAAC,KAAsB,KAAI;AACjE,YAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,MAA4B,CAAC,CAAC;AAC5D,SAAC,CAAC;;AASO,QAAA,IAAA,CAAA,cAAc,GAAG,IAAI,OAAO,EAAiB,CAAC;;AAG9C,QAAA,IAAA,CAAA,qBAAqB,GAAG,IAAI,OAAO,EAAc,CAAC;AAczD,QAAA,IAAI,OAAO,CAAC,cAAc,EAAE;AAC1B,YAAA,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,cAAc,CAAC;AAC9C,YAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SACnC;AAED,QAAA,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,gBAAgB,CAAC;KACnD;;AAGD,IAAA,IAAI,cAAc,GAAA;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;;AAGD,IAAA,IAAI,eAAe,GAAA;QACjB,OAAO,IAAI,CAAC,gBAAgB,CAAC;KAC9B;AAED;;;;AAIG;AACH,IAAA,IAAI,WAAW,GAAA;QACb,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;AAMD;;;;;;AAMG;AACH,IAAA,MAAM,CAAC,MAAmB,EAAA;;;QAGxB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI,IAAI,CAAC,mBAAmB,EAAE;YACzD,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAClD;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAEvD,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAC1B,YAAA,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SACrC;QAED,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,uBAAuB,EAAE,CAAC;AAE/B,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AACxB,YAAA,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;SAC/B;;;;AAKD,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;;AAEjD,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;gBACtB,IAAI,CAAC,cAAc,EAAE,CAAC;aACvB;AACH,SAAC,CAAC,CAAC;;AAGH,QAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;AAEhC,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YAC5B,IAAI,CAAC,eAAe,EAAE,CAAC;SACxB;AAED,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;AAC3B,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;SAChE;;AAGD,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;;AAGzB,QAAA,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAEnC,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;AACpC,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;SACxE;AAED,QAAA,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;;;;AAKvC,QAAA,IAAI,OAAO,YAAY,EAAE,SAAS,KAAK,UAAU,EAAE;;;;;;AAMjD,YAAA,YAAY,CAAC,SAAS,CAAC,MAAK;AAC1B,gBAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;;;;oBAItB,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;iBACnF;AACH,aAAC,CAAC,CAAC;SACJ;AAED,QAAA,OAAO,YAAY,CAAC;KACrB;AAED;;;AAGG;IACH,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;YACvB,OAAO;SACR;QAED,IAAI,CAAC,cAAc,EAAE,CAAC;;;;AAKtB,QAAA,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAEjC,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;AAC3D,YAAA,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC;SACjC;AAED,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AACxB,YAAA,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;SAChC;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;;AAGrD,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;;AAGzB,QAAA,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;;;QAItC,IAAI,CAAC,wBAAwB,EAAE,CAAC;AAChC,QAAA,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC;AACpC,QAAA,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC1C,QAAA,OAAO,gBAAgB,CAAC;KACzB;;IAGD,OAAO,GAAA;AACL,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;AAEtC,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAC1B,YAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;SAClC;QAED,IAAI,CAAC,sBAAsB,EAAE,CAAC;AAC9B,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AAC7C,QAAA,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC;AACpC,QAAA,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACtC,QAAA,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;AAC7B,QAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;AAC7B,QAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;AAC/B,QAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;AAC/B,QAAA,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,CAAC;AACtC,QAAA,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC1C,QAAA,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,CAAC;AAErB,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAK,CAAC;QAE3D,IAAI,UAAU,EAAE;AACd,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;SAC1B;AAED,QAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;KAC9B;;IAGD,WAAW,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;KACzC;;IAGD,aAAa,GAAA;QACX,OAAO,IAAI,CAAC,cAAc,CAAC;KAC5B;;IAGD,WAAW,GAAA;QACT,OAAO,IAAI,CAAC,YAAY,CAAC;KAC1B;;IAGD,WAAW,GAAA;QACT,OAAO,IAAI,CAAC,YAAY,CAAC;KAC1B;;IAGD,aAAa,GAAA;QACX,OAAO,IAAI,CAAC,cAAc,CAAC;KAC5B;;IAGD,oBAAoB,GAAA;QAClB,OAAO,IAAI,CAAC,qBAAqB,CAAC;KACnC;;IAGD,SAAS,GAAA;QACP,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;;IAGD,cAAc,GAAA;AACZ,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAC1B,YAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;SAChC;KACF;;AAGD,IAAA,sBAAsB,CAAC,QAA0B,EAAA;AAC/C,QAAA,IAAI,QAAQ,KAAK,IAAI,CAAC,iBAAiB,EAAE;YACvC,OAAO;SACR;AAED,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAC1B,YAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;SAClC;AAED,QAAA,IAAI,CAAC,iBAAiB,GAAG,QAAQ,CAAC;AAElC,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;AACtB,YAAA,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACtB,IAAI,CAAC,cAAc,EAAE,CAAC;SACvB;KACF;;AAGD,IAAA,UAAU,CAAC,UAA6B,EAAA;AACtC,QAAA,IAAI,CAAC,OAAO,GAAG,EAAC,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,UAAU,EAAC,CAAC;QAChD,IAAI,CAAC,kBAAkB,EAAE,CAAC;KAC3B;;AAGD,IAAA,YAAY,CAAC,GAA+B,EAAA;AAC1C,QAAA,IAAI,CAAC,OAAO,GAAG,EAAC,GAAG,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,GAAG,EAAC,CAAC;QACjD,IAAI,CAAC,uBAAuB,EAAE,CAAC;KAChC;;AAGD,IAAA,aAAa,CAAC,OAA0B,EAAA;AACtC,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;SAChD;KACF;;AAGD,IAAA,gBAAgB,CAAC,OAA0B,EAAA;AACzC,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;SACjD;KACF;AAED;;AAEG;IACH,YAAY,GAAA;AACV,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QAEzC,IAAI,CAAC,SAAS,EAAE;AACd,YAAA,OAAO,KAAK,CAAC;SACd;AAED,QAAA,OAAO,OAAO,SAAS,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC;KACpE;;AAGD,IAAA,oBAAoB,CAAC,QAAwB,EAAA;AAC3C,QAAA,IAAI,QAAQ,KAAK,IAAI,CAAC,eAAe,EAAE;YACrC,OAAO;SACR;QAED,IAAI,CAAC,sBAAsB,EAAE,CAAC;AAC9B,QAAA,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;AAEhC,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;AACtB,YAAA,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACtB,QAAQ,CAAC,MAAM,EAAE,CAAC;SACnB;KACF;;IAGO,uBAAuB,GAAA;AAC7B,QAAA,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;KACrD;;IAGO,kBAAkB,GAAA;AACxB,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACf,OAAO;SACR;AAED,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;QAE/B,KAAK,CAAC,KAAK,GAAG,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACtD,KAAK,CAAC,MAAM,GAAG,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACxD,KAAK,CAAC,QAAQ,GAAG,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC5D,KAAK,CAAC,SAAS,GAAG,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC9D,KAAK,CAAC,QAAQ,GAAG,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC5D,KAAK,CAAC,SAAS,GAAG,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;KAC/D;;AAGO,IAAA,oBAAoB,CAAC,aAAsB,EAAA;AACjD,QAAA,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,aAAa,GAAG,aAAa,GAAG,EAAE,GAAG,MAAM,CAAC;KAC9D;;IAGO,eAAe,GAAA;QACrB,MAAM,YAAY,GAAG,8BAA8B,CAAC;QAEpD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC5D,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;AAE5D,QAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;SAC5E;AAED,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;AAC9B,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;SAC9E;;;AAID,QAAA,IAAI,CAAC,KAAK,CAAC,aAAc,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;;;QAI1E,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;;QAG5E,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,OAAO,qBAAqB,KAAK,WAAW,EAAE;AAC7E,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;gBAClC,qBAAqB,CAAC,MAAK;AACzB,oBAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;wBACzB,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;qBACnD;AACH,iBAAC,CAAC,CAAC;AACL,aAAC,CAAC,CAAC;SACJ;aAAM;YACL,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;SACnD;KACF;AAED;;;;;;AAMG;IACK,oBAAoB,GAAA;AAC1B,QAAA,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;YAC1B,IAAI,CAAC,KAAK,CAAC,UAAW,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAChD;KACF;;IAGD,cAAc,GAAA;AACZ,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAE/C,IAAI,CAAC,gBAAgB,EAAE;YACrB,OAAO;SACR;AAED,QAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC5B,YAAA,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YACxC,OAAO;SACR;AAED,QAAA,gBAAgB,CAAC,SAAS,CAAC,MAAM,CAAC,8BAA8B,CAAC,CAAC;AAElE,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;YAClC,gBAAiB,CAAC,gBAAgB,CAAC,eAAe,EAAE,IAAI,CAAC,6BAA6B,CAAC,CAAC;AAC1F,SAAC,CAAC,CAAC;;;AAIH,QAAA,gBAAgB,CAAC,KAAK,CAAC,aAAa,GAAG,MAAM,CAAC;;;;AAK9C,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MACrD,UAAU,CAAC,MAAK;AACd,YAAA,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAC1C,SAAC,EAAE,GAAG,CAAC,CACR,CAAC;KACH;;AAGO,IAAA,cAAc,CAAC,OAAoB,EAAE,UAA6B,EAAE,KAAc,EAAA;AACxF,QAAA,MAAM,OAAO,GAAG,WAAW,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAE/D,QAAA,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC;SAClF;KACF;;IAGO,wBAAwB,GAAA;;;;AAI9B,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;;;;AAIlC,YAAA,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ;AACvC,iBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;iBAC5D,SAAS,CAAC,MAAK;;;gBAGd,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;oBAClE,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;AACzC,wBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;qBACjE;oBAED,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;wBAC1C,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;AACpD,wBAAA,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;qBACrB;oBAED,YAAY,CAAC,WAAW,EAAE,CAAC;iBAC5B;AACH,aAAC,CAAC,CAAC;AACP,SAAC,CAAC,CAAC;KACJ;;IAGO,sBAAsB,GAAA;AAC5B,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC;QAE5C,IAAI,cAAc,EAAE;YAClB,cAAc,CAAC,OAAO,EAAE,CAAC;AAEzB,YAAA,IAAI,cAAc,CAAC,MAAM,EAAE;gBACzB,cAAc,CAAC,MAAM,EAAE,CAAC;aACzB;SACF;KACF;;AAGO,IAAA,gBAAgB,CAAC,QAA4B,EAAA;QACnD,IAAI,QAAQ,EAAE;YACZ,QAAQ,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAClE,QAAQ,CAAC,mBAAmB,CAAC,eAAe,EAAE,IAAI,CAAC,6BAA6B,CAAC,CAAC;YAClF,QAAQ,CAAC,MAAM,EAAE,CAAC;;;;AAKlB,YAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,QAAQ,EAAE;AACtC,gBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;aAC9B;SACF;AAED,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACzB,YAAA,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACpC,YAAA,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;SACnC;KACF;AACF;;ACzgBD;AACA;AAEA;AACA,MAAM,gBAAgB,GAAG,6CAA6C,CAAC;AAEvE;AACA,MAAM,cAAc,GAAG,eAAe,CAAC;AAcvC;;;;;;AAMG;MACU,iCAAiC,CAAA;;AA4F5C,IAAA,IAAI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,mBAAmB,CAAC;KACjC;IAED,WACE,CAAA,WAAoD,EAC5C,cAA6B,EAC7B,SAAmB,EACnB,SAAmB,EACnB,iBAAmC,EAAA;QAHnC,IAAc,CAAA,cAAA,GAAd,cAAc,CAAe;QAC7B,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QACnB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QACnB,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAkB;;QA7FrC,IAAoB,CAAA,oBAAA,GAAG,EAAC,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAC,CAAC;;QAG7C,IAAS,CAAA,SAAA,GAAG,KAAK,CAAC;;QAGlB,IAAQ,CAAA,QAAA,GAAG,IAAI,CAAC;;QAGhB,IAAc,CAAA,cAAA,GAAG,KAAK,CAAC;;QAGvB,IAAsB,CAAA,sBAAA,GAAG,IAAI,CAAC;;QAG9B,IAAe,CAAA,eAAA,GAAG,KAAK,CAAC;;QAexB,IAAe,CAAA,eAAA,GAAG,CAAC,CAAC;;QAGpB,IAAY,CAAA,YAAA,GAAoB,EAAE,CAAC;;QAG3C,IAAmB,CAAA,mBAAA,GAA6B,EAAE,CAAC;;AAwBlC,QAAA,IAAA,CAAA,gBAAgB,GAAG,IAAI,OAAO,EAAkC,CAAC;;AAG1E,QAAA,IAAA,CAAA,mBAAmB,GAAG,YAAY,CAAC,KAAK,CAAC;;QAGzC,IAAQ,CAAA,QAAA,GAAG,CAAC,CAAC;;QAGb,IAAQ,CAAA,QAAA,GAAG,CAAC,CAAC;;QAMb,IAAoB,CAAA,oBAAA,GAAa,EAAE,CAAC;;AAM5C,QAAA,IAAA,CAAA,eAAe,GAA+C,IAAI,CAAC,gBAAgB,CAAC;AAclF,QAAA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;KAC7B;;AAGD,IAAA,MAAM,CAAC,UAAsB,EAAA;QAC3B,IACE,IAAI,CAAC,WAAW;YAChB,UAAU,KAAK,IAAI,CAAC,WAAW;aAC9B,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAC/C;AACA,YAAA,MAAM,KAAK,CAAC,0DAA0D,CAAC,CAAC;SACzE;QAED,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,UAAU,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;AAEvD,QAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;AAC9B,QAAA,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC,WAAW,CAAC;AAC3C,QAAA,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,cAAc,CAAC;AACvC,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AACzB,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AAC7B,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,QAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;AACvC,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,SAAS,CAAC,MAAK;;;;AAIrE,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC7B,IAAI,CAAC,KAAK,EAAE,CAAC;AACf,SAAC,CAAC,CAAC;KACJ;AAED;;;;;;;;;;;;;AAaG;IACH,KAAK,GAAA;;QAEH,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YACjD,OAAO;SACR;;;;AAKD,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,aAAa,EAAE;YACxE,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAClC,IAAI,CAAC,uBAAuB,EAAE,CAAC;;;;AAK/B,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;AACrD,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACzC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC;AACvD,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,CAAC,qBAAqB,EAAE,CAAC;AAE3F,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;AACpC,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC;AACtC,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;AACxC,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;;QAG1C,MAAM,YAAY,GAAkB,EAAE,CAAC;;AAGvC,QAAA,IAAI,QAAsC,CAAC;;;AAI3C,QAAA,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,mBAAmB,EAAE;;AAExC,YAAA,IAAI,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,aAAa,EAAE,GAAG,CAAC,CAAC;;;;AAKvE,YAAA,IAAI,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC;;AAGxE,YAAA,IAAI,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,CAAC,CAAC;;AAGnF,YAAA,IAAI,UAAU,CAAC,0BAA0B,EAAE;AACzC,gBAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACvB,gBAAA,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;gBACtC,OAAO;aACR;;;YAID,IAAI,IAAI,CAAC,6BAA6B,CAAC,UAAU,EAAE,YAAY,EAAE,YAAY,CAAC,EAAE;;;gBAG9E,YAAY,CAAC,IAAI,CAAC;AAChB,oBAAA,QAAQ,EAAE,GAAG;AACb,oBAAA,MAAM,EAAE,WAAW;oBACnB,WAAW;oBACX,eAAe,EAAE,IAAI,CAAC,yBAAyB,CAAC,WAAW,EAAE,GAAG,CAAC;AAClE,iBAAA,CAAC,CAAC;gBAEH,SAAS;aACV;;;;AAKD,YAAA,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,UAAU,CAAC,WAAW,GAAG,UAAU,CAAC,WAAW,EAAE;AACzE,gBAAA,QAAQ,GAAG,EAAC,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,EAAE,WAAW,EAAC,CAAC;aAChF;SACF;;;AAID,QAAA,IAAI,YAAY,CAAC,MAAM,EAAE;YACvB,IAAI,OAAO,GAAuB,IAAI,CAAC;AACvC,YAAA,IAAI,SAAS,GAAG,CAAC,CAAC,CAAC;AACnB,YAAA,KAAK,MAAM,GAAG,IAAI,YAAY,EAAE;gBAC9B,MAAM,KAAK,GACT,GAAG,CAAC,eAAe,CAAC,KAAK,GAAG,GAAG,CAAC,eAAe,CAAC,MAAM,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC;AACtF,gBAAA,IAAI,KAAK,GAAG,SAAS,EAAE;oBACrB,SAAS,GAAG,KAAK,CAAC;oBAClB,OAAO,GAAG,GAAG,CAAC;iBACf;aACF;AAED,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACvB,IAAI,CAAC,cAAc,CAAC,OAAQ,CAAC,QAAQ,EAAE,OAAQ,CAAC,MAAM,CAAC,CAAC;YACxD,OAAO;SACR;;;AAID,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;;AAEjB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,cAAc,CAAC,QAAS,CAAC,QAAQ,EAAE,QAAS,CAAC,WAAW,CAAC,CAAC;YAC/D,OAAO;SACR;;;QAID,IAAI,CAAC,cAAc,CAAC,QAAS,CAAC,QAAQ,EAAE,QAAS,CAAC,WAAW,CAAC,CAAC;KAChE;IAED,MAAM,GAAA;QACJ,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC1B,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;AAChC,QAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;KACxC;;IAGD,OAAO,GAAA;AACL,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,OAAO;SACR;;;AAID,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;AACpC,gBAAA,GAAG,EAAE,EAAE;AACP,gBAAA,IAAI,EAAE,EAAE;AACR,gBAAA,KAAK,EAAE,EAAE;AACT,gBAAA,MAAM,EAAE,EAAE;AACV,gBAAA,MAAM,EAAE,EAAE;AACV,gBAAA,KAAK,EAAE,EAAE;AACT,gBAAA,UAAU,EAAE,EAAE;AACd,gBAAA,cAAc,EAAE,EAAE;AACI,aAAA,CAAC,CAAC;SAC3B;AAED,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,0BAA0B,EAAE,CAAC;SACnC;AAED,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;SACjE;QAED,IAAI,CAAC,MAAM,EAAE,CAAC;AACd,QAAA,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC;QACjC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,GAAG,IAAK,CAAC;AAC7C,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;KACzB;AAED;;;;AAIG;IACH,mBAAmB,GAAA;QACjB,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YACjD,OAAO;SACR;AAED,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;QAExC,IAAI,YAAY,EAAE;AAChB,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YACzC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC;AACvD,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;AACrD,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,CAAC,qBAAqB,EAAE,CAAC;AAE3F,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;AAC9F,YAAA,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;SAChD;aAAM;YACL,IAAI,CAAC,KAAK,EAAE,CAAC;SACd;KACF;AAED;;;;AAIG;AACH,IAAA,wBAAwB,CAAC,WAA4B,EAAA;AACnD,QAAA,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;AAChC,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;AAGG;AACH,IAAA,aAAa,CAAC,SAA8B,EAAA;AAC1C,QAAA,IAAI,CAAC,mBAAmB,GAAG,SAAS,CAAC;;;AAIrC,QAAA,IAAI,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,aAAc,CAAC,KAAK,CAAC,CAAC,EAAE;AACjD,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC3B;QAED,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAE1B,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;AAGG;AACH,IAAA,kBAAkB,CAAC,MAAc,EAAA;AAC/B,QAAA,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;AAC9B,QAAA,OAAO,IAAI,CAAC;KACb;;IAGD,sBAAsB,CAAC,kBAAkB,GAAG,IAAI,EAAA;AAC9C,QAAA,IAAI,CAAC,sBAAsB,GAAG,kBAAkB,CAAC;AACjD,QAAA,OAAO,IAAI,CAAC;KACb;;IAGD,iBAAiB,CAAC,aAAa,GAAG,IAAI,EAAA;AACpC,QAAA,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;AACpC,QAAA,OAAO,IAAI,CAAC;KACb;;IAGD,QAAQ,CAAC,OAAO,GAAG,IAAI,EAAA;AACrB,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;AACxB,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;;AAKG;IACH,kBAAkB,CAAC,QAAQ,GAAG,IAAI,EAAA;AAChC,QAAA,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;AAChC,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;;;AAMG;AACH,IAAA,SAAS,CAAC,MAA+C,EAAA;AACvD,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;AACtB,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;AAGG;AACH,IAAA,kBAAkB,CAAC,MAAc,EAAA;AAC/B,QAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC;AACvB,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;AAGG;AACH,IAAA,kBAAkB,CAAC,MAAc,EAAA;AAC/B,QAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC;AACvB,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;;;;AAOG;AACH,IAAA,qBAAqB,CAAC,QAAgB,EAAA;AACpC,QAAA,IAAI,CAAC,wBAAwB,GAAG,QAAQ,CAAC;AACzC,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;AAEG;AACK,IAAA,eAAe,CACrB,UAAsB,EACtB,aAAyB,EACzB,GAAsB,EAAA;AAEtB,QAAA,IAAI,CAAS,CAAC;AACd,QAAA,IAAI,GAAG,CAAC,OAAO,IAAI,QAAQ,EAAE;;;YAG3B,CAAC,GAAG,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC;SAC5C;aAAM;AACL,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC;AAClE,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC;AAChE,YAAA,CAAC,GAAG,GAAG,CAAC,OAAO,IAAI,OAAO,GAAG,MAAM,GAAG,IAAI,CAAC;SAC5C;;;AAID,QAAA,IAAI,aAAa,CAAC,IAAI,GAAG,CAAC,EAAE;AAC1B,YAAA,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC;SACzB;AAED,QAAA,IAAI,CAAS,CAAC;AACd,QAAA,IAAI,GAAG,CAAC,OAAO,IAAI,QAAQ,EAAE;YAC3B,CAAC,GAAG,UAAU,CAAC,GAAG,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;SAC5C;aAAM;AACL,YAAA,CAAC,GAAG,GAAG,CAAC,OAAO,IAAI,KAAK,GAAG,UAAU,CAAC,GAAG,GAAG,UAAU,CAAC,MAAM,CAAC;SAC/D;;;;;;AAOD,QAAA,IAAI,aAAa,CAAC,GAAG,GAAG,CAAC,EAAE;AACzB,YAAA,CAAC,IAAI,aAAa,CAAC,GAAG,CAAC;SACxB;AAED,QAAA,OAAO,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC;KACf;AAED;;;AAGG;AACK,IAAA,gBAAgB,CACtB,WAAkB,EAClB,WAAuB,EACvB,GAAsB,EAAA;;;AAItB,QAAA,IAAI,aAAqB,CAAC;AAC1B,QAAA,IAAI,GAAG,CAAC,QAAQ,IAAI,QAAQ,EAAE;AAC5B,YAAA,aAAa,GAAG,CAAC,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC;SACxC;AAAM,aAAA,IAAI,GAAG,CAAC,QAAQ,KAAK,OAAO,EAAE;AACnC,YAAA,aAAa,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC;SACxD;aAAM;AACL,YAAA,aAAa,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC;SACxD;AAED,QAAA,IAAI,aAAqB,CAAC;AAC1B,QAAA,IAAI,GAAG,CAAC,QAAQ,IAAI,QAAQ,EAAE;AAC5B,YAAA,aAAa,GAAG,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;SACzC;aAAM;AACL,YAAA,aAAa,GAAG,GAAG,CAAC,QAAQ,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC;SACjE;;QAGD,OAAO;AACL,YAAA,CAAC,EAAE,WAAW,CAAC,CAAC,GAAG,aAAa;AAChC,YAAA,CAAC,EAAE,WAAW,CAAC,CAAC,GAAG,aAAa;SACjC,CAAC;KACH;;AAGO,IAAA,cAAc,CACpB,KAAY,EACZ,cAA0B,EAC1B,QAAoB,EACpB,QAA2B,EAAA;;;AAI3B,QAAA,MAAM,OAAO,GAAG,4BAA4B,CAAC,cAAc,CAAC,CAAC;AAC7D,QAAA,IAAI,EAAC,CAAC,EAAE,CAAC,EAAC,GAAG,KAAK,CAAC;QACnB,IAAI,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QAC7C,IAAI,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;;QAG7C,IAAI,OAAO,EAAE;YACX,CAAC,IAAI,OAAO,CAAC;SACd;QAED,IAAI,OAAO,EAAE;YACX,CAAC,IAAI,OAAO,CAAC;SACd;;AAGD,QAAA,IAAI,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC;QACzB,IAAI,aAAa,GAAG,CAAC,GAAG,OAAO,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;AACvD,QAAA,IAAI,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;QACxB,IAAI,cAAc,GAAG,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;;AAG1D,QAAA,IAAI,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;AACvF,QAAA,IAAI,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,MAAM,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC;AACzF,QAAA,IAAI,WAAW,GAAG,YAAY,GAAG,aAAa,CAAC;QAE/C,OAAO;YACL,WAAW;YACX,0BAA0B,EAAE,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,MAAM,KAAK,WAAW;AAC1E,YAAA,wBAAwB,EAAE,aAAa,KAAK,OAAO,CAAC,MAAM;AAC1D,YAAA,0BAA0B,EAAE,YAAY,IAAI,OAAO,CAAC,KAAK;SAC1D,CAAC;KACH;AAED;;;;;AAKG;AACK,IAAA,6BAA6B,CAAC,GAAe,EAAE,KAAY,EAAE,QAAoB,EAAA;AACvF,QAAA,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC/B,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC;YAClD,MAAM,cAAc,GAAG,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC;AAChD,YAAA,MAAM,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC,CAAC;AACxE,YAAA,MAAM,QAAQ,GAAG,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC,CAAC;AAEtE,YAAA,MAAM,WAAW,GACf,GAAG,CAAC,wBAAwB,KAAK,SAAS,IAAI,IAAI,IAAI,SAAS,IAAI,eAAe,CAAC,CAAC;AACtF,YAAA,MAAM,aAAa,GACjB,GAAG,CAAC,0BAA0B,KAAK,QAAQ,IAAI,IAAI,IAAI,QAAQ,IAAI,cAAc,CAAC,CAAC;YAErF,OAAO,WAAW,IAAI,aAAa,CAAC;SACrC;AACD,QAAA,OAAO,KAAK,CAAC;KACd;AAED;;;;;;;;;;AAUG;AACK,IAAA,oBAAoB,CAC1B,KAAY,EACZ,cAA0B,EAC1B,cAAsC,EAAA;;;;QAKtC,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,eAAe,EAAE;YACpD,OAAO;gBACL,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBACvC,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC;aACxC,CAAC;SACH;;;AAID,QAAA,MAAM,OAAO,GAAG,4BAA4B,CAAC,cAAc,CAAC,CAAC;AAC7D,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC;;;QAIpC,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC5E,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC/E,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,GAAG,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7E,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;QAGhF,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,KAAK,GAAG,CAAC,CAAC;;;;QAKd,IAAI,OAAO,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,EAAE;AACnC,YAAA,KAAK,GAAG,YAAY,IAAI,CAAC,aAAa,CAAC;SACxC;aAAM;YACL,KAAK,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;SAC5F;QAED,IAAI,OAAO,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,EAAE;AACrC,YAAA,KAAK,GAAG,WAAW,IAAI,CAAC,cAAc,CAAC;SACxC;aAAM;YACL,KAAK,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,GAAG,GAAG,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;SAC1F;AAED,QAAA,IAAI,CAAC,mBAAmB,GAAG,EAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAC,CAAC;QAEhD,OAAO;AACL,YAAA,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,KAAK;AAClB,YAAA,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,KAAK;SACnB,CAAC;KACH;AAED;;;;AAIG;IACK,cAAc,CAAC,QAA2B,EAAE,WAAkB,EAAA;AACpE,QAAA,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;AACnC,QAAA,IAAI,CAAC,wBAAwB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;AACrD,QAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;AAElD,QAAA,IAAI,QAAQ,CAAC,UAAU,EAAE;AACvB,YAAA,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;SAC5C;;;;QAKD,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,MAAM,EAAE;AAC1C,YAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;;;AAIrD,YAAA,IACE,QAAQ,KAAK,IAAI,CAAC,aAAa;gBAC/B,CAAC,IAAI,CAAC,qBAAqB;gBAC3B,CAAC,uBAAuB,CAAC,IAAI,CAAC,qBAAqB,EAAE,gBAAgB,CAAC,EACtE;gBACA,MAAM,WAAW,GAAG,IAAI,8BAA8B,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;AACnF,gBAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;aACzC;AAED,YAAA,IAAI,CAAC,qBAAqB,GAAG,gBAAgB,CAAC;SAC/C;;AAGD,QAAA,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC;AAC9B,QAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;KAC/B;;AAGO,IAAA,mBAAmB,CAAC,QAA2B,EAAA;AACrD,QAAA,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE;YAClC,OAAO;SACR;AAED,QAAA,MAAM,QAAQ,GAA4B,IAAI,CAAC,YAAa,CAAC,gBAAgB,CAC3E,IAAI,CAAC,wBAAwB,CAC9B,CAAC;AACF,QAAA,IAAI,OAAoC,CAAC;AACzC,QAAA,IAAI,OAAO,GAAgC,QAAQ,CAAC,QAAQ,CAAC;AAE7D,QAAA,IAAI,QAAQ,CAAC,QAAQ,KAAK,QAAQ,EAAE;YAClC,OAAO,GAAG,QAAQ,CAAC;SACpB;AAAM,aAAA,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;AACxB,YAAA,OAAO,GAAG,QAAQ,CAAC,QAAQ,KAAK,OAAO,GAAG,OAAO,GAAG,MAAM,CAAC;SAC5D;aAAM;AACL,YAAA,OAAO,GAAG,QAAQ,CAAC,QAAQ,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO,CAAC;SAC5D;AAED,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACxC,YAAA,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,eAAe,GAAG,CAAG,EAAA,OAAO,CAAI,CAAA,EAAA,OAAO,EAAE,CAAC;SAC7D;KACF;AAED;;;;;AAKG;IACK,yBAAyB,CAAC,MAAa,EAAE,QAA2B,EAAA;AAC1E,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC;AACpC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AAC5B,QAAA,IAAI,MAAc,EAAE,GAAW,EAAE,MAAc,CAAC;AAEhD,QAAA,IAAI,QAAQ,CAAC,QAAQ,KAAK,KAAK,EAAE;;AAE/B,YAAA,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC;YACf,MAAM,GAAG,QAAQ,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC;SACvD;AAAM,aAAA,IAAI,QAAQ,CAAC,QAAQ,KAAK,QAAQ,EAAE;;;;AAIzC,YAAA,MAAM,GAAG,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;YAC/D,MAAM,GAAG,QAAQ,CAAC,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC;SAC1D;aAAM;;;;;YAKL,MAAM,8BAA8B,GAAG,IAAI,CAAC,GAAG,CAC7C,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,GAAG,QAAQ,CAAC,GAAG,EACzC,MAAM,CAAC,CAAC,CACT,CAAC;AAEF,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;AAExD,YAAA,MAAM,GAAG,8BAA8B,GAAG,CAAC,CAAC;AAC5C,YAAA,GAAG,GAAG,MAAM,CAAC,CAAC,GAAG,8BAA8B,CAAC;AAEhD,YAAA,IAAI,MAAM,GAAG,cAAc,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBAC7E,GAAG,GAAG,MAAM,CAAC,CAAC,GAAG,cAAc,GAAG,CAAC,CAAC;aACrC;SACF;;QAGD,MAAM,4BAA4B,GAChC,CAAC,QAAQ,CAAC,QAAQ,KAAK,OAAO,IAAI,CAAC,KAAK,MAAM,QAAQ,CAAC,QAAQ,KAAK,KAAK,IAAI,KAAK,CAAC,CAAC;;QAGtF,MAAM,2BAA2B,GAC/B,CAAC,QAAQ,CAAC,QAAQ,KAAK,KAAK,IAAI,CAAC,KAAK,MAAM,QAAQ,CAAC,QAAQ,KAAK,OAAO,IAAI,KAAK,CAAC,CAAC;AAEtF,QAAA,IAAI,KAAa,EAAE,IAAY,EAAE,KAAa,CAAC;QAE/C,IAAI,2BAA2B,EAAE;AAC/B,YAAA,KAAK,GAAG,QAAQ,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;YAC7D,KAAK,GAAG,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;SACzC;aAAM,IAAI,4BAA4B,EAAE;AACvC,YAAA,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC;YAChB,KAAK,GAAG,QAAQ,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC;SACnC;aAAM;;;;;YAKL,MAAM,8BAA8B,GAAG,IAAI,CAAC,GAAG,CAC7C,QAAQ,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,EACzC,MAAM,CAAC,CAAC,CACT,CAAC;AACF,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;AAEtD,YAAA,KAAK,GAAG,8BAA8B,GAAG,CAAC,CAAC;AAC3C,YAAA,IAAI,GAAG,MAAM,CAAC,CAAC,GAAG,8BAA8B,CAAC;AAEjD,YAAA,IAAI,KAAK,GAAG,aAAa,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBAC3E,IAAI,GAAG,MAAM,CAAC,CAAC,GAAG,aAAa,GAAG,CAAC,CAAC;aACrC;SACF;QAED,OAAO,EAAC,GAAG,EAAE,GAAI,EAAE,IAAI,EAAE,IAAK,EAAE,MAAM,EAAE,MAAO,EAAE,KAAK,EAAE,KAAM,EAAE,KAAK,EAAE,MAAM,EAAC,CAAC;KAChF;AAED;;;;;;AAMG;IACK,qBAAqB,CAAC,MAAa,EAAE,QAA2B,EAAA;QACtE,MAAM,eAAe,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;;;QAIzE,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AAClD,YAAA,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;AAC5F,YAAA,eAAe,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;SAC1F;QAED,MAAM,MAAM,GAAG,EAAyB,CAAC;AAEzC,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;YAC5B,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC;AAC/B,YAAA,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,QAAQ,GAAG,EAAE,CAAC;YACvE,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;SACvC;aAAM;YACL,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC;YACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC;YAEvD,MAAM,CAAC,MAAM,GAAG,mBAAmB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAC5D,MAAM,CAAC,GAAG,GAAG,mBAAmB,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,GAAG,mBAAmB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAC5D,MAAM,CAAC,KAAK,GAAG,mBAAmB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAC1D,MAAM,CAAC,IAAI,GAAG,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,KAAK,GAAG,mBAAmB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;;AAG1D,YAAA,IAAI,QAAQ,CAAC,QAAQ,KAAK,QAAQ,EAAE;AAClC,gBAAA,MAAM,CAAC,UAAU,GAAG,QAAQ,CAAC;aAC9B;iBAAM;AACL,gBAAA,MAAM,CAAC,UAAU,GAAG,QAAQ,CAAC,QAAQ,KAAK,KAAK,GAAG,UAAU,GAAG,YAAY,CAAC;aAC7E;AAED,YAAA,IAAI,QAAQ,CAAC,QAAQ,KAAK,QAAQ,EAAE;AAClC,gBAAA,MAAM,CAAC,cAAc,GAAG,QAAQ,CAAC;aAClC;iBAAM;AACL,gBAAA,MAAM,CAAC,cAAc,GAAG,QAAQ,CAAC,QAAQ,KAAK,QAAQ,GAAG,UAAU,GAAG,YAAY,CAAC;aACpF;YAED,IAAI,SAAS,EAAE;AACb,gBAAA,MAAM,CAAC,SAAS,GAAG,mBAAmB,CAAC,SAAS,CAAC,CAAC;aACnD;YAED,IAAI,QAAQ,EAAE;AACZ,gBAAA,MAAM,CAAC,QAAQ,GAAG,mBAAmB,CAAC,QAAQ,CAAC,CAAC;aACjD;SACF;AAED,QAAA,IAAI,CAAC,oBAAoB,GAAG,eAAe,CAAC;QAE5C,YAAY,CAAC,IAAI,CAAC,YAAa,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;KAChD;;IAGO,uBAAuB,GAAA;AAC7B,QAAA,YAAY,CAAC,IAAI,CAAC,YAAa,CAAC,KAAK,EAAE;AACrC,YAAA,GAAG,EAAE,GAAG;AACR,YAAA,IAAI,EAAE,GAAG;AACT,YAAA,KAAK,EAAE,GAAG;AACV,YAAA,MAAM,EAAE,GAAG;AACX,YAAA,MAAM,EAAE,EAAE;AACV,YAAA,KAAK,EAAE,EAAE;AACT,YAAA,UAAU,EAAE,EAAE;AACd,YAAA,cAAc,EAAE,EAAE;AACI,SAAA,CAAC,CAAC;KAC3B;;IAGO,0BAA0B,GAAA;AAChC,QAAA,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;AAC7B,YAAA,GAAG,EAAE,EAAE;AACP,YAAA,IAAI,EAAE,EAAE;AACR,YAAA,MAAM,EAAE,EAAE;AACV,YAAA,KAAK,EAAE,EAAE;AACT,YAAA,QAAQ,EAAE,EAAE;AACZ,YAAA,SAAS,EAAE,EAAE;AACS,SAAA,CAAC,CAAC;KAC3B;;IAGO,wBAAwB,CAAC,WAAkB,EAAE,QAA2B,EAAA;QAC9E,MAAM,MAAM,GAAG,EAAyB,CAAC;AACzC,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAClD,QAAA,MAAM,qBAAqB,GAAG,IAAI,CAAC,sBAAsB,CAAC;QAC1D,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC;QAE5C,IAAI,gBAAgB,EAAE;YACpB,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,yBAAyB,EAAE,CAAC;AACvE,YAAA,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC,CAAC;AACpF,YAAA,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC,CAAC;SACrF;aAAM;AACL,YAAA,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;SAC5B;;;;;;QAOD,IAAI,eAAe,GAAG,EAAE,CAAC;QACzB,IAAI,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QAC7C,IAAI,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QAE7C,IAAI,OAAO,EAAE;AACX,YAAA,eAAe,IAAI,CAAA,WAAA,EAAc,OAAO,CAAA,IAAA,CAAM,CAAC;SAChD;QAED,IAAI,OAAO,EAAE;AACX,YAAA,eAAe,IAAI,CAAA,WAAA,EAAc,OAAO,CAAA,GAAA,CAAK,CAAC;SAC/C;AAED,QAAA,MAAM,CAAC,SAAS,GAAG,eAAe,CAAC,IAAI,EAAE,CAAC;;;;;;AAO1C,QAAA,IAAI,MAAM,CAAC,SAAS,EAAE;YACpB,IAAI,gBAAgB,EAAE;gBACpB,MAAM,CAAC,SAAS,GAAG,mBAAmB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;aAC1D;iBAAM,IAAI,qBAAqB,EAAE;AAChC,gBAAA,MAAM,CAAC,SAAS,GAAG,EAAE,CAAC;aACvB;SACF;AAED,QAAA,IAAI,MAAM,CAAC,QAAQ,EAAE;YACnB,IAAI,gBAAgB,EAAE;gBACpB,MAAM,CAAC,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;aACxD;iBAAM,IAAI,qBAAqB,EAAE;AAChC,gBAAA,MAAM,CAAC,QAAQ,GAAG,EAAE,CAAC;aACtB;SACF;QAED,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;KACxC;;AAGO,IAAA,iBAAiB,CACvB,QAA2B,EAC3B,WAAkB,EAClB,cAAsC,EAAA;;;QAItC,IAAI,MAAM,GAAG,EAAC,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAwB,CAAC;AAC1D,QAAA,IAAI,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;AAEnF,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;SAC3F;;;AAID,QAAA,IAAI,QAAQ,CAAC,QAAQ,KAAK,QAAQ,EAAE;;;YAGlC,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,eAAgB,CAAC,YAAY,CAAC;AACpE,YAAA,MAAM,CAAC,MAAM,GAAG,GAAG,cAAc,IAAI,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC;SACrF;aAAM;YACL,MAAM,CAAC,GAAG,GAAG,mBAAmB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;SAClD;AAED,QAAA,OAAO,MAAM,CAAC;KACf;;AAGO,IAAA,iBAAiB,CACvB,QAA2B,EAC3B,WAAkB,EAClB,cAAsC,EAAA;;;QAItC,IAAI,MAAM,GAAG,EAAC,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAwB,CAAC;AAC1D,QAAA,IAAI,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;AAEnF,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;SAC3F;;;;;AAMD,QAAA,IAAI,uBAAyC,CAAC;AAE9C,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;AACjB,YAAA,uBAAuB,GAAG,QAAQ,CAAC,QAAQ,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO,CAAC;SAC1E;aAAM;AACL,YAAA,uBAAuB,GAAG,QAAQ,CAAC,QAAQ,KAAK,KAAK,GAAG,OAAO,GAAG,MAAM,CAAC;SAC1E;;;AAID,QAAA,IAAI,uBAAuB,KAAK,OAAO,EAAE;YACvC,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,eAAgB,CAAC,WAAW,CAAC;AAClE,YAAA,MAAM,CAAC,KAAK,GAAG,GAAG,aAAa,IAAI,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC;SAClF;aAAM;YACL,MAAM,CAAC,IAAI,GAAG,mBAAmB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;SACnD;AAED,QAAA,OAAO,MAAM,CAAC;KACf;AAED;;;AAGG;IACK,oBAAoB,GAAA;;AAE1B,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAC3C,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC;;;;QAKzD,MAAM,qBAAqB,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,IAAG;YAC/D,OAAO,UAAU,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC;AAC1E,SAAC,CAAC,CAAC;QAEH,OAAO;AACL,YAAA,eAAe,EAAE,2BAA2B,CAAC,YAAY,EAAE,qBAAqB,CAAC;AACjF,YAAA,mBAAmB,EAAE,4BAA4B,CAAC,YAAY,EAAE,qBAAqB,CAAC;AACtF,YAAA,gBAAgB,EAAE,2BAA2B,CAAC,aAAa,EAAE,qBAAqB,CAAC;AACnF,YAAA,oBAAoB,EAAE,4BAA4B,CAAC,aAAa,EAAE,qBAAqB,CAAC;SACzF,CAAC;KACH;;AAGO,IAAA,kBAAkB,CAAC,MAAc,EAAE,GAAG,SAAmB,EAAA;QAC/D,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,YAAoB,EAAE,eAAuB,KAAI;YACxE,OAAO,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;SACpD,EAAE,MAAM,CAAC,CAAC;KACZ;;IAGO,wBAAwB,GAAA;;;;;;QAM9B,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,eAAgB,CAAC,WAAW,CAAC;QAC1D,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,eAAgB,CAAC,YAAY,CAAC;QAC5D,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,yBAAyB,EAAE,CAAC;QAEvE,OAAO;AACL,YAAA,GAAG,EAAE,cAAc,CAAC,GAAG,GAAG,IAAI,CAAC,eAAe;AAC9C,YAAA,IAAI,EAAE,cAAc,CAAC,IAAI,GAAG,IAAI,CAAC,eAAe;YAChD,KAAK,EAAE,cAAc,CAAC,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,eAAe;YACzD,MAAM,EAAE,cAAc,CAAC,GAAG,GAAG,MAAM,GAAG,IAAI,CAAC,eAAe;AAC1D,YAAA,KAAK,EAAE,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,eAAe;AACvC,YAAA,MAAM,EAAE,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,eAAe;SAC1C,CAAC;KACH;;IAGO,MAAM,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,KAAK,KAAK,CAAC;KAClD;;IAGO,iBAAiB,GAAA;QACvB,OAAO,CAAC,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,SAAS,CAAC;KACvD;;IAGO,UAAU,CAAC,QAA2B,EAAE,IAAe,EAAA;AAC7D,QAAA,IAAI,IAAI,KAAK,GAAG,EAAE;;;AAGhB,YAAA,OAAO,QAAQ,CAAC,OAAO,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC;SACpE;AAED,QAAA,OAAO,QAAQ,CAAC,OAAO,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC;KACpE;;IAGO,kBAAkB,GAAA;AACxB,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE;AACpC,gBAAA,MAAM,KAAK,CAAC,uEAAuE,CAAC,CAAC;aACtF;;;AAID,YAAA,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,IAAG;AACtC,gBAAA,0BAA0B,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;AACpD,gBAAA,wBAAwB,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;AAClD,gBAAA,0BAA0B,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AACtD,gBAAA,wBAAwB,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AACtD,aAAC,CAAC,CAAC;SACJ;KACF;;AAGO,IAAA,gBAAgB,CAAC,UAA6B,EAAA;AACpD,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,WAAW,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,QAAQ,IAAG;AACzC,gBAAA,IAAI,QAAQ,KAAK,EAAE,IAAI,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;AACzE,oBAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBACzC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;iBACpC;AACH,aAAC,CAAC,CAAC;SACJ;KACF;;IAGO,kBAAkB,GAAA;AACxB,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,YAAA,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,QAAQ,IAAG;gBAC3C,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AACxC,aAAC,CAAC,CAAC;AACH,YAAA,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;SAChC;KACF;;IAGO,cAAc,GAAA;AACpB,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;AAE5B,QAAA,IAAI,MAAM,YAAY,UAAU,EAAE;AAChC,YAAA,OAAO,MAAM,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC;SACrD;;AAGD,QAAA,IAAI,MAAM,YAAY,OAAO,EAAE;AAC7B,YAAA,OAAO,MAAM,CAAC,qBAAqB,EAAE,CAAC;SACvC;AAED,QAAA,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC;AAChC,QAAA,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,CAAC;;QAGlC,OAAO;YACL,GAAG,EAAE,MAAM,CAAC,CAAC;AACb,YAAA,MAAM,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM;YACzB,IAAI,EAAE,MAAM,CAAC,CAAC;AACd,YAAA,KAAK,EAAE,MAAM,CAAC,CAAC,GAAG,KAAK;YACvB,MAAM;YACN,KAAK;SACN,CAAC;KACH;AACF,CAAA;AAgED;AACA,SAAS,YAAY,CACnB,WAAgC,EAChC,MAA2B,EAAA;AAE3B,IAAA,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE;AACtB,QAAA,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;YAC9B,WAAW,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;SAChC;KACF;AAED,IAAA,OAAO,WAAW,CAAC;AACrB,CAAC;AAED;;;AAGG;AACH,SAAS,aAAa,CAAC,KAAyC,EAAA;IAC9D,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,IAAI,IAAI,EAAE;AAC9C,QAAA,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AACnD,QAAA,OAAO,CAAC,KAAK,IAAI,KAAK,KAAK,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;KAC5D;IAED,OAAO,KAAK,IAAI,IAAI,CAAC;AACvB,CAAC;AAED;;;;;AAKG;AACH,SAAS,4BAA4B,CAAC,UAAsB,EAAA;IAC1D,OAAO;QACL,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC;QAC/B,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC;QACnC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC;QACrC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC;QACjC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC;QACnC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC;KACtC,CAAC;AACJ,CAAC;AAED;AACA,SAAS,uBAAuB,CAAC,CAAsB,EAAE,CAAsB,EAAA;AAC7E,IAAA,IAAI,CAAC,KAAK,CAAC,EAAE;AACX,QAAA,OAAO,IAAI,CAAC;KACb;AAED,IAAA,QACE,CAAC,CAAC,eAAe,KAAK,CAAC,CAAC,eAAe;AACvC,QAAA,CAAC,CAAC,mBAAmB,KAAK,CAAC,CAAC,mBAAmB;AAC/C,QAAA,CAAC,CAAC,gBAAgB,KAAK,CAAC,CAAC,gBAAgB;AACzC,QAAA,CAAC,CAAC,oBAAoB,KAAK,CAAC,CAAC,oBAAoB,EACjD;AACJ,CAAC;AAEY,MAAA,iCAAiC,GAAwB;AACpE,IAAA,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAC;AACzE,IAAA,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAC;AACzE,IAAA,EAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAC;AACrE,IAAA,EAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAC;EACrE;AAEW,MAAA,oCAAoC,GAAwB;AACvE,IAAA,EAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAC;AACpE,IAAA,EAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAC;AAC1E,IAAA,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAC;AACpE,IAAA,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAC;;;ACtyC5E;AACA,MAAM,YAAY,GAAG,4BAA4B,CAAC;AAElD;;;;;AAKG;MACU,sBAAsB,CAAA;AAAnC,IAAA,WAAA,GAAA;QAGU,IAAY,CAAA,YAAA,GAAG,QAAQ,CAAC;QACxB,IAAU,CAAA,UAAA,GAAG,EAAE,CAAC;QAChB,IAAa,CAAA,aAAA,GAAG,EAAE,CAAC;QACnB,IAAW,CAAA,WAAA,GAAG,EAAE,CAAC;QACjB,IAAU,CAAA,UAAA,GAAG,EAAE,CAAC;QAChB,IAAQ,CAAA,QAAA,GAAG,EAAE,CAAC;QACd,IAAM,CAAA,MAAA,GAAG,EAAE,CAAC;QACZ,IAAO,CAAA,OAAA,GAAG,EAAE,CAAC;QACb,IAAW,CAAA,WAAA,GAAG,KAAK,CAAC;KAqO7B;AAnOC,IAAA,MAAM,CAAC,UAAsB,EAAA;AAC3B,QAAA,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;AAEtC,QAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAE9B,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;YAChC,UAAU,CAAC,UAAU,CAAC,EAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAC,CAAC,CAAC;SAC7C;QAED,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAClC,UAAU,CAAC,UAAU,CAAC,EAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAC,CAAC,CAAC;SAC/C;QAED,UAAU,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;AACnD,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;KAC1B;AAED;;;AAGG;IACH,GAAG,CAAC,QAAgB,EAAE,EAAA;AACpB,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;AACxB,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AACxB,QAAA,IAAI,CAAC,WAAW,GAAG,YAAY,CAAC;AAChC,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;AAGG;IACH,IAAI,CAAC,QAAgB,EAAE,EAAA;AACrB,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AACtB,QAAA,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;AACzB,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;AAGG;IACH,MAAM,CAAC,QAAgB,EAAE,EAAA;AACvB,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;AACrB,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC3B,QAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;AAC9B,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;AAGG;IACH,KAAK,CAAC,QAAgB,EAAE,EAAA;AACtB,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AACtB,QAAA,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC;AAC1B,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;AAIG;IACH,KAAK,CAAC,QAAgB,EAAE,EAAA;AACtB,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AACtB,QAAA,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC;AAC1B,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;AAIG;IACH,GAAG,CAAC,QAAgB,EAAE,EAAA;AACpB,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AACtB,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AACxB,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;;AAKG;IACH,KAAK,CAAC,QAAgB,EAAE,EAAA;AACtB,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAC,KAAK,EAAE,KAAK,EAAC,CAAC,CAAC;SAC7C;aAAM;AACL,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;SACrB;AAED,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;;AAKG;IACH,MAAM,CAAC,QAAgB,EAAE,EAAA;AACvB,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAC,MAAM,EAAE,KAAK,EAAC,CAAC,CAAC;SAC9C;aAAM;AACL,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;SACtB;AAED,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;;AAKG;IACH,kBAAkB,CAAC,SAAiB,EAAE,EAAA;AACpC,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAClB,QAAA,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC;AAC3B,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;;AAKG;IACH,gBAAgB,CAAC,SAAiB,EAAE,EAAA;AAClC,QAAA,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AACjB,QAAA,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;AAC5B,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;AAGG;IACH,KAAK,GAAA;;;;AAIH,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE;YACxD,OAAO;SACR;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,KAAK,CAAC;QACrD,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC;QACxD,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC;QAC5C,MAAM,EAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAC,GAAG,MAAM,CAAC;QACpD,MAAM,yBAAyB,GAC7B,CAAC,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,OAAO;aACrC,CAAC,QAAQ,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,OAAO,CAAC,CAAC;QAC7D,MAAM,uBAAuB,GAC3B,CAAC,MAAM,KAAK,MAAM,IAAI,MAAM,KAAK,OAAO;aACvC,CAAC,SAAS,IAAI,SAAS,KAAK,MAAM,IAAI,SAAS,KAAK,OAAO,CAAC,CAAC;AAChE,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;AAClC,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC9B,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,SAAS,KAAK,KAAK,CAAC;QAC/D,IAAI,UAAU,GAAG,EAAE,CAAC;QACpB,IAAI,WAAW,GAAG,EAAE,CAAC;QACrB,IAAI,cAAc,GAAG,EAAE,CAAC;QAExB,IAAI,yBAAyB,EAAE;YAC7B,cAAc,GAAG,YAAY,CAAC;SAC/B;AAAM,aAAA,IAAI,SAAS,KAAK,QAAQ,EAAE;YACjC,cAAc,GAAG,QAAQ,CAAC;YAE1B,IAAI,KAAK,EAAE;gBACT,WAAW,GAAG,OAAO,CAAC;aACvB;iBAAM;gBACL,UAAU,GAAG,OAAO,CAAC;aACtB;SACF;aAAM,IAAI,KAAK,EAAE;YAChB,IAAI,SAAS,KAAK,MAAM,IAAI,SAAS,KAAK,KAAK,EAAE;gBAC/C,cAAc,GAAG,UAAU,CAAC;gBAC5B,UAAU,GAAG,OAAO,CAAC;aACtB;iBAAM,IAAI,SAAS,KAAK,OAAO,IAAI,SAAS,KAAK,OAAO,EAAE;gBACzD,cAAc,GAAG,YAAY,CAAC;gBAC9B,WAAW,GAAG,OAAO,CAAC;aACvB;SACF;aAAM,IAAI,SAAS,KAAK,MAAM,IAAI,SAAS,KAAK,OAAO,EAAE;YACxD,cAAc,GAAG,YAAY,CAAC;YAC9B,UAAU,GAAG,OAAO,CAAC;SACtB;aAAM,IAAI,SAAS,KAAK,OAAO,IAAI,SAAS,KAAK,KAAK,EAAE;YACvD,cAAc,GAAG,UAAU,CAAC;YAC5B,WAAW,GAAG,OAAO,CAAC;SACvB;AAED,QAAA,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC;AACpC,QAAA,MAAM,CAAC,UAAU,GAAG,yBAAyB,GAAG,GAAG,GAAG,UAAU,CAAC;AACjE,QAAA,MAAM,CAAC,SAAS,GAAG,uBAAuB,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC;AACnE,QAAA,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;AACzC,QAAA,MAAM,CAAC,WAAW,GAAG,yBAAyB,GAAG,GAAG,GAAG,WAAW,CAAC;AACnE,QAAA,YAAY,CAAC,cAAc,GAAG,cAAc,CAAC;AAC7C,QAAA,YAAY,CAAC,UAAU,GAAG,uBAAuB,GAAG,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC;KACrF;AAED;;;AAGG;IACH,OAAO,GAAA;QACL,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACzC,OAAO;SACR;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,KAAK,CAAC;AACrD,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC;AAC5C,QAAA,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC;AAElC,QAAA,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AACtC,QAAA,YAAY,CAAC,cAAc;AACzB,YAAA,YAAY,CAAC,UAAU;AACvB,gBAAA,MAAM,CAAC,SAAS;AAChB,oBAAA,MAAM,CAAC,YAAY;AACnB,wBAAA,MAAM,CAAC,UAAU;AACjB,4BAAA,MAAM,CAAC,WAAW;AAClB,gCAAA,MAAM,CAAC,QAAQ;AACb,oCAAA,EAAE,CAAC;AAEP,QAAA,IAAI,CAAC,WAAW,GAAG,IAAK,CAAC;AACzB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;KACzB;AACF;;ACjPD;MAEa,sBAAsB,CAAA;AACjC,IAAA,WAAA,CACU,cAA6B,EACX,SAAc,EAChC,SAAmB,EACnB,iBAAmC,EAAA;QAHnC,IAAc,CAAA,cAAA,GAAd,cAAc,CAAe;QACX,IAAS,CAAA,SAAA,GAAT,SAAS,CAAK;QAChC,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QACnB,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAkB;KACzC;AAEJ;;AAEG;IACH,MAAM,GAAA;QACJ,OAAO,IAAI,sBAAsB,EAAE,CAAC;KACrC;AAED;;;AAGG;AACH,IAAA,mBAAmB,CACjB,MAA+C,EAAA;QAE/C,OAAO,IAAI,iCAAiC,CAC1C,MAAM,EACN,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,iBAAiB,CACvB,CAAC;KACH;AA7BU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,sBAAsB,+CAGvB,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAAC,IAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,gBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAHP,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,sBAAsB,cADV,MAAM,EAAA,CAAA,CAAA,EAAA;;2FAClB,sBAAsB,EAAA,UAAA,EAAA,CAAA;kBADlC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;0BAI3B,MAAM;2BAAC,QAAQ,CAAA;;;ACKpB;AACA,IAAI,YAAY,GAAG,CAAC,CAAC;AAErB;AACA;AAEA;;;;;;;AAOG;MAEU,OAAO,CAAA;AAGlB,IAAA,WAAA;;IAES,gBAAuC,EACtC,iBAAmC,EACnC,yBAAmD,EACnD,gBAAwC,EACxC,mBAA8C,EAC9C,SAAmB,EACnB,OAAe,EACG,SAAc,EAChC,eAA+B,EAC/B,SAAmB,EACnB,uBAAsD,EACX,qBAA8B,EAAA;QAX1E,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAuB;QACtC,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAkB;QACnC,IAAyB,CAAA,yBAAA,GAAzB,yBAAyB,CAA0B;QACnD,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAwB;QACxC,IAAmB,CAAA,mBAAA,GAAnB,mBAAmB,CAA2B;QAC9C,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QACnB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QACG,IAAS,CAAA,SAAA,GAAT,SAAS,CAAK;QAChC,IAAe,CAAA,eAAA,GAAf,eAAe,CAAgB;QAC/B,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QACnB,IAAuB,CAAA,uBAAA,GAAvB,uBAAuB,CAA+B;QACX,IAAqB,CAAA,qBAAA,GAArB,qBAAqB,CAAS;KAC/E;AAEJ;;;;AAIG;AACH,IAAA,MAAM,CAAC,MAAsB,EAAA;AAC3B,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACvC,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAC3C,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;AACpD,QAAA,MAAM,aAAa,GAAG,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC;AAEhD,QAAA,aAAa,CAAC,SAAS,GAAG,aAAa,CAAC,SAAS,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;AAEhF,QAAA,OAAO,IAAI,UAAU,CACnB,YAAY,EACZ,IAAI,EACJ,IAAI,EACJ,aAAa,EACb,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,mBAAmB,EACxB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,uBAAuB,EAC5B,IAAI,CAAC,qBAAqB,KAAK,gBAAgB,CAChD,CAAC;KACH;AAED;;;;AAIG;IACH,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,gBAAgB,CAAC;KAC9B;AAED;;;AAGG;AACK,IAAA,kBAAkB,CAAC,IAAiB,EAAA;QAC1C,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAEjD,QAAA,IAAI,CAAC,EAAE,GAAG,eAAe,YAAY,EAAE,EAAE,CAAC;AAC1C,QAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;AACvC,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAEvB,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;AAIG;IACK,kBAAkB,GAAA;QACxB,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACjD,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC/D,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;AAIG;AACK,IAAA,mBAAmB,CAAC,IAAiB,EAAA;;;AAG3C,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAiB,cAAc,CAAC,CAAC;SACnE;QAED,OAAO,IAAI,eAAe,CACxB,IAAI,EACJ,IAAI,CAAC,yBAAyB,EAC9B,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,SAAS,CACf,CAAC;KACH;8GAnGU,OAAO,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAC,qBAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,wBAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,sBAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,yBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAYR,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,6BAAA,EAAA,EAAA,EAAA,KAAA,EAIR,qBAAqB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAhBpB,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,OAAO,cADK,MAAM,EAAA,CAAA,CAAA,EAAA;;2FAClB,OAAO,EAAA,UAAA,EAAA,CAAA;kBADnB,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;0BAa3B,MAAM;2BAAC,QAAQ,CAAA;;0BAIf,MAAM;2BAAC,qBAAqB,CAAA;;0BAAG,QAAQ;;;ACjB5C;AACA,MAAM,mBAAmB,GAAwB;AAC/C,IAAA;AACE,QAAA,OAAO,EAAE,OAAO;AAChB,QAAA,OAAO,EAAE,QAAQ;AACjB,QAAA,QAAQ,EAAE,OAAO;AACjB,QAAA,QAAQ,EAAE,KAAK;AAChB,KAAA;AACD,IAAA;AACE,QAAA,OAAO,EAAE,OAAO;AAChB,QAAA,OAAO,EAAE,KAAK;AACd,QAAA,QAAQ,EAAE,OAAO;AACjB,QAAA,QAAQ,EAAE,QAAQ;AACnB,KAAA;AACD,IAAA;AACE,QAAA,OAAO,EAAE,KAAK;AACd,QAAA,OAAO,EAAE,KAAK;AACd,QAAA,QAAQ,EAAE,KAAK;AACf,QAAA,QAAQ,EAAE,QAAQ;AACnB,KAAA;AACD,IAAA;AACE,QAAA,OAAO,EAAE,KAAK;AACd,QAAA,OAAO,EAAE,QAAQ;AACjB,QAAA,QAAQ,EAAE,KAAK;AACf,QAAA,QAAQ,EAAE,KAAK;AAChB,KAAA;CACF,CAAC;AAEF;AACO,MAAM,qCAAqC,GAAG,IAAI,cAAc,CACrE,uCAAuC,EACvC;AACE,IAAA,UAAU,EAAE,MAAM;IAClB,OAAO,EAAE,MAAK;AACZ,QAAA,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;QAChC,OAAO,MAAM,OAAO,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;KACpD;AACF,CAAA,CACF,CAAC;AAEF;;;AAGG;MAMU,gBAAgB,CAAA;AAC3B,IAAA,WAAA;;IAES,UAAsB,EAAA;QAAtB,IAAU,CAAA,UAAA,GAAV,UAAU,CAAY;KAC3B;8GAJO,gBAAgB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAhB,gBAAgB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,4DAAA,EAAA,QAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAhB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAL5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,4DAA4D;AACtE,oBAAA,QAAQ,EAAE,kBAAkB;AAC5B,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;AAQD;;;AAGG;MAMU,mBAAmB,CAAA;;AA4B9B,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;IACD,IAAI,OAAO,CAAC,OAAe,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;AAExB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAC9C;KACF;;AAGD,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;IACD,IAAI,OAAO,CAAC,OAAe,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;AAExB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAC9C;KACF;;AAuDD,IAAA,IACI,mBAAmB,GAAA;QACrB,OAAO,IAAI,CAAC,oBAAoB,CAAC;KAClC;IACD,IAAI,mBAAmB,CAAC,KAAc,EAAA;AACpC,QAAA,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;KACnC;;IAsBD,WACU,CAAA,QAAiB,EACzB,WAA6B,EAC7B,gBAAkC,EACa,qBAA0B,EACrD,IAAoB,EAAA;QAJhC,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAS;QAIL,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAgB;AAxIlC,QAAA,IAAA,CAAA,qBAAqB,GAAG,YAAY,CAAC,KAAK,CAAC;AAC3C,QAAA,IAAA,CAAA,mBAAmB,GAAG,YAAY,CAAC,KAAK,CAAC;AACzC,QAAA,IAAA,CAAA,mBAAmB,GAAG,YAAY,CAAC,KAAK,CAAC;AACzC,QAAA,IAAA,CAAA,qBAAqB,GAAG,YAAY,CAAC,KAAK,CAAC;QAK3C,IAAoB,CAAA,oBAAA,GAAG,KAAK,CAAC;AAC7B,QAAA,IAAA,CAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;;QA4DW,IAAc,CAAA,cAAA,GAAW,CAAC,CAAC;;QAMrC,IAAI,CAAA,IAAA,GAAY,KAAK,CAAC;;QAGd,IAAY,CAAA,YAAA,GAAY,KAAK,CAAC;;QAOxE,IAAW,CAAA,WAAA,GAAY,KAAK,CAAC;;QAI7B,IAAY,CAAA,YAAA,GAAY,KAAK,CAAC;;QAI9B,IAAkB,CAAA,kBAAA,GAAY,KAAK,CAAC;;QAIpC,IAAa,CAAA,aAAA,GAAY,KAAK,CAAC;;QAGyC,IAAI,CAAA,IAAA,GAAY,KAAK,CAAC;;AAY3E,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,YAAY,EAAc,CAAC;;AAG/C,QAAA,IAAA,CAAA,cAAc,GAAG,IAAI,YAAY,EAAkC,CAAC;;AAGpE,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,YAAY,EAAQ,CAAC;;AAGlC,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,YAAY,EAAQ,CAAC;;AAGlC,QAAA,IAAA,CAAA,cAAc,GAAG,IAAI,YAAY,EAAiB,CAAC;;AAGnD,QAAA,IAAA,CAAA,mBAAmB,GAAG,IAAI,YAAY,EAAc,CAAC;QAWtE,IAAI,CAAC,eAAe,GAAG,IAAI,cAAc,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;AACzE,QAAA,IAAI,CAAC,sBAAsB,GAAG,qBAAqB,CAAC;AACpD,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;KACrD;;AAGD,IAAA,IAAI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;KACzB;;AAGD,IAAA,IAAI,GAAG,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;KAC5C;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;AACvC,QAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;AACvC,QAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,CAAC;AACzC,QAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,CAAC;AAEzC,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;SAC5B;KACF;AAED,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC7C,YAAA,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;gBAC1B,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,SAAS,EAAE,IAAI,CAAC,SAAS;AAC1B,aAAA,CAAC,CAAC;YAEH,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE;AAClC,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;aACxB;SACF;AAED,QAAA,IAAI,OAAO,CAAC,MAAM,CAAC,EAAE;AACnB,YAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;SAC3D;KACF;;IAGO,cAAc,GAAA;AACpB,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;AAC7C,YAAA,IAAI,CAAC,SAAS,GAAG,mBAAmB,CAAC;SACtC;AAED,QAAA,MAAM,UAAU,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QAClF,IAAI,CAAC,mBAAmB,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;QACxF,IAAI,CAAC,mBAAmB,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;QACxF,UAAU,CAAC,aAAa,EAAE,CAAC,SAAS,CAAC,CAAC,KAAoB,KAAI;AAC5D,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAEhC,YAAA,IAAI,KAAK,CAAC,OAAO,KAAK,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;gBAC5E,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,IAAI,CAAC,cAAc,EAAE,CAAC;aACvB;AACH,SAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,oBAAoB,EAAE,CAAC,SAAS,CAAC,CAAC,KAAiB,KAAI;AACtE,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACxC,YAAA,MAAM,MAAM,GAAG,eAAe,CAAC,KAAK,CAAmB,CAAC;AAExD,YAAA,IAAI,CAAC,MAAM,KAAK,MAAM,KAAK,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE;AAC9D,gBAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACtC;AACH,SAAC,CAAC,CAAC;KACJ;;IAGO,YAAY,GAAA;AAClB,QAAA,MAAM,gBAAgB,IAAI,IAAI,CAAC,SAAS;YACtC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC,CAAC;AAC3D,QAAA,MAAM,aAAa,GAAG,IAAI,aAAa,CAAC;YACtC,SAAS,EAAE,IAAI,CAAC,IAAI;YACpB,gBAAgB;YAChB,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;AAC9C,SAAA,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE;AAClC,YAAA,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;SAClC;QAED,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;AACpC,YAAA,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;SACpC;QAED,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,EAAE;AACxC,YAAA,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;SACxC;QAED,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,CAAC,EAAE;AAC1C,YAAA,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;SAC1C;AAED,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,YAAA,aAAa,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;SAClD;AAED,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,aAAa,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;SAC5C;AAED,QAAA,OAAO,aAAa,CAAC;KACtB;;AAGO,IAAA,uBAAuB,CAAC,gBAAmD,EAAA;AACjF,QAAA,MAAM,SAAS,GAAwB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,eAAe,KAAK;YAC5E,OAAO,EAAE,eAAe,CAAC,OAAO;YAChC,OAAO,EAAE,eAAe,CAAC,OAAO;YAChC,QAAQ,EAAE,eAAe,CAAC,QAAQ;YAClC,QAAQ,EAAE,eAAe,CAAC,QAAQ;AAClC,YAAA,OAAO,EAAE,eAAe,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO;AAChD,YAAA,OAAO,EAAE,eAAe,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO;AAChD,YAAA,UAAU,EAAE,eAAe,CAAC,UAAU,IAAI,SAAS;AACpD,SAAA,CAAC,CAAC,CAAC;AAEJ,QAAA,OAAO,gBAAgB;AACpB,aAAA,SAAS,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;aAC5B,aAAa,CAAC,SAAS,CAAC;AACxB,aAAA,sBAAsB,CAAC,IAAI,CAAC,kBAAkB,CAAC;AAC/C,aAAA,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;AACnB,aAAA,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC;AACrC,aAAA,kBAAkB,CAAC,IAAI,CAAC,cAAc,CAAC;AACvC,aAAA,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC;AACrC,aAAA,qBAAqB,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;KACxD;;IAGO,uBAAuB,GAAA;AAC7B,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;AACjF,QAAA,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;AACvC,QAAA,OAAO,QAAQ,CAAC;KACjB;IAEO,UAAU,GAAA;AAChB,QAAA,IAAI,IAAI,CAAC,MAAM,YAAY,gBAAgB,EAAE;AAC3C,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;SAC/B;aAAM;YACL,OAAO,IAAI,CAAC,MAAM,CAAC;SACpB;KACF;IAEO,iBAAiB,GAAA;AACvB,QAAA,IAAI,IAAI,CAAC,MAAM,YAAY,gBAAgB,EAAE;AAC3C,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC;SAC7C;AAED,QAAA,IAAI,IAAI,CAAC,MAAM,YAAY,UAAU,EAAE;AACrC,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;SAClC;QAED,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,IAAI,CAAC,MAAM,YAAY,OAAO,EAAE;YACpE,OAAO,IAAI,CAAC,MAAM,CAAC;SACpB;AAED,QAAA,OAAO,IAAI,CAAC;KACb;;IAGO,cAAc,GAAA;AACpB,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,IAAI,CAAC,cAAc,EAAE,CAAC;SACvB;aAAM;;YAEL,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;SAC7D;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE;YACnC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SAC/C;AAED,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC,SAAS,CAAC,KAAK,IAAG;AAC9E,gBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACjC,aAAC,CAAC,CAAC;SACJ;aAAM;AACL,YAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,CAAC;SAC1C;AAED,QAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,CAAC;;;QAIzC,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;AAC5C,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe;AACxD,iBAAA,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;iBAC/D,SAAS,CAAC,QAAQ,IAAG;AACpB,gBAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAE3D,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;AAC9C,oBAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,CAAC;iBAC1C;AACH,aAAC,CAAC,CAAC;SACN;KACF;;IAGO,cAAc,GAAA;AACpB,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;SAC3B;AAED,QAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,CAAC;AACzC,QAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,CAAC;KAC1C;AAlWU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,mBAAmB,iGA0IpB,qCAAqC,EAAA,EAAA,EAAA,KAAA,EAAAN,EAAA,CAAA,cAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGA1IpC,mBAAmB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,qEAAA,EAAA,MAAA,EAAA,EAAA,MAAA,EAAA,CAAA,2BAAA,EAAA,QAAA,CAAA,EAAA,SAAA,EAAA,CAAA,8BAAA,EAAA,WAAA,CAAA,EAAA,gBAAA,EAAA,CAAA,qCAAA,EAAA,kBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,4BAAA,EAAA,SAAA,CAAA,EAAA,OAAA,EAAA,CAAA,4BAAA,EAAA,SAAA,CAAA,EAAA,KAAA,EAAA,CAAA,0BAAA,EAAA,OAAA,CAAA,EAAA,MAAA,EAAA,CAAA,2BAAA,EAAA,QAAA,CAAA,EAAA,QAAA,EAAA,CAAA,6BAAA,EAAA,UAAA,CAAA,EAAA,SAAA,EAAA,CAAA,8BAAA,EAAA,WAAA,CAAA,EAAA,aAAA,EAAA,CAAA,kCAAA,EAAA,eAAA,CAAA,EAAA,UAAA,EAAA,CAAA,+BAAA,EAAA,YAAA,CAAA,EAAA,cAAA,EAAA,CAAA,mCAAA,EAAA,gBAAA,CAAA,EAAA,cAAA,EAAA,CAAA,mCAAA,EAAA,gBAAA,CAAA,EAAA,IAAA,EAAA,CAAA,yBAAA,EAAA,MAAA,CAAA,EAAA,YAAA,EAAA,CAAA,iCAAA,EAAA,cAAA,CAAA,EAAA,uBAAA,EAAA,CAAA,sCAAA,EAAA,yBAAA,CAAA,EAAA,WAAA,EAAA,CAAA,gCAAA,EAAA,aAAA,EAuF8B,gBAAgB,CAAA,EAAA,YAAA,EAAA,CAAA,iCAAA,EAAA,cAAA,EAIf,gBAAgB,CAAA,EAAA,kBAAA,EAAA,CAAA,uCAAA,EAAA,oBAAA,EAIV,gBAAgB,CAAA,EAAA,aAAA,EAAA,CAAA,kCAAA,EAAA,eAAA,EAIrB,gBAAgB,CAAA,EAAA,IAAA,EAAA,CAAA,yBAAA,EAAA,MAAA,EAIzB,gBAAgB,CAAA,EAAA,mBAAA,EAAA,CAAA,wCAAA,EAAA,qBAAA,EAGD,gBAAgB,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,aAAA,EAAA,eAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FA1GzE,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAL/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,qEAAqE;AAC/E,oBAAA,QAAQ,EAAE,qBAAqB;AAC/B,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BA2II,MAAM;2BAAC,qCAAqC,CAAA;;0BAC5C,QAAQ;yCA3HX,MAAM,EAAA,CAAA;sBADL,KAAK;uBAAC,2BAA2B,CAAA;gBAIK,SAAS,EAAA,CAAA;sBAA/C,KAAK;uBAAC,8BAA8B,CAAA;gBAMS,gBAAgB,EAAA,CAAA;sBAA7D,KAAK;uBAAC,qCAAqC,CAAA;gBAIxC,OAAO,EAAA,CAAA;sBADV,KAAK;uBAAC,4BAA4B,CAAA;gBAc/B,OAAO,EAAA,CAAA;sBADV,KAAK;uBAAC,4BAA4B,CAAA;gBAaA,KAAK,EAAA,CAAA;sBAAvC,KAAK;uBAAC,0BAA0B,CAAA;gBAGG,MAAM,EAAA,CAAA;sBAAzC,KAAK;uBAAC,2BAA2B,CAAA;gBAGI,QAAQ,EAAA,CAAA;sBAA7C,KAAK;uBAAC,6BAA6B,CAAA;gBAGG,SAAS,EAAA,CAAA;sBAA/C,KAAK;uBAAC,8BAA8B,CAAA;gBAGM,aAAa,EAAA,CAAA;sBAAvD,KAAK;uBAAC,kCAAkC,CAAA;gBAGD,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,+BAA+B,CAAA;gBAGM,cAAc,EAAA,CAAA;sBAAzD,KAAK;uBAAC,mCAAmC,CAAA;gBAGE,cAAc,EAAA,CAAA;sBAAzD,KAAK;uBAAC,mCAAmC,CAAA;gBAGR,IAAI,EAAA,CAAA;sBAArC,KAAK;uBAAC,yBAAyB,CAAA;gBAGU,YAAY,EAAA,CAAA;sBAArD,KAAK;uBAAC,iCAAiC,CAAA;gBAGO,uBAAuB,EAAA,CAAA;sBAArE,KAAK;uBAAC,sCAAsC,CAAA;gBAI7C,WAAW,EAAA,CAAA;sBADV,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,gCAAgC,EAAE,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAK7E,YAAY,EAAA,CAAA;sBADX,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,iCAAiC,EAAE,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAK9E,kBAAkB,EAAA,CAAA;sBADjB,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,uCAAuC,EAAE,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAKpF,aAAa,EAAA,CAAA;sBADZ,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,kCAAkC,EAAE,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAIP,IAAI,EAAA,CAAA;sBAA3E,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,yBAAyB,EAAE,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAIlE,mBAAmB,EAAA,CAAA;sBADtB,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,wCAAwC,EAAE,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBASlE,aAAa,EAAA,CAAA;sBAA/B,MAAM;gBAGY,cAAc,EAAA,CAAA;sBAAhC,MAAM;gBAGY,MAAM,EAAA,CAAA;sBAAxB,MAAM;gBAGY,MAAM,EAAA,CAAA;sBAAxB,MAAM;gBAGY,cAAc,EAAA,CAAA;sBAAhC,MAAM;gBAGY,mBAAmB,EAAA,CAAA;sBAArC,MAAM;;AAmOT;AACM,SAAU,sDAAsD,CACpE,OAAgB,EAAA;IAEhB,OAAO,MAAM,OAAO,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;AACrD,CAAC;AAED;AACO,MAAM,8CAA8C,GAAG;AAC5D,IAAA,OAAO,EAAE,qCAAqC;IAC9C,IAAI,EAAE,CAAC,OAAO,CAAC;AACf,IAAA,UAAU,EAAE,sDAAsD;CACnE;;MCrcY,aAAa,CAAA;8GAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAb,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,EAJd,OAAA,EAAA,CAAA,UAAU,EAAE,YAAY,EAAE,eAAe,EAAE,mBAAmB,EAAE,gBAAgB,CAChF,EAAA,OAAA,EAAA,CAAA,mBAAmB,EAAE,gBAAgB,EAAE,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;AAGrD,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,EAFb,SAAA,EAAA,CAAC,OAAO,EAAE,8CAA8C,CAAC,EAF1D,OAAA,EAAA,CAAA,UAAU,EAAE,YAAY,EAAE,eAAe,EACF,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAGrD,aAAa,EAAA,UAAA,EAAA,CAAA;kBALzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,eAAe,EAAE,mBAAmB,EAAE,gBAAgB,CAAC;AAC3F,oBAAA,OAAO,EAAE,CAAC,mBAAmB,EAAE,gBAAgB,EAAE,eAAe,CAAC;AACjE,oBAAA,SAAS,EAAE,CAAC,OAAO,EAAE,8CAA8C,CAAC;AACrE,iBAAA,CAAA;;;ACVD;;;;;;AAMG;AAEG,MAAO,0BAA2B,SAAQ,gBAAgB,CAAA;IAI9D,WAA8B,CAAA,SAAc,EAAE,QAAkB,EAAA;AAC9D,QAAA,KAAK,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;KAC5B;IAEQ,WAAW,GAAA;QAClB,KAAK,CAAC,WAAW,EAAE,CAAC;QAEpB,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,mBAAmB,EAAE;AACzD,YAAA,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;SACzF;KACF;IAEkB,gBAAgB,GAAA;QACjC,KAAK,CAAC,gBAAgB,EAAE,CAAC;QACzB,IAAI,CAAC,gCAAgC,EAAE,CAAC;QACxC,IAAI,CAAC,4BAA4B,CAAC,MAAM,IAAI,CAAC,gCAAgC,EAAE,CAAC,CAAC;KAClF;IAEO,gCAAgC,GAAA;AACtC,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC3B,OAAO;SACR;AAED,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACtD,MAAM,MAAM,GAAG,iBAAiB,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;AACxD,QAAA,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;KAC5C;AAEO,IAAA,4BAA4B,CAAC,EAAc,EAAA;AACjD,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QAEvC,IAAI,SAAS,EAAE;AACb,YAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC5B,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;aACzE;YAED,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;AAC/C,YAAA,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;SAC/B;KACF;IAEO,aAAa,GAAA;AACnB,QAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;AAC9B,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAgB,CAAC;AAExC,YAAA,IAAI,SAAS,CAAC,iBAAiB,EAAE;AAC/B,gBAAA,IAAI,CAAC,oBAAoB,GAAG,kBAAkB,CAAC;aAChD;AAAM,iBAAA,IAAI,SAAS,CAAC,uBAAuB,EAAE;AAC5C,gBAAA,IAAI,CAAC,oBAAoB,GAAG,wBAAwB,CAAC;aACtD;AAAM,iBAAA,IAAI,SAAS,CAAC,oBAAoB,EAAE;AACzC,gBAAA,IAAI,CAAC,oBAAoB,GAAG,qBAAqB,CAAC;aACnD;AAAM,iBAAA,IAAI,SAAS,CAAC,mBAAmB,EAAE;AACxC,gBAAA,IAAI,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;aAClD;SACF;QAED,OAAO,IAAI,CAAC,oBAAoB,CAAC;KAClC;AAED;;;AAGG;IACH,oBAAoB,GAAA;AAClB,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAgB,CAAC;QAExC,QACE,SAAS,CAAC,iBAAiB;AAC3B,YAAA,SAAS,CAAC,uBAAuB;AACjC,YAAA,SAAS,CAAC,oBAAoB;AAC9B,YAAA,SAAS,CAAC,mBAAmB;AAC7B,YAAA,IAAI,EACJ;KACH;AA7EU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,0BAA0B,kBAIjB,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAAD,IAAA,CAAA,QAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAJjB,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,0BAA0B,cADd,MAAM,EAAA,CAAA,CAAA,EAAA;;2FAClB,0BAA0B,EAAA,UAAA,EAAA,CAAA;kBADtC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;0BAKjB,MAAM;2BAAC,QAAQ,CAAA;;;ACzB9B;;AAEG;;;;"}