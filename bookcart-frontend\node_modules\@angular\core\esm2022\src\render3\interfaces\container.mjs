/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { FLAGS, HOST, NEXT, PARENT, T_HOST } from './view';
/**
 * Special location which allows easy identification of type. If we have an array which was
 * retrieved from the `LView` and that array has `true` at `TYPE` location, we know it is
 * `LContainer`.
 */
export const TYPE = 1;
/**
 * Below are constants for LContainer indices to help us look up LContainer members
 * without having to remember the specific indices.
 * Uglify will inline these when minifying so there shouldn't be a cost.
 */
// FLAGS, PARENT, NEXT, and T_HOST are indices 2, 3, 4, and 5
// As we already have these constants in LView, we don't need to re-create them.
export const DEHYDRATED_VIEWS = 6;
export const NATIVE = 7;
export const VIEW_REFS = 8;
export const MOVED_VIEWS = 9;
/**
 * Size of LContainer's header. Represents the index after which all views in the
 * container will be inserted. We need to keep a record of current views so we know
 * which views are already in the DOM (and don't need to be re-added) and so we can
 * remove views from the DOM when they are no longer required.
 */
export const CONTAINER_HEADER_OFFSET = 10;
/** Flags associated with an LContainer (saved in LContainer[FLAGS]) */
export var LContainerFlags;
(function (LContainerFlags) {
    LContainerFlags[LContainerFlags["None"] = 0] = "None";
    /**
     * Flag to signify that this `LContainer` may have transplanted views which need to be change
     * detected. (see: `LView[DECLARATION_COMPONENT_VIEW])`.
     *
     * This flag, once set, is never unset for the `LContainer`.
     */
    LContainerFlags[LContainerFlags["HasTransplantedViews"] = 2] = "HasTransplantedViews";
})(LContainerFlags || (LContainerFlags = {}));
//# sourceMappingURL=data:application/json;base64,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