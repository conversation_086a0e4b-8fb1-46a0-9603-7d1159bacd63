/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["ne-IN", [["पूर्वाह्न", "अपराह्न"], u, u], u, [["आ", "सो", "म", "बु", "बि", "शु", "श"], ["आइत", "सोम", "मङ्गल", "बुध", "बिहि", "शुक्र", "शनि"], ["आइतबार", "सोमबार", "मङ्गलबार", "बुधबार", "बिहिबार", "शुक्रबार", "शनिबार"], ["आइत", "सोम", "मङ्गल", "बुध", "बिहि", "शुक्र", "शनि"]], u, [["जन", "फेब", "मार्च", "अप्र", "मे", "जुन", "जुल", "अग", "सेप", "अक्टो", "नोभे", "डिसे"], ["जनवरी", "फेब्रुअरी", "मार्च", "अप्रिल", "मे", "जुन", "जुलाई", "अगस्ट", "सेप्टेम्बर", "अक्टोबर", "नोभेम्बर", "डिसेम्बर"], u], [["जन", "फेेब", "मार्च", "अप्र", "मे", "जुन", "जुल", "अग", "सेप", "अक्टो", "नोभे", "डिसे"], ["जनवरी", "फेब्रुअरी", "मार्च", "अप्रिल", "मे", "जुन", "जुलाई", "अगस्ट", "सेप्टेम्बर", "अक्टोबर", "नोभेम्बर", "डिसेम्बर"], u], [["ईसा पूर्व", "सन्"], u, u], 0, [0, 0], ["yy/M/d", "y MMM d", "y MMMM d", "y MMMM d, EEEE"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1}, {0}", u, "{1} {0}", u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##,##0.###", "#,##,##0%", "¤ #,##,##0.00", "#E0"], "INR", "₹", "भारतीय रूपिँया", { "BYN": [u, "р."], "JPY": ["JP¥", "¥"], "NPR": ["नेरू", "रू"], "PHP": [u, "₱"], "THB": ["฿"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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