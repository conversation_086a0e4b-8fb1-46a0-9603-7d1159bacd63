/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
const markedFeatures = new Set();
// tslint:disable:ban
/**
 * A guarded `performance.mark` for feature marking.
 *
 * This method exists because while all supported browser and node.js version supported by Angular
 * support performance.mark API. This is not the case for other environments such as JSDOM and
 * Cloudflare workers.
 */
export function performanceMarkFeature(feature) {
    if (markedFeatures.has(feature)) {
        return;
    }
    markedFeatures.add(feature);
    performance?.mark?.('mark_feature_usage', { detail: { feature } });
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicGVyZm9ybWFuY2UuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9jb3JlL3NyYy91dGlsL3BlcmZvcm1hbmNlLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7R0FNRztBQUVILE1BQU0sY0FBYyxHQUFHLElBQUksR0FBRyxFQUFVLENBQUM7QUFFekMscUJBQXFCO0FBQ3JCOzs7Ozs7R0FNRztBQUNILE1BQU0sVUFBVSxzQkFBc0IsQ0FBQyxPQUFlO0lBQ3BELElBQUksY0FBYyxDQUFDLEdBQUcsQ0FBQyxPQUFPLENBQUMsRUFBRSxDQUFDO1FBQ2hDLE9BQU87SUFDVCxDQUFDO0lBQ0QsY0FBYyxDQUFDLEdBQUcsQ0FBQyxPQUFPLENBQUMsQ0FBQztJQUM1QixXQUFXLEVBQUUsSUFBSSxFQUFFLENBQUMsb0JBQW9CLEVBQUUsRUFBQyxNQUFNLEVBQUUsRUFBQyxPQUFPLEVBQUMsRUFBQyxDQUFDLENBQUM7QUFDakUsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuXG5jb25zdCBtYXJrZWRGZWF0dXJlcyA9IG5ldyBTZXQ8c3RyaW5nPigpO1xuXG4vLyB0c2xpbnQ6ZGlzYWJsZTpiYW5cbi8qKlxuICogQSBndWFyZGVkIGBwZXJmb3JtYW5jZS5tYXJrYCBmb3IgZmVhdHVyZSBtYXJraW5nLlxuICpcbiAqIFRoaXMgbWV0aG9kIGV4aXN0cyBiZWNhdXNlIHdoaWxlIGFsbCBzdXBwb3J0ZWQgYnJvd3NlciBhbmQgbm9kZS5qcyB2ZXJzaW9uIHN1cHBvcnRlZCBieSBBbmd1bGFyXG4gKiBzdXBwb3J0IHBlcmZvcm1hbmNlLm1hcmsgQVBJLiBUaGlzIGlzIG5vdCB0aGUgY2FzZSBmb3Igb3RoZXIgZW52aXJvbm1lbnRzIHN1Y2ggYXMgSlNET00gYW5kXG4gKiBDbG91ZGZsYXJlIHdvcmtlcnMuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBwZXJmb3JtYW5jZU1hcmtGZWF0dXJlKGZlYXR1cmU6IHN0cmluZyk6IHZvaWQge1xuICBpZiAobWFya2VkRmVhdHVyZXMuaGFzKGZlYXR1cmUpKSB7XG4gICAgcmV0dXJuO1xuICB9XG4gIG1hcmtlZEZlYXR1cmVzLmFkZChmZWF0dXJlKTtcbiAgcGVyZm9ybWFuY2U/Lm1hcms/LignbWFya19mZWF0dXJlX3VzYWdlJywge2RldGFpbDoge2ZlYXR1cmV9fSk7XG59XG4iXX0=