/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// Converts a string that represents a URL into a URL class instance.
export function getUrl(src, win) {
    // Don't use a base URL is the URL is absolute.
    return isAbsoluteUrl(src) ? new URL(src) : new URL(src, win.location.href);
}
// Checks whether a URL is absolute (i.e. starts with `http://` or `https://`).
export function isAbsoluteUrl(src) {
    return /^https?:\/\//.test(src);
}
// Given a URL, extract the hostname part.
// If a URL is a relative one - the URL is returned as is.
export function extractHostname(url) {
    return isAbsoluteUrl(url) ? new URL(url).hostname : url;
}
export function isValidPath(path) {
    const isString = typeof path === 'string';
    if (!isString || path.trim() === '') {
        return false;
    }
    // Calling new URL() will throw if the path string is malformed
    try {
        const url = new URL(path);
        return true;
    }
    catch {
        return false;
    }
}
export function normalizePath(path) {
    return path.endsWith('/') ? path.slice(0, -1) : path;
}
export function normalizeSrc(src) {
    return src.startsWith('/') ? src.slice(1) : src;
}
//# sourceMappingURL=data:application/json;base64,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