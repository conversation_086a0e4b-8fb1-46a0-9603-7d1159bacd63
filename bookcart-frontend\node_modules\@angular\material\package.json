{"name": "@angular/material", "version": "17.3.10", "description": "Angular Material", "repository": {"type": "git", "url": "https://github.com/angular/components.git"}, "keywords": ["angular", "material", "material design", "components"], "license": "MIT", "bugs": {"url": "https://github.com/angular/components/issues"}, "homepage": "https://github.com/angular/components#readme", "exports": {".": {"sass": "./_index.scss", "types": "./index.d.ts", "esm2022": "./esm2022/index.mjs", "esm": "./esm2022/index.mjs", "default": "./fesm2022/material.mjs"}, "./theming": {"sass": "./_theming.scss"}, "./_theming": {"sass": "./_theming.scss"}, "./prebuilt-themes/indigo-pink.css": {"style": "./prebuilt-themes/indigo-pink.css"}, "./prebuilt-themes/deeppurple-amber.css": {"style": "./prebuilt-themes/deeppurple-amber.css"}, "./prebuilt-themes/pink-bluegrey.css": {"style": "./prebuilt-themes/pink-bluegrey.css"}, "./prebuilt-themes/purple-green.css": {"style": "./prebuilt-themes/purple-green.css"}, "./prebuilt-themes/*": {"style": "./prebuilt-themes/*.css"}, "./package.json": {"default": "./package.json"}, "./autocomplete": {"types": "./autocomplete/index.d.ts", "esm2022": "./esm2022/autocomplete/autocomplete_public_index.mjs", "esm": "./esm2022/autocomplete/autocomplete_public_index.mjs", "default": "./fesm2022/autocomplete.mjs"}, "./autocomplete/testing": {"types": "./autocomplete/testing/index.d.ts", "esm2022": "./esm2022/autocomplete/testing/index.mjs", "esm": "./esm2022/autocomplete/testing/index.mjs", "default": "./fesm2022/autocomplete/testing.mjs"}, "./badge": {"types": "./badge/index.d.ts", "esm2022": "./esm2022/badge/badge_public_index.mjs", "esm": "./esm2022/badge/badge_public_index.mjs", "default": "./fesm2022/badge.mjs"}, "./badge/testing": {"types": "./badge/testing/index.d.ts", "esm2022": "./esm2022/badge/testing/index.mjs", "esm": "./esm2022/badge/testing/index.mjs", "default": "./fesm2022/badge/testing.mjs"}, "./bottom-sheet": {"types": "./bottom-sheet/index.d.ts", "esm2022": "./esm2022/bottom-sheet/bottom-sheet_public_index.mjs", "esm": "./esm2022/bottom-sheet/bottom-sheet_public_index.mjs", "default": "./fesm2022/bottom-sheet.mjs"}, "./bottom-sheet/testing": {"types": "./bottom-sheet/testing/index.d.ts", "esm2022": "./esm2022/bottom-sheet/testing/index.mjs", "esm": "./esm2022/bottom-sheet/testing/index.mjs", "default": "./fesm2022/bottom-sheet/testing.mjs"}, "./button": {"types": "./button/index.d.ts", "esm2022": "./esm2022/button/button_public_index.mjs", "esm": "./esm2022/button/button_public_index.mjs", "default": "./fesm2022/button.mjs"}, "./button-toggle": {"types": "./button-toggle/index.d.ts", "esm2022": "./esm2022/button-toggle/button-toggle_public_index.mjs", "esm": "./esm2022/button-toggle/button-toggle_public_index.mjs", "default": "./fesm2022/button-toggle.mjs"}, "./button-toggle/testing": {"types": "./button-toggle/testing/index.d.ts", "esm2022": "./esm2022/button-toggle/testing/index.mjs", "esm": "./esm2022/button-toggle/testing/index.mjs", "default": "./fesm2022/button-toggle/testing.mjs"}, "./button/testing": {"types": "./button/testing/index.d.ts", "esm2022": "./esm2022/button/testing/index.mjs", "esm": "./esm2022/button/testing/index.mjs", "default": "./fesm2022/button/testing.mjs"}, "./card": {"types": "./card/index.d.ts", "esm2022": "./esm2022/card/card_public_index.mjs", "esm": "./esm2022/card/card_public_index.mjs", "default": "./fesm2022/card.mjs"}, "./card/testing": {"types": "./card/testing/index.d.ts", "esm2022": "./esm2022/card/testing/index.mjs", "esm": "./esm2022/card/testing/index.mjs", "default": "./fesm2022/card/testing.mjs"}, "./checkbox": {"types": "./checkbox/index.d.ts", "esm2022": "./esm2022/checkbox/checkbox_public_index.mjs", "esm": "./esm2022/checkbox/checkbox_public_index.mjs", "default": "./fesm2022/checkbox.mjs"}, "./checkbox/testing": {"types": "./checkbox/testing/index.d.ts", "esm2022": "./esm2022/checkbox/testing/index.mjs", "esm": "./esm2022/checkbox/testing/index.mjs", "default": "./fesm2022/checkbox/testing.mjs"}, "./chips": {"types": "./chips/index.d.ts", "esm2022": "./esm2022/chips/chips_public_index.mjs", "esm": "./esm2022/chips/chips_public_index.mjs", "default": "./fesm2022/chips.mjs"}, "./chips/testing": {"types": "./chips/testing/index.d.ts", "esm2022": "./esm2022/chips/testing/index.mjs", "esm": "./esm2022/chips/testing/index.mjs", "default": "./fesm2022/chips/testing.mjs"}, "./core": {"types": "./core/index.d.ts", "esm2022": "./esm2022/core/core_public_index.mjs", "esm": "./esm2022/core/core_public_index.mjs", "default": "./fesm2022/core.mjs"}, "./core/testing": {"types": "./core/testing/index.d.ts", "esm2022": "./esm2022/core/testing/index.mjs", "esm": "./esm2022/core/testing/index.mjs", "default": "./fesm2022/core/testing.mjs"}, "./datepicker": {"types": "./datepicker/index.d.ts", "esm2022": "./esm2022/datepicker/datepicker_public_index.mjs", "esm": "./esm2022/datepicker/datepicker_public_index.mjs", "default": "./fesm2022/datepicker.mjs"}, "./datepicker/testing": {"types": "./datepicker/testing/index.d.ts", "esm2022": "./esm2022/datepicker/testing/index.mjs", "esm": "./esm2022/datepicker/testing/index.mjs", "default": "./fesm2022/datepicker/testing.mjs"}, "./dialog": {"types": "./dialog/index.d.ts", "esm2022": "./esm2022/dialog/dialog_public_index.mjs", "esm": "./esm2022/dialog/dialog_public_index.mjs", "default": "./fesm2022/dialog.mjs"}, "./dialog/testing": {"types": "./dialog/testing/index.d.ts", "esm2022": "./esm2022/dialog/testing/index.mjs", "esm": "./esm2022/dialog/testing/index.mjs", "default": "./fesm2022/dialog/testing.mjs"}, "./divider": {"types": "./divider/index.d.ts", "esm2022": "./esm2022/divider/divider_public_index.mjs", "esm": "./esm2022/divider/divider_public_index.mjs", "default": "./fesm2022/divider.mjs"}, "./divider/testing": {"types": "./divider/testing/index.d.ts", "esm2022": "./esm2022/divider/testing/index.mjs", "esm": "./esm2022/divider/testing/index.mjs", "default": "./fesm2022/divider/testing.mjs"}, "./expansion": {"types": "./expansion/index.d.ts", "esm2022": "./esm2022/expansion/expansion_public_index.mjs", "esm": "./esm2022/expansion/expansion_public_index.mjs", "default": "./fesm2022/expansion.mjs"}, "./expansion/testing": {"types": "./expansion/testing/index.d.ts", "esm2022": "./esm2022/expansion/testing/index.mjs", "esm": "./esm2022/expansion/testing/index.mjs", "default": "./fesm2022/expansion/testing.mjs"}, "./form-field": {"types": "./form-field/index.d.ts", "esm2022": "./esm2022/form-field/form-field_public_index.mjs", "esm": "./esm2022/form-field/form-field_public_index.mjs", "default": "./fesm2022/form-field.mjs"}, "./form-field/testing": {"types": "./form-field/testing/index.d.ts", "esm2022": "./esm2022/form-field/testing/index.mjs", "esm": "./esm2022/form-field/testing/index.mjs", "default": "./fesm2022/form-field/testing.mjs"}, "./form-field/testing/control": {"types": "./form-field/testing/control/index.d.ts", "esm2022": "./esm2022/form-field/testing/control/index.mjs", "esm": "./esm2022/form-field/testing/control/index.mjs", "default": "./fesm2022/form-field/testing/control.mjs"}, "./grid-list": {"types": "./grid-list/index.d.ts", "esm2022": "./esm2022/grid-list/grid-list_public_index.mjs", "esm": "./esm2022/grid-list/grid-list_public_index.mjs", "default": "./fesm2022/grid-list.mjs"}, "./grid-list/testing": {"types": "./grid-list/testing/index.d.ts", "esm2022": "./esm2022/grid-list/testing/index.mjs", "esm": "./esm2022/grid-list/testing/index.mjs", "default": "./fesm2022/grid-list/testing.mjs"}, "./icon": {"types": "./icon/index.d.ts", "esm2022": "./esm2022/icon/icon_public_index.mjs", "esm": "./esm2022/icon/icon_public_index.mjs", "default": "./fesm2022/icon.mjs"}, "./icon/testing": {"types": "./icon/testing/index.d.ts", "esm2022": "./esm2022/icon/testing/testing_public_index.mjs", "esm": "./esm2022/icon/testing/testing_public_index.mjs", "default": "./fesm2022/icon/testing.mjs"}, "./input": {"types": "./input/index.d.ts", "esm2022": "./esm2022/input/input_public_index.mjs", "esm": "./esm2022/input/input_public_index.mjs", "default": "./fesm2022/input.mjs"}, "./input/testing": {"types": "./input/testing/index.d.ts", "esm2022": "./esm2022/input/testing/index.mjs", "esm": "./esm2022/input/testing/index.mjs", "default": "./fesm2022/input/testing.mjs"}, "./list": {"types": "./list/index.d.ts", "esm2022": "./esm2022/list/list_public_index.mjs", "esm": "./esm2022/list/list_public_index.mjs", "default": "./fesm2022/list.mjs"}, "./list/testing": {"types": "./list/testing/index.d.ts", "esm2022": "./esm2022/list/testing/index.mjs", "esm": "./esm2022/list/testing/index.mjs", "default": "./fesm2022/list/testing.mjs"}, "./menu": {"types": "./menu/index.d.ts", "esm2022": "./esm2022/menu/menu_public_index.mjs", "esm": "./esm2022/menu/menu_public_index.mjs", "default": "./fesm2022/menu.mjs"}, "./menu/testing": {"types": "./menu/testing/index.d.ts", "esm2022": "./esm2022/menu/testing/index.mjs", "esm": "./esm2022/menu/testing/index.mjs", "default": "./fesm2022/menu/testing.mjs"}, "./paginator": {"types": "./paginator/index.d.ts", "esm2022": "./esm2022/paginator/paginator_public_index.mjs", "esm": "./esm2022/paginator/paginator_public_index.mjs", "default": "./fesm2022/paginator.mjs"}, "./paginator/testing": {"types": "./paginator/testing/index.d.ts", "esm2022": "./esm2022/paginator/testing/index.mjs", "esm": "./esm2022/paginator/testing/index.mjs", "default": "./fesm2022/paginator/testing.mjs"}, "./progress-bar": {"types": "./progress-bar/index.d.ts", "esm2022": "./esm2022/progress-bar/progress-bar_public_index.mjs", "esm": "./esm2022/progress-bar/progress-bar_public_index.mjs", "default": "./fesm2022/progress-bar.mjs"}, "./progress-bar/testing": {"types": "./progress-bar/testing/index.d.ts", "esm2022": "./esm2022/progress-bar/testing/testing_public_index.mjs", "esm": "./esm2022/progress-bar/testing/testing_public_index.mjs", "default": "./fesm2022/progress-bar/testing.mjs"}, "./progress-spinner": {"types": "./progress-spinner/index.d.ts", "esm2022": "./esm2022/progress-spinner/progress-spinner_public_index.mjs", "esm": "./esm2022/progress-spinner/progress-spinner_public_index.mjs", "default": "./fesm2022/progress-spinner.mjs"}, "./progress-spinner/testing": {"types": "./progress-spinner/testing/index.d.ts", "esm2022": "./esm2022/progress-spinner/testing/testing_public_index.mjs", "esm": "./esm2022/progress-spinner/testing/testing_public_index.mjs", "default": "./fesm2022/progress-spinner/testing.mjs"}, "./radio": {"types": "./radio/index.d.ts", "esm2022": "./esm2022/radio/radio_public_index.mjs", "esm": "./esm2022/radio/radio_public_index.mjs", "default": "./fesm2022/radio.mjs"}, "./radio/testing": {"types": "./radio/testing/index.d.ts", "esm2022": "./esm2022/radio/testing/index.mjs", "esm": "./esm2022/radio/testing/index.mjs", "default": "./fesm2022/radio/testing.mjs"}, "./select": {"types": "./select/index.d.ts", "esm2022": "./esm2022/select/select_public_index.mjs", "esm": "./esm2022/select/select_public_index.mjs", "default": "./fesm2022/select.mjs"}, "./select/testing": {"types": "./select/testing/index.d.ts", "esm2022": "./esm2022/select/testing/index.mjs", "esm": "./esm2022/select/testing/index.mjs", "default": "./fesm2022/select/testing.mjs"}, "./sidenav": {"types": "./sidenav/index.d.ts", "esm2022": "./esm2022/sidenav/sidenav_public_index.mjs", "esm": "./esm2022/sidenav/sidenav_public_index.mjs", "default": "./fesm2022/sidenav.mjs"}, "./sidenav/testing": {"types": "./sidenav/testing/index.d.ts", "esm2022": "./esm2022/sidenav/testing/index.mjs", "esm": "./esm2022/sidenav/testing/index.mjs", "default": "./fesm2022/sidenav/testing.mjs"}, "./slide-toggle": {"types": "./slide-toggle/index.d.ts", "esm2022": "./esm2022/slide-toggle/slide-toggle_public_index.mjs", "esm": "./esm2022/slide-toggle/slide-toggle_public_index.mjs", "default": "./fesm2022/slide-toggle.mjs"}, "./slide-toggle/testing": {"types": "./slide-toggle/testing/index.d.ts", "esm2022": "./esm2022/slide-toggle/testing/index.mjs", "esm": "./esm2022/slide-toggle/testing/index.mjs", "default": "./fesm2022/slide-toggle/testing.mjs"}, "./slider": {"types": "./slider/index.d.ts", "esm2022": "./esm2022/slider/slider_public_index.mjs", "esm": "./esm2022/slider/slider_public_index.mjs", "default": "./fesm2022/slider.mjs"}, "./slider/testing": {"types": "./slider/testing/index.d.ts", "esm2022": "./esm2022/slider/testing/index.mjs", "esm": "./esm2022/slider/testing/index.mjs", "default": "./fesm2022/slider/testing.mjs"}, "./snack-bar": {"types": "./snack-bar/index.d.ts", "esm2022": "./esm2022/snack-bar/snack-bar_public_index.mjs", "esm": "./esm2022/snack-bar/snack-bar_public_index.mjs", "default": "./fesm2022/snack-bar.mjs"}, "./snack-bar/testing": {"types": "./snack-bar/testing/index.d.ts", "esm2022": "./esm2022/snack-bar/testing/index.mjs", "esm": "./esm2022/snack-bar/testing/index.mjs", "default": "./fesm2022/snack-bar/testing.mjs"}, "./sort": {"types": "./sort/index.d.ts", "esm2022": "./esm2022/sort/sort_public_index.mjs", "esm": "./esm2022/sort/sort_public_index.mjs", "default": "./fesm2022/sort.mjs"}, "./sort/testing": {"types": "./sort/testing/index.d.ts", "esm2022": "./esm2022/sort/testing/index.mjs", "esm": "./esm2022/sort/testing/index.mjs", "default": "./fesm2022/sort/testing.mjs"}, "./stepper": {"types": "./stepper/index.d.ts", "esm2022": "./esm2022/stepper/stepper_public_index.mjs", "esm": "./esm2022/stepper/stepper_public_index.mjs", "default": "./fesm2022/stepper.mjs"}, "./stepper/testing": {"types": "./stepper/testing/index.d.ts", "esm2022": "./esm2022/stepper/testing/index.mjs", "esm": "./esm2022/stepper/testing/index.mjs", "default": "./fesm2022/stepper/testing.mjs"}, "./table": {"types": "./table/index.d.ts", "esm2022": "./esm2022/table/table_public_index.mjs", "esm": "./esm2022/table/table_public_index.mjs", "default": "./fesm2022/table.mjs"}, "./table/testing": {"types": "./table/testing/index.d.ts", "esm2022": "./esm2022/table/testing/index.mjs", "esm": "./esm2022/table/testing/index.mjs", "default": "./fesm2022/table/testing.mjs"}, "./tabs": {"types": "./tabs/index.d.ts", "esm2022": "./esm2022/tabs/tabs_public_index.mjs", "esm": "./esm2022/tabs/tabs_public_index.mjs", "default": "./fesm2022/tabs.mjs"}, "./tabs/testing": {"types": "./tabs/testing/index.d.ts", "esm2022": "./esm2022/tabs/testing/index.mjs", "esm": "./esm2022/tabs/testing/index.mjs", "default": "./fesm2022/tabs/testing.mjs"}, "./toolbar": {"types": "./toolbar/index.d.ts", "esm2022": "./esm2022/toolbar/toolbar_public_index.mjs", "esm": "./esm2022/toolbar/toolbar_public_index.mjs", "default": "./fesm2022/toolbar.mjs"}, "./toolbar/testing": {"types": "./toolbar/testing/index.d.ts", "esm2022": "./esm2022/toolbar/testing/index.mjs", "esm": "./esm2022/toolbar/testing/index.mjs", "default": "./fesm2022/toolbar/testing.mjs"}, "./tooltip": {"types": "./tooltip/index.d.ts", "esm2022": "./esm2022/tooltip/tooltip_public_index.mjs", "esm": "./esm2022/tooltip/tooltip_public_index.mjs", "default": "./fesm2022/tooltip.mjs"}, "./tooltip/testing": {"types": "./tooltip/testing/index.d.ts", "esm2022": "./esm2022/tooltip/testing/index.mjs", "esm": "./esm2022/tooltip/testing/index.mjs", "default": "./fesm2022/tooltip/testing.mjs"}, "./tree": {"types": "./tree/index.d.ts", "esm2022": "./esm2022/tree/tree_public_index.mjs", "esm": "./esm2022/tree/tree_public_index.mjs", "default": "./fesm2022/tree.mjs"}, "./tree/testing": {"types": "./tree/testing/index.d.ts", "esm2022": "./esm2022/tree/testing/index.mjs", "esm": "./esm2022/tree/testing/index.mjs", "default": "./fesm2022/tree/testing.mjs"}}, "peerDependencies": {"@angular/animations": "^17.0.0 || ^18.0.0", "@angular/cdk": "17.3.10", "@angular/core": "^17.0.0 || ^18.0.0", "@angular/common": "^17.0.0 || ^18.0.0", "@angular/forms": "^17.0.0 || ^18.0.0", "@angular/platform-browser": "^17.0.0 || ^18.0.0", "rxjs": "^6.5.3 || ^7.4.0"}, "dependencies": {"tslib": "^2.3.0", "@material/animation": "15.0.0-canary.7f224ddd4.0", "@material/auto-init": "15.0.0-canary.7f224ddd4.0", "@material/banner": "15.0.0-canary.7f224ddd4.0", "@material/base": "15.0.0-canary.7f224ddd4.0", "@material/button": "15.0.0-canary.7f224ddd4.0", "@material/card": "15.0.0-canary.7f224ddd4.0", "@material/checkbox": "15.0.0-canary.7f224ddd4.0", "@material/chips": "15.0.0-canary.7f224ddd4.0", "@material/circular-progress": "15.0.0-canary.7f224ddd4.0", "@material/data-table": "15.0.0-canary.7f224ddd4.0", "@material/density": "15.0.0-canary.7f224ddd4.0", "@material/dialog": "15.0.0-canary.7f224ddd4.0", "@material/dom": "15.0.0-canary.7f224ddd4.0", "@material/drawer": "15.0.0-canary.7f224ddd4.0", "@material/elevation": "15.0.0-canary.7f224ddd4.0", "@material/fab": "15.0.0-canary.7f224ddd4.0", "@material/feature-targeting": "15.0.0-canary.7f224ddd4.0", "@material/floating-label": "15.0.0-canary.7f224ddd4.0", "@material/form-field": "15.0.0-canary.7f224ddd4.0", "@material/icon-button": "15.0.0-canary.7f224ddd4.0", "@material/image-list": "15.0.0-canary.7f224ddd4.0", "@material/layout-grid": "15.0.0-canary.7f224ddd4.0", "@material/line-ripple": "15.0.0-canary.7f224ddd4.0", "@material/linear-progress": "15.0.0-canary.7f224ddd4.0", "@material/list": "15.0.0-canary.7f224ddd4.0", "@material/menu": "15.0.0-canary.7f224ddd4.0", "@material/menu-surface": "15.0.0-canary.7f224ddd4.0", "@material/notched-outline": "15.0.0-canary.7f224ddd4.0", "@material/radio": "15.0.0-canary.7f224ddd4.0", "@material/ripple": "15.0.0-canary.7f224ddd4.0", "@material/rtl": "15.0.0-canary.7f224ddd4.0", "@material/segmented-button": "15.0.0-canary.7f224ddd4.0", "@material/select": "15.0.0-canary.7f224ddd4.0", "@material/shape": "15.0.0-canary.7f224ddd4.0", "@material/slider": "15.0.0-canary.7f224ddd4.0", "@material/snackbar": "15.0.0-canary.7f224ddd4.0", "@material/switch": "15.0.0-canary.7f224ddd4.0", "@material/tab": "15.0.0-canary.7f224ddd4.0", "@material/tab-bar": "15.0.0-canary.7f224ddd4.0", "@material/tab-indicator": "15.0.0-canary.7f224ddd4.0", "@material/tab-scroller": "15.0.0-canary.7f224ddd4.0", "@material/textfield": "15.0.0-canary.7f224ddd4.0", "@material/theme": "15.0.0-canary.7f224ddd4.0", "@material/tooltip": "15.0.0-canary.7f224ddd4.0", "@material/top-app-bar": "15.0.0-canary.7f224ddd4.0", "@material/touch-target": "15.0.0-canary.7f224ddd4.0", "@material/typography": "15.0.0-canary.7f224ddd4.0"}, "schematics": "./schematics/collection.json", "ng-update": {"migrations": "./schematics/migration.json", "packageGroup": ["@angular/material", "@angular/cdk", "@angular/material-moment-adapter", "@angular/material-luxon-adapter", "@angular/material-date-fns-adapter"]}, "sideEffects": false, "module": "./fesm2022/material.mjs", "typings": "./index.d.ts", "type": "module"}