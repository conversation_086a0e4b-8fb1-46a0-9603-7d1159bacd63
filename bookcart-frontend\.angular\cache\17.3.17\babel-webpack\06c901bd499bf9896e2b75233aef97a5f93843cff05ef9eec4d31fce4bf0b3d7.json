{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class OrderDetailComponent {\n  static {\n    this.ɵfac = function OrderDetailComponent_Factory(t) {\n      return new (t || OrderDetailComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OrderDetailComponent,\n      selectors: [[\"app-order-detail\"]],\n      decls: 5,\n      vars: 0,\n      consts: [[1, \"order-detail-container\"]],\n      template: function OrderDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h1\");\n          i0.ɵɵtext(2, \"Order Details\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"p\");\n          i0.ɵɵtext(4, \"Order detail functionality coming soon...\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      styles: [\".order-detail-container[_ngcontent-%COMP%] {\\n      max-width: 800px;\\n      margin: 0 auto;\\n      padding: 20px;\\n    }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvb3JkZXJzL29yZGVyLWRldGFpbC9vcmRlci1kZXRhaWwuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7SUFDSTtNQUNFLGdCQUFnQjtNQUNoQixjQUFjO01BQ2QsYUFBYTtJQUNmIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLm9yZGVyLWRldGFpbC1jb250YWluZXIge1xuICAgICAgbWF4LXdpZHRoOiA4MDBweDtcbiAgICAgIG1hcmdpbjogMCBhdXRvO1xuICAgICAgcGFkZGluZzogMjBweDtcbiAgICB9XG4gICJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["OrderDetailComponent", "selectors", "decls", "vars", "consts", "template", "OrderDetailComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\BookCart\\bookcart-frontend\\src\\app\\orders\\order-detail\\order-detail.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-order-detail',\n  template: `\n    <div class=\"order-detail-container\">\n      <h1>Order Details</h1>\n      <p>Order detail functionality coming soon...</p>\n    </div>\n  `,\n  styles: [`\n    .order-detail-container {\n      max-width: 800px;\n      margin: 0 auto;\n      padding: 20px;\n    }\n  `]\n})\nexport class OrderDetailComponent {}\n"], "mappings": ";AAkBA,OAAM,MAAOA,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA,oBAAoB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAZ3BE,EADF,CAAAC,cAAA,aAAoC,SAC9B;UAAAD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtBH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,gDAAyC;UAC9CF,EAD8C,CAAAG,YAAA,EAAI,EAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}