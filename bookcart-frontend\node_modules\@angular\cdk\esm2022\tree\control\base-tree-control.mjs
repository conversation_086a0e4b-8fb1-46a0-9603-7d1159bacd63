/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { SelectionModel } from '@angular/cdk/collections';
/** Base tree control. It has basic toggle/expand/collapse operations on a single data node. */
export class BaseTreeControl {
    constructor() {
        /** A selection model with multi-selection to track expansion status. */
        this.expansionModel = new SelectionModel(true);
    }
    /** Toggles one single data node's expanded/collapsed state. */
    toggle(dataNode) {
        this.expansionModel.toggle(this._trackByValue(dataNode));
    }
    /** Expands one single data node. */
    expand(dataNode) {
        this.expansionModel.select(this._trackByValue(dataNode));
    }
    /** Collapses one single data node. */
    collapse(dataNode) {
        this.expansionModel.deselect(this._trackByValue(dataNode));
    }
    /** Whether a given data node is expanded or not. Returns true if the data node is expanded. */
    isExpanded(dataNode) {
        return this.expansionModel.isSelected(this._trackByValue(dataNode));
    }
    /** Toggles a subtree rooted at `node` recursively. */
    toggleDescendants(dataNode) {
        this.expansionModel.isSelected(this._trackByValue(dataNode))
            ? this.collapseDescendants(dataNode)
            : this.expandDescendants(dataNode);
    }
    /** Collapse all dataNodes in the tree. */
    collapseAll() {
        this.expansionModel.clear();
    }
    /** Expands a subtree rooted at given data node recursively. */
    expandDescendants(dataNode) {
        let toBeProcessed = [dataNode];
        toBeProcessed.push(...this.getDescendants(dataNode));
        this.expansionModel.select(...toBeProcessed.map(value => this._trackByValue(value)));
    }
    /** Collapses a subtree rooted at given data node recursively. */
    collapseDescendants(dataNode) {
        let toBeProcessed = [dataNode];
        toBeProcessed.push(...this.getDescendants(dataNode));
        this.expansionModel.deselect(...toBeProcessed.map(value => this._trackByValue(value)));
    }
    _trackByValue(value) {
        return this.trackBy ? this.trackBy(value) : value;
    }
}
//# sourceMappingURL=data:application/json;base64,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