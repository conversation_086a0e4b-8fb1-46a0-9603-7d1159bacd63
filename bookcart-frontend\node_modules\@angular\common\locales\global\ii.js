/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['ii'] = ["ii",[["ꎸꄑ","ꁯꋒ"],u,u],u,[["ꆏ","ꋍ","ꑍ","ꌕ","ꇖ","ꉬ","ꃘ"],["ꑭꆏ","ꆏꋍ","ꆏꑍ","ꆏꌕ","ꆏꇖ","ꆏꉬ","ꆏꃘ"],["ꑭꆏꑍ","ꆏꊂꋍ","ꆏꊂꑍ","ꆏꊂꌕ","ꆏꊂꇖ","ꆏꊂꉬ","ꆏꊂꃘ"],["ꑭꆏ","ꆏꋍ","ꆏꑍ","ꆏꌕ","ꆏꇖ","ꆏꉬ","ꆏꃘ"]],u,[["1","2","3","4","5","6","7","8","9","10","11","12"],["ꋍꆪ","ꑍꆪ","ꌕꆪ","ꇖꆪ","ꉬꆪ","ꃘꆪ","ꏃꆪ","ꉆꆪ","ꈬꆪ","ꊰꆪ","ꊰꊪꆪ","ꊰꑋꆪ"],u],u,[["ꃅꋊꂿ","ꃅꋊꊂ"],u,u],0,[6,0],["y-MM-dd","y MMM d","y MMMM d","y MMMM d, EEEE"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤ #,##0.00","#E0"],"CNY","¥","CNY",{"CNY":["¥"],"JPY":["JP¥","¥"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    