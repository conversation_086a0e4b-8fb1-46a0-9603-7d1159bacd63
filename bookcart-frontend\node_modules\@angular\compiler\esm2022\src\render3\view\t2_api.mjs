/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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