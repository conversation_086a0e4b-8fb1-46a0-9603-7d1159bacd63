/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { makePropDecorator } from '../util/decorators';
// Stores the default value of `emitDistinctChangesOnly` when the `emitDistinctChangesOnly` is not
// explicitly set.
export const emitDistinctChangesOnlyDefaultValue = true;
/**
 * Base class for query metadata.
 *
 * @see {@link ContentChildren}
 * @see {@link ContentChild}
 * @see {@link ViewChildren}
 * @see {@link ViewChild}
 *
 * @publicApi
 */
export class Query {
}
/**
 * ContentChildren decorator and metadata.
 *
 *
 * @Annotation
 * @publicApi
 */
export const ContentChildren = makePropDecorator('ContentChildren', (selector, opts = {}) => ({
    selector,
    first: false,
    isViewQuery: false,
    descendants: false,
    emitDistinctChangesOnly: emitDistinctChangesOnlyDefaultValue,
    ...opts
}), Query);
/**
 * ContentChild decorator and metadata.
 *
 *
 * @Annotation
 *
 * @publicApi
 */
export const ContentChild = makePropDecorator('ContentChild', (selector, opts = {}) => ({ selector, first: true, isViewQuery: false, descendants: true, ...opts }), Query);
/**
 * ViewChildren decorator and metadata.
 *
 * @Annotation
 * @publicApi
 */
export const ViewChildren = makePropDecorator('ViewChildren', (selector, opts = {}) => ({
    selector,
    first: false,
    isViewQuery: true,
    descendants: true,
    emitDistinctChangesOnly: emitDistinctChangesOnlyDefaultValue,
    ...opts
}), Query);
/**
 * ViewChild decorator and metadata.
 *
 * @Annotation
 * @publicApi
 */
export const ViewChild = makePropDecorator('ViewChild', (selector, opts) => ({ selector, first: true, isViewQuery: true, descendants: true, ...opts }), Query);
//# sourceMappingURL=data:application/json;base64,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