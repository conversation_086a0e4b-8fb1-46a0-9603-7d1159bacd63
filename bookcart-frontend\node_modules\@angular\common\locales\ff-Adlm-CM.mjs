/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["ff-Adlm-CM", [["𞤢", "𞤩"], ["𞤀𞤎", "𞤇𞤎"], u], [["𞤀𞤎", "𞤇𞤎"], u, u], [["𞤈", "𞤀𞥄", "𞤃", "𞤔", "𞤐", "𞤃", "𞤖"], ["𞤈𞤫𞤬", "𞤀𞥄𞤩𞤵", "𞤃𞤢𞤦", "𞤔𞤫𞤧", "𞤐𞤢𞥄𞤧", "𞤃𞤢𞤣", "𞤖𞤮𞤪"], ["𞤈𞤫𞤬𞤦𞤭𞤪𞥆𞤫", "𞤀𞥄𞤩𞤵𞤲𞥋𞤣𞤫", "𞤃𞤢𞤱𞤦𞤢𞥄𞤪𞤫", "𞤐𞤶𞤫𞤧𞤤𞤢𞥄𞤪𞤫", "𞤐𞤢𞥄𞤧𞤢𞥄𞤲𞤣𞤫", "𞤃𞤢𞤱𞤲𞤣𞤫", "𞤖𞤮𞤪𞤦𞤭𞤪𞥆𞤫"], ["𞤈𞤫𞤬", "𞤀𞥄𞤩𞤵", "𞤃𞤢𞤦", "𞤔𞤫𞤧", "𞤐𞤢𞥄𞤧", "𞤃𞤢𞤣", "𞤖𞤮𞤪"]], u, [["𞤅", "𞤕", "𞤄", "𞤅", "𞤁", "𞤑", "𞤃", "𞤔", "𞤅", "𞤒", "𞤔", "𞤄"], ["𞤅𞤭𞥅𞤤𞤮", "𞤕𞤮𞤤𞤼𞤮", "𞤐𞤦𞤮𞥅𞤴𞤮", "𞤅𞤫𞥅𞤼𞤮", "𞤁𞤵𞥅𞤶𞤮", "𞤑𞤮𞤪𞤧𞤮", "𞤃𞤮𞤪𞤧𞤮", "𞤔𞤵𞤳𞤮", "𞤅𞤭𞤤𞤼𞤮", "𞤒𞤢𞤪𞤳𞤮", "𞤔𞤮𞤤𞤮", "𞤄𞤮𞤱𞤼𞤮"], u], [["𞤅", "𞤕", "𞤄", "𞤅", "𞤁", "𞤑", "𞤃", "𞤔", "𞤅", "𞤒", "𞤔", "𞤄"], ["𞤅𞤭𞥅𞤤", "𞤕𞤮𞤤", "𞤐𞤦𞤮𞥅𞤴", "𞤅𞤫𞥅𞤼", "𞤁𞤵𞥅𞤶", "𞤑𞤮𞤪", "𞤃𞤮𞤪", "𞤔𞤵𞤳", "𞤅𞤭𞤤", "𞤒𞤢𞤪", "𞤔𞤮𞤤", "𞤄𞤮𞤱"], ["𞤅𞤭𞥅𞤤𞤮", "𞤕𞤮𞤤𞤼𞤮", "𞤐𞤦𞤮𞥅𞤴𞤮", "𞤅𞤫𞥅𞤼𞤮", "𞤁𞤵𞥅𞤶𞤮", "𞤑𞤮𞤪𞤧𞤮", "𞤃𞤮𞤪𞤧𞤮", "𞤔𞤵𞤳𞤮", "𞤅𞤭𞤤𞤼𞤮", "𞤒𞤢𞤪𞤳𞤮", "𞤔𞤮𞤤𞤮", "𞤄𞤮𞤱𞤼𞤮"]], [["𞤀𞤀𞤋", "𞤇𞤀𞤋"], u, ["𞤀𞤣𞤮 𞤀𞤲𞥆𞤢𞤦𞤭 𞤋𞥅𞤧𞤢𞥄", "𞤇𞤢𞥄𞤱𞤮 𞤀𞤲𞥆𞤢𞤦𞤭 𞤋𞥅𞤧𞤢𞥄"]], 1, [6, 0], ["d-M-y", "d MMM⹁ y", "d MMMM⹁ y", "EEEE d MMMM⹁ y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, "{1} 𞤉 {0}", u], [".", "⹁", ";", "%", "+", "-", "E", "×", "‰", "∞", "𞤏𞤮𞤈", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "XAF", "𞤊𞤅𞤊𞤀", "𞤊𞤢𞤪𞤢𞤲 𞤚𞤵𞤦𞤮𞥅𞤪𞤭 𞤀𞤬𞤪𞤭𞤳𞤭𞤲𞤳𞤮", { "BYN": [u, "р."], "GNF": [u, "𞤊𞤘"], "JPY": ["JP¥", "¥"], "NGN": ["𞤐𞤐𞤘", "₦"], "PGK": ["𞤑𞤆𞤘"], "PHP": ["𞤆𞤆𞤖", "₱"], "USD": ["US$", "$"], "XAF": ["𞤊𞤅𞤊𞤀"], "XOF": ["𞤅𞤊𞤀"] }, "rtl", plural];
//# sourceMappingURL=data:application/json;base64,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