/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
let profilerCallback = null;
/**
 * Sets the callback function which will be invoked before and after performing certain actions at
 * runtime (for example, before and after running change detection).
 *
 * Warning: this function is *INTERNAL* and should not be relied upon in application's code.
 * The contract of the function might be changed in any release and/or the function can be removed
 * completely.
 *
 * @param profiler function provided by the caller or null value to disable profiling.
 */
export const setProfiler = (profiler) => {
    profilerCallback = profiler;
};
/**
 * Profiler function which wraps user code executed by the runtime.
 *
 * @param event ProfilerEvent corresponding to the execution context
 * @param instance component instance
 * @param hookOrListener lifecycle hook function or output listener. The value depends on the
 *  execution context
 * @returns
 */
export const profiler = function (event, instance, hookOrListener) {
    if (profilerCallback != null /* both `null` and `undefined` */) {
        profilerCallback(event, instance, hookOrListener);
    }
};
//# sourceMappingURL=data:application/json;base64,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