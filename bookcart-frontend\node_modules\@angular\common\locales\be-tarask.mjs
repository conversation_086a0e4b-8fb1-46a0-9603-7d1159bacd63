/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n % 10 === 1 && !(n % 100 === 11))
        return 1;
    if (n % 10 === Math.floor(n % 10) && (n % 10 >= 2 && n % 10 <= 4) && !(n % 100 >= 12 && n % 100 <= 14))
        return 3;
    if (n % 10 === 0 || (n % 10 === Math.floor(n % 10) && (n % 10 >= 5 && n % 10 <= 9) || n % 100 === Math.floor(n % 100) && (n % 100 >= 11 && n % 100 <= 14)))
        return 4;
    return 5;
}
export default ["be-tarask", [["am", "pm"], ["AM", "PM"], u], [["AM", "PM"], u, u], [["н", "п", "а", "с", "ч", "п", "с"], ["нд", "пн", "аў", "ср", "чц", "пт", "сб"], ["нядзеля", "панядзелак", "аўторак", "серада", "чацвер", "пятніца", "субота"], ["нд", "пн", "аў", "ср", "чц", "пт", "сб"]], u, [["с", "л", "с", "к", "м", "ч", "л", "ж", "в", "к", "л", "с"], ["сту", "лют", "сак", "кра", "мая", "чэр", "ліп", "жні", "вер", "кас", "ліс", "сне"], ["студзеня", "лютага", "сакавіка", "красавіка", "мая", "чэрвеня", "ліпеня", "жніўня", "верасня", "кастрычніка", "лістапада", "снежня"]], [["с", "л", "с", "к", "м", "ч", "л", "ж", "в", "к", "л", "с"], ["сту", "лют", "сак", "кра", "май", "чэр", "ліп", "жні", "вер", "кас", "ліс", "сне"], ["студзень", "люты", "сакавік", "красавік", "май", "чэрвень", "ліпень", "жнівень", "верасень", "кастрычнік", "лістапад", "снежань"]], [["да н.э.", "н.э."], u, ["да нараджэння Хрыстова", "ад нараджэння Хрыстова"]], 1, [6, 0], ["d.MM.yy", "d MMM y 'г'.", "d MMMM y 'г'.", "EEEE, d MMMM y 'г'."], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss, zzzz"], ["{1}, {0}", u, "{1} 'у' {0}", u], [",", " ", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0 %", "#,##0.00 ¤", "#E0"], "BYN", "Br", "беларускі рубель", { "AUD": ["A$"], "BBD": [u, "Bds$"], "BMD": [u, "BD$"], "BRL": [u, "R$"], "BSD": [u, "B$"], "BYN": ["Br"], "BZD": [u, "BZ$"], "CAD": [u, "CA$"], "CUC": [u, "CUC$"], "CUP": [u, "$MN"], "DOP": [u, "RD$"], "FJD": [u, "FJ$"], "FKP": [u, "FK£"], "GYD": [u, "G$"], "ISK": [u, "Íkr"], "JMD": [u, "J$"], "KYD": [u, "CI$"], "LRD": [u, "L$"], "MXN": ["MX$"], "NAD": [u, "N$"], "NZD": [u, "NZ$"], "PHP": [u, "₱"], "RUB": ["₽", "руб."], "SBD": [u, "SI$"], "SGD": [u, "S$"], "TTD": [u, "TT$"], "UYU": [u, "$U"], "XCD": ["EC$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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