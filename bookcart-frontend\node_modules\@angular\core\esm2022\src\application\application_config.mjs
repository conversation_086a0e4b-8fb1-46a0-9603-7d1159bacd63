/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * Merge multiple application configurations from left to right.
 *
 * @param configs Two or more configurations to be merged.
 * @returns A merged [ApplicationConfig](api/core/ApplicationConfig).
 *
 * @publicApi
 */
export function mergeApplicationConfig(...configs) {
    return configs.reduce((prev, curr) => {
        return Object.assign(prev, curr, { providers: [...prev.providers, ...curr.providers] });
    }, { providers: [] });
}
//# sourceMappingURL=data:application/json;base64,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