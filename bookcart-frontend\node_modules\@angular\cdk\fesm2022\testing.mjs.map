{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../src/cdk/testing/change-detection.ts", "../../../../../../src/cdk/testing/component-harness.ts", "../../../../../../src/cdk/testing/harness-environment.ts", "../../../../../../src/cdk/testing/test-element.ts", "../../../../../../src/cdk/testing/test-element-errors.ts", "../../../../../../src/cdk/testing/text-filtering.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {BehaviorSubject, Subscription} from 'rxjs';\n\n/** Represents the status of auto change detection. */\nexport interface AutoChangeDetectionStatus {\n  /** Whether auto change detection is disabled. */\n  isDisabled: boolean;\n  /**\n   * An optional callback, if present it indicates that change detection should be run immediately,\n   * while handling the status change. The callback should then be called as soon as change\n   * detection is done.\n   */\n  onDetectChangesNow?: () => void;\n}\n\n/** Subject used to dispatch and listen for changes to the auto change detection status . */\nconst autoChangeDetectionSubject = new BehaviorSubject<AutoChangeDetectionStatus>({\n  isDisabled: false,\n});\n\n/** The current subscription to `autoChangeDetectionSubject`. */\nlet autoChangeDetectionSubscription: Subscription | null;\n\n/**\n * The default handler for auto change detection status changes. This handler will be used if the\n * specific environment does not install its own.\n * @param status The new auto change detection status.\n */\nfunction defaultAutoChangeDetectionHandler(status: AutoChangeDetectionStatus) {\n  status.onDetectChangesNow?.();\n}\n\n/**\n * Allows a test `HarnessEnvironment` to install its own handler for auto change detection status\n * changes.\n * @param handler The handler for the auto change detection status.\n */\nexport function handleAutoChangeDetectionStatus(\n  handler: (status: AutoChangeDetectionStatus) => void,\n) {\n  stopHandlingAutoChangeDetectionStatus();\n  autoChangeDetectionSubscription = autoChangeDetectionSubject.subscribe(handler);\n}\n\n/** Allows a `HarnessEnvironment` to stop handling auto change detection status changes. */\nexport function stopHandlingAutoChangeDetectionStatus() {\n  autoChangeDetectionSubscription?.unsubscribe();\n  autoChangeDetectionSubscription = null;\n}\n\n/**\n * Batches together triggering of change detection over the duration of the given function.\n * @param fn The function to call with batched change detection.\n * @param triggerBeforeAndAfter Optionally trigger change detection once before and after the batch\n *   operation. If false, change detection will not be triggered.\n * @return The result of the given function.\n */\nasync function batchChangeDetection<T>(fn: () => Promise<T>, triggerBeforeAndAfter: boolean) {\n  // If change detection batching is already in progress, just run the function.\n  if (autoChangeDetectionSubject.getValue().isDisabled) {\n    return await fn();\n  }\n\n  // If nothing is handling change detection batching, install the default handler.\n  if (!autoChangeDetectionSubscription) {\n    handleAutoChangeDetectionStatus(defaultAutoChangeDetectionHandler);\n  }\n\n  if (triggerBeforeAndAfter) {\n    await new Promise(resolve =>\n      autoChangeDetectionSubject.next({\n        isDisabled: true,\n        onDetectChangesNow: resolve as () => void,\n      }),\n    );\n    // The function passed in may throw (e.g. if the user wants to make an expectation of an error\n    // being thrown. If this happens, we need to make sure we still re-enable change detection, so\n    // we wrap it in a `finally` block.\n    try {\n      return await fn();\n    } finally {\n      await new Promise(resolve =>\n        autoChangeDetectionSubject.next({\n          isDisabled: false,\n          onDetectChangesNow: resolve as () => void,\n        }),\n      );\n    }\n  } else {\n    autoChangeDetectionSubject.next({isDisabled: true});\n    // The function passed in may throw (e.g. if the user wants to make an expectation of an error\n    // being thrown. If this happens, we need to make sure we still re-enable change detection, so\n    // we wrap it in a `finally` block.\n    try {\n      return await fn();\n    } finally {\n      autoChangeDetectionSubject.next({isDisabled: false});\n    }\n  }\n}\n\n/**\n * Disables the harness system's auto change detection for the duration of the given function.\n * @param fn The function to disable auto change detection for.\n * @return The result of the given function.\n */\nexport async function manualChangeDetection<T>(fn: () => Promise<T>) {\n  return batchChangeDetection(fn, false);\n}\n\n/**\n * Resolves the given list of async values in parallel (i.e. via Promise.all) while batching change\n * detection over the entire operation such that change detection occurs exactly once before\n * resolving the values and once after.\n * @param values A getter for the async values to resolve in parallel with batched change detection.\n * @return The resolved values.\n */\nexport function parallel<T1, T2, T3, T4, T5>(\n  values: () => [\n    T1 | PromiseLike<T1>,\n    T2 | PromiseLike<T2>,\n    T3 | PromiseLike<T3>,\n    T4 | PromiseLike<T4>,\n    T5 | PromiseLike<T5>,\n  ],\n): Promise<[T1, T2, T3, T4, T5]>;\n\n/**\n * Resolves the given list of async values in parallel (i.e. via Promise.all) while batching change\n * detection over the entire operation such that change detection occurs exactly once before\n * resolving the values and once after.\n * @param values A getter for the async values to resolve in parallel with batched change detection.\n * @return The resolved values.\n */\nexport function parallel<T1, T2, T3, T4>(\n  values: () => [\n    T1 | PromiseLike<T1>,\n    T2 | PromiseLike<T2>,\n    T3 | PromiseLike<T3>,\n    T4 | PromiseLike<T4>,\n  ],\n): Promise<[T1, T2, T3, T4]>;\n\n/**\n * Resolves the given list of async values in parallel (i.e. via Promise.all) while batching change\n * detection over the entire operation such that change detection occurs exactly once before\n * resolving the values and once after.\n * @param values A getter for the async values to resolve in parallel with batched change detection.\n * @return The resolved values.\n */\nexport function parallel<T1, T2, T3>(\n  values: () => [T1 | PromiseLike<T1>, T2 | PromiseLike<T2>, T3 | PromiseLike<T3>],\n): Promise<[T1, T2, T3]>;\n\n/**\n * Resolves the given list of async values in parallel (i.e. via Promise.all) while batching change\n * detection over the entire operation such that change detection occurs exactly once before\n * resolving the values and once after.\n * @param values A getter for the async values to resolve in parallel with batched change detection.\n * @return The resolved values.\n */\nexport function parallel<T1, T2>(\n  values: () => [T1 | PromiseLike<T1>, T2 | PromiseLike<T2>],\n): Promise<[T1, T2]>;\n\n/**\n * Resolves the given list of async values in parallel (i.e. via Promise.all) while batching change\n * detection over the entire operation such that change detection occurs exactly once before\n * resolving the values and once after.\n * @param values A getter for the async values to resolve in parallel with batched change detection.\n * @return The resolved values.\n */\nexport function parallel<T>(values: () => (T | PromiseLike<T>)[]): Promise<T[]>;\n\n/**\n * Resolves the given list of async values in parallel (i.e. via Promise.all) while batching change\n * detection over the entire operation such that change detection occurs exactly once before\n * resolving the values and once after.\n * @param values A getter for the async values to resolve in parallel with batched change detection.\n * @return The resolved values.\n */\nexport async function parallel<T>(values: () => Iterable<T | PromiseLike<T>>): Promise<T[]> {\n  return batchChangeDetection(() => Promise.all(values()), true);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {parallel} from './change-detection';\nimport {TestElement} from './test-element';\n\n/** An async function that returns a promise when called. */\nexport type AsyncFactoryFn<T> = () => Promise<T>;\n\n/** An async function that takes an item and returns a boolean promise */\nexport type AsyncPredicate<T> = (item: T) => Promise<boolean>;\n\n/** An async function that takes an item and an option value and returns a boolean promise. */\nexport type AsyncOptionPredicate<T, O> = (item: T, option: O) => Promise<boolean>;\n\n/**\n * A query for a `ComponentHarness`, which is expressed as either a `ComponentHarnessConstructor` or\n * a `HarnessPredicate`.\n */\nexport type HarnessQuery<T extends ComponentHarness> =\n  | ComponentHarnessConstructor<T>\n  | HarnessPredicate<T>;\n\n/**\n * The result type obtained when searching using a particular list of queries. This type depends on\n * the particular items being queried.\n * - If one of the queries is for a `ComponentHarnessConstructor<C1>`, it means that the result\n *   might be a harness of type `C1`\n * - If one of the queries is for a `HarnessPredicate<C2>`, it means that the result might be a\n *   harness of type `C2`\n * - If one of the queries is for a `string`, it means that the result might be a `TestElement`.\n *\n * Since we don't know for sure which query will match, the result type if the union of the types\n * for all possible results.\n *\n * e.g.\n * The type:\n * `LocatorFnResult&lt;[\n *   ComponentHarnessConstructor&lt;MyHarness&gt;,\n *   HarnessPredicate&lt;MyOtherHarness&gt;,\n *   string\n * ]&gt;`\n * is equivalent to:\n * `MyHarness | MyOtherHarness | TestElement`.\n */\nexport type LocatorFnResult<T extends (HarnessQuery<any> | string)[]> = {\n  [I in keyof T]: T[I] extends new (...args: any[]) => infer C // Map `ComponentHarnessConstructor<C>` to `C`.\n    ? C\n    : // Map `HarnessPredicate<C>` to `C`.\n    T[I] extends {harnessType: new (...args: any[]) => infer C}\n    ? C\n    : // Map `string` to `TestElement`.\n    T[I] extends string\n    ? TestElement\n    : // Map everything else to `never` (should not happen due to the type constraint on `T`).\n      never;\n}[number];\n\n/**\n * Interface used to load ComponentHarness objects. This interface is used by test authors to\n * instantiate `ComponentHarness`es.\n */\nexport interface HarnessLoader {\n  /**\n   * Searches for an element with the given selector under the current instances's root element,\n   * and returns a `HarnessLoader` rooted at the matching element. If multiple elements match the\n   * selector, the first is used. If no elements match, an error is thrown.\n   * @param selector The selector for the root element of the new `HarnessLoader`\n   * @return A `HarnessLoader` rooted at the element matching the given selector.\n   * @throws If a matching element can't be found.\n   */\n  getChildLoader(selector: string): Promise<HarnessLoader>;\n\n  /**\n   * Searches for all elements with the given selector under the current instances's root element,\n   * and returns an array of `HarnessLoader`s, one for each matching element, rooted at that\n   * element.\n   * @param selector The selector for the root element of the new `HarnessLoader`\n   * @return A list of `HarnessLoader`s, one for each matching element, rooted at that element.\n   */\n  getAllChildLoaders(selector: string): Promise<HarnessLoader[]>;\n\n  /**\n   * Searches for an instance of the component corresponding to the given harness type under the\n   * `HarnessLoader`'s root element, and returns a `ComponentHarness` for that instance. If multiple\n   * matching components are found, a harness for the first one is returned. If no matching\n   * component is found, an error is thrown.\n   * @param query A query for a harness to create\n   * @return An instance of the given harness type\n   * @throws If a matching component instance can't be found.\n   */\n  getHarness<T extends ComponentHarness>(query: HarnessQuery<T>): Promise<T>;\n\n  /**\n   * Searches for an instance of the component corresponding to the given harness type under the\n   * `HarnessLoader`'s root element, and returns a `ComponentHarness` for that instance. If multiple\n   * matching components are found, a harness for the first one is returned. If no matching\n   * component is found, null is returned.\n   * @param query A query for a harness to create\n   * @return An instance of the given harness type (or null if not found).\n   */\n  getHarnessOrNull<T extends ComponentHarness>(query: HarnessQuery<T>): Promise<T | null>;\n\n  /**\n   * Searches for all instances of the component corresponding to the given harness type under the\n   * `HarnessLoader`'s root element, and returns a list `ComponentHarness` for each instance.\n   * @param query A query for a harness to create\n   * @return A list instances of the given harness type.\n   */\n  getAllHarnesses<T extends ComponentHarness>(query: HarnessQuery<T>): Promise<T[]>;\n\n  /**\n   * Searches for an instance of the component corresponding to the given harness type under the\n   * `HarnessLoader`'s root element, and returns a boolean indicating if any were found.\n   * @param query A query for a harness to create\n   * @return A boolean indicating if an instance was found.\n   */\n  hasHarness<T extends ComponentHarness>(query: HarnessQuery<T>): Promise<boolean>;\n}\n\n/**\n * Interface used to create asynchronous locator functions used find elements and component\n * harnesses. This interface is used by `ComponentHarness` authors to create locator functions for\n * their `ComponentHarness` subclass.\n */\nexport interface LocatorFactory {\n  /** Gets a locator factory rooted at the document root. */\n  documentRootLocatorFactory(): LocatorFactory;\n\n  /** The root element of this `LocatorFactory` as a `TestElement`. */\n  rootElement: TestElement;\n\n  /**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the root element of this `LocatorFactory`.\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` rejects. The type that the `Promise` resolves to is a union of all result types for\n   *   each query.\n   *\n   * e.g. Given the following DOM: `<div id=\"d1\" /><div id=\"d2\" />`, and assuming\n   * `DivHarness.hostSelector === 'div'`:\n   * - `await lf.locatorFor(DivHarness, 'div')()` gets a `DivHarness` instance for `#d1`\n   * - `await lf.locatorFor('div', DivHarness)()` gets a `TestElement` instance for `#d1`\n   * - `await lf.locatorFor('span')()` throws because the `Promise` rejects.\n   */\n  locatorFor<T extends (HarnessQuery<any> | string)[]>(\n    ...queries: T\n  ): AsyncFactoryFn<LocatorFnResult<T>>;\n\n  /**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the root element of this `LocatorFactory`.\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` is resolved with `null`. The type that the `Promise` resolves to is a union of all\n   *   result types for each query or null.\n   *\n   * e.g. Given the following DOM: `<div id=\"d1\" /><div id=\"d2\" />`, and assuming\n   * `DivHarness.hostSelector === 'div'`:\n   * - `await lf.locatorForOptional(DivHarness, 'div')()` gets a `DivHarness` instance for `#d1`\n   * - `await lf.locatorForOptional('div', DivHarness)()` gets a `TestElement` instance for `#d1`\n   * - `await lf.locatorForOptional('span')()` gets `null`.\n   */\n  locatorForOptional<T extends (HarnessQuery<any> | string)[]>(\n    ...queries: T\n  ): AsyncFactoryFn<LocatorFnResult<T> | null>;\n\n  /**\n   * Creates an asynchronous locator function that can be used to find `ComponentHarness` instances\n   * or elements under the root element of this `LocatorFactory`.\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for all\n   *   elements and harnesses matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If an element matches more than\n   *   one `ComponentHarness` class, the locator gets an instance of each for the same element. If\n   *   an element matches multiple `string` selectors, only one `TestElement` instance is returned\n   *   for that element. The type that the `Promise` resolves to is an array where each element is\n   *   the union of all result types for each query.\n   *\n   * e.g. Given the following DOM: `<div id=\"d1\" /><div id=\"d2\" />`, and assuming\n   * `DivHarness.hostSelector === 'div'` and `IdIsD1Harness.hostSelector === '#d1'`:\n   * - `await lf.locatorForAll(DivHarness, 'div')()` gets `[\n   *     DivHarness, // for #d1\n   *     TestElement, // for #d1\n   *     DivHarness, // for #d2\n   *     TestElement // for #d2\n   *   ]`\n   * - `await lf.locatorForAll('div', '#d1')()` gets `[\n   *     TestElement, // for #d1\n   *     TestElement // for #d2\n   *   ]`\n   * - `await lf.locatorForAll(DivHarness, IdIsD1Harness)()` gets `[\n   *     DivHarness, // for #d1\n   *     IdIsD1Harness, // for #d1\n   *     DivHarness // for #d2\n   *   ]`\n   * - `await lf.locatorForAll('span')()` gets `[]`.\n   */\n  locatorForAll<T extends (HarnessQuery<any> | string)[]>(\n    ...queries: T\n  ): AsyncFactoryFn<LocatorFnResult<T>[]>;\n\n  /** @return A `HarnessLoader` rooted at the root element of this `LocatorFactory`. */\n  rootHarnessLoader(): Promise<HarnessLoader>;\n\n  /**\n   * Gets a `HarnessLoader` instance for an element under the root of this `LocatorFactory`.\n   * @param selector The selector for the root element.\n   * @return A `HarnessLoader` rooted at the first element matching the given selector.\n   * @throws If no matching element is found for the given selector.\n   */\n  harnessLoaderFor(selector: string): Promise<HarnessLoader>;\n\n  /**\n   * Gets a `HarnessLoader` instance for an element under the root of this `LocatorFactory`\n   * @param selector The selector for the root element.\n   * @return A `HarnessLoader` rooted at the first element matching the given selector, or null if\n   *     no matching element is found.\n   */\n  harnessLoaderForOptional(selector: string): Promise<HarnessLoader | null>;\n\n  /**\n   * Gets a list of `HarnessLoader` instances, one for each matching element.\n   * @param selector The selector for the root element.\n   * @return A list of `HarnessLoader`, one rooted at each element matching the given selector.\n   */\n  harnessLoaderForAll(selector: string): Promise<HarnessLoader[]>;\n\n  /**\n   * Flushes change detection and async tasks captured in the Angular zone.\n   * In most cases it should not be necessary to call this manually. However, there may be some edge\n   * cases where it is needed to fully flush animation events.\n   */\n  forceStabilize(): Promise<void>;\n\n  /**\n   * Waits for all scheduled or running async tasks to complete. This allows harness\n   * authors to wait for async tasks outside of the Angular zone.\n   */\n  waitForTasksOutsideAngular(): Promise<void>;\n}\n\n/**\n * Base class for component harnesses that all component harness authors should extend. This base\n * component harness provides the basic ability to locate element and sub-component harness. It\n * should be inherited when defining user's own harness.\n */\nexport abstract class ComponentHarness {\n  constructor(protected readonly locatorFactory: LocatorFactory) {}\n\n  /** Gets a `Promise` for the `TestElement` representing the host element of the component. */\n  async host(): Promise<TestElement> {\n    return this.locatorFactory.rootElement;\n  }\n\n  /**\n   * Gets a `LocatorFactory` for the document root element. This factory can be used to create\n   * locators for elements that a component creates outside of its own root element. (e.g. by\n   * appending to document.body).\n   */\n  protected documentRootLocatorFactory(): LocatorFactory {\n    return this.locatorFactory.documentRootLocatorFactory();\n  }\n\n  /**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the host element of this `ComponentHarness`.\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` rejects. The type that the `Promise` resolves to is a union of all result types for\n   *   each query.\n   *\n   * e.g. Given the following DOM: `<div id=\"d1\" /><div id=\"d2\" />`, and assuming\n   * `DivHarness.hostSelector === 'div'`:\n   * - `await ch.locatorFor(DivHarness, 'div')()` gets a `DivHarness` instance for `#d1`\n   * - `await ch.locatorFor('div', DivHarness)()` gets a `TestElement` instance for `#d1`\n   * - `await ch.locatorFor('span')()` throws because the `Promise` rejects.\n   */\n  protected locatorFor<T extends (HarnessQuery<any> | string)[]>(\n    ...queries: T\n  ): AsyncFactoryFn<LocatorFnResult<T>> {\n    return this.locatorFactory.locatorFor(...queries);\n  }\n\n  /**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the host element of this `ComponentHarness`.\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` is resolved with `null`. The type that the `Promise` resolves to is a union of all\n   *   result types for each query or null.\n   *\n   * e.g. Given the following DOM: `<div id=\"d1\" /><div id=\"d2\" />`, and assuming\n   * `DivHarness.hostSelector === 'div'`:\n   * - `await ch.locatorForOptional(DivHarness, 'div')()` gets a `DivHarness` instance for `#d1`\n   * - `await ch.locatorForOptional('div', DivHarness)()` gets a `TestElement` instance for `#d1`\n   * - `await ch.locatorForOptional('span')()` gets `null`.\n   */\n  protected locatorForOptional<T extends (HarnessQuery<any> | string)[]>(\n    ...queries: T\n  ): AsyncFactoryFn<LocatorFnResult<T> | null> {\n    return this.locatorFactory.locatorForOptional(...queries);\n  }\n\n  /**\n   * Creates an asynchronous locator function that can be used to find `ComponentHarness` instances\n   * or elements under the host element of this `ComponentHarness`.\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for all\n   *   elements and harnesses matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If an element matches more than\n   *   one `ComponentHarness` class, the locator gets an instance of each for the same element. If\n   *   an element matches multiple `string` selectors, only one `TestElement` instance is returned\n   *   for that element. The type that the `Promise` resolves to is an array where each element is\n   *   the union of all result types for each query.\n   *\n   * e.g. Given the following DOM: `<div id=\"d1\" /><div id=\"d2\" />`, and assuming\n   * `DivHarness.hostSelector === 'div'` and `IdIsD1Harness.hostSelector === '#d1'`:\n   * - `await ch.locatorForAll(DivHarness, 'div')()` gets `[\n   *     DivHarness, // for #d1\n   *     TestElement, // for #d1\n   *     DivHarness, // for #d2\n   *     TestElement // for #d2\n   *   ]`\n   * - `await ch.locatorForAll('div', '#d1')()` gets `[\n   *     TestElement, // for #d1\n   *     TestElement // for #d2\n   *   ]`\n   * - `await ch.locatorForAll(DivHarness, IdIsD1Harness)()` gets `[\n   *     DivHarness, // for #d1\n   *     IdIsD1Harness, // for #d1\n   *     DivHarness // for #d2\n   *   ]`\n   * - `await ch.locatorForAll('span')()` gets `[]`.\n   */\n  protected locatorForAll<T extends (HarnessQuery<any> | string)[]>(\n    ...queries: T\n  ): AsyncFactoryFn<LocatorFnResult<T>[]> {\n    return this.locatorFactory.locatorForAll(...queries);\n  }\n\n  /**\n   * Flushes change detection and async tasks in the Angular zone.\n   * In most cases it should not be necessary to call this manually. However, there may be some edge\n   * cases where it is needed to fully flush animation events.\n   */\n  protected async forceStabilize() {\n    return this.locatorFactory.forceStabilize();\n  }\n\n  /**\n   * Waits for all scheduled or running async tasks to complete. This allows harness\n   * authors to wait for async tasks outside of the Angular zone.\n   */\n  protected async waitForTasksOutsideAngular() {\n    return this.locatorFactory.waitForTasksOutsideAngular();\n  }\n}\n\n/**\n * Base class for component harnesses that authors should extend if they anticipate that consumers\n * of the harness may want to access other harnesses within the `<ng-content>` of the component.\n */\nexport abstract class ContentContainerComponentHarness<S extends string = string>\n  extends ComponentHarness\n  implements HarnessLoader\n{\n  async getChildLoader(selector: S): Promise<HarnessLoader> {\n    return (await this.getRootHarnessLoader()).getChildLoader(selector);\n  }\n\n  async getAllChildLoaders(selector: S): Promise<HarnessLoader[]> {\n    return (await this.getRootHarnessLoader()).getAllChildLoaders(selector);\n  }\n\n  async getHarness<T extends ComponentHarness>(query: HarnessQuery<T>): Promise<T> {\n    return (await this.getRootHarnessLoader()).getHarness(query);\n  }\n\n  async getHarnessOrNull<T extends ComponentHarness>(query: HarnessQuery<T>): Promise<T | null> {\n    return (await this.getRootHarnessLoader()).getHarnessOrNull(query);\n  }\n\n  async getAllHarnesses<T extends ComponentHarness>(query: HarnessQuery<T>): Promise<T[]> {\n    return (await this.getRootHarnessLoader()).getAllHarnesses(query);\n  }\n\n  async hasHarness<T extends ComponentHarness>(query: HarnessQuery<T>): Promise<boolean> {\n    return (await this.getRootHarnessLoader()).hasHarness(query);\n  }\n\n  /**\n   * Gets the root harness loader from which to start\n   * searching for content contained by this harness.\n   */\n  protected async getRootHarnessLoader(): Promise<HarnessLoader> {\n    return this.locatorFactory.rootHarnessLoader();\n  }\n}\n\n/** Constructor for a ComponentHarness subclass. */\nexport interface ComponentHarnessConstructor<T extends ComponentHarness> {\n  new (locatorFactory: LocatorFactory): T;\n\n  /**\n   * `ComponentHarness` subclasses must specify a static `hostSelector` property that is used to\n   * find the host element for the corresponding component. This property should match the selector\n   * for the Angular component.\n   */\n  hostSelector: string;\n}\n\n/** A set of criteria that can be used to filter a list of `ComponentHarness` instances. */\nexport interface BaseHarnessFilters {\n  /** Only find instances whose host element matches the given selector. */\n  selector?: string;\n  /** Only find instances that are nested under an element with the given selector. */\n  ancestor?: string;\n}\n\n/**\n * A class used to associate a ComponentHarness class with predicates functions that can be used to\n * filter instances of the class.\n */\nexport class HarnessPredicate<T extends ComponentHarness> {\n  private _predicates: AsyncPredicate<T>[] = [];\n  private _descriptions: string[] = [];\n  private _ancestor: string;\n\n  constructor(public harnessType: ComponentHarnessConstructor<T>, options: BaseHarnessFilters) {\n    this._addBaseOptions(options);\n  }\n\n  /**\n   * Checks if the specified nullable string value matches the given pattern.\n   * @param value The nullable string value to check, or a Promise resolving to the\n   *   nullable string value.\n   * @param pattern The pattern the value is expected to match. If `pattern` is a string,\n   *   `value` is expected to match exactly. If `pattern` is a regex, a partial match is\n   *   allowed. If `pattern` is `null`, the value is expected to be `null`.\n   * @return Whether the value matches the pattern.\n   */\n  static async stringMatches(\n    value: string | null | Promise<string | null>,\n    pattern: string | RegExp | null,\n  ): Promise<boolean> {\n    value = await value;\n    if (pattern === null) {\n      return value === null;\n    } else if (value === null) {\n      return false;\n    }\n    return typeof pattern === 'string' ? value === pattern : pattern.test(value);\n  }\n\n  /**\n   * Adds a predicate function to be run against candidate harnesses.\n   * @param description A description of this predicate that may be used in error messages.\n   * @param predicate An async predicate function.\n   * @return this (for method chaining).\n   */\n  add(description: string, predicate: AsyncPredicate<T>) {\n    this._descriptions.push(description);\n    this._predicates.push(predicate);\n    return this;\n  }\n\n  /**\n   * Adds a predicate function that depends on an option value to be run against candidate\n   * harnesses. If the option value is undefined, the predicate will be ignored.\n   * @param name The name of the option (may be used in error messages).\n   * @param option The option value.\n   * @param predicate The predicate function to run if the option value is not undefined.\n   * @return this (for method chaining).\n   */\n  addOption<O>(name: string, option: O | undefined, predicate: AsyncOptionPredicate<T, O>) {\n    if (option !== undefined) {\n      this.add(`${name} = ${_valueAsString(option)}`, item => predicate(item, option));\n    }\n    return this;\n  }\n\n  /**\n   * Filters a list of harnesses on this predicate.\n   * @param harnesses The list of harnesses to filter.\n   * @return A list of harnesses that satisfy this predicate.\n   */\n  async filter(harnesses: T[]): Promise<T[]> {\n    if (harnesses.length === 0) {\n      return [];\n    }\n    const results = await parallel(() => harnesses.map(h => this.evaluate(h)));\n    return harnesses.filter((_, i) => results[i]);\n  }\n\n  /**\n   * Evaluates whether the given harness satisfies this predicate.\n   * @param harness The harness to check\n   * @return A promise that resolves to true if the harness satisfies this predicate,\n   *   and resolves to false otherwise.\n   */\n  async evaluate(harness: T): Promise<boolean> {\n    const results = await parallel(() => this._predicates.map(p => p(harness)));\n    return results.reduce((combined, current) => combined && current, true);\n  }\n\n  /** Gets a description of this predicate for use in error messages. */\n  getDescription() {\n    return this._descriptions.join(', ');\n  }\n\n  /** Gets the selector used to find candidate elements. */\n  getSelector() {\n    // We don't have to go through the extra trouble if there are no ancestors.\n    if (!this._ancestor) {\n      return (this.harnessType.hostSelector || '').trim();\n    }\n\n    const [ancestors, ancestorPlaceholders] = _splitAndEscapeSelector(this._ancestor);\n    const [selectors, selectorPlaceholders] = _splitAndEscapeSelector(\n      this.harnessType.hostSelector || '',\n    );\n    const result: string[] = [];\n\n    // We have to add the ancestor to each part of the host compound selector, otherwise we can get\n    // incorrect results. E.g. `.ancestor .a, .ancestor .b` vs `.ancestor .a, .b`.\n    ancestors.forEach(escapedAncestor => {\n      const ancestor = _restoreSelector(escapedAncestor, ancestorPlaceholders);\n      return selectors.forEach(escapedSelector =>\n        result.push(`${ancestor} ${_restoreSelector(escapedSelector, selectorPlaceholders)}`),\n      );\n    });\n\n    return result.join(', ');\n  }\n\n  /** Adds base options common to all harness types. */\n  private _addBaseOptions(options: BaseHarnessFilters) {\n    this._ancestor = options.ancestor || '';\n    if (this._ancestor) {\n      this._descriptions.push(`has ancestor matching selector \"${this._ancestor}\"`);\n    }\n    const selector = options.selector;\n    if (selector !== undefined) {\n      this.add(`host matches selector \"${selector}\"`, async item => {\n        return (await item.host()).matchesSelector(selector);\n      });\n    }\n  }\n}\n\n/** Represent a value as a string for the purpose of logging. */\nfunction _valueAsString(value: unknown) {\n  if (value === undefined) {\n    return 'undefined';\n  }\n  try {\n    // `JSON.stringify` doesn't handle RegExp properly, so we need a custom replacer.\n    // Use a character that is unlikely to appear in real strings to denote the start and end of\n    // the regex. This allows us to strip out the extra quotes around the value added by\n    // `JSON.stringify`. Also do custom escaping on `\"` characters to prevent `JSON.stringify`\n    // from escaping them as if they were part of a string.\n    const stringifiedValue = JSON.stringify(value, (_, v) =>\n      v instanceof RegExp\n        ? `◬MAT_RE_ESCAPE◬${v.toString().replace(/\"/g, '◬MAT_RE_ESCAPE◬')}◬MAT_RE_ESCAPE◬`\n        : v,\n    );\n    // Strip out the extra quotes around regexes and put back the manually escaped `\"` characters.\n    return stringifiedValue\n      .replace(/\"◬MAT_RE_ESCAPE◬|◬MAT_RE_ESCAPE◬\"/g, '')\n      .replace(/◬MAT_RE_ESCAPE◬/g, '\"');\n  } catch {\n    // `JSON.stringify` will throw if the object is cyclical,\n    // in this case the best we can do is report the value as `{...}`.\n    return '{...}';\n  }\n}\n\n/**\n * Splits up a compound selector into its parts and escapes any quoted content. The quoted content\n * has to be escaped, because it can contain commas which will throw throw us off when trying to\n * split it.\n * @param selector Selector to be split.\n * @returns The escaped string where any quoted content is replaced with a placeholder. E.g.\n * `[foo=\"bar\"]` turns into `[foo=__cdkPlaceholder-0__]`. Use `_restoreSelector` to restore\n * the placeholders.\n */\nfunction _splitAndEscapeSelector(selector: string): [parts: string[], placeholders: string[]] {\n  const placeholders: string[] = [];\n\n  // Note that the regex doesn't account for nested quotes so something like `\"ab'cd'e\"` will be\n  // considered as two blocks. It's a bit of an edge case, but if we find that it's a problem,\n  // we can make it a bit smarter using a loop. Use this for now since it's more readable and\n  // compact. More complete implementation:\n  // https://github.com/angular/angular/blob/bd34bc9e89f18a/packages/compiler/src/shadow_css.ts#L655\n  const result = selector.replace(/([\"'][^[\"']*[\"'])/g, (_, keep) => {\n    const replaceBy = `__cdkPlaceholder-${placeholders.length}__`;\n    placeholders.push(keep);\n    return replaceBy;\n  });\n\n  return [result.split(',').map(part => part.trim()), placeholders];\n}\n\n/** Restores a selector whose content was escaped in `_splitAndEscapeSelector`. */\nfunction _restoreSelector(selector: string, placeholders: string[]): string {\n  return selector.replace(/__cdkPlaceholder-(\\d+)__/g, (_, index) => placeholders[+index]);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {parallel} from './change-detection';\nimport {\n  AsyncFactoryFn,\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessLoader,\n  HarnessPredicate,\n  HarnessQuery,\n  LocatorFactory,\n  LocatorFnResult,\n} from './component-harness';\nimport {TestElement} from './test-element';\n\n/** Parsed form of the queries passed to the `locatorFor*` methods. */\ntype ParsedQueries<T extends ComponentHarness> = {\n  /** The full list of queries, in their original order. */\n  allQueries: (string | HarnessPredicate<T>)[];\n  /**\n   * A filtered view of `allQueries` containing only the queries that are looking for a\n   * `ComponentHarness`\n   */\n  harnessQueries: HarnessPredicate<T>[];\n  /**\n   * A filtered view of `allQueries` containing only the queries that are looking for a\n   * `TestElement`\n   */\n  elementQueries: string[];\n  /** The set of all `ComponentHarness` subclasses represented in the original query list. */\n  harnessTypes: Set<ComponentHarnessConstructor<T>>;\n};\n\n/**\n * Base harness environment class that can be extended to allow `ComponentHarness`es to be used in\n * different test environments (e.g. testbed, protractor, etc.). This class implements the\n * functionality of both a `HarnessLoader` and `LocatorFactory`. This class is generic on the raw\n * element type, `E`, used by the particular test environment.\n */\nexport abstract class HarnessEnvironment<E> implements HarnessLoader, LocatorFactory {\n  // Implemented as part of the `LocatorFactory` interface.\n  get rootElement(): TestElement {\n    this._rootElement = this._rootElement || this.createTestElement(this.rawRootElement);\n    return this._rootElement;\n  }\n  set rootElement(element: TestElement) {\n    this._rootElement = element;\n  }\n  private _rootElement: TestElement | undefined;\n\n  protected constructor(protected rawRootElement: E) {}\n\n  // Implemented as part of the `LocatorFactory` interface.\n  documentRootLocatorFactory(): LocatorFactory {\n    return this.createEnvironment(this.getDocumentRoot());\n  }\n\n  // Implemented as part of the `LocatorFactory` interface.\n  locatorFor<T extends (HarnessQuery<any> | string)[]>(\n    ...queries: T\n  ): AsyncFactoryFn<LocatorFnResult<T>> {\n    return () =>\n      _assertResultFound(\n        this._getAllHarnessesAndTestElements(queries),\n        _getDescriptionForLocatorForQueries(queries),\n      );\n  }\n\n  // Implemented as part of the `LocatorFactory` interface.\n  locatorForOptional<T extends (HarnessQuery<any> | string)[]>(\n    ...queries: T\n  ): AsyncFactoryFn<LocatorFnResult<T> | null> {\n    return async () => (await this._getAllHarnessesAndTestElements(queries))[0] || null;\n  }\n\n  // Implemented as part of the `LocatorFactory` interface.\n  locatorForAll<T extends (HarnessQuery<any> | string)[]>(\n    ...queries: T\n  ): AsyncFactoryFn<LocatorFnResult<T>[]> {\n    return () => this._getAllHarnessesAndTestElements(queries);\n  }\n\n  // Implemented as part of the `LocatorFactory` interface.\n  async rootHarnessLoader(): Promise<HarnessLoader> {\n    return this;\n  }\n\n  // Implemented as part of the `LocatorFactory` interface.\n  async harnessLoaderFor(selector: string): Promise<HarnessLoader> {\n    return this.createEnvironment(\n      await _assertResultFound(this.getAllRawElements(selector), [\n        _getDescriptionForHarnessLoaderQuery(selector),\n      ]),\n    );\n  }\n\n  // Implemented as part of the `LocatorFactory` interface.\n  async harnessLoaderForOptional(selector: string): Promise<HarnessLoader | null> {\n    const elements = await this.getAllRawElements(selector);\n    return elements[0] ? this.createEnvironment(elements[0]) : null;\n  }\n\n  // Implemented as part of the `LocatorFactory` interface.\n  async harnessLoaderForAll(selector: string): Promise<HarnessLoader[]> {\n    const elements = await this.getAllRawElements(selector);\n    return elements.map(element => this.createEnvironment(element));\n  }\n\n  // Implemented as part of the `HarnessLoader` interface.\n  getHarness<T extends ComponentHarness>(query: HarnessQuery<T>): Promise<T> {\n    return this.locatorFor(query)();\n  }\n\n  // Implemented as part of the `HarnessLoader` interface.\n  getHarnessOrNull<T extends ComponentHarness>(query: HarnessQuery<T>): Promise<T | null> {\n    return this.locatorForOptional(query)();\n  }\n\n  // Implemented as part of the `HarnessLoader` interface.\n  getAllHarnesses<T extends ComponentHarness>(query: HarnessQuery<T>): Promise<T[]> {\n    return this.locatorForAll(query)();\n  }\n\n  // Implemented as part of the `HarnessLoader` interface.\n  async hasHarness<T extends ComponentHarness>(query: HarnessQuery<T>): Promise<boolean> {\n    return (await this.locatorForOptional(query)()) !== null;\n  }\n\n  // Implemented as part of the `HarnessLoader` interface.\n  async getChildLoader(selector: string): Promise<HarnessLoader> {\n    return this.createEnvironment(\n      await _assertResultFound(this.getAllRawElements(selector), [\n        _getDescriptionForHarnessLoaderQuery(selector),\n      ]),\n    );\n  }\n\n  // Implemented as part of the `HarnessLoader` interface.\n  async getAllChildLoaders(selector: string): Promise<HarnessLoader[]> {\n    return (await this.getAllRawElements(selector)).map(e => this.createEnvironment(e));\n  }\n\n  /** Creates a `ComponentHarness` for the given harness type with the given raw host element. */\n  protected createComponentHarness<T extends ComponentHarness>(\n    harnessType: ComponentHarnessConstructor<T>,\n    element: E,\n  ): T {\n    return new harnessType(this.createEnvironment(element));\n  }\n\n  // Part of LocatorFactory interface, subclasses will implement.\n  abstract forceStabilize(): Promise<void>;\n\n  // Part of LocatorFactory interface, subclasses will implement.\n  abstract waitForTasksOutsideAngular(): Promise<void>;\n\n  /** Gets the root element for the document. */\n  protected abstract getDocumentRoot(): E;\n\n  /** Creates a `TestElement` from a raw element. */\n  protected abstract createTestElement(element: E): TestElement;\n\n  /** Creates a `HarnessLoader` rooted at the given raw element. */\n  protected abstract createEnvironment(element: E): HarnessEnvironment<E>;\n\n  /**\n   * Gets a list of all elements matching the given selector under this environment's root element.\n   */\n  protected abstract getAllRawElements(selector: string): Promise<E[]>;\n\n  /**\n   * Matches the given raw elements with the given list of element and harness queries to produce a\n   * list of matched harnesses and test elements.\n   */\n  private async _getAllHarnessesAndTestElements<T extends (HarnessQuery<any> | string)[]>(\n    queries: T,\n  ): Promise<LocatorFnResult<T>[]> {\n    if (!queries.length) {\n      throw Error('CDK Component harness query must contain at least one element.');\n    }\n\n    const {allQueries, harnessQueries, elementQueries, harnessTypes} = _parseQueries(queries);\n\n    // Combine all of the queries into one large comma-delimited selector and use it to get all raw\n    // elements matching any of the individual queries.\n    const rawElements = await this.getAllRawElements(\n      [...elementQueries, ...harnessQueries.map(predicate => predicate.getSelector())].join(','),\n    );\n\n    // If every query is searching for the same harness subclass, we know every result corresponds\n    // to an instance of that subclass. Likewise, if every query is for a `TestElement`, we know\n    // every result corresponds to a `TestElement`. Otherwise we need to verify which result was\n    // found by which selector so it can be matched to the appropriate instance.\n    const skipSelectorCheck =\n      (elementQueries.length === 0 && harnessTypes.size === 1) || harnessQueries.length === 0;\n\n    const perElementMatches = await parallel(() =>\n      rawElements.map(async rawElement => {\n        const testElement = this.createTestElement(rawElement);\n        const allResultsForElement = await parallel(\n          // For each query, get `null` if it doesn't match, or a `TestElement` or\n          // `ComponentHarness` as appropriate if it does match. This gives us everything that\n          // matches the current raw element, but it may contain duplicate entries (e.g.\n          // multiple `TestElement` or multiple `ComponentHarness` of the same type).\n          () =>\n            allQueries.map(query =>\n              this._getQueryResultForElement(query, rawElement, testElement, skipSelectorCheck),\n            ),\n        );\n        return _removeDuplicateQueryResults(allResultsForElement);\n      }),\n    );\n    return ([] as any).concat(...perElementMatches);\n  }\n\n  /**\n   * Check whether the given query matches the given element, if it does return the matched\n   * `TestElement` or `ComponentHarness`, if it does not, return null. In cases where the caller\n   * knows for sure that the query matches the element's selector, `skipSelectorCheck` can be used\n   * to skip verification and optimize performance.\n   */\n  private async _getQueryResultForElement<T extends ComponentHarness>(\n    query: string | HarnessPredicate<T>,\n    rawElement: E,\n    testElement: TestElement,\n    skipSelectorCheck: boolean = false,\n  ): Promise<T | TestElement | null> {\n    if (typeof query === 'string') {\n      return skipSelectorCheck || (await testElement.matchesSelector(query)) ? testElement : null;\n    }\n    if (skipSelectorCheck || (await testElement.matchesSelector(query.getSelector()))) {\n      const harness = this.createComponentHarness(query.harnessType, rawElement);\n      return (await query.evaluate(harness)) ? harness : null;\n    }\n    return null;\n  }\n}\n\n/**\n * Parses a list of queries in the format accepted by the `locatorFor*` methods into an easier to\n * work with format.\n */\nfunction _parseQueries<T extends (HarnessQuery<any> | string)[]>(\n  queries: T,\n): ParsedQueries<LocatorFnResult<T> & ComponentHarness> {\n  const allQueries = [];\n  const harnessQueries = [];\n  const elementQueries = [];\n  const harnessTypes = new Set<\n    ComponentHarnessConstructor<LocatorFnResult<T> & ComponentHarness>\n  >();\n\n  for (const query of queries) {\n    if (typeof query === 'string') {\n      allQueries.push(query);\n      elementQueries.push(query);\n    } else {\n      const predicate = query instanceof HarnessPredicate ? query : new HarnessPredicate(query, {});\n      allQueries.push(predicate);\n      harnessQueries.push(predicate);\n      harnessTypes.add(predicate.harnessType);\n    }\n  }\n\n  return {allQueries, harnessQueries, elementQueries, harnessTypes};\n}\n\n/**\n * Removes duplicate query results for a particular element. (e.g. multiple `TestElement`\n * instances or multiple instances of the same `ComponentHarness` class.\n */\nasync function _removeDuplicateQueryResults<T extends (ComponentHarness | TestElement | null)[]>(\n  results: T,\n): Promise<T> {\n  let testElementMatched = false;\n  let matchedHarnessTypes = new Set();\n  const dedupedMatches = [];\n  for (const result of results) {\n    if (!result) {\n      continue;\n    }\n    if (result instanceof ComponentHarness) {\n      if (!matchedHarnessTypes.has(result.constructor)) {\n        matchedHarnessTypes.add(result.constructor);\n        dedupedMatches.push(result);\n      }\n    } else if (!testElementMatched) {\n      testElementMatched = true;\n      dedupedMatches.push(result);\n    }\n  }\n  return dedupedMatches as T;\n}\n\n/** Verifies that there is at least one result in an array. */\nasync function _assertResultFound<T>(\n  results: Promise<T[]>,\n  queryDescriptions: string[],\n): Promise<T> {\n  const result = (await results)[0];\n  if (result == undefined) {\n    throw Error(\n      `Failed to find element matching one of the following queries:\\n` +\n        queryDescriptions.map(desc => `(${desc})`).join(',\\n'),\n    );\n  }\n  return result;\n}\n\n/** Gets a list of description strings from a list of queries. */\nfunction _getDescriptionForLocatorForQueries(queries: (string | HarnessQuery<any>)[]) {\n  return queries.map(query =>\n    typeof query === 'string'\n      ? _getDescriptionForTestElementQuery(query)\n      : _getDescriptionForComponentHarnessQuery(query),\n  );\n}\n\n/** Gets a description string for a `ComponentHarness` query. */\nfunction _getDescriptionForComponentHarnessQuery(query: HarnessQuery<any>) {\n  const harnessPredicate =\n    query instanceof HarnessPredicate ? query : new HarnessPredicate(query, {});\n  const {name, hostSelector} = harnessPredicate.harnessType;\n  const description = `${name} with host element matching selector: \"${hostSelector}\"`;\n  const constraints = harnessPredicate.getDescription();\n  return (\n    description +\n    (constraints ? ` satisfying the constraints: ${harnessPredicate.getDescription()}` : '')\n  );\n}\n\n/** Gets a description string for a `TestElement` query. */\nfunction _getDescriptionForTestElementQuery(selector: string) {\n  return `TestElement for element matching selector: \"${selector}\"`;\n}\n\n/** Gets a description string for a `HarnessLoader` query. */\nfunction _getDescriptionForHarnessLoaderQuery(selector: string) {\n  return `HarnessLoader for element matching selector: \"${selector}\"`;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ElementDimensions} from './element-dimensions';\n\n/** Modifier keys that may be held while typing. */\nexport interface ModifierKeys {\n  control?: boolean;\n  alt?: boolean;\n  shift?: boolean;\n  meta?: boolean;\n}\n\n/** Data that can be attached to a custom event dispatched from a `TestElement`. */\nexport type EventData =\n  | string\n  | number\n  | boolean\n  | Function\n  | undefined\n  | null\n  | EventData[]\n  | {[key: string]: EventData};\n\n/** An enum of non-text keys that can be used with the `sendKeys` method. */\n// NOTE: This is a separate enum from `@angular/cdk/keycodes` because we don't necessarily want to\n// support every possible keyCode. We also can't rely on <PERSON>tractor's `Key` because we don't want a\n// dependency on any particular testing framework here. Instead we'll just maintain this supported\n// list of keys and let individual concrete `HarnessEnvironment` classes map them to whatever key\n// representation is used in its respective testing framework.\n// tslint:disable-next-line:prefer-const-enum Seems like this causes some issues with System.js\nexport enum TestKey {\n  BACKSPACE,\n  TAB,\n  ENTER,\n  SHIFT,\n  CONTROL,\n  ALT,\n  ESCAPE,\n  PAGE_UP,\n  PAGE_DOWN,\n  END,\n  HOME,\n  LEFT_ARROW,\n  UP_ARROW,\n  RIGHT_ARROW,\n  DOWN_ARROW,\n  INSERT,\n  DELETE,\n  F1,\n  F2,\n  F3,\n  F4,\n  F5,\n  F6,\n  F7,\n  F8,\n  F9,\n  F10,\n  F11,\n  F12,\n  META,\n  COMMA, // Commas are a common separator key.\n}\n\n/**\n * This acts as a common interface for DOM elements across both unit and e2e tests. It is the\n * interface through which the ComponentHarness interacts with the component's DOM.\n */\nexport interface TestElement {\n  /** Blur the element. */\n  blur(): Promise<void>;\n\n  /** Clear the element's input (for input and textarea elements only). */\n  clear(): Promise<void>;\n\n  /**\n   * Click the element at the default location for the current environment. If you need to guarantee\n   * the element is clicked at a specific location, consider using `click('center')` or\n   * `click(x, y)` instead.\n   */\n  click(modifiers?: ModifierKeys): Promise<void>;\n\n  /** Click the element at the element's center. */\n  click(location: 'center', modifiers?: ModifierKeys): Promise<void>;\n\n  /**\n   * Click the element at the specified coordinates relative to the top-left of the element.\n   * @param relativeX Coordinate within the element, along the X-axis at which to click.\n   * @param relativeY Coordinate within the element, along the Y-axis at which to click.\n   * @param modifiers Modifier keys held while clicking\n   */\n  click(relativeX: number, relativeY: number, modifiers?: ModifierKeys): Promise<void>;\n\n  /**\n   * Right clicks on the element at the specified coordinates relative to the top-left of it.\n   * @param relativeX Coordinate within the element, along the X-axis at which to click.\n   * @param relativeY Coordinate within the element, along the Y-axis at which to click.\n   * @param modifiers Modifier keys held while clicking\n   */\n  rightClick(relativeX: number, relativeY: number, modifiers?: ModifierKeys): Promise<void>;\n\n  /** Focus the element. */\n  focus(): Promise<void>;\n\n  /** Get the computed value of the given CSS property for the element. */\n  getCssValue(property: string): Promise<string>;\n\n  /** Hovers the mouse over the element. */\n  hover(): Promise<void>;\n\n  /** Moves the mouse away from the element. */\n  mouseAway(): Promise<void>;\n\n  /**\n   * Sends the given string to the input as a series of key presses. Also fires input events\n   * and attempts to add the string to the Element's value. Note that some environments cannot\n   * reproduce native browser behavior for keyboard shortcuts such as Tab, Ctrl + A, etc.\n   * @throws An error if no keys have been specified.\n   */\n  sendKeys(...keys: (string | TestKey)[]): Promise<void>;\n\n  /**\n   * Sends the given string to the input as a series of key presses. Also fires input\n   * events and attempts to add the string to the Element's value.\n   * @throws An error if no keys have been specified.\n   */\n  sendKeys(modifiers: ModifierKeys, ...keys: (string | TestKey)[]): Promise<void>;\n\n  /**\n   * Gets the text from the element.\n   * @param options Options that affect what text is included.\n   */\n  text(options?: TextOptions): Promise<string>;\n\n  /**\n   * Sets the value of a `contenteditable` element.\n   * @param value Value to be set on the element.\n   * @breaking-change 16.0.0 Will become a required method.\n   */\n  setContenteditableValue?(value: string): Promise<void>;\n\n  /** Gets the value for the given attribute from the element. */\n  getAttribute(name: string): Promise<string | null>;\n\n  /** Checks whether the element has the given class. */\n  hasClass(name: string): Promise<boolean>;\n\n  /** Gets the dimensions of the element. */\n  getDimensions(): Promise<ElementDimensions>;\n\n  /** Gets the value of a property of an element. */\n  getProperty<T = any>(name: string): Promise<T>;\n\n  /** Checks whether this element matches the given selector. */\n  matchesSelector(selector: string): Promise<boolean>;\n\n  /** Checks whether the element is focused. */\n  isFocused(): Promise<boolean>;\n\n  /** Sets the value of a property of an input. */\n  setInputValue(value: string): Promise<void>;\n\n  // Note that ideally here we'd be selecting options based on their value, rather than their\n  // index, but we're limited by `@angular/forms` which will modify the option value in some cases.\n  // Since the value will be truncated, we can't rely on it to do the lookup in the DOM. See:\n  // https://github.com/angular/angular/blob/main/packages/forms/src/directives/select_control_value_accessor.ts#L19\n  /** Selects the options at the specified indexes inside of a native `select` element. */\n  selectOptions(...optionIndexes: number[]): Promise<void>;\n\n  /**\n   * Dispatches an event with a particular name.\n   * @param name Name of the event to be dispatched.\n   */\n  dispatchEvent(name: string, data?: Record<string, EventData>): Promise<void>;\n}\n\nexport interface TextOptions {\n  /** Optional selector for elements to exclude. */\n  exclude?: string;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Returns an error which reports that no keys have been specified.\n * @docs-private\n */\nexport function getNoKeysSpecifiedError() {\n  return Error('No keys have been specified.');\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Gets text of element excluding certain selectors within the element.\n * @param element Element to get text from,\n * @param excludeSelector Selector identifying which elements to exclude,\n */\nexport function _getTextWithExcludedElements(element: Element, excludeSelector: string) {\n  const clone = element.cloneNode(true) as Element;\n  const exclusions = clone.querySelectorAll(excludeSelector);\n  for (let i = 0; i < exclusions.length; i++) {\n    exclusions[i].remove();\n  }\n  return (clone.textContent || '').trim();\n}\n"], "names": [], "mappings": ";;AAsBA;AACA,MAAM,0BAA0B,GAAG,IAAI,eAAe,CAA4B;AAChF,IAAA,UAAU,EAAE,KAAK;AAClB,CAAA,CAAC,CAAC;AAEH;AACA,IAAI,+BAAoD,CAAC;AAEzD;;;;AAIG;AACH,SAAS,iCAAiC,CAAC,MAAiC,EAAA;AAC1E,IAAA,MAAM,CAAC,kBAAkB,IAAI,CAAC;AAChC,CAAC;AAED;;;;AAIG;AACG,SAAU,+BAA+B,CAC7C,OAAoD,EAAA;AAEpD,IAAA,qCAAqC,EAAE,CAAC;AACxC,IAAA,+BAA+B,GAAG,0BAA0B,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AAClF,CAAC;AAED;SACgB,qCAAqC,GAAA;IACnD,+BAA+B,EAAE,WAAW,EAAE,CAAC;IAC/C,+BAA+B,GAAG,IAAI,CAAC;AACzC,CAAC;AAED;;;;;;AAMG;AACH,eAAe,oBAAoB,CAAI,EAAoB,EAAE,qBAA8B,EAAA;;AAEzF,IAAA,IAAI,0BAA0B,CAAC,QAAQ,EAAE,CAAC,UAAU,EAAE;QACpD,OAAO,MAAM,EAAE,EAAE,CAAC;KACnB;;IAGD,IAAI,CAAC,+BAA+B,EAAE;QACpC,+BAA+B,CAAC,iCAAiC,CAAC,CAAC;KACpE;IAED,IAAI,qBAAqB,EAAE;QACzB,MAAM,IAAI,OAAO,CAAC,OAAO,IACvB,0BAA0B,CAAC,IAAI,CAAC;AAC9B,YAAA,UAAU,EAAE,IAAI;AAChB,YAAA,kBAAkB,EAAE,OAAqB;AAC1C,SAAA,CAAC,CACH,CAAC;;;;AAIF,QAAA,IAAI;YACF,OAAO,MAAM,EAAE,EAAE,CAAC;SACnB;gBAAS;YACR,MAAM,IAAI,OAAO,CAAC,OAAO,IACvB,0BAA0B,CAAC,IAAI,CAAC;AAC9B,gBAAA,UAAU,EAAE,KAAK;AACjB,gBAAA,kBAAkB,EAAE,OAAqB;AAC1C,aAAA,CAAC,CACH,CAAC;SACH;KACF;SAAM;QACL,0BAA0B,CAAC,IAAI,CAAC,EAAC,UAAU,EAAE,IAAI,EAAC,CAAC,CAAC;;;;AAIpD,QAAA,IAAI;YACF,OAAO,MAAM,EAAE,EAAE,CAAC;SACnB;gBAAS;YACR,0BAA0B,CAAC,IAAI,CAAC,EAAC,UAAU,EAAE,KAAK,EAAC,CAAC,CAAC;SACtD;KACF;AACH,CAAC;AAED;;;;AAIG;AACI,eAAe,qBAAqB,CAAI,EAAoB,EAAA;AACjE,IAAA,OAAO,oBAAoB,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;AACzC,CAAC;AAkED;;;;;;AAMG;AACI,eAAe,QAAQ,CAAI,MAA0C,EAAA;AAC1E,IAAA,OAAO,oBAAoB,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;AACjE;;AC6EA;;;;AAIG;MACmB,gBAAgB,CAAA;AACpC,IAAA,WAAA,CAA+B,cAA8B,EAAA;QAA9B,IAAc,CAAA,cAAA,GAAd,cAAc,CAAgB;KAAI;;AAGjE,IAAA,MAAM,IAAI,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;KACxC;AAED;;;;AAIG;IACO,0BAA0B,GAAA;AAClC,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,0BAA0B,EAAE,CAAC;KACzD;AAED;;;;;;;;;;;;;;;;;;;;AAoBG;IACO,UAAU,CAClB,GAAG,OAAU,EAAA;QAEb,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC,CAAC;KACnD;AAED;;;;;;;;;;;;;;;;;;;;AAoBG;IACO,kBAAkB,CAC1B,GAAG,OAAU,EAAA;QAEb,OAAO,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,GAAG,OAAO,CAAC,CAAC;KAC3D;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCG;IACO,aAAa,CACrB,GAAG,OAAU,EAAA;QAEb,OAAO,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,GAAG,OAAO,CAAC,CAAC;KACtD;AAED;;;;AAIG;AACO,IAAA,MAAM,cAAc,GAAA;AAC5B,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;KAC7C;AAED;;;AAGG;AACO,IAAA,MAAM,0BAA0B,GAAA;AACxC,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,0BAA0B,EAAE,CAAC;KACzD;AACF,CAAA;AAED;;;AAGG;AACG,MAAgB,gCACpB,SAAQ,gBAAgB,CAAA;IAGxB,MAAM,cAAc,CAAC,QAAW,EAAA;AAC9B,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,oBAAoB,EAAE,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC;KACrE;IAED,MAAM,kBAAkB,CAAC,QAAW,EAAA;AAClC,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,oBAAoB,EAAE,EAAE,kBAAkB,CAAC,QAAQ,CAAC,CAAC;KACzE;IAED,MAAM,UAAU,CAA6B,KAAsB,EAAA;AACjE,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,oBAAoB,EAAE,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC;KAC9D;IAED,MAAM,gBAAgB,CAA6B,KAAsB,EAAA;AACvE,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,oBAAoB,EAAE,EAAE,gBAAgB,CAAC,KAAK,CAAC,CAAC;KACpE;IAED,MAAM,eAAe,CAA6B,KAAsB,EAAA;AACtE,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,oBAAoB,EAAE,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC;KACnE;IAED,MAAM,UAAU,CAA6B,KAAsB,EAAA;AACjE,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,oBAAoB,EAAE,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC;KAC9D;AAED;;;AAGG;AACO,IAAA,MAAM,oBAAoB,GAAA;AAClC,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC;KAChD;AACF,CAAA;AAsBD;;;AAGG;MACU,gBAAgB,CAAA;IAK3B,WAAmB,CAAA,WAA2C,EAAE,OAA2B,EAAA;QAAxE,IAAW,CAAA,WAAA,GAAX,WAAW,CAAgC;QAJtD,IAAW,CAAA,WAAA,GAAwB,EAAE,CAAC;QACtC,IAAa,CAAA,aAAA,GAAa,EAAE,CAAC;AAInC,QAAA,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;KAC/B;AAED;;;;;;;;AAQG;AACH,IAAA,aAAa,aAAa,CACxB,KAA6C,EAC7C,OAA+B,EAAA;QAE/B,KAAK,GAAG,MAAM,KAAK,CAAC;AACpB,QAAA,IAAI,OAAO,KAAK,IAAI,EAAE;YACpB,OAAO,KAAK,KAAK,IAAI,CAAC;SACvB;AAAM,aAAA,IAAI,KAAK,KAAK,IAAI,EAAE;AACzB,YAAA,OAAO,KAAK,CAAC;SACd;AACD,QAAA,OAAO,OAAO,OAAO,KAAK,QAAQ,GAAG,KAAK,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC9E;AAED;;;;;AAKG;IACH,GAAG,CAAC,WAAmB,EAAE,SAA4B,EAAA;AACnD,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AACrC,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACjC,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;;;;AAOG;AACH,IAAA,SAAS,CAAI,IAAY,EAAE,MAAqB,EAAE,SAAqC,EAAA;AACrF,QAAA,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,IAAI,CAAC,GAAG,CAAC,CAAA,EAAG,IAAI,CAAM,GAAA,EAAA,cAAc,CAAC,MAAM,CAAC,CAAA,CAAE,EAAE,IAAI,IAAI,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;SAClF;AACD,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;AAIG;IACH,MAAM,MAAM,CAAC,SAAc,EAAA;AACzB,QAAA,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;AAC1B,YAAA,OAAO,EAAE,CAAC;SACX;QACD,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,MAAM,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3E,QAAA,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;KAC/C;AAED;;;;;AAKG;IACH,MAAM,QAAQ,CAAC,OAAU,EAAA;QACvB,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAC5E,QAAA,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,OAAO,KAAK,QAAQ,IAAI,OAAO,EAAE,IAAI,CAAC,CAAC;KACzE;;IAGD,cAAc,GAAA;QACZ,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACtC;;IAGD,WAAW,GAAA;;AAET,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACnB,YAAA,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC;SACrD;AAED,QAAA,MAAM,CAAC,SAAS,EAAE,oBAAoB,CAAC,GAAG,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAClF,QAAA,MAAM,CAAC,SAAS,EAAE,oBAAoB,CAAC,GAAG,uBAAuB,CAC/D,IAAI,CAAC,WAAW,CAAC,YAAY,IAAI,EAAE,CACpC,CAAC;QACF,MAAM,MAAM,GAAa,EAAE,CAAC;;;AAI5B,QAAA,SAAS,CAAC,OAAO,CAAC,eAAe,IAAG;YAClC,MAAM,QAAQ,GAAG,gBAAgB,CAAC,eAAe,EAAE,oBAAoB,CAAC,CAAC;YACzE,OAAO,SAAS,CAAC,OAAO,CAAC,eAAe,IACtC,MAAM,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAA,CAAA,EAAI,gBAAgB,CAAC,eAAe,EAAE,oBAAoB,CAAC,CAAA,CAAE,CAAC,CACtF,CAAC;AACJ,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC1B;;AAGO,IAAA,eAAe,CAAC,OAA2B,EAAA;QACjD,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC;AACxC,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAmC,gCAAA,EAAA,IAAI,CAAC,SAAS,CAAG,CAAA,CAAA,CAAC,CAAC;SAC/E;AACD,QAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;AAClC,QAAA,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,IAAI,CAAC,GAAG,CAAC,CAA0B,uBAAA,EAAA,QAAQ,CAAG,CAAA,CAAA,EAAE,OAAM,IAAI,KAAG;AAC3D,gBAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC;AACvD,aAAC,CAAC,CAAC;SACJ;KACF;AACF,CAAA;AAED;AACA,SAAS,cAAc,CAAC,KAAc,EAAA;AACpC,IAAA,IAAI,KAAK,KAAK,SAAS,EAAE;AACvB,QAAA,OAAO,WAAW,CAAC;KACpB;AACD,IAAA,IAAI;;;;;;AAMF,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,KAClD,CAAC,YAAY,MAAM;AACjB,cAAE,CAAA,eAAA,EAAkB,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAiB,eAAA,CAAA;cAChF,CAAC,CACN,CAAC;;AAEF,QAAA,OAAO,gBAAgB;AACpB,aAAA,OAAO,CAAC,oCAAoC,EAAE,EAAE,CAAC;AACjD,aAAA,OAAO,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAC;KACrC;AAAC,IAAA,MAAM;;;AAGN,QAAA,OAAO,OAAO,CAAC;KAChB;AACH,CAAC;AAED;;;;;;;;AAQG;AACH,SAAS,uBAAuB,CAAC,QAAgB,EAAA;IAC/C,MAAM,YAAY,GAAa,EAAE,CAAC;;;;;;AAOlC,IAAA,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC,CAAC,EAAE,IAAI,KAAI;AAChE,QAAA,MAAM,SAAS,GAAG,CAAA,iBAAA,EAAoB,YAAY,CAAC,MAAM,IAAI,CAAC;AAC9D,QAAA,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACxB,QAAA,OAAO,SAAS,CAAC;AACnB,KAAC,CAAC,CAAC;IAEH,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;AACpE,CAAC;AAED;AACA,SAAS,gBAAgB,CAAC,QAAgB,EAAE,YAAsB,EAAA;AAChE,IAAA,OAAO,QAAQ,CAAC,OAAO,CAAC,2BAA2B,EAAE,CAAC,CAAC,EAAE,KAAK,KAAK,YAAY,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AAC3F;;ACpmBA;;;;;AAKG;MACmB,kBAAkB,CAAA;;AAEtC,IAAA,IAAI,WAAW,GAAA;AACb,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACrF,OAAO,IAAI,CAAC,YAAY,CAAC;KAC1B;IACD,IAAI,WAAW,CAAC,OAAoB,EAAA;AAClC,QAAA,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC;KAC7B;AAGD,IAAA,WAAA,CAAgC,cAAiB,EAAA;QAAjB,IAAc,CAAA,cAAA,GAAd,cAAc,CAAG;KAAI;;IAGrD,0BAA0B,GAAA;QACxB,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;KACvD;;IAGD,UAAU,CACR,GAAG,OAAU,EAAA;AAEb,QAAA,OAAO,MACL,kBAAkB,CAChB,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,EAC7C,mCAAmC,CAAC,OAAO,CAAC,CAC7C,CAAC;KACL;;IAGD,kBAAkB,CAChB,GAAG,OAAU,EAAA;AAEb,QAAA,OAAO,YAAY,CAAC,MAAM,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC;KACrF;;IAGD,aAAa,CACX,GAAG,OAAU,EAAA;QAEb,OAAO,MAAM,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,CAAC;KAC5D;;AAGD,IAAA,MAAM,iBAAiB,GAAA;AACrB,QAAA,OAAO,IAAI,CAAC;KACb;;IAGD,MAAM,gBAAgB,CAAC,QAAgB,EAAA;AACrC,QAAA,OAAO,IAAI,CAAC,iBAAiB,CAC3B,MAAM,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EAAE;YACzD,oCAAoC,CAAC,QAAQ,CAAC;AAC/C,SAAA,CAAC,CACH,CAAC;KACH;;IAGD,MAAM,wBAAwB,CAAC,QAAgB,EAAA;QAC7C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QACxD,OAAO,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;KACjE;;IAGD,MAAM,mBAAmB,CAAC,QAAgB,EAAA;QACxC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;AACxD,QAAA,OAAO,QAAQ,CAAC,GAAG,CAAC,OAAO,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC;KACjE;;AAGD,IAAA,UAAU,CAA6B,KAAsB,EAAA;AAC3D,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;KACjC;;AAGD,IAAA,gBAAgB,CAA6B,KAAsB,EAAA;AACjE,QAAA,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;KACzC;;AAGD,IAAA,eAAe,CAA6B,KAAsB,EAAA;AAChE,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;KACpC;;IAGD,MAAM,UAAU,CAA6B,KAAsB,EAAA;AACjE,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,MAAM,IAAI,CAAC;KAC1D;;IAGD,MAAM,cAAc,CAAC,QAAgB,EAAA;AACnC,QAAA,OAAO,IAAI,CAAC,iBAAiB,CAC3B,MAAM,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EAAE;YACzD,oCAAoC,CAAC,QAAQ,CAAC;AAC/C,SAAA,CAAC,CACH,CAAC;KACH;;IAGD,MAAM,kBAAkB,CAAC,QAAgB,EAAA;QACvC,OAAO,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;KACrF;;IAGS,sBAAsB,CAC9B,WAA2C,EAC3C,OAAU,EAAA;QAEV,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC;KACzD;AAsBD;;;AAGG;IACK,MAAM,+BAA+B,CAC3C,OAAU,EAAA;AAEV,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;AACnB,YAAA,MAAM,KAAK,CAAC,gEAAgE,CAAC,CAAC;SAC/E;AAED,QAAA,MAAM,EAAC,UAAU,EAAE,cAAc,EAAE,cAAc,EAAE,YAAY,EAAC,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;;;AAI1F,QAAA,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAC9C,CAAC,GAAG,cAAc,EAAE,GAAG,cAAc,CAAC,GAAG,CAAC,SAAS,IAAI,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAC3F,CAAC;;;;;QAMF,MAAM,iBAAiB,GACrB,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,IAAI,YAAY,CAAC,IAAI,KAAK,CAAC,KAAK,cAAc,CAAC,MAAM,KAAK,CAAC,CAAC;AAE1F,QAAA,MAAM,iBAAiB,GAAG,MAAM,QAAQ,CAAC,MACvC,WAAW,CAAC,GAAG,CAAC,OAAM,UAAU,KAAG;YACjC,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACvD,MAAM,oBAAoB,GAAG,MAAM,QAAQ;;;;;YAKzC,MACE,UAAU,CAAC,GAAG,CAAC,KAAK,IAClB,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAClF,CACJ,CAAC;AACF,YAAA,OAAO,4BAA4B,CAAC,oBAAoB,CAAC,CAAC;SAC3D,CAAC,CACH,CAAC;AACF,QAAA,OAAQ,EAAU,CAAC,MAAM,CAAC,GAAG,iBAAiB,CAAC,CAAC;KACjD;AAED;;;;;AAKG;IACK,MAAM,yBAAyB,CACrC,KAAmC,EACnC,UAAa,EACb,WAAwB,EACxB,iBAAA,GAA6B,KAAK,EAAA;AAElC,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,YAAA,OAAO,iBAAiB,KAAK,MAAM,WAAW,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,GAAG,WAAW,GAAG,IAAI,CAAC;SAC7F;AACD,QAAA,IAAI,iBAAiB,KAAK,MAAM,WAAW,CAAC,eAAe,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE;AACjF,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;AAC3E,YAAA,OAAO,CAAC,MAAM,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,GAAG,IAAI,CAAC;SACzD;AACD,QAAA,OAAO,IAAI,CAAC;KACb;AACF,CAAA;AAED;;;AAGG;AACH,SAAS,aAAa,CACpB,OAAU,EAAA;IAEV,MAAM,UAAU,GAAG,EAAE,CAAC;IACtB,MAAM,cAAc,GAAG,EAAE,CAAC;IAC1B,MAAM,cAAc,GAAG,EAAE,CAAC;AAC1B,IAAA,MAAM,YAAY,GAAG,IAAI,GAAG,EAEzB,CAAC;AAEJ,IAAA,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE;AAC3B,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,YAAA,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACvB,YAAA,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC5B;aAAM;AACL,YAAA,MAAM,SAAS,GAAG,KAAK,YAAY,gBAAgB,GAAG,KAAK,GAAG,IAAI,gBAAgB,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AAC9F,YAAA,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC3B,YAAA,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC/B,YAAA,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;SACzC;KACF;IAED,OAAO,EAAC,UAAU,EAAE,cAAc,EAAE,cAAc,EAAE,YAAY,EAAC,CAAC;AACpE,CAAC;AAED;;;AAGG;AACH,eAAe,4BAA4B,CACzC,OAAU,EAAA;IAEV,IAAI,kBAAkB,GAAG,KAAK,CAAC;AAC/B,IAAA,IAAI,mBAAmB,GAAG,IAAI,GAAG,EAAE,CAAC;IACpC,MAAM,cAAc,GAAG,EAAE,CAAC;AAC1B,IAAA,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;QAC5B,IAAI,CAAC,MAAM,EAAE;YACX,SAAS;SACV;AACD,QAAA,IAAI,MAAM,YAAY,gBAAgB,EAAE;YACtC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE;AAChD,gBAAA,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;AAC5C,gBAAA,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aAC7B;SACF;aAAM,IAAI,CAAC,kBAAkB,EAAE;YAC9B,kBAAkB,GAAG,IAAI,CAAC;AAC1B,YAAA,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC7B;KACF;AACD,IAAA,OAAO,cAAmB,CAAC;AAC7B,CAAC;AAED;AACA,eAAe,kBAAkB,CAC/B,OAAqB,EACrB,iBAA2B,EAAA;IAE3B,MAAM,MAAM,GAAG,CAAC,MAAM,OAAO,EAAE,CAAC,CAAC,CAAC;AAClC,IAAA,IAAI,MAAM,IAAI,SAAS,EAAE;QACvB,MAAM,KAAK,CACT,CAAiE,+DAAA,CAAA;AAC/D,YAAA,iBAAiB,CAAC,GAAG,CAAC,IAAI,IAAI,CAAA,CAAA,EAAI,IAAI,CAAA,CAAA,CAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CACzD,CAAC;KACH;AACD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;AACA,SAAS,mCAAmC,CAAC,OAAuC,EAAA;IAClF,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,IACtB,OAAO,KAAK,KAAK,QAAQ;AACvB,UAAE,kCAAkC,CAAC,KAAK,CAAC;AAC3C,UAAE,uCAAuC,CAAC,KAAK,CAAC,CACnD,CAAC;AACJ,CAAC;AAED;AACA,SAAS,uCAAuC,CAAC,KAAwB,EAAA;AACvE,IAAA,MAAM,gBAAgB,GACpB,KAAK,YAAY,gBAAgB,GAAG,KAAK,GAAG,IAAI,gBAAgB,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAC9E,MAAM,EAAC,IAAI,EAAE,YAAY,EAAC,GAAG,gBAAgB,CAAC,WAAW,CAAC;AAC1D,IAAA,MAAM,WAAW,GAAG,CAAA,EAAG,IAAI,CAA0C,uCAAA,EAAA,YAAY,GAAG,CAAC;AACrF,IAAA,MAAM,WAAW,GAAG,gBAAgB,CAAC,cAAc,EAAE,CAAC;AACtD,IAAA,QACE,WAAW;AACX,SAAC,WAAW,GAAG,CAAA,6BAAA,EAAgC,gBAAgB,CAAC,cAAc,EAAE,CAAA,CAAE,GAAG,EAAE,CAAC,EACxF;AACJ,CAAC;AAED;AACA,SAAS,kCAAkC,CAAC,QAAgB,EAAA;IAC1D,OAAO,CAAA,4CAAA,EAA+C,QAAQ,CAAA,CAAA,CAAG,CAAC;AACpE,CAAC;AAED;AACA,SAAS,oCAAoC,CAAC,QAAgB,EAAA;IAC5D,OAAO,CAAA,8CAAA,EAAiD,QAAQ,CAAA,CAAA,CAAG,CAAC;AACtE;;AC5TA;AACA;AACA;AACA;AACA;AACA;AACA;IACY,QAgCX;AAhCD,CAAA,UAAY,OAAO,EAAA;AACjB,IAAA,OAAA,CAAA,OAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAS,CAAA;AACT,IAAA,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAG,CAAA;AACH,IAAA,OAAA,CAAA,OAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK,CAAA;AACL,IAAA,OAAA,CAAA,OAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK,CAAA;AACL,IAAA,OAAA,CAAA,OAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO,CAAA;AACP,IAAA,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAG,CAAA;AACH,IAAA,OAAA,CAAA,OAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM,CAAA;AACN,IAAA,OAAA,CAAA,OAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO,CAAA;AACP,IAAA,OAAA,CAAA,OAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAS,CAAA;AACT,IAAA,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAG,CAAA;AACH,IAAA,OAAA,CAAA,OAAA,CAAA,MAAA,CAAA,GAAA,EAAA,CAAA,GAAA,MAAI,CAAA;AACJ,IAAA,OAAA,CAAA,OAAA,CAAA,YAAA,CAAA,GAAA,EAAA,CAAA,GAAA,YAAU,CAAA;AACV,IAAA,OAAA,CAAA,OAAA,CAAA,UAAA,CAAA,GAAA,EAAA,CAAA,GAAA,UAAQ,CAAA;AACR,IAAA,OAAA,CAAA,OAAA,CAAA,aAAA,CAAA,GAAA,EAAA,CAAA,GAAA,aAAW,CAAA;AACX,IAAA,OAAA,CAAA,OAAA,CAAA,YAAA,CAAA,GAAA,EAAA,CAAA,GAAA,YAAU,CAAA;AACV,IAAA,OAAA,CAAA,OAAA,CAAA,QAAA,CAAA,GAAA,EAAA,CAAA,GAAA,QAAM,CAAA;AACN,IAAA,OAAA,CAAA,OAAA,CAAA,QAAA,CAAA,GAAA,EAAA,CAAA,GAAA,QAAM,CAAA;AACN,IAAA,OAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,GAAA,IAAE,CAAA;AACF,IAAA,OAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,GAAA,IAAE,CAAA;AACF,IAAA,OAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,GAAA,IAAE,CAAA;AACF,IAAA,OAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,GAAA,IAAE,CAAA;AACF,IAAA,OAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,GAAA,IAAE,CAAA;AACF,IAAA,OAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,GAAA,IAAE,CAAA;AACF,IAAA,OAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,GAAA,IAAE,CAAA;AACF,IAAA,OAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,GAAA,IAAE,CAAA;AACF,IAAA,OAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,GAAA,IAAE,CAAA;AACF,IAAA,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,KAAG,CAAA;AACH,IAAA,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,KAAG,CAAA;AACH,IAAA,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,KAAG,CAAA;AACH,IAAA,OAAA,CAAA,OAAA,CAAA,MAAA,CAAA,GAAA,EAAA,CAAA,GAAA,MAAI,CAAA;AACJ,IAAA,OAAA,CAAA,OAAA,CAAA,OAAA,CAAA,GAAA,EAAA,CAAA,GAAA,OAAK,CAAA;AACP,CAAC,EAhCW,OAAO,KAAP,OAAO,GAgClB,EAAA,CAAA,CAAA;;AC5DD;;;AAGG;SACa,uBAAuB,GAAA;AACrC,IAAA,OAAO,KAAK,CAAC,8BAA8B,CAAC,CAAC;AAC/C;;ACNA;;;;AAIG;AACa,SAAA,4BAA4B,CAAC,OAAgB,EAAE,eAAuB,EAAA;IACpF,MAAM,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAY,CAAC;IACjD,MAAM,UAAU,GAAG,KAAK,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;AAC3D,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,QAAA,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;KACxB;IACD,OAAO,CAAC,KAAK,CAAC,WAAW,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC;AAC1C;;;;"}