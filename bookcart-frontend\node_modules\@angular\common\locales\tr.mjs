/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["tr", [["öö", "ös"], ["ÖÖ", "ÖS"], u], [["ÖÖ", "ÖS"], u, u], [["P", "P", "S", "Ç", "P", "C", "C"], ["<PERSON>", "<PERSON>zt", "Sal", "Çar", "Per", "Cum", "Cmt"], ["Pazar", "Pazartesi", "Salı", "Çarşamba", "Perşembe", "Cuma", "Cumartesi"], ["<PERSON>", "Pt", "Sa", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"]], u, [["O", "<PERSON>", "<PERSON>", "N", "M", "H", "T", "A", "E", "E", "K", "A"], ["Oca", "<PERSON><PERSON>", "<PERSON>", "Nis", "May", "Haz", "Tem", "Ağu", "Eyl", "Eki", "Kas", "Ara"], ["Ocak", "Şubat", "Mart", "Nisan", "Mayıs", "Haziran", "<PERSON>mmuz", "<PERSON>ğustos", "<PERSON>ylül", "<PERSON>kim", "Kasım", "Aralık"]], u, [["MÖ", "MS"], u, ["Milattan Önce", "Milattan Sonra"]], 1, [6, 0], ["d.MM.y", "d MMM y", "d MMMM y", "d MMMM y EEEE"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "%#,##0", "¤#,##0.00", "#E0"], "TRY", "₺", "Türk Lirası", { "AUD": ["AU$", "$"], "BYN": [u, "р."], "PHP": [u, "₱"], "RON": [u, "L"], "RUR": [u, "р."], "THB": ["฿"], "TRY": ["₺"], "TWD": ["NT$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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