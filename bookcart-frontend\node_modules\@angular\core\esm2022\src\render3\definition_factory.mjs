/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { stringify } from '../util/stringify';
import { NG_FACTORY_DEF } from './fields';
export function getFactoryDef(type, throwNotFound) {
    const hasFactoryDef = type.hasOwnProperty(NG_FACTORY_DEF);
    if (!hasFactoryDef && throwNotFound === true && ngDevMode) {
        throw new Error(`Type ${stringify(type)} does not have 'ɵfac' property.`);
    }
    return hasFactoryDef ? type[NG_FACTORY_DEF] : null;
}
//# sourceMappingURL=data:application/json;base64,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