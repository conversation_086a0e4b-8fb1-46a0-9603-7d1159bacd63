
      import {createRequire as __cjsCompatRequire} from 'module';
      const require = __cjsCompatRequire(import.meta.url);
    
import {
  GLOBAL_DEFS_FOR_TERSER,
  GLOBAL_DEFS_FOR_TERSER_WITH_AOT,
  constructorParametersDownlevelTransform
} from "../chunk-NDT2FVCM.js";
import "../chunk-26Z5EPVF.js";
import "../chunk-NMMGOE7N.js";
import "../chunk-R4KQI5XI.js";
import "../chunk-75YFKYUJ.js";
import "../chunk-XI2RTGAL.js";
export {
  GLOBAL_DEFS_FOR_TERSER,
  GLOBAL_DEFS_FOR_TERSER_WITH_AOT,
  constructorParametersDownlevelTransform
};
//# sourceMappingURL=tooling.js.map
