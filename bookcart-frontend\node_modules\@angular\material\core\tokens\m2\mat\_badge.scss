@use 'sass:meta';
@use 'sass:map';
@use 'sass:math';
@use 'sass:color';
@use '../../token-utils';
@use '../../../theming/inspection';
@use '../../../style/sass-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, badge);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  $default-size: 22px;
  $small-size: $default-size - 6;
  $large-size: $default-size + 6;

  @return (
    container-shape: 50%,
    container-size: unset,
    small-size-container-size: unset,
    large-size-container-size: unset,

    legacy-container-size: $default-size,
    legacy-small-size-container-size: $small-size,
    legacy-large-size-container-size: $large-size,

    container-offset: math.div($default-size, -2) 0,
    small-size-container-offset: math.div($small-size, -2) 0,
    large-size-container-offset: math.div($large-size, -2) 0,

    container-overlap-offset: math.div($default-size, -2),
    small-size-container-overlap-offset: math.div($small-size, -2),
    large-size-container-overlap-offset: math.div($large-size, -2),

    container-padding: 0,
    small-size-container-padding: 0,
    large-size-container-padding: 0,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  $primary-color-tokens: private-get-color-palette-color-tokens($theme, primary);
  $app-background: inspection.get-theme-color($theme, background, background);
  $disabled-background: inspection.get-theme-color($theme, foreground, disabled-button);

  // The disabled color usually has some kind of opacity, but because the badge is overlayed
  // on top of something else, it won't look good if it's opaque. If it is a color *type*,
  // we convert it into a solid color by taking the opacity from the rgba value and using
  // the value to determine the percentage of the background to put into foreground when
  // mixing the colors together.
  @if (meta.type-of($disabled-background) == color and meta.type-of($app-background) == color) {
    $badge-opacity: opacity($disabled-background);
    $disabled-background: color.mix($app-background,
      rgba($disabled-background, 1), (1 - $badge-opacity) * 100%);
  }

  @return map.merge($primary-color-tokens, (
    disabled-state-background-color: $disabled-background,
    disabled-state-text-color: inspection.get-theme-color($theme, foreground, disabled-text),
  ));
}

// Generates the tokens used to theme the badge based on a palette.
@function private-get-color-palette-color-tokens($theme, $palette-name: primary) {
  @return (
    background-color: inspection.get-theme-color($theme, $palette-name),
    text-color: inspection.get-theme-color($theme, $palette-name, default-contrast),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  $base-size: 12px;

  @return (
    text-font: inspection.get-theme-typography($theme, body-2, font-family),
    text-size: $base-size,
    text-weight: 600,
    small-size-text-size: $base-size * 0.75,
    large-size-text-size: $base-size * 2,
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-utils.$placeholder-color-config),
      get-typography-tokens(token-utils.$placeholder-typography-config),
      get-density-tokens(token-utils.$placeholder-density-config)
  );
}
