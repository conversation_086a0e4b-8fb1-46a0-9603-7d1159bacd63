{"version": 3, "sources": ["../../../../../../../node_modules/tslib/tslib.js", "../../../../../../../src/material/schematics/ng-update/migrations/legacy-imports-error.ts", "../../../../../../../src/material/schematics/ng-update/data/attribute-selectors.ts", "../../../../../../../src/material/schematics/ng-update/data/class-names.ts", "../../../../../../../src/material/schematics/ng-update/data/constructor-checks.ts", "../../../../../../../src/material/schematics/ng-update/data/css-selectors.ts", "../../../../../../../src/material/schematics/ng-update/data/element-selectors.ts", "../../../../../../../src/material/schematics/ng-update/data/input-names.ts", "../../../../../../../src/material/schematics/ng-update/data/method-call-checks.ts", "../../../../../../../src/material/schematics/ng-update/data/output-names.ts", "../../../../../../../src/material/schematics/ng-update/data/property-names.ts", "../../../../../../../src/material/schematics/ng-update/data/symbol-removal.ts", "../../../../../../../src/material/schematics/ng-update/data/index.ts", "../../../../../../../src/material/schematics/ng-update/upgrade-data.ts", "../../../../../../../src/material/schematics/ng-update/migrations/theme-base-v17/migration.ts", "../../../../../../../src/material/schematics/ng-update/migrations/theme-base-v17/index.ts", "../../../../../../../src/material/schematics/ng-update/index.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global global, define, System, Reflect, Promise */\r\nvar __extends;\r\nvar __assign;\r\nvar __rest;\r\nvar __decorate;\r\nvar __param;\r\nvar __metadata;\r\nvar __awaiter;\r\nvar __generator;\r\nvar __exportStar;\r\nvar __values;\r\nvar __read;\r\nvar __spread;\r\nvar __spreadArrays;\r\nvar __spreadArray;\r\nvar __await;\r\nvar __asyncGenerator;\r\nvar __asyncDelegator;\r\nvar __asyncValues;\r\nvar __makeTemplateObject;\r\nvar __importStar;\r\nvar __importDefault;\r\nvar __classPrivateFieldGet;\r\nvar __classPrivateFieldSet;\r\nvar __createBinding;\r\n(function (factory) {\r\n    var root = typeof global === \"object\" ? global : typeof self === \"object\" ? self : typeof this === \"object\" ? this : {};\r\n    if (typeof define === \"function\" && define.amd) {\r\n        define(\"tslib\", [\"exports\"], function (exports) { factory(createExporter(root, createExporter(exports))); });\r\n    }\r\n    else if (typeof module === \"object\" && typeof module.exports === \"object\") {\r\n        factory(createExporter(root, createExporter(module.exports)));\r\n    }\r\n    else {\r\n        factory(createExporter(root));\r\n    }\r\n    function createExporter(exports, previous) {\r\n        if (exports !== root) {\r\n            if (typeof Object.create === \"function\") {\r\n                Object.defineProperty(exports, \"__esModule\", { value: true });\r\n            }\r\n            else {\r\n                exports.__esModule = true;\r\n            }\r\n        }\r\n        return function (id, v) { return exports[id] = previous ? previous(id, v) : v; };\r\n    }\r\n})\r\n(function (exporter) {\r\n    var extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n\r\n    __extends = function (d, b) {\r\n        if (typeof b !== \"function\" && b !== null)\r\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n        extendStatics(d, b);\r\n        function __() { this.constructor = d; }\r\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n    };\r\n\r\n    __assign = Object.assign || function (t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n\r\n    __rest = function (s, e) {\r\n        var t = {};\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n            t[p] = s[p];\r\n        if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n            for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n                if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                    t[p[i]] = s[p[i]];\r\n            }\r\n        return t;\r\n    };\r\n\r\n    __decorate = function (decorators, target, key, desc) {\r\n        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n        if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n        return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n    };\r\n\r\n    __param = function (paramIndex, decorator) {\r\n        return function (target, key) { decorator(target, key, paramIndex); }\r\n    };\r\n\r\n    __metadata = function (metadataKey, metadataValue) {\r\n        if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n    };\r\n\r\n    __awaiter = function (thisArg, _arguments, P, generator) {\r\n        function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n        return new (P || (P = Promise))(function (resolve, reject) {\r\n            function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n            function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n            function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n            step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n        });\r\n    };\r\n\r\n    __generator = function (thisArg, body) {\r\n        var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n        return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n        function verb(n) { return function (v) { return step([n, v]); }; }\r\n        function step(op) {\r\n            if (f) throw new TypeError(\"Generator is already executing.\");\r\n            while (_) try {\r\n                if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n                if (y = 0, t) op = [op[0] & 2, t.value];\r\n                switch (op[0]) {\r\n                    case 0: case 1: t = op; break;\r\n                    case 4: _.label++; return { value: op[1], done: false };\r\n                    case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                    case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                    default:\r\n                        if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                        if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                        if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                        if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                        if (t[2]) _.ops.pop();\r\n                        _.trys.pop(); continue;\r\n                }\r\n                op = body.call(thisArg, _);\r\n            } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n            if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n        }\r\n    };\r\n\r\n    __exportStar = function(m, o) {\r\n        for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n    };\r\n\r\n    __createBinding = Object.create ? (function(o, m, k, k2) {\r\n        if (k2 === undefined) k2 = k;\r\n        Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\r\n    }) : (function(o, m, k, k2) {\r\n        if (k2 === undefined) k2 = k;\r\n        o[k2] = m[k];\r\n    });\r\n\r\n    __values = function (o) {\r\n        var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n        if (m) return m.call(o);\r\n        if (o && typeof o.length === \"number\") return {\r\n            next: function () {\r\n                if (o && i >= o.length) o = void 0;\r\n                return { value: o && o[i++], done: !o };\r\n            }\r\n        };\r\n        throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n    };\r\n\r\n    __read = function (o, n) {\r\n        var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n        if (!m) return o;\r\n        var i = m.call(o), r, ar = [], e;\r\n        try {\r\n            while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n        }\r\n        catch (error) { e = { error: error }; }\r\n        finally {\r\n            try {\r\n                if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n            }\r\n            finally { if (e) throw e.error; }\r\n        }\r\n        return ar;\r\n    };\r\n\r\n    /** @deprecated */\r\n    __spread = function () {\r\n        for (var ar = [], i = 0; i < arguments.length; i++)\r\n            ar = ar.concat(__read(arguments[i]));\r\n        return ar;\r\n    };\r\n\r\n    /** @deprecated */\r\n    __spreadArrays = function () {\r\n        for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n        for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n            for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n                r[k] = a[j];\r\n        return r;\r\n    };\r\n\r\n    __spreadArray = function (to, from, pack) {\r\n        if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n            if (ar || !(i in from)) {\r\n                if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n                ar[i] = from[i];\r\n            }\r\n        }\r\n        return to.concat(ar || Array.prototype.slice.call(from));\r\n    };\r\n\r\n    __await = function (v) {\r\n        return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n    };\r\n\r\n    __asyncGenerator = function (thisArg, _arguments, generator) {\r\n        if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n        var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n        return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n        function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n        function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n        function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);  }\r\n        function fulfill(value) { resume(\"next\", value); }\r\n        function reject(value) { resume(\"throw\", value); }\r\n        function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n    };\r\n\r\n    __asyncDelegator = function (o) {\r\n        var i, p;\r\n        return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n        function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n    };\r\n\r\n    __asyncValues = function (o) {\r\n        if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n        var m = o[Symbol.asyncIterator], i;\r\n        return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n        function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n        function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n    };\r\n\r\n    __makeTemplateObject = function (cooked, raw) {\r\n        if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n        return cooked;\r\n    };\r\n\r\n    var __setModuleDefault = Object.create ? (function(o, v) {\r\n        Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n    }) : function(o, v) {\r\n        o[\"default\"] = v;\r\n    };\r\n\r\n    __importStar = function (mod) {\r\n        if (mod && mod.__esModule) return mod;\r\n        var result = {};\r\n        if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n        __setModuleDefault(result, mod);\r\n        return result;\r\n    };\r\n\r\n    __importDefault = function (mod) {\r\n        return (mod && mod.__esModule) ? mod : { \"default\": mod };\r\n    };\r\n\r\n    __classPrivateFieldGet = function (receiver, state, kind, f) {\r\n        if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n        if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n        return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n    };\r\n\r\n    __classPrivateFieldSet = function (receiver, state, value, kind, f) {\r\n        if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n        if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n        if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n        return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n    };\r\n\r\n    exporter(\"__extends\", __extends);\r\n    exporter(\"__assign\", __assign);\r\n    exporter(\"__rest\", __rest);\r\n    exporter(\"__decorate\", __decorate);\r\n    exporter(\"__param\", __param);\r\n    exporter(\"__metadata\", __metadata);\r\n    exporter(\"__awaiter\", __awaiter);\r\n    exporter(\"__generator\", __generator);\r\n    exporter(\"__exportStar\", __exportStar);\r\n    exporter(\"__createBinding\", __createBinding);\r\n    exporter(\"__values\", __values);\r\n    exporter(\"__read\", __read);\r\n    exporter(\"__spread\", __spread);\r\n    exporter(\"__spreadArrays\", __spreadArrays);\r\n    exporter(\"__spreadArray\", __spreadArray);\r\n    exporter(\"__await\", __await);\r\n    exporter(\"__asyncGenerator\", __asyncGenerator);\r\n    exporter(\"__asyncDelegator\", __asyncDelegator);\r\n    exporter(\"__asyncValues\", __asyncValues);\r\n    exporter(\"__makeTemplateObject\", __makeTemplateObject);\r\n    exporter(\"__importStar\", __importStar);\r\n    exporter(\"__importDefault\", __importDefault);\r\n    exporter(\"__classPrivateFieldGet\", __classPrivateFieldGet);\r\n    exporter(\"__classPrivateFieldSet\", __classPrivateFieldSet);\r\n});\r\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Rule, SchematicContext, Tree} from '@angular-devkit/schematics';\nimport {NodePackageInstallTask} from '@angular-devkit/schematics/tasks';\nimport * as ts from 'typescript';\n\n/** String with which legacy imports start. */\nconst LEGACY_IMPORTS_START = '@angular/material/legacy-';\n\n/** Maximum files to print in the error message. */\nconst MAX_FILES_TO_PRINT = 50;\n\n/**\n * \"Migration\" that logs an error and prevents further migrations\n * from running if the project is using legacy components.\n * @param onSuccess Rule to run if there are no legacy imports.\n */\nexport function legacyImportsError(onSuccess: Rule): Rule {\n  return async (tree: Tree, context: SchematicContext) => {\n    const filesUsingLegacyImports = new Set<string>();\n\n    tree.visit(path => {\n      if (path.includes('node_modules') || path.endsWith('.d.ts') || !path.endsWith('.ts')) {\n        return;\n      }\n\n      const content = tree.readText(path);\n\n      // Skip over any files that definitely cannot contain the legacy imports.\n      if (!content.includes(LEGACY_IMPORTS_START)) {\n        return;\n      }\n\n      const sourceFile = ts.createSourceFile(path, content, ts.ScriptTarget.Latest);\n\n      // Only check top-level imports/exports.\n      for (const statement of sourceFile.statements) {\n        if (!ts.isImportDeclaration(statement) && !ts.isExportDeclaration(statement)) {\n          continue;\n        }\n\n        if (\n          statement.moduleSpecifier &&\n          ts.isStringLiteralLike(statement.moduleSpecifier) &&\n          statement.moduleSpecifier.text.startsWith(LEGACY_IMPORTS_START)\n        ) {\n          filesUsingLegacyImports.add(path);\n        }\n      }\n    });\n\n    // If there are no legacy imports left, we can continue with the migrations.\n    if (filesUsingLegacyImports.size === 0) {\n      return onSuccess;\n    }\n\n    // At this point the project is already at v17 so we need to downgrade it back\n    // to v16 and run `npm install` again. Ideally we would also throw an error here\n    // to interrupt the update process, but that would interrupt `npm install` as well.\n    if (tree.exists('package.json')) {\n      let packageJson: Record<string, any> | null = null;\n\n      try {\n        packageJson = JSON.parse(tree.readText('package.json')) as Record<string, any>;\n      } catch {}\n\n      if (packageJson !== null && packageJson['dependencies']) {\n        packageJson['dependencies']['@angular/material'] = '^16.2.0';\n        tree.overwrite('package.json', JSON.stringify(packageJson, null, 2));\n        context.addTask(new NodePackageInstallTask());\n      }\n    }\n\n    context.logger.fatal(formatErrorMessage(filesUsingLegacyImports));\n    return;\n  };\n}\n\nfunction formatErrorMessage(filesUsingLegacyImports: Set<string>): string {\n  const files = Array.from(filesUsingLegacyImports, path => ' - ' + path);\n  const filesMessage =\n    files.length > MAX_FILES_TO_PRINT\n      ? [\n          ...files.slice(0, MAX_FILES_TO_PRINT),\n          `${files.length - MAX_FILES_TO_PRINT} more...`,\n          `Search your project for \"${LEGACY_IMPORTS_START}\" to view all usages.`,\n        ].join('\\n')\n      : files.join('\\n');\n\n  return (\n    `Cannot update to Angular Material v17 because the project is using the legacy ` +\n    `Material components\\nthat have been deleted. While Angular Material v16 is compatible with ` +\n    `Angular v17, it is recommended\\nto switch away from the legacy components as soon as possible ` +\n    `because they no longer receive bug fixes,\\naccessibility improvements and new features.\\n\\n` +\n    `Read more about migrating away from legacy components: https://material.angular.io/guide/mdc-migration\\n\\n` +\n    `Files in the project using legacy Material components:\\n${filesMessage}\\n`\n  );\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AttributeSelectorUpgradeData, VersionChanges} from '@angular/cdk/schematics';\n\nexport const attributeSelectors: VersionChanges<AttributeSelectorUpgradeData> = {};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ClassNameUpgradeData, VersionChanges} from '@angular/cdk/schematics';\n\nexport const classNames: VersionChanges<ClassNameUpgradeData> = {};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ConstructorChecksUpgradeData, VersionChanges} from '@angular/cdk/schematics';\n\n/**\n * List of class names for which the constructor signature has been changed. The new constructor\n * signature types don't need to be stored here because the signature will be determined\n * automatically through type checking.\n */\nexport const constructorChecks: VersionChanges<ConstructorChecksUpgradeData> = {};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {VersionChanges} from '@angular/cdk/schematics';\n\nexport interface MaterialCssSelectorData {\n  /** The CSS selector to replace. */\n  replace: string;\n  /** The new CSS selector. */\n  replaceWith: string;\n  /**\n   * Controls which file types in which this replacement is made. If omitted, it is made in all\n   * files.\n   */\n  replaceIn?: {\n    /** Replace this name in stylesheet files. */\n    stylesheet?: boolean;\n    /** Replace this name in HTML files. */\n    html?: boolean;\n    /** Replace this name in TypeScript strings. */\n    tsStringLiterals?: boolean;\n  };\n}\n\nexport const cssSelectors: VersionChanges<MaterialCssSelectorData> = {};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ElementSelectorUpgradeData, VersionChanges} from '@angular/cdk/schematics';\n\nexport const elementSelectors: VersionChanges<ElementSelectorUpgradeData> = {};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {InputNameUpgradeData, VersionChanges} from '@angular/cdk/schematics';\n\nexport const inputNames: VersionChanges<InputNameUpgradeData> = {};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {MethodCallUpgradeData, VersionChanges} from '@angular/cdk/schematics';\n\nexport const methodCallChecks: VersionChanges<MethodCallUpgradeData> = {};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {OutputNameUpgradeData, VersionChanges} from '@angular/cdk/schematics';\n\nexport const outputNames: VersionChanges<OutputNameUpgradeData> = {};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {PropertyNameUpgradeData, VersionChanges} from '@angular/cdk/schematics';\n\nexport const propertyNames: VersionChanges<PropertyNameUpgradeData> = {};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {SymbolRemovalUpgradeData, VersionChanges} from '@angular/cdk/schematics';\n\nexport const symbolRemoval: VersionChanges<SymbolRemovalUpgradeData> = {};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport * from './attribute-selectors';\nexport * from './class-names';\nexport * from './constructor-checks';\nexport * from './css-selectors';\nexport * from './element-selectors';\nexport * from './input-names';\nexport * from './method-call-checks';\nexport * from './output-names';\nexport * from './property-names';\nexport * from './symbol-removal';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {UpgradeData} from '@angular/cdk/schematics';\nimport {\n  attributeSelectors,\n  classNames,\n  constructorChecks,\n  cssSelectors,\n  elementSelectors,\n  inputNames,\n  methodCallChecks,\n  outputNames,\n  propertyNames,\n  symbolRemoval,\n} from './data';\n\n/** Upgrade data that will be used for the Angular Material ng-update schematic. */\nexport const materialUpgradeData: UpgradeData = {\n  attributeSelectors,\n  classNames,\n  constructorChecks,\n  cssSelectors,\n  elementSelectors,\n  inputNames,\n  methodCallChecks,\n  outputNames,\n  propertyNames,\n  symbolRemoval,\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Preamble to insert before the missing mixins. */\nconst MISSING_MIXIN_PREAMBLE_LINES = `\n// The following mixins include base theme styles that are only needed once per application. These\n// theme styles do not depend on the color, typography, or density settings in your theme. However,\n// these styles may differ depending on the theme's design system. Currently all themes use the\n// Material 2 design system, but in the future it may be possible to create theme based on other\n// design systems, such as Material 3.\n//\n// Please note: you do not need to include the 'base' mixins, if you include the corresponding\n// 'theme' mixin elsewhere in your Sass. The full 'theme' mixins already include the base styles.\n//\n// To learn more about \"base\" theme styles visit our theming guide:\n// https://material.angular.io/guide/theming#theming-dimensions\n//\n// TODO(v17): Please move these @include statements to the preferred place in your Sass, and pass\n// your theme to them. This will ensure the correct values for your app are included.\\\n`.split('\\n');\n\n/** The sets of theme mixins to check for. */\nconst THEME_MIXIN_SETS: {\n  theme: string;\n  color: string;\n  typography: string;\n  density: string;\n  base: string;\n}[] = [\n  {\n    theme: 'all-component-themes',\n    color: 'all-component-colors',\n    typography: 'all-component-typographies',\n    density: 'all-component-densities',\n    base: 'all-component-bases',\n  },\n  ...[\n    'core',\n    'card',\n    'progress-bar',\n    'tooltip',\n    'form-field',\n    'input',\n    'select',\n    'autocomplete',\n    'dialog',\n    'chips',\n    'slide-toggle',\n    'radio',\n    'slider',\n    'menu',\n    'list',\n    'paginator',\n    'tabs',\n    'checkbox',\n    'button',\n    'icon-button',\n    'fab',\n    'snack-bar',\n    'table',\n    'progress-spinner',\n    'badge',\n    'bottom-sheet',\n    'button-toggle',\n    'datepicker',\n    'divider',\n    'expansion',\n    'grid-list',\n    'icon',\n    'sidenav',\n    'stepper',\n    'sort',\n    'toolbar',\n    'tree',\n  ].map(comp => ({\n    theme: `${comp}-theme`,\n    color: `${comp}-color`,\n    typography: `${comp}-typography`,\n    density: `${comp}-density`,\n    base: `${comp}-base`,\n  })),\n];\n\n/** Possible pairs of comment characters in a Sass file. */\nconst COMMENT_PAIRS = new Map<string, string>([\n  ['/*', '*/'],\n  ['//', '\\n'],\n]);\n\n/** Prefix for the placeholder that will be used to escape comments. */\nconst COMMENT_PLACEHOLDER_START = '__<<ngThemingMigrationEscapedComment';\n\n/** Suffix for the comment escape placeholder. */\nconst COMMENT_PLACEHOLDER_END = '>>__';\n\n/**\n * Replaces all the comments in a Sass file with placeholders and\n * returns the list of placeholders, so they can be restored later.\n */\nfunction escapeComments(content: string): {content: string; placeholders: Record<string, string>} {\n  const placeholders: Record<string, string> = {};\n  let commentCounter = 0;\n  let [openIndex, closeIndex] = findComment(content);\n\n  while (openIndex > -1 && closeIndex > -1) {\n    const placeholder = COMMENT_PLACEHOLDER_START + commentCounter++ + COMMENT_PLACEHOLDER_END;\n    placeholders[placeholder] = content.slice(openIndex, closeIndex);\n    content = content.slice(0, openIndex) + placeholder + content.slice(closeIndex);\n    [openIndex, closeIndex] = findComment(content);\n  }\n\n  return {content, placeholders};\n}\n\n/** Finds the start and end index of a comment in a file. */\nfunction findComment(content: string): [openIndex: number, closeIndex: number] {\n  // Add an extra new line at the end so that we can correctly capture single-line comments\n  // at the end of the file. It doesn't really matter that the end index will be out of bounds,\n  // because `String.prototype.slice` will clamp it to the string length.\n  content += '\\n';\n\n  for (const [open, close] of COMMENT_PAIRS.entries()) {\n    const openIndex = content.indexOf(open);\n\n    if (openIndex > -1) {\n      const closeIndex = content.indexOf(close, openIndex + 1);\n      return closeIndex > -1 ? [openIndex, closeIndex + close.length] : [-1, -1];\n    }\n  }\n\n  return [-1, -1];\n}\n\n/** Restores the comments that have been escaped by `escapeComments`. */\nfunction restoreComments(content: string, placeholders: Record<string, string>): string {\n  Object.keys(placeholders).forEach(key => (content = content.replace(key, placeholders[key])));\n  return content;\n}\n\n/** Escapes special regex characters in a string. */\nfunction escapeRegExp(str: string): string {\n  return str.replace(/([.*+?^=!:${}()|[\\]\\/\\\\])/g, '\\\\$1');\n}\n\n/** Parses out the namespace from a Sass `@use` statement. */\nfunction extractNamespaceFromUseStatement(fullImport: string): string {\n  const closeQuoteIndex = Math.max(fullImport.lastIndexOf(`\"`), fullImport.lastIndexOf(`'`));\n\n  if (closeQuoteIndex > -1) {\n    const asExpression = 'as ';\n    const asIndex = fullImport.indexOf(asExpression, closeQuoteIndex);\n\n    // If we found an ` as ` expression, we consider the rest of the text as the namespace.\n    if (asIndex > -1) {\n      return fullImport\n        .slice(asIndex + asExpression.length)\n        .split(';')[0]\n        .trim();\n    }\n\n    // Otherwise the namespace is the name of the file that is being imported.\n    const lastSlashIndex = fullImport.lastIndexOf('/', closeQuoteIndex);\n\n    if (lastSlashIndex > -1) {\n      const fileName = fullImport\n        .slice(lastSlashIndex + 1, closeQuoteIndex)\n        // Sass allows for leading underscores to be omitted and it technically supports .scss.\n        .replace(/^_|(\\.import)?\\.scss$|\\.import$/g, '');\n\n      // Sass ignores `/index` and infers the namespace as the next segment in the path.\n      if (fileName === 'index') {\n        const nextSlashIndex = fullImport.lastIndexOf('/', lastSlashIndex - 1);\n\n        if (nextSlashIndex > -1) {\n          return fullImport.slice(nextSlashIndex + 1, lastSlashIndex);\n        }\n      } else {\n        return fileName;\n      }\n    }\n  }\n\n  throw Error(`Could not extract namespace from import \"${fullImport}\".`);\n}\n\n/** Gets the set of namespaces that the given import path is aliased to by @use. */\nfunction getAtUseNamespaces(content: string, path: string) {\n  const namespaces = new Set<string>();\n  const pattern = new RegExp(`@use +['\"]~?${escapeRegExp(path)}['\"].*;?\\n`, 'g');\n  let match: RegExpExecArray | null = null;\n\n  while ((match = pattern.exec(content))) {\n    namespaces.add(extractNamespaceFromUseStatement(match[0]));\n  }\n\n  return namespaces;\n}\n\n/** Gets a list of matches representing where the given mixin is included with `@include`. */\nfunction getAtIncludes(content: string, namespace: string, mixin: string): RegExpMatchArray[] {\n  // The ending checks what comes after the mixin name. We need to check that we don't see a word\n  // character or `-` immediately following the mixin name, as that would change the name. Beyond\n  // that character we can match anything, to the end of the line.\n  const ending = '([^\\\\n\\\\w-][^\\\\n]*)?($|\\\\n)';\n  const pattern = new RegExp(\n    `@include\\\\s+${escapeRegExp(namespace)}\\\\.${escapeRegExp(mixin)}${ending}`,\n    'g',\n  );\n  return [...content.matchAll(pattern)];\n}\n\n/** Checks whether the given mixin is included with `@include`. */\nfunction isMixinAtIncluded(content: string, namespace: string, mixin: string) {\n  return !!getAtIncludes(content, namespace, mixin).length;\n}\n\n/** Inserts the given lines after the match point. */\nfunction insertLinesAfterMatch(content: string, match: RegExpMatchArray, lines: string[]): string {\n  const insertionPoint = match.index! + match[0].length;\n  return (\n    content.substring(0, insertionPoint) +\n    lines.join('\\n') +\n    '\\n' +\n    content.substring(insertionPoint)\n  );\n}\n\n/** Gets the indentation at the given line in the content. */\nfunction getIndentation(content: string, index: number) {\n  let indentationStart = 0;\n  let indentationEnd = index;\n  for (let i = index; i >= 0; i--) {\n    if (content[i] === '\\n') {\n      indentationStart = i + 1;\n      break;\n    }\n    if (!/\\s/.exec(content[i])) {\n      indentationEnd = i;\n    }\n  }\n  return content.slice(indentationStart, indentationEnd);\n}\n\n/** Gets the lines to insert to address the missing mixins. */\nfunction getMissingMixinLines(namespace: string, mixins: Set<string>, indentation: string) {\n  return [\n    ...MISSING_MIXIN_PREAMBLE_LINES,\n    ...[...mixins]\n      .sort()\n      .map(mixin => `@include ${namespace}.${mixin}(/* TODO(v17): pass $your-theme here */);`),\n    '',\n  ].map(line => (indentation + line).trimRight());\n}\n\n/**\n * Checks which theme bases are found in the file via the existing included mixins,\n * and which ones may be missing.\n */\nexport function checkThemeBaseMixins(fileContent: string): {\n  found: Set<string>;\n  missing: Set<string>;\n} {\n  const found = new Set<string>();\n  const missing = new Set<string>();\n\n  // Strip out comments, so they don't confuse our migration.\n  const {content} = escapeComments(fileContent);\n  const materialNamespaces = getAtUseNamespaces(content, '@angular/material');\n\n  // Check over all namespaces for mixins of interest.\n  for (const namespace of materialNamespaces) {\n    for (const mixins of THEME_MIXIN_SETS) {\n      // If they include the theme mixin, that accounts for the base theme styles.\n      if (isMixinAtIncluded(content, namespace, mixins.theme)) {\n        found.add(mixins.base);\n        missing.delete(mixins.base);\n        continue;\n      }\n      // If they haven't called the theme mixin, but do call one of the partials,\n      // we assume they're missing the base styles.\n      if (!found.has(mixins.base)) {\n        if (\n          isMixinAtIncluded(content, namespace, mixins.color) ||\n          isMixinAtIncluded(content, namespace, mixins.typography) ||\n          isMixinAtIncluded(content, namespace, mixins.density)\n        ) {\n          missing.add(mixins.base);\n        }\n      }\n    }\n  }\n\n  return {found, missing};\n}\n\n/** Adds the given theme base mixins, after the call to `mat.core()`. */\nexport function addThemeBaseMixins(fileContent: string, mixins: Set<string>): string {\n  // Strip out comments, so they don't confuse our migration.\n  let {content, placeholders} = escapeComments(fileContent);\n  const materialNamespaces = getAtUseNamespaces(content, '@angular/material');\n\n  for (const namespace of materialNamespaces) {\n    // Update the @includes in reverse order, so our changes don't mess up the indices we found.\n    const coreIncludes = getAtIncludes(content, namespace, 'core').reverse();\n    for (const coreInclude of coreIncludes) {\n      if (coreInclude.index === undefined) {\n        throw Error(`Cannot find location of mat.core() match: ${coreInclude}`);\n      }\n      const indentation = getIndentation(content, coreInclude.index);\n      const lines = getMissingMixinLines(namespace, mixins, indentation);\n      content = insertLinesAfterMatch(content, coreInclude, lines);\n    }\n  }\n\n  return restoreComments(content, placeholders);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {extname} from '@angular-devkit/core';\nimport {SchematicContext} from '@angular-devkit/schematics';\nimport {DevkitMigration, ResolvedResource, TargetVersion} from '@angular/cdk/schematics';\nimport {addThemeBaseMixins, checkThemeBaseMixins} from './migration';\n\n/** Adds an @include for theme base mixins that aren't already included by the app. */\nexport class ThemeBaseMigration extends DevkitMigration<null> {\n  /** Number of files that have been migrated. */\n  static migratedFileCount = 0;\n\n  /** All base mixins that we have found an existing @include for. */\n  static foundBaseMixins = new Set<string>();\n\n  /** All base mixins that appear to be missing an @include. */\n  static missingBaseMixins = new Set<string>();\n\n  /** Whether to run this migration. */\n  enabled = this.targetVersion === TargetVersion.V17;\n\n  /**\n   * All Sass stylesheets visited. (We save a record, so we can go back through them in the\n   * `postAnalysis` phase).\n   */\n  visitedSassStylesheets: ResolvedResource[] = [];\n\n  /**\n   * Visit each stylesheet, noting which base mixins are accounted for (because the user is calling\n   * `mat.<component>-theme()`), and which ones are missing (because the user is calling one of the\n   * theme-partial mixins: `mat.<component-color>()`, `mat.<component>-typography()`,\n   * or `mat.<component>-density()`.\n   *\n   * We don't make any modifications at this point. Instead, the results of visiting each stylesheet\n   * are aggregated into a static variable which is used to determine which mixins to add in\n   * `postAnalysis` phase.\n   */\n  override visitStylesheet(stylesheet: ResolvedResource): void {\n    if (extname(stylesheet.filePath) === '.scss') {\n      this.visitedSassStylesheets.push(stylesheet);\n\n      const content = stylesheet.content;\n      const {found, missing} = checkThemeBaseMixins(content);\n      for (const mixin of found) {\n        ThemeBaseMigration.foundBaseMixins.add(mixin);\n        ThemeBaseMigration.missingBaseMixins.delete(mixin);\n      }\n      for (const mixin of missing) {\n        if (!ThemeBaseMigration.foundBaseMixins.has(mixin)) {\n          ThemeBaseMigration.missingBaseMixins.add(mixin);\n        }\n      }\n    }\n  }\n\n  /**\n   * Perform the necessary updates detected while visiting the stylesheets. The\n   * `mat.<component>-base()` mixins behave similarly to `mat.core()`, in that they needed to be\n   * included once globally. So we locate calls to `mat.core()` and add the missing mixins\n   * identified by earlier at these locations.\n   */\n  override postAnalysis() {\n    // If we're not missing any mixins, there's nothing to migrate.\n    if (ThemeBaseMigration.missingBaseMixins.size === 0) {\n      return;\n    }\n    // If we have all-component-bases, we don't need any others and there is nothing to migrate.\n    if (ThemeBaseMigration.foundBaseMixins.has('all-component-bases')) {\n      return;\n    }\n    // If we're missing all-component-bases, we just need to add it, not the individual mixins.\n    if (ThemeBaseMigration.missingBaseMixins.has('all-component-bases')) {\n      ThemeBaseMigration.missingBaseMixins = new Set(['all-component-bases']);\n    }\n    for (const stylesheet of this.visitedSassStylesheets) {\n      const content = stylesheet.content;\n      const migratedContent = content\n        ? addThemeBaseMixins(content, ThemeBaseMigration.missingBaseMixins)\n        : content;\n\n      if (migratedContent && migratedContent !== content) {\n        this.fileSystem\n          .edit(stylesheet.filePath)\n          .remove(0, stylesheet.content.length)\n          .insertLeft(0, migratedContent);\n        ThemeBaseMigration.migratedFileCount++;\n      }\n    }\n    if (ThemeBaseMigration.migratedFileCount === 0) {\n      const mixinsText = [...ThemeBaseMigration.missingBaseMixins]\n        .sort()\n        .map(m => `mat.${m}($theme)`)\n        .join('\\n');\n      this.failures.push({\n        filePath: this.context.tree.root.path,\n        message:\n          `The following mixins could not be automatically added, please add them manually` +\n          ` if needed:\\n${mixinsText}`,\n      });\n    }\n  }\n\n  /** Logs out the number of migrated files at the end of the migration. */\n  static override globalPostMigration(\n    _tree: unknown,\n    _targetVersion: TargetVersion,\n    context: SchematicContext,\n  ): void {\n    const fileCount = ThemeBaseMigration.migratedFileCount;\n    const mixinCount = ThemeBaseMigration.missingBaseMixins.size;\n\n    if (fileCount > 0 && mixinCount > 0) {\n      const fileCountText = fileCount === 1 ? '1 file' : `${fileCount} files`;\n      const mixinCountText =\n        mixinCount === 1 ? '1 theme base mixin' : `${mixinCount} theme base mixins`;\n      context.logger.info(\n        `Added ${mixinCountText} to ${fileCountText}.` +\n          ' Please search for, and address, any \"TODO(v17)\" comments.',\n      );\n    }\n\n    // Reset to avoid leaking between tests.\n    ThemeBaseMigration.migratedFileCount = 0;\n    ThemeBaseMigration.missingBaseMixins = new Set();\n    ThemeBaseMigration.foundBaseMixins = new Set();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Rule, SchematicContext} from '@angular-devkit/schematics';\nimport {\n  createMigrationSchematicRule,\n  NullableDevkitMigration,\n  TargetVersion,\n} from '@angular/cdk/schematics';\n\nimport {legacyImportsError} from './migrations/legacy-imports-error';\nimport {materialUpgradeData} from './upgrade-data';\nimport {ThemeBaseMigration} from './migrations/theme-base-v17';\n\nconst materialMigrations: NullableDevkitMigration[] = [ThemeBaseMigration];\n\n/** Entry point for the migration schematics with target of Angular Material v17 */\nexport function updateToV17(): Rule {\n  // We pass the v17 migration rule as a callback, instead of using `chain()`, because the\n  // legacy imports error only logs an error message, it doesn't actually interrupt the migration\n  // process and we don't want to execute migrations if there are leftover legacy imports.\n  return legacyImportsError(\n    createMigrationSchematicRule(\n      TargetVersion.V17,\n      materialMigrations,\n      materialUpgradeData,\n      onMigrationComplete,\n    ),\n  );\n}\n\n/** Function that will be called when the migration completed. */\nfunction onMigrationComplete(\n  context: SchematicContext,\n  targetVersion: TargetVersion,\n  hasFailures: boolean,\n) {\n  context.logger.info('');\n  context.logger.info(`  ✓  Updated Angular Material to ${targetVersion}`);\n  context.logger.info('');\n\n  if (hasFailures) {\n    context.logger.warn(\n      '  ⚠  Some issues were detected but could not be fixed automatically. Please check the ' +\n        'output above and fix these issues manually.',\n    );\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA,yCAAAA,SAAA;AAeA,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,KAAC,SAAU,SAAS;AAChB,UAAI,OAAO,OAAO,WAAW,WAAW,SAAS,OAAO,SAAS,WAAW,OAAO,OAAO,SAAS,WAAW,OAAO,CAAC;AACtH,UAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AAC5C,eAAO,SAAS,CAAC,SAAS,GAAG,SAAUC,UAAS;AAAE,kBAAQ,eAAe,MAAM,eAAeA,QAAO,CAAC,CAAC;AAAA,QAAG,CAAC;AAAA,MAC/G,WACS,OAAOD,YAAW,YAAY,OAAOA,QAAO,YAAY,UAAU;AACvE,gBAAQ,eAAe,MAAM,eAAeA,QAAO,OAAO,CAAC,CAAC;AAAA,MAChE,OACK;AACD,gBAAQ,eAAe,IAAI,CAAC;AAAA,MAChC;AACA,eAAS,eAAeC,UAAS,UAAU;AACvC,YAAIA,aAAY,MAAM;AAClB,cAAI,OAAO,OAAO,WAAW,YAAY;AACrC,mBAAO,eAAeA,UAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAAA,UAChE,OACK;AACD,YAAAA,SAAQ,aAAa;AAAA,UACzB;AAAA,QACJ;AACA,eAAO,SAAU,IAAI,GAAG;AAAE,iBAAOA,SAAQ,MAAM,WAAW,SAAS,IAAI,CAAC,IAAI;AAAA,QAAG;AAAA,MACnF;AAAA,IACJ,GACC,SAAU,UAAU;AACjB,UAAI,gBAAgB,OAAO,kBACtB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAU,GAAG,GAAG;AAAE,UAAE,YAAY;AAAA,MAAG,KAC1E,SAAU,GAAG,GAAG;AAAE,iBAAS,KAAK;AAAG,cAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAAG,cAAE,KAAK,EAAE;AAAA,MAAI;AAEpG,kBAAY,SAAU,GAAG,GAAG;AACxB,YAAI,OAAO,MAAM,cAAc,MAAM;AACjC,gBAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,sBAAc,GAAG,CAAC;AAClB,iBAAS,KAAK;AAAE,eAAK,cAAc;AAAA,QAAG;AACtC,UAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AAAA,MACtF;AAEA,iBAAW,OAAO,UAAU,SAAU,GAAG;AACrC,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU;AACd,mBAAS,KAAK;AAAG,gBAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAAG,gBAAE,KAAK,EAAE;AAAA,QAC9E;AACA,eAAO;AAAA,MACX;AAEA,eAAS,SAAU,GAAG,GAAG;AACrB,YAAI,IAAI,CAAC;AACT,iBAAS,KAAK;AAAG,cAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI;AAC9E,cAAE,KAAK,EAAE;AACb,YAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,mBAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AACpE,gBAAI,EAAE,QAAQ,EAAE,EAAE,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,EAAE;AACzE,gBAAE,EAAE,MAAM,EAAE,EAAE;AAAA,UACtB;AACJ,eAAO;AAAA,MACX;AAEA,mBAAa,SAAU,YAAY,QAAQ,KAAK,MAAM;AAClD,YAAI,IAAI,UAAU,QAAQ,IAAI,IAAI,IAAI,SAAS,SAAS,OAAO,OAAO,OAAO,yBAAyB,QAAQ,GAAG,IAAI,MAAM;AAC3H,YAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,aAAa;AAAY,cAAI,QAAQ,SAAS,YAAY,QAAQ,KAAK,IAAI;AAAA;AACxH,mBAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG;AAAK,gBAAI,IAAI,WAAW;AAAI,mBAAK,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,IAAI,EAAE,QAAQ,KAAK,CAAC,IAAI,EAAE,QAAQ,GAAG,MAAM;AAChJ,eAAO,IAAI,KAAK,KAAK,OAAO,eAAe,QAAQ,KAAK,CAAC,GAAG;AAAA,MAChE;AAEA,gBAAU,SAAU,YAAY,WAAW;AACvC,eAAO,SAAU,QAAQ,KAAK;AAAE,oBAAU,QAAQ,KAAK,UAAU;AAAA,QAAG;AAAA,MACxE;AAEA,mBAAa,SAAU,aAAa,eAAe;AAC/C,YAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,aAAa;AAAY,iBAAO,QAAQ,SAAS,aAAa,aAAa;AAAA,MACjI;AAEA,kBAAY,SAAU,SAAS,YAAY,GAAG,WAAW;AACrD,iBAAS,MAAM,OAAO;AAAE,iBAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,oBAAQ,KAAK;AAAA,UAAG,CAAC;AAAA,QAAG;AAC3G,eAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,mBAAS,UAAU,OAAO;AAAE,gBAAI;AAAE,mBAAK,UAAU,KAAK,KAAK,CAAC;AAAA,YAAG,SAAS,GAAP;AAAY,qBAAO,CAAC;AAAA,YAAG;AAAA,UAAE;AAC1F,mBAAS,SAAS,OAAO;AAAE,gBAAI;AAAE,mBAAK,UAAU,SAAS,KAAK,CAAC;AAAA,YAAG,SAAS,GAAP;AAAY,qBAAO,CAAC;AAAA,YAAG;AAAA,UAAE;AAC7F,mBAAS,KAAK,QAAQ;AAAE,mBAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,UAAG;AAC7G,gBAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,QACxE,CAAC;AAAA,MACL;AAEA,oBAAc,SAAU,SAAS,MAAM;AACnC,YAAI,IAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,cAAI,EAAE,KAAK;AAAG,kBAAM,EAAE;AAAI,iBAAO,EAAE;AAAA,QAAI,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG;AAC/G,eAAO,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,UAAU,KAAK,CAAC,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,OAAO,YAAY,WAAW;AAAE,iBAAO;AAAA,QAAM,IAAI;AACvJ,iBAAS,KAAK,GAAG;AAAE,iBAAO,SAAU,GAAG;AAAE,mBAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,UAAG;AAAA,QAAG;AACjE,iBAAS,KAAK,IAAI;AACd,cAAI;AAAG,kBAAM,IAAI,UAAU,iCAAiC;AAC5D,iBAAO;AAAG,gBAAI;AACV,kBAAI,IAAI,GAAG,MAAM,IAAI,GAAG,KAAK,IAAI,EAAE,YAAY,GAAG,KAAK,EAAE,cAAc,IAAI,EAAE,cAAc,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,EAAE,GAAG;AAAM,uBAAO;AAC3J,kBAAI,IAAI,GAAG;AAAG,qBAAK,CAAC,GAAG,KAAK,GAAG,EAAE,KAAK;AACtC,sBAAQ,GAAG,IAAI;AAAA,gBACX,KAAK;AAAA,gBAAG,KAAK;AAAG,sBAAI;AAAI;AAAA,gBACxB,KAAK;AAAG,oBAAE;AAAS,yBAAO,EAAE,OAAO,GAAG,IAAI,MAAM,MAAM;AAAA,gBACtD,KAAK;AAAG,oBAAE;AAAS,sBAAI,GAAG;AAAI,uBAAK,CAAC,CAAC;AAAG;AAAA,gBACxC,KAAK;AAAG,uBAAK,EAAE,IAAI,IAAI;AAAG,oBAAE,KAAK,IAAI;AAAG;AAAA,gBACxC;AACI,sBAAI,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,QAAQ,GAAG,OAAO,KAAK,GAAG,OAAO,IAAI;AAAE,wBAAI;AAAG;AAAA,kBAAU;AAC3G,sBAAI,GAAG,OAAO,MAAM,CAAC,KAAM,GAAG,KAAK,EAAE,MAAM,GAAG,KAAK,EAAE,KAAM;AAAE,sBAAE,QAAQ,GAAG;AAAI;AAAA,kBAAO;AACrF,sBAAI,GAAG,OAAO,KAAK,EAAE,QAAQ,EAAE,IAAI;AAAE,sBAAE,QAAQ,EAAE;AAAI,wBAAI;AAAI;AAAA,kBAAO;AACpE,sBAAI,KAAK,EAAE,QAAQ,EAAE,IAAI;AAAE,sBAAE,QAAQ,EAAE;AAAI,sBAAE,IAAI,KAAK,EAAE;AAAG;AAAA,kBAAO;AAClE,sBAAI,EAAE;AAAI,sBAAE,IAAI,IAAI;AACpB,oBAAE,KAAK,IAAI;AAAG;AAAA,cACtB;AACA,mBAAK,KAAK,KAAK,SAAS,CAAC;AAAA,YAC7B,SAAS,GAAP;AAAY,mBAAK,CAAC,GAAG,CAAC;AAAG,kBAAI;AAAA,YAAG,UAAE;AAAU,kBAAI,IAAI;AAAA,YAAG;AACzD,cAAI,GAAG,KAAK;AAAG,kBAAM,GAAG;AAAI,iBAAO,EAAE,OAAO,GAAG,KAAK,GAAG,KAAK,QAAQ,MAAM,KAAK;AAAA,QACnF;AAAA,MACJ;AAEA,qBAAe,SAAS,GAAG,GAAG;AAC1B,iBAAS,KAAK;AAAG,cAAI,MAAM,aAAa,CAAC,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAAG,4BAAgB,GAAG,GAAG,CAAC;AAAA,MAChH;AAEA,wBAAkB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AACrD,YAAI,OAAO;AAAW,eAAK;AAC3B,eAAO,eAAe,GAAG,IAAI,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,iBAAO,EAAE;AAAA,QAAI,EAAE,CAAC;AAAA,MACvF,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,YAAI,OAAO;AAAW,eAAK;AAC3B,UAAE,MAAM,EAAE;AAAA,MACd;AAEA,iBAAW,SAAU,GAAG;AACpB,YAAI,IAAI,OAAO,WAAW,cAAc,OAAO,UAAU,IAAI,KAAK,EAAE,IAAI,IAAI;AAC5E,YAAI;AAAG,iBAAO,EAAE,KAAK,CAAC;AACtB,YAAI,KAAK,OAAO,EAAE,WAAW;AAAU,iBAAO;AAAA,YAC1C,MAAM,WAAY;AACd,kBAAI,KAAK,KAAK,EAAE;AAAQ,oBAAI;AAC5B,qBAAO,EAAE,OAAO,KAAK,EAAE,MAAM,MAAM,CAAC,EAAE;AAAA,YAC1C;AAAA,UACJ;AACA,cAAM,IAAI,UAAU,IAAI,4BAA4B,iCAAiC;AAAA,MACzF;AAEA,eAAS,SAAU,GAAG,GAAG;AACrB,YAAI,IAAI,OAAO,WAAW,cAAc,EAAE,OAAO;AACjD,YAAI,CAAC;AAAG,iBAAO;AACf,YAAI,IAAI,EAAE,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG;AAC/B,YAAI;AACA,kBAAQ,MAAM,UAAU,MAAM,MAAM,EAAE,IAAI,EAAE,KAAK,GAAG;AAAM,eAAG,KAAK,EAAE,KAAK;AAAA,QAC7E,SACO,OAAP;AAAgB,cAAI,EAAE,MAAa;AAAA,QAAG,UACtC;AACI,cAAI;AACA,gBAAI,KAAK,CAAC,EAAE,SAAS,IAAI,EAAE;AAAY,gBAAE,KAAK,CAAC;AAAA,UACnD,UACA;AAAU,gBAAI;AAAG,oBAAM,EAAE;AAAA,UAAO;AAAA,QACpC;AACA,eAAO;AAAA,MACX;AAGA,iBAAW,WAAY;AACnB,iBAAS,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ;AAC3C,eAAK,GAAG,OAAO,OAAO,UAAU,EAAE,CAAC;AACvC,eAAO;AAAA,MACX;AAGA,uBAAiB,WAAY;AACzB,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK,UAAU,QAAQ,IAAI,IAAI;AAAK,eAAK,UAAU,GAAG;AAC7E,iBAAS,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI;AACzC,mBAAS,IAAI,UAAU,IAAI,IAAI,GAAG,KAAK,EAAE,QAAQ,IAAI,IAAI,KAAK;AAC1D,cAAE,KAAK,EAAE;AACjB,eAAO;AAAA,MACX;AAEA,sBAAgB,SAAU,IAAI,MAAM,MAAM;AACtC,YAAI,QAAQ,UAAU,WAAW;AAAG,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,IAAI,GAAG,KAAK;AACjF,gBAAI,MAAM,EAAE,KAAK,OAAO;AACpB,kBAAI,CAAC;AAAI,qBAAK,MAAM,UAAU,MAAM,KAAK,MAAM,GAAG,CAAC;AACnD,iBAAG,KAAK,KAAK;AAAA,YACjB;AAAA,UACJ;AACA,eAAO,GAAG,OAAO,MAAM,MAAM,UAAU,MAAM,KAAK,IAAI,CAAC;AAAA,MAC3D;AAEA,gBAAU,SAAU,GAAG;AACnB,eAAO,gBAAgB,WAAW,KAAK,IAAI,GAAG,QAAQ,IAAI,QAAQ,CAAC;AAAA,MACvE;AAEA,yBAAmB,SAAU,SAAS,YAAY,WAAW;AACzD,YAAI,CAAC,OAAO;AAAe,gBAAM,IAAI,UAAU,sCAAsC;AACrF,YAAI,IAAI,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC;AAC5D,eAAO,IAAI,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ,GAAG,EAAE,OAAO,iBAAiB,WAAY;AAAE,iBAAO;AAAA,QAAM,GAAG;AACpH,iBAAS,KAAK,GAAG;AAAE,cAAI,EAAE;AAAI,cAAE,KAAK,SAAU,GAAG;AAAE,qBAAO,IAAI,QAAQ,SAAU,GAAG,GAAG;AAAE,kBAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,IAAI,KAAK,OAAO,GAAG,CAAC;AAAA,cAAG,CAAC;AAAA,YAAG;AAAA,QAAG;AACzI,iBAAS,OAAO,GAAG,GAAG;AAAE,cAAI;AAAE,iBAAK,EAAE,GAAG,CAAC,CAAC;AAAA,UAAG,SAAS,GAAP;AAAY,mBAAO,EAAE,GAAG,IAAI,CAAC;AAAA,UAAG;AAAA,QAAE;AACjF,iBAAS,KAAK,GAAG;AAAE,YAAE,iBAAiB,UAAU,QAAQ,QAAQ,EAAE,MAAM,CAAC,EAAE,KAAK,SAAS,MAAM,IAAI,OAAO,EAAE,GAAG,IAAI,CAAC;AAAA,QAAI;AACxH,iBAAS,QAAQ,OAAO;AAAE,iBAAO,QAAQ,KAAK;AAAA,QAAG;AACjD,iBAAS,OAAO,OAAO;AAAE,iBAAO,SAAS,KAAK;AAAA,QAAG;AACjD,iBAAS,OAAO,GAAG,GAAG;AAAE,cAAI,EAAE,CAAC,GAAG,EAAE,MAAM,GAAG,EAAE;AAAQ,mBAAO,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE;AAAA,QAAG;AAAA,MACrF;AAEA,yBAAmB,SAAU,GAAG;AAC5B,YAAI,GAAG;AACP,eAAO,IAAI,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK,SAAS,SAAU,GAAG;AAAE,gBAAM;AAAA,QAAG,CAAC,GAAG,KAAK,QAAQ,GAAG,EAAE,OAAO,YAAY,WAAY;AAAE,iBAAO;AAAA,QAAM,GAAG;AAC1I,iBAAS,KAAK,GAAG,GAAG;AAAE,YAAE,KAAK,EAAE,KAAK,SAAU,GAAG;AAAE,oBAAQ,IAAI,CAAC,KAAK,EAAE,OAAO,QAAQ,EAAE,GAAG,CAAC,CAAC,GAAG,MAAM,MAAM,SAAS,IAAI,IAAI,EAAE,CAAC,IAAI;AAAA,UAAG,IAAI;AAAA,QAAG;AAAA,MAClJ;AAEA,sBAAgB,SAAU,GAAG;AACzB,YAAI,CAAC,OAAO;AAAe,gBAAM,IAAI,UAAU,sCAAsC;AACrF,YAAI,IAAI,EAAE,OAAO,gBAAgB;AACjC,eAAO,IAAI,EAAE,KAAK,CAAC,KAAK,IAAI,OAAO,aAAa,aAAa,SAAS,CAAC,IAAI,EAAE,OAAO,UAAU,GAAG,IAAI,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ,GAAG,EAAE,OAAO,iBAAiB,WAAY;AAAE,iBAAO;AAAA,QAAM,GAAG;AAC9M,iBAAS,KAAK,GAAG;AAAE,YAAE,KAAK,EAAE,MAAM,SAAU,GAAG;AAAE,mBAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAAE,kBAAI,EAAE,GAAG,CAAC,GAAG,OAAO,SAAS,QAAQ,EAAE,MAAM,EAAE,KAAK;AAAA,YAAG,CAAC;AAAA,UAAG;AAAA,QAAG;AAC/J,iBAAS,OAAO,SAAS,QAAQ,GAAG,GAAG;AAAE,kBAAQ,QAAQ,CAAC,EAAE,KAAK,SAASC,IAAG;AAAE,oBAAQ,EAAE,OAAOA,IAAG,MAAM,EAAE,CAAC;AAAA,UAAG,GAAG,MAAM;AAAA,QAAG;AAAA,MAC/H;AAEA,6BAAuB,SAAU,QAAQ,KAAK;AAC1C,YAAI,OAAO,gBAAgB;AAAE,iBAAO,eAAe,QAAQ,OAAO,EAAE,OAAO,IAAI,CAAC;AAAA,QAAG,OAAO;AAAE,iBAAO,MAAM;AAAA,QAAK;AAC9G,eAAO;AAAA,MACX;AAEA,UAAI,qBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG;AACrD,eAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,MACtE,IAAK,SAAS,GAAG,GAAG;AAChB,UAAE,aAAa;AAAA,MACnB;AAEA,qBAAe,SAAU,KAAK;AAC1B,YAAI,OAAO,IAAI;AAAY,iBAAO;AAClC,YAAI,SAAS,CAAC;AACd,YAAI,OAAO;AAAM,mBAAS,KAAK;AAAK,gBAAI,MAAM,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC;AAAG,8BAAgB,QAAQ,KAAK,CAAC;AAAA;AACvI,2BAAmB,QAAQ,GAAG;AAC9B,eAAO;AAAA,MACX;AAEA,wBAAkB,SAAU,KAAK;AAC7B,eAAQ,OAAO,IAAI,aAAc,MAAM,EAAE,WAAW,IAAI;AAAA,MAC5D;AAEA,+BAAyB,SAAU,UAAU,OAAO,MAAM,GAAG;AACzD,YAAI,SAAS,OAAO,CAAC;AAAG,gBAAM,IAAI,UAAU,+CAA+C;AAC3F,YAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,QAAQ;AAAG,gBAAM,IAAI,UAAU,0EAA0E;AACjL,eAAO,SAAS,MAAM,IAAI,SAAS,MAAM,EAAE,KAAK,QAAQ,IAAI,IAAI,EAAE,QAAQ,MAAM,IAAI,QAAQ;AAAA,MAChG;AAEA,+BAAyB,SAAU,UAAU,OAAO,OAAO,MAAM,GAAG;AAChE,YAAI,SAAS;AAAK,gBAAM,IAAI,UAAU,gCAAgC;AACtE,YAAI,SAAS,OAAO,CAAC;AAAG,gBAAM,IAAI,UAAU,+CAA+C;AAC3F,YAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,QAAQ;AAAG,gBAAM,IAAI,UAAU,yEAAyE;AAChL,eAAQ,SAAS,MAAM,EAAE,KAAK,UAAU,KAAK,IAAI,IAAI,EAAE,QAAQ,QAAQ,MAAM,IAAI,UAAU,KAAK,GAAI;AAAA,MACxG;AAEA,eAAS,aAAa,SAAS;AAC/B,eAAS,YAAY,QAAQ;AAC7B,eAAS,UAAU,MAAM;AACzB,eAAS,cAAc,UAAU;AACjC,eAAS,WAAW,OAAO;AAC3B,eAAS,cAAc,UAAU;AACjC,eAAS,aAAa,SAAS;AAC/B,eAAS,eAAe,WAAW;AACnC,eAAS,gBAAgB,YAAY;AACrC,eAAS,mBAAmB,eAAe;AAC3C,eAAS,YAAY,QAAQ;AAC7B,eAAS,UAAU,MAAM;AACzB,eAAS,YAAY,QAAQ;AAC7B,eAAS,kBAAkB,cAAc;AACzC,eAAS,iBAAiB,aAAa;AACvC,eAAS,WAAW,OAAO;AAC3B,eAAS,oBAAoB,gBAAgB;AAC7C,eAAS,oBAAoB,gBAAgB;AAC7C,eAAS,iBAAiB,aAAa;AACvC,eAAS,wBAAwB,oBAAoB;AACrD,eAAS,gBAAgB,YAAY;AACrC,eAAS,mBAAmB,eAAe;AAC3C,eAAS,0BAA0B,sBAAsB;AACzD,eAAS,0BAA0B,sBAAsB;AAAA,IAC7D,CAAC;AAAA;AAAA;;;;;;;;;ACxSD,QAAA,UAAA,QAAA;AACA,QAAA,KAAA,QAAA,aAAA,QAAA,aAAA;AAGA,QAAM,uBAAuB;AAG7B,QAAM,qBAAqB;AAO3B,aAAgBC,oBAAmB,WAAe;AAChD,aAAO,CAAO,MAAY,YAA6B;AACrD,cAAM,0BAA0B,oBAAI,IAAG;AAEvC,aAAK,MAAM,UAAO;AAChB,cAAI,KAAK,SAAS,cAAc,KAAK,KAAK,SAAS,OAAO,KAAK,CAAC,KAAK,SAAS,KAAK,GAAG;AACpF;UACF;AAEA,gBAAM,UAAU,KAAK,SAAS,IAAI;AAGlC,cAAI,CAAC,QAAQ,SAAS,oBAAoB,GAAG;AAC3C;UACF;AAEA,gBAAM,aAAa,GAAG,iBAAiB,MAAM,SAAS,GAAG,aAAa,MAAM;AAG5E,qBAAW,aAAa,WAAW,YAAY;AAC7C,gBAAI,CAAC,GAAG,oBAAoB,SAAS,KAAK,CAAC,GAAG,oBAAoB,SAAS,GAAG;AAC5E;YACF;AAEA,gBACE,UAAU,mBACV,GAAG,oBAAoB,UAAU,eAAe,KAChD,UAAU,gBAAgB,KAAK,WAAW,oBAAoB,GAC9D;AACA,sCAAwB,IAAI,IAAI;YAClC;UACF;QACF,CAAC;AAGD,YAAI,wBAAwB,SAAS,GAAG;AACtC,iBAAO;QACT;AAKA,YAAI,KAAK,OAAO,cAAc,GAAG;AAC/B,cAAI,cAA0C;AAE9C,cAAI;AACF,0BAAc,KAAK,MAAM,KAAK,SAAS,cAAc,CAAC;UACxD,SAAQ,GAAN;UAAO;AAET,cAAI,gBAAgB,QAAQ,YAAY,iBAAiB;AACvD,wBAAY,gBAAgB,uBAAuB;AACnD,iBAAK,UAAU,gBAAgB,KAAK,UAAU,aAAa,MAAM,CAAC,CAAC;AACnE,oBAAQ,QAAQ,IAAI,QAAA,uBAAsB,CAAE;UAC9C;QACF;AAEA,gBAAQ,OAAO,MAAM,mBAAmB,uBAAuB,CAAC;AAChE;MACF;IACF;AA3DA,YAAA,qBAAAA;AA6DA,aAAS,mBAAmB,yBAAoC;AAC9D,YAAM,QAAQ,MAAM,KAAK,yBAAyB,UAAQ,QAAQ,IAAI;AACtE,YAAM,eACJ,MAAM,SAAS,qBACX;QACE,GAAG,MAAM,MAAM,GAAG,kBAAkB;QACpC,GAAG,MAAM,SAAS;QAClB,4BAA4B;QAC5B,KAAK,IAAI,IACX,MAAM,KAAK,IAAI;AAErB,aACE;;;;;;;;EAK2D;;IAE/D;;;;;;;;;;AC7Fa,YAAA,qBAAmE,CAAA;;;;;;;;;;ACAnE,YAAA,aAAmD,CAAA;;;;;;;;;;ACKnD,YAAA,oBAAkE,CAAA;;;;;;;;;;ACclE,YAAA,eAAwD,CAAA;;;;;;;;;;ACnBxD,YAAA,mBAA+D,CAAA;;;;;;;;;;ACA/D,YAAA,aAAmD,CAAA;;;;;;;;;;ACAnD,YAAA,mBAA0D,CAAA;;;;;;;;;;ACA1D,YAAA,cAAqD,CAAA;;;;;;;;;;ACArD,YAAA,gBAAyD,CAAA;;;;;;;;;;ACAzD,YAAA,gBAA0D,CAAA;;;;;;;;;;ACFvE,YAAA,aAAA,+BAAA,OAAA;AACA,YAAA,aAAA,uBAAA,OAAA;AACA,YAAA,aAAA,8BAAA,OAAA;AACA,YAAA,aAAA,yBAAA,OAAA;AACA,YAAA,aAAA,6BAAA,OAAA;AACA,YAAA,aAAA,uBAAA,OAAA;AACA,YAAA,aAAA,8BAAA,OAAA;AACA,YAAA,aAAA,wBAAA,OAAA;AACA,YAAA,aAAA,0BAAA,OAAA;AACA,YAAA,aAAA,0BAAA,OAAA;;;;;;;;;;ACRA,QAAA,SAAA;AAca,YAAA,sBAAmC;MAC9C,oBAAA,OAAA;MACA,YAAA,OAAA;MACA,mBAAA,OAAA;MACA,cAAA,OAAA;MACA,kBAAA,OAAA;MACA,YAAA,OAAA;MACA,kBAAA,OAAA;MACA,aAAA,OAAA;MACA,eAAA,OAAA;MACA,eAAA,OAAA;;;;;;;;;;;ACxBF,QAAM,+BAA+B;;;;;;;;;;;;;;uFAenC,MAAM,IAAI;AAGZ,QAAM,mBAMA;MACJ;QACE,OAAO;QACP,OAAO;QACP,YAAY;QACZ,SAAS;QACT,MAAM;;MAER,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI,WAAS;QACb,OAAO,GAAG;QACV,OAAO,GAAG;QACV,YAAY,GAAG;QACf,SAAS,GAAG;QACZ,MAAM,GAAG;QACT;;AAIJ,QAAM,gBAAgB,oBAAI,IAAoB;MAC5C,CAAC,MAAM,IAAI;MACX,CAAC,MAAM,IAAI;KACZ;AAGD,QAAM,4BAA4B;AAGlC,QAAM,0BAA0B;AAMhC,aAAS,eAAe,SAAe;AACrC,YAAM,eAAuC,CAAA;AAC7C,UAAI,iBAAiB;AACrB,UAAI,CAAC,WAAW,UAAU,IAAI,YAAY,OAAO;AAEjD,aAAO,YAAY,MAAM,aAAa,IAAI;AACxC,cAAM,cAAc,4BAA4B,mBAAmB;AACnE,qBAAa,eAAe,QAAQ,MAAM,WAAW,UAAU;AAC/D,kBAAU,QAAQ,MAAM,GAAG,SAAS,IAAI,cAAc,QAAQ,MAAM,UAAU;AAC9E,SAAC,WAAW,UAAU,IAAI,YAAY,OAAO;MAC/C;AAEA,aAAO,EAAC,SAAS,aAAY;IAC/B;AAGA,aAAS,YAAY,SAAe;AAIlC,iBAAW;AAEX,iBAAW,CAAC,MAAM,KAAK,KAAK,cAAc,QAAO,GAAI;AACnD,cAAM,YAAY,QAAQ,QAAQ,IAAI;AAEtC,YAAI,YAAY,IAAI;AAClB,gBAAM,aAAa,QAAQ,QAAQ,OAAO,YAAY,CAAC;AACvD,iBAAO,aAAa,KAAK,CAAC,WAAW,aAAa,MAAM,MAAM,IAAI,CAAC,IAAI,EAAE;QAC3E;MACF;AAEA,aAAO,CAAC,IAAI,EAAE;IAChB;AAGA,aAAS,gBAAgB,SAAiB,cAAoC;AAC5E,aAAO,KAAK,YAAY,EAAE,QAAQ,SAAQ,UAAU,QAAQ,QAAQ,KAAK,aAAa,IAAI,CAAE;AAC5F,aAAO;IACT;AAGA,aAAS,aAAa,KAAW;AAC/B,aAAO,IAAI,QAAQ,8BAA8B,MAAM;IACzD;AAGA,aAAS,iCAAiC,YAAkB;AAC1D,YAAM,kBAAkB,KAAK,IAAI,WAAW,YAAY,GAAG,GAAG,WAAW,YAAY,GAAG,CAAC;AAEzF,UAAI,kBAAkB,IAAI;AACxB,cAAM,eAAe;AACrB,cAAM,UAAU,WAAW,QAAQ,cAAc,eAAe;AAGhE,YAAI,UAAU,IAAI;AAChB,iBAAO,WACJ,MAAM,UAAU,aAAa,MAAM,EACnC,MAAM,GAAG,EAAE,GACX,KAAI;QACT;AAGA,cAAM,iBAAiB,WAAW,YAAY,KAAK,eAAe;AAElE,YAAI,iBAAiB,IAAI;AACvB,gBAAM,WAAW,WACd,MAAM,iBAAiB,GAAG,eAAe,EAEzC,QAAQ,oCAAoC,EAAE;AAGjD,cAAI,aAAa,SAAS;AACxB,kBAAM,iBAAiB,WAAW,YAAY,KAAK,iBAAiB,CAAC;AAErE,gBAAI,iBAAiB,IAAI;AACvB,qBAAO,WAAW,MAAM,iBAAiB,GAAG,cAAc;YAC5D;UACF,OAAO;AACL,mBAAO;UACT;QACF;MACF;AAEA,YAAM,MAAM,4CAA4C,cAAc;IACxE;AAGA,aAAS,mBAAmB,SAAiB,MAAY;AACvD,YAAM,aAAa,oBAAI,IAAG;AAC1B,YAAM,UAAU,IAAI,OAAO,eAAe,aAAa,IAAI;GAAe,GAAG;AAC7E,UAAI,QAAgC;AAEpC,aAAQ,QAAQ,QAAQ,KAAK,OAAO,GAAI;AACtC,mBAAW,IAAI,iCAAiC,MAAM,EAAE,CAAC;MAC3D;AAEA,aAAO;IACT;AAGA,aAAS,cAAc,SAAiB,WAAmB,OAAa;AAItE,YAAM,SAAS;AACf,YAAM,UAAU,IAAI,OAClB,eAAe,aAAa,SAAS,OAAO,aAAa,KAAK,IAAI,UAClE,GAAG;AAEL,aAAO,CAAC,GAAG,QAAQ,SAAS,OAAO,CAAC;IACtC;AAGA,aAAS,kBAAkB,SAAiB,WAAmB,OAAa;AAC1E,aAAO,CAAC,CAAC,cAAc,SAAS,WAAW,KAAK,EAAE;IACpD;AAGA,aAAS,sBAAsB,SAAiB,OAAyB,OAAe;AACtF,YAAM,iBAAiB,MAAM,QAAS,MAAM,GAAG;AAC/C,aACE,QAAQ,UAAU,GAAG,cAAc,IACnC,MAAM,KAAK,IAAI,IACf,OACA,QAAQ,UAAU,cAAc;IAEpC;AAGA,aAAS,eAAe,SAAiB,OAAa;AACpD,UAAI,mBAAmB;AACvB,UAAI,iBAAiB;AACrB,eAAS,IAAI,OAAO,KAAK,GAAG,KAAK;AAC/B,YAAI,QAAQ,OAAO,MAAM;AACvB,6BAAmB,IAAI;AACvB;QACF;AACA,YAAI,CAAC,KAAK,KAAK,QAAQ,EAAE,GAAG;AAC1B,2BAAiB;QACnB;MACF;AACA,aAAO,QAAQ,MAAM,kBAAkB,cAAc;IACvD;AAGA,aAAS,qBAAqB,WAAmB,QAAqB,aAAmB;AACvF,aAAO;QACL,GAAG;QACH,GAAG,CAAC,GAAG,MAAM,EACV,KAAI,EACJ,IAAI,WAAS,YAAY,aAAa,gDAAgD;QACzF;QACA,IAAI,WAAS,cAAc,MAAM,UAAS,CAAE;IAChD;AAMA,aAAgB,qBAAqB,aAAmB;AAItD,YAAM,QAAQ,oBAAI,IAAG;AACrB,YAAM,UAAU,oBAAI,IAAG;AAGvB,YAAM,EAAC,QAAO,IAAI,eAAe,WAAW;AAC5C,YAAM,qBAAqB,mBAAmB,SAAS,mBAAmB;AAG1E,iBAAW,aAAa,oBAAoB;AAC1C,mBAAW,UAAU,kBAAkB;AAErC,cAAI,kBAAkB,SAAS,WAAW,OAAO,KAAK,GAAG;AACvD,kBAAM,IAAI,OAAO,IAAI;AACrB,oBAAQ,OAAO,OAAO,IAAI;AAC1B;UACF;AAGA,cAAI,CAAC,MAAM,IAAI,OAAO,IAAI,GAAG;AAC3B,gBACE,kBAAkB,SAAS,WAAW,OAAO,KAAK,KAClD,kBAAkB,SAAS,WAAW,OAAO,UAAU,KACvD,kBAAkB,SAAS,WAAW,OAAO,OAAO,GACpD;AACA,sBAAQ,IAAI,OAAO,IAAI;YACzB;UACF;QACF;MACF;AAEA,aAAO,EAAC,OAAO,QAAO;IACxB;AAnCA,YAAA,uBAAA;AAsCA,aAAgB,mBAAmB,aAAqB,QAAmB;AAEzE,UAAI,EAAC,SAAS,aAAY,IAAI,eAAe,WAAW;AACxD,YAAM,qBAAqB,mBAAmB,SAAS,mBAAmB;AAE1E,iBAAW,aAAa,oBAAoB;AAE1C,cAAM,eAAe,cAAc,SAAS,WAAW,MAAM,EAAE,QAAO;AACtE,mBAAW,eAAe,cAAc;AACtC,cAAI,YAAY,UAAU,QAAW;AACnC,kBAAM,MAAM,6CAA6C,aAAa;UACxE;AACA,gBAAM,cAAc,eAAe,SAAS,YAAY,KAAK;AAC7D,gBAAM,QAAQ,qBAAqB,WAAW,QAAQ,WAAW;AACjE,oBAAU,sBAAsB,SAAS,aAAa,KAAK;QAC7D;MACF;AAEA,aAAO,gBAAgB,SAAS,YAAY;IAC9C;AAnBA,YAAA,qBAAA;;;;;;;;;;ACrSA,QAAA,SAAA,QAAA;AAEA,QAAA,eAAA,QAAA;AACA,QAAA,cAAA;AAGA,QAAa,sBAAb,cAAwC,aAAA,gBAAqB;MAA7D,cAAA;;AAWE,aAAA,UAAU,KAAK,kBAAkB,aAAA,cAAc;AAM/C,aAAA,yBAA6C,CAAA;MAqG/C;MAzFW,gBAAgB,YAA4B;AACnD,aAAI,GAAA,OAAA,SAAQ,WAAW,QAAQ,MAAM,SAAS;AAC5C,eAAK,uBAAuB,KAAK,UAAU;AAE3C,gBAAM,UAAU,WAAW;AAC3B,gBAAM,EAAC,OAAO,QAAO,KAAI,GAAA,YAAA,sBAAqB,OAAO;AACrD,qBAAW,SAAS,OAAO;AACzB,gCAAmB,gBAAgB,IAAI,KAAK;AAC5C,gCAAmB,kBAAkB,OAAO,KAAK;UACnD;AACA,qBAAW,SAAS,SAAS;AAC3B,gBAAI,CAAC,oBAAmB,gBAAgB,IAAI,KAAK,GAAG;AAClD,kCAAmB,kBAAkB,IAAI,KAAK;YAChD;UACF;QACF;MACF;MAQS,eAAY;AAEnB,YAAI,oBAAmB,kBAAkB,SAAS,GAAG;AACnD;QACF;AAEA,YAAI,oBAAmB,gBAAgB,IAAI,qBAAqB,GAAG;AACjE;QACF;AAEA,YAAI,oBAAmB,kBAAkB,IAAI,qBAAqB,GAAG;AACnE,8BAAmB,oBAAoB,oBAAI,IAAI,CAAC,qBAAqB,CAAC;QACxE;AACA,mBAAW,cAAc,KAAK,wBAAwB;AACpD,gBAAM,UAAU,WAAW;AAC3B,gBAAM,kBAAkB,WACpB,GAAA,YAAA,oBAAmB,SAAS,oBAAmB,iBAAiB,IAChE;AAEJ,cAAI,mBAAmB,oBAAoB,SAAS;AAClD,iBAAK,WACF,KAAK,WAAW,QAAQ,EACxB,OAAO,GAAG,WAAW,QAAQ,MAAM,EACnC,WAAW,GAAG,eAAe;AAChC,gCAAmB;UACrB;QACF;AACA,YAAI,oBAAmB,sBAAsB,GAAG;AAC9C,gBAAM,aAAa,CAAC,GAAG,oBAAmB,iBAAiB,EACxD,KAAI,EACJ,IAAI,OAAK,OAAO,WAAW,EAC3B,KAAK,IAAI;AACZ,eAAK,SAAS,KAAK;YACjB,UAAU,KAAK,QAAQ,KAAK,KAAK;YACjC,SACE;EACgB;WACnB;QACH;MACF;MAGA,OAAgB,oBACd,OACA,gBACA,SAAyB;AAEzB,cAAM,YAAY,oBAAmB;AACrC,cAAM,aAAa,oBAAmB,kBAAkB;AAExD,YAAI,YAAY,KAAK,aAAa,GAAG;AACnC,gBAAM,gBAAgB,cAAc,IAAI,WAAW,GAAG;AACtD,gBAAM,iBACJ,eAAe,IAAI,uBAAuB,GAAG;AAC/C,kBAAQ,OAAO,KACb,SAAS,qBAAqB,0EACgC;QAElE;AAGA,4BAAmB,oBAAoB;AACvC,4BAAmB,oBAAoB,oBAAI,IAAG;AAC9C,4BAAmB,kBAAkB,oBAAI,IAAG;MAC9C;;AArHF,QAAaC,sBAAb;;AAES,0BAAA,oBAAoB;IAAE;;AAGtB,0BAAA,kBAAkB,oBAAI,IAAG;IAAW;;AAGpC,0BAAA,oBAAoB,oBAAI,IAAG;IAAW;AAR/C,YAAA,qBAAAA;;;;;ACdA;;;;;AASA,wBAIO;AAEP,kCAAiC;AACjC,0BAAkC;AAClC,4BAAiC;AAEjC,IAAM,qBAAgD,CAAC,wCAAkB;AAGnE,SAAU,cAAW;AAIzB,aAAO,oDACL,gDACE,gCAAc,KACd,oBACA,yCACA,mBAAmB,CACpB;AAEL;AAGA,SAAS,oBACP,SACA,eACA,aAAoB;AAEpB,UAAQ,OAAO,KAAK,EAAE;AACtB,UAAQ,OAAO,KAAK,yCAAoC,eAAe;AACvE,UAAQ,OAAO,KAAK,EAAE;AAEtB,MAAI,aAAa;AACf,YAAQ,OAAO,KACb,wIAC+C;EAEnD;AACF;", "names": ["module", "exports", "v", "legacyImportsError", "ThemeBaseMigration"]}