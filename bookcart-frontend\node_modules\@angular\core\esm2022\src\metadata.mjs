/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * This indirection is needed to free up Component, etc symbols in the public API
 * to be used by the decorator versions of these annotations.
 */
export { Attribute } from './di/metadata_attr';
export { ContentChild, ContentChildren, Query, ViewChild, ViewChildren } from './metadata/di';
export { Component, Directive, HostBinding, HostListener, Input, Output, Pipe } from './metadata/directives';
export { NgModule } from './metadata/ng_module';
export { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from './metadata/schema';
export { ViewEncapsulation } from './metadata/view';
//# sourceMappingURL=data:application/json;base64,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