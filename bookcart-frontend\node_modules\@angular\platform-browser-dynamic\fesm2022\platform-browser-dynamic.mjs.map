{"version": 3, "file": "platform-browser-dynamic.mjs", "sources": ["../../../../../../packages/platform-browser-dynamic/src/compiler_factory.ts", "../../../../../../packages/platform-browser-dynamic/src/platform_core_dynamic.ts", "../../../../../../packages/platform-browser-dynamic/src/resource_loader/resource_loader_impl.ts", "../../../../../../packages/platform-browser-dynamic/src/platform_providers.ts", "../../../../../../packages/platform-browser-dynamic/src/resource_loader/resource_loader_cache.ts", "../../../../../../packages/platform-browser-dynamic/src/version.ts", "../../../../../../packages/platform-browser-dynamic/src/platform-browser-dynamic.ts", "../../../../../../packages/platform-browser-dynamic/public_api.ts", "../../../../../../packages/platform-browser-dynamic/index.ts", "../../../../../../packages/platform-browser-dynamic/platform-browser-dynamic.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {CompilerConfig} from '@angular/compiler';\nimport {Compiler, CompilerFactory, CompilerOptions, Injector, StaticProvider, ViewEncapsulation} from '@angular/core';\n\nexport const COMPILER_PROVIDERS =\n    <StaticProvider[]>[{provide: Compiler, useFactory: () => new Compiler()}];\n/**\n * @publicApi\n *\n * @deprecated\n * Ivy JIT mode doesn't require accessing this symbol.\n * See [JIT API changes due to ViewEngine deprecation](guide/deprecations#jit-api-changes) for\n * additional context.\n */\nexport class JitCompilerFactory implements CompilerFactory {\n  private _defaultOptions: CompilerOptions[];\n\n  /** @internal */\n  constructor(defaultOptions: CompilerOptions[]) {\n    const compilerOptions: CompilerOptions = {\n      defaultEncapsulation: ViewEncapsulation.Emulated,\n    };\n\n    this._defaultOptions = [compilerOptions, ...defaultOptions];\n  }\n\n  createCompiler(options: CompilerOptions[] = []): Compiler {\n    const opts = _mergeOptions(this._defaultOptions.concat(options));\n    const injector = Injector.create({\n      providers: [\n        COMPILER_PROVIDERS, {\n          provide: CompilerConfig,\n          useFactory: () => {\n            return new CompilerConfig({\n              defaultEncapsulation: opts.defaultEncapsulation,\n              preserveWhitespaces: opts.preserveWhitespaces,\n            });\n          },\n          deps: []\n        },\n        opts.providers!\n      ]\n    });\n    return injector.get(Compiler);\n  }\n}\n\nfunction _mergeOptions(optionsArr: CompilerOptions[]): CompilerOptions {\n  return {\n    defaultEncapsulation: _lastDefined(optionsArr.map(options => options.defaultEncapsulation)),\n    providers: _mergeArrays(optionsArr.map(options => options.providers!)),\n    preserveWhitespaces: _lastDefined(optionsArr.map(options => options.preserveWhitespaces)),\n  };\n}\n\nfunction _lastDefined<T>(args: T[]): T|undefined {\n  for (let i = args.length - 1; i >= 0; i--) {\n    if (args[i] !== undefined) {\n      return args[i];\n    }\n  }\n  return undefined;\n}\n\nfunction _mergeArrays(parts: any[][]): any[] {\n  const result: any[] = [];\n  parts.forEach((part) => part && result.push(...part));\n  return result;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {COMPILER_OPTIONS, CompilerFactory, createPlatformFactory, platformCore} from '@angular/core';\n\nimport {JitCompilerFactory} from './compiler_factory';\n\n/**\n * A platform that included corePlatform and the compiler.\n *\n * @publicApi\n */\nexport const platformCoreDynamic = createPlatformFactory(platformCore, 'coreDynamic', [\n  {provide: COMPILER_OPTIONS, useValue: {}, multi: true},\n  {provide: CompilerFactory, useClass: JitCompilerFactory, deps: [COMPILER_OPTIONS]},\n]);\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {ResourceLoader} from '@angular/compiler';\nimport {Injectable} from '@angular/core';\n\n\n@Injectable()\nexport class ResourceLoaderImpl extends ResourceLoader {\n  override get(url: string): Promise<string> {\n    let resolve: (result: any) => void;\n    let reject: (error: any) => void;\n    const promise = new Promise<string>((res, rej) => {\n      resolve = res;\n      reject = rej;\n    });\n    const xhr = new XMLHttpRequest();\n    xhr.open('GET', url, true);\n    xhr.responseType = 'text';\n\n    xhr.onload = function() {\n      const response = xhr.response;\n\n      let status = xhr.status;\n\n      // fix status code when it is 0 (0 status is undocumented).\n      // Occurs when accessing file resources or on Android 4.1 stock browser\n      // while retrieving files from application cache.\n      if (status === 0) {\n        status = response ? 200 : 0;\n      }\n\n      if (200 <= status && status <= 300) {\n        resolve(response);\n      } else {\n        reject(`Failed to load ${url}`);\n      }\n    };\n\n    xhr.onerror = function() {\n      reject(`Failed to load ${url}`);\n    };\n\n    xhr.send();\n    return promise;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵPLATFORM_BROWSER_ID as PLATFORM_BROWSER_ID} from '@angular/common';\nimport {ResourceLoader} from '@angular/compiler';\nimport {COMPILER_OPTIONS, PLATFORM_ID, StaticProvider} from '@angular/core';\n\nimport {ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS as INTERNAL_BROWSER_PLATFORM_PROVIDERS} from '@angular/platform-browser';\n\nimport {ResourceLoaderImpl} from './resource_loader/resource_loader_impl';\n\n/**\n * @publicApi\n */\nexport const INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS: StaticProvider[] = [\n  INTERNAL_BROWSER_PLATFORM_PROVIDERS,\n  {\n    provide: COMPILER_OPTIONS,\n    useValue: {providers: [{provide: ResourceLoader, useClass: ResourceLoaderImpl, deps: []}]},\n    multi: true\n  },\n  {provide: PLATFORM_ID, useValue: PLATFORM_BROWSER_ID},\n];\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ResourceLoader} from '@angular/compiler';\nimport {ɵglobal as global} from '@angular/core';\n\n/**\n * An implementation of ResourceLoader that uses a template cache to avoid doing an actual\n * ResourceLoader.\n *\n * The template cache needs to be built and loaded into window.$templateCache\n * via a separate mechanism.\n *\n * @publicApi\n *\n * @deprecated This was previously necessary in some cases to test AOT-compiled components with View\n *     Engine, but is no longer since Ivy.\n */\nexport class CachedResourceLoader extends ResourceLoader {\n  private _cache: {[url: string]: string};\n\n  constructor() {\n    super();\n    this._cache = (<any>global).$templateCache;\n    if (this._cache == null) {\n      throw new Error('CachedResourceLoader: Template cache was not found in $templateCache.');\n    }\n  }\n\n  override get(url: string): Promise<string> {\n    if (this._cache.hasOwnProperty(url)) {\n      return Promise.resolve(this._cache[url]);\n    } else {\n      return <Promise<any>>Promise.reject(\n          'CachedResourceLoader: Did not find cached template for ' + url);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser-dynamic package.\n */\n\nimport {Version} from '@angular/core';\n\n/**\n * @publicApi\n */\nexport const VERSION = new Version('17.3.12');\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ResourceLoader} from '@angular/compiler';\nimport {createPlatformFactory, Provider} from '@angular/core';\n\nimport {platformCoreDynamic} from './platform_core_dynamic';\nimport {INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS} from './platform_providers';\nimport {CachedResourceLoader} from './resource_loader/resource_loader_cache';\n\nexport * from './private_export';\nexport {VERSION} from './version';\nexport {JitCompilerFactory} from './compiler_factory';\n\n/**\n * @publicApi\n *\n * @deprecated This was previously necessary in some cases to test AOT-compiled components with View\n *     Engine, but is no longer since Ivy.\n\n */\nexport const RESOURCE_CACHE_PROVIDER: Provider[] =\n    [{provide: ResourceLoader, useClass: CachedResourceLoader, deps: []}];\n\n/**\n * @publicApi\n */\nexport const platformBrowserDynamic = createPlatformFactory(\n    platformCoreDynamic, 'browserDynamic', INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS);\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\nexport * from './src/platform-browser-dynamic';\n\n// This file only reexports content of the `src` folder. Keep it that way.\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// This file is not used to build this module. It is only used during editing\n// by the TypeScript language service and during build for verification. `ngc`\n// replaces this file with production index.ts when it rewrites private symbol\n// names.\n\nexport * from './public_api';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["INTERNAL_BROWSER_PLATFORM_PROVIDERS", "PLATFORM_BROWSER_ID", "global"], "mappings": ";;;;;;;;;;;;AAWO,MAAM,kBAAkB,GACT,CAAC,EAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,IAAI,QAAQ,EAAE,EAAC,CAAC,CAAC;AAC9E;;;;;;;AAOG;MACU,kBAAkB,CAAA;;AAI7B,IAAA,WAAA,CAAY,cAAiC,EAAA;AAC3C,QAAA,MAAM,eAAe,GAAoB;YACvC,oBAAoB,EAAE,iBAAiB,CAAC,QAAQ;SACjD,CAAC;QAEF,IAAI,CAAC,eAAe,GAAG,CAAC,eAAe,EAAE,GAAG,cAAc,CAAC,CAAC;KAC7D;IAED,cAAc,CAAC,UAA6B,EAAE,EAAA;AAC5C,QAAA,MAAM,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;AACjE,QAAA,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC;AAC/B,YAAA,SAAS,EAAE;AACT,gBAAA,kBAAkB,EAAE;AAClB,oBAAA,OAAO,EAAE,cAAc;oBACvB,UAAU,EAAE,MAAK;wBACf,OAAO,IAAI,cAAc,CAAC;4BACxB,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;4BAC/C,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;AAC9C,yBAAA,CAAC,CAAC;qBACJ;AACD,oBAAA,IAAI,EAAE,EAAE;AACT,iBAAA;AACD,gBAAA,IAAI,CAAC,SAAU;AAChB,aAAA;AACF,SAAA,CAAC,CAAC;AACH,QAAA,OAAO,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;KAC/B;AACF,CAAA;AAED,SAAS,aAAa,CAAC,UAA6B,EAAA;IAClD,OAAO;AACL,QAAA,oBAAoB,EAAE,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAC3F,QAAA,SAAS,EAAE,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,SAAU,CAAC,CAAC;AACtE,QAAA,mBAAmB,EAAE,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,mBAAmB,CAAC,CAAC;KAC1F,CAAC;AACJ,CAAC;AAED,SAAS,YAAY,CAAI,IAAS,EAAA;AAChC,IAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AACzC,QAAA,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;AACzB,YAAA,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;SAChB;KACF;AACD,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,YAAY,CAAC,KAAc,EAAA;IAClC,MAAM,MAAM,GAAU,EAAE,CAAC;AACzB,IAAA,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AACtD,IAAA,OAAO,MAAM,CAAC;AAChB;;AC/DA;;;;AAIG;MACU,mBAAmB,GAAG,qBAAqB,CAAC,YAAY,EAAE,aAAa,EAAE;IACpF,EAAC,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAC;AACtD,IAAA,EAAC,OAAO,EAAE,eAAe,EAAE,QAAQ,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAC,gBAAgB,CAAC,EAAC;AACnF,CAAA;;ACRK,MAAO,kBAAmB,SAAQ,cAAc,CAAA;AAC3C,IAAA,GAAG,CAAC,GAAW,EAAA;AACtB,QAAA,IAAI,OAA8B,CAAC;AACnC,QAAA,IAAI,MAA4B,CAAC;QACjC,MAAM,OAAO,GAAG,IAAI,OAAO,CAAS,CAAC,GAAG,EAAE,GAAG,KAAI;YAC/C,OAAO,GAAG,GAAG,CAAC;YACd,MAAM,GAAG,GAAG,CAAC;AACf,SAAC,CAAC,CAAC;AACH,QAAA,MAAM,GAAG,GAAG,IAAI,cAAc,EAAE,CAAC;QACjC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AAC3B,QAAA,GAAG,CAAC,YAAY,GAAG,MAAM,CAAC;QAE1B,GAAG,CAAC,MAAM,GAAG,YAAA;AACX,YAAA,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;AAE9B,YAAA,IAAI,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;;;;AAKxB,YAAA,IAAI,MAAM,KAAK,CAAC,EAAE;gBAChB,MAAM,GAAG,QAAQ,GAAG,GAAG,GAAG,CAAC,CAAC;aAC7B;YAED,IAAI,GAAG,IAAI,MAAM,IAAI,MAAM,IAAI,GAAG,EAAE;gBAClC,OAAO,CAAC,QAAQ,CAAC,CAAC;aACnB;iBAAM;AACL,gBAAA,MAAM,CAAC,CAAA,eAAA,EAAkB,GAAG,CAAA,CAAE,CAAC,CAAC;aACjC;AACH,SAAC,CAAC;QAEF,GAAG,CAAC,OAAO,GAAG,YAAA;AACZ,YAAA,MAAM,CAAC,CAAA,eAAA,EAAkB,GAAG,CAAA,CAAE,CAAC,CAAC;AAClC,SAAC,CAAC;QAEF,GAAG,CAAC,IAAI,EAAE,CAAC;AACX,QAAA,OAAO,OAAO,CAAC;KAChB;yHArCU,kBAAkB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAAlB,kBAAkB,EAAA,CAAA,CAAA,EAAA;;sGAAlB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAD9B,UAAU;;;ACKX;;AAEG;AACU,MAAA,2CAA2C,GAAqB;IAC3EA,oCAAmC;AACnC,IAAA;AACE,QAAA,OAAO,EAAE,gBAAgB;AACzB,QAAA,QAAQ,EAAE,EAAC,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE,kBAAkB,EAAE,IAAI,EAAE,EAAE,EAAC,CAAC,EAAC;AAC1F,QAAA,KAAK,EAAE,IAAI;AACZ,KAAA;AACD,IAAA,EAAC,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAEC,oBAAmB,EAAC;;;ACfvD;;;;;;;;;;;AAWG;AACG,MAAO,oBAAqB,SAAQ,cAAc,CAAA;AAGtD,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE,CAAC;AACR,QAAA,IAAI,CAAC,MAAM,GAASC,OAAO,CAAC,cAAc,CAAC;AAC3C,QAAA,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;AACvB,YAAA,MAAM,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAC;SAC1F;KACF;AAEQ,IAAA,GAAG,CAAC,GAAW,EAAA;QACtB,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;YACnC,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;SAC1C;aAAM;YACL,OAAqB,OAAO,CAAC,MAAM,CAC/B,yDAAyD,GAAG,GAAG,CAAC,CAAC;SACtE;KACF;AACF;;AClCD;;;;AAIG;AAIH;;AAEG;MACU,OAAO,GAAG,IAAI,OAAO,CAAC,mBAAmB;;ACAtD;;;;;;AAMG;AACU,MAAA,uBAAuB,GAChC,CAAC,EAAC,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE,oBAAoB,EAAE,IAAI,EAAE,EAAE,EAAC,EAAE;AAE1E;;AAEG;AACI,MAAM,sBAAsB,GAAG,qBAAqB,CACvD,mBAAmB,EAAE,gBAAgB,EAAE,2CAA2C;;ACzBtF;;;;AAIG;AAGH;;ACPA;;ACRA;;AAEG;;;;"}