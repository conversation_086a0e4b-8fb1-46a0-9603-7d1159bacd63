/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { depsTracker } from './deps_tracker/deps_tracker';
export function ɵɵgetComponentDepsFactory(type, rawImports) {
    return () => {
        try {
            return depsTracker.getComponentDependencies(type, rawImports).dependencies;
        }
        catch (e) {
            console.error(`Computing dependencies in local compilation mode for the component "${type.name}" failed with the exception:`, e);
            throw e;
        }
    };
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibG9jYWxfY29tcGlsYXRpb24uanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9jb3JlL3NyYy9yZW5kZXIzL2xvY2FsX2NvbXBpbGF0aW9uLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7R0FNRztBQUVILE9BQU8sRUFBQyxXQUFXLEVBQUMsTUFBTSw2QkFBNkIsQ0FBQztBQUd4RCxNQUFNLFVBQVUseUJBQXlCLENBQ3JDLElBQXdCLEVBQUUsVUFBd0M7SUFDcEUsT0FBTyxHQUFHLEVBQUU7UUFDVixJQUFJLENBQUM7WUFDSCxPQUFPLFdBQVcsQ0FBQyx3QkFBd0IsQ0FBQyxJQUFJLEVBQUUsVUFBVSxDQUFDLENBQUMsWUFBWSxDQUFDO1FBQzdFLENBQUM7UUFBQyxPQUFPLENBQUMsRUFBRSxDQUFDO1lBQ1gsT0FBTyxDQUFDLEtBQUssQ0FDVCx1RUFDSSxJQUFJLENBQUMsSUFBSSw4QkFBOEIsRUFDM0MsQ0FBQyxDQUFDLENBQUM7WUFDUCxNQUFNLENBQUMsQ0FBQztRQUNWLENBQUM7SUFDSCxDQUFDLENBQUM7QUFDSixDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5cbmltcG9ydCB7ZGVwc1RyYWNrZXJ9IGZyb20gJy4vZGVwc190cmFja2VyL2RlcHNfdHJhY2tlcic7XG5pbXBvcnQge0NvbXBvbmVudFR5cGUsIERlcGVuZGVuY3lUeXBlTGlzdCwgUmF3U2NvcGVJbmZvRnJvbURlY29yYXRvcn0gZnJvbSAnLi9pbnRlcmZhY2VzL2RlZmluaXRpb24nO1xuXG5leHBvcnQgZnVuY3Rpb24gybXJtWdldENvbXBvbmVudERlcHNGYWN0b3J5KFxuICAgIHR5cGU6IENvbXBvbmVudFR5cGU8YW55PiwgcmF3SW1wb3J0cz86IFJhd1Njb3BlSW5mb0Zyb21EZWNvcmF0b3JbXSk6ICgpID0+IERlcGVuZGVuY3lUeXBlTGlzdCB7XG4gIHJldHVybiAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHJldHVybiBkZXBzVHJhY2tlci5nZXRDb21wb25lbnREZXBlbmRlbmNpZXModHlwZSwgcmF3SW1wb3J0cykuZGVwZW5kZW5jaWVzO1xuICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgYENvbXB1dGluZyBkZXBlbmRlbmNpZXMgaW4gbG9jYWwgY29tcGlsYXRpb24gbW9kZSBmb3IgdGhlIGNvbXBvbmVudCBcIiR7XG4gICAgICAgICAgICAgIHR5cGUubmFtZX1cIiBmYWlsZWQgd2l0aCB0aGUgZXhjZXB0aW9uOmAsXG4gICAgICAgICAgZSk7XG4gICAgICB0aHJvdyBlO1xuICAgIH1cbiAgfTtcbn1cbiJdfQ==