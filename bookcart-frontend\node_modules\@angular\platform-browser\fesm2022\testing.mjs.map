{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../packages/platform-browser/testing/src/browser.ts", "../../../../../../packages/platform-browser/testing/src/testing.ts", "../../../../../../packages/platform-browser/testing/public_api.ts", "../../../../../../packages/platform-browser/testing/index.ts", "../../../../../../packages/platform-browser/testing/testing.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {PlatformLocation} from '@angular/common';\nimport {MockPlatformLocation} from '@angular/common/testing';\nimport {APP_ID, createPlatformFactory, NgModule, PLATFORM_INITIALIZER, platformCore, provideZoneChangeDetection, StaticProvider} from '@angular/core';\nimport {BrowserModule, ɵBrowserDomAdapter as BrowserDomAdapter} from '@angular/platform-browser';\n\nfunction initBrowserTests() {\n  BrowserDomAdapter.makeCurrent();\n}\n\nconst _TEST_BROWSER_PLATFORM_PROVIDERS: StaticProvider[] =\n    [{provide: PLATFORM_INITIALIZER, useValue: initBrowserTests, multi: true}];\n\n/**\n * Platform for testing\n *\n * @publicApi\n */\nexport const platformBrowserTesting =\n    createPlatformFactory(platformCore, 'browserTesting', _TEST_BROWSER_PLATFORM_PROVIDERS);\n\n/**\n * NgModule for testing.\n *\n * @publicApi\n */\n@NgModule({\n  exports: [BrowserModule],\n  providers: [\n    {provide: APP_ID, useValue: 'a'},\n    provideZoneChangeDetection(),\n    {provide: PlatformLocation, useClass: MockPlatformLocation},\n  ]\n})\nexport class BrowserTestingModule {\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser/testing package.\n */\nexport * from './browser';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/// <reference types=\"jasmine\" />\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\nexport * from './src/testing';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// This file is not used to build this module. It is only used during editing\n// by the TypeScript language service and during build for verification. `ngc`\n// replaces this file with production index.ts when it rewrites private symbol\n// names.\n\nexport * from './public_api';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["BrowserDomAdapter"], "mappings": ";;;;;;;;;;;;AAYA,SAAS,gBAAgB,GAAA;IACvBA,kBAAiB,CAAC,WAAW,EAAE,CAAC;AAClC,CAAC;AAED,MAAM,gCAAgC,GAClC,CAAC,EAAC,OAAO,EAAE,oBAAoB,EAAE,QAAQ,EAAE,gBAAgB,EAAE,KAAK,EAAE,IAAI,EAAC,CAAC,CAAC;AAE/E;;;;AAIG;AACI,MAAM,sBAAsB,GAC/B,qBAAqB,CAAC,YAAY,EAAE,gBAAgB,EAAE,gCAAgC,EAAE;AAE5F;;;;AAIG;MASU,oBAAoB,CAAA;yHAApB,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAApB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,YAPrB,aAAa,CAAA,EAAA,CAAA,CAAA,EAAA;AAOZ,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,EANpB,SAAA,EAAA;AACT,YAAA,EAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAC;AAChC,YAAA,0BAA0B,EAAE;AAC5B,YAAA,EAAC,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,oBAAoB,EAAC;AAC5D,SAAA,EAAA,OAAA,EAAA,CALS,aAAa,CAAA,EAAA,CAAA,CAAA,EAAA;;sGAOZ,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBARhC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,aAAa,CAAC;AACxB,oBAAA,SAAS,EAAE;AACT,wBAAA,EAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAC;AAChC,wBAAA,0BAA0B,EAAE;AAC5B,wBAAA,EAAC,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,oBAAoB,EAAC;AAC5D,qBAAA;AACF,iBAAA,CAAA;;;AC/BD;;;;AAIG;;ACJH;;ACAA;;ACRA;;AAEG;;;;"}