"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getStartOffsetOfAttribute = exports.findAttributeOnElementWithAttrs = exports.findAttributeOnElementWithTag = exports.findElementsWithAttribute = void 0;
const parse5_1 = require("parse5");
/**
 * Parses a HTML fragment and traverses all AST nodes in order find elements that
 * include the specified attribute.
 */
function findElementsWithAttribute(html, attributeName) {
    const document = (0, parse5_1.parseFragment)(html, { sourceCodeLocationInfo: true });
    const elements = [];
    const visitNodes = (nodes) => {
        nodes.forEach(n => {
            const node = n;
            if (node.childNodes) {
                visitNodes(node.childNodes);
            }
            if (node.attrs?.some(attr => attr.name === attributeName.toLowerCase())) {
                elements.push(node);
            }
        });
    };
    visitNodes(document.childNodes);
    return elements;
}
exports.findElementsWithAttribute = findElementsWithAttribute;
/**
 * Finds elements with explicit tag names that also contain the specified attribute. Returns the
 * attribute start offset based on the specified HTML.
 */
function findAttributeOnElementWithTag(html, name, tagNames) {
    return findElementsWithAttribute(html, name)
        .filter(element => tagNames.includes(element.tagName))
        .map(element => getStartOffsetOfAttribute(element, name));
}
exports.findAttributeOnElementWithTag = findAttributeOnElementWithTag;
/**
 * Finds elements that contain the given attribute and contain at least one of the other
 * specified attributes. Returns the primary attribute's start offset based on the specified HTML.
 */
function findAttributeOnElementWithAttrs(html, name, attrs) {
    return findElementsWithAttribute(html, name)
        .filter(element => attrs.some(attr => hasElementAttribute(element, attr)))
        .map(element => getStartOffsetOfAttribute(element, name));
}
exports.findAttributeOnElementWithAttrs = findAttributeOnElementWithAttrs;
/** Shorthand function that checks if the specified element contains the given attribute. */
function hasElementAttribute(element, attributeName) {
    return element.attrs && element.attrs.some(attr => attr.name === attributeName.toLowerCase());
}
/** Gets the start offset of the given attribute from a Parse5 element. */
function getStartOffsetOfAttribute(element, attributeName) {
    return element.sourceCodeLocation.attrs[attributeName.toLowerCase()].startOffset;
}
exports.getStartOffsetOfAttribute = getStartOffsetOfAttribute;
//# sourceMappingURL=data:application/json;base64,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