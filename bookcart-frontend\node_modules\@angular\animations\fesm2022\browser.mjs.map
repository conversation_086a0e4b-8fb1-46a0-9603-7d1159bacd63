{"version": 3, "file": "browser.mjs", "sources": ["../../../../../../packages/animations/browser/src/error_helpers.ts", "../../../../../../packages/animations/browser/src/render/web_animations/animatable_props_set.ts", "../../../../../../packages/animations/browser/src/render/shared.ts", "../../../../../../packages/animations/browser/src/render/animation_driver.ts", "../../../../../../packages/animations/browser/src/dsl/style_normalization/animation_style_normalizer.ts", "../../../../../../packages/animations/browser/src/util.ts", "../../../../../../packages/animations/browser/src/dsl/style_normalization/web_animations_style_normalizer.ts", "../../../../../../packages/animations/browser/src/warning_helpers.ts", "../../../../../../packages/animations/browser/src/dsl/animation_transition_expr.ts", "../../../../../../packages/animations/browser/src/dsl/animation_ast_builder.ts", "../../../../../../packages/animations/browser/src/dsl/animation_timeline_instruction.ts", "../../../../../../packages/animations/browser/src/dsl/element_instruction_map.ts", "../../../../../../packages/animations/browser/src/dsl/animation_timeline_builder.ts", "../../../../../../packages/animations/browser/src/dsl/animation_transition_instruction.ts", "../../../../../../packages/animations/browser/src/dsl/animation_transition_factory.ts", "../../../../../../packages/animations/browser/src/dsl/animation_trigger.ts", "../../../../../../packages/animations/browser/src/render/timeline_animation_engine.ts", "../../../../../../packages/animations/browser/src/render/transition_animation_engine.ts", "../../../../../../packages/animations/browser/src/render/animation_engine_next.ts", "../../../../../../packages/animations/browser/src/render/special_cased_styles.ts", "../../../../../../packages/animations/browser/src/render/web_animations/web_animations_player.ts", "../../../../../../packages/animations/browser/src/render/web_animations/web_animations_driver.ts", "../../../../../../packages/animations/browser/src/create_engine.ts", "../../../../../../packages/animations/browser/src/dsl/animation.ts", "../../../../../../packages/animations/browser/src/render/renderer.ts", "../../../../../../packages/animations/browser/src/render/animation_renderer.ts", "../../../../../../packages/animations/browser/src/browser.ts", "../../../../../../packages/animations/browser/public_api.ts", "../../../../../../packages/animations/browser/index.ts", "../../../../../../packages/animations/browser/browser.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵRuntimeErrorCode as RuntimeErrorCode} from '@angular/animations';\nimport {ɵRuntimeError as RuntimeError} from '@angular/core';\n\nconst LINE_START = '\\n - ';\n\nexport function invalidTimingValue(exp: string | number): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_TIMING_VALUE,\n    ngDevMode && `The provided timing value \"${exp}\" is invalid.`,\n  );\n}\n\nexport function negativeStepValue(): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.NEGATIVE_STEP_VALUE,\n    ngDevMode && 'Duration values below 0 are not allowed for this animation step.',\n  );\n}\n\nexport function negativeDelayValue(): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.NEGATIVE_DELAY_VALUE,\n    ngDevMode && 'Delay values below 0 are not allowed for this animation step.',\n  );\n}\n\nexport function invalidStyleParams(varName: string): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_STYLE_PARAMS,\n    ngDevMode &&\n      `Unable to resolve the local animation param ${varName} in the given list of values`,\n  );\n}\n\nexport function invalidParamValue(varName: string): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_PARAM_VALUE,\n    ngDevMode && `Please provide a value for the animation param ${varName}`,\n  );\n}\n\nexport function invalidNodeType(nodeType: string): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_NODE_TYPE,\n    ngDevMode && `Unable to resolve animation metadata node #${nodeType}`,\n  );\n}\n\nexport function invalidCssUnitValue(userProvidedProperty: string, value: string): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_CSS_UNIT_VALUE,\n    ngDevMode && `Please provide a CSS unit value for ${userProvidedProperty}:${value}`,\n  );\n}\n\nexport function invalidTrigger(): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_TRIGGER,\n    ngDevMode &&\n      \"animation triggers cannot be prefixed with an `@` sign (e.g. trigger('@foo', [...]))\",\n  );\n}\n\nexport function invalidDefinition(): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_DEFINITION,\n    ngDevMode && 'only state() and transition() definitions can sit inside of a trigger()',\n  );\n}\n\nexport function invalidState(metadataName: string, missingSubs: string[]): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_STATE,\n    ngDevMode &&\n      `state(\"${metadataName}\", ...) must define default values for all the following style substitutions: ${missingSubs.join(\n        ', ',\n      )}`,\n  );\n}\n\nexport function invalidStyleValue(value: string): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_STYLE_VALUE,\n    ngDevMode && `The provided style string value ${value} is not allowed.`,\n  );\n}\n\nexport function invalidProperty(prop: string): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_PROPERTY,\n    ngDevMode &&\n      `The provided animation property \"${prop}\" is not a supported CSS property for animations`,\n  );\n}\n\nexport function invalidParallelAnimation(\n  prop: string,\n  firstStart: number,\n  firstEnd: number,\n  secondStart: number,\n  secondEnd: number,\n): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_PARALLEL_ANIMATION,\n    ngDevMode &&\n      `The CSS property \"${prop}\" that exists between the times of \"${firstStart}ms\" and \"${firstEnd}ms\" is also being animated in a parallel animation between the times of \"${secondStart}ms\" and \"${secondEnd}ms\"`,\n  );\n}\n\nexport function invalidKeyframes(): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_KEYFRAMES,\n    ngDevMode && `keyframes() must be placed inside of a call to animate()`,\n  );\n}\n\nexport function invalidOffset(): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_OFFSET,\n    ngDevMode && `Please ensure that all keyframe offsets are between 0 and 1`,\n  );\n}\n\nexport function keyframeOffsetsOutOfOrder(): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.KEYFRAME_OFFSETS_OUT_OF_ORDER,\n    ngDevMode && `Please ensure that all keyframe offsets are in order`,\n  );\n}\n\nexport function keyframesMissingOffsets(): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.KEYFRAMES_MISSING_OFFSETS,\n    ngDevMode && `Not all style() steps within the declared keyframes() contain offsets`,\n  );\n}\n\nexport function invalidStagger(): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_STAGGER,\n    ngDevMode && `stagger() can only be used inside of query()`,\n  );\n}\n\nexport function invalidQuery(selector: string): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_QUERY,\n    ngDevMode &&\n      `\\`query(\"${selector}\")\\` returned zero elements. (Use \\`query(\"${selector}\", { optional: true })\\` if you wish to allow this.)`,\n  );\n}\n\nexport function invalidExpression(expr: string): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_EXPRESSION,\n    ngDevMode && `The provided transition expression \"${expr}\" is not supported`,\n  );\n}\n\nexport function invalidTransitionAlias(alias: string): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_TRANSITION_ALIAS,\n    ngDevMode && `The transition alias value \"${alias}\" is not supported`,\n  );\n}\n\nexport function validationFailed(errors: Error[]): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.VALIDATION_FAILED,\n    ngDevMode && `animation validation failed:\\n${errors.map((err) => err.message).join('\\n')}`,\n  );\n}\n\nexport function buildingFailed(errors: Error[]): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.BUILDING_FAILED,\n    ngDevMode && `animation building failed:\\n${errors.map((err) => err.message).join('\\n')}`,\n  );\n}\n\nexport function triggerBuildFailed(name: string, errors: Error[]): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.TRIGGER_BUILD_FAILED,\n    ngDevMode &&\n      `The animation trigger \"${name}\" has failed to build due to the following errors:\\n - ${errors\n        .map((err) => err.message)\n        .join('\\n - ')}`,\n  );\n}\n\nexport function animationFailed(errors: Error[]): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.ANIMATION_FAILED,\n    ngDevMode &&\n      `Unable to animate due to the following errors:${LINE_START}${errors\n        .map((err) => err.message)\n        .join(LINE_START)}`,\n  );\n}\n\nexport function registerFailed(errors: Error[]): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.REGISTRATION_FAILED,\n    ngDevMode &&\n      `Unable to build the animation due to the following errors: ${errors\n        .map((err) => err.message)\n        .join('\\n')}`,\n  );\n}\n\nexport function missingOrDestroyedAnimation(): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.MISSING_OR_DESTROYED_ANIMATION,\n    ngDevMode && \"The requested animation doesn't exist or has already been destroyed\",\n  );\n}\n\nexport function createAnimationFailed(errors: Error[]): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.CREATE_ANIMATION_FAILED,\n    ngDevMode &&\n      `Unable to create the animation due to the following errors:${errors\n        .map((err) => err.message)\n        .join('\\n')}`,\n  );\n}\n\nexport function missingPlayer(id: string): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.MISSING_PLAYER,\n    ngDevMode && `Unable to find the timeline player referenced by ${id}`,\n  );\n}\n\nexport function missingTrigger(phase: string, name: string): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.MISSING_TRIGGER,\n    ngDevMode &&\n      `Unable to listen on the animation trigger event \"${phase}\" because the animation trigger \"${name}\" doesn\\'t exist!`,\n  );\n}\n\nexport function missingEvent(name: string): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.MISSING_EVENT,\n    ngDevMode &&\n      `Unable to listen on the animation trigger \"${name}\" because the provided event is undefined!`,\n  );\n}\n\nexport function unsupportedTriggerEvent(phase: string, name: string): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.UNSUPPORTED_TRIGGER_EVENT,\n    ngDevMode &&\n      `The provided animation trigger event \"${phase}\" for the animation trigger \"${name}\" is not supported!`,\n  );\n}\n\nexport function unregisteredTrigger(name: string): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.UNREGISTERED_TRIGGER,\n    ngDevMode && `The provided animation trigger \"${name}\" has not been registered!`,\n  );\n}\n\nexport function triggerTransitionsFailed(errors: Error[]): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.TRIGGER_TRANSITIONS_FAILED,\n    ngDevMode &&\n      `Unable to process animations due to the following failed trigger transitions\\n ${errors\n        .map((err) => err.message)\n        .join('\\n')}`,\n  );\n}\n\nexport function triggerParsingFailed(name: string, errors: Error[]): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.TRIGGER_PARSING_FAILED,\n    ngDevMode &&\n      `Animation parsing for the ${name} trigger have failed:${LINE_START}${errors\n        .map((err) => err.message)\n        .join(LINE_START)}`,\n  );\n}\n\nexport function transitionFailed(name: string, errors: Error[]): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.TRANSITION_FAILED,\n    ngDevMode && `@${name} has failed due to:\\n ${errors.map((err) => err.message).join('\\n- ')}`,\n  );\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Set of all animatable CSS properties\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_animated_properties\n */\nexport const ANIMATABLE_PROP_SET = new Set([\n  '-moz-outline-radius',\n  '-moz-outline-radius-bottomleft',\n  '-moz-outline-radius-bottomright',\n  '-moz-outline-radius-topleft',\n  '-moz-outline-radius-topright',\n  '-ms-grid-columns',\n  '-ms-grid-rows',\n  '-webkit-line-clamp',\n  '-webkit-text-fill-color',\n  '-webkit-text-stroke',\n  '-webkit-text-stroke-color',\n  'accent-color',\n  'all',\n  'backdrop-filter',\n  'background',\n  'background-color',\n  'background-position',\n  'background-size',\n  'block-size',\n  'border',\n  'border-block-end',\n  'border-block-end-color',\n  'border-block-end-width',\n  'border-block-start',\n  'border-block-start-color',\n  'border-block-start-width',\n  'border-bottom',\n  'border-bottom-color',\n  'border-bottom-left-radius',\n  'border-bottom-right-radius',\n  'border-bottom-width',\n  'border-color',\n  'border-end-end-radius',\n  'border-end-start-radius',\n  'border-image-outset',\n  'border-image-slice',\n  'border-image-width',\n  'border-inline-end',\n  'border-inline-end-color',\n  'border-inline-end-width',\n  'border-inline-start',\n  'border-inline-start-color',\n  'border-inline-start-width',\n  'border-left',\n  'border-left-color',\n  'border-left-width',\n  'border-radius',\n  'border-right',\n  'border-right-color',\n  'border-right-width',\n  'border-start-end-radius',\n  'border-start-start-radius',\n  'border-top',\n  'border-top-color',\n  'border-top-left-radius',\n  'border-top-right-radius',\n  'border-top-width',\n  'border-width',\n  'bottom',\n  'box-shadow',\n  'caret-color',\n  'clip',\n  'clip-path',\n  'color',\n  'column-count',\n  'column-gap',\n  'column-rule',\n  'column-rule-color',\n  'column-rule-width',\n  'column-width',\n  'columns',\n  'filter',\n  'flex',\n  'flex-basis',\n  'flex-grow',\n  'flex-shrink',\n  'font',\n  'font-size',\n  'font-size-adjust',\n  'font-stretch',\n  'font-variation-settings',\n  'font-weight',\n  'gap',\n  'grid-column-gap',\n  'grid-gap',\n  'grid-row-gap',\n  'grid-template-columns',\n  'grid-template-rows',\n  'height',\n  'inline-size',\n  'input-security',\n  'inset',\n  'inset-block',\n  'inset-block-end',\n  'inset-block-start',\n  'inset-inline',\n  'inset-inline-end',\n  'inset-inline-start',\n  'left',\n  'letter-spacing',\n  'line-clamp',\n  'line-height',\n  'margin',\n  'margin-block-end',\n  'margin-block-start',\n  'margin-bottom',\n  'margin-inline-end',\n  'margin-inline-start',\n  'margin-left',\n  'margin-right',\n  'margin-top',\n  'mask',\n  'mask-border',\n  'mask-position',\n  'mask-size',\n  'max-block-size',\n  'max-height',\n  'max-inline-size',\n  'max-lines',\n  'max-width',\n  'min-block-size',\n  'min-height',\n  'min-inline-size',\n  'min-width',\n  'object-position',\n  'offset',\n  'offset-anchor',\n  'offset-distance',\n  'offset-path',\n  'offset-position',\n  'offset-rotate',\n  'opacity',\n  'order',\n  'outline',\n  'outline-color',\n  'outline-offset',\n  'outline-width',\n  'padding',\n  'padding-block-end',\n  'padding-block-start',\n  'padding-bottom',\n  'padding-inline-end',\n  'padding-inline-start',\n  'padding-left',\n  'padding-right',\n  'padding-top',\n  'perspective',\n  'perspective-origin',\n  'right',\n  'rotate',\n  'row-gap',\n  'scale',\n  'scroll-margin',\n  'scroll-margin-block',\n  'scroll-margin-block-end',\n  'scroll-margin-block-start',\n  'scroll-margin-bottom',\n  'scroll-margin-inline',\n  'scroll-margin-inline-end',\n  'scroll-margin-inline-start',\n  'scroll-margin-left',\n  'scroll-margin-right',\n  'scroll-margin-top',\n  'scroll-padding',\n  'scroll-padding-block',\n  'scroll-padding-block-end',\n  'scroll-padding-block-start',\n  'scroll-padding-bottom',\n  'scroll-padding-inline',\n  'scroll-padding-inline-end',\n  'scroll-padding-inline-start',\n  'scroll-padding-left',\n  'scroll-padding-right',\n  'scroll-padding-top',\n  'scroll-snap-coordinate',\n  'scroll-snap-destination',\n  'scrollbar-color',\n  'shape-image-threshold',\n  'shape-margin',\n  'shape-outside',\n  'tab-size',\n  'text-decoration',\n  'text-decoration-color',\n  'text-decoration-thickness',\n  'text-emphasis',\n  'text-emphasis-color',\n  'text-indent',\n  'text-shadow',\n  'text-underline-offset',\n  'top',\n  'transform',\n  'transform-origin',\n  'translate',\n  'vertical-align',\n  'visibility',\n  'width',\n  'word-spacing',\n  'z-index',\n  'zoom',\n]);\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {\n  AnimationEvent,\n  AnimationPlayer,\n  AUTO_STYLE,\n  NoopAnimationPlayer,\n  ɵAnimationGroupPlayer,\n  ɵPRE_STYLE as PRE_STYLE,\n  ɵStyleDataMap,\n} from '@angular/animations';\n\nimport {AnimationStyleNormalizer} from '../../src/dsl/style_normalization/animation_style_normalizer';\nimport {animationFailed} from '../error_helpers';\n\nimport {ANIMATABLE_PROP_SET} from './web_animations/animatable_props_set';\n\nexport function optimizeGroupPlayer(players: AnimationPlayer[]): AnimationPlayer {\n  switch (players.length) {\n    case 0:\n      return new NoopAnimationPlayer();\n    case 1:\n      return players[0];\n    default:\n      return new ɵAnimationGroupPlayer(players);\n  }\n}\n\nexport function normalizeKeyframes(\n  normalizer: AnimationStyleNormalizer,\n  keyframes: Array<ɵStyleDataMap>,\n  preStyles: ɵStyleDataMap = new Map(),\n  postStyles: ɵStyleDataMap = new Map(),\n): Array<ɵStyleDataMap> {\n  const errors: Error[] = [];\n  const normalizedKeyframes: Array<ɵStyleDataMap> = [];\n  let previousOffset = -1;\n  let previousKeyframe: ɵStyleDataMap | null = null;\n  keyframes.forEach((kf) => {\n    const offset = kf.get('offset') as number;\n    const isSameOffset = offset == previousOffset;\n    const normalizedKeyframe: ɵStyleDataMap = (isSameOffset && previousKeyframe) || new Map();\n    kf.forEach((val, prop) => {\n      let normalizedProp = prop;\n      let normalizedValue = val;\n      if (prop !== 'offset') {\n        normalizedProp = normalizer.normalizePropertyName(normalizedProp, errors);\n        switch (normalizedValue) {\n          case PRE_STYLE:\n            normalizedValue = preStyles.get(prop)!;\n            break;\n\n          case AUTO_STYLE:\n            normalizedValue = postStyles.get(prop)!;\n            break;\n\n          default:\n            normalizedValue = normalizer.normalizeStyleValue(\n              prop,\n              normalizedProp,\n              normalizedValue,\n              errors,\n            );\n            break;\n        }\n      }\n      normalizedKeyframe.set(normalizedProp, normalizedValue);\n    });\n    if (!isSameOffset) {\n      normalizedKeyframes.push(normalizedKeyframe);\n    }\n    previousKeyframe = normalizedKeyframe;\n    previousOffset = offset;\n  });\n  if (errors.length) {\n    throw animationFailed(errors);\n  }\n\n  return normalizedKeyframes;\n}\n\nexport function listenOnPlayer(\n  player: AnimationPlayer,\n  eventName: string,\n  event: AnimationEvent | undefined,\n  callback: (event: any) => any,\n) {\n  switch (eventName) {\n    case 'start':\n      player.onStart(() => callback(event && copyAnimationEvent(event, 'start', player)));\n      break;\n    case 'done':\n      player.onDone(() => callback(event && copyAnimationEvent(event, 'done', player)));\n      break;\n    case 'destroy':\n      player.onDestroy(() => callback(event && copyAnimationEvent(event, 'destroy', player)));\n      break;\n  }\n}\n\nexport function copyAnimationEvent(\n  e: AnimationEvent,\n  phaseName: string,\n  player: AnimationPlayer,\n): AnimationEvent {\n  const totalTime = player.totalTime;\n  const disabled = (player as any).disabled ? true : false;\n  const event = makeAnimationEvent(\n    e.element,\n    e.triggerName,\n    e.fromState,\n    e.toState,\n    phaseName || e.phaseName,\n    totalTime == undefined ? e.totalTime : totalTime,\n    disabled,\n  );\n  const data = (e as any)['_data'];\n  if (data != null) {\n    (event as any)['_data'] = data;\n  }\n  return event;\n}\n\nexport function makeAnimationEvent(\n  element: any,\n  triggerName: string,\n  fromState: string,\n  toState: string,\n  phaseName: string = '',\n  totalTime: number = 0,\n  disabled?: boolean,\n): AnimationEvent {\n  return {element, triggerName, fromState, toState, phaseName, totalTime, disabled: !!disabled};\n}\n\nexport function getOrSetDefaultValue<T, V>(map: Map<T, V>, key: T, defaultValue: V) {\n  let value = map.get(key);\n  if (!value) {\n    map.set(key, (value = defaultValue));\n  }\n  return value;\n}\n\nexport function parseTimelineCommand(command: string): [string, string] {\n  const separatorPos = command.indexOf(':');\n  const id = command.substring(1, separatorPos);\n  const action = command.slice(separatorPos + 1);\n  return [id, action];\n}\n\nconst documentElement: HTMLElement | null = /* @__PURE__ */ (() =>\n  typeof document === 'undefined' ? null : document.documentElement)();\n\nexport function getParentElement(element: any): unknown | null {\n  const parent = element.parentNode || element.host || null; // consider host to support shadow DOM\n  if (parent === documentElement) {\n    return null;\n  }\n  return parent;\n}\n\nfunction containsVendorPrefix(prop: string): boolean {\n  // Webkit is the only real popular vendor prefix nowadays\n  // cc: http://shouldiprefix.com/\n  return prop.substring(1, 6) == 'ebkit'; // webkit or Webkit\n}\n\nlet _CACHED_BODY: {style: any} | null = null;\nlet _IS_WEBKIT = false;\nexport function validateStyleProperty(prop: string): boolean {\n  if (!_CACHED_BODY) {\n    _CACHED_BODY = getBodyNode() || {};\n    _IS_WEBKIT = _CACHED_BODY!.style ? 'WebkitAppearance' in _CACHED_BODY!.style : false;\n  }\n\n  let result = true;\n  if (_CACHED_BODY!.style && !containsVendorPrefix(prop)) {\n    result = prop in _CACHED_BODY!.style;\n    if (!result && _IS_WEBKIT) {\n      const camelProp = 'Webkit' + prop.charAt(0).toUpperCase() + prop.slice(1);\n      result = camelProp in _CACHED_BODY!.style;\n    }\n  }\n\n  return result;\n}\n\nexport function validateWebAnimatableStyleProperty(prop: string): boolean {\n  return ANIMATABLE_PROP_SET.has(prop);\n}\n\nexport function getBodyNode(): any | null {\n  if (typeof document != 'undefined') {\n    return document.body;\n  }\n  return null;\n}\n\nexport function containsElement(elm1: any, elm2: any): boolean {\n  while (elm2) {\n    if (elm2 === elm1) {\n      return true;\n    }\n    elm2 = getParentElement(elm2);\n  }\n  return false;\n}\n\nexport function invokeQuery(element: any, selector: string, multi: boolean): any[] {\n  if (multi) {\n    return Array.from(element.querySelectorAll(selector));\n  }\n  const elem = element.querySelector(selector);\n  return elem ? [elem] : [];\n}\n\nexport function hypenatePropsKeys(original: ɵStyleDataMap): ɵStyleDataMap {\n  const newMap: ɵStyleDataMap = new Map();\n  original.forEach((val, prop) => {\n    const newProp = prop.replace(/([a-z])([A-Z])/g, '$1-$2');\n    newMap.set(newProp, val);\n  });\n  return newMap;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AnimationPlayer, NoopAnimationPlayer} from '@angular/animations';\nimport {Injectable} from '@angular/core';\n\nimport {containsElement, getParentElement, invokeQuery, validateStyleProperty} from './shared';\n\n/**\n * @publicApi\n *\n * `AnimationDriver` implentation for Noop animations\n */\n@Injectable()\nexport class NoopAnimationDriver implements AnimationDriver {\n  /**\n   * @returns Whether `prop` is a valid CSS property\n   */\n  validateStyleProperty(prop: string): boolean {\n    return validateStyleProperty(prop);\n  }\n\n  /**\n   * @deprecated unused\n   */\n  matchesElement(_element: any, _selector: string): boolean {\n    // This method is deprecated and no longer in use so we return false.\n    return false;\n  }\n\n  /**\n   *\n   * @returns Whether elm1 contains elm2.\n   */\n  containsElement(elm1: any, elm2: any): boolean {\n    return containsElement(elm1, elm2);\n  }\n\n  /**\n   * @returns Rhe parent of the given element or `null` if the element is the `document`\n   */\n  getParentElement(element: unknown): unknown {\n    return getParentElement(element);\n  }\n\n  /**\n   * @returns The result of the query selector on the element. The array will contain up to 1 item\n   *     if `multi` is  `false`.\n   */\n  query(element: any, selector: string, multi: boolean): any[] {\n    return invokeQuery(element, selector, multi);\n  }\n\n  /**\n   * @returns The `defaultValue` or empty string\n   */\n  computeStyle(element: any, prop: string, defaultValue?: string): string {\n    return defaultValue || '';\n  }\n\n  /**\n   * @returns An `NoopAnimationPlayer`\n   */\n  animate(\n    element: any,\n    keyframes: Array<Map<string, string | number>>,\n    duration: number,\n    delay: number,\n    easing: string,\n    previousPlayers: any[] = [],\n    scrubberAccessRequested?: boolean,\n  ): AnimationPlayer {\n    return new NoopAnimationPlayer(duration, delay);\n  }\n}\n\n/**\n * @publicApi\n */\nexport abstract class AnimationDriver {\n  /**\n   * @deprecated Use the NoopAnimationDriver class.\n   */\n  static NOOP: AnimationDriver = /* @__PURE__ */ new NoopAnimationDriver();\n\n  abstract validateStyleProperty(prop: string): boolean;\n\n  abstract validateAnimatableStyleProperty?: (prop: string) => boolean;\n\n  /**\n   * @deprecated No longer in use. Will be removed.\n   */\n  abstract matchesElement(element: any, selector: string): boolean;\n\n  abstract containsElement(elm1: any, elm2: any): boolean;\n\n  /**\n   * Obtains the parent element, if any. `null` is returned if the element does not have a parent.\n   */\n  abstract getParentElement(element: unknown): unknown;\n\n  abstract query(element: any, selector: string, multi: boolean): any[];\n\n  abstract computeStyle(element: any, prop: string, defaultValue?: string): string;\n\n  abstract animate(\n    element: any,\n    keyframes: Array<Map<string, string | number>>,\n    duration: number,\n    delay: number,\n    easing?: string | null,\n    previousPlayers?: any[],\n    scrubberAccessRequested?: boolean,\n  ): any;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport abstract class AnimationStyleNormalizer {\n  abstract normalizePropertyName(propertyName: string, errors: Error[]): string;\n  abstract normalizeStyleValue(\n    userProvidedProperty: string,\n    normalizedProperty: string,\n    value: string | number,\n    errors: Error[],\n  ): string;\n}\n\nexport class NoopAnimationStyleNormalizer {\n  normalizePropertyName(propertyName: string, errors: Error[]): string {\n    return propertyName;\n  }\n\n  normalizeStyleValue(\n    userProvidedProperty: string,\n    normalizedProperty: string,\n    value: string | number,\n    errors: Error[],\n  ): string {\n    return <any>value;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {\n  AnimateTimings,\n  AnimationMetadata,\n  AnimationMetadataType,\n  AnimationOptions,\n  sequence,\n  ɵStyleData,\n  ɵStyleDataMap,\n} from '@angular/animations';\n\nimport {Ast as AnimationAst, AstVisitor as AnimationAstVisitor} from './dsl/animation_ast';\nimport {AnimationDslVisitor} from './dsl/animation_dsl_visitor';\nimport {\n  invalidNodeType,\n  invalidParamValue,\n  invalidStyleParams,\n  invalidTimingValue,\n  negativeDelayValue,\n  negativeStepValue,\n} from './error_helpers';\n\nconst ONE_SECOND = 1000;\n\nexport const SUBSTITUTION_EXPR_START = '{{';\nexport const SUBSTITUTION_EXPR_END = '}}';\nexport const ENTER_CLASSNAME = 'ng-enter';\nexport const LEAVE_CLASSNAME = 'ng-leave';\nexport const NG_TRIGGER_CLASSNAME = 'ng-trigger';\nexport const NG_TRIGGER_SELECTOR = '.ng-trigger';\nexport const NG_ANIMATING_CLASSNAME = 'ng-animating';\nexport const NG_ANIMATING_SELECTOR = '.ng-animating';\n\nexport function resolveTimingValue(value: string | number) {\n  if (typeof value == 'number') return value;\n\n  const matches = value.match(/^(-?[\\.\\d]+)(m?s)/);\n  if (!matches || matches.length < 2) return 0;\n\n  return _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n}\n\nfunction _convertTimeValueToMS(value: number, unit: string): number {\n  switch (unit) {\n    case 's':\n      return value * ONE_SECOND;\n    default: // ms or something else\n      return value;\n  }\n}\n\nexport function resolveTiming(\n  timings: string | number | AnimateTimings,\n  errors: Error[],\n  allowNegativeValues?: boolean,\n) {\n  return timings.hasOwnProperty('duration')\n    ? <AnimateTimings>timings\n    : parseTimeExpression(<string | number>timings, errors, allowNegativeValues);\n}\n\nfunction parseTimeExpression(\n  exp: string | number,\n  errors: Error[],\n  allowNegativeValues?: boolean,\n): AnimateTimings {\n  const regex = /^(-?[\\.\\d]+)(m?s)(?:\\s+(-?[\\.\\d]+)(m?s))?(?:\\s+([-a-z]+(?:\\(.+?\\))?))?$/i;\n  let duration: number;\n  let delay: number = 0;\n  let easing: string = '';\n  if (typeof exp === 'string') {\n    const matches = exp.match(regex);\n    if (matches === null) {\n      errors.push(invalidTimingValue(exp));\n      return {duration: 0, delay: 0, easing: ''};\n    }\n\n    duration = _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n\n    const delayMatch = matches[3];\n    if (delayMatch != null) {\n      delay = _convertTimeValueToMS(parseFloat(delayMatch), matches[4]);\n    }\n\n    const easingVal = matches[5];\n    if (easingVal) {\n      easing = easingVal;\n    }\n  } else {\n    duration = exp;\n  }\n\n  if (!allowNegativeValues) {\n    let containsErrors = false;\n    let startIndex = errors.length;\n    if (duration < 0) {\n      errors.push(negativeStepValue());\n      containsErrors = true;\n    }\n    if (delay < 0) {\n      errors.push(negativeDelayValue());\n      containsErrors = true;\n    }\n    if (containsErrors) {\n      errors.splice(startIndex, 0, invalidTimingValue(exp));\n    }\n  }\n\n  return {duration, delay, easing};\n}\n\nexport function normalizeKeyframes(\n  keyframes: Array<ɵStyleData> | Array<ɵStyleDataMap>,\n): Array<ɵStyleDataMap> {\n  if (!keyframes.length) {\n    return [];\n  }\n  if (keyframes[0] instanceof Map) {\n    return keyframes as Array<ɵStyleDataMap>;\n  }\n  return keyframes.map((kf) => new Map(Object.entries(kf)));\n}\n\nexport function normalizeStyles(styles: ɵStyleDataMap | Array<ɵStyleDataMap>): ɵStyleDataMap {\n  return Array.isArray(styles) ? new Map(...styles) : new Map(styles);\n}\n\nexport function setStyles(element: any, styles: ɵStyleDataMap, formerStyles?: ɵStyleDataMap) {\n  styles.forEach((val, prop) => {\n    const camelProp = dashCaseToCamelCase(prop);\n    if (formerStyles && !formerStyles.has(prop)) {\n      formerStyles.set(prop, element.style[camelProp]);\n    }\n    element.style[camelProp] = val;\n  });\n}\n\nexport function eraseStyles(element: any, styles: ɵStyleDataMap) {\n  styles.forEach((_, prop) => {\n    const camelProp = dashCaseToCamelCase(prop);\n    element.style[camelProp] = '';\n  });\n}\n\nexport function normalizeAnimationEntry(\n  steps: AnimationMetadata | AnimationMetadata[],\n): AnimationMetadata {\n  if (Array.isArray(steps)) {\n    if (steps.length == 1) return steps[0];\n    return sequence(steps);\n  }\n  return steps as AnimationMetadata;\n}\n\nexport function validateStyleParams(\n  value: string | number | null | undefined,\n  options: AnimationOptions,\n  errors: Error[],\n) {\n  const params = options.params || {};\n  const matches = extractStyleParams(value);\n  if (matches.length) {\n    matches.forEach((varName) => {\n      if (!params.hasOwnProperty(varName)) {\n        errors.push(invalidStyleParams(varName));\n      }\n    });\n  }\n}\n\nconst PARAM_REGEX = new RegExp(\n  `${SUBSTITUTION_EXPR_START}\\\\s*(.+?)\\\\s*${SUBSTITUTION_EXPR_END}`,\n  'g',\n);\nexport function extractStyleParams(value: string | number | null | undefined): string[] {\n  let params: string[] = [];\n  if (typeof value === 'string') {\n    let match: any;\n    while ((match = PARAM_REGEX.exec(value))) {\n      params.push(match[1] as string);\n    }\n    PARAM_REGEX.lastIndex = 0;\n  }\n  return params;\n}\n\nexport function interpolateParams(\n  value: string | number,\n  params: {[name: string]: any},\n  errors: Error[],\n): string | number {\n  const original = `${value}`;\n  const str = original.replace(PARAM_REGEX, (_, varName) => {\n    let localVal = params[varName];\n    // this means that the value was never overridden by the data passed in by the user\n    if (localVal == null) {\n      errors.push(invalidParamValue(varName));\n      localVal = '';\n    }\n    return localVal.toString();\n  });\n\n  // we do this to assert that numeric values stay as they are\n  return str == original ? value : str;\n}\n\nconst DASH_CASE_REGEXP = /-+([a-z0-9])/g;\nexport function dashCaseToCamelCase(input: string): string {\n  return input.replace(DASH_CASE_REGEXP, (...m: any[]) => m[1].toUpperCase());\n}\n\nexport function camelCaseToDashCase(input: string): string {\n  return input.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();\n}\n\nexport function allowPreviousPlayerStylesMerge(duration: number, delay: number) {\n  return duration === 0 || delay === 0;\n}\n\nexport function balancePreviousStylesIntoKeyframes(\n  element: any,\n  keyframes: Array<ɵStyleDataMap>,\n  previousStyles: ɵStyleDataMap,\n) {\n  if (previousStyles.size && keyframes.length) {\n    let startingKeyframe = keyframes[0];\n    let missingStyleProps: string[] = [];\n    previousStyles.forEach((val, prop) => {\n      if (!startingKeyframe.has(prop)) {\n        missingStyleProps.push(prop);\n      }\n      startingKeyframe.set(prop, val);\n    });\n\n    if (missingStyleProps.length) {\n      for (let i = 1; i < keyframes.length; i++) {\n        let kf = keyframes[i];\n        missingStyleProps.forEach((prop) => kf.set(prop, computeStyle(element, prop)));\n      }\n    }\n  }\n  return keyframes;\n}\n\nexport function visitDslNode(\n  visitor: AnimationDslVisitor,\n  node: AnimationMetadata,\n  context: any,\n): any;\nexport function visitDslNode(\n  visitor: AnimationAstVisitor,\n  node: AnimationAst<AnimationMetadataType>,\n  context: any,\n): any;\nexport function visitDslNode(visitor: any, node: any, context: any): any {\n  switch (node.type) {\n    case AnimationMetadataType.Trigger:\n      return visitor.visitTrigger(node, context);\n    case AnimationMetadataType.State:\n      return visitor.visitState(node, context);\n    case AnimationMetadataType.Transition:\n      return visitor.visitTransition(node, context);\n    case AnimationMetadataType.Sequence:\n      return visitor.visitSequence(node, context);\n    case AnimationMetadataType.Group:\n      return visitor.visitGroup(node, context);\n    case AnimationMetadataType.Animate:\n      return visitor.visitAnimate(node, context);\n    case AnimationMetadataType.Keyframes:\n      return visitor.visitKeyframes(node, context);\n    case AnimationMetadataType.Style:\n      return visitor.visitStyle(node, context);\n    case AnimationMetadataType.Reference:\n      return visitor.visitReference(node, context);\n    case AnimationMetadataType.AnimateChild:\n      return visitor.visitAnimateChild(node, context);\n    case AnimationMetadataType.AnimateRef:\n      return visitor.visitAnimateRef(node, context);\n    case AnimationMetadataType.Query:\n      return visitor.visitQuery(node, context);\n    case AnimationMetadataType.Stagger:\n      return visitor.visitStagger(node, context);\n    default:\n      throw invalidNodeType(node.type);\n  }\n}\n\nexport function computeStyle(element: any, prop: string): string {\n  return (<any>window.getComputedStyle(element))[prop];\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {invalidCssUnitValue} from '../../error_helpers';\nimport {dashCaseToCamelCase} from '../../util';\n\nimport {AnimationStyleNormalizer} from './animation_style_normalizer';\n\nconst DIMENSIONAL_PROP_SET = new Set([\n  'width',\n  'height',\n  'minWidth',\n  'minHeight',\n  'maxWidth',\n  'maxHeight',\n  'left',\n  'top',\n  'bottom',\n  'right',\n  'fontSize',\n  'outlineWidth',\n  'outlineOffset',\n  'paddingTop',\n  'paddingLeft',\n  'paddingBottom',\n  'paddingRight',\n  'marginTop',\n  'marginLeft',\n  'marginBottom',\n  'marginRight',\n  'borderRadius',\n  'borderWidth',\n  'borderTopWidth',\n  'borderLeftWidth',\n  'borderRightWidth',\n  'borderBottomWidth',\n  'textIndent',\n  'perspective',\n]);\n\nexport class WebAnimationsStyleNormalizer extends AnimationStyleNormalizer {\n  override normalizePropertyName(propertyName: string, errors: Error[]): string {\n    return dashCaseToCamelCase(propertyName);\n  }\n\n  override normalizeStyleValue(\n    userProvidedProperty: string,\n    normalizedProperty: string,\n    value: string | number,\n    errors: Error[],\n  ): string {\n    let unit: string = '';\n    const strVal = value.toString().trim();\n\n    if (DIMENSIONAL_PROP_SET.has(normalizedProperty) && value !== 0 && value !== '0') {\n      if (typeof value === 'number') {\n        unit = 'px';\n      } else {\n        const valAndSuffixMatch = value.match(/^[+-]?[\\d\\.]+([a-z]*)$/);\n        if (valAndSuffixMatch && valAndSuffixMatch[1].length == 0) {\n          errors.push(invalidCssUnitValue(userProvidedProperty, value));\n        }\n      }\n    }\n    return strVal + unit;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nfunction createListOfWarnings(warnings: string[]): string {\n  const LINE_START = '\\n - ';\n  return `${LINE_START}${warnings\n    .filter(Boolean)\n    .map((warning) => warning)\n    .join(LINE_START)}`;\n}\n\nexport function warnValidation(warnings: string[]): void {\n  (typeof ngDevMode === 'undefined' || ngDevMode) &&\n    console.warn(`animation validation warnings:${createListOfWarnings(warnings)}`);\n}\n\nexport function warnTriggerBuild(name: string, warnings: string[]): void {\n  (typeof ngDevMode === 'undefined' || ngDevMode) &&\n    console.warn(\n      `The animation trigger \"${name}\" has built with the following warnings:${createListOfWarnings(\n        warnings,\n      )}`,\n    );\n}\n\nexport function warnRegister(warnings: string[]): void {\n  (typeof ngDevMode === 'undefined' || ngDevMode) &&\n    console.warn(`Animation built with the following warnings:${createListOfWarnings(warnings)}`);\n}\n\nexport function triggerParsingWarnings(name: string, warnings: string[]): void {\n  (typeof ngDevMode === 'undefined' || ngDevMode) &&\n    console.warn(\n      `Animation parsing for the ${name} trigger presents the following warnings:${createListOfWarnings(\n        warnings,\n      )}`,\n    );\n}\n\nexport function pushUnrecognizedPropertiesWarning(warnings: string[], props: string[]): void {\n  if (props.length) {\n    warnings.push(`The following provided properties are not recognized: ${props.join(', ')}`);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {invalidExpression, invalidTransitionAlias} from '../error_helpers';\n\nexport const ANY_STATE = '*';\nexport declare type TransitionMatcherFn = (\n  fromState: any,\n  toState: any,\n  element: any,\n  params: {[key: string]: any},\n) => boolean;\n\nexport function parseTransitionExpr(\n  transitionValue: string | TransitionMatcherFn,\n  errors: Error[],\n): TransitionMatcherFn[] {\n  const expressions: TransitionMatcherFn[] = [];\n  if (typeof transitionValue == 'string') {\n    transitionValue\n      .split(/\\s*,\\s*/)\n      .forEach((str) => parseInnerTransitionStr(str, expressions, errors));\n  } else {\n    expressions.push(<TransitionMatcherFn>transitionValue);\n  }\n  return expressions;\n}\n\nfunction parseInnerTransitionStr(\n  eventStr: string,\n  expressions: TransitionMatcherFn[],\n  errors: Error[],\n) {\n  if (eventStr[0] == ':') {\n    const result = parseAnimationAlias(eventStr, errors);\n    if (typeof result == 'function') {\n      expressions.push(result);\n      return;\n    }\n    eventStr = result;\n  }\n\n  const match = eventStr.match(/^(\\*|[-\\w]+)\\s*(<?[=-]>)\\s*(\\*|[-\\w]+)$/);\n  if (match == null || match.length < 4) {\n    errors.push(invalidExpression(eventStr));\n    return expressions;\n  }\n\n  const fromState = match[1];\n  const separator = match[2];\n  const toState = match[3];\n  expressions.push(makeLambdaFromStates(fromState, toState));\n\n  const isFullAnyStateExpr = fromState == ANY_STATE && toState == ANY_STATE;\n  if (separator[0] == '<' && !isFullAnyStateExpr) {\n    expressions.push(makeLambdaFromStates(toState, fromState));\n  }\n  return;\n}\n\nfunction parseAnimationAlias(alias: string, errors: Error[]): string | TransitionMatcherFn {\n  switch (alias) {\n    case ':enter':\n      return 'void => *';\n    case ':leave':\n      return '* => void';\n    case ':increment':\n      return (fromState: any, toState: any): boolean => parseFloat(toState) > parseFloat(fromState);\n    case ':decrement':\n      return (fromState: any, toState: any): boolean => parseFloat(toState) < parseFloat(fromState);\n    default:\n      errors.push(invalidTransitionAlias(alias));\n      return '* => *';\n  }\n}\n\n// DO NOT REFACTOR ... keep the follow set instantiations\n// with the values intact (closure compiler for some reason\n// removes follow-up lines that add the values outside of\n// the constructor...\nconst TRUE_BOOLEAN_VALUES = new Set<string>(['true', '1']);\nconst FALSE_BOOLEAN_VALUES = new Set<string>(['false', '0']);\n\nfunction makeLambdaFromStates(lhs: string, rhs: string): TransitionMatcherFn {\n  const LHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(lhs) || FALSE_BOOLEAN_VALUES.has(lhs);\n  const RHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(rhs) || FALSE_BOOLEAN_VALUES.has(rhs);\n\n  return (fromState: any, toState: any): boolean => {\n    let lhsMatch = lhs == ANY_STATE || lhs == fromState;\n    let rhsMatch = rhs == ANY_STATE || rhs == toState;\n\n    if (!lhsMatch && LHS_MATCH_BOOLEAN && typeof fromState === 'boolean') {\n      lhsMatch = fromState ? TRUE_BOOLEAN_VALUES.has(lhs) : FALSE_BOOLEAN_VALUES.has(lhs);\n    }\n    if (!rhsMatch && RHS_MATCH_BOOLEAN && typeof toState === 'boolean') {\n      rhsMatch = toState ? TRUE_BOOLEAN_VALUES.has(rhs) : FALSE_BOOLEAN_VALUES.has(rhs);\n    }\n\n    return lhsMatch && rhsMatch;\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {\n  AnimateTimings,\n  AnimationAnimateChildMetadata,\n  AnimationAnimateMetadata,\n  AnimationAnimateRefMetadata,\n  AnimationGroupMetadata,\n  AnimationKeyframesSequenceMetadata,\n  AnimationMetadata,\n  AnimationMetadataType,\n  AnimationOptions,\n  AnimationQueryMetadata,\n  AnimationQueryOptions,\n  AnimationReferenceMetadata,\n  AnimationSequenceMetadata,\n  AnimationStaggerMetadata,\n  AnimationStateMetadata,\n  AnimationStyleMetadata,\n  AnimationTransitionMetadata,\n  AnimationTriggerMetadata,\n  AUTO_STYLE,\n  style,\n  ɵStyleDataMap,\n} from '@angular/animations';\n\nimport {\n  invalidDefinition,\n  invalidKeyframes,\n  invalidOffset,\n  invalidParallelAnimation,\n  invalidProperty,\n  invalidStagger,\n  invalidState,\n  invalidStyleValue,\n  invalidTrigger,\n  keyframeOffsetsOutOfOrder,\n  keyframesMissingOffsets,\n} from '../error_helpers';\nimport {AnimationDriver} from '../render/animation_driver';\nimport {getOrSetDefaultValue} from '../render/shared';\nimport {\n  extractStyleParams,\n  NG_ANIMATING_SELECTOR,\n  NG_TRIGGER_SELECTOR,\n  normalizeAnimationEntry,\n  resolveTiming,\n  SUBSTITUTION_EXPR_START,\n  validateStyleParams,\n  visitDslNode,\n} from '../util';\nimport {pushUnrecognizedPropertiesWarning} from '../warning_helpers';\n\nimport {\n  AnimateAst,\n  AnimateChildAst,\n  AnimateRefAst,\n  Ast,\n  DynamicTimingAst,\n  GroupAst,\n  KeyframesAst,\n  QueryAst,\n  ReferenceAst,\n  SequenceAst,\n  StaggerAst,\n  StateAst,\n  StyleAst,\n  TimingAst,\n  TransitionAst,\n  TriggerAst,\n} from './animation_ast';\nimport {AnimationDslVisitor} from './animation_dsl_visitor';\nimport {parseTransitionExpr} from './animation_transition_expr';\n\nconst SELF_TOKEN = ':self';\nconst SELF_TOKEN_REGEX = new RegExp(`s*${SELF_TOKEN}s*,?`, 'g');\n\n/*\n * [Validation]\n * The visitor code below will traverse the animation AST generated by the animation verb functions\n * (the output is a tree of objects) and attempt to perform a series of validations on the data. The\n * following corner-cases will be validated:\n *\n * 1. Overlap of animations\n * Given that a CSS property cannot be animated in more than one place at the same time, it's\n * important that this behavior is detected and validated. The way in which this occurs is that\n * each time a style property is examined, a string-map containing the property will be updated with\n * the start and end times for when the property is used within an animation step.\n *\n * If there are two or more parallel animations that are currently running (these are invoked by the\n * group()) on the same element then the validator will throw an error. Since the start/end timing\n * values are collected for each property then if the current animation step is animating the same\n * property and its timing values fall anywhere into the window of time that the property is\n * currently being animated within then this is what causes an error.\n *\n * 2. Timing values\n * The validator will validate to see if a timing value of `duration delay easing` or\n * `durationNumber` is valid or not.\n *\n * (note that upon validation the code below will replace the timing data with an object containing\n * {duration,delay,easing}.\n *\n * 3. Offset Validation\n * Each of the style() calls are allowed to have an offset value when placed inside of keyframes().\n * Offsets within keyframes() are considered valid when:\n *\n *   - No offsets are used at all\n *   - Each style() entry contains an offset value\n *   - Each offset is between 0 and 1\n *   - Each offset is greater to or equal than the previous one\n *\n * Otherwise an error will be thrown.\n */\nexport function buildAnimationAst(\n  driver: AnimationDriver,\n  metadata: AnimationMetadata | AnimationMetadata[],\n  errors: Error[],\n  warnings: string[],\n): Ast<AnimationMetadataType> {\n  return new AnimationAstBuilderVisitor(driver).build(metadata, errors, warnings);\n}\n\nconst ROOT_SELECTOR = '';\n\nexport class AnimationAstBuilderVisitor implements AnimationDslVisitor {\n  constructor(private _driver: AnimationDriver) {}\n\n  build(\n    metadata: AnimationMetadata | AnimationMetadata[],\n    errors: Error[],\n    warnings: string[],\n  ): Ast<AnimationMetadataType> {\n    const context = new AnimationAstBuilderContext(errors);\n    this._resetContextStyleTimingState(context);\n    const ast = <Ast<AnimationMetadataType>>(\n      visitDslNode(this, normalizeAnimationEntry(metadata), context)\n    );\n\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (context.unsupportedCSSPropertiesFound.size) {\n        pushUnrecognizedPropertiesWarning(warnings, [\n          ...context.unsupportedCSSPropertiesFound.keys(),\n        ]);\n      }\n    }\n\n    return ast;\n  }\n\n  private _resetContextStyleTimingState(context: AnimationAstBuilderContext) {\n    context.currentQuerySelector = ROOT_SELECTOR;\n    context.collectedStyles = new Map<string, Map<string, StyleTimeTuple>>();\n    context.collectedStyles.set(ROOT_SELECTOR, new Map());\n    context.currentTime = 0;\n  }\n\n  visitTrigger(\n    metadata: AnimationTriggerMetadata,\n    context: AnimationAstBuilderContext,\n  ): TriggerAst {\n    let queryCount = (context.queryCount = 0);\n    let depCount = (context.depCount = 0);\n    const states: StateAst[] = [];\n    const transitions: TransitionAst[] = [];\n    if (metadata.name.charAt(0) == '@') {\n      context.errors.push(invalidTrigger());\n    }\n\n    metadata.definitions.forEach((def) => {\n      this._resetContextStyleTimingState(context);\n      if (def.type == AnimationMetadataType.State) {\n        const stateDef = def as AnimationStateMetadata;\n        const name = stateDef.name;\n        name\n          .toString()\n          .split(/\\s*,\\s*/)\n          .forEach((n) => {\n            stateDef.name = n;\n            states.push(this.visitState(stateDef, context));\n          });\n        stateDef.name = name;\n      } else if (def.type == AnimationMetadataType.Transition) {\n        const transition = this.visitTransition(def as AnimationTransitionMetadata, context);\n        queryCount += transition.queryCount;\n        depCount += transition.depCount;\n        transitions.push(transition);\n      } else {\n        context.errors.push(invalidDefinition());\n      }\n    });\n\n    return {\n      type: AnimationMetadataType.Trigger,\n      name: metadata.name,\n      states,\n      transitions,\n      queryCount,\n      depCount,\n      options: null,\n    };\n  }\n\n  visitState(metadata: AnimationStateMetadata, context: AnimationAstBuilderContext): StateAst {\n    const styleAst = this.visitStyle(metadata.styles, context);\n    const astParams = (metadata.options && metadata.options.params) || null;\n    if (styleAst.containsDynamicStyles) {\n      const missingSubs = new Set<string>();\n      const params = astParams || {};\n      styleAst.styles.forEach((style) => {\n        if (style instanceof Map) {\n          style.forEach((value) => {\n            extractStyleParams(value).forEach((sub) => {\n              if (!params.hasOwnProperty(sub)) {\n                missingSubs.add(sub);\n              }\n            });\n          });\n        }\n      });\n      if (missingSubs.size) {\n        context.errors.push(invalidState(metadata.name, [...missingSubs.values()]));\n      }\n    }\n\n    return {\n      type: AnimationMetadataType.State,\n      name: metadata.name,\n      style: styleAst,\n      options: astParams ? {params: astParams} : null,\n    };\n  }\n\n  visitTransition(\n    metadata: AnimationTransitionMetadata,\n    context: AnimationAstBuilderContext,\n  ): TransitionAst {\n    context.queryCount = 0;\n    context.depCount = 0;\n    const animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);\n    const matchers = parseTransitionExpr(metadata.expr, context.errors);\n\n    return {\n      type: AnimationMetadataType.Transition,\n      matchers,\n      animation,\n      queryCount: context.queryCount,\n      depCount: context.depCount,\n      options: normalizeAnimationOptions(metadata.options),\n    };\n  }\n\n  visitSequence(\n    metadata: AnimationSequenceMetadata,\n    context: AnimationAstBuilderContext,\n  ): SequenceAst {\n    return {\n      type: AnimationMetadataType.Sequence,\n      steps: metadata.steps.map((s) => visitDslNode(this, s, context)),\n      options: normalizeAnimationOptions(metadata.options),\n    };\n  }\n\n  visitGroup(metadata: AnimationGroupMetadata, context: AnimationAstBuilderContext): GroupAst {\n    const currentTime = context.currentTime;\n    let furthestTime = 0;\n    const steps = metadata.steps.map((step) => {\n      context.currentTime = currentTime;\n      const innerAst = visitDslNode(this, step, context);\n      furthestTime = Math.max(furthestTime, context.currentTime);\n      return innerAst;\n    });\n\n    context.currentTime = furthestTime;\n    return {\n      type: AnimationMetadataType.Group,\n      steps,\n      options: normalizeAnimationOptions(metadata.options),\n    };\n  }\n\n  visitAnimate(\n    metadata: AnimationAnimateMetadata,\n    context: AnimationAstBuilderContext,\n  ): AnimateAst {\n    const timingAst = constructTimingAst(metadata.timings, context.errors);\n    context.currentAnimateTimings = timingAst;\n    let styleAst: StyleAst | KeyframesAst;\n    let styleMetadata: AnimationStyleMetadata | AnimationKeyframesSequenceMetadata = metadata.styles\n      ? metadata.styles\n      : style({});\n    if (styleMetadata.type == AnimationMetadataType.Keyframes) {\n      styleAst = this.visitKeyframes(styleMetadata as AnimationKeyframesSequenceMetadata, context);\n    } else {\n      let styleMetadata = metadata.styles as AnimationStyleMetadata;\n      let isEmpty = false;\n      if (!styleMetadata) {\n        isEmpty = true;\n        const newStyleData: {[prop: string]: string | number} = {};\n        if (timingAst.easing) {\n          newStyleData['easing'] = timingAst.easing;\n        }\n        styleMetadata = style(newStyleData);\n      }\n      context.currentTime += timingAst.duration + timingAst.delay;\n      const _styleAst = this.visitStyle(styleMetadata, context);\n      _styleAst.isEmptyStep = isEmpty;\n      styleAst = _styleAst;\n    }\n\n    context.currentAnimateTimings = null;\n    return {\n      type: AnimationMetadataType.Animate,\n      timings: timingAst,\n      style: styleAst,\n      options: null,\n    };\n  }\n\n  visitStyle(metadata: AnimationStyleMetadata, context: AnimationAstBuilderContext): StyleAst {\n    const ast = this._makeStyleAst(metadata, context);\n    this._validateStyleAst(ast, context);\n    return ast;\n  }\n\n  private _makeStyleAst(\n    metadata: AnimationStyleMetadata,\n    context: AnimationAstBuilderContext,\n  ): StyleAst {\n    const styles: Array<ɵStyleDataMap | string> = [];\n    const metadataStyles = Array.isArray(metadata.styles) ? metadata.styles : [metadata.styles];\n\n    for (let styleTuple of metadataStyles) {\n      if (typeof styleTuple === 'string') {\n        if (styleTuple === AUTO_STYLE) {\n          styles.push(styleTuple);\n        } else {\n          context.errors.push(invalidStyleValue(styleTuple));\n        }\n      } else {\n        styles.push(new Map(Object.entries(styleTuple)));\n      }\n    }\n\n    let containsDynamicStyles = false;\n    let collectedEasing: string | null = null;\n    styles.forEach((styleData) => {\n      if (styleData instanceof Map) {\n        if (styleData.has('easing')) {\n          collectedEasing = styleData.get('easing') as string;\n          styleData.delete('easing');\n        }\n        if (!containsDynamicStyles) {\n          for (let value of styleData.values()) {\n            if (value!.toString().indexOf(SUBSTITUTION_EXPR_START) >= 0) {\n              containsDynamicStyles = true;\n              break;\n            }\n          }\n        }\n      }\n    });\n\n    return {\n      type: AnimationMetadataType.Style,\n      styles,\n      easing: collectedEasing,\n      offset: metadata.offset,\n      containsDynamicStyles,\n      options: null,\n    };\n  }\n\n  private _validateStyleAst(ast: StyleAst, context: AnimationAstBuilderContext): void {\n    const timings = context.currentAnimateTimings;\n    let endTime = context.currentTime;\n    let startTime = context.currentTime;\n    if (timings && startTime > 0) {\n      startTime -= timings.duration + timings.delay;\n    }\n\n    ast.styles.forEach((tuple) => {\n      if (typeof tuple === 'string') return;\n\n      tuple.forEach((value, prop) => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          if (!this._driver.validateStyleProperty(prop)) {\n            tuple.delete(prop);\n            context.unsupportedCSSPropertiesFound.add(prop);\n            return;\n          }\n        }\n\n        // This is guaranteed to have a defined Map at this querySelector location making it\n        // safe to add the assertion here. It is set as a default empty map in prior methods.\n        const collectedStyles = context.collectedStyles.get(context.currentQuerySelector!)!;\n        const collectedEntry = collectedStyles.get(prop);\n        let updateCollectedStyle = true;\n        if (collectedEntry) {\n          if (\n            startTime != endTime &&\n            startTime >= collectedEntry.startTime &&\n            endTime <= collectedEntry.endTime\n          ) {\n            context.errors.push(\n              invalidParallelAnimation(\n                prop,\n                collectedEntry.startTime,\n                collectedEntry.endTime,\n                startTime,\n                endTime,\n              ),\n            );\n            updateCollectedStyle = false;\n          }\n\n          // we always choose the smaller start time value since we\n          // want to have a record of the entire animation window where\n          // the style property is being animated in between\n          startTime = collectedEntry.startTime;\n        }\n\n        if (updateCollectedStyle) {\n          collectedStyles.set(prop, {startTime, endTime});\n        }\n\n        if (context.options) {\n          validateStyleParams(value, context.options, context.errors);\n        }\n      });\n    });\n  }\n\n  visitKeyframes(\n    metadata: AnimationKeyframesSequenceMetadata,\n    context: AnimationAstBuilderContext,\n  ): KeyframesAst {\n    const ast: KeyframesAst = {type: AnimationMetadataType.Keyframes, styles: [], options: null};\n    if (!context.currentAnimateTimings) {\n      context.errors.push(invalidKeyframes());\n      return ast;\n    }\n\n    const MAX_KEYFRAME_OFFSET = 1;\n\n    let totalKeyframesWithOffsets = 0;\n    const offsets: number[] = [];\n    let offsetsOutOfOrder = false;\n    let keyframesOutOfRange = false;\n    let previousOffset: number = 0;\n\n    const keyframes: StyleAst[] = metadata.steps.map((styles) => {\n      const style = this._makeStyleAst(styles, context);\n      let offsetVal: number | null =\n        style.offset != null ? style.offset : consumeOffset(style.styles);\n      let offset: number = 0;\n      if (offsetVal != null) {\n        totalKeyframesWithOffsets++;\n        offset = style.offset = offsetVal;\n      }\n      keyframesOutOfRange = keyframesOutOfRange || offset < 0 || offset > 1;\n      offsetsOutOfOrder = offsetsOutOfOrder || offset < previousOffset;\n      previousOffset = offset;\n      offsets.push(offset);\n      return style;\n    });\n\n    if (keyframesOutOfRange) {\n      context.errors.push(invalidOffset());\n    }\n\n    if (offsetsOutOfOrder) {\n      context.errors.push(keyframeOffsetsOutOfOrder());\n    }\n\n    const length = metadata.steps.length;\n    let generatedOffset = 0;\n    if (totalKeyframesWithOffsets > 0 && totalKeyframesWithOffsets < length) {\n      context.errors.push(keyframesMissingOffsets());\n    } else if (totalKeyframesWithOffsets == 0) {\n      generatedOffset = MAX_KEYFRAME_OFFSET / (length - 1);\n    }\n\n    const limit = length - 1;\n    const currentTime = context.currentTime;\n    const currentAnimateTimings = context.currentAnimateTimings!;\n    const animateDuration = currentAnimateTimings.duration;\n    keyframes.forEach((kf, i) => {\n      const offset = generatedOffset > 0 ? (i == limit ? 1 : generatedOffset * i) : offsets[i];\n      const durationUpToThisFrame = offset * animateDuration;\n      context.currentTime = currentTime + currentAnimateTimings.delay + durationUpToThisFrame;\n      currentAnimateTimings.duration = durationUpToThisFrame;\n      this._validateStyleAst(kf, context);\n      kf.offset = offset;\n\n      ast.styles.push(kf);\n    });\n\n    return ast;\n  }\n\n  visitReference(\n    metadata: AnimationReferenceMetadata,\n    context: AnimationAstBuilderContext,\n  ): ReferenceAst {\n    return {\n      type: AnimationMetadataType.Reference,\n      animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context),\n      options: normalizeAnimationOptions(metadata.options),\n    };\n  }\n\n  visitAnimateChild(\n    metadata: AnimationAnimateChildMetadata,\n    context: AnimationAstBuilderContext,\n  ): AnimateChildAst {\n    context.depCount++;\n    return {\n      type: AnimationMetadataType.AnimateChild,\n      options: normalizeAnimationOptions(metadata.options),\n    };\n  }\n\n  visitAnimateRef(\n    metadata: AnimationAnimateRefMetadata,\n    context: AnimationAstBuilderContext,\n  ): AnimateRefAst {\n    return {\n      type: AnimationMetadataType.AnimateRef,\n      animation: this.visitReference(metadata.animation, context),\n      options: normalizeAnimationOptions(metadata.options),\n    };\n  }\n\n  visitQuery(metadata: AnimationQueryMetadata, context: AnimationAstBuilderContext): QueryAst {\n    const parentSelector = context.currentQuerySelector!;\n    const options = (metadata.options || {}) as AnimationQueryOptions;\n\n    context.queryCount++;\n    context.currentQuery = metadata;\n    const [selector, includeSelf] = normalizeSelector(metadata.selector);\n    context.currentQuerySelector = parentSelector.length\n      ? parentSelector + ' ' + selector\n      : selector;\n    getOrSetDefaultValue(context.collectedStyles, context.currentQuerySelector, new Map());\n\n    const animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);\n    context.currentQuery = null;\n    context.currentQuerySelector = parentSelector;\n\n    return {\n      type: AnimationMetadataType.Query,\n      selector,\n      limit: options.limit || 0,\n      optional: !!options.optional,\n      includeSelf,\n      animation,\n      originalSelector: metadata.selector,\n      options: normalizeAnimationOptions(metadata.options),\n    };\n  }\n\n  visitStagger(\n    metadata: AnimationStaggerMetadata,\n    context: AnimationAstBuilderContext,\n  ): StaggerAst {\n    if (!context.currentQuery) {\n      context.errors.push(invalidStagger());\n    }\n    const timings =\n      metadata.timings === 'full'\n        ? {duration: 0, delay: 0, easing: 'full'}\n        : resolveTiming(metadata.timings, context.errors, true);\n\n    return {\n      type: AnimationMetadataType.Stagger,\n      animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context),\n      timings,\n      options: null,\n    };\n  }\n}\n\nfunction normalizeSelector(selector: string): [string, boolean] {\n  const hasAmpersand = selector.split(/\\s*,\\s*/).find((token) => token == SELF_TOKEN)\n    ? true\n    : false;\n  if (hasAmpersand) {\n    selector = selector.replace(SELF_TOKEN_REGEX, '');\n  }\n\n  // Note: the :enter and :leave aren't normalized here since those\n  // selectors are filled in at runtime during timeline building\n  selector = selector\n    .replace(/@\\*/g, NG_TRIGGER_SELECTOR)\n    .replace(/@\\w+/g, (match) => NG_TRIGGER_SELECTOR + '-' + match.slice(1))\n    .replace(/:animating/g, NG_ANIMATING_SELECTOR);\n\n  return [selector, hasAmpersand];\n}\n\nfunction normalizeParams(obj: {[key: string]: any} | any): {[key: string]: any} | null {\n  return obj ? {...obj} : null;\n}\n\nexport type StyleTimeTuple = {\n  startTime: number;\n  endTime: number;\n};\n\nexport class AnimationAstBuilderContext {\n  public queryCount: number = 0;\n  public depCount: number = 0;\n  public currentTransition: AnimationTransitionMetadata | null = null;\n  public currentQuery: AnimationQueryMetadata | null = null;\n  public currentQuerySelector: string | null = null;\n  public currentAnimateTimings: TimingAst | null = null;\n  public currentTime: number = 0;\n  public collectedStyles = new Map<string, Map<string, StyleTimeTuple>>();\n  public options: AnimationOptions | null = null;\n  public unsupportedCSSPropertiesFound: Set<string> = new Set<string>();\n  constructor(public errors: Error[]) {}\n}\n\ntype OffsetStyles = string | ɵStyleDataMap;\n\nfunction consumeOffset(styles: OffsetStyles | Array<OffsetStyles>): number | null {\n  if (typeof styles == 'string') return null;\n\n  let offset: number | null = null;\n\n  if (Array.isArray(styles)) {\n    styles.forEach((styleTuple) => {\n      if (styleTuple instanceof Map && styleTuple.has('offset')) {\n        const obj = styleTuple as ɵStyleDataMap;\n        offset = parseFloat(obj.get('offset') as string);\n        obj.delete('offset');\n      }\n    });\n  } else if (styles instanceof Map && styles.has('offset')) {\n    const obj = styles;\n    offset = parseFloat(obj.get('offset') as string);\n    obj.delete('offset');\n  }\n  return offset;\n}\n\nfunction constructTimingAst(value: string | number | AnimateTimings, errors: Error[]) {\n  if (value.hasOwnProperty('duration')) {\n    return value as AnimateTimings;\n  }\n\n  if (typeof value == 'number') {\n    const duration = resolveTiming(value, errors).duration;\n    return makeTimingAst(duration, 0, '');\n  }\n\n  const strValue = value as string;\n  const isDynamic = strValue.split(/\\s+/).some((v) => v.charAt(0) == '{' && v.charAt(1) == '{');\n  if (isDynamic) {\n    const ast = makeTimingAst(0, 0, '') as any;\n    ast.dynamic = true;\n    ast.strValue = strValue;\n    return ast as DynamicTimingAst;\n  }\n\n  const timings = resolveTiming(strValue, errors);\n  return makeTimingAst(timings.duration, timings.delay, timings.easing);\n}\n\nfunction normalizeAnimationOptions(options: AnimationOptions | null): AnimationOptions {\n  if (options) {\n    options = {...options};\n    if (options['params']) {\n      options['params'] = normalizeParams(options['params'])!;\n    }\n  } else {\n    options = {};\n  }\n  return options;\n}\n\nfunction makeTimingAst(duration: number, delay: number, easing: string | null): TimingAst {\n  return {duration, delay, easing};\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {ɵStyleDataMap} from '@angular/animations';\n\nimport {\n  AnimationEngineInstruction,\n  AnimationTransitionInstructionType,\n} from '../render/animation_engine_instruction';\n\nexport interface AnimationTimelineInstruction extends AnimationEngineInstruction {\n  element: any;\n  keyframes: Array<ɵStyleDataMap>;\n  preStyleProps: string[];\n  postStyleProps: string[];\n  duration: number;\n  delay: number;\n  totalTime: number;\n  easing: string | null;\n  stretchStartingKeyframe?: boolean;\n  subTimeline: boolean;\n}\n\nexport function createTimelineInstruction(\n  element: any,\n  keyframes: Array<ɵStyleDataMap>,\n  preStyleProps: string[],\n  postStyleProps: string[],\n  duration: number,\n  delay: number,\n  easing: string | null = null,\n  subTimeline: boolean = false,\n): AnimationTimelineInstruction {\n  return {\n    type: AnimationTransitionInstructionType.TimelineAnimation,\n    element,\n    keyframes,\n    preStyleProps,\n    postStyleProps,\n    duration,\n    delay,\n    totalTime: duration + delay,\n    easing,\n    subTimeline,\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AnimationTimelineInstruction} from './animation_timeline_instruction';\n\nexport class ElementInstructionMap {\n  private _map = new Map<any, AnimationTimelineInstruction[]>();\n\n  get(element: any): AnimationTimelineInstruction[] {\n    return this._map.get(element) || [];\n  }\n\n  append(element: any, instructions: AnimationTimelineInstruction[]) {\n    let existingInstructions = this._map.get(element);\n    if (!existingInstructions) {\n      this._map.set(element, (existingInstructions = []));\n    }\n    existingInstructions.push(...instructions);\n  }\n\n  has(element: any): boolean {\n    return this._map.has(element);\n  }\n\n  clear() {\n    this._map.clear();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {\n  AnimateChildOptions,\n  AnimateTimings,\n  AnimationMetadataType,\n  AnimationOptions,\n  AnimationQueryOptions,\n  AUTO_STYLE,\n  ɵPRE_STYLE as PRE_STYLE,\n  ɵStyleDataMap,\n} from '@angular/animations';\n\nimport {invalidQuery} from '../error_helpers';\nimport {AnimationDriver} from '../render/animation_driver';\nimport {interpolateParams, resolveTiming, resolveTimingValue, visitDslNode} from '../util';\n\nimport {\n  AnimateAst,\n  AnimateChildAst,\n  AnimateRefAst,\n  Ast,\n  AstVisitor,\n  DynamicTimingAst,\n  GroupAst,\n  KeyframesAst,\n  QueryAst,\n  ReferenceAst,\n  SequenceAst,\n  StaggerAst,\n  StateAst,\n  StyleAst,\n  TimingAst,\n  TransitionAst,\n  TriggerAst,\n} from './animation_ast';\nimport {\n  AnimationTimelineInstruction,\n  createTimelineInstruction,\n} from './animation_timeline_instruction';\nimport {ElementInstructionMap} from './element_instruction_map';\n\nconst ONE_FRAME_IN_MILLISECONDS = 1;\nconst ENTER_TOKEN = ':enter';\nconst ENTER_TOKEN_REGEX = new RegExp(ENTER_TOKEN, 'g');\nconst LEAVE_TOKEN = ':leave';\nconst LEAVE_TOKEN_REGEX = new RegExp(LEAVE_TOKEN, 'g');\n\n/*\n * The code within this file aims to generate web-animations-compatible keyframes from Angular's\n * animation DSL code.\n *\n * The code below will be converted from:\n *\n * ```\n * sequence([\n *   style({ opacity: 0 }),\n *   animate(1000, style({ opacity: 0 }))\n * ])\n * ```\n *\n * To:\n * ```\n * keyframes = [{ opacity: 0, offset: 0 }, { opacity: 1, offset: 1 }]\n * duration = 1000\n * delay = 0\n * easing = ''\n * ```\n *\n * For this operation to cover the combination of animation verbs (style, animate, group, etc...) a\n * combination of AST traversal and merge-sort-like algorithms are used.\n *\n * [AST Traversal]\n * Each of the animation verbs, when executed, will return an string-map object representing what\n * type of action it is (style, animate, group, etc...) and the data associated with it. This means\n * that when functional composition mix of these functions is evaluated (like in the example above)\n * then it will end up producing a tree of objects representing the animation itself.\n *\n * When this animation object tree is processed by the visitor code below it will visit each of the\n * verb statements within the visitor. And during each visit it will build the context of the\n * animation keyframes by interacting with the `TimelineBuilder`.\n *\n * [TimelineBuilder]\n * This class is responsible for tracking the styles and building a series of keyframe objects for a\n * timeline between a start and end time. The builder starts off with an initial timeline and each\n * time the AST comes across a `group()`, `keyframes()` or a combination of the two within a\n * `sequence()` then it will generate a sub timeline for each step as well as a new one after\n * they are complete.\n *\n * As the AST is traversed, the timing state on each of the timelines will be incremented. If a sub\n * timeline was created (based on one of the cases above) then the parent timeline will attempt to\n * merge the styles used within the sub timelines into itself (only with group() this will happen).\n * This happens with a merge operation (much like how the merge works in mergeSort) and it will only\n * copy the most recently used styles from the sub timelines into the parent timeline. This ensures\n * that if the styles are used later on in another phase of the animation then they will be the most\n * up-to-date values.\n *\n * [How Missing Styles Are Updated]\n * Each timeline has a `backFill` property which is responsible for filling in new styles into\n * already processed keyframes if a new style shows up later within the animation sequence.\n *\n * ```\n * sequence([\n *   style({ width: 0 }),\n *   animate(1000, style({ width: 100 })),\n *   animate(1000, style({ width: 200 })),\n *   animate(1000, style({ width: 300 }))\n *   animate(1000, style({ width: 400, height: 400 })) // notice how `height` doesn't exist anywhere\n * else\n * ])\n * ```\n *\n * What is happening here is that the `height` value is added later in the sequence, but is missing\n * from all previous animation steps. Therefore when a keyframe is created it would also be missing\n * from all previous keyframes up until where it is first used. For the timeline keyframe generation\n * to properly fill in the style it will place the previous value (the value from the parent\n * timeline) or a default value of `*` into the backFill map.\n *\n * When a sub-timeline is created it will have its own backFill property. This is done so that\n * styles present within the sub-timeline do not accidentally seep into the previous/future timeline\n * keyframes\n *\n * [Validation]\n * The code in this file is not responsible for validation. That functionality happens with within\n * the `AnimationValidatorVisitor` code.\n */\nexport function buildAnimationTimelines(\n  driver: AnimationDriver,\n  rootElement: any,\n  ast: Ast<AnimationMetadataType>,\n  enterClassName: string,\n  leaveClassName: string,\n  startingStyles: ɵStyleDataMap = new Map(),\n  finalStyles: ɵStyleDataMap = new Map(),\n  options: AnimationOptions,\n  subInstructions?: ElementInstructionMap,\n  errors: Error[] = [],\n): AnimationTimelineInstruction[] {\n  return new AnimationTimelineBuilderVisitor().buildKeyframes(\n    driver,\n    rootElement,\n    ast,\n    enterClassName,\n    leaveClassName,\n    startingStyles,\n    finalStyles,\n    options,\n    subInstructions,\n    errors,\n  );\n}\n\nexport class AnimationTimelineBuilderVisitor implements AstVisitor {\n  buildKeyframes(\n    driver: AnimationDriver,\n    rootElement: any,\n    ast: Ast<AnimationMetadataType>,\n    enterClassName: string,\n    leaveClassName: string,\n    startingStyles: ɵStyleDataMap,\n    finalStyles: ɵStyleDataMap,\n    options: AnimationOptions,\n    subInstructions?: ElementInstructionMap,\n    errors: Error[] = [],\n  ): AnimationTimelineInstruction[] {\n    subInstructions = subInstructions || new ElementInstructionMap();\n    const context = new AnimationTimelineContext(\n      driver,\n      rootElement,\n      subInstructions,\n      enterClassName,\n      leaveClassName,\n      errors,\n      [],\n    );\n    context.options = options;\n    const delay = options.delay ? resolveTimingValue(options.delay) : 0;\n    context.currentTimeline.delayNextStep(delay);\n    context.currentTimeline.setStyles([startingStyles], null, context.errors, options);\n\n    visitDslNode(this, ast, context);\n\n    // this checks to see if an actual animation happened\n    const timelines = context.timelines.filter((timeline) => timeline.containsAnimation());\n\n    // note: we just want to apply the final styles for the rootElement, so we do not\n    //       just apply the styles to the last timeline but the last timeline which\n    //       element is the root one (basically `*`-styles are replaced with the actual\n    //       state style values only for the root element)\n    if (timelines.length && finalStyles.size) {\n      let lastRootTimeline: TimelineBuilder | undefined;\n      for (let i = timelines.length - 1; i >= 0; i--) {\n        const timeline = timelines[i];\n        if (timeline.element === rootElement) {\n          lastRootTimeline = timeline;\n          break;\n        }\n      }\n      if (lastRootTimeline && !lastRootTimeline.allowOnlyTimelineStyles()) {\n        lastRootTimeline.setStyles([finalStyles], null, context.errors, options);\n      }\n    }\n    return timelines.length\n      ? timelines.map((timeline) => timeline.buildKeyframes())\n      : [createTimelineInstruction(rootElement, [], [], [], 0, delay, '', false)];\n  }\n\n  visitTrigger(ast: TriggerAst, context: AnimationTimelineContext): any {\n    // these values are not visited in this AST\n  }\n\n  visitState(ast: StateAst, context: AnimationTimelineContext): any {\n    // these values are not visited in this AST\n  }\n\n  visitTransition(ast: TransitionAst, context: AnimationTimelineContext): any {\n    // these values are not visited in this AST\n  }\n\n  visitAnimateChild(ast: AnimateChildAst, context: AnimationTimelineContext): any {\n    const elementInstructions = context.subInstructions.get(context.element);\n    if (elementInstructions) {\n      const innerContext = context.createSubContext(ast.options);\n      const startTime = context.currentTimeline.currentTime;\n      const endTime = this._visitSubInstructions(\n        elementInstructions,\n        innerContext,\n        innerContext.options as AnimateChildOptions,\n      );\n      if (startTime != endTime) {\n        // we do this on the upper context because we created a sub context for\n        // the sub child animations\n        context.transformIntoNewTimeline(endTime);\n      }\n    }\n    context.previousNode = ast;\n  }\n\n  visitAnimateRef(ast: AnimateRefAst, context: AnimationTimelineContext): any {\n    const innerContext = context.createSubContext(ast.options);\n    innerContext.transformIntoNewTimeline();\n    this._applyAnimationRefDelays([ast.options, ast.animation.options], context, innerContext);\n    this.visitReference(ast.animation, innerContext);\n    context.transformIntoNewTimeline(innerContext.currentTimeline.currentTime);\n    context.previousNode = ast;\n  }\n\n  private _applyAnimationRefDelays(\n    animationsRefsOptions: (AnimationOptions | null)[],\n    context: AnimationTimelineContext,\n    innerContext: AnimationTimelineContext,\n  ) {\n    for (const animationRefOptions of animationsRefsOptions) {\n      const animationDelay = animationRefOptions?.delay;\n      if (animationDelay) {\n        const animationDelayValue =\n          typeof animationDelay === 'number'\n            ? animationDelay\n            : resolveTimingValue(\n                interpolateParams(\n                  animationDelay,\n                  animationRefOptions?.params ?? {},\n                  context.errors,\n                ),\n              );\n        innerContext.delayNextStep(animationDelayValue);\n      }\n    }\n  }\n\n  private _visitSubInstructions(\n    instructions: AnimationTimelineInstruction[],\n    context: AnimationTimelineContext,\n    options: AnimateChildOptions,\n  ): number {\n    const startTime = context.currentTimeline.currentTime;\n    let furthestTime = startTime;\n\n    // this is a special-case for when a user wants to skip a sub\n    // animation from being fired entirely.\n    const duration = options.duration != null ? resolveTimingValue(options.duration) : null;\n    const delay = options.delay != null ? resolveTimingValue(options.delay) : null;\n    if (duration !== 0) {\n      instructions.forEach((instruction) => {\n        const instructionTimings = context.appendInstructionToTimeline(\n          instruction,\n          duration,\n          delay,\n        );\n        furthestTime = Math.max(\n          furthestTime,\n          instructionTimings.duration + instructionTimings.delay,\n        );\n      });\n    }\n\n    return furthestTime;\n  }\n\n  visitReference(ast: ReferenceAst, context: AnimationTimelineContext) {\n    context.updateOptions(ast.options, true);\n    visitDslNode(this, ast.animation, context);\n    context.previousNode = ast;\n  }\n\n  visitSequence(ast: SequenceAst, context: AnimationTimelineContext) {\n    const subContextCount = context.subContextCount;\n    let ctx = context;\n    const options = ast.options;\n\n    if (options && (options.params || options.delay)) {\n      ctx = context.createSubContext(options);\n      ctx.transformIntoNewTimeline();\n\n      if (options.delay != null) {\n        if (ctx.previousNode.type == AnimationMetadataType.Style) {\n          ctx.currentTimeline.snapshotCurrentStyles();\n          ctx.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n        }\n\n        const delay = resolveTimingValue(options.delay);\n        ctx.delayNextStep(delay);\n      }\n    }\n\n    if (ast.steps.length) {\n      ast.steps.forEach((s) => visitDslNode(this, s, ctx));\n\n      // this is here just in case the inner steps only contain or end with a style() call\n      ctx.currentTimeline.applyStylesToKeyframe();\n\n      // this means that some animation function within the sequence\n      // ended up creating a sub timeline (which means the current\n      // timeline cannot overlap with the contents of the sequence)\n      if (ctx.subContextCount > subContextCount) {\n        ctx.transformIntoNewTimeline();\n      }\n    }\n\n    context.previousNode = ast;\n  }\n\n  visitGroup(ast: GroupAst, context: AnimationTimelineContext) {\n    const innerTimelines: TimelineBuilder[] = [];\n    let furthestTime = context.currentTimeline.currentTime;\n    const delay = ast.options && ast.options.delay ? resolveTimingValue(ast.options.delay) : 0;\n\n    ast.steps.forEach((s) => {\n      const innerContext = context.createSubContext(ast.options);\n      if (delay) {\n        innerContext.delayNextStep(delay);\n      }\n\n      visitDslNode(this, s, innerContext);\n      furthestTime = Math.max(furthestTime, innerContext.currentTimeline.currentTime);\n      innerTimelines.push(innerContext.currentTimeline);\n    });\n\n    // this operation is run after the AST loop because otherwise\n    // if the parent timeline's collected styles were updated then\n    // it would pass in invalid data into the new-to-be forked items\n    innerTimelines.forEach((timeline) =>\n      context.currentTimeline.mergeTimelineCollectedStyles(timeline),\n    );\n    context.transformIntoNewTimeline(furthestTime);\n    context.previousNode = ast;\n  }\n\n  private _visitTiming(ast: TimingAst, context: AnimationTimelineContext): AnimateTimings {\n    if ((ast as DynamicTimingAst).dynamic) {\n      const strValue = (ast as DynamicTimingAst).strValue;\n      const timingValue = context.params\n        ? interpolateParams(strValue, context.params, context.errors)\n        : strValue;\n      return resolveTiming(timingValue, context.errors);\n    } else {\n      return {duration: ast.duration, delay: ast.delay, easing: ast.easing};\n    }\n  }\n\n  visitAnimate(ast: AnimateAst, context: AnimationTimelineContext) {\n    const timings = (context.currentAnimateTimings = this._visitTiming(ast.timings, context));\n    const timeline = context.currentTimeline;\n    if (timings.delay) {\n      context.incrementTime(timings.delay);\n      timeline.snapshotCurrentStyles();\n    }\n\n    const style = ast.style;\n    if (style.type == AnimationMetadataType.Keyframes) {\n      this.visitKeyframes(style, context);\n    } else {\n      context.incrementTime(timings.duration);\n      this.visitStyle(style as StyleAst, context);\n      timeline.applyStylesToKeyframe();\n    }\n\n    context.currentAnimateTimings = null;\n    context.previousNode = ast;\n  }\n\n  visitStyle(ast: StyleAst, context: AnimationTimelineContext) {\n    const timeline = context.currentTimeline;\n    const timings = context.currentAnimateTimings!;\n\n    // this is a special case for when a style() call\n    // directly follows  an animate() call (but not inside of an animate() call)\n    if (!timings && timeline.hasCurrentStyleProperties()) {\n      timeline.forwardFrame();\n    }\n\n    const easing = (timings && timings.easing) || ast.easing;\n    if (ast.isEmptyStep) {\n      timeline.applyEmptyStep(easing);\n    } else {\n      timeline.setStyles(ast.styles, easing, context.errors, context.options);\n    }\n\n    context.previousNode = ast;\n  }\n\n  visitKeyframes(ast: KeyframesAst, context: AnimationTimelineContext) {\n    const currentAnimateTimings = context.currentAnimateTimings!;\n    const startTime = context.currentTimeline!.duration;\n    const duration = currentAnimateTimings.duration;\n    const innerContext = context.createSubContext();\n    const innerTimeline = innerContext.currentTimeline;\n    innerTimeline.easing = currentAnimateTimings.easing;\n\n    ast.styles.forEach((step) => {\n      const offset: number = step.offset || 0;\n      innerTimeline.forwardTime(offset * duration);\n      innerTimeline.setStyles(step.styles, step.easing, context.errors, context.options);\n      innerTimeline.applyStylesToKeyframe();\n    });\n\n    // this will ensure that the parent timeline gets all the styles from\n    // the child even if the new timeline below is not used\n    context.currentTimeline.mergeTimelineCollectedStyles(innerTimeline);\n\n    // we do this because the window between this timeline and the sub timeline\n    // should ensure that the styles within are exactly the same as they were before\n    context.transformIntoNewTimeline(startTime + duration);\n    context.previousNode = ast;\n  }\n\n  visitQuery(ast: QueryAst, context: AnimationTimelineContext) {\n    // in the event that the first step before this is a style step we need\n    // to ensure the styles are applied before the children are animated\n    const startTime = context.currentTimeline.currentTime;\n    const options = (ast.options || {}) as AnimationQueryOptions;\n    const delay = options.delay ? resolveTimingValue(options.delay) : 0;\n\n    if (\n      delay &&\n      (context.previousNode.type === AnimationMetadataType.Style ||\n        (startTime == 0 && context.currentTimeline.hasCurrentStyleProperties()))\n    ) {\n      context.currentTimeline.snapshotCurrentStyles();\n      context.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n    }\n\n    let furthestTime = startTime;\n    const elms = context.invokeQuery(\n      ast.selector,\n      ast.originalSelector,\n      ast.limit,\n      ast.includeSelf,\n      options.optional ? true : false,\n      context.errors,\n    );\n\n    context.currentQueryTotal = elms.length;\n    let sameElementTimeline: TimelineBuilder | null = null;\n    elms.forEach((element, i) => {\n      context.currentQueryIndex = i;\n      const innerContext = context.createSubContext(ast.options, element);\n      if (delay) {\n        innerContext.delayNextStep(delay);\n      }\n\n      if (element === context.element) {\n        sameElementTimeline = innerContext.currentTimeline;\n      }\n\n      visitDslNode(this, ast.animation, innerContext);\n\n      // this is here just incase the inner steps only contain or end\n      // with a style() call (which is here to signal that this is a preparatory\n      // call to style an element before it is animated again)\n      innerContext.currentTimeline.applyStylesToKeyframe();\n\n      const endTime = innerContext.currentTimeline.currentTime;\n      furthestTime = Math.max(furthestTime, endTime);\n    });\n\n    context.currentQueryIndex = 0;\n    context.currentQueryTotal = 0;\n    context.transformIntoNewTimeline(furthestTime);\n\n    if (sameElementTimeline) {\n      context.currentTimeline.mergeTimelineCollectedStyles(sameElementTimeline);\n      context.currentTimeline.snapshotCurrentStyles();\n    }\n\n    context.previousNode = ast;\n  }\n\n  visitStagger(ast: StaggerAst, context: AnimationTimelineContext) {\n    const parentContext = context.parentContext!;\n    const tl = context.currentTimeline;\n    const timings = ast.timings;\n    const duration = Math.abs(timings.duration);\n    const maxTime = duration * (context.currentQueryTotal - 1);\n    let delay = duration * context.currentQueryIndex;\n\n    let staggerTransformer = timings.duration < 0 ? 'reverse' : timings.easing;\n    switch (staggerTransformer) {\n      case 'reverse':\n        delay = maxTime - delay;\n        break;\n      case 'full':\n        delay = parentContext.currentStaggerTime;\n        break;\n    }\n\n    const timeline = context.currentTimeline;\n    if (delay) {\n      timeline.delayNextStep(delay);\n    }\n\n    const startingTime = timeline.currentTime;\n    visitDslNode(this, ast.animation, context);\n    context.previousNode = ast;\n\n    // time = duration + delay\n    // the reason why this computation is so complex is because\n    // the inner timeline may either have a delay value or a stretched\n    // keyframe depending on if a subtimeline is not used or is used.\n    parentContext.currentStaggerTime =\n      tl.currentTime - startingTime + (tl.startTime - parentContext.currentTimeline.startTime);\n  }\n}\n\nexport declare type StyleAtTime = {\n  time: number;\n  value: string | number;\n};\n\nconst DEFAULT_NOOP_PREVIOUS_NODE = <Ast<AnimationMetadataType>>{};\nexport class AnimationTimelineContext {\n  public parentContext: AnimationTimelineContext | null = null;\n  public currentTimeline: TimelineBuilder;\n  public currentAnimateTimings: AnimateTimings | null = null;\n  public previousNode: Ast<AnimationMetadataType> = DEFAULT_NOOP_PREVIOUS_NODE;\n  public subContextCount = 0;\n  public options: AnimationOptions = {};\n  public currentQueryIndex: number = 0;\n  public currentQueryTotal: number = 0;\n  public currentStaggerTime: number = 0;\n\n  constructor(\n    private _driver: AnimationDriver,\n    public element: any,\n    public subInstructions: ElementInstructionMap,\n    private _enterClassName: string,\n    private _leaveClassName: string,\n    public errors: Error[],\n    public timelines: TimelineBuilder[],\n    initialTimeline?: TimelineBuilder,\n  ) {\n    this.currentTimeline = initialTimeline || new TimelineBuilder(this._driver, element, 0);\n    timelines.push(this.currentTimeline);\n  }\n\n  get params() {\n    return this.options.params;\n  }\n\n  updateOptions(options: AnimationOptions | null, skipIfExists?: boolean) {\n    if (!options) return;\n\n    const newOptions = options as any;\n    let optionsToUpdate = this.options;\n\n    // NOTE: this will get patched up when other animation methods support duration overrides\n    if (newOptions.duration != null) {\n      (optionsToUpdate as any).duration = resolveTimingValue(newOptions.duration);\n    }\n\n    if (newOptions.delay != null) {\n      optionsToUpdate.delay = resolveTimingValue(newOptions.delay);\n    }\n\n    const newParams = newOptions.params;\n    if (newParams) {\n      let paramsToUpdate: {[name: string]: any} = optionsToUpdate.params!;\n      if (!paramsToUpdate) {\n        paramsToUpdate = this.options.params = {};\n      }\n\n      Object.keys(newParams).forEach((name) => {\n        if (!skipIfExists || !paramsToUpdate.hasOwnProperty(name)) {\n          paramsToUpdate[name] = interpolateParams(newParams[name], paramsToUpdate, this.errors);\n        }\n      });\n    }\n  }\n\n  private _copyOptions() {\n    const options: AnimationOptions = {};\n    if (this.options) {\n      const oldParams = this.options.params;\n      if (oldParams) {\n        const params: {[name: string]: any} = (options['params'] = {});\n        Object.keys(oldParams).forEach((name) => {\n          params[name] = oldParams[name];\n        });\n      }\n    }\n    return options;\n  }\n\n  createSubContext(\n    options: AnimationOptions | null = null,\n    element?: any,\n    newTime?: number,\n  ): AnimationTimelineContext {\n    const target = element || this.element;\n    const context = new AnimationTimelineContext(\n      this._driver,\n      target,\n      this.subInstructions,\n      this._enterClassName,\n      this._leaveClassName,\n      this.errors,\n      this.timelines,\n      this.currentTimeline.fork(target, newTime || 0),\n    );\n    context.previousNode = this.previousNode;\n    context.currentAnimateTimings = this.currentAnimateTimings;\n\n    context.options = this._copyOptions();\n    context.updateOptions(options);\n\n    context.currentQueryIndex = this.currentQueryIndex;\n    context.currentQueryTotal = this.currentQueryTotal;\n    context.parentContext = this;\n    this.subContextCount++;\n    return context;\n  }\n\n  transformIntoNewTimeline(newTime?: number) {\n    this.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n    this.currentTimeline = this.currentTimeline.fork(this.element, newTime);\n    this.timelines.push(this.currentTimeline);\n    return this.currentTimeline;\n  }\n\n  appendInstructionToTimeline(\n    instruction: AnimationTimelineInstruction,\n    duration: number | null,\n    delay: number | null,\n  ): AnimateTimings {\n    const updatedTimings: AnimateTimings = {\n      duration: duration != null ? duration : instruction.duration,\n      delay: this.currentTimeline.currentTime + (delay != null ? delay : 0) + instruction.delay,\n      easing: '',\n    };\n    const builder = new SubTimelineBuilder(\n      this._driver,\n      instruction.element,\n      instruction.keyframes,\n      instruction.preStyleProps,\n      instruction.postStyleProps,\n      updatedTimings,\n      instruction.stretchStartingKeyframe,\n    );\n    this.timelines.push(builder);\n    return updatedTimings;\n  }\n\n  incrementTime(time: number) {\n    this.currentTimeline.forwardTime(this.currentTimeline.duration + time);\n  }\n\n  delayNextStep(delay: number) {\n    // negative delays are not yet supported\n    if (delay > 0) {\n      this.currentTimeline.delayNextStep(delay);\n    }\n  }\n\n  invokeQuery(\n    selector: string,\n    originalSelector: string,\n    limit: number,\n    includeSelf: boolean,\n    optional: boolean,\n    errors: Error[],\n  ): any[] {\n    let results: any[] = [];\n    if (includeSelf) {\n      results.push(this.element);\n    }\n    if (selector.length > 0) {\n      // only if :self is used then the selector can be empty\n      selector = selector.replace(ENTER_TOKEN_REGEX, '.' + this._enterClassName);\n      selector = selector.replace(LEAVE_TOKEN_REGEX, '.' + this._leaveClassName);\n      const multi = limit != 1;\n      let elements = this._driver.query(this.element, selector, multi);\n      if (limit !== 0) {\n        elements =\n          limit < 0\n            ? elements.slice(elements.length + limit, elements.length)\n            : elements.slice(0, limit);\n      }\n      results.push(...elements);\n    }\n\n    if (!optional && results.length == 0) {\n      errors.push(invalidQuery(originalSelector));\n    }\n    return results;\n  }\n}\n\nexport class TimelineBuilder {\n  public duration: number = 0;\n  public easing: string | null = null;\n  private _previousKeyframe: ɵStyleDataMap = new Map();\n  private _currentKeyframe: ɵStyleDataMap = new Map();\n  private _keyframes = new Map<number, ɵStyleDataMap>();\n  private _styleSummary = new Map<string, StyleAtTime>();\n  private _localTimelineStyles: ɵStyleDataMap = new Map();\n  private _globalTimelineStyles: ɵStyleDataMap;\n  private _pendingStyles: ɵStyleDataMap = new Map();\n  private _backFill: ɵStyleDataMap = new Map();\n  private _currentEmptyStepKeyframe: ɵStyleDataMap | null = null;\n\n  constructor(\n    private _driver: AnimationDriver,\n    public element: any,\n    public startTime: number,\n    private _elementTimelineStylesLookup?: Map<any, ɵStyleDataMap>,\n  ) {\n    if (!this._elementTimelineStylesLookup) {\n      this._elementTimelineStylesLookup = new Map<any, ɵStyleDataMap>();\n    }\n\n    this._globalTimelineStyles = this._elementTimelineStylesLookup.get(element)!;\n    if (!this._globalTimelineStyles) {\n      this._globalTimelineStyles = this._localTimelineStyles;\n      this._elementTimelineStylesLookup.set(element, this._localTimelineStyles);\n    }\n    this._loadKeyframe();\n  }\n\n  containsAnimation(): boolean {\n    switch (this._keyframes.size) {\n      case 0:\n        return false;\n      case 1:\n        return this.hasCurrentStyleProperties();\n      default:\n        return true;\n    }\n  }\n\n  hasCurrentStyleProperties(): boolean {\n    return this._currentKeyframe.size > 0;\n  }\n\n  get currentTime() {\n    return this.startTime + this.duration;\n  }\n\n  delayNextStep(delay: number) {\n    // in the event that a style() step is placed right before a stagger()\n    // and that style() step is the very first style() value in the animation\n    // then we need to make a copy of the keyframe [0, copy, 1] so that the delay\n    // properly applies the style() values to work with the stagger...\n    const hasPreStyleStep = this._keyframes.size === 1 && this._pendingStyles.size;\n\n    if (this.duration || hasPreStyleStep) {\n      this.forwardTime(this.currentTime + delay);\n      if (hasPreStyleStep) {\n        this.snapshotCurrentStyles();\n      }\n    } else {\n      this.startTime += delay;\n    }\n  }\n\n  fork(element: any, currentTime?: number): TimelineBuilder {\n    this.applyStylesToKeyframe();\n    return new TimelineBuilder(\n      this._driver,\n      element,\n      currentTime || this.currentTime,\n      this._elementTimelineStylesLookup,\n    );\n  }\n\n  private _loadKeyframe() {\n    if (this._currentKeyframe) {\n      this._previousKeyframe = this._currentKeyframe;\n    }\n    this._currentKeyframe = this._keyframes.get(this.duration)!;\n    if (!this._currentKeyframe) {\n      this._currentKeyframe = new Map();\n      this._keyframes.set(this.duration, this._currentKeyframe);\n    }\n  }\n\n  forwardFrame() {\n    this.duration += ONE_FRAME_IN_MILLISECONDS;\n    this._loadKeyframe();\n  }\n\n  forwardTime(time: number) {\n    this.applyStylesToKeyframe();\n    this.duration = time;\n    this._loadKeyframe();\n  }\n\n  private _updateStyle(prop: string, value: string | number) {\n    this._localTimelineStyles.set(prop, value);\n    this._globalTimelineStyles.set(prop, value);\n    this._styleSummary.set(prop, {time: this.currentTime, value});\n  }\n\n  allowOnlyTimelineStyles() {\n    return this._currentEmptyStepKeyframe !== this._currentKeyframe;\n  }\n\n  applyEmptyStep(easing: string | null) {\n    if (easing) {\n      this._previousKeyframe.set('easing', easing);\n    }\n\n    // special case for animate(duration):\n    // all missing styles are filled with a `*` value then\n    // if any destination styles are filled in later on the same\n    // keyframe then they will override the overridden styles\n    // We use `_globalTimelineStyles` here because there may be\n    // styles in previous keyframes that are not present in this timeline\n    for (let [prop, value] of this._globalTimelineStyles) {\n      this._backFill.set(prop, value || AUTO_STYLE);\n      this._currentKeyframe.set(prop, AUTO_STYLE);\n    }\n    this._currentEmptyStepKeyframe = this._currentKeyframe;\n  }\n\n  setStyles(\n    input: Array<ɵStyleDataMap | string>,\n    easing: string | null,\n    errors: Error[],\n    options?: AnimationOptions,\n  ) {\n    if (easing) {\n      this._previousKeyframe.set('easing', easing);\n    }\n    const params = (options && options.params) || {};\n    const styles = flattenStyles(input, this._globalTimelineStyles);\n    for (let [prop, value] of styles) {\n      const val = interpolateParams(value, params, errors);\n      this._pendingStyles.set(prop, val);\n      if (!this._localTimelineStyles.has(prop)) {\n        this._backFill.set(prop, this._globalTimelineStyles.get(prop) ?? AUTO_STYLE);\n      }\n      this._updateStyle(prop, val);\n    }\n  }\n\n  applyStylesToKeyframe() {\n    if (this._pendingStyles.size == 0) return;\n\n    this._pendingStyles.forEach((val, prop) => {\n      this._currentKeyframe.set(prop, val);\n    });\n    this._pendingStyles.clear();\n\n    this._localTimelineStyles.forEach((val, prop) => {\n      if (!this._currentKeyframe.has(prop)) {\n        this._currentKeyframe.set(prop, val);\n      }\n    });\n  }\n\n  snapshotCurrentStyles() {\n    for (let [prop, val] of this._localTimelineStyles) {\n      this._pendingStyles.set(prop, val);\n      this._updateStyle(prop, val);\n    }\n  }\n\n  getFinalKeyframe() {\n    return this._keyframes.get(this.duration);\n  }\n\n  get properties() {\n    const properties: string[] = [];\n    for (let prop in this._currentKeyframe) {\n      properties.push(prop);\n    }\n    return properties;\n  }\n\n  mergeTimelineCollectedStyles(timeline: TimelineBuilder) {\n    timeline._styleSummary.forEach((details1, prop) => {\n      const details0 = this._styleSummary.get(prop);\n      if (!details0 || details1.time > details0.time) {\n        this._updateStyle(prop, details1.value);\n      }\n    });\n  }\n\n  buildKeyframes(): AnimationTimelineInstruction {\n    this.applyStylesToKeyframe();\n    const preStyleProps = new Set<string>();\n    const postStyleProps = new Set<string>();\n    const isEmpty = this._keyframes.size === 1 && this.duration === 0;\n\n    let finalKeyframes: Array<ɵStyleDataMap> = [];\n    this._keyframes.forEach((keyframe, time) => {\n      const finalKeyframe = new Map([...this._backFill, ...keyframe]);\n      finalKeyframe.forEach((value, prop) => {\n        if (value === PRE_STYLE) {\n          preStyleProps.add(prop);\n        } else if (value === AUTO_STYLE) {\n          postStyleProps.add(prop);\n        }\n      });\n      if (!isEmpty) {\n        finalKeyframe.set('offset', time / this.duration);\n      }\n      finalKeyframes.push(finalKeyframe);\n    });\n\n    const preProps: string[] = [...preStyleProps.values()];\n    const postProps: string[] = [...postStyleProps.values()];\n\n    // special case for a 0-second animation (which is designed just to place styles onscreen)\n    if (isEmpty) {\n      const kf0 = finalKeyframes[0];\n      const kf1 = new Map(kf0);\n      kf0.set('offset', 0);\n      kf1.set('offset', 1);\n      finalKeyframes = [kf0, kf1];\n    }\n\n    return createTimelineInstruction(\n      this.element,\n      finalKeyframes,\n      preProps,\n      postProps,\n      this.duration,\n      this.startTime,\n      this.easing,\n      false,\n    );\n  }\n}\n\nclass SubTimelineBuilder extends TimelineBuilder {\n  public timings: AnimateTimings;\n\n  constructor(\n    driver: AnimationDriver,\n    element: any,\n    public keyframes: Array<ɵStyleDataMap>,\n    public preStyleProps: string[],\n    public postStyleProps: string[],\n    timings: AnimateTimings,\n    private _stretchStartingKeyframe: boolean = false,\n  ) {\n    super(driver, element, timings.delay);\n    this.timings = {duration: timings.duration, delay: timings.delay, easing: timings.easing};\n  }\n\n  override containsAnimation(): boolean {\n    return this.keyframes.length > 1;\n  }\n\n  override buildKeyframes(): AnimationTimelineInstruction {\n    let keyframes = this.keyframes;\n    let {delay, duration, easing} = this.timings;\n    if (this._stretchStartingKeyframe && delay) {\n      const newKeyframes: Array<ɵStyleDataMap> = [];\n      const totalTime = duration + delay;\n      const startingGap = delay / totalTime;\n\n      // the original starting keyframe now starts once the delay is done\n      const newFirstKeyframe = new Map(keyframes[0]);\n      newFirstKeyframe.set('offset', 0);\n      newKeyframes.push(newFirstKeyframe);\n\n      const oldFirstKeyframe = new Map(keyframes[0]);\n      oldFirstKeyframe.set('offset', roundOffset(startingGap));\n      newKeyframes.push(oldFirstKeyframe);\n\n      /*\n        When the keyframe is stretched then it means that the delay before the animation\n        starts is gone. Instead the first keyframe is placed at the start of the animation\n        and it is then copied to where it starts when the original delay is over. This basically\n        means nothing animates during that delay, but the styles are still rendered. For this\n        to work the original offset values that exist in the original keyframes must be \"warped\"\n        so that they can take the new keyframe + delay into account.\n\n        delay=1000, duration=1000, keyframes = 0 .5 1\n\n        turns into\n\n        delay=0, duration=2000, keyframes = 0 .33 .66 1\n       */\n\n      // offsets between 1 ... n -1 are all warped by the keyframe stretch\n      const limit = keyframes.length - 1;\n      for (let i = 1; i <= limit; i++) {\n        let kf = new Map(keyframes[i]);\n        const oldOffset = kf.get('offset') as number;\n        const timeAtKeyframe = delay + oldOffset * duration;\n        kf.set('offset', roundOffset(timeAtKeyframe / totalTime));\n        newKeyframes.push(kf);\n      }\n\n      // the new starting keyframe should be added at the start\n      duration = totalTime;\n      delay = 0;\n      easing = '';\n\n      keyframes = newKeyframes;\n    }\n\n    return createTimelineInstruction(\n      this.element,\n      keyframes,\n      this.preStyleProps,\n      this.postStyleProps,\n      duration,\n      delay,\n      easing,\n      true,\n    );\n  }\n}\n\nfunction roundOffset(offset: number, decimalPoints = 3): number {\n  const mult = Math.pow(10, decimalPoints - 1);\n  return Math.round(offset * mult) / mult;\n}\n\nfunction flattenStyles(input: Array<ɵStyleDataMap | string>, allStyles: ɵStyleDataMap) {\n  const styles: ɵStyleDataMap = new Map();\n  let allProperties: string[] | IterableIterator<string>;\n  input.forEach((token) => {\n    if (token === '*') {\n      allProperties ??= allStyles.keys();\n      for (let prop of allProperties) {\n        styles.set(prop, AUTO_STYLE);\n      }\n    } else {\n      for (let [prop, val] of token as ɵStyleDataMap) {\n        styles.set(prop, val);\n      }\n    }\n  });\n  return styles;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵStyleDataMap} from '@angular/animations';\n\nimport {\n  AnimationEngineInstruction,\n  AnimationTransitionInstructionType,\n} from '../render/animation_engine_instruction';\n\nimport {AnimationTimelineInstruction} from './animation_timeline_instruction';\n\nexport interface AnimationTransitionInstruction extends AnimationEngineInstruction {\n  element: any;\n  triggerName: string;\n  isRemovalTransition: boolean;\n  fromState: string;\n  fromStyles: ɵStyleDataMap;\n  toState: string;\n  toStyles: ɵStyleDataMap;\n  timelines: AnimationTimelineInstruction[];\n  queriedElements: any[];\n  preStyleProps: Map<any, Set<string>>;\n  postStyleProps: Map<any, Set<string>>;\n  totalTime: number;\n  errors?: Error[];\n}\n\nexport function createTransitionInstruction(\n  element: any,\n  triggerName: string,\n  fromState: string,\n  toState: string,\n  isRemovalTransition: boolean,\n  fromStyles: ɵStyleDataMap,\n  toStyles: ɵStyleDataMap,\n  timelines: AnimationTimelineInstruction[],\n  queriedElements: any[],\n  preStyleProps: Map<any, Set<string>>,\n  postStyleProps: Map<any, Set<string>>,\n  totalTime: number,\n  errors?: Error[],\n): AnimationTransitionInstruction {\n  return {\n    type: AnimationTransitionInstructionType.TransitionAnimation,\n    element,\n    triggerName,\n    isRemovalTransition,\n    fromState,\n    fromStyles,\n    toState,\n    toStyles,\n    timelines,\n    queriedElements,\n    preStyleProps,\n    postStyleProps,\n    totalTime,\n    errors,\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AnimationOptions, ɵStyleDataMap} from '@angular/animations';\n\nimport {AnimationDriver} from '../render/animation_driver';\nimport {getOrSetDefaultValue} from '../render/shared';\nimport {interpolateParams} from '../util';\n\nimport {StyleAst, TransitionAst} from './animation_ast';\nimport {buildAnimationTimelines} from './animation_timeline_builder';\nimport {AnimationTimelineInstruction} from './animation_timeline_instruction';\nimport {TransitionMatcherFn} from './animation_transition_expr';\nimport {\n  AnimationTransitionInstruction,\n  createTransitionInstruction,\n} from './animation_transition_instruction';\nimport {ElementInstructionMap} from './element_instruction_map';\nimport {AnimationStyleNormalizer} from './style_normalization/animation_style_normalizer';\n\nconst EMPTY_OBJECT = {};\n\nexport class AnimationTransitionFactory {\n  constructor(\n    private _triggerName: string,\n    public ast: TransitionAst,\n    private _stateStyles: Map<string, AnimationStateStyles>,\n  ) {}\n\n  match(currentState: any, nextState: any, element: any, params: {[key: string]: any}): boolean {\n    return oneOrMoreTransitionsMatch(this.ast.matchers, currentState, nextState, element, params);\n  }\n\n  buildStyles(\n    stateName: string | boolean | undefined,\n    params: {[key: string]: any},\n    errors: Error[],\n  ): ɵStyleDataMap {\n    let styler = this._stateStyles.get('*');\n    if (stateName !== undefined) {\n      styler = this._stateStyles.get(stateName?.toString()) || styler;\n    }\n    return styler ? styler.buildStyles(params, errors) : new Map();\n  }\n\n  build(\n    driver: AnimationDriver,\n    element: any,\n    currentState: any,\n    nextState: any,\n    enterClassName: string,\n    leaveClassName: string,\n    currentOptions?: AnimationOptions,\n    nextOptions?: AnimationOptions,\n    subInstructions?: ElementInstructionMap,\n    skipAstBuild?: boolean,\n  ): AnimationTransitionInstruction {\n    const errors: Error[] = [];\n\n    const transitionAnimationParams = (this.ast.options && this.ast.options.params) || EMPTY_OBJECT;\n    const currentAnimationParams = (currentOptions && currentOptions.params) || EMPTY_OBJECT;\n    const currentStateStyles = this.buildStyles(currentState, currentAnimationParams, errors);\n    const nextAnimationParams = (nextOptions && nextOptions.params) || EMPTY_OBJECT;\n    const nextStateStyles = this.buildStyles(nextState, nextAnimationParams, errors);\n\n    const queriedElements = new Set<any>();\n    const preStyleMap = new Map<any, Set<string>>();\n    const postStyleMap = new Map<any, Set<string>>();\n    const isRemoval = nextState === 'void';\n\n    const animationOptions: AnimationOptions = {\n      params: applyParamDefaults(nextAnimationParams, transitionAnimationParams),\n      delay: this.ast.options?.delay,\n    };\n\n    const timelines = skipAstBuild\n      ? []\n      : buildAnimationTimelines(\n          driver,\n          element,\n          this.ast.animation,\n          enterClassName,\n          leaveClassName,\n          currentStateStyles,\n          nextStateStyles,\n          animationOptions,\n          subInstructions,\n          errors,\n        );\n\n    let totalTime = 0;\n    timelines.forEach((tl) => {\n      totalTime = Math.max(tl.duration + tl.delay, totalTime);\n    });\n\n    if (errors.length) {\n      return createTransitionInstruction(\n        element,\n        this._triggerName,\n        currentState,\n        nextState,\n        isRemoval,\n        currentStateStyles,\n        nextStateStyles,\n        [],\n        [],\n        preStyleMap,\n        postStyleMap,\n        totalTime,\n        errors,\n      );\n    }\n\n    timelines.forEach((tl) => {\n      const elm = tl.element;\n      const preProps = getOrSetDefaultValue(preStyleMap, elm, new Set<string>());\n      tl.preStyleProps.forEach((prop) => preProps.add(prop));\n\n      const postProps = getOrSetDefaultValue(postStyleMap, elm, new Set<string>());\n      tl.postStyleProps.forEach((prop) => postProps.add(prop));\n\n      if (elm !== element) {\n        queriedElements.add(elm);\n      }\n    });\n\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      checkNonAnimatableInTimelines(timelines, this._triggerName, driver);\n    }\n\n    return createTransitionInstruction(\n      element,\n      this._triggerName,\n      currentState,\n      nextState,\n      isRemoval,\n      currentStateStyles,\n      nextStateStyles,\n      timelines,\n      [...queriedElements.values()],\n      preStyleMap,\n      postStyleMap,\n      totalTime,\n    );\n  }\n}\n\n/**\n * Checks inside a set of timelines if they try to animate a css property which is not considered\n * animatable, in that case it prints a warning on the console.\n * Besides that the function doesn't have any other effect.\n *\n * Note: this check is done here after the timelines are built instead of doing on a lower level so\n * that we can make sure that the warning appears only once per instruction (we can aggregate here\n * all the issues instead of finding them separately).\n *\n * @param timelines The built timelines for the current instruction.\n * @param triggerName The name of the trigger for the current instruction.\n * @param driver Animation driver used to perform the check.\n *\n */\nfunction checkNonAnimatableInTimelines(\n  timelines: AnimationTimelineInstruction[],\n  triggerName: string,\n  driver: AnimationDriver,\n): void {\n  if (!driver.validateAnimatableStyleProperty) {\n    return;\n  }\n\n  const allowedNonAnimatableProps = new Set<string>([\n    // 'easing' is a utility/synthetic prop we use to represent\n    // easing functions, it represents a property of the animation\n    // which is not animatable but different values can be used\n    // in different steps\n    'easing',\n  ]);\n\n  const invalidNonAnimatableProps = new Set<string>();\n\n  timelines.forEach(({keyframes}) => {\n    const nonAnimatablePropsInitialValues = new Map<string, string | number>();\n    keyframes.forEach((keyframe) => {\n      const entriesToCheck = Array.from(keyframe.entries()).filter(\n        ([prop]) => !allowedNonAnimatableProps.has(prop),\n      );\n      for (const [prop, value] of entriesToCheck) {\n        if (!driver.validateAnimatableStyleProperty!(prop)) {\n          if (nonAnimatablePropsInitialValues.has(prop) && !invalidNonAnimatableProps.has(prop)) {\n            const propInitialValue = nonAnimatablePropsInitialValues.get(prop);\n            if (propInitialValue !== value) {\n              invalidNonAnimatableProps.add(prop);\n            }\n          } else {\n            nonAnimatablePropsInitialValues.set(prop, value);\n          }\n        }\n      }\n    });\n  });\n\n  if (invalidNonAnimatableProps.size > 0) {\n    console.warn(\n      `Warning: The animation trigger \"${triggerName}\" is attempting to animate the following` +\n        ' not animatable properties: ' +\n        Array.from(invalidNonAnimatableProps).join(', ') +\n        '\\n' +\n        '(to check the list of all animatable properties visit https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_animated_properties)',\n    );\n  }\n}\n\nfunction oneOrMoreTransitionsMatch(\n  matchFns: TransitionMatcherFn[],\n  currentState: any,\n  nextState: any,\n  element: any,\n  params: {[key: string]: any},\n): boolean {\n  return matchFns.some((fn) => fn(currentState, nextState, element, params));\n}\n\nfunction applyParamDefaults(userParams: Record<string, any>, defaults: Record<string, any>) {\n  const result: Record<string, any> = {...defaults};\n  Object.entries(userParams).forEach(([key, value]) => {\n    if (value != null) {\n      result[key] = value;\n    }\n  });\n  return result;\n}\n\nexport class AnimationStateStyles {\n  constructor(\n    private styles: StyleAst,\n    private defaultParams: {[key: string]: any},\n    private normalizer: AnimationStyleNormalizer,\n  ) {}\n\n  buildStyles(params: {[key: string]: any}, errors: Error[]): ɵStyleDataMap {\n    const finalStyles: ɵStyleDataMap = new Map();\n    const combinedParams = applyParamDefaults(params, this.defaultParams);\n    this.styles.styles.forEach((value) => {\n      if (typeof value !== 'string') {\n        value.forEach((val, prop) => {\n          if (val) {\n            val = interpolateParams(val, combinedParams, errors);\n          }\n          const normalizedProp = this.normalizer.normalizePropertyName(prop, errors);\n          val = this.normalizer.normalizeStyleValue(prop, normalizedProp, val, errors);\n          finalStyles.set(prop, val);\n        });\n      }\n    });\n    return finalStyles;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AnimationMetadataType, ɵStyleDataMap} from '@angular/animations';\n\nimport {SequenceAst, TransitionAst, TriggerAst} from './animation_ast';\nimport {AnimationStateStyles, AnimationTransitionFactory} from './animation_transition_factory';\nimport {AnimationStyleNormalizer} from './style_normalization/animation_style_normalizer';\n\nexport function buildTrigger(\n  name: string,\n  ast: TriggerAst,\n  normalizer: AnimationStyleNormalizer,\n): AnimationTrigger {\n  return new AnimationTrigger(name, ast, normalizer);\n}\n\nexport class AnimationTrigger {\n  public transitionFactories: AnimationTransitionFactory[] = [];\n  public fallbackTransition: AnimationTransitionFactory;\n  public states = new Map<string, AnimationStateStyles>();\n\n  constructor(\n    public name: string,\n    public ast: TriggerAst,\n    private _normalizer: AnimationStyleNormalizer,\n  ) {\n    ast.states.forEach((ast) => {\n      const defaultParams = (ast.options && ast.options.params) || {};\n      this.states.set(ast.name, new AnimationStateStyles(ast.style, defaultParams, _normalizer));\n    });\n\n    balanceProperties(this.states, 'true', '1');\n    balanceProperties(this.states, 'false', '0');\n\n    ast.transitions.forEach((ast) => {\n      this.transitionFactories.push(new AnimationTransitionFactory(name, ast, this.states));\n    });\n    this.fallbackTransition = createFallbackTransition(name, this.states, this._normalizer);\n  }\n\n  get containsQueries() {\n    return this.ast.queryCount > 0;\n  }\n\n  matchTransition(\n    currentState: any,\n    nextState: any,\n    element: any,\n    params: {[key: string]: any},\n  ): AnimationTransitionFactory | null {\n    const entry = this.transitionFactories.find((f) =>\n      f.match(currentState, nextState, element, params),\n    );\n    return entry || null;\n  }\n\n  matchStyles(currentState: any, params: {[key: string]: any}, errors: Error[]): ɵStyleDataMap {\n    return this.fallbackTransition.buildStyles(currentState, params, errors);\n  }\n}\n\nfunction createFallbackTransition(\n  triggerName: string,\n  states: Map<string, AnimationStateStyles>,\n  normalizer: AnimationStyleNormalizer,\n): AnimationTransitionFactory {\n  const matchers = [(fromState: any, toState: any) => true];\n  const animation: SequenceAst = {type: AnimationMetadataType.Sequence, steps: [], options: null};\n  const transition: TransitionAst = {\n    type: AnimationMetadataType.Transition,\n    animation,\n    matchers,\n    options: null,\n    queryCount: 0,\n    depCount: 0,\n  };\n  return new AnimationTransitionFactory(triggerName, transition, states);\n}\n\nfunction balanceProperties(\n  stateMap: Map<string, AnimationStateStyles>,\n  key1: string,\n  key2: string,\n) {\n  if (stateMap.has(key1)) {\n    if (!stateMap.has(key2)) {\n      stateMap.set(key2, stateMap.get(key1)!);\n    }\n  } else if (stateMap.has(key2)) {\n    stateMap.set(key1, stateMap.get(key2)!);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {\n  AnimationMetadata,\n  AnimationMetadataType,\n  AnimationOptions,\n  AnimationPlayer,\n  AUTO_STYLE,\n  ɵStyleDataMap,\n} from '@angular/animations';\n\nimport {Ast} from '../dsl/animation_ast';\nimport {buildAnimationAst} from '../dsl/animation_ast_builder';\nimport {buildAnimationTimelines} from '../dsl/animation_timeline_builder';\nimport {AnimationTimelineInstruction} from '../dsl/animation_timeline_instruction';\nimport {ElementInstructionMap} from '../dsl/element_instruction_map';\nimport {AnimationStyleNormalizer} from '../dsl/style_normalization/animation_style_normalizer';\nimport {\n  createAnimationFailed,\n  missingOrDestroyedAnimation,\n  missingPlayer,\n  registerFailed,\n} from '../error_helpers';\nimport {ENTER_CLASSNAME, LEAVE_CLASSNAME} from '../util';\nimport {warnRegister} from '../warning_helpers';\n\nimport {AnimationDriver} from './animation_driver';\nimport {\n  getOrSetDefaultValue,\n  listenOnPlayer,\n  makeAnimationEvent,\n  normalizeKeyframes,\n  optimizeGroupPlayer,\n} from './shared';\n\nconst EMPTY_INSTRUCTION_MAP = new ElementInstructionMap();\n\nexport class TimelineAnimationEngine {\n  private _animations = new Map<string, Ast<AnimationMetadataType>>();\n  private _playersById = new Map<string, AnimationPlayer>();\n  public players: AnimationPlayer[] = [];\n\n  constructor(\n    public bodyNode: any,\n    private _driver: AnimationDriver,\n    private _normalizer: AnimationStyleNormalizer,\n  ) {}\n\n  register(id: string, metadata: AnimationMetadata | AnimationMetadata[]) {\n    const errors: Error[] = [];\n    const warnings: string[] = [];\n    const ast = buildAnimationAst(this._driver, metadata, errors, warnings);\n    if (errors.length) {\n      throw registerFailed(errors);\n    } else {\n      if (warnings.length) {\n        warnRegister(warnings);\n      }\n      this._animations.set(id, ast);\n    }\n  }\n\n  private _buildPlayer(\n    i: AnimationTimelineInstruction,\n    preStyles: ɵStyleDataMap,\n    postStyles?: ɵStyleDataMap,\n  ): AnimationPlayer {\n    const element = i.element;\n    const keyframes = normalizeKeyframes(this._normalizer, i.keyframes, preStyles, postStyles);\n    return this._driver.animate(element, keyframes, i.duration, i.delay, i.easing, [], true);\n  }\n\n  create(id: string, element: any, options: AnimationOptions = {}): AnimationPlayer {\n    const errors: Error[] = [];\n    const ast = this._animations.get(id);\n    let instructions: AnimationTimelineInstruction[];\n\n    const autoStylesMap = new Map<any, ɵStyleDataMap>();\n\n    if (ast) {\n      instructions = buildAnimationTimelines(\n        this._driver,\n        element,\n        ast,\n        ENTER_CLASSNAME,\n        LEAVE_CLASSNAME,\n        new Map(),\n        new Map(),\n        options,\n        EMPTY_INSTRUCTION_MAP,\n        errors,\n      );\n      instructions.forEach((inst) => {\n        const styles = getOrSetDefaultValue(\n          autoStylesMap,\n          inst.element,\n          new Map<string, string | number | null>(),\n        );\n        inst.postStyleProps.forEach((prop) => styles.set(prop, null));\n      });\n    } else {\n      errors.push(missingOrDestroyedAnimation());\n      instructions = [];\n    }\n\n    if (errors.length) {\n      throw createAnimationFailed(errors);\n    }\n\n    autoStylesMap.forEach((styles, element) => {\n      styles.forEach((_, prop) => {\n        styles.set(prop, this._driver.computeStyle(element, prop, AUTO_STYLE));\n      });\n    });\n\n    const players = instructions.map((i) => {\n      const styles = autoStylesMap.get(i.element);\n      return this._buildPlayer(i, new Map(), styles);\n    });\n    const player = optimizeGroupPlayer(players);\n    this._playersById.set(id, player);\n    player.onDestroy(() => this.destroy(id));\n\n    this.players.push(player);\n    return player;\n  }\n\n  destroy(id: string) {\n    const player = this._getPlayer(id);\n    player.destroy();\n    this._playersById.delete(id);\n    const index = this.players.indexOf(player);\n    if (index >= 0) {\n      this.players.splice(index, 1);\n    }\n  }\n\n  private _getPlayer(id: string): AnimationPlayer {\n    const player = this._playersById.get(id);\n    if (!player) {\n      throw missingPlayer(id);\n    }\n    return player;\n  }\n\n  listen(\n    id: string,\n    element: string,\n    eventName: string,\n    callback: (event: any) => any,\n  ): () => void {\n    // triggerName, fromState, toState are all ignored for timeline animations\n    const baseEvent = makeAnimationEvent(element, '', '', '');\n    listenOnPlayer(this._getPlayer(id), eventName, baseEvent, callback);\n    return () => {};\n  }\n\n  command(id: string, element: any, command: string, args: any[]): void {\n    if (command == 'register') {\n      this.register(id, args[0] as AnimationMetadata | AnimationMetadata[]);\n      return;\n    }\n\n    if (command == 'create') {\n      const options = (args[0] || {}) as AnimationOptions;\n      this.create(id, element, options);\n      return;\n    }\n\n    const player = this._getPlayer(id);\n    switch (command) {\n      case 'play':\n        player.play();\n        break;\n      case 'pause':\n        player.pause();\n        break;\n      case 'reset':\n        player.reset();\n        break;\n      case 'restart':\n        player.restart();\n        break;\n      case 'finish':\n        player.finish();\n        break;\n      case 'init':\n        player.init();\n        break;\n      case 'setPosition':\n        player.setPosition(parseFloat(args[0] as string));\n        break;\n      case 'destroy':\n        this.destroy(id);\n        break;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {\n  AnimationOptions,\n  AnimationPlayer,\n  AUTO_STYLE,\n  NoopAnimationPlayer,\n  ɵAnimationGroupPlayer as AnimationGroupPlayer,\n  ɵPRE_STYLE as PRE_STYLE,\n  ɵStyleDataMap,\n} from '@angular/animations';\nimport {\n  ɵChangeDetectionScheduler as ChangeDetectionScheduler,\n  ɵWritable as Writable,\n} from '@angular/core';\n\nimport {AnimationTimelineInstruction} from '../dsl/animation_timeline_instruction';\nimport {AnimationTransitionFactory} from '../dsl/animation_transition_factory';\nimport {AnimationTransitionInstruction} from '../dsl/animation_transition_instruction';\nimport {AnimationTrigger} from '../dsl/animation_trigger';\nimport {ElementInstructionMap} from '../dsl/element_instruction_map';\nimport {AnimationStyleNormalizer} from '../dsl/style_normalization/animation_style_normalizer';\nimport {\n  missingEvent,\n  missingTrigger,\n  transitionFailed,\n  triggerTransitionsFailed,\n  unregisteredTrigger,\n  unsupportedTriggerEvent,\n} from '../error_helpers';\nimport {\n  ENTER_CLASSNAME,\n  eraseStyles,\n  LEAVE_CLASSNAME,\n  NG_ANIMATING_CLASSNAME,\n  NG_ANIMATING_SELECTOR,\n  NG_TRIGGER_CLASSNAME,\n  NG_TRIGGER_SELECTOR,\n  setStyles,\n} from '../util';\n\nimport {AnimationDriver} from './animation_driver';\nimport {\n  getOrSetDefaultValue,\n  listenOnPlayer,\n  makeAnimationEvent,\n  normalizeKeyframes,\n  optimizeGroupPlayer,\n} from './shared';\n\nconst QUEUED_CLASSNAME = 'ng-animate-queued';\nconst QUEUED_SELECTOR = '.ng-animate-queued';\nconst DISABLED_CLASSNAME = 'ng-animate-disabled';\nconst DISABLED_SELECTOR = '.ng-animate-disabled';\nconst STAR_CLASSNAME = 'ng-star-inserted';\nconst STAR_SELECTOR = '.ng-star-inserted';\n\nconst EMPTY_PLAYER_ARRAY: TransitionAnimationPlayer[] = [];\nconst NULL_REMOVAL_STATE: ElementAnimationState = {\n  namespaceId: '',\n  setForRemoval: false,\n  setForMove: false,\n  hasAnimation: false,\n  removedBeforeQueried: false,\n};\nconst NULL_REMOVED_QUERIED_STATE: ElementAnimationState = {\n  namespaceId: '',\n  setForMove: false,\n  setForRemoval: false,\n  hasAnimation: false,\n  removedBeforeQueried: true,\n};\n\ninterface TriggerListener {\n  name: string;\n  phase: string;\n  callback: (event: any) => any;\n}\n\ninterface QueueInstruction {\n  element: any;\n  triggerName: string;\n  fromState: StateValue;\n  toState: StateValue;\n  transition: AnimationTransitionFactory;\n  player: TransitionAnimationPlayer;\n  isFallbackTransition: boolean;\n}\n\nconst REMOVAL_FLAG = '__ng_removed';\n\ninterface ElementAnimationState {\n  setForRemoval: boolean;\n  setForMove: boolean;\n  hasAnimation: boolean;\n  namespaceId: string;\n  removedBeforeQueried: boolean;\n  previousTriggersValues?: Map<string, string>;\n}\n\nclass StateValue {\n  public value: string;\n  public options: AnimationOptions;\n\n  get params(): {[key: string]: any} {\n    return this.options.params as {[key: string]: any};\n  }\n\n  constructor(\n    input: any,\n    public namespaceId: string = '',\n  ) {\n    const isObj = input && input.hasOwnProperty('value');\n    const value = isObj ? input['value'] : input;\n    this.value = normalizeTriggerValue(value);\n    if (isObj) {\n      // we drop the value property from options.\n      const {value, ...options} = input;\n      this.options = options as AnimationOptions;\n    } else {\n      this.options = {};\n    }\n    if (!this.options.params) {\n      this.options.params = {};\n    }\n  }\n\n  absorbOptions(options: AnimationOptions) {\n    const newParams = options.params;\n    if (newParams) {\n      const oldParams = this.options.params!;\n      Object.keys(newParams).forEach((prop) => {\n        if (oldParams[prop] == null) {\n          oldParams[prop] = newParams[prop];\n        }\n      });\n    }\n  }\n}\n\nconst VOID_VALUE = 'void';\nconst DEFAULT_STATE_VALUE = new StateValue(VOID_VALUE);\n\nclass AnimationTransitionNamespace {\n  public players: TransitionAnimationPlayer[] = [];\n\n  private _triggers = new Map<string, AnimationTrigger>();\n  private _queue: QueueInstruction[] = [];\n\n  private _elementListeners = new Map<any, TriggerListener[]>();\n\n  private _hostClassName: string;\n\n  constructor(\n    public id: string,\n    public hostElement: any,\n    private _engine: TransitionAnimationEngine,\n  ) {\n    this._hostClassName = 'ng-tns-' + id;\n    addClass(hostElement, this._hostClassName);\n  }\n\n  listen(element: any, name: string, phase: string, callback: (event: any) => boolean): () => any {\n    if (!this._triggers.has(name)) {\n      throw missingTrigger(phase, name);\n    }\n\n    if (phase == null || phase.length == 0) {\n      throw missingEvent(name);\n    }\n\n    if (!isTriggerEventValid(phase)) {\n      throw unsupportedTriggerEvent(phase, name);\n    }\n\n    const listeners = getOrSetDefaultValue(this._elementListeners, element, []);\n    const data = {name, phase, callback};\n    listeners.push(data);\n\n    const triggersWithStates = getOrSetDefaultValue(\n      this._engine.statesByElement,\n      element,\n      new Map<string, StateValue>(),\n    );\n    if (!triggersWithStates.has(name)) {\n      addClass(element, NG_TRIGGER_CLASSNAME);\n      addClass(element, NG_TRIGGER_CLASSNAME + '-' + name);\n      triggersWithStates.set(name, DEFAULT_STATE_VALUE);\n    }\n\n    return () => {\n      // the event listener is removed AFTER the flush has occurred such\n      // that leave animations callbacks can fire (otherwise if the node\n      // is removed in between then the listeners would be deregistered)\n      this._engine.afterFlush(() => {\n        const index = listeners.indexOf(data);\n        if (index >= 0) {\n          listeners.splice(index, 1);\n        }\n\n        if (!this._triggers.has(name)) {\n          triggersWithStates.delete(name);\n        }\n      });\n    };\n  }\n\n  register(name: string, ast: AnimationTrigger): boolean {\n    if (this._triggers.has(name)) {\n      // throw\n      return false;\n    } else {\n      this._triggers.set(name, ast);\n      return true;\n    }\n  }\n\n  private _getTrigger(name: string) {\n    const trigger = this._triggers.get(name);\n    if (!trigger) {\n      throw unregisteredTrigger(name);\n    }\n    return trigger;\n  }\n\n  trigger(\n    element: any,\n    triggerName: string,\n    value: any,\n    defaultToFallback: boolean = true,\n  ): TransitionAnimationPlayer | undefined {\n    const trigger = this._getTrigger(triggerName);\n    const player = new TransitionAnimationPlayer(this.id, triggerName, element);\n\n    let triggersWithStates = this._engine.statesByElement.get(element);\n    if (!triggersWithStates) {\n      addClass(element, NG_TRIGGER_CLASSNAME);\n      addClass(element, NG_TRIGGER_CLASSNAME + '-' + triggerName);\n      this._engine.statesByElement.set(\n        element,\n        (triggersWithStates = new Map<string, StateValue>()),\n      );\n    }\n\n    let fromState = triggersWithStates.get(triggerName);\n    const toState = new StateValue(value, this.id);\n    const isObj = value && value.hasOwnProperty('value');\n    if (!isObj && fromState) {\n      toState.absorbOptions(fromState.options);\n    }\n\n    triggersWithStates.set(triggerName, toState);\n\n    if (!fromState) {\n      fromState = DEFAULT_STATE_VALUE;\n    }\n\n    const isRemoval = toState.value === VOID_VALUE;\n\n    // normally this isn't reached by here, however, if an object expression\n    // is passed in then it may be a new object each time. Comparing the value\n    // is important since that will stay the same despite there being a new object.\n    // The removal arc here is special cased because the same element is triggered\n    // twice in the event that it contains animations on the outer/inner portions\n    // of the host container\n    if (!isRemoval && fromState.value === toState.value) {\n      // this means that despite the value not changing, some inner params\n      // have changed which means that the animation final styles need to be applied\n      if (!objEquals(fromState.params, toState.params)) {\n        const errors: Error[] = [];\n        const fromStyles = trigger.matchStyles(fromState.value, fromState.params, errors);\n        const toStyles = trigger.matchStyles(toState.value, toState.params, errors);\n        if (errors.length) {\n          this._engine.reportError(errors);\n        } else {\n          this._engine.afterFlush(() => {\n            eraseStyles(element, fromStyles);\n            setStyles(element, toStyles);\n          });\n        }\n      }\n      return;\n    }\n\n    const playersOnElement: TransitionAnimationPlayer[] = getOrSetDefaultValue(\n      this._engine.playersByElement,\n      element,\n      [],\n    );\n    playersOnElement.forEach((player) => {\n      // only remove the player if it is queued on the EXACT same trigger/namespace\n      // we only also deal with queued players here because if the animation has\n      // started then we want to keep the player alive until the flush happens\n      // (which is where the previousPlayers are passed into the new player)\n      if (player.namespaceId == this.id && player.triggerName == triggerName && player.queued) {\n        player.destroy();\n      }\n    });\n\n    let transition = trigger.matchTransition(\n      fromState.value,\n      toState.value,\n      element,\n      toState.params,\n    );\n    let isFallbackTransition = false;\n    if (!transition) {\n      if (!defaultToFallback) return;\n      transition = trigger.fallbackTransition;\n      isFallbackTransition = true;\n    }\n\n    this._engine.totalQueuedPlayers++;\n    this._queue.push({\n      element,\n      triggerName,\n      transition,\n      fromState,\n      toState,\n      player,\n      isFallbackTransition,\n    });\n\n    if (!isFallbackTransition) {\n      addClass(element, QUEUED_CLASSNAME);\n      player.onStart(() => {\n        removeClass(element, QUEUED_CLASSNAME);\n      });\n    }\n\n    player.onDone(() => {\n      let index = this.players.indexOf(player);\n      if (index >= 0) {\n        this.players.splice(index, 1);\n      }\n\n      const players = this._engine.playersByElement.get(element);\n      if (players) {\n        let index = players.indexOf(player);\n        if (index >= 0) {\n          players.splice(index, 1);\n        }\n      }\n    });\n\n    this.players.push(player);\n    playersOnElement.push(player);\n\n    return player;\n  }\n\n  deregister(name: string) {\n    this._triggers.delete(name);\n\n    this._engine.statesByElement.forEach((stateMap) => stateMap.delete(name));\n\n    this._elementListeners.forEach((listeners, element) => {\n      this._elementListeners.set(\n        element,\n        listeners.filter((entry) => {\n          return entry.name != name;\n        }),\n      );\n    });\n  }\n\n  clearElementCache(element: any) {\n    this._engine.statesByElement.delete(element);\n    this._elementListeners.delete(element);\n    const elementPlayers = this._engine.playersByElement.get(element);\n    if (elementPlayers) {\n      elementPlayers.forEach((player) => player.destroy());\n      this._engine.playersByElement.delete(element);\n    }\n  }\n\n  private _signalRemovalForInnerTriggers(rootElement: any, context: any) {\n    const elements = this._engine.driver.query(rootElement, NG_TRIGGER_SELECTOR, true);\n\n    // emulate a leave animation for all inner nodes within this node.\n    // If there are no animations found for any of the nodes then clear the cache\n    // for the element.\n    elements.forEach((elm) => {\n      // this means that an inner remove() operation has already kicked off\n      // the animation on this element...\n      if (elm[REMOVAL_FLAG]) return;\n\n      const namespaces = this._engine.fetchNamespacesByElement(elm);\n      if (namespaces.size) {\n        namespaces.forEach((ns) => ns.triggerLeaveAnimation(elm, context, false, true));\n      } else {\n        this.clearElementCache(elm);\n      }\n    });\n\n    // If the child elements were removed along with the parent, their animations might not\n    // have completed. Clear all the elements from the cache so we don't end up with a memory leak.\n    this._engine.afterFlushAnimationsDone(() =>\n      elements.forEach((elm) => this.clearElementCache(elm)),\n    );\n  }\n\n  triggerLeaveAnimation(\n    element: any,\n    context: any,\n    destroyAfterComplete?: boolean,\n    defaultToFallback?: boolean,\n  ): boolean {\n    const triggerStates = this._engine.statesByElement.get(element);\n    const previousTriggersValues = new Map<string, string>();\n    if (triggerStates) {\n      const players: TransitionAnimationPlayer[] = [];\n      triggerStates.forEach((state, triggerName) => {\n        previousTriggersValues.set(triggerName, state.value);\n        // this check is here in the event that an element is removed\n        // twice (both on the host level and the component level)\n        if (this._triggers.has(triggerName)) {\n          const player = this.trigger(element, triggerName, VOID_VALUE, defaultToFallback);\n          if (player) {\n            players.push(player);\n          }\n        }\n      });\n\n      if (players.length) {\n        this._engine.markElementAsRemoved(this.id, element, true, context, previousTriggersValues);\n        if (destroyAfterComplete) {\n          optimizeGroupPlayer(players).onDone(() => this._engine.processLeaveNode(element));\n        }\n        return true;\n      }\n    }\n    return false;\n  }\n\n  prepareLeaveAnimationListeners(element: any) {\n    const listeners = this._elementListeners.get(element);\n    const elementStates = this._engine.statesByElement.get(element);\n\n    // if this statement fails then it means that the element was picked up\n    // by an earlier flush (or there are no listeners at all to track the leave).\n    if (listeners && elementStates) {\n      const visitedTriggers = new Set<string>();\n      listeners.forEach((listener) => {\n        const triggerName = listener.name;\n        if (visitedTriggers.has(triggerName)) return;\n        visitedTriggers.add(triggerName);\n\n        const trigger = this._triggers.get(triggerName)!;\n        const transition = trigger.fallbackTransition;\n        const fromState = elementStates.get(triggerName) || DEFAULT_STATE_VALUE;\n        const toState = new StateValue(VOID_VALUE);\n        const player = new TransitionAnimationPlayer(this.id, triggerName, element);\n\n        this._engine.totalQueuedPlayers++;\n        this._queue.push({\n          element,\n          triggerName,\n          transition,\n          fromState,\n          toState,\n          player,\n          isFallbackTransition: true,\n        });\n      });\n    }\n  }\n\n  removeNode(element: any, context: any): void {\n    const engine = this._engine;\n    if (element.childElementCount) {\n      this._signalRemovalForInnerTriggers(element, context);\n    }\n\n    // this means that a * => VOID animation was detected and kicked off\n    if (this.triggerLeaveAnimation(element, context, true)) return;\n\n    // find the player that is animating and make sure that the\n    // removal is delayed until that player has completed\n    let containsPotentialParentTransition = false;\n    if (engine.totalAnimations) {\n      const currentPlayers = engine.players.length\n        ? engine.playersByQueriedElement.get(element)\n        : [];\n\n      // when this `if statement` does not continue forward it means that\n      // a previous animation query has selected the current element and\n      // is animating it. In this situation want to continue forwards and\n      // allow the element to be queued up for animation later.\n      if (currentPlayers && currentPlayers.length) {\n        containsPotentialParentTransition = true;\n      } else {\n        let parent = element;\n        while ((parent = parent.parentNode)) {\n          const triggers = engine.statesByElement.get(parent);\n          if (triggers) {\n            containsPotentialParentTransition = true;\n            break;\n          }\n        }\n      }\n    }\n\n    // at this stage we know that the element will either get removed\n    // during flush or will be picked up by a parent query. Either way\n    // we need to fire the listeners for this element when it DOES get\n    // removed (once the query parent animation is done or after flush)\n    this.prepareLeaveAnimationListeners(element);\n\n    // whether or not a parent has an animation we need to delay the deferral of the leave\n    // operation until we have more information (which we do after flush() has been called)\n    if (containsPotentialParentTransition) {\n      engine.markElementAsRemoved(this.id, element, false, context);\n    } else {\n      const removalFlag = element[REMOVAL_FLAG];\n      if (!removalFlag || removalFlag === NULL_REMOVAL_STATE) {\n        // we do this after the flush has occurred such\n        // that the callbacks can be fired\n        engine.afterFlush(() => this.clearElementCache(element));\n        engine.destroyInnerAnimations(element);\n        engine._onRemovalComplete(element, context);\n      }\n    }\n  }\n\n  insertNode(element: any, parent: any): void {\n    addClass(element, this._hostClassName);\n  }\n\n  drainQueuedTransitions(microtaskId: number): QueueInstruction[] {\n    const instructions: QueueInstruction[] = [];\n    this._queue.forEach((entry) => {\n      const player = entry.player;\n      if (player.destroyed) return;\n\n      const element = entry.element;\n      const listeners = this._elementListeners.get(element);\n      if (listeners) {\n        listeners.forEach((listener: TriggerListener) => {\n          if (listener.name == entry.triggerName) {\n            const baseEvent = makeAnimationEvent(\n              element,\n              entry.triggerName,\n              entry.fromState.value,\n              entry.toState.value,\n            );\n            (baseEvent as any)['_data'] = microtaskId;\n            listenOnPlayer(entry.player, listener.phase, baseEvent, listener.callback);\n          }\n        });\n      }\n\n      if (player.markedForDestroy) {\n        this._engine.afterFlush(() => {\n          // now we can destroy the element properly since the event listeners have\n          // been bound to the player\n          player.destroy();\n        });\n      } else {\n        instructions.push(entry);\n      }\n    });\n\n    this._queue = [];\n\n    return instructions.sort((a, b) => {\n      // if depCount == 0 them move to front\n      // otherwise if a contains b then move back\n      const d0 = a.transition.ast.depCount;\n      const d1 = b.transition.ast.depCount;\n      if (d0 == 0 || d1 == 0) {\n        return d0 - d1;\n      }\n      return this._engine.driver.containsElement(a.element, b.element) ? 1 : -1;\n    });\n  }\n\n  destroy(context: any) {\n    this.players.forEach((p) => p.destroy());\n    this._signalRemovalForInnerTriggers(this.hostElement, context);\n  }\n}\n\ninterface QueuedTransition {\n  element: any;\n  instruction: AnimationTransitionInstruction;\n  player: TransitionAnimationPlayer;\n}\n\nexport class TransitionAnimationEngine {\n  public players: TransitionAnimationPlayer[] = [];\n  public newHostElements = new Map<any, AnimationTransitionNamespace>();\n  public playersByElement = new Map<any, TransitionAnimationPlayer[]>();\n  public playersByQueriedElement = new Map<any, TransitionAnimationPlayer[]>();\n  public statesByElement = new Map<any, Map<string, StateValue>>();\n  public disabledNodes = new Set<any>();\n\n  public totalAnimations = 0;\n  public totalQueuedPlayers = 0;\n\n  private _namespaceLookup: {[id: string]: AnimationTransitionNamespace} = {};\n  private _namespaceList: AnimationTransitionNamespace[] = [];\n  private _flushFns: (() => any)[] = [];\n  private _whenQuietFns: (() => any)[] = [];\n\n  public namespacesByHostElement = new Map<any, AnimationTransitionNamespace>();\n  public collectedEnterElements: any[] = [];\n  public collectedLeaveElements: any[] = [];\n\n  // this method is designed to be overridden by the code that uses this engine\n  public onRemovalComplete = (element: any, context: any) => {};\n\n  /** @internal */\n  _onRemovalComplete(element: any, context: any) {\n    this.onRemovalComplete(element, context);\n  }\n\n  constructor(\n    public bodyNode: any,\n    public driver: AnimationDriver,\n    private _normalizer: AnimationStyleNormalizer,\n    private readonly scheduler: ChangeDetectionScheduler | null,\n  ) {}\n\n  get queuedPlayers(): TransitionAnimationPlayer[] {\n    const players: TransitionAnimationPlayer[] = [];\n    this._namespaceList.forEach((ns) => {\n      ns.players.forEach((player) => {\n        if (player.queued) {\n          players.push(player);\n        }\n      });\n    });\n    return players;\n  }\n\n  createNamespace(namespaceId: string, hostElement: any) {\n    const ns = new AnimationTransitionNamespace(namespaceId, hostElement, this);\n    if (this.bodyNode && this.driver.containsElement(this.bodyNode, hostElement)) {\n      this._balanceNamespaceList(ns, hostElement);\n    } else {\n      // defer this later until flush during when the host element has\n      // been inserted so that we know exactly where to place it in\n      // the namespace list\n      this.newHostElements.set(hostElement, ns);\n\n      // given that this host element is a part of the animation code, it\n      // may or may not be inserted by a parent node that is of an\n      // animation renderer type. If this happens then we can still have\n      // access to this item when we query for :enter nodes. If the parent\n      // is a renderer then the set data-structure will normalize the entry\n      this.collectEnterElement(hostElement);\n    }\n    return (this._namespaceLookup[namespaceId] = ns);\n  }\n\n  private _balanceNamespaceList(ns: AnimationTransitionNamespace, hostElement: any) {\n    const namespaceList = this._namespaceList;\n    const namespacesByHostElement = this.namespacesByHostElement;\n    const limit = namespaceList.length - 1;\n    if (limit >= 0) {\n      let found = false;\n      // Find the closest ancestor with an existing namespace so we can then insert `ns` after it,\n      // establishing a top-down ordering of namespaces in `this._namespaceList`.\n      let ancestor = this.driver.getParentElement(hostElement);\n      while (ancestor) {\n        const ancestorNs = namespacesByHostElement.get(ancestor);\n        if (ancestorNs) {\n          // An animation namespace has been registered for this ancestor, so we insert `ns`\n          // right after it to establish top-down ordering of animation namespaces.\n          const index = namespaceList.indexOf(ancestorNs);\n          namespaceList.splice(index + 1, 0, ns);\n          found = true;\n          break;\n        }\n        ancestor = this.driver.getParentElement(ancestor);\n      }\n      if (!found) {\n        // No namespace exists that is an ancestor of `ns`, so `ns` is inserted at the front to\n        // ensure that any existing descendants are ordered after `ns`, retaining the desired\n        // top-down ordering.\n        namespaceList.unshift(ns);\n      }\n    } else {\n      namespaceList.push(ns);\n    }\n\n    namespacesByHostElement.set(hostElement, ns);\n    return ns;\n  }\n\n  register(namespaceId: string, hostElement: any) {\n    let ns = this._namespaceLookup[namespaceId];\n    if (!ns) {\n      ns = this.createNamespace(namespaceId, hostElement);\n    }\n    return ns;\n  }\n\n  registerTrigger(namespaceId: string, name: string, trigger: AnimationTrigger) {\n    let ns = this._namespaceLookup[namespaceId];\n    if (ns && ns.register(name, trigger)) {\n      this.totalAnimations++;\n    }\n  }\n\n  destroy(namespaceId: string, context: any) {\n    if (!namespaceId) return;\n    this.afterFlush(() => {});\n\n    this.afterFlushAnimationsDone(() => {\n      const ns = this._fetchNamespace(namespaceId);\n      this.namespacesByHostElement.delete(ns.hostElement);\n      const index = this._namespaceList.indexOf(ns);\n      if (index >= 0) {\n        this._namespaceList.splice(index, 1);\n      }\n      ns.destroy(context);\n      delete this._namespaceLookup[namespaceId];\n    });\n  }\n\n  private _fetchNamespace(id: string) {\n    return this._namespaceLookup[id];\n  }\n\n  fetchNamespacesByElement(element: any): Set<AnimationTransitionNamespace> {\n    // normally there should only be one namespace per element, however\n    // if @triggers are placed on both the component element and then\n    // its host element (within the component code) then there will be\n    // two namespaces returned. We use a set here to simply deduplicate\n    // the namespaces in case (for the reason described above) there are multiple triggers\n    const namespaces = new Set<AnimationTransitionNamespace>();\n    const elementStates = this.statesByElement.get(element);\n    if (elementStates) {\n      for (let stateValue of elementStates.values()) {\n        if (stateValue.namespaceId) {\n          const ns = this._fetchNamespace(stateValue.namespaceId);\n          if (ns) {\n            namespaces.add(ns);\n          }\n        }\n      }\n    }\n    return namespaces;\n  }\n\n  trigger(namespaceId: string, element: any, name: string, value: any): boolean {\n    if (isElementNode(element)) {\n      const ns = this._fetchNamespace(namespaceId);\n      if (ns) {\n        ns.trigger(element, name, value);\n        return true;\n      }\n    }\n    return false;\n  }\n\n  insertNode(namespaceId: string, element: any, parent: any, insertBefore: boolean): void {\n    if (!isElementNode(element)) return;\n\n    // special case for when an element is removed and reinserted (move operation)\n    // when this occurs we do not want to use the element for deletion later\n    const details = element[REMOVAL_FLAG] as ElementAnimationState;\n    if (details && details.setForRemoval) {\n      details.setForRemoval = false;\n      details.setForMove = true;\n      const index = this.collectedLeaveElements.indexOf(element);\n      if (index >= 0) {\n        this.collectedLeaveElements.splice(index, 1);\n      }\n    }\n\n    // in the event that the namespaceId is blank then the caller\n    // code does not contain any animation code in it, but it is\n    // just being called so that the node is marked as being inserted\n    if (namespaceId) {\n      const ns = this._fetchNamespace(namespaceId);\n      // This if-statement is a workaround for router issue #21947.\n      // The router sometimes hits a race condition where while a route\n      // is being instantiated a new navigation arrives, triggering leave\n      // animation of DOM that has not been fully initialized, until this\n      // is resolved, we need to handle the scenario when DOM is not in a\n      // consistent state during the animation.\n      if (ns) {\n        ns.insertNode(element, parent);\n      }\n    }\n\n    // only *directives and host elements are inserted before\n    if (insertBefore) {\n      this.collectEnterElement(element);\n    }\n  }\n\n  collectEnterElement(element: any) {\n    this.collectedEnterElements.push(element);\n  }\n\n  markElementAsDisabled(element: any, value: boolean) {\n    if (value) {\n      if (!this.disabledNodes.has(element)) {\n        this.disabledNodes.add(element);\n        addClass(element, DISABLED_CLASSNAME);\n      }\n    } else if (this.disabledNodes.has(element)) {\n      this.disabledNodes.delete(element);\n      removeClass(element, DISABLED_CLASSNAME);\n    }\n  }\n\n  removeNode(namespaceId: string, element: any, context: any): void {\n    if (isElementNode(element)) {\n      this.scheduler?.notify();\n      const ns = namespaceId ? this._fetchNamespace(namespaceId) : null;\n      if (ns) {\n        ns.removeNode(element, context);\n      } else {\n        this.markElementAsRemoved(namespaceId, element, false, context);\n      }\n\n      const hostNS = this.namespacesByHostElement.get(element);\n      if (hostNS && hostNS.id !== namespaceId) {\n        hostNS.removeNode(element, context);\n      }\n    } else {\n      this._onRemovalComplete(element, context);\n    }\n  }\n\n  markElementAsRemoved(\n    namespaceId: string,\n    element: any,\n    hasAnimation?: boolean,\n    context?: any,\n    previousTriggersValues?: Map<string, string>,\n  ) {\n    this.collectedLeaveElements.push(element);\n    element[REMOVAL_FLAG] = {\n      namespaceId,\n      setForRemoval: context,\n      hasAnimation,\n      removedBeforeQueried: false,\n      previousTriggersValues,\n    };\n  }\n\n  listen(\n    namespaceId: string,\n    element: any,\n    name: string,\n    phase: string,\n    callback: (event: any) => boolean,\n  ): () => any {\n    if (isElementNode(element)) {\n      return this._fetchNamespace(namespaceId).listen(element, name, phase, callback);\n    }\n    return () => {};\n  }\n\n  private _buildInstruction(\n    entry: QueueInstruction,\n    subTimelines: ElementInstructionMap,\n    enterClassName: string,\n    leaveClassName: string,\n    skipBuildAst?: boolean,\n  ) {\n    return entry.transition.build(\n      this.driver,\n      entry.element,\n      entry.fromState.value,\n      entry.toState.value,\n      enterClassName,\n      leaveClassName,\n      entry.fromState.options,\n      entry.toState.options,\n      subTimelines,\n      skipBuildAst,\n    );\n  }\n\n  destroyInnerAnimations(containerElement: any) {\n    let elements = this.driver.query(containerElement, NG_TRIGGER_SELECTOR, true);\n    elements.forEach((element) => this.destroyActiveAnimationsForElement(element));\n\n    if (this.playersByQueriedElement.size == 0) return;\n\n    elements = this.driver.query(containerElement, NG_ANIMATING_SELECTOR, true);\n    elements.forEach((element) => this.finishActiveQueriedAnimationOnElement(element));\n  }\n\n  destroyActiveAnimationsForElement(element: any) {\n    const players = this.playersByElement.get(element);\n    if (players) {\n      players.forEach((player) => {\n        // special case for when an element is set for destruction, but hasn't started.\n        // in this situation we want to delay the destruction until the flush occurs\n        // so that any event listeners attached to the player are triggered.\n        if (player.queued) {\n          player.markedForDestroy = true;\n        } else {\n          player.destroy();\n        }\n      });\n    }\n  }\n\n  finishActiveQueriedAnimationOnElement(element: any) {\n    const players = this.playersByQueriedElement.get(element);\n    if (players) {\n      players.forEach((player) => player.finish());\n    }\n  }\n\n  whenRenderingDone(): Promise<any> {\n    return new Promise<void>((resolve) => {\n      if (this.players.length) {\n        return optimizeGroupPlayer(this.players).onDone(() => resolve());\n      } else {\n        resolve();\n      }\n    });\n  }\n\n  processLeaveNode(element: any) {\n    const details = element[REMOVAL_FLAG] as ElementAnimationState;\n    if (details && details.setForRemoval) {\n      // this will prevent it from removing it twice\n      element[REMOVAL_FLAG] = NULL_REMOVAL_STATE;\n      if (details.namespaceId) {\n        this.destroyInnerAnimations(element);\n        const ns = this._fetchNamespace(details.namespaceId);\n        if (ns) {\n          ns.clearElementCache(element);\n        }\n      }\n      this._onRemovalComplete(element, details.setForRemoval);\n    }\n\n    if (element.classList?.contains(DISABLED_CLASSNAME)) {\n      this.markElementAsDisabled(element, false);\n    }\n\n    this.driver.query(element, DISABLED_SELECTOR, true).forEach((node) => {\n      this.markElementAsDisabled(node, false);\n    });\n  }\n\n  flush(microtaskId: number = -1) {\n    let players: AnimationPlayer[] = [];\n    if (this.newHostElements.size) {\n      this.newHostElements.forEach((ns, element) => this._balanceNamespaceList(ns, element));\n      this.newHostElements.clear();\n    }\n\n    if (this.totalAnimations && this.collectedEnterElements.length) {\n      for (let i = 0; i < this.collectedEnterElements.length; i++) {\n        const elm = this.collectedEnterElements[i];\n        addClass(elm, STAR_CLASSNAME);\n      }\n    }\n\n    if (\n      this._namespaceList.length &&\n      (this.totalQueuedPlayers || this.collectedLeaveElements.length)\n    ) {\n      const cleanupFns: Function[] = [];\n      try {\n        players = this._flushAnimations(cleanupFns, microtaskId);\n      } finally {\n        for (let i = 0; i < cleanupFns.length; i++) {\n          cleanupFns[i]();\n        }\n      }\n    } else {\n      for (let i = 0; i < this.collectedLeaveElements.length; i++) {\n        const element = this.collectedLeaveElements[i];\n        this.processLeaveNode(element);\n      }\n    }\n\n    this.totalQueuedPlayers = 0;\n    this.collectedEnterElements.length = 0;\n    this.collectedLeaveElements.length = 0;\n    this._flushFns.forEach((fn) => fn());\n    this._flushFns = [];\n\n    if (this._whenQuietFns.length) {\n      // we move these over to a variable so that\n      // if any new callbacks are registered in another\n      // flush they do not populate the existing set\n      const quietFns = this._whenQuietFns;\n      this._whenQuietFns = [];\n\n      if (players.length) {\n        optimizeGroupPlayer(players).onDone(() => {\n          quietFns.forEach((fn) => fn());\n        });\n      } else {\n        quietFns.forEach((fn) => fn());\n      }\n    }\n  }\n\n  reportError(errors: Error[]) {\n    throw triggerTransitionsFailed(errors);\n  }\n\n  private _flushAnimations(\n    cleanupFns: Function[],\n    microtaskId: number,\n  ): TransitionAnimationPlayer[] {\n    const subTimelines = new ElementInstructionMap();\n    const skippedPlayers: TransitionAnimationPlayer[] = [];\n    const skippedPlayersMap = new Map<any, AnimationPlayer[]>();\n    const queuedInstructions: QueuedTransition[] = [];\n    const queriedElements = new Map<any, TransitionAnimationPlayer[]>();\n    const allPreStyleElements = new Map<any, Set<string>>();\n    const allPostStyleElements = new Map<any, Set<string>>();\n\n    const disabledElementsSet = new Set<any>();\n    this.disabledNodes.forEach((node) => {\n      disabledElementsSet.add(node);\n      const nodesThatAreDisabled = this.driver.query(node, QUEUED_SELECTOR, true);\n      for (let i = 0; i < nodesThatAreDisabled.length; i++) {\n        disabledElementsSet.add(nodesThatAreDisabled[i]);\n      }\n    });\n\n    const bodyNode = this.bodyNode;\n    const allTriggerElements = Array.from(this.statesByElement.keys());\n    const enterNodeMap = buildRootMap(allTriggerElements, this.collectedEnterElements);\n\n    // this must occur before the instructions are built below such that\n    // the :enter queries match the elements (since the timeline queries\n    // are fired during instruction building).\n    const enterNodeMapIds = new Map<any, string>();\n    let i = 0;\n    enterNodeMap.forEach((nodes, root) => {\n      const className = ENTER_CLASSNAME + i++;\n      enterNodeMapIds.set(root, className);\n      nodes.forEach((node) => addClass(node, className));\n    });\n\n    const allLeaveNodes: any[] = [];\n    const mergedLeaveNodes = new Set<any>();\n    const leaveNodesWithoutAnimations = new Set<any>();\n    for (let i = 0; i < this.collectedLeaveElements.length; i++) {\n      const element = this.collectedLeaveElements[i];\n      const details = element[REMOVAL_FLAG] as ElementAnimationState;\n      if (details && details.setForRemoval) {\n        allLeaveNodes.push(element);\n        mergedLeaveNodes.add(element);\n        if (details.hasAnimation) {\n          this.driver\n            .query(element, STAR_SELECTOR, true)\n            .forEach((elm) => mergedLeaveNodes.add(elm));\n        } else {\n          leaveNodesWithoutAnimations.add(element);\n        }\n      }\n    }\n\n    const leaveNodeMapIds = new Map<any, string>();\n    const leaveNodeMap = buildRootMap(allTriggerElements, Array.from(mergedLeaveNodes));\n    leaveNodeMap.forEach((nodes, root) => {\n      const className = LEAVE_CLASSNAME + i++;\n      leaveNodeMapIds.set(root, className);\n      nodes.forEach((node) => addClass(node, className));\n    });\n\n    cleanupFns.push(() => {\n      enterNodeMap.forEach((nodes, root) => {\n        const className = enterNodeMapIds.get(root)!;\n        nodes.forEach((node) => removeClass(node, className));\n      });\n\n      leaveNodeMap.forEach((nodes, root) => {\n        const className = leaveNodeMapIds.get(root)!;\n        nodes.forEach((node) => removeClass(node, className));\n      });\n\n      allLeaveNodes.forEach((element) => {\n        this.processLeaveNode(element);\n      });\n    });\n\n    const allPlayers: TransitionAnimationPlayer[] = [];\n    const erroneousTransitions: AnimationTransitionInstruction[] = [];\n    for (let i = this._namespaceList.length - 1; i >= 0; i--) {\n      const ns = this._namespaceList[i];\n      ns.drainQueuedTransitions(microtaskId).forEach((entry) => {\n        const player = entry.player;\n        const element = entry.element;\n        allPlayers.push(player);\n\n        if (this.collectedEnterElements.length) {\n          const details = element[REMOVAL_FLAG] as ElementAnimationState;\n          // animations for move operations (elements being removed and reinserted,\n          // e.g. when the order of an *ngFor list changes) are currently not supported\n          if (details && details.setForMove) {\n            if (\n              details.previousTriggersValues &&\n              details.previousTriggersValues.has(entry.triggerName)\n            ) {\n              const previousValue = details.previousTriggersValues.get(entry.triggerName) as string;\n\n              // we need to restore the previous trigger value since the element has\n              // only been moved and hasn't actually left the DOM\n              const triggersWithStates = this.statesByElement.get(entry.element);\n              if (triggersWithStates && triggersWithStates.has(entry.triggerName)) {\n                const state = triggersWithStates.get(entry.triggerName)!;\n                state.value = previousValue;\n                triggersWithStates.set(entry.triggerName, state);\n              }\n            }\n\n            player.destroy();\n            return;\n          }\n        }\n\n        const nodeIsOrphaned = !bodyNode || !this.driver.containsElement(bodyNode, element);\n        const leaveClassName = leaveNodeMapIds.get(element)!;\n        const enterClassName = enterNodeMapIds.get(element)!;\n        const instruction = this._buildInstruction(\n          entry,\n          subTimelines,\n          enterClassName,\n          leaveClassName,\n          nodeIsOrphaned,\n        )!;\n        if (instruction.errors && instruction.errors.length) {\n          erroneousTransitions.push(instruction);\n          return;\n        }\n\n        // even though the element may not be in the DOM, it may still\n        // be added at a later point (due to the mechanics of content\n        // projection and/or dynamic component insertion) therefore it's\n        // important to still style the element.\n        if (nodeIsOrphaned) {\n          player.onStart(() => eraseStyles(element, instruction.fromStyles));\n          player.onDestroy(() => setStyles(element, instruction.toStyles));\n          skippedPlayers.push(player);\n          return;\n        }\n\n        // if an unmatched transition is queued and ready to go\n        // then it SHOULD NOT render an animation and cancel the\n        // previously running animations.\n        if (entry.isFallbackTransition) {\n          player.onStart(() => eraseStyles(element, instruction.fromStyles));\n          player.onDestroy(() => setStyles(element, instruction.toStyles));\n          skippedPlayers.push(player);\n          return;\n        }\n\n        // this means that if a parent animation uses this animation as a sub-trigger\n        // then it will instruct the timeline builder not to add a player delay, but\n        // instead stretch the first keyframe gap until the animation starts. This is\n        // important in order to prevent extra initialization styles from being\n        // required by the user for the animation.\n        const timelines: AnimationTimelineInstruction[] = [];\n        instruction.timelines.forEach((tl) => {\n          tl.stretchStartingKeyframe = true;\n          if (!this.disabledNodes.has(tl.element)) {\n            timelines.push(tl);\n          }\n        });\n        instruction.timelines = timelines;\n\n        subTimelines.append(element, instruction.timelines);\n\n        const tuple = {instruction, player, element};\n\n        queuedInstructions.push(tuple);\n\n        instruction.queriedElements.forEach((element) =>\n          getOrSetDefaultValue(queriedElements, element, []).push(player),\n        );\n\n        instruction.preStyleProps.forEach((stringMap, element) => {\n          if (stringMap.size) {\n            let setVal: Set<string> = allPreStyleElements.get(element)!;\n            if (!setVal) {\n              allPreStyleElements.set(element, (setVal = new Set<string>()));\n            }\n            stringMap.forEach((_, prop) => setVal.add(prop));\n          }\n        });\n\n        instruction.postStyleProps.forEach((stringMap, element) => {\n          let setVal: Set<string> = allPostStyleElements.get(element)!;\n          if (!setVal) {\n            allPostStyleElements.set(element, (setVal = new Set<string>()));\n          }\n          stringMap.forEach((_, prop) => setVal.add(prop));\n        });\n      });\n    }\n\n    if (erroneousTransitions.length) {\n      const errors: Error[] = [];\n      erroneousTransitions.forEach((instruction) => {\n        errors.push(transitionFailed(instruction.triggerName, instruction.errors!));\n      });\n\n      allPlayers.forEach((player) => player.destroy());\n      this.reportError(errors);\n    }\n\n    const allPreviousPlayersMap = new Map<any, TransitionAnimationPlayer[]>();\n    // this map tells us which element in the DOM tree is contained by\n    // which animation. Further down this map will get populated once\n    // the players are built and in doing so we can use it to efficiently\n    // figure out if a sub player is skipped due to a parent player having priority.\n    const animationElementMap = new Map<any, any>();\n    queuedInstructions.forEach((entry) => {\n      const element = entry.element;\n      if (subTimelines.has(element)) {\n        animationElementMap.set(element, element);\n        this._beforeAnimationBuild(\n          entry.player.namespaceId,\n          entry.instruction,\n          allPreviousPlayersMap,\n        );\n      }\n    });\n\n    skippedPlayers.forEach((player) => {\n      const element = player.element;\n      const previousPlayers = this._getPreviousPlayers(\n        element,\n        false,\n        player.namespaceId,\n        player.triggerName,\n        null,\n      );\n      previousPlayers.forEach((prevPlayer) => {\n        getOrSetDefaultValue(allPreviousPlayersMap, element, []).push(prevPlayer);\n        prevPlayer.destroy();\n      });\n    });\n\n    // this is a special case for nodes that will be removed either by\n    // having their own leave animations or by being queried in a container\n    // that will be removed once a parent animation is complete. The idea\n    // here is that * styles must be identical to ! styles because of\n    // backwards compatibility (* is also filled in by default in many places).\n    // Otherwise * styles will return an empty value or \"auto\" since the element\n    // passed to getComputedStyle will not be visible (since * === destination)\n    const replaceNodes = allLeaveNodes.filter((node) => {\n      return replacePostStylesAsPre(node, allPreStyleElements, allPostStyleElements);\n    });\n\n    // POST STAGE: fill the * styles\n    const postStylesMap = new Map<any, ɵStyleDataMap>();\n    const allLeaveQueriedNodes = cloakAndComputeStyles(\n      postStylesMap,\n      this.driver,\n      leaveNodesWithoutAnimations,\n      allPostStyleElements,\n      AUTO_STYLE,\n    );\n\n    allLeaveQueriedNodes.forEach((node) => {\n      if (replacePostStylesAsPre(node, allPreStyleElements, allPostStyleElements)) {\n        replaceNodes.push(node);\n      }\n    });\n\n    // PRE STAGE: fill the ! styles\n    const preStylesMap = new Map<any, ɵStyleDataMap>();\n    enterNodeMap.forEach((nodes, root) => {\n      cloakAndComputeStyles(\n        preStylesMap,\n        this.driver,\n        new Set(nodes),\n        allPreStyleElements,\n        PRE_STYLE,\n      );\n    });\n\n    replaceNodes.forEach((node) => {\n      const post = postStylesMap.get(node);\n      const pre = preStylesMap.get(node);\n      postStylesMap.set(node, new Map([...(post?.entries() ?? []), ...(pre?.entries() ?? [])]));\n    });\n\n    const rootPlayers: TransitionAnimationPlayer[] = [];\n    const subPlayers: TransitionAnimationPlayer[] = [];\n    const NO_PARENT_ANIMATION_ELEMENT_DETECTED = {};\n    queuedInstructions.forEach((entry) => {\n      const {element, player, instruction} = entry;\n      // this means that it was never consumed by a parent animation which\n      // means that it is independent and therefore should be set for animation\n      if (subTimelines.has(element)) {\n        if (disabledElementsSet.has(element)) {\n          player.onDestroy(() => setStyles(element, instruction.toStyles));\n          player.disabled = true;\n          player.overrideTotalTime(instruction.totalTime);\n          skippedPlayers.push(player);\n          return;\n        }\n\n        // this will flow up the DOM and query the map to figure out\n        // if a parent animation has priority over it. In the situation\n        // that a parent is detected then it will cancel the loop. If\n        // nothing is detected, or it takes a few hops to find a parent,\n        // then it will fill in the missing nodes and signal them as having\n        // a detected parent (or a NO_PARENT value via a special constant).\n        let parentWithAnimation: any = NO_PARENT_ANIMATION_ELEMENT_DETECTED;\n        if (animationElementMap.size > 1) {\n          let elm = element;\n          const parentsToAdd: any[] = [];\n          while ((elm = elm.parentNode)) {\n            const detectedParent = animationElementMap.get(elm);\n            if (detectedParent) {\n              parentWithAnimation = detectedParent;\n              break;\n            }\n            parentsToAdd.push(elm);\n          }\n          parentsToAdd.forEach((parent) => animationElementMap.set(parent, parentWithAnimation));\n        }\n\n        const innerPlayer = this._buildAnimation(\n          player.namespaceId,\n          instruction,\n          allPreviousPlayersMap,\n          skippedPlayersMap,\n          preStylesMap,\n          postStylesMap,\n        );\n\n        player.setRealPlayer(innerPlayer);\n\n        if (parentWithAnimation === NO_PARENT_ANIMATION_ELEMENT_DETECTED) {\n          rootPlayers.push(player);\n        } else {\n          const parentPlayers = this.playersByElement.get(parentWithAnimation);\n          if (parentPlayers && parentPlayers.length) {\n            player.parentPlayer = optimizeGroupPlayer(parentPlayers);\n          }\n          skippedPlayers.push(player);\n        }\n      } else {\n        eraseStyles(element, instruction.fromStyles);\n        player.onDestroy(() => setStyles(element, instruction.toStyles));\n        // there still might be a ancestor player animating this\n        // element therefore we will still add it as a sub player\n        // even if its animation may be disabled\n        subPlayers.push(player);\n        if (disabledElementsSet.has(element)) {\n          skippedPlayers.push(player);\n        }\n      }\n    });\n\n    // find all of the sub players' corresponding inner animation players\n    subPlayers.forEach((player) => {\n      // even if no players are found for a sub animation it\n      // will still complete itself after the next tick since it's Noop\n      const playersForElement = skippedPlayersMap.get(player.element);\n      if (playersForElement && playersForElement.length) {\n        const innerPlayer = optimizeGroupPlayer(playersForElement);\n        player.setRealPlayer(innerPlayer);\n      }\n    });\n\n    // the reason why we don't actually play the animation is\n    // because all that a skipped player is designed to do is to\n    // fire the start/done transition callback events\n    skippedPlayers.forEach((player) => {\n      if (player.parentPlayer) {\n        player.syncPlayerEvents(player.parentPlayer);\n      } else {\n        player.destroy();\n      }\n    });\n\n    // run through all of the queued removals and see if they\n    // were picked up by a query. If not then perform the removal\n    // operation right away unless a parent animation is ongoing.\n    for (let i = 0; i < allLeaveNodes.length; i++) {\n      const element = allLeaveNodes[i];\n      const details = element[REMOVAL_FLAG] as ElementAnimationState;\n      removeClass(element, LEAVE_CLASSNAME);\n\n      // this means the element has a removal animation that is being\n      // taken care of and therefore the inner elements will hang around\n      // until that animation is over (or the parent queried animation)\n      if (details && details.hasAnimation) continue;\n\n      let players: TransitionAnimationPlayer[] = [];\n\n      // if this element is queried or if it contains queried children\n      // then we want for the element not to be removed from the page\n      // until the queried animations have finished\n      if (queriedElements.size) {\n        let queriedPlayerResults = queriedElements.get(element);\n        if (queriedPlayerResults && queriedPlayerResults.length) {\n          players.push(...queriedPlayerResults);\n        }\n\n        let queriedInnerElements = this.driver.query(element, NG_ANIMATING_SELECTOR, true);\n        for (let j = 0; j < queriedInnerElements.length; j++) {\n          let queriedPlayers = queriedElements.get(queriedInnerElements[j]);\n          if (queriedPlayers && queriedPlayers.length) {\n            players.push(...queriedPlayers);\n          }\n        }\n      }\n\n      const activePlayers = players.filter((p) => !p.destroyed);\n      if (activePlayers.length) {\n        removeNodesAfterAnimationDone(this, element, activePlayers);\n      } else {\n        this.processLeaveNode(element);\n      }\n    }\n\n    // this is required so the cleanup method doesn't remove them\n    allLeaveNodes.length = 0;\n\n    rootPlayers.forEach((player) => {\n      this.players.push(player);\n      player.onDone(() => {\n        player.destroy();\n\n        const index = this.players.indexOf(player);\n        this.players.splice(index, 1);\n      });\n      player.play();\n    });\n\n    return rootPlayers;\n  }\n\n  afterFlush(callback: () => any) {\n    this._flushFns.push(callback);\n  }\n\n  afterFlushAnimationsDone(callback: () => any) {\n    this._whenQuietFns.push(callback);\n  }\n\n  private _getPreviousPlayers(\n    element: string,\n    isQueriedElement: boolean,\n    namespaceId?: string,\n    triggerName?: string,\n    toStateValue?: any,\n  ): TransitionAnimationPlayer[] {\n    let players: TransitionAnimationPlayer[] = [];\n    if (isQueriedElement) {\n      const queriedElementPlayers = this.playersByQueriedElement.get(element);\n      if (queriedElementPlayers) {\n        players = queriedElementPlayers;\n      }\n    } else {\n      const elementPlayers = this.playersByElement.get(element);\n      if (elementPlayers) {\n        const isRemovalAnimation = !toStateValue || toStateValue == VOID_VALUE;\n        elementPlayers.forEach((player) => {\n          if (player.queued) return;\n          if (!isRemovalAnimation && player.triggerName != triggerName) return;\n          players.push(player);\n        });\n      }\n    }\n    if (namespaceId || triggerName) {\n      players = players.filter((player) => {\n        if (namespaceId && namespaceId != player.namespaceId) return false;\n        if (triggerName && triggerName != player.triggerName) return false;\n        return true;\n      });\n    }\n    return players;\n  }\n\n  private _beforeAnimationBuild(\n    namespaceId: string,\n    instruction: AnimationTransitionInstruction,\n    allPreviousPlayersMap: Map<any, TransitionAnimationPlayer[]>,\n  ) {\n    const triggerName = instruction.triggerName;\n    const rootElement = instruction.element;\n\n    // when a removal animation occurs, ALL previous players are collected\n    // and destroyed (even if they are outside of the current namespace)\n    const targetNameSpaceId: string | undefined = instruction.isRemovalTransition\n      ? undefined\n      : namespaceId;\n    const targetTriggerName: string | undefined = instruction.isRemovalTransition\n      ? undefined\n      : triggerName;\n\n    for (const timelineInstruction of instruction.timelines) {\n      const element = timelineInstruction.element;\n      const isQueriedElement = element !== rootElement;\n      const players = getOrSetDefaultValue(allPreviousPlayersMap, element, []);\n      const previousPlayers = this._getPreviousPlayers(\n        element,\n        isQueriedElement,\n        targetNameSpaceId,\n        targetTriggerName,\n        instruction.toState,\n      );\n      previousPlayers.forEach((player) => {\n        const realPlayer = (player as TransitionAnimationPlayer).getRealPlayer() as any;\n        if (realPlayer.beforeDestroy) {\n          realPlayer.beforeDestroy();\n        }\n        player.destroy();\n        players.push(player);\n      });\n    }\n\n    // this needs to be done so that the PRE/POST styles can be\n    // computed properly without interfering with the previous animation\n    eraseStyles(rootElement, instruction.fromStyles);\n  }\n\n  private _buildAnimation(\n    namespaceId: string,\n    instruction: AnimationTransitionInstruction,\n    allPreviousPlayersMap: Map<any, TransitionAnimationPlayer[]>,\n    skippedPlayersMap: Map<any, AnimationPlayer[]>,\n    preStylesMap: Map<any, ɵStyleDataMap>,\n    postStylesMap: Map<any, ɵStyleDataMap>,\n  ): AnimationPlayer {\n    const triggerName = instruction.triggerName;\n    const rootElement = instruction.element;\n\n    // we first run this so that the previous animation player\n    // data can be passed into the successive animation players\n    const allQueriedPlayers: TransitionAnimationPlayer[] = [];\n    const allConsumedElements = new Set<any>();\n    const allSubElements = new Set<any>();\n    const allNewPlayers = instruction.timelines.map((timelineInstruction) => {\n      const element = timelineInstruction.element;\n      allConsumedElements.add(element);\n\n      // FIXME (matsko): make sure to-be-removed animations are removed properly\n      const details = element[REMOVAL_FLAG];\n      if (details && details.removedBeforeQueried)\n        return new NoopAnimationPlayer(timelineInstruction.duration, timelineInstruction.delay);\n      const isQueriedElement = element !== rootElement;\n      const previousPlayers = flattenGroupPlayers(\n        (allPreviousPlayersMap.get(element) || EMPTY_PLAYER_ARRAY).map((p) => p.getRealPlayer()),\n      ).filter((p) => {\n        // the `element` is not apart of the AnimationPlayer definition, but\n        // Mock/WebAnimations\n        // use the element within their implementation. This will be added in Angular5 to\n        // AnimationPlayer\n        const pp = p as any;\n        return pp.element ? pp.element === element : false;\n      });\n\n      const preStyles = preStylesMap.get(element);\n      const postStyles = postStylesMap.get(element);\n\n      const keyframes = normalizeKeyframes(\n        this._normalizer,\n        timelineInstruction.keyframes,\n        preStyles,\n        postStyles,\n      );\n      const player = this._buildPlayer(timelineInstruction, keyframes, previousPlayers);\n\n      // this means that this particular player belongs to a sub trigger. It is\n      // important that we match this player up with the corresponding (@trigger.listener)\n      if (timelineInstruction.subTimeline && skippedPlayersMap) {\n        allSubElements.add(element);\n      }\n\n      if (isQueriedElement) {\n        const wrappedPlayer = new TransitionAnimationPlayer(namespaceId, triggerName, element);\n        wrappedPlayer.setRealPlayer(player);\n        allQueriedPlayers.push(wrappedPlayer);\n      }\n\n      return player;\n    });\n\n    allQueriedPlayers.forEach((player) => {\n      getOrSetDefaultValue(this.playersByQueriedElement, player.element, []).push(player);\n      player.onDone(() => deleteOrUnsetInMap(this.playersByQueriedElement, player.element, player));\n    });\n\n    allConsumedElements.forEach((element) => addClass(element, NG_ANIMATING_CLASSNAME));\n    const player = optimizeGroupPlayer(allNewPlayers);\n    player.onDestroy(() => {\n      allConsumedElements.forEach((element) => removeClass(element, NG_ANIMATING_CLASSNAME));\n      setStyles(rootElement, instruction.toStyles);\n    });\n\n    // this basically makes all of the callbacks for sub element animations\n    // be dependent on the upper players for when they finish\n    allSubElements.forEach((element) => {\n      getOrSetDefaultValue(skippedPlayersMap, element, []).push(player);\n    });\n\n    return player;\n  }\n\n  private _buildPlayer(\n    instruction: AnimationTimelineInstruction,\n    keyframes: Array<ɵStyleDataMap>,\n    previousPlayers: AnimationPlayer[],\n  ): AnimationPlayer {\n    if (keyframes.length > 0) {\n      return this.driver.animate(\n        instruction.element,\n        keyframes,\n        instruction.duration,\n        instruction.delay,\n        instruction.easing,\n        previousPlayers,\n      );\n    }\n\n    // special case for when an empty transition|definition is provided\n    // ... there is no point in rendering an empty animation\n    return new NoopAnimationPlayer(instruction.duration, instruction.delay);\n  }\n}\n\nexport class TransitionAnimationPlayer implements AnimationPlayer {\n  private _player: AnimationPlayer = new NoopAnimationPlayer();\n  private _containsRealPlayer = false;\n\n  private _queuedCallbacks = new Map<string, ((event: any) => any)[]>();\n  public readonly destroyed = false;\n  public parentPlayer: AnimationPlayer | null = null;\n\n  public markedForDestroy: boolean = false;\n  public disabled = false;\n\n  readonly queued: boolean = true;\n  public readonly totalTime: number = 0;\n\n  constructor(\n    public namespaceId: string,\n    public triggerName: string,\n    public element: any,\n  ) {}\n\n  setRealPlayer(player: AnimationPlayer) {\n    if (this._containsRealPlayer) return;\n\n    this._player = player;\n    this._queuedCallbacks.forEach((callbacks, phase) => {\n      callbacks.forEach((callback) => listenOnPlayer(player, phase, undefined, callback));\n    });\n\n    this._queuedCallbacks.clear();\n    this._containsRealPlayer = true;\n    this.overrideTotalTime(player.totalTime);\n    (this as Writable<this>).queued = false;\n  }\n\n  getRealPlayer() {\n    return this._player;\n  }\n\n  overrideTotalTime(totalTime: number) {\n    (this as any).totalTime = totalTime;\n  }\n\n  syncPlayerEvents(player: AnimationPlayer) {\n    const p = this._player as any;\n    if (p.triggerCallback) {\n      player.onStart(() => p.triggerCallback!('start'));\n    }\n    player.onDone(() => this.finish());\n    player.onDestroy(() => this.destroy());\n  }\n\n  private _queueEvent(name: string, callback: (event: any) => any): void {\n    getOrSetDefaultValue(this._queuedCallbacks, name, []).push(callback);\n  }\n\n  onDone(fn: () => void): void {\n    if (this.queued) {\n      this._queueEvent('done', fn);\n    }\n    this._player.onDone(fn);\n  }\n\n  onStart(fn: () => void): void {\n    if (this.queued) {\n      this._queueEvent('start', fn);\n    }\n    this._player.onStart(fn);\n  }\n\n  onDestroy(fn: () => void): void {\n    if (this.queued) {\n      this._queueEvent('destroy', fn);\n    }\n    this._player.onDestroy(fn);\n  }\n\n  init(): void {\n    this._player.init();\n  }\n\n  hasStarted(): boolean {\n    return this.queued ? false : this._player.hasStarted();\n  }\n\n  play(): void {\n    !this.queued && this._player.play();\n  }\n\n  pause(): void {\n    !this.queued && this._player.pause();\n  }\n\n  restart(): void {\n    !this.queued && this._player.restart();\n  }\n\n  finish(): void {\n    this._player.finish();\n  }\n\n  destroy(): void {\n    (this as {destroyed: boolean}).destroyed = true;\n    this._player.destroy();\n  }\n\n  reset(): void {\n    !this.queued && this._player.reset();\n  }\n\n  setPosition(p: number): void {\n    if (!this.queued) {\n      this._player.setPosition(p);\n    }\n  }\n\n  getPosition(): number {\n    return this.queued ? 0 : this._player.getPosition();\n  }\n\n  /** @internal */\n  triggerCallback(phaseName: string): void {\n    const p = this._player as any;\n    if (p.triggerCallback) {\n      p.triggerCallback(phaseName);\n    }\n  }\n}\n\nfunction deleteOrUnsetInMap<T, V>(map: Map<T, V[]>, key: T, value: V) {\n  let currentValues = map.get(key);\n  if (currentValues) {\n    if (currentValues.length) {\n      const index = currentValues.indexOf(value);\n      currentValues.splice(index, 1);\n    }\n    if (currentValues.length == 0) {\n      map.delete(key);\n    }\n  }\n  return currentValues;\n}\n\nfunction normalizeTriggerValue(value: any): any {\n  // we use `!= null` here because it's the most simple\n  // way to test against a \"falsy\" value without mixing\n  // in empty strings or a zero value. DO NOT OPTIMIZE.\n  return value != null ? value : null;\n}\n\nfunction isElementNode(node: any) {\n  return node && node['nodeType'] === 1;\n}\n\nfunction isTriggerEventValid(eventName: string): boolean {\n  return eventName == 'start' || eventName == 'done';\n}\n\nfunction cloakElement(element: any, value?: string) {\n  const oldValue = element.style.display;\n  element.style.display = value != null ? value : 'none';\n  return oldValue;\n}\n\nfunction cloakAndComputeStyles(\n  valuesMap: Map<any, ɵStyleDataMap>,\n  driver: AnimationDriver,\n  elements: Set<any>,\n  elementPropsMap: Map<any, Set<string>>,\n  defaultStyle: string,\n): any[] {\n  const cloakVals: string[] = [];\n  elements.forEach((element) => cloakVals.push(cloakElement(element)));\n\n  const failedElements: any[] = [];\n\n  elementPropsMap.forEach((props: Set<string>, element: any) => {\n    const styles: ɵStyleDataMap = new Map();\n    props.forEach((prop) => {\n      const value = driver.computeStyle(element, prop, defaultStyle);\n      styles.set(prop, value);\n\n      // there is no easy way to detect this because a sub element could be removed\n      // by a parent animation element being detached.\n      if (!value || value.length == 0) {\n        element[REMOVAL_FLAG] = NULL_REMOVED_QUERIED_STATE;\n        failedElements.push(element);\n      }\n    });\n    valuesMap.set(element, styles);\n  });\n\n  // we use a index variable here since Set.forEach(a, i) does not return\n  // an index value for the closure (but instead just the value)\n  let i = 0;\n  elements.forEach((element) => cloakElement(element, cloakVals[i++]));\n\n  return failedElements;\n}\n\n/*\nSince the Angular renderer code will return a collection of inserted\nnodes in all areas of a DOM tree, it's up to this algorithm to figure\nout which nodes are roots for each animation @trigger.\n\nBy placing each inserted node into a Set and traversing upwards, it\nis possible to find the @trigger elements and well any direct *star\ninsertion nodes, if a @trigger root is found then the enter element\nis placed into the Map[@trigger] spot.\n */\nfunction buildRootMap(roots: any[], nodes: any[]): Map<any, any[]> {\n  const rootMap = new Map<any, any[]>();\n  roots.forEach((root) => rootMap.set(root, []));\n\n  if (nodes.length == 0) return rootMap;\n\n  const NULL_NODE = 1;\n  const nodeSet = new Set(nodes);\n  const localRootMap = new Map<any, any>();\n\n  function getRoot(node: any): any {\n    if (!node) return NULL_NODE;\n\n    let root = localRootMap.get(node);\n    if (root) return root;\n\n    const parent = node.parentNode;\n    if (rootMap.has(parent)) {\n      // ngIf inside @trigger\n      root = parent;\n    } else if (nodeSet.has(parent)) {\n      // ngIf inside ngIf\n      root = NULL_NODE;\n    } else {\n      // recurse upwards\n      root = getRoot(parent);\n    }\n\n    localRootMap.set(node, root);\n    return root;\n  }\n\n  nodes.forEach((node) => {\n    const root = getRoot(node);\n    if (root !== NULL_NODE) {\n      rootMap.get(root)!.push(node);\n    }\n  });\n\n  return rootMap;\n}\n\nfunction addClass(element: any, className: string) {\n  element.classList?.add(className);\n}\n\nfunction removeClass(element: any, className: string) {\n  element.classList?.remove(className);\n}\n\nfunction removeNodesAfterAnimationDone(\n  engine: TransitionAnimationEngine,\n  element: any,\n  players: AnimationPlayer[],\n) {\n  optimizeGroupPlayer(players).onDone(() => engine.processLeaveNode(element));\n}\n\nfunction flattenGroupPlayers(players: AnimationPlayer[]): AnimationPlayer[] {\n  const finalPlayers: AnimationPlayer[] = [];\n  _flattenGroupPlayersRecur(players, finalPlayers);\n  return finalPlayers;\n}\n\nfunction _flattenGroupPlayersRecur(players: AnimationPlayer[], finalPlayers: AnimationPlayer[]) {\n  for (let i = 0; i < players.length; i++) {\n    const player = players[i];\n    if (player instanceof AnimationGroupPlayer) {\n      _flattenGroupPlayersRecur(player.players, finalPlayers);\n    } else {\n      finalPlayers.push(player);\n    }\n  }\n}\n\nfunction objEquals(a: {[key: string]: any}, b: {[key: string]: any}): boolean {\n  const k1 = Object.keys(a);\n  const k2 = Object.keys(b);\n  if (k1.length != k2.length) return false;\n  for (let i = 0; i < k1.length; i++) {\n    const prop = k1[i];\n    if (!b.hasOwnProperty(prop) || a[prop] !== b[prop]) return false;\n  }\n  return true;\n}\n\nfunction replacePostStylesAsPre(\n  element: any,\n  allPreStyleElements: Map<any, Set<string>>,\n  allPostStyleElements: Map<any, Set<string>>,\n): boolean {\n  const postEntry = allPostStyleElements.get(element);\n  if (!postEntry) return false;\n\n  let preEntry = allPreStyleElements.get(element);\n  if (preEntry) {\n    postEntry.forEach((data) => preEntry!.add(data));\n  } else {\n    allPreStyleElements.set(element, postEntry);\n  }\n\n  allPostStyleElements.delete(element);\n  return true;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AnimationMetadata, AnimationPlayer, AnimationTriggerMetadata} from '@angular/animations';\nimport {ɵChangeDetectionScheduler as ChangeDetectionScheduler} from '@angular/core';\n\nimport {TriggerAst} from '../dsl/animation_ast';\nimport {buildAnimationAst} from '../dsl/animation_ast_builder';\nimport {AnimationTrigger, buildTrigger} from '../dsl/animation_trigger';\nimport {AnimationStyleNormalizer} from '../dsl/style_normalization/animation_style_normalizer';\nimport {triggerBuildFailed} from '../error_helpers';\nimport {warnTriggerBuild} from '../warning_helpers';\n\nimport {AnimationDriver} from './animation_driver';\nimport {parseTimelineCommand} from './shared';\nimport {TimelineAnimationEngine} from './timeline_animation_engine';\nimport {TransitionAnimationEngine} from './transition_animation_engine';\n\nexport class AnimationEngine {\n  private _transitionEngine: TransitionAnimationEngine;\n  private _timelineEngine: TimelineAnimationEngine;\n\n  private _triggerCache: {[key: string]: AnimationTrigger} = {};\n\n  // this method is designed to be overridden by the code that uses this engine\n  public onRemovalComplete = (element: any, context: any) => {};\n\n  constructor(\n    doc: Document,\n    private _driver: AnimationDriver,\n    private _normalizer: AnimationStyleNormalizer,\n    scheduler: ChangeDetectionScheduler | null,\n  ) {\n    this._transitionEngine = new TransitionAnimationEngine(\n      doc.body,\n      _driver,\n      _normalizer,\n      scheduler,\n    );\n    this._timelineEngine = new TimelineAnimationEngine(doc.body, _driver, _normalizer);\n\n    this._transitionEngine.onRemovalComplete = (element: any, context: any) =>\n      this.onRemovalComplete(element, context);\n  }\n\n  registerTrigger(\n    componentId: string,\n    namespaceId: string,\n    hostElement: any,\n    name: string,\n    metadata: AnimationTriggerMetadata,\n  ): void {\n    const cacheKey = componentId + '-' + name;\n    let trigger = this._triggerCache[cacheKey];\n    if (!trigger) {\n      const errors: Error[] = [];\n      const warnings: string[] = [];\n      const ast = buildAnimationAst(\n        this._driver,\n        metadata as AnimationMetadata,\n        errors,\n        warnings,\n      ) as TriggerAst;\n      if (errors.length) {\n        throw triggerBuildFailed(name, errors);\n      }\n      if (warnings.length) {\n        warnTriggerBuild(name, warnings);\n      }\n      trigger = buildTrigger(name, ast, this._normalizer);\n      this._triggerCache[cacheKey] = trigger;\n    }\n    this._transitionEngine.registerTrigger(namespaceId, name, trigger);\n  }\n\n  register(namespaceId: string, hostElement: any) {\n    this._transitionEngine.register(namespaceId, hostElement);\n  }\n\n  destroy(namespaceId: string, context: any) {\n    this._transitionEngine.destroy(namespaceId, context);\n  }\n\n  onInsert(namespaceId: string, element: any, parent: any, insertBefore: boolean): void {\n    this._transitionEngine.insertNode(namespaceId, element, parent, insertBefore);\n  }\n\n  onRemove(namespaceId: string, element: any, context: any): void {\n    this._transitionEngine.removeNode(namespaceId, element, context);\n  }\n\n  disableAnimations(element: any, disable: boolean) {\n    this._transitionEngine.markElementAsDisabled(element, disable);\n  }\n\n  process(namespaceId: string, element: any, property: string, value: any) {\n    if (property.charAt(0) == '@') {\n      const [id, action] = parseTimelineCommand(property);\n      const args = value as any[];\n      this._timelineEngine.command(id, element, action, args);\n    } else {\n      this._transitionEngine.trigger(namespaceId, element, property, value);\n    }\n  }\n\n  listen(\n    namespaceId: string,\n    element: any,\n    eventName: string,\n    eventPhase: string,\n    callback: (event: any) => any,\n  ): () => any {\n    // @@listen\n    if (eventName.charAt(0) == '@') {\n      const [id, action] = parseTimelineCommand(eventName);\n      return this._timelineEngine.listen(id, element, action, callback);\n    }\n    return this._transitionEngine.listen(namespaceId, element, eventName, eventPhase, callback);\n  }\n\n  flush(microtaskId: number = -1): void {\n    this._transitionEngine.flush(microtaskId);\n  }\n\n  get players(): AnimationPlayer[] {\n    return [...this._transitionEngine.players, ...this._timelineEngine.players];\n  }\n\n  whenRenderingDone(): Promise<any> {\n    return this._transitionEngine.whenRenderingDone();\n  }\n\n  afterFlushAnimationsDone(cb: VoidFunction): void {\n    this._transitionEngine.afterFlushAnimationsDone(cb);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {ɵStyleDataMap} from '@angular/animations';\n\nimport {eraseStyles, setStyles} from '../util';\n\n/**\n * Returns an instance of `SpecialCasedStyles` if and when any special (non animateable) styles are\n * detected.\n *\n * In CSS there exist properties that cannot be animated within a keyframe animation\n * (whether it be via CSS keyframes or web-animations) and the animation implementation\n * will ignore them. This function is designed to detect those special cased styles and\n * return a container that will be executed at the start and end of the animation.\n *\n * @returns an instance of `SpecialCasedStyles` if any special styles are detected otherwise `null`\n */\nexport function packageNonAnimatableStyles(\n  element: any,\n  styles: ɵStyleDataMap | Array<ɵStyleDataMap>,\n): SpecialCasedStyles | null {\n  let startStyles: ɵStyleDataMap | null = null;\n  let endStyles: ɵStyleDataMap | null = null;\n  if (Array.isArray(styles) && styles.length) {\n    startStyles = filterNonAnimatableStyles(styles[0]);\n    if (styles.length > 1) {\n      endStyles = filterNonAnimatableStyles(styles[styles.length - 1]);\n    }\n  } else if (styles instanceof Map) {\n    startStyles = filterNonAnimatableStyles(styles);\n  }\n\n  return startStyles || endStyles ? new SpecialCasedStyles(element, startStyles, endStyles) : null;\n}\n\n/**\n * Designed to be executed during a keyframe-based animation to apply any special-cased styles.\n *\n * When started (when the `start()` method is run) then the provided `startStyles`\n * will be applied. When finished (when the `finish()` method is called) the\n * `endStyles` will be applied as well any any starting styles. Finally when\n * `destroy()` is called then all styles will be removed.\n */\nexport class SpecialCasedStyles {\n  static initialStylesByElement = /* @__PURE__ */ new WeakMap<any, ɵStyleDataMap>();\n\n  private _state = SpecialCasedStylesState.Pending;\n  private _initialStyles!: ɵStyleDataMap;\n\n  constructor(\n    private _element: any,\n    private _startStyles: ɵStyleDataMap | null,\n    private _endStyles: ɵStyleDataMap | null,\n  ) {\n    let initialStyles = SpecialCasedStyles.initialStylesByElement.get(_element);\n    if (!initialStyles) {\n      SpecialCasedStyles.initialStylesByElement.set(_element, (initialStyles = new Map()));\n    }\n    this._initialStyles = initialStyles;\n  }\n\n  start() {\n    if (this._state < SpecialCasedStylesState.Started) {\n      if (this._startStyles) {\n        setStyles(this._element, this._startStyles, this._initialStyles);\n      }\n      this._state = SpecialCasedStylesState.Started;\n    }\n  }\n\n  finish() {\n    this.start();\n    if (this._state < SpecialCasedStylesState.Finished) {\n      setStyles(this._element, this._initialStyles);\n      if (this._endStyles) {\n        setStyles(this._element, this._endStyles);\n        this._endStyles = null;\n      }\n      this._state = SpecialCasedStylesState.Started;\n    }\n  }\n\n  destroy() {\n    this.finish();\n    if (this._state < SpecialCasedStylesState.Destroyed) {\n      SpecialCasedStyles.initialStylesByElement.delete(this._element);\n      if (this._startStyles) {\n        eraseStyles(this._element, this._startStyles);\n        this._endStyles = null;\n      }\n      if (this._endStyles) {\n        eraseStyles(this._element, this._endStyles);\n        this._endStyles = null;\n      }\n      setStyles(this._element, this._initialStyles);\n      this._state = SpecialCasedStylesState.Destroyed;\n    }\n  }\n}\n\n/**\n * An enum of states reflective of what the status of `SpecialCasedStyles` is.\n *\n * Depending on how `SpecialCasedStyles` is interacted with, the start and end\n * styles may not be applied in the same way. This enum ensures that if and when\n * the ending styles are applied then the starting styles are applied. It is\n * also used to reflect what the current status of the special cased styles are\n * which helps prevent the starting/ending styles not be applied twice. It is\n * also used to cleanup the styles once `SpecialCasedStyles` is destroyed.\n */\nconst enum SpecialCasedStylesState {\n  Pending = 0,\n  Started = 1,\n  Finished = 2,\n  Destroyed = 3,\n}\n\nfunction filterNonAnimatableStyles(styles: ɵStyleDataMap): ɵStyleDataMap | null {\n  let result: ɵStyleDataMap | null = null;\n  styles.forEach((val, prop) => {\n    if (isNonAnimatableStyle(prop)) {\n      result = result || new Map();\n      result.set(prop, val);\n    }\n  });\n  return result;\n}\n\nfunction isNonAnimatableStyle(prop: string) {\n  return prop === 'display' || prop === 'position';\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AnimationPlayer, ɵStyleDataMap} from '@angular/animations';\n\nimport {computeStyle} from '../../util';\nimport {SpecialCasedStyles} from '../special_cased_styles';\n\nexport class WebAnimationsPlayer implements AnimationPlayer {\n  private _onDoneFns: Function[] = [];\n  private _onStartFns: Function[] = [];\n  private _onDestroyFns: Function[] = [];\n  private _duration: number;\n  private _delay: number;\n  private _initialized = false;\n  private _finished = false;\n  private _started = false;\n  private _destroyed = false;\n  private _finalKeyframe?: ɵStyleDataMap;\n\n  // the following original fns are persistent copies of the _onStartFns and _onDoneFns\n  // and are used to reset the fns to their original values upon reset()\n  // (since the _onStartFns and _onDoneFns get deleted after they are called)\n  private _originalOnDoneFns: Function[] = [];\n  private _originalOnStartFns: Function[] = [];\n\n  // using non-null assertion because it's re(set) by init();\n  public readonly domPlayer!: Animation;\n  public time = 0;\n\n  public parentPlayer: AnimationPlayer | null = null;\n  public currentSnapshot: ɵStyleDataMap = new Map();\n\n  constructor(\n    public element: any,\n    public keyframes: Array<ɵStyleDataMap>,\n    public options: {[key: string]: string | number},\n    private _specialStyles?: SpecialCasedStyles | null,\n  ) {\n    this._duration = <number>options['duration'];\n    this._delay = <number>options['delay'] || 0;\n    this.time = this._duration + this._delay;\n  }\n\n  private _onFinish() {\n    if (!this._finished) {\n      this._finished = true;\n      this._onDoneFns.forEach((fn) => fn());\n      this._onDoneFns = [];\n    }\n  }\n\n  init(): void {\n    this._buildPlayer();\n    this._preparePlayerBeforeStart();\n  }\n\n  private _buildPlayer(): void {\n    if (this._initialized) return;\n    this._initialized = true;\n\n    const keyframes = this.keyframes;\n    // @ts-expect-error overwriting a readonly property\n    this.domPlayer = this._triggerWebAnimation(this.element, keyframes, this.options);\n    this._finalKeyframe = keyframes.length ? keyframes[keyframes.length - 1] : new Map();\n    const onFinish = () => this._onFinish();\n    this.domPlayer.addEventListener('finish', onFinish);\n    this.onDestroy(() => {\n      // We must remove the `finish` event listener once an animation has completed all its\n      // iterations. This action is necessary to prevent a memory leak since the listener captures\n      // `this`, creating a closure that prevents `this` from being garbage collected.\n      this.domPlayer.removeEventListener('finish', onFinish);\n    });\n  }\n\n  private _preparePlayerBeforeStart() {\n    // this is required so that the player doesn't start to animate right away\n    if (this._delay) {\n      this._resetDomPlayerState();\n    } else {\n      this.domPlayer.pause();\n    }\n  }\n\n  private _convertKeyframesToObject(keyframes: Array<ɵStyleDataMap>): any[] {\n    const kfs: any[] = [];\n    keyframes.forEach((frame) => {\n      kfs.push(Object.fromEntries(frame));\n    });\n    return kfs;\n  }\n\n  /** @internal */\n  _triggerWebAnimation(\n    element: HTMLElement,\n    keyframes: Array<ɵStyleDataMap>,\n    options: any,\n  ): Animation {\n    return element.animate(this._convertKeyframesToObject(keyframes), options);\n  }\n\n  onStart(fn: () => void): void {\n    this._originalOnStartFns.push(fn);\n    this._onStartFns.push(fn);\n  }\n\n  onDone(fn: () => void): void {\n    this._originalOnDoneFns.push(fn);\n    this._onDoneFns.push(fn);\n  }\n\n  onDestroy(fn: () => void): void {\n    this._onDestroyFns.push(fn);\n  }\n\n  play(): void {\n    this._buildPlayer();\n    if (!this.hasStarted()) {\n      this._onStartFns.forEach((fn) => fn());\n      this._onStartFns = [];\n      this._started = true;\n      if (this._specialStyles) {\n        this._specialStyles.start();\n      }\n    }\n    this.domPlayer.play();\n  }\n\n  pause(): void {\n    this.init();\n    this.domPlayer.pause();\n  }\n\n  finish(): void {\n    this.init();\n    if (this._specialStyles) {\n      this._specialStyles.finish();\n    }\n    this._onFinish();\n    this.domPlayer.finish();\n  }\n\n  reset(): void {\n    this._resetDomPlayerState();\n    this._destroyed = false;\n    this._finished = false;\n    this._started = false;\n    this._onStartFns = this._originalOnStartFns;\n    this._onDoneFns = this._originalOnDoneFns;\n  }\n\n  private _resetDomPlayerState() {\n    if (this.domPlayer) {\n      this.domPlayer.cancel();\n    }\n  }\n\n  restart(): void {\n    this.reset();\n    this.play();\n  }\n\n  hasStarted(): boolean {\n    return this._started;\n  }\n\n  destroy(): void {\n    if (!this._destroyed) {\n      this._destroyed = true;\n      this._resetDomPlayerState();\n      this._onFinish();\n      if (this._specialStyles) {\n        this._specialStyles.destroy();\n      }\n      this._onDestroyFns.forEach((fn) => fn());\n      this._onDestroyFns = [];\n    }\n  }\n\n  setPosition(p: number): void {\n    if (this.domPlayer === undefined) {\n      this.init();\n    }\n    this.domPlayer.currentTime = p * this.time;\n  }\n\n  getPosition(): number {\n    // tsc is complaining with TS2362 without the conversion to number\n    return +(this.domPlayer.currentTime ?? 0) / this.time;\n  }\n\n  get totalTime(): number {\n    return this._delay + this._duration;\n  }\n\n  beforeDestroy() {\n    const styles: ɵStyleDataMap = new Map();\n    if (this.hasStarted()) {\n      // note: this code is invoked only when the `play` function was called prior to this\n      // (thus `hasStarted` returns true), this implies that the code that initializes\n      // `_finalKeyframe` has also been executed and the non-null assertion can be safely used here\n      const finalKeyframe = this._finalKeyframe!;\n      finalKeyframe.forEach((val, prop) => {\n        if (prop !== 'offset') {\n          styles.set(prop, this._finished ? val : computeStyle(this.element, prop));\n        }\n      });\n    }\n\n    this.currentSnapshot = styles;\n  }\n\n  /** @internal */\n  triggerCallback(phaseName: string): void {\n    const methods = phaseName === 'start' ? this._onStartFns : this._onDoneFns;\n    methods.forEach((fn) => fn());\n    methods.length = 0;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AnimationPlayer, ɵStyleDataMap} from '@angular/animations';\n\nimport {\n  allowPreviousPlayerStylesMerge,\n  balancePreviousStylesIntoKeyframes,\n  camelCaseToDashCase,\n  computeStyle,\n  normalizeKeyframes,\n} from '../../util';\nimport {AnimationDriver} from '../animation_driver';\nimport {\n  containsElement,\n  getParentElement,\n  invokeQuery,\n  validateStyleProperty,\n  validateWebAnimatableStyleProperty,\n} from '../shared';\nimport {packageNonAnimatableStyles} from '../special_cased_styles';\n\nimport {WebAnimationsPlayer} from './web_animations_player';\n\nexport class WebAnimationsDriver implements AnimationDriver {\n  validateStyleProperty(prop: string): boolean {\n    // Perform actual validation in dev mode only, in prod mode this check is a noop.\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      return validateStyleProperty(prop);\n    }\n    return true;\n  }\n\n  validateAnimatableStyleProperty(prop: string): boolean {\n    // Perform actual validation in dev mode only, in prod mode this check is a noop.\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const cssProp = camelCaseToDashCase(prop);\n      return validateWebAnimatableStyleProperty(cssProp);\n    }\n    return true;\n  }\n\n  matchesElement(_element: any, _selector: string): boolean {\n    // This method is deprecated and no longer in use so we return false.\n    return false;\n  }\n\n  containsElement(elm1: any, elm2: any): boolean {\n    return containsElement(elm1, elm2);\n  }\n\n  getParentElement(element: unknown): unknown {\n    return getParentElement(element);\n  }\n\n  query(element: any, selector: string, multi: boolean): any[] {\n    return invokeQuery(element, selector, multi);\n  }\n\n  computeStyle(element: any, prop: string, defaultValue?: string): string {\n    return computeStyle(element, prop);\n  }\n\n  animate(\n    element: any,\n    keyframes: Array<Map<string, string | number>>,\n    duration: number,\n    delay: number,\n    easing: string,\n    previousPlayers: AnimationPlayer[] = [],\n  ): AnimationPlayer {\n    const fill = delay == 0 ? 'both' : 'forwards';\n    const playerOptions: {[key: string]: string | number} = {duration, delay, fill};\n    // we check for this to avoid having a null|undefined value be present\n    // for the easing (which results in an error for certain browsers #9752)\n    if (easing) {\n      playerOptions['easing'] = easing;\n    }\n\n    const previousStyles: ɵStyleDataMap = new Map();\n    const previousWebAnimationPlayers = <WebAnimationsPlayer[]>(\n      previousPlayers.filter((player) => player instanceof WebAnimationsPlayer)\n    );\n    if (allowPreviousPlayerStylesMerge(duration, delay)) {\n      previousWebAnimationPlayers.forEach((player) => {\n        player.currentSnapshot.forEach((val, prop) => previousStyles.set(prop, val));\n      });\n    }\n\n    let _keyframes = normalizeKeyframes(keyframes).map((styles) => new Map(styles));\n    _keyframes = balancePreviousStylesIntoKeyframes(element, _keyframes, previousStyles);\n    const specialStyles = packageNonAnimatableStyles(element, _keyframes);\n    return new WebAnimationsPlayer(element, _keyframes, playerOptions, specialStyles);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵChangeDetectionScheduler as ChangeDetectionScheduler} from '@angular/core';\n\nimport {NoopAnimationStyleNormalizer} from './dsl/style_normalization/animation_style_normalizer';\nimport {WebAnimationsStyleNormalizer} from './dsl/style_normalization/web_animations_style_normalizer';\nimport {NoopAnimationDriver} from './render/animation_driver';\nimport {AnimationEngine} from './render/animation_engine_next';\nimport {WebAnimationsDriver} from './render/web_animations/web_animations_driver';\n\nexport function createEngine(\n  type: 'animations' | 'noop',\n  doc: Document,\n  scheduler: ChangeDetectionScheduler | null,\n): AnimationEngine {\n  // TODO: find a way to make this tree shakable.\n  if (type === 'noop') {\n    return new AnimationEngine(\n      doc,\n      new NoopAnimationDriver(),\n      new NoopAnimationStyleNormalizer(),\n      scheduler,\n    );\n  }\n\n  return new AnimationEngine(\n    doc,\n    new WebAnimationsDriver(),\n    new WebAnimationsStyleNormalizer(),\n    scheduler,\n  );\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {\n  AnimationMetadata,\n  AnimationMetadataType,\n  AnimationOptions,\n  ɵStyleDataMap,\n} from '@angular/animations';\n\nimport {buildingFailed, validationFailed} from '../error_helpers';\nimport {AnimationDriver} from '../render/animation_driver';\nimport {ENTER_CLASSNAME, LEAVE_CLASSNAME, normalizeStyles} from '../util';\nimport {warnValidation} from '../warning_helpers';\n\nimport {Ast} from './animation_ast';\nimport {buildAnimationAst} from './animation_ast_builder';\nimport {buildAnimationTimelines} from './animation_timeline_builder';\nimport {AnimationTimelineInstruction} from './animation_timeline_instruction';\nimport {ElementInstructionMap} from './element_instruction_map';\n\nexport class Animation {\n  private _animationAst: Ast<AnimationMetadataType>;\n  constructor(\n    private _driver: AnimationDriver,\n    input: AnimationMetadata | AnimationMetadata[],\n  ) {\n    const errors: Error[] = [];\n    const warnings: string[] = [];\n    const ast = buildAnimationAst(_driver, input, errors, warnings);\n    if (errors.length) {\n      throw validationFailed(errors);\n    }\n    if (warnings.length) {\n      warnValidation(warnings);\n    }\n    this._animationAst = ast;\n  }\n\n  buildTimelines(\n    element: any,\n    startingStyles: ɵStyleDataMap | Array<ɵStyleDataMap>,\n    destinationStyles: ɵStyleDataMap | Array<ɵStyleDataMap>,\n    options: AnimationOptions,\n    subInstructions?: ElementInstructionMap,\n  ): AnimationTimelineInstruction[] {\n    const start = Array.isArray(startingStyles)\n      ? normalizeStyles(startingStyles)\n      : <ɵStyleDataMap>startingStyles;\n    const dest = Array.isArray(destinationStyles)\n      ? normalizeStyles(destinationStyles)\n      : <ɵStyleDataMap>destinationStyles;\n    const errors: any = [];\n    subInstructions = subInstructions || new ElementInstructionMap();\n    const result = buildAnimationTimelines(\n      this._driver,\n      element,\n      this._animationAst,\n      ENTER_CLASSNAME,\n      LEAVE_CLASSNAME,\n      start,\n      dest,\n      options,\n      subInstructions,\n      errors,\n    );\n    if (errors.length) {\n      throw buildingFailed(errors);\n    }\n    return result;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nconst ANIMATION_PREFIX = '@';\nconst DISABLE_ANIMATIONS_FLAG = '@.disabled';\n\nimport {\n  Renderer2,\n  RendererFactory2,\n  RendererStyleFlags2,\n  ɵAnimationRendererType as AnimationRendererType,\n} from '@angular/core';\nimport type {AnimationEngine} from './animation_engine_next';\n\ntype AnimationFactoryWithListenerCallback = RendererFactory2 & {\n  scheduleListenerCallback: (count: number, fn: (e: any) => any, data: any) => void;\n};\n\nexport class BaseAnimationRenderer implements Renderer2 {\n  // We need to explicitly type this property because of an api-extractor bug\n  // See https://github.com/microsoft/rushstack/issues/4390\n  readonly ɵtype: AnimationRendererType.Regular = AnimationRendererType.Regular;\n\n  constructor(\n    protected namespaceId: string,\n    public delegate: Renderer2,\n    public engine: <PERSON><PERSON><PERSON>ine,\n    private _onDestroy?: () => void,\n  ) {}\n\n  get data() {\n    return this.delegate.data;\n  }\n\n  destroyNode(node: any): void {\n    this.delegate.destroyNode?.(node);\n  }\n\n  destroy(): void {\n    this.engine.destroy(this.namespaceId, this.delegate);\n    this.engine.afterFlushAnimationsDone(() => {\n      // Call the renderer destroy method after the animations has finished as otherwise\n      // styles will be removed too early which will cause an unstyled animation.\n      queueMicrotask(() => {\n        this.delegate.destroy();\n      });\n    });\n\n    this._onDestroy?.();\n  }\n\n  createElement(name: string, namespace?: string | null | undefined) {\n    return this.delegate.createElement(name, namespace);\n  }\n\n  createComment(value: string) {\n    return this.delegate.createComment(value);\n  }\n\n  createText(value: string) {\n    return this.delegate.createText(value);\n  }\n\n  appendChild(parent: any, newChild: any): void {\n    this.delegate.appendChild(parent, newChild);\n    this.engine.onInsert(this.namespaceId, newChild, parent, false);\n  }\n\n  insertBefore(parent: any, newChild: any, refChild: any, isMove: boolean = true): void {\n    this.delegate.insertBefore(parent, newChild, refChild);\n    // If `isMove` true than we should animate this insert.\n    this.engine.onInsert(this.namespaceId, newChild, parent, isMove);\n  }\n\n  removeChild(parent: any, oldChild: any, isHostElement?: boolean): void {\n    this.engine.onRemove(this.namespaceId, oldChild, this.delegate);\n  }\n\n  selectRootElement(selectorOrNode: any, preserveContent?: boolean) {\n    return this.delegate.selectRootElement(selectorOrNode, preserveContent);\n  }\n\n  parentNode(node: any) {\n    return this.delegate.parentNode(node);\n  }\n\n  nextSibling(node: any) {\n    return this.delegate.nextSibling(node);\n  }\n\n  setAttribute(el: any, name: string, value: string, namespace?: string | null | undefined): void {\n    this.delegate.setAttribute(el, name, value, namespace);\n  }\n\n  removeAttribute(el: any, name: string, namespace?: string | null | undefined): void {\n    this.delegate.removeAttribute(el, name, namespace);\n  }\n\n  addClass(el: any, name: string): void {\n    this.delegate.addClass(el, name);\n  }\n\n  removeClass(el: any, name: string): void {\n    this.delegate.removeClass(el, name);\n  }\n\n  setStyle(el: any, style: string, value: any, flags?: RendererStyleFlags2 | undefined): void {\n    this.delegate.setStyle(el, style, value, flags);\n  }\n\n  removeStyle(el: any, style: string, flags?: RendererStyleFlags2 | undefined): void {\n    this.delegate.removeStyle(el, style, flags);\n  }\n\n  setProperty(el: any, name: string, value: any): void {\n    if (name.charAt(0) == ANIMATION_PREFIX && name == DISABLE_ANIMATIONS_FLAG) {\n      this.disableAnimations(el, !!value);\n    } else {\n      this.delegate.setProperty(el, name, value);\n    }\n  }\n\n  setValue(node: any, value: string): void {\n    this.delegate.setValue(node, value);\n  }\n\n  listen(target: any, eventName: string, callback: (event: any) => boolean | void): () => void {\n    return this.delegate.listen(target, eventName, callback);\n  }\n\n  protected disableAnimations(element: any, value: boolean) {\n    this.engine.disableAnimations(element, value);\n  }\n}\n\nexport class AnimationRenderer extends BaseAnimationRenderer implements Renderer2 {\n  constructor(\n    public factory: AnimationFactoryWithListenerCallback,\n    namespaceId: string,\n    delegate: Renderer2,\n    engine: AnimationEngine,\n    onDestroy?: () => void,\n  ) {\n    super(namespaceId, delegate, engine, onDestroy);\n    this.namespaceId = namespaceId;\n  }\n\n  override setProperty(el: any, name: string, value: any): void {\n    if (name.charAt(0) == ANIMATION_PREFIX) {\n      if (name.charAt(1) == '.' && name == DISABLE_ANIMATIONS_FLAG) {\n        value = value === undefined ? true : !!value;\n        this.disableAnimations(el, value as boolean);\n      } else {\n        this.engine.process(this.namespaceId, el, name.slice(1), value);\n      }\n    } else {\n      this.delegate.setProperty(el, name, value);\n    }\n  }\n\n  override listen(\n    target: 'window' | 'document' | 'body' | any,\n    eventName: string,\n    callback: (event: any) => any,\n  ): () => void {\n    if (eventName.charAt(0) == ANIMATION_PREFIX) {\n      const element = resolveElementFromTarget(target);\n      let name = eventName.slice(1);\n      let phase = '';\n      // @listener.phase is for trigger animation callbacks\n      // @@listener is for animation builder callbacks\n      if (name.charAt(0) != ANIMATION_PREFIX) {\n        [name, phase] = parseTriggerCallbackName(name);\n      }\n      return this.engine.listen(this.namespaceId, element, name, phase, (event) => {\n        const countId = (event as any)['_data'] || -1;\n        this.factory.scheduleListenerCallback(countId, callback, event);\n      });\n    }\n    return this.delegate.listen(target, eventName, callback);\n  }\n}\n\nfunction resolveElementFromTarget(target: 'window' | 'document' | 'body' | any): any {\n  switch (target) {\n    case 'body':\n      return document.body;\n    case 'document':\n      return document;\n    case 'window':\n      return window;\n    default:\n      return target;\n  }\n}\n\nfunction parseTriggerCallbackName(triggerName: string) {\n  const dotIndex = triggerName.indexOf('.');\n  const trigger = triggerName.substring(0, dotIndex);\n  const phase = triggerName.slice(dotIndex + 1);\n  return [trigger, phase];\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AnimationTriggerMetadata} from '@angular/animations';\nimport type {NgZone, Renderer2, RendererFactory2, RendererType2} from '@angular/core';\n\nimport {AnimationEngine} from './animation_engine_next';\nimport {AnimationRenderer, BaseAnimationRenderer} from './renderer';\n\n// Define a recursive type to allow for nested arrays of `AnimationTriggerMetadata`. Note that an\n// interface declaration is used as TypeScript prior to 3.7 does not support recursive type\n// references, see https://github.com/microsoft/TypeScript/pull/33050 for details.\ntype NestedAnimationTriggerMetadata = AnimationTriggerMetadata | RecursiveAnimationTriggerMetadata;\ninterface RecursiveAnimationTriggerMetadata extends Array<NestedAnimationTriggerMetadata> {}\n\nexport class AnimationRendererFactory implements RendererFactory2 {\n  private _currentId: number = 0;\n  private _microtaskId: number = 1;\n  private _animationCallbacksBuffer: [(e: any) => any, any][] = [];\n  private _rendererCache = new Map<Renderer2, BaseAnimationRenderer>();\n  private _cdRecurDepth = 0;\n\n  constructor(\n    private delegate: RendererFactory2,\n    private engine: AnimationEngine,\n    private _zone: NgZone,\n  ) {\n    engine.onRemovalComplete = (element: any, delegate: Renderer2) => {\n      // Note: if a component element has a leave animation, and a host leave animation,\n      // the view engine will call `removeChild` for the parent\n      // component renderer as well as for the child component renderer.\n      // Therefore, we need to check if we already removed the element.\n      const parentNode = delegate?.parentNode(element);\n      if (parentNode) {\n        delegate.removeChild(parentNode, element);\n      }\n    };\n  }\n\n  createRenderer(hostElement: any, type: RendererType2): BaseAnimationRenderer {\n    const EMPTY_NAMESPACE_ID = '';\n\n    // cache the delegates to find out which cached delegate can\n    // be used by which cached renderer\n    const delegate = this.delegate.createRenderer(hostElement, type);\n    if (!hostElement || !type?.data?.['animation']) {\n      const cache = this._rendererCache;\n      let renderer: BaseAnimationRenderer | undefined = cache.get(delegate);\n      if (!renderer) {\n        // Ensure that the renderer is removed from the cache on destroy\n        // since it may contain references to detached DOM nodes.\n        const onRendererDestroy = () => cache.delete(delegate);\n        renderer = new BaseAnimationRenderer(\n          EMPTY_NAMESPACE_ID,\n          delegate,\n          this.engine,\n          onRendererDestroy,\n        );\n        // only cache this result when the base renderer is used\n        cache.set(delegate, renderer);\n      }\n      return renderer;\n    }\n\n    const componentId = type.id;\n    const namespaceId = type.id + '-' + this._currentId;\n    this._currentId++;\n\n    this.engine.register(namespaceId, hostElement);\n\n    const registerTrigger = (trigger: NestedAnimationTriggerMetadata) => {\n      if (Array.isArray(trigger)) {\n        trigger.forEach(registerTrigger);\n      } else {\n        this.engine.registerTrigger(componentId, namespaceId, hostElement, trigger.name, trigger);\n      }\n    };\n    const animationTriggers = type.data['animation'] as NestedAnimationTriggerMetadata[];\n    animationTriggers.forEach(registerTrigger);\n\n    return new AnimationRenderer(this, namespaceId, delegate, this.engine);\n  }\n\n  begin() {\n    this._cdRecurDepth++;\n    if (this.delegate.begin) {\n      this.delegate.begin();\n    }\n  }\n\n  private _scheduleCountTask() {\n    queueMicrotask(() => {\n      this._microtaskId++;\n    });\n  }\n\n  /** @internal */\n  scheduleListenerCallback(count: number, fn: (e: any) => any, data: any) {\n    if (count >= 0 && count < this._microtaskId) {\n      this._zone.run(() => fn(data));\n      return;\n    }\n\n    const animationCallbacksBuffer = this._animationCallbacksBuffer;\n    if (animationCallbacksBuffer.length == 0) {\n      queueMicrotask(() => {\n        this._zone.run(() => {\n          animationCallbacksBuffer.forEach((tuple) => {\n            const [fn, data] = tuple;\n            fn(data);\n          });\n          this._animationCallbacksBuffer = [];\n        });\n      });\n    }\n    animationCallbacksBuffer.push([fn, data]);\n  }\n\n  end() {\n    this._cdRecurDepth--;\n\n    // this is to prevent animations from running twice when an inner\n    // component does CD when a parent component instead has inserted it\n    if (this._cdRecurDepth == 0) {\n      this._zone.runOutsideAngular(() => {\n        this._scheduleCountTask();\n        this.engine.flush(this._microtaskId);\n      });\n    }\n    if (this.delegate.end) {\n      this.delegate.end();\n    }\n  }\n\n  whenRenderingDone(): Promise<any> {\n    return this.engine.whenRenderingDone();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all animation APIs of the animation browser package.\n */\nexport {AnimationDriver, NoopAnimationDriver} from './render/animation_driver';\nexport * from './private_export';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\nexport * from './src/browser';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// This file is not used to build this module. It is only used during editing\n// by the TypeScript language service and during build for verification. `ngc`\n// replaces this file with production index.ts when it rewrites private symbol\n// names.\n\nexport * from './public_api';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["RuntimeError", "normalizeKeyframes", "PRE_STYLE", "AnimationGroupPlayer"], "mappings": ";;;;;;;;;;AAWA,MAAM,UAAU,GAAG,OAAO,CAAC;AAErB,SAAU,kBAAkB,CAAC,GAAoB,EAAA;IACrD,OAAO,IAAIA,aAAY,CAErB,IAAA,8CAAA,SAAS,IAAI,CAA8B,2BAAA,EAAA,GAAG,CAAe,aAAA,CAAA,CAC9D,CAAC;AACJ,CAAC;SAEe,iBAAiB,GAAA;AAC/B,IAAA,OAAO,IAAIA,aAAY,CAAA,IAAA,6CAErB,SAAS,IAAI,kEAAkE,CAChF,CAAC;AACJ,CAAC;SAEe,kBAAkB,GAAA;AAChC,IAAA,OAAO,IAAIA,aAAY,CAAA,IAAA,8CAErB,SAAS,IAAI,+DAA+D,CAC7E,CAAC;AACJ,CAAC;AAEK,SAAU,kBAAkB,CAAC,OAAe,EAAA;IAChD,OAAO,IAAIA,aAAY,CAAA,IAAA,8CAErB,SAAS;QACP,CAA+C,4CAAA,EAAA,OAAO,CAA8B,4BAAA,CAAA,CACvF,CAAC;AACJ,CAAC;AAEK,SAAU,iBAAiB,CAAC,OAAe,EAAA;IAC/C,OAAO,IAAIA,aAAY,CAErB,IAAA,6CAAA,SAAS,IAAI,CAAkD,+CAAA,EAAA,OAAO,CAAE,CAAA,CACzE,CAAC;AACJ,CAAC;AAEK,SAAU,eAAe,CAAC,QAAgB,EAAA;IAC9C,OAAO,IAAIA,aAAY,CAErB,IAAA,2CAAA,SAAS,IAAI,CAA8C,2CAAA,EAAA,QAAQ,CAAE,CAAA,CACtE,CAAC;AACJ,CAAC;AAEe,SAAA,mBAAmB,CAAC,oBAA4B,EAAE,KAAa,EAAA;IAC7E,OAAO,IAAIA,aAAY,CAAA,IAAA,gDAErB,SAAS,IAAI,CAAuC,oCAAA,EAAA,oBAAoB,CAAI,CAAA,EAAA,KAAK,CAAE,CAAA,CACpF,CAAC;AACJ,CAAC;SAEe,cAAc,GAAA;IAC5B,OAAO,IAAIA,aAAY,CAAA,IAAA,yCAErB,SAAS;AACP,QAAA,sFAAsF,CACzF,CAAC;AACJ,CAAC;SAEe,iBAAiB,GAAA;AAC/B,IAAA,OAAO,IAAIA,aAAY,CAAA,IAAA,4CAErB,SAAS,IAAI,yEAAyE,CACvF,CAAC;AACJ,CAAC;AAEe,SAAA,YAAY,CAAC,YAAoB,EAAE,WAAqB,EAAA;IACtE,OAAO,IAAIA,aAAY,CAAA,IAAA,uCAErB,SAAS;QACP,CAAU,OAAA,EAAA,YAAY,CAAiF,8EAAA,EAAA,WAAW,CAAC,IAAI,CACrH,IAAI,CACL,CAAE,CAAA,CACN,CAAC;AACJ,CAAC;AAEK,SAAU,iBAAiB,CAAC,KAAa,EAAA;IAC7C,OAAO,IAAIA,aAAY,CAErB,IAAA,6CAAA,SAAS,IAAI,CAAmC,gCAAA,EAAA,KAAK,CAAkB,gBAAA,CAAA,CACxE,CAAC;AACJ,CAAC;AAEK,SAAU,eAAe,CAAC,IAAY,EAAA;IAC1C,OAAO,IAAIA,aAAY,CAAA,IAAA,0CAErB,SAAS;QACP,CAAoC,iCAAA,EAAA,IAAI,CAAkD,gDAAA,CAAA,CAC7F,CAAC;AACJ,CAAC;AAEK,SAAU,wBAAwB,CACtC,IAAY,EACZ,UAAkB,EAClB,QAAgB,EAChB,WAAmB,EACnB,SAAiB,EAAA;IAEjB,OAAO,IAAIA,aAAY,CAAA,IAAA,oDAErB,SAAS;QACP,CAAqB,kBAAA,EAAA,IAAI,CAAuC,oCAAA,EAAA,UAAU,CAAY,SAAA,EAAA,QAAQ,CAA4E,yEAAA,EAAA,WAAW,CAAY,SAAA,EAAA,SAAS,CAAK,GAAA,CAAA,CAClN,CAAC;AACJ,CAAC;SAEe,gBAAgB,GAAA;AAC9B,IAAA,OAAO,IAAIA,aAAY,CAAA,IAAA,2CAErB,SAAS,IAAI,CAAA,wDAAA,CAA0D,CACxE,CAAC;AACJ,CAAC;SAEe,aAAa,GAAA;AAC3B,IAAA,OAAO,IAAIA,aAAY,CAAA,IAAA,wCAErB,SAAS,IAAI,CAAA,2DAAA,CAA6D,CAC3E,CAAC;AACJ,CAAC;SAEe,yBAAyB,GAAA;AACvC,IAAA,OAAO,IAAIA,aAAY,CAAA,IAAA,uDAErB,SAAS,IAAI,CAAA,oDAAA,CAAsD,CACpE,CAAC;AACJ,CAAC;SAEe,uBAAuB,GAAA;AACrC,IAAA,OAAO,IAAIA,aAAY,CAAA,IAAA,mDAErB,SAAS,IAAI,CAAA,qEAAA,CAAuE,CACrF,CAAC;AACJ,CAAC;SAEe,cAAc,GAAA;AAC5B,IAAA,OAAO,IAAIA,aAAY,CAAA,IAAA,yCAErB,SAAS,IAAI,CAAA,4CAAA,CAA8C,CAC5D,CAAC;AACJ,CAAC;AAEK,SAAU,YAAY,CAAC,QAAgB,EAAA;IAC3C,OAAO,IAAIA,aAAY,CAAA,IAAA,uCAErB,SAAS;AACP,QAAA,CAAA,SAAA,EAAY,QAAQ,CAAA,2CAAA,EAA8C,QAAQ,CAAA,oDAAA,CAAsD,CACnI,CAAC;AACJ,CAAC;AAEK,SAAU,iBAAiB,CAAC,IAAY,EAAA;IAC5C,OAAO,IAAIA,aAAY,CAErB,IAAA,4CAAA,SAAS,IAAI,CAAuC,oCAAA,EAAA,IAAI,CAAoB,kBAAA,CAAA,CAC7E,CAAC;AACJ,CAAC;AAEK,SAAU,sBAAsB,CAAC,KAAa,EAAA;IAClD,OAAO,IAAIA,aAAY,CAErB,IAAA,kDAAA,SAAS,IAAI,CAA+B,4BAAA,EAAA,KAAK,CAAoB,kBAAA,CAAA,CACtE,CAAC;AACJ,CAAC;AAEK,SAAU,gBAAgB,CAAC,MAAe,EAAA;IAC9C,OAAO,IAAIA,aAAY,CAAA,IAAA,2CAErB,SAAS,IAAI,CAAiC,8BAAA,EAAA,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE,CAAA,CAC5F,CAAC;AACJ,CAAC;AAEK,SAAU,cAAc,CAAC,MAAe,EAAA;IAC5C,OAAO,IAAIA,aAAY,CAAA,IAAA,yCAErB,SAAS,IAAI,CAA+B,4BAAA,EAAA,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE,CAAA,CAC1F,CAAC;AACJ,CAAC;AAEe,SAAA,kBAAkB,CAAC,IAAY,EAAE,MAAe,EAAA;IAC9D,OAAO,IAAIA,aAAY,CAAA,IAAA,8CAErB,SAAS;QACP,CAA0B,uBAAA,EAAA,IAAI,0DAA0D,MAAM;aAC3F,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,CAAC;AACzB,aAAA,IAAI,CAAC,OAAO,CAAC,CAAA,CAAE,CACrB,CAAC;AACJ,CAAC;AAEK,SAAU,eAAe,CAAC,MAAe,EAAA;IAC7C,OAAO,IAAIA,aAAY,CAAA,IAAA,0CAErB,SAAS;QACP,CAAiD,8CAAA,EAAA,UAAU,GAAG,MAAM;aACjE,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,CAAC;AACzB,aAAA,IAAI,CAAC,UAAU,CAAC,CAAA,CAAE,CACxB,CAAC;AACJ,CAAC;AAEK,SAAU,cAAc,CAAC,MAAe,EAAA;IAC5C,OAAO,IAAIA,aAAY,CAAA,IAAA,6CAErB,SAAS;AACP,QAAA,CAAA,2DAAA,EAA8D,MAAM;aACjE,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,CAAC;AACzB,aAAA,IAAI,CAAC,IAAI,CAAC,CAAA,CAAE,CAClB,CAAC;AACJ,CAAC;SAEe,2BAA2B,GAAA;AACzC,IAAA,OAAO,IAAIA,aAAY,CAAA,IAAA,wDAErB,SAAS,IAAI,qEAAqE,CACnF,CAAC;AACJ,CAAC;AAEK,SAAU,qBAAqB,CAAC,MAAe,EAAA;IACnD,OAAO,IAAIA,aAAY,CAAA,IAAA,iDAErB,SAAS;AACP,QAAA,CAAA,2DAAA,EAA8D,MAAM;aACjE,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,CAAC;AACzB,aAAA,IAAI,CAAC,IAAI,CAAC,CAAA,CAAE,CAClB,CAAC;AACJ,CAAC;AAEK,SAAU,aAAa,CAAC,EAAU,EAAA;IACtC,OAAO,IAAIA,aAAY,CAErB,IAAA,wCAAA,SAAS,IAAI,CAAoD,iDAAA,EAAA,EAAE,CAAE,CAAA,CACtE,CAAC;AACJ,CAAC;AAEe,SAAA,cAAc,CAAC,KAAa,EAAE,IAAY,EAAA;IACxD,OAAO,IAAIA,aAAY,CAAA,IAAA,yCAErB,SAAS;AACP,QAAA,CAAA,iDAAA,EAAoD,KAAK,CAAA,iCAAA,EAAoC,IAAI,CAAA,iBAAA,CAAmB,CACvH,CAAC;AACJ,CAAC;AAEK,SAAU,YAAY,CAAC,IAAY,EAAA;IACvC,OAAO,IAAIA,aAAY,CAAA,IAAA,uCAErB,SAAS;QACP,CAA8C,2CAAA,EAAA,IAAI,CAA4C,0CAAA,CAAA,CACjG,CAAC;AACJ,CAAC;AAEe,SAAA,uBAAuB,CAAC,KAAa,EAAE,IAAY,EAAA;IACjE,OAAO,IAAIA,aAAY,CAAA,IAAA,mDAErB,SAAS;AACP,QAAA,CAAA,sCAAA,EAAyC,KAAK,CAAA,6BAAA,EAAgC,IAAI,CAAA,mBAAA,CAAqB,CAC1G,CAAC;AACJ,CAAC;AAEK,SAAU,mBAAmB,CAAC,IAAY,EAAA;IAC9C,OAAO,IAAIA,aAAY,CAErB,IAAA,8CAAA,SAAS,IAAI,CAAmC,gCAAA,EAAA,IAAI,CAA4B,0BAAA,CAAA,CACjF,CAAC;AACJ,CAAC;AAEK,SAAU,wBAAwB,CAAC,MAAe,EAAA;IACtD,OAAO,IAAIA,aAAY,CAAA,IAAA,oDAErB,SAAS;AACP,QAAA,CAAA,+EAAA,EAAkF,MAAM;aACrF,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,CAAC;AACzB,aAAA,IAAI,CAAC,IAAI,CAAC,CAAA,CAAE,CAClB,CAAC;AACJ,CAAC;AAEe,SAAA,oBAAoB,CAAC,IAAY,EAAE,MAAe,EAAA;IAChE,OAAO,IAAIA,aAAY,CAAA,IAAA,gDAErB,SAAS;AACP,QAAA,CAAA,0BAAA,EAA6B,IAAI,CAAA,qBAAA,EAAwB,UAAU,CAAA,EAAG,MAAM;aACzE,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,CAAC;AACzB,aAAA,IAAI,CAAC,UAAU,CAAC,CAAA,CAAE,CACxB,CAAC;AACJ,CAAC;AAEe,SAAA,gBAAgB,CAAC,IAAY,EAAE,MAAe,EAAA;AAC5D,IAAA,OAAO,IAAIA,aAAY,CAErB,IAAA,2CAAA,SAAS,IAAI,CAAA,CAAA,EAAI,IAAI,CAAA,sBAAA,EAAyB,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAE,CAAA,CAC9F,CAAC;AACJ;;AClSA;;;;AAIG;AACI,MAAM,mBAAmB,GAAG,IAAI,GAAG,CAAC;IACzC,qBAAqB;IACrB,gCAAgC;IAChC,iCAAiC;IACjC,6BAA6B;IAC7B,8BAA8B;IAC9B,kBAAkB;IAClB,eAAe;IACf,oBAAoB;IACpB,yBAAyB;IACzB,qBAAqB;IACrB,2BAA2B;IAC3B,cAAc;IACd,KAAK;IACL,iBAAiB;IACjB,YAAY;IACZ,kBAAkB;IAClB,qBAAqB;IACrB,iBAAiB;IACjB,YAAY;IACZ,QAAQ;IACR,kBAAkB;IAClB,wBAAwB;IACxB,wBAAwB;IACxB,oBAAoB;IACpB,0BAA0B;IAC1B,0BAA0B;IAC1B,eAAe;IACf,qBAAqB;IACrB,2BAA2B;IAC3B,4BAA4B;IAC5B,qBAAqB;IACrB,cAAc;IACd,uBAAuB;IACvB,yBAAyB;IACzB,qBAAqB;IACrB,oBAAoB;IACpB,oBAAoB;IACpB,mBAAmB;IACnB,yBAAyB;IACzB,yBAAyB;IACzB,qBAAqB;IACrB,2BAA2B;IAC3B,2BAA2B;IAC3B,aAAa;IACb,mBAAmB;IACnB,mBAAmB;IACnB,eAAe;IACf,cAAc;IACd,oBAAoB;IACpB,oBAAoB;IACpB,yBAAyB;IACzB,2BAA2B;IAC3B,YAAY;IACZ,kBAAkB;IAClB,wBAAwB;IACxB,yBAAyB;IACzB,kBAAkB;IAClB,cAAc;IACd,QAAQ;IACR,YAAY;IACZ,aAAa;IACb,MAAM;IACN,WAAW;IACX,OAAO;IACP,cAAc;IACd,YAAY;IACZ,aAAa;IACb,mBAAmB;IACnB,mBAAmB;IACnB,cAAc;IACd,SAAS;IACT,QAAQ;IACR,MAAM;IACN,YAAY;IACZ,WAAW;IACX,aAAa;IACb,MAAM;IACN,WAAW;IACX,kBAAkB;IAClB,cAAc;IACd,yBAAyB;IACzB,aAAa;IACb,KAAK;IACL,iBAAiB;IACjB,UAAU;IACV,cAAc;IACd,uBAAuB;IACvB,oBAAoB;IACpB,QAAQ;IACR,aAAa;IACb,gBAAgB;IAChB,OAAO;IACP,aAAa;IACb,iBAAiB;IACjB,mBAAmB;IACnB,cAAc;IACd,kBAAkB;IAClB,oBAAoB;IACpB,MAAM;IACN,gBAAgB;IAChB,YAAY;IACZ,aAAa;IACb,QAAQ;IACR,kBAAkB;IAClB,oBAAoB;IACpB,eAAe;IACf,mBAAmB;IACnB,qBAAqB;IACrB,aAAa;IACb,cAAc;IACd,YAAY;IACZ,MAAM;IACN,aAAa;IACb,eAAe;IACf,WAAW;IACX,gBAAgB;IAChB,YAAY;IACZ,iBAAiB;IACjB,WAAW;IACX,WAAW;IACX,gBAAgB;IAChB,YAAY;IACZ,iBAAiB;IACjB,WAAW;IACX,iBAAiB;IACjB,QAAQ;IACR,eAAe;IACf,iBAAiB;IACjB,aAAa;IACb,iBAAiB;IACjB,eAAe;IACf,SAAS;IACT,OAAO;IACP,SAAS;IACT,eAAe;IACf,gBAAgB;IAChB,eAAe;IACf,SAAS;IACT,mBAAmB;IACnB,qBAAqB;IACrB,gBAAgB;IAChB,oBAAoB;IACpB,sBAAsB;IACtB,cAAc;IACd,eAAe;IACf,aAAa;IACb,aAAa;IACb,oBAAoB;IACpB,OAAO;IACP,QAAQ;IACR,SAAS;IACT,OAAO;IACP,eAAe;IACf,qBAAqB;IACrB,yBAAyB;IACzB,2BAA2B;IAC3B,sBAAsB;IACtB,sBAAsB;IACtB,0BAA0B;IAC1B,4BAA4B;IAC5B,oBAAoB;IACpB,qBAAqB;IACrB,mBAAmB;IACnB,gBAAgB;IAChB,sBAAsB;IACtB,0BAA0B;IAC1B,4BAA4B;IAC5B,uBAAuB;IACvB,uBAAuB;IACvB,2BAA2B;IAC3B,6BAA6B;IAC7B,qBAAqB;IACrB,sBAAsB;IACtB,oBAAoB;IACpB,wBAAwB;IACxB,yBAAyB;IACzB,iBAAiB;IACjB,uBAAuB;IACvB,cAAc;IACd,eAAe;IACf,UAAU;IACV,iBAAiB;IACjB,uBAAuB;IACvB,2BAA2B;IAC3B,eAAe;IACf,qBAAqB;IACrB,aAAa;IACb,aAAa;IACb,uBAAuB;IACvB,KAAK;IACL,WAAW;IACX,kBAAkB;IAClB,WAAW;IACX,gBAAgB;IAChB,YAAY;IACZ,OAAO;IACP,cAAc;IACd,SAAS;IACT,MAAM;AACP,CAAA,CAAC;;AC/LI,SAAU,mBAAmB,CAAC,OAA0B,EAAA;AAC5D,IAAA,QAAQ,OAAO,CAAC,MAAM;AACpB,QAAA,KAAK,CAAC;YACJ,OAAO,IAAI,mBAAmB,EAAE,CAAC;AACnC,QAAA,KAAK,CAAC;AACJ,YAAA,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;AACpB,QAAA;AACE,YAAA,OAAO,IAAI,qBAAqB,CAAC,OAAO,CAAC,CAAC;KAC7C;AACH,CAAC;AAEe,SAAAC,oBAAkB,CAChC,UAAoC,EACpC,SAA+B,EAC/B,SAA2B,GAAA,IAAI,GAAG,EAAE,EACpC,UAA4B,GAAA,IAAI,GAAG,EAAE,EAAA;IAErC,MAAM,MAAM,GAAY,EAAE,CAAC;IAC3B,MAAM,mBAAmB,GAAyB,EAAE,CAAC;AACrD,IAAA,IAAI,cAAc,GAAG,CAAC,CAAC,CAAC;IACxB,IAAI,gBAAgB,GAAyB,IAAI,CAAC;AAClD,IAAA,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,KAAI;QACvB,MAAM,MAAM,GAAG,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAW,CAAC;AAC1C,QAAA,MAAM,YAAY,GAAG,MAAM,IAAI,cAAc,CAAC;QAC9C,MAAM,kBAAkB,GAAkB,CAAC,YAAY,IAAI,gBAAgB,KAAK,IAAI,GAAG,EAAE,CAAC;QAC1F,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,KAAI;YACvB,IAAI,cAAc,GAAG,IAAI,CAAC;YAC1B,IAAI,eAAe,GAAG,GAAG,CAAC;AAC1B,YAAA,IAAI,IAAI,KAAK,QAAQ,EAAE;gBACrB,cAAc,GAAG,UAAU,CAAC,qBAAqB,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;gBAC1E,QAAQ,eAAe;AACrB,oBAAA,KAAKC,UAAS;AACZ,wBAAA,eAAe,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC;wBACvC,MAAM;AAER,oBAAA,KAAK,UAAU;AACb,wBAAA,eAAe,GAAG,UAAU,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC;wBACxC,MAAM;AAER,oBAAA;AACE,wBAAA,eAAe,GAAG,UAAU,CAAC,mBAAmB,CAC9C,IAAI,EACJ,cAAc,EACd,eAAe,EACf,MAAM,CACP,CAAC;wBACF,MAAM;iBACT;aACF;AACD,YAAA,kBAAkB,CAAC,GAAG,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;AAC1D,SAAC,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,EAAE;AACjB,YAAA,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAC9C;QACD,gBAAgB,GAAG,kBAAkB,CAAC;QACtC,cAAc,GAAG,MAAM,CAAC;AAC1B,KAAC,CAAC,CAAC;AACH,IAAA,IAAI,MAAM,CAAC,MAAM,EAAE;AACjB,QAAA,MAAM,eAAe,CAAC,MAAM,CAAC,CAAC;KAC/B;AAED,IAAA,OAAO,mBAAmB,CAAC;AAC7B,CAAC;AAEK,SAAU,cAAc,CAC5B,MAAuB,EACvB,SAAiB,EACjB,KAAiC,EACjC,QAA6B,EAAA;IAE7B,QAAQ,SAAS;AACf,QAAA,KAAK,OAAO;YACV,MAAM,CAAC,OAAO,CAAC,MAAM,QAAQ,CAAC,KAAK,IAAI,kBAAkB,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;YACpF,MAAM;AACR,QAAA,KAAK,MAAM;YACT,MAAM,CAAC,MAAM,CAAC,MAAM,QAAQ,CAAC,KAAK,IAAI,kBAAkB,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;YAClF,MAAM;AACR,QAAA,KAAK,SAAS;YACZ,MAAM,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC,KAAK,IAAI,kBAAkB,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;YACxF,MAAM;KACT;AACH,CAAC;SAEe,kBAAkB,CAChC,CAAiB,EACjB,SAAiB,EACjB,MAAuB,EAAA;AAEvB,IAAA,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;AACnC,IAAA,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,GAAG,IAAI,GAAG,KAAK,CAAC;AACzD,IAAA,MAAM,KAAK,GAAG,kBAAkB,CAC9B,CAAC,CAAC,OAAO,EACT,CAAC,CAAC,WAAW,EACb,CAAC,CAAC,SAAS,EACX,CAAC,CAAC,OAAO,EACT,SAAS,IAAI,CAAC,CAAC,SAAS,EACxB,SAAS,IAAI,SAAS,GAAG,CAAC,CAAC,SAAS,GAAG,SAAS,EAChD,QAAQ,CACT,CAAC;AACF,IAAA,MAAM,IAAI,GAAI,CAAS,CAAC,OAAO,CAAC,CAAC;AACjC,IAAA,IAAI,IAAI,IAAI,IAAI,EAAE;AACf,QAAA,KAAa,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;KAChC;AACD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;SAEe,kBAAkB,CAChC,OAAY,EACZ,WAAmB,EACnB,SAAiB,EACjB,OAAe,EACf,SAAoB,GAAA,EAAE,EACtB,SAAoB,GAAA,CAAC,EACrB,QAAkB,EAAA;AAElB,IAAA,OAAO,EAAC,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,CAAC,QAAQ,EAAC,CAAC;AAChG,CAAC;SAEe,oBAAoB,CAAO,GAAc,EAAE,GAAM,EAAE,YAAe,EAAA;IAChF,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACzB,IAAI,CAAC,KAAK,EAAE;QACV,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,GAAG,YAAY,EAAE,CAAC;KACtC;AACD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAEK,SAAU,oBAAoB,CAAC,OAAe,EAAA;IAClD,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC1C,MAAM,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;IAC9C,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;AAC/C,IAAA,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;AACtB,CAAC;AAED,MAAM,eAAe,mBAAuC,CAAC,MAC3D,OAAO,QAAQ,KAAK,WAAW,GAAG,IAAI,GAAG,QAAQ,CAAC,eAAe,GAAG,CAAC;AAEjE,SAAU,gBAAgB,CAAC,OAAY,EAAA;AAC3C,IAAA,MAAM,MAAM,GAAG,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC;AAC1D,IAAA,IAAI,MAAM,KAAK,eAAe,EAAE;AAC9B,QAAA,OAAO,IAAI,CAAC;KACb;AACD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,oBAAoB,CAAC,IAAY,EAAA;;;AAGxC,IAAA,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,OAAO,CAAC;AACzC,CAAC;AAED,IAAI,YAAY,GAAwB,IAAI,CAAC;AAC7C,IAAI,UAAU,GAAG,KAAK,CAAC;AACjB,SAAU,qBAAqB,CAAC,IAAY,EAAA;IAChD,IAAI,CAAC,YAAY,EAAE;AACjB,QAAA,YAAY,GAAG,WAAW,EAAE,IAAI,EAAE,CAAC;AACnC,QAAA,UAAU,GAAG,YAAa,CAAC,KAAK,GAAG,kBAAkB,IAAI,YAAa,CAAC,KAAK,GAAG,KAAK,CAAC;KACtF;IAED,IAAI,MAAM,GAAG,IAAI,CAAC;IAClB,IAAI,YAAa,CAAC,KAAK,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE;AACtD,QAAA,MAAM,GAAG,IAAI,IAAI,YAAa,CAAC,KAAK,CAAC;AACrC,QAAA,IAAI,CAAC,MAAM,IAAI,UAAU,EAAE;YACzB,MAAM,SAAS,GAAG,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC1E,YAAA,MAAM,GAAG,SAAS,IAAI,YAAa,CAAC,KAAK,CAAC;SAC3C;KACF;AAED,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAEK,SAAU,kCAAkC,CAAC,IAAY,EAAA;AAC7D,IAAA,OAAO,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACvC,CAAC;SAEe,WAAW,GAAA;AACzB,IAAA,IAAI,OAAO,QAAQ,IAAI,WAAW,EAAE;QAClC,OAAO,QAAQ,CAAC,IAAI,CAAC;KACtB;AACD,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAEe,SAAA,eAAe,CAAC,IAAS,EAAE,IAAS,EAAA;IAClD,OAAO,IAAI,EAAE;AACX,QAAA,IAAI,IAAI,KAAK,IAAI,EAAE;AACjB,YAAA,OAAO,IAAI,CAAC;SACb;AACD,QAAA,IAAI,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;KAC/B;AACD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;SAEe,WAAW,CAAC,OAAY,EAAE,QAAgB,EAAE,KAAc,EAAA;IACxE,IAAI,KAAK,EAAE;QACT,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC;KACvD;IACD,MAAM,IAAI,GAAG,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAC7C,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;AAC5B,CAAC;AAEK,SAAU,iBAAiB,CAAC,QAAuB,EAAA;AACvD,IAAA,MAAM,MAAM,GAAkB,IAAI,GAAG,EAAE,CAAC;IACxC,QAAQ,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,KAAI;QAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;AACzD,QAAA,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAC3B,KAAC,CAAC,CAAC;AACH,IAAA,OAAO,MAAM,CAAC;AAChB;;ACxNA;;;;AAIG;MAEU,mBAAmB,CAAA;AAC9B;;AAEG;AACH,IAAA,qBAAqB,CAAC,IAAY,EAAA;AAChC,QAAA,OAAO,qBAAqB,CAAC,IAAI,CAAC,CAAC;KACpC;AAED;;AAEG;IACH,cAAc,CAAC,QAAa,EAAE,SAAiB,EAAA;;AAE7C,QAAA,OAAO,KAAK,CAAC;KACd;AAED;;;AAGG;IACH,eAAe,CAAC,IAAS,EAAE,IAAS,EAAA;AAClC,QAAA,OAAO,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;KACpC;AAED;;AAEG;AACH,IAAA,gBAAgB,CAAC,OAAgB,EAAA;AAC/B,QAAA,OAAO,gBAAgB,CAAC,OAAO,CAAC,CAAC;KAClC;AAED;;;AAGG;AACH,IAAA,KAAK,CAAC,OAAY,EAAE,QAAgB,EAAE,KAAc,EAAA;QAClD,OAAO,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;KAC9C;AAED;;AAEG;AACH,IAAA,YAAY,CAAC,OAAY,EAAE,IAAY,EAAE,YAAqB,EAAA;QAC5D,OAAO,YAAY,IAAI,EAAE,CAAC;KAC3B;AAED;;AAEG;AACH,IAAA,OAAO,CACL,OAAY,EACZ,SAA8C,EAC9C,QAAgB,EAChB,KAAa,EACb,MAAc,EACd,eAAyB,GAAA,EAAE,EAC3B,uBAAiC,EAAA;AAEjC,QAAA,OAAO,IAAI,mBAAmB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;KACjD;yHA3DU,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAAnB,mBAAmB,EAAA,CAAA,CAAA,EAAA;;sGAAnB,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAD/B,UAAU;;AA+DX;;AAEG;MACmB,eAAe,CAAA;AACnC;;AAEG;AACI,IAAA,SAAA,IAAA,CAAA,IAAI,GAAoC,IAAI,mBAAmB,EAAE,CAAC,EAAA;;;MC/ErD,wBAAwB,CAAA;AAQ7C,CAAA;MAEY,4BAA4B,CAAA;IACvC,qBAAqB,CAAC,YAAoB,EAAE,MAAe,EAAA;AACzD,QAAA,OAAO,YAAY,CAAC;KACrB;AAED,IAAA,mBAAmB,CACjB,oBAA4B,EAC5B,kBAA0B,EAC1B,KAAsB,EACtB,MAAe,EAAA;AAEf,QAAA,OAAY,KAAK,CAAC;KACnB;AACF;;ACHD,MAAM,UAAU,GAAG,IAAI,CAAC;AAEjB,MAAM,uBAAuB,GAAG,IAAI,CAAC;AACrC,MAAM,qBAAqB,GAAG,IAAI,CAAC;AACnC,MAAM,eAAe,GAAG,UAAU,CAAC;AACnC,MAAM,eAAe,GAAG,UAAU,CAAC;AACnC,MAAM,oBAAoB,GAAG,YAAY,CAAC;AAC1C,MAAM,mBAAmB,GAAG,aAAa,CAAC;AAC1C,MAAM,sBAAsB,GAAG,cAAc,CAAC;AAC9C,MAAM,qBAAqB,GAAG,eAAe,CAAC;AAE/C,SAAU,kBAAkB,CAAC,KAAsB,EAAA;IACvD,IAAI,OAAO,KAAK,IAAI,QAAQ;AAAE,QAAA,OAAO,KAAK,CAAC;IAE3C,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;AACjD,IAAA,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC;AAAE,QAAA,OAAO,CAAC,CAAC;AAE7C,IAAA,OAAO,qBAAqB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AACnE,CAAC;AAED,SAAS,qBAAqB,CAAC,KAAa,EAAE,IAAY,EAAA;IACxD,QAAQ,IAAI;AACV,QAAA,KAAK,GAAG;YACN,OAAO,KAAK,GAAG,UAAU,CAAC;AAC5B,QAAA;AACE,YAAA,OAAO,KAAK,CAAC;KAChB;AACH,CAAC;SAEe,aAAa,CAC3B,OAAyC,EACzC,MAAe,EACf,mBAA6B,EAAA;AAE7B,IAAA,OAAO,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC;AACvC,UAAkB,OAAO;UACvB,mBAAmB,CAAkB,OAAO,EAAE,MAAM,EAAE,mBAAmB,CAAC,CAAC;AACjF,CAAC;AAED,SAAS,mBAAmB,CAC1B,GAAoB,EACpB,MAAe,EACf,mBAA6B,EAAA;IAE7B,MAAM,KAAK,GAAG,0EAA0E,CAAC;AACzF,IAAA,IAAI,QAAgB,CAAC;IACrB,IAAI,KAAK,GAAW,CAAC,CAAC;IACtB,IAAI,MAAM,GAAW,EAAE,CAAC;AACxB,IAAA,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QAC3B,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACjC,QAAA,IAAI,OAAO,KAAK,IAAI,EAAE;YACpB,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC;AACrC,YAAA,OAAO,EAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAC,CAAC;SAC5C;AAED,QAAA,QAAQ,GAAG,qBAAqB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AAErE,QAAA,MAAM,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AAC9B,QAAA,IAAI,UAAU,IAAI,IAAI,EAAE;AACtB,YAAA,KAAK,GAAG,qBAAqB,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;SACnE;AAED,QAAA,MAAM,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QAC7B,IAAI,SAAS,EAAE;YACb,MAAM,GAAG,SAAS,CAAC;SACpB;KACF;SAAM;QACL,QAAQ,GAAG,GAAG,CAAC;KAChB;IAED,IAAI,CAAC,mBAAmB,EAAE;QACxB,IAAI,cAAc,GAAG,KAAK,CAAC;AAC3B,QAAA,IAAI,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC;AAC/B,QAAA,IAAI,QAAQ,GAAG,CAAC,EAAE;AAChB,YAAA,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;YACjC,cAAc,GAAG,IAAI,CAAC;SACvB;AACD,QAAA,IAAI,KAAK,GAAG,CAAC,EAAE;AACb,YAAA,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;YAClC,cAAc,GAAG,IAAI,CAAC;SACvB;QACD,IAAI,cAAc,EAAE;AAClB,YAAA,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC;SACvD;KACF;AAED,IAAA,OAAO,EAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAC,CAAC;AACnC,CAAC;AAEK,SAAU,kBAAkB,CAChC,SAAmD,EAAA;AAEnD,IAAA,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;AACrB,QAAA,OAAO,EAAE,CAAC;KACX;AACD,IAAA,IAAI,SAAS,CAAC,CAAC,CAAC,YAAY,GAAG,EAAE;AAC/B,QAAA,OAAO,SAAiC,CAAC;KAC1C;IACD,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,IAAI,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC;AAEK,SAAU,eAAe,CAAC,MAA4C,EAAA;IAC1E,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC;AACtE,CAAC;SAEe,SAAS,CAAC,OAAY,EAAE,MAAqB,EAAE,YAA4B,EAAA;IACzF,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,KAAI;AAC3B,QAAA,MAAM,SAAS,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC;QAC5C,IAAI,YAAY,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAC3C,YAAA,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;SAClD;AACD,QAAA,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC;AACjC,KAAC,CAAC,CAAC;AACL,CAAC;AAEe,SAAA,WAAW,CAAC,OAAY,EAAE,MAAqB,EAAA;IAC7D,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,KAAI;AACzB,QAAA,MAAM,SAAS,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC;AAC5C,QAAA,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;AAChC,KAAC,CAAC,CAAC;AACL,CAAC;AAEK,SAAU,uBAAuB,CACrC,KAA8C,EAAA;AAE9C,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACxB,QAAA,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC;AAAE,YAAA,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;AACvC,QAAA,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;KACxB;AACD,IAAA,OAAO,KAA0B,CAAC;AACpC,CAAC;SAEe,mBAAmB,CACjC,KAAyC,EACzC,OAAyB,EACzB,MAAe,EAAA;AAEf,IAAA,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC;AACpC,IAAA,MAAM,OAAO,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;AAC1C,IAAA,IAAI,OAAO,CAAC,MAAM,EAAE;AAClB,QAAA,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,KAAI;YAC1B,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;gBACnC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC;aAC1C;AACH,SAAC,CAAC,CAAC;KACJ;AACH,CAAC;AAED,MAAM,WAAW,GAAG,IAAI,MAAM,CAC5B,CAAA,EAAG,uBAAuB,CAAA,aAAA,EAAgB,qBAAqB,CAAA,CAAE,EACjE,GAAG,CACJ,CAAC;AACI,SAAU,kBAAkB,CAAC,KAAyC,EAAA;IAC1E,IAAI,MAAM,GAAa,EAAE,CAAC;AAC1B,IAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,QAAA,IAAI,KAAU,CAAC;QACf,QAAQ,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG;YACxC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAW,CAAC,CAAC;SACjC;AACD,QAAA,WAAW,CAAC,SAAS,GAAG,CAAC,CAAC;KAC3B;AACD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;SAEe,iBAAiB,CAC/B,KAAsB,EACtB,MAA6B,EAC7B,MAAe,EAAA;AAEf,IAAA,MAAM,QAAQ,GAAG,CAAG,EAAA,KAAK,EAAE,CAAC;AAC5B,IAAA,MAAM,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,OAAO,KAAI;AACvD,QAAA,IAAI,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;;AAE/B,QAAA,IAAI,QAAQ,IAAI,IAAI,EAAE;YACpB,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC;YACxC,QAAQ,GAAG,EAAE,CAAC;SACf;AACD,QAAA,OAAO,QAAQ,CAAC,QAAQ,EAAE,CAAC;AAC7B,KAAC,CAAC,CAAC;;IAGH,OAAO,GAAG,IAAI,QAAQ,GAAG,KAAK,GAAG,GAAG,CAAC;AACvC,CAAC;AAED,MAAM,gBAAgB,GAAG,eAAe,CAAC;AACnC,SAAU,mBAAmB,CAAC,KAAa,EAAA;IAC/C,OAAO,KAAK,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC,GAAG,CAAQ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;AAC9E,CAAC;AAEK,SAAU,mBAAmB,CAAC,KAAa,EAAA;IAC/C,OAAO,KAAK,CAAC,OAAO,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;AACjE,CAAC;AAEe,SAAA,8BAA8B,CAAC,QAAgB,EAAE,KAAa,EAAA;AAC5E,IAAA,OAAO,QAAQ,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC;AACvC,CAAC;SAEe,kCAAkC,CAChD,OAAY,EACZ,SAA+B,EAC/B,cAA6B,EAAA;IAE7B,IAAI,cAAc,CAAC,IAAI,IAAI,SAAS,CAAC,MAAM,EAAE;AAC3C,QAAA,IAAI,gBAAgB,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,iBAAiB,GAAa,EAAE,CAAC;QACrC,cAAc,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,KAAI;YACnC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAC/B,gBAAA,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAC9B;AACD,YAAA,gBAAgB,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAClC,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,iBAAiB,CAAC,MAAM,EAAE;AAC5B,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACzC,gBAAA,IAAI,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;gBACtB,iBAAiB,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;aAChF;SACF;KACF;AACD,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;SAYe,YAAY,CAAC,OAAY,EAAE,IAAS,EAAE,OAAY,EAAA;AAChE,IAAA,QAAQ,IAAI,CAAC,IAAI;QACf,KAAK,qBAAqB,CAAC,OAAO;YAChC,OAAO,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC7C,KAAK,qBAAqB,CAAC,KAAK;YAC9B,OAAO,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC3C,KAAK,qBAAqB,CAAC,UAAU;YACnC,OAAO,OAAO,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAChD,KAAK,qBAAqB,CAAC,QAAQ;YACjC,OAAO,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC9C,KAAK,qBAAqB,CAAC,KAAK;YAC9B,OAAO,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC3C,KAAK,qBAAqB,CAAC,OAAO;YAChC,OAAO,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC7C,KAAK,qBAAqB,CAAC,SAAS;YAClC,OAAO,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC/C,KAAK,qBAAqB,CAAC,KAAK;YAC9B,OAAO,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC3C,KAAK,qBAAqB,CAAC,SAAS;YAClC,OAAO,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC/C,KAAK,qBAAqB,CAAC,YAAY;YACrC,OAAO,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAClD,KAAK,qBAAqB,CAAC,UAAU;YACnC,OAAO,OAAO,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAChD,KAAK,qBAAqB,CAAC,KAAK;YAC9B,OAAO,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC3C,KAAK,qBAAqB,CAAC,OAAO;YAChC,OAAO,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC7C,QAAA;AACE,YAAA,MAAM,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACpC;AACH,CAAC;AAEe,SAAA,YAAY,CAAC,OAAY,EAAE,IAAY,EAAA;IACrD,OAAa,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAE,CAAC,IAAI,CAAC,CAAC;AACvD;;AC3RA,MAAM,oBAAoB,GAAG,IAAI,GAAG,CAAC;IACnC,OAAO;IACP,QAAQ;IACR,UAAU;IACV,WAAW;IACX,UAAU;IACV,WAAW;IACX,MAAM;IACN,KAAK;IACL,QAAQ;IACR,OAAO;IACP,UAAU;IACV,cAAc;IACd,eAAe;IACf,YAAY;IACZ,aAAa;IACb,eAAe;IACf,cAAc;IACd,WAAW;IACX,YAAY;IACZ,cAAc;IACd,aAAa;IACb,cAAc;IACd,aAAa;IACb,gBAAgB;IAChB,iBAAiB;IACjB,kBAAkB;IAClB,mBAAmB;IACnB,YAAY;IACZ,aAAa;AACd,CAAA,CAAC,CAAC;AAEG,MAAO,4BAA6B,SAAQ,wBAAwB,CAAA;IAC/D,qBAAqB,CAAC,YAAoB,EAAE,MAAe,EAAA;AAClE,QAAA,OAAO,mBAAmB,CAAC,YAAY,CAAC,CAAC;KAC1C;AAEQ,IAAA,mBAAmB,CAC1B,oBAA4B,EAC5B,kBAA0B,EAC1B,KAAsB,EACtB,MAAe,EAAA;QAEf,IAAI,IAAI,GAAW,EAAE,CAAC;QACtB,MAAM,MAAM,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC;AAEvC,QAAA,IAAI,oBAAoB,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,GAAG,EAAE;AAChF,YAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC7B,IAAI,GAAG,IAAI,CAAC;aACb;iBAAM;gBACL,MAAM,iBAAiB,GAAG,KAAK,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;gBAChE,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,EAAE;oBACzD,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC,CAAC;iBAC/D;aACF;SACF;QACD,OAAO,MAAM,GAAG,IAAI,CAAC;KACtB;AACF;;AC9DD,SAAS,oBAAoB,CAAC,QAAkB,EAAA;IAC9C,MAAM,UAAU,GAAG,OAAO,CAAC;IAC3B,OAAO,CAAA,EAAG,UAAU,CAAA,EAAG,QAAQ;SAC5B,MAAM,CAAC,OAAO,CAAC;AACf,SAAA,GAAG,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC;AACzB,SAAA,IAAI,CAAC,UAAU,CAAC,CAAA,CAAE,CAAC;AACxB,CAAC;AAEK,SAAU,cAAc,CAAC,QAAkB,EAAA;AAC/C,IAAA,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;QAC5C,OAAO,CAAC,IAAI,CAAC,CAAiC,8BAAA,EAAA,oBAAoB,CAAC,QAAQ,CAAC,CAAE,CAAA,CAAC,CAAC;AACpF,CAAC;AAEe,SAAA,gBAAgB,CAAC,IAAY,EAAE,QAAkB,EAAA;AAC/D,IAAA,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AAC5C,QAAA,OAAO,CAAC,IAAI,CACV,CAAA,uBAAA,EAA0B,IAAI,CAAA,wCAAA,EAA2C,oBAAoB,CAC3F,QAAQ,CACT,CAAE,CAAA,CACJ,CAAC;AACN,CAAC;AAEK,SAAU,YAAY,CAAC,QAAkB,EAAA;AAC7C,IAAA,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;QAC5C,OAAO,CAAC,IAAI,CAAC,CAA+C,4CAAA,EAAA,oBAAoB,CAAC,QAAQ,CAAC,CAAE,CAAA,CAAC,CAAC;AAClG,CAAC;AAEe,SAAA,sBAAsB,CAAC,IAAY,EAAE,QAAkB,EAAA;AACrE,IAAA,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AAC5C,QAAA,OAAO,CAAC,IAAI,CACV,CAAA,0BAAA,EAA6B,IAAI,CAAA,yCAAA,EAA4C,oBAAoB,CAC/F,QAAQ,CACT,CAAE,CAAA,CACJ,CAAC;AACN,CAAC;AAEe,SAAA,iCAAiC,CAAC,QAAkB,EAAE,KAAe,EAAA;AACnF,IAAA,IAAI,KAAK,CAAC,MAAM,EAAE;AAChB,QAAA,QAAQ,CAAC,IAAI,CAAC,CAAA,sDAAA,EAAyD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE,CAAA,CAAC,CAAC;KAC5F;AACH;;ACtCO,MAAM,SAAS,GAAG,GAAG,CAAC;AAQb,SAAA,mBAAmB,CACjC,eAA6C,EAC7C,MAAe,EAAA;IAEf,MAAM,WAAW,GAA0B,EAAE,CAAC;AAC9C,IAAA,IAAI,OAAO,eAAe,IAAI,QAAQ,EAAE;QACtC,eAAe;aACZ,KAAK,CAAC,SAAS,CAAC;AAChB,aAAA,OAAO,CAAC,CAAC,GAAG,KAAK,uBAAuB,CAAC,GAAG,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC;KACxE;SAAM;AACL,QAAA,WAAW,CAAC,IAAI,CAAsB,eAAe,CAAC,CAAC;KACxD;AACD,IAAA,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,SAAS,uBAAuB,CAC9B,QAAgB,EAChB,WAAkC,EAClC,MAAe,EAAA;AAEf,IAAA,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;QACtB,MAAM,MAAM,GAAG,mBAAmB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AACrD,QAAA,IAAI,OAAO,MAAM,IAAI,UAAU,EAAE;AAC/B,YAAA,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACzB,OAAO;SACR;QACD,QAAQ,GAAG,MAAM,CAAC;KACnB;IAED,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;IACxE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;QACrC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC;AACzC,QAAA,OAAO,WAAW,CAAC;KACpB;AAED,IAAA,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAC3B,IAAA,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAC3B,IAAA,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACzB,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;IAE3D,MAAM,kBAAkB,GAAG,SAAS,IAAI,SAAS,IAAI,OAAO,IAAI,SAAS,CAAC;IAC1E,IAAI,SAAS,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,EAAE;QAC9C,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC;KAC5D;IACD,OAAO;AACT,CAAC;AAED,SAAS,mBAAmB,CAAC,KAAa,EAAE,MAAe,EAAA;IACzD,QAAQ,KAAK;AACX,QAAA,KAAK,QAAQ;AACX,YAAA,OAAO,WAAW,CAAC;AACrB,QAAA,KAAK,QAAQ;AACX,YAAA,OAAO,WAAW,CAAC;AACrB,QAAA,KAAK,YAAY;AACf,YAAA,OAAO,CAAC,SAAc,EAAE,OAAY,KAAc,UAAU,CAAC,OAAO,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;AAChG,QAAA,KAAK,YAAY;AACf,YAAA,OAAO,CAAC,SAAc,EAAE,OAAY,KAAc,UAAU,CAAC,OAAO,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;AAChG,QAAA;YACE,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC,CAAC;AAC3C,YAAA,OAAO,QAAQ,CAAC;KACnB;AACH,CAAC;AAED;AACA;AACA;AACA;AACA,MAAM,mBAAmB,GAAG,IAAI,GAAG,CAAS,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;AAC3D,MAAM,oBAAoB,GAAG,IAAI,GAAG,CAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;AAE7D,SAAS,oBAAoB,CAAC,GAAW,EAAE,GAAW,EAAA;AACpD,IAAA,MAAM,iBAAiB,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxF,IAAA,MAAM,iBAAiB,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAExF,IAAA,OAAO,CAAC,SAAc,EAAE,OAAY,KAAa;QAC/C,IAAI,QAAQ,GAAG,GAAG,IAAI,SAAS,IAAI,GAAG,IAAI,SAAS,CAAC;QACpD,IAAI,QAAQ,GAAG,GAAG,IAAI,SAAS,IAAI,GAAG,IAAI,OAAO,CAAC;QAElD,IAAI,CAAC,QAAQ,IAAI,iBAAiB,IAAI,OAAO,SAAS,KAAK,SAAS,EAAE;YACpE,QAAQ,GAAG,SAAS,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SACrF;QACD,IAAI,CAAC,QAAQ,IAAI,iBAAiB,IAAI,OAAO,OAAO,KAAK,SAAS,EAAE;YAClE,QAAQ,GAAG,OAAO,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SACnF;QAED,OAAO,QAAQ,IAAI,QAAQ,CAAC;AAC9B,KAAC,CAAC;AACJ;;AC1BA,MAAM,UAAU,GAAG,OAAO,CAAC;AAC3B,MAAM,gBAAgB,GAAG,IAAI,MAAM,CAAC,CAAK,EAAA,EAAA,UAAU,CAAM,IAAA,CAAA,EAAE,GAAG,CAAC,CAAC;AAEhE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCG;AACG,SAAU,iBAAiB,CAC/B,MAAuB,EACvB,QAAiD,EACjD,MAAe,EACf,QAAkB,EAAA;AAElB,IAAA,OAAO,IAAI,0BAA0B,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;AAClF,CAAC;AAED,MAAM,aAAa,GAAG,EAAE,CAAC;MAEZ,0BAA0B,CAAA;AACrC,IAAA,WAAA,CAAoB,OAAwB,EAAA;QAAxB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAiB;KAAI;AAEhD,IAAA,KAAK,CACH,QAAiD,EACjD,MAAe,EACf,QAAkB,EAAA;AAElB,QAAA,MAAM,OAAO,GAAG,IAAI,0BAA0B,CAAC,MAAM,CAAC,CAAC;AACvD,QAAA,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,CAAC;AAC5C,QAAA,MAAM,GAAG,IACP,YAAY,CAAC,IAAI,EAAE,uBAAuB,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,CAC/D,CAAC;AAEF,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,IAAI,OAAO,CAAC,6BAA6B,CAAC,IAAI,EAAE;gBAC9C,iCAAiC,CAAC,QAAQ,EAAE;AAC1C,oBAAA,GAAG,OAAO,CAAC,6BAA6B,CAAC,IAAI,EAAE;AAChD,iBAAA,CAAC,CAAC;aACJ;SACF;AAED,QAAA,OAAO,GAAG,CAAC;KACZ;AAEO,IAAA,6BAA6B,CAAC,OAAmC,EAAA;AACvE,QAAA,OAAO,CAAC,oBAAoB,GAAG,aAAa,CAAC;AAC7C,QAAA,OAAO,CAAC,eAAe,GAAG,IAAI,GAAG,EAAuC,CAAC;QACzE,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;AACtD,QAAA,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC;KACzB;IAED,YAAY,CACV,QAAkC,EAClC,OAAmC,EAAA;QAEnC,IAAI,UAAU,IAAI,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;QAC1C,IAAI,QAAQ,IAAI,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;QACtC,MAAM,MAAM,GAAe,EAAE,CAAC;QAC9B,MAAM,WAAW,GAAoB,EAAE,CAAC;QACxC,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;YAClC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;SACvC;QAED,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,KAAI;AACnC,YAAA,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,CAAC;YAC5C,IAAI,GAAG,CAAC,IAAI,IAAI,qBAAqB,CAAC,KAAK,EAAE;gBAC3C,MAAM,QAAQ,GAAG,GAA6B,CAAC;AAC/C,gBAAA,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;gBAC3B,IAAI;AACD,qBAAA,QAAQ,EAAE;qBACV,KAAK,CAAC,SAAS,CAAC;AAChB,qBAAA,OAAO,CAAC,CAAC,CAAC,KAAI;AACb,oBAAA,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC;AAClB,oBAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;AAClD,iBAAC,CAAC,CAAC;AACL,gBAAA,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;aACtB;iBAAM,IAAI,GAAG,CAAC,IAAI,IAAI,qBAAqB,CAAC,UAAU,EAAE;gBACvD,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,GAAkC,EAAE,OAAO,CAAC,CAAC;AACrF,gBAAA,UAAU,IAAI,UAAU,CAAC,UAAU,CAAC;AACpC,gBAAA,QAAQ,IAAI,UAAU,CAAC,QAAQ,CAAC;AAChC,gBAAA,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAC9B;iBAAM;gBACL,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;aAC1C;AACH,SAAC,CAAC,CAAC;QAEH,OAAO;YACL,IAAI,EAAE,qBAAqB,CAAC,OAAO;YACnC,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,MAAM;YACN,WAAW;YACX,UAAU;YACV,QAAQ;AACR,YAAA,OAAO,EAAE,IAAI;SACd,CAAC;KACH;IAED,UAAU,CAAC,QAAgC,EAAE,OAAmC,EAAA;AAC9E,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AAC3D,QAAA,MAAM,SAAS,GAAG,CAAC,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC;AACxE,QAAA,IAAI,QAAQ,CAAC,qBAAqB,EAAE;AAClC,YAAA,MAAM,WAAW,GAAG,IAAI,GAAG,EAAU,CAAC;AACtC,YAAA,MAAM,MAAM,GAAG,SAAS,IAAI,EAAE,CAAC;YAC/B,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;AAChC,gBAAA,IAAI,KAAK,YAAY,GAAG,EAAE;AACxB,oBAAA,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;wBACtB,kBAAkB,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAI;4BACxC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;AAC/B,gCAAA,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;6BACtB;AACH,yBAAC,CAAC,CAAC;AACL,qBAAC,CAAC,CAAC;iBACJ;AACH,aAAC,CAAC,CAAC;AACH,YAAA,IAAI,WAAW,CAAC,IAAI,EAAE;gBACpB,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;aAC7E;SACF;QAED,OAAO;YACL,IAAI,EAAE,qBAAqB,CAAC,KAAK;YACjC,IAAI,EAAE,QAAQ,CAAC,IAAI;AACnB,YAAA,KAAK,EAAE,QAAQ;AACf,YAAA,OAAO,EAAE,SAAS,GAAG,EAAC,MAAM,EAAE,SAAS,EAAC,GAAG,IAAI;SAChD,CAAC;KACH;IAED,eAAe,CACb,QAAqC,EACrC,OAAmC,EAAA;AAEnC,QAAA,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC;AACvB,QAAA,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;AACrB,QAAA,MAAM,SAAS,GAAG,YAAY,CAAC,IAAI,EAAE,uBAAuB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,CAAC;AAC3F,QAAA,MAAM,QAAQ,GAAG,mBAAmB,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;QAEpE,OAAO;YACL,IAAI,EAAE,qBAAqB,CAAC,UAAU;YACtC,QAAQ;YACR,SAAS;YACT,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,QAAQ,EAAE,OAAO,CAAC,QAAQ;AAC1B,YAAA,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;SACrD,CAAC;KACH;IAED,aAAa,CACX,QAAmC,EACnC,OAAmC,EAAA;QAEnC,OAAO;YACL,IAAI,EAAE,qBAAqB,CAAC,QAAQ;YACpC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,YAAY,CAAC,IAAI,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;AAChE,YAAA,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;SACrD,CAAC;KACH;IAED,UAAU,CAAC,QAAgC,EAAE,OAAmC,EAAA;AAC9E,QAAA,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACxC,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAI;AACxC,YAAA,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC;YAClC,MAAM,QAAQ,GAAG,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;YACnD,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;AAC3D,YAAA,OAAO,QAAQ,CAAC;AAClB,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,CAAC,WAAW,GAAG,YAAY,CAAC;QACnC,OAAO;YACL,IAAI,EAAE,qBAAqB,CAAC,KAAK;YACjC,KAAK;AACL,YAAA,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;SACrD,CAAC;KACH;IAED,YAAY,CACV,QAAkC,EAClC,OAAmC,EAAA;AAEnC,QAAA,MAAM,SAAS,GAAG,kBAAkB,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;AACvE,QAAA,OAAO,CAAC,qBAAqB,GAAG,SAAS,CAAC;AAC1C,QAAA,IAAI,QAAiC,CAAC;AACtC,QAAA,IAAI,aAAa,GAAgE,QAAQ,CAAC,MAAM;cAC5F,QAAQ,CAAC,MAAM;AACjB,cAAE,KAAK,CAAC,EAAE,CAAC,CAAC;QACd,IAAI,aAAa,CAAC,IAAI,IAAI,qBAAqB,CAAC,SAAS,EAAE;YACzD,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,aAAmD,EAAE,OAAO,CAAC,CAAC;SAC9F;aAAM;AACL,YAAA,IAAI,aAAa,GAAG,QAAQ,CAAC,MAAgC,CAAC;YAC9D,IAAI,OAAO,GAAG,KAAK,CAAC;YACpB,IAAI,CAAC,aAAa,EAAE;gBAClB,OAAO,GAAG,IAAI,CAAC;gBACf,MAAM,YAAY,GAAsC,EAAE,CAAC;AAC3D,gBAAA,IAAI,SAAS,CAAC,MAAM,EAAE;AACpB,oBAAA,YAAY,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC;iBAC3C;AACD,gBAAA,aAAa,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC;aACrC;YACD,OAAO,CAAC,WAAW,IAAI,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC;YAC5D,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;AAC1D,YAAA,SAAS,CAAC,WAAW,GAAG,OAAO,CAAC;YAChC,QAAQ,GAAG,SAAS,CAAC;SACtB;AAED,QAAA,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC;QACrC,OAAO;YACL,IAAI,EAAE,qBAAqB,CAAC,OAAO;AACnC,YAAA,OAAO,EAAE,SAAS;AAClB,YAAA,KAAK,EAAE,QAAQ;AACf,YAAA,OAAO,EAAE,IAAI;SACd,CAAC;KACH;IAED,UAAU,CAAC,QAAgC,EAAE,OAAmC,EAAA;QAC9E,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AAClD,QAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AACrC,QAAA,OAAO,GAAG,CAAC;KACZ;IAEO,aAAa,CACnB,QAAgC,EAChC,OAAmC,EAAA;QAEnC,MAAM,MAAM,GAAkC,EAAE,CAAC;QACjD,MAAM,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAE5F,QAAA,KAAK,IAAI,UAAU,IAAI,cAAc,EAAE;AACrC,YAAA,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;AAClC,gBAAA,IAAI,UAAU,KAAK,UAAU,EAAE;AAC7B,oBAAA,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;iBACzB;qBAAM;oBACL,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,CAAC;iBACpD;aACF;iBAAM;AACL,gBAAA,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;aAClD;SACF;QAED,IAAI,qBAAqB,GAAG,KAAK,CAAC;QAClC,IAAI,eAAe,GAAkB,IAAI,CAAC;AAC1C,QAAA,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,KAAI;AAC3B,YAAA,IAAI,SAAS,YAAY,GAAG,EAAE;AAC5B,gBAAA,IAAI,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;AAC3B,oBAAA,eAAe,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAW,CAAC;AACpD,oBAAA,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;iBAC5B;gBACD,IAAI,CAAC,qBAAqB,EAAE;oBAC1B,KAAK,IAAI,KAAK,IAAI,SAAS,CAAC,MAAM,EAAE,EAAE;AACpC,wBAAA,IAAI,KAAM,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAAE;4BAC3D,qBAAqB,GAAG,IAAI,CAAC;4BAC7B,MAAM;yBACP;qBACF;iBACF;aACF;AACH,SAAC,CAAC,CAAC;QAEH,OAAO;YACL,IAAI,EAAE,qBAAqB,CAAC,KAAK;YACjC,MAAM;AACN,YAAA,MAAM,EAAE,eAAe;YACvB,MAAM,EAAE,QAAQ,CAAC,MAAM;YACvB,qBAAqB;AACrB,YAAA,OAAO,EAAE,IAAI;SACd,CAAC;KACH;IAEO,iBAAiB,CAAC,GAAa,EAAE,OAAmC,EAAA;AAC1E,QAAA,MAAM,OAAO,GAAG,OAAO,CAAC,qBAAqB,CAAC;AAC9C,QAAA,IAAI,OAAO,GAAG,OAAO,CAAC,WAAW,CAAC;AAClC,QAAA,IAAI,SAAS,GAAG,OAAO,CAAC,WAAW,CAAC;AACpC,QAAA,IAAI,OAAO,IAAI,SAAS,GAAG,CAAC,EAAE;YAC5B,SAAS,IAAI,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC;SAC/C;QAED,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;YAC3B,IAAI,OAAO,KAAK,KAAK,QAAQ;gBAAE,OAAO;YAEtC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,KAAI;AAC5B,gBAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;oBACjD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE;AAC7C,wBAAA,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACnB,wBAAA,OAAO,CAAC,6BAA6B,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;wBAChD,OAAO;qBACR;iBACF;;;AAID,gBAAA,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,oBAAqB,CAAE,CAAC;gBACpF,MAAM,cAAc,GAAG,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACjD,IAAI,oBAAoB,GAAG,IAAI,CAAC;gBAChC,IAAI,cAAc,EAAE;oBAClB,IACE,SAAS,IAAI,OAAO;wBACpB,SAAS,IAAI,cAAc,CAAC,SAAS;AACrC,wBAAA,OAAO,IAAI,cAAc,CAAC,OAAO,EACjC;wBACA,OAAO,CAAC,MAAM,CAAC,IAAI,CACjB,wBAAwB,CACtB,IAAI,EACJ,cAAc,CAAC,SAAS,EACxB,cAAc,CAAC,OAAO,EACtB,SAAS,EACT,OAAO,CACR,CACF,CAAC;wBACF,oBAAoB,GAAG,KAAK,CAAC;qBAC9B;;;;AAKD,oBAAA,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC;iBACtC;gBAED,IAAI,oBAAoB,EAAE;oBACxB,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,EAAC,SAAS,EAAE,OAAO,EAAC,CAAC,CAAC;iBACjD;AAED,gBAAA,IAAI,OAAO,CAAC,OAAO,EAAE;oBACnB,mBAAmB,CAAC,KAAK,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;iBAC7D;AACH,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;KACJ;IAED,cAAc,CACZ,QAA4C,EAC5C,OAAmC,EAAA;AAEnC,QAAA,MAAM,GAAG,GAAiB,EAAC,IAAI,EAAE,qBAAqB,CAAC,SAAS,EAAE,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;AAC7F,QAAA,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE;YAClC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;AACxC,YAAA,OAAO,GAAG,CAAC;SACZ;QAED,MAAM,mBAAmB,GAAG,CAAC,CAAC;QAE9B,IAAI,yBAAyB,GAAG,CAAC,CAAC;QAClC,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,IAAI,iBAAiB,GAAG,KAAK,CAAC;QAC9B,IAAI,mBAAmB,GAAG,KAAK,CAAC;QAChC,IAAI,cAAc,GAAW,CAAC,CAAC;QAE/B,MAAM,SAAS,GAAe,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,KAAI;YAC1D,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAClD,IAAI,SAAS,GACX,KAAK,CAAC,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,MAAM,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YACpE,IAAI,MAAM,GAAW,CAAC,CAAC;AACvB,YAAA,IAAI,SAAS,IAAI,IAAI,EAAE;AACrB,gBAAA,yBAAyB,EAAE,CAAC;AAC5B,gBAAA,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC;aACnC;YACD,mBAAmB,GAAG,mBAAmB,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC;AACtE,YAAA,iBAAiB,GAAG,iBAAiB,IAAI,MAAM,GAAG,cAAc,CAAC;YACjE,cAAc,GAAG,MAAM,CAAC;AACxB,YAAA,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACrB,YAAA,OAAO,KAAK,CAAC;AACf,SAAC,CAAC,CAAC;QAEH,IAAI,mBAAmB,EAAE;YACvB,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;SACtC;QAED,IAAI,iBAAiB,EAAE;YACrB,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE,CAAC,CAAC;SAClD;AAED,QAAA,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC;QACrC,IAAI,eAAe,GAAG,CAAC,CAAC;QACxB,IAAI,yBAAyB,GAAG,CAAC,IAAI,yBAAyB,GAAG,MAAM,EAAE;YACvE,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC,CAAC;SAChD;AAAM,aAAA,IAAI,yBAAyB,IAAI,CAAC,EAAE;YACzC,eAAe,GAAG,mBAAmB,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC;SACtD;AAED,QAAA,MAAM,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC;AACzB,QAAA,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;AACxC,QAAA,MAAM,qBAAqB,GAAG,OAAO,CAAC,qBAAsB,CAAC;AAC7D,QAAA,MAAM,eAAe,GAAG,qBAAqB,CAAC,QAAQ,CAAC;QACvD,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,KAAI;AAC1B,YAAA,MAAM,MAAM,GAAG,eAAe,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;AACzF,YAAA,MAAM,qBAAqB,GAAG,MAAM,GAAG,eAAe,CAAC;YACvD,OAAO,CAAC,WAAW,GAAG,WAAW,GAAG,qBAAqB,CAAC,KAAK,GAAG,qBAAqB,CAAC;AACxF,YAAA,qBAAqB,CAAC,QAAQ,GAAG,qBAAqB,CAAC;AACvD,YAAA,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;AACpC,YAAA,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC;AAEnB,YAAA,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACtB,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,GAAG,CAAC;KACZ;IAED,cAAc,CACZ,QAAoC,EACpC,OAAmC,EAAA;QAEnC,OAAO;YACL,IAAI,EAAE,qBAAqB,CAAC,SAAS;AACrC,YAAA,SAAS,EAAE,YAAY,CAAC,IAAI,EAAE,uBAAuB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC;AACnF,YAAA,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;SACrD,CAAC;KACH;IAED,iBAAiB,CACf,QAAuC,EACvC,OAAmC,EAAA;QAEnC,OAAO,CAAC,QAAQ,EAAE,CAAC;QACnB,OAAO;YACL,IAAI,EAAE,qBAAqB,CAAC,YAAY;AACxC,YAAA,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;SACrD,CAAC;KACH;IAED,eAAe,CACb,QAAqC,EACrC,OAAmC,EAAA;QAEnC,OAAO;YACL,IAAI,EAAE,qBAAqB,CAAC,UAAU;YACtC,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC;AAC3D,YAAA,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;SACrD,CAAC;KACH;IAED,UAAU,CAAC,QAAgC,EAAE,OAAmC,EAAA;AAC9E,QAAA,MAAM,cAAc,GAAG,OAAO,CAAC,oBAAqB,CAAC;QACrD,MAAM,OAAO,IAAI,QAAQ,CAAC,OAAO,IAAI,EAAE,CAA0B,CAAC;QAElE,OAAO,CAAC,UAAU,EAAE,CAAC;AACrB,QAAA,OAAO,CAAC,YAAY,GAAG,QAAQ,CAAC;AAChC,QAAA,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACrE,QAAA,OAAO,CAAC,oBAAoB,GAAG,cAAc,CAAC,MAAM;AAClD,cAAE,cAAc,GAAG,GAAG,GAAG,QAAQ;cAC/B,QAAQ,CAAC;AACb,QAAA,oBAAoB,CAAC,OAAO,CAAC,eAAe,EAAE,OAAO,CAAC,oBAAoB,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;AAEvF,QAAA,MAAM,SAAS,GAAG,YAAY,CAAC,IAAI,EAAE,uBAAuB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,CAAC;AAC3F,QAAA,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;AAC5B,QAAA,OAAO,CAAC,oBAAoB,GAAG,cAAc,CAAC;QAE9C,OAAO;YACL,IAAI,EAAE,qBAAqB,CAAC,KAAK;YACjC,QAAQ;AACR,YAAA,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC;AACzB,YAAA,QAAQ,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ;YAC5B,WAAW;YACX,SAAS;YACT,gBAAgB,EAAE,QAAQ,CAAC,QAAQ;AACnC,YAAA,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;SACrD,CAAC;KACH;IAED,YAAY,CACV,QAAkC,EAClC,OAAmC,EAAA;AAEnC,QAAA,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;YACzB,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;SACvC;AACD,QAAA,MAAM,OAAO,GACX,QAAQ,CAAC,OAAO,KAAK,MAAM;AACzB,cAAE,EAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAC;AACzC,cAAE,aAAa,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAE5D,OAAO;YACL,IAAI,EAAE,qBAAqB,CAAC,OAAO;AACnC,YAAA,SAAS,EAAE,YAAY,CAAC,IAAI,EAAE,uBAAuB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC;YACnF,OAAO;AACP,YAAA,OAAO,EAAE,IAAI;SACd,CAAC;KACH;AACF,CAAA;AAED,SAAS,iBAAiB,CAAC,QAAgB,EAAA;AACzC,IAAA,MAAM,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,KAAK,KAAK,IAAI,UAAU,CAAC;AACjF,UAAE,IAAI;UACJ,KAAK,CAAC;IACV,IAAI,YAAY,EAAE;QAChB,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;KACnD;;;AAID,IAAA,QAAQ,GAAG,QAAQ;AAChB,SAAA,OAAO,CAAC,MAAM,EAAE,mBAAmB,CAAC;AACpC,SAAA,OAAO,CAAC,OAAO,EAAE,CAAC,KAAK,KAAK,mBAAmB,GAAG,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACvE,SAAA,OAAO,CAAC,aAAa,EAAE,qBAAqB,CAAC,CAAC;AAEjD,IAAA,OAAO,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;AAClC,CAAC;AAED,SAAS,eAAe,CAAC,GAA+B,EAAA;AACtD,IAAA,OAAO,GAAG,GAAG,EAAC,GAAG,GAAG,EAAC,GAAG,IAAI,CAAC;AAC/B,CAAC;MAOY,0BAA0B,CAAA;AAWrC,IAAA,WAAA,CAAmB,MAAe,EAAA;QAAf,IAAM,CAAA,MAAA,GAAN,MAAM,CAAS;QAV3B,IAAU,CAAA,UAAA,GAAW,CAAC,CAAC;QACvB,IAAQ,CAAA,QAAA,GAAW,CAAC,CAAC;QACrB,IAAiB,CAAA,iBAAA,GAAuC,IAAI,CAAC;QAC7D,IAAY,CAAA,YAAA,GAAkC,IAAI,CAAC;QACnD,IAAoB,CAAA,oBAAA,GAAkB,IAAI,CAAC;QAC3C,IAAqB,CAAA,qBAAA,GAAqB,IAAI,CAAC;QAC/C,IAAW,CAAA,WAAA,GAAW,CAAC,CAAC;AACxB,QAAA,IAAA,CAAA,eAAe,GAAG,IAAI,GAAG,EAAuC,CAAC;QACjE,IAAO,CAAA,OAAA,GAA4B,IAAI,CAAC;AACxC,QAAA,IAAA,CAAA,6BAA6B,GAAgB,IAAI,GAAG,EAAU,CAAC;KAChC;AACvC,CAAA;AAID,SAAS,aAAa,CAAC,MAA0C,EAAA;IAC/D,IAAI,OAAO,MAAM,IAAI,QAAQ;AAAE,QAAA,OAAO,IAAI,CAAC;IAE3C,IAAI,MAAM,GAAkB,IAAI,CAAC;AAEjC,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AACzB,QAAA,MAAM,CAAC,OAAO,CAAC,CAAC,UAAU,KAAI;YAC5B,IAAI,UAAU,YAAY,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;gBACzD,MAAM,GAAG,GAAG,UAA2B,CAAC;gBACxC,MAAM,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAW,CAAC,CAAC;AACjD,gBAAA,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;aACtB;AACH,SAAC,CAAC,CAAC;KACJ;SAAM,IAAI,MAAM,YAAY,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;QACxD,MAAM,GAAG,GAAG,MAAM,CAAC;QACnB,MAAM,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAW,CAAC,CAAC;AACjD,QAAA,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;KACtB;AACD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,kBAAkB,CAAC,KAAuC,EAAE,MAAe,EAAA;AAClF,IAAA,IAAI,KAAK,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE;AACpC,QAAA,OAAO,KAAuB,CAAC;KAChC;AAED,IAAA,IAAI,OAAO,KAAK,IAAI,QAAQ,EAAE;QAC5B,MAAM,QAAQ,GAAG,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC;QACvD,OAAO,aAAa,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;KACvC;IAED,MAAM,QAAQ,GAAG,KAAe,CAAC;AACjC,IAAA,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;IAC9F,IAAI,SAAS,EAAE;QACb,MAAM,GAAG,GAAG,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAQ,CAAC;AAC3C,QAAA,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC;AACnB,QAAA,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACxB,QAAA,OAAO,GAAuB,CAAC;KAChC;IAED,MAAM,OAAO,GAAG,aAAa,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AAChD,IAAA,OAAO,aAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;AACxE,CAAC;AAED,SAAS,yBAAyB,CAAC,OAAgC,EAAA;IACjE,IAAI,OAAO,EAAE;AACX,QAAA,OAAO,GAAG,EAAC,GAAG,OAAO,EAAC,CAAC;AACvB,QAAA,IAAI,OAAO,CAAC,QAAQ,CAAC,EAAE;YACrB,OAAO,CAAC,QAAQ,CAAC,GAAG,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAE,CAAC;SACzD;KACF;SAAM;QACL,OAAO,GAAG,EAAE,CAAC;KACd;AACD,IAAA,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,aAAa,CAAC,QAAgB,EAAE,KAAa,EAAE,MAAqB,EAAA;AAC3E,IAAA,OAAO,EAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAC,CAAC;AACnC;;SCrpBgB,yBAAyB,CACvC,OAAY,EACZ,SAA+B,EAC/B,aAAuB,EACvB,cAAwB,EACxB,QAAgB,EAChB,KAAa,EACb,SAAwB,IAAI,EAC5B,cAAuB,KAAK,EAAA;IAE5B,OAAO;AACL,QAAA,IAAI,EAAsD,CAAA;QAC1D,OAAO;QACP,SAAS;QACT,aAAa;QACb,cAAc;QACd,QAAQ;QACR,KAAK;QACL,SAAS,EAAE,QAAQ,GAAG,KAAK;QAC3B,MAAM;QACN,WAAW;KACZ,CAAC;AACJ;;MCxCa,qBAAqB,CAAA;AAAlC,IAAA,WAAA,GAAA;AACU,QAAA,IAAA,CAAA,IAAI,GAAG,IAAI,GAAG,EAAuC,CAAC;KAqB/D;AAnBC,IAAA,GAAG,CAAC,OAAY,EAAA;QACd,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;KACrC;IAED,MAAM,CAAC,OAAY,EAAE,YAA4C,EAAA;QAC/D,IAAI,oBAAoB,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAClD,IAAI,CAAC,oBAAoB,EAAE;AACzB,YAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,oBAAoB,GAAG,EAAE,EAAE,CAAC;SACrD;AACD,QAAA,oBAAoB,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC;KAC5C;AAED,IAAA,GAAG,CAAC,OAAY,EAAA;QACd,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;KAC/B;IAED,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;KACnB;AACF;;ACgBD,MAAM,yBAAyB,GAAG,CAAC,CAAC;AACpC,MAAM,WAAW,GAAG,QAAQ,CAAC;AAC7B,MAAM,iBAAiB,GAAG,IAAI,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;AACvD,MAAM,WAAW,GAAG,QAAQ,CAAC;AAC7B,MAAM,iBAAiB,GAAG,IAAI,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;AAEvD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6EG;AACa,SAAA,uBAAuB,CACrC,MAAuB,EACvB,WAAgB,EAChB,GAA+B,EAC/B,cAAsB,EACtB,cAAsB,EACtB,cAAA,GAAgC,IAAI,GAAG,EAAE,EACzC,WAA6B,GAAA,IAAI,GAAG,EAAE,EACtC,OAAyB,EACzB,eAAuC,EACvC,MAAA,GAAkB,EAAE,EAAA;IAEpB,OAAO,IAAI,+BAA+B,EAAE,CAAC,cAAc,CACzD,MAAM,EACN,WAAW,EACX,GAAG,EACH,cAAc,EACd,cAAc,EACd,cAAc,EACd,WAAW,EACX,OAAO,EACP,eAAe,EACf,MAAM,CACP,CAAC;AACJ,CAAC;MAEY,+BAA+B,CAAA;IAC1C,cAAc,CACZ,MAAuB,EACvB,WAAgB,EAChB,GAA+B,EAC/B,cAAsB,EACtB,cAAsB,EACtB,cAA6B,EAC7B,WAA0B,EAC1B,OAAyB,EACzB,eAAuC,EACvC,SAAkB,EAAE,EAAA;AAEpB,QAAA,eAAe,GAAG,eAAe,IAAI,IAAI,qBAAqB,EAAE,CAAC;AACjE,QAAA,MAAM,OAAO,GAAG,IAAI,wBAAwB,CAC1C,MAAM,EACN,WAAW,EACX,eAAe,EACf,cAAc,EACd,cAAc,EACd,MAAM,EACN,EAAE,CACH,CAAC;AACF,QAAA,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;AAC1B,QAAA,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACpE,QAAA,OAAO,CAAC,eAAe,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC7C,QAAA,OAAO,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AAEnF,QAAA,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;;AAGjC,QAAA,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,iBAAiB,EAAE,CAAC,CAAC;;;;;QAMvF,IAAI,SAAS,CAAC,MAAM,IAAI,WAAW,CAAC,IAAI,EAAE;AACxC,YAAA,IAAI,gBAA6C,CAAC;AAClD,YAAA,KAAK,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AAC9C,gBAAA,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AAC9B,gBAAA,IAAI,QAAQ,CAAC,OAAO,KAAK,WAAW,EAAE;oBACpC,gBAAgB,GAAG,QAAQ,CAAC;oBAC5B,MAAM;iBACP;aACF;YACD,IAAI,gBAAgB,IAAI,CAAC,gBAAgB,CAAC,uBAAuB,EAAE,EAAE;AACnE,gBAAA,gBAAgB,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;aAC1E;SACF;QACD,OAAO,SAAS,CAAC,MAAM;AACrB,cAAE,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,cAAc,EAAE,CAAC;cACtD,CAAC,yBAAyB,CAAC,WAAW,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;KAC/E;IAED,YAAY,CAAC,GAAe,EAAE,OAAiC,EAAA;;KAE9D;IAED,UAAU,CAAC,GAAa,EAAE,OAAiC,EAAA;;KAE1D;IAED,eAAe,CAAC,GAAkB,EAAE,OAAiC,EAAA;;KAEpE;IAED,iBAAiB,CAAC,GAAoB,EAAE,OAAiC,EAAA;AACvE,QAAA,MAAM,mBAAmB,GAAG,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACzE,IAAI,mBAAmB,EAAE;YACvB,MAAM,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AAC3D,YAAA,MAAM,SAAS,GAAG,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC;AACtD,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CACxC,mBAAmB,EACnB,YAAY,EACZ,YAAY,CAAC,OAA8B,CAC5C,CAAC;AACF,YAAA,IAAI,SAAS,IAAI,OAAO,EAAE;;;AAGxB,gBAAA,OAAO,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;aAC3C;SACF;AACD,QAAA,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;KAC5B;IAED,eAAe,CAAC,GAAkB,EAAE,OAAiC,EAAA;QACnE,MAAM,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC3D,YAAY,CAAC,wBAAwB,EAAE,CAAC;AACxC,QAAA,IAAI,CAAC,wBAAwB,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;QAC3F,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;QACjD,OAAO,CAAC,wBAAwB,CAAC,YAAY,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;AAC3E,QAAA,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;KAC5B;AAEO,IAAA,wBAAwB,CAC9B,qBAAkD,EAClD,OAAiC,EACjC,YAAsC,EAAA;AAEtC,QAAA,KAAK,MAAM,mBAAmB,IAAI,qBAAqB,EAAE;AACvD,YAAA,MAAM,cAAc,GAAG,mBAAmB,EAAE,KAAK,CAAC;YAClD,IAAI,cAAc,EAAE;AAClB,gBAAA,MAAM,mBAAmB,GACvB,OAAO,cAAc,KAAK,QAAQ;AAChC,sBAAE,cAAc;AAChB,sBAAE,kBAAkB,CAChB,iBAAiB,CACf,cAAc,EACd,mBAAmB,EAAE,MAAM,IAAI,EAAE,EACjC,OAAO,CAAC,MAAM,CACf,CACF,CAAC;AACR,gBAAA,YAAY,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;aACjD;SACF;KACF;AAEO,IAAA,qBAAqB,CAC3B,YAA4C,EAC5C,OAAiC,EACjC,OAA4B,EAAA;AAE5B,QAAA,MAAM,SAAS,GAAG,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC;QACtD,IAAI,YAAY,GAAG,SAAS,CAAC;;;QAI7B,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,IAAI,GAAG,kBAAkB,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;QACxF,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,IAAI,GAAG,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;AAC/E,QAAA,IAAI,QAAQ,KAAK,CAAC,EAAE;AAClB,YAAA,YAAY,CAAC,OAAO,CAAC,CAAC,WAAW,KAAI;AACnC,gBAAA,MAAM,kBAAkB,GAAG,OAAO,CAAC,2BAA2B,CAC5D,WAAW,EACX,QAAQ,EACR,KAAK,CACN,CAAC;AACF,gBAAA,YAAY,GAAG,IAAI,CAAC,GAAG,CACrB,YAAY,EACZ,kBAAkB,CAAC,QAAQ,GAAG,kBAAkB,CAAC,KAAK,CACvD,CAAC;AACJ,aAAC,CAAC,CAAC;SACJ;AAED,QAAA,OAAO,YAAY,CAAC;KACrB;IAED,cAAc,CAAC,GAAiB,EAAE,OAAiC,EAAA;QACjE,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACzC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AAC3C,QAAA,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;KAC5B;IAED,aAAa,CAAC,GAAgB,EAAE,OAAiC,EAAA;AAC/D,QAAA,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;QAChD,IAAI,GAAG,GAAG,OAAO,CAAC;AAClB,QAAA,MAAM,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;AAE5B,QAAA,IAAI,OAAO,KAAK,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;AAChD,YAAA,GAAG,GAAG,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YACxC,GAAG,CAAC,wBAAwB,EAAE,CAAC;AAE/B,YAAA,IAAI,OAAO,CAAC,KAAK,IAAI,IAAI,EAAE;gBACzB,IAAI,GAAG,CAAC,YAAY,CAAC,IAAI,IAAI,qBAAqB,CAAC,KAAK,EAAE;AACxD,oBAAA,GAAG,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC;AAC5C,oBAAA,GAAG,CAAC,YAAY,GAAG,0BAA0B,CAAC;iBAC/C;gBAED,MAAM,KAAK,GAAG,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAChD,gBAAA,GAAG,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;aAC1B;SACF;AAED,QAAA,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE;AACpB,YAAA,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,YAAY,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;AAGrD,YAAA,GAAG,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC;;;;AAK5C,YAAA,IAAI,GAAG,CAAC,eAAe,GAAG,eAAe,EAAE;gBACzC,GAAG,CAAC,wBAAwB,EAAE,CAAC;aAChC;SACF;AAED,QAAA,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;KAC5B;IAED,UAAU,CAAC,GAAa,EAAE,OAAiC,EAAA;QACzD,MAAM,cAAc,GAAsB,EAAE,CAAC;AAC7C,QAAA,IAAI,YAAY,GAAG,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC;QACvD,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,KAAK,GAAG,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAE3F,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAI;YACtB,MAAM,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC3D,IAAI,KAAK,EAAE;AACT,gBAAA,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;aACnC;AAED,YAAA,YAAY,CAAC,IAAI,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;AACpC,YAAA,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,YAAY,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;AAChF,YAAA,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;AACpD,SAAC,CAAC,CAAC;;;;AAKH,QAAA,cAAc,CAAC,OAAO,CAAC,CAAC,QAAQ,KAC9B,OAAO,CAAC,eAAe,CAAC,4BAA4B,CAAC,QAAQ,CAAC,CAC/D,CAAC;AACF,QAAA,OAAO,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;AAC/C,QAAA,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;KAC5B;IAEO,YAAY,CAAC,GAAc,EAAE,OAAiC,EAAA;AACpE,QAAA,IAAK,GAAwB,CAAC,OAAO,EAAE;AACrC,YAAA,MAAM,QAAQ,GAAI,GAAwB,CAAC,QAAQ,CAAC;AACpD,YAAA,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM;AAChC,kBAAE,iBAAiB,CAAC,QAAQ,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC;kBAC3D,QAAQ,CAAC;YACb,OAAO,aAAa,CAAC,WAAW,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;SACnD;aAAM;AACL,YAAA,OAAO,EAAC,QAAQ,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,EAAC,CAAC;SACvE;KACF;IAED,YAAY,CAAC,GAAe,EAAE,OAAiC,EAAA;AAC7D,QAAA,MAAM,OAAO,IAAI,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;AAC1F,QAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,eAAe,CAAC;AACzC,QAAA,IAAI,OAAO,CAAC,KAAK,EAAE;AACjB,YAAA,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACrC,QAAQ,CAAC,qBAAqB,EAAE,CAAC;SAClC;AAED,QAAA,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;QACxB,IAAI,KAAK,CAAC,IAAI,IAAI,qBAAqB,CAAC,SAAS,EAAE;AACjD,YAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;SACrC;aAAM;AACL,YAAA,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AACxC,YAAA,IAAI,CAAC,UAAU,CAAC,KAAiB,EAAE,OAAO,CAAC,CAAC;YAC5C,QAAQ,CAAC,qBAAqB,EAAE,CAAC;SAClC;AAED,QAAA,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC;AACrC,QAAA,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;KAC5B;IAED,UAAU,CAAC,GAAa,EAAE,OAAiC,EAAA;AACzD,QAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,eAAe,CAAC;AACzC,QAAA,MAAM,OAAO,GAAG,OAAO,CAAC,qBAAsB,CAAC;;;QAI/C,IAAI,CAAC,OAAO,IAAI,QAAQ,CAAC,yBAAyB,EAAE,EAAE;YACpD,QAAQ,CAAC,YAAY,EAAE,CAAC;SACzB;AAED,QAAA,MAAM,MAAM,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,CAAC;AACzD,QAAA,IAAI,GAAG,CAAC,WAAW,EAAE;AACnB,YAAA,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;SACjC;aAAM;AACL,YAAA,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;SACzE;AAED,QAAA,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;KAC5B;IAED,cAAc,CAAC,GAAiB,EAAE,OAAiC,EAAA;AACjE,QAAA,MAAM,qBAAqB,GAAG,OAAO,CAAC,qBAAsB,CAAC;AAC7D,QAAA,MAAM,SAAS,GAAG,OAAO,CAAC,eAAgB,CAAC,QAAQ,CAAC;AACpD,QAAA,MAAM,QAAQ,GAAG,qBAAqB,CAAC,QAAQ,CAAC;AAChD,QAAA,MAAM,YAAY,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;AAChD,QAAA,MAAM,aAAa,GAAG,YAAY,CAAC,eAAe,CAAC;AACnD,QAAA,aAAa,CAAC,MAAM,GAAG,qBAAqB,CAAC,MAAM,CAAC;QAEpD,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC1B,YAAA,MAAM,MAAM,GAAW,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;AACxC,YAAA,aAAa,CAAC,WAAW,CAAC,MAAM,GAAG,QAAQ,CAAC,CAAC;AAC7C,YAAA,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;YACnF,aAAa,CAAC,qBAAqB,EAAE,CAAC;AACxC,SAAC,CAAC,CAAC;;;AAIH,QAAA,OAAO,CAAC,eAAe,CAAC,4BAA4B,CAAC,aAAa,CAAC,CAAC;;;AAIpE,QAAA,OAAO,CAAC,wBAAwB,CAAC,SAAS,GAAG,QAAQ,CAAC,CAAC;AACvD,QAAA,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;KAC5B;IAED,UAAU,CAAC,GAAa,EAAE,OAAiC,EAAA;;;AAGzD,QAAA,MAAM,SAAS,GAAG,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC;QACtD,MAAM,OAAO,IAAI,GAAG,CAAC,OAAO,IAAI,EAAE,CAA0B,CAAC;AAC7D,QAAA,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAEpE,QAAA,IACE,KAAK;aACJ,OAAO,CAAC,YAAY,CAAC,IAAI,KAAK,qBAAqB,CAAC,KAAK;AACxD,iBAAC,SAAS,IAAI,CAAC,IAAI,OAAO,CAAC,eAAe,CAAC,yBAAyB,EAAE,CAAC,CAAC,EAC1E;AACA,YAAA,OAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC;AAChD,YAAA,OAAO,CAAC,YAAY,GAAG,0BAA0B,CAAC;SACnD;QAED,IAAI,YAAY,GAAG,SAAS,CAAC;AAC7B,QAAA,MAAM,IAAI,GAAG,OAAO,CAAC,WAAW,CAC9B,GAAG,CAAC,QAAQ,EACZ,GAAG,CAAC,gBAAgB,EACpB,GAAG,CAAC,KAAK,EACT,GAAG,CAAC,WAAW,EACf,OAAO,CAAC,QAAQ,GAAG,IAAI,GAAG,KAAK,EAC/B,OAAO,CAAC,MAAM,CACf,CAAC;AAEF,QAAA,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC;QACxC,IAAI,mBAAmB,GAA2B,IAAI,CAAC;QACvD,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC,KAAI;AAC1B,YAAA,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC;AAC9B,YAAA,MAAM,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACpE,IAAI,KAAK,EAAE;AACT,gBAAA,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;aACnC;AAED,YAAA,IAAI,OAAO,KAAK,OAAO,CAAC,OAAO,EAAE;AAC/B,gBAAA,mBAAmB,GAAG,YAAY,CAAC,eAAe,CAAC;aACpD;YAED,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;;;;AAKhD,YAAA,YAAY,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC;AAErD,YAAA,MAAM,OAAO,GAAG,YAAY,CAAC,eAAe,CAAC,WAAW,CAAC;YACzD,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;AACjD,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC;AAC9B,QAAA,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC;AAC9B,QAAA,OAAO,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;QAE/C,IAAI,mBAAmB,EAAE;AACvB,YAAA,OAAO,CAAC,eAAe,CAAC,4BAA4B,CAAC,mBAAmB,CAAC,CAAC;AAC1E,YAAA,OAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC;SACjD;AAED,QAAA,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;KAC5B;IAED,YAAY,CAAC,GAAe,EAAE,OAAiC,EAAA;AAC7D,QAAA,MAAM,aAAa,GAAG,OAAO,CAAC,aAAc,CAAC;AAC7C,QAAA,MAAM,EAAE,GAAG,OAAO,CAAC,eAAe,CAAC;AACnC,QAAA,MAAM,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;QAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,IAAI,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;AAC3D,QAAA,IAAI,KAAK,GAAG,QAAQ,GAAG,OAAO,CAAC,iBAAiB,CAAC;AAEjD,QAAA,IAAI,kBAAkB,GAAG,OAAO,CAAC,QAAQ,GAAG,CAAC,GAAG,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC;QAC3E,QAAQ,kBAAkB;AACxB,YAAA,KAAK,SAAS;AACZ,gBAAA,KAAK,GAAG,OAAO,GAAG,KAAK,CAAC;gBACxB,MAAM;AACR,YAAA,KAAK,MAAM;AACT,gBAAA,KAAK,GAAG,aAAa,CAAC,kBAAkB,CAAC;gBACzC,MAAM;SACT;AAED,QAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,eAAe,CAAC;QACzC,IAAI,KAAK,EAAE;AACT,YAAA,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;SAC/B;AAED,QAAA,MAAM,YAAY,GAAG,QAAQ,CAAC,WAAW,CAAC;QAC1C,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AAC3C,QAAA,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;;;;;AAM3B,QAAA,aAAa,CAAC,kBAAkB;AAC9B,YAAA,EAAE,CAAC,WAAW,GAAG,YAAY,IAAI,EAAE,CAAC,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;KAC5F;AACF,CAAA;AAOD,MAAM,0BAA0B,GAA+B,EAAE,CAAC;MACrD,wBAAwB,CAAA;AAWnC,IAAA,WAAA,CACU,OAAwB,EACzB,OAAY,EACZ,eAAsC,EACrC,eAAuB,EACvB,eAAuB,EACxB,MAAe,EACf,SAA4B,EACnC,eAAiC,EAAA;QAPzB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAiB;QACzB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAK;QACZ,IAAe,CAAA,eAAA,GAAf,eAAe,CAAuB;QACrC,IAAe,CAAA,eAAA,GAAf,eAAe,CAAQ;QACvB,IAAe,CAAA,eAAA,GAAf,eAAe,CAAQ;QACxB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAS;QACf,IAAS,CAAA,SAAA,GAAT,SAAS,CAAmB;QAjB9B,IAAa,CAAA,aAAA,GAAoC,IAAI,CAAC;QAEtD,IAAqB,CAAA,qBAAA,GAA0B,IAAI,CAAC;QACpD,IAAY,CAAA,YAAA,GAA+B,0BAA0B,CAAC;QACtE,IAAe,CAAA,eAAA,GAAG,CAAC,CAAC;QACpB,IAAO,CAAA,OAAA,GAAqB,EAAE,CAAC;QAC/B,IAAiB,CAAA,iBAAA,GAAW,CAAC,CAAC;QAC9B,IAAiB,CAAA,iBAAA,GAAW,CAAC,CAAC;QAC9B,IAAkB,CAAA,kBAAA,GAAW,CAAC,CAAC;AAYpC,QAAA,IAAI,CAAC,eAAe,GAAG,eAAe,IAAI,IAAI,eAAe,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;AACxF,QAAA,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;KACtC;AAED,IAAA,IAAI,MAAM,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;KAC5B;IAED,aAAa,CAAC,OAAgC,EAAE,YAAsB,EAAA;AACpE,QAAA,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,MAAM,UAAU,GAAG,OAAc,CAAC;AAClC,QAAA,IAAI,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC;;AAGnC,QAAA,IAAI,UAAU,CAAC,QAAQ,IAAI,IAAI,EAAE;YAC9B,eAAuB,CAAC,QAAQ,GAAG,kBAAkB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;SAC7E;AAED,QAAA,IAAI,UAAU,CAAC,KAAK,IAAI,IAAI,EAAE;YAC5B,eAAe,CAAC,KAAK,GAAG,kBAAkB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;SAC9D;AAED,QAAA,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC;QACpC,IAAI,SAAS,EAAE;AACb,YAAA,IAAI,cAAc,GAA0B,eAAe,CAAC,MAAO,CAAC;YACpE,IAAI,CAAC,cAAc,EAAE;gBACnB,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC;aAC3C;YAED,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;gBACtC,IAAI,CAAC,YAAY,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;AACzD,oBAAA,cAAc,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;iBACxF;AACH,aAAC,CAAC,CAAC;SACJ;KACF;IAEO,YAAY,GAAA;QAClB,MAAM,OAAO,GAAqB,EAAE,CAAC;AACrC,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;YACtC,IAAI,SAAS,EAAE;gBACb,MAAM,MAAM,IAA2B,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC;gBAC/D,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;oBACtC,MAAM,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;AACjC,iBAAC,CAAC,CAAC;aACJ;SACF;AACD,QAAA,OAAO,OAAO,CAAC;KAChB;AAED,IAAA,gBAAgB,CACd,OAAmC,GAAA,IAAI,EACvC,OAAa,EACb,OAAgB,EAAA;AAEhB,QAAA,MAAM,MAAM,GAAG,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC;QACvC,MAAM,OAAO,GAAG,IAAI,wBAAwB,CAC1C,IAAI,CAAC,OAAO,EACZ,MAAM,EACN,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,CAAC,CAChD,CAAC;AACF,QAAA,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;AACzC,QAAA,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;AAE3D,QAAA,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;AACtC,QAAA,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AAE/B,QAAA,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;AACnD,QAAA,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;AACnD,QAAA,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,eAAe,EAAE,CAAC;AACvB,QAAA,OAAO,OAAO,CAAC;KAChB;AAED,IAAA,wBAAwB,CAAC,OAAgB,EAAA;AACvC,QAAA,IAAI,CAAC,YAAY,GAAG,0BAA0B,CAAC;AAC/C,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACxE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC1C,OAAO,IAAI,CAAC,eAAe,CAAC;KAC7B;AAED,IAAA,2BAA2B,CACzB,WAAyC,EACzC,QAAuB,EACvB,KAAoB,EAAA;AAEpB,QAAA,MAAM,cAAc,GAAmB;AACrC,YAAA,QAAQ,EAAE,QAAQ,IAAI,IAAI,GAAG,QAAQ,GAAG,WAAW,CAAC,QAAQ;YAC5D,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,WAAW,IAAI,KAAK,IAAI,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,KAAK;AACzF,YAAA,MAAM,EAAE,EAAE;SACX,CAAC;AACF,QAAA,MAAM,OAAO,GAAG,IAAI,kBAAkB,CACpC,IAAI,CAAC,OAAO,EACZ,WAAW,CAAC,OAAO,EACnB,WAAW,CAAC,SAAS,EACrB,WAAW,CAAC,aAAa,EACzB,WAAW,CAAC,cAAc,EAC1B,cAAc,EACd,WAAW,CAAC,uBAAuB,CACpC,CAAC;AACF,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC7B,QAAA,OAAO,cAAc,CAAC;KACvB;AAED,IAAA,aAAa,CAAC,IAAY,EAAA;AACxB,QAAA,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC;KACxE;AAED,IAAA,aAAa,CAAC,KAAa,EAAA;;AAEzB,QAAA,IAAI,KAAK,GAAG,CAAC,EAAE;AACb,YAAA,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;SAC3C;KACF;IAED,WAAW,CACT,QAAgB,EAChB,gBAAwB,EACxB,KAAa,EACb,WAAoB,EACpB,QAAiB,EACjB,MAAe,EAAA;QAEf,IAAI,OAAO,GAAU,EAAE,CAAC;QACxB,IAAI,WAAW,EAAE;AACf,YAAA,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC5B;AACD,QAAA,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;;AAEvB,YAAA,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,iBAAiB,EAAE,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC;AAC3E,YAAA,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,iBAAiB,EAAE,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC;AAC3E,YAAA,MAAM,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC;AACzB,YAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;AACjE,YAAA,IAAI,KAAK,KAAK,CAAC,EAAE;gBACf,QAAQ;AACN,oBAAA,KAAK,GAAG,CAAC;AACP,0BAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;0BACxD,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;aAChC;AACD,YAAA,OAAO,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;SAC3B;QAED,IAAI,CAAC,QAAQ,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE;YACpC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC,CAAC;SAC7C;AACD,QAAA,OAAO,OAAO,CAAC;KAChB;AACF,CAAA;MAEY,eAAe,CAAA;AAa1B,IAAA,WAAA,CACU,OAAwB,EACzB,OAAY,EACZ,SAAiB,EAChB,4BAAsD,EAAA;QAHtD,IAAO,CAAA,OAAA,GAAP,OAAO,CAAiB;QACzB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAK;QACZ,IAAS,CAAA,SAAA,GAAT,SAAS,CAAQ;QAChB,IAA4B,CAAA,4BAAA,GAA5B,4BAA4B,CAA0B;QAhBzD,IAAQ,CAAA,QAAA,GAAW,CAAC,CAAC;QACrB,IAAM,CAAA,MAAA,GAAkB,IAAI,CAAC;AAC5B,QAAA,IAAA,CAAA,iBAAiB,GAAkB,IAAI,GAAG,EAAE,CAAC;AAC7C,QAAA,IAAA,CAAA,gBAAgB,GAAkB,IAAI,GAAG,EAAE,CAAC;AAC5C,QAAA,IAAA,CAAA,UAAU,GAAG,IAAI,GAAG,EAAyB,CAAC;AAC9C,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,GAAG,EAAuB,CAAC;AAC/C,QAAA,IAAA,CAAA,oBAAoB,GAAkB,IAAI,GAAG,EAAE,CAAC;AAEhD,QAAA,IAAA,CAAA,cAAc,GAAkB,IAAI,GAAG,EAAE,CAAC;AAC1C,QAAA,IAAA,CAAA,SAAS,GAAkB,IAAI,GAAG,EAAE,CAAC;QACrC,IAAyB,CAAA,yBAAA,GAAyB,IAAI,CAAC;AAQ7D,QAAA,IAAI,CAAC,IAAI,CAAC,4BAA4B,EAAE;AACtC,YAAA,IAAI,CAAC,4BAA4B,GAAG,IAAI,GAAG,EAAsB,CAAC;SACnE;QAED,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC;AAC7E,QAAA,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;AAC/B,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,oBAAoB,CAAC;YACvD,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;SAC3E;QACD,IAAI,CAAC,aAAa,EAAE,CAAC;KACtB;IAED,iBAAiB,GAAA;AACf,QAAA,QAAQ,IAAI,CAAC,UAAU,CAAC,IAAI;AAC1B,YAAA,KAAK,CAAC;AACJ,gBAAA,OAAO,KAAK,CAAC;AACf,YAAA,KAAK,CAAC;AACJ,gBAAA,OAAO,IAAI,CAAC,yBAAyB,EAAE,CAAC;AAC1C,YAAA;AACE,gBAAA,OAAO,IAAI,CAAC;SACf;KACF;IAED,yBAAyB,GAAA;AACvB,QAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,GAAG,CAAC,CAAC;KACvC;AAED,IAAA,IAAI,WAAW,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;KACvC;AAED,IAAA,aAAa,CAAC,KAAa,EAAA;;;;;AAKzB,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;AAE/E,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,eAAe,EAAE;YACpC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,CAAC;YAC3C,IAAI,eAAe,EAAE;gBACnB,IAAI,CAAC,qBAAqB,EAAE,CAAC;aAC9B;SACF;aAAM;AACL,YAAA,IAAI,CAAC,SAAS,IAAI,KAAK,CAAC;SACzB;KACF;IAED,IAAI,CAAC,OAAY,EAAE,WAAoB,EAAA;QACrC,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,QAAA,OAAO,IAAI,eAAe,CACxB,IAAI,CAAC,OAAO,EACZ,OAAO,EACP,WAAW,IAAI,IAAI,CAAC,WAAW,EAC/B,IAAI,CAAC,4BAA4B,CAClC,CAAC;KACH;IAEO,aAAa,GAAA;AACnB,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACzB,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC;SAChD;AACD,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAE,CAAC;AAC5D,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;AAC1B,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAC;AAClC,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAC3D;KACF;IAED,YAAY,GAAA;AACV,QAAA,IAAI,CAAC,QAAQ,IAAI,yBAAyB,CAAC;QAC3C,IAAI,CAAC,aAAa,EAAE,CAAC;KACtB;AAED,IAAA,WAAW,CAAC,IAAY,EAAA;QACtB,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,aAAa,EAAE,CAAC;KACtB;IAEO,YAAY,CAAC,IAAY,EAAE,KAAsB,EAAA;QACvD,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC3C,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAC5C,QAAA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,EAAE,EAAC,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,KAAK,EAAC,CAAC,CAAC;KAC/D;IAED,uBAAuB,GAAA;AACrB,QAAA,OAAO,IAAI,CAAC,yBAAyB,KAAK,IAAI,CAAC,gBAAgB,CAAC;KACjE;AAED,IAAA,cAAc,CAAC,MAAqB,EAAA;QAClC,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;SAC9C;;;;;;;QAQD,KAAK,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,qBAAqB,EAAE;YACpD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,IAAI,UAAU,CAAC,CAAC;YAC9C,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;SAC7C;AACD,QAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,gBAAgB,CAAC;KACxD;AAED,IAAA,SAAS,CACP,KAAoC,EACpC,MAAqB,EACrB,MAAe,EACf,OAA0B,EAAA;QAE1B,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;SAC9C;QACD,MAAM,MAAM,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,CAAC;QACjD,MAAM,MAAM,GAAG,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAChE,KAAK,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,MAAM,EAAE;YAChC,MAAM,GAAG,GAAG,iBAAiB,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;YACrD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YACnC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AACxC,gBAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,CAAC;aAC9E;AACD,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;SAC9B;KACF;IAED,qBAAqB,GAAA;AACnB,QAAA,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;YAAE,OAAO;QAE1C,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,KAAI;YACxC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AACvC,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAE5B,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,KAAI;YAC9C,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBACpC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;aACtC;AACH,SAAC,CAAC,CAAC;KACJ;IAED,qBAAqB,GAAA;QACnB,KAAK,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,oBAAoB,EAAE;YACjD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AACnC,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;SAC9B;KACF;IAED,gBAAgB,GAAA;QACd,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KAC3C;AAED,IAAA,IAAI,UAAU,GAAA;QACZ,MAAM,UAAU,GAAa,EAAE,CAAC;AAChC,QAAA,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACtC,YAAA,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACvB;AACD,QAAA,OAAO,UAAU,CAAC;KACnB;AAED,IAAA,4BAA4B,CAAC,QAAyB,EAAA;QACpD,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,IAAI,KAAI;YAChD,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAC9C,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE;gBAC9C,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;aACzC;AACH,SAAC,CAAC,CAAC;KACJ;IAED,cAAc,GAAA;QACZ,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,QAAA,MAAM,aAAa,GAAG,IAAI,GAAG,EAAU,CAAC;AACxC,QAAA,MAAM,cAAc,GAAG,IAAI,GAAG,EAAU,CAAC;AACzC,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,CAAC;QAElE,IAAI,cAAc,GAAyB,EAAE,CAAC;QAC9C,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,IAAI,KAAI;AACzC,YAAA,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC;YAChE,aAAa,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,KAAI;AACpC,gBAAA,IAAI,KAAK,KAAKA,UAAS,EAAE;AACvB,oBAAA,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;iBACzB;AAAM,qBAAA,IAAI,KAAK,KAAK,UAAU,EAAE;AAC/B,oBAAA,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;iBAC1B;AACH,aAAC,CAAC,CAAC;YACH,IAAI,CAAC,OAAO,EAAE;gBACZ,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;aACnD;AACD,YAAA,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACrC,SAAC,CAAC,CAAC;QAEH,MAAM,QAAQ,GAAa,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;QACvD,MAAM,SAAS,GAAa,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC;;QAGzD,IAAI,OAAO,EAAE;AACX,YAAA,MAAM,GAAG,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;AAC9B,YAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;AACzB,YAAA,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;AACrB,YAAA,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;AACrB,YAAA,cAAc,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;SAC7B;QAED,OAAO,yBAAyB,CAC9B,IAAI,CAAC,OAAO,EACZ,cAAc,EACd,QAAQ,EACR,SAAS,EACT,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,MAAM,EACX,KAAK,CACN,CAAC;KACH;AACF,CAAA;AAED,MAAM,kBAAmB,SAAQ,eAAe,CAAA;AAG9C,IAAA,WAAA,CACE,MAAuB,EACvB,OAAY,EACL,SAA+B,EAC/B,aAAuB,EACvB,cAAwB,EAC/B,OAAuB,EACf,2BAAoC,KAAK,EAAA;QAEjD,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;QAN/B,IAAS,CAAA,SAAA,GAAT,SAAS,CAAsB;QAC/B,IAAa,CAAA,aAAA,GAAb,aAAa,CAAU;QACvB,IAAc,CAAA,cAAA,GAAd,cAAc,CAAU;QAEvB,IAAwB,CAAA,wBAAA,GAAxB,wBAAwB,CAAiB;QAGjD,IAAI,CAAC,OAAO,GAAG,EAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAC,CAAC;KAC3F;IAEQ,iBAAiB,GAAA;AACxB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;KAClC;IAEQ,cAAc,GAAA;AACrB,QAAA,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC/B,IAAI,EAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC7C,QAAA,IAAI,IAAI,CAAC,wBAAwB,IAAI,KAAK,EAAE;YAC1C,MAAM,YAAY,GAAyB,EAAE,CAAC;AAC9C,YAAA,MAAM,SAAS,GAAG,QAAQ,GAAG,KAAK,CAAC;AACnC,YAAA,MAAM,WAAW,GAAG,KAAK,GAAG,SAAS,CAAC;;YAGtC,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,YAAA,gBAAgB,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;AAClC,YAAA,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAEpC,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/C,gBAAgB,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,CAAC;AACzD,YAAA,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AAEpC;;;;;;;;;;;;;AAaG;;AAGH,YAAA,MAAM,KAAK,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;AACnC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE;gBAC/B,IAAI,EAAE,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/B,MAAM,SAAS,GAAG,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAW,CAAC;AAC7C,gBAAA,MAAM,cAAc,GAAG,KAAK,GAAG,SAAS,GAAG,QAAQ,CAAC;AACpD,gBAAA,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,cAAc,GAAG,SAAS,CAAC,CAAC,CAAC;AAC1D,gBAAA,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;aACvB;;YAGD,QAAQ,GAAG,SAAS,CAAC;YACrB,KAAK,GAAG,CAAC,CAAC;YACV,MAAM,GAAG,EAAE,CAAC;YAEZ,SAAS,GAAG,YAAY,CAAC;SAC1B;QAED,OAAO,yBAAyB,CAC9B,IAAI,CAAC,OAAO,EACZ,SAAS,EACT,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,cAAc,EACnB,QAAQ,EACR,KAAK,EACL,MAAM,EACN,IAAI,CACL,CAAC;KACH;AACF,CAAA;AAED,SAAS,WAAW,CAAC,MAAc,EAAE,aAAa,GAAG,CAAC,EAAA;AACpD,IAAA,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,aAAa,GAAG,CAAC,CAAC,CAAC;IAC7C,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;AAC1C,CAAC;AAED,SAAS,aAAa,CAAC,KAAoC,EAAE,SAAwB,EAAA;AACnF,IAAA,MAAM,MAAM,GAAkB,IAAI,GAAG,EAAE,CAAC;AACxC,IAAA,IAAI,aAAkD,CAAC;AACvD,IAAA,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;AACtB,QAAA,IAAI,KAAK,KAAK,GAAG,EAAE;AACjB,YAAA,aAAa,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;AACnC,YAAA,KAAK,IAAI,IAAI,IAAI,aAAa,EAAE;AAC9B,gBAAA,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;aAC9B;SACF;aAAM;YACL,KAAK,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,KAAsB,EAAE;AAC9C,gBAAA,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;aACvB;SACF;AACH,KAAC,CAAC,CAAC;AACH,IAAA,OAAO,MAAM,CAAC;AAChB;;ACjhCM,SAAU,2BAA2B,CACzC,OAAY,EACZ,WAAmB,EACnB,SAAiB,EACjB,OAAe,EACf,mBAA4B,EAC5B,UAAyB,EACzB,QAAuB,EACvB,SAAyC,EACzC,eAAsB,EACtB,aAAoC,EACpC,cAAqC,EACrC,SAAiB,EACjB,MAAgB,EAAA;IAEhB,OAAO;AACL,QAAA,IAAI,EAAwD,CAAA;QAC5D,OAAO;QACP,WAAW;QACX,mBAAmB;QACnB,SAAS;QACT,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;QACT,eAAe;QACf,aAAa;QACb,cAAc;QACd,SAAS;QACT,MAAM;KACP,CAAC;AACJ;;ACxCA,MAAM,YAAY,GAAG,EAAE,CAAC;MAEX,0BAA0B,CAAA;AACrC,IAAA,WAAA,CACU,YAAoB,EACrB,GAAkB,EACjB,YAA+C,EAAA;QAF/C,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAQ;QACrB,IAAG,CAAA,GAAA,GAAH,GAAG,CAAe;QACjB,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAmC;KACrD;AAEJ,IAAA,KAAK,CAAC,YAAiB,EAAE,SAAc,EAAE,OAAY,EAAE,MAA4B,EAAA;AACjF,QAAA,OAAO,yBAAyB,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;KAC/F;AAED,IAAA,WAAW,CACT,SAAuC,EACvC,MAA4B,EAC5B,MAAe,EAAA;QAEf,IAAI,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxC,QAAA,IAAI,SAAS,KAAK,SAAS,EAAE;AAC3B,YAAA,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,EAAE,CAAC,IAAI,MAAM,CAAC;SACjE;AACD,QAAA,OAAO,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC;KAChE;AAED,IAAA,KAAK,CACH,MAAuB,EACvB,OAAY,EACZ,YAAiB,EACjB,SAAc,EACd,cAAsB,EACtB,cAAsB,EACtB,cAAiC,EACjC,WAA8B,EAC9B,eAAuC,EACvC,YAAsB,EAAA;QAEtB,MAAM,MAAM,GAAY,EAAE,CAAC;AAE3B,QAAA,MAAM,yBAAyB,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;QAChG,MAAM,sBAAsB,GAAG,CAAC,cAAc,IAAI,cAAc,CAAC,MAAM,KAAK,YAAY,CAAC;AACzF,QAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,sBAAsB,EAAE,MAAM,CAAC,CAAC;QAC1F,MAAM,mBAAmB,GAAG,CAAC,WAAW,IAAI,WAAW,CAAC,MAAM,KAAK,YAAY,CAAC;AAChF,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,mBAAmB,EAAE,MAAM,CAAC,CAAC;AAEjF,QAAA,MAAM,eAAe,GAAG,IAAI,GAAG,EAAO,CAAC;AACvC,QAAA,MAAM,WAAW,GAAG,IAAI,GAAG,EAAoB,CAAC;AAChD,QAAA,MAAM,YAAY,GAAG,IAAI,GAAG,EAAoB,CAAC;AACjD,QAAA,MAAM,SAAS,GAAG,SAAS,KAAK,MAAM,CAAC;AAEvC,QAAA,MAAM,gBAAgB,GAAqB;AACzC,YAAA,MAAM,EAAE,kBAAkB,CAAC,mBAAmB,EAAE,yBAAyB,CAAC;AAC1E,YAAA,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK;SAC/B,CAAC;QAEF,MAAM,SAAS,GAAG,YAAY;AAC5B,cAAE,EAAE;cACF,uBAAuB,CACrB,MAAM,EACN,OAAO,EACP,IAAI,CAAC,GAAG,CAAC,SAAS,EAClB,cAAc,EACd,cAAc,EACd,kBAAkB,EAClB,eAAe,EACf,gBAAgB,EAChB,eAAe,EACf,MAAM,CACP,CAAC;QAEN,IAAI,SAAS,GAAG,CAAC,CAAC;AAClB,QAAA,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,KAAI;AACvB,YAAA,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,GAAG,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AAC1D,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,MAAM,CAAC,MAAM,EAAE;AACjB,YAAA,OAAO,2BAA2B,CAChC,OAAO,EACP,IAAI,CAAC,YAAY,EACjB,YAAY,EACZ,SAAS,EACT,SAAS,EACT,kBAAkB,EAClB,eAAe,EACf,EAAE,EACF,EAAE,EACF,WAAW,EACX,YAAY,EACZ,SAAS,EACT,MAAM,CACP,CAAC;SACH;AAED,QAAA,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,KAAI;AACvB,YAAA,MAAM,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC;AACvB,YAAA,MAAM,QAAQ,GAAG,oBAAoB,CAAC,WAAW,EAAE,GAAG,EAAE,IAAI,GAAG,EAAU,CAAC,CAAC;AAC3E,YAAA,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AAEvD,YAAA,MAAM,SAAS,GAAG,oBAAoB,CAAC,YAAY,EAAE,GAAG,EAAE,IAAI,GAAG,EAAU,CAAC,CAAC;AAC7E,YAAA,EAAE,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AAEzD,YAAA,IAAI,GAAG,KAAK,OAAO,EAAE;AACnB,gBAAA,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;aAC1B;AACH,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;YACjD,6BAA6B,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;SACrE;AAED,QAAA,OAAO,2BAA2B,CAChC,OAAO,EACP,IAAI,CAAC,YAAY,EACjB,YAAY,EACZ,SAAS,EACT,SAAS,EACT,kBAAkB,EAClB,eAAe,EACf,SAAS,EACT,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAC7B,WAAW,EACX,YAAY,EACZ,SAAS,CACV,CAAC;KACH;AACF,CAAA;AAED;;;;;;;;;;;;;AAaG;AACH,SAAS,6BAA6B,CACpC,SAAyC,EACzC,WAAmB,EACnB,MAAuB,EAAA;AAEvB,IAAA,IAAI,CAAC,MAAM,CAAC,+BAA+B,EAAE;QAC3C,OAAO;KACR;AAED,IAAA,MAAM,yBAAyB,GAAG,IAAI,GAAG,CAAS;;;;;QAKhD,QAAQ;AACT,KAAA,CAAC,CAAC;AAEH,IAAA,MAAM,yBAAyB,GAAG,IAAI,GAAG,EAAU,CAAC;IAEpD,SAAS,CAAC,OAAO,CAAC,CAAC,EAAC,SAAS,EAAC,KAAI;AAChC,QAAA,MAAM,+BAA+B,GAAG,IAAI,GAAG,EAA2B,CAAC;AAC3E,QAAA,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAI;AAC7B,YAAA,MAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,MAAM,CAC1D,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,GAAG,CAAC,IAAI,CAAC,CACjD,CAAC;YACF,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,cAAc,EAAE;gBAC1C,IAAI,CAAC,MAAM,CAAC,+BAAgC,CAAC,IAAI,CAAC,EAAE;AAClD,oBAAA,IAAI,+BAA+B,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;wBACrF,MAAM,gBAAgB,GAAG,+BAA+B,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACnE,wBAAA,IAAI,gBAAgB,KAAK,KAAK,EAAE;AAC9B,4BAAA,yBAAyB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;yBACrC;qBACF;yBAAM;AACL,wBAAA,+BAA+B,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;qBAClD;iBACF;aACF;AACH,SAAC,CAAC,CAAC;AACL,KAAC,CAAC,CAAC;AAEH,IAAA,IAAI,yBAAyB,CAAC,IAAI,GAAG,CAAC,EAAE;AACtC,QAAA,OAAO,CAAC,IAAI,CACV,CAAA,gCAAA,EAAmC,WAAW,CAA0C,wCAAA,CAAA;YACtF,8BAA8B;YAC9B,KAAK,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;YAChD,IAAI;AACJ,YAAA,iIAAiI,CACpI,CAAC;KACH;AACH,CAAC;AAED,SAAS,yBAAyB,CAChC,QAA+B,EAC/B,YAAiB,EACjB,SAAc,EACd,OAAY,EACZ,MAA4B,EAAA;IAE5B,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;AAC7E,CAAC;AAED,SAAS,kBAAkB,CAAC,UAA+B,EAAE,QAA6B,EAAA;AACxF,IAAA,MAAM,MAAM,GAAwB,EAAC,GAAG,QAAQ,EAAC,CAAC;AAClD,IAAA,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAI;AAClD,QAAA,IAAI,KAAK,IAAI,IAAI,EAAE;AACjB,YAAA,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;SACrB;AACH,KAAC,CAAC,CAAC;AACH,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;MAEY,oBAAoB,CAAA;AAC/B,IAAA,WAAA,CACU,MAAgB,EAChB,aAAmC,EACnC,UAAoC,EAAA;QAFpC,IAAM,CAAA,MAAA,GAAN,MAAM,CAAU;QAChB,IAAa,CAAA,aAAA,GAAb,aAAa,CAAsB;QACnC,IAAU,CAAA,UAAA,GAAV,UAAU,CAA0B;KAC1C;IAEJ,WAAW,CAAC,MAA4B,EAAE,MAAe,EAAA;AACvD,QAAA,MAAM,WAAW,GAAkB,IAAI,GAAG,EAAE,CAAC;QAC7C,MAAM,cAAc,GAAG,kBAAkB,CAAC,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QACtE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;AACnC,YAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC7B,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,KAAI;oBAC1B,IAAI,GAAG,EAAE;wBACP,GAAG,GAAG,iBAAiB,CAAC,GAAG,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;qBACtD;AACD,oBAAA,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAC3E,oBAAA,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,IAAI,EAAE,cAAc,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;AAC7E,oBAAA,WAAW,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAC7B,iBAAC,CAAC,CAAC;aACJ;AACH,SAAC,CAAC,CAAC;AACH,QAAA,OAAO,WAAW,CAAC;KACpB;AACF;;SCvPe,YAAY,CAC1B,IAAY,EACZ,GAAe,EACf,UAAoC,EAAA;IAEpC,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;AACrD,CAAC;MAEY,gBAAgB,CAAA;AAK3B,IAAA,WAAA,CACS,IAAY,EACZ,GAAe,EACd,WAAqC,EAAA;QAFtC,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;QACZ,IAAG,CAAA,GAAA,GAAH,GAAG,CAAY;QACd,IAAW,CAAA,WAAA,GAAX,WAAW,CAA0B;QAPxC,IAAmB,CAAA,mBAAA,GAAiC,EAAE,CAAC;AAEvD,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,GAAG,EAAgC,CAAC;QAOtD,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,KAAI;AACzB,YAAA,MAAM,aAAa,GAAG,CAAC,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,KAAK,EAAE,CAAC;YAChE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,oBAAoB,CAAC,GAAG,CAAC,KAAK,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC;AAC7F,SAAC,CAAC,CAAC;QAEH,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;QAC5C,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;QAE7C,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,KAAI;AAC9B,YAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,0BAA0B,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AACxF,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,kBAAkB,GAAG,wBAAwB,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;KACzF;AAED,IAAA,IAAI,eAAe,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC;KAChC;AAED,IAAA,eAAe,CACb,YAAiB,EACjB,SAAc,EACd,OAAY,EACZ,MAA4B,EAAA;QAE5B,MAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,KAC5C,CAAC,CAAC,KAAK,CAAC,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC,CAClD,CAAC;QACF,OAAO,KAAK,IAAI,IAAI,CAAC;KACtB;AAED,IAAA,WAAW,CAAC,YAAiB,EAAE,MAA4B,EAAE,MAAe,EAAA;AAC1E,QAAA,OAAO,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,YAAY,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;KAC1E;AACF,CAAA;AAED,SAAS,wBAAwB,CAC/B,WAAmB,EACnB,MAAyC,EACzC,UAAoC,EAAA;AAEpC,IAAA,MAAM,QAAQ,GAAG,CAAC,CAAC,SAAc,EAAE,OAAY,KAAK,IAAI,CAAC,CAAC;AAC1D,IAAA,MAAM,SAAS,GAAgB,EAAC,IAAI,EAAE,qBAAqB,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;AAChG,IAAA,MAAM,UAAU,GAAkB;QAChC,IAAI,EAAE,qBAAqB,CAAC,UAAU;QACtC,SAAS;QACT,QAAQ;AACR,QAAA,OAAO,EAAE,IAAI;AACb,QAAA,UAAU,EAAE,CAAC;AACb,QAAA,QAAQ,EAAE,CAAC;KACZ,CAAC;IACF,OAAO,IAAI,0BAA0B,CAAC,WAAW,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;AACzE,CAAC;AAED,SAAS,iBAAiB,CACxB,QAA2C,EAC3C,IAAY,EACZ,IAAY,EAAA;AAEZ,IAAA,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;QACtB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AACvB,YAAA,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC,CAAC;SACzC;KACF;AAAM,SAAA,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAC7B,QAAA,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC,CAAC;KACzC;AACH;;ACxDA,MAAM,qBAAqB,GAAG,IAAI,qBAAqB,EAAE,CAAC;MAE7C,uBAAuB,CAAA;AAKlC,IAAA,WAAA,CACS,QAAa,EACZ,OAAwB,EACxB,WAAqC,EAAA;QAFtC,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAK;QACZ,IAAO,CAAA,OAAA,GAAP,OAAO,CAAiB;QACxB,IAAW,CAAA,WAAA,GAAX,WAAW,CAA0B;AAPvC,QAAA,IAAA,CAAA,WAAW,GAAG,IAAI,GAAG,EAAsC,CAAC;AAC5D,QAAA,IAAA,CAAA,YAAY,GAAG,IAAI,GAAG,EAA2B,CAAC;QACnD,IAAO,CAAA,OAAA,GAAsB,EAAE,CAAC;KAMnC;IAEJ,QAAQ,CAAC,EAAU,EAAE,QAAiD,EAAA;QACpE,MAAM,MAAM,GAAY,EAAE,CAAC;QAC3B,MAAM,QAAQ,GAAa,EAAE,CAAC;AAC9B,QAAA,MAAM,GAAG,GAAG,iBAAiB,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;AACxE,QAAA,IAAI,MAAM,CAAC,MAAM,EAAE;AACjB,YAAA,MAAM,cAAc,CAAC,MAAM,CAAC,CAAC;SAC9B;aAAM;AACL,YAAA,IAAI,QAAQ,CAAC,MAAM,EAAE;gBACnB,YAAY,CAAC,QAAQ,CAAC,CAAC;aACxB;YACD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;SAC/B;KACF;AAEO,IAAA,YAAY,CAClB,CAA+B,EAC/B,SAAwB,EACxB,UAA0B,EAAA;AAE1B,QAAA,MAAM,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;AAC1B,QAAA,MAAM,SAAS,GAAGD,oBAAkB,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;QAC3F,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;KAC1F;AAED,IAAA,MAAM,CAAC,EAAU,EAAE,OAAY,EAAE,UAA4B,EAAE,EAAA;QAC7D,MAAM,MAAM,GAAY,EAAE,CAAC;QAC3B,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AACrC,QAAA,IAAI,YAA4C,CAAC;AAEjD,QAAA,MAAM,aAAa,GAAG,IAAI,GAAG,EAAsB,CAAC;QAEpD,IAAI,GAAG,EAAE;AACP,YAAA,YAAY,GAAG,uBAAuB,CACpC,IAAI,CAAC,OAAO,EACZ,OAAO,EACP,GAAG,EACH,eAAe,EACf,eAAe,EACf,IAAI,GAAG,EAAE,EACT,IAAI,GAAG,EAAE,EACT,OAAO,EACP,qBAAqB,EACrB,MAAM,CACP,CAAC;AACF,YAAA,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC5B,gBAAA,MAAM,MAAM,GAAG,oBAAoB,CACjC,aAAa,EACb,IAAI,CAAC,OAAO,EACZ,IAAI,GAAG,EAAkC,CAC1C,CAAC;AACF,gBAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AAChE,aAAC,CAAC,CAAC;SACJ;aAAM;AACL,YAAA,MAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE,CAAC,CAAC;YAC3C,YAAY,GAAG,EAAE,CAAC;SACnB;AAED,QAAA,IAAI,MAAM,CAAC,MAAM,EAAE;AACjB,YAAA,MAAM,qBAAqB,CAAC,MAAM,CAAC,CAAC;SACrC;QAED,aAAa,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,OAAO,KAAI;YACxC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,KAAI;AACzB,gBAAA,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;AACzE,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;QAEH,MAAM,OAAO,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,KAAI;YACrC,MAAM,MAAM,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AAC5C,YAAA,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,GAAG,EAAE,EAAE,MAAM,CAAC,CAAC;AACjD,SAAC,CAAC,CAAC;AACH,QAAA,MAAM,MAAM,GAAG,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAC5C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;AAClC,QAAA,MAAM,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;AAEzC,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC1B,QAAA,OAAO,MAAM,CAAC;KACf;AAED,IAAA,OAAO,CAAC,EAAU,EAAA;QAChB,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QACnC,MAAM,CAAC,OAAO,EAAE,CAAC;AACjB,QAAA,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC3C,QAAA,IAAI,KAAK,IAAI,CAAC,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAC/B;KACF;AAEO,IAAA,UAAU,CAAC,EAAU,EAAA;QAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACzC,IAAI,CAAC,MAAM,EAAE;AACX,YAAA,MAAM,aAAa,CAAC,EAAE,CAAC,CAAC;SACzB;AACD,QAAA,OAAO,MAAM,CAAC;KACf;AAED,IAAA,MAAM,CACJ,EAAU,EACV,OAAe,EACf,SAAiB,EACjB,QAA6B,EAAA;;AAG7B,QAAA,MAAM,SAAS,GAAG,kBAAkB,CAAC,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AAC1D,QAAA,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;AACpE,QAAA,OAAO,MAAO,GAAC,CAAC;KACjB;AAED,IAAA,OAAO,CAAC,EAAU,EAAE,OAAY,EAAE,OAAe,EAAE,IAAW,EAAA;AAC5D,QAAA,IAAI,OAAO,IAAI,UAAU,EAAE;YACzB,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAA4C,CAAC,CAAC;YACtE,OAAO;SACR;AAED,QAAA,IAAI,OAAO,IAAI,QAAQ,EAAE;YACvB,MAAM,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAqB,CAAC;YACpD,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YAClC,OAAO;SACR;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QACnC,QAAQ,OAAO;AACb,YAAA,KAAK,MAAM;gBACT,MAAM,CAAC,IAAI,EAAE,CAAC;gBACd,MAAM;AACR,YAAA,KAAK,OAAO;gBACV,MAAM,CAAC,KAAK,EAAE,CAAC;gBACf,MAAM;AACR,YAAA,KAAK,OAAO;gBACV,MAAM,CAAC,KAAK,EAAE,CAAC;gBACf,MAAM;AACR,YAAA,KAAK,SAAS;gBACZ,MAAM,CAAC,OAAO,EAAE,CAAC;gBACjB,MAAM;AACR,YAAA,KAAK,QAAQ;gBACX,MAAM,CAAC,MAAM,EAAE,CAAC;gBAChB,MAAM;AACR,YAAA,KAAK,MAAM;gBACT,MAAM,CAAC,IAAI,EAAE,CAAC;gBACd,MAAM;AACR,YAAA,KAAK,aAAa;gBAChB,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAW,CAAC,CAAC,CAAC;gBAClD,MAAM;AACR,YAAA,KAAK,SAAS;AACZ,gBAAA,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBACjB,MAAM;SACT;KACF;AACF;;ACnJD,MAAM,gBAAgB,GAAG,mBAAmB,CAAC;AAC7C,MAAM,eAAe,GAAG,oBAAoB,CAAC;AAC7C,MAAM,kBAAkB,GAAG,qBAAqB,CAAC;AACjD,MAAM,iBAAiB,GAAG,sBAAsB,CAAC;AACjD,MAAM,cAAc,GAAG,kBAAkB,CAAC;AAC1C,MAAM,aAAa,GAAG,mBAAmB,CAAC;AAE1C,MAAM,kBAAkB,GAAgC,EAAE,CAAC;AAC3D,MAAM,kBAAkB,GAA0B;AAChD,IAAA,WAAW,EAAE,EAAE;AACf,IAAA,aAAa,EAAE,KAAK;AACpB,IAAA,UAAU,EAAE,KAAK;AACjB,IAAA,YAAY,EAAE,KAAK;AACnB,IAAA,oBAAoB,EAAE,KAAK;CAC5B,CAAC;AACF,MAAM,0BAA0B,GAA0B;AACxD,IAAA,WAAW,EAAE,EAAE;AACf,IAAA,UAAU,EAAE,KAAK;AACjB,IAAA,aAAa,EAAE,KAAK;AACpB,IAAA,YAAY,EAAE,KAAK;AACnB,IAAA,oBAAoB,EAAE,IAAI;CAC3B,CAAC;AAkBF,MAAM,YAAY,GAAG,cAAc,CAAC;AAWpC,MAAM,UAAU,CAAA;AAId,IAAA,IAAI,MAAM,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,MAA8B,CAAC;KACpD;IAED,WACE,CAAA,KAAU,EACH,WAAA,GAAsB,EAAE,EAAA;QAAxB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAa;QAE/B,MAAM,KAAK,GAAG,KAAK,IAAI,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;AACrD,QAAA,MAAM,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC7C,QAAA,IAAI,CAAC,KAAK,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;QAC1C,IAAI,KAAK,EAAE;;YAET,MAAM,EAAC,KAAK,EAAE,GAAG,OAAO,EAAC,GAAG,KAAK,CAAC;AAClC,YAAA,IAAI,CAAC,OAAO,GAAG,OAA2B,CAAC;SAC5C;aAAM;AACL,YAAA,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;SACnB;AACD,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;AACxB,YAAA,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC;SAC1B;KACF;AAED,IAAA,aAAa,CAAC,OAAyB,EAAA;AACrC,QAAA,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC;QACjC,IAAI,SAAS,EAAE;AACb,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAO,CAAC;YACvC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AACtC,gBAAA,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE;oBAC3B,SAAS,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;iBACnC;AACH,aAAC,CAAC,CAAC;SACJ;KACF;AACF,CAAA;AAED,MAAM,UAAU,GAAG,MAAM,CAAC;AAC1B,MAAM,mBAAmB,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;AAEvD,MAAM,4BAA4B,CAAA;AAUhC,IAAA,WAAA,CACS,EAAU,EACV,WAAgB,EACf,OAAkC,EAAA;QAFnC,IAAE,CAAA,EAAA,GAAF,EAAE,CAAQ;QACV,IAAW,CAAA,WAAA,GAAX,WAAW,CAAK;QACf,IAAO,CAAA,OAAA,GAAP,OAAO,CAA2B;QAZrC,IAAO,CAAA,OAAA,GAAgC,EAAE,CAAC;AAEzC,QAAA,IAAA,CAAA,SAAS,GAAG,IAAI,GAAG,EAA4B,CAAC;QAChD,IAAM,CAAA,MAAA,GAAuB,EAAE,CAAC;AAEhC,QAAA,IAAA,CAAA,iBAAiB,GAAG,IAAI,GAAG,EAA0B,CAAC;AAS5D,QAAA,IAAI,CAAC,cAAc,GAAG,SAAS,GAAG,EAAE,CAAC;AACrC,QAAA,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;KAC5C;AAED,IAAA,MAAM,CAAC,OAAY,EAAE,IAAY,EAAE,KAAa,EAAE,QAAiC,EAAA;QACjF,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAC7B,YAAA,MAAM,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;SACnC;QAED,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;AACtC,YAAA,MAAM,YAAY,CAAC,IAAI,CAAC,CAAC;SAC1B;AAED,QAAA,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE;AAC/B,YAAA,MAAM,uBAAuB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;SAC5C;AAED,QAAA,MAAM,SAAS,GAAG,oBAAoB,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;QAC5E,MAAM,IAAI,GAAG,EAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAC,CAAC;AACrC,QAAA,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAErB,QAAA,MAAM,kBAAkB,GAAG,oBAAoB,CAC7C,IAAI,CAAC,OAAO,CAAC,eAAe,EAC5B,OAAO,EACP,IAAI,GAAG,EAAsB,CAC9B,CAAC;QACF,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AACjC,YAAA,QAAQ,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;YACxC,QAAQ,CAAC,OAAO,EAAE,oBAAoB,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC;AACrD,YAAA,kBAAkB,CAAC,GAAG,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;SACnD;AAED,QAAA,OAAO,MAAK;;;;AAIV,YAAA,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,MAAK;gBAC3B,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACtC,gBAAA,IAAI,KAAK,IAAI,CAAC,EAAE;AACd,oBAAA,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;iBAC5B;gBAED,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAC7B,oBAAA,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;iBACjC;AACH,aAAC,CAAC,CAAC;AACL,SAAC,CAAC;KACH;IAED,QAAQ,CAAC,IAAY,EAAE,GAAqB,EAAA;QAC1C,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;;AAE5B,YAAA,OAAO,KAAK,CAAC;SACd;aAAM;YACL,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAC9B,YAAA,OAAO,IAAI,CAAC;SACb;KACF;AAEO,IAAA,WAAW,CAAC,IAAY,EAAA;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACzC,IAAI,CAAC,OAAO,EAAE;AACZ,YAAA,MAAM,mBAAmB,CAAC,IAAI,CAAC,CAAC;SACjC;AACD,QAAA,OAAO,OAAO,CAAC;KAChB;IAED,OAAO,CACL,OAAY,EACZ,WAAmB,EACnB,KAAU,EACV,oBAA6B,IAAI,EAAA;QAEjC,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AAC9C,QAAA,MAAM,MAAM,GAAG,IAAI,yBAAyB,CAAC,IAAI,CAAC,EAAE,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;AAE5E,QAAA,IAAI,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACnE,IAAI,CAAC,kBAAkB,EAAE;AACvB,YAAA,QAAQ,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;YACxC,QAAQ,CAAC,OAAO,EAAE,oBAAoB,GAAG,GAAG,GAAG,WAAW,CAAC,CAAC;AAC5D,YAAA,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,CAC9B,OAAO,GACN,kBAAkB,GAAG,IAAI,GAAG,EAAsB,EACpD,CAAC;SACH;QAED,IAAI,SAAS,GAAG,kBAAkB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACpD,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QAC/C,MAAM,KAAK,GAAG,KAAK,IAAI,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;AACrD,QAAA,IAAI,CAAC,KAAK,IAAI,SAAS,EAAE;AACvB,YAAA,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;SAC1C;AAED,QAAA,kBAAkB,CAAC,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;QAE7C,IAAI,CAAC,SAAS,EAAE;YACd,SAAS,GAAG,mBAAmB,CAAC;SACjC;AAED,QAAA,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,KAAK,UAAU,CAAC;;;;;;;QAQ/C,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,KAAK,KAAK,OAAO,CAAC,KAAK,EAAE;;;AAGnD,YAAA,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE;gBAChD,MAAM,MAAM,GAAY,EAAE,CAAC;AAC3B,gBAAA,MAAM,UAAU,GAAG,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAClF,gBAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC5E,gBAAA,IAAI,MAAM,CAAC,MAAM,EAAE;AACjB,oBAAA,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;iBAClC;qBAAM;AACL,oBAAA,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,MAAK;AAC3B,wBAAA,WAAW,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;AACjC,wBAAA,SAAS,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AAC/B,qBAAC,CAAC,CAAC;iBACJ;aACF;YACD,OAAO;SACR;AAED,QAAA,MAAM,gBAAgB,GAAgC,oBAAoB,CACxE,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAC7B,OAAO,EACP,EAAE,CACH,CAAC;AACF,QAAA,gBAAgB,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;;;;;AAKlC,YAAA,IAAI,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,EAAE,IAAI,MAAM,CAAC,WAAW,IAAI,WAAW,IAAI,MAAM,CAAC,MAAM,EAAE;gBACvF,MAAM,CAAC,OAAO,EAAE,CAAC;aAClB;AACH,SAAC,CAAC,CAAC;QAEH,IAAI,UAAU,GAAG,OAAO,CAAC,eAAe,CACtC,SAAS,CAAC,KAAK,EACf,OAAO,CAAC,KAAK,EACb,OAAO,EACP,OAAO,CAAC,MAAM,CACf,CAAC;QACF,IAAI,oBAAoB,GAAG,KAAK,CAAC;QACjC,IAAI,CAAC,UAAU,EAAE;AACf,YAAA,IAAI,CAAC,iBAAiB;gBAAE,OAAO;AAC/B,YAAA,UAAU,GAAG,OAAO,CAAC,kBAAkB,CAAC;YACxC,oBAAoB,GAAG,IAAI,CAAC;SAC7B;AAED,QAAA,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;AAClC,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACf,OAAO;YACP,WAAW;YACX,UAAU;YACV,SAAS;YACT,OAAO;YACP,MAAM;YACN,oBAAoB;AACrB,SAAA,CAAC,CAAC;QAEH,IAAI,CAAC,oBAAoB,EAAE;AACzB,YAAA,QAAQ,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;AACpC,YAAA,MAAM,CAAC,OAAO,CAAC,MAAK;AAClB,gBAAA,WAAW,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;AACzC,aAAC,CAAC,CAAC;SACJ;AAED,QAAA,MAAM,CAAC,MAAM,CAAC,MAAK;YACjB,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACzC,YAAA,IAAI,KAAK,IAAI,CAAC,EAAE;gBACd,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aAC/B;AAED,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC3D,IAAI,OAAO,EAAE;gBACX,IAAI,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACpC,gBAAA,IAAI,KAAK,IAAI,CAAC,EAAE;AACd,oBAAA,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;iBAC1B;aACF;AACH,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC1B,QAAA,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAE9B,QAAA,OAAO,MAAM,CAAC;KACf;AAED,IAAA,UAAU,CAAC,IAAY,EAAA;AACrB,QAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAE5B,QAAA,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;QAE1E,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,OAAO,KAAI;AACpD,YAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CACxB,OAAO,EACP,SAAS,CAAC,MAAM,CAAC,CAAC,KAAK,KAAI;AACzB,gBAAA,OAAO,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC;aAC3B,CAAC,CACH,CAAC;AACJ,SAAC,CAAC,CAAC;KACJ;AAED,IAAA,iBAAiB,CAAC,OAAY,EAAA;QAC5B,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC7C,QAAA,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACvC,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAClE,IAAI,cAAc,EAAE;AAClB,YAAA,cAAc,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;YACrD,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;SAC/C;KACF;IAEO,8BAA8B,CAAC,WAAgB,EAAE,OAAY,EAAA;AACnE,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,mBAAmB,EAAE,IAAI,CAAC,CAAC;;;;AAKnF,QAAA,QAAQ,CAAC,OAAO,CAAC,CAAC,GAAG,KAAI;;;YAGvB,IAAI,GAAG,CAAC,YAAY,CAAC;gBAAE,OAAO;YAE9B,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;AAC9D,YAAA,IAAI,UAAU,CAAC,IAAI,EAAE;gBACnB,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,qBAAqB,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;aACjF;iBAAM;AACL,gBAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;aAC7B;AACH,SAAC,CAAC,CAAC;;;QAIH,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,MACpC,QAAQ,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CACvD,CAAC;KACH;AAED,IAAA,qBAAqB,CACnB,OAAY,EACZ,OAAY,EACZ,oBAA8B,EAC9B,iBAA2B,EAAA;AAE3B,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AAChE,QAAA,MAAM,sBAAsB,GAAG,IAAI,GAAG,EAAkB,CAAC;QACzD,IAAI,aAAa,EAAE;YACjB,MAAM,OAAO,GAAgC,EAAE,CAAC;YAChD,aAAa,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,WAAW,KAAI;gBAC3C,sBAAsB,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;;;gBAGrD,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;AACnC,oBAAA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,iBAAiB,CAAC,CAAC;oBACjF,IAAI,MAAM,EAAE;AACV,wBAAA,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;qBACtB;iBACF;AACH,aAAC,CAAC,CAAC;AAEH,YAAA,IAAI,OAAO,CAAC,MAAM,EAAE;AAClB,gBAAA,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,sBAAsB,CAAC,CAAC;gBAC3F,IAAI,oBAAoB,EAAE;AACxB,oBAAA,mBAAmB,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;iBACnF;AACD,gBAAA,OAAO,IAAI,CAAC;aACb;SACF;AACD,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,8BAA8B,CAAC,OAAY,EAAA;QACzC,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AACtD,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;;;AAIhE,QAAA,IAAI,SAAS,IAAI,aAAa,EAAE;AAC9B,YAAA,MAAM,eAAe,GAAG,IAAI,GAAG,EAAU,CAAC;AAC1C,YAAA,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAI;AAC7B,gBAAA,MAAM,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC;AAClC,gBAAA,IAAI,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC;oBAAE,OAAO;AAC7C,gBAAA,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBAEjC,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAE,CAAC;AACjD,gBAAA,MAAM,UAAU,GAAG,OAAO,CAAC,kBAAkB,CAAC;gBAC9C,MAAM,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,mBAAmB,CAAC;AACxE,gBAAA,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;AAC3C,gBAAA,MAAM,MAAM,GAAG,IAAI,yBAAyB,CAAC,IAAI,CAAC,EAAE,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;AAE5E,gBAAA,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;AAClC,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;oBACf,OAAO;oBACP,WAAW;oBACX,UAAU;oBACV,SAAS;oBACT,OAAO;oBACP,MAAM;AACN,oBAAA,oBAAoB,EAAE,IAAI;AAC3B,iBAAA,CAAC,CAAC;AACL,aAAC,CAAC,CAAC;SACJ;KACF;IAED,UAAU,CAAC,OAAY,EAAE,OAAY,EAAA;AACnC,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5B,QAAA,IAAI,OAAO,CAAC,iBAAiB,EAAE;AAC7B,YAAA,IAAI,CAAC,8BAA8B,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;SACvD;;QAGD,IAAI,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC;YAAE,OAAO;;;QAI/D,IAAI,iCAAiC,GAAG,KAAK,CAAC;AAC9C,QAAA,IAAI,MAAM,CAAC,eAAe,EAAE;AAC1B,YAAA,MAAM,cAAc,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM;kBACxC,MAAM,CAAC,uBAAuB,CAAC,GAAG,CAAC,OAAO,CAAC;kBAC3C,EAAE,CAAC;;;;;AAMP,YAAA,IAAI,cAAc,IAAI,cAAc,CAAC,MAAM,EAAE;gBAC3C,iCAAiC,GAAG,IAAI,CAAC;aAC1C;iBAAM;gBACL,IAAI,MAAM,GAAG,OAAO,CAAC;gBACrB,QAAQ,MAAM,GAAG,MAAM,CAAC,UAAU,GAAG;oBACnC,MAAM,QAAQ,GAAG,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBACpD,IAAI,QAAQ,EAAE;wBACZ,iCAAiC,GAAG,IAAI,CAAC;wBACzC,MAAM;qBACP;iBACF;aACF;SACF;;;;;AAMD,QAAA,IAAI,CAAC,8BAA8B,CAAC,OAAO,CAAC,CAAC;;;QAI7C,IAAI,iCAAiC,EAAE;AACrC,YAAA,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SAC/D;aAAM;AACL,YAAA,MAAM,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;AAC1C,YAAA,IAAI,CAAC,WAAW,IAAI,WAAW,KAAK,kBAAkB,EAAE;;;AAGtD,gBAAA,MAAM,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC;AACzD,gBAAA,MAAM,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;AACvC,gBAAA,MAAM,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;aAC7C;SACF;KACF;IAED,UAAU,CAAC,OAAY,EAAE,MAAW,EAAA;AAClC,QAAA,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;KACxC;AAED,IAAA,sBAAsB,CAAC,WAAmB,EAAA;QACxC,MAAM,YAAY,GAAuB,EAAE,CAAC;QAC5C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;AAC5B,YAAA,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YAC5B,IAAI,MAAM,CAAC,SAAS;gBAAE,OAAO;AAE7B,YAAA,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;YAC9B,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACtD,IAAI,SAAS,EAAE;AACb,gBAAA,SAAS,CAAC,OAAO,CAAC,CAAC,QAAyB,KAAI;oBAC9C,IAAI,QAAQ,CAAC,IAAI,IAAI,KAAK,CAAC,WAAW,EAAE;wBACtC,MAAM,SAAS,GAAG,kBAAkB,CAClC,OAAO,EACP,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,SAAS,CAAC,KAAK,EACrB,KAAK,CAAC,OAAO,CAAC,KAAK,CACpB,CAAC;AACD,wBAAA,SAAiB,CAAC,OAAO,CAAC,GAAG,WAAW,CAAC;AAC1C,wBAAA,cAAc,CAAC,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK,EAAE,SAAS,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;qBAC5E;AACH,iBAAC,CAAC,CAAC;aACJ;AAED,YAAA,IAAI,MAAM,CAAC,gBAAgB,EAAE;AAC3B,gBAAA,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,MAAK;;;oBAG3B,MAAM,CAAC,OAAO,EAAE,CAAC;AACnB,iBAAC,CAAC,CAAC;aACJ;iBAAM;AACL,gBAAA,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAC1B;AACH,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QAEjB,OAAO,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAI;;;YAGhC,MAAM,EAAE,GAAG,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC;YACrC,MAAM,EAAE,GAAG,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC;YACrC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;gBACtB,OAAO,EAAE,GAAG,EAAE,CAAC;aAChB;YACD,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5E,SAAC,CAAC,CAAC;KACJ;AAED,IAAA,OAAO,CAAC,OAAY,EAAA;AAClB,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QACzC,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;KAChE;AACF,CAAA;MAQY,yBAAyB,CAAA;;IAwBpC,kBAAkB,CAAC,OAAY,EAAE,OAAY,EAAA;AAC3C,QAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;KAC1C;AAED,IAAA,WAAA,CACS,QAAa,EACb,MAAuB,EACtB,WAAqC,EAC5B,SAA0C,EAAA;QAHpD,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAK;QACb,IAAM,CAAA,MAAA,GAAN,MAAM,CAAiB;QACtB,IAAW,CAAA,WAAA,GAAX,WAAW,CAA0B;QAC5B,IAAS,CAAA,SAAA,GAAT,SAAS,CAAiC;QA/BtD,IAAO,CAAA,OAAA,GAAgC,EAAE,CAAC;AAC1C,QAAA,IAAA,CAAA,eAAe,GAAG,IAAI,GAAG,EAAqC,CAAC;AAC/D,QAAA,IAAA,CAAA,gBAAgB,GAAG,IAAI,GAAG,EAAoC,CAAC;AAC/D,QAAA,IAAA,CAAA,uBAAuB,GAAG,IAAI,GAAG,EAAoC,CAAC;AACtE,QAAA,IAAA,CAAA,eAAe,GAAG,IAAI,GAAG,EAAgC,CAAC;AAC1D,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,GAAG,EAAO,CAAC;QAE/B,IAAe,CAAA,eAAA,GAAG,CAAC,CAAC;QACpB,IAAkB,CAAA,kBAAA,GAAG,CAAC,CAAC;QAEtB,IAAgB,CAAA,gBAAA,GAAiD,EAAE,CAAC;QACpE,IAAc,CAAA,cAAA,GAAmC,EAAE,CAAC;QACpD,IAAS,CAAA,SAAA,GAAkB,EAAE,CAAC;QAC9B,IAAa,CAAA,aAAA,GAAkB,EAAE,CAAC;AAEnC,QAAA,IAAA,CAAA,uBAAuB,GAAG,IAAI,GAAG,EAAqC,CAAC;QACvE,IAAsB,CAAA,sBAAA,GAAU,EAAE,CAAC;QACnC,IAAsB,CAAA,sBAAA,GAAU,EAAE,CAAC;;QAGnC,IAAiB,CAAA,iBAAA,GAAG,CAAC,OAAY,EAAE,OAAY,KAAM,GAAC,CAAC;KAY1D;AAEJ,IAAA,IAAI,aAAa,GAAA;QACf,MAAM,OAAO,GAAgC,EAAE,CAAC;QAChD,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,EAAE,KAAI;YACjC,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;AAC5B,gBAAA,IAAI,MAAM,CAAC,MAAM,EAAE;AACjB,oBAAA,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;iBACtB;AACH,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;AACH,QAAA,OAAO,OAAO,CAAC;KAChB;IAED,eAAe,CAAC,WAAmB,EAAE,WAAgB,EAAA;QACnD,MAAM,EAAE,GAAG,IAAI,4BAA4B,CAAC,WAAW,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;AAC5E,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,EAAE;AAC5E,YAAA,IAAI,CAAC,qBAAqB,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;SAC7C;aAAM;;;;YAIL,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;;;;;;AAO1C,YAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;SACvC;QACD,QAAQ,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,GAAG,EAAE,EAAE;KAClD;IAEO,qBAAqB,CAAC,EAAgC,EAAE,WAAgB,EAAA;AAC9E,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;AAC1C,QAAA,MAAM,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,CAAC;AAC7D,QAAA,MAAM,KAAK,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;AACvC,QAAA,IAAI,KAAK,IAAI,CAAC,EAAE;YACd,IAAI,KAAK,GAAG,KAAK,CAAC;;;YAGlB,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;YACzD,OAAO,QAAQ,EAAE;gBACf,MAAM,UAAU,GAAG,uBAAuB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBACzD,IAAI,UAAU,EAAE;;;oBAGd,MAAM,KAAK,GAAG,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;oBAChD,aAAa,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;oBACvC,KAAK,GAAG,IAAI,CAAC;oBACb,MAAM;iBACP;gBACD,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;aACnD;YACD,IAAI,CAAC,KAAK,EAAE;;;;AAIV,gBAAA,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;aAC3B;SACF;aAAM;AACL,YAAA,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SACxB;AAED,QAAA,uBAAuB,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;AAC7C,QAAA,OAAO,EAAE,CAAC;KACX;IAED,QAAQ,CAAC,WAAmB,EAAE,WAAgB,EAAA;QAC5C,IAAI,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QAC5C,IAAI,CAAC,EAAE,EAAE;YACP,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;SACrD;AACD,QAAA,OAAO,EAAE,CAAC;KACX;AAED,IAAA,eAAe,CAAC,WAAmB,EAAE,IAAY,EAAE,OAAyB,EAAA;QAC1E,IAAI,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QAC5C,IAAI,EAAE,IAAI,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;YACpC,IAAI,CAAC,eAAe,EAAE,CAAC;SACxB;KACF;IAED,OAAO,CAAC,WAAmB,EAAE,OAAY,EAAA;AACvC,QAAA,IAAI,CAAC,WAAW;YAAE,OAAO;QACzB,IAAI,CAAC,UAAU,CAAC,MAAO,GAAC,CAAC,CAAC;AAE1B,QAAA,IAAI,CAAC,wBAAwB,CAAC,MAAK;YACjC,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAC7C,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC;YACpD,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AAC9C,YAAA,IAAI,KAAK,IAAI,CAAC,EAAE;gBACd,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aACtC;AACD,YAAA,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACpB,YAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;AAC5C,SAAC,CAAC,CAAC;KACJ;AAEO,IAAA,eAAe,CAAC,EAAU,EAAA;AAChC,QAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;KAClC;AAED,IAAA,wBAAwB,CAAC,OAAY,EAAA;;;;;;AAMnC,QAAA,MAAM,UAAU,GAAG,IAAI,GAAG,EAAgC,CAAC;QAC3D,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACxD,IAAI,aAAa,EAAE;YACjB,KAAK,IAAI,UAAU,IAAI,aAAa,CAAC,MAAM,EAAE,EAAE;AAC7C,gBAAA,IAAI,UAAU,CAAC,WAAW,EAAE;oBAC1B,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;oBACxD,IAAI,EAAE,EAAE;AACN,wBAAA,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;qBACpB;iBACF;aACF;SACF;AACD,QAAA,OAAO,UAAU,CAAC;KACnB;AAED,IAAA,OAAO,CAAC,WAAmB,EAAE,OAAY,EAAE,IAAY,EAAE,KAAU,EAAA;AACjE,QAAA,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE;YAC1B,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAC7C,IAAI,EAAE,EAAE;gBACN,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AACjC,gBAAA,OAAO,IAAI,CAAC;aACb;SACF;AACD,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,UAAU,CAAC,WAAmB,EAAE,OAAY,EAAE,MAAW,EAAE,YAAqB,EAAA;AAC9E,QAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;YAAE,OAAO;;;AAIpC,QAAA,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAA0B,CAAC;AAC/D,QAAA,IAAI,OAAO,IAAI,OAAO,CAAC,aAAa,EAAE;AACpC,YAAA,OAAO,CAAC,aAAa,GAAG,KAAK,CAAC;AAC9B,YAAA,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC;YAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC3D,YAAA,IAAI,KAAK,IAAI,CAAC,EAAE;gBACd,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aAC9C;SACF;;;;QAKD,IAAI,WAAW,EAAE;YACf,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;;;;;;;YAO7C,IAAI,EAAE,EAAE;AACN,gBAAA,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;aAChC;SACF;;QAGD,IAAI,YAAY,EAAE;AAChB,YAAA,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;SACnC;KACF;AAED,IAAA,mBAAmB,CAAC,OAAY,EAAA;AAC9B,QAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KAC3C;IAED,qBAAqB,CAAC,OAAY,EAAE,KAAc,EAAA;QAChD,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;AACpC,gBAAA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AAChC,gBAAA,QAAQ,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;aACvC;SACF;aAAM,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;AAC1C,YAAA,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACnC,YAAA,WAAW,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;SAC1C;KACF;AAED,IAAA,UAAU,CAAC,WAAmB,EAAE,OAAY,EAAE,OAAY,EAAA;AACxD,QAAA,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE;AAC1B,YAAA,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,CAAC;AACzB,YAAA,MAAM,EAAE,GAAG,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;YAClE,IAAI,EAAE,EAAE;AACN,gBAAA,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;aACjC;iBAAM;gBACL,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;aACjE;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACzD,IAAI,MAAM,IAAI,MAAM,CAAC,EAAE,KAAK,WAAW,EAAE;AACvC,gBAAA,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;aACrC;SACF;aAAM;AACL,YAAA,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;SAC3C;KACF;IAED,oBAAoB,CAClB,WAAmB,EACnB,OAAY,EACZ,YAAsB,EACtB,OAAa,EACb,sBAA4C,EAAA;AAE5C,QAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1C,OAAO,CAAC,YAAY,CAAC,GAAG;YACtB,WAAW;AACX,YAAA,aAAa,EAAE,OAAO;YACtB,YAAY;AACZ,YAAA,oBAAoB,EAAE,KAAK;YAC3B,sBAAsB;SACvB,CAAC;KACH;IAED,MAAM,CACJ,WAAmB,EACnB,OAAY,EACZ,IAAY,EACZ,KAAa,EACb,QAAiC,EAAA;AAEjC,QAAA,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE;AAC1B,YAAA,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;SACjF;AACD,QAAA,OAAO,MAAO,GAAC,CAAC;KACjB;IAEO,iBAAiB,CACvB,KAAuB,EACvB,YAAmC,EACnC,cAAsB,EACtB,cAAsB,EACtB,YAAsB,EAAA;QAEtB,OAAO,KAAK,CAAC,UAAU,CAAC,KAAK,CAC3B,IAAI,CAAC,MAAM,EACX,KAAK,CAAC,OAAO,EACb,KAAK,CAAC,SAAS,CAAC,KAAK,EACrB,KAAK,CAAC,OAAO,CAAC,KAAK,EACnB,cAAc,EACd,cAAc,EACd,KAAK,CAAC,SAAS,CAAC,OAAO,EACvB,KAAK,CAAC,OAAO,CAAC,OAAO,EACrB,YAAY,EACZ,YAAY,CACb,CAAC;KACH;AAED,IAAA,sBAAsB,CAAC,gBAAqB,EAAA;AAC1C,QAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,mBAAmB,EAAE,IAAI,CAAC,CAAC;AAC9E,QAAA,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK,IAAI,CAAC,iCAAiC,CAAC,OAAO,CAAC,CAAC,CAAC;AAE/E,QAAA,IAAI,IAAI,CAAC,uBAAuB,CAAC,IAAI,IAAI,CAAC;YAAE,OAAO;AAEnD,QAAA,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;AAC5E,QAAA,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK,IAAI,CAAC,qCAAqC,CAAC,OAAO,CAAC,CAAC,CAAC;KACpF;AAED,IAAA,iCAAiC,CAAC,OAAY,EAAA;QAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACnD,IAAI,OAAO,EAAE;AACX,YAAA,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;;;;AAIzB,gBAAA,IAAI,MAAM,CAAC,MAAM,EAAE;AACjB,oBAAA,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC;iBAChC;qBAAM;oBACL,MAAM,CAAC,OAAO,EAAE,CAAC;iBAClB;AACH,aAAC,CAAC,CAAC;SACJ;KACF;AAED,IAAA,qCAAqC,CAAC,OAAY,EAAA;QAChD,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC1D,IAAI,OAAO,EAAE;AACX,YAAA,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;SAC9C;KACF;IAED,iBAAiB,GAAA;AACf,QAAA,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,KAAI;AACnC,YAAA,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;AACvB,gBAAA,OAAO,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,MAAM,OAAO,EAAE,CAAC,CAAC;aAClE;iBAAM;AACL,gBAAA,OAAO,EAAE,CAAC;aACX;AACH,SAAC,CAAC,CAAC;KACJ;AAED,IAAA,gBAAgB,CAAC,OAAY,EAAA;AAC3B,QAAA,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAA0B,CAAC;AAC/D,QAAA,IAAI,OAAO,IAAI,OAAO,CAAC,aAAa,EAAE;;AAEpC,YAAA,OAAO,CAAC,YAAY,CAAC,GAAG,kBAAkB,CAAC;AAC3C,YAAA,IAAI,OAAO,CAAC,WAAW,EAAE;AACvB,gBAAA,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;gBACrC,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;gBACrD,IAAI,EAAE,EAAE;AACN,oBAAA,EAAE,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;iBAC/B;aACF;YACD,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;SACzD;QAED,IAAI,OAAO,CAAC,SAAS,EAAE,QAAQ,CAAC,kBAAkB,CAAC,EAAE;AACnD,YAAA,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;SAC5C;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,iBAAiB,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AACnE,YAAA,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAC1C,SAAC,CAAC,CAAC;KACJ;IAED,KAAK,CAAC,WAAsB,GAAA,CAAC,CAAC,EAAA;QAC5B,IAAI,OAAO,GAAsB,EAAE,CAAC;AACpC,QAAA,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE;YAC7B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,OAAO,KAAK,IAAI,CAAC,qBAAqB,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC;AACvF,YAAA,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;SAC9B;QAED,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE;AAC9D,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC3D,MAAM,GAAG,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;AAC3C,gBAAA,QAAQ,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;aAC/B;SACF;AAED,QAAA,IACE,IAAI,CAAC,cAAc,CAAC,MAAM;aACzB,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,EAC/D;YACA,MAAM,UAAU,GAAe,EAAE,CAAC;AAClC,YAAA,IAAI;gBACF,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;aAC1D;oBAAS;AACR,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,oBAAA,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;iBACjB;aACF;SACF;aAAM;AACL,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC3D,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;AAC/C,gBAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;aAChC;SACF;AAED,QAAA,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;AAC5B,QAAA,IAAI,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC,CAAC;AACvC,QAAA,IAAI,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC,CAAC;AACvC,QAAA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;AACrC,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;AAEpB,QAAA,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;;;;AAI7B,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC;AACpC,YAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;AAExB,YAAA,IAAI,OAAO,CAAC,MAAM,EAAE;AAClB,gBAAA,mBAAmB,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,MAAK;oBACvC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;AACjC,iBAAC,CAAC,CAAC;aACJ;iBAAM;gBACL,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;aAChC;SACF;KACF;AAED,IAAA,WAAW,CAAC,MAAe,EAAA;AACzB,QAAA,MAAM,wBAAwB,CAAC,MAAM,CAAC,CAAC;KACxC;IAEO,gBAAgB,CACtB,UAAsB,EACtB,WAAmB,EAAA;AAEnB,QAAA,MAAM,YAAY,GAAG,IAAI,qBAAqB,EAAE,CAAC;QACjD,MAAM,cAAc,GAAgC,EAAE,CAAC;AACvD,QAAA,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAA0B,CAAC;QAC5D,MAAM,kBAAkB,GAAuB,EAAE,CAAC;AAClD,QAAA,MAAM,eAAe,GAAG,IAAI,GAAG,EAAoC,CAAC;AACpE,QAAA,MAAM,mBAAmB,GAAG,IAAI,GAAG,EAAoB,CAAC;AACxD,QAAA,MAAM,oBAAoB,GAAG,IAAI,GAAG,EAAoB,CAAC;AAEzD,QAAA,MAAM,mBAAmB,GAAG,IAAI,GAAG,EAAO,CAAC;QAC3C,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAClC,YAAA,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC9B,YAAA,MAAM,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;AAC5E,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,oBAAoB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACpD,mBAAmB,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;aAClD;AACH,SAAC,CAAC,CAAC;AAEH,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC/B,QAAA,MAAM,kBAAkB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC;QACnE,MAAM,YAAY,GAAG,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;;;;AAKnF,QAAA,MAAM,eAAe,GAAG,IAAI,GAAG,EAAe,CAAC;QAC/C,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,KAAI;AACnC,YAAA,MAAM,SAAS,GAAG,eAAe,GAAG,CAAC,EAAE,CAAC;AACxC,YAAA,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AACrC,YAAA,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;AACrD,SAAC,CAAC,CAAC;QAEH,MAAM,aAAa,GAAU,EAAE,CAAC;AAChC,QAAA,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAO,CAAC;AACxC,QAAA,MAAM,2BAA2B,GAAG,IAAI,GAAG,EAAO,CAAC;AACnD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3D,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;AAC/C,YAAA,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAA0B,CAAC;AAC/D,YAAA,IAAI,OAAO,IAAI,OAAO,CAAC,aAAa,EAAE;AACpC,gBAAA,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC5B,gBAAA,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AAC9B,gBAAA,IAAI,OAAO,CAAC,YAAY,EAAE;AACxB,oBAAA,IAAI,CAAC,MAAM;AACR,yBAAA,KAAK,CAAC,OAAO,EAAE,aAAa,EAAE,IAAI,CAAC;AACnC,yBAAA,OAAO,CAAC,CAAC,GAAG,KAAK,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;iBAChD;qBAAM;AACL,oBAAA,2BAA2B,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;iBAC1C;aACF;SACF;AAED,QAAA,MAAM,eAAe,GAAG,IAAI,GAAG,EAAe,CAAC;AAC/C,QAAA,MAAM,YAAY,GAAG,YAAY,CAAC,kBAAkB,EAAE,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;QACpF,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,KAAI;AACnC,YAAA,MAAM,SAAS,GAAG,eAAe,GAAG,CAAC,EAAE,CAAC;AACxC,YAAA,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AACrC,YAAA,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;AACrD,SAAC,CAAC,CAAC;AAEH,QAAA,UAAU,CAAC,IAAI,CAAC,MAAK;YACnB,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,KAAI;gBACnC,MAAM,SAAS,GAAG,eAAe,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC;AAC7C,gBAAA,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;AACxD,aAAC,CAAC,CAAC;YAEH,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,KAAI;gBACnC,MAAM,SAAS,GAAG,eAAe,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC;AAC7C,gBAAA,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;AACxD,aAAC,CAAC,CAAC;AAEH,YAAA,aAAa,CAAC,OAAO,CAAC,CAAC,OAAO,KAAI;AAChC,gBAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;AACjC,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;QAEH,MAAM,UAAU,GAAgC,EAAE,CAAC;QACnD,MAAM,oBAAoB,GAAqC,EAAE,CAAC;AAClE,QAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YACxD,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;YAClC,EAAE,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;AACvD,gBAAA,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;AAC5B,gBAAA,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;AAC9B,gBAAA,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAExB,gBAAA,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE;AACtC,oBAAA,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAA0B,CAAC;;;AAG/D,oBAAA,IAAI,OAAO,IAAI,OAAO,CAAC,UAAU,EAAE;wBACjC,IACE,OAAO,CAAC,sBAAsB;4BAC9B,OAAO,CAAC,sBAAsB,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,EACrD;AACA,4BAAA,MAAM,aAAa,GAAG,OAAO,CAAC,sBAAsB,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,CAAW,CAAC;;;AAItF,4BAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;4BACnE,IAAI,kBAAkB,IAAI,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE;gCACnE,MAAM,KAAK,GAAG,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,CAAE,CAAC;AACzD,gCAAA,KAAK,CAAC,KAAK,GAAG,aAAa,CAAC;gCAC5B,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;6BAClD;yBACF;wBAED,MAAM,CAAC,OAAO,EAAE,CAAC;wBACjB,OAAO;qBACR;iBACF;AAED,gBAAA,MAAM,cAAc,GAAG,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;gBACpF,MAAM,cAAc,GAAG,eAAe,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC;gBACrD,MAAM,cAAc,GAAG,eAAe,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC;AACrD,gBAAA,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CACxC,KAAK,EACL,YAAY,EACZ,cAAc,EACd,cAAc,EACd,cAAc,CACd,CAAC;gBACH,IAAI,WAAW,CAAC,MAAM,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE;AACnD,oBAAA,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBACvC,OAAO;iBACR;;;;;gBAMD,IAAI,cAAc,EAAE;AAClB,oBAAA,MAAM,CAAC,OAAO,CAAC,MAAM,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC;AACnE,oBAAA,MAAM,CAAC,SAAS,CAAC,MAAM,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;AACjE,oBAAA,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBAC5B,OAAO;iBACR;;;;AAKD,gBAAA,IAAI,KAAK,CAAC,oBAAoB,EAAE;AAC9B,oBAAA,MAAM,CAAC,OAAO,CAAC,MAAM,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC;AACnE,oBAAA,MAAM,CAAC,SAAS,CAAC,MAAM,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;AACjE,oBAAA,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBAC5B,OAAO;iBACR;;;;;;gBAOD,MAAM,SAAS,GAAmC,EAAE,CAAC;gBACrD,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,KAAI;AACnC,oBAAA,EAAE,CAAC,uBAAuB,GAAG,IAAI,CAAC;AAClC,oBAAA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE;AACvC,wBAAA,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;qBACpB;AACH,iBAAC,CAAC,CAAC;AACH,gBAAA,WAAW,CAAC,SAAS,GAAG,SAAS,CAAC;gBAElC,YAAY,CAAC,MAAM,CAAC,OAAO,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC;gBAEpD,MAAM,KAAK,GAAG,EAAC,WAAW,EAAE,MAAM,EAAE,OAAO,EAAC,CAAC;AAE7C,gBAAA,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAE/B,WAAW,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,OAAO,KAC1C,oBAAoB,CAAC,eAAe,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAChE,CAAC;gBAEF,WAAW,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,OAAO,KAAI;AACvD,oBAAA,IAAI,SAAS,CAAC,IAAI,EAAE;wBAClB,IAAI,MAAM,GAAgB,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC;wBAC5D,IAAI,CAAC,MAAM,EAAE;AACX,4BAAA,mBAAmB,CAAC,GAAG,CAAC,OAAO,GAAG,MAAM,GAAG,IAAI,GAAG,EAAU,EAAE,CAAC;yBAChE;AACD,wBAAA,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;qBAClD;AACH,iBAAC,CAAC,CAAC;gBAEH,WAAW,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,OAAO,KAAI;oBACxD,IAAI,MAAM,GAAgB,oBAAoB,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC;oBAC7D,IAAI,CAAC,MAAM,EAAE;AACX,wBAAA,oBAAoB,CAAC,GAAG,CAAC,OAAO,GAAG,MAAM,GAAG,IAAI,GAAG,EAAU,EAAE,CAAC;qBACjE;AACD,oBAAA,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AACnD,iBAAC,CAAC,CAAC;AACL,aAAC,CAAC,CAAC;SACJ;AAED,QAAA,IAAI,oBAAoB,CAAC,MAAM,EAAE;YAC/B,MAAM,MAAM,GAAY,EAAE,CAAC;AAC3B,YAAA,oBAAoB,CAAC,OAAO,CAAC,CAAC,WAAW,KAAI;AAC3C,gBAAA,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,WAAW,EAAE,WAAW,CAAC,MAAO,CAAC,CAAC,CAAC;AAC9E,aAAC,CAAC,CAAC;AAEH,YAAA,UAAU,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;AACjD,YAAA,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;SAC1B;AAED,QAAA,MAAM,qBAAqB,GAAG,IAAI,GAAG,EAAoC,CAAC;;;;;AAK1E,QAAA,MAAM,mBAAmB,GAAG,IAAI,GAAG,EAAY,CAAC;AAChD,QAAA,kBAAkB,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;AACnC,YAAA,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;AAC9B,YAAA,IAAI,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;AAC7B,gBAAA,mBAAmB,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAC1C,gBAAA,IAAI,CAAC,qBAAqB,CACxB,KAAK,CAAC,MAAM,CAAC,WAAW,EACxB,KAAK,CAAC,WAAW,EACjB,qBAAqB,CACtB,CAAC;aACH;AACH,SAAC,CAAC,CAAC;AAEH,QAAA,cAAc,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;AAChC,YAAA,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;YAC/B,MAAM,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAC9C,OAAO,EACP,KAAK,EACL,MAAM,CAAC,WAAW,EAClB,MAAM,CAAC,WAAW,EAClB,IAAI,CACL,CAAC;AACF,YAAA,eAAe,CAAC,OAAO,CAAC,CAAC,UAAU,KAAI;AACrC,gBAAA,oBAAoB,CAAC,qBAAqB,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC1E,UAAU,CAAC,OAAO,EAAE,CAAC;AACvB,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;;;;;;;;QASH,MAAM,YAAY,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,IAAI,KAAI;YACjD,OAAO,sBAAsB,CAAC,IAAI,EAAE,mBAAmB,EAAE,oBAAoB,CAAC,CAAC;AACjF,SAAC,CAAC,CAAC;;AAGH,QAAA,MAAM,aAAa,GAAG,IAAI,GAAG,EAAsB,CAAC;AACpD,QAAA,MAAM,oBAAoB,GAAG,qBAAqB,CAChD,aAAa,EACb,IAAI,CAAC,MAAM,EACX,2BAA2B,EAC3B,oBAAoB,EACpB,UAAU,CACX,CAAC;AAEF,QAAA,oBAAoB,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;YACpC,IAAI,sBAAsB,CAAC,IAAI,EAAE,mBAAmB,EAAE,oBAAoB,CAAC,EAAE;AAC3E,gBAAA,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACzB;AACH,SAAC,CAAC,CAAC;;AAGH,QAAA,MAAM,YAAY,GAAG,IAAI,GAAG,EAAsB,CAAC;QACnD,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,KAAI;AACnC,YAAA,qBAAqB,CACnB,YAAY,EACZ,IAAI,CAAC,MAAM,EACX,IAAI,GAAG,CAAC,KAAK,CAAC,EACd,mBAAmB,EACnBC,UAAS,CACV,CAAC;AACJ,SAAC,CAAC,CAAC;AAEH,QAAA,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;YAC5B,MAAM,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACnC,YAAA,aAAa,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5F,SAAC,CAAC,CAAC;QAEH,MAAM,WAAW,GAAgC,EAAE,CAAC;QACpD,MAAM,UAAU,GAAgC,EAAE,CAAC;QACnD,MAAM,oCAAoC,GAAG,EAAE,CAAC;AAChD,QAAA,kBAAkB,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;YACnC,MAAM,EAAC,OAAO,EAAE,MAAM,EAAE,WAAW,EAAC,GAAG,KAAK,CAAC;;;AAG7C,YAAA,IAAI,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;AAC7B,gBAAA,IAAI,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;AACpC,oBAAA,MAAM,CAAC,SAAS,CAAC,MAAM,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;AACjE,oBAAA,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC;AACvB,oBAAA,MAAM,CAAC,iBAAiB,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;AAChD,oBAAA,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBAC5B,OAAO;iBACR;;;;;;;gBAQD,IAAI,mBAAmB,GAAQ,oCAAoC,CAAC;AACpE,gBAAA,IAAI,mBAAmB,CAAC,IAAI,GAAG,CAAC,EAAE;oBAChC,IAAI,GAAG,GAAG,OAAO,CAAC;oBAClB,MAAM,YAAY,GAAU,EAAE,CAAC;oBAC/B,QAAQ,GAAG,GAAG,GAAG,CAAC,UAAU,GAAG;wBAC7B,MAAM,cAAc,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;wBACpD,IAAI,cAAc,EAAE;4BAClB,mBAAmB,GAAG,cAAc,CAAC;4BACrC,MAAM;yBACP;AACD,wBAAA,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;qBACxB;AACD,oBAAA,YAAY,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,mBAAmB,CAAC,GAAG,CAAC,MAAM,EAAE,mBAAmB,CAAC,CAAC,CAAC;iBACxF;gBAED,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CACtC,MAAM,CAAC,WAAW,EAClB,WAAW,EACX,qBAAqB,EACrB,iBAAiB,EACjB,YAAY,EACZ,aAAa,CACd,CAAC;AAEF,gBAAA,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;AAElC,gBAAA,IAAI,mBAAmB,KAAK,oCAAoC,EAAE;AAChE,oBAAA,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;iBAC1B;qBAAM;oBACL,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;AACrE,oBAAA,IAAI,aAAa,IAAI,aAAa,CAAC,MAAM,EAAE;AACzC,wBAAA,MAAM,CAAC,YAAY,GAAG,mBAAmB,CAAC,aAAa,CAAC,CAAC;qBAC1D;AACD,oBAAA,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;iBAC7B;aACF;iBAAM;AACL,gBAAA,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC;AAC7C,gBAAA,MAAM,CAAC,SAAS,CAAC,MAAM,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;;;;AAIjE,gBAAA,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACxB,gBAAA,IAAI,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;AACpC,oBAAA,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;iBAC7B;aACF;AACH,SAAC,CAAC,CAAC;;AAGH,QAAA,UAAU,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;;;YAG5B,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAChE,YAAA,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,EAAE;AACjD,gBAAA,MAAM,WAAW,GAAG,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;AAC3D,gBAAA,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;aACnC;AACH,SAAC,CAAC,CAAC;;;;AAKH,QAAA,cAAc,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;AAChC,YAAA,IAAI,MAAM,CAAC,YAAY,EAAE;AACvB,gBAAA,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;aAC9C;iBAAM;gBACL,MAAM,CAAC,OAAO,EAAE,CAAC;aAClB;AACH,SAAC,CAAC,CAAC;;;;AAKH,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC7C,YAAA,MAAM,OAAO,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;AACjC,YAAA,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAA0B,CAAC;AAC/D,YAAA,WAAW,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;;;;AAKtC,YAAA,IAAI,OAAO,IAAI,OAAO,CAAC,YAAY;gBAAE,SAAS;YAE9C,IAAI,OAAO,GAAgC,EAAE,CAAC;;;;AAK9C,YAAA,IAAI,eAAe,CAAC,IAAI,EAAE;gBACxB,IAAI,oBAAoB,GAAG,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AACxD,gBAAA,IAAI,oBAAoB,IAAI,oBAAoB,CAAC,MAAM,EAAE;AACvD,oBAAA,OAAO,CAAC,IAAI,CAAC,GAAG,oBAAoB,CAAC,CAAC;iBACvC;AAED,gBAAA,IAAI,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;AACnF,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,oBAAoB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACpD,IAAI,cAAc,GAAG,eAAe,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;AAClE,oBAAA,IAAI,cAAc,IAAI,cAAc,CAAC,MAAM,EAAE;AAC3C,wBAAA,OAAO,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,CAAC;qBACjC;iBACF;aACF;AAED,YAAA,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;AAC1D,YAAA,IAAI,aAAa,CAAC,MAAM,EAAE;AACxB,gBAAA,6BAA6B,CAAC,IAAI,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;aAC7D;iBAAM;AACL,gBAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;aAChC;SACF;;AAGD,QAAA,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;AAEzB,QAAA,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;AAC7B,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC1B,YAAA,MAAM,CAAC,MAAM,CAAC,MAAK;gBACjB,MAAM,CAAC,OAAO,EAAE,CAAC;gBAEjB,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBAC3C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAChC,aAAC,CAAC,CAAC;YACH,MAAM,CAAC,IAAI,EAAE,CAAC;AAChB,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,WAAW,CAAC;KACpB;AAED,IAAA,UAAU,CAAC,QAAmB,EAAA;AAC5B,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KAC/B;AAED,IAAA,wBAAwB,CAAC,QAAmB,EAAA;AAC1C,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KACnC;IAEO,mBAAmB,CACzB,OAAe,EACf,gBAAyB,EACzB,WAAoB,EACpB,WAAoB,EACpB,YAAkB,EAAA;QAElB,IAAI,OAAO,GAAgC,EAAE,CAAC;QAC9C,IAAI,gBAAgB,EAAE;YACpB,MAAM,qBAAqB,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACxE,IAAI,qBAAqB,EAAE;gBACzB,OAAO,GAAG,qBAAqB,CAAC;aACjC;SACF;aAAM;YACL,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC1D,IAAI,cAAc,EAAE;gBAClB,MAAM,kBAAkB,GAAG,CAAC,YAAY,IAAI,YAAY,IAAI,UAAU,CAAC;AACvE,gBAAA,cAAc,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;oBAChC,IAAI,MAAM,CAAC,MAAM;wBAAE,OAAO;AAC1B,oBAAA,IAAI,CAAC,kBAAkB,IAAI,MAAM,CAAC,WAAW,IAAI,WAAW;wBAAE,OAAO;AACrE,oBAAA,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACvB,iBAAC,CAAC,CAAC;aACJ;SACF;AACD,QAAA,IAAI,WAAW,IAAI,WAAW,EAAE;YAC9B,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,KAAI;AAClC,gBAAA,IAAI,WAAW,IAAI,WAAW,IAAI,MAAM,CAAC,WAAW;AAAE,oBAAA,OAAO,KAAK,CAAC;AACnE,gBAAA,IAAI,WAAW,IAAI,WAAW,IAAI,MAAM,CAAC,WAAW;AAAE,oBAAA,OAAO,KAAK,CAAC;AACnE,gBAAA,OAAO,IAAI,CAAC;AACd,aAAC,CAAC,CAAC;SACJ;AACD,QAAA,OAAO,OAAO,CAAC;KAChB;AAEO,IAAA,qBAAqB,CAC3B,WAAmB,EACnB,WAA2C,EAC3C,qBAA4D,EAAA;AAE5D,QAAA,MAAM,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;AAC5C,QAAA,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC;;;AAIxC,QAAA,MAAM,iBAAiB,GAAuB,WAAW,CAAC,mBAAmB;AAC3E,cAAE,SAAS;cACT,WAAW,CAAC;AAChB,QAAA,MAAM,iBAAiB,GAAuB,WAAW,CAAC,mBAAmB;AAC3E,cAAE,SAAS;cACT,WAAW,CAAC;AAEhB,QAAA,KAAK,MAAM,mBAAmB,IAAI,WAAW,CAAC,SAAS,EAAE;AACvD,YAAA,MAAM,OAAO,GAAG,mBAAmB,CAAC,OAAO,CAAC;AAC5C,YAAA,MAAM,gBAAgB,GAAG,OAAO,KAAK,WAAW,CAAC;YACjD,MAAM,OAAO,GAAG,oBAAoB,CAAC,qBAAqB,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;AACzE,YAAA,MAAM,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAC9C,OAAO,EACP,gBAAgB,EAChB,iBAAiB,EACjB,iBAAiB,EACjB,WAAW,CAAC,OAAO,CACpB,CAAC;AACF,YAAA,eAAe,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;AACjC,gBAAA,MAAM,UAAU,GAAI,MAAoC,CAAC,aAAa,EAAS,CAAC;AAChF,gBAAA,IAAI,UAAU,CAAC,aAAa,EAAE;oBAC5B,UAAU,CAAC,aAAa,EAAE,CAAC;iBAC5B;gBACD,MAAM,CAAC,OAAO,EAAE,CAAC;AACjB,gBAAA,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACvB,aAAC,CAAC,CAAC;SACJ;;;AAID,QAAA,WAAW,CAAC,WAAW,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC;KAClD;IAEO,eAAe,CACrB,WAAmB,EACnB,WAA2C,EAC3C,qBAA4D,EAC5D,iBAA8C,EAC9C,YAAqC,EACrC,aAAsC,EAAA;AAEtC,QAAA,MAAM,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;AAC5C,QAAA,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC;;;QAIxC,MAAM,iBAAiB,GAAgC,EAAE,CAAC;AAC1D,QAAA,MAAM,mBAAmB,GAAG,IAAI,GAAG,EAAO,CAAC;AAC3C,QAAA,MAAM,cAAc,GAAG,IAAI,GAAG,EAAO,CAAC;QACtC,MAAM,aAAa,GAAG,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,mBAAmB,KAAI;AACtE,YAAA,MAAM,OAAO,GAAG,mBAAmB,CAAC,OAAO,CAAC;AAC5C,YAAA,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;;AAGjC,YAAA,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;AACtC,YAAA,IAAI,OAAO,IAAI,OAAO,CAAC,oBAAoB;gBACzC,OAAO,IAAI,mBAAmB,CAAC,mBAAmB,CAAC,QAAQ,EAAE,mBAAmB,CAAC,KAAK,CAAC,CAAC;AAC1F,YAAA,MAAM,gBAAgB,GAAG,OAAO,KAAK,WAAW,CAAC;AACjD,YAAA,MAAM,eAAe,GAAG,mBAAmB,CACzC,CAAC,qBAAqB,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,kBAAkB,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,CAAC,CACzF,CAAC,MAAM,CAAC,CAAC,CAAC,KAAI;;;;;gBAKb,MAAM,EAAE,GAAG,CAAQ,CAAC;AACpB,gBAAA,OAAO,EAAE,CAAC,OAAO,GAAG,EAAE,CAAC,OAAO,KAAK,OAAO,GAAG,KAAK,CAAC;AACrD,aAAC,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC5C,MAAM,UAAU,GAAG,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AAE9C,YAAA,MAAM,SAAS,GAAGD,oBAAkB,CAClC,IAAI,CAAC,WAAW,EAChB,mBAAmB,CAAC,SAAS,EAC7B,SAAS,EACT,UAAU,CACX,CAAC;AACF,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,mBAAmB,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC;;;AAIlF,YAAA,IAAI,mBAAmB,CAAC,WAAW,IAAI,iBAAiB,EAAE;AACxD,gBAAA,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;aAC7B;YAED,IAAI,gBAAgB,EAAE;gBACpB,MAAM,aAAa,GAAG,IAAI,yBAAyB,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;AACvF,gBAAA,aAAa,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AACpC,gBAAA,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;aACvC;AAED,YAAA,OAAO,MAAM,CAAC;AAChB,SAAC,CAAC,CAAC;AAEH,QAAA,iBAAiB,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;AACnC,YAAA,oBAAoB,CAAC,IAAI,CAAC,uBAAuB,EAAE,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACpF,YAAA,MAAM,CAAC,MAAM,CAAC,MAAM,kBAAkB,CAAC,IAAI,CAAC,uBAAuB,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;AAChG,SAAC,CAAC,CAAC;AAEH,QAAA,mBAAmB,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK,QAAQ,CAAC,OAAO,EAAE,sBAAsB,CAAC,CAAC,CAAC;AACpF,QAAA,MAAM,MAAM,GAAG,mBAAmB,CAAC,aAAa,CAAC,CAAC;AAClD,QAAA,MAAM,CAAC,SAAS,CAAC,MAAK;AACpB,YAAA,mBAAmB,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK,WAAW,CAAC,OAAO,EAAE,sBAAsB,CAAC,CAAC,CAAC;AACvF,YAAA,SAAS,CAAC,WAAW,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC/C,SAAC,CAAC,CAAC;;;AAIH,QAAA,cAAc,CAAC,OAAO,CAAC,CAAC,OAAO,KAAI;AACjC,YAAA,oBAAoB,CAAC,iBAAiB,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACpE,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,MAAM,CAAC;KACf;AAEO,IAAA,YAAY,CAClB,WAAyC,EACzC,SAA+B,EAC/B,eAAkC,EAAA;AAElC,QAAA,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YACxB,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CACxB,WAAW,CAAC,OAAO,EACnB,SAAS,EACT,WAAW,CAAC,QAAQ,EACpB,WAAW,CAAC,KAAK,EACjB,WAAW,CAAC,MAAM,EAClB,eAAe,CAChB,CAAC;SACH;;;QAID,OAAO,IAAI,mBAAmB,CAAC,WAAW,CAAC,QAAQ,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;KACzE;AACF,CAAA;MAEY,yBAAyB,CAAA;AAcpC,IAAA,WAAA,CACS,WAAmB,EACnB,WAAmB,EACnB,OAAY,EAAA;QAFZ,IAAW,CAAA,WAAA,GAAX,WAAW,CAAQ;QACnB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAQ;QACnB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAK;AAhBb,QAAA,IAAA,CAAA,OAAO,GAAoB,IAAI,mBAAmB,EAAE,CAAC;QACrD,IAAmB,CAAA,mBAAA,GAAG,KAAK,CAAC;AAE5B,QAAA,IAAA,CAAA,gBAAgB,GAAG,IAAI,GAAG,EAAmC,CAAC;QACtD,IAAS,CAAA,SAAA,GAAG,KAAK,CAAC;QAC3B,IAAY,CAAA,YAAA,GAA2B,IAAI,CAAC;QAE5C,IAAgB,CAAA,gBAAA,GAAY,KAAK,CAAC;QAClC,IAAQ,CAAA,QAAA,GAAG,KAAK,CAAC;QAEf,IAAM,CAAA,MAAA,GAAY,IAAI,CAAC;QAChB,IAAS,CAAA,SAAA,GAAW,CAAC,CAAC;KAMlC;AAEJ,IAAA,aAAa,CAAC,MAAuB,EAAA;QACnC,IAAI,IAAI,CAAC,mBAAmB;YAAE,OAAO;AAErC,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,KAAK,KAAI;AACjD,YAAA,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAK,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC;AACtF,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;AAC9B,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;AAChC,QAAA,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACxC,QAAA,IAAuB,CAAC,MAAM,GAAG,KAAK,CAAC;KACzC;IAED,aAAa,GAAA;QACX,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;AAED,IAAA,iBAAiB,CAAC,SAAiB,EAAA;AAChC,QAAA,IAAY,CAAC,SAAS,GAAG,SAAS,CAAC;KACrC;AAED,IAAA,gBAAgB,CAAC,MAAuB,EAAA;AACtC,QAAA,MAAM,CAAC,GAAG,IAAI,CAAC,OAAc,CAAC;AAC9B,QAAA,IAAI,CAAC,CAAC,eAAe,EAAE;AACrB,YAAA,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,eAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;SACnD;QACD,MAAM,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;QACnC,MAAM,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;KACxC;IAEO,WAAW,CAAC,IAAY,EAAE,QAA6B,EAAA;AAC7D,QAAA,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KACtE;AAED,IAAA,MAAM,CAAC,EAAc,EAAA;AACnB,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,YAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;SAC9B;AACD,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;KACzB;AAED,IAAA,OAAO,CAAC,EAAc,EAAA;AACpB,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,YAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;SAC/B;AACD,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;KAC1B;AAED,IAAA,SAAS,CAAC,EAAc,EAAA;AACtB,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,YAAA,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;SACjC;AACD,QAAA,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;KAC5B;IAED,IAAI,GAAA;AACF,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;KACrB;IAED,UAAU,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;KACxD;IAED,IAAI,GAAA;QACF,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;KACrC;IAED,KAAK,GAAA;QACH,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;KACtC;IAED,OAAO,GAAA;QACL,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;KACxC;IAED,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;KACvB;IAED,OAAO,GAAA;AACJ,QAAA,IAA6B,CAAC,SAAS,GAAG,IAAI,CAAC;AAChD,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;KACxB;IAED,KAAK,GAAA;QACH,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;KACtC;AAED,IAAA,WAAW,CAAC,CAAS,EAAA;AACnB,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAChB,YAAA,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;SAC7B;KACF;IAED,WAAW,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;KACrD;;AAGD,IAAA,eAAe,CAAC,SAAiB,EAAA;AAC/B,QAAA,MAAM,CAAC,GAAG,IAAI,CAAC,OAAc,CAAC;AAC9B,QAAA,IAAI,CAAC,CAAC,eAAe,EAAE;AACrB,YAAA,CAAC,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;SAC9B;KACF;AACF,CAAA;AAED,SAAS,kBAAkB,CAAO,GAAgB,EAAE,GAAM,EAAE,KAAQ,EAAA;IAClE,IAAI,aAAa,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACjC,IAAI,aAAa,EAAE;AACjB,QAAA,IAAI,aAAa,CAAC,MAAM,EAAE;YACxB,MAAM,KAAK,GAAG,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC3C,YAAA,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAChC;AACD,QAAA,IAAI,aAAa,CAAC,MAAM,IAAI,CAAC,EAAE;AAC7B,YAAA,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;SACjB;KACF;AACD,IAAA,OAAO,aAAa,CAAC;AACvB,CAAC;AAED,SAAS,qBAAqB,CAAC,KAAU,EAAA;;;;IAIvC,OAAO,KAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC;AACtC,CAAC;AAED,SAAS,aAAa,CAAC,IAAS,EAAA;IAC9B,OAAO,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AACxC,CAAC;AAED,SAAS,mBAAmB,CAAC,SAAiB,EAAA;AAC5C,IAAA,OAAO,SAAS,IAAI,OAAO,IAAI,SAAS,IAAI,MAAM,CAAC;AACrD,CAAC;AAED,SAAS,YAAY,CAAC,OAAY,EAAE,KAAc,EAAA;AAChD,IAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC;AACvC,IAAA,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,GAAG,MAAM,CAAC;AACvD,IAAA,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAS,qBAAqB,CAC5B,SAAkC,EAClC,MAAuB,EACvB,QAAkB,EAClB,eAAsC,EACtC,YAAoB,EAAA;IAEpB,MAAM,SAAS,GAAa,EAAE,CAAC;AAC/B,IAAA,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAErE,MAAM,cAAc,GAAU,EAAE,CAAC;IAEjC,eAAe,CAAC,OAAO,CAAC,CAAC,KAAkB,EAAE,OAAY,KAAI;AAC3D,QAAA,MAAM,MAAM,GAAkB,IAAI,GAAG,EAAE,CAAC;AACxC,QAAA,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AACrB,YAAA,MAAM,KAAK,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;AAC/D,YAAA,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;;;YAIxB,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;AAC/B,gBAAA,OAAO,CAAC,YAAY,CAAC,GAAG,0BAA0B,CAAC;AACnD,gBAAA,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aAC9B;AACH,SAAC,CAAC,CAAC;AACH,QAAA,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AACjC,KAAC,CAAC,CAAC;;;IAIH,IAAI,CAAC,GAAG,CAAC,CAAC;AACV,IAAA,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAErE,IAAA,OAAO,cAAc,CAAC;AACxB,CAAC;AAED;;;;;;;;;AASG;AACH,SAAS,YAAY,CAAC,KAAY,EAAE,KAAY,EAAA;AAC9C,IAAA,MAAM,OAAO,GAAG,IAAI,GAAG,EAAc,CAAC;AACtC,IAAA,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;AAE/C,IAAA,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC;AAAE,QAAA,OAAO,OAAO,CAAC;IAEtC,MAAM,SAAS,GAAG,CAAC,CAAC;AACpB,IAAA,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;AAC/B,IAAA,MAAM,YAAY,GAAG,IAAI,GAAG,EAAY,CAAC;IAEzC,SAAS,OAAO,CAAC,IAAS,EAAA;AACxB,QAAA,IAAI,CAAC,IAAI;AAAE,YAAA,OAAO,SAAS,CAAC;QAE5B,IAAI,IAAI,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAClC,QAAA,IAAI,IAAI;AAAE,YAAA,OAAO,IAAI,CAAC;AAEtB,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;AAC/B,QAAA,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;;YAEvB,IAAI,GAAG,MAAM,CAAC;SACf;AAAM,aAAA,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;;YAE9B,IAAI,GAAG,SAAS,CAAC;SAClB;aAAM;;AAEL,YAAA,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;SACxB;AAED,QAAA,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC7B,QAAA,OAAO,IAAI,CAAC;KACb;AAED,IAAA,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AACrB,QAAA,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AAC3B,QAAA,IAAI,IAAI,KAAK,SAAS,EAAE;YACtB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC/B;AACH,KAAC,CAAC,CAAC;AAEH,IAAA,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,QAAQ,CAAC,OAAY,EAAE,SAAiB,EAAA;AAC/C,IAAA,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC;AACpC,CAAC;AAED,SAAS,WAAW,CAAC,OAAY,EAAE,SAAiB,EAAA;AAClD,IAAA,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;AACvC,CAAC;AAED,SAAS,6BAA6B,CACpC,MAAiC,EACjC,OAAY,EACZ,OAA0B,EAAA;AAE1B,IAAA,mBAAmB,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,MAAM,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;AAC9E,CAAC;AAED,SAAS,mBAAmB,CAAC,OAA0B,EAAA;IACrD,MAAM,YAAY,GAAsB,EAAE,CAAC;AAC3C,IAAA,yBAAyB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;AACjD,IAAA,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,SAAS,yBAAyB,CAAC,OAA0B,EAAE,YAA+B,EAAA;AAC5F,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACvC,QAAA,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AAC1B,QAAA,IAAI,MAAM,YAAYE,qBAAoB,EAAE;AAC1C,YAAA,yBAAyB,CAAC,MAAM,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;SACzD;aAAM;AACL,YAAA,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC3B;KACF;AACH,CAAC;AAED,SAAS,SAAS,CAAC,CAAuB,EAAE,CAAuB,EAAA;IACjE,MAAM,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC1B,MAAM,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1B,IAAA,IAAI,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,MAAM;AAAE,QAAA,OAAO,KAAK,CAAC;AACzC,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAClC,QAAA,MAAM,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AACnB,QAAA,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AAAE,YAAA,OAAO,KAAK,CAAC;KAClE;AACD,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,sBAAsB,CAC7B,OAAY,EACZ,mBAA0C,EAC1C,oBAA2C,EAAA;IAE3C,MAAM,SAAS,GAAG,oBAAoB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AACpD,IAAA,IAAI,CAAC,SAAS;AAAE,QAAA,OAAO,KAAK,CAAC;IAE7B,IAAI,QAAQ,GAAG,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IAChD,IAAI,QAAQ,EAAE;AACZ,QAAA,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,QAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;KAClD;SAAM;AACL,QAAA,mBAAmB,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;KAC7C;AAED,IAAA,oBAAoB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACrC,IAAA,OAAO,IAAI,CAAC;AACd;;MCr4Da,eAAe,CAAA;AAS1B,IAAA,WAAA,CACE,GAAa,EACL,OAAwB,EACxB,WAAqC,EAC7C,SAA0C,EAAA;QAFlC,IAAO,CAAA,OAAA,GAAP,OAAO,CAAiB;QACxB,IAAW,CAAA,WAAA,GAAX,WAAW,CAA0B;QARvC,IAAa,CAAA,aAAA,GAAsC,EAAE,CAAC;;QAGvD,IAAiB,CAAA,iBAAA,GAAG,CAAC,OAAY,EAAE,OAAY,KAAM,GAAC,CAAC;AAQ5D,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,yBAAyB,CACpD,GAAG,CAAC,IAAI,EACR,OAAO,EACP,WAAW,EACX,SAAS,CACV,CAAC;AACF,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,uBAAuB,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;QAEnF,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,GAAG,CAAC,OAAY,EAAE,OAAY,KACpE,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;KAC5C;IAED,eAAe,CACb,WAAmB,EACnB,WAAmB,EACnB,WAAgB,EAChB,IAAY,EACZ,QAAkC,EAAA;AAElC,QAAA,MAAM,QAAQ,GAAG,WAAW,GAAG,GAAG,GAAG,IAAI,CAAC;QAC1C,IAAI,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC3C,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,MAAM,GAAY,EAAE,CAAC;YAC3B,MAAM,QAAQ,GAAa,EAAE,CAAC;AAC9B,YAAA,MAAM,GAAG,GAAG,iBAAiB,CAC3B,IAAI,CAAC,OAAO,EACZ,QAA6B,EAC7B,MAAM,EACN,QAAQ,CACK,CAAC;AAChB,YAAA,IAAI,MAAM,CAAC,MAAM,EAAE;AACjB,gBAAA,MAAM,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;aACxC;AACD,YAAA,IAAI,QAAQ,CAAC,MAAM,EAAE;AACnB,gBAAA,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;aAClC;YACD,OAAO,GAAG,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AACpD,YAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC;SACxC;QACD,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,WAAW,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;KACpE;IAED,QAAQ,CAAC,WAAmB,EAAE,WAAgB,EAAA;QAC5C,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;KAC3D;IAED,OAAO,CAAC,WAAmB,EAAE,OAAY,EAAA;QACvC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;KACtD;AAED,IAAA,QAAQ,CAAC,WAAmB,EAAE,OAAY,EAAE,MAAW,EAAE,YAAqB,EAAA;AAC5E,QAAA,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;KAC/E;AAED,IAAA,QAAQ,CAAC,WAAmB,EAAE,OAAY,EAAE,OAAY,EAAA;QACtD,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;KAClE;IAED,iBAAiB,CAAC,OAAY,EAAE,OAAgB,EAAA;QAC9C,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;KAChE;AAED,IAAA,OAAO,CAAC,WAAmB,EAAE,OAAY,EAAE,QAAgB,EAAE,KAAU,EAAA;QACrE,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;YAC7B,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,GAAG,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YACpD,MAAM,IAAI,GAAG,KAAc,CAAC;AAC5B,YAAA,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;SACzD;aAAM;AACL,YAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;SACvE;KACF;IAED,MAAM,CACJ,WAAmB,EACnB,OAAY,EACZ,SAAiB,EACjB,UAAkB,EAClB,QAA6B,EAAA;;QAG7B,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;YAC9B,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,GAAG,oBAAoB,CAAC,SAAS,CAAC,CAAC;AACrD,YAAA,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;SACnE;AACD,QAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;KAC7F;IAED,KAAK,CAAC,WAAsB,GAAA,CAAC,CAAC,EAAA;AAC5B,QAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;KAC3C;AAED,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,OAAO,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;KAC7E;IAED,iBAAiB,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,CAAC;KACnD;AAED,IAAA,wBAAwB,CAAC,EAAgB,EAAA;AACvC,QAAA,IAAI,CAAC,iBAAiB,CAAC,wBAAwB,CAAC,EAAE,CAAC,CAAC;KACrD;AACF;;AChID;;;;;;;;;;AAUG;AACa,SAAA,0BAA0B,CACxC,OAAY,EACZ,MAA4C,EAAA;IAE5C,IAAI,WAAW,GAAyB,IAAI,CAAC;IAC7C,IAAI,SAAS,GAAyB,IAAI,CAAC;IAC3C,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,EAAE;QAC1C,WAAW,GAAG,yBAAyB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,QAAA,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;AACrB,YAAA,SAAS,GAAG,yBAAyB,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;SAClE;KACF;AAAM,SAAA,IAAI,MAAM,YAAY,GAAG,EAAE;AAChC,QAAA,WAAW,GAAG,yBAAyB,CAAC,MAAM,CAAC,CAAC;KACjD;AAED,IAAA,OAAO,WAAW,IAAI,SAAS,GAAG,IAAI,kBAAkB,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;AACnG,CAAC;AAED;;;;;;;AAOG;MACU,kBAAkB,CAAA;AACtB,IAAA,SAAA,IAAA,CAAA,sBAAsB,GAAmB,IAAI,OAAO,EAAsB,CAAC,EAAA;AAKlF,IAAA,WAAA,CACU,QAAa,EACb,YAAkC,EAClC,UAAgC,EAAA;QAFhC,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAK;QACb,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAsB;QAClC,IAAU,CAAA,UAAA,GAAV,UAAU,CAAsB;AANlC,QAAA,IAAA,CAAA,MAAM,GAAmC,CAAA,uCAAA;QAQ/C,IAAI,aAAa,GAAG,kBAAkB,CAAC,sBAAsB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC5E,IAAI,CAAC,aAAa,EAAE;AAClB,YAAA,kBAAkB,CAAC,sBAAsB,CAAC,GAAG,CAAC,QAAQ,GAAG,aAAa,GAAG,IAAI,GAAG,EAAE,EAAE,CAAC;SACtF;AACD,QAAA,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;KACrC;IAED,KAAK,GAAA;AACH,QAAA,IAAI,IAAI,CAAC,MAAM,GAAA,CAAA,wCAAoC;AACjD,YAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,gBAAA,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;aAClE;YACD,IAAI,CAAC,MAAM,GAAA,CAAA,uCAAmC;SAC/C;KACF;IAED,MAAM,GAAA;QACJ,IAAI,CAAC,KAAK,EAAE,CAAC;AACb,QAAA,IAAI,IAAI,CAAC,MAAM,GAAA,CAAA,yCAAqC;YAClD,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;AAC9C,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AAC1C,gBAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;aACxB;YACD,IAAI,CAAC,MAAM,GAAA,CAAA,uCAAmC;SAC/C;KACF;IAED,OAAO,GAAA;QACL,IAAI,CAAC,MAAM,EAAE,CAAC;AACd,QAAA,IAAI,IAAI,CAAC,MAAM,GAAA,CAAA,0CAAsC;YACnD,kBAAkB,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAChE,YAAA,IAAI,IAAI,CAAC,YAAY,EAAE;gBACrB,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;AAC9C,gBAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;aACxB;AACD,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AAC5C,gBAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;aACxB;YACD,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YAC9C,IAAI,CAAC,MAAM,GAAA,CAAA,yCAAqC;SACjD;KACF;;AAoBH,SAAS,yBAAyB,CAAC,MAAqB,EAAA;IACtD,IAAI,MAAM,GAAyB,IAAI,CAAC;IACxC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,KAAI;AAC3B,QAAA,IAAI,oBAAoB,CAAC,IAAI,CAAC,EAAE;AAC9B,YAAA,MAAM,GAAG,MAAM,IAAI,IAAI,GAAG,EAAE,CAAC;AAC7B,YAAA,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;SACvB;AACH,KAAC,CAAC,CAAC;AACH,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,oBAAoB,CAAC,IAAY,EAAA;AACxC,IAAA,OAAO,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK,UAAU,CAAC;AACnD;;MC3Ha,mBAAmB,CAAA;AAyB9B,IAAA,WAAA,CACS,OAAY,EACZ,SAA+B,EAC/B,OAAyC,EACxC,cAA0C,EAAA;QAH3C,IAAO,CAAA,OAAA,GAAP,OAAO,CAAK;QACZ,IAAS,CAAA,SAAA,GAAT,SAAS,CAAsB;QAC/B,IAAO,CAAA,OAAA,GAAP,OAAO,CAAkC;QACxC,IAAc,CAAA,cAAA,GAAd,cAAc,CAA4B;QA5B5C,IAAU,CAAA,UAAA,GAAe,EAAE,CAAC;QAC5B,IAAW,CAAA,WAAA,GAAe,EAAE,CAAC;QAC7B,IAAa,CAAA,aAAA,GAAe,EAAE,CAAC;QAG/B,IAAY,CAAA,YAAA,GAAG,KAAK,CAAC;QACrB,IAAS,CAAA,SAAA,GAAG,KAAK,CAAC;QAClB,IAAQ,CAAA,QAAA,GAAG,KAAK,CAAC;QACjB,IAAU,CAAA,UAAA,GAAG,KAAK,CAAC;;;;QAMnB,IAAkB,CAAA,kBAAA,GAAe,EAAE,CAAC;QACpC,IAAmB,CAAA,mBAAA,GAAe,EAAE,CAAC;QAItC,IAAI,CAAA,IAAA,GAAG,CAAC,CAAC;QAET,IAAY,CAAA,YAAA,GAA2B,IAAI,CAAC;AAC5C,QAAA,IAAA,CAAA,eAAe,GAAkB,IAAI,GAAG,EAAE,CAAC;AAQhD,QAAA,IAAI,CAAC,SAAS,GAAW,OAAO,CAAC,UAAU,CAAC,CAAC;QAC7C,IAAI,CAAC,MAAM,GAAW,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC5C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;KAC1C;IAEO,SAAS,GAAA;AACf,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACnB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,YAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;AACtC,YAAA,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;SACtB;KACF;IAED,IAAI,GAAA;QACF,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,yBAAyB,EAAE,CAAC;KAClC;IAEO,YAAY,GAAA;QAClB,IAAI,IAAI,CAAC,YAAY;YAAE,OAAO;AAC9B,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AAEzB,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;;AAEjC,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAClF,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC;QACrF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;QACxC,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AACpD,QAAA,IAAI,CAAC,SAAS,CAAC,MAAK;;;;YAIlB,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AACzD,SAAC,CAAC,CAAC;KACJ;IAEO,yBAAyB,GAAA;;AAE/B,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,oBAAoB,EAAE,CAAC;SAC7B;aAAM;AACL,YAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;SACxB;KACF;AAEO,IAAA,yBAAyB,CAAC,SAA+B,EAAA;QAC/D,MAAM,GAAG,GAAU,EAAE,CAAC;AACtB,QAAA,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;YAC1B,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;AACtC,SAAC,CAAC,CAAC;AACH,QAAA,OAAO,GAAG,CAAC;KACZ;;AAGD,IAAA,oBAAoB,CAClB,OAAoB,EACpB,SAA+B,EAC/B,OAAY,EAAA;AAEZ,QAAA,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,CAAC;KAC5E;AAED,IAAA,OAAO,CAAC,EAAc,EAAA;AACpB,QAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAClC,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KAC3B;AAED,IAAA,MAAM,CAAC,EAAc,EAAA;AACnB,QAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACjC,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KAC1B;AAED,IAAA,SAAS,CAAC,EAAc,EAAA;AACtB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KAC7B;IAED,IAAI,GAAA;QACF,IAAI,CAAC,YAAY,EAAE,CAAC;AACpB,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;AACtB,YAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;AACvC,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACtB,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrB,YAAA,IAAI,IAAI,CAAC,cAAc,EAAE;AACvB,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;aAC7B;SACF;AACD,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;KACvB;IAED,KAAK,GAAA;QACH,IAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;KACxB;IAED,MAAM,GAAA;QACJ,IAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE;AACvB,YAAA,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;SAC9B;QACD,IAAI,CAAC,SAAS,EAAE,CAAC;AACjB,QAAA,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;KACzB;IAED,KAAK,GAAA;QACH,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AACxB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACvB,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AACtB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC;AAC5C,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC;KAC3C;IAEO,oBAAoB,GAAA;AAC1B,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;SACzB;KACF;IAED,OAAO,GAAA;QACL,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,IAAI,CAAC,IAAI,EAAE,CAAC;KACb;IAED,UAAU,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;IAED,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACpB,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,SAAS,EAAE,CAAC;AACjB,YAAA,IAAI,IAAI,CAAC,cAAc,EAAE;AACvB,gBAAA,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;aAC/B;AACD,YAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;AACzC,YAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;SACzB;KACF;AAED,IAAA,WAAW,CAAC,CAAS,EAAA;AACnB,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE;YAChC,IAAI,CAAC,IAAI,EAAE,CAAC;SACb;QACD,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;KAC5C;IAED,WAAW,GAAA;;AAET,QAAA,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;KACvD;AAED,IAAA,IAAI,SAAS,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;KACrC;IAED,aAAa,GAAA;AACX,QAAA,MAAM,MAAM,GAAkB,IAAI,GAAG,EAAE,CAAC;AACxC,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;;;;AAIrB,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,cAAe,CAAC;YAC3C,aAAa,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,KAAI;AAClC,gBAAA,IAAI,IAAI,KAAK,QAAQ,EAAE;oBACrB,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;iBAC3E;AACH,aAAC,CAAC,CAAC;SACJ;AAED,QAAA,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;KAC/B;;AAGD,IAAA,eAAe,CAAC,SAAiB,EAAA;AAC/B,QAAA,MAAM,OAAO,GAAG,SAAS,KAAK,OAAO,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;QAC3E,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;AAC9B,QAAA,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;KACpB;AACF;;MClMY,mBAAmB,CAAA;AAC9B,IAAA,qBAAqB,CAAC,IAAY,EAAA;;AAEhC,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,OAAO,qBAAqB,CAAC,IAAI,CAAC,CAAC;SACpC;AACD,QAAA,OAAO,IAAI,CAAC;KACb;AAED,IAAA,+BAA+B,CAAC,IAAY,EAAA;;AAE1C,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,MAAM,OAAO,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC;AAC1C,YAAA,OAAO,kCAAkC,CAAC,OAAO,CAAC,CAAC;SACpD;AACD,QAAA,OAAO,IAAI,CAAC;KACb;IAED,cAAc,CAAC,QAAa,EAAE,SAAiB,EAAA;;AAE7C,QAAA,OAAO,KAAK,CAAC;KACd;IAED,eAAe,CAAC,IAAS,EAAE,IAAS,EAAA;AAClC,QAAA,OAAO,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;KACpC;AAED,IAAA,gBAAgB,CAAC,OAAgB,EAAA;AAC/B,QAAA,OAAO,gBAAgB,CAAC,OAAO,CAAC,CAAC;KAClC;AAED,IAAA,KAAK,CAAC,OAAY,EAAE,QAAgB,EAAE,KAAc,EAAA;QAClD,OAAO,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;KAC9C;AAED,IAAA,YAAY,CAAC,OAAY,EAAE,IAAY,EAAE,YAAqB,EAAA;AAC5D,QAAA,OAAO,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;KACpC;AAED,IAAA,OAAO,CACL,OAAY,EACZ,SAA8C,EAC9C,QAAgB,EAChB,KAAa,EACb,MAAc,EACd,eAAA,GAAqC,EAAE,EAAA;AAEvC,QAAA,MAAM,IAAI,GAAG,KAAK,IAAI,CAAC,GAAG,MAAM,GAAG,UAAU,CAAC;QAC9C,MAAM,aAAa,GAAqC,EAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAC,CAAC;;;QAGhF,IAAI,MAAM,EAAE;AACV,YAAA,aAAa,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;SAClC;AAED,QAAA,MAAM,cAAc,GAAkB,IAAI,GAAG,EAAE,CAAC;AAChD,QAAA,MAAM,2BAA2B,IAC/B,eAAe,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,MAAM,YAAY,mBAAmB,CAAC,CAC1E,CAAC;AACF,QAAA,IAAI,8BAA8B,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE;AACnD,YAAA,2BAA2B,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;gBAC7C,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;AAC/E,aAAC,CAAC,CAAC;SACJ;QAED,IAAI,UAAU,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;QAChF,UAAU,GAAG,kCAAkC,CAAC,OAAO,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;QACrF,MAAM,aAAa,GAAG,0BAA0B,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QACtE,OAAO,IAAI,mBAAmB,CAAC,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;KACnF;AACF;;SClFe,YAAY,CAC1B,IAA2B,EAC3B,GAAa,EACb,SAA0C,EAAA;;AAG1C,IAAA,IAAI,IAAI,KAAK,MAAM,EAAE;AACnB,QAAA,OAAO,IAAI,eAAe,CACxB,GAAG,EACH,IAAI,mBAAmB,EAAE,EACzB,IAAI,4BAA4B,EAAE,EAClC,SAAS,CACV,CAAC;KACH;AAED,IAAA,OAAO,IAAI,eAAe,CACxB,GAAG,EACH,IAAI,mBAAmB,EAAE,EACzB,IAAI,4BAA4B,EAAE,EAClC,SAAS,CACV,CAAC;AACJ;;MCZa,SAAS,CAAA;IAEpB,WACU,CAAA,OAAwB,EAChC,KAA8C,EAAA;QADtC,IAAO,CAAA,OAAA,GAAP,OAAO,CAAiB;QAGhC,MAAM,MAAM,GAAY,EAAE,CAAC;QAC3B,MAAM,QAAQ,GAAa,EAAE,CAAC;AAC9B,QAAA,MAAM,GAAG,GAAG,iBAAiB,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;AAChE,QAAA,IAAI,MAAM,CAAC,MAAM,EAAE;AACjB,YAAA,MAAM,gBAAgB,CAAC,MAAM,CAAC,CAAC;SAChC;AACD,QAAA,IAAI,QAAQ,CAAC,MAAM,EAAE;YACnB,cAAc,CAAC,QAAQ,CAAC,CAAC;SAC1B;AACD,QAAA,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC;KAC1B;IAED,cAAc,CACZ,OAAY,EACZ,cAAoD,EACpD,iBAAuD,EACvD,OAAyB,EACzB,eAAuC,EAAA;AAEvC,QAAA,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC;AACzC,cAAE,eAAe,CAAC,cAAc,CAAC;cAChB,cAAc,CAAC;AAClC,QAAA,MAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC;AAC3C,cAAE,eAAe,CAAC,iBAAiB,CAAC;cACnB,iBAAiB,CAAC;QACrC,MAAM,MAAM,GAAQ,EAAE,CAAC;AACvB,QAAA,eAAe,GAAG,eAAe,IAAI,IAAI,qBAAqB,EAAE,CAAC;AACjE,QAAA,MAAM,MAAM,GAAG,uBAAuB,CACpC,IAAI,CAAC,OAAO,EACZ,OAAO,EACP,IAAI,CAAC,aAAa,EAClB,eAAe,EACf,eAAe,EACf,KAAK,EACL,IAAI,EACJ,OAAO,EACP,eAAe,EACf,MAAM,CACP,CAAC;AACF,QAAA,IAAI,MAAM,CAAC,MAAM,EAAE;AACjB,YAAA,MAAM,cAAc,CAAC,MAAM,CAAC,CAAC;SAC9B;AACD,QAAA,OAAO,MAAM,CAAC;KACf;AACF;;ACnED,MAAM,gBAAgB,GAAG,GAAG,CAAC;AAC7B,MAAM,uBAAuB,GAAG,YAAY,CAAC;MAchC,qBAAqB,CAAA;AAKhC,IAAA,WAAA,CACY,WAAmB,EACtB,QAAmB,EACnB,MAAuB,EACtB,UAAuB,EAAA;QAHrB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAQ;QACtB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QACnB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAiB;QACtB,IAAU,CAAA,UAAA,GAAV,UAAU,CAAa;;;AANxB,QAAA,IAAA,CAAA,KAAK,GAAgE,CAAA,qCAAA;KAO1E;AAEJ,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;KAC3B;AAED,IAAA,WAAW,CAAC,IAAS,EAAA;QACnB,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,CAAC;KACnC;IAED,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AACrD,QAAA,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,MAAK;;;YAGxC,cAAc,CAAC,MAAK;AAClB,gBAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;AAC1B,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,UAAU,IAAI,CAAC;KACrB;IAED,aAAa,CAAC,IAAY,EAAE,SAAqC,EAAA;QAC/D,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;KACrD;AAED,IAAA,aAAa,CAAC,KAAa,EAAA;QACzB,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;KAC3C;AAED,IAAA,UAAU,CAAC,KAAa,EAAA;QACtB,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;KACxC;IAED,WAAW,CAAC,MAAW,EAAE,QAAa,EAAA;QACpC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC5C,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;KACjE;IAED,YAAY,CAAC,MAAW,EAAE,QAAa,EAAE,QAAa,EAAE,SAAkB,IAAI,EAAA;QAC5E,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;;AAEvD,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;KAClE;AAED,IAAA,WAAW,CAAC,MAAW,EAAE,QAAa,EAAE,aAAuB,EAAA;AAC7D,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;KACjE;IAED,iBAAiB,CAAC,cAAmB,EAAE,eAAyB,EAAA;QAC9D,OAAO,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;KACzE;AAED,IAAA,UAAU,CAAC,IAAS,EAAA;QAClB,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;KACvC;AAED,IAAA,WAAW,CAAC,IAAS,EAAA;QACnB,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;KACxC;AAED,IAAA,YAAY,CAAC,EAAO,EAAE,IAAY,EAAE,KAAa,EAAE,SAAqC,EAAA;AACtF,QAAA,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;KACxD;AAED,IAAA,eAAe,CAAC,EAAO,EAAE,IAAY,EAAE,SAAqC,EAAA;QAC1E,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;KACpD;IAED,QAAQ,CAAC,EAAO,EAAE,IAAY,EAAA;QAC5B,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;KAClC;IAED,WAAW,CAAC,EAAO,EAAE,IAAY,EAAA;QAC/B,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;KACrC;AAED,IAAA,QAAQ,CAAC,EAAO,EAAE,KAAa,EAAE,KAAU,EAAE,KAAuC,EAAA;AAClF,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;KACjD;AAED,IAAA,WAAW,CAAC,EAAO,EAAE,KAAa,EAAE,KAAuC,EAAA;QACzE,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;KAC7C;AAED,IAAA,WAAW,CAAC,EAAO,EAAE,IAAY,EAAE,KAAU,EAAA;AAC3C,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,gBAAgB,IAAI,IAAI,IAAI,uBAAuB,EAAE;YACzE,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;SACrC;aAAM;YACL,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;SAC5C;KACF;IAED,QAAQ,CAAC,IAAS,EAAE,KAAa,EAAA;QAC/B,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KACrC;AAED,IAAA,MAAM,CAAC,MAAW,EAAE,SAAiB,EAAE,QAAwC,EAAA;AAC7E,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;KAC1D;IAES,iBAAiB,CAAC,OAAY,EAAE,KAAc,EAAA;QACtD,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;KAC/C;AACF,CAAA;AAEK,MAAO,iBAAkB,SAAQ,qBAAqB,CAAA;IAC1D,WACS,CAAA,OAA6C,EACpD,WAAmB,EACnB,QAAmB,EACnB,MAAuB,EACvB,SAAsB,EAAA;QAEtB,KAAK,CAAC,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;QANzC,IAAO,CAAA,OAAA,GAAP,OAAO,CAAsC;AAOpD,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;KAChC;AAEQ,IAAA,WAAW,CAAC,EAAO,EAAE,IAAY,EAAE,KAAU,EAAA;QACpD,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,gBAAgB,EAAE;AACtC,YAAA,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,IAAI,IAAI,uBAAuB,EAAE;AAC5D,gBAAA,KAAK,GAAG,KAAK,KAAK,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC;AAC7C,gBAAA,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,KAAgB,CAAC,CAAC;aAC9C;iBAAM;gBACL,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;aACjE;SACF;aAAM;YACL,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;SAC5C;KACF;AAEQ,IAAA,MAAM,CACb,MAA4C,EAC5C,SAAiB,EACjB,QAA6B,EAAA;QAE7B,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,gBAAgB,EAAE;AAC3C,YAAA,MAAM,OAAO,GAAG,wBAAwB,CAAC,MAAM,CAAC,CAAC;YACjD,IAAI,IAAI,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC9B,IAAI,KAAK,GAAG,EAAE,CAAC;;;YAGf,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,gBAAgB,EAAE;gBACtC,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,wBAAwB,CAAC,IAAI,CAAC,CAAC;aAChD;AACD,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,KAAK,KAAI;gBAC1E,MAAM,OAAO,GAAI,KAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC9C,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;AAClE,aAAC,CAAC,CAAC;SACJ;AACD,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;KAC1D;AACF,CAAA;AAED,SAAS,wBAAwB,CAAC,MAA4C,EAAA;IAC5E,QAAQ,MAAM;AACZ,QAAA,KAAK,MAAM;YACT,OAAO,QAAQ,CAAC,IAAI,CAAC;AACvB,QAAA,KAAK,UAAU;AACb,YAAA,OAAO,QAAQ,CAAC;AAClB,QAAA,KAAK,QAAQ;AACX,YAAA,OAAO,MAAM,CAAC;AAChB,QAAA;AACE,YAAA,OAAO,MAAM,CAAC;KACjB;AACH,CAAC;AAED,SAAS,wBAAwB,CAAC,WAAmB,EAAA;IACnD,MAAM,QAAQ,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC1C,MAAM,OAAO,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;IACnD,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;AAC9C,IAAA,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAC1B;;MC3La,wBAAwB,CAAA;AAOnC,IAAA,WAAA,CACU,QAA0B,EAC1B,MAAuB,EACvB,KAAa,EAAA;QAFb,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAkB;QAC1B,IAAM,CAAA,MAAA,GAAN,MAAM,CAAiB;QACvB,IAAK,CAAA,KAAA,GAAL,KAAK,CAAQ;QATf,IAAU,CAAA,UAAA,GAAW,CAAC,CAAC;QACvB,IAAY,CAAA,YAAA,GAAW,CAAC,CAAC;QACzB,IAAyB,CAAA,yBAAA,GAA6B,EAAE,CAAC;AACzD,QAAA,IAAA,CAAA,cAAc,GAAG,IAAI,GAAG,EAAoC,CAAC;QAC7D,IAAa,CAAA,aAAA,GAAG,CAAC,CAAC;QAOxB,MAAM,CAAC,iBAAiB,GAAG,CAAC,OAAY,EAAE,QAAmB,KAAI;;;;;YAK/D,MAAM,UAAU,GAAG,QAAQ,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC;YACjD,IAAI,UAAU,EAAE;AACd,gBAAA,QAAQ,CAAC,WAAW,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;aAC3C;AACH,SAAC,CAAC;KACH;IAED,cAAc,CAAC,WAAgB,EAAE,IAAmB,EAAA;QAClD,MAAM,kBAAkB,GAAG,EAAE,CAAC;;;AAI9B,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;AACjE,QAAA,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,WAAW,CAAC,EAAE;AAC9C,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC;YAClC,IAAI,QAAQ,GAAsC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACtE,IAAI,CAAC,QAAQ,EAAE;;;gBAGb,MAAM,iBAAiB,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AACvD,gBAAA,QAAQ,GAAG,IAAI,qBAAqB,CAClC,kBAAkB,EAClB,QAAQ,EACR,IAAI,CAAC,MAAM,EACX,iBAAiB,CAClB,CAAC;;AAEF,gBAAA,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;aAC/B;AACD,YAAA,OAAO,QAAQ,CAAC;SACjB;AAED,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,EAAE,CAAC;QAC5B,MAAM,WAAW,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC;QACpD,IAAI,CAAC,UAAU,EAAE,CAAC;QAElB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AAE/C,QAAA,MAAM,eAAe,GAAG,CAAC,OAAuC,KAAI;AAClE,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AAC1B,gBAAA,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;aAClC;iBAAM;AACL,gBAAA,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;aAC3F;AACH,SAAC,CAAC;QACF,MAAM,iBAAiB,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAqC,CAAC;AACrF,QAAA,iBAAiB,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;AAE3C,QAAA,OAAO,IAAI,iBAAiB,CAAC,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;KACxE;IAED,KAAK,GAAA;QACH,IAAI,CAAC,aAAa,EAAE,CAAC;AACrB,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;AACvB,YAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;SACvB;KACF;IAEO,kBAAkB,GAAA;QACxB,cAAc,CAAC,MAAK;YAClB,IAAI,CAAC,YAAY,EAAE,CAAC;AACtB,SAAC,CAAC,CAAC;KACJ;;AAGD,IAAA,wBAAwB,CAAC,KAAa,EAAE,EAAmB,EAAE,IAAS,EAAA;QACpE,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE;AAC3C,YAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;YAC/B,OAAO;SACR;AAED,QAAA,MAAM,wBAAwB,GAAG,IAAI,CAAC,yBAAyB,CAAC;AAChE,QAAA,IAAI,wBAAwB,CAAC,MAAM,IAAI,CAAC,EAAE;YACxC,cAAc,CAAC,MAAK;AAClB,gBAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAK;AAClB,oBAAA,wBAAwB,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;AACzC,wBAAA,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC;wBACzB,EAAE,CAAC,IAAI,CAAC,CAAC;AACX,qBAAC,CAAC,CAAC;AACH,oBAAA,IAAI,CAAC,yBAAyB,GAAG,EAAE,CAAC;AACtC,iBAAC,CAAC,CAAC;AACL,aAAC,CAAC,CAAC;SACJ;QACD,wBAAwB,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;KAC3C;IAED,GAAG,GAAA;QACD,IAAI,CAAC,aAAa,EAAE,CAAC;;;AAIrB,QAAA,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,EAAE;AAC3B,YAAA,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,MAAK;gBAChC,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACvC,aAAC,CAAC,CAAC;SACJ;AACD,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE;AACrB,YAAA,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;SACrB;KACF;IAED,iBAAiB,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC;KACxC;AACF;;ACrID;;;;AAIG;;ACJH;;;;AAIG;;ACJH;;ACRA;;AAEG;;;;"}