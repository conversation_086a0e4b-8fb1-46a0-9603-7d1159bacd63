{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Optional, Input, Directive, QueryList, EventEmitter, TemplateRef, ContentChildren, ViewChild, ContentChild, Output, inject, ChangeDetectorRef, Self, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { FocusKeyManager, isFakeTouchstartFromScreenReader, isFakeMousedownFromScreenReader } from '@angular/cdk/a11y';\nimport { UP_ARROW, DOWN_ARROW, RIGHT_ARROW, LEFT_ARROW, ESCAPE, hasModifierKey, ENTER, SPACE } from '@angular/cdk/keycodes';\nimport { Subject, merge, Subscription, of, asapScheduler } from 'rxjs';\nimport { startWith, switchMap, take, takeUntil, filter, delay } from 'rxjs/operators';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport { MatR<PERSON>ple, MatRippleModule, MatCommonModule } from '@angular/material/core';\nimport { TemplatePortal, DomPortalOutlet } from '@angular/cdk/portal';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i3 from '@angular/cdk/bidi';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\n\n/**\n * Injection token used to provide the parent menu to menu-specific components.\n * @docs-private\n */\nconst _c0 = [\"mat-menu-item\", \"\"];\nconst _c1 = [[[\"mat-icon\"], [\"\", \"matMenuItemIcon\", \"\"]], \"*\"];\nconst _c2 = [\"mat-icon, [matMenuItemIcon]\", \"*\"];\nfunction MatMenuItem_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 2);\n    i0.ɵɵelement(1, \"polygon\", 3);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c3 = [\"*\"];\nfunction MatMenu_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵlistener(\"keydown\", function MatMenu_ng_template_0_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._handleKeydown($event));\n    })(\"click\", function MatMenu_ng_template_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closed.emit(\"click\"));\n    })(\"@transformMenu.start\", function MatMenu_ng_template_0_Template_div_animation_transformMenu_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._onAnimationStart($event));\n    })(\"@transformMenu.done\", function MatMenu_ng_template_0_Template_div_animation_transformMenu_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._onAnimationDone($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵprojection(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1._classList);\n    i0.ɵɵproperty(\"id\", ctx_r1.panelId)(\"@transformMenu\", ctx_r1._panelAnimationState);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.ariaLabel || null)(\"aria-labelledby\", ctx_r1.ariaLabelledby || null)(\"aria-describedby\", ctx_r1.ariaDescribedby || null);\n  }\n}\nconst MAT_MENU_PANEL = new InjectionToken('MAT_MENU_PANEL');\n\n/**\n * Single item inside a `mat-menu`. Provides the menu item styling and accessibility treatment.\n */\nclass MatMenuItem {\n  constructor(_elementRef, _document, _focusMonitor, _parentMenu, _changeDetectorRef) {\n    this._elementRef = _elementRef;\n    this._document = _document;\n    this._focusMonitor = _focusMonitor;\n    this._parentMenu = _parentMenu;\n    this._changeDetectorRef = _changeDetectorRef;\n    /** ARIA role for the menu item. */\n    this.role = 'menuitem';\n    /** Whether the menu item is disabled. */\n    this.disabled = false;\n    /** Whether ripples are disabled on the menu item. */\n    this.disableRipple = false;\n    /** Stream that emits when the menu item is hovered. */\n    this._hovered = new Subject();\n    /** Stream that emits when the menu item is focused. */\n    this._focused = new Subject();\n    /** Whether the menu item is highlighted. */\n    this._highlighted = false;\n    /** Whether the menu item acts as a trigger for a sub-menu. */\n    this._triggersSubmenu = false;\n    _parentMenu?.addItem?.(this);\n  }\n  /** Focuses the menu item. */\n  focus(origin, options) {\n    if (this._focusMonitor && origin) {\n      this._focusMonitor.focusVia(this._getHostElement(), origin, options);\n    } else {\n      this._getHostElement().focus(options);\n    }\n    this._focused.next(this);\n  }\n  ngAfterViewInit() {\n    if (this._focusMonitor) {\n      // Start monitoring the element, so it gets the appropriate focused classes. We want\n      // to show the focus style for menu items only when the focus was not caused by a\n      // mouse or touch interaction.\n      this._focusMonitor.monitor(this._elementRef, false);\n    }\n  }\n  ngOnDestroy() {\n    if (this._focusMonitor) {\n      this._focusMonitor.stopMonitoring(this._elementRef);\n    }\n    if (this._parentMenu && this._parentMenu.removeItem) {\n      this._parentMenu.removeItem(this);\n    }\n    this._hovered.complete();\n    this._focused.complete();\n  }\n  /** Used to set the `tabindex`. */\n  _getTabIndex() {\n    return this.disabled ? '-1' : '0';\n  }\n  /** Returns the host DOM element. */\n  _getHostElement() {\n    return this._elementRef.nativeElement;\n  }\n  /** Prevents the default element actions if it is disabled. */\n  _checkDisabled(event) {\n    if (this.disabled) {\n      event.preventDefault();\n      event.stopPropagation();\n    }\n  }\n  /** Emits to the hover stream. */\n  _handleMouseEnter() {\n    this._hovered.next(this);\n  }\n  /** Gets the label to be used when determining whether the option should be focused. */\n  getLabel() {\n    const clone = this._elementRef.nativeElement.cloneNode(true);\n    const icons = clone.querySelectorAll('mat-icon, .material-icons');\n    // Strip away icons, so they don't show up in the text.\n    for (let i = 0; i < icons.length; i++) {\n      icons[i].remove();\n    }\n    return clone.textContent?.trim() || '';\n  }\n  _setHighlighted(isHighlighted) {\n    // We need to mark this for check for the case where the content is coming from a\n    // `matMenuContent` whose change detection tree is at the declaration position,\n    // not the insertion position. See #23175.\n    // @breaking-change 12.0.0 Remove null check for `_changeDetectorRef`.\n    this._highlighted = isHighlighted;\n    this._changeDetectorRef?.markForCheck();\n  }\n  _setTriggersSubmenu(triggersSubmenu) {\n    // @breaking-change 12.0.0 Remove null check for `_changeDetectorRef`.\n    this._triggersSubmenu = triggersSubmenu;\n    this._changeDetectorRef?.markForCheck();\n  }\n  _hasFocus() {\n    return this._document && this._document.activeElement === this._getHostElement();\n  }\n  static {\n    this.ɵfac = function MatMenuItem_Factory(t) {\n      return new (t || MatMenuItem)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵdirectiveInject(MAT_MENU_PANEL, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatMenuItem,\n      selectors: [[\"\", \"mat-menu-item\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-menu-item\", \"mat-mdc-focus-indicator\"],\n      hostVars: 8,\n      hostBindings: function MatMenuItem_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function MatMenuItem_click_HostBindingHandler($event) {\n            return ctx._checkDisabled($event);\n          })(\"mouseenter\", function MatMenuItem_mouseenter_HostBindingHandler() {\n            return ctx._handleMouseEnter();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"role\", ctx.role)(\"tabindex\", ctx._getTabIndex())(\"aria-disabled\", ctx.disabled)(\"disabled\", ctx.disabled || null);\n          i0.ɵɵclassProp(\"mat-mdc-menu-item-highlighted\", ctx._highlighted)(\"mat-mdc-menu-item-submenu-trigger\", ctx._triggersSubmenu);\n        }\n      },\n      inputs: {\n        role: \"role\",\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute],\n        disableRipple: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disableRipple\", \"disableRipple\", booleanAttribute]\n      },\n      exportAs: [\"matMenuItem\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c0,\n      ngContentSelectors: _c2,\n      decls: 5,\n      vars: 3,\n      consts: [[1, \"mat-mdc-menu-item-text\"], [\"matRipple\", \"\", 1, \"mat-mdc-menu-ripple\", 3, \"matRippleDisabled\", \"matRippleTrigger\"], [\"viewBox\", \"0 0 5 10\", \"focusable\", \"false\", \"aria-hidden\", \"true\", 1, \"mat-mdc-menu-submenu-icon\"], [\"points\", \"0,0 5,5 0,10\"]],\n      template: function MatMenuItem_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c1);\n          i0.ɵɵprojection(0);\n          i0.ɵɵelementStart(1, \"span\", 0);\n          i0.ɵɵprojection(2, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(3, \"div\", 1);\n          i0.ɵɵtemplate(4, MatMenuItem_Conditional_4_Template, 2, 0, \":svg:svg\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"matRippleDisabled\", ctx.disableRipple || ctx.disabled)(\"matRippleTrigger\", ctx._getHostElement());\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(4, ctx._triggersSubmenu ? 4 : -1);\n        }\n      },\n      dependencies: [MatRipple],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenuItem, [{\n    type: Component,\n    args: [{\n      selector: '[mat-menu-item]',\n      exportAs: 'matMenuItem',\n      host: {\n        '[attr.role]': 'role',\n        'class': 'mat-mdc-menu-item mat-mdc-focus-indicator',\n        '[class.mat-mdc-menu-item-highlighted]': '_highlighted',\n        '[class.mat-mdc-menu-item-submenu-trigger]': '_triggersSubmenu',\n        '[attr.tabindex]': '_getTabIndex()',\n        '[attr.aria-disabled]': 'disabled',\n        '[attr.disabled]': 'disabled || null',\n        '(click)': '_checkDisabled($event)',\n        '(mouseenter)': '_handleMouseEnter()'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      standalone: true,\n      imports: [MatRipple],\n      template: \"<ng-content select=\\\"mat-icon, [matMenuItemIcon]\\\"></ng-content>\\n<span class=\\\"mat-mdc-menu-item-text\\\"><ng-content></ng-content></span>\\n<div class=\\\"mat-mdc-menu-ripple\\\" matRipple\\n     [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n     [matRippleTrigger]=\\\"_getHostElement()\\\">\\n</div>\\n\\n@if (_triggersSubmenu) {\\n     <svg\\n       class=\\\"mat-mdc-menu-submenu-icon\\\"\\n       viewBox=\\\"0 0 5 10\\\"\\n       focusable=\\\"false\\\"\\n       aria-hidden=\\\"true\\\"><polygon points=\\\"0,0 5,5 0,10\\\"/></svg>\\n}\\n\"\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i1.FocusMonitor\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_MENU_PANEL]\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    role: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Throws an exception for the case when menu's x-position value isn't valid.\n * In other words, it doesn't match 'before' or 'after'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionX() {\n  throw Error(`xPosition value must be either 'before' or after'.\n      Example: <mat-menu xPosition=\"before\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when menu's y-position value isn't valid.\n * In other words, it doesn't match 'above' or 'below'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionY() {\n  throw Error(`yPosition value must be either 'above' or below'.\n      Example: <mat-menu yPosition=\"above\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when a menu is assigned\n * to a trigger that is placed inside the same menu.\n * @docs-private\n */\nfunction throwMatMenuRecursiveError() {\n  throw Error(`matMenuTriggerFor: menu cannot contain its own trigger. Assign a menu that is ` + `not a parent of the trigger or move the trigger outside of the menu.`);\n}\n\n/**\n * Injection token that can be used to reference instances of `MatMenuContent`. It serves\n * as alternative token to the actual `MatMenuContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_MENU_CONTENT = new InjectionToken('MatMenuContent');\n/** Menu content that will be rendered lazily once the menu is opened. */\nclass MatMenuContent {\n  constructor(_template, _componentFactoryResolver, _appRef, _injector, _viewContainerRef, _document, _changeDetectorRef) {\n    this._template = _template;\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._appRef = _appRef;\n    this._injector = _injector;\n    this._viewContainerRef = _viewContainerRef;\n    this._document = _document;\n    this._changeDetectorRef = _changeDetectorRef;\n    /** Emits when the menu content has been attached. */\n    this._attached = new Subject();\n  }\n  /**\n   * Attaches the content with a particular context.\n   * @docs-private\n   */\n  attach(context = {}) {\n    if (!this._portal) {\n      this._portal = new TemplatePortal(this._template, this._viewContainerRef);\n    }\n    this.detach();\n    if (!this._outlet) {\n      this._outlet = new DomPortalOutlet(this._document.createElement('div'), this._componentFactoryResolver, this._appRef, this._injector);\n    }\n    const element = this._template.elementRef.nativeElement;\n    // Because we support opening the same menu from different triggers (which in turn have their\n    // own `OverlayRef` panel), we have to re-insert the host element every time, otherwise we\n    // risk it staying attached to a pane that's no longer in the DOM.\n    element.parentNode.insertBefore(this._outlet.outletElement, element);\n    // When `MatMenuContent` is used in an `OnPush` component, the insertion of the menu\n    // content via `createEmbeddedView` does not cause the content to be seen as \"dirty\"\n    // by Angular. This causes the `@ContentChildren` for menu items within the menu to\n    // not be updated by Angular. By explicitly marking for check here, we tell Angular that\n    // it needs to check for new menu items and update the `@ContentChild` in `MatMenu`.\n    // @breaking-change 9.0.0 Make change detector ref required\n    this._changeDetectorRef?.markForCheck();\n    this._portal.attach(this._outlet, context);\n    this._attached.next();\n  }\n  /**\n   * Detaches the content.\n   * @docs-private\n   */\n  detach() {\n    if (this._portal.isAttached) {\n      this._portal.detach();\n    }\n  }\n  ngOnDestroy() {\n    if (this._outlet) {\n      this._outlet.dispose();\n    }\n  }\n  static {\n    this.ɵfac = function MatMenuContent_Factory(t) {\n      return new (t || MatMenuContent)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ComponentFactoryResolver), i0.ɵɵdirectiveInject(i0.ApplicationRef), i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatMenuContent,\n      selectors: [[\"ng-template\", \"matMenuContent\", \"\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_MENU_CONTENT,\n        useExisting: MatMenuContent\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenuContent, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[matMenuContent]',\n      providers: [{\n        provide: MAT_MENU_CONTENT,\n        useExisting: MatMenuContent\n      }],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }, {\n    type: i0.ComponentFactoryResolver\n  }, {\n    type: i0.ApplicationRef\n  }, {\n    type: i0.Injector\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }], null);\n})();\n\n/**\n * Animations used by the mat-menu component.\n * Animation duration and timing values are based on:\n * https://material.io/guidelines/components/menus.html#menus-usage\n * @docs-private\n */\nconst matMenuAnimations = {\n  /**\n   * This animation controls the menu panel's entry and exit from the page.\n   *\n   * When the menu panel is added to the DOM, it scales in and fades in its border.\n   *\n   * When the menu panel is removed from the DOM, it simply fades out after a brief\n   * delay to display the ripple.\n   */\n  transformMenu: trigger('transformMenu', [state('void', style({\n    opacity: 0,\n    transform: 'scale(0.8)'\n  })), transition('void => enter', animate('120ms cubic-bezier(0, 0, 0.2, 1)', style({\n    opacity: 1,\n    transform: 'scale(1)'\n  }))), transition('* => void', animate('100ms 25ms linear', style({\n    opacity: 0\n  })))]),\n  /**\n   * This animation fades in the background color and content of the menu panel\n   * after its containing element is scaled in.\n   */\n  fadeInItems: trigger('fadeInItems', [\n  // TODO(crisbeto): this is inside the `transformMenu`\n  // now. Remove next time we do breaking changes.\n  state('showing', style({\n    opacity: 1\n  })), transition('void => *', [style({\n    opacity: 0\n  }), animate('400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)')])])\n};\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst fadeInItems = matMenuAnimations.fadeInItems;\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst transformMenu = matMenuAnimations.transformMenu;\nlet menuPanelUid = 0;\n/** Injection token to be used to override the default options for `mat-menu`. */\nconst MAT_MENU_DEFAULT_OPTIONS = new InjectionToken('mat-menu-default-options', {\n  providedIn: 'root',\n  factory: MAT_MENU_DEFAULT_OPTIONS_FACTORY\n});\n/** @docs-private */\nfunction MAT_MENU_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    overlapTrigger: false,\n    xPosition: 'after',\n    yPosition: 'below',\n    backdropClass: 'cdk-overlay-transparent-backdrop'\n  };\n}\nclass MatMenu {\n  /** Position of the menu in the X axis. */\n  get xPosition() {\n    return this._xPosition;\n  }\n  set xPosition(value) {\n    if (value !== 'before' && value !== 'after' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwMatMenuInvalidPositionX();\n    }\n    this._xPosition = value;\n    this.setPositionClasses();\n  }\n  /** Position of the menu in the Y axis. */\n  get yPosition() {\n    return this._yPosition;\n  }\n  set yPosition(value) {\n    if (value !== 'above' && value !== 'below' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwMatMenuInvalidPositionY();\n    }\n    this._yPosition = value;\n    this.setPositionClasses();\n  }\n  /**\n   * This method takes classes set on the host mat-menu element and applies them on the\n   * menu template that displays in the overlay container.  Otherwise, it's difficult\n   * to style the containing menu from outside the component.\n   * @param classes list of class names\n   */\n  set panelClass(classes) {\n    const previousPanelClass = this._previousPanelClass;\n    const newClassList = {\n      ...this._classList\n    };\n    if (previousPanelClass && previousPanelClass.length) {\n      previousPanelClass.split(' ').forEach(className => {\n        newClassList[className] = false;\n      });\n    }\n    this._previousPanelClass = classes;\n    if (classes && classes.length) {\n      classes.split(' ').forEach(className => {\n        newClassList[className] = true;\n      });\n      this._elementRef.nativeElement.className = '';\n    }\n    this._classList = newClassList;\n  }\n  /**\n   * This method takes classes set on the host mat-menu element and applies them on the\n   * menu template that displays in the overlay container.  Otherwise, it's difficult\n   * to style the containing menu from outside the component.\n   * @deprecated Use `panelClass` instead.\n   * @breaking-change 8.0.0\n   */\n  get classList() {\n    return this.panelClass;\n  }\n  set classList(classes) {\n    this.panelClass = classes;\n  }\n  constructor(_elementRef, _ngZone, defaultOptions,\n  // @breaking-change 15.0.0 `_changeDetectorRef` to become a required parameter.\n  _changeDetectorRef) {\n    this._elementRef = _elementRef;\n    this._ngZone = _ngZone;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._elevationPrefix = 'mat-elevation-z';\n    this._baseElevation = 8;\n    /** Only the direct descendant menu items. */\n    this._directDescendantItems = new QueryList();\n    /** Classes to be applied to the menu panel. */\n    this._classList = {};\n    /** Current state of the panel animation. */\n    this._panelAnimationState = 'void';\n    /** Emits whenever an animation on the menu completes. */\n    this._animationDone = new Subject();\n    /** Event emitted when the menu is closed. */\n    this.closed = new EventEmitter();\n    /**\n     * Event emitted when the menu is closed.\n     * @deprecated Switch to `closed` instead\n     * @breaking-change 8.0.0\n     */\n    this.close = this.closed;\n    this.panelId = `mat-menu-panel-${menuPanelUid++}`;\n    this.overlayPanelClass = defaultOptions.overlayPanelClass || '';\n    this._xPosition = defaultOptions.xPosition;\n    this._yPosition = defaultOptions.yPosition;\n    this.backdropClass = defaultOptions.backdropClass;\n    this.overlapTrigger = defaultOptions.overlapTrigger;\n    this.hasBackdrop = defaultOptions.hasBackdrop;\n  }\n  ngOnInit() {\n    this.setPositionClasses();\n  }\n  ngAfterContentInit() {\n    this._updateDirectDescendants();\n    this._keyManager = new FocusKeyManager(this._directDescendantItems).withWrap().withTypeAhead().withHomeAndEnd();\n    this._keyManager.tabOut.subscribe(() => this.closed.emit('tab'));\n    // If a user manually (programmatically) focuses a menu item, we need to reflect that focus\n    // change back to the key manager. Note that we don't need to unsubscribe here because _focused\n    // is internal and we know that it gets completed on destroy.\n    this._directDescendantItems.changes.pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map(item => item._focused)))).subscribe(focusedItem => this._keyManager.updateActiveItem(focusedItem));\n    this._directDescendantItems.changes.subscribe(itemsList => {\n      // Move focus to another item, if the active item is removed from the list.\n      // We need to debounce the callback, because multiple items might be removed\n      // in quick succession.\n      const manager = this._keyManager;\n      if (this._panelAnimationState === 'enter' && manager.activeItem?._hasFocus()) {\n        const items = itemsList.toArray();\n        const index = Math.max(0, Math.min(items.length - 1, manager.activeItemIndex || 0));\n        if (items[index] && !items[index].disabled) {\n          manager.setActiveItem(index);\n        } else {\n          manager.setNextItemActive();\n        }\n      }\n    });\n  }\n  ngOnDestroy() {\n    this._keyManager?.destroy();\n    this._directDescendantItems.destroy();\n    this.closed.complete();\n    this._firstItemFocusSubscription?.unsubscribe();\n  }\n  /** Stream that emits whenever the hovered menu item changes. */\n  _hovered() {\n    // Coerce the `changes` property because Angular types it as `Observable<any>`\n    const itemChanges = this._directDescendantItems.changes;\n    return itemChanges.pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map(item => item._hovered))));\n  }\n  /*\n   * Registers a menu item with the menu.\n   * @docs-private\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 9.0.0\n   */\n  addItem(_item) {}\n  /**\n   * Removes an item from the menu.\n   * @docs-private\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 9.0.0\n   */\n  removeItem(_item) {}\n  /** Handle a keyboard event from the menu, delegating to the appropriate action. */\n  _handleKeydown(event) {\n    const keyCode = event.keyCode;\n    const manager = this._keyManager;\n    switch (keyCode) {\n      case ESCAPE:\n        if (!hasModifierKey(event)) {\n          event.preventDefault();\n          this.closed.emit('keydown');\n        }\n        break;\n      case LEFT_ARROW:\n        if (this.parentMenu && this.direction === 'ltr') {\n          this.closed.emit('keydown');\n        }\n        break;\n      case RIGHT_ARROW:\n        if (this.parentMenu && this.direction === 'rtl') {\n          this.closed.emit('keydown');\n        }\n        break;\n      default:\n        if (keyCode === UP_ARROW || keyCode === DOWN_ARROW) {\n          manager.setFocusOrigin('keyboard');\n        }\n        manager.onKeydown(event);\n        return;\n    }\n    // Don't allow the event to propagate if we've already handled it, or it may\n    // end up reaching other overlays that were opened earlier (see #22694).\n    event.stopPropagation();\n  }\n  /**\n   * Focus the first item in the menu.\n   * @param origin Action from which the focus originated. Used to set the correct styling.\n   */\n  focusFirstItem(origin = 'program') {\n    // Wait for `onStable` to ensure iOS VoiceOver screen reader focuses the first item (#24735).\n    this._firstItemFocusSubscription?.unsubscribe();\n    this._firstItemFocusSubscription = this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n      let menuPanel = null;\n      if (this._directDescendantItems.length) {\n        // Because the `mat-menuPanel` is at the DOM insertion point, not inside the overlay, we don't\n        // have a nice way of getting a hold of the menuPanel panel. We can't use a `ViewChild` either\n        // because the panel is inside an `ng-template`. We work around it by starting from one of\n        // the items and walking up the DOM.\n        menuPanel = this._directDescendantItems.first._getHostElement().closest('[role=\"menu\"]');\n      }\n      // If an item in the menuPanel is already focused, avoid overriding the focus.\n      if (!menuPanel || !menuPanel.contains(document.activeElement)) {\n        const manager = this._keyManager;\n        manager.setFocusOrigin(origin).setFirstItemActive();\n        // If there's no active item at this point, it means that all the items are disabled.\n        // Move focus to the menuPanel panel so keyboard events like Escape still work. Also this will\n        // give _some_ feedback to screen readers.\n        if (!manager.activeItem && menuPanel) {\n          menuPanel.focus();\n        }\n      }\n    });\n  }\n  /**\n   * Resets the active item in the menu. This is used when the menu is opened, allowing\n   * the user to start from the first option when pressing the down arrow.\n   */\n  resetActiveItem() {\n    this._keyManager.setActiveItem(-1);\n  }\n  /**\n   * Sets the menu panel elevation.\n   * @param depth Number of parent menus that come before the menu.\n   */\n  setElevation(depth) {\n    // The elevation starts at the base and increases by one for each level.\n    // Capped at 24 because that's the maximum elevation defined in the Material design spec.\n    const elevation = Math.min(this._baseElevation + depth, 24);\n    const newElevation = `${this._elevationPrefix}${elevation}`;\n    const customElevation = Object.keys(this._classList).find(className => {\n      return className.startsWith(this._elevationPrefix);\n    });\n    if (!customElevation || customElevation === this._previousElevation) {\n      const newClassList = {\n        ...this._classList\n      };\n      if (this._previousElevation) {\n        newClassList[this._previousElevation] = false;\n      }\n      newClassList[newElevation] = true;\n      this._previousElevation = newElevation;\n      this._classList = newClassList;\n    }\n  }\n  /**\n   * Adds classes to the menu panel based on its position. Can be used by\n   * consumers to add specific styling based on the position.\n   * @param posX Position of the menu along the x axis.\n   * @param posY Position of the menu along the y axis.\n   * @docs-private\n   */\n  setPositionClasses(posX = this.xPosition, posY = this.yPosition) {\n    this._classList = {\n      ...this._classList,\n      ['mat-menu-before']: posX === 'before',\n      ['mat-menu-after']: posX === 'after',\n      ['mat-menu-above']: posY === 'above',\n      ['mat-menu-below']: posY === 'below'\n    };\n    // @breaking-change 15.0.0 Remove null check for `_changeDetectorRef`.\n    this._changeDetectorRef?.markForCheck();\n  }\n  /** Starts the enter animation. */\n  _startAnimation() {\n    // @breaking-change 8.0.0 Combine with _resetAnimation.\n    this._panelAnimationState = 'enter';\n  }\n  /** Resets the panel animation to its initial state. */\n  _resetAnimation() {\n    // @breaking-change 8.0.0 Combine with _startAnimation.\n    this._panelAnimationState = 'void';\n  }\n  /** Callback that is invoked when the panel animation completes. */\n  _onAnimationDone(event) {\n    this._animationDone.next(event);\n    this._isAnimating = false;\n  }\n  _onAnimationStart(event) {\n    this._isAnimating = true;\n    // Scroll the content element to the top as soon as the animation starts. This is necessary,\n    // because we move focus to the first item while it's still being animated, which can throw\n    // the browser off when it determines the scroll position. Alternatively we can move focus\n    // when the animation is done, however moving focus asynchronously will interrupt screen\n    // readers which are in the process of reading out the menu already. We take the `element`\n    // from the `event` since we can't use a `ViewChild` to access the pane.\n    if (event.toState === 'enter' && this._keyManager.activeItemIndex === 0) {\n      event.element.scrollTop = 0;\n    }\n  }\n  /**\n   * Sets up a stream that will keep track of any newly-added menu items and will update the list\n   * of direct descendants. We collect the descendants this way, because `_allItems` can include\n   * items that are part of child menus, and using a custom way of registering items is unreliable\n   * when it comes to maintaining the item order.\n   */\n  _updateDirectDescendants() {\n    this._allItems.changes.pipe(startWith(this._allItems)).subscribe(items => {\n      this._directDescendantItems.reset(items.filter(item => item._parentMenu === this));\n      this._directDescendantItems.notifyOnChanges();\n    });\n  }\n  static {\n    this.ɵfac = function MatMenu_Factory(t) {\n      return new (t || MatMenu)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(MAT_MENU_DEFAULT_OPTIONS), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatMenu,\n      selectors: [[\"mat-menu\"]],\n      contentQueries: function MatMenu_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MAT_MENU_CONTENT, 5);\n          i0.ɵɵcontentQuery(dirIndex, MatMenuItem, 5);\n          i0.ɵɵcontentQuery(dirIndex, MatMenuItem, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.lazyContent = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allItems = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.items = _t);\n        }\n      },\n      viewQuery: function MatMenu_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TemplateRef, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateRef = _t.first);\n        }\n      },\n      hostVars: 3,\n      hostBindings: function MatMenu_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-label\", null)(\"aria-labelledby\", null)(\"aria-describedby\", null);\n        }\n      },\n      inputs: {\n        backdropClass: \"backdropClass\",\n        ariaLabel: [i0.ɵɵInputFlags.None, \"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [i0.ɵɵInputFlags.None, \"aria-labelledby\", \"ariaLabelledby\"],\n        ariaDescribedby: [i0.ɵɵInputFlags.None, \"aria-describedby\", \"ariaDescribedby\"],\n        xPosition: \"xPosition\",\n        yPosition: \"yPosition\",\n        overlapTrigger: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"overlapTrigger\", \"overlapTrigger\", booleanAttribute],\n        hasBackdrop: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"hasBackdrop\", \"hasBackdrop\", value => value == null ? null : booleanAttribute(value)],\n        panelClass: [i0.ɵɵInputFlags.None, \"class\", \"panelClass\"],\n        classList: \"classList\"\n      },\n      outputs: {\n        closed: \"closed\",\n        close: \"close\"\n      },\n      exportAs: [\"matMenu\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_MENU_PANEL,\n        useExisting: MatMenu\n      }]), i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c3,\n      decls: 1,\n      vars: 0,\n      consts: [[\"tabindex\", \"-1\", \"role\", \"menu\", 1, \"mat-mdc-menu-panel\", \"mat-mdc-elevation-specific\", 3, \"keydown\", \"click\", \"id\"], [1, \"mat-mdc-menu-content\"]],\n      template: function MatMenu_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, MatMenu_ng_template_0_Template, 3, 7, \"ng-template\");\n        }\n      },\n      styles: [\"mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;list-style-type:none}.mat-mdc-menu-content:focus{outline:none}.mat-mdc-menu-content,.mat-mdc-menu-content .mat-mdc-menu-item .mat-mdc-menu-item-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;flex:1;white-space:normal;font-family:var(--mat-menu-item-label-text-font);line-height:var(--mat-menu-item-label-text-line-height);font-size:var(--mat-menu-item-label-text-size);letter-spacing:var(--mat-menu-item-label-text-tracking);font-weight:var(--mat-menu-item-label-text-weight)}.mat-mdc-menu-panel{min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;box-sizing:border-box;outline:0;border-radius:var(--mat-menu-container-shape);background-color:var(--mat-menu-container-color);will-change:transform,opacity}.mat-mdc-menu-panel.ng-animating{pointer-events:none}.cdk-high-contrast-active .mat-mdc-menu-panel{outline:solid 1px}.mat-divider{color:var(--mat-menu-divider-color);margin-bottom:var(--mat-menu-divider-bottom-spacing);margin-top:var(--mat-menu-divider-top-spacing)}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:var(--mat-menu-item-leading-spacing);padding-right:var(--mat-menu-item-trailing-spacing);-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;align-items:center;min-height:48px}.mat-mdc-menu-item:focus{outline:none}[dir=rtl] .mat-mdc-menu-item,.mat-mdc-menu-item[dir=rtl]{padding-left:var(--mat-menu-item-trailing-spacing);padding-right:var(--mat-menu-item-leading-spacing)}.mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-leading-spacing);padding-right:var(--mat-menu-item-with-icon-trailing-spacing)}[dir=rtl] .mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]),.mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon])[dir=rtl]{padding-left:var(--mat-menu-item-with-icon-trailing-spacing);padding-right:var(--mat-menu-item-with-icon-leading-spacing)}.mat-mdc-menu-item::-moz-focus-inner{border:0}.mat-mdc-menu-item,.mat-mdc-menu-item:visited,.mat-mdc-menu-item:link{color:var(--mat-menu-item-label-text-color)}.mat-mdc-menu-item .mat-icon-no-color,.mat-mdc-menu-item .mat-mdc-menu-submenu-icon{color:var(--mat-menu-item-icon-color)}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item .mat-icon{flex-shrink:0;margin-right:var(--mat-menu-item-spacing);height:var(--mat-menu-item-icon-size);width:var(--mat-menu-item-icon-size)}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:var(--mat-menu-item-spacing)}.mat-mdc-menu-item:not([disabled]):hover{background-color:var(--mat-menu-item-hover-state-layer-color)}.mat-mdc-menu-item:not([disabled]).cdk-program-focused,.mat-mdc-menu-item:not([disabled]).cdk-keyboard-focused,.mat-mdc-menu-item:not([disabled]).mat-mdc-menu-item-highlighted{background-color:var(--mat-menu-item-focus-state-layer-color)}.cdk-high-contrast-active .mat-mdc-menu-item{margin-top:1px}.mat-mdc-menu-submenu-icon{width:var(--mat-menu-item-icon-size);height:10px;fill:currentColor;padding-left:var(--mat-menu-item-spacing)}[dir=rtl] .mat-mdc-menu-submenu-icon{padding-right:var(--mat-menu-item-spacing);padding-left:0}[dir=rtl] .mat-mdc-menu-submenu-icon polygon{transform:scaleX(-1)}.cdk-high-contrast-active .mat-mdc-menu-submenu-icon{fill:CanvasText}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\"],\n      encapsulation: 2,\n      data: {\n        animation: [matMenuAnimations.transformMenu, matMenuAnimations.fadeInItems]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenu, [{\n    type: Component,\n    args: [{\n      selector: 'mat-menu',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matMenu',\n      host: {\n        '[attr.aria-label]': 'null',\n        '[attr.aria-labelledby]': 'null',\n        '[attr.aria-describedby]': 'null'\n      },\n      animations: [matMenuAnimations.transformMenu, matMenuAnimations.fadeInItems],\n      providers: [{\n        provide: MAT_MENU_PANEL,\n        useExisting: MatMenu\n      }],\n      standalone: true,\n      template: \"<ng-template>\\n  <div\\n    class=\\\"mat-mdc-menu-panel mat-mdc-elevation-specific\\\"\\n    [id]=\\\"panelId\\\"\\n    [class]=\\\"_classList\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\"\\n    (click)=\\\"closed.emit('click')\\\"\\n    [@transformMenu]=\\\"_panelAnimationState\\\"\\n    (@transformMenu.start)=\\\"_onAnimationStart($event)\\\"\\n    (@transformMenu.done)=\\\"_onAnimationDone($event)\\\"\\n    tabindex=\\\"-1\\\"\\n    role=\\\"menu\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"ariaLabelledby || null\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby || null\\\">\\n    <div class=\\\"mat-mdc-menu-content\\\">\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</ng-template>\\n\",\n      styles: [\"mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;list-style-type:none}.mat-mdc-menu-content:focus{outline:none}.mat-mdc-menu-content,.mat-mdc-menu-content .mat-mdc-menu-item .mat-mdc-menu-item-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;flex:1;white-space:normal;font-family:var(--mat-menu-item-label-text-font);line-height:var(--mat-menu-item-label-text-line-height);font-size:var(--mat-menu-item-label-text-size);letter-spacing:var(--mat-menu-item-label-text-tracking);font-weight:var(--mat-menu-item-label-text-weight)}.mat-mdc-menu-panel{min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;box-sizing:border-box;outline:0;border-radius:var(--mat-menu-container-shape);background-color:var(--mat-menu-container-color);will-change:transform,opacity}.mat-mdc-menu-panel.ng-animating{pointer-events:none}.cdk-high-contrast-active .mat-mdc-menu-panel{outline:solid 1px}.mat-divider{color:var(--mat-menu-divider-color);margin-bottom:var(--mat-menu-divider-bottom-spacing);margin-top:var(--mat-menu-divider-top-spacing)}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:var(--mat-menu-item-leading-spacing);padding-right:var(--mat-menu-item-trailing-spacing);-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;align-items:center;min-height:48px}.mat-mdc-menu-item:focus{outline:none}[dir=rtl] .mat-mdc-menu-item,.mat-mdc-menu-item[dir=rtl]{padding-left:var(--mat-menu-item-trailing-spacing);padding-right:var(--mat-menu-item-leading-spacing)}.mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-leading-spacing);padding-right:var(--mat-menu-item-with-icon-trailing-spacing)}[dir=rtl] .mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]),.mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon])[dir=rtl]{padding-left:var(--mat-menu-item-with-icon-trailing-spacing);padding-right:var(--mat-menu-item-with-icon-leading-spacing)}.mat-mdc-menu-item::-moz-focus-inner{border:0}.mat-mdc-menu-item,.mat-mdc-menu-item:visited,.mat-mdc-menu-item:link{color:var(--mat-menu-item-label-text-color)}.mat-mdc-menu-item .mat-icon-no-color,.mat-mdc-menu-item .mat-mdc-menu-submenu-icon{color:var(--mat-menu-item-icon-color)}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item .mat-icon{flex-shrink:0;margin-right:var(--mat-menu-item-spacing);height:var(--mat-menu-item-icon-size);width:var(--mat-menu-item-icon-size)}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:var(--mat-menu-item-spacing)}.mat-mdc-menu-item:not([disabled]):hover{background-color:var(--mat-menu-item-hover-state-layer-color)}.mat-mdc-menu-item:not([disabled]).cdk-program-focused,.mat-mdc-menu-item:not([disabled]).cdk-keyboard-focused,.mat-mdc-menu-item:not([disabled]).mat-mdc-menu-item-highlighted{background-color:var(--mat-menu-item-focus-state-layer-color)}.cdk-high-contrast-active .mat-mdc-menu-item{margin-top:1px}.mat-mdc-menu-submenu-icon{width:var(--mat-menu-item-icon-size);height:10px;fill:currentColor;padding-left:var(--mat-menu-item-spacing)}[dir=rtl] .mat-mdc-menu-submenu-icon{padding-right:var(--mat-menu-item-spacing);padding-left:0}[dir=rtl] .mat-mdc-menu-submenu-icon polygon{transform:scaleX(-1)}.cdk-high-contrast-active .mat-mdc-menu-submenu-icon{fill:CanvasText}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\"]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_MENU_DEFAULT_OPTIONS]\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    _allItems: [{\n      type: ContentChildren,\n      args: [MatMenuItem, {\n        descendants: true\n      }]\n    }],\n    backdropClass: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    ariaDescribedby: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    xPosition: [{\n      type: Input\n    }],\n    yPosition: [{\n      type: Input\n    }],\n    templateRef: [{\n      type: ViewChild,\n      args: [TemplateRef]\n    }],\n    items: [{\n      type: ContentChildren,\n      args: [MatMenuItem, {\n        descendants: false\n      }]\n    }],\n    lazyContent: [{\n      type: ContentChild,\n      args: [MAT_MENU_CONTENT]\n    }],\n    overlapTrigger: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    hasBackdrop: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? null : booleanAttribute(value)\n      }]\n    }],\n    panelClass: [{\n      type: Input,\n      args: ['class']\n    }],\n    classList: [{\n      type: Input\n    }],\n    closed: [{\n      type: Output\n    }],\n    close: [{\n      type: Output\n    }]\n  });\n})();\n\n/** Injection token that determines the scroll handling while the menu is open. */\nconst MAT_MENU_SCROLL_STRATEGY = new InjectionToken('mat-menu-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.reposition();\n  }\n});\n/** @docs-private */\nfunction MAT_MENU_SCROLL_STRATEGY_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/** @docs-private */\nconst MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_MENU_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_MENU_SCROLL_STRATEGY_FACTORY\n};\n/** Options for binding a passive event listener. */\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true\n});\n/**\n * Default top padding of the menu panel.\n * @deprecated No longer being used. Will be removed.\n * @breaking-change 15.0.0\n */\nconst MENU_PANEL_TOP_PADDING = 8;\n/** Directive applied to an element that should trigger a `mat-menu`. */\nclass MatMenuTrigger {\n  /**\n   * @deprecated\n   * @breaking-change 8.0.0\n   */\n  get _deprecatedMatMenuTriggerFor() {\n    return this.menu;\n  }\n  set _deprecatedMatMenuTriggerFor(v) {\n    this.menu = v;\n  }\n  /** References the menu instance that the trigger is associated with. */\n  get menu() {\n    return this._menu;\n  }\n  set menu(menu) {\n    if (menu === this._menu) {\n      return;\n    }\n    this._menu = menu;\n    this._menuCloseSubscription.unsubscribe();\n    if (menu) {\n      if (menu === this._parentMaterialMenu && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwMatMenuRecursiveError();\n      }\n      this._menuCloseSubscription = menu.close.subscribe(reason => {\n        this._destroyMenu(reason);\n        // If a click closed the menu, we should close the entire chain of nested menus.\n        if ((reason === 'click' || reason === 'tab') && this._parentMaterialMenu) {\n          this._parentMaterialMenu.closed.emit(reason);\n        }\n      });\n    }\n    this._menuItemInstance?._setTriggersSubmenu(this.triggersSubmenu());\n  }\n  constructor(_overlay, _element, _viewContainerRef, scrollStrategy, parentMenu,\n  // `MatMenuTrigger` is commonly used in combination with a `MatMenuItem`.\n  // tslint:disable-next-line: lightweight-tokens\n  _menuItemInstance, _dir, _focusMonitor, _ngZone) {\n    this._overlay = _overlay;\n    this._element = _element;\n    this._viewContainerRef = _viewContainerRef;\n    this._menuItemInstance = _menuItemInstance;\n    this._dir = _dir;\n    this._focusMonitor = _focusMonitor;\n    this._ngZone = _ngZone;\n    this._overlayRef = null;\n    this._menuOpen = false;\n    this._closingActionsSubscription = Subscription.EMPTY;\n    this._hoverSubscription = Subscription.EMPTY;\n    this._menuCloseSubscription = Subscription.EMPTY;\n    this._changeDetectorRef = inject(ChangeDetectorRef);\n    /**\n     * Handles touch start events on the trigger.\n     * Needs to be an arrow function so we can easily use addEventListener and removeEventListener.\n     */\n    this._handleTouchStart = event => {\n      if (!isFakeTouchstartFromScreenReader(event)) {\n        this._openedBy = 'touch';\n      }\n    };\n    // Tracking input type is necessary so it's possible to only auto-focus\n    // the first item of the list when the menu is opened via the keyboard\n    this._openedBy = undefined;\n    /**\n     * Whether focus should be restored when the menu is closed.\n     * Note that disabling this option can have accessibility implications\n     * and it's up to you to manage focus, if you decide to turn it off.\n     */\n    this.restoreFocus = true;\n    /** Event emitted when the associated menu is opened. */\n    this.menuOpened = new EventEmitter();\n    /**\n     * Event emitted when the associated menu is opened.\n     * @deprecated Switch to `menuOpened` instead\n     * @breaking-change 8.0.0\n     */\n    // tslint:disable-next-line:no-output-on-prefix\n    this.onMenuOpen = this.menuOpened;\n    /** Event emitted when the associated menu is closed. */\n    this.menuClosed = new EventEmitter();\n    /**\n     * Event emitted when the associated menu is closed.\n     * @deprecated Switch to `menuClosed` instead\n     * @breaking-change 8.0.0\n     */\n    // tslint:disable-next-line:no-output-on-prefix\n    this.onMenuClose = this.menuClosed;\n    this._scrollStrategy = scrollStrategy;\n    this._parentMaterialMenu = parentMenu instanceof MatMenu ? parentMenu : undefined;\n    _element.nativeElement.addEventListener('touchstart', this._handleTouchStart, passiveEventListenerOptions);\n  }\n  ngAfterContentInit() {\n    this._handleHover();\n  }\n  ngOnDestroy() {\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n      this._overlayRef = null;\n    }\n    this._element.nativeElement.removeEventListener('touchstart', this._handleTouchStart, passiveEventListenerOptions);\n    this._menuCloseSubscription.unsubscribe();\n    this._closingActionsSubscription.unsubscribe();\n    this._hoverSubscription.unsubscribe();\n  }\n  /** Whether the menu is open. */\n  get menuOpen() {\n    return this._menuOpen;\n  }\n  /** The text direction of the containing app. */\n  get dir() {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n  /** Whether the menu triggers a sub-menu or a top-level one. */\n  triggersSubmenu() {\n    return !!(this._menuItemInstance && this._parentMaterialMenu && this.menu);\n  }\n  /** Toggles the menu between the open and closed states. */\n  toggleMenu() {\n    return this._menuOpen ? this.closeMenu() : this.openMenu();\n  }\n  /** Opens the menu. */\n  openMenu() {\n    const menu = this.menu;\n    if (this._menuOpen || !menu) {\n      return;\n    }\n    const overlayRef = this._createOverlay(menu);\n    const overlayConfig = overlayRef.getConfig();\n    const positionStrategy = overlayConfig.positionStrategy;\n    this._setPosition(menu, positionStrategy);\n    overlayConfig.hasBackdrop = menu.hasBackdrop == null ? !this.triggersSubmenu() : menu.hasBackdrop;\n    overlayRef.attach(this._getPortal(menu));\n    if (menu.lazyContent) {\n      menu.lazyContent.attach(this.menuData);\n    }\n    this._closingActionsSubscription = this._menuClosingActions().subscribe(() => this.closeMenu());\n    this._initMenu(menu);\n    if (menu instanceof MatMenu) {\n      menu._startAnimation();\n      menu._directDescendantItems.changes.pipe(takeUntil(menu.close)).subscribe(() => {\n        // Re-adjust the position without locking when the amount of items\n        // changes so that the overlay is allowed to pick a new optimal position.\n        positionStrategy.withLockedPosition(false).reapplyLastPosition();\n        positionStrategy.withLockedPosition(true);\n      });\n    }\n  }\n  /** Closes the menu. */\n  closeMenu() {\n    this.menu?.close.emit();\n  }\n  /**\n   * Focuses the menu trigger.\n   * @param origin Source of the menu trigger's focus.\n   */\n  focus(origin, options) {\n    if (this._focusMonitor && origin) {\n      this._focusMonitor.focusVia(this._element, origin, options);\n    } else {\n      this._element.nativeElement.focus(options);\n    }\n  }\n  /**\n   * Updates the position of the menu to ensure that it fits all options within the viewport.\n   */\n  updatePosition() {\n    this._overlayRef?.updatePosition();\n  }\n  /** Closes the menu and does the necessary cleanup. */\n  _destroyMenu(reason) {\n    if (!this._overlayRef || !this.menuOpen) {\n      return;\n    }\n    const menu = this.menu;\n    this._closingActionsSubscription.unsubscribe();\n    this._overlayRef.detach();\n    // Always restore focus if the user is navigating using the keyboard or the menu was opened\n    // programmatically. We don't restore for non-root triggers, because it can prevent focus\n    // from making it back to the root trigger when closing a long chain of menus by clicking\n    // on the backdrop.\n    if (this.restoreFocus && (reason === 'keydown' || !this._openedBy || !this.triggersSubmenu())) {\n      this.focus(this._openedBy);\n    }\n    this._openedBy = undefined;\n    if (menu instanceof MatMenu) {\n      menu._resetAnimation();\n      if (menu.lazyContent) {\n        // Wait for the exit animation to finish before detaching the content.\n        menu._animationDone.pipe(filter(event => event.toState === 'void'), take(1),\n        // Interrupt if the content got re-attached.\n        takeUntil(menu.lazyContent._attached)).subscribe({\n          next: () => menu.lazyContent.detach(),\n          // No matter whether the content got re-attached, reset the menu.\n          complete: () => this._setIsMenuOpen(false)\n        });\n      } else {\n        this._setIsMenuOpen(false);\n      }\n    } else {\n      this._setIsMenuOpen(false);\n      menu?.lazyContent?.detach();\n    }\n  }\n  /**\n   * This method sets the menu state to open and focuses the first item if\n   * the menu was opened via the keyboard.\n   */\n  _initMenu(menu) {\n    menu.parentMenu = this.triggersSubmenu() ? this._parentMaterialMenu : undefined;\n    menu.direction = this.dir;\n    this._setMenuElevation(menu);\n    menu.focusFirstItem(this._openedBy || 'program');\n    this._setIsMenuOpen(true);\n  }\n  /** Updates the menu elevation based on the amount of parent menus that it has. */\n  _setMenuElevation(menu) {\n    if (menu.setElevation) {\n      let depth = 0;\n      let parentMenu = menu.parentMenu;\n      while (parentMenu) {\n        depth++;\n        parentMenu = parentMenu.parentMenu;\n      }\n      menu.setElevation(depth);\n    }\n  }\n  // set state rather than toggle to support triggers sharing a menu\n  _setIsMenuOpen(isOpen) {\n    if (isOpen !== this._menuOpen) {\n      this._menuOpen = isOpen;\n      this._menuOpen ? this.menuOpened.emit() : this.menuClosed.emit();\n      if (this.triggersSubmenu()) {\n        this._menuItemInstance._setHighlighted(isOpen);\n      }\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * This method creates the overlay from the provided menu's template and saves its\n   * OverlayRef so that it can be attached to the DOM when openMenu is called.\n   */\n  _createOverlay(menu) {\n    if (!this._overlayRef) {\n      const config = this._getOverlayConfig(menu);\n      this._subscribeToPositions(menu, config.positionStrategy);\n      this._overlayRef = this._overlay.create(config);\n      // Consume the `keydownEvents` in order to prevent them from going to another overlay.\n      // Ideally we'd also have our keyboard event logic in here, however doing so will\n      // break anybody that may have implemented the `MatMenuPanel` themselves.\n      this._overlayRef.keydownEvents().subscribe();\n    }\n    return this._overlayRef;\n  }\n  /**\n   * This method builds the configuration object needed to create the overlay, the OverlayState.\n   * @returns OverlayConfig\n   */\n  _getOverlayConfig(menu) {\n    return new OverlayConfig({\n      positionStrategy: this._overlay.position().flexibleConnectedTo(this._element).withLockedPosition().withGrowAfterOpen().withTransformOriginOn('.mat-menu-panel, .mat-mdc-menu-panel'),\n      backdropClass: menu.backdropClass || 'cdk-overlay-transparent-backdrop',\n      panelClass: menu.overlayPanelClass,\n      scrollStrategy: this._scrollStrategy(),\n      direction: this._dir\n    });\n  }\n  /**\n   * Listens to changes in the position of the overlay and sets the correct classes\n   * on the menu based on the new position. This ensures the animation origin is always\n   * correct, even if a fallback position is used for the overlay.\n   */\n  _subscribeToPositions(menu, position) {\n    if (menu.setPositionClasses) {\n      position.positionChanges.subscribe(change => {\n        const posX = change.connectionPair.overlayX === 'start' ? 'after' : 'before';\n        const posY = change.connectionPair.overlayY === 'top' ? 'below' : 'above';\n        // @breaking-change 15.0.0 Remove null check for `ngZone`.\n        // `positionChanges` fires outside of the `ngZone` and `setPositionClasses` might be\n        // updating something in the view so we need to bring it back in.\n        if (this._ngZone) {\n          this._ngZone.run(() => menu.setPositionClasses(posX, posY));\n        } else {\n          menu.setPositionClasses(posX, posY);\n        }\n      });\n    }\n  }\n  /**\n   * Sets the appropriate positions on a position strategy\n   * so the overlay connects with the trigger correctly.\n   * @param positionStrategy Strategy whose position to update.\n   */\n  _setPosition(menu, positionStrategy) {\n    let [originX, originFallbackX] = menu.xPosition === 'before' ? ['end', 'start'] : ['start', 'end'];\n    let [overlayY, overlayFallbackY] = menu.yPosition === 'above' ? ['bottom', 'top'] : ['top', 'bottom'];\n    let [originY, originFallbackY] = [overlayY, overlayFallbackY];\n    let [overlayX, overlayFallbackX] = [originX, originFallbackX];\n    let offsetY = 0;\n    if (this.triggersSubmenu()) {\n      // When the menu is a sub-menu, it should always align itself\n      // to the edges of the trigger, instead of overlapping it.\n      overlayFallbackX = originX = menu.xPosition === 'before' ? 'start' : 'end';\n      originFallbackX = overlayX = originX === 'end' ? 'start' : 'end';\n      if (this._parentMaterialMenu) {\n        if (this._parentInnerPadding == null) {\n          const firstItem = this._parentMaterialMenu.items.first;\n          this._parentInnerPadding = firstItem ? firstItem._getHostElement().offsetTop : 0;\n        }\n        offsetY = overlayY === 'bottom' ? this._parentInnerPadding : -this._parentInnerPadding;\n      }\n    } else if (!menu.overlapTrigger) {\n      originY = overlayY === 'top' ? 'bottom' : 'top';\n      originFallbackY = overlayFallbackY === 'top' ? 'bottom' : 'top';\n    }\n    positionStrategy.withPositions([{\n      originX,\n      originY,\n      overlayX,\n      overlayY,\n      offsetY\n    }, {\n      originX: originFallbackX,\n      originY,\n      overlayX: overlayFallbackX,\n      overlayY,\n      offsetY\n    }, {\n      originX,\n      originY: originFallbackY,\n      overlayX,\n      overlayY: overlayFallbackY,\n      offsetY: -offsetY\n    }, {\n      originX: originFallbackX,\n      originY: originFallbackY,\n      overlayX: overlayFallbackX,\n      overlayY: overlayFallbackY,\n      offsetY: -offsetY\n    }]);\n  }\n  /** Returns a stream that emits whenever an action that should close the menu occurs. */\n  _menuClosingActions() {\n    const backdrop = this._overlayRef.backdropClick();\n    const detachments = this._overlayRef.detachments();\n    const parentClose = this._parentMaterialMenu ? this._parentMaterialMenu.closed : of();\n    const hover = this._parentMaterialMenu ? this._parentMaterialMenu._hovered().pipe(filter(active => active !== this._menuItemInstance), filter(() => this._menuOpen)) : of();\n    return merge(backdrop, parentClose, hover, detachments);\n  }\n  /** Handles mouse presses on the trigger. */\n  _handleMousedown(event) {\n    if (!isFakeMousedownFromScreenReader(event)) {\n      // Since right or middle button clicks won't trigger the `click` event,\n      // we shouldn't consider the menu as opened by mouse in those cases.\n      this._openedBy = event.button === 0 ? 'mouse' : undefined;\n      // Since clicking on the trigger won't close the menu if it opens a sub-menu,\n      // we should prevent focus from moving onto it via click to avoid the\n      // highlight from lingering on the menu item.\n      if (this.triggersSubmenu()) {\n        event.preventDefault();\n      }\n    }\n  }\n  /** Handles key presses on the trigger. */\n  _handleKeydown(event) {\n    const keyCode = event.keyCode;\n    // Pressing enter on the trigger will trigger the click handler later.\n    if (keyCode === ENTER || keyCode === SPACE) {\n      this._openedBy = 'keyboard';\n    }\n    if (this.triggersSubmenu() && (keyCode === RIGHT_ARROW && this.dir === 'ltr' || keyCode === LEFT_ARROW && this.dir === 'rtl')) {\n      this._openedBy = 'keyboard';\n      this.openMenu();\n    }\n  }\n  /** Handles click events on the trigger. */\n  _handleClick(event) {\n    if (this.triggersSubmenu()) {\n      // Stop event propagation to avoid closing the parent menu.\n      event.stopPropagation();\n      this.openMenu();\n    } else {\n      this.toggleMenu();\n    }\n  }\n  /** Handles the cases where the user hovers over the trigger. */\n  _handleHover() {\n    // Subscribe to changes in the hovered item in order to toggle the panel.\n    if (!this.triggersSubmenu() || !this._parentMaterialMenu) {\n      return;\n    }\n    this._hoverSubscription = this._parentMaterialMenu._hovered()\n    // Since we might have multiple competing triggers for the same menu (e.g. a sub-menu\n    // with different data and triggers), we have to delay it by a tick to ensure that\n    // it won't be closed immediately after it is opened.\n    .pipe(filter(active => active === this._menuItemInstance && !active.disabled), delay(0, asapScheduler)).subscribe(() => {\n      this._openedBy = 'mouse';\n      // If the same menu is used between multiple triggers, it might still be animating\n      // while the new trigger tries to re-open it. Wait for the animation to finish\n      // before doing so. Also interrupt if the user moves to another item.\n      if (this.menu instanceof MatMenu && this.menu._isAnimating) {\n        // We need the `delay(0)` here in order to avoid\n        // 'changed after checked' errors in some cases. See #12194.\n        this.menu._animationDone.pipe(take(1), delay(0, asapScheduler), takeUntil(this._parentMaterialMenu._hovered())).subscribe(() => this.openMenu());\n      } else {\n        this.openMenu();\n      }\n    });\n  }\n  /** Gets the portal that should be attached to the overlay. */\n  _getPortal(menu) {\n    // Note that we can avoid this check by keeping the portal on the menu panel.\n    // While it would be cleaner, we'd have to introduce another required method on\n    // `MatMenuPanel`, making it harder to consume.\n    if (!this._portal || this._portal.templateRef !== menu.templateRef) {\n      this._portal = new TemplatePortal(menu.templateRef, this._viewContainerRef);\n    }\n    return this._portal;\n  }\n  static {\n    this.ɵfac = function MatMenuTrigger_Factory(t) {\n      return new (t || MatMenuTrigger)(i0.ɵɵdirectiveInject(i1$1.Overlay), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(MAT_MENU_SCROLL_STRATEGY), i0.ɵɵdirectiveInject(MAT_MENU_PANEL, 8), i0.ɵɵdirectiveInject(MatMenuItem, 10), i0.ɵɵdirectiveInject(i3.Directionality, 8), i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatMenuTrigger,\n      selectors: [[\"\", \"mat-menu-trigger-for\", \"\"], [\"\", \"matMenuTriggerFor\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-menu-trigger\"],\n      hostVars: 3,\n      hostBindings: function MatMenuTrigger_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function MatMenuTrigger_click_HostBindingHandler($event) {\n            return ctx._handleClick($event);\n          })(\"mousedown\", function MatMenuTrigger_mousedown_HostBindingHandler($event) {\n            return ctx._handleMousedown($event);\n          })(\"keydown\", function MatMenuTrigger_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-haspopup\", ctx.menu ? \"menu\" : null)(\"aria-expanded\", ctx.menuOpen)(\"aria-controls\", ctx.menuOpen ? ctx.menu.panelId : null);\n        }\n      },\n      inputs: {\n        _deprecatedMatMenuTriggerFor: [i0.ɵɵInputFlags.None, \"mat-menu-trigger-for\", \"_deprecatedMatMenuTriggerFor\"],\n        menu: [i0.ɵɵInputFlags.None, \"matMenuTriggerFor\", \"menu\"],\n        menuData: [i0.ɵɵInputFlags.None, \"matMenuTriggerData\", \"menuData\"],\n        restoreFocus: [i0.ɵɵInputFlags.None, \"matMenuTriggerRestoreFocus\", \"restoreFocus\"]\n      },\n      outputs: {\n        menuOpened: \"menuOpened\",\n        onMenuOpen: \"onMenuOpen\",\n        menuClosed: \"menuClosed\",\n        onMenuClose: \"onMenuClose\"\n      },\n      exportAs: [\"matMenuTrigger\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenuTrigger, [{\n    type: Directive,\n    args: [{\n      selector: `[mat-menu-trigger-for], [matMenuTriggerFor]`,\n      host: {\n        'class': 'mat-mdc-menu-trigger',\n        '[attr.aria-haspopup]': 'menu ? \"menu\" : null',\n        '[attr.aria-expanded]': 'menuOpen',\n        '[attr.aria-controls]': 'menuOpen ? menu.panelId : null',\n        '(click)': '_handleClick($event)',\n        '(mousedown)': '_handleMousedown($event)',\n        '(keydown)': '_handleKeydown($event)'\n      },\n      exportAs: 'matMenuTrigger',\n      standalone: true\n    }]\n  }], () => [{\n    type: i1$1.Overlay\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_MENU_SCROLL_STRATEGY]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_MENU_PANEL]\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: MatMenuItem,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }]\n  }, {\n    type: i3.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i1.FocusMonitor\n  }, {\n    type: i0.NgZone\n  }], {\n    _deprecatedMatMenuTriggerFor: [{\n      type: Input,\n      args: ['mat-menu-trigger-for']\n    }],\n    menu: [{\n      type: Input,\n      args: ['matMenuTriggerFor']\n    }],\n    menuData: [{\n      type: Input,\n      args: ['matMenuTriggerData']\n    }],\n    restoreFocus: [{\n      type: Input,\n      args: ['matMenuTriggerRestoreFocus']\n    }],\n    menuOpened: [{\n      type: Output\n    }],\n    onMenuOpen: [{\n      type: Output\n    }],\n    menuClosed: [{\n      type: Output\n    }],\n    onMenuClose: [{\n      type: Output\n    }]\n  });\n})();\nclass MatMenuModule {\n  static {\n    this.ɵfac = function MatMenuModule_Factory(t) {\n      return new (t || MatMenuModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatMenuModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER],\n      imports: [CommonModule, MatRippleModule, MatCommonModule, OverlayModule, CdkScrollableModule, MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenuModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, MatRippleModule, MatCommonModule, OverlayModule, MatMenu, MatMenuItem, MatMenuContent, MatMenuTrigger],\n      exports: [CdkScrollableModule, MatMenu, MatCommonModule, MatMenuItem, MatMenuContent, MatMenuTrigger],\n      providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_MENU_CONTENT, MAT_MENU_DEFAULT_OPTIONS, MAT_MENU_PANEL, MAT_MENU_SCROLL_STRATEGY, MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER, MENU_PANEL_TOP_PADDING, MatMenu, MatMenuContent, MatMenuItem, MatMenuModule, MatMenuTrigger, fadeInItems, matMenuAnimations, transformMenu };", "map": {"version": 3, "names": ["i0", "InjectionToken", "booleanAttribute", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "Optional", "Input", "Directive", "QueryList", "EventEmitter", "TemplateRef", "ContentChildren", "ViewChild", "ContentChild", "Output", "inject", "ChangeDetectorRef", "Self", "NgModule", "i1", "FocusKeyManager", "isFakeTouchstartFromScreenReader", "isFakeMousedownFromScreenReader", "UP_ARROW", "DOWN_ARROW", "RIGHT_ARROW", "LEFT_ARROW", "ESCAPE", "hasModifierKey", "ENTER", "SPACE", "Subject", "merge", "Subscription", "of", "asapScheduler", "startWith", "switchMap", "take", "takeUntil", "filter", "delay", "DOCUMENT", "CommonModule", "<PERSON><PERSON><PERSON><PERSON>", "MatRippleModule", "MatCommonModule", "TemplatePortal", "DomPortalOutlet", "trigger", "state", "style", "transition", "animate", "i3", "i1$1", "Overlay", "OverlayConfig", "OverlayModule", "normalizePassiveListenerOptions", "CdkScrollableModule", "_c0", "_c1", "_c2", "MatMenuItem_Conditional_4_Template", "rf", "ctx", "ɵɵnamespaceSVG", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "_c3", "MatMenu_ng_template_0_Template", "_r1", "ɵɵgetCurrentView", "ɵɵlistener", "MatMenu_ng_template_0_Template_div_keydown_0_listener", "$event", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "_handleKeydown", "MatMenu_ng_template_0_Template_div_click_0_listener", "closed", "emit", "MatMenu_ng_template_0_Template_div_animation_transformMenu_start_0_listener", "_onAnimationStart", "MatMenu_ng_template_0_Template_div_animation_transformMenu_done_0_listener", "_onAnimationDone", "ɵɵprojection", "ɵɵclassMap", "_classList", "ɵɵproperty", "panelId", "_panelAnimationState", "ɵɵattribute", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON><PERSON>", "MAT_MENU_PANEL", "MatMenuItem", "constructor", "_elementRef", "_document", "_focusMonitor", "_parentMenu", "_changeDetectorRef", "role", "disabled", "disable<PERSON><PERSON><PERSON>", "_hovered", "_focused", "_highlighted", "_triggersSubmenu", "addItem", "focus", "origin", "options", "focusVia", "_getHostElement", "next", "ngAfterViewInit", "monitor", "ngOnDestroy", "stopMonitoring", "removeItem", "complete", "_getTabIndex", "nativeElement", "_checkDisabled", "event", "preventDefault", "stopPropagation", "_handleMouseEnter", "get<PERSON><PERSON><PERSON>", "clone", "cloneNode", "icons", "querySelectorAll", "i", "length", "remove", "textContent", "trim", "_setHighlighted", "isHighlighted", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_setTriggersSubmenu", "triggersSubmenu", "_hasFocus", "activeElement", "ɵfac", "MatMenuItem_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "FocusMonitor", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatMenuItem_HostBindings", "MatMenuItem_click_HostBindingHandler", "MatMenuItem_mouseenter_HostBindingHandler", "ɵɵclassProp", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "exportAs", "standalone", "features", "ɵɵInputTransformsFeature", "ɵɵStandaloneFeature", "attrs", "ngContentSelectors", "decls", "vars", "consts", "template", "MatMenuItem_Template", "ɵɵprojectionDef", "ɵɵtemplate", "ɵɵadvance", "ɵɵconditional", "dependencies", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "OnPush", "None", "imports", "undefined", "decorators", "transform", "throwMatMenuInvalidPositionX", "Error", "throwMatMenuInvalidPositionY", "throwMatMenuRecursiveError", "MAT_MENU_CONTENT", "Mat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_template", "_componentFactoryResolver", "_appRef", "_injector", "_viewContainerRef", "_attached", "attach", "context", "_portal", "detach", "_outlet", "createElement", "element", "elementRef", "parentNode", "insertBefore", "outletElement", "isAttached", "dispose", "MatMenuContent_Factory", "ComponentFactoryResolver", "ApplicationRef", "Injector", "ViewContainerRef", "ɵdir", "ɵɵdefineDirective", "ɵɵProvidersFeature", "provide", "useExisting", "providers", "matMenuAnimations", "transformMenu", "opacity", "fadeInItems", "menuPanelUid", "MAT_MENU_DEFAULT_OPTIONS", "providedIn", "factory", "MAT_MENU_DEFAULT_OPTIONS_FACTORY", "overlapTrigger", "xPosition", "yPosition", "backdropClass", "MatMenu", "_xPosition", "value", "setPositionClasses", "_yPosition", "panelClass", "classes", "previousPanelClass", "_previousPanelClass", "newClassList", "split", "for<PERSON>ach", "className", "classList", "_ngZone", "defaultOptions", "_elevationPrefix", "_baseElevation", "_directDescendantItems", "_animationDone", "close", "overlayPanelClass", "hasBackdrop", "ngOnInit", "ngAfterContentInit", "_updateDirectDescendants", "_keyManager", "withWrap", "withTypeAhead", "withHomeAndEnd", "tabOut", "subscribe", "changes", "pipe", "items", "map", "item", "focusedItem", "updateActiveItem", "itemsList", "manager", "activeItem", "toArray", "index", "Math", "max", "min", "activeItemIndex", "setActiveItem", "setNextItemActive", "destroy", "_firstItemFocusSubscription", "unsubscribe", "itemChanges", "_item", "keyCode", "parentMenu", "direction", "setFocusOrigin", "onKeydown", "focusFirstItem", "onStable", "menuPanel", "first", "closest", "contains", "document", "setFirstItemActive", "resetActiveItem", "setElevation", "depth", "elevation", "newElevation", "customElevation", "Object", "keys", "find", "startsWith", "_previousElevation", "posX", "posY", "_startAnimation", "_resetAnimation", "_isAnimating", "toState", "scrollTop", "_allItems", "reset", "notifyOn<PERSON><PERSON>es", "MatMenu_Factory", "NgZone", "contentQueries", "MatMenu_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "lazyContent", "viewQuery", "MatMenu_Query", "ɵɵviewQuery", "templateRef", "MatMenu_HostBindings", "outputs", "MatMenu_Template", "styles", "data", "animation", "animations", "descendants", "MAT_MENU_SCROLL_STRATEGY", "overlay", "scrollStrategies", "reposition", "MAT_MENU_SCROLL_STRATEGY_FACTORY", "MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER", "deps", "useFactory", "passiveEventListenerOptions", "passive", "MENU_PANEL_TOP_PADDING", "MatMenuTrigger", "_deprecatedMatMenuTriggerFor", "menu", "v", "_menu", "_menuCloseSubscription", "_parentMaterialMenu", "reason", "_destroyMenu", "_menuItemInstance", "_overlay", "_element", "scrollStrategy", "_dir", "_overlayRef", "_menuOpen", "_closingActionsSubscription", "EMPTY", "_hoverSubscription", "_handleTouchStart", "_openedBy", "restoreFocus", "menuOpened", "onMenuOpen", "menuClosed", "onMenuClose", "_scrollStrategy", "addEventListener", "_handleHover", "removeEventListener", "menuOpen", "dir", "toggleMenu", "closeMenu", "openMenu", "overlayRef", "_createOverlay", "overlayConfig", "getConfig", "positionStrategy", "_setPosition", "_getPortal", "menuData", "_menuClosingActions", "_initMenu", "withLockedPosition", "reapplyLastPosition", "updatePosition", "_setIsMenuOpen", "_setMenuElevation", "isOpen", "config", "_getOverlayConfig", "_subscribeToPositions", "create", "keydownEvents", "position", "flexibleConnectedTo", "withGrowAfterOpen", "withTransformOriginOn", "position<PERSON><PERSON>es", "change", "connectionPair", "overlayX", "overlayY", "run", "originX", "originFallbackX", "overlayFallbackY", "originY", "originFallbackY", "overlayFallbackX", "offsetY", "_parentInnerPadding", "firstItem", "offsetTop", "withPositions", "backdrop", "backdropClick", "detachments", "parentClose", "hover", "active", "_handleMousedown", "button", "_handleClick", "MatMenuTrigger_Factory", "Directionality", "MatMenuTrigger_HostBindings", "MatMenuTrigger_click_HostBindingHandler", "MatMenuTrigger_mousedown_HostBindingHandler", "MatMenuTrigger_keydown_HostBindingHandler", "MatMenuModule", "MatMenuModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports"], "sources": ["C:/Users/<USER>/Desktop/BookCart/bookcart-frontend/node_modules/@angular/material/fesm2022/menu.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Optional, Input, Directive, QueryList, EventEmitter, TemplateRef, ContentChildren, ViewChild, ContentChild, Output, inject, ChangeDetectorRef, Self, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { FocusKeyManager, isFakeTouchstartFromScreenReader, isFakeMousedownFromScreenReader } from '@angular/cdk/a11y';\nimport { UP_ARROW, DOWN_ARROW, RIGHT_ARROW, LEFT_ARROW, ESCAPE, hasModifierKey, ENTER, SPACE } from '@angular/cdk/keycodes';\nimport { Subject, merge, Subscription, of, asapScheduler } from 'rxjs';\nimport { startWith, switchMap, take, takeUntil, filter, delay } from 'rxjs/operators';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport { MatR<PERSON>ple, MatRippleModule, MatCommonModule } from '@angular/material/core';\nimport { TemplatePortal, DomPortalOutlet } from '@angular/cdk/portal';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i3 from '@angular/cdk/bidi';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\n\n/**\n * Injection token used to provide the parent menu to menu-specific components.\n * @docs-private\n */\nconst MAT_MENU_PANEL = new InjectionToken('MAT_MENU_PANEL');\n\n/**\n * Single item inside a `mat-menu`. Provides the menu item styling and accessibility treatment.\n */\nclass MatMenuItem {\n    constructor(_elementRef, _document, _focusMonitor, _parentMenu, _changeDetectorRef) {\n        this._elementRef = _elementRef;\n        this._document = _document;\n        this._focusMonitor = _focusMonitor;\n        this._parentMenu = _parentMenu;\n        this._changeDetectorRef = _changeDetectorRef;\n        /** ARIA role for the menu item. */\n        this.role = 'menuitem';\n        /** Whether the menu item is disabled. */\n        this.disabled = false;\n        /** Whether ripples are disabled on the menu item. */\n        this.disableRipple = false;\n        /** Stream that emits when the menu item is hovered. */\n        this._hovered = new Subject();\n        /** Stream that emits when the menu item is focused. */\n        this._focused = new Subject();\n        /** Whether the menu item is highlighted. */\n        this._highlighted = false;\n        /** Whether the menu item acts as a trigger for a sub-menu. */\n        this._triggersSubmenu = false;\n        _parentMenu?.addItem?.(this);\n    }\n    /** Focuses the menu item. */\n    focus(origin, options) {\n        if (this._focusMonitor && origin) {\n            this._focusMonitor.focusVia(this._getHostElement(), origin, options);\n        }\n        else {\n            this._getHostElement().focus(options);\n        }\n        this._focused.next(this);\n    }\n    ngAfterViewInit() {\n        if (this._focusMonitor) {\n            // Start monitoring the element, so it gets the appropriate focused classes. We want\n            // to show the focus style for menu items only when the focus was not caused by a\n            // mouse or touch interaction.\n            this._focusMonitor.monitor(this._elementRef, false);\n        }\n    }\n    ngOnDestroy() {\n        if (this._focusMonitor) {\n            this._focusMonitor.stopMonitoring(this._elementRef);\n        }\n        if (this._parentMenu && this._parentMenu.removeItem) {\n            this._parentMenu.removeItem(this);\n        }\n        this._hovered.complete();\n        this._focused.complete();\n    }\n    /** Used to set the `tabindex`. */\n    _getTabIndex() {\n        return this.disabled ? '-1' : '0';\n    }\n    /** Returns the host DOM element. */\n    _getHostElement() {\n        return this._elementRef.nativeElement;\n    }\n    /** Prevents the default element actions if it is disabled. */\n    _checkDisabled(event) {\n        if (this.disabled) {\n            event.preventDefault();\n            event.stopPropagation();\n        }\n    }\n    /** Emits to the hover stream. */\n    _handleMouseEnter() {\n        this._hovered.next(this);\n    }\n    /** Gets the label to be used when determining whether the option should be focused. */\n    getLabel() {\n        const clone = this._elementRef.nativeElement.cloneNode(true);\n        const icons = clone.querySelectorAll('mat-icon, .material-icons');\n        // Strip away icons, so they don't show up in the text.\n        for (let i = 0; i < icons.length; i++) {\n            icons[i].remove();\n        }\n        return clone.textContent?.trim() || '';\n    }\n    _setHighlighted(isHighlighted) {\n        // We need to mark this for check for the case where the content is coming from a\n        // `matMenuContent` whose change detection tree is at the declaration position,\n        // not the insertion position. See #23175.\n        // @breaking-change 12.0.0 Remove null check for `_changeDetectorRef`.\n        this._highlighted = isHighlighted;\n        this._changeDetectorRef?.markForCheck();\n    }\n    _setTriggersSubmenu(triggersSubmenu) {\n        // @breaking-change 12.0.0 Remove null check for `_changeDetectorRef`.\n        this._triggersSubmenu = triggersSubmenu;\n        this._changeDetectorRef?.markForCheck();\n    }\n    _hasFocus() {\n        return this._document && this._document.activeElement === this._getHostElement();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatMenuItem, deps: [{ token: i0.ElementRef }, { token: DOCUMENT }, { token: i1.FocusMonitor }, { token: MAT_MENU_PANEL, optional: true }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.2.0\", type: MatMenuItem, isStandalone: true, selector: \"[mat-menu-item]\", inputs: { role: \"role\", disabled: [\"disabled\", \"disabled\", booleanAttribute], disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute] }, host: { listeners: { \"click\": \"_checkDisabled($event)\", \"mouseenter\": \"_handleMouseEnter()\" }, properties: { \"attr.role\": \"role\", \"class.mat-mdc-menu-item-highlighted\": \"_highlighted\", \"class.mat-mdc-menu-item-submenu-trigger\": \"_triggersSubmenu\", \"attr.tabindex\": \"_getTabIndex()\", \"attr.aria-disabled\": \"disabled\", \"attr.disabled\": \"disabled || null\" }, classAttribute: \"mat-mdc-menu-item mat-mdc-focus-indicator\" }, exportAs: [\"matMenuItem\"], ngImport: i0, template: \"<ng-content select=\\\"mat-icon, [matMenuItemIcon]\\\"></ng-content>\\n<span class=\\\"mat-mdc-menu-item-text\\\"><ng-content></ng-content></span>\\n<div class=\\\"mat-mdc-menu-ripple\\\" matRipple\\n     [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n     [matRippleTrigger]=\\\"_getHostElement()\\\">\\n</div>\\n\\n@if (_triggersSubmenu) {\\n     <svg\\n       class=\\\"mat-mdc-menu-submenu-icon\\\"\\n       viewBox=\\\"0 0 5 10\\\"\\n       focusable=\\\"false\\\"\\n       aria-hidden=\\\"true\\\"><polygon points=\\\"0,0 5,5 0,10\\\"/></svg>\\n}\\n\", dependencies: [{ kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatMenuItem, decorators: [{\n            type: Component,\n            args: [{ selector: '[mat-menu-item]', exportAs: 'matMenuItem', host: {\n                        '[attr.role]': 'role',\n                        'class': 'mat-mdc-menu-item mat-mdc-focus-indicator',\n                        '[class.mat-mdc-menu-item-highlighted]': '_highlighted',\n                        '[class.mat-mdc-menu-item-submenu-trigger]': '_triggersSubmenu',\n                        '[attr.tabindex]': '_getTabIndex()',\n                        '[attr.aria-disabled]': 'disabled',\n                        '[attr.disabled]': 'disabled || null',\n                        '(click)': '_checkDisabled($event)',\n                        '(mouseenter)': '_handleMouseEnter()',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, standalone: true, imports: [MatRipple], template: \"<ng-content select=\\\"mat-icon, [matMenuItemIcon]\\\"></ng-content>\\n<span class=\\\"mat-mdc-menu-item-text\\\"><ng-content></ng-content></span>\\n<div class=\\\"mat-mdc-menu-ripple\\\" matRipple\\n     [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n     [matRippleTrigger]=\\\"_getHostElement()\\\">\\n</div>\\n\\n@if (_triggersSubmenu) {\\n     <svg\\n       class=\\\"mat-mdc-menu-submenu-icon\\\"\\n       viewBox=\\\"0 0 5 10\\\"\\n       focusable=\\\"false\\\"\\n       aria-hidden=\\\"true\\\"><polygon points=\\\"0,0 5,5 0,10\\\"/></svg>\\n}\\n\" }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1.FocusMonitor }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_MENU_PANEL]\n                }, {\n                    type: Optional\n                }] }, { type: i0.ChangeDetectorRef }], propDecorators: { role: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\n/**\n * Throws an exception for the case when menu's x-position value isn't valid.\n * In other words, it doesn't match 'before' or 'after'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionX() {\n    throw Error(`xPosition value must be either 'before' or after'.\n      Example: <mat-menu xPosition=\"before\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when menu's y-position value isn't valid.\n * In other words, it doesn't match 'above' or 'below'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionY() {\n    throw Error(`yPosition value must be either 'above' or below'.\n      Example: <mat-menu yPosition=\"above\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when a menu is assigned\n * to a trigger that is placed inside the same menu.\n * @docs-private\n */\nfunction throwMatMenuRecursiveError() {\n    throw Error(`matMenuTriggerFor: menu cannot contain its own trigger. Assign a menu that is ` +\n        `not a parent of the trigger or move the trigger outside of the menu.`);\n}\n\n/**\n * Injection token that can be used to reference instances of `MatMenuContent`. It serves\n * as alternative token to the actual `MatMenuContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_MENU_CONTENT = new InjectionToken('MatMenuContent');\n/** Menu content that will be rendered lazily once the menu is opened. */\nclass MatMenuContent {\n    constructor(_template, _componentFactoryResolver, _appRef, _injector, _viewContainerRef, _document, _changeDetectorRef) {\n        this._template = _template;\n        this._componentFactoryResolver = _componentFactoryResolver;\n        this._appRef = _appRef;\n        this._injector = _injector;\n        this._viewContainerRef = _viewContainerRef;\n        this._document = _document;\n        this._changeDetectorRef = _changeDetectorRef;\n        /** Emits when the menu content has been attached. */\n        this._attached = new Subject();\n    }\n    /**\n     * Attaches the content with a particular context.\n     * @docs-private\n     */\n    attach(context = {}) {\n        if (!this._portal) {\n            this._portal = new TemplatePortal(this._template, this._viewContainerRef);\n        }\n        this.detach();\n        if (!this._outlet) {\n            this._outlet = new DomPortalOutlet(this._document.createElement('div'), this._componentFactoryResolver, this._appRef, this._injector);\n        }\n        const element = this._template.elementRef.nativeElement;\n        // Because we support opening the same menu from different triggers (which in turn have their\n        // own `OverlayRef` panel), we have to re-insert the host element every time, otherwise we\n        // risk it staying attached to a pane that's no longer in the DOM.\n        element.parentNode.insertBefore(this._outlet.outletElement, element);\n        // When `MatMenuContent` is used in an `OnPush` component, the insertion of the menu\n        // content via `createEmbeddedView` does not cause the content to be seen as \"dirty\"\n        // by Angular. This causes the `@ContentChildren` for menu items within the menu to\n        // not be updated by Angular. By explicitly marking for check here, we tell Angular that\n        // it needs to check for new menu items and update the `@ContentChild` in `MatMenu`.\n        // @breaking-change 9.0.0 Make change detector ref required\n        this._changeDetectorRef?.markForCheck();\n        this._portal.attach(this._outlet, context);\n        this._attached.next();\n    }\n    /**\n     * Detaches the content.\n     * @docs-private\n     */\n    detach() {\n        if (this._portal.isAttached) {\n            this._portal.detach();\n        }\n    }\n    ngOnDestroy() {\n        if (this._outlet) {\n            this._outlet.dispose();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatMenuContent, deps: [{ token: i0.TemplateRef }, { token: i0.ComponentFactoryResolver }, { token: i0.ApplicationRef }, { token: i0.Injector }, { token: i0.ViewContainerRef }, { token: DOCUMENT }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatMenuContent, isStandalone: true, selector: \"ng-template[matMenuContent]\", providers: [{ provide: MAT_MENU_CONTENT, useExisting: MatMenuContent }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatMenuContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[matMenuContent]',\n                    providers: [{ provide: MAT_MENU_CONTENT, useExisting: MatMenuContent }],\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }, { type: i0.ComponentFactoryResolver }, { type: i0.ApplicationRef }, { type: i0.Injector }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i0.ChangeDetectorRef }] });\n\n/**\n * Animations used by the mat-menu component.\n * Animation duration and timing values are based on:\n * https://material.io/guidelines/components/menus.html#menus-usage\n * @docs-private\n */\nconst matMenuAnimations = {\n    /**\n     * This animation controls the menu panel's entry and exit from the page.\n     *\n     * When the menu panel is added to the DOM, it scales in and fades in its border.\n     *\n     * When the menu panel is removed from the DOM, it simply fades out after a brief\n     * delay to display the ripple.\n     */\n    transformMenu: trigger('transformMenu', [\n        state('void', style({\n            opacity: 0,\n            transform: 'scale(0.8)',\n        })),\n        transition('void => enter', animate('120ms cubic-bezier(0, 0, 0.2, 1)', style({\n            opacity: 1,\n            transform: 'scale(1)',\n        }))),\n        transition('* => void', animate('100ms 25ms linear', style({ opacity: 0 }))),\n    ]),\n    /**\n     * This animation fades in the background color and content of the menu panel\n     * after its containing element is scaled in.\n     */\n    fadeInItems: trigger('fadeInItems', [\n        // TODO(crisbeto): this is inside the `transformMenu`\n        // now. Remove next time we do breaking changes.\n        state('showing', style({ opacity: 1 })),\n        transition('void => *', [\n            style({ opacity: 0 }),\n            animate('400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)'),\n        ]),\n    ]),\n};\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst fadeInItems = matMenuAnimations.fadeInItems;\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst transformMenu = matMenuAnimations.transformMenu;\n\nlet menuPanelUid = 0;\n/** Injection token to be used to override the default options for `mat-menu`. */\nconst MAT_MENU_DEFAULT_OPTIONS = new InjectionToken('mat-menu-default-options', {\n    providedIn: 'root',\n    factory: MAT_MENU_DEFAULT_OPTIONS_FACTORY,\n});\n/** @docs-private */\nfunction MAT_MENU_DEFAULT_OPTIONS_FACTORY() {\n    return {\n        overlapTrigger: false,\n        xPosition: 'after',\n        yPosition: 'below',\n        backdropClass: 'cdk-overlay-transparent-backdrop',\n    };\n}\nclass MatMenu {\n    /** Position of the menu in the X axis. */\n    get xPosition() {\n        return this._xPosition;\n    }\n    set xPosition(value) {\n        if (value !== 'before' &&\n            value !== 'after' &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwMatMenuInvalidPositionX();\n        }\n        this._xPosition = value;\n        this.setPositionClasses();\n    }\n    /** Position of the menu in the Y axis. */\n    get yPosition() {\n        return this._yPosition;\n    }\n    set yPosition(value) {\n        if (value !== 'above' && value !== 'below' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwMatMenuInvalidPositionY();\n        }\n        this._yPosition = value;\n        this.setPositionClasses();\n    }\n    /**\n     * This method takes classes set on the host mat-menu element and applies them on the\n     * menu template that displays in the overlay container.  Otherwise, it's difficult\n     * to style the containing menu from outside the component.\n     * @param classes list of class names\n     */\n    set panelClass(classes) {\n        const previousPanelClass = this._previousPanelClass;\n        const newClassList = { ...this._classList };\n        if (previousPanelClass && previousPanelClass.length) {\n            previousPanelClass.split(' ').forEach((className) => {\n                newClassList[className] = false;\n            });\n        }\n        this._previousPanelClass = classes;\n        if (classes && classes.length) {\n            classes.split(' ').forEach((className) => {\n                newClassList[className] = true;\n            });\n            this._elementRef.nativeElement.className = '';\n        }\n        this._classList = newClassList;\n    }\n    /**\n     * This method takes classes set on the host mat-menu element and applies them on the\n     * menu template that displays in the overlay container.  Otherwise, it's difficult\n     * to style the containing menu from outside the component.\n     * @deprecated Use `panelClass` instead.\n     * @breaking-change 8.0.0\n     */\n    get classList() {\n        return this.panelClass;\n    }\n    set classList(classes) {\n        this.panelClass = classes;\n    }\n    constructor(_elementRef, _ngZone, defaultOptions, \n    // @breaking-change 15.0.0 `_changeDetectorRef` to become a required parameter.\n    _changeDetectorRef) {\n        this._elementRef = _elementRef;\n        this._ngZone = _ngZone;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._elevationPrefix = 'mat-elevation-z';\n        this._baseElevation = 8;\n        /** Only the direct descendant menu items. */\n        this._directDescendantItems = new QueryList();\n        /** Classes to be applied to the menu panel. */\n        this._classList = {};\n        /** Current state of the panel animation. */\n        this._panelAnimationState = 'void';\n        /** Emits whenever an animation on the menu completes. */\n        this._animationDone = new Subject();\n        /** Event emitted when the menu is closed. */\n        this.closed = new EventEmitter();\n        /**\n         * Event emitted when the menu is closed.\n         * @deprecated Switch to `closed` instead\n         * @breaking-change 8.0.0\n         */\n        this.close = this.closed;\n        this.panelId = `mat-menu-panel-${menuPanelUid++}`;\n        this.overlayPanelClass = defaultOptions.overlayPanelClass || '';\n        this._xPosition = defaultOptions.xPosition;\n        this._yPosition = defaultOptions.yPosition;\n        this.backdropClass = defaultOptions.backdropClass;\n        this.overlapTrigger = defaultOptions.overlapTrigger;\n        this.hasBackdrop = defaultOptions.hasBackdrop;\n    }\n    ngOnInit() {\n        this.setPositionClasses();\n    }\n    ngAfterContentInit() {\n        this._updateDirectDescendants();\n        this._keyManager = new FocusKeyManager(this._directDescendantItems)\n            .withWrap()\n            .withTypeAhead()\n            .withHomeAndEnd();\n        this._keyManager.tabOut.subscribe(() => this.closed.emit('tab'));\n        // If a user manually (programmatically) focuses a menu item, we need to reflect that focus\n        // change back to the key manager. Note that we don't need to unsubscribe here because _focused\n        // is internal and we know that it gets completed on destroy.\n        this._directDescendantItems.changes\n            .pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map((item) => item._focused))))\n            .subscribe(focusedItem => this._keyManager.updateActiveItem(focusedItem));\n        this._directDescendantItems.changes.subscribe((itemsList) => {\n            // Move focus to another item, if the active item is removed from the list.\n            // We need to debounce the callback, because multiple items might be removed\n            // in quick succession.\n            const manager = this._keyManager;\n            if (this._panelAnimationState === 'enter' && manager.activeItem?._hasFocus()) {\n                const items = itemsList.toArray();\n                const index = Math.max(0, Math.min(items.length - 1, manager.activeItemIndex || 0));\n                if (items[index] && !items[index].disabled) {\n                    manager.setActiveItem(index);\n                }\n                else {\n                    manager.setNextItemActive();\n                }\n            }\n        });\n    }\n    ngOnDestroy() {\n        this._keyManager?.destroy();\n        this._directDescendantItems.destroy();\n        this.closed.complete();\n        this._firstItemFocusSubscription?.unsubscribe();\n    }\n    /** Stream that emits whenever the hovered menu item changes. */\n    _hovered() {\n        // Coerce the `changes` property because Angular types it as `Observable<any>`\n        const itemChanges = this._directDescendantItems.changes;\n        return itemChanges.pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map((item) => item._hovered))));\n    }\n    /*\n     * Registers a menu item with the menu.\n     * @docs-private\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 9.0.0\n     */\n    addItem(_item) { }\n    /**\n     * Removes an item from the menu.\n     * @docs-private\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 9.0.0\n     */\n    removeItem(_item) { }\n    /** Handle a keyboard event from the menu, delegating to the appropriate action. */\n    _handleKeydown(event) {\n        const keyCode = event.keyCode;\n        const manager = this._keyManager;\n        switch (keyCode) {\n            case ESCAPE:\n                if (!hasModifierKey(event)) {\n                    event.preventDefault();\n                    this.closed.emit('keydown');\n                }\n                break;\n            case LEFT_ARROW:\n                if (this.parentMenu && this.direction === 'ltr') {\n                    this.closed.emit('keydown');\n                }\n                break;\n            case RIGHT_ARROW:\n                if (this.parentMenu && this.direction === 'rtl') {\n                    this.closed.emit('keydown');\n                }\n                break;\n            default:\n                if (keyCode === UP_ARROW || keyCode === DOWN_ARROW) {\n                    manager.setFocusOrigin('keyboard');\n                }\n                manager.onKeydown(event);\n                return;\n        }\n        // Don't allow the event to propagate if we've already handled it, or it may\n        // end up reaching other overlays that were opened earlier (see #22694).\n        event.stopPropagation();\n    }\n    /**\n     * Focus the first item in the menu.\n     * @param origin Action from which the focus originated. Used to set the correct styling.\n     */\n    focusFirstItem(origin = 'program') {\n        // Wait for `onStable` to ensure iOS VoiceOver screen reader focuses the first item (#24735).\n        this._firstItemFocusSubscription?.unsubscribe();\n        this._firstItemFocusSubscription = this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n            let menuPanel = null;\n            if (this._directDescendantItems.length) {\n                // Because the `mat-menuPanel` is at the DOM insertion point, not inside the overlay, we don't\n                // have a nice way of getting a hold of the menuPanel panel. We can't use a `ViewChild` either\n                // because the panel is inside an `ng-template`. We work around it by starting from one of\n                // the items and walking up the DOM.\n                menuPanel = this._directDescendantItems.first._getHostElement().closest('[role=\"menu\"]');\n            }\n            // If an item in the menuPanel is already focused, avoid overriding the focus.\n            if (!menuPanel || !menuPanel.contains(document.activeElement)) {\n                const manager = this._keyManager;\n                manager.setFocusOrigin(origin).setFirstItemActive();\n                // If there's no active item at this point, it means that all the items are disabled.\n                // Move focus to the menuPanel panel so keyboard events like Escape still work. Also this will\n                // give _some_ feedback to screen readers.\n                if (!manager.activeItem && menuPanel) {\n                    menuPanel.focus();\n                }\n            }\n        });\n    }\n    /**\n     * Resets the active item in the menu. This is used when the menu is opened, allowing\n     * the user to start from the first option when pressing the down arrow.\n     */\n    resetActiveItem() {\n        this._keyManager.setActiveItem(-1);\n    }\n    /**\n     * Sets the menu panel elevation.\n     * @param depth Number of parent menus that come before the menu.\n     */\n    setElevation(depth) {\n        // The elevation starts at the base and increases by one for each level.\n        // Capped at 24 because that's the maximum elevation defined in the Material design spec.\n        const elevation = Math.min(this._baseElevation + depth, 24);\n        const newElevation = `${this._elevationPrefix}${elevation}`;\n        const customElevation = Object.keys(this._classList).find(className => {\n            return className.startsWith(this._elevationPrefix);\n        });\n        if (!customElevation || customElevation === this._previousElevation) {\n            const newClassList = { ...this._classList };\n            if (this._previousElevation) {\n                newClassList[this._previousElevation] = false;\n            }\n            newClassList[newElevation] = true;\n            this._previousElevation = newElevation;\n            this._classList = newClassList;\n        }\n    }\n    /**\n     * Adds classes to the menu panel based on its position. Can be used by\n     * consumers to add specific styling based on the position.\n     * @param posX Position of the menu along the x axis.\n     * @param posY Position of the menu along the y axis.\n     * @docs-private\n     */\n    setPositionClasses(posX = this.xPosition, posY = this.yPosition) {\n        this._classList = {\n            ...this._classList,\n            ['mat-menu-before']: posX === 'before',\n            ['mat-menu-after']: posX === 'after',\n            ['mat-menu-above']: posY === 'above',\n            ['mat-menu-below']: posY === 'below',\n        };\n        // @breaking-change 15.0.0 Remove null check for `_changeDetectorRef`.\n        this._changeDetectorRef?.markForCheck();\n    }\n    /** Starts the enter animation. */\n    _startAnimation() {\n        // @breaking-change 8.0.0 Combine with _resetAnimation.\n        this._panelAnimationState = 'enter';\n    }\n    /** Resets the panel animation to its initial state. */\n    _resetAnimation() {\n        // @breaking-change 8.0.0 Combine with _startAnimation.\n        this._panelAnimationState = 'void';\n    }\n    /** Callback that is invoked when the panel animation completes. */\n    _onAnimationDone(event) {\n        this._animationDone.next(event);\n        this._isAnimating = false;\n    }\n    _onAnimationStart(event) {\n        this._isAnimating = true;\n        // Scroll the content element to the top as soon as the animation starts. This is necessary,\n        // because we move focus to the first item while it's still being animated, which can throw\n        // the browser off when it determines the scroll position. Alternatively we can move focus\n        // when the animation is done, however moving focus asynchronously will interrupt screen\n        // readers which are in the process of reading out the menu already. We take the `element`\n        // from the `event` since we can't use a `ViewChild` to access the pane.\n        if (event.toState === 'enter' && this._keyManager.activeItemIndex === 0) {\n            event.element.scrollTop = 0;\n        }\n    }\n    /**\n     * Sets up a stream that will keep track of any newly-added menu items and will update the list\n     * of direct descendants. We collect the descendants this way, because `_allItems` can include\n     * items that are part of child menus, and using a custom way of registering items is unreliable\n     * when it comes to maintaining the item order.\n     */\n    _updateDirectDescendants() {\n        this._allItems.changes\n            .pipe(startWith(this._allItems))\n            .subscribe((items) => {\n            this._directDescendantItems.reset(items.filter(item => item._parentMenu === this));\n            this._directDescendantItems.notifyOnChanges();\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatMenu, deps: [{ token: i0.ElementRef }, { token: i0.NgZone }, { token: MAT_MENU_DEFAULT_OPTIONS }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"17.2.0\", type: MatMenu, isStandalone: true, selector: \"mat-menu\", inputs: { backdropClass: \"backdropClass\", ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], ariaDescribedby: [\"aria-describedby\", \"ariaDescribedby\"], xPosition: \"xPosition\", yPosition: \"yPosition\", overlapTrigger: [\"overlapTrigger\", \"overlapTrigger\", booleanAttribute], hasBackdrop: [\"hasBackdrop\", \"hasBackdrop\", (value) => (value == null ? null : booleanAttribute(value))], panelClass: [\"class\", \"panelClass\"], classList: \"classList\" }, outputs: { closed: \"closed\", close: \"close\" }, host: { properties: { \"attr.aria-label\": \"null\", \"attr.aria-labelledby\": \"null\", \"attr.aria-describedby\": \"null\" } }, providers: [{ provide: MAT_MENU_PANEL, useExisting: MatMenu }], queries: [{ propertyName: \"lazyContent\", first: true, predicate: MAT_MENU_CONTENT, descendants: true }, { propertyName: \"_allItems\", predicate: MatMenuItem, descendants: true }, { propertyName: \"items\", predicate: MatMenuItem }], viewQueries: [{ propertyName: \"templateRef\", first: true, predicate: TemplateRef, descendants: true }], exportAs: [\"matMenu\"], ngImport: i0, template: \"<ng-template>\\n  <div\\n    class=\\\"mat-mdc-menu-panel mat-mdc-elevation-specific\\\"\\n    [id]=\\\"panelId\\\"\\n    [class]=\\\"_classList\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\"\\n    (click)=\\\"closed.emit('click')\\\"\\n    [@transformMenu]=\\\"_panelAnimationState\\\"\\n    (@transformMenu.start)=\\\"_onAnimationStart($event)\\\"\\n    (@transformMenu.done)=\\\"_onAnimationDone($event)\\\"\\n    tabindex=\\\"-1\\\"\\n    role=\\\"menu\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"ariaLabelledby || null\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby || null\\\">\\n    <div class=\\\"mat-mdc-menu-content\\\">\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</ng-template>\\n\", styles: [\"mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;list-style-type:none}.mat-mdc-menu-content:focus{outline:none}.mat-mdc-menu-content,.mat-mdc-menu-content .mat-mdc-menu-item .mat-mdc-menu-item-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;flex:1;white-space:normal;font-family:var(--mat-menu-item-label-text-font);line-height:var(--mat-menu-item-label-text-line-height);font-size:var(--mat-menu-item-label-text-size);letter-spacing:var(--mat-menu-item-label-text-tracking);font-weight:var(--mat-menu-item-label-text-weight)}.mat-mdc-menu-panel{min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;box-sizing:border-box;outline:0;border-radius:var(--mat-menu-container-shape);background-color:var(--mat-menu-container-color);will-change:transform,opacity}.mat-mdc-menu-panel.ng-animating{pointer-events:none}.cdk-high-contrast-active .mat-mdc-menu-panel{outline:solid 1px}.mat-divider{color:var(--mat-menu-divider-color);margin-bottom:var(--mat-menu-divider-bottom-spacing);margin-top:var(--mat-menu-divider-top-spacing)}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:var(--mat-menu-item-leading-spacing);padding-right:var(--mat-menu-item-trailing-spacing);-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;align-items:center;min-height:48px}.mat-mdc-menu-item:focus{outline:none}[dir=rtl] .mat-mdc-menu-item,.mat-mdc-menu-item[dir=rtl]{padding-left:var(--mat-menu-item-trailing-spacing);padding-right:var(--mat-menu-item-leading-spacing)}.mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-leading-spacing);padding-right:var(--mat-menu-item-with-icon-trailing-spacing)}[dir=rtl] .mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]),.mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon])[dir=rtl]{padding-left:var(--mat-menu-item-with-icon-trailing-spacing);padding-right:var(--mat-menu-item-with-icon-leading-spacing)}.mat-mdc-menu-item::-moz-focus-inner{border:0}.mat-mdc-menu-item,.mat-mdc-menu-item:visited,.mat-mdc-menu-item:link{color:var(--mat-menu-item-label-text-color)}.mat-mdc-menu-item .mat-icon-no-color,.mat-mdc-menu-item .mat-mdc-menu-submenu-icon{color:var(--mat-menu-item-icon-color)}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item .mat-icon{flex-shrink:0;margin-right:var(--mat-menu-item-spacing);height:var(--mat-menu-item-icon-size);width:var(--mat-menu-item-icon-size)}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:var(--mat-menu-item-spacing)}.mat-mdc-menu-item:not([disabled]):hover{background-color:var(--mat-menu-item-hover-state-layer-color)}.mat-mdc-menu-item:not([disabled]).cdk-program-focused,.mat-mdc-menu-item:not([disabled]).cdk-keyboard-focused,.mat-mdc-menu-item:not([disabled]).mat-mdc-menu-item-highlighted{background-color:var(--mat-menu-item-focus-state-layer-color)}.cdk-high-contrast-active .mat-mdc-menu-item{margin-top:1px}.mat-mdc-menu-submenu-icon{width:var(--mat-menu-item-icon-size);height:10px;fill:currentColor;padding-left:var(--mat-menu-item-spacing)}[dir=rtl] .mat-mdc-menu-submenu-icon{padding-right:var(--mat-menu-item-spacing);padding-left:0}[dir=rtl] .mat-mdc-menu-submenu-icon polygon{transform:scaleX(-1)}.cdk-high-contrast-active .mat-mdc-menu-submenu-icon{fill:CanvasText}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\"], animations: [matMenuAnimations.transformMenu, matMenuAnimations.fadeInItems], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatMenu, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-menu', changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, exportAs: 'matMenu', host: {\n                        '[attr.aria-label]': 'null',\n                        '[attr.aria-labelledby]': 'null',\n                        '[attr.aria-describedby]': 'null',\n                    }, animations: [matMenuAnimations.transformMenu, matMenuAnimations.fadeInItems], providers: [{ provide: MAT_MENU_PANEL, useExisting: MatMenu }], standalone: true, template: \"<ng-template>\\n  <div\\n    class=\\\"mat-mdc-menu-panel mat-mdc-elevation-specific\\\"\\n    [id]=\\\"panelId\\\"\\n    [class]=\\\"_classList\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\"\\n    (click)=\\\"closed.emit('click')\\\"\\n    [@transformMenu]=\\\"_panelAnimationState\\\"\\n    (@transformMenu.start)=\\\"_onAnimationStart($event)\\\"\\n    (@transformMenu.done)=\\\"_onAnimationDone($event)\\\"\\n    tabindex=\\\"-1\\\"\\n    role=\\\"menu\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"ariaLabelledby || null\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby || null\\\">\\n    <div class=\\\"mat-mdc-menu-content\\\">\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</ng-template>\\n\", styles: [\"mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;list-style-type:none}.mat-mdc-menu-content:focus{outline:none}.mat-mdc-menu-content,.mat-mdc-menu-content .mat-mdc-menu-item .mat-mdc-menu-item-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;flex:1;white-space:normal;font-family:var(--mat-menu-item-label-text-font);line-height:var(--mat-menu-item-label-text-line-height);font-size:var(--mat-menu-item-label-text-size);letter-spacing:var(--mat-menu-item-label-text-tracking);font-weight:var(--mat-menu-item-label-text-weight)}.mat-mdc-menu-panel{min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;box-sizing:border-box;outline:0;border-radius:var(--mat-menu-container-shape);background-color:var(--mat-menu-container-color);will-change:transform,opacity}.mat-mdc-menu-panel.ng-animating{pointer-events:none}.cdk-high-contrast-active .mat-mdc-menu-panel{outline:solid 1px}.mat-divider{color:var(--mat-menu-divider-color);margin-bottom:var(--mat-menu-divider-bottom-spacing);margin-top:var(--mat-menu-divider-top-spacing)}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:var(--mat-menu-item-leading-spacing);padding-right:var(--mat-menu-item-trailing-spacing);-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;align-items:center;min-height:48px}.mat-mdc-menu-item:focus{outline:none}[dir=rtl] .mat-mdc-menu-item,.mat-mdc-menu-item[dir=rtl]{padding-left:var(--mat-menu-item-trailing-spacing);padding-right:var(--mat-menu-item-leading-spacing)}.mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-leading-spacing);padding-right:var(--mat-menu-item-with-icon-trailing-spacing)}[dir=rtl] .mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]),.mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon])[dir=rtl]{padding-left:var(--mat-menu-item-with-icon-trailing-spacing);padding-right:var(--mat-menu-item-with-icon-leading-spacing)}.mat-mdc-menu-item::-moz-focus-inner{border:0}.mat-mdc-menu-item,.mat-mdc-menu-item:visited,.mat-mdc-menu-item:link{color:var(--mat-menu-item-label-text-color)}.mat-mdc-menu-item .mat-icon-no-color,.mat-mdc-menu-item .mat-mdc-menu-submenu-icon{color:var(--mat-menu-item-icon-color)}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item .mat-icon{flex-shrink:0;margin-right:var(--mat-menu-item-spacing);height:var(--mat-menu-item-icon-size);width:var(--mat-menu-item-icon-size)}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:var(--mat-menu-item-spacing)}.mat-mdc-menu-item:not([disabled]):hover{background-color:var(--mat-menu-item-hover-state-layer-color)}.mat-mdc-menu-item:not([disabled]).cdk-program-focused,.mat-mdc-menu-item:not([disabled]).cdk-keyboard-focused,.mat-mdc-menu-item:not([disabled]).mat-mdc-menu-item-highlighted{background-color:var(--mat-menu-item-focus-state-layer-color)}.cdk-high-contrast-active .mat-mdc-menu-item{margin-top:1px}.mat-mdc-menu-submenu-icon{width:var(--mat-menu-item-icon-size);height:10px;fill:currentColor;padding-left:var(--mat-menu-item-spacing)}[dir=rtl] .mat-mdc-menu-submenu-icon{padding-right:var(--mat-menu-item-spacing);padding-left:0}[dir=rtl] .mat-mdc-menu-submenu-icon polygon{transform:scaleX(-1)}.cdk-high-contrast-active .mat-mdc-menu-submenu-icon{fill:CanvasText}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\"] }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_MENU_DEFAULT_OPTIONS]\n                }] }, { type: i0.ChangeDetectorRef }], propDecorators: { _allItems: [{\n                type: ContentChildren,\n                args: [MatMenuItem, { descendants: true }]\n            }], backdropClass: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], ariaDescribedby: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], xPosition: [{\n                type: Input\n            }], yPosition: [{\n                type: Input\n            }], templateRef: [{\n                type: ViewChild,\n                args: [TemplateRef]\n            }], items: [{\n                type: ContentChildren,\n                args: [MatMenuItem, { descendants: false }]\n            }], lazyContent: [{\n                type: ContentChild,\n                args: [MAT_MENU_CONTENT]\n            }], overlapTrigger: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], hasBackdrop: [{\n                type: Input,\n                args: [{ transform: (value) => (value == null ? null : booleanAttribute(value)) }]\n            }], panelClass: [{\n                type: Input,\n                args: ['class']\n            }], classList: [{\n                type: Input\n            }], closed: [{\n                type: Output\n            }], close: [{\n                type: Output\n            }] } });\n\n/** Injection token that determines the scroll handling while the menu is open. */\nconst MAT_MENU_SCROLL_STRATEGY = new InjectionToken('mat-menu-scroll-strategy', {\n    providedIn: 'root',\n    factory: () => {\n        const overlay = inject(Overlay);\n        return () => overlay.scrollStrategies.reposition();\n    },\n});\n/** @docs-private */\nfunction MAT_MENU_SCROLL_STRATEGY_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition();\n}\n/** @docs-private */\nconst MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n    provide: MAT_MENU_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: MAT_MENU_SCROLL_STRATEGY_FACTORY,\n};\n/** Options for binding a passive event listener. */\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({ passive: true });\n/**\n * Default top padding of the menu panel.\n * @deprecated No longer being used. Will be removed.\n * @breaking-change 15.0.0\n */\nconst MENU_PANEL_TOP_PADDING = 8;\n/** Directive applied to an element that should trigger a `mat-menu`. */\nclass MatMenuTrigger {\n    /**\n     * @deprecated\n     * @breaking-change 8.0.0\n     */\n    get _deprecatedMatMenuTriggerFor() {\n        return this.menu;\n    }\n    set _deprecatedMatMenuTriggerFor(v) {\n        this.menu = v;\n    }\n    /** References the menu instance that the trigger is associated with. */\n    get menu() {\n        return this._menu;\n    }\n    set menu(menu) {\n        if (menu === this._menu) {\n            return;\n        }\n        this._menu = menu;\n        this._menuCloseSubscription.unsubscribe();\n        if (menu) {\n            if (menu === this._parentMaterialMenu && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throwMatMenuRecursiveError();\n            }\n            this._menuCloseSubscription = menu.close.subscribe((reason) => {\n                this._destroyMenu(reason);\n                // If a click closed the menu, we should close the entire chain of nested menus.\n                if ((reason === 'click' || reason === 'tab') && this._parentMaterialMenu) {\n                    this._parentMaterialMenu.closed.emit(reason);\n                }\n            });\n        }\n        this._menuItemInstance?._setTriggersSubmenu(this.triggersSubmenu());\n    }\n    constructor(_overlay, _element, _viewContainerRef, scrollStrategy, parentMenu, \n    // `MatMenuTrigger` is commonly used in combination with a `MatMenuItem`.\n    // tslint:disable-next-line: lightweight-tokens\n    _menuItemInstance, _dir, _focusMonitor, _ngZone) {\n        this._overlay = _overlay;\n        this._element = _element;\n        this._viewContainerRef = _viewContainerRef;\n        this._menuItemInstance = _menuItemInstance;\n        this._dir = _dir;\n        this._focusMonitor = _focusMonitor;\n        this._ngZone = _ngZone;\n        this._overlayRef = null;\n        this._menuOpen = false;\n        this._closingActionsSubscription = Subscription.EMPTY;\n        this._hoverSubscription = Subscription.EMPTY;\n        this._menuCloseSubscription = Subscription.EMPTY;\n        this._changeDetectorRef = inject(ChangeDetectorRef);\n        /**\n         * Handles touch start events on the trigger.\n         * Needs to be an arrow function so we can easily use addEventListener and removeEventListener.\n         */\n        this._handleTouchStart = (event) => {\n            if (!isFakeTouchstartFromScreenReader(event)) {\n                this._openedBy = 'touch';\n            }\n        };\n        // Tracking input type is necessary so it's possible to only auto-focus\n        // the first item of the list when the menu is opened via the keyboard\n        this._openedBy = undefined;\n        /**\n         * Whether focus should be restored when the menu is closed.\n         * Note that disabling this option can have accessibility implications\n         * and it's up to you to manage focus, if you decide to turn it off.\n         */\n        this.restoreFocus = true;\n        /** Event emitted when the associated menu is opened. */\n        this.menuOpened = new EventEmitter();\n        /**\n         * Event emitted when the associated menu is opened.\n         * @deprecated Switch to `menuOpened` instead\n         * @breaking-change 8.0.0\n         */\n        // tslint:disable-next-line:no-output-on-prefix\n        this.onMenuOpen = this.menuOpened;\n        /** Event emitted when the associated menu is closed. */\n        this.menuClosed = new EventEmitter();\n        /**\n         * Event emitted when the associated menu is closed.\n         * @deprecated Switch to `menuClosed` instead\n         * @breaking-change 8.0.0\n         */\n        // tslint:disable-next-line:no-output-on-prefix\n        this.onMenuClose = this.menuClosed;\n        this._scrollStrategy = scrollStrategy;\n        this._parentMaterialMenu = parentMenu instanceof MatMenu ? parentMenu : undefined;\n        _element.nativeElement.addEventListener('touchstart', this._handleTouchStart, passiveEventListenerOptions);\n    }\n    ngAfterContentInit() {\n        this._handleHover();\n    }\n    ngOnDestroy() {\n        if (this._overlayRef) {\n            this._overlayRef.dispose();\n            this._overlayRef = null;\n        }\n        this._element.nativeElement.removeEventListener('touchstart', this._handleTouchStart, passiveEventListenerOptions);\n        this._menuCloseSubscription.unsubscribe();\n        this._closingActionsSubscription.unsubscribe();\n        this._hoverSubscription.unsubscribe();\n    }\n    /** Whether the menu is open. */\n    get menuOpen() {\n        return this._menuOpen;\n    }\n    /** The text direction of the containing app. */\n    get dir() {\n        return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Whether the menu triggers a sub-menu or a top-level one. */\n    triggersSubmenu() {\n        return !!(this._menuItemInstance && this._parentMaterialMenu && this.menu);\n    }\n    /** Toggles the menu between the open and closed states. */\n    toggleMenu() {\n        return this._menuOpen ? this.closeMenu() : this.openMenu();\n    }\n    /** Opens the menu. */\n    openMenu() {\n        const menu = this.menu;\n        if (this._menuOpen || !menu) {\n            return;\n        }\n        const overlayRef = this._createOverlay(menu);\n        const overlayConfig = overlayRef.getConfig();\n        const positionStrategy = overlayConfig.positionStrategy;\n        this._setPosition(menu, positionStrategy);\n        overlayConfig.hasBackdrop =\n            menu.hasBackdrop == null ? !this.triggersSubmenu() : menu.hasBackdrop;\n        overlayRef.attach(this._getPortal(menu));\n        if (menu.lazyContent) {\n            menu.lazyContent.attach(this.menuData);\n        }\n        this._closingActionsSubscription = this._menuClosingActions().subscribe(() => this.closeMenu());\n        this._initMenu(menu);\n        if (menu instanceof MatMenu) {\n            menu._startAnimation();\n            menu._directDescendantItems.changes.pipe(takeUntil(menu.close)).subscribe(() => {\n                // Re-adjust the position without locking when the amount of items\n                // changes so that the overlay is allowed to pick a new optimal position.\n                positionStrategy.withLockedPosition(false).reapplyLastPosition();\n                positionStrategy.withLockedPosition(true);\n            });\n        }\n    }\n    /** Closes the menu. */\n    closeMenu() {\n        this.menu?.close.emit();\n    }\n    /**\n     * Focuses the menu trigger.\n     * @param origin Source of the menu trigger's focus.\n     */\n    focus(origin, options) {\n        if (this._focusMonitor && origin) {\n            this._focusMonitor.focusVia(this._element, origin, options);\n        }\n        else {\n            this._element.nativeElement.focus(options);\n        }\n    }\n    /**\n     * Updates the position of the menu to ensure that it fits all options within the viewport.\n     */\n    updatePosition() {\n        this._overlayRef?.updatePosition();\n    }\n    /** Closes the menu and does the necessary cleanup. */\n    _destroyMenu(reason) {\n        if (!this._overlayRef || !this.menuOpen) {\n            return;\n        }\n        const menu = this.menu;\n        this._closingActionsSubscription.unsubscribe();\n        this._overlayRef.detach();\n        // Always restore focus if the user is navigating using the keyboard or the menu was opened\n        // programmatically. We don't restore for non-root triggers, because it can prevent focus\n        // from making it back to the root trigger when closing a long chain of menus by clicking\n        // on the backdrop.\n        if (this.restoreFocus && (reason === 'keydown' || !this._openedBy || !this.triggersSubmenu())) {\n            this.focus(this._openedBy);\n        }\n        this._openedBy = undefined;\n        if (menu instanceof MatMenu) {\n            menu._resetAnimation();\n            if (menu.lazyContent) {\n                // Wait for the exit animation to finish before detaching the content.\n                menu._animationDone\n                    .pipe(filter(event => event.toState === 'void'), take(1), \n                // Interrupt if the content got re-attached.\n                takeUntil(menu.lazyContent._attached))\n                    .subscribe({\n                    next: () => menu.lazyContent.detach(),\n                    // No matter whether the content got re-attached, reset the menu.\n                    complete: () => this._setIsMenuOpen(false),\n                });\n            }\n            else {\n                this._setIsMenuOpen(false);\n            }\n        }\n        else {\n            this._setIsMenuOpen(false);\n            menu?.lazyContent?.detach();\n        }\n    }\n    /**\n     * This method sets the menu state to open and focuses the first item if\n     * the menu was opened via the keyboard.\n     */\n    _initMenu(menu) {\n        menu.parentMenu = this.triggersSubmenu() ? this._parentMaterialMenu : undefined;\n        menu.direction = this.dir;\n        this._setMenuElevation(menu);\n        menu.focusFirstItem(this._openedBy || 'program');\n        this._setIsMenuOpen(true);\n    }\n    /** Updates the menu elevation based on the amount of parent menus that it has. */\n    _setMenuElevation(menu) {\n        if (menu.setElevation) {\n            let depth = 0;\n            let parentMenu = menu.parentMenu;\n            while (parentMenu) {\n                depth++;\n                parentMenu = parentMenu.parentMenu;\n            }\n            menu.setElevation(depth);\n        }\n    }\n    // set state rather than toggle to support triggers sharing a menu\n    _setIsMenuOpen(isOpen) {\n        if (isOpen !== this._menuOpen) {\n            this._menuOpen = isOpen;\n            this._menuOpen ? this.menuOpened.emit() : this.menuClosed.emit();\n            if (this.triggersSubmenu()) {\n                this._menuItemInstance._setHighlighted(isOpen);\n            }\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /**\n     * This method creates the overlay from the provided menu's template and saves its\n     * OverlayRef so that it can be attached to the DOM when openMenu is called.\n     */\n    _createOverlay(menu) {\n        if (!this._overlayRef) {\n            const config = this._getOverlayConfig(menu);\n            this._subscribeToPositions(menu, config.positionStrategy);\n            this._overlayRef = this._overlay.create(config);\n            // Consume the `keydownEvents` in order to prevent them from going to another overlay.\n            // Ideally we'd also have our keyboard event logic in here, however doing so will\n            // break anybody that may have implemented the `MatMenuPanel` themselves.\n            this._overlayRef.keydownEvents().subscribe();\n        }\n        return this._overlayRef;\n    }\n    /**\n     * This method builds the configuration object needed to create the overlay, the OverlayState.\n     * @returns OverlayConfig\n     */\n    _getOverlayConfig(menu) {\n        return new OverlayConfig({\n            positionStrategy: this._overlay\n                .position()\n                .flexibleConnectedTo(this._element)\n                .withLockedPosition()\n                .withGrowAfterOpen()\n                .withTransformOriginOn('.mat-menu-panel, .mat-mdc-menu-panel'),\n            backdropClass: menu.backdropClass || 'cdk-overlay-transparent-backdrop',\n            panelClass: menu.overlayPanelClass,\n            scrollStrategy: this._scrollStrategy(),\n            direction: this._dir,\n        });\n    }\n    /**\n     * Listens to changes in the position of the overlay and sets the correct classes\n     * on the menu based on the new position. This ensures the animation origin is always\n     * correct, even if a fallback position is used for the overlay.\n     */\n    _subscribeToPositions(menu, position) {\n        if (menu.setPositionClasses) {\n            position.positionChanges.subscribe(change => {\n                const posX = change.connectionPair.overlayX === 'start' ? 'after' : 'before';\n                const posY = change.connectionPair.overlayY === 'top' ? 'below' : 'above';\n                // @breaking-change 15.0.0 Remove null check for `ngZone`.\n                // `positionChanges` fires outside of the `ngZone` and `setPositionClasses` might be\n                // updating something in the view so we need to bring it back in.\n                if (this._ngZone) {\n                    this._ngZone.run(() => menu.setPositionClasses(posX, posY));\n                }\n                else {\n                    menu.setPositionClasses(posX, posY);\n                }\n            });\n        }\n    }\n    /**\n     * Sets the appropriate positions on a position strategy\n     * so the overlay connects with the trigger correctly.\n     * @param positionStrategy Strategy whose position to update.\n     */\n    _setPosition(menu, positionStrategy) {\n        let [originX, originFallbackX] = menu.xPosition === 'before' ? ['end', 'start'] : ['start', 'end'];\n        let [overlayY, overlayFallbackY] = menu.yPosition === 'above' ? ['bottom', 'top'] : ['top', 'bottom'];\n        let [originY, originFallbackY] = [overlayY, overlayFallbackY];\n        let [overlayX, overlayFallbackX] = [originX, originFallbackX];\n        let offsetY = 0;\n        if (this.triggersSubmenu()) {\n            // When the menu is a sub-menu, it should always align itself\n            // to the edges of the trigger, instead of overlapping it.\n            overlayFallbackX = originX = menu.xPosition === 'before' ? 'start' : 'end';\n            originFallbackX = overlayX = originX === 'end' ? 'start' : 'end';\n            if (this._parentMaterialMenu) {\n                if (this._parentInnerPadding == null) {\n                    const firstItem = this._parentMaterialMenu.items.first;\n                    this._parentInnerPadding = firstItem ? firstItem._getHostElement().offsetTop : 0;\n                }\n                offsetY = overlayY === 'bottom' ? this._parentInnerPadding : -this._parentInnerPadding;\n            }\n        }\n        else if (!menu.overlapTrigger) {\n            originY = overlayY === 'top' ? 'bottom' : 'top';\n            originFallbackY = overlayFallbackY === 'top' ? 'bottom' : 'top';\n        }\n        positionStrategy.withPositions([\n            { originX, originY, overlayX, overlayY, offsetY },\n            { originX: originFallbackX, originY, overlayX: overlayFallbackX, overlayY, offsetY },\n            {\n                originX,\n                originY: originFallbackY,\n                overlayX,\n                overlayY: overlayFallbackY,\n                offsetY: -offsetY,\n            },\n            {\n                originX: originFallbackX,\n                originY: originFallbackY,\n                overlayX: overlayFallbackX,\n                overlayY: overlayFallbackY,\n                offsetY: -offsetY,\n            },\n        ]);\n    }\n    /** Returns a stream that emits whenever an action that should close the menu occurs. */\n    _menuClosingActions() {\n        const backdrop = this._overlayRef.backdropClick();\n        const detachments = this._overlayRef.detachments();\n        const parentClose = this._parentMaterialMenu ? this._parentMaterialMenu.closed : of();\n        const hover = this._parentMaterialMenu\n            ? this._parentMaterialMenu._hovered().pipe(filter(active => active !== this._menuItemInstance), filter(() => this._menuOpen))\n            : of();\n        return merge(backdrop, parentClose, hover, detachments);\n    }\n    /** Handles mouse presses on the trigger. */\n    _handleMousedown(event) {\n        if (!isFakeMousedownFromScreenReader(event)) {\n            // Since right or middle button clicks won't trigger the `click` event,\n            // we shouldn't consider the menu as opened by mouse in those cases.\n            this._openedBy = event.button === 0 ? 'mouse' : undefined;\n            // Since clicking on the trigger won't close the menu if it opens a sub-menu,\n            // we should prevent focus from moving onto it via click to avoid the\n            // highlight from lingering on the menu item.\n            if (this.triggersSubmenu()) {\n                event.preventDefault();\n            }\n        }\n    }\n    /** Handles key presses on the trigger. */\n    _handleKeydown(event) {\n        const keyCode = event.keyCode;\n        // Pressing enter on the trigger will trigger the click handler later.\n        if (keyCode === ENTER || keyCode === SPACE) {\n            this._openedBy = 'keyboard';\n        }\n        if (this.triggersSubmenu() &&\n            ((keyCode === RIGHT_ARROW && this.dir === 'ltr') ||\n                (keyCode === LEFT_ARROW && this.dir === 'rtl'))) {\n            this._openedBy = 'keyboard';\n            this.openMenu();\n        }\n    }\n    /** Handles click events on the trigger. */\n    _handleClick(event) {\n        if (this.triggersSubmenu()) {\n            // Stop event propagation to avoid closing the parent menu.\n            event.stopPropagation();\n            this.openMenu();\n        }\n        else {\n            this.toggleMenu();\n        }\n    }\n    /** Handles the cases where the user hovers over the trigger. */\n    _handleHover() {\n        // Subscribe to changes in the hovered item in order to toggle the panel.\n        if (!this.triggersSubmenu() || !this._parentMaterialMenu) {\n            return;\n        }\n        this._hoverSubscription = this._parentMaterialMenu\n            ._hovered()\n            // Since we might have multiple competing triggers for the same menu (e.g. a sub-menu\n            // with different data and triggers), we have to delay it by a tick to ensure that\n            // it won't be closed immediately after it is opened.\n            .pipe(filter(active => active === this._menuItemInstance && !active.disabled), delay(0, asapScheduler))\n            .subscribe(() => {\n            this._openedBy = 'mouse';\n            // If the same menu is used between multiple triggers, it might still be animating\n            // while the new trigger tries to re-open it. Wait for the animation to finish\n            // before doing so. Also interrupt if the user moves to another item.\n            if (this.menu instanceof MatMenu && this.menu._isAnimating) {\n                // We need the `delay(0)` here in order to avoid\n                // 'changed after checked' errors in some cases. See #12194.\n                this.menu._animationDone\n                    .pipe(take(1), delay(0, asapScheduler), takeUntil(this._parentMaterialMenu._hovered()))\n                    .subscribe(() => this.openMenu());\n            }\n            else {\n                this.openMenu();\n            }\n        });\n    }\n    /** Gets the portal that should be attached to the overlay. */\n    _getPortal(menu) {\n        // Note that we can avoid this check by keeping the portal on the menu panel.\n        // While it would be cleaner, we'd have to introduce another required method on\n        // `MatMenuPanel`, making it harder to consume.\n        if (!this._portal || this._portal.templateRef !== menu.templateRef) {\n            this._portal = new TemplatePortal(menu.templateRef, this._viewContainerRef);\n        }\n        return this._portal;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatMenuTrigger, deps: [{ token: i1$1.Overlay }, { token: i0.ElementRef }, { token: i0.ViewContainerRef }, { token: MAT_MENU_SCROLL_STRATEGY }, { token: MAT_MENU_PANEL, optional: true }, { token: MatMenuItem, optional: true, self: true }, { token: i3.Directionality, optional: true }, { token: i1.FocusMonitor }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatMenuTrigger, isStandalone: true, selector: \"[mat-menu-trigger-for], [matMenuTriggerFor]\", inputs: { _deprecatedMatMenuTriggerFor: [\"mat-menu-trigger-for\", \"_deprecatedMatMenuTriggerFor\"], menu: [\"matMenuTriggerFor\", \"menu\"], menuData: [\"matMenuTriggerData\", \"menuData\"], restoreFocus: [\"matMenuTriggerRestoreFocus\", \"restoreFocus\"] }, outputs: { menuOpened: \"menuOpened\", onMenuOpen: \"onMenuOpen\", menuClosed: \"menuClosed\", onMenuClose: \"onMenuClose\" }, host: { listeners: { \"click\": \"_handleClick($event)\", \"mousedown\": \"_handleMousedown($event)\", \"keydown\": \"_handleKeydown($event)\" }, properties: { \"attr.aria-haspopup\": \"menu ? \\\"menu\\\" : null\", \"attr.aria-expanded\": \"menuOpen\", \"attr.aria-controls\": \"menuOpen ? menu.panelId : null\" }, classAttribute: \"mat-mdc-menu-trigger\" }, exportAs: [\"matMenuTrigger\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatMenuTrigger, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[mat-menu-trigger-for], [matMenuTriggerFor]`,\n                    host: {\n                        'class': 'mat-mdc-menu-trigger',\n                        '[attr.aria-haspopup]': 'menu ? \"menu\" : null',\n                        '[attr.aria-expanded]': 'menuOpen',\n                        '[attr.aria-controls]': 'menuOpen ? menu.panelId : null',\n                        '(click)': '_handleClick($event)',\n                        '(mousedown)': '_handleMousedown($event)',\n                        '(keydown)': '_handleKeydown($event)',\n                    },\n                    exportAs: 'matMenuTrigger',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i1$1.Overlay }, { type: i0.ElementRef }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_MENU_SCROLL_STRATEGY]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_MENU_PANEL]\n                }, {\n                    type: Optional\n                }] }, { type: MatMenuItem, decorators: [{\n                    type: Optional\n                }, {\n                    type: Self\n                }] }, { type: i3.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i1.FocusMonitor }, { type: i0.NgZone }], propDecorators: { _deprecatedMatMenuTriggerFor: [{\n                type: Input,\n                args: ['mat-menu-trigger-for']\n            }], menu: [{\n                type: Input,\n                args: ['matMenuTriggerFor']\n            }], menuData: [{\n                type: Input,\n                args: ['matMenuTriggerData']\n            }], restoreFocus: [{\n                type: Input,\n                args: ['matMenuTriggerRestoreFocus']\n            }], menuOpened: [{\n                type: Output\n            }], onMenuOpen: [{\n                type: Output\n            }], menuClosed: [{\n                type: Output\n            }], onMenuClose: [{\n                type: Output\n            }] } });\n\nclass MatMenuModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatMenuModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: MatMenuModule, imports: [CommonModule,\n            MatRippleModule,\n            MatCommonModule,\n            OverlayModule,\n            MatMenu,\n            MatMenuItem,\n            MatMenuContent,\n            MatMenuTrigger], exports: [CdkScrollableModule,\n            MatMenu,\n            MatCommonModule,\n            MatMenuItem,\n            MatMenuContent,\n            MatMenuTrigger] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatMenuModule, providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER], imports: [CommonModule,\n            MatRippleModule,\n            MatCommonModule,\n            OverlayModule, CdkScrollableModule,\n            MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatMenuModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        CommonModule,\n                        MatRippleModule,\n                        MatCommonModule,\n                        OverlayModule,\n                        MatMenu,\n                        MatMenuItem,\n                        MatMenuContent,\n                        MatMenuTrigger,\n                    ],\n                    exports: [\n                        CdkScrollableModule,\n                        MatMenu,\n                        MatCommonModule,\n                        MatMenuItem,\n                        MatMenuContent,\n                        MatMenuTrigger,\n                    ],\n                    providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_MENU_CONTENT, MAT_MENU_DEFAULT_OPTIONS, MAT_MENU_PANEL, MAT_MENU_SCROLL_STRATEGY, MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER, MENU_PANEL_TOP_PADDING, MatMenu, MatMenuContent, MatMenuItem, MatMenuModule, MatMenuTrigger, fadeInItems, matMenuAnimations, transformMenu };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,YAAY,EAAEC,WAAW,EAAEC,eAAe,EAAEC,SAAS,EAAEC,YAAY,EAAEC,MAAM,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,eAAe;AAC9R,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,eAAe,EAAEC,gCAAgC,EAAEC,+BAA+B,QAAQ,mBAAmB;AACtH,SAASC,QAAQ,EAAEC,UAAU,EAAEC,WAAW,EAAEC,UAAU,EAAEC,MAAM,EAAEC,cAAc,EAAEC,KAAK,EAAEC,KAAK,QAAQ,uBAAuB;AAC3H,SAASC,OAAO,EAAEC,KAAK,EAAEC,YAAY,EAAEC,EAAE,EAAEC,aAAa,QAAQ,MAAM;AACtE,SAASC,SAAS,EAAEC,SAAS,EAAEC,IAAI,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,QAAQ,gBAAgB;AACrF,SAASC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AACxD,SAASC,SAAS,EAAEC,eAAe,EAAEC,eAAe,QAAQ,wBAAwB;AACpF,SAASC,cAAc,EAAEC,eAAe,QAAQ,qBAAqB;AACrE,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AAChF,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,OAAO,KAAKC,IAAI,MAAM,sBAAsB;AAC5C,SAASC,OAAO,EAAEC,aAAa,EAAEC,aAAa,QAAQ,sBAAsB;AAC5E,SAASC,+BAA+B,QAAQ,uBAAuB;AACvE,SAASC,mBAAmB,QAAQ,wBAAwB;;AAE5D;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,mCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAyGoGnE,EAAE,CAAAqE,cAAA;IAAFrE,EAAE,CAAAsE,cAAA,YACsnC,CAAC;IADznCtE,EAAE,CAAAuE,SAAA,gBACwpC,CAAC;IAD3pCvE,EAAE,CAAAwE,YAAA,CAC8pC,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,+BAAAP,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAQ,GAAA,GADjqC3E,EAAE,CAAA4E,gBAAA;IAAF5E,EAAE,CAAAsE,cAAA,YA2fgrD,CAAC;IA3fnrDtE,EAAE,CAAA6E,UAAA,qBAAAC,sDAAAC,MAAA;MAAF/E,EAAE,CAAAgF,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFjF,EAAE,CAAAkF,aAAA;MAAA,OAAFlF,EAAE,CAAAmF,WAAA,CA2f4wCF,MAAA,CAAAG,cAAA,CAAAL,MAAqB,CAAC;IAAA,CAAC,CAAC,mBAAAM,oDAAA;MA3ftyCrF,EAAE,CAAAgF,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFjF,EAAE,CAAAkF,aAAA;MAAA,OAAFlF,EAAE,CAAAmF,WAAA,CA2fozCF,MAAA,CAAAK,MAAA,CAAAC,IAAA,CAAY,OAAO,CAAC;IAAA,CAAC,CAAC,kCAAAC,4EAAAT,MAAA;MA3f50C/E,EAAE,CAAAgF,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFjF,EAAE,CAAAkF,aAAA;MAAA,OAAFlF,EAAE,CAAAmF,WAAA,CA2fw5CF,MAAA,CAAAQ,iBAAA,CAAAV,MAAwB,CAAC;IAAA,CAAC,CAAC,iCAAAW,2EAAAX,MAAA;MA3fr7C/E,EAAE,CAAAgF,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFjF,EAAE,CAAAkF,aAAA;MAAA,OAAFlF,EAAE,CAAAmF,WAAA,CA2fi9CF,MAAA,CAAAU,gBAAA,CAAAZ,MAAuB,CAAC;IAAA,CAAC,CAAC;IA3f7+C/E,EAAE,CAAAsE,cAAA,YA2f0tD,CAAC;IA3f7tDtE,EAAE,CAAA4F,YAAA,EA2f2vD,CAAC;IA3f9vD5F,EAAE,CAAAwE,YAAA,CA2fuwD,CAAC,CAAS,CAAC;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAAc,MAAA,GA3fpxDjF,EAAE,CAAAkF,aAAA;IAAFlF,EAAE,CAAA6F,UAAA,CAAAZ,MAAA,CAAAa,UA2fyvC,CAAC;IA3f5vC9F,EAAE,CAAA+F,UAAA,OAAAd,MAAA,CAAAe,OA2f6tC,CAAC,mBAAAf,MAAA,CAAAgB,oBAA0J,CAAC;IA3f33CjG,EAAE,CAAAkG,WAAA,eAAAjB,MAAA,CAAAkB,SAAA,6BAAAlB,MAAA,CAAAmB,cAAA,8BAAAnB,MAAA,CAAAoB,eAAA;EAAA;AAAA;AArGtG,MAAMC,cAAc,GAAG,IAAIrG,cAAc,CAAC,gBAAgB,CAAC;;AAE3D;AACA;AACA;AACA,MAAMsG,WAAW,CAAC;EACdC,WAAWA,CAACC,WAAW,EAAEC,SAAS,EAAEC,aAAa,EAAEC,WAAW,EAAEC,kBAAkB,EAAE;IAChF,IAAI,CAACJ,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C;IACA,IAAI,CAACC,IAAI,GAAG,UAAU;IACtB;IACA,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;IACA,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B;IACA,IAAI,CAACC,QAAQ,GAAG,IAAIhF,OAAO,CAAC,CAAC;IAC7B;IACA,IAAI,CAACiF,QAAQ,GAAG,IAAIjF,OAAO,CAAC,CAAC;IAC7B;IACA,IAAI,CAACkF,YAAY,GAAG,KAAK;IACzB;IACA,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7BR,WAAW,EAAES,OAAO,GAAG,IAAI,CAAC;EAChC;EACA;EACAC,KAAKA,CAACC,MAAM,EAAEC,OAAO,EAAE;IACnB,IAAI,IAAI,CAACb,aAAa,IAAIY,MAAM,EAAE;MAC9B,IAAI,CAACZ,aAAa,CAACc,QAAQ,CAAC,IAAI,CAACC,eAAe,CAAC,CAAC,EAAEH,MAAM,EAAEC,OAAO,CAAC;IACxE,CAAC,MACI;MACD,IAAI,CAACE,eAAe,CAAC,CAAC,CAACJ,KAAK,CAACE,OAAO,CAAC;IACzC;IACA,IAAI,CAACN,QAAQ,CAACS,IAAI,CAAC,IAAI,CAAC;EAC5B;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACjB,aAAa,EAAE;MACpB;MACA;MACA;MACA,IAAI,CAACA,aAAa,CAACkB,OAAO,CAAC,IAAI,CAACpB,WAAW,EAAE,KAAK,CAAC;IACvD;EACJ;EACAqB,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACnB,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACoB,cAAc,CAAC,IAAI,CAACtB,WAAW,CAAC;IACvD;IACA,IAAI,IAAI,CAACG,WAAW,IAAI,IAAI,CAACA,WAAW,CAACoB,UAAU,EAAE;MACjD,IAAI,CAACpB,WAAW,CAACoB,UAAU,CAAC,IAAI,CAAC;IACrC;IACA,IAAI,CAACf,QAAQ,CAACgB,QAAQ,CAAC,CAAC;IACxB,IAAI,CAACf,QAAQ,CAACe,QAAQ,CAAC,CAAC;EAC5B;EACA;EACAC,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACnB,QAAQ,GAAG,IAAI,GAAG,GAAG;EACrC;EACA;EACAW,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACjB,WAAW,CAAC0B,aAAa;EACzC;EACA;EACAC,cAAcA,CAACC,KAAK,EAAE;IAClB,IAAI,IAAI,CAACtB,QAAQ,EAAE;MACfsB,KAAK,CAACC,cAAc,CAAC,CAAC;MACtBD,KAAK,CAACE,eAAe,CAAC,CAAC;IAC3B;EACJ;EACA;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACvB,QAAQ,CAACU,IAAI,CAAC,IAAI,CAAC;EAC5B;EACA;EACAc,QAAQA,CAAA,EAAG;IACP,MAAMC,KAAK,GAAG,IAAI,CAACjC,WAAW,CAAC0B,aAAa,CAACQ,SAAS,CAAC,IAAI,CAAC;IAC5D,MAAMC,KAAK,GAAGF,KAAK,CAACG,gBAAgB,CAAC,2BAA2B,CAAC;IACjE;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACnCF,KAAK,CAACE,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC;IACrB;IACA,OAAON,KAAK,CAACO,WAAW,EAAEC,IAAI,CAAC,CAAC,IAAI,EAAE;EAC1C;EACAC,eAAeA,CAACC,aAAa,EAAE;IAC3B;IACA;IACA;IACA;IACA,IAAI,CAACjC,YAAY,GAAGiC,aAAa;IACjC,IAAI,CAACvC,kBAAkB,EAAEwC,YAAY,CAAC,CAAC;EAC3C;EACAC,mBAAmBA,CAACC,eAAe,EAAE;IACjC;IACA,IAAI,CAACnC,gBAAgB,GAAGmC,eAAe;IACvC,IAAI,CAAC1C,kBAAkB,EAAEwC,YAAY,CAAC,CAAC;EAC3C;EACAG,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC9C,SAAS,IAAI,IAAI,CAACA,SAAS,CAAC+C,aAAa,KAAK,IAAI,CAAC/B,eAAe,CAAC,CAAC;EACpF;EACA;IAAS,IAAI,CAACgC,IAAI,YAAAC,oBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFrD,WAAW,EAArBvG,EAAE,CAAA6J,iBAAA,CAAqC7J,EAAE,CAAC8J,UAAU,GAApD9J,EAAE,CAAA6J,iBAAA,CAA+DjH,QAAQ,GAAzE5C,EAAE,CAAA6J,iBAAA,CAAoFxI,EAAE,CAAC0I,YAAY,GAArG/J,EAAE,CAAA6J,iBAAA,CAAgHvD,cAAc,MAAhItG,EAAE,CAAA6J,iBAAA,CAA2J7J,EAAE,CAACkB,iBAAiB;IAAA,CAA4C;EAAE;EAC/T;IAAS,IAAI,CAAC8I,IAAI,kBAD8EhK,EAAE,CAAAiK,iBAAA;MAAAC,IAAA,EACJ3D,WAAW;MAAA4D,SAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,yBAAApG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADTnE,EAAE,CAAA6E,UAAA,mBAAA2F,qCAAAzF,MAAA;YAAA,OACJX,GAAA,CAAAgE,cAAA,CAAArD,MAAqB,CAAC;UAAA,CAAZ,CAAC,wBAAA0F,0CAAA;YAAA,OAAXrG,GAAA,CAAAoE,iBAAA,CAAkB,CAAC;UAAA,CAAT,CAAC;QAAA;QAAA,IAAArE,EAAA;UADTnE,EAAE,CAAAkG,WAAA,SAAA9B,GAAA,CAAA0C,IAAA,cACJ1C,GAAA,CAAA8D,YAAA,CAAa,CAAC,mBAAA9D,GAAA,CAAA2C,QAAA,cAAA3C,GAAA,CAAA2C,QAAA,IAAF,IAAI;UADd/G,EAAE,CAAA0K,WAAA,kCAAAtG,GAAA,CAAA+C,YACM,CAAC,sCAAA/C,GAAA,CAAAgD,gBAAD,CAAC;QAAA;MAAA;MAAAuD,MAAA;QAAA7D,IAAA;QAAAC,QAAA,GADT/G,EAAE,CAAA4K,YAAA,CAAAC,0BAAA,0BACqH3K,gBAAgB;QAAA8G,aAAA,GADvIhH,EAAE,CAAA4K,YAAA,CAAAC,0BAAA,oCAC0L3K,gBAAgB;MAAA;MAAA4K,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAD5MhL,EAAE,CAAAiL,wBAAA,EAAFjL,EAAE,CAAAkL,mBAAA;MAAAC,KAAA,EAAApH,GAAA;MAAAqH,kBAAA,EAAAnH,GAAA;MAAAoH,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qBAAAtH,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFnE,EAAE,CAAA0L,eAAA,CAAA1H,GAAA;UAAFhE,EAAE,CAAA4F,YAAA,EACquB,CAAC;UADxuB5F,EAAE,CAAAsE,cAAA,aAC8wB,CAAC;UADjxBtE,EAAE,CAAA4F,YAAA,KACuyB,CAAC;UAD1yB5F,EAAE,CAAAwE,YAAA,CAC8yB,CAAC;UADjzBxE,EAAE,CAAAuE,SAAA,YAC48B,CAAC;UAD/8BvE,EAAE,CAAA2L,UAAA,IAAAzH,kCAAA,qBACw+B,CAAC;QAAA;QAAA,IAAAC,EAAA;UAD3+BnE,EAAE,CAAA4L,SAAA,EACo5B,CAAC;UADv5B5L,EAAE,CAAA+F,UAAA,sBAAA3B,GAAA,CAAA4C,aAAA,IAAA5C,GAAA,CAAA2C,QACo5B,CAAC,qBAAA3C,GAAA,CAAAsD,eAAA,EAA8C,CAAC;UADt8B1H,EAAE,CAAA4L,SAAA,CACiqC,CAAC;UADpqC5L,EAAE,CAAA6L,aAAA,IAAAzH,GAAA,CAAAgD,gBAAA,SACiqC,CAAC;QAAA;MAAA;MAAA0E,YAAA,GAA+ChJ,SAAS;MAAAiJ,aAAA;MAAAC,eAAA;IAAA,EAA6T;EAAE;AAC/nD;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGjM,EAAE,CAAAkM,iBAAA,CAGX3F,WAAW,EAAc,CAAC;IACzG2D,IAAI,EAAE/J,SAAS;IACfgM,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,iBAAiB;MAAEtB,QAAQ,EAAE,aAAa;MAAEuB,IAAI,EAAE;QACzD,aAAa,EAAE,MAAM;QACrB,OAAO,EAAE,2CAA2C;QACpD,uCAAuC,EAAE,cAAc;QACvD,2CAA2C,EAAE,kBAAkB;QAC/D,iBAAiB,EAAE,gBAAgB;QACnC,sBAAsB,EAAE,UAAU;QAClC,iBAAiB,EAAE,kBAAkB;QACrC,SAAS,EAAE,wBAAwB;QACnC,cAAc,EAAE;MACpB,CAAC;MAAEL,eAAe,EAAE5L,uBAAuB,CAACkM,MAAM;MAAEP,aAAa,EAAE1L,iBAAiB,CAACkM,IAAI;MAAExB,UAAU,EAAE,IAAI;MAAEyB,OAAO,EAAE,CAAC1J,SAAS,CAAC;MAAE0I,QAAQ,EAAE;IAAigB,CAAC;EAC3pB,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEtB,IAAI,EAAElK,EAAE,CAAC8J;EAAW,CAAC,EAAE;IAAEI,IAAI,EAAEuC,SAAS;IAAEC,UAAU,EAAE,CAAC;MACxExC,IAAI,EAAE5J,MAAM;MACZ6L,IAAI,EAAE,CAACvJ,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEsH,IAAI,EAAE7I,EAAE,CAAC0I;EAAa,CAAC,EAAE;IAAEG,IAAI,EAAEuC,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC7DxC,IAAI,EAAE5J,MAAM;MACZ6L,IAAI,EAAE,CAAC7F,cAAc;IACzB,CAAC,EAAE;MACC4D,IAAI,EAAE3J;IACV,CAAC;EAAE,CAAC,EAAE;IAAE2J,IAAI,EAAElK,EAAE,CAACkB;EAAkB,CAAC,CAAC,EAAkB;IAAE4F,IAAI,EAAE,CAAC;MAChEoD,IAAI,EAAE1J;IACV,CAAC,CAAC;IAAEuG,QAAQ,EAAE,CAAC;MACXmD,IAAI,EAAE1J,KAAK;MACX2L,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAEzM;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE8G,aAAa,EAAE,CAAC;MAChBkD,IAAI,EAAE1J,KAAK;MACX2L,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAEzM;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,SAAS0M,4BAA4BA,CAAA,EAAG;EACpC,MAAMC,KAAK,CAAC;AAChB,wEAAwE,CAAC;AACzE;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,4BAA4BA,CAAA,EAAG;EACpC,MAAMD,KAAK,CAAC;AAChB,uEAAuE,CAAC;AACxE;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,0BAA0BA,CAAA,EAAG;EAClC,MAAMF,KAAK,CAAC,gFAAgF,GACxF,sEAAsE,CAAC;AAC/E;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMG,gBAAgB,GAAG,IAAI/M,cAAc,CAAC,gBAAgB,CAAC;AAC7D;AACA,MAAMgN,cAAc,CAAC;EACjBzG,WAAWA,CAAC0G,SAAS,EAAEC,yBAAyB,EAAEC,OAAO,EAAEC,SAAS,EAAEC,iBAAiB,EAAE5G,SAAS,EAAEG,kBAAkB,EAAE;IACpH,IAAI,CAACqG,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAAC5G,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACG,kBAAkB,GAAGA,kBAAkB;IAC5C;IACA,IAAI,CAAC0G,SAAS,GAAG,IAAItL,OAAO,CAAC,CAAC;EAClC;EACA;AACJ;AACA;AACA;EACIuL,MAAMA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;IACjB,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE;MACf,IAAI,CAACA,OAAO,GAAG,IAAIzK,cAAc,CAAC,IAAI,CAACiK,SAAS,EAAE,IAAI,CAACI,iBAAiB,CAAC;IAC7E;IACA,IAAI,CAACK,MAAM,CAAC,CAAC;IACb,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE;MACf,IAAI,CAACA,OAAO,GAAG,IAAI1K,eAAe,CAAC,IAAI,CAACwD,SAAS,CAACmH,aAAa,CAAC,KAAK,CAAC,EAAE,IAAI,CAACV,yBAAyB,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAACC,SAAS,CAAC;IACzI;IACA,MAAMS,OAAO,GAAG,IAAI,CAACZ,SAAS,CAACa,UAAU,CAAC5F,aAAa;IACvD;IACA;IACA;IACA2F,OAAO,CAACE,UAAU,CAACC,YAAY,CAAC,IAAI,CAACL,OAAO,CAACM,aAAa,EAAEJ,OAAO,CAAC;IACpE;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACjH,kBAAkB,EAAEwC,YAAY,CAAC,CAAC;IACvC,IAAI,CAACqE,OAAO,CAACF,MAAM,CAAC,IAAI,CAACI,OAAO,EAAEH,OAAO,CAAC;IAC1C,IAAI,CAACF,SAAS,CAAC5F,IAAI,CAAC,CAAC;EACzB;EACA;AACJ;AACA;AACA;EACIgG,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACD,OAAO,CAACS,UAAU,EAAE;MACzB,IAAI,CAACT,OAAO,CAACC,MAAM,CAAC,CAAC;IACzB;EACJ;EACA7F,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC8F,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACQ,OAAO,CAAC,CAAC;IAC1B;EACJ;EACA;IAAS,IAAI,CAAC1E,IAAI,YAAA2E,uBAAAzE,CAAA;MAAA,YAAAA,CAAA,IAAwFqD,cAAc,EA1HxBjN,EAAE,CAAA6J,iBAAA,CA0HwC7J,EAAE,CAACY,WAAW,GA1HxDZ,EAAE,CAAA6J,iBAAA,CA0HmE7J,EAAE,CAACsO,wBAAwB,GA1HhGtO,EAAE,CAAA6J,iBAAA,CA0H2G7J,EAAE,CAACuO,cAAc,GA1H9HvO,EAAE,CAAA6J,iBAAA,CA0HyI7J,EAAE,CAACwO,QAAQ,GA1HtJxO,EAAE,CAAA6J,iBAAA,CA0HiK7J,EAAE,CAACyO,gBAAgB,GA1HtLzO,EAAE,CAAA6J,iBAAA,CA0HiMjH,QAAQ,GA1H3M5C,EAAE,CAAA6J,iBAAA,CA0HsN7J,EAAE,CAACkB,iBAAiB;IAAA,CAA4C;EAAE;EAC1X;IAAS,IAAI,CAACwN,IAAI,kBA3H8E1O,EAAE,CAAA2O,iBAAA;MAAAzE,IAAA,EA2HJ+C,cAAc;MAAA9C,SAAA;MAAAY,UAAA;MAAAC,QAAA,GA3HZhL,EAAE,CAAA4O,kBAAA,CA2HoF,CAAC;QAAEC,OAAO,EAAE7B,gBAAgB;QAAE8B,WAAW,EAAE7B;MAAe,CAAC,CAAC;IAAA,EAAiB;EAAE;AACzQ;AACA;EAAA,QAAAhB,SAAA,oBAAAA,SAAA,KA7HoGjM,EAAE,CAAAkM,iBAAA,CA6HXe,cAAc,EAAc,CAAC;IAC5G/C,IAAI,EAAEzJ,SAAS;IACf0L,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,6BAA6B;MACvC2C,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAE7B,gBAAgB;QAAE8B,WAAW,EAAE7B;MAAe,CAAC,CAAC;MACvElC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEb,IAAI,EAAElK,EAAE,CAACY;EAAY,CAAC,EAAE;IAAEsJ,IAAI,EAAElK,EAAE,CAACsO;EAAyB,CAAC,EAAE;IAAEpE,IAAI,EAAElK,EAAE,CAACuO;EAAe,CAAC,EAAE;IAAErE,IAAI,EAAElK,EAAE,CAACwO;EAAS,CAAC,EAAE;IAAEtE,IAAI,EAAElK,EAAE,CAACyO;EAAiB,CAAC,EAAE;IAAEvE,IAAI,EAAEuC,SAAS;IAAEC,UAAU,EAAE,CAAC;MACnMxC,IAAI,EAAE5J,MAAM;MACZ6L,IAAI,EAAE,CAACvJ,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEsH,IAAI,EAAElK,EAAE,CAACkB;EAAkB,CAAC,CAAC;AAAA;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8N,iBAAiB,GAAG;EACtB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,aAAa,EAAE9L,OAAO,CAAC,eAAe,EAAE,CACpCC,KAAK,CAAC,MAAM,EAAEC,KAAK,CAAC;IAChB6L,OAAO,EAAE,CAAC;IACVvC,SAAS,EAAE;EACf,CAAC,CAAC,CAAC,EACHrJ,UAAU,CAAC,eAAe,EAAEC,OAAO,CAAC,kCAAkC,EAAEF,KAAK,CAAC;IAC1E6L,OAAO,EAAE,CAAC;IACVvC,SAAS,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,EACJrJ,UAAU,CAAC,WAAW,EAAEC,OAAO,CAAC,mBAAmB,EAAEF,KAAK,CAAC;IAAE6L,OAAO,EAAE;EAAE,CAAC,CAAC,CAAC,CAAC,CAC/E,CAAC;EACF;AACJ;AACA;AACA;EACIC,WAAW,EAAEhM,OAAO,CAAC,aAAa,EAAE;EAChC;EACA;EACAC,KAAK,CAAC,SAAS,EAAEC,KAAK,CAAC;IAAE6L,OAAO,EAAE;EAAE,CAAC,CAAC,CAAC,EACvC5L,UAAU,CAAC,WAAW,EAAE,CACpBD,KAAK,CAAC;IAAE6L,OAAO,EAAE;EAAE,CAAC,CAAC,EACrB3L,OAAO,CAAC,8CAA8C,CAAC,CAC1D,CAAC,CACL;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAM4L,WAAW,GAAGH,iBAAiB,CAACG,WAAW;AACjD;AACA;AACA;AACA;AACA;AACA,MAAMF,aAAa,GAAGD,iBAAiB,CAACC,aAAa;AAErD,IAAIG,YAAY,GAAG,CAAC;AACpB;AACA,MAAMC,wBAAwB,GAAG,IAAIpP,cAAc,CAAC,0BAA0B,EAAE;EAC5EqP,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF;AACA,SAASA,gCAAgCA,CAAA,EAAG;EACxC,OAAO;IACHC,cAAc,EAAE,KAAK;IACrBC,SAAS,EAAE,OAAO;IAClBC,SAAS,EAAE,OAAO;IAClBC,aAAa,EAAE;EACnB,CAAC;AACL;AACA,MAAMC,OAAO,CAAC;EACV;EACA,IAAIH,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACI,UAAU;EAC1B;EACA,IAAIJ,SAASA,CAACK,KAAK,EAAE;IACjB,IAAIA,KAAK,KAAK,QAAQ,IAClBA,KAAK,KAAK,OAAO,KAChB,OAAO9D,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjDW,4BAA4B,CAAC,CAAC;IAClC;IACA,IAAI,CAACkD,UAAU,GAAGC,KAAK;IACvB,IAAI,CAACC,kBAAkB,CAAC,CAAC;EAC7B;EACA;EACA,IAAIL,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACM,UAAU;EAC1B;EACA,IAAIN,SAASA,CAACI,KAAK,EAAE;IACjB,IAAIA,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,OAAO,KAAK,OAAO9D,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC3Fa,4BAA4B,CAAC,CAAC;IAClC;IACA,IAAI,CAACmD,UAAU,GAAGF,KAAK;IACvB,IAAI,CAACC,kBAAkB,CAAC,CAAC;EAC7B;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIE,UAAUA,CAACC,OAAO,EAAE;IACpB,MAAMC,kBAAkB,GAAG,IAAI,CAACC,mBAAmB;IACnD,MAAMC,YAAY,GAAG;MAAE,GAAG,IAAI,CAACxK;IAAW,CAAC;IAC3C,IAAIsK,kBAAkB,IAAIA,kBAAkB,CAACrH,MAAM,EAAE;MACjDqH,kBAAkB,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAEC,SAAS,IAAK;QACjDH,YAAY,CAACG,SAAS,CAAC,GAAG,KAAK;MACnC,CAAC,CAAC;IACN;IACA,IAAI,CAACJ,mBAAmB,GAAGF,OAAO;IAClC,IAAIA,OAAO,IAAIA,OAAO,CAACpH,MAAM,EAAE;MAC3BoH,OAAO,CAACI,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAEC,SAAS,IAAK;QACtCH,YAAY,CAACG,SAAS,CAAC,GAAG,IAAI;MAClC,CAAC,CAAC;MACF,IAAI,CAAChK,WAAW,CAAC0B,aAAa,CAACsI,SAAS,GAAG,EAAE;IACjD;IACA,IAAI,CAAC3K,UAAU,GAAGwK,YAAY;EAClC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAII,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACR,UAAU;EAC1B;EACA,IAAIQ,SAASA,CAACP,OAAO,EAAE;IACnB,IAAI,CAACD,UAAU,GAAGC,OAAO;EAC7B;EACA3J,WAAWA,CAACC,WAAW,EAAEkK,OAAO,EAAEC,cAAc;EAChD;EACA/J,kBAAkB,EAAE;IAChB,IAAI,CAACJ,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACkK,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC9J,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACgK,gBAAgB,GAAG,iBAAiB;IACzC,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB;IACA,IAAI,CAACC,sBAAsB,GAAG,IAAIrQ,SAAS,CAAC,CAAC;IAC7C;IACA,IAAI,CAACoF,UAAU,GAAG,CAAC,CAAC;IACpB;IACA,IAAI,CAACG,oBAAoB,GAAG,MAAM;IAClC;IACA,IAAI,CAAC+K,cAAc,GAAG,IAAI/O,OAAO,CAAC,CAAC;IACnC;IACA,IAAI,CAACqD,MAAM,GAAG,IAAI3E,YAAY,CAAC,CAAC;IAChC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACsQ,KAAK,GAAG,IAAI,CAAC3L,MAAM;IACxB,IAAI,CAACU,OAAO,GAAG,kBAAkBoJ,YAAY,EAAE,EAAE;IACjD,IAAI,CAAC8B,iBAAiB,GAAGN,cAAc,CAACM,iBAAiB,IAAI,EAAE;IAC/D,IAAI,CAACpB,UAAU,GAAGc,cAAc,CAAClB,SAAS;IAC1C,IAAI,CAACO,UAAU,GAAGW,cAAc,CAACjB,SAAS;IAC1C,IAAI,CAACC,aAAa,GAAGgB,cAAc,CAAChB,aAAa;IACjD,IAAI,CAACH,cAAc,GAAGmB,cAAc,CAACnB,cAAc;IACnD,IAAI,CAAC0B,WAAW,GAAGP,cAAc,CAACO,WAAW;EACjD;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACpB,kBAAkB,CAAC,CAAC;EAC7B;EACAqB,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACC,wBAAwB,CAAC,CAAC;IAC/B,IAAI,CAACC,WAAW,GAAG,IAAIjQ,eAAe,CAAC,IAAI,CAACyP,sBAAsB,CAAC,CAC9DS,QAAQ,CAAC,CAAC,CACVC,aAAa,CAAC,CAAC,CACfC,cAAc,CAAC,CAAC;IACrB,IAAI,CAACH,WAAW,CAACI,MAAM,CAACC,SAAS,CAAC,MAAM,IAAI,CAACtM,MAAM,CAACC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChE;IACA;IACA;IACA,IAAI,CAACwL,sBAAsB,CAACc,OAAO,CAC9BC,IAAI,CAACxP,SAAS,CAAC,IAAI,CAACyO,sBAAsB,CAAC,EAAExO,SAAS,CAACwP,KAAK,IAAI7P,KAAK,CAAC,GAAG6P,KAAK,CAACC,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAAC/K,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC9G0K,SAAS,CAACM,WAAW,IAAI,IAAI,CAACX,WAAW,CAACY,gBAAgB,CAACD,WAAW,CAAC,CAAC;IAC7E,IAAI,CAACnB,sBAAsB,CAACc,OAAO,CAACD,SAAS,CAAEQ,SAAS,IAAK;MACzD;MACA;MACA;MACA,MAAMC,OAAO,GAAG,IAAI,CAACd,WAAW;MAChC,IAAI,IAAI,CAACtL,oBAAoB,KAAK,OAAO,IAAIoM,OAAO,CAACC,UAAU,EAAE9I,SAAS,CAAC,CAAC,EAAE;QAC1E,MAAMuI,KAAK,GAAGK,SAAS,CAACG,OAAO,CAAC,CAAC;QACjC,MAAMC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACZ,KAAK,CAAChJ,MAAM,GAAG,CAAC,EAAEsJ,OAAO,CAACO,eAAe,IAAI,CAAC,CAAC,CAAC;QACnF,IAAIb,KAAK,CAACS,KAAK,CAAC,IAAI,CAACT,KAAK,CAACS,KAAK,CAAC,CAACzL,QAAQ,EAAE;UACxCsL,OAAO,CAACQ,aAAa,CAACL,KAAK,CAAC;QAChC,CAAC,MACI;UACDH,OAAO,CAACS,iBAAiB,CAAC,CAAC;QAC/B;MACJ;IACJ,CAAC,CAAC;EACN;EACAhL,WAAWA,CAAA,EAAG;IACV,IAAI,CAACyJ,WAAW,EAAEwB,OAAO,CAAC,CAAC;IAC3B,IAAI,CAAChC,sBAAsB,CAACgC,OAAO,CAAC,CAAC;IACrC,IAAI,CAACzN,MAAM,CAAC2C,QAAQ,CAAC,CAAC;IACtB,IAAI,CAAC+K,2BAA2B,EAAEC,WAAW,CAAC,CAAC;EACnD;EACA;EACAhM,QAAQA,CAAA,EAAG;IACP;IACA,MAAMiM,WAAW,GAAG,IAAI,CAACnC,sBAAsB,CAACc,OAAO;IACvD,OAAOqB,WAAW,CAACpB,IAAI,CAACxP,SAAS,CAAC,IAAI,CAACyO,sBAAsB,CAAC,EAAExO,SAAS,CAACwP,KAAK,IAAI7P,KAAK,CAAC,GAAG6P,KAAK,CAACC,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAAChL,QAAQ,CAAC,CAAC,CAAC,CAAC;EACrI;EACA;AACJ;AACA;AACA;AACA;AACA;EACII,OAAOA,CAAC8L,KAAK,EAAE,CAAE;EACjB;AACJ;AACA;AACA;AACA;AACA;EACInL,UAAUA,CAACmL,KAAK,EAAE,CAAE;EACpB;EACA/N,cAAcA,CAACiD,KAAK,EAAE;IAClB,MAAM+K,OAAO,GAAG/K,KAAK,CAAC+K,OAAO;IAC7B,MAAMf,OAAO,GAAG,IAAI,CAACd,WAAW;IAChC,QAAQ6B,OAAO;MACX,KAAKvR,MAAM;QACP,IAAI,CAACC,cAAc,CAACuG,KAAK,CAAC,EAAE;UACxBA,KAAK,CAACC,cAAc,CAAC,CAAC;UACtB,IAAI,CAAChD,MAAM,CAACC,IAAI,CAAC,SAAS,CAAC;QAC/B;QACA;MACJ,KAAK3D,UAAU;QACX,IAAI,IAAI,CAACyR,UAAU,IAAI,IAAI,CAACC,SAAS,KAAK,KAAK,EAAE;UAC7C,IAAI,CAAChO,MAAM,CAACC,IAAI,CAAC,SAAS,CAAC;QAC/B;QACA;MACJ,KAAK5D,WAAW;QACZ,IAAI,IAAI,CAAC0R,UAAU,IAAI,IAAI,CAACC,SAAS,KAAK,KAAK,EAAE;UAC7C,IAAI,CAAChO,MAAM,CAACC,IAAI,CAAC,SAAS,CAAC;QAC/B;QACA;MACJ;QACI,IAAI6N,OAAO,KAAK3R,QAAQ,IAAI2R,OAAO,KAAK1R,UAAU,EAAE;UAChD2Q,OAAO,CAACkB,cAAc,CAAC,UAAU,CAAC;QACtC;QACAlB,OAAO,CAACmB,SAAS,CAACnL,KAAK,CAAC;QACxB;IACR;IACA;IACA;IACAA,KAAK,CAACE,eAAe,CAAC,CAAC;EAC3B;EACA;AACJ;AACA;AACA;EACIkL,cAAcA,CAAClM,MAAM,GAAG,SAAS,EAAE;IAC/B;IACA,IAAI,CAACyL,2BAA2B,EAAEC,WAAW,CAAC,CAAC;IAC/C,IAAI,CAACD,2BAA2B,GAAG,IAAI,CAACrC,OAAO,CAAC+C,QAAQ,CAAC5B,IAAI,CAACtP,IAAI,CAAC,CAAC,CAAC,CAAC,CAACoP,SAAS,CAAC,MAAM;MACnF,IAAI+B,SAAS,GAAG,IAAI;MACpB,IAAI,IAAI,CAAC5C,sBAAsB,CAAChI,MAAM,EAAE;QACpC;QACA;QACA;QACA;QACA4K,SAAS,GAAG,IAAI,CAAC5C,sBAAsB,CAAC6C,KAAK,CAAClM,eAAe,CAAC,CAAC,CAACmM,OAAO,CAAC,eAAe,CAAC;MAC5F;MACA;MACA,IAAI,CAACF,SAAS,IAAI,CAACA,SAAS,CAACG,QAAQ,CAACC,QAAQ,CAACtK,aAAa,CAAC,EAAE;QAC3D,MAAM4I,OAAO,GAAG,IAAI,CAACd,WAAW;QAChCc,OAAO,CAACkB,cAAc,CAAChM,MAAM,CAAC,CAACyM,kBAAkB,CAAC,CAAC;QACnD;QACA;QACA;QACA,IAAI,CAAC3B,OAAO,CAACC,UAAU,IAAIqB,SAAS,EAAE;UAClCA,SAAS,CAACrM,KAAK,CAAC,CAAC;QACrB;MACJ;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACI2M,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC1C,WAAW,CAACsB,aAAa,CAAC,CAAC,CAAC,CAAC;EACtC;EACA;AACJ;AACA;AACA;EACIqB,YAAYA,CAACC,KAAK,EAAE;IAChB;IACA;IACA,MAAMC,SAAS,GAAG3B,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC7B,cAAc,GAAGqD,KAAK,EAAE,EAAE,CAAC;IAC3D,MAAME,YAAY,GAAG,GAAG,IAAI,CAACxD,gBAAgB,GAAGuD,SAAS,EAAE;IAC3D,MAAME,eAAe,GAAGC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC1O,UAAU,CAAC,CAAC2O,IAAI,CAAChE,SAAS,IAAI;MACnE,OAAOA,SAAS,CAACiE,UAAU,CAAC,IAAI,CAAC7D,gBAAgB,CAAC;IACtD,CAAC,CAAC;IACF,IAAI,CAACyD,eAAe,IAAIA,eAAe,KAAK,IAAI,CAACK,kBAAkB,EAAE;MACjE,MAAMrE,YAAY,GAAG;QAAE,GAAG,IAAI,CAACxK;MAAW,CAAC;MAC3C,IAAI,IAAI,CAAC6O,kBAAkB,EAAE;QACzBrE,YAAY,CAAC,IAAI,CAACqE,kBAAkB,CAAC,GAAG,KAAK;MACjD;MACArE,YAAY,CAAC+D,YAAY,CAAC,GAAG,IAAI;MACjC,IAAI,CAACM,kBAAkB,GAAGN,YAAY;MACtC,IAAI,CAACvO,UAAU,GAAGwK,YAAY;IAClC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIN,kBAAkBA,CAAC4E,IAAI,GAAG,IAAI,CAAClF,SAAS,EAAEmF,IAAI,GAAG,IAAI,CAAClF,SAAS,EAAE;IAC7D,IAAI,CAAC7J,UAAU,GAAG;MACd,GAAG,IAAI,CAACA,UAAU;MAClB,CAAC,iBAAiB,GAAG8O,IAAI,KAAK,QAAQ;MACtC,CAAC,gBAAgB,GAAGA,IAAI,KAAK,OAAO;MACpC,CAAC,gBAAgB,GAAGC,IAAI,KAAK,OAAO;MACpC,CAAC,gBAAgB,GAAGA,IAAI,KAAK;IACjC,CAAC;IACD;IACA,IAAI,CAAChO,kBAAkB,EAAEwC,YAAY,CAAC,CAAC;EAC3C;EACA;EACAyL,eAAeA,CAAA,EAAG;IACd;IACA,IAAI,CAAC7O,oBAAoB,GAAG,OAAO;EACvC;EACA;EACA8O,eAAeA,CAAA,EAAG;IACd;IACA,IAAI,CAAC9O,oBAAoB,GAAG,MAAM;EACtC;EACA;EACAN,gBAAgBA,CAAC0C,KAAK,EAAE;IACpB,IAAI,CAAC2I,cAAc,CAACrJ,IAAI,CAACU,KAAK,CAAC;IAC/B,IAAI,CAAC2M,YAAY,GAAG,KAAK;EAC7B;EACAvP,iBAAiBA,CAAC4C,KAAK,EAAE;IACrB,IAAI,CAAC2M,YAAY,GAAG,IAAI;IACxB;IACA;IACA;IACA;IACA;IACA;IACA,IAAI3M,KAAK,CAAC4M,OAAO,KAAK,OAAO,IAAI,IAAI,CAAC1D,WAAW,CAACqB,eAAe,KAAK,CAAC,EAAE;MACrEvK,KAAK,CAACyF,OAAO,CAACoH,SAAS,GAAG,CAAC;IAC/B;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI5D,wBAAwBA,CAAA,EAAG;IACvB,IAAI,CAAC6D,SAAS,CAACtD,OAAO,CACjBC,IAAI,CAACxP,SAAS,CAAC,IAAI,CAAC6S,SAAS,CAAC,CAAC,CAC/BvD,SAAS,CAAEG,KAAK,IAAK;MACtB,IAAI,CAAChB,sBAAsB,CAACqE,KAAK,CAACrD,KAAK,CAACrP,MAAM,CAACuP,IAAI,IAAIA,IAAI,CAACrL,WAAW,KAAK,IAAI,CAAC,CAAC;MAClF,IAAI,CAACmK,sBAAsB,CAACsE,eAAe,CAAC,CAAC;IACjD,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAAC3L,IAAI,YAAA4L,gBAAA1L,CAAA;MAAA,YAAAA,CAAA,IAAwFiG,OAAO,EA1fjB7P,EAAE,CAAA6J,iBAAA,CA0fiC7J,EAAE,CAAC8J,UAAU,GA1fhD9J,EAAE,CAAA6J,iBAAA,CA0f2D7J,EAAE,CAACuV,MAAM,GA1ftEvV,EAAE,CAAA6J,iBAAA,CA0fiFwF,wBAAwB,GA1f3GrP,EAAE,CAAA6J,iBAAA,CA0fsH7J,EAAE,CAACkB,iBAAiB;IAAA,CAA4C;EAAE;EAC1R;IAAS,IAAI,CAAC8I,IAAI,kBA3f8EhK,EAAE,CAAAiK,iBAAA;MAAAC,IAAA,EA2fJ2F,OAAO;MAAA1F,SAAA;MAAAqL,cAAA,WAAAC,uBAAAtR,EAAA,EAAAC,GAAA,EAAAsR,QAAA;QAAA,IAAAvR,EAAA;UA3fLnE,EAAE,CAAA2V,cAAA,CAAAD,QAAA,EA2fyzB1I,gBAAgB;UA3f30BhN,EAAE,CAAA2V,cAAA,CAAAD,QAAA,EA2fw4BnP,WAAW;UA3fr5BvG,EAAE,CAAA2V,cAAA,CAAAD,QAAA,EA2f88BnP,WAAW;QAAA;QAAA,IAAApC,EAAA;UAAA,IAAAyR,EAAA;UA3f39B5V,EAAE,CAAA6V,cAAA,CAAAD,EAAA,GAAF5V,EAAE,CAAA8V,WAAA,QAAA1R,GAAA,CAAA2R,WAAA,GAAAH,EAAA,CAAAhC,KAAA;UAAF5T,EAAE,CAAA6V,cAAA,CAAAD,EAAA,GAAF5V,EAAE,CAAA8V,WAAA,QAAA1R,GAAA,CAAA+Q,SAAA,GAAAS,EAAA;UAAF5V,EAAE,CAAA6V,cAAA,CAAAD,EAAA,GAAF5V,EAAE,CAAA8V,WAAA,QAAA1R,GAAA,CAAA2N,KAAA,GAAA6D,EAAA;QAAA;MAAA;MAAAI,SAAA,WAAAC,cAAA9R,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFnE,EAAE,CAAAkW,WAAA,CA2fmiCtV,WAAW;QAAA;QAAA,IAAAuD,EAAA;UAAA,IAAAyR,EAAA;UA3fhjC5V,EAAE,CAAA6V,cAAA,CAAAD,EAAA,GAAF5V,EAAE,CAAA8V,WAAA,QAAA1R,GAAA,CAAA+R,WAAA,GAAAP,EAAA,CAAAhC,KAAA;QAAA;MAAA;MAAAvJ,QAAA;MAAAC,YAAA,WAAA8L,qBAAAjS,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFnE,EAAE,CAAAkG,WAAA,eA2fJ,IAAI,qBAAJ,IAAI,sBAAJ,IAAI;QAAA;MAAA;MAAAyE,MAAA;QAAAiF,aAAA;QAAAzJ,SAAA,GA3fFnG,EAAE,CAAA4K,YAAA,CAAA2B,IAAA;QAAAnG,cAAA,GAAFpG,EAAE,CAAA4K,YAAA,CAAA2B,IAAA;QAAAlG,eAAA,GAAFrG,EAAE,CAAA4K,YAAA,CAAA2B,IAAA;QAAAmD,SAAA;QAAAC,SAAA;QAAAF,cAAA,GAAFzP,EAAE,CAAA4K,YAAA,CAAAC,0BAAA,sCA2fuV3K,gBAAgB;QAAAiR,WAAA,GA3fzWnR,EAAE,CAAA4K,YAAA,CAAAC,0BAAA,gCA2fuZkF,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,IAAI,GAAG7P,gBAAgB,CAAC6P,KAAK,CAAE;QAAAG,UAAA,GA3fndlQ,EAAE,CAAA4K,YAAA,CAAA2B,IAAA;QAAAmE,SAAA;MAAA;MAAA2F,OAAA;QAAA/Q,MAAA;QAAA2L,KAAA;MAAA;MAAAnG,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAAFhL,EAAE,CAAA4O,kBAAA,CA2fmsB,CAAC;QAAEC,OAAO,EAAEvI,cAAc;QAAEwI,WAAW,EAAEe;MAAQ,CAAC,CAAC,GA3fxvB7P,EAAE,CAAAiL,wBAAA,EAAFjL,EAAE,CAAAkL,mBAAA;MAAAE,kBAAA,EAAA3G,GAAA;MAAA4G,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA8K,iBAAAnS,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFnE,EAAE,CAAA0L,eAAA;UAAF1L,EAAE,CAAA2L,UAAA,IAAAjH,8BAAA,qBA2fkoC,CAAC;QAAA;MAAA;MAAA6R,MAAA;MAAAxK,aAAA;MAAAyK,IAAA;QAAAC,SAAA,EAAu/I,CAACzH,iBAAiB,CAACC,aAAa,EAAED,iBAAiB,CAACG,WAAW;MAAC;MAAAnD,eAAA;IAAA,EAAiG;EAAE;AACn4L;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA7foGjM,EAAE,CAAAkM,iBAAA,CA6fX2D,OAAO,EAAc,CAAC;IACrG3F,IAAI,EAAE/J,SAAS;IACfgM,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,UAAU;MAAEJ,eAAe,EAAE5L,uBAAuB,CAACkM,MAAM;MAAEP,aAAa,EAAE1L,iBAAiB,CAACkM,IAAI;MAAEzB,QAAQ,EAAE,SAAS;MAAEuB,IAAI,EAAE;QACtI,mBAAmB,EAAE,MAAM;QAC3B,wBAAwB,EAAE,MAAM;QAChC,yBAAyB,EAAE;MAC/B,CAAC;MAAEqK,UAAU,EAAE,CAAC1H,iBAAiB,CAACC,aAAa,EAAED,iBAAiB,CAACG,WAAW,CAAC;MAAEJ,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEvI,cAAc;QAAEwI,WAAW,EAAEe;MAAQ,CAAC,CAAC;MAAE9E,UAAU,EAAE,IAAI;MAAES,QAAQ,EAAE,grBAAgrB;MAAE+K,MAAM,EAAE,CAAC,2zHAA2zH;IAAE,CAAC;EAClrJ,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAErM,IAAI,EAAElK,EAAE,CAAC8J;EAAW,CAAC,EAAE;IAAEI,IAAI,EAAElK,EAAE,CAACuV;EAAO,CAAC,EAAE;IAAErL,IAAI,EAAEuC,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC7FxC,IAAI,EAAE5J,MAAM;MACZ6L,IAAI,EAAE,CAACkD,wBAAwB;IACnC,CAAC;EAAE,CAAC,EAAE;IAAEnF,IAAI,EAAElK,EAAE,CAACkB;EAAkB,CAAC,CAAC,EAAkB;IAAEiU,SAAS,EAAE,CAAC;MACrEjL,IAAI,EAAErJ,eAAe;MACrBsL,IAAI,EAAE,CAAC5F,WAAW,EAAE;QAAEoQ,WAAW,EAAE;MAAK,CAAC;IAC7C,CAAC,CAAC;IAAE/G,aAAa,EAAE,CAAC;MAChB1F,IAAI,EAAE1J;IACV,CAAC,CAAC;IAAE2F,SAAS,EAAE,CAAC;MACZ+D,IAAI,EAAE1J,KAAK;MACX2L,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAE/F,cAAc,EAAE,CAAC;MACjB8D,IAAI,EAAE1J,KAAK;MACX2L,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAE9F,eAAe,EAAE,CAAC;MAClB6D,IAAI,EAAE1J,KAAK;MACX2L,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAEuD,SAAS,EAAE,CAAC;MACZxF,IAAI,EAAE1J;IACV,CAAC,CAAC;IAAEmP,SAAS,EAAE,CAAC;MACZzF,IAAI,EAAE1J;IACV,CAAC,CAAC;IAAE2V,WAAW,EAAE,CAAC;MACdjM,IAAI,EAAEpJ,SAAS;MACfqL,IAAI,EAAE,CAACvL,WAAW;IACtB,CAAC,CAAC;IAAEmR,KAAK,EAAE,CAAC;MACR7H,IAAI,EAAErJ,eAAe;MACrBsL,IAAI,EAAE,CAAC5F,WAAW,EAAE;QAAEoQ,WAAW,EAAE;MAAM,CAAC;IAC9C,CAAC,CAAC;IAAEZ,WAAW,EAAE,CAAC;MACd7L,IAAI,EAAEnJ,YAAY;MAClBoL,IAAI,EAAE,CAACa,gBAAgB;IAC3B,CAAC,CAAC;IAAEyC,cAAc,EAAE,CAAC;MACjBvF,IAAI,EAAE1J,KAAK;MACX2L,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAEzM;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEiR,WAAW,EAAE,CAAC;MACdjH,IAAI,EAAE1J,KAAK;MACX2L,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAGoD,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,IAAI,GAAG7P,gBAAgB,CAAC6P,KAAK;MAAG,CAAC;IACrF,CAAC,CAAC;IAAEG,UAAU,EAAE,CAAC;MACbhG,IAAI,EAAE1J,KAAK;MACX2L,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEuE,SAAS,EAAE,CAAC;MACZxG,IAAI,EAAE1J;IACV,CAAC,CAAC;IAAE8E,MAAM,EAAE,CAAC;MACT4E,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEiQ,KAAK,EAAE,CAAC;MACR/G,IAAI,EAAElJ;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAM4V,wBAAwB,GAAG,IAAI3W,cAAc,CAAC,0BAA0B,EAAE;EAC5EqP,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAM;IACX,MAAMsH,OAAO,GAAG5V,MAAM,CAACyC,OAAO,CAAC;IAC/B,OAAO,MAAMmT,OAAO,CAACC,gBAAgB,CAACC,UAAU,CAAC,CAAC;EACtD;AACJ,CAAC,CAAC;AACF;AACA,SAASC,gCAAgCA,CAACH,OAAO,EAAE;EAC/C,OAAO,MAAMA,OAAO,CAACC,gBAAgB,CAACC,UAAU,CAAC,CAAC;AACtD;AACA;AACA,MAAME,yCAAyC,GAAG;EAC9CpI,OAAO,EAAE+H,wBAAwB;EACjCM,IAAI,EAAE,CAACxT,OAAO,CAAC;EACfyT,UAAU,EAAEH;AAChB,CAAC;AACD;AACA,MAAMI,2BAA2B,GAAGvT,+BAA+B,CAAC;EAAEwT,OAAO,EAAE;AAAK,CAAC,CAAC;AACtF;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAG,CAAC;AAChC;AACA,MAAMC,cAAc,CAAC;EACjB;AACJ;AACA;AACA;EACI,IAAIC,4BAA4BA,CAAA,EAAG;IAC/B,OAAO,IAAI,CAACC,IAAI;EACpB;EACA,IAAID,4BAA4BA,CAACE,CAAC,EAAE;IAChC,IAAI,CAACD,IAAI,GAAGC,CAAC;EACjB;EACA;EACA,IAAID,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACE,KAAK;EACrB;EACA,IAAIF,IAAIA,CAACA,IAAI,EAAE;IACX,IAAIA,IAAI,KAAK,IAAI,CAACE,KAAK,EAAE;MACrB;IACJ;IACA,IAAI,CAACA,KAAK,GAAGF,IAAI;IACjB,IAAI,CAACG,sBAAsB,CAAC3E,WAAW,CAAC,CAAC;IACzC,IAAIwE,IAAI,EAAE;MACN,IAAIA,IAAI,KAAK,IAAI,CAACI,mBAAmB,KAAK,OAAO5L,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACtFc,0BAA0B,CAAC,CAAC;MAChC;MACA,IAAI,CAAC6K,sBAAsB,GAAGH,IAAI,CAACxG,KAAK,CAACW,SAAS,CAAEkG,MAAM,IAAK;QAC3D,IAAI,CAACC,YAAY,CAACD,MAAM,CAAC;QACzB;QACA,IAAI,CAACA,MAAM,KAAK,OAAO,IAAIA,MAAM,KAAK,KAAK,KAAK,IAAI,CAACD,mBAAmB,EAAE;UACtE,IAAI,CAACA,mBAAmB,CAACvS,MAAM,CAACC,IAAI,CAACuS,MAAM,CAAC;QAChD;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAACE,iBAAiB,EAAE1O,mBAAmB,CAAC,IAAI,CAACC,eAAe,CAAC,CAAC,CAAC;EACvE;EACA/C,WAAWA,CAACyR,QAAQ,EAAEC,QAAQ,EAAE5K,iBAAiB,EAAE6K,cAAc,EAAE9E,UAAU;EAC7E;EACA;EACA2E,iBAAiB,EAAEI,IAAI,EAAEzR,aAAa,EAAEgK,OAAO,EAAE;IAC7C,IAAI,CAACsH,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC5K,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAAC0K,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACI,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACzR,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACgK,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC0H,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,2BAA2B,GAAGpW,YAAY,CAACqW,KAAK;IACrD,IAAI,CAACC,kBAAkB,GAAGtW,YAAY,CAACqW,KAAK;IAC5C,IAAI,CAACZ,sBAAsB,GAAGzV,YAAY,CAACqW,KAAK;IAChD,IAAI,CAAC3R,kBAAkB,GAAG5F,MAAM,CAACC,iBAAiB,CAAC;IACnD;AACR;AACA;AACA;IACQ,IAAI,CAACwX,iBAAiB,GAAIrQ,KAAK,IAAK;MAChC,IAAI,CAAC9G,gCAAgC,CAAC8G,KAAK,CAAC,EAAE;QAC1C,IAAI,CAACsQ,SAAS,GAAG,OAAO;MAC5B;IACJ,CAAC;IACD;IACA;IACA,IAAI,CAACA,SAAS,GAAGlM,SAAS;IAC1B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACmM,YAAY,GAAG,IAAI;IACxB;IACA,IAAI,CAACC,UAAU,GAAG,IAAIlY,YAAY,CAAC,CAAC;IACpC;AACR;AACA;AACA;AACA;IACQ;IACA,IAAI,CAACmY,UAAU,GAAG,IAAI,CAACD,UAAU;IACjC;IACA,IAAI,CAACE,UAAU,GAAG,IAAIpY,YAAY,CAAC,CAAC;IACpC;AACR;AACA;AACA;AACA;IACQ;IACA,IAAI,CAACqY,WAAW,GAAG,IAAI,CAACD,UAAU;IAClC,IAAI,CAACE,eAAe,GAAGd,cAAc;IACrC,IAAI,CAACN,mBAAmB,GAAGxE,UAAU,YAAYxD,OAAO,GAAGwD,UAAU,GAAG5G,SAAS;IACjFyL,QAAQ,CAAC/P,aAAa,CAAC+Q,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAACR,iBAAiB,EAAEtB,2BAA2B,CAAC;EAC9G;EACA/F,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC8H,YAAY,CAAC,CAAC;EACvB;EACArR,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACuQ,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAACjK,OAAO,CAAC,CAAC;MAC1B,IAAI,CAACiK,WAAW,GAAG,IAAI;IAC3B;IACA,IAAI,CAACH,QAAQ,CAAC/P,aAAa,CAACiR,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAACV,iBAAiB,EAAEtB,2BAA2B,CAAC;IAClH,IAAI,CAACQ,sBAAsB,CAAC3E,WAAW,CAAC,CAAC;IACzC,IAAI,CAACsF,2BAA2B,CAACtF,WAAW,CAAC,CAAC;IAC9C,IAAI,CAACwF,kBAAkB,CAACxF,WAAW,CAAC,CAAC;EACzC;EACA;EACA,IAAIoG,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACf,SAAS;EACzB;EACA;EACA,IAAIgB,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAAClB,IAAI,IAAI,IAAI,CAACA,IAAI,CAACrI,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;EACjE;EACA;EACAxG,eAAeA,CAAA,EAAG;IACd,OAAO,CAAC,EAAE,IAAI,CAACyO,iBAAiB,IAAI,IAAI,CAACH,mBAAmB,IAAI,IAAI,CAACJ,IAAI,CAAC;EAC9E;EACA;EACA8B,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACjB,SAAS,GAAG,IAAI,CAACkB,SAAS,CAAC,CAAC,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;EAC9D;EACA;EACAA,QAAQA,CAAA,EAAG;IACP,MAAMhC,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,IAAI,IAAI,CAACa,SAAS,IAAI,CAACb,IAAI,EAAE;MACzB;IACJ;IACA,MAAMiC,UAAU,GAAG,IAAI,CAACC,cAAc,CAAClC,IAAI,CAAC;IAC5C,MAAMmC,aAAa,GAAGF,UAAU,CAACG,SAAS,CAAC,CAAC;IAC5C,MAAMC,gBAAgB,GAAGF,aAAa,CAACE,gBAAgB;IACvD,IAAI,CAACC,YAAY,CAACtC,IAAI,EAAEqC,gBAAgB,CAAC;IACzCF,aAAa,CAACzI,WAAW,GACrBsG,IAAI,CAACtG,WAAW,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC5H,eAAe,CAAC,CAAC,GAAGkO,IAAI,CAACtG,WAAW;IACzEuI,UAAU,CAAClM,MAAM,CAAC,IAAI,CAACwM,UAAU,CAACvC,IAAI,CAAC,CAAC;IACxC,IAAIA,IAAI,CAAC1B,WAAW,EAAE;MAClB0B,IAAI,CAAC1B,WAAW,CAACvI,MAAM,CAAC,IAAI,CAACyM,QAAQ,CAAC;IAC1C;IACA,IAAI,CAAC1B,2BAA2B,GAAG,IAAI,CAAC2B,mBAAmB,CAAC,CAAC,CAACtI,SAAS,CAAC,MAAM,IAAI,CAAC4H,SAAS,CAAC,CAAC,CAAC;IAC/F,IAAI,CAACW,SAAS,CAAC1C,IAAI,CAAC;IACpB,IAAIA,IAAI,YAAY5H,OAAO,EAAE;MACzB4H,IAAI,CAAC3C,eAAe,CAAC,CAAC;MACtB2C,IAAI,CAAC1G,sBAAsB,CAACc,OAAO,CAACC,IAAI,CAACrP,SAAS,CAACgV,IAAI,CAACxG,KAAK,CAAC,CAAC,CAACW,SAAS,CAAC,MAAM;QAC5E;QACA;QACAkI,gBAAgB,CAACM,kBAAkB,CAAC,KAAK,CAAC,CAACC,mBAAmB,CAAC,CAAC;QAChEP,gBAAgB,CAACM,kBAAkB,CAAC,IAAI,CAAC;MAC7C,CAAC,CAAC;IACN;EACJ;EACA;EACAZ,SAASA,CAAA,EAAG;IACR,IAAI,CAAC/B,IAAI,EAAExG,KAAK,CAAC1L,IAAI,CAAC,CAAC;EAC3B;EACA;AACJ;AACA;AACA;EACI+B,KAAKA,CAACC,MAAM,EAAEC,OAAO,EAAE;IACnB,IAAI,IAAI,CAACb,aAAa,IAAIY,MAAM,EAAE;MAC9B,IAAI,CAACZ,aAAa,CAACc,QAAQ,CAAC,IAAI,CAACyQ,QAAQ,EAAE3Q,MAAM,EAAEC,OAAO,CAAC;IAC/D,CAAC,MACI;MACD,IAAI,CAAC0Q,QAAQ,CAAC/P,aAAa,CAACb,KAAK,CAACE,OAAO,CAAC;IAC9C;EACJ;EACA;AACJ;AACA;EACI8S,cAAcA,CAAA,EAAG;IACb,IAAI,CAACjC,WAAW,EAAEiC,cAAc,CAAC,CAAC;EACtC;EACA;EACAvC,YAAYA,CAACD,MAAM,EAAE;IACjB,IAAI,CAAC,IAAI,CAACO,WAAW,IAAI,CAAC,IAAI,CAACgB,QAAQ,EAAE;MACrC;IACJ;IACA,MAAM5B,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,IAAI,CAACc,2BAA2B,CAACtF,WAAW,CAAC,CAAC;IAC9C,IAAI,CAACoF,WAAW,CAAC1K,MAAM,CAAC,CAAC;IACzB;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACiL,YAAY,KAAKd,MAAM,KAAK,SAAS,IAAI,CAAC,IAAI,CAACa,SAAS,IAAI,CAAC,IAAI,CAACpP,eAAe,CAAC,CAAC,CAAC,EAAE;MAC3F,IAAI,CAACjC,KAAK,CAAC,IAAI,CAACqR,SAAS,CAAC;IAC9B;IACA,IAAI,CAACA,SAAS,GAAGlM,SAAS;IAC1B,IAAIgL,IAAI,YAAY5H,OAAO,EAAE;MACzB4H,IAAI,CAAC1C,eAAe,CAAC,CAAC;MACtB,IAAI0C,IAAI,CAAC1B,WAAW,EAAE;QAClB;QACA0B,IAAI,CAACzG,cAAc,CACdc,IAAI,CAACpP,MAAM,CAAC2F,KAAK,IAAIA,KAAK,CAAC4M,OAAO,KAAK,MAAM,CAAC,EAAEzS,IAAI,CAAC,CAAC,CAAC;QAC5D;QACAC,SAAS,CAACgV,IAAI,CAAC1B,WAAW,CAACxI,SAAS,CAAC,CAAC,CACjCqE,SAAS,CAAC;UACXjK,IAAI,EAAEA,CAAA,KAAM8P,IAAI,CAAC1B,WAAW,CAACpI,MAAM,CAAC,CAAC;UACrC;UACA1F,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACsS,cAAc,CAAC,KAAK;QAC7C,CAAC,CAAC;MACN,CAAC,MACI;QACD,IAAI,CAACA,cAAc,CAAC,KAAK,CAAC;MAC9B;IACJ,CAAC,MACI;MACD,IAAI,CAACA,cAAc,CAAC,KAAK,CAAC;MAC1B9C,IAAI,EAAE1B,WAAW,EAAEpI,MAAM,CAAC,CAAC;IAC/B;EACJ;EACA;AACJ;AACA;AACA;EACIwM,SAASA,CAAC1C,IAAI,EAAE;IACZA,IAAI,CAACpE,UAAU,GAAG,IAAI,CAAC9J,eAAe,CAAC,CAAC,GAAG,IAAI,CAACsO,mBAAmB,GAAGpL,SAAS;IAC/EgL,IAAI,CAACnE,SAAS,GAAG,IAAI,CAACgG,GAAG;IACzB,IAAI,CAACkB,iBAAiB,CAAC/C,IAAI,CAAC;IAC5BA,IAAI,CAAChE,cAAc,CAAC,IAAI,CAACkF,SAAS,IAAI,SAAS,CAAC;IAChD,IAAI,CAAC4B,cAAc,CAAC,IAAI,CAAC;EAC7B;EACA;EACAC,iBAAiBA,CAAC/C,IAAI,EAAE;IACpB,IAAIA,IAAI,CAACvD,YAAY,EAAE;MACnB,IAAIC,KAAK,GAAG,CAAC;MACb,IAAId,UAAU,GAAGoE,IAAI,CAACpE,UAAU;MAChC,OAAOA,UAAU,EAAE;QACfc,KAAK,EAAE;QACPd,UAAU,GAAGA,UAAU,CAACA,UAAU;MACtC;MACAoE,IAAI,CAACvD,YAAY,CAACC,KAAK,CAAC;IAC5B;EACJ;EACA;EACAoG,cAAcA,CAACE,MAAM,EAAE;IACnB,IAAIA,MAAM,KAAK,IAAI,CAACnC,SAAS,EAAE;MAC3B,IAAI,CAACA,SAAS,GAAGmC,MAAM;MACvB,IAAI,CAACnC,SAAS,GAAG,IAAI,CAACO,UAAU,CAACtT,IAAI,CAAC,CAAC,GAAG,IAAI,CAACwT,UAAU,CAACxT,IAAI,CAAC,CAAC;MAChE,IAAI,IAAI,CAACgE,eAAe,CAAC,CAAC,EAAE;QACxB,IAAI,CAACyO,iBAAiB,CAAC7O,eAAe,CAACsR,MAAM,CAAC;MAClD;MACA,IAAI,CAAC5T,kBAAkB,CAACwC,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;AACA;EACIsQ,cAAcA,CAAClC,IAAI,EAAE;IACjB,IAAI,CAAC,IAAI,CAACY,WAAW,EAAE;MACnB,MAAMqC,MAAM,GAAG,IAAI,CAACC,iBAAiB,CAAClD,IAAI,CAAC;MAC3C,IAAI,CAACmD,qBAAqB,CAACnD,IAAI,EAAEiD,MAAM,CAACZ,gBAAgB,CAAC;MACzD,IAAI,CAACzB,WAAW,GAAG,IAAI,CAACJ,QAAQ,CAAC4C,MAAM,CAACH,MAAM,CAAC;MAC/C;MACA;MACA;MACA,IAAI,CAACrC,WAAW,CAACyC,aAAa,CAAC,CAAC,CAAClJ,SAAS,CAAC,CAAC;IAChD;IACA,OAAO,IAAI,CAACyG,WAAW;EAC3B;EACA;AACJ;AACA;AACA;EACIsC,iBAAiBA,CAAClD,IAAI,EAAE;IACpB,OAAO,IAAI9T,aAAa,CAAC;MACrBmW,gBAAgB,EAAE,IAAI,CAAC7B,QAAQ,CAC1B8C,QAAQ,CAAC,CAAC,CACVC,mBAAmB,CAAC,IAAI,CAAC9C,QAAQ,CAAC,CAClCkC,kBAAkB,CAAC,CAAC,CACpBa,iBAAiB,CAAC,CAAC,CACnBC,qBAAqB,CAAC,sCAAsC,CAAC;MAClEtL,aAAa,EAAE6H,IAAI,CAAC7H,aAAa,IAAI,kCAAkC;MACvEM,UAAU,EAAEuH,IAAI,CAACvG,iBAAiB;MAClCiH,cAAc,EAAE,IAAI,CAACc,eAAe,CAAC,CAAC;MACtC3F,SAAS,EAAE,IAAI,CAAC8E;IACpB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIwC,qBAAqBA,CAACnD,IAAI,EAAEsD,QAAQ,EAAE;IAClC,IAAItD,IAAI,CAACzH,kBAAkB,EAAE;MACzB+K,QAAQ,CAACI,eAAe,CAACvJ,SAAS,CAACwJ,MAAM,IAAI;QACzC,MAAMxG,IAAI,GAAGwG,MAAM,CAACC,cAAc,CAACC,QAAQ,KAAK,OAAO,GAAG,OAAO,GAAG,QAAQ;QAC5E,MAAMzG,IAAI,GAAGuG,MAAM,CAACC,cAAc,CAACE,QAAQ,KAAK,KAAK,GAAG,OAAO,GAAG,OAAO;QACzE;QACA;QACA;QACA,IAAI,IAAI,CAAC5K,OAAO,EAAE;UACd,IAAI,CAACA,OAAO,CAAC6K,GAAG,CAAC,MAAM/D,IAAI,CAACzH,kBAAkB,CAAC4E,IAAI,EAAEC,IAAI,CAAC,CAAC;QAC/D,CAAC,MACI;UACD4C,IAAI,CAACzH,kBAAkB,CAAC4E,IAAI,EAAEC,IAAI,CAAC;QACvC;MACJ,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIkF,YAAYA,CAACtC,IAAI,EAAEqC,gBAAgB,EAAE;IACjC,IAAI,CAAC2B,OAAO,EAAEC,eAAe,CAAC,GAAGjE,IAAI,CAAC/H,SAAS,KAAK,QAAQ,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC;IAClG,IAAI,CAAC6L,QAAQ,EAAEI,gBAAgB,CAAC,GAAGlE,IAAI,CAAC9H,SAAS,KAAK,OAAO,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC;IACrG,IAAI,CAACiM,OAAO,EAAEC,eAAe,CAAC,GAAG,CAACN,QAAQ,EAAEI,gBAAgB,CAAC;IAC7D,IAAI,CAACL,QAAQ,EAAEQ,gBAAgB,CAAC,GAAG,CAACL,OAAO,EAAEC,eAAe,CAAC;IAC7D,IAAIK,OAAO,GAAG,CAAC;IACf,IAAI,IAAI,CAACxS,eAAe,CAAC,CAAC,EAAE;MACxB;MACA;MACAuS,gBAAgB,GAAGL,OAAO,GAAGhE,IAAI,CAAC/H,SAAS,KAAK,QAAQ,GAAG,OAAO,GAAG,KAAK;MAC1EgM,eAAe,GAAGJ,QAAQ,GAAGG,OAAO,KAAK,KAAK,GAAG,OAAO,GAAG,KAAK;MAChE,IAAI,IAAI,CAAC5D,mBAAmB,EAAE;QAC1B,IAAI,IAAI,CAACmE,mBAAmB,IAAI,IAAI,EAAE;UAClC,MAAMC,SAAS,GAAG,IAAI,CAACpE,mBAAmB,CAAC9F,KAAK,CAAC6B,KAAK;UACtD,IAAI,CAACoI,mBAAmB,GAAGC,SAAS,GAAGA,SAAS,CAACvU,eAAe,CAAC,CAAC,CAACwU,SAAS,GAAG,CAAC;QACpF;QACAH,OAAO,GAAGR,QAAQ,KAAK,QAAQ,GAAG,IAAI,CAACS,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;MAC1F;IACJ,CAAC,MACI,IAAI,CAACvE,IAAI,CAAChI,cAAc,EAAE;MAC3BmM,OAAO,GAAGL,QAAQ,KAAK,KAAK,GAAG,QAAQ,GAAG,KAAK;MAC/CM,eAAe,GAAGF,gBAAgB,KAAK,KAAK,GAAG,QAAQ,GAAG,KAAK;IACnE;IACA7B,gBAAgB,CAACqC,aAAa,CAAC,CAC3B;MAAEV,OAAO;MAAEG,OAAO;MAAEN,QAAQ;MAAEC,QAAQ;MAAEQ;IAAQ,CAAC,EACjD;MAAEN,OAAO,EAAEC,eAAe;MAAEE,OAAO;MAAEN,QAAQ,EAAEQ,gBAAgB;MAAEP,QAAQ;MAAEQ;IAAQ,CAAC,EACpF;MACIN,OAAO;MACPG,OAAO,EAAEC,eAAe;MACxBP,QAAQ;MACRC,QAAQ,EAAEI,gBAAgB;MAC1BI,OAAO,EAAE,CAACA;IACd,CAAC,EACD;MACIN,OAAO,EAAEC,eAAe;MACxBE,OAAO,EAAEC,eAAe;MACxBP,QAAQ,EAAEQ,gBAAgB;MAC1BP,QAAQ,EAAEI,gBAAgB;MAC1BI,OAAO,EAAE,CAACA;IACd,CAAC,CACJ,CAAC;EACN;EACA;EACA7B,mBAAmBA,CAAA,EAAG;IAClB,MAAMkC,QAAQ,GAAG,IAAI,CAAC/D,WAAW,CAACgE,aAAa,CAAC,CAAC;IACjD,MAAMC,WAAW,GAAG,IAAI,CAACjE,WAAW,CAACiE,WAAW,CAAC,CAAC;IAClD,MAAMC,WAAW,GAAG,IAAI,CAAC1E,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACvS,MAAM,GAAGlD,EAAE,CAAC,CAAC;IACrF,MAAMoa,KAAK,GAAG,IAAI,CAAC3E,mBAAmB,GAChC,IAAI,CAACA,mBAAmB,CAAC5Q,QAAQ,CAAC,CAAC,CAAC6K,IAAI,CAACpP,MAAM,CAAC+Z,MAAM,IAAIA,MAAM,KAAK,IAAI,CAACzE,iBAAiB,CAAC,EAAEtV,MAAM,CAAC,MAAM,IAAI,CAAC4V,SAAS,CAAC,CAAC,GAC3HlW,EAAE,CAAC,CAAC;IACV,OAAOF,KAAK,CAACka,QAAQ,EAAEG,WAAW,EAAEC,KAAK,EAAEF,WAAW,CAAC;EAC3D;EACA;EACAI,gBAAgBA,CAACrU,KAAK,EAAE;IACpB,IAAI,CAAC7G,+BAA+B,CAAC6G,KAAK,CAAC,EAAE;MACzC;MACA;MACA,IAAI,CAACsQ,SAAS,GAAGtQ,KAAK,CAACsU,MAAM,KAAK,CAAC,GAAG,OAAO,GAAGlQ,SAAS;MACzD;MACA;MACA;MACA,IAAI,IAAI,CAAClD,eAAe,CAAC,CAAC,EAAE;QACxBlB,KAAK,CAACC,cAAc,CAAC,CAAC;MAC1B;IACJ;EACJ;EACA;EACAlD,cAAcA,CAACiD,KAAK,EAAE;IAClB,MAAM+K,OAAO,GAAG/K,KAAK,CAAC+K,OAAO;IAC7B;IACA,IAAIA,OAAO,KAAKrR,KAAK,IAAIqR,OAAO,KAAKpR,KAAK,EAAE;MACxC,IAAI,CAAC2W,SAAS,GAAG,UAAU;IAC/B;IACA,IAAI,IAAI,CAACpP,eAAe,CAAC,CAAC,KACpB6J,OAAO,KAAKzR,WAAW,IAAI,IAAI,CAAC2X,GAAG,KAAK,KAAK,IAC1ClG,OAAO,KAAKxR,UAAU,IAAI,IAAI,CAAC0X,GAAG,KAAK,KAAM,CAAC,EAAE;MACrD,IAAI,CAACX,SAAS,GAAG,UAAU;MAC3B,IAAI,CAACc,QAAQ,CAAC,CAAC;IACnB;EACJ;EACA;EACAmD,YAAYA,CAACvU,KAAK,EAAE;IAChB,IAAI,IAAI,CAACkB,eAAe,CAAC,CAAC,EAAE;MACxB;MACAlB,KAAK,CAACE,eAAe,CAAC,CAAC;MACvB,IAAI,CAACkR,QAAQ,CAAC,CAAC;IACnB,CAAC,MACI;MACD,IAAI,CAACF,UAAU,CAAC,CAAC;IACrB;EACJ;EACA;EACAJ,YAAYA,CAAA,EAAG;IACX;IACA,IAAI,CAAC,IAAI,CAAC5P,eAAe,CAAC,CAAC,IAAI,CAAC,IAAI,CAACsO,mBAAmB,EAAE;MACtD;IACJ;IACA,IAAI,CAACY,kBAAkB,GAAG,IAAI,CAACZ,mBAAmB,CAC7C5Q,QAAQ,CAAC;IACV;IACA;IACA;IAAA,CACC6K,IAAI,CAACpP,MAAM,CAAC+Z,MAAM,IAAIA,MAAM,KAAK,IAAI,CAACzE,iBAAiB,IAAI,CAACyE,MAAM,CAAC1V,QAAQ,CAAC,EAAEpE,KAAK,CAAC,CAAC,EAAEN,aAAa,CAAC,CAAC,CACtGuP,SAAS,CAAC,MAAM;MACjB,IAAI,CAAC+G,SAAS,GAAG,OAAO;MACxB;MACA;MACA;MACA,IAAI,IAAI,CAAClB,IAAI,YAAY5H,OAAO,IAAI,IAAI,CAAC4H,IAAI,CAACzC,YAAY,EAAE;QACxD;QACA;QACA,IAAI,CAACyC,IAAI,CAACzG,cAAc,CACnBc,IAAI,CAACtP,IAAI,CAAC,CAAC,CAAC,EAAEG,KAAK,CAAC,CAAC,EAAEN,aAAa,CAAC,EAAEI,SAAS,CAAC,IAAI,CAACoV,mBAAmB,CAAC5Q,QAAQ,CAAC,CAAC,CAAC,CAAC,CACtF2K,SAAS,CAAC,MAAM,IAAI,CAAC6H,QAAQ,CAAC,CAAC,CAAC;MACzC,CAAC,MACI;QACD,IAAI,CAACA,QAAQ,CAAC,CAAC;MACnB;IACJ,CAAC,CAAC;EACN;EACA;EACAO,UAAUA,CAACvC,IAAI,EAAE;IACb;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAC/J,OAAO,IAAI,IAAI,CAACA,OAAO,CAACyI,WAAW,KAAKsB,IAAI,CAACtB,WAAW,EAAE;MAChE,IAAI,CAACzI,OAAO,GAAG,IAAIzK,cAAc,CAACwU,IAAI,CAACtB,WAAW,EAAE,IAAI,CAAC7I,iBAAiB,CAAC;IAC/E;IACA,OAAO,IAAI,CAACI,OAAO;EACvB;EACA;IAAS,IAAI,CAAChE,IAAI,YAAAmT,uBAAAjT,CAAA;MAAA,YAAAA,CAAA,IAAwF2N,cAAc,EAjgCxBvX,EAAE,CAAA6J,iBAAA,CAigCwCpG,IAAI,CAACC,OAAO,GAjgCtD1D,EAAE,CAAA6J,iBAAA,CAigCiE7J,EAAE,CAAC8J,UAAU,GAjgChF9J,EAAE,CAAA6J,iBAAA,CAigC2F7J,EAAE,CAACyO,gBAAgB,GAjgChHzO,EAAE,CAAA6J,iBAAA,CAigC2H+M,wBAAwB,GAjgCrJ5W,EAAE,CAAA6J,iBAAA,CAigCgKvD,cAAc,MAjgChLtG,EAAE,CAAA6J,iBAAA,CAigC2MtD,WAAW,OAjgCxNvG,EAAE,CAAA6J,iBAAA,CAigC+PrG,EAAE,CAACsZ,cAAc,MAjgClR9c,EAAE,CAAA6J,iBAAA,CAigC6SxI,EAAE,CAAC0I,YAAY,GAjgC9T/J,EAAE,CAAA6J,iBAAA,CAigCyU7J,EAAE,CAACuV,MAAM;IAAA,CAA4C;EAAE;EACle;IAAS,IAAI,CAAC7G,IAAI,kBAlgC8E1O,EAAE,CAAA2O,iBAAA;MAAAzE,IAAA,EAkgCJqN,cAAc;MAAApN,SAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAyS,4BAAA5Y,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAlgCZnE,EAAE,CAAA6E,UAAA,mBAAAmY,wCAAAjY,MAAA;YAAA,OAkgCJX,GAAA,CAAAwY,YAAA,CAAA7X,MAAmB,CAAC;UAAA,CAAP,CAAC,uBAAAkY,4CAAAlY,MAAA;YAAA,OAAdX,GAAA,CAAAsY,gBAAA,CAAA3X,MAAuB,CAAC;UAAA,CAAX,CAAC,qBAAAmY,0CAAAnY,MAAA;YAAA,OAAdX,GAAA,CAAAgB,cAAA,CAAAL,MAAqB,CAAC;UAAA,CAAT,CAAC;QAAA;QAAA,IAAAZ,EAAA;UAlgCZnE,EAAE,CAAAkG,WAAA,kBAAA9B,GAAA,CAAAqT,IAAA,GAkgCG,MAAM,GAAG,IAAI,mBAAArT,GAAA,CAAAiV,QAAA,mBAAAjV,GAAA,CAAAiV,QAAA,GAAAjV,GAAA,CAAAqT,IAAA,CAAAzR,OAAA,GAAM,IAAI;QAAA;MAAA;MAAA2E,MAAA;QAAA6M,4BAAA,GAlgC5BxX,EAAE,CAAA4K,YAAA,CAAA2B,IAAA;QAAAkL,IAAA,GAAFzX,EAAE,CAAA4K,YAAA,CAAA2B,IAAA;QAAA0N,QAAA,GAAFja,EAAE,CAAA4K,YAAA,CAAA2B,IAAA;QAAAqM,YAAA,GAAF5Y,EAAE,CAAA4K,YAAA,CAAA2B,IAAA;MAAA;MAAA8J,OAAA;QAAAwC,UAAA;QAAAC,UAAA;QAAAC,UAAA;QAAAC,WAAA;MAAA;MAAAlO,QAAA;MAAAC,UAAA;IAAA,EAkgC4zB;EAAE;AACp6B;AACA;EAAA,QAAAkB,SAAA,oBAAAA,SAAA,KApgCoGjM,EAAE,CAAAkM,iBAAA,CAogCXqL,cAAc,EAAc,CAAC;IAC5GrN,IAAI,EAAEzJ,SAAS;IACf0L,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,6CAA6C;MACvDC,IAAI,EAAE;QACF,OAAO,EAAE,sBAAsB;QAC/B,sBAAsB,EAAE,sBAAsB;QAC9C,sBAAsB,EAAE,UAAU;QAClC,sBAAsB,EAAE,gCAAgC;QACxD,SAAS,EAAE,sBAAsB;QACjC,aAAa,EAAE,0BAA0B;QACzC,WAAW,EAAE;MACjB,CAAC;MACDvB,QAAQ,EAAE,gBAAgB;MAC1BC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEb,IAAI,EAAEzG,IAAI,CAACC;EAAQ,CAAC,EAAE;IAAEwG,IAAI,EAAElK,EAAE,CAAC8J;EAAW,CAAC,EAAE;IAAEI,IAAI,EAAElK,EAAE,CAACyO;EAAiB,CAAC,EAAE;IAAEvE,IAAI,EAAEuC,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/HxC,IAAI,EAAE5J,MAAM;MACZ6L,IAAI,EAAE,CAACyK,wBAAwB;IACnC,CAAC;EAAE,CAAC,EAAE;IAAE1M,IAAI,EAAEuC,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCxC,IAAI,EAAE5J,MAAM;MACZ6L,IAAI,EAAE,CAAC7F,cAAc;IACzB,CAAC,EAAE;MACC4D,IAAI,EAAE3J;IACV,CAAC;EAAE,CAAC,EAAE;IAAE2J,IAAI,EAAE3D,WAAW;IAAEmG,UAAU,EAAE,CAAC;MACpCxC,IAAI,EAAE3J;IACV,CAAC,EAAE;MACC2J,IAAI,EAAE/I;IACV,CAAC;EAAE,CAAC,EAAE;IAAE+I,IAAI,EAAE1G,EAAE,CAACsZ,cAAc;IAAEpQ,UAAU,EAAE,CAAC;MAC1CxC,IAAI,EAAE3J;IACV,CAAC;EAAE,CAAC,EAAE;IAAE2J,IAAI,EAAE7I,EAAE,CAAC0I;EAAa,CAAC,EAAE;IAAEG,IAAI,EAAElK,EAAE,CAACuV;EAAO,CAAC,CAAC,EAAkB;IAAEiC,4BAA4B,EAAE,CAAC;MACxGtN,IAAI,EAAE1J,KAAK;MACX2L,IAAI,EAAE,CAAC,sBAAsB;IACjC,CAAC,CAAC;IAAEsL,IAAI,EAAE,CAAC;MACPvN,IAAI,EAAE1J,KAAK;MACX2L,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAE8N,QAAQ,EAAE,CAAC;MACX/P,IAAI,EAAE1J,KAAK;MACX2L,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAEyM,YAAY,EAAE,CAAC;MACf1O,IAAI,EAAE1J,KAAK;MACX2L,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,CAAC;IAAE0M,UAAU,EAAE,CAAC;MACb3O,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAE8X,UAAU,EAAE,CAAC;MACb5O,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAE+X,UAAU,EAAE,CAAC;MACb7O,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEgY,WAAW,EAAE,CAAC;MACd9O,IAAI,EAAElJ;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMmc,aAAa,CAAC;EAChB;IAAS,IAAI,CAACzT,IAAI,YAAA0T,sBAAAxT,CAAA;MAAA,YAAAA,CAAA,IAAwFuT,aAAa;IAAA,CAAkD;EAAE;EAC3K;IAAS,IAAI,CAACE,IAAI,kBA1jC8Erd,EAAE,CAAAsd,gBAAA;MAAApT,IAAA,EA0jCSiT;IAAa,EAY9F;EAAE;EAC5B;IAAS,IAAI,CAACI,IAAI,kBAvkC8Evd,EAAE,CAAAwd,gBAAA;MAAAzO,SAAA,EAukCmC,CAACkI,yCAAyC,CAAC;MAAAzK,OAAA,GAAY3J,YAAY,EAChME,eAAe,EACfC,eAAe,EACfY,aAAa,EAAEE,mBAAmB,EAClCd,eAAe;IAAA,EAAI;EAAE;AACjC;AACA;EAAA,QAAAiJ,SAAA,oBAAAA,SAAA,KA7kCoGjM,EAAE,CAAAkM,iBAAA,CA6kCXiR,aAAa,EAAc,CAAC;IAC3GjT,IAAI,EAAE9I,QAAQ;IACd+K,IAAI,EAAE,CAAC;MACCK,OAAO,EAAE,CACL3J,YAAY,EACZE,eAAe,EACfC,eAAe,EACfY,aAAa,EACbiM,OAAO,EACPtJ,WAAW,EACX0G,cAAc,EACdsK,cAAc,CACjB;MACDkG,OAAO,EAAE,CACL3Z,mBAAmB,EACnB+L,OAAO,EACP7M,eAAe,EACfuD,WAAW,EACX0G,cAAc,EACdsK,cAAc,CACjB;MACDxI,SAAS,EAAE,CAACkI,yCAAyC;IACzD,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASjK,gBAAgB,EAAEqC,wBAAwB,EAAE/I,cAAc,EAAEsQ,wBAAwB,EAAEK,yCAAyC,EAAEK,sBAAsB,EAAEzH,OAAO,EAAE5C,cAAc,EAAE1G,WAAW,EAAE4W,aAAa,EAAE5F,cAAc,EAAEpI,WAAW,EAAEH,iBAAiB,EAAEC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}