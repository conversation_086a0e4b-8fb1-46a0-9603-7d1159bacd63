{"$schema": "http://json-schema.org/draft-07/schema", "$id": "SchematicsMaterialMDCMigration", "title": "Material MDC Migration Schema", "type": "object", "properties": {"directory": {"type": "string", "format": "path", "description": "Workspace-relative path to a directory which will be migrated.", "x-prompt": "Limit the migration to a specific directory? (Enter the relative path such as 'src/app/shared' or leave blank for all directories)"}, "components": {"type": "array", "items": {"type": "string", "enum": ["all", "autocomplete", "button", "card", "checkbox", "chips", "dialog", "form-field", "input", "list", "menu", "optgroup", "option", "paginator", "progress-bar", "progress-spinner", "radio", "select", "slide-toggle", "slider", "snack-bar", "table", "tabs", "tooltip"]}, "description": "The components to migrate.", "x-prompt": {"message": "What components do you want to migrate?", "type": "list", "multiselect": true, "items": [{"value": "button", "label": "<PERSON><PERSON>"}, {"value": "card", "label": "Card"}, {"value": "checkbox", "label": "Checkbox"}, {"value": "chips", "label": "Chips"}, {"value": "dialog", "label": "Dialog"}, {"value": "form-field", "label": "Form Field, Input, Select, Autocomplete"}, {"value": "list", "label": "List"}, {"value": "menu", "label": "<PERSON><PERSON>"}, {"value": "paginator", "label": "Paginator"}, {"value": "progress-bar", "label": "Progress Bar"}, {"value": "progress-spinner", "label": "Progress Spinner"}, {"value": "radio", "label": "Radio"}, {"value": "slide-toggle", "label": "Slide Toggle"}, {"value": "slider", "label": "Slide<PERSON>"}, {"value": "snack-bar", "label": "Snack Bar"}, {"value": "table", "label": "Table"}, {"value": "tabs", "label": "Tabs"}, {"value": "tooltip", "label": "<PERSON><PERSON><PERSON>"}]}}}}