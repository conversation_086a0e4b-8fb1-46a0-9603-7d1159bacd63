/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { InjectionToken } from '@angular/core';
export const LIVE_ANNOUNCER_ELEMENT_TOKEN = new InjectionToken('liveAnnouncerElement', {
    providedIn: 'root',
    factory: LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY,
});
/** @docs-private */
export function LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY() {
    return null;
}
/** Injection token that can be used to configure the default options for the LiveAnnouncer. */
export const LIVE_ANNOUNCER_DEFAULT_OPTIONS = new InjectionToken('LIVE_ANNOUNCER_DEFAULT_OPTIONS');
//# sourceMappingURL=data:application/json;base64,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