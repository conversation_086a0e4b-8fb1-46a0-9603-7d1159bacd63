{"version": 3, "file": "core.mjs", "sources": ["../../../../../../src/material/core/version.ts", "../../../../../../src/material/core/animation/animation.ts", "../../../../../../src/material/core/common-behaviors/common-module.ts", "../../../../../../src/material/core/common-behaviors/disabled.ts", "../../../../../../src/material/core/common-behaviors/color.ts", "../../../../../../src/material/core/common-behaviors/disable-ripple.ts", "../../../../../../src/material/core/common-behaviors/tabindex.ts", "../../../../../../src/material/core/common-behaviors/error-state.ts", "../../../../../../src/material/core/common-behaviors/initialized.ts", "../../../../../../src/material/core/datetime/date-adapter.ts", "../../../../../../src/material/core/datetime/date-formats.ts", "../../../../../../src/material/core/datetime/native-date-adapter.ts", "../../../../../../src/material/core/datetime/native-date-formats.ts", "../../../../../../src/material/core/datetime/index.ts", "../../../../../../src/material/core/error/error-options.ts", "../../../../../../src/material/core/line/line.ts", "../../../../../../src/material/core/ripple/ripple-ref.ts", "../../../../../../src/material/core/ripple/ripple-event-manager.ts", "../../../../../../src/material/core/ripple/ripple-renderer.ts", "../../../../../../src/material/core/ripple/ripple.ts", "../../../../../../src/material/core/ripple/index.ts", "../../../../../../src/material/core/selection/pseudo-checkbox/pseudo-checkbox.ts", "../../../../../../src/material/core/selection/pseudo-checkbox/pseudo-checkbox-module.ts", "../../../../../../src/material/core/option/option-parent.ts", "../../../../../../src/material/core/option/optgroup.ts", "../../../../../../src/material/core/option/optgroup.html", "../../../../../../src/material/core/option/option.ts", "../../../../../../src/material/core/option/option.html", "../../../../../../src/material/core/option/index.ts", "../../../../../../src/material/core/private/ripple-loader.ts", "../../../../../../src/material/core/internal-form-field/internal-form-field.ts", "../../../../../../src/material/core/core_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Version} from '@angular/core';\n\n/** Current version of Angular Material. */\nexport const VERSION = new Version('17.3.10');\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** @docs-private */\nexport class AnimationCurves {\n  static STANDARD_CURVE = 'cubic-bezier(0.4,0.0,0.2,1)';\n  static DECELERATION_CURVE = 'cubic-bezier(0.0,0.0,0.2,1)';\n  static ACCELERATION_CURVE = 'cubic-bezier(0.4,0.0,1,1)';\n  static SHARP_CURVE = 'cubic-bezier(0.4,0.0,0.6,1)';\n}\n\n/** @docs-private */\nexport class AnimationDurations {\n  static COMPLEX = '375ms';\n  static ENTERING = '225ms';\n  static EXITING = '195ms';\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {HighContrastModeDetector} from '@angular/cdk/a11y';\nimport {BidiModule} from '@angular/cdk/bidi';\nimport {inject, Inject, InjectionToken, NgModule, Optional} from '@angular/core';\nimport {VERSION as CDK_VERSION} from '@angular/cdk';\nimport {DOCUMENT} from '@angular/common';\nimport {Platform, _isTestEnvironment} from '@angular/cdk/platform';\nimport {VERSION} from '../version';\n\n/** @docs-private */\nexport function MATERIAL_SANITY_CHECKS_FACTORY(): SanityChecks {\n  return true;\n}\n\n/** Injection token that configures whether the Material sanity checks are enabled. */\nexport const MATERIAL_SANITY_CHECKS = new InjectionToken<SanityChecks>('mat-sanity-checks', {\n  providedIn: 'root',\n  factory: MATERIAL_SANITY_CHECKS_FACTORY,\n});\n\n/**\n * Possible sanity checks that can be enabled. If set to\n * true/false, all checks will be enabled/disabled.\n */\nexport type SanityChecks = boolean | GranularSanityChecks;\n\n/** Object that can be used to configure the sanity checks granularly. */\nexport interface GranularSanityChecks {\n  doctype: boolean;\n  theme: boolean;\n  version: boolean;\n}\n\n/**\n * Module that captures anything that should be loaded and/or run for *all* Angular Material\n * components. This includes Bidi, etc.\n *\n * This module should be imported to each top-level component module (e.g., MatTabsModule).\n */\n@NgModule({\n  imports: [BidiModule],\n  exports: [BidiModule],\n})\nexport class MatCommonModule {\n  /** Whether we've done the global sanity checks (e.g. a theme is loaded, there is a doctype). */\n  private _hasDoneGlobalChecks = false;\n\n  constructor(\n    highContrastModeDetector: HighContrastModeDetector,\n    @Optional() @Inject(MATERIAL_SANITY_CHECKS) private _sanityChecks: SanityChecks,\n    @Inject(DOCUMENT) private _document: Document,\n  ) {\n    // While A11yModule also does this, we repeat it here to avoid importing A11yModule\n    // in MatCommonModule.\n    highContrastModeDetector._applyBodyHighContrastModeCssClasses();\n\n    if (!this._hasDoneGlobalChecks) {\n      this._hasDoneGlobalChecks = true;\n\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        // Inject in here so the reference to `Platform` can be removed in production mode.\n        const platform = inject(Platform, {optional: true});\n\n        if (this._checkIsEnabled('doctype')) {\n          _checkDoctypeIsDefined(this._document);\n        }\n\n        if (this._checkIsEnabled('theme')) {\n          _checkThemeIsPresent(this._document, !!platform?.isBrowser);\n        }\n\n        if (this._checkIsEnabled('version')) {\n          _checkCdkVersionMatch();\n        }\n      }\n    }\n  }\n\n  /** Gets whether a specific sanity check is enabled. */\n  private _checkIsEnabled(name: keyof GranularSanityChecks): boolean {\n    if (_isTestEnvironment()) {\n      return false;\n    }\n\n    if (typeof this._sanityChecks === 'boolean') {\n      return this._sanityChecks;\n    }\n\n    return !!this._sanityChecks[name];\n  }\n}\n\n/** Checks that the page has a doctype. */\nfunction _checkDoctypeIsDefined(doc: Document): void {\n  if (!doc.doctype) {\n    console.warn(\n      'Current document does not have a doctype. This may cause ' +\n        'some Angular Material components not to behave as expected.',\n    );\n  }\n}\n\n/** Checks that a theme has been included. */\nfunction _checkThemeIsPresent(doc: Document, isBrowser: boolean): void {\n  // We need to assert that the `body` is defined, because these checks run very early\n  // and the `body` won't be defined if the consumer put their scripts in the `head`.\n  if (!doc.body || !isBrowser) {\n    return;\n  }\n\n  const testElement = doc.createElement('div');\n  testElement.classList.add('mat-theme-loaded-marker');\n  doc.body.appendChild(testElement);\n\n  const computedStyle = getComputedStyle(testElement);\n\n  // In some situations the computed style of the test element can be null. For example in\n  // Firefox, the computed style is null if an application is running inside of a hidden iframe.\n  // See: https://bugzilla.mozilla.org/show_bug.cgi?id=548397\n  if (computedStyle && computedStyle.display !== 'none') {\n    console.warn(\n      'Could not find Angular Material core theme. Most Material ' +\n        'components may not work as expected. For more info refer ' +\n        'to the theming guide: https://material.angular.io/guide/theming',\n    );\n  }\n\n  testElement.remove();\n}\n\n/** Checks whether the Material version matches the CDK version. */\nfunction _checkCdkVersionMatch(): void {\n  if (VERSION.full !== CDK_VERSION.full) {\n    console.warn(\n      'The Angular Material version (' +\n        VERSION.full +\n        ') does not match ' +\n        'the Angular CDK version (' +\n        CDK_VERSION.full +\n        ').\\n' +\n        'Please ensure the versions of these two packages exactly match.',\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {AbstractConstructor, Constructor} from './constructor';\n\n/**\n * @docs-private\n * @deprecated Will be removed together with `mixinDisabled`.\n * @breaking-change 19.0.0\n */\nexport interface CanDisable {\n  /** Whether the component is disabled. */\n  disabled: boolean;\n}\n\ntype CanDisableCtor = Constructor<CanDisable> & AbstractConstructor<CanDisable>;\n\n/**\n * Mixin to augment a directive with a `disabled` property.\n * @deprecated Use an input with a transform instead.\n * @breaking-change 19.0.0\n */\nexport function mixinDisabled<T extends AbstractConstructor<{}>>(base: T): CanDisableCtor & T;\nexport function mixinDisabled<T extends Constructor<{}>>(base: T): CanDisableCtor & T {\n  return class extends base {\n    private _disabled: boolean = false;\n\n    get disabled(): boolean {\n      return this._disabled;\n    }\n    set disabled(value: any) {\n      this._disabled = coerceBooleanProperty(value);\n    }\n\n    constructor(...args: any[]) {\n      super(...args);\n    }\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AbstractConstructor, Constructor} from './constructor';\nimport {ElementRef} from '@angular/core';\n\n/**\n * @docs-private\n * @deprecated Will be removed together with `mixinColor`.\n * @breaking-change 19.0.0\n */\nexport interface CanColor {\n  /** Theme color palette for the component. */\n  color: ThemePalette;\n\n  /** Default color to fall back to if no value is set. */\n  defaultColor: ThemePalette | undefined;\n}\n\ntype CanColorCtor = Constructor<CanColor> & AbstractConstructor<CanColor>;\n\n/** @docs-private */\ninterface HasElementRef {\n  _elementRef: ElementRef;\n}\n\n/** Possible color palette values. */\nexport type ThemePalette = 'primary' | 'accent' | 'warn' | undefined;\n\n/**\n * Mixin to augment a directive with a `color` property.\n * @deprecated Use a plain input and host bindings instead.\n * @breaking-change 19.0.0\n */\nexport function mixinColor<T extends AbstractConstructor<HasElementRef>>(\n  base: T,\n  defaultColor?: ThemePalette,\n): CanColorCtor & T;\nexport function mixinColor<T extends Constructor<HasElementRef>>(\n  base: T,\n  defaultColor?: ThemePalette,\n): CanColorCtor & T {\n  return class extends base {\n    private _color: ThemePalette;\n    defaultColor = defaultColor;\n\n    get color(): ThemePalette {\n      return this._color;\n    }\n    set color(value: ThemePalette) {\n      const colorPalette = value || this.defaultColor;\n\n      if (colorPalette !== this._color) {\n        if (this._color) {\n          this._elementRef.nativeElement.classList.remove(`mat-${this._color}`);\n        }\n        if (colorPalette) {\n          this._elementRef.nativeElement.classList.add(`mat-${colorPalette}`);\n        }\n\n        this._color = colorPalette;\n      }\n    }\n\n    constructor(...args: any[]) {\n      super(...args);\n\n      // Set the default color that can be specified from the mixin.\n      this.color = defaultColor;\n    }\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {AbstractConstructor, Constructor} from './constructor';\n\n/**\n * @docs-private\n * @deprecated Will be removed together with `mixinDisableRipple`.\n * @breaking-change 19.0.0\n */\nexport interface CanDisableRipple {\n  /** Whether ripples are disabled. */\n  disableRipple: boolean;\n}\n\ntype CanDisableRippleCtor = Constructor<CanDisableRipple> & AbstractConstructor<CanDisableRipple>;\n\n/**\n * Mixin to augment a directive with a `disableRipple` property.\n * @deprecated Use an input with a transform instead.\n * @breaking-change 19.0.0\n */\nexport function mixinDisableRipple<T extends AbstractConstructor<{}>>(\n  base: T,\n): CanDisableRippleCtor & T;\nexport function mixinDisableRipple<T extends Constructor<{}>>(base: T): CanDisableRippleCtor & T {\n  return class extends base {\n    private _disableRipple: boolean = false;\n\n    /** Whether the ripple effect is disabled or not. */\n    get disableRipple(): boolean {\n      return this._disableRipple;\n    }\n    set disableRipple(value: any) {\n      this._disableRipple = coerceBooleanProperty(value);\n    }\n\n    constructor(...args: any[]) {\n      super(...args);\n    }\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {coerceNumberProperty} from '@angular/cdk/coercion';\nimport {Constructor, AbstractConstructor} from './constructor';\nimport {CanDisable} from './disabled';\n\n/**\n * @docs-private\n * @deprecated Will be removed together with `mixinTabIndex`.\n * @breaking-change 19.0.0\n */\nexport interface HasTabIndex {\n  /** Tabindex of the component. */\n  tabIndex: number;\n\n  /** Tabindex to which to fall back to if no value is set. */\n  defaultTabIndex: number;\n}\n\ntype HasTabIndexCtor = Constructor<HasTabIndex> & AbstractConstructor<HasTabIndex>;\n\n/**\n * Mixin to augment a directive with a `tabIndex` property.\n * @deprecated Use an input with a transform instead.\n * @breaking-change 19.0.0\n */\nexport function mixinTabIndex<T extends AbstractConstructor<CanDisable>>(\n  base: T,\n  defaultTabIndex?: number,\n): HasTabIndexCtor & T;\nexport function mixinTabIndex<T extends Constructor<CanDisable>>(\n  base: T,\n  defaultTabIndex = 0,\n): HasTabIndexCtor & T {\n  return class extends base implements HasTabIndex {\n    private _tabIndex: number = defaultTabIndex;\n    defaultTabIndex = defaultTabIndex;\n\n    get tabIndex(): number {\n      return this.disabled ? -1 : this._tabIndex;\n    }\n    set tabIndex(value: number) {\n      // If the specified tabIndex value is null or undefined, fall back to the default value.\n      this._tabIndex = value != null ? coerceNumberProperty(value) : this.defaultTabIndex;\n    }\n\n    constructor(...args: any[]) {\n      super(...args);\n    }\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AbstractControl, FormGroupDirective, NgControl, NgForm} from '@angular/forms';\nimport {Subject} from 'rxjs';\nimport {ErrorStateMatcher as _ErrorStateMatcher} from '../error/error-options';\nimport {AbstractConstructor, Constructor} from './constructor';\n\n// Declare ErrorStateMatcher as an interface to have compatibility with Closure Compiler.\ninterface ErrorStateMatcher extends _ErrorStateMatcher {}\n\n/**\n * @docs-private\n * @deprecated Will be removed together with `mixinErrorState`.\n * @breaking-change 19.0.0\n */\nexport interface CanUpdateErrorState {\n  /** Updates the error state based on the provided error state matcher. */\n  updateErrorState(): void;\n  /** Whether the component is in an error state. */\n  errorState: boolean;\n  /** An object used to control the error state of the component. */\n  errorStateMatcher: ErrorStateMatcher;\n}\n\ntype CanUpdateErrorStateCtor = Constructor<CanUpdateErrorState> &\n  AbstractConstructor<CanUpdateErrorState>;\n\n/** @docs-private */\ninterface HasErrorState {\n  _parentFormGroup: FormGroupDirective | null;\n  _parentForm: NgForm | null;\n  _defaultErrorStateMatcher: ErrorStateMatcher;\n\n  // These properties are defined as per the `MatFormFieldControl` interface. Since\n  // this mixin is commonly used with custom form-field controls, we respect the\n  // properties (also with the public name they need according to `MatFormFieldControl`).\n  ngControl: NgControl | null;\n  stateChanges: Subject<void>;\n}\n\n/**\n * Class that tracks the error state of a component.\n * @docs-private\n */\nexport class _ErrorStateTracker {\n  /** Whether the tracker is currently in an error state. */\n  errorState = false;\n\n  /** User-defined matcher for the error state. */\n  matcher: ErrorStateMatcher;\n\n  constructor(\n    private _defaultMatcher: ErrorStateMatcher | null,\n    public ngControl: NgControl | null,\n    private _parentFormGroup: FormGroupDirective | null,\n    private _parentForm: NgForm | null,\n    private _stateChanges: Subject<void>,\n  ) {}\n\n  /** Updates the error state based on the provided error state matcher. */\n  updateErrorState() {\n    const oldState = this.errorState;\n    const parent = this._parentFormGroup || this._parentForm;\n    const matcher = this.matcher || this._defaultMatcher;\n    const control = this.ngControl ? (this.ngControl.control as AbstractControl) : null;\n    const newState = matcher?.isErrorState(control, parent) ?? false;\n\n    if (newState !== oldState) {\n      this.errorState = newState;\n      this._stateChanges.next();\n    }\n  }\n}\n\n/**\n * Mixin to augment a directive with updateErrorState method.\n * For component with `errorState` and need to update `errorState`.\n * @deprecated Implement the `updateErrorState` method directly.\n * @breaking-change 19.0.0\n */\nexport function mixinErrorState<T extends AbstractConstructor<HasErrorState>>(\n  base: T,\n): CanUpdateErrorStateCtor & T;\nexport function mixinErrorState<T extends Constructor<HasErrorState>>(\n  base: T,\n): CanUpdateErrorStateCtor & T {\n  return class extends base {\n    private _tracker: _ErrorStateTracker | undefined;\n\n    /** Whether the component is in an error state. */\n    get errorState() {\n      return this._getTracker().errorState;\n    }\n    set errorState(value: boolean) {\n      this._getTracker().errorState = value;\n    }\n\n    /** An object used to control the error state of the component. */\n    get errorStateMatcher() {\n      return this._getTracker().matcher;\n    }\n    set errorStateMatcher(value: ErrorStateMatcher) {\n      this._getTracker().matcher = value;\n    }\n\n    /** Updates the error state based on the provided error state matcher. */\n    updateErrorState() {\n      this._getTracker().updateErrorState();\n    }\n\n    private _getTracker() {\n      if (!this._tracker) {\n        this._tracker = new _ErrorStateTracker(\n          this._defaultErrorStateMatcher,\n          this.ngControl,\n          this._parentFormGroup,\n          this._parentForm,\n          this.stateChanges,\n        );\n      }\n\n      return this._tracker;\n    }\n\n    constructor(...args: any[]) {\n      super(...args);\n    }\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Observable, Subscriber} from 'rxjs';\nimport {Constructor} from './constructor';\n\n/**\n * Mixin that adds an initialized property to a directive which, when subscribed to, will emit a\n * value once markInitialized has been called, which should be done during the ngOnInit function.\n * If the subscription is made after it has already been marked as initialized, then it will trigger\n * an emit immediately.\n * @docs-private\n * @deprecated Will be removed together with `mixinInitializer`.\n * @breaking-change 19.0.0\n */\nexport interface HasInitialized {\n  /** Stream that emits once during the directive/component's ngOnInit. */\n  initialized: Observable<void>;\n\n  /**\n   * Sets the state as initialized and must be called during ngOnInit to notify subscribers that\n   * the directive has been initialized.\n   * @docs-private\n   */\n  _markInitialized: () => void;\n}\n\ntype HasInitializedCtor = Constructor<HasInitialized>;\n\n/**\n * Mixin to augment a directive with an initialized property that will emits when ngOnInit ends.\n * @deprecated Track the initialized state manually.\n * @breaking-change 19.0.0\n */\nexport function mixinInitialized<T extends Constructor<{}>>(base: T): HasInitializedCtor & T {\n  return class extends base {\n    /** Whether this directive has been marked as initialized. */\n    _isInitialized = false;\n\n    /**\n     * List of subscribers that subscribed before the directive was initialized. Should be notified\n     * during _markInitialized. Set to null after pending subscribers are notified, and should\n     * not expect to be populated after.\n     */\n    _pendingSubscribers: Subscriber<void>[] | null = [];\n\n    /**\n     * Observable stream that emits when the directive initializes. If already initialized, the\n     * subscriber is stored to be notified once _markInitialized is called.\n     */\n    initialized = new Observable<void>(subscriber => {\n      // If initialized, immediately notify the subscriber. Otherwise store the subscriber to notify\n      // when _markInitialized is called.\n      if (this._isInitialized) {\n        this._notifySubscriber(subscriber);\n      } else {\n        this._pendingSubscribers!.push(subscriber);\n      }\n    });\n\n    constructor(...args: any[]) {\n      super(...args);\n    }\n\n    /**\n     * Marks the state as initialized and notifies pending subscribers. Should be called at the end\n     * of ngOnInit.\n     * @docs-private\n     */\n    _markInitialized(): void {\n      if (this._isInitialized && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error(\n          'This directive has already been marked as initialized and ' +\n            'should not be called twice.',\n        );\n      }\n\n      this._isInitialized = true;\n\n      this._pendingSubscribers!.forEach(this._notifySubscriber);\n      this._pendingSubscribers = null;\n    }\n\n    /** Emits and completes the subscriber stream (should only emit once). */\n    _notifySubscriber(subscriber: Subscriber<void>): void {\n      subscriber.next();\n      subscriber.complete();\n    }\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {inject, InjectionToken, LOCALE_ID} from '@angular/core';\nimport {Observable, Subject} from 'rxjs';\n\n/** InjectionToken for datepicker that can be used to override default locale code. */\nexport const MAT_DATE_LOCALE = new InjectionToken<{}>('MAT_DATE_LOCALE', {\n  providedIn: 'root',\n  factory: MAT_DATE_LOCALE_FACTORY,\n});\n\n/** @docs-private */\nexport function MAT_DATE_LOCALE_FACTORY(): {} {\n  return inject(LOCALE_ID);\n}\n\n/** Adapts type `D` to be usable as a date by cdk-based components that work with dates. */\nexport abstract class DateAdapter<D, L = any> {\n  /** The locale to use for all dates. */\n  protected locale: L;\n  protected readonly _localeChanges = new Subject<void>();\n\n  /** A stream that emits when the locale changes. */\n  readonly localeChanges: Observable<void> = this._localeChanges;\n\n  /**\n   * Gets the year component of the given date.\n   * @param date The date to extract the year from.\n   * @returns The year component.\n   */\n  abstract getYear(date: D): number;\n\n  /**\n   * Gets the month component of the given date.\n   * @param date The date to extract the month from.\n   * @returns The month component (0-indexed, 0 = January).\n   */\n  abstract getMonth(date: D): number;\n\n  /**\n   * Gets the date of the month component of the given date.\n   * @param date The date to extract the date of the month from.\n   * @returns The month component (1-indexed, 1 = first of month).\n   */\n  abstract getDate(date: D): number;\n\n  /**\n   * Gets the day of the week component of the given date.\n   * @param date The date to extract the day of the week from.\n   * @returns The month component (0-indexed, 0 = Sunday).\n   */\n  abstract getDayOfWeek(date: D): number;\n\n  /**\n   * Gets a list of names for the months.\n   * @param style The naming style (e.g. long = 'January', short = 'Jan', narrow = 'J').\n   * @returns An ordered list of all month names, starting with January.\n   */\n  abstract getMonthNames(style: 'long' | 'short' | 'narrow'): string[];\n\n  /**\n   * Gets a list of names for the dates of the month.\n   * @returns An ordered list of all date of the month names, starting with '1'.\n   */\n  abstract getDateNames(): string[];\n\n  /**\n   * Gets a list of names for the days of the week.\n   * @param style The naming style (e.g. long = 'Sunday', short = 'Sun', narrow = 'S').\n   * @returns An ordered list of all weekday names, starting with Sunday.\n   */\n  abstract getDayOfWeekNames(style: 'long' | 'short' | 'narrow'): string[];\n\n  /**\n   * Gets the name for the year of the given date.\n   * @param date The date to get the year name for.\n   * @returns The name of the given year (e.g. '2017').\n   */\n  abstract getYearName(date: D): string;\n\n  /**\n   * Gets the first day of the week.\n   * @returns The first day of the week (0-indexed, 0 = Sunday).\n   */\n  abstract getFirstDayOfWeek(): number;\n\n  /**\n   * Gets the number of days in the month of the given date.\n   * @param date The date whose month should be checked.\n   * @returns The number of days in the month of the given date.\n   */\n  abstract getNumDaysInMonth(date: D): number;\n\n  /**\n   * Clones the given date.\n   * @param date The date to clone\n   * @returns A new date equal to the given date.\n   */\n  abstract clone(date: D): D;\n\n  /**\n   * Creates a date with the given year, month, and date. Does not allow over/under-flow of the\n   * month and date.\n   * @param year The full year of the date. (e.g. 89 means the year 89, not the year 1989).\n   * @param month The month of the date (0-indexed, 0 = January). Must be an integer 0 - 11.\n   * @param date The date of month of the date. Must be an integer 1 - length of the given month.\n   * @returns The new date, or null if invalid.\n   */\n  abstract createDate(year: number, month: number, date: number): D;\n\n  /**\n   * Gets today's date.\n   * @returns Today's date.\n   */\n  abstract today(): D;\n\n  /**\n   * Parses a date from a user-provided value.\n   * @param value The value to parse.\n   * @param parseFormat The expected format of the value being parsed\n   *     (type is implementation-dependent).\n   * @returns The parsed date.\n   */\n  abstract parse(value: any, parseFormat: any): D | null;\n\n  /**\n   * Formats a date as a string according to the given format.\n   * @param date The value to format.\n   * @param displayFormat The format to use to display the date as a string.\n   * @returns The formatted date string.\n   */\n  abstract format(date: D, displayFormat: any): string;\n\n  /**\n   * Adds the given number of years to the date. Years are counted as if flipping 12 pages on the\n   * calendar for each year and then finding the closest date in the new month. For example when\n   * adding 1 year to Feb 29, 2016, the resulting date will be Feb 28, 2017.\n   * @param date The date to add years to.\n   * @param years The number of years to add (may be negative).\n   * @returns A new date equal to the given one with the specified number of years added.\n   */\n  abstract addCalendarYears(date: D, years: number): D;\n\n  /**\n   * Adds the given number of months to the date. Months are counted as if flipping a page on the\n   * calendar for each month and then finding the closest date in the new month. For example when\n   * adding 1 month to Jan 31, 2017, the resulting date will be Feb 28, 2017.\n   * @param date The date to add months to.\n   * @param months The number of months to add (may be negative).\n   * @returns A new date equal to the given one with the specified number of months added.\n   */\n  abstract addCalendarMonths(date: D, months: number): D;\n\n  /**\n   * Adds the given number of days to the date. Days are counted as if moving one cell on the\n   * calendar for each day.\n   * @param date The date to add days to.\n   * @param days The number of days to add (may be negative).\n   * @returns A new date equal to the given one with the specified number of days added.\n   */\n  abstract addCalendarDays(date: D, days: number): D;\n\n  /**\n   * Gets the RFC 3339 compatible string (https://tools.ietf.org/html/rfc3339) for the given date.\n   * This method is used to generate date strings that are compatible with native HTML attributes\n   * such as the `min` or `max` attribute of an `<input>`.\n   * @param date The date to get the ISO date string for.\n   * @returns The ISO date string date string.\n   */\n  abstract toIso8601(date: D): string;\n\n  /**\n   * Checks whether the given object is considered a date instance by this DateAdapter.\n   * @param obj The object to check\n   * @returns Whether the object is a date instance.\n   */\n  abstract isDateInstance(obj: any): boolean;\n\n  /**\n   * Checks whether the given date is valid.\n   * @param date The date to check.\n   * @returns Whether the date is valid.\n   */\n  abstract isValid(date: D): boolean;\n\n  /**\n   * Gets date instance that is not valid.\n   * @returns An invalid date.\n   */\n  abstract invalid(): D;\n\n  /**\n   * Given a potential date object, returns that same date object if it is\n   * a valid date, or `null` if it's not a valid date.\n   * @param obj The object to check.\n   * @returns A date or `null`.\n   */\n  getValidDateOrNull(obj: unknown): D | null {\n    return this.isDateInstance(obj) && this.isValid(obj as D) ? (obj as D) : null;\n  }\n\n  /**\n   * Attempts to deserialize a value to a valid date object. This is different from parsing in that\n   * deserialize should only accept non-ambiguous, locale-independent formats (e.g. a ISO 8601\n   * string). The default implementation does not allow any deserialization, it simply checks that\n   * the given value is already a valid date object or null. The `<mat-datepicker>` will call this\n   * method on all of its `@Input()` properties that accept dates. It is therefore possible to\n   * support passing values from your backend directly to these properties by overriding this method\n   * to also deserialize the format used by your backend.\n   * @param value The value to be deserialized into a date object.\n   * @returns The deserialized date object, either a valid date, null if the value can be\n   *     deserialized into a null date (e.g. the empty string), or an invalid date.\n   */\n  deserialize(value: any): D | null {\n    if (value == null || (this.isDateInstance(value) && this.isValid(value))) {\n      return value;\n    }\n    return this.invalid();\n  }\n\n  /**\n   * Sets the locale used for all dates.\n   * @param locale The new locale.\n   */\n  setLocale(locale: L) {\n    this.locale = locale;\n    this._localeChanges.next();\n  }\n\n  /**\n   * Compares two dates.\n   * @param first The first date to compare.\n   * @param second The second date to compare.\n   * @returns 0 if the dates are equal, a number less than 0 if the first date is earlier,\n   *     a number greater than 0 if the first date is later.\n   */\n  compareDate(first: D, second: D): number {\n    return (\n      this.getYear(first) - this.getYear(second) ||\n      this.getMonth(first) - this.getMonth(second) ||\n      this.getDate(first) - this.getDate(second)\n    );\n  }\n\n  /**\n   * Checks if two dates are equal.\n   * @param first The first date to check.\n   * @param second The second date to check.\n   * @returns Whether the two dates are equal.\n   *     Null dates are considered equal to other null dates.\n   */\n  sameDate(first: D | null, second: D | null): boolean {\n    if (first && second) {\n      let firstValid = this.isValid(first);\n      let secondValid = this.isValid(second);\n      if (firstValid && secondValid) {\n        return !this.compareDate(first, second);\n      }\n      return firstValid == secondValid;\n    }\n    return first == second;\n  }\n\n  /**\n   * Clamp the given date between min and max dates.\n   * @param date The date to clamp.\n   * @param min The minimum value to allow. If null or omitted no min is enforced.\n   * @param max The maximum value to allow. If null or omitted no max is enforced.\n   * @returns `min` if `date` is less than `min`, `max` if date is greater than `max`,\n   *     otherwise `date`.\n   */\n  clampDate(date: D, min?: D | null, max?: D | null): D {\n    if (min && this.compareDate(date, min) < 0) {\n      return min;\n    }\n    if (max && this.compareDate(date, max) > 0) {\n      return max;\n    }\n    return date;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {InjectionToken} from '@angular/core';\n\nexport type MatDateFormats = {\n  parse: {\n    dateInput: any;\n  };\n  display: {\n    dateInput: any;\n    monthLabel?: any;\n    monthYearLabel: any;\n    dateA11yLabel: any;\n    monthYearA11yLabel: any;\n  };\n};\n\nexport const MAT_DATE_FORMATS = new InjectionToken<MatDateFormats>('mat-date-formats');\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {inject, Inject, Injectable, Optional} from '@angular/core';\nimport {DateAdapter, MAT_DATE_LOCALE} from './date-adapter';\n\n/**\n * Matches strings that have the form of a valid RFC 3339 string\n * (https://tools.ietf.org/html/rfc3339). Note that the string may not actually be a valid date\n * because the regex will match strings an with out of bounds month, date, etc.\n */\nconst ISO_8601_REGEX =\n  /^\\d{4}-\\d{2}-\\d{2}(?:T\\d{2}:\\d{2}:\\d{2}(?:\\.\\d+)?(?:Z|(?:(?:\\+|-)\\d{2}:\\d{2}))?)?$/;\n\n/** Creates an array and fills it with values. */\nfunction range<T>(length: number, valueFunction: (index: number) => T): T[] {\n  const valuesArray = Array(length);\n  for (let i = 0; i < length; i++) {\n    valuesArray[i] = valueFunction(i);\n  }\n  return valuesArray;\n}\n\n/** Adapts the native JS Date for use with cdk-based components that work with dates. */\n@Injectable()\nexport class NativeDateAdapter extends DateAdapter<Date> {\n  /**\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 14.0.0\n   */\n  useUtcForDisplay: boolean = false;\n\n  /** The injected locale. */\n  private readonly _matDateLocale = inject(MAT_DATE_LOCALE, {optional: true});\n\n  constructor(\n    /**\n     * @deprecated Now injected via inject(), param to be removed.\n     * @breaking-change 18.0.0\n     */\n    @Optional() @Inject(MAT_DATE_LOCALE) matDateLocale?: string,\n  ) {\n    super();\n    if (matDateLocale !== undefined) {\n      this._matDateLocale = matDateLocale;\n    }\n    super.setLocale(this._matDateLocale);\n  }\n\n  getYear(date: Date): number {\n    return date.getFullYear();\n  }\n\n  getMonth(date: Date): number {\n    return date.getMonth();\n  }\n\n  getDate(date: Date): number {\n    return date.getDate();\n  }\n\n  getDayOfWeek(date: Date): number {\n    return date.getDay();\n  }\n\n  getMonthNames(style: 'long' | 'short' | 'narrow'): string[] {\n    const dtf = new Intl.DateTimeFormat(this.locale, {month: style, timeZone: 'utc'});\n    return range(12, i => this._format(dtf, new Date(2017, i, 1)));\n  }\n\n  getDateNames(): string[] {\n    const dtf = new Intl.DateTimeFormat(this.locale, {day: 'numeric', timeZone: 'utc'});\n    return range(31, i => this._format(dtf, new Date(2017, 0, i + 1)));\n  }\n\n  getDayOfWeekNames(style: 'long' | 'short' | 'narrow'): string[] {\n    const dtf = new Intl.DateTimeFormat(this.locale, {weekday: style, timeZone: 'utc'});\n    return range(7, i => this._format(dtf, new Date(2017, 0, i + 1)));\n  }\n\n  getYearName(date: Date): string {\n    const dtf = new Intl.DateTimeFormat(this.locale, {year: 'numeric', timeZone: 'utc'});\n    return this._format(dtf, date);\n  }\n\n  getFirstDayOfWeek(): number {\n    // We can't tell using native JS Date what the first day of the week is, we default to Sunday.\n    return 0;\n  }\n\n  getNumDaysInMonth(date: Date): number {\n    return this.getDate(\n      this._createDateWithOverflow(this.getYear(date), this.getMonth(date) + 1, 0),\n    );\n  }\n\n  clone(date: Date): Date {\n    return new Date(date.getTime());\n  }\n\n  createDate(year: number, month: number, date: number): Date {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      // Check for invalid month and date (except upper bound on date which we have to check after\n      // creating the Date).\n      if (month < 0 || month > 11) {\n        throw Error(`Invalid month index \"${month}\". Month index has to be between 0 and 11.`);\n      }\n\n      if (date < 1) {\n        throw Error(`Invalid date \"${date}\". Date has to be greater than 0.`);\n      }\n    }\n\n    let result = this._createDateWithOverflow(year, month, date);\n    // Check that the date wasn't above the upper bound for the month, causing the month to overflow\n    if (result.getMonth() != month && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`Invalid date \"${date}\" for month with index \"${month}\".`);\n    }\n\n    return result;\n  }\n\n  today(): Date {\n    return new Date();\n  }\n\n  parse(value: any, parseFormat?: any): Date | null {\n    // We have no way using the native JS Date to set the parse format or locale, so we ignore these\n    // parameters.\n    if (typeof value == 'number') {\n      return new Date(value);\n    }\n    return value ? new Date(Date.parse(value)) : null;\n  }\n\n  format(date: Date, displayFormat: Object): string {\n    if (!this.isValid(date)) {\n      throw Error('NativeDateAdapter: Cannot format invalid date.');\n    }\n\n    const dtf = new Intl.DateTimeFormat(this.locale, {...displayFormat, timeZone: 'utc'});\n    return this._format(dtf, date);\n  }\n\n  addCalendarYears(date: Date, years: number): Date {\n    return this.addCalendarMonths(date, years * 12);\n  }\n\n  addCalendarMonths(date: Date, months: number): Date {\n    let newDate = this._createDateWithOverflow(\n      this.getYear(date),\n      this.getMonth(date) + months,\n      this.getDate(date),\n    );\n\n    // It's possible to wind up in the wrong month if the original month has more days than the new\n    // month. In this case we want to go to the last day of the desired month.\n    // Note: the additional + 12 % 12 ensures we end up with a positive number, since JS % doesn't\n    // guarantee this.\n    if (this.getMonth(newDate) != (((this.getMonth(date) + months) % 12) + 12) % 12) {\n      newDate = this._createDateWithOverflow(this.getYear(newDate), this.getMonth(newDate), 0);\n    }\n\n    return newDate;\n  }\n\n  addCalendarDays(date: Date, days: number): Date {\n    return this._createDateWithOverflow(\n      this.getYear(date),\n      this.getMonth(date),\n      this.getDate(date) + days,\n    );\n  }\n\n  toIso8601(date: Date): string {\n    return [\n      date.getUTCFullYear(),\n      this._2digit(date.getUTCMonth() + 1),\n      this._2digit(date.getUTCDate()),\n    ].join('-');\n  }\n\n  /**\n   * Returns the given value if given a valid Date or null. Deserializes valid ISO 8601 strings\n   * (https://www.ietf.org/rfc/rfc3339.txt) into valid Dates and empty string into null. Returns an\n   * invalid date for all other values.\n   */\n  override deserialize(value: any): Date | null {\n    if (typeof value === 'string') {\n      if (!value) {\n        return null;\n      }\n      // The `Date` constructor accepts formats other than ISO 8601, so we need to make sure the\n      // string is the right format first.\n      if (ISO_8601_REGEX.test(value)) {\n        let date = new Date(value);\n        if (this.isValid(date)) {\n          return date;\n        }\n      }\n    }\n    return super.deserialize(value);\n  }\n\n  isDateInstance(obj: any) {\n    return obj instanceof Date;\n  }\n\n  isValid(date: Date) {\n    return !isNaN(date.getTime());\n  }\n\n  invalid(): Date {\n    return new Date(NaN);\n  }\n\n  /** Creates a date but allows the month and date to overflow. */\n  private _createDateWithOverflow(year: number, month: number, date: number) {\n    // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n    // To work around this we use `setFullYear` and `setHours` instead.\n    const d = new Date();\n    d.setFullYear(year, month, date);\n    d.setHours(0, 0, 0, 0);\n    return d;\n  }\n\n  /**\n   * Pads a number to make it two digits.\n   * @param n The number to pad.\n   * @returns The padded number.\n   */\n  private _2digit(n: number) {\n    return ('00' + n).slice(-2);\n  }\n\n  /**\n   * When converting Date object to string, javascript built-in functions may return wrong\n   * results because it applies its internal DST rules. The DST rules around the world change\n   * very frequently, and the current valid rule is not always valid in previous years though.\n   * We work around this problem building a new Date object which has its internal UTC\n   * representation with the local date and time.\n   * @param dtf Intl.DateTimeFormat object, containing the desired string format. It must have\n   *    timeZone set to 'utc' to work fine.\n   * @param date Date from which we want to get the string representation according to dtf\n   * @returns A Date object with its UTC representation based on the passed in date info\n   */\n  private _format(dtf: Intl.DateTimeFormat, date: Date) {\n    // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n    // To work around this we use `setUTCFullYear` and `setUTCHours` instead.\n    const d = new Date();\n    d.setUTCFullYear(date.getFullYear(), date.getMonth(), date.getDate());\n    d.setUTCHours(date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n    return dtf.format(d);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {MatDateFormats} from './date-formats';\n\nexport const MAT_NATIVE_DATE_FORMATS: MatDateFormats = {\n  parse: {\n    dateInput: null,\n  },\n  display: {\n    dateInput: {year: 'numeric', month: 'numeric', day: 'numeric'},\n    monthYearLabel: {year: 'numeric', month: 'short'},\n    dateA11yLabel: {year: 'numeric', month: 'long', day: 'numeric'},\n    monthYearA11yLabel: {year: 'numeric', month: 'long'},\n  },\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule, Provider} from '@angular/core';\nimport {DateAdapter} from './date-adapter';\nimport {MAT_DATE_FORMATS, MatDateFormats} from './date-formats';\nimport {NativeDateAdapter} from './native-date-adapter';\nimport {MAT_NATIVE_DATE_FORMATS} from './native-date-formats';\n\nexport * from './date-adapter';\nexport * from './date-formats';\nexport * from './native-date-adapter';\nexport * from './native-date-formats';\n\n@NgModule({\n  providers: [{provide: DateAdapter, useClass: NativeDateAdapter}],\n})\nexport class NativeDateModule {}\n\n@NgModule({\n  providers: [provideNativeDateAdapter()],\n})\nexport class MatNativeDateModule {}\n\nexport function provideNativeDateAdapter(\n  formats: MatDateFormats = MAT_NATIVE_DATE_FORMATS,\n): Provider[] {\n  return [\n    {provide: DateAdapter, useClass: NativeDateAdapter},\n    {provide: MAT_DATE_FORMATS, useValue: formats},\n  ];\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injectable} from '@angular/core';\nimport {FormGroupDirective, NgForm, AbstractControl} from '@angular/forms';\n\n/** Error state matcher that matches when a control is invalid and dirty. */\n@Injectable()\nexport class ShowOnDirtyErrorStateMatcher implements ErrorStateMatcher {\n  isErrorState(control: AbstractControl | null, form: FormGroupDirective | NgForm | null): boolean {\n    return !!(control && control.invalid && (control.dirty || (form && form.submitted)));\n  }\n}\n\n/** Provider that defines how form controls behave with regards to displaying error messages. */\n@Injectable({providedIn: 'root'})\nexport class ErrorStateMatcher {\n  isErrorState(control: AbstractControl | null, form: FormGroupDirective | NgForm | null): boolean {\n    return !!(control && control.invalid && (control.touched || (form && form.submitted)));\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule, Directive, ElementRef, QueryList} from '@angular/core';\nimport {startWith} from 'rxjs/operators';\nimport {MatCommonModule} from '../common-behaviors/common-module';\n\n/**\n * Shared directive to count lines inside a text area, such as a list item.\n * Line elements can be extracted with a @ContentChildren(MatLine) query, then\n * counted by checking the query list's length.\n */\n@Directive({\n  selector: '[mat-line], [matLine]',\n  host: {'class': 'mat-line'},\n  standalone: true,\n})\nexport class MatLine {}\n\n/**\n * Helper that takes a query list of lines and sets the correct class on the host.\n * @docs-private\n */\nexport function setLines(\n  lines: QueryList<unknown>,\n  element: ElementRef<HTMLElement>,\n  prefix = 'mat',\n) {\n  // Note: doesn't need to unsubscribe, because `changes`\n  // gets completed by <PERSON><PERSON> when the view is destroyed.\n  lines.changes.pipe(startWith(lines)).subscribe(({length}) => {\n    setClass(element, `${prefix}-2-line`, false);\n    setClass(element, `${prefix}-3-line`, false);\n    setClass(element, `${prefix}-multi-line`, false);\n\n    if (length === 2 || length === 3) {\n      setClass(element, `${prefix}-${length}-line`, true);\n    } else if (length > 3) {\n      setClass(element, `${prefix}-multi-line`, true);\n    }\n  });\n}\n\n/** Adds or removes a class from an element. */\nfunction setClass(element: ElementRef<HTMLElement>, className: string, isAdd: boolean): void {\n  element.nativeElement.classList.toggle(className, isAdd);\n}\n\n@NgModule({\n  imports: [MatCommonModule, MatLine],\n  exports: [MatLine, MatCommonModule],\n})\nexport class MatLineModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Possible states for a ripple element. */\nexport enum RippleState {\n  FADING_IN,\n  VISIBLE,\n  FADING_OUT,\n  HIDDEN,\n}\n\nexport type RippleConfig = {\n  color?: string;\n  centered?: boolean;\n  radius?: number;\n  persistent?: boolean;\n  animation?: RippleAnimationConfig;\n  terminateOnPointerUp?: boolean;\n};\n\n/**\n * Interface that describes the configuration for the animation of a ripple.\n * There are two animation phases with different durations for the ripples.\n */\nexport interface RippleAnimationConfig {\n  /** Duration in milliseconds for the enter animation (expansion from point of contact). */\n  enterDuration?: number;\n  /** Duration in milliseconds for the exit animation (fade-out). */\n  exitDuration?: number;\n}\n\n/**\n * Reference to a previously launched ripple element.\n */\nexport class RippleRef {\n  /** Current state of the ripple. */\n  state: RippleState = RippleState.HIDDEN;\n\n  constructor(\n    private _renderer: {fadeOutRipple(ref: RippleRef): void},\n    /** Reference to the ripple HTML element. */\n    public element: HTMLElement,\n    /** Ripple configuration used for the ripple. */\n    public config: RippleConfig,\n    /* Whether animations are forcibly disabled for ripples through CSS. */\n    public _animationForciblyDisabledThroughCss = false,\n  ) {}\n\n  /** Fades out the ripple element. */\n  fadeOut() {\n    this._renderer.fadeOutRipple(this);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {normalizePassiveListenerOptions, _getEventTarget} from '@angular/cdk/platform';\nimport {NgZone} from '@angular/core';\n\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true,\n});\n\n/** Manages events through delegation so that as few event handlers as possible are bound. */\nexport class RippleEventManager {\n  private _events = new Map<string, Map<HTMLElement, Set<EventListenerObject>>>();\n\n  /** Adds an event handler. */\n  addHandler(ngZone: NgZone, name: string, element: HTMLElement, handler: EventListenerObject) {\n    const handlersForEvent = this._events.get(name);\n\n    if (handlersForEvent) {\n      const handlersForElement = handlersForEvent.get(element);\n\n      if (handlersForElement) {\n        handlersForElement.add(handler);\n      } else {\n        handlersForEvent.set(element, new Set([handler]));\n      }\n    } else {\n      this._events.set(name, new Map([[element, new Set([handler])]]));\n\n      ngZone.runOutsideAngular(() => {\n        document.addEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions);\n      });\n    }\n  }\n\n  /** Removes an event handler. */\n  removeHandler(name: string, element: HTMLElement, handler: EventListenerObject) {\n    const handlersForEvent = this._events.get(name);\n\n    if (!handlersForEvent) {\n      return;\n    }\n\n    const handlersForElement = handlersForEvent.get(element);\n\n    if (!handlersForElement) {\n      return;\n    }\n\n    handlersForElement.delete(handler);\n\n    if (handlersForElement.size === 0) {\n      handlersForEvent.delete(element);\n    }\n\n    if (handlersForEvent.size === 0) {\n      this._events.delete(name);\n      document.removeEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions);\n    }\n  }\n\n  /** Event handler that is bound and which dispatches the events to the different targets. */\n  private _delegateEventHandler = (event: Event) => {\n    const target = _getEventTarget(event);\n\n    if (target) {\n      this._events.get(event.type)?.forEach((handlers, element) => {\n        if (element === target || element.contains(target as Node)) {\n          handlers.forEach(handler => handler.handleEvent(event));\n        }\n      });\n    }\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {ElementRef, NgZone} from '@angular/core';\nimport {Platform, normalizePassiveListenerOptions, _getEventTarget} from '@angular/cdk/platform';\nimport {isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader} from '@angular/cdk/a11y';\nimport {coerceElement} from '@angular/cdk/coercion';\nimport {RippleRef, RippleState, RippleConfig} from './ripple-ref';\nimport {RippleEventManager} from './ripple-event-manager';\n\n/**\n * Interface that describes the target for launching ripples.\n * It defines the ripple configuration and disabled state for interaction ripples.\n * @docs-private\n */\nexport interface RippleTarget {\n  /** Configuration for ripples that are launched on pointer down. */\n  rippleConfig: RippleConfig;\n  /** Whether ripples on pointer down should be disabled. */\n  rippleDisabled: boolean;\n}\n\n/** Interfaces the defines ripple element transition event listeners. */\ninterface RippleEventListeners {\n  onTransitionEnd: EventListener;\n  onTransitionCancel: EventListener;\n}\n\n/**\n * Default ripple animation configuration for ripples without an explicit\n * animation config specified.\n */\nexport const defaultRippleAnimationConfig = {\n  enterDuration: 225,\n  exitDuration: 150,\n};\n\n/**\n * Timeout for ignoring mouse events. Mouse events will be temporary ignored after touch\n * events to avoid synthetic mouse events.\n */\nconst ignoreMouseEventsTimeout = 800;\n\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true,\n});\n\n/** Events that signal that the pointer is down. */\nconst pointerDownEvents = ['mousedown', 'touchstart'];\n\n/** Events that signal that the pointer is up. */\nconst pointerUpEvents = ['mouseup', 'mouseleave', 'touchend', 'touchcancel'];\n\n/**\n * Helper service that performs DOM manipulations. Not intended to be used outside this module.\n * The constructor takes a reference to the ripple directive's host element and a map of DOM\n * event handlers to be installed on the element that triggers ripple animations.\n * This will eventually become a custom renderer once Angular support exists.\n * @docs-private\n */\nexport class RippleRenderer implements EventListenerObject {\n  /** Element where the ripples are being added to. */\n  private _containerElement: HTMLElement;\n\n  /** Element which triggers the ripple elements on mouse events. */\n  private _triggerElement: HTMLElement | null;\n\n  /** Whether the pointer is currently down or not. */\n  private _isPointerDown = false;\n\n  /**\n   * Map of currently active ripple references.\n   * The ripple reference is mapped to its element event listeners.\n   * The reason why `| null` is used is that event listeners are added only\n   * when the condition is truthy (see the `_startFadeOutTransition` method).\n   */\n  private _activeRipples = new Map<RippleRef, RippleEventListeners | null>();\n\n  /** Latest non-persistent ripple that was triggered. */\n  private _mostRecentTransientRipple: RippleRef | null;\n\n  /** Time in milliseconds when the last touchstart event happened. */\n  private _lastTouchStartEvent: number;\n\n  /** Whether pointer-up event listeners have been registered. */\n  private _pointerUpEventsRegistered = false;\n\n  /**\n   * Cached dimensions of the ripple container. Set when the first\n   * ripple is shown and cleared once no more ripples are visible.\n   */\n  private _containerRect: DOMRect | null;\n\n  private static _eventManager = new RippleEventManager();\n\n  constructor(\n    private _target: RippleTarget,\n    private _ngZone: NgZone,\n    elementOrElementRef: HTMLElement | ElementRef<HTMLElement>,\n    private _platform: Platform,\n  ) {\n    // Only do anything if we're on the browser.\n    if (_platform.isBrowser) {\n      this._containerElement = coerceElement(elementOrElementRef);\n    }\n  }\n\n  /**\n   * Fades in a ripple at the given coordinates.\n   * @param x Coordinate within the element, along the X axis at which to start the ripple.\n   * @param y Coordinate within the element, along the Y axis at which to start the ripple.\n   * @param config Extra ripple options.\n   */\n  fadeInRipple(x: number, y: number, config: RippleConfig = {}): RippleRef {\n    const containerRect = (this._containerRect =\n      this._containerRect || this._containerElement.getBoundingClientRect());\n    const animationConfig = {...defaultRippleAnimationConfig, ...config.animation};\n\n    if (config.centered) {\n      x = containerRect.left + containerRect.width / 2;\n      y = containerRect.top + containerRect.height / 2;\n    }\n\n    const radius = config.radius || distanceToFurthestCorner(x, y, containerRect);\n    const offsetX = x - containerRect.left;\n    const offsetY = y - containerRect.top;\n    const enterDuration = animationConfig.enterDuration;\n\n    const ripple = document.createElement('div');\n    ripple.classList.add('mat-ripple-element');\n\n    ripple.style.left = `${offsetX - radius}px`;\n    ripple.style.top = `${offsetY - radius}px`;\n    ripple.style.height = `${radius * 2}px`;\n    ripple.style.width = `${radius * 2}px`;\n\n    // If a custom color has been specified, set it as inline style. If no color is\n    // set, the default color will be applied through the ripple theme styles.\n    if (config.color != null) {\n      ripple.style.backgroundColor = config.color;\n    }\n\n    ripple.style.transitionDuration = `${enterDuration}ms`;\n\n    this._containerElement.appendChild(ripple);\n\n    // By default the browser does not recalculate the styles of dynamically created\n    // ripple elements. This is critical to ensure that the `scale` animates properly.\n    // We enforce a style recalculation by calling `getComputedStyle` and *accessing* a property.\n    // See: https://gist.github.com/paulirish/5d52fb081b3570c81e3a\n    const computedStyles = window.getComputedStyle(ripple);\n    const userTransitionProperty = computedStyles.transitionProperty;\n    const userTransitionDuration = computedStyles.transitionDuration;\n\n    // Note: We detect whether animation is forcibly disabled through CSS (e.g. through\n    // `transition: none` or `display: none`). This is technically unexpected since animations are\n    // controlled through the animation config, but this exists for backwards compatibility. This\n    // logic does not need to be super accurate since it covers some edge cases which can be easily\n    // avoided by users.\n    const animationForciblyDisabledThroughCss =\n      userTransitionProperty === 'none' ||\n      // Note: The canonical unit for serialized CSS `<time>` properties is seconds. Additionally\n      // some browsers expand the duration for every property (in our case `opacity` and `transform`).\n      userTransitionDuration === '0s' ||\n      userTransitionDuration === '0s, 0s' ||\n      // If the container is 0x0, it's likely `display: none`.\n      (containerRect.width === 0 && containerRect.height === 0);\n\n    // Exposed reference to the ripple that will be returned.\n    const rippleRef = new RippleRef(this, ripple, config, animationForciblyDisabledThroughCss);\n\n    // Start the enter animation by setting the transform/scale to 100%. The animation will\n    // execute as part of this statement because we forced a style recalculation before.\n    // Note: We use a 3d transform here in order to avoid an issue in Safari where\n    // the ripples aren't clipped when inside the shadow DOM (see #24028).\n    ripple.style.transform = 'scale3d(1, 1, 1)';\n\n    rippleRef.state = RippleState.FADING_IN;\n\n    if (!config.persistent) {\n      this._mostRecentTransientRipple = rippleRef;\n    }\n\n    let eventListeners: RippleEventListeners | null = null;\n\n    // Do not register the `transition` event listener if fade-in and fade-out duration\n    // are set to zero. The events won't fire anyway and we can save resources here.\n    if (!animationForciblyDisabledThroughCss && (enterDuration || animationConfig.exitDuration)) {\n      this._ngZone.runOutsideAngular(() => {\n        const onTransitionEnd = () => this._finishRippleTransition(rippleRef);\n        const onTransitionCancel = () => this._destroyRipple(rippleRef);\n        ripple.addEventListener('transitionend', onTransitionEnd);\n        // If the transition is cancelled (e.g. due to DOM removal), we destroy the ripple\n        // directly as otherwise we would keep it part of the ripple container forever.\n        // https://www.w3.org/TR/css-transitions-1/#:~:text=no%20longer%20in%20the%20document.\n        ripple.addEventListener('transitioncancel', onTransitionCancel);\n        eventListeners = {onTransitionEnd, onTransitionCancel};\n      });\n    }\n\n    // Add the ripple reference to the list of all active ripples.\n    this._activeRipples.set(rippleRef, eventListeners);\n\n    // In case there is no fade-in transition duration, we need to manually call the transition\n    // end listener because `transitionend` doesn't fire if there is no transition.\n    if (animationForciblyDisabledThroughCss || !enterDuration) {\n      this._finishRippleTransition(rippleRef);\n    }\n\n    return rippleRef;\n  }\n\n  /** Fades out a ripple reference. */\n  fadeOutRipple(rippleRef: RippleRef) {\n    // For ripples already fading out or hidden, this should be a noop.\n    if (rippleRef.state === RippleState.FADING_OUT || rippleRef.state === RippleState.HIDDEN) {\n      return;\n    }\n\n    const rippleEl = rippleRef.element;\n    const animationConfig = {...defaultRippleAnimationConfig, ...rippleRef.config.animation};\n\n    // This starts the fade-out transition and will fire the transition end listener that\n    // removes the ripple element from the DOM.\n    rippleEl.style.transitionDuration = `${animationConfig.exitDuration}ms`;\n    rippleEl.style.opacity = '0';\n    rippleRef.state = RippleState.FADING_OUT;\n\n    // In case there is no fade-out transition duration, we need to manually call the\n    // transition end listener because `transitionend` doesn't fire if there is no transition.\n    if (rippleRef._animationForciblyDisabledThroughCss || !animationConfig.exitDuration) {\n      this._finishRippleTransition(rippleRef);\n    }\n  }\n\n  /** Fades out all currently active ripples. */\n  fadeOutAll() {\n    this._getActiveRipples().forEach(ripple => ripple.fadeOut());\n  }\n\n  /** Fades out all currently active non-persistent ripples. */\n  fadeOutAllNonPersistent() {\n    this._getActiveRipples().forEach(ripple => {\n      if (!ripple.config.persistent) {\n        ripple.fadeOut();\n      }\n    });\n  }\n\n  /** Sets up the trigger event listeners */\n  setupTriggerEvents(elementOrElementRef: HTMLElement | ElementRef<HTMLElement>) {\n    const element = coerceElement(elementOrElementRef);\n\n    if (!this._platform.isBrowser || !element || element === this._triggerElement) {\n      return;\n    }\n\n    // Remove all previously registered event listeners from the trigger element.\n    this._removeTriggerEvents();\n    this._triggerElement = element;\n\n    // Use event delegation for the trigger events since they're\n    // set up during creation and are performance-sensitive.\n    pointerDownEvents.forEach(type => {\n      RippleRenderer._eventManager.addHandler(this._ngZone, type, element, this);\n    });\n  }\n\n  /**\n   * Handles all registered events.\n   * @docs-private\n   */\n  handleEvent(event: Event) {\n    if (event.type === 'mousedown') {\n      this._onMousedown(event as MouseEvent);\n    } else if (event.type === 'touchstart') {\n      this._onTouchStart(event as TouchEvent);\n    } else {\n      this._onPointerUp();\n    }\n\n    // If pointer-up events haven't been registered yet, do so now.\n    // We do this on-demand in order to reduce the total number of event listeners\n    // registered by the ripples, which speeds up the rendering time for large UIs.\n    if (!this._pointerUpEventsRegistered) {\n      // The events for hiding the ripple are bound directly on the trigger, because:\n      // 1. Some of them occur frequently (e.g. `mouseleave`) and any advantage we get from\n      // delegation will be diminished by having to look through all the data structures often.\n      // 2. They aren't as performance-sensitive, because they're bound only after the user\n      // has interacted with an element.\n      this._ngZone.runOutsideAngular(() => {\n        pointerUpEvents.forEach(type => {\n          this._triggerElement!.addEventListener(type, this, passiveCapturingEventOptions);\n        });\n      });\n\n      this._pointerUpEventsRegistered = true;\n    }\n  }\n\n  /** Method that will be called if the fade-in or fade-in transition completed. */\n  private _finishRippleTransition(rippleRef: RippleRef) {\n    if (rippleRef.state === RippleState.FADING_IN) {\n      this._startFadeOutTransition(rippleRef);\n    } else if (rippleRef.state === RippleState.FADING_OUT) {\n      this._destroyRipple(rippleRef);\n    }\n  }\n\n  /**\n   * Starts the fade-out transition of the given ripple if it's not persistent and the pointer\n   * is not held down anymore.\n   */\n  private _startFadeOutTransition(rippleRef: RippleRef) {\n    const isMostRecentTransientRipple = rippleRef === this._mostRecentTransientRipple;\n    const {persistent} = rippleRef.config;\n\n    rippleRef.state = RippleState.VISIBLE;\n\n    // When the timer runs out while the user has kept their pointer down, we want to\n    // keep only the persistent ripples and the latest transient ripple. We do this,\n    // because we don't want stacked transient ripples to appear after their enter\n    // animation has finished.\n    if (!persistent && (!isMostRecentTransientRipple || !this._isPointerDown)) {\n      rippleRef.fadeOut();\n    }\n  }\n\n  /** Destroys the given ripple by removing it from the DOM and updating its state. */\n  private _destroyRipple(rippleRef: RippleRef) {\n    const eventListeners = this._activeRipples.get(rippleRef) ?? null;\n    this._activeRipples.delete(rippleRef);\n\n    // Clear out the cached bounding rect if we have no more ripples.\n    if (!this._activeRipples.size) {\n      this._containerRect = null;\n    }\n\n    // If the current ref is the most recent transient ripple, unset it\n    // avoid memory leaks.\n    if (rippleRef === this._mostRecentTransientRipple) {\n      this._mostRecentTransientRipple = null;\n    }\n\n    rippleRef.state = RippleState.HIDDEN;\n    if (eventListeners !== null) {\n      rippleRef.element.removeEventListener('transitionend', eventListeners.onTransitionEnd);\n      rippleRef.element.removeEventListener('transitioncancel', eventListeners.onTransitionCancel);\n    }\n    rippleRef.element.remove();\n  }\n\n  /** Function being called whenever the trigger is being pressed using mouse. */\n  private _onMousedown(event: MouseEvent) {\n    // Screen readers will fire fake mouse events for space/enter. Skip launching a\n    // ripple in this case for consistency with the non-screen-reader experience.\n    const isFakeMousedown = isFakeMousedownFromScreenReader(event);\n    const isSyntheticEvent =\n      this._lastTouchStartEvent &&\n      Date.now() < this._lastTouchStartEvent + ignoreMouseEventsTimeout;\n\n    if (!this._target.rippleDisabled && !isFakeMousedown && !isSyntheticEvent) {\n      this._isPointerDown = true;\n      this.fadeInRipple(event.clientX, event.clientY, this._target.rippleConfig);\n    }\n  }\n\n  /** Function being called whenever the trigger is being pressed using touch. */\n  private _onTouchStart(event: TouchEvent) {\n    if (!this._target.rippleDisabled && !isFakeTouchstartFromScreenReader(event)) {\n      // Some browsers fire mouse events after a `touchstart` event. Those synthetic mouse\n      // events will launch a second ripple if we don't ignore mouse events for a specific\n      // time after a touchstart event.\n      this._lastTouchStartEvent = Date.now();\n      this._isPointerDown = true;\n\n      // Use `changedTouches` so we skip any touches where the user put\n      // their finger down, but used another finger to tap the element again.\n      const touches = event.changedTouches as TouchList | undefined;\n\n      // According to the typings the touches should always be defined, but in some cases\n      // the browser appears to not assign them in tests which leads to flakes.\n      if (touches) {\n        for (let i = 0; i < touches.length; i++) {\n          this.fadeInRipple(touches[i].clientX, touches[i].clientY, this._target.rippleConfig);\n        }\n      }\n    }\n  }\n\n  /** Function being called whenever the trigger is being released. */\n  private _onPointerUp() {\n    if (!this._isPointerDown) {\n      return;\n    }\n\n    this._isPointerDown = false;\n\n    // Fade-out all ripples that are visible and not persistent.\n    this._getActiveRipples().forEach(ripple => {\n      // By default, only ripples that are completely visible will fade out on pointer release.\n      // If the `terminateOnPointerUp` option is set, ripples that still fade in will also fade out.\n      const isVisible =\n        ripple.state === RippleState.VISIBLE ||\n        (ripple.config.terminateOnPointerUp && ripple.state === RippleState.FADING_IN);\n\n      if (!ripple.config.persistent && isVisible) {\n        ripple.fadeOut();\n      }\n    });\n  }\n\n  private _getActiveRipples(): RippleRef[] {\n    return Array.from(this._activeRipples.keys());\n  }\n\n  /** Removes previously registered event listeners from the trigger element. */\n  _removeTriggerEvents() {\n    const trigger = this._triggerElement;\n\n    if (trigger) {\n      pointerDownEvents.forEach(type =>\n        RippleRenderer._eventManager.removeHandler(type, trigger, this),\n      );\n\n      if (this._pointerUpEventsRegistered) {\n        pointerUpEvents.forEach(type =>\n          trigger.removeEventListener(type, this, passiveCapturingEventOptions),\n        );\n\n        this._pointerUpEventsRegistered = false;\n      }\n    }\n  }\n}\n\n/**\n * Returns the distance from the point (x, y) to the furthest corner of a rectangle.\n */\nfunction distanceToFurthestCorner(x: number, y: number, rect: DOMRect) {\n  const distX = Math.max(Math.abs(x - rect.left), Math.abs(x - rect.right));\n  const distY = Math.max(Math.abs(y - rect.top), Math.abs(y - rect.bottom));\n  return Math.sqrt(distX * distX + distY * distY);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Platform} from '@angular/cdk/platform';\nimport {\n  Directive,\n  ElementRef,\n  Inject,\n  InjectionToken,\n  Input,\n  NgZone,\n  OnDestroy,\n  OnInit,\n  Optional,\n  ANIMATION_MODULE_TYPE,\n} from '@angular/core';\nimport {RippleAnimationConfig, RippleConfig, RippleRef} from './ripple-ref';\nimport {RippleRenderer, RippleTarget} from './ripple-renderer';\n\n/** Configurable options for `matRipple`. */\nexport interface RippleGlobalOptions {\n  /**\n   * Whether ripples should be disabled. Ripples can be still launched manually by using\n   * the `launch()` method. Therefore focus indicators will still show up.\n   */\n  disabled?: boolean;\n\n  /**\n   * Default configuration for the animation duration of the ripples. There are two phases with\n   * different durations for the ripples: `enter` and `leave`. The durations will be overwritten\n   * by the value of `matRippleAnimation` or if the `NoopAnimationsModule` is included.\n   */\n  animation?: RippleAnimationConfig;\n\n  /**\n   * Whether ripples should start fading out immediately after the mouse or touch is released. By\n   * default, ripples will wait for the enter animation to complete and for mouse or touch release.\n   */\n  terminateOnPointerUp?: boolean;\n}\n\n/** Injection token that can be used to specify the global ripple options. */\nexport const MAT_RIPPLE_GLOBAL_OPTIONS = new InjectionToken<RippleGlobalOptions>(\n  'mat-ripple-global-options',\n);\n\n@Directive({\n  selector: '[mat-ripple], [matRipple]',\n  exportAs: 'matRipple',\n  host: {\n    'class': 'mat-ripple',\n    '[class.mat-ripple-unbounded]': 'unbounded',\n  },\n  standalone: true,\n})\nexport class MatRipple implements OnInit, OnDestroy, RippleTarget {\n  /** Custom color for all ripples. */\n  @Input('matRippleColor') color: string;\n\n  /** Whether the ripples should be visible outside the component's bounds. */\n  @Input('matRippleUnbounded') unbounded: boolean;\n\n  /**\n   * Whether the ripple always originates from the center of the host element's bounds, rather\n   * than originating from the location of the click event.\n   */\n  @Input('matRippleCentered') centered: boolean;\n\n  /**\n   * If set, the radius in pixels of foreground ripples when fully expanded. If unset, the radius\n   * will be the distance from the center of the ripple to the furthest corner of the host element's\n   * bounding rectangle.\n   */\n  @Input('matRippleRadius') radius: number = 0;\n\n  /**\n   * Configuration for the ripple animation. Allows modifying the enter and exit animation\n   * duration of the ripples. The animation durations will be overwritten if the\n   * `NoopAnimationsModule` is being used.\n   */\n  @Input('matRippleAnimation') animation: RippleAnimationConfig;\n\n  /**\n   * Whether click events will not trigger the ripple. Ripples can be still launched manually\n   * by using the `launch()` method.\n   */\n  @Input('matRippleDisabled')\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value: boolean) {\n    if (value) {\n      this.fadeOutAllNonPersistent();\n    }\n    this._disabled = value;\n    this._setupTriggerEventsIfEnabled();\n  }\n  private _disabled: boolean = false;\n\n  /**\n   * The element that triggers the ripple when click events are received.\n   * Defaults to the directive's host element.\n   */\n  @Input('matRippleTrigger')\n  get trigger() {\n    return this._trigger || this._elementRef.nativeElement;\n  }\n  set trigger(trigger: HTMLElement) {\n    this._trigger = trigger;\n    this._setupTriggerEventsIfEnabled();\n  }\n  private _trigger: HTMLElement;\n\n  /** Renderer for the ripple DOM manipulations. */\n  private _rippleRenderer: RippleRenderer;\n\n  /** Options that are set globally for all ripples. */\n  private _globalOptions: RippleGlobalOptions;\n\n  /** @docs-private Whether ripple directive is initialized and the input bindings are set. */\n  _isInitialized: boolean = false;\n\n  constructor(\n    private _elementRef: ElementRef<HTMLElement>,\n    ngZone: NgZone,\n    platform: Platform,\n    @Optional() @Inject(MAT_RIPPLE_GLOBAL_OPTIONS) globalOptions?: RippleGlobalOptions,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) private _animationMode?: string,\n  ) {\n    this._globalOptions = globalOptions || {};\n    this._rippleRenderer = new RippleRenderer(this, ngZone, _elementRef, platform);\n  }\n\n  ngOnInit() {\n    this._isInitialized = true;\n    this._setupTriggerEventsIfEnabled();\n  }\n\n  ngOnDestroy() {\n    this._rippleRenderer._removeTriggerEvents();\n  }\n\n  /** Fades out all currently showing ripple elements. */\n  fadeOutAll() {\n    this._rippleRenderer.fadeOutAll();\n  }\n\n  /** Fades out all currently showing non-persistent ripple elements. */\n  fadeOutAllNonPersistent() {\n    this._rippleRenderer.fadeOutAllNonPersistent();\n  }\n\n  /**\n   * Ripple configuration from the directive's input values.\n   * @docs-private Implemented as part of RippleTarget\n   */\n  get rippleConfig(): RippleConfig {\n    return {\n      centered: this.centered,\n      radius: this.radius,\n      color: this.color,\n      animation: {\n        ...this._globalOptions.animation,\n        ...(this._animationMode === 'NoopAnimations' ? {enterDuration: 0, exitDuration: 0} : {}),\n        ...this.animation,\n      },\n      terminateOnPointerUp: this._globalOptions.terminateOnPointerUp,\n    };\n  }\n\n  /**\n   * Whether ripples on pointer-down are disabled or not.\n   * @docs-private Implemented as part of RippleTarget\n   */\n  get rippleDisabled(): boolean {\n    return this.disabled || !!this._globalOptions.disabled;\n  }\n\n  /** Sets up the trigger event listeners if ripples are enabled. */\n  private _setupTriggerEventsIfEnabled() {\n    if (!this.disabled && this._isInitialized) {\n      this._rippleRenderer.setupTriggerEvents(this.trigger);\n    }\n  }\n\n  /**\n   * Launches a manual ripple using the specified ripple configuration.\n   * @param config Configuration for the manual ripple.\n   */\n  launch(config: RippleConfig): RippleRef;\n\n  /**\n   * Launches a manual ripple at the specified coordinates relative to the viewport.\n   * @param x Coordinate along the X axis at which to fade-in the ripple. Coordinate\n   *   should be relative to the viewport.\n   * @param y Coordinate along the Y axis at which to fade-in the ripple. Coordinate\n   *   should be relative to the viewport.\n   * @param config Optional ripple configuration for the manual ripple.\n   */\n  launch(x: number, y: number, config?: RippleConfig): RippleRef;\n\n  /** Launches a manual ripple at the specified coordinated or just by the ripple config. */\n  launch(configOrX: number | RippleConfig, y: number = 0, config?: RippleConfig): RippleRef {\n    if (typeof configOrX === 'number') {\n      return this._rippleRenderer.fadeInRipple(configOrX, y, {...this.rippleConfig, ...config});\n    } else {\n      return this._rippleRenderer.fadeInRipple(0, 0, {...this.rippleConfig, ...configOrX});\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '../common-behaviors/common-module';\nimport {MatRipple} from './ripple';\n\nexport * from './ripple';\nexport * from './ripple-ref';\nexport * from './ripple-renderer';\n\n@NgModule({\n  imports: [MatCommonModule, MatRipple],\n  exports: [MatRipple, MatCommonModule],\n})\nexport class MatRippleModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Component,\n  ViewEncapsulation,\n  Input,\n  ChangeDetectionStrategy,\n  Inject,\n  Optional,\n  ANIMATION_MODULE_TYPE,\n} from '@angular/core';\n\n/**\n * Possible states for a pseudo checkbox.\n * @docs-private\n */\nexport type MatPseudoCheckboxState = 'unchecked' | 'checked' | 'indeterminate';\n\n/**\n * Component that shows a simplified checkbox without including any kind of \"real\" checkbox.\n * Meant to be used when the checkbox is purely decorative and a large number of them will be\n * included, such as for the options in a multi-select. Uses no SVGs or complex animations.\n * Note that theming is meant to be handled by the parent element, e.g.\n * `mat-primary .mat-pseudo-checkbox`.\n *\n * Note that this component will be completely invisible to screen-reader users. This is *not*\n * interchangeable with `<mat-checkbox>` and should *not* be used if the user would directly\n * interact with the checkbox. The pseudo-checkbox should only be used as an implementation detail\n * of more complex components that appropriately handle selected / checked state.\n * @docs-private\n */\n@Component({\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  selector: 'mat-pseudo-checkbox',\n  styleUrl: 'pseudo-checkbox.css',\n  template: '',\n  host: {\n    'class': 'mat-pseudo-checkbox',\n    '[class.mat-pseudo-checkbox-indeterminate]': 'state === \"indeterminate\"',\n    '[class.mat-pseudo-checkbox-checked]': 'state === \"checked\"',\n    '[class.mat-pseudo-checkbox-disabled]': 'disabled',\n    '[class.mat-pseudo-checkbox-minimal]': 'appearance === \"minimal\"',\n    '[class.mat-pseudo-checkbox-full]': 'appearance === \"full\"',\n    '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n  },\n  standalone: true,\n})\nexport class MatPseudoCheckbox {\n  /** Display state of the checkbox. */\n  @Input() state: MatPseudoCheckboxState = 'unchecked';\n\n  /** Whether the checkbox is disabled. */\n  @Input() disabled: boolean = false;\n\n  /**\n   * Appearance of the pseudo checkbox. Default appearance of 'full' renders a checkmark/mixedmark\n   * indicator inside a square box. 'minimal' appearance only renders the checkmark/mixedmark.\n   */\n  @Input() appearance: 'minimal' | 'full' = 'full';\n\n  constructor(@Optional() @Inject(ANIMATION_MODULE_TYPE) public _animationMode?: string) {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatPseudoCheckbox} from './pseudo-checkbox';\nimport {MatCommonModule} from '../../common-behaviors/common-module';\n\n@NgModule({\n  imports: [MatCommonModule, MatPseudoCheckbox],\n  exports: [MatPseudoCheckbox],\n})\nexport class MatPseudoCheckboxModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {InjectionToken} from '@angular/core';\n\n/**\n * Describes a parent component that manages a list of options.\n * Contains properties that the options can inherit.\n * @docs-private\n */\nexport interface MatOptionParentComponent {\n  disableRipple?: boolean;\n  multiple?: boolean;\n  inertGroups?: boolean;\n  hideSingleSelectionIndicator?: boolean;\n}\n\n/**\n * Injection token used to provide the parent component to options.\n */\nexport const MAT_OPTION_PARENT_COMPONENT = new InjectionToken<MatOptionParentComponent>(\n  'MAT_OPTION_PARENT_COMPONENT',\n);\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Component,\n  ViewEncapsulation,\n  ChangeDetectionStrategy,\n  Input,\n  Inject,\n  Optional,\n  InjectionToken,\n  booleanAttribute,\n} from '@angular/core';\nimport {MatOptionParentComponent, MAT_OPTION_PARENT_COMPONENT} from './option-parent';\n\n// Notes on the accessibility pattern used for `mat-optgroup`.\n// The option group has two different \"modes\": regular and inert. The regular mode uses the\n// recommended a11y pattern which has `role=\"group\"` on the group element with `aria-labelledby`\n// pointing to the label. This works for `mat-select`, but it seems to hit a bug for autocomplete\n// under VoiceOver where the group doesn't get read out at all. The bug appears to be that if\n// there's __any__ a11y-related attribute on the group (e.g. `role` or `aria-labelledby`),\n// VoiceOver on Safari won't read it out.\n// We've introduced the `inert` mode as a workaround. Under this mode, all a11y attributes are\n// removed from the group, and we get the screen reader to read out the group label by mirroring it\n// inside an invisible element in the option. This is sub-optimal, because the screen reader will\n// repeat the group label on each navigation, whereas the default pattern only reads the group when\n// the user enters a new group. The following alternate approaches were considered:\n// 1. Reading out the group label using the `LiveAnnouncer` solves the problem, but we can't control\n//    when the text will be read out so sometimes it comes in too late or never if the user\n//    navigates quickly.\n// 2. `<mat-option aria-describedby=\"groupLabel\"` - This works on Safari, but VoiceOver in Chrome\n//    won't read out the description at all.\n// 3. `<mat-option aria-labelledby=\"optionLabel groupLabel\"` - This works on Chrome, but Safari\n//     doesn't read out the text at all. Furthermore, on\n\n// Counter for unique group ids.\nlet _uniqueOptgroupIdCounter = 0;\n\n/**\n * Injection token that can be used to reference instances of `MatOptgroup`. It serves as\n * alternative token to the actual `MatOptgroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nexport const MAT_OPTGROUP = new InjectionToken<MatOptgroup>('MatOptgroup');\n\n/**\n * Component that is used to group instances of `mat-option`.\n */\n@Component({\n  selector: 'mat-optgroup',\n  exportAs: 'matOptgroup',\n  templateUrl: 'optgroup.html',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  styleUrl: 'optgroup.css',\n  host: {\n    'class': 'mat-mdc-optgroup',\n    '[attr.role]': '_inert ? null : \"group\"',\n    '[attr.aria-disabled]': '_inert ? null : disabled.toString()',\n    '[attr.aria-labelledby]': '_inert ? null : _labelId',\n  },\n  providers: [{provide: MAT_OPTGROUP, useExisting: MatOptgroup}],\n  standalone: true,\n})\nexport class MatOptgroup {\n  /** Label for the option group. */\n  @Input() label: string;\n\n  /** whether the option group is disabled. */\n  @Input({transform: booleanAttribute}) disabled: boolean = false;\n\n  /** Unique id for the underlying label. */\n  _labelId: string = `mat-optgroup-label-${_uniqueOptgroupIdCounter++}`;\n\n  /** Whether the group is in inert a11y mode. */\n  _inert: boolean;\n\n  constructor(@Inject(MAT_OPTION_PARENT_COMPONENT) @Optional() parent?: MatOptionParentComponent) {\n    this._inert = parent?.inertGroups ?? false;\n  }\n}\n", "<span\n  class=\"mat-mdc-optgroup-label\"\n  role=\"presentation\"\n  [class.mdc-list-item--disabled]=\"disabled\"\n  [id]=\"_labelId\">\n  <span class=\"mdc-list-item__primary-text\">{{ label }} <ng-content></ng-content></span>\n</span>\n\n<ng-content select=\"mat-option, ng-container\"></ng-content>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {FocusableOption, FocusOrigin} from '@angular/cdk/a11y';\nimport {ENTER, hasModifier<PERSON><PERSON>, SPACE} from '@angular/cdk/keycodes';\nimport {\n  Component,\n  ViewEncapsulation,\n  ChangeDetectionStrategy,\n  ElementRef,\n  ChangeDetectorRef,\n  Optional,\n  Inject,\n  AfterViewChecked,\n  OnDestroy,\n  Input,\n  Output,\n  EventEmitter,\n  QueryList,\n  ViewChild,\n  booleanAttribute,\n} from '@angular/core';\nimport {Subject} from 'rxjs';\nimport {MAT_OPTGROUP, MatOptgroup} from './optgroup';\nimport {MatOptionParentComponent, MAT_OPTION_PARENT_COMPONENT} from './option-parent';\nimport {MatRipple} from '../ripple/ripple';\nimport {MatPseudoCheckbox} from '../selection/pseudo-checkbox/pseudo-checkbox';\n\n/**\n * Option IDs need to be unique across components, so this counter exists outside of\n * the component definition.\n */\nlet _uniqueIdCounter = 0;\n\n/** Event object emitted by MatOption when selected or deselected. */\nexport class MatOptionSelectionChange<T = any> {\n  constructor(\n    /** Reference to the option that emitted the event. */\n    public source: MatOption<T>,\n    /** Whether the change in the option's value was a result of a user action. */\n    public isUserInput = false,\n  ) {}\n}\n\n/**\n * Single option inside of a `<mat-select>` element.\n */\n@Component({\n  selector: 'mat-option',\n  exportAs: 'matOption',\n  host: {\n    'role': 'option',\n    '[class.mdc-list-item--selected]': 'selected',\n    '[class.mat-mdc-option-multiple]': 'multiple',\n    '[class.mat-mdc-option-active]': 'active',\n    '[class.mdc-list-item--disabled]': 'disabled',\n    '[id]': 'id',\n    // Set aria-selected to false for non-selected items and true for selected items. Conform to\n    // [WAI ARIA Listbox authoring practices guide](\n    //  https://www.w3.org/WAI/ARIA/apg/patterns/listbox/), \"If any options are selected, each\n    // selected option has either aria-selected or aria-checked  set to true. All options that are\n    // selectable but not selected have either aria-selected or aria-checked set to false.\" Align\n    // aria-selected implementation of Chips and List components.\n    //\n    // Set `aria-selected=\"false\"` on not-selected listbox options to fix VoiceOver announcing\n    // every option as \"selected\" (#21491).\n    '[attr.aria-selected]': 'selected',\n    '[attr.aria-disabled]': 'disabled.toString()',\n    '(click)': '_selectViaInteraction()',\n    '(keydown)': '_handleKeydown($event)',\n    'class': 'mat-mdc-option mdc-list-item',\n  },\n  styleUrl: 'option.css',\n  templateUrl: 'option.html',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: true,\n  imports: [MatPseudoCheckbox, MatRipple],\n})\nexport class MatOption<T = any> implements FocusableOption, AfterViewChecked, OnDestroy {\n  private _selected = false;\n  private _active = false;\n  private _disabled = false;\n  private _mostRecentViewValue = '';\n\n  /** Whether the wrapping component is in multiple selection mode. */\n  get multiple() {\n    return this._parent && this._parent.multiple;\n  }\n\n  /** Whether or not the option is currently selected. */\n  get selected(): boolean {\n    return this._selected;\n  }\n\n  /** The form value of the option. */\n  @Input() value: T;\n\n  /** The unique ID of the option. */\n  @Input() id: string = `mat-option-${_uniqueIdCounter++}`;\n\n  /** Whether the option is disabled. */\n  @Input({transform: booleanAttribute})\n  get disabled(): boolean {\n    return (this.group && this.group.disabled) || this._disabled;\n  }\n  set disabled(value: boolean) {\n    this._disabled = value;\n  }\n\n  /** Whether ripples for the option are disabled. */\n  get disableRipple(): boolean {\n    return !!(this._parent && this._parent.disableRipple);\n  }\n\n  /** Whether to display checkmark for single-selection. */\n  get hideSingleSelectionIndicator(): boolean {\n    return !!(this._parent && this._parent.hideSingleSelectionIndicator);\n  }\n\n  /** Event emitted when the option is selected or deselected. */\n  // tslint:disable-next-line:no-output-on-prefix\n  @Output() readonly onSelectionChange = new EventEmitter<MatOptionSelectionChange<T>>();\n\n  /** Element containing the option's text. */\n  @ViewChild('text', {static: true}) _text: ElementRef<HTMLElement> | undefined;\n\n  /** Emits when the state of the option changes and any parents have to be notified. */\n  readonly _stateChanges = new Subject<void>();\n\n  constructor(\n    private _element: ElementRef<HTMLElement>,\n    public _changeDetectorRef: ChangeDetectorRef,\n    @Optional() @Inject(MAT_OPTION_PARENT_COMPONENT) private _parent: MatOptionParentComponent,\n    @Optional() @Inject(MAT_OPTGROUP) public group: MatOptgroup,\n  ) {}\n\n  /**\n   * Whether or not the option is currently active and ready to be selected.\n   * An active option displays styles as if it is focused, but the\n   * focus is actually retained somewhere else. This comes in handy\n   * for components like autocomplete where focus must remain on the input.\n   */\n  get active(): boolean {\n    return this._active;\n  }\n\n  /**\n   * The displayed value of the option. It is necessary to show the selected option in the\n   * select's trigger.\n   */\n  get viewValue(): string {\n    // TODO(kara): Add input property alternative for node envs.\n    return (this._text?.nativeElement.textContent || '').trim();\n  }\n\n  /** Selects the option. */\n  select(emitEvent = true): void {\n    if (!this._selected) {\n      this._selected = true;\n      this._changeDetectorRef.markForCheck();\n\n      if (emitEvent) {\n        this._emitSelectionChangeEvent();\n      }\n    }\n  }\n\n  /** Deselects the option. */\n  deselect(emitEvent = true): void {\n    if (this._selected) {\n      this._selected = false;\n      this._changeDetectorRef.markForCheck();\n\n      if (emitEvent) {\n        this._emitSelectionChangeEvent();\n      }\n    }\n  }\n\n  /** Sets focus onto this option. */\n  focus(_origin?: FocusOrigin, options?: FocusOptions): void {\n    // Note that we aren't using `_origin`, but we need to keep it because some internal consumers\n    // use `MatOption` in a `FocusKeyManager` and we need it to match `FocusableOption`.\n    const element = this._getHostElement();\n\n    if (typeof element.focus === 'function') {\n      element.focus(options);\n    }\n  }\n\n  /**\n   * This method sets display styles on the option to make it appear\n   * active. This is used by the ActiveDescendantKeyManager so key\n   * events will display the proper options as active on arrow key events.\n   */\n  setActiveStyles(): void {\n    if (!this._active) {\n      this._active = true;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n\n  /**\n   * This method removes display styles on the option that made it appear\n   * active. This is used by the ActiveDescendantKeyManager so key\n   * events will display the proper options as active on arrow key events.\n   */\n  setInactiveStyles(): void {\n    if (this._active) {\n      this._active = false;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n\n  /** Gets the label to be used when determining whether the option should be focused. */\n  getLabel(): string {\n    return this.viewValue;\n  }\n\n  /** Ensures the option is selected when activated from the keyboard. */\n  _handleKeydown(event: KeyboardEvent): void {\n    if ((event.keyCode === ENTER || event.keyCode === SPACE) && !hasModifierKey(event)) {\n      this._selectViaInteraction();\n\n      // Prevent the page from scrolling down and form submits.\n      event.preventDefault();\n    }\n  }\n\n  /**\n   * `Selects the option while indicating the selection came from the user. Used to\n   * determine if the select's view -> model callback should be invoked.`\n   */\n  _selectViaInteraction(): void {\n    if (!this.disabled) {\n      this._selected = this.multiple ? !this._selected : true;\n      this._changeDetectorRef.markForCheck();\n      this._emitSelectionChangeEvent(true);\n    }\n  }\n\n  /** Returns the correct tabindex for the option depending on disabled state. */\n  // This method is only used by `MatLegacyOption`. Keeping it here to avoid breaking the types.\n  // That's because `MatLegacyOption` use `MatOption` type in a few places such as\n  // `MatOptionSelectionChange`. It is safe to delete this when `MatLegacyOption` is deleted.\n  _getTabIndex(): string {\n    return this.disabled ? '-1' : '0';\n  }\n\n  /** Gets the host DOM element. */\n  _getHostElement(): HTMLElement {\n    return this._element.nativeElement;\n  }\n\n  ngAfterViewChecked() {\n    // Since parent components could be using the option's label to display the selected values\n    // (e.g. `mat-select`) and they don't have a way of knowing if the option's label has changed\n    // we have to check for changes in the DOM ourselves and dispatch an event. These checks are\n    // relatively cheap, however we still limit them only to selected options in order to avoid\n    // hitting the DOM too often.\n    if (this._selected) {\n      const viewValue = this.viewValue;\n\n      if (viewValue !== this._mostRecentViewValue) {\n        if (this._mostRecentViewValue) {\n          this._stateChanges.next();\n        }\n\n        this._mostRecentViewValue = viewValue;\n      }\n    }\n  }\n\n  ngOnDestroy() {\n    this._stateChanges.complete();\n  }\n\n  /** Emits the selection change event. */\n  private _emitSelectionChangeEvent(isUserInput = false): void {\n    this.onSelectionChange.emit(new MatOptionSelectionChange<T>(this, isUserInput));\n  }\n}\n\n/**\n * Counts the amount of option group labels that precede the specified option.\n * @param optionIndex Index of the option at which to start counting.\n * @param options Flat list of all of the options.\n * @param optionGroups Flat list of all of the option groups.\n * @docs-private\n */\nexport function _countGroupLabelsBeforeOption(\n  optionIndex: number,\n  options: QueryList<MatOption>,\n  optionGroups: QueryList<MatOptgroup>,\n): number {\n  if (optionGroups.length) {\n    let optionsArray = options.toArray();\n    let groups = optionGroups.toArray();\n    let groupCounter = 0;\n\n    for (let i = 0; i < optionIndex + 1; i++) {\n      if (optionsArray[i].group && optionsArray[i].group === groups[groupCounter]) {\n        groupCounter++;\n      }\n    }\n\n    return groupCounter;\n  }\n\n  return 0;\n}\n\n/**\n * Determines the position to which to scroll a panel in order for an option to be into view.\n * @param optionOffset Offset of the option from the top of the panel.\n * @param optionHeight Height of the options.\n * @param currentScrollPosition Current scroll position of the panel.\n * @param panelHeight Height of the panel.\n * @docs-private\n */\nexport function _getOptionScrollPosition(\n  optionOffset: number,\n  optionHeight: number,\n  currentScrollPosition: number,\n  panelHeight: number,\n): number {\n  if (optionOffset < currentScrollPosition) {\n    return optionOffset;\n  }\n\n  if (optionOffset + optionHeight > currentScrollPosition + panelHeight) {\n    return Math.max(0, optionOffset - panelHeight + optionHeight);\n  }\n\n  return currentScrollPosition;\n}\n", "<!-- Set aria-hidden=\"true\" to this DOM node and other decorative nodes in this file. This might\n be contributing to issue where sometimes VoiceOver focuses on a TextNode in the a11y tree instead\n of the Option node (#23202). Most assistive technology will generally ignore non-role,\n non-text-content elements. Adding aria-hidden seems to make VoiceOver behave more consistently. -->\n@if (multiple) {\n    <mat-pseudo-checkbox\n        class=\"mat-mdc-option-pseudo-checkbox\"\n        [disabled]=\"disabled\"\n        [state]=\"selected ? 'checked' : 'unchecked'\"\n        aria-hidden=\"true\"></mat-pseudo-checkbox>\n}\n\n<ng-content select=\"mat-icon\"></ng-content>\n\n<span class=\"mdc-list-item__primary-text\" #text><ng-content></ng-content></span>\n\n<!-- Render checkmark at the end for single-selection. -->\n@if (!multiple && selected && !hideSingleSelectionIndicator) {\n    <mat-pseudo-checkbox\n        class=\"mat-mdc-option-pseudo-checkbox\"\n        [disabled]=\"disabled\"\n        state=\"checked\"\n        aria-hidden=\"true\"\n        appearance=\"minimal\"></mat-pseudo-checkbox>\n}\n\n<!-- See a11y notes inside optgroup.ts for context behind this element. -->\n@if (group && group._inert) {\n    <span class=\"cdk-visually-hidden\">({{ group.label }})</span>\n}\n\n<div class=\"mat-mdc-option-ripple mat-mdc-focus-indicator\" aria-hidden=\"true\" mat-ripple\n     [matRippleTrigger]=\"_getHostElement()\" [matRippleDisabled]=\"disabled || disableRipple\">\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatRippleModule} from '../ripple/index';\nimport {MatPseudoCheckboxModule} from '../selection/index';\nimport {MatCommonModule} from '../common-behaviors/common-module';\nimport {MatOption} from './option';\nimport {MatOptgroup} from './optgroup';\n\n@NgModule({\n  imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, MatOption, MatOptgroup],\n  exports: [MatOption, MatOptgroup],\n})\nexport class MatOptionModule {}\n\nexport * from './option';\nexport * from './optgroup';\nexport * from './option-parent';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {DOCUMENT} from '@angular/common';\nimport {\n  ANIMATION_MODULE_TYPE,\n  ElementRef,\n  Injectable,\n  NgZ<PERSON>,\n  OnDestroy,\n  inject,\n} from '@angular/core';\nimport {MAT_RIPPLE_GLOBAL_OPTIONS, MatRipple} from '../ripple';\nimport {Platform} from '@angular/cdk/platform';\n\n/** The options for the MatRippleLoader's event listeners. */\nconst eventListenerOptions = {capture: true};\n\n/** The events that should trigger the initialization of the ripple. */\nconst rippleInteractionEvents = ['focus', 'click', 'mouseenter', 'touchstart'];\n\n/** The attribute attached to a component whose ripple has not yet been initialized. */\nconst matRippleUninitialized = 'mat-ripple-loader-uninitialized';\n\n/** Additional classes that should be added to the ripple when it is rendered. */\nconst matRippleClassName = 'mat-ripple-loader-class-name';\n\n/** Whether the ripple should be centered. */\nconst matRippleCentered = 'mat-ripple-loader-centered';\n\n/** Whether the ripple should be disabled. */\nconst matRippleDisabled = 'mat-ripple-loader-disabled';\n\n/**\n * Handles attaching ripples on demand.\n *\n * This service allows us to avoid eagerly creating & attaching MatRipples.\n * It works by creating & attaching a ripple only when a component is first interacted with.\n *\n * @docs-private\n */\n@Injectable({providedIn: 'root'})\nexport class MatRippleLoader implements OnDestroy {\n  private _document = inject(DOCUMENT, {optional: true});\n  private _animationMode = inject(ANIMATION_MODULE_TYPE, {optional: true});\n  private _globalRippleOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {optional: true});\n  private _platform = inject(Platform);\n  private _ngZone = inject(NgZone);\n  private _hosts = new Map<HTMLElement, MatRipple>();\n\n  constructor() {\n    this._ngZone.runOutsideAngular(() => {\n      for (const event of rippleInteractionEvents) {\n        this._document?.addEventListener(event, this._onInteraction, eventListenerOptions);\n      }\n    });\n  }\n\n  ngOnDestroy() {\n    const hosts = this._hosts.keys();\n\n    for (const host of hosts) {\n      this.destroyRipple(host);\n    }\n\n    for (const event of rippleInteractionEvents) {\n      this._document?.removeEventListener(event, this._onInteraction, eventListenerOptions);\n    }\n  }\n\n  /**\n   * Configures the ripple that will be rendered by the ripple loader.\n   *\n   * Stores the given information about how the ripple should be configured on the host\n   * element so that it can later be retrived & used when the ripple is actually created.\n   */\n  configureRipple(\n    host: HTMLElement,\n    config: {\n      className?: string;\n      centered?: boolean;\n      disabled?: boolean;\n    },\n  ): void {\n    // Indicates that the ripple has not yet been rendered for this component.\n    host.setAttribute(matRippleUninitialized, '');\n\n    // Store the additional class name(s) that should be added to the ripple element.\n    if (config.className || !host.hasAttribute(matRippleClassName)) {\n      host.setAttribute(matRippleClassName, config.className || '');\n    }\n\n    // Store whether the ripple should be centered.\n    if (config.centered) {\n      host.setAttribute(matRippleCentered, '');\n    }\n\n    if (config.disabled) {\n      host.setAttribute(matRippleDisabled, '');\n    }\n  }\n\n  /** Returns the ripple instance for the given host element. */\n  getRipple(host: HTMLElement): MatRipple | undefined {\n    const ripple = this._hosts.get(host);\n    return ripple || this._createRipple(host);\n  }\n\n  /** Sets the disabled state on the ripple instance corresponding to the given host element. */\n  setDisabled(host: HTMLElement, disabled: boolean): void {\n    const ripple = this._hosts.get(host);\n\n    // If the ripple has already been instantiated, just disable it.\n    if (ripple) {\n      ripple.disabled = disabled;\n      return;\n    }\n\n    // Otherwise, set an attribute so we know what the\n    // disabled state should be when the ripple is initialized.\n    if (disabled) {\n      host.setAttribute(matRippleDisabled, '');\n    } else {\n      host.removeAttribute(matRippleDisabled);\n    }\n  }\n\n  /** Handles creating and attaching component internals when a component it is initially interacted with. */\n  private _onInteraction = (event: Event) => {\n    if (!(event.target instanceof HTMLElement)) {\n      return;\n    }\n    const eventTarget = event.target as HTMLElement;\n\n    // TODO(wagnermaciel): Consider batching these events to improve runtime performance.\n\n    const element = eventTarget.closest(`[${matRippleUninitialized}]`);\n    if (element) {\n      this._createRipple(element as HTMLElement);\n    }\n  };\n\n  /** Creates a MatRipple and appends it to the given element. */\n  private _createRipple(host: HTMLElement): MatRipple | undefined {\n    if (!this._document) {\n      return;\n    }\n\n    const existingRipple = this._hosts.get(host);\n    if (existingRipple) {\n      return existingRipple;\n    }\n\n    // Create the ripple element.\n    host.querySelector('.mat-ripple')?.remove();\n    const rippleEl = this._document.createElement('span');\n    rippleEl.classList.add('mat-ripple', host.getAttribute(matRippleClassName)!);\n    host.append(rippleEl);\n\n    // Create the MatRipple.\n    const ripple = new MatRipple(\n      new ElementRef(rippleEl),\n      this._ngZone,\n      this._platform,\n      this._globalRippleOptions ? this._globalRippleOptions : undefined,\n      this._animationMode ? this._animationMode : undefined,\n    );\n    ripple._isInitialized = true;\n    ripple.trigger = host;\n    ripple.centered = host.hasAttribute(matRippleCentered);\n    ripple.disabled = host.hasAttribute(matRippleDisabled);\n    this.attachRipple(host, ripple);\n    return ripple;\n  }\n\n  attachRipple(host: HTMLElement, ripple: MatRipple): void {\n    host.removeAttribute(matRippleUninitialized);\n    this._hosts.set(host, ripple);\n  }\n\n  destroyRipple(host: HTMLElement) {\n    const ripple = this._hosts.get(host);\n\n    if (ripple) {\n      // Since this directive is created manually, it needs to be destroyed manually too.\n      // tslint:disable-next-line:no-lifecycle-invocation\n      ripple.ngOnDestroy();\n      this._hosts.delete(host);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ChangeDetectionStrategy, Component, Input, ViewEncapsulation} from '@angular/core';\n\n/**\n * Internal shared component used as a container in form field controls.\n * Not to be confused with `mat-form-field` which MDC calls a \"text field\".\n * @docs-private\n */\n@Component({\n  // Use a `div` selector to match the old markup closer.\n  selector: 'div[mat-internal-form-field]',\n  standalone: true,\n  template: '<ng-content></ng-content>',\n  styleUrl: 'internal-form-field.css',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  host: {\n    'class': 'mdc-form-field mat-internal-form-field',\n    '[class.mdc-form-field--align-end]': 'labelPosition === \"before\"',\n  },\n})\nexport class _MatInternalFormField {\n  /** Position of the label relative to the content. */\n  @Input({required: true}) labelPosition: 'before' | 'after';\n}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["CDK_VERSION", "passiveCapturingEventOptions", "i1"], "mappings": ";;;;;;;;;;;;;;AAUA;MACa,OAAO,GAAG,IAAI,OAAO,CAAC,mBAAmB;;ACHtD;MACa,eAAe,CAAA;aACnB,IAAc,CAAA,cAAA,GAAG,6BAA6B,CAAC,EAAA;aAC/C,IAAkB,CAAA,kBAAA,GAAG,6BAA6B,CAAC,EAAA;aACnD,IAAkB,CAAA,kBAAA,GAAG,2BAA2B,CAAC,EAAA;aACjD,IAAW,CAAA,WAAA,GAAG,6BAA6B,CAAC,EAAA;;AAGrD;MACa,kBAAkB,CAAA;aACtB,IAAO,CAAA,OAAA,GAAG,OAAO,CAAC,EAAA;aAClB,IAAQ,CAAA,QAAA,GAAG,OAAO,CAAC,EAAA;aACnB,IAAO,CAAA,OAAA,GAAG,OAAO,CAAC,EAAA;;;ACJ3B;SACgB,8BAA8B,GAAA;AAC5C,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;MACa,sBAAsB,GAAG,IAAI,cAAc,CAAe,mBAAmB,EAAE;AAC1F,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,8BAA8B;AACxC,CAAA,EAAE;AAeH;;;;;AAKG;MAKU,eAAe,CAAA;AAI1B,IAAA,WAAA,CACE,wBAAkD,EACE,aAA2B,EACrD,SAAmB,EAAA;QADO,IAAa,CAAA,aAAA,GAAb,aAAa,CAAc;QACrD,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;;QALvC,IAAoB,CAAA,oBAAA,GAAG,KAAK,CAAC;;;QASnC,wBAAwB,CAAC,oCAAoC,EAAE,CAAC;AAEhE,QAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;AAC9B,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AAEjC,YAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;;AAEjD,gBAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;AAEpD,gBAAA,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE;AACnC,oBAAA,sBAAsB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;iBACxC;AAED,gBAAA,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE;oBACjC,oBAAoB,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;iBAC7D;AAED,gBAAA,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE;AACnC,oBAAA,qBAAqB,EAAE,CAAC;iBACzB;aACF;SACF;KACF;;AAGO,IAAA,eAAe,CAAC,IAAgC,EAAA;QACtD,IAAI,kBAAkB,EAAE,EAAE;AACxB,YAAA,OAAO,KAAK,CAAC;SACd;AAED,QAAA,IAAI,OAAO,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE;YAC3C,OAAO,IAAI,CAAC,aAAa,CAAC;SAC3B;QAED,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;KACnC;8GA9CU,eAAe,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,wBAAA,EAAA,EAAA,EAAA,KAAA,EAMJ,sBAAsB,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAClC,QAAQ,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;+GAPP,eAAe,EAAA,OAAA,EAAA,CAHhB,UAAU,CAAA,EAAA,OAAA,EAAA,CACV,UAAU,CAAA,EAAA,CAAA,CAAA,EAAA;+GAET,eAAe,EAAA,OAAA,EAAA,CAHhB,UAAU,EACV,UAAU,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAET,eAAe,EAAA,UAAA,EAAA,CAAA;kBAJ3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,UAAU,CAAC;oBACrB,OAAO,EAAE,CAAC,UAAU,CAAC;AACtB,iBAAA,CAAA;;0BAOI,QAAQ;;0BAAI,MAAM;2BAAC,sBAAsB,CAAA;;0BACzC,MAAM;2BAAC,QAAQ,CAAA;;AA0CpB;AACA,SAAS,sBAAsB,CAAC,GAAa,EAAA;AAC3C,IAAA,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;QAChB,OAAO,CAAC,IAAI,CACV,2DAA2D;AACzD,YAAA,6DAA6D,CAChE,CAAC;KACH;AACH,CAAC;AAED;AACA,SAAS,oBAAoB,CAAC,GAAa,EAAE,SAAkB,EAAA;;;IAG7D,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE;QAC3B,OAAO;KACR;IAED,MAAM,WAAW,GAAG,GAAG,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC7C,IAAA,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;AACrD,IAAA,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AAElC,IAAA,MAAM,aAAa,GAAG,gBAAgB,CAAC,WAAW,CAAC,CAAC;;;;IAKpD,IAAI,aAAa,IAAI,aAAa,CAAC,OAAO,KAAK,MAAM,EAAE;QACrD,OAAO,CAAC,IAAI,CACV,4DAA4D;YAC1D,2DAA2D;AAC3D,YAAA,iEAAiE,CACpE,CAAC;KACH;IAED,WAAW,CAAC,MAAM,EAAE,CAAC;AACvB,CAAC;AAED;AACA,SAAS,qBAAqB,GAAA;IAC5B,IAAI,OAAO,CAAC,IAAI,KAAKA,SAAW,CAAC,IAAI,EAAE;QACrC,OAAO,CAAC,IAAI,CACV,gCAAgC;AAC9B,YAAA,OAAO,CAAC,IAAI;YACZ,mBAAmB;YACnB,2BAA2B;AAC3B,YAAAA,SAAW,CAAC,IAAI;YAChB,MAAM;AACN,YAAA,iEAAiE,CACpE,CAAC;KACH;AACH;;ACzHM,SAAU,aAAa,CAA4B,IAAO,EAAA;IAC9D,OAAO,cAAc,IAAI,CAAA;AAGvB,QAAA,IAAI,QAAQ,GAAA;YACV,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QACD,IAAI,QAAQ,CAAC,KAAU,EAAA;AACrB,YAAA,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;SAC/C;AAED,QAAA,WAAA,CAAY,GAAG,IAAW,EAAA;AACxB,YAAA,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;YAVT,IAAS,CAAA,SAAA,GAAY,KAAK,CAAC;SAWlC;KACF,CAAC;AACJ;;ACDgB,SAAA,UAAU,CACxB,IAAO,EACP,YAA2B,EAAA;IAE3B,OAAO,cAAc,IAAI,CAAA;AAIvB,QAAA,IAAI,KAAK,GAAA;YACP,OAAO,IAAI,CAAC,MAAM,CAAC;SACpB;QACD,IAAI,KAAK,CAAC,KAAmB,EAAA;AAC3B,YAAA,MAAM,YAAY,GAAG,KAAK,IAAI,IAAI,CAAC,YAAY,CAAC;AAEhD,YAAA,IAAI,YAAY,KAAK,IAAI,CAAC,MAAM,EAAE;AAChC,gBAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,oBAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,MAAM,CAAA,CAAE,CAAC,CAAC;iBACvE;gBACD,IAAI,YAAY,EAAE;AAChB,oBAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA,IAAA,EAAO,YAAY,CAAA,CAAE,CAAC,CAAC;iBACrE;AAED,gBAAA,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC;aAC5B;SACF;AAED,QAAA,WAAA,CAAY,GAAG,IAAW,EAAA;AACxB,YAAA,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;YArBjB,IAAY,CAAA,YAAA,GAAG,YAAY,CAAC;;AAwB1B,YAAA,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC;SAC3B;KACF,CAAC;AACJ;;AC7CM,SAAU,kBAAkB,CAA4B,IAAO,EAAA;IACnE,OAAO,cAAc,IAAI,CAAA;;AAIvB,QAAA,IAAI,aAAa,GAAA;YACf,OAAO,IAAI,CAAC,cAAc,CAAC;SAC5B;QACD,IAAI,aAAa,CAAC,KAAU,EAAA;AAC1B,YAAA,IAAI,CAAC,cAAc,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;SACpD;AAED,QAAA,WAAA,CAAY,GAAG,IAAW,EAAA;AACxB,YAAA,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;YAXT,IAAc,CAAA,cAAA,GAAY,KAAK,CAAC;SAYvC;KACF,CAAC;AACJ;;SCXgB,aAAa,CAC3B,IAAO,EACP,eAAe,GAAG,CAAC,EAAA;IAEnB,OAAO,cAAc,IAAI,CAAA;AAIvB,QAAA,IAAI,QAAQ,GAAA;AACV,YAAA,OAAO,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;SAC5C;QACD,IAAI,QAAQ,CAAC,KAAa,EAAA;;AAExB,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,IAAI,IAAI,GAAG,oBAAoB,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;SACrF;AAED,QAAA,WAAA,CAAY,GAAG,IAAW,EAAA;AACxB,YAAA,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;YAZT,IAAS,CAAA,SAAA,GAAW,eAAe,CAAC;YAC5C,IAAe,CAAA,eAAA,GAAG,eAAe,CAAC;SAYjC;KACF,CAAC;AACJ;;ACVA;;;AAGG;MACU,kBAAkB,CAAA;IAO7B,WACU,CAAA,eAAyC,EAC1C,SAA2B,EAC1B,gBAA2C,EAC3C,WAA0B,EAC1B,aAA4B,EAAA;QAJ5B,IAAe,CAAA,eAAA,GAAf,eAAe,CAA0B;QAC1C,IAAS,CAAA,SAAA,GAAT,SAAS,CAAkB;QAC1B,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAA2B;QAC3C,IAAW,CAAA,WAAA,GAAX,WAAW,CAAe;QAC1B,IAAa,CAAA,aAAA,GAAb,aAAa,CAAe;;QAVtC,IAAU,CAAA,UAAA,GAAG,KAAK,CAAC;KAWf;;IAGJ,gBAAgB,GAAA;AACd,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC;QACjC,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,WAAW,CAAC;QACzD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,eAAe,CAAC;AACrD,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,GAAI,IAAI,CAAC,SAAS,CAAC,OAA2B,GAAG,IAAI,CAAC;AACpF,QAAA,MAAM,QAAQ,GAAG,OAAO,EAAE,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI,KAAK,CAAC;AAEjE,QAAA,IAAI,QAAQ,KAAK,QAAQ,EAAE;AACzB,YAAA,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC;AAC3B,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;SAC3B;KACF;AACF,CAAA;AAWK,SAAU,eAAe,CAC7B,IAAO,EAAA;IAEP,OAAO,cAAc,IAAI,CAAA;;AAIvB,QAAA,IAAI,UAAU,GAAA;AACZ,YAAA,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC;SACtC;QACD,IAAI,UAAU,CAAC,KAAc,EAAA;AAC3B,YAAA,IAAI,CAAC,WAAW,EAAE,CAAC,UAAU,GAAG,KAAK,CAAC;SACvC;;AAGD,QAAA,IAAI,iBAAiB,GAAA;AACnB,YAAA,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC;SACnC;QACD,IAAI,iBAAiB,CAAC,KAAwB,EAAA;AAC5C,YAAA,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,GAAG,KAAK,CAAC;SACpC;;QAGD,gBAAgB,GAAA;AACd,YAAA,IAAI,CAAC,WAAW,EAAE,CAAC,gBAAgB,EAAE,CAAC;SACvC;QAEO,WAAW,GAAA;AACjB,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAClB,IAAI,CAAC,QAAQ,GAAG,IAAI,kBAAkB,CACpC,IAAI,CAAC,yBAAyB,EAC9B,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,YAAY,CAClB,CAAC;aACH;YAED,OAAO,IAAI,CAAC,QAAQ,CAAC;SACtB;AAED,QAAA,WAAA,CAAY,GAAG,IAAW,EAAA;AACxB,YAAA,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;SAChB;KACF,CAAC;AACJ;;ACpGA;;;;AAIG;AACG,SAAU,gBAAgB,CAA4B,IAAO,EAAA;IACjE,OAAO,cAAc,IAAI,CAAA;AAyBvB,QAAA,WAAA,CAAY,GAAG,IAAW,EAAA;AACxB,YAAA,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;;YAxBjB,IAAc,CAAA,cAAA,GAAG,KAAK,CAAC;AAEvB;;;;AAIG;YACH,IAAmB,CAAA,mBAAA,GAA8B,EAAE,CAAC;AAEpD;;;AAGG;AACH,YAAA,IAAA,CAAA,WAAW,GAAG,IAAI,UAAU,CAAO,UAAU,IAAG;;;AAG9C,gBAAA,IAAI,IAAI,CAAC,cAAc,EAAE;AACvB,oBAAA,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;iBACpC;qBAAM;AACL,oBAAA,IAAI,CAAC,mBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;iBAC5C;AACH,aAAC,CAAC,CAAC;SAIF;AAED;;;;AAIG;QACH,gBAAgB,GAAA;AACd,YAAA,IAAI,IAAI,CAAC,cAAc,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;gBAC1E,MAAM,KAAK,CACT,4DAA4D;AAC1D,oBAAA,6BAA6B,CAChC,CAAC;aACH;AAED,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAE3B,IAAI,CAAC,mBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AAC1D,YAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;SACjC;;AAGD,QAAA,iBAAiB,CAAC,UAA4B,EAAA;YAC5C,UAAU,CAAC,IAAI,EAAE,CAAC;YAClB,UAAU,CAAC,QAAQ,EAAE,CAAC;SACvB;KACF,CAAC;AACJ;;ACnFA;MACa,eAAe,GAAG,IAAI,cAAc,CAAK,iBAAiB,EAAE;AACvE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,uBAAuB;AACjC,CAAA,EAAE;AAEH;SACgB,uBAAuB,GAAA;AACrC,IAAA,OAAO,MAAM,CAAC,SAAS,CAAC,CAAC;AAC3B,CAAC;AAED;MACsB,WAAW,CAAA;AAAjC,IAAA,WAAA,GAAA;AAGqB,QAAA,IAAA,CAAA,cAAc,GAAG,IAAI,OAAO,EAAQ,CAAC;;AAG/C,QAAA,IAAA,CAAA,aAAa,GAAqB,IAAI,CAAC,cAAc,CAAC;KAiQhE;AAzFC;;;;;AAKG;AACH,IAAA,kBAAkB,CAAC,GAAY,EAAA;QAC7B,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,GAAQ,CAAC,GAAI,GAAS,GAAG,IAAI,CAAC;KAC/E;AAED;;;;;;;;;;;AAWG;AACH,IAAA,WAAW,CAAC,KAAU,EAAA;QACpB,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;AACxE,YAAA,OAAO,KAAK,CAAC;SACd;AACD,QAAA,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;KACvB;AAED;;;AAGG;AACH,IAAA,SAAS,CAAC,MAAS,EAAA;AACjB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;KAC5B;AAED;;;;;;AAMG;IACH,WAAW,CAAC,KAAQ,EAAE,MAAS,EAAA;AAC7B,QAAA,QACE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1C,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC5C,YAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAC1C;KACH;AAED;;;;;;AAMG;IACH,QAAQ,CAAC,KAAe,EAAE,MAAgB,EAAA;AACxC,QAAA,IAAI,KAAK,IAAI,MAAM,EAAE;YACnB,IAAI,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACrC,IAAI,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACvC,YAAA,IAAI,UAAU,IAAI,WAAW,EAAE;gBAC7B,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;aACzC;YACD,OAAO,UAAU,IAAI,WAAW,CAAC;SAClC;QACD,OAAO,KAAK,IAAI,MAAM,CAAC;KACxB;AAED;;;;;;;AAOG;AACH,IAAA,SAAS,CAAC,IAAO,EAAE,GAAc,EAAE,GAAc,EAAA;AAC/C,QAAA,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE;AAC1C,YAAA,OAAO,GAAG,CAAC;SACZ;AACD,QAAA,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE;AAC1C,YAAA,OAAO,GAAG,CAAC;SACZ;AACD,QAAA,OAAO,IAAI,CAAC;KACb;AACF;;MCvQY,gBAAgB,GAAG,IAAI,cAAc,CAAiB,kBAAkB;;ACZrF;;;;AAIG;AACH,MAAM,cAAc,GAClB,oFAAoF,CAAC;AAEvF;AACA,SAAS,KAAK,CAAI,MAAc,EAAE,aAAmC,EAAA;AACnE,IAAA,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;AAClC,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;QAC/B,WAAW,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;KACnC;AACD,IAAA,OAAO,WAAW,CAAC;AACrB,CAAC;AAED;AAEM,MAAO,iBAAkB,SAAQ,WAAiB,CAAA;AAUtD,IAAA,WAAA;AACE;;;AAGG;IACkC,aAAsB,EAAA;AAE3D,QAAA,KAAK,EAAE,CAAC;AAhBV;;;AAGG;QACH,IAAgB,CAAA,gBAAA,GAAY,KAAK,CAAC;;QAGjB,IAAc,CAAA,cAAA,GAAG,MAAM,CAAC,eAAe,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;AAU1E,QAAA,IAAI,aAAa,KAAK,SAAS,EAAE;AAC/B,YAAA,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;SACrC;AACD,QAAA,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;KACtC;AAED,IAAA,OAAO,CAAC,IAAU,EAAA;AAChB,QAAA,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;KAC3B;AAED,IAAA,QAAQ,CAAC,IAAU,EAAA;AACjB,QAAA,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;KACxB;AAED,IAAA,OAAO,CAAC,IAAU,EAAA;AAChB,QAAA,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;KACvB;AAED,IAAA,YAAY,CAAC,IAAU,EAAA;AACrB,QAAA,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;KACtB;AAED,IAAA,aAAa,CAAC,KAAkC,EAAA;QAC9C,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,EAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAC,CAAC,CAAC;QAClF,OAAO,KAAK,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;KAChE;IAED,YAAY,GAAA;QACV,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,EAAC,GAAG,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAC,CAAC,CAAC;QACpF,OAAO,KAAK,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACpE;AAED,IAAA,iBAAiB,CAAC,KAAkC,EAAA;QAClD,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,EAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAC,CAAC,CAAC;QACpF,OAAO,KAAK,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACnE;AAED,IAAA,WAAW,CAAC,IAAU,EAAA;QACpB,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,EAAC,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAC,CAAC,CAAC;QACrF,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;KAChC;IAED,iBAAiB,GAAA;;AAEf,QAAA,OAAO,CAAC,CAAC;KACV;AAED,IAAA,iBAAiB,CAAC,IAAU,EAAA;QAC1B,OAAO,IAAI,CAAC,OAAO,CACjB,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAC7E,CAAC;KACH;AAED,IAAA,KAAK,CAAC,IAAU,EAAA;QACd,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;KACjC;AAED,IAAA,UAAU,CAAC,IAAY,EAAE,KAAa,EAAE,IAAY,EAAA;AAClD,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;;;YAGjD,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,EAAE,EAAE;AAC3B,gBAAA,MAAM,KAAK,CAAC,CAAA,qBAAA,EAAwB,KAAK,CAAA,0CAAA,CAA4C,CAAC,CAAC;aACxF;AAED,YAAA,IAAI,IAAI,GAAG,CAAC,EAAE;AACZ,gBAAA,MAAM,KAAK,CAAC,CAAA,cAAA,EAAiB,IAAI,CAAA,iCAAA,CAAmC,CAAC,CAAC;aACvE;SACF;AAED,QAAA,IAAI,MAAM,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;;AAE7D,QAAA,IAAI,MAAM,CAAC,QAAQ,EAAE,IAAI,KAAK,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YACjF,MAAM,KAAK,CAAC,CAAiB,cAAA,EAAA,IAAI,2BAA2B,KAAK,CAAA,EAAA,CAAI,CAAC,CAAC;SACxE;AAED,QAAA,OAAO,MAAM,CAAC;KACf;IAED,KAAK,GAAA;QACH,OAAO,IAAI,IAAI,EAAE,CAAC;KACnB;IAED,KAAK,CAAC,KAAU,EAAE,WAAiB,EAAA;;;AAGjC,QAAA,IAAI,OAAO,KAAK,IAAI,QAAQ,EAAE;AAC5B,YAAA,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;SACxB;AACD,QAAA,OAAO,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC;KACnD;IAED,MAAM,CAAC,IAAU,EAAE,aAAqB,EAAA;QACtC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AACvB,YAAA,MAAM,KAAK,CAAC,gDAAgD,CAAC,CAAC;SAC/D;QAED,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,EAAC,GAAG,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAC,CAAC,CAAC;QACtF,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;KAChC;IAED,gBAAgB,CAAC,IAAU,EAAE,KAAa,EAAA;QACxC,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,GAAG,EAAE,CAAC,CAAC;KACjD;IAED,iBAAiB,CAAC,IAAU,EAAE,MAAc,EAAA;AAC1C,QAAA,IAAI,OAAO,GAAG,IAAI,CAAC,uBAAuB,CACxC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAClB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,EAC5B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CACnB,CAAC;;;;;AAMF,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE;YAC/E,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;SAC1F;AAED,QAAA,OAAO,OAAO,CAAC;KAChB;IAED,eAAe,CAAC,IAAU,EAAE,IAAY,EAAA;QACtC,OAAO,IAAI,CAAC,uBAAuB,CACjC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAClB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EACnB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAC1B,CAAC;KACH;AAED,IAAA,SAAS,CAAC,IAAU,EAAA;QAClB,OAAO;YACL,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;AACpC,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;AAChC,SAAA,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KACb;AAED;;;;AAIG;AACM,IAAA,WAAW,CAAC,KAAU,EAAA;AAC7B,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,IAAI,CAAC,KAAK,EAAE;AACV,gBAAA,OAAO,IAAI,CAAC;aACb;;;AAGD,YAAA,IAAI,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AAC9B,gBAAA,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;AAC3B,gBAAA,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AACtB,oBAAA,OAAO,IAAI,CAAC;iBACb;aACF;SACF;AACD,QAAA,OAAO,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;KACjC;AAED,IAAA,cAAc,CAAC,GAAQ,EAAA;QACrB,OAAO,GAAG,YAAY,IAAI,CAAC;KAC5B;AAED,IAAA,OAAO,CAAC,IAAU,EAAA;QAChB,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;KAC/B;IAED,OAAO,GAAA;AACL,QAAA,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;KACtB;;AAGO,IAAA,uBAAuB,CAAC,IAAY,EAAE,KAAa,EAAE,IAAY,EAAA;;;AAGvE,QAAA,MAAM,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;QACrB,CAAC,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QACjC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACvB,QAAA,OAAO,CAAC,CAAC;KACV;AAED;;;;AAIG;AACK,IAAA,OAAO,CAAC,CAAS,EAAA;QACvB,OAAO,CAAC,IAAI,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;KAC7B;AAED;;;;;;;;;;AAUG;IACK,OAAO,CAAC,GAAwB,EAAE,IAAU,EAAA;;;AAGlD,QAAA,MAAM,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;AACrB,QAAA,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;QACtE,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;AAC7F,QAAA,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;KACtB;AApOU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,kBAeN,eAAe,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;kHAf1B,iBAAiB,EAAA,CAAA,CAAA,EAAA;;2FAAjB,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAD7B,UAAU;;0BAgBN,QAAQ;;0BAAI,MAAM;2BAAC,eAAe,CAAA;;;ACnC1B,MAAA,uBAAuB,GAAmB;AACrD,IAAA,KAAK,EAAE;AACL,QAAA,SAAS,EAAE,IAAI;AAChB,KAAA;AACD,IAAA,OAAO,EAAE;AACP,QAAA,SAAS,EAAE,EAAC,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,EAAE,SAAS,EAAC;QAC9D,cAAc,EAAE,EAAC,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAC;AACjD,QAAA,aAAa,EAAE,EAAC,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,SAAS,EAAC;QAC/D,kBAAkB,EAAE,EAAC,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAC;AACrD,KAAA;;;MCGU,gBAAgB,CAAA;8GAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;+GAAhB,gBAAgB,EAAA,CAAA,CAAA,EAAA;+GAAhB,gBAAgB,EAAA,SAAA,EAFhB,CAAC,EAAC,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,iBAAiB,EAAC,CAAC,EAAA,CAAA,CAAA,EAAA;;2FAErD,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAH5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,iBAAiB,EAAC,CAAC;AACjE,iBAAA,CAAA;;MAMY,mBAAmB,CAAA;8GAAnB,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;+GAAnB,mBAAmB,EAAA,CAAA,CAAA,EAAA;AAAnB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,mBAAmB,EAFnB,SAAA,EAAA,CAAC,wBAAwB,EAAE,CAAC,EAAA,CAAA,CAAA,EAAA;;2FAE5B,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAH/B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,SAAS,EAAE,CAAC,wBAAwB,EAAE,CAAC;AACxC,iBAAA,CAAA;;AAGe,SAAA,wBAAwB,CACtC,OAAA,GAA0B,uBAAuB,EAAA;IAEjD,OAAO;AACL,QAAA,EAAC,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,iBAAiB,EAAC;AACnD,QAAA,EAAC,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,OAAO,EAAC;KAC/C,CAAC;AACJ;;ACzBA;MAEa,4BAA4B,CAAA;IACvC,YAAY,CAAC,OAA+B,EAAE,IAAwC,EAAA;QACpF,OAAO,CAAC,EAAE,OAAO,IAAI,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;KACtF;8GAHU,4BAA4B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;kHAA5B,4BAA4B,EAAA,CAAA,CAAA,EAAA;;2FAA5B,4BAA4B,EAAA,UAAA,EAAA,CAAA;kBADxC,UAAU;;AAOX;MAEa,iBAAiB,CAAA;IAC5B,YAAY,CAAC,OAA+B,EAAE,IAAwC,EAAA;QACpF,OAAO,CAAC,EAAE,OAAO,IAAI,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO,KAAK,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;KACxF;8GAHU,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAAjB,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,cADL,MAAM,EAAA,CAAA,CAAA,EAAA;;2FAClB,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAD7B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;;ACRhC;;;;AAIG;MAMU,OAAO,CAAA;8GAAP,OAAO,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAP,OAAO,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,uBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,UAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAP,OAAO,EAAA,UAAA,EAAA,CAAA;kBALnB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,uBAAuB;AACjC,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,UAAU,EAAC;AAC3B,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;AAGD;;;AAGG;AACG,SAAU,QAAQ,CACtB,KAAyB,EACzB,OAAgC,EAChC,MAAM,GAAG,KAAK,EAAA;;;AAId,IAAA,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAC,MAAM,EAAC,KAAI;QAC1D,QAAQ,CAAC,OAAO,EAAE,CAAA,EAAG,MAAM,CAAS,OAAA,CAAA,EAAE,KAAK,CAAC,CAAC;QAC7C,QAAQ,CAAC,OAAO,EAAE,CAAA,EAAG,MAAM,CAAS,OAAA,CAAA,EAAE,KAAK,CAAC,CAAC;QAC7C,QAAQ,CAAC,OAAO,EAAE,CAAA,EAAG,MAAM,CAAa,WAAA,CAAA,EAAE,KAAK,CAAC,CAAC;QAEjD,IAAI,MAAM,KAAK,CAAC,IAAI,MAAM,KAAK,CAAC,EAAE;YAChC,QAAQ,CAAC,OAAO,EAAE,CAAG,EAAA,MAAM,CAAI,CAAA,EAAA,MAAM,CAAO,KAAA,CAAA,EAAE,IAAI,CAAC,CAAC;SACrD;AAAM,aAAA,IAAI,MAAM,GAAG,CAAC,EAAE;YACrB,QAAQ,CAAC,OAAO,EAAE,CAAA,EAAG,MAAM,CAAa,WAAA,CAAA,EAAE,IAAI,CAAC,CAAC;SACjD;AACH,KAAC,CAAC,CAAC;AACL,CAAC;AAED;AACA,SAAS,QAAQ,CAAC,OAAgC,EAAE,SAAiB,EAAE,KAAc,EAAA;IACnF,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AAC3D,CAAC;MAMY,aAAa,CAAA;8GAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAb,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,YAHd,eAAe,EAhCd,OAAO,CAAP,EAAA,OAAA,EAAA,CAAA,OAAO,EAiCC,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;+GAEvB,aAAa,EAAA,OAAA,EAAA,CAHd,eAAe,EACN,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAEvB,aAAa,EAAA,UAAA,EAAA,CAAA;kBAJzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,OAAO,CAAC;AACnC,oBAAA,OAAO,EAAE,CAAC,OAAO,EAAE,eAAe,CAAC;AACpC,iBAAA,CAAA;;;AChDD;IACY,YAKX;AALD,CAAA,UAAY,WAAW,EAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAS,CAAA;AACT,IAAA,WAAA,CAAA,WAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO,CAAA;AACP,IAAA,WAAA,CAAA,WAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAU,CAAA;AACV,IAAA,WAAA,CAAA,WAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM,CAAA;AACR,CAAC,EALW,WAAW,KAAX,WAAW,GAKtB,EAAA,CAAA,CAAA,CAAA;AAsBD;;AAEG;MACU,SAAS,CAAA;AAIpB,IAAA,WAAA,CACU,SAAgD;;IAEjD,OAAoB;;IAEpB,MAAoB;;AAEpB,IAAA,oCAAA,GAAuC,KAAK,EAAA;QAN3C,IAAS,CAAA,SAAA,GAAT,SAAS,CAAuC;QAEjD,IAAO,CAAA,OAAA,GAAP,OAAO,CAAa;QAEpB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAc;QAEpB,IAAoC,CAAA,oCAAA,GAApC,oCAAoC,CAAQ;;AATrD,QAAA,IAAA,CAAA,KAAK,GAAgB,WAAW,CAAC,MAAM,CAAC;KAUpC;;IAGJ,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;KACpC;AACF;;AC9CD;AACA,MAAMC,8BAA4B,GAAG,+BAA+B,CAAC;AACnE,IAAA,OAAO,EAAE,IAAI;AACb,IAAA,OAAO,EAAE,IAAI;AACd,CAAA,CAAC,CAAC;AAEH;MACa,kBAAkB,CAAA;AAA/B,IAAA,WAAA,GAAA;AACU,QAAA,IAAA,CAAA,OAAO,GAAG,IAAI,GAAG,EAAsD,CAAC;;AAkDxE,QAAA,IAAA,CAAA,qBAAqB,GAAG,CAAC,KAAY,KAAI;AAC/C,YAAA,MAAM,MAAM,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;YAEtC,IAAI,MAAM,EAAE;AACV,gBAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,QAAQ,EAAE,OAAO,KAAI;oBAC1D,IAAI,OAAO,KAAK,MAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAc,CAAC,EAAE;AAC1D,wBAAA,QAAQ,CAAC,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;qBACzD;AACH,iBAAC,CAAC,CAAC;aACJ;AACH,SAAC,CAAC;KACH;;AA1DC,IAAA,UAAU,CAAC,MAAc,EAAE,IAAY,EAAE,OAAoB,EAAE,OAA4B,EAAA;QACzF,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAEhD,IAAI,gBAAgB,EAAE;YACpB,MAAM,kBAAkB,GAAG,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAEzD,IAAI,kBAAkB,EAAE;AACtB,gBAAA,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;aACjC;iBAAM;AACL,gBAAA,gBAAgB,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;aACnD;SACF;aAAM;YACL,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,OAAO,EAAE,IAAI,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAEjE,YAAA,MAAM,CAAC,iBAAiB,CAAC,MAAK;gBAC5B,QAAQ,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,qBAAqB,EAAEA,8BAA4B,CAAC,CAAC;AAC5F,aAAC,CAAC,CAAC;SACJ;KACF;;AAGD,IAAA,aAAa,CAAC,IAAY,EAAE,OAAoB,EAAE,OAA4B,EAAA;QAC5E,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAEhD,IAAI,CAAC,gBAAgB,EAAE;YACrB,OAAO;SACR;QAED,MAAM,kBAAkB,GAAG,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAEzD,IAAI,CAAC,kBAAkB,EAAE;YACvB,OAAO;SACR;AAED,QAAA,kBAAkB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAEnC,QAAA,IAAI,kBAAkB,CAAC,IAAI,KAAK,CAAC,EAAE;AACjC,YAAA,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;SAClC;AAED,QAAA,IAAI,gBAAgB,CAAC,IAAI,KAAK,CAAC,EAAE;AAC/B,YAAA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC1B,QAAQ,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,qBAAqB,EAAEA,8BAA4B,CAAC,CAAC;SAC9F;KACF;AAcF;;AChDD;;;AAGG;AACU,MAAA,4BAA4B,GAAG;AAC1C,IAAA,aAAa,EAAE,GAAG;AAClB,IAAA,YAAY,EAAE,GAAG;EACjB;AAEF;;;AAGG;AACH,MAAM,wBAAwB,GAAG,GAAG,CAAC;AAErC;AACA,MAAM,4BAA4B,GAAG,+BAA+B,CAAC;AACnE,IAAA,OAAO,EAAE,IAAI;AACb,IAAA,OAAO,EAAE,IAAI;AACd,CAAA,CAAC,CAAC;AAEH;AACA,MAAM,iBAAiB,GAAG,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;AAEtD;AACA,MAAM,eAAe,GAAG,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;AAE7E;;;;;;AAMG;MACU,cAAc,CAAA;AAiCV,IAAA,SAAA,IAAA,CAAA,aAAa,GAAG,IAAI,kBAAkB,EAAE,CAAC,EAAA;AAExD,IAAA,WAAA,CACU,OAAqB,EACrB,OAAe,EACvB,mBAA0D,EAClD,SAAmB,EAAA;QAHnB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAc;QACrB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QAEf,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;;QA/BrB,IAAc,CAAA,cAAA,GAAG,KAAK,CAAC;AAE/B;;;;;AAKG;AACK,QAAA,IAAA,CAAA,cAAc,GAAG,IAAI,GAAG,EAA0C,CAAC;;QASnE,IAA0B,CAAA,0BAAA,GAAG,KAAK,CAAC;;AAiBzC,QAAA,IAAI,SAAS,CAAC,SAAS,EAAE;AACvB,YAAA,IAAI,CAAC,iBAAiB,GAAG,aAAa,CAAC,mBAAmB,CAAC,CAAC;SAC7D;KACF;AAED;;;;;AAKG;AACH,IAAA,YAAY,CAAC,CAAS,EAAE,CAAS,EAAE,SAAuB,EAAE,EAAA;AAC1D,QAAA,MAAM,aAAa,IAAI,IAAI,CAAC,cAAc;YACxC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,EAAE,CAAC,CAAC;QACzE,MAAM,eAAe,GAAG,EAAC,GAAG,4BAA4B,EAAE,GAAG,MAAM,CAAC,SAAS,EAAC,CAAC;AAE/E,QAAA,IAAI,MAAM,CAAC,QAAQ,EAAE;YACnB,CAAC,GAAG,aAAa,CAAC,IAAI,GAAG,aAAa,CAAC,KAAK,GAAG,CAAC,CAAC;YACjD,CAAC,GAAG,aAAa,CAAC,GAAG,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;SAClD;AAED,QAAA,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,wBAAwB,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC;AAC9E,QAAA,MAAM,OAAO,GAAG,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC;AACvC,QAAA,MAAM,OAAO,GAAG,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC;AACtC,QAAA,MAAM,aAAa,GAAG,eAAe,CAAC,aAAa,CAAC;QAEpD,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC7C,QAAA,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;QAE3C,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,OAAO,GAAG,MAAM,CAAA,EAAA,CAAI,CAAC;QAC5C,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,OAAO,GAAG,MAAM,CAAA,EAAA,CAAI,CAAC;QAC3C,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,MAAM,GAAG,CAAC,CAAA,EAAA,CAAI,CAAC;QACxC,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,MAAM,GAAG,CAAC,CAAA,EAAA,CAAI,CAAC;;;AAIvC,QAAA,IAAI,MAAM,CAAC,KAAK,IAAI,IAAI,EAAE;YACxB,MAAM,CAAC,KAAK,CAAC,eAAe,GAAG,MAAM,CAAC,KAAK,CAAC;SAC7C;QAED,MAAM,CAAC,KAAK,CAAC,kBAAkB,GAAG,CAAG,EAAA,aAAa,IAAI,CAAC;AAEvD,QAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;;;;;QAM3C,MAAM,cAAc,GAAG,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;AACvD,QAAA,MAAM,sBAAsB,GAAG,cAAc,CAAC,kBAAkB,CAAC;AACjE,QAAA,MAAM,sBAAsB,GAAG,cAAc,CAAC,kBAAkB,CAAC;;;;;;AAOjE,QAAA,MAAM,mCAAmC,GACvC,sBAAsB,KAAK,MAAM;;;AAGjC,YAAA,sBAAsB,KAAK,IAAI;AAC/B,YAAA,sBAAsB,KAAK,QAAQ;;AAEnC,aAAC,aAAa,CAAC,KAAK,KAAK,CAAC,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;;AAG5D,QAAA,MAAM,SAAS,GAAG,IAAI,SAAS,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,mCAAmC,CAAC,CAAC;;;;;AAM3F,QAAA,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG,kBAAkB,CAAC;AAE5C,QAAA,SAAS,CAAC,KAAK,GAAG,WAAW,CAAC,SAAS,CAAC;AAExC,QAAA,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;AACtB,YAAA,IAAI,CAAC,0BAA0B,GAAG,SAAS,CAAC;SAC7C;QAED,IAAI,cAAc,GAAgC,IAAI,CAAC;;;QAIvD,IAAI,CAAC,mCAAmC,KAAK,aAAa,IAAI,eAAe,CAAC,YAAY,CAAC,EAAE;AAC3F,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;gBAClC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;gBACtE,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;AAChE,gBAAA,MAAM,CAAC,gBAAgB,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC;;;;AAI1D,gBAAA,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;AAChE,gBAAA,cAAc,GAAG,EAAC,eAAe,EAAE,kBAAkB,EAAC,CAAC;AACzD,aAAC,CAAC,CAAC;SACJ;;QAGD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;;;AAInD,QAAA,IAAI,mCAAmC,IAAI,CAAC,aAAa,EAAE;AACzD,YAAA,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;SACzC;AAED,QAAA,OAAO,SAAS,CAAC;KAClB;;AAGD,IAAA,aAAa,CAAC,SAAoB,EAAA;;AAEhC,QAAA,IAAI,SAAS,CAAC,KAAK,KAAK,WAAW,CAAC,UAAU,IAAI,SAAS,CAAC,KAAK,KAAK,WAAW,CAAC,MAAM,EAAE;YACxF,OAAO;SACR;AAED,QAAA,MAAM,QAAQ,GAAG,SAAS,CAAC,OAAO,CAAC;AACnC,QAAA,MAAM,eAAe,GAAG,EAAC,GAAG,4BAA4B,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,SAAS,EAAC,CAAC;;;QAIzF,QAAQ,CAAC,KAAK,CAAC,kBAAkB,GAAG,GAAG,eAAe,CAAC,YAAY,CAAA,EAAA,CAAI,CAAC;AACxE,QAAA,QAAQ,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;AAC7B,QAAA,SAAS,CAAC,KAAK,GAAG,WAAW,CAAC,UAAU,CAAC;;;QAIzC,IAAI,SAAS,CAAC,oCAAoC,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE;AACnF,YAAA,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;SACzC;KACF;;IAGD,UAAU,GAAA;AACR,QAAA,IAAI,CAAC,iBAAiB,EAAE,CAAC,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;KAC9D;;IAGD,uBAAuB,GAAA;QACrB,IAAI,CAAC,iBAAiB,EAAE,CAAC,OAAO,CAAC,MAAM,IAAG;AACxC,YAAA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE;gBAC7B,MAAM,CAAC,OAAO,EAAE,CAAC;aAClB;AACH,SAAC,CAAC,CAAC;KACJ;;AAGD,IAAA,kBAAkB,CAAC,mBAA0D,EAAA;AAC3E,QAAA,MAAM,OAAO,GAAG,aAAa,CAAC,mBAAmB,CAAC,CAAC;AAEnD,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,CAAC,OAAO,IAAI,OAAO,KAAK,IAAI,CAAC,eAAe,EAAE;YAC7E,OAAO;SACR;;QAGD,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,QAAA,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC;;;AAI/B,QAAA,iBAAiB,CAAC,OAAO,CAAC,IAAI,IAAG;AAC/B,YAAA,cAAc,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AAC7E,SAAC,CAAC,CAAC;KACJ;AAED;;;AAGG;AACH,IAAA,WAAW,CAAC,KAAY,EAAA;AACtB,QAAA,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,EAAE;AAC9B,YAAA,IAAI,CAAC,YAAY,CAAC,KAAmB,CAAC,CAAC;SACxC;AAAM,aAAA,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE;AACtC,YAAA,IAAI,CAAC,aAAa,CAAC,KAAmB,CAAC,CAAC;SACzC;aAAM;YACL,IAAI,CAAC,YAAY,EAAE,CAAC;SACrB;;;;AAKD,QAAA,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE;;;;;;AAMpC,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,gBAAA,eAAe,CAAC,OAAO,CAAC,IAAI,IAAG;oBAC7B,IAAI,CAAC,eAAgB,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE,4BAA4B,CAAC,CAAC;AACnF,iBAAC,CAAC,CAAC;AACL,aAAC,CAAC,CAAC;AAEH,YAAA,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;SACxC;KACF;;AAGO,IAAA,uBAAuB,CAAC,SAAoB,EAAA;QAClD,IAAI,SAAS,CAAC,KAAK,KAAK,WAAW,CAAC,SAAS,EAAE;AAC7C,YAAA,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;SACzC;aAAM,IAAI,SAAS,CAAC,KAAK,KAAK,WAAW,CAAC,UAAU,EAAE;AACrD,YAAA,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;SAChC;KACF;AAED;;;AAGG;AACK,IAAA,uBAAuB,CAAC,SAAoB,EAAA;AAClD,QAAA,MAAM,2BAA2B,GAAG,SAAS,KAAK,IAAI,CAAC,0BAA0B,CAAC;AAClF,QAAA,MAAM,EAAC,UAAU,EAAC,GAAG,SAAS,CAAC,MAAM,CAAC;AAEtC,QAAA,SAAS,CAAC,KAAK,GAAG,WAAW,CAAC,OAAO,CAAC;;;;;AAMtC,QAAA,IAAI,CAAC,UAAU,KAAK,CAAC,2BAA2B,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE;YACzE,SAAS,CAAC,OAAO,EAAE,CAAC;SACrB;KACF;;AAGO,IAAA,cAAc,CAAC,SAAoB,EAAA;AACzC,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC;AAClE,QAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;;AAGtC,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE;AAC7B,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;SAC5B;;;AAID,QAAA,IAAI,SAAS,KAAK,IAAI,CAAC,0BAA0B,EAAE;AACjD,YAAA,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;SACxC;AAED,QAAA,SAAS,CAAC,KAAK,GAAG,WAAW,CAAC,MAAM,CAAC;AACrC,QAAA,IAAI,cAAc,KAAK,IAAI,EAAE;YAC3B,SAAS,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,EAAE,cAAc,CAAC,eAAe,CAAC,CAAC;YACvF,SAAS,CAAC,OAAO,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,cAAc,CAAC,kBAAkB,CAAC,CAAC;SAC9F;AACD,QAAA,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;KAC5B;;AAGO,IAAA,YAAY,CAAC,KAAiB,EAAA;;;AAGpC,QAAA,MAAM,eAAe,GAAG,+BAA+B,CAAC,KAAK,CAAC,CAAC;AAC/D,QAAA,MAAM,gBAAgB,GACpB,IAAI,CAAC,oBAAoB;YACzB,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,oBAAoB,GAAG,wBAAwB,CAAC;AAEpE,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,CAAC,eAAe,IAAI,CAAC,gBAAgB,EAAE;AACzE,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC3B,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;SAC5E;KACF;;AAGO,IAAA,aAAa,CAAC,KAAiB,EAAA;AACrC,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,EAAE;;;;AAI5E,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AACvC,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;;;AAI3B,YAAA,MAAM,OAAO,GAAG,KAAK,CAAC,cAAuC,CAAC;;;YAI9D,IAAI,OAAO,EAAE;AACX,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACvC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;iBACtF;aACF;SACF;KACF;;IAGO,YAAY,GAAA;AAClB,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,OAAO;SACR;AAED,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;;QAG5B,IAAI,CAAC,iBAAiB,EAAE,CAAC,OAAO,CAAC,MAAM,IAAG;;;YAGxC,MAAM,SAAS,GACb,MAAM,CAAC,KAAK,KAAK,WAAW,CAAC,OAAO;AACpC,iBAAC,MAAM,CAAC,MAAM,CAAC,oBAAoB,IAAI,MAAM,CAAC,KAAK,KAAK,WAAW,CAAC,SAAS,CAAC,CAAC;YAEjF,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,IAAI,SAAS,EAAE;gBAC1C,MAAM,CAAC,OAAO,EAAE,CAAC;aAClB;AACH,SAAC,CAAC,CAAC;KACJ;IAEO,iBAAiB,GAAA;QACvB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC;KAC/C;;IAGD,oBAAoB,GAAA;AAClB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC;QAErC,IAAI,OAAO,EAAE;YACX,iBAAiB,CAAC,OAAO,CAAC,IAAI,IAC5B,cAAc,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAChE,CAAC;AAEF,YAAA,IAAI,IAAI,CAAC,0BAA0B,EAAE;AACnC,gBAAA,eAAe,CAAC,OAAO,CAAC,IAAI,IAC1B,OAAO,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,EAAE,4BAA4B,CAAC,CACtE,CAAC;AAEF,gBAAA,IAAI,CAAC,0BAA0B,GAAG,KAAK,CAAC;aACzC;SACF;KACF;;AAGH;;AAEG;AACH,SAAS,wBAAwB,CAAC,CAAS,EAAE,CAAS,EAAE,IAAa,EAAA;AACnE,IAAA,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AAC1E,IAAA,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AAC1E,IAAA,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,CAAC;AAClD;;ACnZA;MACa,yBAAyB,GAAG,IAAI,cAAc,CACzD,2BAA2B,EAC3B;MAWW,SAAS,CAAA;AA2BpB;;;AAGG;AACH,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,KAAc,EAAA;QACzB,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,uBAAuB,EAAE,CAAC;SAChC;AACD,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,4BAA4B,EAAE,CAAC;KACrC;AAGD;;;AAGG;AACH,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;KACxD;IACD,IAAI,OAAO,CAAC,OAAoB,EAAA;AAC9B,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,4BAA4B,EAAE,CAAC;KACrC;IAYD,WACU,CAAA,WAAoC,EAC5C,MAAc,EACd,QAAkB,EAC6B,aAAmC,EAC/B,cAAuB,EAAA;QAJlE,IAAW,CAAA,WAAA,GAAX,WAAW,CAAyB;QAIO,IAAc,CAAA,cAAA,GAAd,cAAc,CAAS;AA3D5E;;;;AAIG;QACuB,IAAM,CAAA,MAAA,GAAW,CAAC,CAAC;QAwBrC,IAAS,CAAA,SAAA,GAAY,KAAK,CAAC;;QAuBnC,IAAc,CAAA,cAAA,GAAY,KAAK,CAAC;AAS9B,QAAA,IAAI,CAAC,cAAc,GAAG,aAAa,IAAI,EAAE,CAAC;AAC1C,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;KAChF;IAED,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,4BAA4B,EAAE,CAAC;KACrC;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,eAAe,CAAC,oBAAoB,EAAE,CAAC;KAC7C;;IAGD,UAAU,GAAA;AACR,QAAA,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC;KACnC;;IAGD,uBAAuB,GAAA;AACrB,QAAA,IAAI,CAAC,eAAe,CAAC,uBAAuB,EAAE,CAAC;KAChD;AAED;;;AAGG;AACH,IAAA,IAAI,YAAY,GAAA;QACd,OAAO;YACL,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,KAAK,EAAE,IAAI,CAAC,KAAK;AACjB,YAAA,SAAS,EAAE;AACT,gBAAA,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS;gBAChC,IAAI,IAAI,CAAC,cAAc,KAAK,gBAAgB,GAAG,EAAC,aAAa,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAC,GAAG,EAAE,CAAC;gBACxF,GAAG,IAAI,CAAC,SAAS;AAClB,aAAA;AACD,YAAA,oBAAoB,EAAE,IAAI,CAAC,cAAc,CAAC,oBAAoB;SAC/D,CAAC;KACH;AAED;;;AAGG;AACH,IAAA,IAAI,cAAc,GAAA;QAChB,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;KACxD;;IAGO,4BAA4B,GAAA;QAClC,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,cAAc,EAAE;YACzC,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACvD;KACF;;AAmBD,IAAA,MAAM,CAAC,SAAgC,EAAE,CAAY,GAAA,CAAC,EAAE,MAAqB,EAAA;AAC3E,QAAA,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;YACjC,OAAO,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,EAAE,EAAC,GAAG,IAAI,CAAC,YAAY,EAAE,GAAG,MAAM,EAAC,CAAC,CAAC;SAC3F;aAAM;YACL,OAAO,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,EAAC,GAAG,IAAI,CAAC,YAAY,EAAE,GAAG,SAAS,EAAC,CAAC,CAAC;SACtF;KACF;8GAzJU,SAAS,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,IAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAuEE,yBAAyB,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EACzB,qBAAqB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAxEhC,SAAS,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,2BAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,CAAA,gBAAA,EAAA,OAAA,CAAA,EAAA,SAAA,EAAA,CAAA,oBAAA,EAAA,WAAA,CAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,EAAA,UAAA,CAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,QAAA,CAAA,EAAA,SAAA,EAAA,CAAA,oBAAA,EAAA,WAAA,CAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,EAAA,UAAA,CAAA,EAAA,OAAA,EAAA,CAAA,kBAAA,EAAA,SAAA,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,4BAAA,EAAA,WAAA,EAAA,EAAA,cAAA,EAAA,YAAA,EAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAT,SAAS,EAAA,UAAA,EAAA,CAAA;kBATrB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,2BAA2B;AACrC,oBAAA,QAAQ,EAAE,WAAW;AACrB,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,YAAY;AACrB,wBAAA,8BAA8B,EAAE,WAAW;AAC5C,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BAwEI,QAAQ;;0BAAI,MAAM;2BAAC,yBAAyB,CAAA;;0BAC5C,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;yCAtElB,KAAK,EAAA,CAAA;sBAA7B,KAAK;uBAAC,gBAAgB,CAAA;gBAGM,SAAS,EAAA,CAAA;sBAArC,KAAK;uBAAC,oBAAoB,CAAA;gBAMC,QAAQ,EAAA,CAAA;sBAAnC,KAAK;uBAAC,mBAAmB,CAAA;gBAOA,MAAM,EAAA,CAAA;sBAA/B,KAAK;uBAAC,iBAAiB,CAAA;gBAOK,SAAS,EAAA,CAAA;sBAArC,KAAK;uBAAC,oBAAoB,CAAA;gBAOvB,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,mBAAmB,CAAA;gBAkBtB,OAAO,EAAA,CAAA;sBADV,KAAK;uBAAC,kBAAkB,CAAA;;;MCxFd,eAAe,CAAA;8GAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAf,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YAHhB,eAAe,EAAE,SAAS,CAC1B,EAAA,OAAA,EAAA,CAAA,SAAS,EAAE,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;+GAEzB,eAAe,EAAA,OAAA,EAAA,CAHhB,eAAe,EACJ,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAEzB,eAAe,EAAA,UAAA,EAAA,CAAA;kBAJ3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,SAAS,CAAC;AACrC,oBAAA,OAAO,EAAE,CAAC,SAAS,EAAE,eAAe,CAAC;AACtC,iBAAA,CAAA;;;ACKD;;;;;;;;;;;;AAYG;MAkBU,iBAAiB,CAAA;AAa5B,IAAA,WAAA,CAA8D,cAAuB,EAAA;QAAvB,IAAc,CAAA,cAAA,GAAd,cAAc,CAAS;;QAX5E,IAAK,CAAA,KAAA,GAA2B,WAAW,CAAC;;QAG5C,IAAQ,CAAA,QAAA,GAAY,KAAK,CAAC;AAEnC;;;AAGG;QACM,IAAU,CAAA,UAAA,GAAuB,MAAM,CAAC;KAEwC;AAb9E,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,kBAaI,qBAAqB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAb1C,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,iBAAiB,gmBAZlB,EAAE,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,y9FAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAYD,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAjB7B,SAAS;oCACO,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,MAAM,EACrC,QAAA,EAAA,qBAAqB,EAErB,QAAA,EAAA,EAAE,EACN,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,qBAAqB;AAC9B,wBAAA,2CAA2C,EAAE,2BAA2B;AACxE,wBAAA,qCAAqC,EAAE,qBAAqB;AAC5D,wBAAA,sCAAsC,EAAE,UAAU;AAClD,wBAAA,qCAAqC,EAAE,0BAA0B;AACjE,wBAAA,kCAAkC,EAAE,uBAAuB;AAC3D,wBAAA,iCAAiC,EAAE,qCAAqC;AACzE,qBAAA,EAAA,UAAA,EACW,IAAI,EAAA,MAAA,EAAA,CAAA,y9FAAA,CAAA,EAAA,CAAA;;0BAeH,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;yCAX5C,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAGG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAMG,UAAU,EAAA,CAAA;sBAAlB,KAAK;;;MCjDK,uBAAuB,CAAA;8GAAvB,uBAAuB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAvB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,uBAAuB,EAHxB,OAAA,EAAA,CAAA,eAAe,EAAE,iBAAiB,aAClC,iBAAiB,CAAA,EAAA,CAAA,CAAA,EAAA;AAEhB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,uBAAuB,YAHxB,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAGd,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBAJnC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,iBAAiB,CAAC;oBAC7C,OAAO,EAAE,CAAC,iBAAiB,CAAC;AAC7B,iBAAA,CAAA;;;ACOD;;AAEG;MACU,2BAA2B,GAAG,IAAI,cAAc,CAC3D,6BAA6B;;ACN/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA,IAAI,wBAAwB,GAAG,CAAC,CAAC;AAEjC;;;;AAIG;MACU,YAAY,GAAG,IAAI,cAAc,CAAc,aAAa,EAAE;AAE3E;;AAEG;MAiBU,WAAW,CAAA;AAatB,IAAA,WAAA,CAA6D,MAAiC,EAAA;;QARxD,IAAQ,CAAA,QAAA,GAAY,KAAK,CAAC;;AAGhE,QAAA,IAAA,CAAA,QAAQ,GAAW,CAAA,mBAAA,EAAsB,wBAAwB,EAAE,EAAE,CAAC;QAMpE,IAAI,CAAC,MAAM,GAAG,MAAM,EAAE,WAAW,IAAI,KAAK,CAAC;KAC5C;AAfU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAW,kBAaF,2BAA2B,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAbpC,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,WAAW,EAKH,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CARxB,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,WAAA,EAAA,2BAAA,EAAA,oBAAA,EAAA,qCAAA,EAAA,sBAAA,EAAA,0BAAA,EAAA,EAAA,cAAA,EAAA,kBAAA,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAC,CAAC,qDClEhE,kTASA,EAAA,MAAA,EAAA,CAAA,45BAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FD4Da,WAAW,EAAA,UAAA,EAAA,CAAA;kBAhBvB,SAAS;+BACE,cAAc,EAAA,QAAA,EACd,aAAa,EAAA,aAAA,EAER,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAEzC,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,kBAAkB;AAC3B,wBAAA,aAAa,EAAE,yBAAyB;AACxC,wBAAA,sBAAsB,EAAE,qCAAqC;AAC7D,wBAAA,wBAAwB,EAAE,0BAA0B;qBACrD,EACU,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,YAAY,EAAE,WAAW,EAAa,WAAA,EAAC,CAAC,EAAA,UAAA,EAClD,IAAI,EAAA,QAAA,EAAA,kTAAA,EAAA,MAAA,EAAA,CAAA,45BAAA,CAAA,EAAA,CAAA;;0BAeH,MAAM;2BAAC,2BAA2B,CAAA;;0BAAG,QAAQ;yCAXjD,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAGgC,QAAQ,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;;;AEzCtC;;;AAGG;AACH,IAAI,gBAAgB,GAAG,CAAC,CAAC;AAEzB;MACa,wBAAwB,CAAA;AACnC,IAAA,WAAA;;IAES,MAAoB;;AAEpB,IAAA,WAAA,GAAc,KAAK,EAAA;QAFnB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAc;QAEpB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAQ;KACxB;AACL,CAAA;AAED;;AAEG;MAiCU,SAAS,CAAA;;AAOpB,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;KAC9C;;AAGD,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;;AASD,IAAA,IACI,QAAQ,GAAA;AACV,QAAA,OAAO,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,CAAC;KAC9D;IACD,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;KACxB;;AAGD,IAAA,IAAI,aAAa,GAAA;AACf,QAAA,OAAO,CAAC,EAAE,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;KACvD;;AAGD,IAAA,IAAI,4BAA4B,GAAA;AAC9B,QAAA,OAAO,CAAC,EAAE,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAC;KACtE;AAYD,IAAA,WAAA,CACU,QAAiC,EAClC,kBAAqC,EACa,OAAiC,EACjD,KAAkB,EAAA;QAHnD,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAyB;QAClC,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAmB;QACa,IAAO,CAAA,OAAA,GAAP,OAAO,CAA0B;QACjD,IAAK,CAAA,KAAA,GAAL,KAAK,CAAa;QAtDrD,IAAS,CAAA,SAAA,GAAG,KAAK,CAAC;QAClB,IAAO,CAAA,OAAA,GAAG,KAAK,CAAC;QAChB,IAAS,CAAA,SAAA,GAAG,KAAK,CAAC;QAClB,IAAoB,CAAA,oBAAA,GAAG,EAAE,CAAC;;AAgBzB,QAAA,IAAA,CAAA,EAAE,GAAW,CAAA,WAAA,EAAc,gBAAgB,EAAE,EAAE,CAAC;;;AAuBtC,QAAA,IAAA,CAAA,iBAAiB,GAAG,IAAI,YAAY,EAA+B,CAAC;;AAM9E,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,OAAO,EAAQ,CAAC;KAOzC;AAEJ;;;;;AAKG;AACH,IAAA,IAAI,MAAM,GAAA;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;AAED;;;AAGG;AACH,IAAA,IAAI,SAAS,GAAA;;AAEX,QAAA,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,WAAW,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC;KAC7D;;IAGD,MAAM,CAAC,SAAS,GAAG,IAAI,EAAA;AACrB,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACnB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;YAEvC,IAAI,SAAS,EAAE;gBACb,IAAI,CAAC,yBAAyB,EAAE,CAAC;aAClC;SACF;KACF;;IAGD,QAAQ,CAAC,SAAS,GAAG,IAAI,EAAA;AACvB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACvB,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;YAEvC,IAAI,SAAS,EAAE;gBACb,IAAI,CAAC,yBAAyB,EAAE,CAAC;aAClC;SACF;KACF;;IAGD,KAAK,CAAC,OAAqB,EAAE,OAAsB,EAAA;;;AAGjD,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;AAEvC,QAAA,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,UAAU,EAAE;AACvC,YAAA,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;SACxB;KACF;AAED;;;;AAIG;IACH,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACjB,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;SACxC;KACF;AAED;;;;AAIG;IACH,iBAAiB,GAAA;AACf,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACrB,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;SACxC;KACF;;IAGD,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;;AAGD,IAAA,cAAc,CAAC,KAAoB,EAAA;QACjC,IAAI,CAAC,KAAK,CAAC,OAAO,KAAK,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;YAClF,IAAI,CAAC,qBAAqB,EAAE,CAAC;;YAG7B,KAAK,CAAC,cAAc,EAAE,CAAC;SACxB;KACF;AAED;;;AAGG;IACH,qBAAqB,GAAA;AACnB,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACxD,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACvC,YAAA,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;SACtC;KACF;;;;;IAMD,YAAY,GAAA;QACV,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,GAAG,CAAC;KACnC;;IAGD,eAAe,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;KACpC;IAED,kBAAkB,GAAA;;;;;;AAMhB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AAEjC,YAAA,IAAI,SAAS,KAAK,IAAI,CAAC,oBAAoB,EAAE;AAC3C,gBAAA,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC7B,oBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;iBAC3B;AAED,gBAAA,IAAI,CAAC,oBAAoB,GAAG,SAAS,CAAC;aACvC;SACF;KACF;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;KAC/B;;IAGO,yBAAyB,CAAC,WAAW,GAAG,KAAK,EAAA;AACnD,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,wBAAwB,CAAI,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC;KACjF;8GA1MU,SAAS,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAsDE,2BAA2B,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAC3B,YAAY,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAvDvB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAS,qHAuBD,gBAAgB,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,yBAAA,EAAA,SAAA,EAAA,wBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,+BAAA,EAAA,UAAA,EAAA,+BAAA,EAAA,UAAA,EAAA,6BAAA,EAAA,QAAA,EAAA,+BAAA,EAAA,UAAA,EAAA,IAAA,EAAA,IAAA,EAAA,oBAAA,EAAA,UAAA,EAAA,oBAAA,EAAA,qBAAA,EAAA,EAAA,cAAA,EAAA,8BAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,OAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,MAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EC3GrC,49CAkCA,EDgDY,MAAA,EAAA,CAAA,irGAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,iBAAiB,6GAAE,SAAS,EAAA,QAAA,EAAA,2BAAA,EAAA,MAAA,EAAA,CAAA,gBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAE3B,SAAS,EAAA,UAAA,EAAA,CAAA;kBAhCrB,SAAS;+BACE,YAAY,EAAA,QAAA,EACZ,WAAW,EACf,IAAA,EAAA;AACJ,wBAAA,MAAM,EAAE,QAAQ;AAChB,wBAAA,iCAAiC,EAAE,UAAU;AAC7C,wBAAA,iCAAiC,EAAE,UAAU;AAC7C,wBAAA,+BAA+B,EAAE,QAAQ;AACzC,wBAAA,iCAAiC,EAAE,UAAU;AAC7C,wBAAA,MAAM,EAAE,IAAI;;;;;;;;;;AAUZ,wBAAA,sBAAsB,EAAE,UAAU;AAClC,wBAAA,sBAAsB,EAAE,qBAAqB;AAC7C,wBAAA,SAAS,EAAE,yBAAyB;AACpC,wBAAA,WAAW,EAAE,wBAAwB;AACrC,wBAAA,OAAO,EAAE,8BAA8B;AACxC,qBAAA,EAAA,aAAA,EAGc,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,MAAM,EACnC,UAAA,EAAA,IAAI,EACP,OAAA,EAAA,CAAC,iBAAiB,EAAE,SAAS,CAAC,EAAA,QAAA,EAAA,49CAAA,EAAA,MAAA,EAAA,CAAA,irGAAA,CAAA,EAAA,CAAA;;0BAwDpC,QAAQ;;0BAAI,MAAM;2BAAC,2BAA2B,CAAA;;0BAC9C,QAAQ;;0BAAI,MAAM;2BAAC,YAAY,CAAA;yCAtCzB,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAGG,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAIF,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAoBjB,iBAAiB,EAAA,CAAA;sBAAnC,MAAM;gBAG4B,KAAK,EAAA,CAAA;sBAAvC,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,MAAM,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC,CAAA;;AA+JnC;;;;;;AAMG;SACa,6BAA6B,CAC3C,WAAmB,EACnB,OAA6B,EAC7B,YAAoC,EAAA;AAEpC,IAAA,IAAI,YAAY,CAAC,MAAM,EAAE;AACvB,QAAA,IAAI,YAAY,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;AACrC,QAAA,IAAI,MAAM,GAAG,YAAY,CAAC,OAAO,EAAE,CAAC;QACpC,IAAI,YAAY,GAAG,CAAC,CAAC;AAErB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACxC,YAAA,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,YAAY,CAAC,EAAE;AAC3E,gBAAA,YAAY,EAAE,CAAC;aAChB;SACF;AAED,QAAA,OAAO,YAAY,CAAC;KACrB;AAED,IAAA,OAAO,CAAC,CAAC;AACX,CAAC;AAED;;;;;;;AAOG;AACG,SAAU,wBAAwB,CACtC,YAAoB,EACpB,YAAoB,EACpB,qBAA6B,EAC7B,WAAmB,EAAA;AAEnB,IAAA,IAAI,YAAY,GAAG,qBAAqB,EAAE;AACxC,QAAA,OAAO,YAAY,CAAC;KACrB;IAED,IAAI,YAAY,GAAG,YAAY,GAAG,qBAAqB,GAAG,WAAW,EAAE;AACrE,QAAA,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,YAAY,GAAG,WAAW,GAAG,YAAY,CAAC,CAAC;KAC/D;AAED,IAAA,OAAO,qBAAqB,CAAC;AAC/B;;MElUa,eAAe,CAAA;8GAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAf,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,EAHhB,OAAA,EAAA,CAAA,eAAe,EAAE,eAAe,EAAE,uBAAuB,EAAE,SAAS,EAAE,WAAW,CACjF,EAAA,OAAA,EAAA,CAAA,SAAS,EAAE,WAAW,CAAA,EAAA,CAAA,CAAA,EAAA;AAErB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,EAHhB,OAAA,EAAA,CAAA,eAAe,EAAE,eAAe,EAAE,uBAAuB,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAGxD,eAAe,EAAA,UAAA,EAAA,CAAA;kBAJ3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,uBAAuB,EAAE,SAAS,EAAE,WAAW,CAAC;AAC5F,oBAAA,OAAO,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC;AAClC,iBAAA,CAAA;;;ACED;AACA,MAAM,oBAAoB,GAAG,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC;AAE7C;AACA,MAAM,uBAAuB,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;AAE/E;AACA,MAAM,sBAAsB,GAAG,iCAAiC,CAAC;AAEjE;AACA,MAAM,kBAAkB,GAAG,8BAA8B,CAAC;AAE1D;AACA,MAAM,iBAAiB,GAAG,4BAA4B,CAAC;AAEvD;AACA,MAAM,iBAAiB,GAAG,4BAA4B,CAAC;AAEvD;;;;;;;AAOG;MAEU,eAAe,CAAA;AAQ1B,IAAA,WAAA,GAAA;QAPQ,IAAS,CAAA,SAAA,GAAG,MAAM,CAAC,QAAQ,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;QAC/C,IAAc,CAAA,cAAA,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;QACjE,IAAoB,CAAA,oBAAA,GAAG,MAAM,CAAC,yBAAyB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;AAC3E,QAAA,IAAA,CAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC7B,QAAA,IAAA,CAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AACzB,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,GAAG,EAA0B,CAAC;;AAgF3C,QAAA,IAAA,CAAA,cAAc,GAAG,CAAC,KAAY,KAAI;YACxC,IAAI,EAAE,KAAK,CAAC,MAAM,YAAY,WAAW,CAAC,EAAE;gBAC1C,OAAO;aACR;AACD,YAAA,MAAM,WAAW,GAAG,KAAK,CAAC,MAAqB,CAAC;;YAIhD,MAAM,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,CAAI,CAAA,EAAA,sBAAsB,CAAG,CAAA,CAAA,CAAC,CAAC;YACnE,IAAI,OAAO,EAAE;AACX,gBAAA,IAAI,CAAC,aAAa,CAAC,OAAsB,CAAC,CAAC;aAC5C;AACH,SAAC,CAAC;AAzFA,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,KAAK,MAAM,KAAK,IAAI,uBAAuB,EAAE;AAC3C,gBAAA,IAAI,CAAC,SAAS,EAAE,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,oBAAoB,CAAC,CAAC;aACpF;AACH,SAAC,CAAC,CAAC;KACJ;IAED,WAAW,GAAA;QACT,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;AAEjC,QAAA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AACxB,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;SAC1B;AAED,QAAA,KAAK,MAAM,KAAK,IAAI,uBAAuB,EAAE;AAC3C,YAAA,IAAI,CAAC,SAAS,EAAE,mBAAmB,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,oBAAoB,CAAC,CAAC;SACvF;KACF;AAED;;;;;AAKG;IACH,eAAe,CACb,IAAiB,EACjB,MAIC,EAAA;;AAGD,QAAA,IAAI,CAAC,YAAY,CAAC,sBAAsB,EAAE,EAAE,CAAC,CAAC;;AAG9C,QAAA,IAAI,MAAM,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,EAAE;YAC9D,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,MAAM,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC;SAC/D;;AAGD,QAAA,IAAI,MAAM,CAAC,QAAQ,EAAE;AACnB,YAAA,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;SAC1C;AAED,QAAA,IAAI,MAAM,CAAC,QAAQ,EAAE;AACnB,YAAA,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;SAC1C;KACF;;AAGD,IAAA,SAAS,CAAC,IAAiB,EAAA;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACrC,OAAO,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;KAC3C;;IAGD,WAAW,CAAC,IAAiB,EAAE,QAAiB,EAAA;QAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;;QAGrC,IAAI,MAAM,EAAE;AACV,YAAA,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;YAC3B,OAAO;SACR;;;QAID,IAAI,QAAQ,EAAE;AACZ,YAAA,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;SAC1C;aAAM;AACL,YAAA,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;SACzC;KACF;;AAkBO,IAAA,aAAa,CAAC,IAAiB,EAAA;AACrC,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,OAAO;SACR;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC7C,IAAI,cAAc,EAAE;AAClB,YAAA,OAAO,cAAc,CAAC;SACvB;;QAGD,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,EAAE,MAAM,EAAE,CAAC;QAC5C,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AACtD,QAAA,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAE,CAAC,CAAC;AAC7E,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;;QAGtB,MAAM,MAAM,GAAG,IAAI,SAAS,CAC1B,IAAI,UAAU,CAAC,QAAQ,CAAC,EACxB,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,GAAG,SAAS,EACjE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,GAAG,SAAS,CACtD,CAAC;AACF,QAAA,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC;AAC7B,QAAA,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;QACtB,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;QACvD,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;AACvD,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAChC,QAAA,OAAO,MAAM,CAAC;KACf;IAED,YAAY,CAAC,IAAiB,EAAE,MAAiB,EAAA;AAC/C,QAAA,IAAI,CAAC,eAAe,CAAC,sBAAsB,CAAC,CAAC;QAC7C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KAC/B;AAED,IAAA,aAAa,CAAC,IAAiB,EAAA;QAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAErC,IAAI,MAAM,EAAE;;;YAGV,MAAM,CAAC,WAAW,EAAE,CAAC;AACrB,YAAA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SAC1B;KACF;8GAnJU,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAAf,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,cADH,MAAM,EAAA,CAAA,CAAA,EAAA;;2FAClB,eAAe,EAAA,UAAA,EAAA,CAAA;kBAD3B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;;ACpChC;;;;AAIG;MAcU,qBAAqB,CAAA;8GAArB,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAArB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,qBAAqB,2RATtB,2BAA2B,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,o7CAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAS1B,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBAbjC,SAAS;AAEE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,8BAA8B,EAC5B,UAAA,EAAA,IAAI,EACN,QAAA,EAAA,2BAA2B,EAEtB,aAAA,EAAA,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EACzC,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,wCAAwC;AACjD,wBAAA,mCAAmC,EAAE,4BAA4B;AAClE,qBAAA,EAAA,MAAA,EAAA,CAAA,o7CAAA,CAAA,EAAA,CAAA;8BAIwB,aAAa,EAAA,CAAA;sBAArC,KAAK;uBAAC,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAA;;;AC9BzB;;AAEG;;;;"}