/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['qu'] = ["qu",[["a.m.","p.m."],u,u],u,[["D","L","M","X","J","V","S"],["<PERSON>","Lun","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","Sab"],["<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","Mi<PERSON>rcoles","Ju<PERSON>","Viernes","Sábad<PERSON>"],["<PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>b"]],u,[["1","2","3","4","5","6","7","8","9","10","11","12"],["Ene","Feb","Mar","Abr","May","Jun","Jul","Ago","<PERSON>","Oct","Nov","Dic"],["Enero","Feb<PERSON>o","<PERSON>zo","Abril","Mayo","<PERSON><PERSON>","<PERSON>","A<PERSON>to","<PERSON>iembre","Octubre","<PERSON>embre","<PERSON>ciembre"]],u,[["a.d.","dC"],["a.d.","d.C."],["ñawpa cristu","chanta cristu"]],0,[6,0],["dd/MM/y","d MMM y","d MMMM y","EEEE, d MMMM, y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,"{0} {1}","{1} {0}"],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0 %","¤ #,##0.00","#E0"],"PEN","S/","Sol Peruano",{"BBD":["BBG","$"],"BMD":["DBM","$"],"BZD":["DBZ","$"],"CAD":["$CA","$"],"GHS":[u,"GHC"],"JPY":["JP¥","¥"],"PEN":["S/"],"PHP":[u,"₱"],"USD":["$US","$"]},"ltr", plural, []];
  })(globalThis);
    