/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// Assembles directive details string, useful for error messages.
export function imgDirectiveDetails(ngSrc, includeNgSrc = true) {
    const ngSrcInfo = includeNgSrc
        ? `(activated on an <img> element with the \`ngSrc="${ngSrc}"\`) `
        : '';
    return `The NgOptimizedImage directive ${ngSrcInfo}has detected that`;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZXJyb3JfaGVscGVyLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vLi4vLi4vLi4vcGFja2FnZXMvY29tbW9uL3NyYy9kaXJlY3RpdmVzL25nX29wdGltaXplZF9pbWFnZS9lcnJvcl9oZWxwZXIudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7OztHQU1HO0FBRUgsaUVBQWlFO0FBQ2pFLE1BQU0sVUFBVSxtQkFBbUIsQ0FBQyxLQUFhLEVBQUUsWUFBWSxHQUFHLElBQUk7SUFDcEUsTUFBTSxTQUFTLEdBQUcsWUFBWTtRQUM1QixDQUFDLENBQUMsb0RBQW9ELEtBQUssT0FBTztRQUNsRSxDQUFDLENBQUMsRUFBRSxDQUFDO0lBQ1AsT0FBTyxrQ0FBa0MsU0FBUyxtQkFBbUIsQ0FBQztBQUN4RSxDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5cbi8vIEFzc2VtYmxlcyBkaXJlY3RpdmUgZGV0YWlscyBzdHJpbmcsIHVzZWZ1bCBmb3IgZXJyb3IgbWVzc2FnZXMuXG5leHBvcnQgZnVuY3Rpb24gaW1nRGlyZWN0aXZlRGV0YWlscyhuZ1NyYzogc3RyaW5nLCBpbmNsdWRlTmdTcmMgPSB0cnVlKSB7XG4gIGNvbnN0IG5nU3JjSW5mbyA9IGluY2x1ZGVOZ1NyY1xuICAgID8gYChhY3RpdmF0ZWQgb24gYW4gPGltZz4gZWxlbWVudCB3aXRoIHRoZSBcXGBuZ1NyYz1cIiR7bmdTcmN9XCJcXGApIGBcbiAgICA6ICcnO1xuICByZXR1cm4gYFRoZSBOZ09wdGltaXplZEltYWdlIGRpcmVjdGl2ZSAke25nU3JjSW5mb31oYXMgZGV0ZWN0ZWQgdGhhdGA7XG59XG4iXX0=