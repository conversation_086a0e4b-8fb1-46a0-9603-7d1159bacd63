/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * Describes the state of defer block dependency loading.
 */
export var DeferDependenciesLoadingState;
(function (DeferDependenciesLoadingState) {
    /** Initial state, dependency loading is not yet triggered */
    DeferDependenciesLoadingState[DeferDependenciesLoadingState["NOT_STARTED"] = 0] = "NOT_STARTED";
    /** Dependency loading is in progress */
    DeferDependenciesLoadingState[DeferDependenciesLoadingState["IN_PROGRESS"] = 1] = "IN_PROGRESS";
    /** Dependency loading has completed successfully */
    DeferDependenciesLoadingState[DeferDependenciesLoadingState["COMPLETE"] = 2] = "COMPLETE";
    /** Dependency loading has failed */
    DeferDependenciesLoadingState[DeferDependenciesLoadingState["FAILED"] = 3] = "FAILED";
})(DeferDependenciesLoadingState || (DeferDependenciesLoadingState = {}));
/** Slot index where `minimum` parameter value is stored. */
export const MINIMUM_SLOT = 0;
/** Slot index where `after` parameter value is stored. */
export const LOADING_AFTER_SLOT = 1;
/**
 * Describes the current state of this defer block instance.
 *
 * @publicApi
 * @developerPreview
 */
export var DeferBlockState;
(function (DeferBlockState) {
    /** The placeholder block content is rendered */
    DeferBlockState[DeferBlockState["Placeholder"] = 0] = "Placeholder";
    /** The loading block content is rendered */
    DeferBlockState[DeferBlockState["Loading"] = 1] = "Loading";
    /** The main content block content is rendered */
    DeferBlockState[DeferBlockState["Complete"] = 2] = "Complete";
    /** The error block content is rendered */
    DeferBlockState[DeferBlockState["Error"] = 3] = "Error";
})(DeferBlockState || (DeferBlockState = {}));
/**
 * Describes the initial state of this defer block instance.
 *
 * Note: this state is internal only and *must* be represented
 * with a number lower than any value in the `DeferBlockState` enum.
 */
export var DeferBlockInternalState;
(function (DeferBlockInternalState) {
    /** Initial state. Nothing is rendered yet. */
    DeferBlockInternalState[DeferBlockInternalState["Initial"] = -1] = "Initial";
})(DeferBlockInternalState || (DeferBlockInternalState = {}));
export const NEXT_DEFER_BLOCK_STATE = 0;
// Note: it's *important* to keep the state in this slot, because this slot
// is used by runtime logic to differentiate between LViews, LContainers and
// other types (see `isLView` and `isLContainer` functions). In case of defer
// blocks, this slot would always be a number.
export const DEFER_BLOCK_STATE = 1;
export const STATE_IS_FROZEN_UNTIL = 2;
export const LOADING_AFTER_CLEANUP_FN = 3;
export const TRIGGER_CLEANUP_FNS = 4;
export const PREFETCH_TRIGGER_CLEANUP_FNS = 5;
/**
 * Options for configuring defer blocks behavior.
 * @publicApi
 * @developerPreview
 */
export var DeferBlockBehavior;
(function (DeferBlockBehavior) {
    /**
     * Manual triggering mode for defer blocks. Provides control over when defer blocks render
     * and which state they render.
     */
    DeferBlockBehavior[DeferBlockBehavior["Manual"] = 0] = "Manual";
    /**
     * Playthrough mode for defer blocks. This mode behaves like defer blocks would in a browser.
     * This is the default behavior in test environments.
     */
    DeferBlockBehavior[DeferBlockBehavior["Playthrough"] = 1] = "Playthrough";
})(DeferBlockBehavior || (DeferBlockBehavior = {}));
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW50ZXJmYWNlcy5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uLy4uL3BhY2thZ2VzL2NvcmUvc3JjL2RlZmVyL2ludGVyZmFjZXMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7OztHQU1HO0FBMEJIOztHQUVHO0FBQ0gsTUFBTSxDQUFOLElBQVksNkJBWVg7QUFaRCxXQUFZLDZCQUE2QjtJQUN2Qyw2REFBNkQ7SUFDN0QsK0ZBQVcsQ0FBQTtJQUVYLHdDQUF3QztJQUN4QywrRkFBVyxDQUFBO0lBRVgsb0RBQW9EO0lBQ3BELHlGQUFRLENBQUE7SUFFUixvQ0FBb0M7SUFDcEMscUZBQU0sQ0FBQTtBQUNSLENBQUMsRUFaVyw2QkFBNkIsS0FBN0IsNkJBQTZCLFFBWXhDO0FBRUQsNERBQTREO0FBQzVELE1BQU0sQ0FBQyxNQUFNLFlBQVksR0FBRyxDQUFDLENBQUM7QUFFOUIsMERBQTBEO0FBQzFELE1BQU0sQ0FBQyxNQUFNLGtCQUFrQixHQUFHLENBQUMsQ0FBQztBQW1FcEM7Ozs7O0dBS0c7QUFDSCxNQUFNLENBQU4sSUFBWSxlQVlYO0FBWkQsV0FBWSxlQUFlO0lBQ3pCLGdEQUFnRDtJQUNoRCxtRUFBZSxDQUFBO0lBRWYsNENBQTRDO0lBQzVDLDJEQUFXLENBQUE7SUFFWCxpREFBaUQ7SUFDakQsNkRBQVksQ0FBQTtJQUVaLDBDQUEwQztJQUMxQyx1REFBUyxDQUFBO0FBQ1gsQ0FBQyxFQVpXLGVBQWUsS0FBZixlQUFlLFFBWTFCO0FBRUQ7Ozs7O0dBS0c7QUFDSCxNQUFNLENBQU4sSUFBWSx1QkFHWDtBQUhELFdBQVksdUJBQXVCO0lBQ2pDLDhDQUE4QztJQUM5Qyw0RUFBWSxDQUFBO0FBQ2QsQ0FBQyxFQUhXLHVCQUF1QixLQUF2Qix1QkFBdUIsUUFHbEM7QUFFRCxNQUFNLENBQUMsTUFBTSxzQkFBc0IsR0FBRyxDQUFDLENBQUM7QUFDeEMsMkVBQTJFO0FBQzNFLDRFQUE0RTtBQUM1RSw2RUFBNkU7QUFDN0UsOENBQThDO0FBQzlDLE1BQU0sQ0FBQyxNQUFNLGlCQUFpQixHQUFHLENBQUMsQ0FBQztBQUNuQyxNQUFNLENBQUMsTUFBTSxxQkFBcUIsR0FBRyxDQUFDLENBQUM7QUFDdkMsTUFBTSxDQUFDLE1BQU0sd0JBQXdCLEdBQUcsQ0FBQyxDQUFDO0FBQzFDLE1BQU0sQ0FBQyxNQUFNLG1CQUFtQixHQUFHLENBQUMsQ0FBQztBQUNyQyxNQUFNLENBQUMsTUFBTSw0QkFBNEIsR0FBRyxDQUFDLENBQUM7QUFtRDlDOzs7O0dBSUc7QUFDSCxNQUFNLENBQU4sSUFBWSxrQkFZWDtBQVpELFdBQVksa0JBQWtCO0lBQzVCOzs7T0FHRztJQUNILCtEQUFNLENBQUE7SUFFTjs7O09BR0c7SUFDSCx5RUFBVyxDQUFBO0FBQ2IsQ0FBQyxFQVpXLGtCQUFrQixLQUFsQixrQkFBa0IsUUFZN0IiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgR29vZ2xlIExMQyBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICpcbiAqIFVzZSBvZiB0aGlzIHNvdXJjZSBjb2RlIGlzIGdvdmVybmVkIGJ5IGFuIE1JVC1zdHlsZSBsaWNlbnNlIHRoYXQgY2FuIGJlXG4gKiBmb3VuZCBpbiB0aGUgTElDRU5TRSBmaWxlIGF0IGh0dHBzOi8vYW5ndWxhci5pby9saWNlbnNlXG4gKi9cblxuaW1wb3J0IHR5cGUge1Byb3ZpZGVyfSBmcm9tICcuLi9kaS9pbnRlcmZhY2UvcHJvdmlkZXInO1xuaW1wb3J0IHR5cGUge0RlcGVuZGVuY3lUeXBlfSBmcm9tICcuLi9yZW5kZXIzL2ludGVyZmFjZXMvZGVmaW5pdGlvbic7XG5cbi8qKlxuICogRGVzY3JpYmVzIHRoZSBzaGFwZSBvZiBhIGZ1bmN0aW9uIGdlbmVyYXRlZCBieSB0aGUgY29tcGlsZXJcbiAqIHRvIGRvd25sb2FkIGRlcGVuZGVuY2llcyB0aGF0IGNhbiBiZSBkZWZlci1sb2FkZWQuXG4gKi9cbmV4cG9ydCB0eXBlIERlcGVuZGVuY3lSZXNvbHZlckZuID0gKCkgPT4gQXJyYXk8UHJvbWlzZTxEZXBlbmRlbmN5VHlwZT4+O1xuXG4vKipcbiAqIERlZmluZXMgdHlwZXMgb2YgZGVmZXIgYmxvY2sgdHJpZ2dlcnMuXG4gKi9cbmV4cG9ydCBjb25zdCBlbnVtIFRyaWdnZXJUeXBlIHtcbiAgLyoqXG4gICAqIFJlcHJlc2VudHMgcmVndWxhciB0cmlnZ2VycyAoZS5nLiBgQGRlZmVyIChvbiBpZGxlKSB7IC4uLiB9YCkuXG4gICAqL1xuICBSZWd1bGFyLFxuXG4gIC8qKlxuICAgKiBSZXByZXNlbnRzIHByZWZldGNoIHRyaWdnZXJzIChlLmcuIGBAZGVmZXIgKHByZWZldGNoIG9uIGlkbGUpIHsgLi4uIH1gKS5cbiAgICovXG4gIFByZWZldGNoLFxufVxuXG4vKipcbiAqIERlc2NyaWJlcyB0aGUgc3RhdGUgb2YgZGVmZXIgYmxvY2sgZGVwZW5kZW5jeSBsb2FkaW5nLlxuICovXG5leHBvcnQgZW51bSBEZWZlckRlcGVuZGVuY2llc0xvYWRpbmdTdGF0ZSB7XG4gIC8qKiBJbml0aWFsIHN0YXRlLCBkZXBlbmRlbmN5IGxvYWRpbmcgaXMgbm90IHlldCB0cmlnZ2VyZWQgKi9cbiAgTk9UX1NUQVJURUQsXG5cbiAgLyoqIERlcGVuZGVuY3kgbG9hZGluZyBpcyBpbiBwcm9ncmVzcyAqL1xuICBJTl9QUk9HUkVTUyxcblxuICAvKiogRGVwZW5kZW5jeSBsb2FkaW5nIGhhcyBjb21wbGV0ZWQgc3VjY2Vzc2Z1bGx5ICovXG4gIENPTVBMRVRFLFxuXG4gIC8qKiBEZXBlbmRlbmN5IGxvYWRpbmcgaGFzIGZhaWxlZCAqL1xuICBGQUlMRUQsXG59XG5cbi8qKiBTbG90IGluZGV4IHdoZXJlIGBtaW5pbXVtYCBwYXJhbWV0ZXIgdmFsdWUgaXMgc3RvcmVkLiAqL1xuZXhwb3J0IGNvbnN0IE1JTklNVU1fU0xPVCA9IDA7XG5cbi8qKiBTbG90IGluZGV4IHdoZXJlIGBhZnRlcmAgcGFyYW1ldGVyIHZhbHVlIGlzIHN0b3JlZC4gKi9cbmV4cG9ydCBjb25zdCBMT0FESU5HX0FGVEVSX1NMT1QgPSAxO1xuXG4vKiogQ29uZmlndXJhdGlvbiBvYmplY3QgZm9yIGEgbG9hZGluZyBibG9jayBhcyBpdCBpcyBzdG9yZWQgaW4gdGhlIGNvbXBvbmVudCBjb25zdGFudHMuICovXG5leHBvcnQgdHlwZSBEZWZlcnJlZExvYWRpbmdCbG9ja0NvbmZpZyA9IFttaW5pbXVtVGltZTogbnVtYmVyfG51bGwsIGFmdGVyVGltZTogbnVtYmVyfG51bGxdO1xuXG4vKiogQ29uZmlndXJhdGlvbiBvYmplY3QgZm9yIGEgcGxhY2Vob2xkZXIgYmxvY2sgYXMgaXQgaXMgc3RvcmVkIGluIHRoZSBjb21wb25lbnQgY29uc3RhbnRzLiAqL1xuZXhwb3J0IHR5cGUgRGVmZXJyZWRQbGFjZWhvbGRlckJsb2NrQ29uZmlnID0gW21pbmltdW1UaW1lOiBudW1iZXJ8bnVsbF07XG5cbi8qKlxuICogRGVzY3JpYmVzIHRoZSBkYXRhIHNoYXJlZCBhY3Jvc3MgYWxsIGluc3RhbmNlcyBvZiBhIGRlZmVyIGJsb2NrLlxuICovXG5leHBvcnQgaW50ZXJmYWNlIFREZWZlckJsb2NrRGV0YWlscyB7XG4gIC8qKlxuICAgKiBJbmRleCBpbiBhbiBMVmlldyBhbmQgVERhdGEgYXJyYXlzIHdoZXJlIGEgdGVtcGxhdGUgZm9yIHRoZSBwcmltYXJ5IGNvbnRlbnRcbiAgICogY2FuIGJlIGZvdW5kLlxuICAgKi9cbiAgcHJpbWFyeVRtcGxJbmRleDogbnVtYmVyO1xuXG4gIC8qKlxuICAgKiBJbmRleCBpbiBhbiBMVmlldyBhbmQgVERhdGEgYXJyYXlzIHdoZXJlIGEgdGVtcGxhdGUgZm9yIHRoZSBsb2FkaW5nIGJsb2NrIGNhbiBiZSBmb3VuZC5cbiAgICovXG4gIGxvYWRpbmdUbXBsSW5kZXg6IG51bWJlcnxudWxsO1xuXG4gIC8qKlxuICAgKiBFeHRyYSBjb25maWd1cmF0aW9uIHBhcmFtZXRlcnMgKHN1Y2ggYXMgYGFmdGVyYCBhbmQgYG1pbmltdW1gKSBmb3IgdGhlIGxvYWRpbmcgYmxvY2suXG4gICAqL1xuICBsb2FkaW5nQmxvY2tDb25maWc6IERlZmVycmVkTG9hZGluZ0Jsb2NrQ29uZmlnfG51bGw7XG5cbiAgLyoqXG4gICAqIEluZGV4IGluIGFuIExWaWV3IGFuZCBURGF0YSBhcnJheXMgd2hlcmUgYSB0ZW1wbGF0ZSBmb3IgdGhlIHBsYWNlaG9sZGVyIGJsb2NrIGNhbiBiZSBmb3VuZC5cbiAgICovXG4gIHBsYWNlaG9sZGVyVG1wbEluZGV4OiBudW1iZXJ8bnVsbDtcblxuICAvKipcbiAgICogRXh0cmEgY29uZmlndXJhdGlvbiBwYXJhbWV0ZXJzIChzdWNoIGFzIGBhZnRlcmAgYW5kIGBtaW5pbXVtYCkgZm9yIHRoZSBwbGFjZWhvbGRlciBibG9jay5cbiAgICovXG4gIHBsYWNlaG9sZGVyQmxvY2tDb25maWc6IERlZmVycmVkUGxhY2Vob2xkZXJCbG9ja0NvbmZpZ3xudWxsO1xuXG4gIC8qKlxuICAgKiBJbmRleCBpbiBhbiBMVmlldyBhbmQgVERhdGEgYXJyYXlzIHdoZXJlIGEgdGVtcGxhdGUgZm9yIHRoZSBlcnJvciBibG9jayBjYW4gYmUgZm91bmQuXG4gICAqL1xuICBlcnJvclRtcGxJbmRleDogbnVtYmVyfG51bGw7XG5cbiAgLyoqXG4gICAqIENvbXBpbGVyLWdlbmVyYXRlZCBmdW5jdGlvbiB0aGF0IGxvYWRzIGFsbCBkZXBlbmRlbmNpZXMgZm9yIGEgZGVmZXIgYmxvY2suXG4gICAqL1xuICBkZXBlbmRlbmN5UmVzb2x2ZXJGbjogRGVwZW5kZW5jeVJlc29sdmVyRm58bnVsbDtcblxuICAvKipcbiAgICogS2VlcHMgdHJhY2sgb2YgdGhlIGN1cnJlbnQgbG9hZGluZyBzdGF0ZSBvZiBkZWZlciBibG9jayBkZXBlbmRlbmNpZXMuXG4gICAqL1xuICBsb2FkaW5nU3RhdGU6IERlZmVyRGVwZW5kZW5jaWVzTG9hZGluZ1N0YXRlO1xuXG4gIC8qKlxuICAgKiBEZXBlbmRlbmN5IGxvYWRpbmcgUHJvbWlzZS4gVGhpcyBQcm9taXNlIGlzIGhlbHBmdWwgZm9yIGNhc2VzIHdoZW4gdGhlcmVcbiAgICogYXJlIG11bHRpcGxlIGluc3RhbmNlcyBvZiBhIGRlZmVyIGJsb2NrIChlLmcuIGlmIGl0IHdhcyB1c2VkIGluc2lkZSBvZiBhbiAqbmdGb3IpLFxuICAgKiB3aGljaCBhbGwgYXdhaXQgdGhlIHNhbWUgc2V0IG9mIGRlcGVuZGVuY2llcy5cbiAgICovXG4gIGxvYWRpbmdQcm9taXNlOiBQcm9taXNlPHVua25vd24+fG51bGw7XG5cbiAgLyoqXG4gICAqIExpc3Qgb2YgcHJvdmlkZXJzIGNvbGxlY3RlZCBmcm9tIGFsbCBOZ01vZHVsZXMgdGhhdCB3ZXJlIGltcG9ydGVkIGJ5XG4gICAqIHN0YW5kYWxvbmUgY29tcG9uZW50cyB1c2VkIHdpdGhpbiB0aGlzIGRlZmVyIGJsb2NrLlxuICAgKi9cbiAgcHJvdmlkZXJzOiBQcm92aWRlcltdfG51bGw7XG59XG5cbi8qKlxuICogRGVzY3JpYmVzIHRoZSBjdXJyZW50IHN0YXRlIG9mIHRoaXMgZGVmZXIgYmxvY2sgaW5zdGFuY2UuXG4gKlxuICogQHB1YmxpY0FwaVxuICogQGRldmVsb3BlclByZXZpZXdcbiAqL1xuZXhwb3J0IGVudW0gRGVmZXJCbG9ja1N0YXRlIHtcbiAgLyoqIFRoZSBwbGFjZWhvbGRlciBibG9jayBjb250ZW50IGlzIHJlbmRlcmVkICovXG4gIFBsYWNlaG9sZGVyID0gMCxcblxuICAvKiogVGhlIGxvYWRpbmcgYmxvY2sgY29udGVudCBpcyByZW5kZXJlZCAqL1xuICBMb2FkaW5nID0gMSxcblxuICAvKiogVGhlIG1haW4gY29udGVudCBibG9jayBjb250ZW50IGlzIHJlbmRlcmVkICovXG4gIENvbXBsZXRlID0gMixcblxuICAvKiogVGhlIGVycm9yIGJsb2NrIGNvbnRlbnQgaXMgcmVuZGVyZWQgKi9cbiAgRXJyb3IgPSAzLFxufVxuXG4vKipcbiAqIERlc2NyaWJlcyB0aGUgaW5pdGlhbCBzdGF0ZSBvZiB0aGlzIGRlZmVyIGJsb2NrIGluc3RhbmNlLlxuICpcbiAqIE5vdGU6IHRoaXMgc3RhdGUgaXMgaW50ZXJuYWwgb25seSBhbmQgKm11c3QqIGJlIHJlcHJlc2VudGVkXG4gKiB3aXRoIGEgbnVtYmVyIGxvd2VyIHRoYW4gYW55IHZhbHVlIGluIHRoZSBgRGVmZXJCbG9ja1N0YXRlYCBlbnVtLlxuICovXG5leHBvcnQgZW51bSBEZWZlckJsb2NrSW50ZXJuYWxTdGF0ZSB7XG4gIC8qKiBJbml0aWFsIHN0YXRlLiBOb3RoaW5nIGlzIHJlbmRlcmVkIHlldC4gKi9cbiAgSW5pdGlhbCA9IC0xLFxufVxuXG5leHBvcnQgY29uc3QgTkVYVF9ERUZFUl9CTE9DS19TVEFURSA9IDA7XG4vLyBOb3RlOiBpdCdzICppbXBvcnRhbnQqIHRvIGtlZXAgdGhlIHN0YXRlIGluIHRoaXMgc2xvdCwgYmVjYXVzZSB0aGlzIHNsb3Rcbi8vIGlzIHVzZWQgYnkgcnVudGltZSBsb2dpYyB0byBkaWZmZXJlbnRpYXRlIGJldHdlZW4gTFZpZXdzLCBMQ29udGFpbmVycyBhbmRcbi8vIG90aGVyIHR5cGVzIChzZWUgYGlzTFZpZXdgIGFuZCBgaXNMQ29udGFpbmVyYCBmdW5jdGlvbnMpLiBJbiBjYXNlIG9mIGRlZmVyXG4vLyBibG9ja3MsIHRoaXMgc2xvdCB3b3VsZCBhbHdheXMgYmUgYSBudW1iZXIuXG5leHBvcnQgY29uc3QgREVGRVJfQkxPQ0tfU1RBVEUgPSAxO1xuZXhwb3J0IGNvbnN0IFNUQVRFX0lTX0ZST1pFTl9VTlRJTCA9IDI7XG5leHBvcnQgY29uc3QgTE9BRElOR19BRlRFUl9DTEVBTlVQX0ZOID0gMztcbmV4cG9ydCBjb25zdCBUUklHR0VSX0NMRUFOVVBfRk5TID0gNDtcbmV4cG9ydCBjb25zdCBQUkVGRVRDSF9UUklHR0VSX0NMRUFOVVBfRk5TID0gNTtcblxuLyoqXG4gKiBEZXNjcmliZXMgaW5zdGFuY2Utc3BlY2lmaWMgZGVmZXIgYmxvY2sgZGF0YS5cbiAqXG4gKiBOb3RlOiBjdXJyZW50bHkgdGhlcmUgaXMgb25seSB0aGUgYHN0YXRlYCBzbG90LCBidXQgbW9yZSBzbG90c1xuICogd291bGQgYmUgYWRkZWQgbGF0ZXIgdG8ga2VlcCB0cmFjayBvZiBgYWZ0ZXJgIGFuZCBgbWF4aW11bWAgZmVhdHVyZXNcbiAqICh3aGljaCB3b3VsZCByZXF1aXJlIHBlci1pbnN0YW5jZSBzdGF0ZSkuXG4gKi9cbmV4cG9ydCBpbnRlcmZhY2UgTERlZmVyQmxvY2tEZXRhaWxzIGV4dGVuZHMgQXJyYXk8dW5rbm93bj4ge1xuICAvKipcbiAgICogQ3VycmVudGx5IHJlbmRlcmVkIGJsb2NrIHN0YXRlLlxuICAgKi9cbiAgW0RFRkVSX0JMT0NLX1NUQVRFXTogRGVmZXJCbG9ja1N0YXRlfERlZmVyQmxvY2tJbnRlcm5hbFN0YXRlO1xuXG4gIC8qKlxuICAgKiBCbG9jayBzdGF0ZSB0aGF0IHdhcyByZXF1ZXN0ZWQgd2hlbiBhbm90aGVyIHN0YXRlIHdhcyByZW5kZXJlZC5cbiAgICovXG4gIFtORVhUX0RFRkVSX0JMT0NLX1NUQVRFXTogRGVmZXJCbG9ja1N0YXRlfG51bGw7XG5cbiAgLyoqXG4gICAqIFRpbWVzdGFtcCBpbmRpY2F0aW5nIHdoZW4gdGhlIGN1cnJlbnQgc3RhdGUgY2FuIGJlIHN3aXRjaGVkIHRvXG4gICAqIHRoZSBuZXh0IG9uZSwgaW4gY2FzZSB0ZWggY3VycmVudCBzdGF0ZSBoYXMgYG1pbmltdW1gIHBhcmFtZXRlci5cbiAgICovXG4gIFtTVEFURV9JU19GUk9aRU5fVU5USUxdOiBudW1iZXJ8bnVsbDtcblxuICAvKipcbiAgICogQ29udGFpbnMgYSByZWZlcmVuY2UgdG8gYSBjbGVhbnVwIGZ1bmN0aW9uIHdoaWNoIGNhbmNlbHMgYSB0aW1lb3V0XG4gICAqIHdoZW4gQW5ndWxhciB3YWl0cyBiZWZvcmUgcmVuZGVyaW5nIGxvYWRpbmcgc3RhdGUuIFRoaXMgaXMgdXNlZCB3aGVuXG4gICAqIHRoZSBsb2FkaW5nIGJsb2NrIGhhcyB0aGUgYGFmdGVyYCBwYXJhbWV0ZXIgY29uZmlndXJlZC5cbiAgICovXG4gIFtMT0FESU5HX0FGVEVSX0NMRUFOVVBfRk5dOiBWb2lkRnVuY3Rpb258bnVsbDtcblxuICAvKipcbiAgICogTGlzdCBvZiBjbGVhbnVwIGZ1bmN0aW9ucyBmb3IgcmVndWxhciB0cmlnZ2Vycy5cbiAgICovXG4gIFtUUklHR0VSX0NMRUFOVVBfRk5TXTogVm9pZEZ1bmN0aW9uW118bnVsbDtcblxuICAvKipcbiAgICogTGlzdCBvZiBjbGVhbnVwIGZ1bmN0aW9ucyBmb3IgcHJlZmV0Y2ggdHJpZ2dlcnMuXG4gICAqL1xuICBbUFJFRkVUQ0hfVFJJR0dFUl9DTEVBTlVQX0ZOU106IFZvaWRGdW5jdGlvbltdfG51bGw7XG59XG5cbi8qKlxuICogSW50ZXJuYWwgc3RydWN0dXJlIHVzZWQgZm9yIGNvbmZpZ3VyYXRpb24gb2YgZGVmZXIgYmxvY2sgYmVoYXZpb3IuXG4gKiAqL1xuZXhwb3J0IGludGVyZmFjZSBEZWZlckJsb2NrQ29uZmlnIHtcbiAgYmVoYXZpb3I6IERlZmVyQmxvY2tCZWhhdmlvcjtcbn1cblxuLyoqXG4gKiBPcHRpb25zIGZvciBjb25maWd1cmluZyBkZWZlciBibG9ja3MgYmVoYXZpb3IuXG4gKiBAcHVibGljQXBpXG4gKiBAZGV2ZWxvcGVyUHJldmlld1xuICovXG5leHBvcnQgZW51bSBEZWZlckJsb2NrQmVoYXZpb3Ige1xuICAvKipcbiAgICogTWFudWFsIHRyaWdnZXJpbmcgbW9kZSBmb3IgZGVmZXIgYmxvY2tzLiBQcm92aWRlcyBjb250cm9sIG92ZXIgd2hlbiBkZWZlciBibG9ja3MgcmVuZGVyXG4gICAqIGFuZCB3aGljaCBzdGF0ZSB0aGV5IHJlbmRlci5cbiAgICovXG4gIE1hbnVhbCxcblxuICAvKipcbiAgICogUGxheXRocm91Z2ggbW9kZSBmb3IgZGVmZXIgYmxvY2tzLiBUaGlzIG1vZGUgYmVoYXZlcyBsaWtlIGRlZmVyIGJsb2NrcyB3b3VsZCBpbiBhIGJyb3dzZXIuXG4gICAqIFRoaXMgaXMgdGhlIGRlZmF1bHQgYmVoYXZpb3IgaW4gdGVzdCBlbnZpcm9ubWVudHMuXG4gICAqL1xuICBQbGF5dGhyb3VnaCxcbn1cblxuLyoqXG4gKiAqKklOVEVSTkFMKiosIGF2b2lkIHJlZmVyZW5jaW5nIGl0IGluIGFwcGxpY2F0aW9uIGNvZGUuXG4gKlxuICogRGVzY3JpYmVzIGEgaGVscGVyIGNsYXNzIHRoYXQgYWxsb3dzIHRvIGludGVyY2VwdCBhIGNhbGwgdG8gcmV0cmlldmUgY3VycmVudFxuICogZGVwZW5kZW5jeSBsb2FkaW5nIGZ1bmN0aW9uIGFuZCByZXBsYWNlIGl0IHdpdGggYSBkaWZmZXJlbnQgaW1wbGVtZW50YXRpb24uXG4gKiBUaGlzIGludGVyY2VwdG9yIGNsYXNzIGlzIG5lZWRlZCB0byBhbGxvdyB0ZXN0aW5nIGJsb2NrcyBpbiBkaWZmZXJlbnQgc3RhdGVzXG4gKiBieSBzaW11bGF0aW5nIGxvYWRpbmcgcmVzcG9uc2UuXG4gKi9cbmV4cG9ydCBpbnRlcmZhY2UgRGVmZXJCbG9ja0RlcGVuZGVuY3lJbnRlcmNlcHRvciB7XG4gIC8qKlxuICAgKiBJbnZva2VkIGZvciBlYWNoIGRlZmVyIGJsb2NrIHdoZW4gZGVwZW5kZW5jeSBsb2FkaW5nIGZ1bmN0aW9uIGlzIGFjY2Vzc2VkLlxuICAgKi9cbiAgaW50ZXJjZXB0KGRlcGVuZGVuY3lGbjogRGVwZW5kZW5jeVJlc29sdmVyRm58bnVsbCk6IERlcGVuZGVuY3lSZXNvbHZlckZufG51bGw7XG5cbiAgLyoqXG4gICAqIEFsbG93cyB0byBjb25maWd1cmUgYW4gaW50ZXJjZXB0b3IgZnVuY3Rpb24uXG4gICAqL1xuICBzZXRJbnRlcmNlcHRvcihpbnRlcmNlcHRvckZuOiAoY3VycmVudDogRGVwZW5kZW5jeVJlc29sdmVyRm4pID0+IERlcGVuZGVuY3lSZXNvbHZlckZuKTogdm9pZDtcbn1cbiJdfQ==