/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { InjectionToken } from '@angular/core';
/**
 * In SSR scenarios, a preload `<link>` element is generated for priority images.
 * Having a large number of preload tags may negatively affect the performance,
 * so we warn developers (by throwing an error) if the number of preloaded images
 * is above a certain threshold. This const specifies this threshold.
 */
export const DEFAULT_PRELOADED_IMAGES_LIMIT = 5;
/**
 * Helps to keep track of priority images that already have a corresponding
 * preload tag (to avoid generating multiple preload tags with the same URL).
 *
 * This Set tracks the original src passed into the `ngSrc` input not the src after it has been
 * run through the specified `IMAGE_LOADER`.
 */
export const PRELOADED_IMAGES = new InjectionToken('NG_OPTIMIZED_PRELOADED_IMAGES', {
    providedIn: 'root',
    factory: () => new Set(),
});
//# sourceMappingURL=data:application/json;base64,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