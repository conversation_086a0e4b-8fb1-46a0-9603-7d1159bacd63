/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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