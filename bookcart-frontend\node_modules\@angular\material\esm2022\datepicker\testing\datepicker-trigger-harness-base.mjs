/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { ComponentHarness, parallel } from '@angular/cdk/testing';
import { MatCalendarHarness } from './calendar-harness';
/** Base class for harnesses that can trigger a calendar. */
export class DatepickerTriggerHarnessBase extends ComponentHarness {
    /** Opens the calendar if the trigger is enabled and it has a calendar. */
    async openCalendar() {
        const [isDisabled, hasCalendar] = await parallel(() => [this.isDisabled(), this.hasCalendar()]);
        if (!isDisabled && hasCalendar) {
            return this._openCalendar();
        }
    }
    /** Closes the calendar if it is open. */
    async closeCalendar() {
        if (await this.isCalendarOpen()) {
            await closeCalendar(getCalendarId(this.host()), this.documentRootLocatorFactory());
            // This is necessary so that we wait for the closing animation to finish in touch UI mode.
            await this.forceStabilize();
        }
    }
    /** Gets whether there is a calendar associated with the trigger. */
    async hasCalendar() {
        return (await getCalendarId(this.host())) != null;
    }
    /**
     * Gets the `MatCalendarHarness` that is associated with the trigger.
     * @param filter Optionally filters which calendar is included.
     */
    async getCalendar(filter = {}) {
        return getCalendar(filter, this.host(), this.documentRootLocatorFactory());
    }
}
/** Gets the ID of the calendar that a particular test element can trigger. */
export async function getCalendarId(host) {
    return (await host).getAttribute('data-mat-calendar');
}
/** Closes the calendar with a specific ID. */
export async function closeCalendar(calendarId, documentLocator) {
    // We close the calendar by clicking on the backdrop, even though all datepicker variants
    // have the ability to close by pressing escape. The backdrop is preferrable, because the
    // escape key has multiple functions inside a range picker (either cancel the current range
    // or close the calendar). Since we don't have access to set the ID on the backdrop in all
    // cases, we set a unique class instead which is the same as the calendar's ID and suffixed
    // with `-backdrop`.
    const backdropSelector = `.${await calendarId}-backdrop`;
    return (await documentLocator.locatorFor(backdropSelector)()).click();
}
/** Gets the test harness for a calendar associated with a particular host. */
export async function getCalendar(filter, host, documentLocator) {
    const calendarId = await getCalendarId(host);
    if (!calendarId) {
        throw Error(`Element is not associated with a calendar`);
    }
    return documentLocator.locatorFor(MatCalendarHarness.with({
        ...filter,
        selector: `#${calendarId}`,
    }))();
}
//# sourceMappingURL=data:application/json;base64,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