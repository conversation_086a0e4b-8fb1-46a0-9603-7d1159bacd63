/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import * as ir from '../../ir';
import { CompilationJobKind } from '../compilation';
function kindTest(kind) {
    return (op) => op.kind === kind;
}
function kindWithInterpolationTest(kind, interpolation) {
    return (op) => {
        return op.kind === kind && interpolation === op.expression instanceof ir.Interpolation;
    };
}
function basicListenerKindTest(op) {
    return (op.kind === ir.OpKind.Listener && !(op.hostListener && op.isAnimationListener)) ||
        op.kind === ir.OpKind.TwoWayListener;
}
function nonInterpolationPropertyKindTest(op) {
    return (op.kind === ir.OpKind.Property || op.kind === ir.OpKind.TwoWayProperty) &&
        !(op.expression instanceof ir.Interpolation);
}
/**
 * Defines the groups based on `OpKind` that ops will be divided into, for the various create
 * op kinds. Ops will be collected into groups, then optionally transformed, before recombining
 * the groups in the order defined here.
 */
const CREATE_ORDERING = [
    { test: op => op.kind === ir.OpKind.Listener && op.hostListener && op.isAnimationListener },
    { test: basicListenerKindTest },
];
/**
 * Defines the groups based on `OpKind` that ops will be divided into, for the various update
 * op kinds.
 */
const UPDATE_ORDERING = [
    { test: kindTest(ir.OpKind.StyleMap), transform: keepLast },
    { test: kindTest(ir.OpKind.ClassMap), transform: keepLast },
    { test: kindTest(ir.OpKind.StyleProp) },
    { test: kindTest(ir.OpKind.ClassProp) },
    { test: kindWithInterpolationTest(ir.OpKind.Attribute, true) },
    { test: kindWithInterpolationTest(ir.OpKind.Property, true) },
    { test: nonInterpolationPropertyKindTest },
    { test: kindWithInterpolationTest(ir.OpKind.Attribute, false) },
];
/**
 * Host bindings have their own update ordering.
 */
const UPDATE_HOST_ORDERING = [
    { test: kindWithInterpolationTest(ir.OpKind.HostProperty, true) },
    { test: kindWithInterpolationTest(ir.OpKind.HostProperty, false) },
    { test: kindTest(ir.OpKind.Attribute) },
    { test: kindTest(ir.OpKind.StyleMap), transform: keepLast },
    { test: kindTest(ir.OpKind.ClassMap), transform: keepLast },
    { test: kindTest(ir.OpKind.StyleProp) },
    { test: kindTest(ir.OpKind.ClassProp) },
];
/**
 * The set of all op kinds we handle in the reordering phase.
 */
const handledOpKinds = new Set([
    ir.OpKind.Listener, ir.OpKind.TwoWayListener, ir.OpKind.StyleMap, ir.OpKind.ClassMap,
    ir.OpKind.StyleProp, ir.OpKind.ClassProp, ir.OpKind.Property, ir.OpKind.TwoWayProperty,
    ir.OpKind.HostProperty, ir.OpKind.Attribute
]);
/**
 * Many type of operations have ordering constraints that must be respected. For example, a
 * `ClassMap` instruction must be ordered after a `StyleMap` instruction, in order to have
 * predictable semantics that match TemplateDefinitionBuilder and don't break applications.
 */
export function orderOps(job) {
    for (const unit of job.units) {
        // First, we pull out ops that need to be ordered. Then, when we encounter an op that shouldn't
        // be reordered, put the ones we've pulled so far back in the correct order. Finally, if we
        // still have ops pulled at the end, put them back in the correct order.
        // Create mode:
        orderWithin(unit.create, CREATE_ORDERING);
        // Update mode:
        const ordering = unit.job.kind === CompilationJobKind.Host ? UPDATE_HOST_ORDERING : UPDATE_ORDERING;
        orderWithin(unit.update, ordering);
    }
}
/**
 * Order all the ops within the specified group.
 */
function orderWithin(opList, ordering) {
    let opsToOrder = [];
    // Only reorder ops that target the same xref; do not mix ops that target different xrefs.
    let firstTargetInGroup = null;
    for (const op of opList) {
        const currentTarget = ir.hasDependsOnSlotContextTrait(op) ? op.target : null;
        if (!handledOpKinds.has(op.kind) ||
            (currentTarget !== firstTargetInGroup &&
                (firstTargetInGroup !== null && currentTarget !== null))) {
            ir.OpList.insertBefore(reorder(opsToOrder, ordering), op);
            opsToOrder = [];
            firstTargetInGroup = null;
        }
        if (handledOpKinds.has(op.kind)) {
            opsToOrder.push(op);
            ir.OpList.remove(op);
            firstTargetInGroup = currentTarget ?? firstTargetInGroup;
        }
    }
    opList.push(reorder(opsToOrder, ordering));
}
/**
 * Reorders the given list of ops according to the ordering defined by `ORDERING`.
 */
function reorder(ops, ordering) {
    // Break the ops list into groups based on OpKind.
    const groups = Array.from(ordering, () => new Array());
    for (const op of ops) {
        const groupIndex = ordering.findIndex(o => o.test(op));
        groups[groupIndex].push(op);
    }
    // Reassemble the groups into a single list, in the correct order.
    return groups.flatMap((group, i) => {
        const transform = ordering[i].transform;
        return transform ? transform(group) : group;
    });
}
/**
 * Keeps only the last op in a list of ops.
 */
function keepLast(ops) {
    return ops.slice(ops.length - 1);
}
//# sourceMappingURL=data:application/json;base64,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