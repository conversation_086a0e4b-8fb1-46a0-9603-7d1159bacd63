/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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