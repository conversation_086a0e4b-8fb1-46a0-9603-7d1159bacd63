{"version": 3, "file": "button-toggle.mjs", "sources": ["../../../../../../src/material/button-toggle/button-toggle.ts", "../../../../../../src/material/button-toggle/button-toggle.html", "../../../../../../src/material/button-toggle/button-toggle-module.ts", "../../../../../../src/material/button-toggle/button-toggle_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {FocusMonitor} from '@angular/cdk/a11y';\nimport {SelectionModel} from '@angular/cdk/collections';\nimport {\n  AfterContentInit,\n  Attribute,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChildren,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  forwardRef,\n  Input,\n  OnDestroy,\n  OnInit,\n  Optional,\n  Output,\n  QueryList,\n  ViewChild,\n  ViewEncapsulation,\n  InjectionToken,\n  Inject,\n  AfterViewInit,\n  booleanAttribute,\n} from '@angular/core';\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from '@angular/forms';\nimport {MatRipple, MatPseudoCheckbox} from '@angular/material/core';\n\n/**\n * @deprecated No longer used.\n * @breaking-change 11.0.0\n */\nexport type ToggleType = 'checkbox' | 'radio';\n\n/** Possible appearance styles for the button toggle. */\nexport type MatButtonToggleAppearance = 'legacy' | 'standard';\n\n/**\n * Represents the default options for the button toggle that can be configured\n * using the `MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS` injection token.\n */\nexport interface MatButtonToggleDefaultOptions {\n  /**\n   * Default appearance to be used by button toggles. Can be overridden by explicitly\n   * setting an appearance on a button toggle or group.\n   */\n  appearance?: MatButtonToggleAppearance;\n  /** Whetehr icon indicators should be hidden for single-selection button toggle groups. */\n  hideSingleSelectionIndicator?: boolean;\n  /** Whether icon indicators should be hidden for multiple-selection button toggle groups. */\n  hideMultipleSelectionIndicator?: boolean;\n}\n\n/**\n * Injection token that can be used to configure the\n * default options for all button toggles within an app.\n */\nexport const MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS = new InjectionToken<MatButtonToggleDefaultOptions>(\n  'MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS',\n  {\n    providedIn: 'root',\n    factory: MAT_BUTTON_TOGGLE_GROUP_DEFAULT_OPTIONS_FACTORY,\n  },\n);\n\nexport function MAT_BUTTON_TOGGLE_GROUP_DEFAULT_OPTIONS_FACTORY(): MatButtonToggleDefaultOptions {\n  return {\n    hideSingleSelectionIndicator: false,\n    hideMultipleSelectionIndicator: false,\n  };\n}\n\n/**\n * Injection token that can be used to reference instances of `MatButtonToggleGroup`.\n * It serves as alternative token to the actual `MatButtonToggleGroup` class which\n * could cause unnecessary retention of the class and its component metadata.\n */\nexport const MAT_BUTTON_TOGGLE_GROUP = new InjectionToken<MatButtonToggleGroup>(\n  'MatButtonToggleGroup',\n);\n\n/**\n * Provider Expression that allows mat-button-toggle-group to register as a ControlValueAccessor.\n * This allows it to support [(ngModel)].\n * @docs-private\n */\nexport const MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR: any = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatButtonToggleGroup),\n  multi: true,\n};\n\n// Counter used to generate unique IDs.\nlet uniqueIdCounter = 0;\n\n/** Change event object emitted by button toggle. */\nexport class MatButtonToggleChange {\n  constructor(\n    /** The button toggle that emits the event. */\n    public source: MatButtonToggle,\n\n    /** The value assigned to the button toggle. */\n    public value: any,\n  ) {}\n}\n\n/** Exclusive selection button toggle group that behaves like a radio-button group. */\n@Directive({\n  selector: 'mat-button-toggle-group',\n  providers: [\n    MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR,\n    {provide: MAT_BUTTON_TOGGLE_GROUP, useExisting: MatButtonToggleGroup},\n  ],\n  host: {\n    'role': 'group',\n    'class': 'mat-button-toggle-group',\n    '[attr.aria-disabled]': 'disabled',\n    '[class.mat-button-toggle-vertical]': 'vertical',\n    '[class.mat-button-toggle-group-appearance-standard]': 'appearance === \"standard\"',\n  },\n  exportAs: 'matButtonToggleGroup',\n  standalone: true,\n})\nexport class MatButtonToggleGroup implements ControlValueAccessor, OnInit, AfterContentInit {\n  private _multiple = false;\n  private _disabled = false;\n  private _selectionModel: SelectionModel<MatButtonToggle>;\n\n  /**\n   * Reference to the raw value that the consumer tried to assign. The real\n   * value will exclude any values from this one that don't correspond to a\n   * toggle. Useful for the cases where the value is assigned before the toggles\n   * have been initialized or at the same that they're being swapped out.\n   */\n  private _rawValue: any;\n\n  /**\n   * The method to be called in order to update ngModel.\n   * Now `ngModel` binding is not supported in multiple selection mode.\n   */\n  _controlValueAccessorChangeFn: (value: any) => void = () => {};\n\n  /** onTouch function registered via registerOnTouch (ControlValueAccessor). */\n  _onTouched: () => any = () => {};\n\n  /** Child button toggle buttons. */\n  @ContentChildren(forwardRef(() => MatButtonToggle), {\n    // Note that this would technically pick up toggles\n    // from nested groups, but that's not a case that we support.\n    descendants: true,\n  })\n  _buttonToggles: QueryList<MatButtonToggle>;\n\n  /** The appearance for all the buttons in the group. */\n  @Input() appearance: MatButtonToggleAppearance;\n\n  /** `name` attribute for the underlying `input` element. */\n  @Input()\n  get name(): string {\n    return this._name;\n  }\n  set name(value: string) {\n    this._name = value;\n    this._markButtonsForCheck();\n  }\n  private _name = `mat-button-toggle-group-${uniqueIdCounter++}`;\n\n  /** Whether the toggle group is vertical. */\n  @Input({transform: booleanAttribute}) vertical: boolean;\n\n  /** Value of the toggle group. */\n  @Input()\n  get value(): any {\n    const selected = this._selectionModel ? this._selectionModel.selected : [];\n\n    if (this.multiple) {\n      return selected.map(toggle => toggle.value);\n    }\n\n    return selected[0] ? selected[0].value : undefined;\n  }\n  set value(newValue: any) {\n    this._setSelectionByValue(newValue);\n    this.valueChange.emit(this.value);\n  }\n\n  /**\n   * Event that emits whenever the value of the group changes.\n   * Used to facilitate two-way data binding.\n   * @docs-private\n   */\n  @Output() readonly valueChange = new EventEmitter<any>();\n\n  /** Selected button toggles in the group. */\n  get selected(): MatButtonToggle | MatButtonToggle[] {\n    const selected = this._selectionModel ? this._selectionModel.selected : [];\n    return this.multiple ? selected : selected[0] || null;\n  }\n\n  /** Whether multiple button toggles can be selected. */\n  @Input({transform: booleanAttribute})\n  get multiple(): boolean {\n    return this._multiple;\n  }\n  set multiple(value: boolean) {\n    this._multiple = value;\n    this._markButtonsForCheck();\n  }\n\n  /** Whether multiple button toggle group is disabled. */\n  @Input({transform: booleanAttribute})\n  get disabled(): boolean {\n    return this._disabled;\n  }\n  set disabled(value: boolean) {\n    this._disabled = value;\n    this._markButtonsForCheck();\n  }\n\n  /** Event emitted when the group's value changes. */\n  @Output() readonly change: EventEmitter<MatButtonToggleChange> =\n    new EventEmitter<MatButtonToggleChange>();\n\n  /** Whether checkmark indicator for single-selection button toggle groups is hidden. */\n  @Input({transform: booleanAttribute})\n  get hideSingleSelectionIndicator(): boolean {\n    return this._hideSingleSelectionIndicator;\n  }\n  set hideSingleSelectionIndicator(value: boolean) {\n    this._hideSingleSelectionIndicator = value;\n    this._markButtonsForCheck();\n  }\n  private _hideSingleSelectionIndicator: boolean;\n\n  /** Whether checkmark indicator for multiple-selection button toggle groups is hidden. */\n  @Input({transform: booleanAttribute})\n  get hideMultipleSelectionIndicator(): boolean {\n    return this._hideMultipleSelectionIndicator;\n  }\n  set hideMultipleSelectionIndicator(value: boolean) {\n    this._hideMultipleSelectionIndicator = value;\n    this._markButtonsForCheck();\n  }\n  private _hideMultipleSelectionIndicator: boolean;\n\n  constructor(\n    private _changeDetector: ChangeDetectorRef,\n    @Optional()\n    @Inject(MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS)\n    defaultOptions?: MatButtonToggleDefaultOptions,\n  ) {\n    this.appearance =\n      defaultOptions && defaultOptions.appearance ? defaultOptions.appearance : 'standard';\n    this.hideSingleSelectionIndicator = defaultOptions?.hideSingleSelectionIndicator ?? false;\n    this.hideMultipleSelectionIndicator = defaultOptions?.hideMultipleSelectionIndicator ?? false;\n  }\n\n  ngOnInit() {\n    this._selectionModel = new SelectionModel<MatButtonToggle>(this.multiple, undefined, false);\n  }\n\n  ngAfterContentInit() {\n    this._selectionModel.select(...this._buttonToggles.filter(toggle => toggle.checked));\n  }\n\n  /**\n   * Sets the model value. Implemented as part of ControlValueAccessor.\n   * @param value Value to be set to the model.\n   */\n  writeValue(value: any) {\n    this.value = value;\n    this._changeDetector.markForCheck();\n  }\n\n  // Implemented as part of ControlValueAccessor.\n  registerOnChange(fn: (value: any) => void) {\n    this._controlValueAccessorChangeFn = fn;\n  }\n\n  // Implemented as part of ControlValueAccessor.\n  registerOnTouched(fn: any) {\n    this._onTouched = fn;\n  }\n\n  // Implemented as part of ControlValueAccessor.\n  setDisabledState(isDisabled: boolean): void {\n    this.disabled = isDisabled;\n  }\n\n  /** Dispatch change event with current selection and group value. */\n  _emitChangeEvent(toggle: MatButtonToggle): void {\n    const event = new MatButtonToggleChange(toggle, this.value);\n    this._rawValue = event.value;\n    this._controlValueAccessorChangeFn(event.value);\n    this.change.emit(event);\n  }\n\n  /**\n   * Syncs a button toggle's selected state with the model value.\n   * @param toggle Toggle to be synced.\n   * @param select Whether the toggle should be selected.\n   * @param isUserInput Whether the change was a result of a user interaction.\n   * @param deferEvents Whether to defer emitting the change events.\n   */\n  _syncButtonToggle(\n    toggle: MatButtonToggle,\n    select: boolean,\n    isUserInput = false,\n    deferEvents = false,\n  ) {\n    // Deselect the currently-selected toggle, if we're in single-selection\n    // mode and the button being toggled isn't selected at the moment.\n    if (!this.multiple && this.selected && !toggle.checked) {\n      (this.selected as MatButtonToggle).checked = false;\n    }\n\n    if (this._selectionModel) {\n      if (select) {\n        this._selectionModel.select(toggle);\n      } else {\n        this._selectionModel.deselect(toggle);\n      }\n    } else {\n      deferEvents = true;\n    }\n\n    // We need to defer in some cases in order to avoid \"changed after checked errors\", however\n    // the side-effect is that we may end up updating the model value out of sequence in others\n    // The `deferEvents` flag allows us to decide whether to do it on a case-by-case basis.\n    if (deferEvents) {\n      Promise.resolve().then(() => this._updateModelValue(toggle, isUserInput));\n    } else {\n      this._updateModelValue(toggle, isUserInput);\n    }\n  }\n\n  /** Checks whether a button toggle is selected. */\n  _isSelected(toggle: MatButtonToggle) {\n    return this._selectionModel && this._selectionModel.isSelected(toggle);\n  }\n\n  /** Determines whether a button toggle should be checked on init. */\n  _isPrechecked(toggle: MatButtonToggle) {\n    if (typeof this._rawValue === 'undefined') {\n      return false;\n    }\n\n    if (this.multiple && Array.isArray(this._rawValue)) {\n      return this._rawValue.some(value => toggle.value != null && value === toggle.value);\n    }\n\n    return toggle.value === this._rawValue;\n  }\n\n  /** Updates the selection state of the toggles in the group based on a value. */\n  private _setSelectionByValue(value: any | any[]) {\n    this._rawValue = value;\n\n    if (!this._buttonToggles) {\n      return;\n    }\n\n    if (this.multiple && value) {\n      if (!Array.isArray(value) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('Value must be an array in multiple-selection mode.');\n      }\n\n      this._clearSelection();\n      value.forEach((currentValue: any) => this._selectValue(currentValue));\n    } else {\n      this._clearSelection();\n      this._selectValue(value);\n    }\n  }\n\n  /** Clears the selected toggles. */\n  private _clearSelection() {\n    this._selectionModel.clear();\n    this._buttonToggles.forEach(toggle => (toggle.checked = false));\n  }\n\n  /** Selects a value if there's a toggle that corresponds to it. */\n  private _selectValue(value: any) {\n    const correspondingOption = this._buttonToggles.find(toggle => {\n      return toggle.value != null && toggle.value === value;\n    });\n\n    if (correspondingOption) {\n      correspondingOption.checked = true;\n      this._selectionModel.select(correspondingOption);\n    }\n  }\n\n  /** Syncs up the group's value with the model and emits the change event. */\n  private _updateModelValue(toggle: MatButtonToggle, isUserInput: boolean) {\n    // Only emit the change event for user input.\n    if (isUserInput) {\n      this._emitChangeEvent(toggle);\n    }\n\n    // Note: we emit this one no matter whether it was a user interaction, because\n    // it is used by Angular to sync up the two-way data binding.\n    this.valueChange.emit(this.value);\n  }\n\n  /** Marks all of the child button toggles to be checked. */\n  private _markButtonsForCheck() {\n    this._buttonToggles?.forEach(toggle => toggle._markForCheck());\n  }\n}\n\n/** Single button inside of a toggle group. */\n@Component({\n  selector: 'mat-button-toggle',\n  templateUrl: 'button-toggle.html',\n  styleUrl: 'button-toggle.css',\n  encapsulation: ViewEncapsulation.None,\n  exportAs: 'matButtonToggle',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  host: {\n    '[class.mat-button-toggle-standalone]': '!buttonToggleGroup',\n    '[class.mat-button-toggle-checked]': 'checked',\n    '[class.mat-button-toggle-disabled]': 'disabled',\n    '[class.mat-button-toggle-appearance-standard]': 'appearance === \"standard\"',\n    'class': 'mat-button-toggle',\n    '[attr.aria-label]': 'null',\n    '[attr.aria-labelledby]': 'null',\n    '[attr.id]': 'id',\n    '[attr.name]': 'null',\n    '(focus)': 'focus()',\n    'role': 'presentation',\n  },\n  standalone: true,\n  imports: [MatRipple, MatPseudoCheckbox],\n})\nexport class MatButtonToggle implements OnInit, AfterViewInit, OnDestroy {\n  private _checked = false;\n\n  /**\n   * Attached to the aria-label attribute of the host element. In most cases, aria-labelledby will\n   * take precedence so this may be omitted.\n   */\n  @Input('aria-label') ariaLabel: string;\n\n  /**\n   * Users can specify the `aria-labelledby` attribute which will be forwarded to the input element\n   */\n  @Input('aria-labelledby') ariaLabelledby: string | null = null;\n\n  /** Underlying native `button` element. */\n  @ViewChild('button') _buttonElement: ElementRef<HTMLButtonElement>;\n\n  /** The parent button toggle group (exclusive selection). Optional. */\n  buttonToggleGroup: MatButtonToggleGroup;\n\n  /** Unique ID for the underlying `button` element. */\n  get buttonId(): string {\n    return `${this.id}-button`;\n  }\n\n  /** The unique ID for this button toggle. */\n  @Input() id: string;\n\n  /** HTML's 'name' attribute used to group radios for unique selection. */\n  @Input() name: string;\n\n  /** MatButtonToggleGroup reads this to assign its own value. */\n  @Input() value: any;\n\n  /** Tabindex for the toggle. */\n  @Input() tabIndex: number | null;\n\n  /** Whether ripples are disabled on the button toggle. */\n  @Input({transform: booleanAttribute}) disableRipple: boolean;\n\n  /** The appearance style of the button. */\n  @Input()\n  get appearance(): MatButtonToggleAppearance {\n    return this.buttonToggleGroup ? this.buttonToggleGroup.appearance : this._appearance;\n  }\n  set appearance(value: MatButtonToggleAppearance) {\n    this._appearance = value;\n  }\n  private _appearance: MatButtonToggleAppearance;\n\n  /** Whether the button is checked. */\n  @Input({transform: booleanAttribute})\n  get checked(): boolean {\n    return this.buttonToggleGroup ? this.buttonToggleGroup._isSelected(this) : this._checked;\n  }\n  set checked(value: boolean) {\n    if (value !== this._checked) {\n      this._checked = value;\n\n      if (this.buttonToggleGroup) {\n        this.buttonToggleGroup._syncButtonToggle(this, this._checked);\n      }\n\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n\n  /** Whether the button is disabled. */\n  @Input({transform: booleanAttribute})\n  get disabled(): boolean {\n    return this._disabled || (this.buttonToggleGroup && this.buttonToggleGroup.disabled);\n  }\n  set disabled(value: boolean) {\n    this._disabled = value;\n  }\n  private _disabled: boolean = false;\n\n  /** Event emitted when the group value changes. */\n  @Output() readonly change: EventEmitter<MatButtonToggleChange> =\n    new EventEmitter<MatButtonToggleChange>();\n\n  constructor(\n    @Optional() @Inject(MAT_BUTTON_TOGGLE_GROUP) toggleGroup: MatButtonToggleGroup,\n    private _changeDetectorRef: ChangeDetectorRef,\n    private _elementRef: ElementRef<HTMLElement>,\n    private _focusMonitor: FocusMonitor,\n    @Attribute('tabindex') defaultTabIndex: string,\n    @Optional()\n    @Inject(MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS)\n    defaultOptions?: MatButtonToggleDefaultOptions,\n  ) {\n    const parsedTabIndex = Number(defaultTabIndex);\n    this.tabIndex = parsedTabIndex || parsedTabIndex === 0 ? parsedTabIndex : null;\n    this.buttonToggleGroup = toggleGroup;\n    this.appearance =\n      defaultOptions && defaultOptions.appearance ? defaultOptions.appearance : 'standard';\n  }\n\n  ngOnInit() {\n    const group = this.buttonToggleGroup;\n    this.id = this.id || `mat-button-toggle-${uniqueIdCounter++}`;\n\n    if (group) {\n      if (group._isPrechecked(this)) {\n        this.checked = true;\n      } else if (group._isSelected(this) !== this._checked) {\n        // As side effect of the circular dependency between the toggle group and the button,\n        // we may end up in a state where the button is supposed to be checked on init, but it\n        // isn't, because the checked value was assigned too early. This can happen when Ivy\n        // assigns the static input value before the `ngOnInit` has run.\n        group._syncButtonToggle(this, this._checked);\n      }\n    }\n  }\n\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this._elementRef, true);\n  }\n\n  ngOnDestroy() {\n    const group = this.buttonToggleGroup;\n\n    this._focusMonitor.stopMonitoring(this._elementRef);\n\n    // Remove the toggle from the selection once it's destroyed. Needs to happen\n    // on the next tick in order to avoid \"changed after checked\" errors.\n    if (group && group._isSelected(this)) {\n      group._syncButtonToggle(this, false, false, true);\n    }\n  }\n\n  /** Focuses the button. */\n  focus(options?: FocusOptions): void {\n    this._buttonElement.nativeElement.focus(options);\n  }\n\n  /** Checks the button toggle due to an interaction with the underlying native button. */\n  _onButtonClick() {\n    const newChecked = this._isSingleSelector() ? true : !this._checked;\n\n    if (newChecked !== this._checked) {\n      this._checked = newChecked;\n      if (this.buttonToggleGroup) {\n        this.buttonToggleGroup._syncButtonToggle(this, this._checked, true);\n        this.buttonToggleGroup._onTouched();\n      }\n    }\n    // Emit a change event when it's the single selector\n    this.change.emit(new MatButtonToggleChange(this, this.value));\n  }\n\n  /**\n   * Marks the button toggle as needing checking for change detection.\n   * This method is exposed because the parent button toggle group will directly\n   * update bound properties of the radio button.\n   */\n  _markForCheck() {\n    // When the group value changes, the button will not be notified.\n    // Use `markForCheck` to explicit update button toggle's status.\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /** Gets the name that should be assigned to the inner DOM node. */\n  _getButtonName(): string | null {\n    if (this._isSingleSelector()) {\n      return this.buttonToggleGroup.name;\n    }\n    return this.name || null;\n  }\n\n  /** Whether the toggle is in single selection mode. */\n  private _isSingleSelector(): boolean {\n    return this.buttonToggleGroup && !this.buttonToggleGroup.multiple;\n  }\n}\n", "<button #button class=\"mat-button-toggle-button mat-focus-indicator\"\n        type=\"button\"\n        [id]=\"buttonId\"\n        [attr.tabindex]=\"disabled ? -1 : tabIndex\"\n        [attr.aria-pressed]=\"checked\"\n        [disabled]=\"disabled || null\"\n        [attr.name]=\"_getButtonName()\"\n        [attr.aria-label]=\"ariaLabel\"\n        [attr.aria-labelledby]=\"ariaLabelledby\"\n        (click)=\"_onButtonClick()\">\n  <span class=\"mat-button-toggle-label-content\">\n    <!-- Render checkmark at the beginning for single-selection. -->\n    @if (buttonToggleGroup && checked && !buttonToggleGroup.multiple && !buttonToggleGroup.hideSingleSelectionIndicator) {\n      <mat-pseudo-checkbox\n          class=\"mat-mdc-option-pseudo-checkbox\"\n          [disabled]=\"disabled\"\n          state=\"checked\"\n          aria-hidden=\"true\"\n          appearance=\"minimal\"></mat-pseudo-checkbox>\n    }\n    <!-- Render checkmark at the beginning for multiple-selection. -->\n    @if (buttonToggleGroup && checked && buttonToggleGroup.multiple && !buttonToggleGroup.hideMultipleSelectionIndicator) {\n      <mat-pseudo-checkbox\n          class=\"mat-mdc-option-pseudo-checkbox\"\n          [disabled]=\"disabled\"\n          state=\"checked\"\n          aria-hidden=\"true\"\n          appearance=\"minimal\"></mat-pseudo-checkbox>\n    }\n    <ng-content></ng-content>\n  </span>\n</button>\n\n<span class=\"mat-button-toggle-focus-overlay\"></span>\n<span class=\"mat-button-toggle-ripple\" matRipple\n     [matRippleTrigger]=\"button\"\n     [matRippleDisabled]=\"this.disableRipple || this.disabled\">\n</span>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule, MatRippleModule} from '@angular/material/core';\nimport {MatButtonToggle, MatButtonToggleGroup} from './button-toggle';\n\n@NgModule({\n  imports: [MatCommonModule, MatRippleModule, MatButtonToggleGroup, MatButtonToggle],\n  exports: [MatCommonModule, MatButtonToggleGroup, MatButtonToggle],\n})\nexport class MatButtonToggleModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;;;AA8DA;;;AAGG;MACU,iCAAiC,GAAG,IAAI,cAAc,CACjE,mCAAmC,EACnC;AACE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,+CAA+C;AACzD,CAAA,EACD;SAEc,+CAA+C,GAAA;IAC7D,OAAO;AACL,QAAA,4BAA4B,EAAE,KAAK;AACnC,QAAA,8BAA8B,EAAE,KAAK;KACtC,CAAC;AACJ,CAAC;AAED;;;;AAIG;MACU,uBAAuB,GAAG,IAAI,cAAc,CACvD,sBAAsB,EACtB;AAEF;;;;AAIG;AACU,MAAA,sCAAsC,GAAQ;AACzD,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,oBAAoB,CAAC;AACnD,IAAA,KAAK,EAAE,IAAI;EACX;AAEF;AACA,IAAI,eAAe,GAAG,CAAC,CAAC;AAExB;MACa,qBAAqB,CAAA;AAChC,IAAA,WAAA;;IAES,MAAuB;;IAGvB,KAAU,EAAA;QAHV,IAAM,CAAA,MAAA,GAAN,MAAM,CAAiB;QAGvB,IAAK,CAAA,KAAA,GAAL,KAAK,CAAK;KACf;AACL,CAAA;AAED;MAiBa,oBAAoB,CAAA;;AAkC/B,IAAA,IACI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;IACD,IAAI,IAAI,CAAC,KAAa,EAAA;AACpB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC7B;;AAOD,IAAA,IACI,KAAK,GAAA;AACP,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,GAAG,EAAE,CAAC;AAE3E,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,OAAO,QAAQ,CAAC,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC;SAC7C;AAED,QAAA,OAAO,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,SAAS,CAAC;KACpD;IACD,IAAI,KAAK,CAAC,QAAa,EAAA;AACrB,QAAA,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QACpC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACnC;;AAUD,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,GAAG,EAAE,CAAC;AAC3E,QAAA,OAAO,IAAI,CAAC,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;KACvD;;AAGD,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC7B;;AAGD,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC7B;;AAOD,IAAA,IACI,4BAA4B,GAAA;QAC9B,OAAO,IAAI,CAAC,6BAA6B,CAAC;KAC3C;IACD,IAAI,4BAA4B,CAAC,KAAc,EAAA;AAC7C,QAAA,IAAI,CAAC,6BAA6B,GAAG,KAAK,CAAC;QAC3C,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC7B;;AAID,IAAA,IACI,8BAA8B,GAAA;QAChC,OAAO,IAAI,CAAC,+BAA+B,CAAC;KAC7C;IACD,IAAI,8BAA8B,CAAC,KAAc,EAAA;AAC/C,QAAA,IAAI,CAAC,+BAA+B,GAAG,KAAK,CAAC;QAC7C,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC7B;IAGD,WACU,CAAA,eAAkC,EAG1C,cAA8C,EAAA;QAHtC,IAAe,CAAA,eAAA,GAAf,eAAe,CAAmB;QA1HpC,IAAS,CAAA,SAAA,GAAG,KAAK,CAAC;QAClB,IAAS,CAAA,SAAA,GAAG,KAAK,CAAC;AAW1B;;;AAGG;AACH,QAAA,IAAA,CAAA,6BAA6B,GAAyB,MAAK,GAAG,CAAC;;AAG/D,QAAA,IAAA,CAAA,UAAU,GAAc,MAAK,GAAG,CAAC;AAsBzB,QAAA,IAAA,CAAA,KAAK,GAAG,CAAA,wBAAA,EAA2B,eAAe,EAAE,EAAE,CAAC;AAqB/D;;;;AAIG;AACgB,QAAA,IAAA,CAAA,WAAW,GAAG,IAAI,YAAY,EAAO,CAAC;;AA6BtC,QAAA,IAAA,CAAA,MAAM,GACvB,IAAI,YAAY,EAAyB,CAAC;AA8B1C,QAAA,IAAI,CAAC,UAAU;AACb,YAAA,cAAc,IAAI,cAAc,CAAC,UAAU,GAAG,cAAc,CAAC,UAAU,GAAG,UAAU,CAAC;QACvF,IAAI,CAAC,4BAA4B,GAAG,cAAc,EAAE,4BAA4B,IAAI,KAAK,CAAC;QAC1F,IAAI,CAAC,8BAA8B,GAAG,cAAc,EAAE,8BAA8B,IAAI,KAAK,CAAC;KAC/F;IAED,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,cAAc,CAAkB,IAAI,CAAC,QAAQ,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;KAC7F;IAED,kBAAkB,GAAA;QAChB,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;KACtF;AAED;;;AAGG;AACH,IAAA,UAAU,CAAC,KAAU,EAAA;AACnB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;KACrC;;AAGD,IAAA,gBAAgB,CAAC,EAAwB,EAAA;AACvC,QAAA,IAAI,CAAC,6BAA6B,GAAG,EAAE,CAAC;KACzC;;AAGD,IAAA,iBAAiB,CAAC,EAAO,EAAA;AACvB,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;KACtB;;AAGD,IAAA,gBAAgB,CAAC,UAAmB,EAAA;AAClC,QAAA,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC;KAC5B;;AAGD,IAAA,gBAAgB,CAAC,MAAuB,EAAA;QACtC,MAAM,KAAK,GAAG,IAAI,qBAAqB,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5D,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC;AAC7B,QAAA,IAAI,CAAC,6BAA6B,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAChD,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACzB;AAED;;;;;;AAMG;IACH,iBAAiB,CACf,MAAuB,EACvB,MAAe,EACf,WAAW,GAAG,KAAK,EACnB,WAAW,GAAG,KAAK,EAAA;;;AAInB,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;AACrD,YAAA,IAAI,CAAC,QAA4B,CAAC,OAAO,GAAG,KAAK,CAAC;SACpD;AAED,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,IAAI,MAAM,EAAE;AACV,gBAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;aACrC;iBAAM;AACL,gBAAA,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;aACvC;SACF;aAAM;YACL,WAAW,GAAG,IAAI,CAAC;SACpB;;;;QAKD,IAAI,WAAW,EAAE;AACf,YAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC;SAC3E;aAAM;AACL,YAAA,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;SAC7C;KACF;;AAGD,IAAA,WAAW,CAAC,MAAuB,EAAA;AACjC,QAAA,OAAO,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;KACxE;;AAGD,IAAA,aAAa,CAAC,MAAuB,EAAA;AACnC,QAAA,IAAI,OAAO,IAAI,CAAC,SAAS,KAAK,WAAW,EAAE;AACzC,YAAA,OAAO,KAAK,CAAC;SACd;AAED,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YAClD,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC;SACrF;AAED,QAAA,OAAO,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC,SAAS,CAAC;KACxC;;AAGO,IAAA,oBAAoB,CAAC,KAAkB,EAAA;AAC7C,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAEvB,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,OAAO;SACR;AAED,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,EAAE;AAC1B,YAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAC5E,gBAAA,MAAM,KAAK,CAAC,oDAAoD,CAAC,CAAC;aACnE;YAED,IAAI,CAAC,eAAe,EAAE,CAAC;AACvB,YAAA,KAAK,CAAC,OAAO,CAAC,CAAC,YAAiB,KAAK,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,CAAC;SACvE;aAAM;YACL,IAAI,CAAC,eAAe,EAAE,CAAC;AACvB,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;SAC1B;KACF;;IAGO,eAAe,GAAA;AACrB,QAAA,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;AAC7B,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,KAAK,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC;KACjE;;AAGO,IAAA,YAAY,CAAC,KAAU,EAAA;QAC7B,MAAM,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,IAAG;YAC5D,OAAO,MAAM,CAAC,KAAK,IAAI,IAAI,IAAI,MAAM,CAAC,KAAK,KAAK,KAAK,CAAC;AACxD,SAAC,CAAC,CAAC;QAEH,IAAI,mBAAmB,EAAE;AACvB,YAAA,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC;AACnC,YAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;SAClD;KACF;;IAGO,iBAAiB,CAAC,MAAuB,EAAE,WAAoB,EAAA;;QAErE,IAAI,WAAW,EAAE;AACf,YAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;SAC/B;;;QAID,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACnC;;IAGO,oBAAoB,GAAA;AAC1B,QAAA,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC;KAChE;AA7RU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,mDA6HrB,iCAAiC,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGA7HhC,oBAAoB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,yBAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,IAAA,EAAA,MAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EA6CZ,gBAAgB,CAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAgChB,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAUhB,gBAAgB,CAchB,EAAA,4BAAA,EAAA,CAAA,8BAAA,EAAA,8BAAA,EAAA,gBAAgB,CAWhB,EAAA,8BAAA,EAAA,CAAA,gCAAA,EAAA,gCAAA,EAAA,gBAAgB,CA9HxB,EAAA,EAAA,OAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,OAAA,EAAA,EAAA,UAAA,EAAA,EAAA,oBAAA,EAAA,UAAA,EAAA,kCAAA,EAAA,UAAA,EAAA,mDAAA,EAAA,6BAAA,EAAA,EAAA,cAAA,EAAA,yBAAA,EAAA,EAAA,SAAA,EAAA;YACT,sCAAsC;AACtC,YAAA,EAAC,OAAO,EAAE,uBAAuB,EAAE,WAAW,EAAE,oBAAoB,EAAC;AACtE,SAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAkCiC,eAAe,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,sBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAvBtC,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAhBhC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,yBAAyB;AACnC,oBAAA,SAAS,EAAE;wBACT,sCAAsC;AACtC,wBAAA,EAAC,OAAO,EAAE,uBAAuB,EAAE,WAAW,sBAAsB,EAAC;AACtE,qBAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,MAAM,EAAE,OAAO;AACf,wBAAA,OAAO,EAAE,yBAAyB;AAClC,wBAAA,sBAAsB,EAAE,UAAU;AAClC,wBAAA,oCAAoC,EAAE,UAAU;AAChD,wBAAA,qDAAqD,EAAE,2BAA2B;AACnF,qBAAA;AACD,oBAAA,QAAQ,EAAE,sBAAsB;AAChC,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BA6HI,QAAQ;;0BACR,MAAM;2BAAC,iCAAiC,CAAA;yCAjG3C,cAAc,EAAA,CAAA;sBALb,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,UAAU,CAAC,MAAM,eAAe,CAAC,EAAE;;;AAGlD,wBAAA,WAAW,EAAE,IAAI;AAClB,qBAAA,CAAA;gBAIQ,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAIF,IAAI,EAAA,CAAA;sBADP,KAAK;gBAWgC,QAAQ,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAIhC,KAAK,EAAA,CAAA;sBADR,KAAK;gBAoBa,WAAW,EAAA,CAAA;sBAA7B,MAAM;gBAUH,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAWhC,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAUjB,MAAM,EAAA,CAAA;sBAAxB,MAAM;gBAKH,4BAA4B,EAAA,CAAA;sBAD/B,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAYhC,8BAA8B,EAAA,CAAA;sBADjC,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;;AAgLtC;MAwBa,eAAe,CAAA;;AAqB1B,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,CAAG,EAAA,IAAI,CAAC,EAAE,SAAS,CAAC;KAC5B;;AAkBD,IAAA,IACI,UAAU,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;KACtF;IACD,IAAI,UAAU,CAAC,KAAgC,EAAA;AAC7C,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;KAC1B;;AAID,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;KAC1F;IACD,IAAI,OAAO,CAAC,KAAc,EAAA;AACxB,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,QAAQ,EAAE;AAC3B,YAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AAEtB,YAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBAC1B,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC/D;AAED,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;SACxC;KACF;;AAGD,IAAA,IACI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;KACtF;IACD,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;KACxB;IAOD,WAC+C,CAAA,WAAiC,EACtE,kBAAqC,EACrC,WAAoC,EACpC,aAA2B,EACZ,eAAuB,EAG9C,cAA8C,EAAA;QANtC,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAmB;QACrC,IAAW,CAAA,WAAA,GAAX,WAAW,CAAyB;QACpC,IAAa,CAAA,aAAA,GAAb,aAAa,CAAc;QApF7B,IAAQ,CAAA,QAAA,GAAG,KAAK,CAAC;AAQzB;;AAEG;QACuB,IAAc,CAAA,cAAA,GAAkB,IAAI,CAAC;QA+DvD,IAAS,CAAA,SAAA,GAAY,KAAK,CAAC;;AAGhB,QAAA,IAAA,CAAA,MAAM,GACvB,IAAI,YAAY,EAAyB,CAAC;AAY1C,QAAA,MAAM,cAAc,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC;AAC/C,QAAA,IAAI,CAAC,QAAQ,GAAG,cAAc,IAAI,cAAc,KAAK,CAAC,GAAG,cAAc,GAAG,IAAI,CAAC;AAC/E,QAAA,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC;AACrC,QAAA,IAAI,CAAC,UAAU;AACb,YAAA,cAAc,IAAI,cAAc,CAAC,UAAU,GAAG,cAAc,CAAC,UAAU,GAAG,UAAU,CAAC;KACxF;IAED,QAAQ,GAAA;AACN,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACrC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,CAAqB,kBAAA,EAAA,eAAe,EAAE,CAAA,CAAE,CAAC;QAE9D,IAAI,KAAK,EAAE;AACT,YAAA,IAAI,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;AAC7B,gBAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;aACrB;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE;;;;;gBAKpD,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC9C;SACF;KACF;IAED,eAAe,GAAA;QACb,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;KACpD;IAED,WAAW,GAAA;AACT,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAErC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;;;QAIpD,IAAI,KAAK,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;YACpC,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;SACnD;KACF;;AAGD,IAAA,KAAK,CAAC,OAAsB,EAAA;QAC1B,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;KAClD;;IAGD,cAAc,GAAA;AACZ,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,EAAE,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC;AAEpE,QAAA,IAAI,UAAU,KAAK,IAAI,CAAC,QAAQ,EAAE;AAChC,YAAA,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC;AAC3B,YAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAC1B,gBAAA,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AACpE,gBAAA,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,CAAC;aACrC;SACF;;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;KAC/D;AAED;;;;AAIG;IACH,aAAa,GAAA;;;AAGX,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;KACxC;;IAGD,cAAc,GAAA;AACZ,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;AAC5B,YAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;SACpC;AACD,QAAA,OAAO,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC;KAC1B;;IAGO,iBAAiB,GAAA;QACvB,OAAO,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC;KACnE;AA7KU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,EAkFJ,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,uBAAuB,EAIhC,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,YAAA,EAAA,EAAA,EAAA,KAAA,EAAA,UAAU,8BAEb,iCAAiC,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAxFhC,eAAe,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,EAAA,SAAA,EAAA,CAAA,YAAA,EAAA,WAAA,CAAA,EAAA,cAAA,EAAA,CAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,UAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAsCP,gBAAgB,CAAA,EAAA,UAAA,EAAA,YAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAahB,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAiBhB,gBAAgB,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,cAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,UAAA,EAAA,EAAA,oCAAA,EAAA,oBAAA,EAAA,iCAAA,EAAA,SAAA,EAAA,kCAAA,EAAA,UAAA,EAAA,6CAAA,EAAA,6BAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,SAAA,EAAA,IAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,QAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EChgBrC,kmDAsCA,EAAA,MAAA,EAAA,CAAA,smPAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EDoZY,SAAS,EAAA,QAAA,EAAA,2BAAA,EAAA,MAAA,EAAA,CAAA,gBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,iBAAiB,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,UAAA,EAAA,YAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAE3B,eAAe,EAAA,UAAA,EAAA,CAAA;kBAvB3B,SAAS;+BACE,mBAAmB,EAAA,aAAA,EAGd,iBAAiB,CAAC,IAAI,EAAA,QAAA,EAC3B,iBAAiB,EACV,eAAA,EAAA,uBAAuB,CAAC,MAAM,EACzC,IAAA,EAAA;AACJ,wBAAA,sCAAsC,EAAE,oBAAoB;AAC5D,wBAAA,mCAAmC,EAAE,SAAS;AAC9C,wBAAA,oCAAoC,EAAE,UAAU;AAChD,wBAAA,+CAA+C,EAAE,2BAA2B;AAC5E,wBAAA,OAAO,EAAE,mBAAmB;AAC5B,wBAAA,mBAAmB,EAAE,MAAM;AAC3B,wBAAA,wBAAwB,EAAE,MAAM;AAChC,wBAAA,WAAW,EAAE,IAAI;AACjB,wBAAA,aAAa,EAAE,MAAM;AACrB,wBAAA,SAAS,EAAE,SAAS;AACpB,wBAAA,MAAM,EAAE,cAAc;AACvB,qBAAA,EAAA,UAAA,EACW,IAAI,EACP,OAAA,EAAA,CAAC,SAAS,EAAE,iBAAiB,CAAC,EAAA,QAAA,EAAA,kmDAAA,EAAA,MAAA,EAAA,CAAA,smPAAA,CAAA,EAAA,CAAA;;0BAoFpC,QAAQ;;0BAAI,MAAM;2BAAC,uBAAuB,CAAA;;0BAI1C,SAAS;2BAAC,UAAU,CAAA;;0BACpB,QAAQ;;0BACR,MAAM;2BAAC,iCAAiC,CAAA;yCAjFtB,SAAS,EAAA,CAAA;sBAA7B,KAAK;uBAAC,YAAY,CAAA;gBAKO,cAAc,EAAA,CAAA;sBAAvC,KAAK;uBAAC,iBAAiB,CAAA;gBAGH,cAAc,EAAA,CAAA;sBAAlC,SAAS;uBAAC,QAAQ,CAAA;gBAWV,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAGG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAGG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAGG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAGgC,aAAa,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAIhC,UAAU,EAAA,CAAA;sBADb,KAAK;gBAWF,OAAO,EAAA,CAAA;sBADV,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAkBhC,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAUjB,MAAM,EAAA,CAAA;sBAAxB,MAAM;;;ME1fI,qBAAqB,CAAA;8GAArB,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAArB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,EAHtB,OAAA,EAAA,CAAA,eAAe,EAAE,eAAe,EAAE,oBAAoB,EAAE,eAAe,CACvE,EAAA,OAAA,EAAA,CAAA,eAAe,EAAE,oBAAoB,EAAE,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;AAErD,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,YAHtB,eAAe,EAAE,eAAe,EAAwB,eAAe,EACvE,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAEd,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBAJjC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,oBAAoB,EAAE,eAAe,CAAC;AAClF,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,oBAAoB,EAAE,eAAe,CAAC;AAClE,iBAAA,CAAA;;;ACfD;;AAEG;;;;"}