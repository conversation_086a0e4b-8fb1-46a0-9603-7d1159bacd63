/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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