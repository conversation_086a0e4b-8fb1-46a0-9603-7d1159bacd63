{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../shared/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/form-field\";\nimport * as i9 from \"@angular/material/input\";\nimport * as i10 from \"@angular/material/icon\";\nfunction RegisterComponent_mat_error_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getFieldErrorMessage(\"firstName\"), \" \");\n  }\n}\nfunction RegisterComponent_mat_error_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getFieldErrorMessage(\"lastName\"), \" \");\n  }\n}\nfunction RegisterComponent_mat_error_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getFieldErrorMessage(\"email\"), \" \");\n  }\n}\nfunction RegisterComponent_mat_error_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getFieldErrorMessage(\"phoneNumber\"), \" \");\n  }\n}\nfunction RegisterComponent_mat_error_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getFieldErrorMessage(\"password\"), \" \");\n  }\n}\nfunction RegisterComponent_mat_error_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getPasswordConfirmErrorMessage(), \" \");\n  }\n}\nfunction RegisterComponent_mat_icon_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 21);\n    i0.ɵɵtext(1, \"refresh\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_icon_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"person_add\");\n    i0.ɵɵelementEnd();\n  }\n}\n// Custom validator for password confirmation\nfunction passwordMatchValidator(control) {\n  const password = control.get('password');\n  const confirmPassword = control.get('confirmPassword');\n  if (password && confirmPassword && password.value !== confirmPassword.value) {\n    return {\n      'passwordMismatch': true\n    };\n  }\n  return null;\n}\nexport class RegisterComponent {\n  constructor(formBuilder, authService, router, route, snackBar) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.router = router;\n    this.route = route;\n    this.snackBar = snackBar;\n    this.isLoading = false;\n    this.hidePassword = true;\n    this.hideConfirmPassword = true;\n    this.returnUrl = '/books';\n    this.registerForm = this.formBuilder.group({\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      phoneNumber: ['', [Validators.pattern(/^\\+?[\\d\\s\\-\\(\\)]+$/)]],\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      confirmPassword: ['', [Validators.required]]\n    }, {\n      validators: passwordMatchValidator\n    });\n  }\n  ngOnInit() {\n    // Get return url from route parameters or default to '/books'\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/books';\n    // Redirect if already logged in\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate([this.returnUrl]);\n    }\n  }\n  onSubmit() {\n    if (this.registerForm.valid && !this.isLoading) {\n      this.isLoading = true;\n      const registerData = {\n        ...this.registerForm.value,\n        phoneNumber: this.registerForm.value.phoneNumber || undefined\n      };\n      this.authService.register(registerData).subscribe({\n        next: response => {\n          this.isLoading = false;\n          this.snackBar.open('Registration successful! Welcome to BookCart!', 'Close', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n          this.router.navigate([this.returnUrl]);\n        },\n        error: error => {\n          this.isLoading = false;\n          let errorMessage = 'Registration failed. Please try again.';\n          if (error.status === 400) {\n            if (error.error?.errors) {\n              // Handle validation errors\n              const errors = Object.values(error.error.errors).flat();\n              errorMessage = errors.join(', ');\n            } else if (error.error?.message) {\n              errorMessage = error.error.message;\n            }\n          }\n          this.snackBar.open(errorMessage, 'Close', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    }\n  }\n  getFieldErrorMessage(fieldName) {\n    const field = this.registerForm.get(fieldName);\n    if (!field?.errors || !field.touched) return '';\n    const errors = field.errors;\n    if (errors['required']) {\n      return `${this.getFieldDisplayName(fieldName)} is required`;\n    }\n    if (errors['email']) {\n      return 'Please enter a valid email address';\n    }\n    if (errors['minlength']) {\n      const requiredLength = errors['minlength'].requiredLength;\n      return `${this.getFieldDisplayName(fieldName)} must be at least ${requiredLength} characters long`;\n    }\n    if (errors['pattern'] && fieldName === 'phoneNumber') {\n      return 'Please enter a valid phone number';\n    }\n    return '';\n  }\n  getPasswordConfirmErrorMessage() {\n    const confirmField = this.registerForm.get('confirmPassword');\n    if (!confirmField?.errors || !confirmField.touched) return '';\n    if (confirmField.errors['required']) {\n      return 'Password confirmation is required';\n    }\n    if (this.registerForm.errors?.['passwordMismatch']) {\n      return 'Passwords do not match';\n    }\n    return '';\n  }\n  getFieldDisplayName(fieldName) {\n    const displayNames = {\n      firstName: 'First name',\n      lastName: 'Last name',\n      email: 'Email',\n      phoneNumber: 'Phone number',\n      password: 'Password',\n      confirmPassword: 'Password confirmation'\n    };\n    return displayNames[fieldName] || fieldName;\n  }\n  goToLogin() {\n    this.router.navigate(['/login'], {\n      queryParams: {\n        returnUrl: this.returnUrl\n      }\n    });\n  }\n  static {\n    this.ɵfac = function RegisterComponent_Factory(t) {\n      return new (t || RegisterComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RegisterComponent,\n      selectors: [[\"app-register\"]],\n      decls: 65,\n      vars: 19,\n      consts: [[1, \"register-container\"], [1, \"register-card\"], [\"mat-card-avatar\", \"\", 1, \"register-header-image\"], [1, \"register-form\", 3, \"ngSubmit\", \"formGroup\"], [1, \"name-row\"], [\"appearance\", \"outline\", 1, \"half-width\"], [\"matInput\", \"\", \"formControlName\", \"firstName\", \"placeholder\", \"Enter your first name\", \"autocomplete\", \"given-name\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"lastName\", \"placeholder\", \"Enter your last name\", \"autocomplete\", \"family-name\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email\", \"autocomplete\", \"email\"], [\"matInput\", \"\", \"type\", \"tel\", \"formControlName\", \"phoneNumber\", \"placeholder\", \"Enter your phone number\", \"autocomplete\", \"tel\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"placeholder\", \"Enter your password\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"confirmPassword\", \"placeholder\", \"Confirm your password\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"full-width\", \"register-button\", 3, \"disabled\"], [\"class\", \"spinning\", 4, \"ngIf\"], [1, \"register-actions\"], [1, \"login-link\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"spinning\"]],\n      template: function RegisterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"div\", 2)(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"person_add\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"mat-card-title\");\n          i0.ɵɵtext(7, \"Join BookCart\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"mat-card-subtitle\");\n          i0.ɵɵtext(9, \"Create your account to start shopping\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"mat-card-content\")(11, \"form\", 3);\n          i0.ɵɵlistener(\"ngSubmit\", function RegisterComponent_Template_form_ngSubmit_11_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(12, \"div\", 4)(13, \"mat-form-field\", 5)(14, \"mat-label\");\n          i0.ɵɵtext(15, \"First Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(16, \"input\", 6);\n          i0.ɵɵelementStart(17, \"mat-icon\", 7);\n          i0.ɵɵtext(18, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(19, RegisterComponent_mat_error_19_Template, 2, 1, \"mat-error\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"mat-form-field\", 5)(21, \"mat-label\");\n          i0.ɵɵtext(22, \"Last Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(23, \"input\", 9);\n          i0.ɵɵtemplate(24, RegisterComponent_mat_error_24_Template, 2, 1, \"mat-error\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"mat-form-field\", 10)(26, \"mat-label\");\n          i0.ɵɵtext(27, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(28, \"input\", 11);\n          i0.ɵɵelementStart(29, \"mat-icon\", 7);\n          i0.ɵɵtext(30, \"email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(31, RegisterComponent_mat_error_31_Template, 2, 1, \"mat-error\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"mat-form-field\", 10)(33, \"mat-label\");\n          i0.ɵɵtext(34, \"Phone Number (Optional)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(35, \"input\", 12);\n          i0.ɵɵelementStart(36, \"mat-icon\", 7);\n          i0.ɵɵtext(37, \"phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(38, RegisterComponent_mat_error_38_Template, 2, 1, \"mat-error\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"mat-form-field\", 10)(40, \"mat-label\");\n          i0.ɵɵtext(41, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(42, \"input\", 13);\n          i0.ɵɵelementStart(43, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_43_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(44, \"mat-icon\");\n          i0.ɵɵtext(45);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(46, RegisterComponent_mat_error_46_Template, 2, 1, \"mat-error\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"mat-form-field\", 10)(48, \"mat-label\");\n          i0.ɵɵtext(49, \"Confirm Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(50, \"input\", 15);\n          i0.ɵɵelementStart(51, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_51_listener() {\n            return ctx.hideConfirmPassword = !ctx.hideConfirmPassword;\n          });\n          i0.ɵɵelementStart(52, \"mat-icon\");\n          i0.ɵɵtext(53);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(54, RegisterComponent_mat_error_54_Template, 2, 1, \"mat-error\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"button\", 16);\n          i0.ɵɵtemplate(56, RegisterComponent_mat_icon_56_Template, 2, 0, \"mat-icon\", 17)(57, RegisterComponent_mat_icon_57_Template, 2, 0, \"mat-icon\", 8);\n          i0.ɵɵtext(58);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(59, \"mat-card-actions\", 18)(60, \"div\", 19)(61, \"span\");\n          i0.ɵɵtext(62, \"Already have an account?\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_63_listener() {\n            return ctx.goToLogin();\n          });\n          i0.ɵɵtext(64, \" Sign In \");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_2_0;\n          let tmp_3_0;\n          let tmp_4_0;\n          let tmp_9_0;\n          let tmp_14_0;\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"formGroup\", ctx.registerForm);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx.registerForm.get(\"firstName\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.registerForm.get(\"firstName\")) == null ? null : tmp_1_0.touched));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.registerForm.get(\"lastName\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.registerForm.get(\"lastName\")) == null ? null : tmp_2_0.touched));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_3_0.touched));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.registerForm.get(\"phoneNumber\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.registerForm.get(\"phoneNumber\")) == null ? null : tmp_4_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"aria-label\", \"Hide password\")(\"aria-pressed\", ctx.hidePassword);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ((tmp_9_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_9_0.invalid) && ((tmp_9_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_9_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hideConfirmPassword ? \"password\" : \"text\");\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"aria-label\", \"Hide password\")(\"aria-pressed\", ctx.hideConfirmPassword);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.hideConfirmPassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ((tmp_14_0 = ctx.registerForm.get(\"confirmPassword\")) == null ? null : tmp_14_0.invalid) && ((tmp_14_0 = ctx.registerForm.get(\"confirmPassword\")) == null ? null : tmp_14_0.touched) || (ctx.registerForm.errors == null ? null : ctx.registerForm.errors[\"passwordMismatch\"]));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.registerForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.isLoading ? \"Creating Account...\" : \"Create Account\", \" \");\n        }\n      },\n      dependencies: [i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.MatButton, i6.MatIconButton, i7.MatCard, i7.MatCardActions, i7.MatCardAvatar, i7.MatCardContent, i7.MatCardHeader, i7.MatCardSubtitle, i7.MatCardTitle, i8.MatFormField, i8.MatLabel, i8.MatError, i8.MatSuffix, i9.MatInput, i10.MatIcon],\n      styles: [\".register-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-height: calc(100vh - 64px);\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n\\n.register-card[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 500px;\\n  padding: 20px;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  border-radius: 16px;\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.register-header-image[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.register-header-image[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  height: 24px;\\n  width: 24px;\\n}\\n\\n.register-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n  margin-top: 20px;\\n}\\n\\n.name-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.half-width[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.register-button[_ngcontent-%COMP%] {\\n  height: 48px;\\n  font-size: 16px;\\n  font-weight: 500;\\n  margin-top: 8px;\\n}\\n\\n.register-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.spinning[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% { transform: rotate(0deg); }\\n  100% { transform: rotate(360deg); }\\n}\\n\\n.register-actions[_ngcontent-%COMP%] {\\n  padding: 16px 0 0 0;\\n  justify-content: center;\\n}\\n\\n.login-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 8px;\\n  text-align: center;\\n}\\n\\n.login-link[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n}\\n\\n\\n\\n  .success-snackbar {\\n  background-color: #4caf50 !important;\\n  color: white !important;\\n}\\n\\n  .error-snackbar {\\n  background-color: #f44336 !important;\\n  color: white !important;\\n}\\n\\n\\n\\n@media (max-width: 600px) {\\n  .register-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  \\n  .register-card[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  \\n  .name-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 16px;\\n  }\\n  \\n  .half-width[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "getFieldErrorMessage", "getPasswordConfirmErrorMessage", "passwordMatchValidator", "control", "password", "get", "confirmPassword", "value", "RegisterComponent", "constructor", "formBuilder", "authService", "router", "route", "snackBar", "isLoading", "hidePassword", "hideConfirmPassword", "returnUrl", "registerForm", "group", "firstName", "required", "<PERSON><PERSON><PERSON><PERSON>", "lastName", "email", "phoneNumber", "pattern", "validators", "ngOnInit", "snapshot", "queryParams", "isAuthenticated", "navigate", "onSubmit", "valid", "registerData", "undefined", "register", "subscribe", "next", "response", "open", "duration", "panelClass", "error", "errorMessage", "status", "errors", "Object", "values", "flat", "join", "message", "fieldName", "field", "touched", "getFieldDisplayName", "<PERSON><PERSON><PERSON><PERSON>", "confirmField", "displayNames", "goToLogin", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "ActivatedRoute", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "RegisterComponent_Template", "rf", "ctx", "ɵɵlistener", "RegisterComponent_Template_form_ngSubmit_11_listener", "ɵɵelement", "ɵɵtemplate", "RegisterComponent_mat_error_19_Template", "RegisterComponent_mat_error_24_Template", "RegisterComponent_mat_error_31_Template", "RegisterComponent_mat_error_38_Template", "RegisterComponent_Template_button_click_43_listener", "RegisterComponent_mat_error_46_Template", "RegisterComponent_Template_button_click_51_listener", "RegisterComponent_mat_error_54_Template", "RegisterComponent_mat_icon_56_Template", "RegisterComponent_mat_icon_57_Template", "RegisterComponent_Template_button_click_63_listener", "ɵɵproperty", "tmp_1_0", "invalid", "tmp_2_0", "tmp_3_0", "tmp_4_0", "ɵɵtextInterpolate", "tmp_9_0", "tmp_14_0"], "sources": ["C:\\Users\\<USER>\\Desktop\\BookCart\\bookcart-frontend\\src\\app\\auth\\register\\register.component.ts", "C:\\Users\\<USER>\\Desktop\\BookCart\\bookcart-frontend\\src\\app\\auth\\register\\register.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators, AbstractControl } from '@angular/forms';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { AuthService } from '../../shared/services/auth.service';\n\n// Custom validator for password confirmation\nfunction passwordMatchValidator(control: AbstractControl): { [key: string]: any } | null {\n  const password = control.get('password');\n  const confirmPassword = control.get('confirmPassword');\n  \n  if (password && confirmPassword && password.value !== confirmPassword.value) {\n    return { 'passwordMismatch': true };\n  }\n  return null;\n}\n\n@Component({\n  selector: 'app-register',\n  templateUrl: './register.component.html',\n  styleUrls: ['./register.component.css']\n})\nexport class RegisterComponent implements OnInit {\n  registerForm: FormGroup;\n  isLoading = false;\n  hidePassword = true;\n  hideConfirmPassword = true;\n  returnUrl = '/books';\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private route: ActivatedRoute,\n    private snackBar: MatSnackBar\n  ) {\n    this.registerForm = this.formBuilder.group({\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      phoneNumber: ['', [Validators.pattern(/^\\+?[\\d\\s\\-\\(\\)]+$/)]],\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      confirmPassword: ['', [Validators.required]]\n    }, { validators: passwordMatchValidator });\n  }\n\n  ngOnInit(): void {\n    // Get return url from route parameters or default to '/books'\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/books';\n\n    // Redirect if already logged in\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate([this.returnUrl]);\n    }\n  }\n\n  onSubmit(): void {\n    if (this.registerForm.valid && !this.isLoading) {\n      this.isLoading = true;\n      \n      const registerData = {\n        ...this.registerForm.value,\n        phoneNumber: this.registerForm.value.phoneNumber || undefined\n      };\n      \n      this.authService.register(registerData).subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          this.snackBar.open('Registration successful! Welcome to BookCart!', 'Close', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n          this.router.navigate([this.returnUrl]);\n        },\n        error: (error) => {\n          this.isLoading = false;\n          let errorMessage = 'Registration failed. Please try again.';\n          \n          if (error.status === 400) {\n            if (error.error?.errors) {\n              // Handle validation errors\n              const errors = Object.values(error.error.errors).flat();\n              errorMessage = errors.join(', ');\n            } else if (error.error?.message) {\n              errorMessage = error.error.message;\n            }\n          }\n          \n          this.snackBar.open(errorMessage, 'Close', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    }\n  }\n\n  getFieldErrorMessage(fieldName: string): string {\n    const field = this.registerForm.get(fieldName);\n    if (!field?.errors || !field.touched) return '';\n\n    const errors = field.errors;\n    \n    if (errors['required']) {\n      return `${this.getFieldDisplayName(fieldName)} is required`;\n    }\n    if (errors['email']) {\n      return 'Please enter a valid email address';\n    }\n    if (errors['minlength']) {\n      const requiredLength = errors['minlength'].requiredLength;\n      return `${this.getFieldDisplayName(fieldName)} must be at least ${requiredLength} characters long`;\n    }\n    if (errors['pattern'] && fieldName === 'phoneNumber') {\n      return 'Please enter a valid phone number';\n    }\n    \n    return '';\n  }\n\n  getPasswordConfirmErrorMessage(): string {\n    const confirmField = this.registerForm.get('confirmPassword');\n    if (!confirmField?.errors || !confirmField.touched) return '';\n\n    if (confirmField.errors['required']) {\n      return 'Password confirmation is required';\n    }\n    if (this.registerForm.errors?.['passwordMismatch']) {\n      return 'Passwords do not match';\n    }\n    \n    return '';\n  }\n\n  private getFieldDisplayName(fieldName: string): string {\n    const displayNames: { [key: string]: string } = {\n      firstName: 'First name',\n      lastName: 'Last name',\n      email: 'Email',\n      phoneNumber: 'Phone number',\n      password: 'Password',\n      confirmPassword: 'Password confirmation'\n    };\n    return displayNames[fieldName] || fieldName;\n  }\n\n  goToLogin(): void {\n    this.router.navigate(['/login'], { \n      queryParams: { returnUrl: this.returnUrl } \n    });\n  }\n}\n", "<div class=\"register-container\">\n  <mat-card class=\"register-card\">\n    <mat-card-header>\n      <div mat-card-avatar class=\"register-header-image\">\n        <mat-icon>person_add</mat-icon>\n      </div>\n      <mat-card-title>Join <PERSON><PERSON><PERSON></mat-card-title>\n      <mat-card-subtitle>Create your account to start shopping</mat-card-subtitle>\n    </mat-card-header>\n\n    <mat-card-content>\n      <form [formGroup]=\"registerForm\" (ngSubmit)=\"onSubmit()\" class=\"register-form\">\n        <!-- Name Fields Row -->\n        <div class=\"name-row\">\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\n            <mat-label>First Name</mat-label>\n            <input matInput \n                   formControlName=\"firstName\"\n                   placeholder=\"Enter your first name\"\n                   autocomplete=\"given-name\">\n            <mat-icon matSuffix>person</mat-icon>\n            <mat-error *ngIf=\"registerForm.get('firstName')?.invalid && registerForm.get('firstName')?.touched\">\n              {{ getFieldErrorMessage('firstName') }}\n            </mat-error>\n          </mat-form-field>\n\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\n            <mat-label>Last Name</mat-label>\n            <input matInput \n                   formControlName=\"lastName\"\n                   placeholder=\"Enter your last name\"\n                   autocomplete=\"family-name\">\n            <mat-error *ngIf=\"registerForm.get('lastName')?.invalid && registerForm.get('lastName')?.touched\">\n              {{ getFieldErrorMessage('lastName') }}\n            </mat-error>\n          </mat-form-field>\n        </div>\n\n        <!-- Email Field -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Email</mat-label>\n          <input matInput \n                 type=\"email\" \n                 formControlName=\"email\"\n                 placeholder=\"Enter your email\"\n                 autocomplete=\"email\">\n          <mat-icon matSuffix>email</mat-icon>\n          <mat-error *ngIf=\"registerForm.get('email')?.invalid && registerForm.get('email')?.touched\">\n            {{ getFieldErrorMessage('email') }}\n          </mat-error>\n        </mat-form-field>\n\n        <!-- Phone Number Field -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Phone Number (Optional)</mat-label>\n          <input matInput \n                 type=\"tel\" \n                 formControlName=\"phoneNumber\"\n                 placeholder=\"Enter your phone number\"\n                 autocomplete=\"tel\">\n          <mat-icon matSuffix>phone</mat-icon>\n          <mat-error *ngIf=\"registerForm.get('phoneNumber')?.invalid && registerForm.get('phoneNumber')?.touched\">\n            {{ getFieldErrorMessage('phoneNumber') }}\n          </mat-error>\n        </mat-form-field>\n\n        <!-- Password Field -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Password</mat-label>\n          <input matInput \n                 [type]=\"hidePassword ? 'password' : 'text'\"\n                 formControlName=\"password\"\n                 placeholder=\"Enter your password\"\n                 autocomplete=\"new-password\">\n          <button mat-icon-button \n                  matSuffix \n                  (click)=\"hidePassword = !hidePassword\"\n                  [attr.aria-label]=\"'Hide password'\"\n                  [attr.aria-pressed]=\"hidePassword\"\n                  type=\"button\">\n            <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>\n          </button>\n          <mat-error *ngIf=\"registerForm.get('password')?.invalid && registerForm.get('password')?.touched\">\n            {{ getFieldErrorMessage('password') }}\n          </mat-error>\n        </mat-form-field>\n\n        <!-- Confirm Password Field -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Confirm Password</mat-label>\n          <input matInput \n                 [type]=\"hideConfirmPassword ? 'password' : 'text'\"\n                 formControlName=\"confirmPassword\"\n                 placeholder=\"Confirm your password\"\n                 autocomplete=\"new-password\">\n          <button mat-icon-button \n                  matSuffix \n                  (click)=\"hideConfirmPassword = !hideConfirmPassword\"\n                  [attr.aria-label]=\"'Hide password'\"\n                  [attr.aria-pressed]=\"hideConfirmPassword\"\n                  type=\"button\">\n            <mat-icon>{{ hideConfirmPassword ? 'visibility_off' : 'visibility' }}</mat-icon>\n          </button>\n          <mat-error *ngIf=\"(registerForm.get('confirmPassword')?.invalid && registerForm.get('confirmPassword')?.touched) || registerForm.errors?.['passwordMismatch']\">\n            {{ getPasswordConfirmErrorMessage() }}\n          </mat-error>\n        </mat-form-field>\n\n        <!-- Submit Button -->\n        <button mat-raised-button \n                color=\"primary\" \n                type=\"submit\"\n                class=\"full-width register-button\"\n                [disabled]=\"registerForm.invalid || isLoading\">\n          <mat-icon *ngIf=\"isLoading\" class=\"spinning\">refresh</mat-icon>\n          <mat-icon *ngIf=\"!isLoading\">person_add</mat-icon>\n          {{ isLoading ? 'Creating Account...' : 'Create Account' }}\n        </button>\n      </form>\n    </mat-card-content>\n\n    <mat-card-actions class=\"register-actions\">\n      <div class=\"login-link\">\n        <span>Already have an account?</span>\n        <button mat-button color=\"primary\" (click)=\"goToLogin()\">\n          Sign In\n        </button>\n      </div>\n    </mat-card-actions>\n  </mat-card>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAyB,gBAAgB;;;;;;;;;;;;;;ICoBxEC,EAAA,CAAAC,cAAA,gBAAoG;IAClGD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,oBAAA,mBACF;;;;;IASAP,EAAA,CAAAC,cAAA,gBAAkG;IAChGD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,oBAAA,kBACF;;;;;IAaFP,EAAA,CAAAC,cAAA,gBAA4F;IAC1FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,oBAAA,eACF;;;;;IAYAP,EAAA,CAAAC,cAAA,gBAAwG;IACtGD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,oBAAA,qBACF;;;;;IAmBAP,EAAA,CAAAC,cAAA,gBAAkG;IAChGD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,oBAAA,kBACF;;;;;IAmBAP,EAAA,CAAAC,cAAA,gBAA+J;IAC7JD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAE,8BAAA,QACF;;;;;IASAR,EAAA,CAAAC,cAAA,mBAA6C;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAC/DH,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;AD7G5D;AACA,SAASM,sBAAsBA,CAACC,OAAwB;EACtD,MAAMC,QAAQ,GAAGD,OAAO,CAACE,GAAG,CAAC,UAAU,CAAC;EACxC,MAAMC,eAAe,GAAGH,OAAO,CAACE,GAAG,CAAC,iBAAiB,CAAC;EAEtD,IAAID,QAAQ,IAAIE,eAAe,IAAIF,QAAQ,CAACG,KAAK,KAAKD,eAAe,CAACC,KAAK,EAAE;IAC3E,OAAO;MAAE,kBAAkB,EAAE;IAAI,CAAE;;EAErC,OAAO,IAAI;AACb;AAOA,OAAM,MAAOC,iBAAiB;EAO5BC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc,EACdC,KAAqB,EACrBC,QAAqB;IAJrB,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IAVlB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,mBAAmB,GAAG,IAAI;IAC1B,KAAAC,SAAS,GAAG,QAAQ;IASlB,IAAI,CAACC,YAAY,GAAG,IAAI,CAACT,WAAW,CAACU,KAAK,CAAC;MACzCC,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC7B,UAAU,CAAC8B,QAAQ,EAAE9B,UAAU,CAAC+B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/DC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAChC,UAAU,CAAC8B,QAAQ,EAAE9B,UAAU,CAAC+B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DE,KAAK,EAAE,CAAC,EAAE,EAAE,CAACjC,UAAU,CAAC8B,QAAQ,EAAE9B,UAAU,CAACiC,KAAK,CAAC,CAAC;MACpDC,WAAW,EAAE,CAAC,EAAE,EAAE,CAAClC,UAAU,CAACmC,OAAO,CAAC,oBAAoB,CAAC,CAAC,CAAC;MAC7DvB,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACZ,UAAU,CAAC8B,QAAQ,EAAE9B,UAAU,CAAC+B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DjB,eAAe,EAAE,CAAC,EAAE,EAAE,CAACd,UAAU,CAAC8B,QAAQ,CAAC;KAC5C,EAAE;MAAEM,UAAU,EAAE1B;IAAsB,CAAE,CAAC;EAC5C;EAEA2B,QAAQA,CAAA;IACN;IACA,IAAI,CAACX,SAAS,GAAG,IAAI,CAACL,KAAK,CAACiB,QAAQ,CAACC,WAAW,CAAC,WAAW,CAAC,IAAI,QAAQ;IAEzE;IACA,IAAI,IAAI,CAACpB,WAAW,CAACqB,eAAe,EAAE,EAAE;MACtC,IAAI,CAACpB,MAAM,CAACqB,QAAQ,CAAC,CAAC,IAAI,CAACf,SAAS,CAAC,CAAC;;EAE1C;EAEAgB,QAAQA,CAAA;IACN,IAAI,IAAI,CAACf,YAAY,CAACgB,KAAK,IAAI,CAAC,IAAI,CAACpB,SAAS,EAAE;MAC9C,IAAI,CAACA,SAAS,GAAG,IAAI;MAErB,MAAMqB,YAAY,GAAG;QACnB,GAAG,IAAI,CAACjB,YAAY,CAACZ,KAAK;QAC1BmB,WAAW,EAAE,IAAI,CAACP,YAAY,CAACZ,KAAK,CAACmB,WAAW,IAAIW;OACrD;MAED,IAAI,CAAC1B,WAAW,CAAC2B,QAAQ,CAACF,YAAY,CAAC,CAACG,SAAS,CAAC;QAChDC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAC1B,SAAS,GAAG,KAAK;UACtB,IAAI,CAACD,QAAQ,CAAC4B,IAAI,CAAC,+CAA+C,EAAE,OAAO,EAAE;YAC3EC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,kBAAkB;WAChC,CAAC;UACF,IAAI,CAAChC,MAAM,CAACqB,QAAQ,CAAC,CAAC,IAAI,CAACf,SAAS,CAAC,CAAC;QACxC,CAAC;QACD2B,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAC9B,SAAS,GAAG,KAAK;UACtB,IAAI+B,YAAY,GAAG,wCAAwC;UAE3D,IAAID,KAAK,CAACE,MAAM,KAAK,GAAG,EAAE;YACxB,IAAIF,KAAK,CAACA,KAAK,EAAEG,MAAM,EAAE;cACvB;cACA,MAAMA,MAAM,GAAGC,MAAM,CAACC,MAAM,CAACL,KAAK,CAACA,KAAK,CAACG,MAAM,CAAC,CAACG,IAAI,EAAE;cACvDL,YAAY,GAAGE,MAAM,CAACI,IAAI,CAAC,IAAI,CAAC;aACjC,MAAM,IAAIP,KAAK,CAACA,KAAK,EAAEQ,OAAO,EAAE;cAC/BP,YAAY,GAAGD,KAAK,CAACA,KAAK,CAACQ,OAAO;;;UAItC,IAAI,CAACvC,QAAQ,CAAC4B,IAAI,CAACI,YAAY,EAAE,OAAO,EAAE;YACxCH,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,gBAAgB;WAC9B,CAAC;QACJ;OACD,CAAC;;EAEN;EAEA5C,oBAAoBA,CAACsD,SAAiB;IACpC,MAAMC,KAAK,GAAG,IAAI,CAACpC,YAAY,CAACd,GAAG,CAACiD,SAAS,CAAC;IAC9C,IAAI,CAACC,KAAK,EAAEP,MAAM,IAAI,CAACO,KAAK,CAACC,OAAO,EAAE,OAAO,EAAE;IAE/C,MAAMR,MAAM,GAAGO,KAAK,CAACP,MAAM;IAE3B,IAAIA,MAAM,CAAC,UAAU,CAAC,EAAE;MACtB,OAAO,GAAG,IAAI,CAACS,mBAAmB,CAACH,SAAS,CAAC,cAAc;;IAE7D,IAAIN,MAAM,CAAC,OAAO,CAAC,EAAE;MACnB,OAAO,oCAAoC;;IAE7C,IAAIA,MAAM,CAAC,WAAW,CAAC,EAAE;MACvB,MAAMU,cAAc,GAAGV,MAAM,CAAC,WAAW,CAAC,CAACU,cAAc;MACzD,OAAO,GAAG,IAAI,CAACD,mBAAmB,CAACH,SAAS,CAAC,qBAAqBI,cAAc,kBAAkB;;IAEpG,IAAIV,MAAM,CAAC,SAAS,CAAC,IAAIM,SAAS,KAAK,aAAa,EAAE;MACpD,OAAO,mCAAmC;;IAG5C,OAAO,EAAE;EACX;EAEArD,8BAA8BA,CAAA;IAC5B,MAAM0D,YAAY,GAAG,IAAI,CAACxC,YAAY,CAACd,GAAG,CAAC,iBAAiB,CAAC;IAC7D,IAAI,CAACsD,YAAY,EAAEX,MAAM,IAAI,CAACW,YAAY,CAACH,OAAO,EAAE,OAAO,EAAE;IAE7D,IAAIG,YAAY,CAACX,MAAM,CAAC,UAAU,CAAC,EAAE;MACnC,OAAO,mCAAmC;;IAE5C,IAAI,IAAI,CAAC7B,YAAY,CAAC6B,MAAM,GAAG,kBAAkB,CAAC,EAAE;MAClD,OAAO,wBAAwB;;IAGjC,OAAO,EAAE;EACX;EAEQS,mBAAmBA,CAACH,SAAiB;IAC3C,MAAMM,YAAY,GAA8B;MAC9CvC,SAAS,EAAE,YAAY;MACvBG,QAAQ,EAAE,WAAW;MACrBC,KAAK,EAAE,OAAO;MACdC,WAAW,EAAE,cAAc;MAC3BtB,QAAQ,EAAE,UAAU;MACpBE,eAAe,EAAE;KAClB;IACD,OAAOsD,YAAY,CAACN,SAAS,CAAC,IAAIA,SAAS;EAC7C;EAEAO,SAASA,CAAA;IACP,IAAI,CAACjD,MAAM,CAACqB,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;MAC/BF,WAAW,EAAE;QAAEb,SAAS,EAAE,IAAI,CAACA;MAAS;KACzC,CAAC;EACJ;;;uBAhIWV,iBAAiB,EAAAf,EAAA,CAAAqE,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAvE,EAAA,CAAAqE,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAzE,EAAA,CAAAqE,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA3E,EAAA,CAAAqE,iBAAA,CAAAK,EAAA,CAAAE,cAAA,GAAA5E,EAAA,CAAAqE,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAjB/D,iBAAiB;MAAAgE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClBtBrF,EAJR,CAAAC,cAAA,aAAgC,kBACE,sBACb,aACoC,eACvC;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UACtBF,EADsB,CAAAG,YAAA,EAAW,EAC3B;UACNH,EAAA,CAAAC,cAAA,qBAAgB;UAAAD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UAC9CH,EAAA,CAAAC,cAAA,wBAAmB;UAAAD,EAAA,CAAAE,MAAA,4CAAqC;UAC1DF,EAD0D,CAAAG,YAAA,EAAoB,EAC5D;UAGhBH,EADF,CAAAC,cAAA,wBAAkB,eAC+D;UAA9CD,EAAA,CAAAuF,UAAA,sBAAAC,qDAAA;YAAA,OAAYF,GAAA,CAAA7C,QAAA,EAAU;UAAA,EAAC;UAIlDzC,EAFJ,CAAAC,cAAA,cAAsB,yBACoC,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACjCH,EAAA,CAAAyF,SAAA,gBAGiC;UACjCzF,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACrCH,EAAA,CAAA0F,UAAA,KAAAC,uCAAA,uBAAoG;UAGtG3F,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAChCH,EAAA,CAAAyF,SAAA,gBAGkC;UAClCzF,EAAA,CAAA0F,UAAA,KAAAE,uCAAA,uBAAkG;UAItG5F,EADE,CAAAG,YAAA,EAAiB,EACb;UAIJH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAAyF,SAAA,iBAI4B;UAC5BzF,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACpCH,EAAA,CAAA0F,UAAA,KAAAG,uCAAA,uBAA4F;UAG9F7F,EAAA,CAAAG,YAAA,EAAiB;UAIfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC9CH,EAAA,CAAAyF,SAAA,iBAI0B;UAC1BzF,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACpCH,EAAA,CAAA0F,UAAA,KAAAI,uCAAA,uBAAwG;UAG1G9F,EAAA,CAAAG,YAAA,EAAiB;UAIfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAyF,SAAA,iBAImC;UACnCzF,EAAA,CAAAC,cAAA,kBAKsB;UAHdD,EAAA,CAAAuF,UAAA,mBAAAQ,oDAAA;YAAA,OAAAT,GAAA,CAAA/D,YAAA,IAAA+D,GAAA,CAAA/D,YAAA;UAAA,EAAsC;UAI5CvB,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAoD;UAChEF,EADgE,CAAAG,YAAA,EAAW,EAClE;UACTH,EAAA,CAAA0F,UAAA,KAAAM,uCAAA,uBAAkG;UAGpGhG,EAAA,CAAAG,YAAA,EAAiB;UAIfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAyF,SAAA,iBAImC;UACnCzF,EAAA,CAAAC,cAAA,kBAKsB;UAHdD,EAAA,CAAAuF,UAAA,mBAAAU,oDAAA;YAAA,OAAAX,GAAA,CAAA9D,mBAAA,IAAA8D,GAAA,CAAA9D,mBAAA;UAAA,EAAoD;UAI1DxB,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAA2D;UACvEF,EADuE,CAAAG,YAAA,EAAW,EACzE;UACTH,EAAA,CAAA0F,UAAA,KAAAQ,uCAAA,uBAA+J;UAGjKlG,EAAA,CAAAG,YAAA,EAAiB;UAGjBH,EAAA,CAAAC,cAAA,kBAIuD;UAErDD,EADA,CAAA0F,UAAA,KAAAS,sCAAA,uBAA6C,KAAAC,sCAAA,sBAChB;UAC7BpG,EAAA,CAAAE,MAAA,IACF;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACJ,EACU;UAIfH,EAFJ,CAAAC,cAAA,4BAA2C,eACjB,YAChB;UAAAD,EAAA,CAAAE,MAAA,gCAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACrCH,EAAA,CAAAC,cAAA,kBAAyD;UAAtBD,EAAA,CAAAuF,UAAA,mBAAAc,oDAAA;YAAA,OAASf,GAAA,CAAAlB,SAAA,EAAW;UAAA,EAAC;UACtDpE,EAAA,CAAAE,MAAA,iBACF;UAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACW,EACV,EACP;;;;;;;;;UAvHMH,EAAA,CAAAI,SAAA,IAA0B;UAA1BJ,EAAA,CAAAsG,UAAA,cAAAhB,GAAA,CAAA5D,YAAA,CAA0B;UAUd1B,EAAA,CAAAI,SAAA,GAAsF;UAAtFJ,EAAA,CAAAsG,UAAA,WAAAC,OAAA,GAAAjB,GAAA,CAAA5D,YAAA,CAAAd,GAAA,gCAAA2F,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAjB,GAAA,CAAA5D,YAAA,CAAAd,GAAA,gCAAA2F,OAAA,CAAAxC,OAAA,EAAsF;UAWtF/D,EAAA,CAAAI,SAAA,GAAoF;UAApFJ,EAAA,CAAAsG,UAAA,WAAAG,OAAA,GAAAnB,GAAA,CAAA5D,YAAA,CAAAd,GAAA,+BAAA6F,OAAA,CAAAD,OAAA,OAAAC,OAAA,GAAAnB,GAAA,CAAA5D,YAAA,CAAAd,GAAA,+BAAA6F,OAAA,CAAA1C,OAAA,EAAoF;UAetF/D,EAAA,CAAAI,SAAA,GAA8E;UAA9EJ,EAAA,CAAAsG,UAAA,WAAAI,OAAA,GAAApB,GAAA,CAAA5D,YAAA,CAAAd,GAAA,4BAAA8F,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAApB,GAAA,CAAA5D,YAAA,CAAAd,GAAA,4BAAA8F,OAAA,CAAA3C,OAAA,EAA8E;UAc9E/D,EAAA,CAAAI,SAAA,GAA0F;UAA1FJ,EAAA,CAAAsG,UAAA,WAAAK,OAAA,GAAArB,GAAA,CAAA5D,YAAA,CAAAd,GAAA,kCAAA+F,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAArB,GAAA,CAAA5D,YAAA,CAAAd,GAAA,kCAAA+F,OAAA,CAAA5C,OAAA,EAA0F;UAS/F/D,EAAA,CAAAI,SAAA,GAA2C;UAA3CJ,EAAA,CAAAsG,UAAA,SAAAhB,GAAA,CAAA/D,YAAA,uBAA2C;UAO1CvB,EAAA,CAAAI,SAAA,EAAmC;;UAG/BJ,EAAA,CAAAI,SAAA,GAAoD;UAApDJ,EAAA,CAAA4G,iBAAA,CAAAtB,GAAA,CAAA/D,YAAA,mCAAoD;UAEpDvB,EAAA,CAAAI,SAAA,EAAoF;UAApFJ,EAAA,CAAAsG,UAAA,WAAAO,OAAA,GAAAvB,GAAA,CAAA5D,YAAA,CAAAd,GAAA,+BAAAiG,OAAA,CAAAL,OAAA,OAAAK,OAAA,GAAAvB,GAAA,CAAA5D,YAAA,CAAAd,GAAA,+BAAAiG,OAAA,CAAA9C,OAAA,EAAoF;UASzF/D,EAAA,CAAAI,SAAA,GAAkD;UAAlDJ,EAAA,CAAAsG,UAAA,SAAAhB,GAAA,CAAA9D,mBAAA,uBAAkD;UAOjDxB,EAAA,CAAAI,SAAA,EAAmC;;UAG/BJ,EAAA,CAAAI,SAAA,GAA2D;UAA3DJ,EAAA,CAAA4G,iBAAA,CAAAtB,GAAA,CAAA9D,mBAAA,mCAA2D;UAE3DxB,EAAA,CAAAI,SAAA,EAAiJ;UAAjJJ,EAAA,CAAAsG,UAAA,WAAAQ,QAAA,GAAAxB,GAAA,CAAA5D,YAAA,CAAAd,GAAA,sCAAAkG,QAAA,CAAAN,OAAA,OAAAM,QAAA,GAAAxB,GAAA,CAAA5D,YAAA,CAAAd,GAAA,sCAAAkG,QAAA,CAAA/C,OAAA,MAAAuB,GAAA,CAAA5D,YAAA,CAAA6B,MAAA,kBAAA+B,GAAA,CAAA5D,YAAA,CAAA6B,MAAA,sBAAiJ;UAUvJvD,EAAA,CAAAI,SAAA,EAA8C;UAA9CJ,EAAA,CAAAsG,UAAA,aAAAhB,GAAA,CAAA5D,YAAA,CAAA8E,OAAA,IAAAlB,GAAA,CAAAhE,SAAA,CAA8C;UACzCtB,EAAA,CAAAI,SAAA,EAAe;UAAfJ,EAAA,CAAAsG,UAAA,SAAAhB,GAAA,CAAAhE,SAAA,CAAe;UACftB,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAAsG,UAAA,UAAAhB,GAAA,CAAAhE,SAAA,CAAgB;UAC3BtB,EAAA,CAAAI,SAAA,EACF;UADEJ,EAAA,CAAAK,kBAAA,MAAAiF,GAAA,CAAAhE,SAAA,iDACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}