{"version": 3, "file": "tabs.mjs", "sources": ["../../../../../../src/material/tabs/tab-content.ts", "../../../../../../src/material/tabs/tab-label.ts", "../../../../../../src/material/tabs/tab.ts", "../../../../../../src/material/tabs/tab.html", "../../../../../../src/material/tabs/ink-bar.ts", "../../../../../../src/material/tabs/tab-label-wrapper.ts", "../../../../../../src/material/tabs/paginated-tab-header.ts", "../../../../../../src/material/tabs/tab-header.ts", "../../../../../../src/material/tabs/tab-header.html", "../../../../../../src/material/tabs/tab-config.ts", "../../../../../../src/material/tabs/tabs-animations.ts", "../../../../../../src/material/tabs/tab-body.ts", "../../../../../../src/material/tabs/tab-body.html", "../../../../../../src/material/tabs/tab-group.ts", "../../../../../../src/material/tabs/tab-group.html", "../../../../../../src/material/tabs/tab-nav-bar/tab-nav-bar.ts", "../../../../../../src/material/tabs/tab-nav-bar/tab-nav-bar.html", "../../../../../../src/material/tabs/tab-nav-bar/tab-link.html", "../../../../../../src/material/tabs/module.ts", "../../../../../../src/material/tabs/tabs_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, InjectionToken, TemplateRef} from '@angular/core';\n\n/**\n * Injection token that can be used to reference instances of `MatTabContent`. It serves as\n * alternative token to the actual `MatTabContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nexport const MAT_TAB_CONTENT = new InjectionToken<MatTabContent>('MatTabContent');\n\n/** Decorates the `ng-template` tags and reads out the template from it. */\n@Directive({\n  selector: '[matTabContent]',\n  providers: [{provide: MAT_TAB_CONTENT, useExisting: MatTabContent}],\n  standalone: true,\n})\nexport class MatTabContent {\n  constructor(/** Content for the tab. */ public template: TemplateRef<any>) {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Directive,\n  Inject,\n  InjectionToken,\n  Optional,\n  TemplateRef,\n  ViewContainerRef,\n} from '@angular/core';\nimport {CdkPortal} from '@angular/cdk/portal';\n\n/**\n * Injection token that can be used to reference instances of `MatTabLabel`. It serves as\n * alternative token to the actual `MatTabLabel` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nexport const MAT_TAB_LABEL = new InjectionToken<MatTabLabel>('MatTabLabel');\n\n/**\n * Used to provide a tab label to a tab without causing a circular dependency.\n * @docs-private\n */\nexport const MAT_TAB = new InjectionToken<any>('MAT_TAB');\n\n/** Used to flag tab labels for use with the portal directive */\n@Directive({\n  selector: '[mat-tab-label], [matTabLabel]',\n  providers: [{provide: MAT_TAB_LABEL, useExisting: MatTabLabel}],\n  standalone: true,\n})\nexport class MatTabLabel extends CdkPortal {\n  constructor(\n    templateRef: TemplateRef<any>,\n    viewContainerRef: ViewContainerRef,\n    @Inject(MAT_TAB) @Optional() public _closestTab: any,\n  ) {\n    super(templateRef, viewContainerRef);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  ContentChild,\n  Inject,\n  InjectionToken,\n  Input,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  Optional,\n  SimpleChanges,\n  TemplateRef,\n  ViewChild,\n  ViewContainerRef,\n  ViewEncapsulation,\n  booleanAttribute,\n} from '@angular/core';\nimport {MatTabContent} from './tab-content';\nimport {MAT_TAB, MatTabLabel} from './tab-label';\nimport {TemplatePortal} from '@angular/cdk/portal';\nimport {Subject} from 'rxjs';\n\n/**\n * Used to provide a tab group to a tab without causing a circular dependency.\n * @docs-private\n */\nexport const MAT_TAB_GROUP = new InjectionToken<any>('MAT_TAB_GROUP');\n\n@Component({\n  selector: 'mat-tab',\n  // Note that usually we'd go through a bit more trouble and set up another class so that\n  // the inlined template of `MatTab` isn't duplicated, however the template is small enough\n  // that creating the extra class will generate more code than just duplicating the template.\n  templateUrl: 'tab.html',\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  encapsulation: ViewEncapsulation.None,\n  exportAs: 'matTab',\n  providers: [{provide: MAT_TAB, useExisting: MatTab}],\n  standalone: true,\n  host: {\n    // This element will be rendered on the server in order to support hydration.\n    // Hide it so it doesn't cause a layout shift when it's removed on the client.\n    'hidden': '',\n  },\n})\nexport class MatTab implements OnInit, OnChanges, OnDestroy {\n  /** whether the tab is disabled. */\n  @Input({transform: booleanAttribute})\n  disabled: boolean = false;\n\n  /** Content for the tab label given by `<ng-template mat-tab-label>`. */\n  @ContentChild(MatTabLabel)\n  get templateLabel(): MatTabLabel {\n    return this._templateLabel;\n  }\n  set templateLabel(value: MatTabLabel) {\n    this._setTemplateLabelInput(value);\n  }\n  private _templateLabel: MatTabLabel;\n\n  /**\n   * Template provided in the tab content that will be used if present, used to enable lazy-loading\n   */\n  @ContentChild(MatTabContent, {read: TemplateRef, static: true})\n  // We need an initializer here to avoid a TS error. The value will be set in `ngAfterViewInit`.\n  private _explicitContent: TemplateRef<any> = undefined!;\n\n  /** Template inside the MatTab view that contains an `<ng-content>`. */\n  @ViewChild(TemplateRef, {static: true}) _implicitContent: TemplateRef<any>;\n\n  /** Plain text label for the tab, used when there is no template label. */\n  @Input('label') textLabel: string = '';\n\n  /** Aria label for the tab. */\n  @Input('aria-label') ariaLabel: string;\n\n  /**\n   * Reference to the element that the tab is labelled by.\n   * Will be cleared if `aria-label` is set at the same time.\n   */\n  @Input('aria-labelledby') ariaLabelledby: string;\n\n  /** Classes to be passed to the tab label inside the mat-tab-header container. */\n  @Input() labelClass: string | string[];\n\n  /** Classes to be passed to the tab mat-tab-body container. */\n  @Input() bodyClass: string | string[];\n\n  /** Portal that will be the hosted content of the tab */\n  private _contentPortal: TemplatePortal | null = null;\n\n  /** @docs-private */\n  get content(): TemplatePortal | null {\n    return this._contentPortal;\n  }\n\n  /** Emits whenever the internal state of the tab changes. */\n  readonly _stateChanges = new Subject<void>();\n\n  /**\n   * The relatively indexed position where 0 represents the center, negative is left, and positive\n   * represents the right.\n   */\n  position: number | null = null;\n\n  /**\n   * The initial relatively index origin of the tab if it was created and selected after there\n   * was already a selected tab. Provides context of what position the tab should originate from.\n   */\n  origin: number | null = null;\n\n  /**\n   * Whether the tab is currently active.\n   */\n  isActive = false;\n\n  constructor(\n    private _viewContainerRef: ViewContainerRef,\n    @Inject(MAT_TAB_GROUP) @Optional() public _closestTabGroup: any,\n  ) {}\n\n  ngOnChanges(changes: SimpleChanges): void {\n    if (changes.hasOwnProperty('textLabel') || changes.hasOwnProperty('disabled')) {\n      this._stateChanges.next();\n    }\n  }\n\n  ngOnDestroy(): void {\n    this._stateChanges.complete();\n  }\n\n  ngOnInit(): void {\n    this._contentPortal = new TemplatePortal(\n      this._explicitContent || this._implicitContent,\n      this._viewContainerRef,\n    );\n  }\n\n  /**\n   * This has been extracted to a util because of TS 4 and VE.\n   * View Engine doesn't support property rename inheritance.\n   * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n   * @docs-private\n   */\n  private _setTemplateLabelInput(value: MatTabLabel | undefined) {\n    // Only update the label if the query managed to find one. This works around an issue where a\n    // user may have manually set `templateLabel` during creation mode, which would then get\n    // clobbered by `undefined` when the query resolves. Also note that we check that the closest\n    // tab matches the current one so that we don't pick up labels from nested tabs.\n    if (value && value._closestTab === this) {\n      this._templateLabel = value;\n    }\n  }\n}\n", "<!-- Create a template for the content of the <mat-tab> so that we can grab a reference to this\n    TemplateRef and use it in a Portal to render the tab content in the appropriate place in the\n    tab-group. -->\n<ng-template><ng-content></ng-content></ng-template>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Directive,\n  ElementRef,\n  InjectionToken,\n  Input,\n  OnDestroy,\n  OnInit,\n  QueryList,\n  booleanAttribute,\n  inject,\n} from '@angular/core';\n\n/**\n * Item inside a tab header relative to which the ink bar can be aligned.\n * @docs-private\n */\nexport interface MatInkBarItem extends OnInit, OnDestroy {\n  elementRef: ElementRef<HTMLElement>;\n  activateInkBar(previousIndicatorClientRect?: DOMRect): void;\n  deactivateInkBar(): void;\n  fitInkBarToContent: boolean;\n}\n\n/** Class that is applied when a tab indicator is active. */\nconst ACTIVE_CLASS = 'mdc-tab-indicator--active';\n\n/** Class that is applied when the tab indicator should not transition. */\nconst NO_TRANSITION_CLASS = 'mdc-tab-indicator--no-transition';\n\n/**\n * Abstraction around the MDC tab indicator that acts as the tab header's ink bar.\n * @docs-private\n */\nexport class MatInkBar {\n  /** Item to which the ink bar is aligned currently. */\n  private _currentItem: MatInkBarItem | undefined;\n\n  constructor(private _items: QueryList<MatInkBarItem>) {}\n\n  /** Hides the ink bar. */\n  hide() {\n    this._items.forEach(item => item.deactivateInkBar());\n  }\n\n  /** Aligns the ink bar to a DOM node. */\n  alignToElement(element: HTMLElement) {\n    const correspondingItem = this._items.find(item => item.elementRef.nativeElement === element);\n    const currentItem = this._currentItem;\n\n    if (correspondingItem === currentItem) {\n      return;\n    }\n\n    currentItem?.deactivateInkBar();\n\n    if (correspondingItem) {\n      const domRect = currentItem?.elementRef.nativeElement.getBoundingClientRect?.();\n\n      // The ink bar won't animate unless we give it the `DOMRect` of the previous item.\n      correspondingItem.activateInkBar(domRect);\n      this._currentItem = correspondingItem;\n    }\n  }\n}\n\n@Directive()\nexport abstract class InkBarItem implements OnInit, OnDestroy {\n  private _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _inkBarElement: HTMLElement | null;\n  private _inkBarContentElement: HTMLElement | null;\n  private _fitToContent = false;\n\n  /** Whether the ink bar should fit to the entire tab or just its content. */\n  @Input({transform: booleanAttribute})\n  get fitInkBarToContent(): boolean {\n    return this._fitToContent;\n  }\n  set fitInkBarToContent(newValue: boolean) {\n    if (this._fitToContent !== newValue) {\n      this._fitToContent = newValue;\n\n      if (this._inkBarElement) {\n        this._appendInkBarElement();\n      }\n    }\n  }\n\n  /** Aligns the ink bar to the current item. */\n  activateInkBar(previousIndicatorClientRect?: DOMRect) {\n    const element = this._elementRef.nativeElement;\n\n    // Early exit if no indicator is present to handle cases where an indicator\n    // may be activated without a prior indicator state\n    if (\n      !previousIndicatorClientRect ||\n      !element.getBoundingClientRect ||\n      !this._inkBarContentElement\n    ) {\n      element.classList.add(ACTIVE_CLASS);\n      return;\n    }\n\n    // This animation uses the FLIP approach. You can read more about it at the link below:\n    // https://aerotwist.com/blog/flip-your-animations/\n\n    // Calculate the dimensions based on the dimensions of the previous indicator\n    const currentClientRect = element.getBoundingClientRect();\n    const widthDelta = previousIndicatorClientRect.width / currentClientRect.width;\n    const xPosition = previousIndicatorClientRect.left - currentClientRect.left;\n    element.classList.add(NO_TRANSITION_CLASS);\n    this._inkBarContentElement.style.setProperty(\n      'transform',\n      `translateX(${xPosition}px) scaleX(${widthDelta})`,\n    );\n\n    // Force repaint before updating classes and transform to ensure the transform properly takes effect\n    element.getBoundingClientRect();\n\n    element.classList.remove(NO_TRANSITION_CLASS);\n    element.classList.add(ACTIVE_CLASS);\n    this._inkBarContentElement.style.setProperty('transform', '');\n  }\n\n  /** Removes the ink bar from the current item. */\n  deactivateInkBar() {\n    this._elementRef.nativeElement.classList.remove(ACTIVE_CLASS);\n  }\n\n  /** Initializes the foundation. */\n  ngOnInit() {\n    this._createInkBarElement();\n  }\n\n  /** Destroys the foundation. */\n  ngOnDestroy() {\n    this._inkBarElement?.remove();\n    this._inkBarElement = this._inkBarContentElement = null!;\n  }\n\n  /** Creates and appends the ink bar element. */\n  private _createInkBarElement() {\n    const documentNode = this._elementRef.nativeElement.ownerDocument || document;\n    const inkBarElement = (this._inkBarElement = documentNode.createElement('span'));\n    const inkBarContentElement = (this._inkBarContentElement = documentNode.createElement('span'));\n\n    inkBarElement.className = 'mdc-tab-indicator';\n    inkBarContentElement.className =\n      'mdc-tab-indicator__content mdc-tab-indicator__content--underline';\n\n    inkBarElement.appendChild(this._inkBarContentElement);\n    this._appendInkBarElement();\n  }\n\n  /**\n   * Appends the ink bar to the tab host element or content, depending on whether\n   * the ink bar should fit to content.\n   */\n  private _appendInkBarElement() {\n    if (!this._inkBarElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Ink bar element has not been created and cannot be appended');\n    }\n\n    const parentElement = this._fitToContent\n      ? this._elementRef.nativeElement.querySelector('.mdc-tab__content')\n      : this._elementRef.nativeElement;\n\n    if (!parentElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Missing element to host the ink bar');\n    }\n\n    parentElement!.appendChild(this._inkBarElement!);\n  }\n}\n\n/**\n * Interface for a MatInkBar positioner method, defining the positioning and width of the ink\n * bar in a set of tabs.\n */\nexport interface _MatInkBarPositioner {\n  (element: HTMLElement): {left: string; width: string};\n}\n\n/**\n * The default positioner function for the MatInkBar.\n * @docs-private\n */\nexport function _MAT_INK_BAR_POSITIONER_FACTORY(): _MatInkBarPositioner {\n  const method = (element: HTMLElement) => ({\n    left: element ? (element.offsetLeft || 0) + 'px' : '0',\n    width: element ? (element.offsetWidth || 0) + 'px' : '0',\n  });\n\n  return method;\n}\n\n/** Injection token for the MatInkBar's Positioner. */\nexport const _MAT_INK_BAR_POSITIONER = new InjectionToken<_MatInkBarPositioner>(\n  'MatInkBarPositioner',\n  {\n    providedIn: 'root',\n    factory: _MAT_INK_BAR_POSITIONER_FACTORY,\n  },\n);\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, ElementRef, Input, booleanAttribute} from '@angular/core';\nimport {InkBarItem} from './ink-bar';\n\n/**\n * Used in the `mat-tab-group` view to display tab labels.\n * @docs-private\n */\n@Directive({\n  selector: '[matTabLabelWrapper]',\n  host: {\n    '[class.mat-mdc-tab-disabled]': 'disabled',\n    '[attr.aria-disabled]': '!!disabled',\n  },\n  standalone: true,\n})\nexport class MatTabLabelWrapper extends InkBarItem {\n  /** Whether the tab is disabled. */\n  @Input({transform: booleanAttribute})\n  disabled: boolean = false;\n\n  constructor(public elementRef: ElementRef) {\n    super();\n  }\n\n  /** Sets focus on the wrapper element */\n  focus(): void {\n    this.elementRef.nativeElement.focus();\n  }\n\n  getOffsetLeft(): number {\n    return this.elementRef.nativeElement.offsetLeft;\n  }\n\n  getOffsetWidth(): number {\n    return this.elementRef.nativeElement.offsetWidth;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ChangeDetectorRef,\n  ElementRef,\n  NgZone,\n  Optional,\n  QueryList,\n  EventEmitter,\n  AfterContentChecked,\n  AfterContentInit,\n  AfterViewInit,\n  OnDestroy,\n  Directive,\n  Inject,\n  Input,\n  booleanAttribute,\n  numberAttribute,\n  Output,\n  ANIMATION_MODULE_TYPE,\n} from '@angular/core';\nimport {Direction, Directionality} from '@angular/cdk/bidi';\nimport {ViewportRuler} from '@angular/cdk/scrolling';\nimport {FocusKeyManager, FocusableOption} from '@angular/cdk/a11y';\nimport {ENTER, SPACE, hasModifierKey} from '@angular/cdk/keycodes';\nimport {\n  merge,\n  of as observableOf,\n  Subject,\n  EMPTY,\n  Observer,\n  Observable,\n  timer,\n  fromEvent,\n} from 'rxjs';\nimport {take, switchMap, startWith, skip, takeUntil, filter} from 'rxjs/operators';\nimport {Platform, normalizePassiveListenerOptions} from '@angular/cdk/platform';\n\n/** Config used to bind passive event listeners */\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true,\n}) as EventListenerOptions;\n\n/**\n * The directions that scrolling can go in when the header's tabs exceed the header width. 'After'\n * will scroll the header towards the end of the tabs list and 'before' will scroll towards the\n * beginning of the list.\n */\nexport type ScrollDirection = 'after' | 'before';\n\n/**\n * Amount of milliseconds to wait before starting to scroll the header automatically.\n * Set a little conservatively in order to handle fake events dispatched on touch devices.\n */\nconst HEADER_SCROLL_DELAY = 650;\n\n/**\n * Interval in milliseconds at which to scroll the header\n * while the user is holding their pointer.\n */\nconst HEADER_SCROLL_INTERVAL = 100;\n\n/** Item inside a paginated tab header. */\nexport type MatPaginatedTabHeaderItem = FocusableOption & {elementRef: ElementRef};\n\n/**\n * Base class for a tab header that supported pagination.\n * @docs-private\n */\n@Directive()\nexport abstract class MatPaginatedTabHeader\n  implements AfterContentChecked, AfterContentInit, AfterViewInit, OnDestroy\n{\n  abstract _items: QueryList<MatPaginatedTabHeaderItem>;\n  abstract _inkBar: {hide: () => void; alignToElement: (element: HTMLElement) => void};\n  abstract _tabListContainer: ElementRef<HTMLElement>;\n  abstract _tabList: ElementRef<HTMLElement>;\n  abstract _tabListInner: ElementRef<HTMLElement>;\n  abstract _nextPaginator: ElementRef<HTMLElement>;\n  abstract _previousPaginator: ElementRef<HTMLElement>;\n\n  /** The distance in pixels that the tab labels should be translated to the left. */\n  private _scrollDistance = 0;\n\n  /** Whether the header should scroll to the selected index after the view has been checked. */\n  private _selectedIndexChanged = false;\n\n  /** Emits when the component is destroyed. */\n  protected readonly _destroyed = new Subject<void>();\n\n  /** Whether the controls for pagination should be displayed */\n  _showPaginationControls = false;\n\n  /** Whether the tab list can be scrolled more towards the end of the tab label list. */\n  _disableScrollAfter = true;\n\n  /** Whether the tab list can be scrolled more towards the beginning of the tab label list. */\n  _disableScrollBefore = true;\n\n  /**\n   * The number of tab labels that are displayed on the header. When this changes, the header\n   * should re-evaluate the scroll position.\n   */\n  private _tabLabelCount: number;\n\n  /** Whether the scroll distance has changed and should be applied after the view is checked. */\n  private _scrollDistanceChanged: boolean;\n\n  /** Used to manage focus between the tabs. */\n  private _keyManager: FocusKeyManager<MatPaginatedTabHeaderItem>;\n\n  /** Cached text content of the header. */\n  private _currentTextContent: string;\n\n  /** Stream that will stop the automated scrolling. */\n  private _stopScrolling = new Subject<void>();\n\n  /**\n   * Whether pagination should be disabled. This can be used to avoid unnecessary\n   * layout recalculations if it's known that pagination won't be required.\n   */\n  @Input({transform: booleanAttribute})\n  disablePagination: boolean = false;\n\n  /** The index of the active tab. */\n  @Input({transform: numberAttribute})\n  get selectedIndex(): number {\n    return this._selectedIndex;\n  }\n  set selectedIndex(v: number) {\n    const value = isNaN(v) ? 0 : v;\n\n    if (this._selectedIndex != value) {\n      this._selectedIndexChanged = true;\n      this._selectedIndex = value;\n\n      if (this._keyManager) {\n        this._keyManager.updateActiveItem(value);\n      }\n    }\n  }\n  private _selectedIndex: number = 0;\n\n  /** Event emitted when the option is selected. */\n  @Output() readonly selectFocusedIndex: EventEmitter<number> = new EventEmitter<number>();\n\n  /** Event emitted when a label is focused. */\n  @Output() readonly indexFocused: EventEmitter<number> = new EventEmitter<number>();\n\n  constructor(\n    protected _elementRef: ElementRef<HTMLElement>,\n    protected _changeDetectorRef: ChangeDetectorRef,\n    private _viewportRuler: ViewportRuler,\n    @Optional() private _dir: Directionality,\n    private _ngZone: NgZone,\n    private _platform: Platform,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) public _animationMode?: string,\n  ) {\n    // Bind the `mouseleave` event on the outside since it doesn't change anything in the view.\n    _ngZone.runOutsideAngular(() => {\n      fromEvent(_elementRef.nativeElement, 'mouseleave')\n        .pipe(takeUntil(this._destroyed))\n        .subscribe(() => {\n          this._stopInterval();\n        });\n    });\n  }\n\n  /** Called when the user has selected an item via the keyboard. */\n  protected abstract _itemSelected(event: KeyboardEvent): void;\n\n  ngAfterViewInit() {\n    // We need to handle these events manually, because we want to bind passive event listeners.\n    fromEvent(this._previousPaginator.nativeElement, 'touchstart', passiveEventListenerOptions)\n      .pipe(takeUntil(this._destroyed))\n      .subscribe(() => {\n        this._handlePaginatorPress('before');\n      });\n\n    fromEvent(this._nextPaginator.nativeElement, 'touchstart', passiveEventListenerOptions)\n      .pipe(takeUntil(this._destroyed))\n      .subscribe(() => {\n        this._handlePaginatorPress('after');\n      });\n  }\n\n  ngAfterContentInit() {\n    const dirChange = this._dir ? this._dir.change : observableOf('ltr');\n    const resize = this._viewportRuler.change(150);\n    const realign = () => {\n      this.updatePagination();\n      this._alignInkBarToSelectedTab();\n    };\n\n    this._keyManager = new FocusKeyManager<MatPaginatedTabHeaderItem>(this._items)\n      .withHorizontalOrientation(this._getLayoutDirection())\n      .withHomeAndEnd()\n      .withWrap()\n      // Allow focus to land on disabled tabs, as per https://w3c.github.io/aria-practices/#kbd_disabled_controls\n      .skipPredicate(() => false);\n\n    this._keyManager.updateActiveItem(this._selectedIndex);\n\n    // Defer the first call in order to allow for slower browsers to lay out the elements.\n    // This helps in cases where the user lands directly on a page with paginated tabs.\n    // Note that we use `onStable` instead of `requestAnimationFrame`, because the latter\n    // can hold up tests that are in a background tab.\n    this._ngZone.onStable.pipe(take(1)).subscribe(realign);\n\n    // On dir change or window resize, realign the ink bar and update the orientation of\n    // the key manager if the direction has changed.\n    merge(dirChange, resize, this._items.changes, this._itemsResized())\n      .pipe(takeUntil(this._destroyed))\n      .subscribe(() => {\n        // We need to defer this to give the browser some time to recalculate\n        // the element dimensions. The call has to be wrapped in `NgZone.run`,\n        // because the viewport change handler runs outside of Angular.\n        this._ngZone.run(() => {\n          Promise.resolve().then(() => {\n            // Clamp the scroll distance, because it can change with the number of tabs.\n            this._scrollDistance = Math.max(\n              0,\n              Math.min(this._getMaxScrollDistance(), this._scrollDistance),\n            );\n            realign();\n          });\n        });\n        this._keyManager.withHorizontalOrientation(this._getLayoutDirection());\n      });\n\n    // If there is a change in the focus key manager we need to emit the `indexFocused`\n    // event in order to provide a public event that notifies about focus changes. Also we realign\n    // the tabs container by scrolling the new focused tab into the visible section.\n    this._keyManager.change.subscribe(newFocusIndex => {\n      this.indexFocused.emit(newFocusIndex);\n      this._setTabFocus(newFocusIndex);\n    });\n  }\n\n  /** Sends any changes that could affect the layout of the items. */\n  private _itemsResized(): Observable<ResizeObserverEntry[]> {\n    if (typeof ResizeObserver !== 'function') {\n      return EMPTY;\n    }\n\n    return this._items.changes.pipe(\n      startWith(this._items),\n      switchMap(\n        (tabItems: QueryList<MatPaginatedTabHeaderItem>) =>\n          new Observable((observer: Observer<ResizeObserverEntry[]>) =>\n            this._ngZone.runOutsideAngular(() => {\n              const resizeObserver = new ResizeObserver(entries => observer.next(entries));\n              tabItems.forEach(item => resizeObserver.observe(item.elementRef.nativeElement));\n              return () => {\n                resizeObserver.disconnect();\n              };\n            }),\n          ),\n      ),\n      // Skip the first emit since the resize observer emits when an item\n      // is observed for new items when the tab is already inserted\n      skip(1),\n      // Skip emissions where all the elements are invisible since we don't want\n      // the header to try and re-render with invalid measurements. See #25574.\n      filter(entries => entries.some(e => e.contentRect.width > 0 && e.contentRect.height > 0)),\n    );\n  }\n\n  ngAfterContentChecked(): void {\n    // If the number of tab labels have changed, check if scrolling should be enabled\n    if (this._tabLabelCount != this._items.length) {\n      this.updatePagination();\n      this._tabLabelCount = this._items.length;\n      this._changeDetectorRef.markForCheck();\n    }\n\n    // If the selected index has changed, scroll to the label and check if the scrolling controls\n    // should be disabled.\n    if (this._selectedIndexChanged) {\n      this._scrollToLabel(this._selectedIndex);\n      this._checkScrollingControls();\n      this._alignInkBarToSelectedTab();\n      this._selectedIndexChanged = false;\n      this._changeDetectorRef.markForCheck();\n    }\n\n    // If the scroll distance has been changed (tab selected, focused, scroll controls activated),\n    // then translate the header to reflect this.\n    if (this._scrollDistanceChanged) {\n      this._updateTabScrollPosition();\n      this._scrollDistanceChanged = false;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n\n  ngOnDestroy() {\n    this._keyManager?.destroy();\n    this._destroyed.next();\n    this._destroyed.complete();\n    this._stopScrolling.complete();\n  }\n\n  /** Handles keyboard events on the header. */\n  _handleKeydown(event: KeyboardEvent) {\n    // We don't handle any key bindings with a modifier key.\n    if (hasModifierKey(event)) {\n      return;\n    }\n\n    switch (event.keyCode) {\n      case ENTER:\n      case SPACE:\n        if (this.focusIndex !== this.selectedIndex) {\n          const item = this._items.get(this.focusIndex);\n\n          if (item && !item.disabled) {\n            this.selectFocusedIndex.emit(this.focusIndex);\n            this._itemSelected(event);\n          }\n        }\n        break;\n      default:\n        this._keyManager.onKeydown(event);\n    }\n  }\n\n  /**\n   * Callback for when the MutationObserver detects that the content has changed.\n   */\n  _onContentChanges() {\n    const textContent = this._elementRef.nativeElement.textContent;\n\n    // We need to diff the text content of the header, because the MutationObserver callback\n    // will fire even if the text content didn't change which is inefficient and is prone\n    // to infinite loops if a poorly constructed expression is passed in (see #14249).\n    if (textContent !== this._currentTextContent) {\n      this._currentTextContent = textContent || '';\n\n      // The content observer runs outside the `NgZone` by default, which\n      // means that we need to bring the callback back in ourselves.\n      this._ngZone.run(() => {\n        this.updatePagination();\n        this._alignInkBarToSelectedTab();\n        this._changeDetectorRef.markForCheck();\n      });\n    }\n  }\n\n  /**\n   * Updates the view whether pagination should be enabled or not.\n   *\n   * WARNING: Calling this method can be very costly in terms of performance. It should be called\n   * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n   * page.\n   */\n  updatePagination() {\n    this._checkPaginationEnabled();\n    this._checkScrollingControls();\n    this._updateTabScrollPosition();\n  }\n\n  /** Tracks which element has focus; used for keyboard navigation */\n  get focusIndex(): number {\n    return this._keyManager ? this._keyManager.activeItemIndex! : 0;\n  }\n\n  /** When the focus index is set, we must manually send focus to the correct label */\n  set focusIndex(value: number) {\n    if (!this._isValidIndex(value) || this.focusIndex === value || !this._keyManager) {\n      return;\n    }\n\n    this._keyManager.setActiveItem(value);\n  }\n\n  /**\n   * Determines if an index is valid.  If the tabs are not ready yet, we assume that the user is\n   * providing a valid index and return true.\n   */\n  _isValidIndex(index: number): boolean {\n    return this._items ? !!this._items.toArray()[index] : true;\n  }\n\n  /**\n   * Sets focus on the HTML element for the label wrapper and scrolls it into the view if\n   * scrolling is enabled.\n   */\n  _setTabFocus(tabIndex: number) {\n    if (this._showPaginationControls) {\n      this._scrollToLabel(tabIndex);\n    }\n\n    if (this._items && this._items.length) {\n      this._items.toArray()[tabIndex].focus();\n\n      // Do not let the browser manage scrolling to focus the element, this will be handled\n      // by using translation. In LTR, the scroll left should be 0. In RTL, the scroll width\n      // should be the full width minus the offset width.\n      const containerEl = this._tabListContainer.nativeElement;\n      const dir = this._getLayoutDirection();\n\n      if (dir == 'ltr') {\n        containerEl.scrollLeft = 0;\n      } else {\n        containerEl.scrollLeft = containerEl.scrollWidth - containerEl.offsetWidth;\n      }\n    }\n  }\n\n  /** The layout direction of the containing app. */\n  _getLayoutDirection(): Direction {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n\n  /** Performs the CSS transformation on the tab list that will cause the list to scroll. */\n  _updateTabScrollPosition() {\n    if (this.disablePagination) {\n      return;\n    }\n\n    const scrollDistance = this.scrollDistance;\n    const translateX = this._getLayoutDirection() === 'ltr' ? -scrollDistance : scrollDistance;\n\n    // Don't use `translate3d` here because we don't want to create a new layer. A new layer\n    // seems to cause flickering and overflow in Internet Explorer. For example, the ink bar\n    // and ripples will exceed the boundaries of the visible tab bar.\n    // See: https://github.com/angular/components/issues/10276\n    // We round the `transform` here, because transforms with sub-pixel precision cause some\n    // browsers to blur the content of the element.\n    this._tabList.nativeElement.style.transform = `translateX(${Math.round(translateX)}px)`;\n\n    // Setting the `transform` on IE will change the scroll offset of the parent, causing the\n    // position to be thrown off in some cases. We have to reset it ourselves to ensure that\n    // it doesn't get thrown off. Note that we scope it only to IE and Edge, because messing\n    // with the scroll position throws off Chrome 71+ in RTL mode (see #14689).\n    if (this._platform.TRIDENT || this._platform.EDGE) {\n      this._tabListContainer.nativeElement.scrollLeft = 0;\n    }\n  }\n\n  /** Sets the distance in pixels that the tab header should be transformed in the X-axis. */\n  get scrollDistance(): number {\n    return this._scrollDistance;\n  }\n  set scrollDistance(value: number) {\n    this._scrollTo(value);\n  }\n\n  /**\n   * Moves the tab list in the 'before' or 'after' direction (towards the beginning of the list or\n   * the end of the list, respectively). The distance to scroll is computed to be a third of the\n   * length of the tab list view window.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _scrollHeader(direction: ScrollDirection) {\n    const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n\n    // Move the scroll distance one-third the length of the tab list's viewport.\n    const scrollAmount = ((direction == 'before' ? -1 : 1) * viewLength) / 3;\n\n    return this._scrollTo(this._scrollDistance + scrollAmount);\n  }\n\n  /** Handles click events on the pagination arrows. */\n  _handlePaginatorClick(direction: ScrollDirection) {\n    this._stopInterval();\n    this._scrollHeader(direction);\n  }\n\n  /**\n   * Moves the tab list such that the desired tab label (marked by index) is moved into view.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _scrollToLabel(labelIndex: number) {\n    if (this.disablePagination) {\n      return;\n    }\n\n    const selectedLabel = this._items ? this._items.toArray()[labelIndex] : null;\n\n    if (!selectedLabel) {\n      return;\n    }\n\n    // The view length is the visible width of the tab labels.\n    const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n    const {offsetLeft, offsetWidth} = selectedLabel.elementRef.nativeElement;\n\n    let labelBeforePos: number, labelAfterPos: number;\n    if (this._getLayoutDirection() == 'ltr') {\n      labelBeforePos = offsetLeft;\n      labelAfterPos = labelBeforePos + offsetWidth;\n    } else {\n      labelAfterPos = this._tabListInner.nativeElement.offsetWidth - offsetLeft;\n      labelBeforePos = labelAfterPos - offsetWidth;\n    }\n\n    const beforeVisiblePos = this.scrollDistance;\n    const afterVisiblePos = this.scrollDistance + viewLength;\n\n    if (labelBeforePos < beforeVisiblePos) {\n      // Scroll header to move label to the before direction\n      this.scrollDistance -= beforeVisiblePos - labelBeforePos;\n    } else if (labelAfterPos > afterVisiblePos) {\n      // Scroll header to move label to the after direction\n      this.scrollDistance += Math.min(\n        labelAfterPos - afterVisiblePos,\n        labelBeforePos - beforeVisiblePos,\n      );\n    }\n  }\n\n  /**\n   * Evaluate whether the pagination controls should be displayed. If the scroll width of the\n   * tab list is wider than the size of the header container, then the pagination controls should\n   * be shown.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _checkPaginationEnabled() {\n    if (this.disablePagination) {\n      this._showPaginationControls = false;\n    } else {\n      const isEnabled =\n        this._tabListInner.nativeElement.scrollWidth > this._elementRef.nativeElement.offsetWidth;\n\n      if (!isEnabled) {\n        this.scrollDistance = 0;\n      }\n\n      if (isEnabled !== this._showPaginationControls) {\n        this._changeDetectorRef.markForCheck();\n      }\n\n      this._showPaginationControls = isEnabled;\n    }\n  }\n\n  /**\n   * Evaluate whether the before and after controls should be enabled or disabled.\n   * If the header is at the beginning of the list (scroll distance is equal to 0) then disable the\n   * before button. If the header is at the end of the list (scroll distance is equal to the\n   * maximum distance we can scroll), then disable the after button.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _checkScrollingControls() {\n    if (this.disablePagination) {\n      this._disableScrollAfter = this._disableScrollBefore = true;\n    } else {\n      // Check if the pagination arrows should be activated.\n      this._disableScrollBefore = this.scrollDistance == 0;\n      this._disableScrollAfter = this.scrollDistance == this._getMaxScrollDistance();\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n\n  /**\n   * Determines what is the maximum length in pixels that can be set for the scroll distance. This\n   * is equal to the difference in width between the tab list container and tab header container.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _getMaxScrollDistance(): number {\n    const lengthOfTabList = this._tabListInner.nativeElement.scrollWidth;\n    const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n    return lengthOfTabList - viewLength || 0;\n  }\n\n  /** Tells the ink-bar to align itself to the current label wrapper */\n  _alignInkBarToSelectedTab(): void {\n    const selectedItem =\n      this._items && this._items.length ? this._items.toArray()[this.selectedIndex] : null;\n    const selectedLabelWrapper = selectedItem ? selectedItem.elementRef.nativeElement : null;\n\n    if (selectedLabelWrapper) {\n      this._inkBar.alignToElement(selectedLabelWrapper);\n    } else {\n      this._inkBar.hide();\n    }\n  }\n\n  /** Stops the currently-running paginator interval.  */\n  _stopInterval() {\n    this._stopScrolling.next();\n  }\n\n  /**\n   * Handles the user pressing down on one of the paginators.\n   * Starts scrolling the header after a certain amount of time.\n   * @param direction In which direction the paginator should be scrolled.\n   */\n  _handlePaginatorPress(direction: ScrollDirection, mouseEvent?: MouseEvent) {\n    // Don't start auto scrolling for right mouse button clicks. Note that we shouldn't have to\n    // null check the `button`, but we do it so we don't break tests that use fake events.\n    if (mouseEvent && mouseEvent.button != null && mouseEvent.button !== 0) {\n      return;\n    }\n\n    // Avoid overlapping timers.\n    this._stopInterval();\n\n    // Start a timer after the delay and keep firing based on the interval.\n    timer(HEADER_SCROLL_DELAY, HEADER_SCROLL_INTERVAL)\n      // Keep the timer going until something tells it to stop or the component is destroyed.\n      .pipe(takeUntil(merge(this._stopScrolling, this._destroyed)))\n      .subscribe(() => {\n        const {maxScrollDistance, distance} = this._scrollHeader(direction);\n\n        // Stop the timer if we've reached the start or the end.\n        if (distance === 0 || distance >= maxScrollDistance) {\n          this._stopInterval();\n        }\n      });\n  }\n\n  /**\n   * Scrolls the header to a given position.\n   * @param position Position to which to scroll.\n   * @returns Information on the current scroll distance and the maximum.\n   */\n  private _scrollTo(position: number) {\n    if (this.disablePagination) {\n      return {maxScrollDistance: 0, distance: 0};\n    }\n\n    const maxScrollDistance = this._getMaxScrollDistance();\n    this._scrollDistance = Math.max(0, Math.min(maxScrollDistance, position));\n\n    // Mark that the scroll distance has changed so that after the view is checked, the CSS\n    // transformation can move the header.\n    this._scrollDistanceChanged = true;\n    this._checkScrollingControls();\n\n    return {maxScrollDistance, distance: this._scrollDistance};\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  AfterContentChecked,\n  AfterContentInit,\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChildren,\n  ElementRef,\n  Inject,\n  Input,\n  NgZone,\n  OnDestroy,\n  Optional,\n  QueryList,\n  ViewChild,\n  ViewEncapsulation,\n  booleanAttribute,\n  ANIMATION_MODULE_TYPE,\n} from '@angular/core';\nimport {ViewportRuler} from '@angular/cdk/scrolling';\nimport {Platform} from '@angular/cdk/platform';\nimport {Directionality} from '@angular/cdk/bidi';\nimport {MatTabLabelWrapper} from './tab-label-wrapper';\nimport {MatInkBar} from './ink-bar';\nimport {MatPaginatedTabHeader} from './paginated-tab-header';\nimport {CdkObserveContent} from '@angular/cdk/observers';\nimport {MatRipple} from '@angular/material/core';\n\n/**\n * The header of the tab group which displays a list of all the tabs in the tab group. Includes\n * an ink bar that follows the currently selected tab. When the tabs list's width exceeds the\n * width of the header container, then arrows will be displayed to allow the user to scroll\n * left and right across the header.\n * @docs-private\n */\n@Component({\n  selector: 'mat-tab-header',\n  templateUrl: 'tab-header.html',\n  styleUrl: 'tab-header.css',\n  encapsulation: ViewEncapsulation.None,\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  host: {\n    'class': 'mat-mdc-tab-header',\n    '[class.mat-mdc-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n    '[class.mat-mdc-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\",\n  },\n  standalone: true,\n  imports: [MatRipple, CdkObserveContent],\n})\nexport class MatTabHeader\n  extends MatPaginatedTabHeader\n  implements AfterContentChecked, AfterContentInit, AfterViewInit, OnDestroy\n{\n  @ContentChildren(MatTabLabelWrapper, {descendants: false}) _items: QueryList<MatTabLabelWrapper>;\n  @ViewChild('tabListContainer', {static: true}) _tabListContainer: ElementRef;\n  @ViewChild('tabList', {static: true}) _tabList: ElementRef;\n  @ViewChild('tabListInner', {static: true}) _tabListInner: ElementRef;\n  @ViewChild('nextPaginator') _nextPaginator: ElementRef<HTMLElement>;\n  @ViewChild('previousPaginator') _previousPaginator: ElementRef<HTMLElement>;\n  _inkBar: MatInkBar;\n\n  /** Whether the ripple effect is disabled or not. */\n  @Input({transform: booleanAttribute})\n  disableRipple: boolean = false;\n\n  constructor(\n    elementRef: ElementRef,\n    changeDetectorRef: ChangeDetectorRef,\n    viewportRuler: ViewportRuler,\n    @Optional() dir: Directionality,\n    ngZone: NgZone,\n    platform: Platform,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) animationMode?: string,\n  ) {\n    super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n  }\n\n  override ngAfterContentInit() {\n    this._inkBar = new MatInkBar(this._items);\n    super.ngAfterContentInit();\n  }\n\n  protected _itemSelected(event: KeyboardEvent) {\n    event.preventDefault();\n  }\n}\n", "<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\n<button class=\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\"\n     #previousPaginator\n     aria-hidden=\"true\"\n     type=\"button\"\n     mat-ripple\n     tabindex=\"-1\"\n     [matRippleDisabled]=\"_disableScrollBefore || disableRipple\"\n     [class.mat-mdc-tab-header-pagination-disabled]=\"_disableScrollBefore\"\n     [disabled]=\"_disableScrollBefore || null\"\n     (click)=\"_handlePaginatorClick('before')\"\n     (mousedown)=\"_handlePaginatorPress('before', $event)\"\n     (touchend)=\"_stopInterval()\">\n  <div class=\"mat-mdc-tab-header-pagination-chevron\"></div>\n</button>\n\n<div\n  class=\"mat-mdc-tab-label-container\"\n  #tabListContainer\n  (keydown)=\"_handleKeydown($event)\"\n  [class._mat-animation-noopable]=\"_animationMode === 'NoopAnimations'\">\n  <div\n    #tabList\n    class=\"mat-mdc-tab-list\"\n    role=\"tablist\"\n    (cdkObserveContent)=\"_onContentChanges()\">\n    <div class=\"mat-mdc-tab-labels\" #tabListInner>\n      <ng-content></ng-content>\n    </div>\n  </div>\n</div>\n\n<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\n<button class=\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\"\n     #nextPaginator\n     aria-hidden=\"true\"\n     type=\"button\"\n     mat-ripple\n     [matRippleDisabled]=\"_disableScrollAfter || disableRipple\"\n     [class.mat-mdc-tab-header-pagination-disabled]=\"_disableScrollAfter\"\n     [disabled]=\"_disableScrollAfter || null\"\n     tabindex=\"-1\"\n     (mousedown)=\"_handlePaginatorPress('after', $event)\"\n     (click)=\"_handlePaginatorClick('after')\"\n     (touchend)=\"_stopInterval()\">\n  <div class=\"mat-mdc-tab-header-pagination-chevron\"></div>\n</button>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {InjectionToken} from '@angular/core';\n\n/** Object that can be used to configure the default options for the tabs module. */\nexport interface MatTabsConfig {\n  /** Duration for the tab animation. Must be a valid CSS value (e.g. 600ms). */\n  animationDuration?: string;\n\n  /**\n   * Whether pagination should be disabled. This can be used to avoid unnecessary\n   * layout recalculations if it's known that pagination won't be required.\n   */\n  disablePagination?: boolean;\n\n  /**\n   * Whether the ink bar should fit its width to the size of the tab label content.\n   * This only applies to the MDC-based tabs.\n   */\n  fitInkBarToContent?: boolean;\n\n  /** Whether the tab group should grow to the size of the active tab. */\n  dynamicHeight?: boolean;\n\n  /** `tabindex` to be set on the inner element that wraps the tab content. */\n  contentTabIndex?: number;\n\n  /**\n   * By default tabs remove their content from the DOM while it's off-screen.\n   * Setting this to `true` will keep it in the DOM which will prevent elements\n   * like iframes and videos from reloading next time it comes back into the view.\n   */\n  preserveContent?: boolean;\n\n  /** Whether tabs should be stretched to fill the header. */\n  stretchTabs?: boolean;\n}\n\n/** Injection token that can be used to provide the default options the tabs module. */\nexport const MAT_TABS_CONFIG = new InjectionToken<MatTabsConfig>('MAT_TABS_CONFIG');\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {\n  animate,\n  state,\n  style,\n  transition,\n  trigger,\n  AnimationTriggerMetadata,\n} from '@angular/animations';\n\n/**\n * Animations used by the Material tabs.\n * @docs-private\n */\nexport const matTabsAnimations: {\n  readonly translateTab: AnimationTriggerMetadata;\n} = {\n  /** Animation translates a tab along the X axis. */\n  translateTab: trigger('translateTab', [\n    // Transitions to `none` instead of 0, because some browsers might blur the content.\n    state('center, void, left-origin-center, right-origin-center', style({transform: 'none'})),\n\n    // If the tab is either on the left or right, we additionally add a `min-height` of 1px\n    // in order to ensure that the element has a height before its state changes. This is\n    // necessary because <PERSON><PERSON> does seem to skip the transition in RTL mode if the element does\n    // not have a static height and is not rendered. See related issue: #9465\n    state(\n      'left',\n      style({\n        transform: 'translate3d(-100%, 0, 0)',\n        minHeight: '1px',\n\n        // Normally this is redundant since we detach the content from the DOM, but if the user\n        // opted into keeping the content in the DOM, we have to hide it so it isn't focusable.\n        visibility: 'hidden',\n      }),\n    ),\n    state(\n      'right',\n      style({\n        transform: 'translate3d(100%, 0, 0)',\n        minHeight: '1px',\n        visibility: 'hidden',\n      }),\n    ),\n\n    transition(\n      '* => left, * => right, left => center, right => center',\n      animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n    ),\n    transition('void => left-origin-center', [\n      style({transform: 'translate3d(-100%, 0, 0)', visibility: 'hidden'}),\n      animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n    ]),\n    transition('void => right-origin-center', [\n      style({transform: 'translate3d(100%, 0, 0)', visibility: 'hidden'}),\n      animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n    ]),\n  ]),\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ComponentFactoryResolver,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  forwardRef,\n  Inject,\n  Input,\n  OnDestroy,\n  OnInit,\n  Optional,\n  Output,\n  ViewChild,\n  ViewContainerRef,\n  ViewEncapsulation,\n} from '@angular/core';\nimport {CdkPortalOutlet, TemplatePortal} from '@angular/cdk/portal';\nimport {Direction, Directionality} from '@angular/cdk/bidi';\nimport {DOCUMENT} from '@angular/common';\nimport {Subject, Subscription} from 'rxjs';\nimport {distinctUntilChanged, startWith} from 'rxjs/operators';\nimport {AnimationEvent} from '@angular/animations';\nimport {matTabsAnimations} from './tabs-animations';\nimport {CdkScrollable} from '@angular/cdk/scrolling';\n\n/**\n * The portal host directive for the contents of the tab.\n * @docs-private\n */\n@Directive({\n  selector: '[matTabBodyHost]',\n  standalone: true,\n})\nexport class MatTabBodyPortal extends CdkPortalOutlet implements OnInit, OnDestroy {\n  /** Subscription to events for when the tab body begins centering. */\n  private _centeringSub = Subscription.EMPTY;\n  /** Subscription to events for when the tab body finishes leaving from center position. */\n  private _leavingSub = Subscription.EMPTY;\n\n  constructor(\n    componentFactoryResolver: ComponentFactoryResolver,\n    viewContainerRef: ViewContainerRef,\n    @Inject(forwardRef(() => MatTabBody)) private _host: MatTabBody,\n    @Inject(DOCUMENT) _document: any,\n  ) {\n    super(componentFactoryResolver, viewContainerRef, _document);\n  }\n\n  /** Set initial visibility or set up subscription for changing visibility. */\n  override ngOnInit(): void {\n    super.ngOnInit();\n\n    this._centeringSub = this._host._beforeCentering\n      .pipe(startWith(this._host._isCenterPosition(this._host._position)))\n      .subscribe((isCentering: boolean) => {\n        if (isCentering && !this.hasAttached()) {\n          this.attach(this._host._content);\n        }\n      });\n\n    this._leavingSub = this._host._afterLeavingCenter.subscribe(() => {\n      if (!this._host.preserveContent) {\n        this.detach();\n      }\n    });\n  }\n\n  /** Clean up centering subscription. */\n  override ngOnDestroy(): void {\n    super.ngOnDestroy();\n    this._centeringSub.unsubscribe();\n    this._leavingSub.unsubscribe();\n  }\n}\n\n/**\n * These position states are used internally as animation states for the tab body. Setting the\n * position state to left, right, or center will transition the tab body from its current\n * position to its respective state. If there is not current position (void, in the case of a new\n * tab body), then there will be no transition animation to its state.\n *\n * In the case of a new tab body that should immediately be centered with an animating transition,\n * then left-origin-center or right-origin-center can be used, which will use left or right as its\n * pseudo-prior state.\n */\nexport type MatTabBodyPositionState =\n  | 'left'\n  | 'center'\n  | 'right'\n  | 'left-origin-center'\n  | 'right-origin-center';\n\n/**\n * Wrapper for the contents of a tab.\n * @docs-private\n */\n@Component({\n  selector: 'mat-tab-body',\n  templateUrl: 'tab-body.html',\n  styleUrl: 'tab-body.css',\n  encapsulation: ViewEncapsulation.None,\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  animations: [matTabsAnimations.translateTab],\n  host: {\n    'class': 'mat-mdc-tab-body',\n  },\n  standalone: true,\n  imports: [MatTabBodyPortal, CdkScrollable],\n})\nexport class MatTabBody implements OnInit, OnDestroy {\n  /** Current position of the tab-body in the tab-group. Zero means that the tab is visible. */\n  private _positionIndex: number;\n\n  /** Subscription to the directionality change observable. */\n  private _dirChangeSubscription = Subscription.EMPTY;\n\n  /** Tab body position state. Used by the animation trigger for the current state. */\n  _position: MatTabBodyPositionState;\n\n  /** Emits when an animation on the tab is complete. */\n  readonly _translateTabComplete = new Subject<AnimationEvent>();\n\n  /** Event emitted when the tab begins to animate towards the center as the active tab. */\n  @Output() readonly _onCentering: EventEmitter<number> = new EventEmitter<number>();\n\n  /** Event emitted before the centering of the tab begins. */\n  @Output() readonly _beforeCentering: EventEmitter<boolean> = new EventEmitter<boolean>();\n\n  /** Event emitted before the centering of the tab begins. */\n  @Output() readonly _afterLeavingCenter: EventEmitter<void> = new EventEmitter<void>();\n\n  /** Event emitted when the tab completes its animation towards the center. */\n  @Output() readonly _onCentered: EventEmitter<void> = new EventEmitter<void>(true);\n\n  /** The portal host inside of this container into which the tab body content will be loaded. */\n  @ViewChild(CdkPortalOutlet) _portalHost: CdkPortalOutlet;\n\n  /** The tab body content to display. */\n  @Input('content') _content: TemplatePortal;\n\n  /** Position that will be used when the tab is immediately becoming visible after creation. */\n  @Input() origin: number | null;\n\n  // Note that the default value will always be overwritten by `MatTabBody`, but we need one\n  // anyway to prevent the animations module from throwing an error if the body is used on its own.\n  /** Duration for the tab's animation. */\n  @Input() animationDuration: string = '500ms';\n\n  /** Whether the tab's content should be kept in the DOM while it's off-screen. */\n  @Input() preserveContent: boolean = false;\n\n  /** The shifted index position of the tab body, where zero represents the active center tab. */\n  @Input()\n  set position(position: number) {\n    this._positionIndex = position;\n    this._computePositionAnimationState();\n  }\n\n  constructor(\n    private _elementRef: ElementRef<HTMLElement>,\n    @Optional() private _dir: Directionality,\n    changeDetectorRef: ChangeDetectorRef,\n  ) {\n    if (_dir) {\n      this._dirChangeSubscription = _dir.change.subscribe((dir: Direction) => {\n        this._computePositionAnimationState(dir);\n        changeDetectorRef.markForCheck();\n      });\n    }\n\n    // Ensure that we get unique animation events, because the `.done` callback can get\n    // invoked twice in some browsers. See https://github.com/angular/angular/issues/24084.\n    this._translateTabComplete\n      .pipe(\n        distinctUntilChanged((x, y) => {\n          return x.fromState === y.fromState && x.toState === y.toState;\n        }),\n      )\n      .subscribe(event => {\n        // If the transition to the center is complete, emit an event.\n        if (this._isCenterPosition(event.toState) && this._isCenterPosition(this._position)) {\n          this._onCentered.emit();\n        }\n\n        if (this._isCenterPosition(event.fromState) && !this._isCenterPosition(this._position)) {\n          this._afterLeavingCenter.emit();\n        }\n      });\n  }\n\n  /**\n   * After initialized, check if the content is centered and has an origin. If so, set the\n   * special position states that transition the tab from the left or right before centering.\n   */\n  ngOnInit() {\n    if (this._position == 'center' && this.origin != null) {\n      this._position = this._computePositionFromOrigin(this.origin);\n    }\n  }\n\n  ngOnDestroy() {\n    this._dirChangeSubscription.unsubscribe();\n    this._translateTabComplete.complete();\n  }\n\n  _onTranslateTabStarted(event: AnimationEvent): void {\n    const isCentering = this._isCenterPosition(event.toState);\n    this._beforeCentering.emit(isCentering);\n    if (isCentering) {\n      this._onCentering.emit(this._elementRef.nativeElement.clientHeight);\n    }\n  }\n\n  /** The text direction of the containing app. */\n  _getLayoutDirection(): Direction {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n\n  /** Whether the provided position state is considered center, regardless of origin. */\n  _isCenterPosition(position: MatTabBodyPositionState | string): boolean {\n    return (\n      position == 'center' || position == 'left-origin-center' || position == 'right-origin-center'\n    );\n  }\n\n  /** Computes the position state that will be used for the tab-body animation trigger. */\n  private _computePositionAnimationState(dir: Direction = this._getLayoutDirection()) {\n    if (this._positionIndex < 0) {\n      this._position = dir == 'ltr' ? 'left' : 'right';\n    } else if (this._positionIndex > 0) {\n      this._position = dir == 'ltr' ? 'right' : 'left';\n    } else {\n      this._position = 'center';\n    }\n  }\n\n  /**\n   * Computes the position state based on the specified origin position. This is used if the\n   * tab is becoming visible immediately after creation.\n   */\n  private _computePositionFromOrigin(origin: number): MatTabBodyPositionState {\n    const dir = this._getLayoutDirection();\n\n    if ((dir == 'ltr' && origin <= 0) || (dir == 'rtl' && origin > 0)) {\n      return 'left-origin-center';\n    }\n\n    return 'right-origin-center';\n  }\n}\n\n/**\n * The origin state is an internally used state that is set on a new tab body indicating if it\n * began to the left or right of the prior selected index. For example, if the selected index was\n * set to 1, and a new tab is created and selected at index 2, then the tab body would have an\n * origin of right because its index was greater than the prior selected index.\n */\nexport type MatTabBodyOriginState = 'left' | 'right';\n", "<div class=\"mat-mdc-tab-body-content\" #content\n     [@translateTab]=\"{\n        value: _position,\n        params: {animationDuration: animationDuration}\n     }\"\n     (@translateTab.start)=\"_onTranslateTabStarted($event)\"\n     (@translateTab.done)=\"_translateTabComplete.next($event)\"\n     cdkScrollable>\n  <ng-template matTabBodyHost></ng-template>\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  AfterContentChecked,\n  AfterContentInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChildren,\n  ElementRef,\n  EventEmitter,\n  Inject,\n  Input,\n  OnDestroy,\n  Optional,\n  Output,\n  QueryList,\n  ViewChild,\n  ViewEncapsulation,\n  booleanAttribute,\n  inject,\n  numberAttribute,\n  ANIMATION_MODULE_TYPE,\n} from '@angular/core';\nimport {MAT_TAB_GROUP, MatTab} from './tab';\nimport {MatTabHeader} from './tab-header';\nimport {ThemePalette, MatRipple} from '@angular/material/core';\nimport {merge, Subscription} from 'rxjs';\nimport {MAT_TABS_CONFIG, MatTabsConfig} from './tab-config';\nimport {startWith} from 'rxjs/operators';\nimport {CdkMonitorFocus, FocusOrigin} from '@angular/cdk/a11y';\nimport {MatTabBody} from './tab-body';\nimport {CdkPortalOutlet} from '@angular/cdk/portal';\nimport {MatTabLabelWrapper} from './tab-label-wrapper';\nimport {Platform} from '@angular/cdk/platform';\n\n/** Used to generate unique ID's for each tab component */\nlet nextId = 0;\n\n/** @docs-private */\nexport interface MatTabGroupBaseHeader {\n  _alignInkBarToSelectedTab(): void;\n  updatePagination(): void;\n  focusIndex: number;\n}\n\n/** Possible positions for the tab header. */\nexport type MatTabHeaderPosition = 'above' | 'below';\n\n/** Boolean constant that determines whether the tab group supports the `backgroundColor` input */\nconst ENABLE_BACKGROUND_INPUT = true;\n\n/**\n * Material design tab-group component. Supports basic tab pairs (label + content) and includes\n * animated ink-bar, keyboard navigation, and screen reader.\n * See: https://material.io/design/components/tabs.html\n */\n@Component({\n  selector: 'mat-tab-group',\n  exportAs: 'matTabGroup',\n  templateUrl: 'tab-group.html',\n  styleUrl: 'tab-group.css',\n  encapsulation: ViewEncapsulation.None,\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  providers: [\n    {\n      provide: MAT_TAB_GROUP,\n      useExisting: MatTabGroup,\n    },\n  ],\n  host: {\n    'class': 'mat-mdc-tab-group',\n    '[class]': '\"mat-\" + (color || \"primary\")',\n    '[class.mat-mdc-tab-group-dynamic-height]': 'dynamicHeight',\n    '[class.mat-mdc-tab-group-inverted-header]': 'headerPosition === \"below\"',\n    '[class.mat-mdc-tab-group-stretch-tabs]': 'stretchTabs',\n    '[style.--mat-tab-animation-duration]': 'animationDuration',\n  },\n  standalone: true,\n  imports: [\n    MatTabHeader,\n    MatTabLabelWrapper,\n    CdkMonitorFocus,\n    MatRipple,\n    CdkPortalOutlet,\n    MatTabBody,\n  ],\n})\nexport class MatTabGroup implements AfterContentInit, AfterContentChecked, OnDestroy {\n  /**\n   * All tabs inside the tab group. This includes tabs that belong to groups that are nested\n   * inside the current one. We filter out only the tabs that belong to this group in `_tabs`.\n   */\n  @ContentChildren(MatTab, {descendants: true}) _allTabs: QueryList<MatTab>;\n  @ViewChild('tabBodyWrapper') _tabBodyWrapper: ElementRef;\n  @ViewChild('tabHeader') _tabHeader: MatTabHeader;\n\n  /** All of the tabs that belong to the group. */\n  _tabs: QueryList<MatTab> = new QueryList<MatTab>();\n\n  /** The tab index that should be selected after the content has been checked. */\n  private _indexToSelect: number | null = 0;\n\n  /** Index of the tab that was focused last. */\n  private _lastFocusedTabIndex: number | null = null;\n\n  /** Snapshot of the height of the tab body wrapper before another tab is activated. */\n  private _tabBodyWrapperHeight: number = 0;\n\n  /** Subscription to tabs being added/removed. */\n  private _tabsSubscription = Subscription.EMPTY;\n\n  /** Subscription to changes in the tab labels. */\n  private _tabLabelSubscription = Subscription.EMPTY;\n\n  /** Theme color of the tab group. */\n  @Input()\n  color: ThemePalette;\n\n  /** Whether the ink bar should fit its width to the size of the tab label content. */\n  @Input({transform: booleanAttribute})\n  get fitInkBarToContent(): boolean {\n    return this._fitInkBarToContent;\n  }\n  set fitInkBarToContent(value: boolean) {\n    this._fitInkBarToContent = value;\n    this._changeDetectorRef.markForCheck();\n  }\n  private _fitInkBarToContent = false;\n\n  /** Whether tabs should be stretched to fill the header. */\n  @Input({alias: 'mat-stretch-tabs', transform: booleanAttribute})\n  stretchTabs: boolean = true;\n\n  /** Whether the tab group should grow to the size of the active tab. */\n  @Input({transform: booleanAttribute})\n  dynamicHeight: boolean = false;\n\n  /** The index of the active tab. */\n  @Input({transform: numberAttribute})\n  get selectedIndex(): number | null {\n    return this._selectedIndex;\n  }\n  set selectedIndex(value: number) {\n    this._indexToSelect = isNaN(value) ? null : value;\n  }\n  private _selectedIndex: number | null = null;\n\n  /** Position of the tab header. */\n  @Input() headerPosition: MatTabHeaderPosition = 'above';\n\n  /** Duration for the tab animation. Will be normalized to milliseconds if no units are set. */\n  @Input()\n  get animationDuration(): string {\n    return this._animationDuration;\n  }\n  set animationDuration(value: string | number) {\n    const stringValue = value + '';\n    this._animationDuration = /^\\d+$/.test(stringValue) ? value + 'ms' : stringValue;\n  }\n  private _animationDuration: string;\n\n  /**\n   * `tabindex` to be set on the inner element that wraps the tab content. Can be used for improved\n   * accessibility when the tab does not have focusable elements or if it has scrollable content.\n   * The `tabindex` will be removed automatically for inactive tabs.\n   * Read more at https://www.w3.org/TR/wai-aria-practices/examples/tabs/tabs-2/tabs.html\n   */\n  @Input({transform: numberAttribute})\n  get contentTabIndex(): number | null {\n    return this._contentTabIndex;\n  }\n\n  set contentTabIndex(value: number) {\n    this._contentTabIndex = isNaN(value) ? null : value;\n  }\n\n  private _contentTabIndex: number | null;\n\n  /**\n   * Whether pagination should be disabled. This can be used to avoid unnecessary\n   * layout recalculations if it's known that pagination won't be required.\n   */\n  @Input({transform: booleanAttribute})\n  disablePagination: boolean = false;\n\n  /** Whether ripples in the tab group are disabled. */\n  @Input({transform: booleanAttribute})\n  disableRipple: boolean = false;\n\n  /**\n   * By default tabs remove their content from the DOM while it's off-screen.\n   * Setting this to `true` will keep it in the DOM which will prevent elements\n   * like iframes and videos from reloading next time it comes back into the view.\n   */\n  @Input({transform: booleanAttribute})\n  preserveContent: boolean = false;\n\n  /**\n   * Background color of the tab group.\n   * @deprecated The background color should be customized through Sass theming APIs.\n   * @breaking-change 20.0.0 Remove this input\n   */\n  @Input()\n  get backgroundColor(): ThemePalette {\n    return this._backgroundColor;\n  }\n\n  set backgroundColor(value: ThemePalette) {\n    if (!ENABLE_BACKGROUND_INPUT) {\n      throw new Error(`mat-tab-group background color must be set through the Sass theming API`);\n    }\n\n    const classList: DOMTokenList = this._elementRef.nativeElement.classList;\n\n    classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n\n    if (value) {\n      classList.add('mat-tabs-with-background', `mat-background-${value}`);\n    }\n\n    this._backgroundColor = value;\n  }\n\n  private _backgroundColor: ThemePalette;\n\n  /** Output to enable support for two-way binding on `[(selectedIndex)]` */\n  @Output() readonly selectedIndexChange: EventEmitter<number> = new EventEmitter<number>();\n\n  /** Event emitted when focus has changed within a tab group. */\n  @Output() readonly focusChange: EventEmitter<MatTabChangeEvent> =\n    new EventEmitter<MatTabChangeEvent>();\n\n  /** Event emitted when the body animation has completed */\n  @Output() readonly animationDone: EventEmitter<void> = new EventEmitter<void>();\n\n  /** Event emitted when the tab selection has changed. */\n  @Output() readonly selectedTabChange: EventEmitter<MatTabChangeEvent> =\n    new EventEmitter<MatTabChangeEvent>(true);\n\n  private _groupId: number;\n\n  /** Whether the tab group is rendered on the server. */\n  protected _isServer: boolean = !inject(Platform).isBrowser;\n\n  constructor(\n    readonly _elementRef: ElementRef,\n    private _changeDetectorRef: ChangeDetectorRef,\n    @Inject(MAT_TABS_CONFIG) @Optional() defaultConfig?: MatTabsConfig,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) public _animationMode?: string,\n  ) {\n    this._groupId = nextId++;\n    this.animationDuration =\n      defaultConfig && defaultConfig.animationDuration ? defaultConfig.animationDuration : '500ms';\n    this.disablePagination =\n      defaultConfig && defaultConfig.disablePagination != null\n        ? defaultConfig.disablePagination\n        : false;\n    this.dynamicHeight =\n      defaultConfig && defaultConfig.dynamicHeight != null ? defaultConfig.dynamicHeight : false;\n    if (defaultConfig?.contentTabIndex != null) {\n      this.contentTabIndex = defaultConfig.contentTabIndex;\n    }\n    this.preserveContent = !!defaultConfig?.preserveContent;\n    this.fitInkBarToContent =\n      defaultConfig && defaultConfig.fitInkBarToContent != null\n        ? defaultConfig.fitInkBarToContent\n        : false;\n    this.stretchTabs =\n      defaultConfig && defaultConfig.stretchTabs != null ? defaultConfig.stretchTabs : true;\n  }\n\n  /**\n   * After the content is checked, this component knows what tabs have been defined\n   * and what the selected index should be. This is where we can know exactly what position\n   * each tab should be in according to the new selected index, and additionally we know how\n   * a new selected tab should transition in (from the left or right).\n   */\n  ngAfterContentChecked() {\n    // Don't clamp the `indexToSelect` immediately in the setter because it can happen that\n    // the amount of tabs changes before the actual change detection runs.\n    const indexToSelect = (this._indexToSelect = this._clampTabIndex(this._indexToSelect));\n\n    // If there is a change in selected index, emit a change event. Should not trigger if\n    // the selected index has not yet been initialized.\n    if (this._selectedIndex != indexToSelect) {\n      const isFirstRun = this._selectedIndex == null;\n\n      if (!isFirstRun) {\n        this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n        // Preserve the height so page doesn't scroll up during tab change.\n        // Fixes https://stackblitz.com/edit/mat-tabs-scroll-page-top-on-tab-change\n        const wrapper = this._tabBodyWrapper.nativeElement;\n        wrapper.style.minHeight = wrapper.clientHeight + 'px';\n      }\n\n      // Changing these values after change detection has run\n      // since the checked content may contain references to them.\n      Promise.resolve().then(() => {\n        this._tabs.forEach((tab, index) => (tab.isActive = index === indexToSelect));\n\n        if (!isFirstRun) {\n          this.selectedIndexChange.emit(indexToSelect);\n          // Clear the min-height, this was needed during tab change to avoid\n          // unnecessary scrolling.\n          this._tabBodyWrapper.nativeElement.style.minHeight = '';\n        }\n      });\n    }\n\n    // Setup the position for each tab and optionally setup an origin on the next selected tab.\n    this._tabs.forEach((tab: MatTab, index: number) => {\n      tab.position = index - indexToSelect;\n\n      // If there is already a selected tab, then set up an origin for the next selected tab\n      // if it doesn't have one already.\n      if (this._selectedIndex != null && tab.position == 0 && !tab.origin) {\n        tab.origin = indexToSelect - this._selectedIndex;\n      }\n    });\n\n    if (this._selectedIndex !== indexToSelect) {\n      this._selectedIndex = indexToSelect;\n      this._lastFocusedTabIndex = null;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n\n  ngAfterContentInit() {\n    this._subscribeToAllTabChanges();\n    this._subscribeToTabLabels();\n\n    // Subscribe to changes in the amount of tabs, in order to be\n    // able to re-render the content as new tabs are added or removed.\n    this._tabsSubscription = this._tabs.changes.subscribe(() => {\n      const indexToSelect = this._clampTabIndex(this._indexToSelect);\n\n      // Maintain the previously-selected tab if a new tab is added or removed and there is no\n      // explicit change that selects a different tab.\n      if (indexToSelect === this._selectedIndex) {\n        const tabs = this._tabs.toArray();\n        let selectedTab: MatTab | undefined;\n\n        for (let i = 0; i < tabs.length; i++) {\n          if (tabs[i].isActive) {\n            // Assign both to the `_indexToSelect` and `_selectedIndex` so we don't fire a changed\n            // event, otherwise the consumer may end up in an infinite loop in some edge cases like\n            // adding a tab within the `selectedIndexChange` event.\n            this._indexToSelect = this._selectedIndex = i;\n            this._lastFocusedTabIndex = null;\n            selectedTab = tabs[i];\n            break;\n          }\n        }\n\n        // If we haven't found an active tab and a tab exists at the selected index, it means\n        // that the active tab was swapped out. Since this won't be picked up by the rendering\n        // loop in `ngAfterContentChecked`, we need to sync it up manually.\n        if (!selectedTab && tabs[indexToSelect]) {\n          Promise.resolve().then(() => {\n            tabs[indexToSelect].isActive = true;\n            this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n          });\n        }\n      }\n\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n\n  /** Listens to changes in all of the tabs. */\n  private _subscribeToAllTabChanges() {\n    // Since we use a query with `descendants: true` to pick up the tabs, we may end up catching\n    // some that are inside of nested tab groups. We filter them out manually by checking that\n    // the closest group to the tab is the current one.\n    this._allTabs.changes.pipe(startWith(this._allTabs)).subscribe((tabs: QueryList<MatTab>) => {\n      this._tabs.reset(\n        tabs.filter(tab => {\n          return tab._closestTabGroup === this || !tab._closestTabGroup;\n        }),\n      );\n      this._tabs.notifyOnChanges();\n    });\n  }\n\n  ngOnDestroy() {\n    this._tabs.destroy();\n    this._tabsSubscription.unsubscribe();\n    this._tabLabelSubscription.unsubscribe();\n  }\n\n  /** Re-aligns the ink bar to the selected tab element. */\n  realignInkBar() {\n    if (this._tabHeader) {\n      this._tabHeader._alignInkBarToSelectedTab();\n    }\n  }\n\n  /**\n   * Recalculates the tab group's pagination dimensions.\n   *\n   * WARNING: Calling this method can be very costly in terms of performance. It should be called\n   * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n   * page.\n   */\n  updatePagination() {\n    if (this._tabHeader) {\n      this._tabHeader.updatePagination();\n    }\n  }\n\n  /**\n   * Sets focus to a particular tab.\n   * @param index Index of the tab to be focused.\n   */\n  focusTab(index: number) {\n    const header = this._tabHeader;\n\n    if (header) {\n      header.focusIndex = index;\n    }\n  }\n\n  _focusChanged(index: number) {\n    this._lastFocusedTabIndex = index;\n    this.focusChange.emit(this._createChangeEvent(index));\n  }\n\n  private _createChangeEvent(index: number): MatTabChangeEvent {\n    const event = new MatTabChangeEvent();\n    event.index = index;\n    if (this._tabs && this._tabs.length) {\n      event.tab = this._tabs.toArray()[index];\n    }\n    return event;\n  }\n\n  /**\n   * Subscribes to changes in the tab labels. This is needed, because the @Input for the label is\n   * on the MatTab component, whereas the data binding is inside the MatTabGroup. In order for the\n   * binding to be updated, we need to subscribe to changes in it and trigger change detection\n   * manually.\n   */\n  private _subscribeToTabLabels() {\n    if (this._tabLabelSubscription) {\n      this._tabLabelSubscription.unsubscribe();\n    }\n\n    this._tabLabelSubscription = merge(...this._tabs.map(tab => tab._stateChanges)).subscribe(() =>\n      this._changeDetectorRef.markForCheck(),\n    );\n  }\n\n  /** Clamps the given index to the bounds of 0 and the tabs length. */\n  private _clampTabIndex(index: number | null): number {\n    // Note the `|| 0`, which ensures that values like NaN can't get through\n    // and which would otherwise throw the component into an infinite loop\n    // (since Math.max(NaN, 0) === NaN).\n    return Math.min(this._tabs.length - 1, Math.max(index || 0, 0));\n  }\n\n  /** Returns a unique id for each tab label element */\n  _getTabLabelId(i: number): string {\n    return `mat-tab-label-${this._groupId}-${i}`;\n  }\n\n  /** Returns a unique id for each tab content element */\n  _getTabContentId(i: number): string {\n    return `mat-tab-content-${this._groupId}-${i}`;\n  }\n\n  /**\n   * Sets the height of the body wrapper to the height of the activating tab if dynamic\n   * height property is true.\n   */\n  _setTabBodyWrapperHeight(tabHeight: number): void {\n    if (!this.dynamicHeight || !this._tabBodyWrapperHeight) {\n      return;\n    }\n\n    const wrapper: HTMLElement = this._tabBodyWrapper.nativeElement;\n\n    wrapper.style.height = this._tabBodyWrapperHeight + 'px';\n\n    // This conditional forces the browser to paint the height so that\n    // the animation to the new height can have an origin.\n    if (this._tabBodyWrapper.nativeElement.offsetHeight) {\n      wrapper.style.height = tabHeight + 'px';\n    }\n  }\n\n  /** Removes the height of the tab body wrapper. */\n  _removeTabBodyWrapperHeight(): void {\n    const wrapper = this._tabBodyWrapper.nativeElement;\n    this._tabBodyWrapperHeight = wrapper.clientHeight;\n    wrapper.style.height = '';\n    this.animationDone.emit();\n  }\n\n  /** Handle click events, setting new selected index if appropriate. */\n  _handleClick(tab: MatTab, tabHeader: MatTabGroupBaseHeader, index: number) {\n    tabHeader.focusIndex = index;\n\n    if (!tab.disabled) {\n      this.selectedIndex = index;\n    }\n  }\n\n  /** Retrieves the tabindex for the tab. */\n  _getTabIndex(index: number): number {\n    const targetIndex = this._lastFocusedTabIndex ?? this.selectedIndex;\n    return index === targetIndex ? 0 : -1;\n  }\n\n  /** Callback for when the focused state of a tab has changed. */\n  _tabFocusChanged(focusOrigin: FocusOrigin, index: number) {\n    // Mouse/touch focus happens during the `mousedown`/`touchstart` phase which\n    // can cause the tab to be moved out from under the pointer, interrupting the\n    // click sequence (see #21898). We don't need to scroll the tab into view for\n    // such cases anyway, because it will be done when the tab becomes selected.\n    if (focusOrigin && focusOrigin !== 'mouse' && focusOrigin !== 'touch') {\n      this._tabHeader.focusIndex = index;\n    }\n  }\n}\n\n/** A simple change event emitted on focus or selection changes. */\nexport class MatTabChangeEvent {\n  /** Index of the currently-selected tab. */\n  index: number;\n  /** Reference to the currently-selected tab. */\n  tab: MatTab;\n}\n", "<mat-tab-header #tabHeader\n                [selectedIndex]=\"selectedIndex || 0\"\n                [disableRipple]=\"disableRipple\"\n                [disablePagination]=\"disablePagination\"\n                (indexFocused)=\"_focusChanged($event)\"\n                (selectFocusedIndex)=\"selectedIndex = $event\">\n\n  @for (tab of _tabs; track tab; let i = $index) {\n    <div class=\"mdc-tab mat-mdc-tab mat-mdc-focus-indicator\"\n        #tabNode\n        role=\"tab\"\n        matTabLabelWrapper\n        cdkMonitorElementFocus\n        [id]=\"_getTabLabelId(i)\"\n        [attr.tabIndex]=\"_getTabIndex(i)\"\n        [attr.aria-posinset]=\"i + 1\"\n        [attr.aria-setsize]=\"_tabs.length\"\n        [attr.aria-controls]=\"_getTabContentId(i)\"\n        [attr.aria-selected]=\"selectedIndex === i\"\n        [attr.aria-label]=\"tab.ariaLabel || null\"\n        [attr.aria-labelledby]=\"(!tab.ariaLabel && tab.ariaLabelledby) ? tab.ariaLabelledby : null\"\n        [class.mdc-tab--active]=\"selectedIndex === i\"\n        [class]=\"tab.labelClass\"\n        [disabled]=\"tab.disabled\"\n        [fitInkBarToContent]=\"fitInkBarToContent\"\n        (click)=\"_handleClick(tab, tabHeader, i)\"\n        (cdkFocusChange)=\"_tabFocusChanged($event, i)\">\n      <span class=\"mdc-tab__ripple\"></span>\n\n      <!-- Needs to be a separate element, because we can't put\n          `overflow: hidden` on tab due to the ink bar. -->\n      <div\n        class=\"mat-mdc-tab-ripple\"\n        mat-ripple\n        [matRippleTrigger]=\"tabNode\"\n        [matRippleDisabled]=\"tab.disabled || disableRipple\"></div>\n\n      <span class=\"mdc-tab__content\">\n        <span class=\"mdc-tab__text-label\">\n          <!--\n            If there is a label template, use it, otherwise fall back to the text label.\n            Note that we don't have indentation around the text label, because it adds\n            whitespace around the text which breaks some internal tests.\n          -->\n          @if (tab.templateLabel) {\n            <ng-template [cdkPortalOutlet]=\"tab.templateLabel\"></ng-template>\n          } @else {{{tab.textLabel}}}\n        </span>\n      </span>\n    </div>\n  }\n</mat-tab-header>\n\n<!--\n  We need to project the content somewhere to avoid hydration errors. Some observations:\n  1. This is only necessary on the server.\n  2. We get a hydration error if there aren't any nodes after the `ng-content`.\n  3. We get a hydration error if `ng-content` is wrapped in another element.\n-->\n@if (_isServer) {\n  <ng-content/>\n}\n\n<div\n  class=\"mat-mdc-tab-body-wrapper\"\n  [class._mat-animation-noopable]=\"_animationMode === 'NoopAnimations'\"\n  #tabBodyWrapper>\n  @for (tab of _tabs; track tab; let i = $index) {\n    <mat-tab-body role=\"tabpanel\"\n                 [id]=\"_getTabContentId(i)\"\n                 [attr.tabindex]=\"(contentTabIndex != null && selectedIndex === i) ? contentTabIndex : null\"\n                 [attr.aria-labelledby]=\"_getTabLabelId(i)\"\n                 [attr.aria-hidden]=\"selectedIndex !== i\"\n                 [class.mat-mdc-tab-body-active]=\"selectedIndex === i\"\n                 [class]=\"tab.bodyClass\"\n                 [content]=\"tab.content!\"\n                 [position]=\"tab.position!\"\n                 [origin]=\"tab.origin\"\n                 [animationDuration]=\"animationDuration\"\n                 [preserveContent]=\"preserveContent\"\n                 (_onCentered)=\"_removeTabBodyWrapperHeight()\"\n                 (_onCentering)=\"_setTabBodyWrapperHeight($event)\">\n    </mat-tab-body>\n  }\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {\n  AfterContentChecked,\n  AfterContentInit,\n  AfterViewInit,\n  Attribute,\n  booleanAttribute,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChildren,\n  ElementRef,\n  forwardRef,\n  Inject,\n  Input,\n  NgZone,\n  numberAttribute,\n  OnDestroy,\n  Optional,\n  QueryList,\n  ViewChild,\n  ViewEncapsulation,\n  ANIMATION_MODULE_TYPE,\n} from '@angular/core';\nimport {\n  MAT_RIPPLE_GLOBAL_OPTIONS,\n  MatRipple,\n  RippleConfig,\n  RippleGlobalOptions,\n  RippleTarget,\n  ThemePalette,\n} from '@angular/material/core';\nimport {FocusableOption, FocusMonitor} from '@angular/cdk/a11y';\nimport {Directionality} from '@angular/cdk/bidi';\nimport {ViewportRuler} from '@angular/cdk/scrolling';\nimport {Platform} from '@angular/cdk/platform';\nimport {MatInkBar, InkBarItem} from '../ink-bar';\nimport {BehaviorSubject, Subject} from 'rxjs';\nimport {startWith, takeUntil} from 'rxjs/operators';\nimport {ENTER, SPACE} from '@angular/cdk/keycodes';\nimport {MAT_TABS_CONFIG, MatTabsConfig} from '../tab-config';\nimport {MatPaginatedTabHeader} from '../paginated-tab-header';\nimport {CdkObserveContent} from '@angular/cdk/observers';\n\n// Increasing integer for generating unique ids for tab nav components.\nlet nextUniqueId = 0;\n\n/**\n * Navigation component matching the styles of the tab group header.\n * Provides anchored navigation with animated ink bar.\n */\n@Component({\n  selector: '[mat-tab-nav-bar]',\n  exportAs: 'matTabNavBar, matTabNav',\n  templateUrl: 'tab-nav-bar.html',\n  styleUrl: 'tab-nav-bar.css',\n  host: {\n    '[attr.role]': '_getRole()',\n    'class': 'mat-mdc-tab-nav-bar mat-mdc-tab-header',\n    '[class.mat-mdc-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n    '[class.mat-mdc-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\",\n    '[class.mat-mdc-tab-nav-bar-stretch-tabs]': 'stretchTabs',\n    '[class.mat-primary]': 'color !== \"warn\" && color !== \"accent\"',\n    '[class.mat-accent]': 'color === \"accent\"',\n    '[class.mat-warn]': 'color === \"warn\"',\n    '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n    '[style.--mat-tab-animation-duration]': 'animationDuration',\n  },\n  encapsulation: ViewEncapsulation.None,\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  standalone: true,\n  imports: [MatRipple, CdkObserveContent],\n})\nexport class MatTabNav\n  extends MatPaginatedTabHeader\n  implements AfterContentChecked, AfterContentInit, OnDestroy, AfterViewInit\n{\n  /** Whether the ink bar should fit its width to the size of the tab label content. */\n  @Input({transform: booleanAttribute})\n  get fitInkBarToContent(): boolean {\n    return this._fitInkBarToContent.value;\n  }\n  set fitInkBarToContent(value: boolean) {\n    this._fitInkBarToContent.next(value);\n    this._changeDetectorRef.markForCheck();\n  }\n  _fitInkBarToContent = new BehaviorSubject(false);\n\n  /** Whether tabs should be stretched to fill the header. */\n  @Input({alias: 'mat-stretch-tabs', transform: booleanAttribute})\n  stretchTabs: boolean = true;\n\n  @Input()\n  get animationDuration(): string {\n    return this._animationDuration;\n  }\n\n  set animationDuration(value: string | number) {\n    const stringValue = value + '';\n    this._animationDuration = /^\\d+$/.test(stringValue) ? value + 'ms' : stringValue;\n  }\n\n  private _animationDuration: string;\n\n  /** Query list of all tab links of the tab navigation. */\n  @ContentChildren(forwardRef(() => MatTabLink), {descendants: true}) _items: QueryList<MatTabLink>;\n\n  /** Background color of the tab nav. */\n  @Input()\n  get backgroundColor(): ThemePalette {\n    return this._backgroundColor;\n  }\n\n  set backgroundColor(value: ThemePalette) {\n    const classList = this._elementRef.nativeElement.classList;\n    classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n\n    if (value) {\n      classList.add('mat-tabs-with-background', `mat-background-${value}`);\n    }\n\n    this._backgroundColor = value;\n  }\n\n  private _backgroundColor: ThemePalette;\n\n  /** Whether the ripple effect is disabled or not. */\n  @Input({transform: booleanAttribute})\n  disableRipple: boolean = false;\n\n  /** Theme color of the nav bar. */\n  @Input() color: ThemePalette = 'primary';\n\n  /**\n   * Associated tab panel controlled by the nav bar. If not provided, then the nav bar\n   * follows the ARIA link / navigation landmark pattern. If provided, it follows the\n   * ARIA tabs design pattern.\n   */\n  @Input() tabPanel?: MatTabNavPanel;\n\n  @ViewChild('tabListContainer', {static: true}) _tabListContainer: ElementRef;\n  @ViewChild('tabList', {static: true}) _tabList: ElementRef;\n  @ViewChild('tabListInner', {static: true}) _tabListInner: ElementRef;\n  @ViewChild('nextPaginator') _nextPaginator: ElementRef<HTMLElement>;\n  @ViewChild('previousPaginator') _previousPaginator: ElementRef<HTMLElement>;\n  _inkBar: MatInkBar;\n\n  constructor(\n    elementRef: ElementRef,\n    @Optional() dir: Directionality,\n    ngZone: NgZone,\n    changeDetectorRef: ChangeDetectorRef,\n    viewportRuler: ViewportRuler,\n    platform: Platform,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) animationMode?: string,\n    @Optional() @Inject(MAT_TABS_CONFIG) defaultConfig?: MatTabsConfig,\n  ) {\n    super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n    this.disablePagination =\n      defaultConfig && defaultConfig.disablePagination != null\n        ? defaultConfig.disablePagination\n        : false;\n    this.fitInkBarToContent =\n      defaultConfig && defaultConfig.fitInkBarToContent != null\n        ? defaultConfig.fitInkBarToContent\n        : false;\n    this.stretchTabs =\n      defaultConfig && defaultConfig.stretchTabs != null ? defaultConfig.stretchTabs : true;\n  }\n\n  protected _itemSelected() {\n    // noop\n  }\n\n  override ngAfterContentInit() {\n    this._inkBar = new MatInkBar(this._items);\n    // We need this to run before the `changes` subscription in parent to ensure that the\n    // selectedIndex is up-to-date by the time the super class starts looking for it.\n    this._items.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n      this.updateActiveLink();\n    });\n\n    super.ngAfterContentInit();\n  }\n\n  override ngAfterViewInit() {\n    if (!this.tabPanel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw new Error('A mat-tab-nav-panel must be specified via [tabPanel].');\n    }\n    super.ngAfterViewInit();\n  }\n\n  /** Notifies the component that the active link has been changed. */\n  updateActiveLink() {\n    if (!this._items) {\n      return;\n    }\n\n    const items = this._items.toArray();\n\n    for (let i = 0; i < items.length; i++) {\n      if (items[i].active) {\n        this.selectedIndex = i;\n        this._changeDetectorRef.markForCheck();\n\n        if (this.tabPanel) {\n          this.tabPanel._activeTabId = items[i].id;\n        }\n\n        return;\n      }\n    }\n\n    // The ink bar should hide itself if no items are active.\n    this.selectedIndex = -1;\n    this._inkBar.hide();\n  }\n\n  _getRole(): string | null {\n    return this.tabPanel ? 'tablist' : this._elementRef.nativeElement.getAttribute('role');\n  }\n}\n\n/**\n * Link inside a `mat-tab-nav-bar`.\n */\n@Component({\n  selector: '[mat-tab-link], [matTabLink]',\n  exportAs: 'matTabLink',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  templateUrl: 'tab-link.html',\n  styleUrl: 'tab-link.css',\n  host: {\n    'class': 'mdc-tab mat-mdc-tab-link mat-mdc-focus-indicator',\n    '[attr.aria-controls]': '_getAriaControls()',\n    '[attr.aria-current]': '_getAriaCurrent()',\n    '[attr.aria-disabled]': 'disabled',\n    '[attr.aria-selected]': '_getAriaSelected()',\n    '[attr.id]': 'id',\n    '[attr.tabIndex]': '_getTabIndex()',\n    '[attr.role]': '_getRole()',\n    '[class.mat-mdc-tab-disabled]': 'disabled',\n    '[class.mdc-tab--active]': 'active',\n    '(focus)': '_handleFocus()',\n    '(keydown)': '_handleKeydown($event)',\n  },\n  standalone: true,\n  imports: [MatRipple],\n})\nexport class MatTabLink\n  extends InkBarItem\n  implements AfterViewInit, OnDestroy, RippleTarget, FocusableOption\n{\n  private readonly _destroyed = new Subject<void>();\n\n  /** Whether the tab link is active or not. */\n  protected _isActive: boolean = false;\n\n  /** Whether the link is active. */\n  @Input({transform: booleanAttribute})\n  get active(): boolean {\n    return this._isActive;\n  }\n\n  set active(value: boolean) {\n    if (value !== this._isActive) {\n      this._isActive = value;\n      this._tabNavBar.updateActiveLink();\n    }\n  }\n\n  /** Whether the tab link is disabled. */\n  @Input({transform: booleanAttribute})\n  disabled: boolean = false;\n\n  /** Whether ripples are disabled on the tab link. */\n  @Input({transform: booleanAttribute})\n  disableRipple: boolean = false;\n\n  @Input({\n    transform: (value: unknown) => (value == null ? 0 : numberAttribute(value)),\n  })\n  tabIndex: number = 0;\n\n  /**\n   * Ripple configuration for ripples that are launched on pointer down. The ripple config\n   * is set to the global ripple options since we don't have any configurable options for\n   * the tab link ripples.\n   * @docs-private\n   */\n  rippleConfig: RippleConfig & RippleGlobalOptions;\n\n  /**\n   * Whether ripples are disabled on interaction.\n   * @docs-private\n   */\n  get rippleDisabled(): boolean {\n    return (\n      this.disabled ||\n      this.disableRipple ||\n      this._tabNavBar.disableRipple ||\n      !!this.rippleConfig.disabled\n    );\n  }\n\n  /** Unique id for the tab. */\n  @Input() id = `mat-tab-link-${nextUniqueId++}`;\n\n  constructor(\n    private _tabNavBar: MatTabNav,\n    /** @docs-private */ public elementRef: ElementRef,\n    @Optional() @Inject(MAT_RIPPLE_GLOBAL_OPTIONS) globalRippleOptions: RippleGlobalOptions | null,\n    @Attribute('tabindex') tabIndex: string,\n    private _focusMonitor: FocusMonitor,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) animationMode?: string,\n  ) {\n    super();\n\n    this.rippleConfig = globalRippleOptions || {};\n    this.tabIndex = parseInt(tabIndex) || 0;\n\n    if (animationMode === 'NoopAnimations') {\n      this.rippleConfig.animation = {enterDuration: 0, exitDuration: 0};\n    }\n\n    _tabNavBar._fitInkBarToContent\n      .pipe(takeUntil(this._destroyed))\n      .subscribe(fitInkBarToContent => {\n        this.fitInkBarToContent = fitInkBarToContent;\n      });\n  }\n\n  /** Focuses the tab link. */\n  focus() {\n    this.elementRef.nativeElement.focus();\n  }\n\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this.elementRef);\n  }\n\n  override ngOnDestroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n    super.ngOnDestroy();\n    this._focusMonitor.stopMonitoring(this.elementRef);\n  }\n\n  _handleFocus() {\n    // Since we allow navigation through tabbing in the nav bar, we\n    // have to update the focused index whenever the link receives focus.\n    this._tabNavBar.focusIndex = this._tabNavBar._items.toArray().indexOf(this);\n  }\n\n  _handleKeydown(event: KeyboardEvent) {\n    if (event.keyCode === SPACE || event.keyCode === ENTER) {\n      if (this.disabled) {\n        event.preventDefault();\n      } else if (this._tabNavBar.tabPanel) {\n        // Only prevent the default action on space since it can scroll the page.\n        // Don't prevent enter since it can break link navigation.\n        if (event.keyCode === SPACE) {\n          event.preventDefault();\n        }\n\n        this.elementRef.nativeElement.click();\n      }\n    }\n  }\n\n  _getAriaControls(): string | null {\n    return this._tabNavBar.tabPanel\n      ? this._tabNavBar.tabPanel?.id\n      : this.elementRef.nativeElement.getAttribute('aria-controls');\n  }\n\n  _getAriaSelected(): string | null {\n    if (this._tabNavBar.tabPanel) {\n      return this.active ? 'true' : 'false';\n    } else {\n      return this.elementRef.nativeElement.getAttribute('aria-selected');\n    }\n  }\n\n  _getAriaCurrent(): string | null {\n    return this.active && !this._tabNavBar.tabPanel ? 'page' : null;\n  }\n\n  _getRole(): string | null {\n    return this._tabNavBar.tabPanel ? 'tab' : this.elementRef.nativeElement.getAttribute('role');\n  }\n\n  _getTabIndex(): number {\n    if (this._tabNavBar.tabPanel) {\n      return this._isActive && !this.disabled ? 0 : -1;\n    } else {\n      return this.disabled ? -1 : this.tabIndex;\n    }\n  }\n}\n\n/**\n * Tab panel component associated with MatTabNav.\n */\n@Component({\n  selector: 'mat-tab-nav-panel',\n  exportAs: 'matTabNavPanel',\n  template: '<ng-content></ng-content>',\n  host: {\n    '[attr.aria-labelledby]': '_activeTabId',\n    '[attr.id]': 'id',\n    'class': 'mat-mdc-tab-nav-panel',\n    'role': 'tabpanel',\n  },\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: true,\n})\nexport class MatTabNavPanel {\n  /** Unique id for the tab panel. */\n  @Input() id = `mat-tab-nav-panel-${nextUniqueId++}`;\n\n  /** Id of the active tab in the nav bar. */\n  _activeTabId?: string;\n}\n", "<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\n<button class=\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\"\n     #previousPaginator\n     aria-hidden=\"true\"\n     type=\"button\"\n     mat-ripple\n     tabindex=\"-1\"\n     [matRippleDisabled]=\"_disableScrollBefore || disableRipple\"\n     [class.mat-mdc-tab-header-pagination-disabled]=\"_disableScrollBefore\"\n     [disabled]=\"_disableScrollBefore || null\"\n     (click)=\"_handlePaginatorClick('before')\"\n     (mousedown)=\"_handlePaginatorPress('before', $event)\"\n     (touchend)=\"_stopInterval()\">\n  <div class=\"mat-mdc-tab-header-pagination-chevron\"></div>\n</button>\n\n<div class=\"mat-mdc-tab-link-container\" #tabListContainer (keydown)=\"_handleKeydown($event)\">\n  <div class=\"mat-mdc-tab-list\" #tabList (cdkObserveContent)=\"_onContentChanges()\">\n    <div class=\"mat-mdc-tab-links\" #tabListInner>\n      <ng-content></ng-content>\n    </div>\n  </div>\n</div>\n\n<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\n<button class=\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\"\n     #nextPaginator\n     aria-hidden=\"true\"\n     type=\"button\"\n     mat-ripple\n     [matRippleDisabled]=\"_disableScrollAfter || disableRipple\"\n     [class.mat-mdc-tab-header-pagination-disabled]=\"_disableScrollAfter\"\n     [disabled]=\"_disableScrollAfter || null\"\n     tabindex=\"-1\"\n     (mousedown)=\"_handlePaginatorPress('after', $event)\"\n     (click)=\"_handlePaginatorClick('after')\"\n     (touchend)=\"_stopInterval()\">\n  <div class=\"mat-mdc-tab-header-pagination-chevron\"></div>\n</button>\n", "<span class=\"mdc-tab__ripple\"></span>\n\n<div\n  class=\"mat-mdc-tab-ripple\"\n  mat-ripple\n  [matRippleTrigger]=\"elementRef.nativeElement\"\n  [matRippleDisabled]=\"rippleDisabled\"></div>\n\n<span class=\"mdc-tab__content\">\n  <span class=\"mdc-tab__text-label\">\n    <ng-content></ng-content>\n  </span>\n</span>\n\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '@angular/material/core';\nimport {MatTabContent} from './tab-content';\nimport {MatTabLabel} from './tab-label';\nimport {MatTab} from './tab';\nimport {MatTabGroup} from './tab-group';\nimport {MatTabNav, MatTabNavPanel, MatTabLink} from './tab-nav-bar/tab-nav-bar';\n\n@NgModule({\n  imports: [\n    MatCommonModule,\n    MatTabContent,\n    MatTabLabel,\n    MatTab,\n    MatTabGroup,\n    MatTabNav,\n    MatTabNavPanel,\n    MatTabLink,\n  ],\n  exports: [\n    MatCommonModule,\n    MatTabContent,\n    MatTabLabel,\n    MatTab,\n    MatTabGroup,\n    MatTabNav,\n    MatTabNavPanel,\n    MatTabLink,\n  ],\n})\nexport class MatTabsModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["observableOf", "i1", "i2"], "mappings": ";;;;;;;;;;;;;;;;;;AAUA;;;;AAIG;MACU,eAAe,GAAG,IAAI,cAAc,CAAgB,eAAe,EAAE;AAElF;MAMa,aAAa,CAAA;IACxB,WAAY,6BAAmC,QAA0B,EAAA;QAA1B,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAkB;KAAI;8GADlE,aAAa,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,WAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,SAAA,EAHb,CAAC,EAAC,OAAO,EAAE,eAAe,EAAE,WAAW,EAAE,aAAa,EAAC,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAGxD,aAAa,EAAA,UAAA,EAAA,CAAA;kBALzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iBAAiB;oBAC3B,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,eAAe,EAAE,WAAW,EAAe,aAAA,EAAC,CAAC;AACnE,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;;ACJD;;;;AAIG;MACU,aAAa,GAAG,IAAI,cAAc,CAAc,aAAa,EAAE;AAE5E;;;AAGG;MACU,OAAO,GAAG,IAAI,cAAc,CAAM,SAAS,EAAE;AAE1D;AAMM,MAAO,WAAY,SAAQ,SAAS,CAAA;AACxC,IAAA,WAAA,CACE,WAA6B,EAC7B,gBAAkC,EACE,WAAgB,EAAA;AAEpD,QAAA,KAAK,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;QAFD,IAAW,CAAA,WAAA,GAAX,WAAW,CAAK;KAGrD;AAPU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAW,6EAIZ,OAAO,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAJN,WAAW,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gCAAA,EAAA,SAAA,EAHX,CAAC,EAAC,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,WAAW,EAAC,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAGpD,WAAW,EAAA,UAAA,EAAA,CAAA;kBALvB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gCAAgC;oBAC1C,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,aAAa,EAAE,WAAW,EAAa,WAAA,EAAC,CAAC;AAC/D,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BAKI,MAAM;2BAAC,OAAO,CAAA;;0BAAG,QAAQ;;;ACV9B;;;AAGG;MACU,aAAa,GAAG,IAAI,cAAc,CAAM,eAAe,EAAE;MAoBzD,MAAM,CAAA;;AAMjB,IAAA,IACI,aAAa,GAAA;QACf,OAAO,IAAI,CAAC,cAAc,CAAC;KAC5B;IACD,IAAI,aAAa,CAAC,KAAkB,EAAA;AAClC,QAAA,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;KACpC;;AAmCD,IAAA,IAAI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,cAAc,CAAC;KAC5B;IAsBD,WACU,CAAA,iBAAmC,EACD,gBAAqB,EAAA;QADvD,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAkB;QACD,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAK;;QAtEjE,IAAQ,CAAA,QAAA,GAAY,KAAK,CAAC;AAY1B;;AAEG;QAGK,IAAgB,CAAA,gBAAA,GAAqB,SAAU,CAAC;;QAMxC,IAAS,CAAA,SAAA,GAAW,EAAE,CAAC;;QAkB/B,IAAc,CAAA,cAAA,GAA0B,IAAI,CAAC;;AAQ5C,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,OAAO,EAAQ,CAAC;AAE7C;;;AAGG;QACH,IAAQ,CAAA,QAAA,GAAkB,IAAI,CAAC;AAE/B;;;AAGG;QACH,IAAM,CAAA,MAAA,GAAkB,IAAI,CAAC;AAE7B;;AAEG;QACH,IAAQ,CAAA,QAAA,GAAG,KAAK,CAAC;KAKb;AAEJ,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,IAAI,OAAO,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE;AAC7E,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;SAC3B;KACF;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;KAC/B;IAED,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,CACtC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,EAC9C,IAAI,CAAC,iBAAiB,CACvB,CAAC;KACH;AAED;;;;;AAKG;AACK,IAAA,sBAAsB,CAAC,KAA8B,EAAA;;;;;QAK3D,IAAI,KAAK,IAAI,KAAK,CAAC,WAAW,KAAK,IAAI,EAAE;AACvC,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;SAC7B;KACF;AA3GU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,MAAM,kDAyEP,aAAa,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAzEZ,MAAM,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,SAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAEE,gBAAgB,CAVxB,EAAA,SAAA,EAAA,CAAA,OAAA,EAAA,WAAA,CAAA,EAAA,SAAA,EAAA,CAAA,YAAA,EAAA,WAAA,CAAA,EAAA,cAAA,EAAA,CAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,EAAC,CAAC,EActC,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,WAAW,EAYX,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,aAAa,2BAAS,WAAW,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAKpC,WAAW,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EC9ExB,+QAIA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FDmDa,MAAM,EAAA,UAAA,EAAA,CAAA;kBAlBlB,SAAS;+BACE,SAAS,EAAA,eAAA,EAMF,uBAAuB,CAAC,OAAO,iBACjC,iBAAiB,CAAC,IAAI,EAAA,QAAA,EAC3B,QAAQ,EAAA,SAAA,EACP,CAAC,EAAC,OAAO,EAAE,OAAO,EAAE,WAAW,QAAQ,EAAC,CAAC,EACxC,UAAA,EAAA,IAAI,EACV,IAAA,EAAA;;;AAGJ,wBAAA,QAAQ,EAAE,EAAE;AACb,qBAAA,EAAA,QAAA,EAAA,+QAAA,EAAA,CAAA;;0BA2EE,MAAM;2BAAC,aAAa,CAAA;;0BAAG,QAAQ;yCAtElC,QAAQ,EAAA,CAAA;sBADP,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAKhC,aAAa,EAAA,CAAA;sBADhB,YAAY;uBAAC,WAAW,CAAA;gBAcjB,gBAAgB,EAAA,CAAA;sBAFvB,YAAY;uBAAC,aAAa,EAAE,EAAC,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAC,CAAA;gBAKtB,gBAAgB,EAAA,CAAA;sBAAvD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,WAAW,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC,CAAA;gBAGtB,SAAS,EAAA,CAAA;sBAAxB,KAAK;uBAAC,OAAO,CAAA;gBAGO,SAAS,EAAA,CAAA;sBAA7B,KAAK;uBAAC,YAAY,CAAA;gBAMO,cAAc,EAAA,CAAA;sBAAvC,KAAK;uBAAC,iBAAiB,CAAA;gBAGf,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAGG,SAAS,EAAA,CAAA;sBAAjB,KAAK;;;AEjER;AACA,MAAM,YAAY,GAAG,2BAA2B,CAAC;AAEjD;AACA,MAAM,mBAAmB,GAAG,kCAAkC,CAAC;AAE/D;;;AAGG;MACU,SAAS,CAAA;AAIpB,IAAA,WAAA,CAAoB,MAAgC,EAAA;QAAhC,IAAM,CAAA,MAAA,GAAN,MAAM,CAA0B;KAAI;;IAGxD,IAAI,GAAA;AACF,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;KACtD;;AAGD,IAAA,cAAc,CAAC,OAAoB,EAAA;QACjC,MAAM,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,KAAK,OAAO,CAAC,CAAC;AAC9F,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC;AAEtC,QAAA,IAAI,iBAAiB,KAAK,WAAW,EAAE;YACrC,OAAO;SACR;QAED,WAAW,EAAE,gBAAgB,EAAE,CAAC;QAEhC,IAAI,iBAAiB,EAAE;YACrB,MAAM,OAAO,GAAG,WAAW,EAAE,UAAU,CAAC,aAAa,CAAC,qBAAqB,IAAI,CAAC;;AAGhF,YAAA,iBAAiB,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;AAC1C,YAAA,IAAI,CAAC,YAAY,GAAG,iBAAiB,CAAC;SACvC;KACF;AACF,CAAA;MAGqB,UAAU,CAAA;AADhC,IAAA,WAAA,GAAA;AAEU,QAAA,IAAA,CAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC,CAAC;QAG1D,IAAa,CAAA,aAAA,GAAG,KAAK,CAAC;AAsG/B,KAAA;;AAnGC,IAAA,IACI,kBAAkB,GAAA;QACpB,OAAO,IAAI,CAAC,aAAa,CAAC;KAC3B;IACD,IAAI,kBAAkB,CAAC,QAAiB,EAAA;AACtC,QAAA,IAAI,IAAI,CAAC,aAAa,KAAK,QAAQ,EAAE;AACnC,YAAA,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC;AAE9B,YAAA,IAAI,IAAI,CAAC,cAAc,EAAE;gBACvB,IAAI,CAAC,oBAAoB,EAAE,CAAC;aAC7B;SACF;KACF;;AAGD,IAAA,cAAc,CAAC,2BAAqC,EAAA;AAClD,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;;;AAI/C,QAAA,IACE,CAAC,2BAA2B;YAC5B,CAAC,OAAO,CAAC,qBAAqB;AAC9B,YAAA,CAAC,IAAI,CAAC,qBAAqB,EAC3B;AACA,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YACpC,OAAO;SACR;;;;AAMD,QAAA,MAAM,iBAAiB,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;QAC1D,MAAM,UAAU,GAAG,2BAA2B,CAAC,KAAK,GAAG,iBAAiB,CAAC,KAAK,CAAC;QAC/E,MAAM,SAAS,GAAG,2BAA2B,CAAC,IAAI,GAAG,iBAAiB,CAAC,IAAI,CAAC;AAC5E,QAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;AAC3C,QAAA,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,WAAW,CAC1C,WAAW,EACX,cAAc,SAAS,CAAA,WAAA,EAAc,UAAU,CAAA,CAAA,CAAG,CACnD,CAAC;;QAGF,OAAO,CAAC,qBAAqB,EAAE,CAAC;AAEhC,QAAA,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAC9C,QAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QACpC,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;KAC/D;;IAGD,gBAAgB,GAAA;QACd,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;KAC/D;;IAGD,QAAQ,GAAA;QACN,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC7B;;IAGD,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,cAAc,EAAE,MAAM,EAAE,CAAC;QAC9B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,qBAAqB,GAAG,IAAK,CAAC;KAC1D;;IAGO,oBAAoB,GAAA;QAC1B,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,aAAa,IAAI,QAAQ,CAAC;AAC9E,QAAA,MAAM,aAAa,IAAI,IAAI,CAAC,cAAc,GAAG,YAAY,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;AACjF,QAAA,MAAM,oBAAoB,IAAI,IAAI,CAAC,qBAAqB,GAAG,YAAY,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;AAE/F,QAAA,aAAa,CAAC,SAAS,GAAG,mBAAmB,CAAC;AAC9C,QAAA,oBAAoB,CAAC,SAAS;AAC5B,YAAA,kEAAkE,CAAC;AAErE,QAAA,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACtD,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC7B;AAED;;;AAGG;IACK,oBAAoB,GAAA;AAC1B,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAC3E,YAAA,MAAM,KAAK,CAAC,6DAA6D,CAAC,CAAC;SAC5E;AAED,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa;cACpC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,aAAa,CAAC,mBAAmB,CAAC;AACnE,cAAE,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;AAEnC,QAAA,IAAI,CAAC,aAAa,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AACrE,YAAA,MAAM,KAAK,CAAC,qCAAqC,CAAC,CAAC;SACpD;AAED,QAAA,aAAc,CAAC,WAAW,CAAC,IAAI,CAAC,cAAe,CAAC,CAAC;KAClD;8GAzGmB,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAV,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAU,6EAOX,gBAAgB,CAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAPf,UAAU,EAAA,UAAA,EAAA,CAAA;kBAD/B,SAAS;8BASJ,kBAAkB,EAAA,CAAA;sBADrB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;;AA6GtC;;;AAGG;SACa,+BAA+B,GAAA;AAC7C,IAAA,MAAM,MAAM,GAAG,CAAC,OAAoB,MAAM;AACxC,QAAA,IAAI,EAAE,OAAO,GAAG,CAAC,OAAO,CAAC,UAAU,IAAI,CAAC,IAAI,IAAI,GAAG,GAAG;AACtD,QAAA,KAAK,EAAE,OAAO,GAAG,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,GAAG,GAAG;AACzD,KAAA,CAAC,CAAC;AAEH,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;MACa,uBAAuB,GAAG,IAAI,cAAc,CACvD,qBAAqB,EACrB;AACE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,+BAA+B;AACzC,CAAA;;ACtMH;;;AAGG;AASG,MAAO,kBAAmB,SAAQ,UAAU,CAAA;AAKhD,IAAA,WAAA,CAAmB,UAAsB,EAAA;AACvC,QAAA,KAAK,EAAE,CAAC;QADS,IAAU,CAAA,UAAA,GAAV,UAAU,CAAY;;QAFzC,IAAQ,CAAA,QAAA,GAAY,KAAK,CAAC;KAIzB;;IAGD,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;KACvC;IAED,aAAa,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,UAAU,CAAC;KACjD;IAED,cAAc,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,WAAW,CAAC;KAClD;8GApBU,kBAAkB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAlB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,kBAAkB,qGAEV,gBAAgB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,4BAAA,EAAA,UAAA,EAAA,oBAAA,EAAA,YAAA,EAAA,EAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAFxB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAR9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sBAAsB;AAChC,oBAAA,IAAI,EAAE;AACJ,wBAAA,8BAA8B,EAAE,UAAU;AAC1C,wBAAA,sBAAsB,EAAE,YAAY;AACrC,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;+EAIC,QAAQ,EAAA,CAAA;sBADP,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;;;ACmBtC;AACA,MAAM,2BAA2B,GAAG,+BAA+B,CAAC;AAClE,IAAA,OAAO,EAAE,IAAI;AACd,CAAA,CAAyB,CAAC;AAS3B;;;AAGG;AACH,MAAM,mBAAmB,GAAG,GAAG,CAAC;AAEhC;;;AAGG;AACH,MAAM,sBAAsB,GAAG,GAAG,CAAC;AAKnC;;;AAGG;MAEmB,qBAAqB,CAAA;;AAuDzC,IAAA,IACI,aAAa,GAAA;QACf,OAAO,IAAI,CAAC,cAAc,CAAC;KAC5B;IACD,IAAI,aAAa,CAAC,CAAS,EAAA;AACzB,QAAA,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAE/B,QAAA,IAAI,IAAI,CAAC,cAAc,IAAI,KAAK,EAAE;AAChC,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AAClC,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;AAE5B,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,gBAAA,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;aAC1C;SACF;KACF;AASD,IAAA,WAAA,CACY,WAAoC,EACpC,kBAAqC,EACvC,cAA6B,EACjB,IAAoB,EAChC,OAAe,EACf,SAAmB,EACuB,cAAuB,EAAA;QAN/D,IAAW,CAAA,WAAA,GAAX,WAAW,CAAyB;QACpC,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAmB;QACvC,IAAc,CAAA,cAAA,GAAd,cAAc,CAAe;QACjB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAgB;QAChC,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QACf,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QACuB,IAAc,CAAA,cAAA,GAAd,cAAc,CAAS;;QA1EnE,IAAe,CAAA,eAAA,GAAG,CAAC,CAAC;;QAGpB,IAAqB,CAAA,qBAAA,GAAG,KAAK,CAAC;;AAGnB,QAAA,IAAA,CAAA,UAAU,GAAG,IAAI,OAAO,EAAQ,CAAC;;QAGpD,IAAuB,CAAA,uBAAA,GAAG,KAAK,CAAC;;QAGhC,IAAmB,CAAA,mBAAA,GAAG,IAAI,CAAC;;QAG3B,IAAoB,CAAA,oBAAA,GAAG,IAAI,CAAC;;AAkBpB,QAAA,IAAA,CAAA,cAAc,GAAG,IAAI,OAAO,EAAQ,CAAC;AAE7C;;;AAGG;QAEH,IAAiB,CAAA,iBAAA,GAAY,KAAK,CAAC;QAmB3B,IAAc,CAAA,cAAA,GAAW,CAAC,CAAC;;AAGhB,QAAA,IAAA,CAAA,kBAAkB,GAAyB,IAAI,YAAY,EAAU,CAAC;;AAGtE,QAAA,IAAA,CAAA,YAAY,GAAyB,IAAI,YAAY,EAAU,CAAC;;AAYjF,QAAA,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAC7B,YAAA,SAAS,CAAC,WAAW,CAAC,aAAa,EAAE,YAAY,CAAC;AAC/C,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;iBAChC,SAAS,CAAC,MAAK;gBACd,IAAI,CAAC,aAAa,EAAE,CAAC;AACvB,aAAC,CAAC,CAAC;AACP,SAAC,CAAC,CAAC;KACJ;IAKD,eAAe,GAAA;;QAEb,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,YAAY,EAAE,2BAA2B,CAAC;AACxF,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAChC,SAAS,CAAC,MAAK;AACd,YAAA,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;AACvC,SAAC,CAAC,CAAC;QAEL,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,YAAY,EAAE,2BAA2B,CAAC;AACpF,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAChC,SAAS,CAAC,MAAK;AACd,YAAA,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;AACtC,SAAC,CAAC,CAAC;KACN;IAED,kBAAkB,GAAA;QAChB,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAGA,EAAY,CAAC,KAAK,CAAC,CAAC;QACrE,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC/C,MAAM,OAAO,GAAG,MAAK;YACnB,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,yBAAyB,EAAE,CAAC;AACnC,SAAC,CAAC;QAEF,IAAI,CAAC,WAAW,GAAG,IAAI,eAAe,CAA4B,IAAI,CAAC,MAAM,CAAC;AAC3E,aAAA,yBAAyB,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;AACrD,aAAA,cAAc,EAAE;AAChB,aAAA,QAAQ,EAAE;;AAEV,aAAA,aAAa,CAAC,MAAM,KAAK,CAAC,CAAC;QAE9B,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;;;;;AAMvD,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;;;AAIvD,QAAA,KAAK,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC;AAChE,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAChC,SAAS,CAAC,MAAK;;;;AAId,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAK;AACpB,gBAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;;oBAE1B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,CAC7B,CAAC,EACD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAE,IAAI,CAAC,eAAe,CAAC,CAC7D,CAAC;AACF,oBAAA,OAAO,EAAE,CAAC;AACZ,iBAAC,CAAC,CAAC;AACL,aAAC,CAAC,CAAC;YACH,IAAI,CAAC,WAAW,CAAC,yBAAyB,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC;AACzE,SAAC,CAAC,CAAC;;;;QAKL,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,aAAa,IAAG;AAChD,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACtC,YAAA,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;AACnC,SAAC,CAAC,CAAC;KACJ;;IAGO,aAAa,GAAA;AACnB,QAAA,IAAI,OAAO,cAAc,KAAK,UAAU,EAAE;AACxC,YAAA,OAAO,KAAK,CAAC;SACd;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAC7B,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,EACtB,SAAS,CACP,CAAC,QAA8C,KAC7C,IAAI,UAAU,CAAC,CAAC,QAAyC,KACvD,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,MAAM,cAAc,GAAG,IAAI,cAAc,CAAC,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;AAC7E,YAAA,QAAQ,CAAC,OAAO,CAAC,IAAI,IAAI,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC;AAChF,YAAA,OAAO,MAAK;gBACV,cAAc,CAAC,UAAU,EAAE,CAAC;AAC9B,aAAC,CAAC;SACH,CAAC,CACH,CACJ;;;QAGD,IAAI,CAAC,CAAC,CAAC;;;AAGP,QAAA,MAAM,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAC1F,CAAC;KACH;IAED,qBAAqB,GAAA;;QAEnB,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAC7C,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;AACzC,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;SACxC;;;AAID,QAAA,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAC9B,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACzC,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,IAAI,CAAC,yBAAyB,EAAE,CAAC;AACjC,YAAA,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;AACnC,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;SACxC;;;AAID,QAAA,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC/B,IAAI,CAAC,wBAAwB,EAAE,CAAC;AAChC,YAAA,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;AACpC,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;SACxC;KACF;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,CAAC;AAC5B,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;AAC3B,QAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;KAChC;;AAGD,IAAA,cAAc,CAAC,KAAoB,EAAA;;AAEjC,QAAA,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE;YACzB,OAAO;SACR;AAED,QAAA,QAAQ,KAAK,CAAC,OAAO;AACnB,YAAA,KAAK,KAAK,CAAC;AACX,YAAA,KAAK,KAAK;gBACR,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,aAAa,EAAE;AAC1C,oBAAA,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAE9C,oBAAA,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;wBAC1B,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC9C,wBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;qBAC3B;iBACF;gBACD,MAAM;AACR,YAAA;AACE,gBAAA,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;SACrC;KACF;AAED;;AAEG;IACH,iBAAiB,GAAA;QACf,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC;;;;AAK/D,QAAA,IAAI,WAAW,KAAK,IAAI,CAAC,mBAAmB,EAAE;AAC5C,YAAA,IAAI,CAAC,mBAAmB,GAAG,WAAW,IAAI,EAAE,CAAC;;;AAI7C,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAK;gBACpB,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,IAAI,CAAC,yBAAyB,EAAE,CAAC;AACjC,gBAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACzC,aAAC,CAAC,CAAC;SACJ;KACF;AAED;;;;;;AAMG;IACH,gBAAgB,GAAA;QACd,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,IAAI,CAAC,wBAAwB,EAAE,CAAC;KACjC;;AAGD,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,eAAgB,GAAG,CAAC,CAAC;KACjE;;IAGD,IAAI,UAAU,CAAC,KAAa,EAAA;AAC1B,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YAChF,OAAO;SACR;AAED,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;KACvC;AAED;;;AAGG;AACH,IAAA,aAAa,CAAC,KAAa,EAAA;QACzB,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;KAC5D;AAED;;;AAGG;AACH,IAAA,YAAY,CAAC,QAAgB,EAAA;AAC3B,QAAA,IAAI,IAAI,CAAC,uBAAuB,EAAE;AAChC,YAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;SAC/B;QAED,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YACrC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC;;;;AAKxC,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC;AACzD,YAAA,MAAM,GAAG,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;AAEvC,YAAA,IAAI,GAAG,IAAI,KAAK,EAAE;AAChB,gBAAA,WAAW,CAAC,UAAU,GAAG,CAAC,CAAC;aAC5B;iBAAM;gBACL,WAAW,CAAC,UAAU,GAAG,WAAW,CAAC,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;aAC5E;SACF;KACF;;IAGD,mBAAmB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;KAC/D;;IAGD,wBAAwB,GAAA;AACtB,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,OAAO;SACR;AAED,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;AAC3C,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,EAAE,KAAK,KAAK,GAAG,CAAC,cAAc,GAAG,cAAc,CAAC;;;;;;;AAQ3F,QAAA,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG,CAAA,WAAA,EAAc,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;AAMxF,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;YACjD,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,UAAU,GAAG,CAAC,CAAC;SACrD;KACF;;AAGD,IAAA,IAAI,cAAc,GAAA;QAChB,OAAO,IAAI,CAAC,eAAe,CAAC;KAC7B;IACD,IAAI,cAAc,CAAC,KAAa,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;KACvB;AAED;;;;;;;AAOG;AACH,IAAA,aAAa,CAAC,SAA0B,EAAA;QACtC,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,WAAW,CAAC;;QAGpE,MAAM,YAAY,GAAG,CAAC,CAAC,SAAS,IAAI,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,UAAU,IAAI,CAAC,CAAC;QAEzE,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,GAAG,YAAY,CAAC,CAAC;KAC5D;;AAGD,IAAA,qBAAqB,CAAC,SAA0B,EAAA;QAC9C,IAAI,CAAC,aAAa,EAAE,CAAC;AACrB,QAAA,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;KAC/B;AAED;;;;;AAKG;AACH,IAAA,cAAc,CAAC,UAAkB,EAAA;AAC/B,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,OAAO;SACR;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;QAE7E,IAAI,CAAC,aAAa,EAAE;YAClB,OAAO;SACR;;QAGD,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,WAAW,CAAC;QACpE,MAAM,EAAC,UAAU,EAAE,WAAW,EAAC,GAAG,aAAa,CAAC,UAAU,CAAC,aAAa,CAAC;QAEzE,IAAI,cAAsB,EAAE,aAAqB,CAAC;AAClD,QAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE,IAAI,KAAK,EAAE;YACvC,cAAc,GAAG,UAAU,CAAC;AAC5B,YAAA,aAAa,GAAG,cAAc,GAAG,WAAW,CAAC;SAC9C;aAAM;YACL,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,WAAW,GAAG,UAAU,CAAC;AAC1E,YAAA,cAAc,GAAG,aAAa,GAAG,WAAW,CAAC;SAC9C;AAED,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC;AAC7C,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC;AAEzD,QAAA,IAAI,cAAc,GAAG,gBAAgB,EAAE;;AAErC,YAAA,IAAI,CAAC,cAAc,IAAI,gBAAgB,GAAG,cAAc,CAAC;SAC1D;AAAM,aAAA,IAAI,aAAa,GAAG,eAAe,EAAE;;AAE1C,YAAA,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,GAAG,CAC7B,aAAa,GAAG,eAAe,EAC/B,cAAc,GAAG,gBAAgB,CAClC,CAAC;SACH;KACF;AAED;;;;;;;AAOG;IACH,uBAAuB,GAAA;AACrB,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAC1B,YAAA,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;SACtC;aAAM;AACL,YAAA,MAAM,SAAS,GACb,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC;YAE5F,IAAI,CAAC,SAAS,EAAE;AACd,gBAAA,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;aACzB;AAED,YAAA,IAAI,SAAS,KAAK,IAAI,CAAC,uBAAuB,EAAE;AAC9C,gBAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;aACxC;AAED,YAAA,IAAI,CAAC,uBAAuB,GAAG,SAAS,CAAC;SAC1C;KACF;AAED;;;;;;;;AAQG;IACH,uBAAuB,GAAA;AACrB,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;SAC7D;aAAM;;YAEL,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC;YACrD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC/E,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;SACxC;KACF;AAED;;;;;;AAMG;IACH,qBAAqB,GAAA;QACnB,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,WAAW,CAAC;QACrE,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,WAAW,CAAC;AACpE,QAAA,OAAO,eAAe,GAAG,UAAU,IAAI,CAAC,CAAC;KAC1C;;IAGD,yBAAyB,GAAA;AACvB,QAAA,MAAM,YAAY,GAChB,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC;AACvF,QAAA,MAAM,oBAAoB,GAAG,YAAY,GAAG,YAAY,CAAC,UAAU,CAAC,aAAa,GAAG,IAAI,CAAC;QAEzF,IAAI,oBAAoB,EAAE;AACxB,YAAA,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC;SACnD;aAAM;AACL,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;SACrB;KACF;;IAGD,aAAa,GAAA;AACX,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;KAC5B;AAED;;;;AAIG;IACH,qBAAqB,CAAC,SAA0B,EAAE,UAAuB,EAAA;;;AAGvE,QAAA,IAAI,UAAU,IAAI,UAAU,CAAC,MAAM,IAAI,IAAI,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YACtE,OAAO;SACR;;QAGD,IAAI,CAAC,aAAa,EAAE,CAAC;;AAGrB,QAAA,KAAK,CAAC,mBAAmB,EAAE,sBAAsB,CAAC;;AAE/C,aAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;aAC5D,SAAS,CAAC,MAAK;AACd,YAAA,MAAM,EAAC,iBAAiB,EAAE,QAAQ,EAAC,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;;YAGpE,IAAI,QAAQ,KAAK,CAAC,IAAI,QAAQ,IAAI,iBAAiB,EAAE;gBACnD,IAAI,CAAC,aAAa,EAAE,CAAC;aACtB;AACH,SAAC,CAAC,CAAC;KACN;AAED;;;;AAIG;AACK,IAAA,SAAS,CAAC,QAAgB,EAAA;AAChC,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,OAAO,EAAC,iBAAiB,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAC,CAAC;SAC5C;AAED,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;AACvD,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC,CAAC;;;AAI1E,QAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;QACnC,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAE/B,OAAO,EAAC,iBAAiB,EAAE,QAAQ,EAAE,IAAI,CAAC,eAAe,EAAC,CAAC;KAC5D;AA5jBmB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,sMAsFnB,qBAAqB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAtFvB,qBAAqB,EAAA,MAAA,EAAA,EAAA,iBAAA,EAAA,CAAA,mBAAA,EAAA,mBAAA,EAmDtB,gBAAgB,CAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAIhB,eAAe,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAvDd,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBAD1C,SAAS;;0BAoFL,QAAQ;;0BAGR,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;yCAlC3C,iBAAiB,EAAA,CAAA;sBADhB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAKhC,aAAa,EAAA,CAAA;sBADhB,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAA;gBAmBhB,kBAAkB,EAAA,CAAA;sBAApC,MAAM;gBAGY,YAAY,EAAA,CAAA;sBAA9B,MAAM;;;ACpHT;;;;;;AAMG;AAgBG,MAAO,YACX,SAAQ,qBAAqB,CAAA;AAe7B,IAAA,WAAA,CACE,UAAsB,EACtB,iBAAoC,EACpC,aAA4B,EAChB,GAAmB,EAC/B,MAAc,EACd,QAAkB,EACyB,aAAsB,EAAA;AAEjE,QAAA,KAAK,CAAC,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;;QAX5F,IAAa,CAAA,aAAA,GAAY,KAAK,CAAC;KAY9B;IAEQ,kBAAkB,GAAA;QACzB,IAAI,CAAC,OAAO,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1C,KAAK,CAAC,kBAAkB,EAAE,CAAC;KAC5B;AAES,IAAA,aAAa,CAAC,KAAoB,EAAA;QAC1C,KAAK,CAAC,cAAc,EAAE,CAAC;KACxB;AAnCU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,sMAuBD,qBAAqB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAvBhC,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAaJ,gBAAgB,CATlB,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,sDAAA,EAAA,yBAAA,EAAA,8BAAA,EAAA,gCAAA,EAAA,EAAA,cAAA,EAAA,oBAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,QAAA,EAAA,SAAA,EAAA,kBAAkB,ulBC/DrC,6yDA+CA,EAAA,MAAA,EAAA,CAAA,uvFAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EDUY,SAAS,EAAA,QAAA,EAAA,2BAAA,EAAA,MAAA,EAAA,CAAA,gBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,iBAAiB,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,CAAA,2BAAA,EAAA,UAAA,CAAA,EAAA,OAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAE3B,YAAY,EAAA,UAAA,EAAA,CAAA;kBAfxB,SAAS;+BACE,gBAAgB,EAAA,aAAA,EAGX,iBAAiB,CAAC,IAAI,mBAEpB,uBAAuB,CAAC,OAAO,EAC1C,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,oBAAoB;AAC7B,wBAAA,wDAAwD,EAAE,yBAAyB;AACnF,wBAAA,gCAAgC,EAAE,gCAAgC;AACnE,qBAAA,EAAA,UAAA,EACW,IAAI,EACP,OAAA,EAAA,CAAC,SAAS,EAAE,iBAAiB,CAAC,EAAA,QAAA,EAAA,6yDAAA,EAAA,MAAA,EAAA,CAAA,uvFAAA,CAAA,EAAA,CAAA;;0BAsBpC,QAAQ;;0BAGR,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;yCAnBgB,MAAM,EAAA,CAAA;sBAAhE,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,kBAAkB,EAAE,EAAC,WAAW,EAAE,KAAK,EAAC,CAAA;gBACV,iBAAiB,EAAA,CAAA;sBAA/D,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,kBAAkB,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC,CAAA;gBACP,QAAQ,EAAA,CAAA;sBAA7C,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,SAAS,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC,CAAA;gBACO,aAAa,EAAA,CAAA;sBAAvD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,cAAc,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC,CAAA;gBACb,cAAc,EAAA,CAAA;sBAAzC,SAAS;uBAAC,eAAe,CAAA;gBACM,kBAAkB,EAAA,CAAA;sBAAjD,SAAS;uBAAC,mBAAmB,CAAA;gBAK9B,aAAa,EAAA,CAAA;sBADZ,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;;;AE7BtC;MACa,eAAe,GAAG,IAAI,cAAc,CAAgB,iBAAiB;;AC5BlF;;;AAGG;AACU,MAAA,iBAAiB,GAE1B;;AAEF,IAAA,YAAY,EAAE,OAAO,CAAC,cAAc,EAAE;;QAEpC,KAAK,CAAC,uDAAuD,EAAE,KAAK,CAAC,EAAC,SAAS,EAAE,MAAM,EAAC,CAAC,CAAC;;;;;AAM1F,QAAA,KAAK,CACH,MAAM,EACN,KAAK,CAAC;AACJ,YAAA,SAAS,EAAE,0BAA0B;AACrC,YAAA,SAAS,EAAE,KAAK;;;AAIhB,YAAA,UAAU,EAAE,QAAQ;AACrB,SAAA,CAAC,CACH;AACD,QAAA,KAAK,CACH,OAAO,EACP,KAAK,CAAC;AACJ,YAAA,SAAS,EAAE,yBAAyB;AACpC,YAAA,SAAS,EAAE,KAAK;AAChB,YAAA,UAAU,EAAE,QAAQ;AACrB,SAAA,CAAC,CACH;AAED,QAAA,UAAU,CACR,wDAAwD,EACxD,OAAO,CAAC,sDAAsD,CAAC,CAChE;QACD,UAAU,CAAC,4BAA4B,EAAE;YACvC,KAAK,CAAC,EAAC,SAAS,EAAE,0BAA0B,EAAE,UAAU,EAAE,QAAQ,EAAC,CAAC;YACpE,OAAO,CAAC,sDAAsD,CAAC;SAChE,CAAC;QACF,UAAU,CAAC,6BAA6B,EAAE;YACxC,KAAK,CAAC,EAAC,SAAS,EAAE,yBAAyB,EAAE,UAAU,EAAE,QAAQ,EAAC,CAAC;YACnE,OAAO,CAAC,sDAAsD,CAAC;SAChE,CAAC;KACH,CAAC;;;AC5BJ;;;AAGG;AAKG,MAAO,gBAAiB,SAAQ,eAAe,CAAA;AAMnD,IAAA,WAAA,CACE,wBAAkD,EAClD,gBAAkC,EACY,KAAiB,EAC7C,SAAc,EAAA;AAEhC,QAAA,KAAK,CAAC,wBAAwB,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;QAHf,IAAK,CAAA,KAAA,GAAL,KAAK,CAAY;;AAPzD,QAAA,IAAA,CAAA,aAAa,GAAG,YAAY,CAAC,KAAK,CAAC;;AAEnC,QAAA,IAAA,CAAA,WAAW,GAAG,YAAY,CAAC,KAAK,CAAC;KASxC;;IAGQ,QAAQ,GAAA;QACf,KAAK,CAAC,QAAQ,EAAE,CAAC;AAEjB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB;AAC7C,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;AACnE,aAAA,SAAS,CAAC,CAAC,WAAoB,KAAI;YAClC,IAAI,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;gBACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;aAClC;AACH,SAAC,CAAC,CAAC;AAEL,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,SAAS,CAAC,MAAK;AAC/D,YAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE;gBAC/B,IAAI,CAAC,MAAM,EAAE,CAAC;aACf;AACH,SAAC,CAAC,CAAC;KACJ;;IAGQ,WAAW,GAAA;QAClB,KAAK,CAAC,WAAW,EAAE,CAAC;AACpB,QAAA,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;AACjC,QAAA,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;KAChC;8GAvCU,gBAAgB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,wBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EASjB,UAAU,CAAC,MAAM,UAAU,CAAC,EAAA,EAAA,EAAA,KAAA,EAC5B,QAAQ,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAVP,gBAAgB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAhB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAJ5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,kBAAkB;AAC5B,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BAUI,MAAM;AAAC,oBAAA,IAAA,EAAA,CAAA,UAAU,CAAC,MAAM,UAAU,CAAC,CAAA;;0BACnC,MAAM;2BAAC,QAAQ,CAAA;;AAiDpB;;;AAGG;MAeU,UAAU,CAAA;;IA2CrB,IACI,QAAQ,CAAC,QAAgB,EAAA;AAC3B,QAAA,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC;QAC/B,IAAI,CAAC,8BAA8B,EAAE,CAAC;KACvC;AAED,IAAA,WAAA,CACU,WAAoC,EACxB,IAAoB,EACxC,iBAAoC,EAAA;QAF5B,IAAW,CAAA,WAAA,GAAX,WAAW,CAAyB;QACxB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAgB;;AA9ClC,QAAA,IAAA,CAAA,sBAAsB,GAAG,YAAY,CAAC,KAAK,CAAC;;AAM3C,QAAA,IAAA,CAAA,qBAAqB,GAAG,IAAI,OAAO,EAAkB,CAAC;;AAG5C,QAAA,IAAA,CAAA,YAAY,GAAyB,IAAI,YAAY,EAAU,CAAC;;AAGhE,QAAA,IAAA,CAAA,gBAAgB,GAA0B,IAAI,YAAY,EAAW,CAAC;;AAGtE,QAAA,IAAA,CAAA,mBAAmB,GAAuB,IAAI,YAAY,EAAQ,CAAC;;AAGnE,QAAA,IAAA,CAAA,WAAW,GAAuB,IAAI,YAAY,CAAO,IAAI,CAAC,CAAC;;;;QAczE,IAAiB,CAAA,iBAAA,GAAW,OAAO,CAAC;;QAGpC,IAAe,CAAA,eAAA,GAAY,KAAK,CAAC;QAcxC,IAAI,IAAI,EAAE;AACR,YAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,GAAc,KAAI;AACrE,gBAAA,IAAI,CAAC,8BAA8B,CAAC,GAAG,CAAC,CAAC;gBACzC,iBAAiB,CAAC,YAAY,EAAE,CAAC;AACnC,aAAC,CAAC,CAAC;SACJ;;;AAID,QAAA,IAAI,CAAC,qBAAqB;aACvB,IAAI,CACH,oBAAoB,CAAC,CAAC,CAAC,EAAE,CAAC,KAAI;AAC5B,YAAA,OAAO,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,OAAO,CAAC;AAChE,SAAC,CAAC,CACH;aACA,SAAS,CAAC,KAAK,IAAG;;AAEjB,YAAA,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;AACnF,gBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;aACzB;AAED,YAAA,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;AACtF,gBAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,CAAC;aACjC;AACH,SAAC,CAAC,CAAC;KACN;AAED;;;AAGG;IACH,QAAQ,GAAA;AACN,QAAA,IAAI,IAAI,CAAC,SAAS,IAAI,QAAQ,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;YACrD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC/D;KACF;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,CAAC;AAC1C,QAAA,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,CAAC;KACvC;AAED,IAAA,sBAAsB,CAAC,KAAqB,EAAA;QAC1C,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AAC1D,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACxC,IAAI,WAAW,EAAE;AACf,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;SACrE;KACF;;IAGD,mBAAmB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;KAC/D;;AAGD,IAAA,iBAAiB,CAAC,QAA0C,EAAA;AAC1D,QAAA,QACE,QAAQ,IAAI,QAAQ,IAAI,QAAQ,IAAI,oBAAoB,IAAI,QAAQ,IAAI,qBAAqB,EAC7F;KACH;;AAGO,IAAA,8BAA8B,CAAC,GAAA,GAAiB,IAAI,CAAC,mBAAmB,EAAE,EAAA;AAChF,QAAA,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE;AAC3B,YAAA,IAAI,CAAC,SAAS,GAAG,GAAG,IAAI,KAAK,GAAG,MAAM,GAAG,OAAO,CAAC;SAClD;AAAM,aAAA,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE;AAClC,YAAA,IAAI,CAAC,SAAS,GAAG,GAAG,IAAI,KAAK,GAAG,OAAO,GAAG,MAAM,CAAC;SAClD;aAAM;AACL,YAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;SAC3B;KACF;AAED;;;AAGG;AACK,IAAA,0BAA0B,CAAC,MAAc,EAAA;AAC/C,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEvC,IAAI,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,IAAI,CAAC,MAAM,GAAG,IAAI,KAAK,IAAI,MAAM,GAAG,CAAC,CAAC,EAAE;AACjE,YAAA,OAAO,oBAAoB,CAAC;SAC7B;AAED,QAAA,OAAO,qBAAqB,CAAC;KAC9B;8GA3IU,UAAU,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,EAAA,CAAA,cAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAV,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAU,EA0BV,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,SAAA,EAAA,UAAA,CAAA,EAAA,MAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,kBAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,eAAe,ECnJ5B,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,uXAUA,EDkCa,MAAA,EAAA,CAAA,siBAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,gBAAgB,EA2EC,QAAA,EAAA,kBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,aAAa,EAL7B,QAAA,EAAA,mCAAA,EAAA,CAAA,EAAA,UAAA,EAAA,CAAC,iBAAiB,CAAC,YAAY,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAOjC,UAAU,EAAA,UAAA,EAAA,CAAA;kBAdtB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,cAAc,EAGT,aAAA,EAAA,iBAAiB,CAAC,IAAI,mBAEpB,uBAAuB,CAAC,OAAO,EAAA,UAAA,EACpC,CAAC,iBAAiB,CAAC,YAAY,CAAC,EACtC,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,kBAAkB;AAC5B,qBAAA,EAAA,UAAA,EACW,IAAI,EACP,OAAA,EAAA,CAAC,gBAAgB,EAAE,aAAa,CAAC,EAAA,QAAA,EAAA,uXAAA,EAAA,MAAA,EAAA,CAAA,siBAAA,CAAA,EAAA,CAAA;;0BAqDvC,QAAQ;yEArCQ,YAAY,EAAA,CAAA;sBAA9B,MAAM;gBAGY,gBAAgB,EAAA,CAAA;sBAAlC,MAAM;gBAGY,mBAAmB,EAAA,CAAA;sBAArC,MAAM;gBAGY,WAAW,EAAA,CAAA;sBAA7B,MAAM;gBAGqB,WAAW,EAAA,CAAA;sBAAtC,SAAS;uBAAC,eAAe,CAAA;gBAGR,QAAQ,EAAA,CAAA;sBAAzB,KAAK;uBAAC,SAAS,CAAA;gBAGP,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAKG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAGG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAIF,QAAQ,EAAA,CAAA;sBADX,KAAK;;;AE1HR;AACA,IAAI,MAAM,GAAG,CAAC,CAAC;AAYf;AACA,MAAM,uBAAuB,GAAG,IAAI,CAAC;AAErC;;;;AAIG;MAiCU,WAAW,CAAA;;AAgCtB,IAAA,IACI,kBAAkB,GAAA;QACpB,OAAO,IAAI,CAAC,mBAAmB,CAAC;KACjC;IACD,IAAI,kBAAkB,CAAC,KAAc,EAAA;AACnC,QAAA,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;AACjC,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;KACxC;;AAYD,IAAA,IACI,aAAa,GAAA;QACf,OAAO,IAAI,CAAC,cAAc,CAAC;KAC5B;IACD,IAAI,aAAa,CAAC,KAAa,EAAA;AAC7B,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC;KACnD;;AAOD,IAAA,IACI,iBAAiB,GAAA;QACnB,OAAO,IAAI,CAAC,kBAAkB,CAAC;KAChC;IACD,IAAI,iBAAiB,CAAC,KAAsB,EAAA;AAC1C,QAAA,MAAM,WAAW,GAAG,KAAK,GAAG,EAAE,CAAC;AAC/B,QAAA,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,KAAK,GAAG,IAAI,GAAG,WAAW,CAAC;KAClF;AAGD;;;;;AAKG;AACH,IAAA,IACI,eAAe,GAAA;QACjB,OAAO,IAAI,CAAC,gBAAgB,CAAC;KAC9B;IAED,IAAI,eAAe,CAAC,KAAa,EAAA;AAC/B,QAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC;KACrD;AAuBD;;;;AAIG;AACH,IAAA,IACI,eAAe,GAAA;QACjB,OAAO,IAAI,CAAC,gBAAgB,CAAC;KAC9B;IAED,IAAI,eAAe,CAAC,KAAmB,EAAA;QACrC,IAAI,CAAC,uBAAuB,EAAE;AAC5B,YAAA,MAAM,IAAI,KAAK,CAAC,CAAA,uEAAA,CAAyE,CAAC,CAAC;SAC5F;QAED,MAAM,SAAS,GAAiB,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC;QAEzE,SAAS,CAAC,MAAM,CAAC,0BAA0B,EAAE,CAAkB,eAAA,EAAA,IAAI,CAAC,eAAe,CAAE,CAAA,CAAC,CAAC;QAEvF,IAAI,KAAK,EAAE;YACT,SAAS,CAAC,GAAG,CAAC,0BAA0B,EAAE,CAAkB,eAAA,EAAA,KAAK,CAAE,CAAA,CAAC,CAAC;SACtE;AAED,QAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;KAC/B;AAuBD,IAAA,WAAA,CACW,WAAuB,EACxB,kBAAqC,EACR,aAA6B,EAChB,cAAuB,EAAA;QAHhE,IAAW,CAAA,WAAA,GAAX,WAAW,CAAY;QACxB,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAmB;QAEK,IAAc,CAAA,cAAA,GAAd,cAAc,CAAS;;AAvJ3E,QAAA,IAAA,CAAA,KAAK,GAAsB,IAAI,SAAS,EAAU,CAAC;;QAG3C,IAAc,CAAA,cAAA,GAAkB,CAAC,CAAC;;QAGlC,IAAoB,CAAA,oBAAA,GAAkB,IAAI,CAAC;;QAG3C,IAAqB,CAAA,qBAAA,GAAW,CAAC,CAAC;;AAGlC,QAAA,IAAA,CAAA,iBAAiB,GAAG,YAAY,CAAC,KAAK,CAAC;;AAGvC,QAAA,IAAA,CAAA,qBAAqB,GAAG,YAAY,CAAC,KAAK,CAAC;QAe3C,IAAmB,CAAA,mBAAA,GAAG,KAAK,CAAC;;QAIpC,IAAW,CAAA,WAAA,GAAY,IAAI,CAAC;;QAI5B,IAAa,CAAA,aAAA,GAAY,KAAK,CAAC;QAUvB,IAAc,CAAA,cAAA,GAAkB,IAAI,CAAC;;QAGpC,IAAc,CAAA,cAAA,GAAyB,OAAO,CAAC;AA8BxD;;;AAGG;QAEH,IAAiB,CAAA,iBAAA,GAAY,KAAK,CAAC;;QAInC,IAAa,CAAA,aAAA,GAAY,KAAK,CAAC;AAE/B;;;;AAIG;QAEH,IAAe,CAAA,eAAA,GAAY,KAAK,CAAC;;AA+Bd,QAAA,IAAA,CAAA,mBAAmB,GAAyB,IAAI,YAAY,EAAU,CAAC;;AAGvE,QAAA,IAAA,CAAA,WAAW,GAC5B,IAAI,YAAY,EAAqB,CAAC;;AAGrB,QAAA,IAAA,CAAA,aAAa,GAAuB,IAAI,YAAY,EAAQ,CAAC;;AAG7D,QAAA,IAAA,CAAA,iBAAiB,GAClC,IAAI,YAAY,CAAoB,IAAI,CAAC,CAAC;;QAKlC,IAAS,CAAA,SAAA,GAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC;AAQzD,QAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,EAAE,CAAC;AACzB,QAAA,IAAI,CAAC,iBAAiB;AACpB,YAAA,aAAa,IAAI,aAAa,CAAC,iBAAiB,GAAG,aAAa,CAAC,iBAAiB,GAAG,OAAO,CAAC;AAC/F,QAAA,IAAI,CAAC,iBAAiB;AACpB,YAAA,aAAa,IAAI,aAAa,CAAC,iBAAiB,IAAI,IAAI;kBACpD,aAAa,CAAC,iBAAiB;kBAC/B,KAAK,CAAC;AACZ,QAAA,IAAI,CAAC,aAAa;AAChB,YAAA,aAAa,IAAI,aAAa,CAAC,aAAa,IAAI,IAAI,GAAG,aAAa,CAAC,aAAa,GAAG,KAAK,CAAC;AAC7F,QAAA,IAAI,aAAa,EAAE,eAAe,IAAI,IAAI,EAAE;AAC1C,YAAA,IAAI,CAAC,eAAe,GAAG,aAAa,CAAC,eAAe,CAAC;SACtD;QACD,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,aAAa,EAAE,eAAe,CAAC;AACxD,QAAA,IAAI,CAAC,kBAAkB;AACrB,YAAA,aAAa,IAAI,aAAa,CAAC,kBAAkB,IAAI,IAAI;kBACrD,aAAa,CAAC,kBAAkB;kBAChC,KAAK,CAAC;AACZ,QAAA,IAAI,CAAC,WAAW;AACd,YAAA,aAAa,IAAI,aAAa,CAAC,WAAW,IAAI,IAAI,GAAG,aAAa,CAAC,WAAW,GAAG,IAAI,CAAC;KACzF;AAED;;;;;AAKG;IACH,qBAAqB,GAAA;;;AAGnB,QAAA,MAAM,aAAa,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;;;AAIvF,QAAA,IAAI,IAAI,CAAC,cAAc,IAAI,aAAa,EAAE;AACxC,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC;YAE/C,IAAI,CAAC,UAAU,EAAE;AACf,gBAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC,CAAC;;;AAGpE,gBAAA,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC;gBACnD,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;aACvD;;;AAID,YAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;gBAC1B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,MAAM,GAAG,CAAC,QAAQ,GAAG,KAAK,KAAK,aAAa,CAAC,CAAC,CAAC;gBAE7E,IAAI,CAAC,UAAU,EAAE;AACf,oBAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;;;oBAG7C,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;iBACzD;AACH,aAAC,CAAC,CAAC;SACJ;;QAGD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAW,EAAE,KAAa,KAAI;AAChD,YAAA,GAAG,CAAC,QAAQ,GAAG,KAAK,GAAG,aAAa,CAAC;;;AAIrC,YAAA,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,IAAI,GAAG,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;gBACnE,GAAG,CAAC,MAAM,GAAG,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;aAClD;AACH,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,aAAa,EAAE;AACzC,YAAA,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;AACpC,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AACjC,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;SACxC;KACF;IAED,kBAAkB,GAAA;QAChB,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,IAAI,CAAC,qBAAqB,EAAE,CAAC;;;AAI7B,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,MAAK;YACzD,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;;;AAI/D,YAAA,IAAI,aAAa,KAAK,IAAI,CAAC,cAAc,EAAE;gBACzC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;AAClC,gBAAA,IAAI,WAA+B,CAAC;AAEpC,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACpC,oBAAA,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;;;;wBAIpB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;AAC9C,wBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AACjC,wBAAA,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;wBACtB,MAAM;qBACP;iBACF;;;;gBAKD,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,aAAa,CAAC,EAAE;AACvC,oBAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;AAC1B,wBAAA,IAAI,CAAC,aAAa,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC;AACpC,wBAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC,CAAC;AACtE,qBAAC,CAAC,CAAC;iBACJ;aACF;AAED,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACzC,SAAC,CAAC,CAAC;KACJ;;IAGO,yBAAyB,GAAA;;;;QAI/B,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAuB,KAAI;YACzF,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,IAAI,CAAC,MAAM,CAAC,GAAG,IAAG;gBAChB,OAAO,GAAG,CAAC,gBAAgB,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC;aAC/D,CAAC,CACH,CAAC;AACF,YAAA,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;AAC/B,SAAC,CAAC,CAAC;KACJ;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;AACrB,QAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,CAAC;AACrC,QAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,CAAC;KAC1C;;IAGD,aAAa,GAAA;AACX,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,IAAI,CAAC,UAAU,CAAC,yBAAyB,EAAE,CAAC;SAC7C;KACF;AAED;;;;;;AAMG;IACH,gBAAgB,GAAA;AACd,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;SACpC;KACF;AAED;;;AAGG;AACH,IAAA,QAAQ,CAAC,KAAa,EAAA;AACpB,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;QAE/B,IAAI,MAAM,EAAE;AACV,YAAA,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC;SAC3B;KACF;AAED,IAAA,aAAa,CAAC,KAAa,EAAA;AACzB,QAAA,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;AAClC,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;KACvD;AAEO,IAAA,kBAAkB,CAAC,KAAa,EAAA;AACtC,QAAA,MAAM,KAAK,GAAG,IAAI,iBAAiB,EAAE,CAAC;AACtC,QAAA,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;QACpB,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;AACnC,YAAA,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC;SACzC;AACD,QAAA,OAAO,KAAK,CAAC;KACd;AAED;;;;;AAKG;IACK,qBAAqB,GAAA;AAC3B,QAAA,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAC9B,YAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,CAAC;SAC1C;AAED,QAAA,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,MACxF,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CACvC,CAAC;KACH;;AAGO,IAAA,cAAc,CAAC,KAAoB,EAAA;;;;QAIzC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KACjE;;AAGD,IAAA,cAAc,CAAC,CAAS,EAAA;AACtB,QAAA,OAAO,iBAAiB,IAAI,CAAC,QAAQ,CAAI,CAAA,EAAA,CAAC,EAAE,CAAC;KAC9C;;AAGD,IAAA,gBAAgB,CAAC,CAAS,EAAA;AACxB,QAAA,OAAO,mBAAmB,IAAI,CAAC,QAAQ,CAAI,CAAA,EAAA,CAAC,EAAE,CAAC;KAChD;AAED;;;AAGG;AACH,IAAA,wBAAwB,CAAC,SAAiB,EAAA;QACxC,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;YACtD,OAAO;SACR;AAED,QAAA,MAAM,OAAO,GAAgB,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC;QAEhE,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;;;QAIzD,IAAI,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,YAAY,EAAE;YACnD,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,GAAG,IAAI,CAAC;SACzC;KACF;;IAGD,2BAA2B,GAAA;AACzB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC;AACnD,QAAA,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,YAAY,CAAC;AAClD,QAAA,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC;AAC1B,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;KAC3B;;AAGD,IAAA,YAAY,CAAC,GAAW,EAAE,SAAgC,EAAE,KAAa,EAAA;AACvE,QAAA,SAAS,CAAC,UAAU,GAAG,KAAK,CAAC;AAE7B,QAAA,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE;AACjB,YAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;SAC5B;KACF;;AAGD,IAAA,YAAY,CAAC,KAAa,EAAA;QACxB,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,aAAa,CAAC;AACpE,QAAA,OAAO,KAAK,KAAK,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;KACvC;;IAGD,gBAAgB,CAAC,WAAwB,EAAE,KAAa,EAAA;;;;;QAKtD,IAAI,WAAW,IAAI,WAAW,KAAK,OAAO,IAAI,WAAW,KAAK,OAAO,EAAE;AACrE,YAAA,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,KAAK,CAAC;SACpC;KACF;8GAnbU,WAAW,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAgKZ,eAAe,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EACH,qBAAqB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAjKhC,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,WAAW,4IAgCH,gBAAgB,CAAA,EAAA,WAAA,EAAA,CAAA,kBAAA,EAAA,aAAA,EAWW,gBAAgB,CAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAI3C,gBAAgB,CAIhB,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAAA,eAAe,CA6Bf,EAAA,cAAA,EAAA,gBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,EAAA,eAAe,iEAef,gBAAgB,CAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAIhB,gBAAgB,CAAA,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,EAQhB,gBAAgB,CAnIxB,EAAA,eAAA,EAAA,iBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,WAAA,EAAA,aAAA,EAAA,aAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,mCAAA,EAAA,wCAAA,EAAA,eAAA,EAAA,yCAAA,EAAA,8BAAA,EAAA,sCAAA,EAAA,aAAA,EAAA,oCAAA,EAAA,mBAAA,EAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,aAAa;AACtB,gBAAA,WAAW,EAAE,WAAW;AACzB,aAAA;AACF,SAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,UAAA,EAAA,SAAA,EAwBgB,MAAM,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,YAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,aAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECpGzB,6hHAqFA,EAAA,MAAA,EAAA,CAAA,m5QAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EDEI,YAAY,EACZ,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,CAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,kBAAkB,EAClB,QAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,CAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,eAAe,EACf,QAAA,EAAA,oDAAA,EAAA,OAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,SAAS,EACT,QAAA,EAAA,2BAAA,EAAA,MAAA,EAAA,CAAA,gBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,eAAe,iJACf,UAAU,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,UAAA,CAAA,EAAA,OAAA,EAAA,CAAA,cAAA,EAAA,kBAAA,EAAA,qBAAA,EAAA,aAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAGD,WAAW,EAAA,UAAA,EAAA,CAAA;kBAhCvB,SAAS;+BACE,eAAe,EAAA,QAAA,EACf,aAAa,EAAA,aAAA,EAGR,iBAAiB,CAAC,IAAI,EAEpB,eAAA,EAAA,uBAAuB,CAAC,OAAO,EACrC,SAAA,EAAA;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,aAAa;AACtB,4BAAA,WAAW,EAAa,WAAA;AACzB,yBAAA;qBACF,EACK,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,mBAAmB;AAC5B,wBAAA,SAAS,EAAE,+BAA+B;AAC1C,wBAAA,0CAA0C,EAAE,eAAe;AAC3D,wBAAA,2CAA2C,EAAE,4BAA4B;AACzE,wBAAA,wCAAwC,EAAE,aAAa;AACvD,wBAAA,sCAAsC,EAAE,mBAAmB;AAC5D,qBAAA,EAAA,UAAA,EACW,IAAI,EACP,OAAA,EAAA;wBACP,YAAY;wBACZ,kBAAkB;wBAClB,eAAe;wBACf,SAAS;wBACT,eAAe;wBACf,UAAU;AACX,qBAAA,EAAA,QAAA,EAAA,6hHAAA,EAAA,MAAA,EAAA,CAAA,m5QAAA,CAAA,EAAA,CAAA;;0BAkKE,MAAM;2BAAC,eAAe,CAAA;;0BAAG,QAAQ;;0BACjC,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;yCA5JG,QAAQ,EAAA,CAAA;sBAArD,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,MAAM,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC,CAAA;gBACf,eAAe,EAAA,CAAA;sBAA3C,SAAS;uBAAC,gBAAgB,CAAA;gBACH,UAAU,EAAA,CAAA;sBAAjC,SAAS;uBAAC,WAAW,CAAA;gBAsBtB,KAAK,EAAA,CAAA;sBADJ,KAAK;gBAKF,kBAAkB,EAAA,CAAA;sBADrB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAYpC,WAAW,EAAA,CAAA;sBADV,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,kBAAkB,EAAE,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAK/D,aAAa,EAAA,CAAA;sBADZ,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAKhC,aAAa,EAAA,CAAA;sBADhB,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAA;gBAU1B,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAIF,iBAAiB,EAAA,CAAA;sBADpB,KAAK;gBAiBF,eAAe,EAAA,CAAA;sBADlB,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAA;gBAgBnC,iBAAiB,EAAA,CAAA;sBADhB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAKpC,aAAa,EAAA,CAAA;sBADZ,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBASpC,eAAe,EAAA,CAAA;sBADd,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAShC,eAAe,EAAA,CAAA;sBADlB,KAAK;gBAwBa,mBAAmB,EAAA,CAAA;sBAArC,MAAM;gBAGY,WAAW,EAAA,CAAA;sBAA7B,MAAM;gBAIY,aAAa,EAAA,CAAA;sBAA/B,MAAM;gBAGY,iBAAiB,EAAA,CAAA;sBAAnC,MAAM;;AAiST;MACa,iBAAiB,CAAA;AAK7B;;AEzeD;AACA,IAAI,YAAY,GAAG,CAAC,CAAC;AAErB;;;AAGG;AAwBG,MAAO,SACX,SAAQ,qBAAqB,CAAA;;AAI7B,IAAA,IACI,kBAAkB,GAAA;AACpB,QAAA,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC;KACvC;IACD,IAAI,kBAAkB,CAAC,KAAc,EAAA;AACnC,QAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACrC,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;KACxC;AAOD,IAAA,IACI,iBAAiB,GAAA;QACnB,OAAO,IAAI,CAAC,kBAAkB,CAAC;KAChC;IAED,IAAI,iBAAiB,CAAC,KAAsB,EAAA;AAC1C,QAAA,MAAM,WAAW,GAAG,KAAK,GAAG,EAAE,CAAC;AAC/B,QAAA,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,KAAK,GAAG,IAAI,GAAG,WAAW,CAAC;KAClF;;AAQD,IAAA,IACI,eAAe,GAAA;QACjB,OAAO,IAAI,CAAC,gBAAgB,CAAC;KAC9B;IAED,IAAI,eAAe,CAAC,KAAmB,EAAA;QACrC,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC;QAC3D,SAAS,CAAC,MAAM,CAAC,0BAA0B,EAAE,CAAkB,eAAA,EAAA,IAAI,CAAC,eAAe,CAAE,CAAA,CAAC,CAAC;QAEvF,IAAI,KAAK,EAAE;YACT,SAAS,CAAC,GAAG,CAAC,0BAA0B,EAAE,CAAkB,eAAA,EAAA,KAAK,CAAE,CAAA,CAAC,CAAC;SACtE;AAED,QAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;KAC/B;AAyBD,IAAA,WAAA,CACE,UAAsB,EACV,GAAmB,EAC/B,MAAc,EACd,iBAAoC,EACpC,aAA4B,EAC5B,QAAkB,EACyB,aAAsB,EAC5B,aAA6B,EAAA;AAElE,QAAA,KAAK,CAAC,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;AAvE5F,QAAA,IAAA,CAAA,mBAAmB,GAAG,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC;;QAIjD,IAAW,CAAA,WAAA,GAAY,IAAI,CAAC;;QAsC5B,IAAa,CAAA,aAAA,GAAY,KAAK,CAAC;;QAGtB,IAAK,CAAA,KAAA,GAAiB,SAAS,CAAC;AA2BvC,QAAA,IAAI,CAAC,iBAAiB;AACpB,YAAA,aAAa,IAAI,aAAa,CAAC,iBAAiB,IAAI,IAAI;kBACpD,aAAa,CAAC,iBAAiB;kBAC/B,KAAK,CAAC;AACZ,QAAA,IAAI,CAAC,kBAAkB;AACrB,YAAA,aAAa,IAAI,aAAa,CAAC,kBAAkB,IAAI,IAAI;kBACrD,aAAa,CAAC,kBAAkB;kBAChC,KAAK,CAAC;AACZ,QAAA,IAAI,CAAC,WAAW;AACd,YAAA,aAAa,IAAI,aAAa,CAAC,WAAW,IAAI,IAAI,GAAG,aAAa,CAAC,WAAW,GAAG,IAAI,CAAC;KACzF;IAES,aAAa,GAAA;;KAEtB;IAEQ,kBAAkB,GAAA;QACzB,IAAI,CAAC,OAAO,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;;;QAG1C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;YACnF,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAC1B,SAAC,CAAC,CAAC;QAEH,KAAK,CAAC,kBAAkB,EAAE,CAAC;KAC5B;IAEQ,eAAe,GAAA;AACtB,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AACrE,YAAA,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;SAC1E;QACD,KAAK,CAAC,eAAe,EAAE,CAAC;KACzB;;IAGD,gBAAgB,GAAA;AACd,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,OAAO;SACR;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;AAEpC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACrC,YAAA,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;AACnB,gBAAA,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;AACvB,gBAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AAEvC,gBAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;oBACjB,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;iBAC1C;gBAED,OAAO;aACR;SACF;;AAGD,QAAA,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;AACxB,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;KACrB;IAED,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,QAAQ,GAAG,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;KACxF;8GAnJU,SAAS,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAAA,EAAA,CAAA,cAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAiFE,qBAAqB,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EACrB,eAAe,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAlF1B,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAS,EAKD,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,EAAA,kBAAA,EAAA,CAAA,oBAAA,EAAA,oBAAA,EAAA,gBAAgB,CAWW,EAAA,WAAA,EAAA,CAAA,kBAAA,EAAA,aAAA,EAAA,gBAAgB,CAsC3C,EAAA,iBAAA,EAAA,mBAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAAA,gBAAgB,CAtBD,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,WAAA,EAAA,YAAA,EAAA,sDAAA,EAAA,yBAAA,EAAA,8BAAA,EAAA,gCAAA,EAAA,wCAAA,EAAA,aAAA,EAAA,mBAAA,EAAA,4CAAA,EAAA,kBAAA,EAAA,sBAAA,EAAA,gBAAA,EAAA,oBAAA,EAAA,+BAAA,EAAA,uCAAA,EAAA,oCAAA,EAAA,mBAAA,EAAA,EAAA,cAAA,EAAA,wCAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,UAAU,CChH9C,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,mBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,cAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,eAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,oBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,cAAA,EAAA,WAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,krDAuCA,EDuCY,MAAA,EAAA,CAAA,qxNAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,SAAS,wPAAE,iBAAiB,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,CAAA,2BAAA,EAAA,UAAA,CAAA,EAAA,OAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAE3B,SAAS,EAAA,UAAA,EAAA,CAAA;kBAvBrB,SAAS;+BACE,mBAAmB,EAAA,QAAA,EACnB,yBAAyB,EAG7B,IAAA,EAAA;AACJ,wBAAA,aAAa,EAAE,YAAY;AAC3B,wBAAA,OAAO,EAAE,wCAAwC;AACjD,wBAAA,wDAAwD,EAAE,yBAAyB;AACnF,wBAAA,gCAAgC,EAAE,gCAAgC;AAClE,wBAAA,0CAA0C,EAAE,aAAa;AACzD,wBAAA,qBAAqB,EAAE,wCAAwC;AAC/D,wBAAA,oBAAoB,EAAE,oBAAoB;AAC1C,wBAAA,kBAAkB,EAAE,kBAAkB;AACtC,wBAAA,iCAAiC,EAAE,qCAAqC;AACxE,wBAAA,sCAAsC,EAAE,mBAAmB;AAC5D,qBAAA,EAAA,aAAA,EACc,iBAAiB,CAAC,IAAI,EAAA,eAAA,EAEpB,uBAAuB,CAAC,OAAO,EACpC,UAAA,EAAA,IAAI,EACP,OAAA,EAAA,CAAC,SAAS,EAAE,iBAAiB,CAAC,EAAA,QAAA,EAAA,krDAAA,EAAA,MAAA,EAAA,CAAA,qxNAAA,CAAA,EAAA,CAAA;;0BA8EpC,QAAQ;;0BAKR,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;;0BACxC,QAAQ;;0BAAI,MAAM;2BAAC,eAAe,CAAA;yCA5EjC,kBAAkB,EAAA,CAAA;sBADrB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAYpC,WAAW,EAAA,CAAA;sBADV,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,kBAAkB,EAAE,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAI3D,iBAAiB,EAAA,CAAA;sBADpB,KAAK;gBAa8D,MAAM,EAAA,CAAA;sBAAzE,eAAe;uBAAC,UAAU,CAAC,MAAM,UAAU,CAAC,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC,CAAA;gBAI9D,eAAe,EAAA,CAAA;sBADlB,KAAK;gBAoBN,aAAa,EAAA,CAAA;sBADZ,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAI3B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAOG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAEyC,iBAAiB,EAAA,CAAA;sBAA/D,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,kBAAkB,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC,CAAA;gBACP,QAAQ,EAAA,CAAA;sBAA7C,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,SAAS,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC,CAAA;gBACO,aAAa,EAAA,CAAA;sBAAvD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,cAAc,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC,CAAA;gBACb,cAAc,EAAA,CAAA;sBAAzC,SAAS;uBAAC,eAAe,CAAA;gBACM,kBAAkB,EAAA,CAAA;sBAAjD,SAAS;uBAAC,mBAAmB,CAAA;;AA+EhC;;AAEG;AAyBG,MAAO,UACX,SAAQ,UAAU,CAAA;;AASlB,IAAA,IACI,MAAM,GAAA;QACR,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IAED,IAAI,MAAM,CAAC,KAAc,EAAA;AACvB,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,EAAE;AAC5B,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACvB,YAAA,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;SACpC;KACF;AAuBD;;;AAGG;AACH,IAAA,IAAI,cAAc,GAAA;QAChB,QACE,IAAI,CAAC,QAAQ;AACb,YAAA,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,UAAU,CAAC,aAAa;AAC7B,YAAA,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,EAC5B;KACH;AAKD,IAAA,WAAA,CACU,UAAqB;yBACD,UAAsB,EACH,mBAA+C,EACvE,QAAgB,EAC/B,aAA2B,EACQ,aAAsB,EAAA;AAEjE,QAAA,KAAK,EAAE,CAAC;QAPA,IAAU,CAAA,UAAA,GAAV,UAAU,CAAW;QACD,IAAU,CAAA,UAAA,GAAV,UAAU,CAAY;QAG1C,IAAa,CAAA,aAAA,GAAb,aAAa,CAAc;AA5DpB,QAAA,IAAA,CAAA,UAAU,GAAG,IAAI,OAAO,EAAQ,CAAC;;QAGxC,IAAS,CAAA,SAAA,GAAY,KAAK,CAAC;;QAiBrC,IAAQ,CAAA,QAAA,GAAY,KAAK,CAAC;;QAI1B,IAAa,CAAA,aAAA,GAAY,KAAK,CAAC;QAK/B,IAAQ,CAAA,QAAA,GAAW,CAAC,CAAC;;AAwBZ,QAAA,IAAA,CAAA,EAAE,GAAG,CAAA,aAAA,EAAgB,YAAY,EAAE,EAAE,CAAC;AAY7C,QAAA,IAAI,CAAC,YAAY,GAAG,mBAAmB,IAAI,EAAE,CAAC;QAC9C,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAExC,QAAA,IAAI,aAAa,KAAK,gBAAgB,EAAE;AACtC,YAAA,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,EAAC,aAAa,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAC,CAAC;SACnE;AAED,QAAA,UAAU,CAAC,mBAAmB;AAC3B,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAChC,SAAS,CAAC,kBAAkB,IAAG;AAC9B,YAAA,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;AAC/C,SAAC,CAAC,CAAC;KACN;;IAGD,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;KACvC;IAED,eAAe,GAAA;QACb,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;KAC7C;IAEQ,WAAW,GAAA;AAClB,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;QAC3B,KAAK,CAAC,WAAW,EAAE,CAAC;QACpB,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;KACpD;IAED,YAAY,GAAA;;;AAGV,QAAA,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;KAC7E;AAED,IAAA,cAAc,CAAC,KAAoB,EAAA;AACjC,QAAA,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,EAAE;AACtD,YAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,KAAK,CAAC,cAAc,EAAE,CAAC;aACxB;AAAM,iBAAA,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;;;AAGnC,gBAAA,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,EAAE;oBAC3B,KAAK,CAAC,cAAc,EAAE,CAAC;iBACxB;AAED,gBAAA,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;aACvC;SACF;KACF;IAED,gBAAgB,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ;AAC7B,cAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,EAAE;cAC5B,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;KACjE;IAED,gBAAgB,GAAA;AACd,QAAA,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;YAC5B,OAAO,IAAI,CAAC,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC;SACvC;aAAM;YACL,OAAO,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;SACpE;KACF;IAED,eAAe,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,MAAM,GAAG,IAAI,CAAC;KACjE;IAED,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;KAC9F;IAED,YAAY,GAAA;AACV,QAAA,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;AAC5B,YAAA,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;SAClD;aAAM;AACL,YAAA,OAAO,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;SAC3C;KACF;AArJU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAU,EA8DC,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,yBAAyB,EAClC,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,UAAU,0DAED,qBAAqB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAjEhC,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAU,EAUF,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,8BAAA,EAAA,MAAA,EAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAAA,gBAAgB,CAahB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAIhB,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAAA,gBAAgB,CAItB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,CAAC,KAAc,MAAM,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,CEhS/E,EAAA,EAAA,EAAA,IAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,wBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,oBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,mBAAA,EAAA,oBAAA,EAAA,UAAA,EAAA,oBAAA,EAAA,oBAAA,EAAA,SAAA,EAAA,IAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,WAAA,EAAA,YAAA,EAAA,4BAAA,EAAA,UAAA,EAAA,uBAAA,EAAA,QAAA,EAAA,EAAA,cAAA,EAAA,kDAAA,EAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,uUAcA,ghIFiPY,SAAS,EAAA,QAAA,EAAA,2BAAA,EAAA,MAAA,EAAA,CAAA,gBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAER,UAAU,EAAA,UAAA,EAAA,CAAA;kBAxBtB,SAAS;+BACE,8BAA8B,EAAA,QAAA,EAC9B,YAAY,EAAA,eAAA,EACL,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAG/B,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,kDAAkD;AAC3D,wBAAA,sBAAsB,EAAE,oBAAoB;AAC5C,wBAAA,qBAAqB,EAAE,mBAAmB;AAC1C,wBAAA,sBAAsB,EAAE,UAAU;AAClC,wBAAA,sBAAsB,EAAE,oBAAoB;AAC5C,wBAAA,WAAW,EAAE,IAAI;AACjB,wBAAA,iBAAiB,EAAE,gBAAgB;AACnC,wBAAA,aAAa,EAAE,YAAY;AAC3B,wBAAA,8BAA8B,EAAE,UAAU;AAC1C,wBAAA,yBAAyB,EAAE,QAAQ;AACnC,wBAAA,SAAS,EAAE,gBAAgB;AAC3B,wBAAA,WAAW,EAAE,wBAAwB;AACtC,qBAAA,EAAA,UAAA,EACW,IAAI,EAAA,OAAA,EACP,CAAC,SAAS,CAAC,EAAA,QAAA,EAAA,uUAAA,EAAA,MAAA,EAAA,CAAA,w9HAAA,CAAA,EAAA,CAAA;;0BAgEjB,QAAQ;;0BAAI,MAAM;2BAAC,yBAAyB,CAAA;;0BAC5C,SAAS;2BAAC,UAAU,CAAA;;0BAEpB,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;yCAtDvC,MAAM,EAAA,CAAA;sBADT,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAcpC,QAAQ,EAAA,CAAA;sBADP,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAKpC,aAAa,EAAA,CAAA;sBADZ,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAMpC,QAAQ,EAAA,CAAA;sBAHP,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA;wBACL,SAAS,EAAE,CAAC,KAAc,MAAM,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;AAC5E,qBAAA,CAAA;gBAyBQ,EAAE,EAAA,CAAA;sBAAV,KAAK;;AA+FR;;AAEG;MAeU,cAAc,CAAA;AAd3B,IAAA,WAAA,GAAA;;AAgBW,QAAA,IAAA,CAAA,EAAE,GAAG,CAAA,kBAAA,EAAqB,YAAY,EAAE,EAAE,CAAC;AAIrD,KAAA;8GANY,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAd,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,cAAc,iSAXf,2BAA2B,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAW1B,cAAc,EAAA,UAAA,EAAA,CAAA;kBAd1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,mBAAmB;AAC7B,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,QAAQ,EAAE,2BAA2B;AACrC,oBAAA,IAAI,EAAE;AACJ,wBAAA,wBAAwB,EAAE,cAAc;AACxC,wBAAA,WAAW,EAAE,IAAI;AACjB,wBAAA,OAAO,EAAE,uBAAuB;AAChC,wBAAA,MAAM,EAAE,UAAU;AACnB,qBAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;8BAGU,EAAE,EAAA,CAAA;sBAAV,KAAK;;;MGtYK,aAAa,CAAA;8GAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAb,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,YApBtB,eAAe;YACf,aAAa;YACb,WAAW;YACX,MAAM;YACN,WAAW;YACX,SAAS;YACT,cAAc;AACd,YAAA,UAAU,aAGV,eAAe;YACf,aAAa;YACb,WAAW;YACX,MAAM;YACN,WAAW;YACX,SAAS;YACT,cAAc;YACd,UAAU,CAAA,EAAA,CAAA,CAAA,EAAA;+GAGD,aAAa,EAAA,OAAA,EAAA,CApBtB,eAAe,EAUf,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAUN,aAAa,EAAA,UAAA,EAAA,CAAA;kBAtBzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE;wBACP,eAAe;wBACf,aAAa;wBACb,WAAW;wBACX,MAAM;wBACN,WAAW;wBACX,SAAS;wBACT,cAAc;wBACd,UAAU;AACX,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,eAAe;wBACf,aAAa;wBACb,WAAW;wBACX,MAAM;wBACN,WAAW;wBACX,SAAS;wBACT,cAAc;wBACd,UAAU;AACX,qBAAA;AACF,iBAAA,CAAA;;;ACrCD;;AAEG;;;;"}