"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getChildElementIndentation = void 0;
const schematics_1 = require("@angular-devkit/schematics");
/** Determines the indentation of child elements for the given Parse5 element. */
function getChildElementIndentation(element) {
    const childElement = element.childNodes.find(node => node.tagName);
    if ((childElement && !childElement.sourceCodeLocation) || !element.sourceCodeLocation) {
        throw new schematics_1.SchematicsException('Cannot determine child element indentation because the ' +
            'specified Parse5 element does not have any source code location metadata.');
    }
    const startColumns = childElement
        ? // In case there are child elements inside of the element, we assume that their
            // indentation is also applicable for other child elements.
            childElement.sourceCodeLocation.startCol
        : // In case there is no child element, we just assume that child elements should be indented
            // by two spaces.
            element.sourceCodeLocation.startCol + 2;
    // Since Parse5 does not set the `startCol` properties as zero-based, we need to subtract
    // one column in order to have a proper zero-based offset for the indentation.
    return startColumns - 1;
}
exports.getChildElementIndentation = getChildElementIndentation;
//# sourceMappingURL=data:application/json;base64,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