{"version": 3, "sources": ["../../../../../../packages/compiler-cli/src/main.ts", "../../../../../../packages/compiler-cli/src/perform_watch.ts"], "mappings": ";;;;;;;;;;;;;;;;;AAQA,OAAOA,SAAQ;AACf,OAAO,WAAW;;;ACDlB,YAAY,cAAc;AAC1B,YAAY,UAAU;AACtB,OAAO,QAAQ;AAOf,SAAS,+BAA+B,cAAoB;AAC1D,MAAI;AACJ,MAAI,eAAe,KAAM;AACvB,eAAW,IAAI,eAAe,KAAM,YAAY,CAAC;EACnD,OAAO;AACL,eAAW,GAAG;EAChB;AACA,SAAO;IACL,UAAU,GAAG,mBAAmB;IAChC,aAAa,eAAe;IAC5B,MAAU;IACV,QAAY;IACZ,MAAM;IACN,OAAO;IACP,QAAQ;;AAEZ;AAEA,IAAY;CAAZ,SAAYC,kBAAe;AACzB,EAAAA,iBAAAA,iBAAA,YAAA,KAAA;AACA,EAAAA,iBAAAA,iBAAA,kBAAA,KAAA;AACA,EAAAA,iBAAAA,iBAAA,qBAAA,KAAA;AACF,GAJY,oBAAA,kBAAe,CAAA,EAAA;AAkBrB,SAAU,uBACZ,gBAAwB,mBACxB,iBACA,oBAC4C;AAC9C,SAAO;IACL;IACA,oBAAoB,aAAW,mBAAmB,EAAC,QAAO,CAAC;IAC3D,mBAAmB,MAAM,kBAAkB,gBAAgB,eAAe;IAC1E,oBAAoB,aAAW,qBAAqB,mBAAmB,OAAO,IAAI;IAClF,cAAc,CAAC,SAAS,UAAU,UAAqB;AACrD,UAAI,CAAC,QAAQ,UAAU;AACrB,0BAAkB,CAAC;UACjB,UAAU,GAAG,mBAAmB;UAChC,aAAa;UACb,QAAY;UACZ,MAAU;UACV,MAAM;UACN,OAAO;UACP,QAAQ;SACT,CAAC;AACF,eAAO,EAAC,OAAO,MAAK;QAAE,EAAC;MACzB;AACA,YAAM,UAAmB,eAAM,QAAQ,UAAU;QAG/C,SAAS;QACT,eAAe;QACf,YAAY;OACb;AACD,cAAQ,GAAG,OAAO,CAAC,OAAeC,UAAgB;AAChD,gBAAQ,OAAO;UACb,KAAK;AACH,qBAAS,gBAAgB,QAAQA,KAAI;AACrC;UACF,KAAK;UACL,KAAK;AACH,qBAAS,gBAAgB,cAAcA,KAAI;AAC3C;UACF,KAAK;UACL,KAAK;AACH,qBAAS,gBAAgB,iBAAiBA,KAAI;AAC9C;QACJ;MACF,CAAC;AACD,cAAQ,GAAG,SAAS,KAAK;AACzB,aAAO,EAAC,OAAO,MAAM,QAAQ,MAAK,GAAI,MAAK;IAC7C;IACA,YAAa,GAAG,IAAI,gBAAgB,GAAG,IAAI,cAAe;IAC1D,cAAe,GAAG,IAAI,cAAc,GAAG,IAAI,gBAAiB;;AAEhE;AAgBM,SAAU,wBAAwB,MAAsB;AAK5D,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AAGJ,QAAM,sBAAsB,oBAAI,IAAG;AACnC,QAAM,YAAY,oBAAI,IAAG;AAEzB,QAAM,qBAAqB,cAAa;AAGxC,MAAI;AACJ,QAAM,eAAe,IAAI,QAAc,aAAW,sBAAsB,OAAO;AAG/E,QAAM,cACF,KAAK,aAAa,cAAe,SAAS,oBAAoB,mBAAoB;AAEtF,SAAO,EAAC,OAAO,OAAO,QAAM,aAAa,KAAK,EAAE,GAAG,mBAAkB;AAErE,WAAS,WAAW,UAAgB;AAClC,eAAgB,eAAU,QAAQ;AAClC,QAAI,QAAQ,UAAU,IAAI,QAAQ;AAClC,QAAI,CAAC,OAAO;AACV,cAAQ,CAAA;AACR,gBAAU,IAAI,UAAU,KAAK;IAC/B;AACA,WAAO;EACT;AAEA,WAAS,QAAK;AACZ,gBAAY,MAAK;AACjB,QAAI,6BAA6B;AAC/B,WAAK,aAAa,4BAA4B,WAAW;AACzD,oCAA8B;IAChC;EACF;AAGA,WAAS,gBAAa;AACpB,QAAI,CAAC,eAAe;AAClB,sBAAgB,KAAK,kBAAiB;IACxC;AACA,QAAI,cAAc,UAAU,cAAc,OAAO,QAAQ;AACvD,WAAK,kBAAkB,cAAc,MAAM;AAC3C,aAAO,cAAc;IACvB;AACA,UAAM,YAAY,KAAK,IAAG;AAC1B,QAAI,CAAC,oBAAoB;AACvB,2BAAqB,KAAK,mBAAmB,cAAc,OAAO;AAClE,YAAM,4BAA4B,mBAAmB;AACrD,yBAAmB,YAAY,SAC3B,UAAkB,MAAc,oBAChC,SAAqC,cAA4C,CAAA,GAAE;AACrF,4BAAoB,IAAS,eAAU,QAAQ,CAAC;AAChD,eAAO,0BAA0B,UAAU,MAAM,oBAAoB,SAAS,WAAW;MAC3F;AACA,YAAM,qBAAqB,mBAAmB;AAC9C,yBAAmB,aAAa,SAAS,UAAgB;AACvD,cAAM,KAAK,WAAW,QAAQ;AAC9B,YAAI,GAAG,UAAU,MAAM;AACrB,aAAG,SAAS,mBAAmB,KAAK,MAAM,QAAQ;QACpD;AACA,eAAO,GAAG;MACZ;AACA,YAAM,wBAAwB,mBAAmB;AACjD,yBAAmB,gBAAgB,SAC/B,UAAkB,iBAAgC;AACpD,cAAM,KAAK,WAAW,QAAQ;AAC9B,YAAI,CAAC,GAAG,IAAI;AACV,aAAG,KAAK,sBAAsB,KAAK,MAAM,UAAU,eAAe;QACpE;AACA,eAAO,GAAG;MACZ;AACA,YAAM,mBAAmB,mBAAmB;AAC5C,yBAAmB,WAAW,SAAS,UAAgB;AACrD,cAAM,KAAK,WAAW,QAAQ;AAC9B,YAAI,GAAG,WAAW,MAAM;AACtB,aAAG,UAAU,iBAAiB,KAAK,MAAM,QAAQ;QACnD;AACA,eAAO,GAAG;MACZ;AAEA,yBAAmB,2BAA2B,WAAA;AAC5C,YAAI,gCAAgC,QAAW;AAC7C,iBAAO;QACT;AACA,eAAO,4BAA4B;MACrC;IACF;AACA,wBAAoB,MAAK;AACzB,UAAM,aAAa;AAGnB,oBAAgB;AAChB,UAAM,gBAAgB,mBAAmB;MACvC,WAAW,cAAc;MACzB,SAAS,cAAc;MACvB,MAAM;MACN;MACA,cAAc,KAAK,mBAAmB,cAAc,OAAO;KAC5D;AAED,QAAI,cAAc,YAAY,QAAQ;AACpC,WAAK,kBAAkB,cAAc,WAAW;IAClD;AAEA,UAAM,UAAU,KAAK,IAAG;AACxB,QAAI,cAAc,QAAQ,aAAa;AACrC,YAAM,aAAa,UAAU,aAAa;AAC1C,WAAK,kBAAkB,CAAC,+BAA+B,UAAU,SAAS,CAAC,CAAC;IAC9E;AACA,UAAM,WAAW,mBAAmB,cAAc,WAAW;AAC7D,QAAI,YAAY,GAAG;AACjB,sBAAgB,cAAc;AAC9B,WAAK,kBACD,CAAC,wBAAwB,kDAAkD,CAAC,CAAC;IACnF,OAAO;AACL,WAAK,kBACD,CAAC,wBAAwB,gDAAgD,CAAC,CAAC;IACjF;AAEA,WAAO,cAAc;EACvB;AAEA,WAAS,eAAY;AACnB,oBAAgB;AAChB,yBAAqB;AACrB,oBAAgB;EAClB;AAEA,WAAS,mBAAmB,OAAwB,UAAgB;AAClE,UAAM,iBAAsB,eAAU,QAAQ;AAE9C,QAAI,iBAAiB,UAAU,gBAAgB,UAI3C,mBAAwB,eAAU,cAAc,OAAO,GAAG;AAE5D,mBAAY;IACd,WACI,UAAU,gBAAgB,gBAAgB,UAAU,gBAAgB,iBAAiB;AAGvF,sBAAgB;IAClB;AAEA,QAAI,UAAU,gBAAgB,iBAAiB;AAC7C,gBAAU,MAAK;IACjB,OAAO;AACL,gBAAU,OAAO,cAAc;IACjC;AAEA,QAAI,CAAC,oBAAoB,IAAI,cAAc,GAAG;AAE5C,iCAA2B,cAAc;IAC3C;EACF;AAKA,WAAS,2BAA2B,aAAmB;AACrD,QAAI,6BAA6B;AAC/B,WAAK,aAAa,4BAA4B,WAAW;IAC3D,OAAO;AACL,oCAA8B;QAC5B,uBAAuB,oBAAI,IAAG;QAC9B,aAAa;;IAEjB;AACA,gCAA4B,cAAc,KAAK,WAAW,WAAW,GAAG;AACxE,gCAA4B,sBAAsB,IAAI,WAAW;EACnE;AAEA,WAAS,YAAS;AAChB,SAAK,kBACD,CAAC,wBAAwB,yDAAyD,CAAC,CAAC;AACxF,kBAAa;AACb,kCAA8B;EAChC;AACF;;;ADrSM,SAAU,KACZ,MAAgB,eAAoC,QAAQ,OAC5D,QAAiC,oBAA6C,cAG9E,uBAAwC;AAC1C,MAAI,EAAC,SAAS,WAAW,SAAS,QAAQ,cAAc,OAAAC,QAAO,UAAS,IACpE,UAAU,mCAAmC,IAAI;AACrD,MAAI,aAAa,QAAQ;AACvB,WAAO,oBAAoB,cAA0B,QAAW,YAAY;EAC9E;AACA,MAAIA,QAAO;AACT,UAAM,SAAS,UAAU,SAAS,SAAS,YAAY;AACvD,WAAO,oBAAoB,OAAO,oBAAoB,SAAS,YAAY;EAC7E;AAEA,MAAI;AACJ,MAAI,iBAAiB,QAAW;AAC9B,iBAAa,aAAa;EAC5B;AAEA,QAAM,EAAC,aAAa,cAAc,QAAO,IAAI,mBACzC,EAAC,WAAW,SAAS,WAAW,YAAY,oBAAoB,sBAAqB,CAAC;AAC1F,MAAI,iBAAiB,QAAW;AAC9B,iBAAa,UAAU;EACzB;AACA,SAAO,oBAAoB,cAAc,SAAS,YAAY;AAChE;AA4CM,SAAU,mCAAmC,MAAc;AAC/D,QAAM,UAA+B,CAAA;AACrC,QAAM,aACF,MAAM,IAAI,EACL,oBAAoB,EAAC,iBAAiB,KAAI,CAAC,EAC3C,OAAO,YAAY,EAAC,MAAM,SAAQ,CAAC,EACnC,OAAO,cAAc,EAAC,MAAM,SAAQ,CAAC,EACrC,OAAO,UAAU,EAAC,MAAM,SAAQ,CAAC,EACjC,OAAO,sBAAsB,EAAC,MAAM,UAAU,SAAS,CAAC,SAAS,WAAW,QAAQ,EAAC,CAAC,EACtF,OAAO,WAAW,EAAC,MAAM,SAAQ,CAAC,EAClC,OAAO,SAAS,EAAC,MAAM,WAAW,OAAO,CAAC,GAAG,EAAC,CAAC,EAC/C,UAAS;AAElB,MAAI,WAAW;AAAU,YAAQ,aAAa,WAAW;AACzD,MAAI,WAAW;AAAY,YAAQ,eAAe,WAAW;AAC7D,MAAI,WAAW;AAAQ,YAAQ,eAAe,WAAW;AACzD,MAAI,WAAW;AACb,YAAQ,4BACJ,WAAW;AAEjB,QAAM,SAAS,gCACX,MAAM,SAAS,CAAC,YAAY,cAAc,UAAU,sBAAsB,OAAO,CAAC;AACtF,SAAO,EAAC,GAAG,QAAQ,OAAO,WAAW,MAAK;AAC5C;AAEM,SAAU,gCACZ,MAAgB,kBAAuC,CAAA,GACvD,mBAA6B,CAAA,GAAE;AACjC,MAAI,YAAYC,IAAG,iBAAiB,IAAI;AACxC,QAAM,UAAU,UAAU,QAAQ,WAAW;AAC7C,QAAM,YAAY,UAAU,OAAO,OAAO,OAAI;AAC5C,QAAI,OAAO,EAAE,gBAAgB,UAAU;AACrC,YAAM,MAAM,EAAE;AACd,aAAO,CAAC,iBAAiB,KAAK,OAAK,IAAI,QAAQ,CAAC,KAAK,CAAC;IACxD;AACA,WAAO;EACT,CAAC;AACD,MAAI,UAAU,QAAQ;AACpB,WAAO;MACL;MACA,WAAW,CAAA;MACX,SAAS,UAAU;MACnB,QAAQ;MACR,WAAe,UAAU;;EAE7B;AACA,QAAM,SAAS,kBAAkB,SAAS,UAAU,OAAO;AAC3D,QAAM,UAAU,EAAC,GAAG,OAAO,SAAS,GAAG,gBAAe;AACtD,MAAI,QAAQ,QAAQ;AAClB,YAAQ,eAAe,QAAQ;EACjC;AACA,SAAO;IACL;IACA,WAAW,OAAO;IAClB;IACA,QAAQ,OAAO;IACf,WAAW,OAAO;;AAEtB;AAEA,SAAS,yBAAyB,SAA6B;AAC7D,QAAM,WAAW,UAAU,QAAQ,WAAW;AAC9C,SAAO;IACL,qBAAqB,MAAM,YAAYA,IAAG,IAAI,oBAAmB;IAIjE,sBAAsB,cAAY,SAAS,QAAQ,OAAO,GAAG;IAC7D,YAAY,MAAK;AAIf,UAAI,WAAW,QAAQ,YAAY,QAAW;AAC5C,eAAO,QAAQ,YAAYA,IAAG,YAAY,WAAW,OAAO;MAC9D;AACA,aAAOA,IAAG,IAAI;IAChB;;AAEJ;AAEA,SAAS,oBACL,gBAA8C,SAC9C,eAAoC,QAAQ,OAAK;AACnD,QAAM,oBACF,eAAe,OAAO,OAAK,EAAE,aAAaA,IAAG,mBAAmB,OAAO;AAC3E,mBAAiB,mBAAmB,SAAS,YAAY;AACzD,SAAO,mBAAmB,cAAc;AAC1C;AAEM,SAAU,UACZ,SAAiB,SAA8B,cAAiC;AAClF,SAAO,wBAAwB,uBAAuB,SAAS,iBAAc;AAC3E,qBAAiB,aAAa,SAAS,YAAY;EACrD,GAAG,SAAS,MAAS,CAAC;AACxB;AAEA,SAAS,iBACL,aAA2C,SAC3C,cAAiC;AACnC,MAAI,YAAY,WAAW,GAAG;AAC5B;EACF;AACA,QAAM,aAAa,yBAAyB,OAAO;AACnD,eAAa,kBAAkB,aAAa,UAAU,CAAC;AACzD;", "names": ["ts", "FileChangeEvent", "path", "watch", "ts"]}