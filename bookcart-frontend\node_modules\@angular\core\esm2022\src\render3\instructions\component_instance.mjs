/*!
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { assertDefined } from '../../util/assert';
import { CONTEXT, DECLARATION_COMPONENT_VIEW } from '../interfaces/view';
import { getLView } from '../state';
/**
 * Instruction that returns the component instance in which the current instruction is executing.
 * This is a constant-time version of `nextContent` for the case where we know that we need the
 * component instance specifically, rather than the context of a particular template.
 *
 * @codeGenApi
 */
export function ɵɵcomponentInstance() {
    const instance = getLView()[DECLARATION_COMPONENT_VIEW][CONTEXT];
    ngDevMode && assertDefined(instance, 'Expected component instance to be defined');
    return instance;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiY29tcG9uZW50X2luc3RhbmNlLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vLi4vLi4vLi4vcGFja2FnZXMvY29yZS9zcmMvcmVuZGVyMy9pbnN0cnVjdGlvbnMvY29tcG9uZW50X2luc3RhbmNlLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7R0FNRztBQUVILE9BQU8sRUFBQyxhQUFhLEVBQUMsTUFBTSxtQkFBbUIsQ0FBQztBQUNoRCxPQUFPLEVBQUMsT0FBTyxFQUFFLDBCQUEwQixFQUFDLE1BQU0sb0JBQW9CLENBQUM7QUFDdkUsT0FBTyxFQUFDLFFBQVEsRUFBQyxNQUFNLFVBQVUsQ0FBQztBQUdsQzs7Ozs7O0dBTUc7QUFDSCxNQUFNLFVBQVUsbUJBQW1CO0lBQ2pDLE1BQU0sUUFBUSxHQUFHLFFBQVEsRUFBRSxDQUFDLDBCQUEwQixDQUFDLENBQUMsT0FBTyxDQUFDLENBQUM7SUFDakUsU0FBUyxJQUFJLGFBQWEsQ0FBQyxRQUFRLEVBQUUsMkNBQTJDLENBQUMsQ0FBQztJQUNsRixPQUFPLFFBQVEsQ0FBQztBQUNsQixDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyohXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5cbmltcG9ydCB7YXNzZXJ0RGVmaW5lZH0gZnJvbSAnLi4vLi4vdXRpbC9hc3NlcnQnO1xuaW1wb3J0IHtDT05URVhULCBERUNMQVJBVElPTl9DT01QT05FTlRfVklFV30gZnJvbSAnLi4vaW50ZXJmYWNlcy92aWV3JztcbmltcG9ydCB7Z2V0TFZpZXd9IGZyb20gJy4uL3N0YXRlJztcblxuXG4vKipcbiAqIEluc3RydWN0aW9uIHRoYXQgcmV0dXJucyB0aGUgY29tcG9uZW50IGluc3RhbmNlIGluIHdoaWNoIHRoZSBjdXJyZW50IGluc3RydWN0aW9uIGlzIGV4ZWN1dGluZy5cbiAqIFRoaXMgaXMgYSBjb25zdGFudC10aW1lIHZlcnNpb24gb2YgYG5leHRDb250ZW50YCBmb3IgdGhlIGNhc2Ugd2hlcmUgd2Uga25vdyB0aGF0IHdlIG5lZWQgdGhlXG4gKiBjb21wb25lbnQgaW5zdGFuY2Ugc3BlY2lmaWNhbGx5LCByYXRoZXIgdGhhbiB0aGUgY29udGV4dCBvZiBhIHBhcnRpY3VsYXIgdGVtcGxhdGUuXG4gKlxuICogQGNvZGVHZW5BcGlcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIMm1ybVjb21wb25lbnRJbnN0YW5jZSgpOiB1bmtub3duIHtcbiAgY29uc3QgaW5zdGFuY2UgPSBnZXRMVmlldygpW0RFQ0xBUkFUSU9OX0NPTVBPTkVOVF9WSUVXXVtDT05URVhUXTtcbiAgbmdEZXZNb2RlICYmIGFzc2VydERlZmluZWQoaW5zdGFuY2UsICdFeHBlY3RlZCBjb21wb25lbnQgaW5zdGFuY2UgdG8gYmUgZGVmaW5lZCcpO1xuICByZXR1cm4gaW5zdGFuY2U7XG59XG4iXX0=