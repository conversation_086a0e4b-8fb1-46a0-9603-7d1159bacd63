/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["ks-Arab", [["AM", "PM"], u, u], u, [["ا", "ژ", "ب", "ب", "ب", "ج", "ب"], ["آتھوار", "ژٔندٕروار", "بۆموار", "بودوار", "برؠسوار", "جُمہ", "بٹوار"], ["اَتھوار", "ژٔندرٕروار", "بۆموار", "بودوار", "برؠسوار", "جُمہ", "بٹوار"], ["آتھوار", "ژٔندٕروار", "بۆموار", "بودوار", "برؠسوار", "جُمہ", "بٹوار"]], u, [["ج", "ف", "م", "ا", "م", "ج", "ج", "ا", "س", "س", "ا", "ن"], ["جنؤری", "فرؤری", "مارٕچ", "اپریل", "مئی", "جوٗن", "جوٗلایی", "اگست", "ستمبر", "اکتوٗبر", "نومبر", "دسمبر"], u], u, [["بی سی", "اے ڈی"], u, ["قبٕل مسیٖح", "عیٖسوی سنہٕ"]], 0, [0, 0], ["M/d/yy", "MMM d, y", "MMMM d, y", "EEEE, MMMM d, y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1}, {0}", u, "{0} پٮ۪ٹھۍ {1}", u], [".", "،", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "INR", "₹", "ہِندُستٲنۍ رۄپَے", {}, "rtl", plural];
//# sourceMappingURL=data:application/json;base64,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