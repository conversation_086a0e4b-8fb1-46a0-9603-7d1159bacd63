/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['lu'] = ["lu",[["<PERSON><PERSON>","<PERSON>lolo"],u,u],u,[["L","N","N","N","N","N","L"],["Lum","Nko","Ndy","Ndg","Njw","Ngv","Lub"],["<PERSON>mingu","Nkodya","Nd<PERSON><PERSON><PERSON>","Ndang<PERSON>","<PERSON>j<PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>"],["<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","Ndg","Njw","Ngv","Lub"]],u,[["C","L","L","M","L","L","K","L","<PERSON>","<PERSON>","<PERSON>","<PERSON>"],["<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>u","<PERSON>m","<PERSON>f","<PERSON>b","<PERSON>sh","<PERSON>t","<PERSON>n","<PERSON><PERSON>","<PERSON><PERSON>"],["<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON>s<PERSON>lo","<PERSON><PERSON><PERSON><PERSON>","<PERSON>m<PERSON>ng<PERSON>l<PERSON>","<PERSON>fuimi","<PERSON>b<PERSON>làshìpù","Lùshìkà","Lutongolo","Lungùdi","Kaswèkèsè","Ciswà"]],u,[["kmp. Y.K.","kny. Y. K."],u,["Kumpala kwa Yezu Kli","Kunyima kwa Yezu Kli"]],1,[6,0],["d/M/y","d MMM y","d MMMM y","EEEE d MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[",",".",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","#,##0.00¤","#E0"],"CDF","FC","Nfalanga wa Kongu",{"CDF":["FC"],"JPY":["JP¥","¥"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    