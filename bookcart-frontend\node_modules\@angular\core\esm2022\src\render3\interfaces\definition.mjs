/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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