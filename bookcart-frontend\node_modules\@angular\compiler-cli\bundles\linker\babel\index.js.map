{"version": 3, "sources": ["../../../../../../../../packages/compiler-cli/linker/babel/src/es2015_linker_plugin.ts", "../../../../../../../../packages/compiler-cli/linker/babel/src/ast/babel_ast_factory.ts", "../../../../../../../../packages/compiler-cli/linker/babel/src/ast/babel_ast_host.ts", "../../../../../../../../packages/compiler-cli/linker/babel/src/babel_declaration_scope.ts", "../../../../../../../../packages/compiler-cli/linker/babel/src/babel_plugin.ts", "../../../../../../../../packages/compiler-cli/linker/babel/index.ts"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAQA,SAAwC,SAASA,UAAQ;;;ACDzD,SAAQ,SAAS,SAAQ;AAQnB,IAAO,kBAAP,MAAsB;EAC1B,YAEY,WAAiB;AAAjB,SAAA,YAAA;AAUZ,SAAA,qBAAqB,EAAE;AAoBvB,SAAA,cAAc,EAAE;AAUhB,SAAA,oBAAoB,EAAE;AAMtB,SAAA,4BAA4B,EAAE;AAwB9B,SAAA,mBAAmB,EAAE;AAErB,SAAA,oBAAoB,EAAE;AAsBtB,SAAA,sBAAsB,EAAE;AAUxB,SAAA,gCAAgC,EAAE;AAMlC,SAAA,wBAAwB,EAAE;AAS1B,SAAA,uBAAuB,EAAE;AAMzB,SAAA,wBAAwB,EAAE;EA7HM;EAEhC,eAAe,WAAwB,iBAAiC;AAEtE,aAAS,IAAI,gBAAgB,SAAS,GAAG,KAAK,GAAG,KAAK;AACpD,YAAM,UAAU,gBAAgB;AAChC,QAAE,WAAW,WAAW,WAAW,QAAQ,SAAQ,GAAI,CAAC,QAAQ,SAAS;IAC3E;EACF;EAIA,iBAAiB,QAAsB,OAAmB;AACxD,WAAO,QAAQ,eAAe,qCAAqC;AACnE,WAAO,EAAE,qBAAqB,KAAK,QAAQ,KAAK;EAClD;EAEA,uBACI,aAA2B,UAC3B,cAA0B;AAC5B,YAAQ,UAAU;MAChB,KAAK;MACL,KAAK;MACL,KAAK;AACH,eAAO,EAAE,kBAAkB,UAAU,aAAa,YAAY;MAChE;AACE,eAAO,EAAE,iBAAiB,UAAU,aAAa,YAAY;IACjE;EACF;EAIA,qBAAqB,QAAsB,MAAsB,MAAa;AAC5E,UAAM,OAAO,EAAE,eAAe,QAAQ,IAAI;AAC1C,QAAI,MAAM;AACR,QAAE,WAAW,MAAM,WAAW,eAA0B,KAAK;IAC/D;AACA,WAAO;EACT;EAIA,oBAAoB,YAA0B,SAAqB;AACjE,WAAO,EAAE,iBAAiB,YAAY,SAAwB,IAAI;EACpE;EAIA,0BAA0B,cAAsB,YAAsB,MAAiB;AAErF,WAAO,MAAM,EAAE,kBAAkB,SAAS;AAC1C,WAAO,EAAE,oBACL,EAAE,WAAW,YAAY,GAAG,WAAW,IAAI,WAAS,EAAE,WAAW,KAAK,CAAC,GAAG,IAAI;EACpF;EAEA,8BAA8B,YAAsB,MAA8B;AAEhF,QAAI,EAAE,YAAY,IAAI,GAAG;AACvB,aAAO,MAAM,EAAE,kBAAkB,SAAS;IAC5C;AACA,WAAO,EAAE,wBAAwB,WAAW,IAAI,WAAS,EAAE,WAAW,KAAK,CAAC,GAAG,IAAI;EACrF;EAEA,yBAAyB,cAA2B,YAAsB,MAAiB;AAEzF,WAAO,MAAM,EAAE,kBAAkB,SAAS;AAC1C,UAAM,OAAO,iBAAiB,OAAO,EAAE,WAAW,YAAY,IAAI;AAClE,WAAO,EAAE,mBAAmB,MAAM,WAAW,IAAI,WAAS,EAAE,WAAW,KAAK,CAAC,GAAG,IAAI;EACtF;EAMA,oBAAoB,KAAW;AAC7B,WAAO,KAAK,qBAAqB,EAAE,OAAM,GAAI,CAAC,EAAE,cAAc,GAAG,CAAC,GAAG,KAAgB;EACvF;EAEA,cAAc,OAA2C;AACvD,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO,EAAE,cAAc,KAAK;IAC9B,WAAW,OAAO,UAAU,UAAU;AACpC,aAAO,EAAE,eAAe,KAAK;IAC/B,WAAW,OAAO,UAAU,WAAW;AACrC,aAAO,EAAE,eAAe,KAAK;IAC/B,WAAW,UAAU,QAAW;AAC9B,aAAO,EAAE,WAAW,WAAW;IACjC,WAAW,UAAU,MAAM;AACzB,aAAO,EAAE,YAAW;IACtB,OAAO;AACL,YAAM,IAAI,MAAM,oBAAoB,UAAU,OAAO,QAAQ;IAC/D;EACF;EAIA,oBAAoB,YAAiD;AACnE,WAAO,EAAE,iBAAiB,WAAW,IAAI,UAAO;AAC9C,YAAM,MACF,KAAK,SAAS,EAAE,cAAc,KAAK,YAAY,IAAI,EAAE,WAAW,KAAK,YAAY;AACrF,aAAO,EAAE,eAAe,KAAK,KAAK,KAAK;IACzC,CAAC,CAAC;EACJ;EAIA,qBAAqB,YAA0B,cAAoB;AACjE,WAAO,EAAE,iBAAiB,YAAY,EAAE,WAAW,YAAY,GAAkB,KAAK;EACxF;EAIA,qBAAqB,KAAmB,UAAuC;AAC7E,UAAM,WAAW,SAAS,SAAS,IAC/B,CAAC,SAAS,MAAM,KAAK,kBACjB,EAAE,gBAAgB,SAAS,MAAM,SAAS,SAAS,SAAS,CAAC,GAAG,QAAQ,KAAK,CAAC;AACtF,WAAO,EAAE,yBAAyB,KAAK,EAAE,gBAAgB,UAAU,SAAS,WAAW,CAAC;EAC1F;EAIA,uBAAuB,YAAwB;AAC7C,WAAO,EAAE,gBAAgB,UAAU,UAAU;EAC/C;EAIA,0BACI,cAAsB,aACtB,MAA6B;AAC/B,WAAO,EAAE,oBACL,MAAM,CAAC,EAAE,mBAAmB,EAAE,WAAW,YAAY,GAAG,WAAW,CAAC,CAAC;EAC3E;EAEA,kBACI,MAAS,gBAAmC;AAC9C,QAAI,mBAAmB,MAAM;AAC3B,aAAO;IACT;AACA,SAAK,MAAM;MAIT,UAAU,eAAe,QAAQ,KAAK,YAAY,eAAe,MAAM;MACvE,OAAO;QACL,MAAM,eAAe,MAAM,OAAO;QAClC,QAAQ,eAAe,MAAM;;MAE/B,KAAK;QACH,MAAM,eAAe,IAAI,OAAO;QAChC,QAAQ,eAAe,IAAI;;;AAG/B,SAAK,QAAQ,eAAe,MAAM;AAClC,SAAK,MAAM,eAAe,IAAI;AAE9B,WAAO;EACT;;AAGF,SAAS,cAAc,MAAkB;AAGvC,SAAO,EAAE,OAAO,IAAI;AACtB;;;AC9KA,SAAQ,SAASC,UAAQ;AAOnB,IAAO,eAAP,MAAmB;EAAzB,cAAA;AAWE,SAAA,kBAAkBC,GAAE;AAOpB,SAAA,mBAAmBA,GAAE;AAyBrB,SAAA,iBAAiBA,GAAE;AAWnB,SAAA,kBAAkBA,GAAE;AA+CpB,SAAA,mBAAmBA,GAAE;EA2BvB;EA/HE,cAAc,MAAkB;AAC9B,QAAIA,GAAE,aAAa,IAAI,GAAG;AACxB,aAAO,KAAK;IACd,WAAWA,GAAE,mBAAmB,IAAI,KAAKA,GAAE,aAAa,KAAK,QAAQ,GAAG;AACtE,aAAO,KAAK,SAAS;IACvB,OAAO;AACL,aAAO;IACT;EACF;EAIA,mBAAmB,KAAiB;AAClC,WAAO,KAAKA,GAAE,iBAAiB,kBAAkB;AACjD,WAAO,IAAI;EACb;EAIA,oBAAoB,KAAiB;AACnC,WAAO,KAAKA,GAAE,kBAAkB,mBAAmB;AACnD,WAAO,IAAI;EACb;EAEA,iBAAiB,MAAkB;AACjC,WAAOA,GAAE,iBAAiB,IAAI,KAAK,yBAAyB,IAAI;EAClE;EAEA,oBAAoB,MAAkB;AACpC,QAAIA,GAAE,iBAAiB,IAAI,GAAG;AAC5B,aAAO,KAAK;IACd,WAAW,yBAAyB,IAAI,GAAG;AACzC,aAAO,CAAC,KAAK,SAAS;IACxB,OAAO;AACL,YAAM,IAAI,iBAAiB,MAAM,iDAAiD;IACpF;EACF;EAEA,OAAO,MAAkB;AACvB,WAAOA,GAAE,cAAc,IAAI;EAC7B;EAIA,kBAAkB,OAAmB;AACnC,WAAO,OAAOA,GAAE,mBAAmB,kBAAkB;AACrD,WAAO,MAAM,SAAS,IAAI,aAAU;AAClC,aAAO,SAAS,mBAAmB,kCAAkC;AACrE,aAAO,SAAS,oBAAoB,2CAA2C;AAC/E,aAAO;IACT,CAAC;EACH;EAIA,mBAAmB,KAAiB;AAClC,WAAO,KAAKA,GAAE,oBAAoB,mBAAmB;AAErD,UAAM,SAAS,oBAAI,IAAG;AACtB,eAAW,YAAY,IAAI,YAAY;AACrC,aAAO,UAAUA,GAAE,kBAAkB,uBAAuB;AAC5D,aAAO,SAAS,OAAOA,GAAE,cAAc,eAAe;AACtD,aAAO,SAAS,KAAK,gCAAgC,iBAAiB;AAEtE,YAAM,MAAMA,GAAE,aAAa,SAAS,GAAG,IAAI,SAAS,IAAI,OAAO,SAAS,IAAI;AAC5E,aAAO,IAAI,GAAG,OAAO,SAAS,KAAK;IACrC;AACA,WAAO;EACT;EAEA,qBAAqB,MAAkB;AACrC,WAAOA,GAAE,WAAW,IAAI;EAC1B;EAEA,iBAAiB,IAAgB;AAC/B,WAAO,IAAI,KAAK,sBAAsB,YAAY;AAClD,QAAI,CAACA,GAAE,iBAAiB,GAAG,IAAI,GAAG;AAEhC,aAAO,GAAG;IACZ;AAMA,QAAI,GAAG,KAAK,KAAK,WAAW,GAAG;AAC7B,YAAM,IAAI,iBACN,GAAG,MAAM,8EAA8E;IAC7F;AACA,UAAM,OAAO,GAAG,KAAK,KAAK;AAC1B,WAAO,MAAMA,GAAE,mBAAmB,gDAAgD;AAGlF,QAAI,KAAK,aAAa,QAAQ,KAAK,aAAa,QAAW;AACzD,YAAM,IAAI,iBAAiB,MAAM,0DAA0D;IAC7F;AAEA,WAAO,KAAK;EACd;EAGA,YAAY,MAAkB;AAC5B,WAAO,MAAMA,GAAE,kBAAkB,mBAAmB;AACpD,WAAO,KAAK,QAAQA,GAAE,cAAc,eAAe;AACnD,WAAO,KAAK;EACd;EACA,eAAe,MAAkB;AAC/B,WAAO,MAAMA,GAAE,kBAAkB,mBAAmB;AACpD,WAAO,KAAK,UAAU,IAAI,SAAM;AAC9B,aAAO,KAAK,qBAAqB,mCAAmC;AACpE,aAAO,KAAKA,GAAE,cAAc,8BAA8B;AAC1D,aAAO;IACT,CAAC;EACH;EAEA,SAAS,MAAkB;AACzB,QAAI,KAAK,OAAO,QAAQ,KAAK,SAAS,QAAQ,KAAK,OAAO,MAAM;AAC9D,YAAM,IAAI,iBACN,MAAM,qEAAqE;IACjF;AACA,WAAO;MACL,WAAW,KAAK,IAAI,MAAM,OAAO;MACjC,UAAU,KAAK,IAAI,MAAM;MACzB,UAAU,KAAK;MACf,QAAQ,KAAK;;EAEjB;;AAOF,SAAS,kBAAkB,GAAoC;AAE7D,SAAO,MAAM;AACf;AAMA,SAAS,mBAAmB,GAA+B;AACzD,SAAO,CAACA,GAAE,gBAAgB,CAAC;AAC7B;AAUA,SAAS,+BAA+B,GAAS;AAE/C,SAAOA,GAAE,aAAa,CAAC,KAAKA,GAAE,gBAAgB,CAAC,KAAKA,GAAE,iBAAiB,CAAC;AAC1E;AAUA,SAAS,oBAAoB,KAAiB;AAC5C,SAAO,CAACA,GAAE,gBAAgB,GAAG;AAC/B;AAOA,SAAS,yBAAyB,MAAkB;AAClD,SAAOA,GAAE,kBAAkB,IAAI,KAAK,KAAK,UAAU,KAAK,aAAa,OACjEA,GAAE,iBAAiB,KAAK,QAAQ,MAAM,KAAK,SAAS,UAAU,KAAK,KAAK,SAAS,UAAU;AACjG;;;AC5LA,SAAkB,SAASC,UAAQ;AAa7B,IAAO,wBAAP,MAA4B;EAMhC,YAAoB,kBAAmC;AAAnC,SAAA,mBAAA;EAAsC;EAY1D,oBAAoB,YAAwB;AAE1C,QAAI,oBAAoB;AACxB,WAAOA,GAAE,mBAAmB,iBAAiB,GAAG;AAC9C,0BAAoB,kBAAkB;IACxC;AAEA,QAAI,CAACA,GAAE,aAAa,iBAAiB,GAAG;AACtC,aAAO;IACT;AAIA,UAAM,UAAU,KAAK,iBAAiB,WAAW,kBAAkB,IAAI;AACvE,QAAI,YAAY,QAAW;AACzB,aAAO;IACT;AAKA,UAAM,OAAO,QAAQ,MAAM;AAC3B,QAAI,CAAC,KAAK,sBAAqB,KAAM,CAAC,KAAK,qBAAoB,KAC3D,EAAE,KAAK,UAAS,KAAM,KAAK,KAAK,eAAe,WAAW;AAC5D,aAAO;IACT;AAEA,WAAO;EACT;;;;AH3CI,SAAU,yBAAyB,EAAC,YAAY,WAAW,QAAO,GAAsB;AAE5F,MAAI,aAA4E;AAEhF,SAAO;IACL,SAAS;MACP,SAAS;QAKP,MAAM,GAAG,OAAK;AAlCtB;AAmCU,qBAAW,UAAU;AAGrB,gBAAM,OAAO,MAAM;AACnB,gBAAM,YAAW,UAAK,KAAK,aAAV,YAAsB,KAAK,KAAK;AACjD,cAAI,CAAC,UAAU;AACb,kBAAM,IAAI,MACN,yIAAyI;UAC/I;AACA,gBAAM,YAAY,WAAW,SAAQ,UAAK,KAAK,QAAV,YAAiB,KAAK,QAAQ;AAEnE,gBAAM,oBAAoB,kBAAkB,OACxC,YAAY,QAAQ,IAAI,aAAY,GAAI,IAAI,gBAAgB,SAAS,GAAG,OAAO;AACnF,uBAAa,IAAI,WAAW,mBAAmB,WAAW,KAAK,IAAI;QACrE;QAMA,OAAI;AACF,wBAAc,UAAU;AACxB,qBAAW,EAAC,eAAe,WAAU,KAAK,WAAW,sBAAqB,GAAI;AAC5E,6BAAiB,eAAe,UAAU;UAC5C;AACA,uBAAa;QACf;;MAOF,eAAe,MAAkC,OAAK;AACpD,YAAI,eAAe,MAAM;AAIvB;QACF;AAEA,YAAI;AACF,gBAAM,aAAa,cAAc,IAAI;AACrC,cAAI,eAAe,MAAM;AACvB;UACF;AACA,gBAAM,OAAO,KAAK,KAAK;AACvB,cAAI,CAAC,WAAW,qBAAqB,UAAU,KAAK,CAAC,kBAAkB,IAAI,GAAG;AAC5E;UACF;AAEA,gBAAM,mBAAmB,IAAI,sBAAsB,KAAK,KAAK;AAC7D,gBAAM,cAAc,WAAW,uBAAuB,YAAY,MAAM,gBAAgB;AAExF,eAAK,YAAY,WAAW;QAC9B,SAAS,GAAP;AACA,gBAAM,OAAO,mBAAmB,CAAC,IAAI,EAAE,OAAiB,KAAK;AAC7D,gBAAM,oBAAoB,MAAM,MAAO,EAAY,SAAS,IAAI;QAClE;MACF;;;AAGN;AAOA,SAAS,iBAAiB,MAAyB,YAAyB;AAC1E,MAAI,KAAK,UAAS,GAAI;AACpB,sBAAkB,MAAM,UAAU;EACpC,OAAO;AACL,uBAAmB,MAAM,UAAU;EACrC;AACF;AAKA,SAAS,mBACL,IAA0D,YAAyB;AACrF,QAAM,OAAO,GAAG,IAAI,MAAM;AAC1B,OAAK,iBAAiB,QAAQ,UAAU;AAC1C;AAKA,SAAS,kBAAkB,SAA8B,YAAyB;AAChF,QAAM,OAAO,QAAQ,IAAI,MAAM;AAC/B,QAAM,mBAAmB,KAAK,OAAO,eAAa,UAAU,oBAAmB,CAAE;AACjF,MAAI,iBAAiB,WAAW,GAAG;AACjC,YAAQ,iBAAiB,QAAQ,UAAU;EAC7C,OAAO;AACL,qBAAiB,iBAAiB,SAAS,GAAG,YAAY,UAAU;EACtE;AACF;AAEA,SAAS,cAAc,MAAgC;AACrD,QAAM,SAAS,KAAK,KAAK;AACzB,MAAIC,GAAE,aAAa,MAAM,GAAG;AAC1B,WAAO,OAAO;EAChB,WAAWA,GAAE,mBAAmB,MAAM,KAAKA,GAAE,aAAa,OAAO,QAAQ,GAAG;AAC1E,WAAO,OAAO,SAAS;EACzB,WAAWA,GAAE,mBAAmB,MAAM,KAAKA,GAAE,gBAAgB,OAAO,QAAQ,GAAG;AAC7E,WAAO,OAAO,SAAS;EACzB,OAAO;AACL,WAAO;EACT;AACF;AAKA,SAAS,kBAAkB,OAAe;AACxC,SAAO,MAAM,MAAM,UAAQA,GAAE,aAAa,IAAI,CAAC;AACjD;AAKA,SAAS,WAAc,KAAW;AAChC,MAAI,QAAQ,MAAM;AAChB,UAAM,IAAI,MAAM,iCAAiC;EACnD;AACF;AAKA,SAAS,cAAiB,KAAW;AACnC,MAAI,QAAQ,MAAM;AAChB,UAAM,IAAI,MAAM,qCAAqC;EACvD;AACF;AAKA,SAAS,oBAAoB,MAAiB,SAAiB,MAAY;AACzE,QAAM,WAAW,KAAK,KAAK,YAAY;AACvC,QAAM,QAAQ,KAAK,IAAI,WAAW,MAAM,OAAO;AAC/C,SAAO,GAAG,aAAa,MAAM;AAC/B;;;AIrJM,SAAU,oBAAoB,KAAgB,SAA+B;AACjF,MAAI,cAAc,CAAC;AAEnB,SAAO,yBAAyB;IAC9B,GAAG;IACH,YAAY,IAAI,iBAAgB;IAChC,QAAQ,IAAI,cAAc,SAAS,IAAI;GACxC;AACH;;;AC5BA,IAAA,gBAAe;", "names": ["t", "t", "t", "t", "t"]}