{"version": 3, "file": "AsyncScheduler.js", "sourceRoot": "", "sources": ["../../../../src/internal/scheduler/AsyncScheduler.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAKzC,MAAM,OAAO,cAAe,SAAQ,SAAS;IAkB3C,YAAY,eAA8B,EAAE,MAAoB,SAAS,CAAC,GAAG;QAC3E,KAAK,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC;QAlBvB,YAAO,GAA4B,EAAE,CAAC;QAOtC,YAAO,GAAY,KAAK,CAAC;IAYhC,CAAC;IAEM,KAAK,CAAC,MAAwB;QACnC,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;QAEzB,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACrB,OAAO;SACR;QAED,IAAI,KAAU,CAAC;QACf,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QAEpB,GAAG;YACD,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;gBACxD,MAAM;aACP;SACF,QAAQ,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,EAAG,CAAC,EAAE;QAEtC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QAErB,IAAI,KAAK,EAAE;YACT,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,EAAG,CAAC,EAAE;gBAClC,MAAM,CAAC,WAAW,EAAE,CAAC;aACtB;YACD,MAAM,KAAK,CAAC;SACb;IACH,CAAC;CACF"}