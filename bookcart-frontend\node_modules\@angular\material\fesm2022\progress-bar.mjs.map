{"version": 3, "file": "progress-bar.mjs", "sources": ["../../../../../../src/material/progress-bar/progress-bar.ts", "../../../../../../src/material/progress-bar/progress-bar.html", "../../../../../../src/material/progress-bar/module.ts", "../../../../../../src/material/progress-bar/progress-bar_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  ViewEncapsulation,\n  ElementRef,\n  NgZone,\n  Optional,\n  Inject,\n  Input,\n  Output,\n  EventEmitter,\n  AfterViewInit,\n  OnDestroy,\n  ChangeDetectorRef,\n  InjectionToken,\n  inject,\n  numberAttribute,\n  ANIMATION_MODULE_TYPE,\n} from '@angular/core';\nimport {DOCUMENT} from '@angular/common';\nimport {ThemePalette} from '@angular/material/core';\n\n/** Last animation end data. */\nexport interface ProgressAnimationEnd {\n  value: number;\n}\n\n/** Default `mat-progress-bar` options that can be overridden. */\nexport interface MatProgressBarDefaultOptions {\n  /** Default color of the progress bar. */\n  color?: ThemePalette;\n\n  /** Default mode of the progress bar. */\n  mode?: ProgressBarMode;\n}\n\n/** Injection token to be used to override the default options for `mat-progress-bar`. */\nexport const MAT_PROGRESS_BAR_DEFAULT_OPTIONS = new InjectionToken<MatProgressBarDefaultOptions>(\n  'MAT_PROGRESS_BAR_DEFAULT_OPTIONS',\n);\n\n/**\n * Injection token used to provide the current location to `MatProgressBar`.\n * Used to handle server-side rendering and to stub out during unit tests.\n * @docs-private\n */\nexport const MAT_PROGRESS_BAR_LOCATION = new InjectionToken<MatProgressBarLocation>(\n  'mat-progress-bar-location',\n  {providedIn: 'root', factory: MAT_PROGRESS_BAR_LOCATION_FACTORY},\n);\n\n/**\n * Stubbed out location for `MatProgressBar`.\n * @docs-private\n */\nexport interface MatProgressBarLocation {\n  getPathname: () => string;\n}\n\n/** @docs-private */\nexport function MAT_PROGRESS_BAR_LOCATION_FACTORY(): MatProgressBarLocation {\n  const _document = inject(DOCUMENT);\n  const _location = _document ? _document.location : null;\n\n  return {\n    // Note that this needs to be a function, rather than a property, because Angular\n    // will only resolve it once, but we want the current path on each call.\n    getPathname: () => (_location ? _location.pathname + _location.search : ''),\n  };\n}\n\nexport type ProgressBarMode = 'determinate' | 'indeterminate' | 'buffer' | 'query';\n\n@Component({\n  selector: 'mat-progress-bar',\n  exportAs: 'matProgressBar',\n  host: {\n    'role': 'progressbar',\n    'aria-valuemin': '0',\n    'aria-valuemax': '100',\n    // set tab index to -1 so screen readers will read the aria-label\n    // Note: there is a known issue with JAWS that does not read progressbar aria labels on FireFox\n    'tabindex': '-1',\n    '[attr.aria-valuenow]': '_isIndeterminate() ? null : value',\n    '[attr.mode]': 'mode',\n    'class': 'mat-mdc-progress-bar mdc-linear-progress',\n    '[class]': '\"mat-\" + color',\n    '[class._mat-animation-noopable]': '_isNoopAnimation',\n    '[class.mdc-linear-progress--animation-ready]': '!_isNoopAnimation',\n    '[class.mdc-linear-progress--indeterminate]': '_isIndeterminate()',\n  },\n  templateUrl: 'progress-bar.html',\n  styleUrl: 'progress-bar.css',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  standalone: true,\n})\nexport class MatProgressBar implements AfterViewInit, OnDestroy {\n  constructor(\n    readonly _elementRef: ElementRef<HTMLElement>,\n    private _ngZone: NgZone,\n    private _changeDetectorRef: ChangeDetectorRef,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) public _animationMode?: string,\n    @Optional()\n    @Inject(MAT_PROGRESS_BAR_DEFAULT_OPTIONS)\n    defaults?: MatProgressBarDefaultOptions,\n  ) {\n    this._isNoopAnimation = _animationMode === 'NoopAnimations';\n\n    if (defaults) {\n      if (defaults.color) {\n        this.color = this._defaultColor = defaults.color;\n      }\n\n      this.mode = defaults.mode || this.mode;\n    }\n  }\n\n  /** Flag that indicates whether NoopAnimations mode is set to true. */\n  _isNoopAnimation = false;\n\n  // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n  /** Theme palette color of the progress bar. */\n  @Input()\n  get color() {\n    return this._color || this._defaultColor;\n  }\n  set color(value: string | null | undefined) {\n    this._color = value;\n  }\n  private _color: string | null | undefined;\n  private _defaultColor: ThemePalette = 'primary';\n\n  /** Value of the progress bar. Defaults to zero. Mirrored to aria-valuenow. */\n  @Input({transform: numberAttribute})\n  get value(): number {\n    return this._value;\n  }\n  set value(v: number) {\n    this._value = clamp(v || 0);\n    this._changeDetectorRef.markForCheck();\n  }\n  private _value = 0;\n\n  /** Buffer value of the progress bar. Defaults to zero. */\n  @Input({transform: numberAttribute})\n  get bufferValue(): number {\n    return this._bufferValue || 0;\n  }\n  set bufferValue(v: number) {\n    this._bufferValue = clamp(v || 0);\n    this._changeDetectorRef.markForCheck();\n  }\n  private _bufferValue = 0;\n\n  /**\n   * Event emitted when animation of the primary progress bar completes. This event will not\n   * be emitted when animations are disabled, nor will it be emitted for modes with continuous\n   * animations (indeterminate and query).\n   */\n  @Output() readonly animationEnd = new EventEmitter<ProgressAnimationEnd>();\n\n  /**\n   * Mode of the progress bar.\n   *\n   * Input must be one of these values: determinate, indeterminate, buffer, query, defaults to\n   * 'determinate'.\n   * Mirrored to mode attribute.\n   */\n  @Input()\n  get mode(): ProgressBarMode {\n    return this._mode;\n  }\n  set mode(value: ProgressBarMode) {\n    // Note that we don't technically need a getter and a setter here,\n    // but we use it to match the behavior of the existing mat-progress-bar.\n    this._mode = value;\n    this._changeDetectorRef.markForCheck();\n  }\n  private _mode: ProgressBarMode = 'determinate';\n\n  ngAfterViewInit() {\n    // Run outside angular so change detection didn't get triggered on every transition end\n    // instead only on the animation that we care about (primary value bar's transitionend)\n    this._ngZone.runOutsideAngular(() => {\n      this._elementRef.nativeElement.addEventListener('transitionend', this._transitionendHandler);\n    });\n  }\n\n  ngOnDestroy() {\n    this._elementRef.nativeElement.removeEventListener('transitionend', this._transitionendHandler);\n  }\n\n  /** Gets the transform style that should be applied to the primary bar. */\n  _getPrimaryBarTransform(): string {\n    return `scaleX(${this._isIndeterminate() ? 1 : this.value / 100})`;\n  }\n\n  /** Gets the `flex-basis` value that should be applied to the buffer bar. */\n  _getBufferBarFlexBasis(): string {\n    return `${this.mode === 'buffer' ? this.bufferValue : 100}%`;\n  }\n\n  /** Returns whether the progress bar is indeterminate. */\n  _isIndeterminate(): boolean {\n    return this.mode === 'indeterminate' || this.mode === 'query';\n  }\n\n  /** Event handler for `transitionend` events. */\n  private _transitionendHandler = (event: TransitionEvent) => {\n    if (\n      this.animationEnd.observers.length === 0 ||\n      !event.target ||\n      !(event.target as HTMLElement).classList.contains('mdc-linear-progress__primary-bar')\n    ) {\n      return;\n    }\n\n    if (this.mode === 'determinate' || this.mode === 'buffer') {\n      this._ngZone.run(() => this.animationEnd.next({value: this.value}));\n    }\n  };\n}\n\n/** Clamps a value to be between two numbers, by default 0 and 100. */\nfunction clamp(v: number, min = 0, max = 100) {\n  return Math.max(min, Math.min(max, v));\n}\n", "<!--\n  All children need to be hidden for screen readers in order to support ChromeVox.\n  More context in the issue: https://github.com/angular/components/issues/22165.\n-->\n<div class=\"mdc-linear-progress__buffer\" aria-hidden=\"true\">\n  <div\n    class=\"mdc-linear-progress__buffer-bar\"\n    [style.flex-basis]=\"_getBufferBarFlexBasis()\"></div>\n  <!-- Remove the dots outside of buffer mode since they can cause CSP issues (see #28938) -->\n  @if (mode === 'buffer') {\n    <div class=\"mdc-linear-progress__buffer-dots\"></div>\n  }\n</div>\n<div\n  class=\"mdc-linear-progress__bar mdc-linear-progress__primary-bar\"\n  aria-hidden=\"true\"\n  [style.transform]=\"_getPrimaryBarTransform()\">\n  <span class=\"mdc-linear-progress__bar-inner\"></span>\n</div>\n<div class=\"mdc-linear-progress__bar mdc-linear-progress__secondary-bar\" aria-hidden=\"true\">\n  <span class=\"mdc-linear-progress__bar-inner\"></span>\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '@angular/material/core';\nimport {MatProgressBar} from './progress-bar';\n\n@NgModule({\n  imports: [MatProgressBar],\n  exports: [MatProgressBar, MatCommonModule],\n})\nexport class MatProgressBarModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;AA4CA;MACa,gCAAgC,GAAG,IAAI,cAAc,CAChE,kCAAkC,EAClC;AAEF;;;;AAIG;AACU,MAAA,yBAAyB,GAAG,IAAI,cAAc,CACzD,2BAA2B,EAC3B,EAAC,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,iCAAiC,EAAC,EAChE;AAUF;SACgB,iCAAiC,GAAA;AAC/C,IAAA,MAAM,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AACnC,IAAA,MAAM,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC;IAExD,OAAO;;;QAGL,WAAW,EAAE,OAAO,SAAS,GAAG,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,MAAM,GAAG,EAAE,CAAC;KAC5E,CAAC;AACJ,CAAC;MA4BY,cAAc,CAAA;IACzB,WACW,CAAA,WAAoC,EACrC,OAAe,EACf,kBAAqC,EACK,cAAuB,EAGzE,QAAuC,EAAA;QAN9B,IAAW,CAAA,WAAA,GAAX,WAAW,CAAyB;QACrC,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QACf,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAmB;QACK,IAAc,CAAA,cAAA,GAAd,cAAc,CAAS;;QAiB3E,IAAgB,CAAA,gBAAA,GAAG,KAAK,CAAC;QAYjB,IAAa,CAAA,aAAA,GAAiB,SAAS,CAAC;QAWxC,IAAM,CAAA,MAAA,GAAG,CAAC,CAAC;QAWX,IAAY,CAAA,YAAA,GAAG,CAAC,CAAC;AAEzB;;;;AAIG;AACgB,QAAA,IAAA,CAAA,YAAY,GAAG,IAAI,YAAY,EAAwB,CAAC;QAmBnE,IAAK,CAAA,KAAA,GAAoB,aAAa,CAAC;;AA8BvC,QAAA,IAAA,CAAA,qBAAqB,GAAG,CAAC,KAAsB,KAAI;YACzD,IACE,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC;gBACxC,CAAC,KAAK,CAAC,MAAM;gBACb,CAAE,KAAK,CAAC,MAAsB,CAAC,SAAS,CAAC,QAAQ,CAAC,kCAAkC,CAAC,EACrF;gBACA,OAAO;aACR;AAED,YAAA,IAAI,IAAI,CAAC,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;gBACzD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAC,CAAC,CAAC,CAAC;aACrE;AACH,SAAC,CAAC;AAlHA,QAAA,IAAI,CAAC,gBAAgB,GAAG,cAAc,KAAK,gBAAgB,CAAC;QAE5D,IAAI,QAAQ,EAAE;AACZ,YAAA,IAAI,QAAQ,CAAC,KAAK,EAAE;gBAClB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,KAAK,CAAC;aAClD;YAED,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC;SACxC;KACF;;;AAOD,IAAA,IACI,KAAK,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC;KAC1C;IACD,IAAI,KAAK,CAAC,KAAgC,EAAA;AACxC,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;KACrB;;AAKD,IAAA,IACI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;KACpB;IACD,IAAI,KAAK,CAAC,CAAS,EAAA;QACjB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AAC5B,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;KACxC;;AAID,IAAA,IACI,WAAW,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC;KAC/B;IACD,IAAI,WAAW,CAAC,CAAS,EAAA;QACvB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AAClC,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;KACxC;AAUD;;;;;;AAMG;AACH,IAAA,IACI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;IACD,IAAI,IAAI,CAAC,KAAsB,EAAA;;;AAG7B,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;KACxC;IAGD,eAAe,GAAA;;;AAGb,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,gBAAgB,CAAC,eAAe,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAC/F,SAAC,CAAC,CAAC;KACJ;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,mBAAmB,CAAC,eAAe,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;KACjG;;IAGD,uBAAuB,GAAA;AACrB,QAAA,OAAO,UAAU,IAAI,CAAC,gBAAgB,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,GAAG,GAAG,CAAC;KACpE;;IAGD,sBAAsB,GAAA;AACpB,QAAA,OAAO,GAAG,IAAI,CAAC,IAAI,KAAK,QAAQ,GAAG,IAAI,CAAC,WAAW,GAAG,GAAG,GAAG,CAAC;KAC9D;;IAGD,gBAAgB,GAAA;QACd,OAAO,IAAI,CAAC,IAAI,KAAK,eAAe,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC;KAC/D;8GA7GU,cAAc,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAKH,qBAAqB,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAEjC,gCAAgC,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAP/B,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,cAAc,EAqCN,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAAA,eAAe,CAWf,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAAA,eAAe,wlBCzJpC,66BAsBA,EAAA,MAAA,EAAA,CAAA,olUAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FDmFa,cAAc,EAAA,UAAA,EAAA,CAAA;kBAxB1B,SAAS;+BACE,kBAAkB,EAAA,QAAA,EAClB,gBAAgB,EACpB,IAAA,EAAA;AACJ,wBAAA,MAAM,EAAE,aAAa;AACrB,wBAAA,eAAe,EAAE,GAAG;AACpB,wBAAA,eAAe,EAAE,KAAK;;;AAGtB,wBAAA,UAAU,EAAE,IAAI;AAChB,wBAAA,sBAAsB,EAAE,mCAAmC;AAC3D,wBAAA,aAAa,EAAE,MAAM;AACrB,wBAAA,OAAO,EAAE,0CAA0C;AACnD,wBAAA,SAAS,EAAE,gBAAgB;AAC3B,wBAAA,iCAAiC,EAAE,kBAAkB;AACrD,wBAAA,8CAA8C,EAAE,mBAAmB;AACnE,wBAAA,4CAA4C,EAAE,oBAAoB;qBACnE,EAGgB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,aAAA,EAChC,iBAAiB,CAAC,IAAI,cACzB,IAAI,EAAA,QAAA,EAAA,66BAAA,EAAA,MAAA,EAAA,CAAA,olUAAA,CAAA,EAAA,CAAA;;0BAOb,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;;0BACxC,QAAQ;;0BACR,MAAM;2BAAC,gCAAgC,CAAA;yCAoBtC,KAAK,EAAA,CAAA;sBADR,KAAK;gBAYF,KAAK,EAAA,CAAA;sBADR,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAA;gBAY/B,WAAW,EAAA,CAAA;sBADd,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAA;gBAehB,YAAY,EAAA,CAAA;sBAA9B,MAAM;gBAUH,IAAI,EAAA,CAAA;sBADP,KAAK;;AAuDR;AACA,SAAS,KAAK,CAAC,CAAS,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,EAAA;AAC1C,IAAA,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AACzC;;ME3Na,oBAAoB,CAAA;8GAApB,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAApB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,EAHrB,OAAA,EAAA,CAAA,cAAc,CACd,EAAA,OAAA,EAAA,CAAA,cAAc,EAAE,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;AAE9B,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,YAFL,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAE9B,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAJhC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,cAAc,CAAC;AACzB,oBAAA,OAAO,EAAE,CAAC,cAAc,EAAE,eAAe,CAAC;AAC3C,iBAAA,CAAA;;;ACfD;;AAEG;;;;"}