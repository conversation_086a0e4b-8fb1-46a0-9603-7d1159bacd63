/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export class AsyncStackTaggingZoneSpec {
    constructor(namePrefix, consoleAsyncStackTaggingImpl = console) {
        this.name = 'asyncStackTagging for ' + namePrefix;
        this.createTask = consoleAsyncStackTaggingImpl?.createTask ?? (() => null);
    }
    onScheduleTask(delegate, _current, target, task) {
        task.consoleTask = this.createTask(`Zone - ${task.source || task.type}`);
        return delegate.scheduleTask(target, task);
    }
    onInvokeTask(delegate, _currentZone, targetZone, task, applyThis, applyArgs) {
        let ret;
        if (task.consoleTask) {
            ret = task.consoleTask.run(() => delegate.invokeTask(targetZone, task, applyThis, applyArgs));
        }
        else {
            ret = delegate.invokeTask(targetZone, task, applyThis, applyArgs);
        }
        return ret;
    }
}
//# sourceMappingURL=data:application/json;base64,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