/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/** Parses a CSS time value to milliseconds. */
function parseCssTimeUnitsToMs(value) {
    // Some browsers will return it in seconds, whereas others will return milliseconds.
    const multiplier = value.toLowerCase().indexOf('ms') > -1 ? 1 : 1000;
    return parseFloat(value) * multiplier;
}
/** Gets the transform transition duration, including the delay, of an element in milliseconds. */
export function getTransformTransitionDurationInMs(element) {
    const computedStyle = getComputedStyle(element);
    const transitionedProperties = parseCssPropertyValue(computedStyle, 'transition-property');
    const property = transitionedProperties.find(prop => prop === 'transform' || prop === 'all');
    // If there's no transition for `all` or `transform`, we shouldn't do anything.
    if (!property) {
        return 0;
    }
    // Get the index of the property that we're interested in and match
    // it up to the same index in `transition-delay` and `transition-duration`.
    const propertyIndex = transitionedProperties.indexOf(property);
    const rawDurations = parseCssPropertyValue(computedStyle, 'transition-duration');
    const rawDelays = parseCssPropertyValue(computedStyle, 'transition-delay');
    return (parseCssTimeUnitsToMs(rawDurations[propertyIndex]) +
        parseCssTimeUnitsToMs(rawDelays[propertyIndex]));
}
/** Parses out multiple values from a computed style into an array. */
function parseCssPropertyValue(computedStyle, name) {
    const value = computedStyle.getPropertyValue(name);
    return value.split(',').map(part => part.trim());
}
//# sourceMappingURL=data:application/json;base64,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