/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import * as o from '../output/output_ast';
import { Identifiers as R3 } from './r3_identifiers';
import { devOnlyGuardedExpression } from './util';
export function compileClassMetadata(metadata) {
    // Generate an ngDevMode guarded call to setClassMetadata with the class identifier and its
    // metadata.
    const fnCall = o.importExpr(R3.setClassMetadata).callFn([
        metadata.type,
        metadata.decorators,
        metadata.ctorParameters ?? o.literal(null),
        metadata.propDecorators ?? o.literal(null),
    ]);
    const iife = o.arrowFn([], [devOnlyGuardedExpression(fnCall).toStmt()]);
    return iife.callFn([]);
}
/**
 * Wraps the `setClassMetadata` function with extra logic that dynamically
 * loads dependencies from `@defer` blocks.
 *
 * Generates a call like this:
 * ```
 * setClassMetadataAsync(type, () => [
 *   import('./cmp-a').then(m => m.CmpA);
 *   import('./cmp-b').then(m => m.CmpB);
 * ], (CmpA, CmpB) => {
 *   setClassMetadata(type, decorators, ctorParameters, propParameters);
 * });
 * ```
 *
 * Similar to the `setClassMetadata` call, it's wrapped into the `ngDevMode`
 * check to tree-shake away this code in production mode.
 */
export function compileComponentClassMetadata(metadata, deferrableTypes) {
    if (deferrableTypes === null || deferrableTypes.size === 0) {
        // If there are no deferrable symbols - just generate a regular `setClassMetadata` call.
        return compileClassMetadata(metadata);
    }
    const dynamicImports = [];
    const importedSymbols = [];
    for (const [symbolName, { importPath, isDefaultImport }] of deferrableTypes) {
        // e.g. `(m) => m.CmpA`
        const innerFn = 
        // Default imports are always accessed through the `default` property.
        o.arrowFn([new o.FnParam('m', o.DYNAMIC_TYPE)], o.variable('m').prop(isDefaultImport ? 'default' : symbolName));
        // e.g. `import('./cmp-a').then(...)`
        const importExpr = (new o.DynamicImportExpr(importPath)).prop('then').callFn([innerFn]);
        dynamicImports.push(importExpr);
        importedSymbols.push(new o.FnParam(symbolName, o.DYNAMIC_TYPE));
    }
    // e.g. `() => [ ... ];`
    const dependencyLoadingFn = o.arrowFn([], o.literalArr(dynamicImports));
    // e.g. `setClassMetadata(...)`
    const setClassMetadataCall = o.importExpr(R3.setClassMetadata).callFn([
        metadata.type,
        metadata.decorators,
        metadata.ctorParameters ?? o.literal(null),
        metadata.propDecorators ?? o.literal(null),
    ]);
    // e.g. `(CmpA) => setClassMetadata(...)`
    const setClassMetaWrapper = o.arrowFn(importedSymbols, [setClassMetadataCall.toStmt()]);
    // Final `setClassMetadataAsync()` call with all arguments
    const setClassMetaAsync = o.importExpr(R3.setClassMetadataAsync).callFn([
        metadata.type, dependencyLoadingFn, setClassMetaWrapper
    ]);
    // Generate an ngDevMode guarded call to `setClassMetadataAsync` with
    // the class identifier and its metadata, so that this call can be tree-shaken.
    const iife = o.arrowFn([], [devOnlyGuardedExpression(setClassMetaAsync).toStmt()]);
    return iife.callFn([]);
}
//# sourceMappingURL=data:application/json;base64,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