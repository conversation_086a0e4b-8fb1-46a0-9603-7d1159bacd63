/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val, i = Math.floor(Math.abs(val));
    if (i === 0 || n === 1)
        return 1;
    return 5;
}
export default ["as", [["পূৰ্বাহ্ন", "অপৰাহ্ন"], u, u], u, [["দ", "স", "ম", "ব", "ব", "শ", "শ"], ["দেও", "সোম", "মঙ্গল", "বুধ", "বৃহ", "শুক্ৰ", "শনি"], ["দেওবাৰ", "সোমবাৰ", "মঙ্গলবাৰ", "বুধবাৰ", "বৃহস্পতিবাৰ", "শুক্ৰবাৰ", "শনিবাৰ"], ["দেও", "সোম", "মঙ্গল", "বুধ", "বৃহ", "শুক্ৰ", "শনি"]], u, [["জ", "ফ", "ম", "এ", "ম", "জ", "জ", "আ", "ছ", "অ", "ন", "ড"], ["জানু", "ফেব্ৰু", "মাৰ্চ", "এপ্ৰিল", "মে’", "জুন", "জুলাই", "আগ", "ছেপ্তে", "অক্টো", "নৱে", "ডিচে"], ["জানুৱাৰী", "ফেব্ৰুৱাৰী", "মাৰ্চ", "এপ্ৰিল", "মে’", "জুন", "জুলাই", "আগষ্ট", "ছেপ্তেম্বৰ", "অক্টোবৰ", "নৱেম্বৰ", "ডিচেম্বৰ"]], u, [["খ্ৰীঃ পূঃ", "খ্ৰীঃ"], u, ["খ্ৰীষ্টপূৰ্ব", "খ্ৰীষ্টাব্দ"]], 0, [0, 0], ["d-M-y", "dd-MM-y", "d MMMM, y", "EEEE, d MMMM, y"], ["a h.mm", "a h.mm.ss", "a h.mm.ss z", "a h.mm.ss zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##,##0.###", "#,##,##0%", "¤ #,##,##0.00", "#E0"], "INR", "₹", "ভাৰতীয় ৰুপী", { "BYN": [u, "р."], "JPY": ["JP¥", "¥"], "PHP": [u, "₱"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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