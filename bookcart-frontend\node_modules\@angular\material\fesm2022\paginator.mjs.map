{"version": 3, "file": "paginator.mjs", "sources": ["../../../../../../src/material/paginator/paginator-intl.ts", "../../../../../../src/material/paginator/paginator.ts", "../../../../../../src/material/paginator/paginator.html", "../../../../../../src/material/paginator/module.ts", "../../../../../../src/material/paginator/paginator_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injectable, Optional, SkipSelf} from '@angular/core';\nimport {Subject} from 'rxjs';\n\n/**\n * To modify the labels and text displayed, create a new instance of MatPaginatorIntl and\n * include it in a custom provider\n */\n@Injectable({providedIn: 'root'})\nexport class MatPaginatorIntl {\n  /**\n   * Stream to emit from when labels are changed. Use this to notify components when the labels have\n   * changed after initialization.\n   */\n  readonly changes: Subject<void> = new Subject<void>();\n\n  /** A label for the page size selector. */\n  itemsPerPageLabel: string = 'Items per page:';\n\n  /** A label for the button that increments the current page. */\n  nextPageLabel: string = 'Next page';\n\n  /** A label for the button that decrements the current page. */\n  previousPageLabel: string = 'Previous page';\n\n  /** A label for the button that moves to the first page. */\n  firstPageLabel: string = 'First page';\n\n  /** A label for the button that moves to the last page. */\n  lastPageLabel: string = 'Last page';\n\n  /** A label for the range of items within the current page and the length of the whole list. */\n  getRangeLabel: (page: number, pageSize: number, length: number) => string = (\n    page: number,\n    pageSize: number,\n    length: number,\n  ) => {\n    if (length == 0 || pageSize == 0) {\n      return `0 of ${length}`;\n    }\n\n    length = Math.max(length, 0);\n\n    const startIndex = page * pageSize;\n\n    // If the start index exceeds the list length, do not try and fix the end index to the end.\n    const endIndex =\n      startIndex < length ? Math.min(startIndex + pageSize, length) : startIndex + pageSize;\n\n    return `${startIndex + 1} – ${endIndex} of ${length}`;\n  };\n}\n\n/** @docs-private */\nexport function MAT_PAGINATOR_INTL_PROVIDER_FACTORY(parentIntl: MatPaginatorIntl) {\n  return parentIntl || new MatPaginatorIntl();\n}\n\n/** @docs-private */\nexport const MAT_PAGINATOR_INTL_PROVIDER = {\n  // If there is already an MatPaginatorIntl available, use that. Otherwise, provide a new one.\n  provide: MatPaginatorIntl,\n  deps: [[new Optional(), new SkipSelf(), MatPaginatorIntl]],\n  useFactory: MAT_PAGINATOR_INTL_PROVIDER_FACTORY,\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  EventEmitter,\n  Inject,\n  InjectionToken,\n  Input,\n  OnDestroy,\n  OnInit,\n  Optional,\n  Output,\n  ViewEncapsulation,\n  booleanAttribute,\n  numberAttribute,\n} from '@angular/core';\nimport {MatOption, ThemePalette} from '@angular/material/core';\nimport {MatSelect} from '@angular/material/select';\nimport {MatIconButton} from '@angular/material/button';\nimport {MatTooltip} from '@angular/material/tooltip';\nimport {MatFormField, MatFormFieldAppearance} from '@angular/material/form-field';\nimport {Observable, ReplaySubject, Subscription} from 'rxjs';\nimport {MatPaginatorIntl} from './paginator-intl';\n\n/** The default page size if there is no page size and there are no provided page size options. */\nconst DEFAULT_PAGE_SIZE = 50;\n\n/** Object that can used to configure the underlying `MatSelect` inside a `MatPaginator`. */\nexport interface MatPaginatorSelectConfig {\n  /** Whether to center the active option over the trigger. */\n  disableOptionCentering?: boolean;\n\n  /** Classes to be passed to the select panel. */\n  panelClass?: string | string[] | Set<string> | {[key: string]: any};\n}\n\n/**\n * Change event object that is emitted when the user selects a\n * different page size or navigates to another page.\n */\nexport class PageEvent {\n  /** The current page index. */\n  pageIndex: number;\n\n  /**\n   * Index of the page that was selected previously.\n   * @breaking-change 8.0.0 To be made into a required property.\n   */\n  previousPageIndex?: number;\n\n  /** The current page size. */\n  pageSize: number;\n\n  /** The current total number of items being paged. */\n  length: number;\n}\n\n// Note that while `MatPaginatorDefaultOptions` and `MAT_PAGINATOR_DEFAULT_OPTIONS` are identical\n// between the MDC and non-MDC versions, we have to duplicate them, because the type of\n// `formFieldAppearance` is narrower in the MDC version.\n\n/** Object that can be used to configure the default options for the paginator module. */\nexport interface MatPaginatorDefaultOptions {\n  /** Number of items to display on a page. By default set to 50. */\n  pageSize?: number;\n\n  /** The set of provided page size options to display to the user. */\n  pageSizeOptions?: number[];\n\n  /** Whether to hide the page size selection UI from the user. */\n  hidePageSize?: boolean;\n\n  /** Whether to show the first/last buttons UI to the user. */\n  showFirstLastButtons?: boolean;\n\n  /** The default form-field appearance to apply to the page size options selector. */\n  formFieldAppearance?: MatFormFieldAppearance;\n}\n\n/** Injection token that can be used to provide the default options for the paginator module. */\nexport const MAT_PAGINATOR_DEFAULT_OPTIONS = new InjectionToken<MatPaginatorDefaultOptions>(\n  'MAT_PAGINATOR_DEFAULT_OPTIONS',\n);\n\nlet nextUniqueId = 0;\n\n/**\n * Component to provide navigation between paged information. Displays the size of the current\n * page, user-selectable options to change that size, what items are being shown, and\n * navigational button to go to the previous or next page.\n */\n@Component({\n  selector: 'mat-paginator',\n  exportAs: 'matPaginator',\n  templateUrl: 'paginator.html',\n  styleUrl: 'paginator.css',\n  host: {\n    'class': 'mat-mdc-paginator',\n    'role': 'group',\n  },\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  standalone: true,\n  imports: [MatFormField, MatSelect, MatOption, MatIconButton, MatTooltip],\n})\nexport class MatPaginator implements OnInit, OnDestroy {\n  /** If set, styles the \"page size\" form field with the designated style. */\n  _formFieldAppearance?: MatFormFieldAppearance;\n\n  /** ID for the DOM node containing the paginator's items per page label. */\n  readonly _pageSizeLabelId = `mat-paginator-page-size-label-${nextUniqueId++}`;\n\n  private _intlChanges: Subscription;\n  private _isInitialized = false;\n  private _initializedStream = new ReplaySubject<void>(1);\n\n  /** Theme color to be used for the underlying form controls. */\n  @Input() color: ThemePalette;\n\n  /** The zero-based page index of the displayed list of items. Defaulted to 0. */\n  @Input({transform: numberAttribute})\n  get pageIndex(): number {\n    return this._pageIndex;\n  }\n  set pageIndex(value: number) {\n    this._pageIndex = Math.max(value || 0, 0);\n    this._changeDetectorRef.markForCheck();\n  }\n  private _pageIndex = 0;\n\n  /** The length of the total number of items that are being paginated. Defaulted to 0. */\n  @Input({transform: numberAttribute})\n  get length(): number {\n    return this._length;\n  }\n  set length(value: number) {\n    this._length = value || 0;\n    this._changeDetectorRef.markForCheck();\n  }\n  private _length = 0;\n\n  /** Number of items to display on a page. By default set to 50. */\n  @Input({transform: numberAttribute})\n  get pageSize(): number {\n    return this._pageSize;\n  }\n  set pageSize(value: number) {\n    this._pageSize = Math.max(value || 0, 0);\n    this._updateDisplayedPageSizeOptions();\n  }\n  private _pageSize: number;\n\n  /** The set of provided page size options to display to the user. */\n  @Input()\n  get pageSizeOptions(): number[] {\n    return this._pageSizeOptions;\n  }\n  set pageSizeOptions(value: number[] | readonly number[]) {\n    this._pageSizeOptions = (value || ([] as number[])).map(p => numberAttribute(p, 0));\n    this._updateDisplayedPageSizeOptions();\n  }\n  private _pageSizeOptions: number[] = [];\n\n  /** Whether to hide the page size selection UI from the user. */\n  @Input({transform: booleanAttribute})\n  hidePageSize: boolean = false;\n\n  /** Whether to show the first/last buttons UI to the user. */\n  @Input({transform: booleanAttribute})\n  showFirstLastButtons: boolean = false;\n\n  /** Used to configure the underlying `MatSelect` inside the paginator. */\n  @Input() selectConfig: MatPaginatorSelectConfig = {};\n\n  /** Whether the paginator is disabled. */\n  @Input({transform: booleanAttribute})\n  disabled: boolean = false;\n\n  /** Event emitted when the paginator changes the page size or page index. */\n  @Output() readonly page: EventEmitter<PageEvent> = new EventEmitter<PageEvent>();\n\n  /** Displayed set of page size options. Will be sorted and include current page size. */\n  _displayedPageSizeOptions: number[];\n\n  /** Emits when the paginator is initialized. */\n  initialized: Observable<void> = this._initializedStream;\n\n  constructor(\n    public _intl: MatPaginatorIntl,\n    private _changeDetectorRef: ChangeDetectorRef,\n    @Optional() @Inject(MAT_PAGINATOR_DEFAULT_OPTIONS) defaults?: MatPaginatorDefaultOptions,\n  ) {\n    this._intlChanges = _intl.changes.subscribe(() => this._changeDetectorRef.markForCheck());\n\n    if (defaults) {\n      const {pageSize, pageSizeOptions, hidePageSize, showFirstLastButtons} = defaults;\n\n      if (pageSize != null) {\n        this._pageSize = pageSize;\n      }\n\n      if (pageSizeOptions != null) {\n        this._pageSizeOptions = pageSizeOptions;\n      }\n\n      if (hidePageSize != null) {\n        this.hidePageSize = hidePageSize;\n      }\n\n      if (showFirstLastButtons != null) {\n        this.showFirstLastButtons = showFirstLastButtons;\n      }\n    }\n\n    this._formFieldAppearance = defaults?.formFieldAppearance || 'outline';\n  }\n\n  ngOnInit() {\n    this._isInitialized = true;\n    this._updateDisplayedPageSizeOptions();\n    this._initializedStream.next();\n  }\n\n  ngOnDestroy() {\n    this._initializedStream.complete();\n    this._intlChanges.unsubscribe();\n  }\n\n  /** Advances to the next page if it exists. */\n  nextPage(): void {\n    if (!this.hasNextPage()) {\n      return;\n    }\n\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = this.pageIndex + 1;\n    this._emitPageEvent(previousPageIndex);\n  }\n\n  /** Move back to the previous page if it exists. */\n  previousPage(): void {\n    if (!this.hasPreviousPage()) {\n      return;\n    }\n\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = this.pageIndex - 1;\n    this._emitPageEvent(previousPageIndex);\n  }\n\n  /** Move to the first page if not already there. */\n  firstPage(): void {\n    // hasPreviousPage being false implies at the start\n    if (!this.hasPreviousPage()) {\n      return;\n    }\n\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = 0;\n    this._emitPageEvent(previousPageIndex);\n  }\n\n  /** Move to the last page if not already there. */\n  lastPage(): void {\n    // hasNextPage being false implies at the end\n    if (!this.hasNextPage()) {\n      return;\n    }\n\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = this.getNumberOfPages() - 1;\n    this._emitPageEvent(previousPageIndex);\n  }\n\n  /** Whether there is a previous page. */\n  hasPreviousPage(): boolean {\n    return this.pageIndex >= 1 && this.pageSize != 0;\n  }\n\n  /** Whether there is a next page. */\n  hasNextPage(): boolean {\n    const maxPageIndex = this.getNumberOfPages() - 1;\n    return this.pageIndex < maxPageIndex && this.pageSize != 0;\n  }\n\n  /** Calculate the number of pages */\n  getNumberOfPages(): number {\n    if (!this.pageSize) {\n      return 0;\n    }\n\n    return Math.ceil(this.length / this.pageSize);\n  }\n\n  /**\n   * Changes the page size so that the first item displayed on the page will still be\n   * displayed using the new page size.\n   *\n   * For example, if the page size is 10 and on the second page (items indexed 10-19) then\n   * switching so that the page size is 5 will set the third page as the current page so\n   * that the 10th item will still be displayed.\n   */\n  _changePageSize(pageSize: number) {\n    // Current page needs to be updated to reflect the new page size. Navigate to the page\n    // containing the previous page's first item.\n    const startIndex = this.pageIndex * this.pageSize;\n    const previousPageIndex = this.pageIndex;\n\n    this.pageIndex = Math.floor(startIndex / pageSize) || 0;\n    this.pageSize = pageSize;\n    this._emitPageEvent(previousPageIndex);\n  }\n\n  /** Checks whether the buttons for going forwards should be disabled. */\n  _nextButtonsDisabled() {\n    return this.disabled || !this.hasNextPage();\n  }\n\n  /** Checks whether the buttons for going backwards should be disabled. */\n  _previousButtonsDisabled() {\n    return this.disabled || !this.hasPreviousPage();\n  }\n\n  /**\n   * Updates the list of page size options to display to the user. Includes making sure that\n   * the page size is an option and that the list is sorted.\n   */\n  private _updateDisplayedPageSizeOptions() {\n    if (!this._isInitialized) {\n      return;\n    }\n\n    // If no page size is provided, use the first page size option or the default page size.\n    if (!this.pageSize) {\n      this._pageSize =\n        this.pageSizeOptions.length != 0 ? this.pageSizeOptions[0] : DEFAULT_PAGE_SIZE;\n    }\n\n    this._displayedPageSizeOptions = this.pageSizeOptions.slice();\n\n    if (this._displayedPageSizeOptions.indexOf(this.pageSize) === -1) {\n      this._displayedPageSizeOptions.push(this.pageSize);\n    }\n\n    // Sort the numbers using a number-specific sort function.\n    this._displayedPageSizeOptions.sort((a, b) => a - b);\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /** Emits an event notifying that a change of the paginator's properties has been triggered. */\n  private _emitPageEvent(previousPageIndex: number) {\n    this.page.emit({\n      previousPageIndex,\n      pageIndex: this.pageIndex,\n      pageSize: this.pageSize,\n      length: this.length,\n    });\n  }\n}\n", "<div class=\"mat-mdc-paginator-outer-container\">\n  <div class=\"mat-mdc-paginator-container\">\n    @if (!hidePageSize) {\n      <div class=\"mat-mdc-paginator-page-size\">\n        <div class=\"mat-mdc-paginator-page-size-label\" [attr.id]=\"_pageSizeLabelId\">\n          {{_intl.itemsPerPageLabel}}\n        </div>\n\n        @if (_displayedPageSizeOptions.length > 1) {\n          <mat-form-field\n            [appearance]=\"_formFieldAppearance!\"\n            [color]=\"color\"\n            class=\"mat-mdc-paginator-page-size-select\">\n            <mat-select\n              [value]=\"pageSize\"\n              [disabled]=\"disabled\"\n              [aria-labelledby]=\"_pageSizeLabelId\"\n              [panelClass]=\"selectConfig.panelClass || ''\"\n              [disableOptionCentering]=\"selectConfig.disableOptionCentering\"\n              (selectionChange)=\"_changePageSize($event.value)\"\n              hideSingleSelectionIndicator>\n              @for (pageSizeOption of _displayedPageSizeOptions; track pageSizeOption) {\n                <mat-option [value]=\"pageSizeOption\">\n                  {{pageSizeOption}}\n                </mat-option>\n              }\n            </mat-select>\n          </mat-form-field>\n        }\n\n        @if (_displayedPageSizeOptions.length <= 1) {\n          <div class=\"mat-mdc-paginator-page-size-value\">{{pageSize}}</div>\n        }\n      </div>\n    }\n\n    <div class=\"mat-mdc-paginator-range-actions\">\n      <div class=\"mat-mdc-paginator-range-label\" aria-live=\"polite\">\n        {{_intl.getRangeLabel(pageIndex, pageSize, length)}}\n      </div>\n\n      @if (showFirstLastButtons) {\n        <button mat-icon-button type=\"button\"\n                class=\"mat-mdc-paginator-navigation-first\"\n                (click)=\"firstPage()\"\n                [attr.aria-label]=\"_intl.firstPageLabel\"\n                [matTooltip]=\"_intl.firstPageLabel\"\n                [matTooltipDisabled]=\"_previousButtonsDisabled()\"\n                [matTooltipPosition]=\"'above'\"\n                [disabled]=\"_previousButtonsDisabled()\">\n          <svg class=\"mat-mdc-paginator-icon\"\n              viewBox=\"0 0 24 24\"\n              focusable=\"false\"\n              aria-hidden=\"true\">\n            <path d=\"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\"/>\n          </svg>\n        </button>\n      }\n      <button mat-icon-button type=\"button\"\n              class=\"mat-mdc-paginator-navigation-previous\"\n              (click)=\"previousPage()\"\n              [attr.aria-label]=\"_intl.previousPageLabel\"\n              [matTooltip]=\"_intl.previousPageLabel\"\n              [matTooltipDisabled]=\"_previousButtonsDisabled()\"\n              [matTooltipPosition]=\"'above'\"\n              [disabled]=\"_previousButtonsDisabled()\">\n        <svg class=\"mat-mdc-paginator-icon\"\n             viewBox=\"0 0 24 24\"\n             focusable=\"false\"\n             aria-hidden=\"true\">\n          <path d=\"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\"/>\n        </svg>\n      </button>\n      <button mat-icon-button type=\"button\"\n              class=\"mat-mdc-paginator-navigation-next\"\n              (click)=\"nextPage()\"\n              [attr.aria-label]=\"_intl.nextPageLabel\"\n              [matTooltip]=\"_intl.nextPageLabel\"\n              [matTooltipDisabled]=\"_nextButtonsDisabled()\"\n              [matTooltipPosition]=\"'above'\"\n              [disabled]=\"_nextButtonsDisabled()\">\n        <svg class=\"mat-mdc-paginator-icon\"\n             viewBox=\"0 0 24 24\"\n             focusable=\"false\"\n             aria-hidden=\"true\">\n          <path d=\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"/>\n        </svg>\n      </button>\n      @if (showFirstLastButtons) {\n        <button mat-icon-button type=\"button\"\n                class=\"mat-mdc-paginator-navigation-last\"\n                (click)=\"lastPage()\"\n                [attr.aria-label]=\"_intl.lastPageLabel\"\n                [matTooltip]=\"_intl.lastPageLabel\"\n                [matTooltipDisabled]=\"_nextButtonsDisabled()\"\n                [matTooltipPosition]=\"'above'\"\n                [disabled]=\"_nextButtonsDisabled()\">\n          <svg class=\"mat-mdc-paginator-icon\"\n              viewBox=\"0 0 24 24\"\n              focusable=\"false\"\n              aria-hidden=\"true\">\n            <path d=\"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\"/>\n          </svg>\n        </button>\n      }\n    </div>\n  </div>\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MAT_PAGINATOR_INTL_PROVIDER} from './paginator-intl';\nimport {MatButtonModule} from '@angular/material/button';\nimport {MatSelectModule} from '@angular/material/select';\nimport {MatTooltipModule} from '@angular/material/tooltip';\nimport {MatPaginator} from './paginator';\n\n@NgModule({\n  imports: [MatButtonModule, MatSelectModule, MatTooltipModule, MatPaginator],\n  exports: [MatPaginator],\n  providers: [MAT_PAGINATOR_INTL_PROVIDER],\n})\nexport class MatPaginatorModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;;;;;AAWA;;;AAGG;MAEU,gBAAgB,CAAA;AAD7B,IAAA,WAAA,GAAA;AAEE;;;AAGG;AACM,QAAA,IAAA,CAAA,OAAO,GAAkB,IAAI,OAAO,EAAQ,CAAC;;QAGtD,IAAiB,CAAA,iBAAA,GAAW,iBAAiB,CAAC;;QAG9C,IAAa,CAAA,aAAA,GAAW,WAAW,CAAC;;QAGpC,IAAiB,CAAA,iBAAA,GAAW,eAAe,CAAC;;QAG5C,IAAc,CAAA,cAAA,GAAW,YAAY,CAAC;;QAGtC,IAAa,CAAA,aAAA,GAAW,WAAW,CAAC;;QAGpC,IAAa,CAAA,aAAA,GAA+D,CAC1E,IAAY,EACZ,QAAgB,EAChB,MAAc,KACZ;YACF,IAAI,MAAM,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,EAAE;gBAChC,OAAO,CAAA,KAAA,EAAQ,MAAM,CAAA,CAAE,CAAC;aACzB;YAED,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AAE7B,YAAA,MAAM,UAAU,GAAG,IAAI,GAAG,QAAQ,CAAC;;YAGnC,MAAM,QAAQ,GACZ,UAAU,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,QAAQ,EAAE,MAAM,CAAC,GAAG,UAAU,GAAG,QAAQ,CAAC;YAExF,OAAO,CAAA,EAAG,UAAU,GAAG,CAAC,MAAM,QAAQ,CAAA,IAAA,EAAO,MAAM,CAAA,CAAE,CAAC;AACxD,SAAC,CAAC;AACH,KAAA;8GA1CY,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAAhB,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,cADJ,MAAM,EAAA,CAAA,CAAA,EAAA;;2FAClB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAD5B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;AA6ChC;AACM,SAAU,mCAAmC,CAAC,UAA4B,EAAA;AAC9E,IAAA,OAAO,UAAU,IAAI,IAAI,gBAAgB,EAAE,CAAC;AAC9C,CAAC;AAED;AACa,MAAA,2BAA2B,GAAG;;AAEzC,IAAA,OAAO,EAAE,gBAAgB;AACzB,IAAA,IAAI,EAAE,CAAC,CAAC,IAAI,QAAQ,EAAE,EAAE,IAAI,QAAQ,EAAE,EAAE,gBAAgB,CAAC,CAAC;AAC1D,IAAA,UAAU,EAAE,mCAAmC;;;ACtCjD;AACA,MAAM,iBAAiB,GAAG,EAAE,CAAC;AAW7B;;;AAGG;MACU,SAAS,CAAA;AAerB,CAAA;AAwBD;MACa,6BAA6B,GAAG,IAAI,cAAc,CAC7D,+BAA+B,EAC/B;AAEF,IAAI,YAAY,GAAG,CAAC,CAAC;AAErB;;;;AAIG;MAeU,YAAY,CAAA;;AAevB,IAAA,IACI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;KACxB;IACD,IAAI,SAAS,CAAC,KAAa,EAAA;AACzB,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAC1C,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;KACxC;;AAID,IAAA,IACI,MAAM,GAAA;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;IACD,IAAI,MAAM,CAAC,KAAa,EAAA;AACtB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,IAAI,CAAC,CAAC;AAC1B,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;KACxC;;AAID,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,KAAa,EAAA;AACxB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QACzC,IAAI,CAAC,+BAA+B,EAAE,CAAC;KACxC;;AAID,IAAA,IACI,eAAe,GAAA;QACjB,OAAO,IAAI,CAAC,gBAAgB,CAAC;KAC9B;IACD,IAAI,eAAe,CAAC,KAAmC,EAAA;QACrD,IAAI,CAAC,gBAAgB,GAAG,CAAC,KAAK,IAAK,EAAe,EAAE,GAAG,CAAC,CAAC,IAAI,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACpF,IAAI,CAAC,+BAA+B,EAAE,CAAC;KACxC;AA2BD,IAAA,WAAA,CACS,KAAuB,EACtB,kBAAqC,EACM,QAAqC,EAAA;QAFjF,IAAK,CAAA,KAAA,GAAL,KAAK,CAAkB;QACtB,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAmB;;AA/EtC,QAAA,IAAA,CAAA,gBAAgB,GAAG,CAAA,8BAAA,EAAiC,YAAY,EAAE,EAAE,CAAC;QAGtE,IAAc,CAAA,cAAA,GAAG,KAAK,CAAC;AACvB,QAAA,IAAA,CAAA,kBAAkB,GAAG,IAAI,aAAa,CAAO,CAAC,CAAC,CAAC;QAchD,IAAU,CAAA,UAAA,GAAG,CAAC,CAAC;QAWf,IAAO,CAAA,OAAA,GAAG,CAAC,CAAC;QAsBZ,IAAgB,CAAA,gBAAA,GAAa,EAAE,CAAC;;QAIxC,IAAY,CAAA,YAAA,GAAY,KAAK,CAAC;;QAI9B,IAAoB,CAAA,oBAAA,GAAY,KAAK,CAAC;;QAG7B,IAAY,CAAA,YAAA,GAA6B,EAAE,CAAC;;QAIrD,IAAQ,CAAA,QAAA,GAAY,KAAK,CAAC;;AAGP,QAAA,IAAA,CAAA,IAAI,GAA4B,IAAI,YAAY,EAAa,CAAC;;AAMjF,QAAA,IAAA,CAAA,WAAW,GAAqB,IAAI,CAAC,kBAAkB,CAAC;AAOtD,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC,CAAC;QAE1F,IAAI,QAAQ,EAAE;YACZ,MAAM,EAAC,QAAQ,EAAE,eAAe,EAAE,YAAY,EAAE,oBAAoB,EAAC,GAAG,QAAQ,CAAC;AAEjF,YAAA,IAAI,QAAQ,IAAI,IAAI,EAAE;AACpB,gBAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;aAC3B;AAED,YAAA,IAAI,eAAe,IAAI,IAAI,EAAE;AAC3B,gBAAA,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;aACzC;AAED,YAAA,IAAI,YAAY,IAAI,IAAI,EAAE;AACxB,gBAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;aAClC;AAED,YAAA,IAAI,oBAAoB,IAAI,IAAI,EAAE;AAChC,gBAAA,IAAI,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;aAClD;SACF;QAED,IAAI,CAAC,oBAAoB,GAAG,QAAQ,EAAE,mBAAmB,IAAI,SAAS,CAAC;KACxE;IAED,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,+BAA+B,EAAE,CAAC;AACvC,QAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;KAChC;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC;AACnC,QAAA,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;KACjC;;IAGD,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;YACvB,OAAO;SACR;AAED,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC;QACzC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;AACpC,QAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;KACxC;;IAGD,YAAY,GAAA;AACV,QAAA,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE;YAC3B,OAAO;SACR;AAED,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC;QACzC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;AACpC,QAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;KACxC;;IAGD,SAAS,GAAA;;AAEP,QAAA,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE;YAC3B,OAAO;SACR;AAED,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC;AACzC,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;AACnB,QAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;KACxC;;IAGD,QAAQ,GAAA;;AAEN,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;YACvB,OAAO;SACR;AAED,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC;QACzC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;AAC7C,QAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;KACxC;;IAGD,eAAe,GAAA;QACb,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;KAClD;;IAGD,WAAW,GAAA;QACT,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;QACjD,OAAO,IAAI,CAAC,SAAS,GAAG,YAAY,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;KAC5D;;IAGD,gBAAgB,GAAA;AACd,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,OAAO,CAAC,CAAC;SACV;AAED,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;KAC/C;AAED;;;;;;;AAOG;AACH,IAAA,eAAe,CAAC,QAAgB,EAAA;;;QAG9B,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;AAClD,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC;AAEzC,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;AACxD,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,QAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;KACxC;;IAGD,oBAAoB,GAAA;QAClB,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;KAC7C;;IAGD,wBAAwB,GAAA;QACtB,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;KACjD;AAED;;;AAGG;IACK,+BAA+B,GAAA;AACrC,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,OAAO;SACR;;AAGD,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,IAAI,CAAC,SAAS;AACZ,gBAAA,IAAI,CAAC,eAAe,CAAC,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,iBAAiB,CAAC;SAClF;QAED,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;AAE9D,QAAA,IAAI,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;YAChE,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACpD;;AAGD,QAAA,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AACrD,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;KACxC;;AAGO,IAAA,cAAc,CAAC,iBAAyB,EAAA;AAC9C,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YACb,iBAAiB;YACjB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,MAAM,EAAE,IAAI,CAAC,MAAM;AACpB,SAAA,CAAC,CAAC;KACJ;AA5PU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,gFAqFD,6BAA6B,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGArFxC,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAeJ,eAAe,CAWf,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAAA,eAAe,sCAWf,eAAe,CAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAsBf,gBAAgB,CAAA,EAAA,oBAAA,EAAA,CAAA,sBAAA,EAAA,sBAAA,EAIhB,gBAAgB,CAAA,EAAA,YAAA,EAAA,cAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAOhB,gBAAgB,CCvLrC,EAAA,EAAA,OAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,OAAA,EAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,w9IA4GA,EDGY,MAAA,EAAA,CAAA,o+DAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,YAAY,EAAE,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,CAAA,oBAAA,EAAA,OAAA,EAAA,YAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,WAAA,CAAA,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,SAAS,weAAE,SAAS,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,IAAA,EAAA,UAAA,CAAA,EAAA,OAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,aAAa,EAAA,QAAA,EAAA,yBAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,UAAU,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,oBAAA,EAAA,4BAAA,EAAA,oBAAA,EAAA,qBAAA,EAAA,qBAAA,EAAA,yBAAA,EAAA,YAAA,EAAA,iBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAE5D,YAAY,EAAA,UAAA,EAAA,CAAA;kBAdxB,SAAS;+BACE,eAAe,EAAA,QAAA,EACf,cAAc,EAGlB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,mBAAmB;AAC5B,wBAAA,MAAM,EAAE,OAAO;qBAChB,EACgB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,aAAA,EAChC,iBAAiB,CAAC,IAAI,cACzB,IAAI,EAAA,OAAA,EACP,CAAC,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa,EAAE,UAAU,CAAC,EAAA,QAAA,EAAA,w9IAAA,EAAA,MAAA,EAAA,CAAA,o+DAAA,CAAA,EAAA,CAAA;;0BAuFrE,QAAQ;;0BAAI,MAAM;2BAAC,6BAA6B,CAAA;yCAzE1C,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAIF,SAAS,EAAA,CAAA;sBADZ,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAA;gBAY/B,MAAM,EAAA,CAAA;sBADT,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAA;gBAY/B,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAA;gBAY/B,eAAe,EAAA,CAAA;sBADlB,KAAK;gBAYN,YAAY,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAKpC,oBAAoB,EAAA,CAAA;sBADnB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAI3B,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAIN,QAAQ,EAAA,CAAA;sBADP,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAIjB,IAAI,EAAA,CAAA;sBAAtB,MAAM;;;MEvKI,kBAAkB,CAAA;8GAAlB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;+GAAlB,kBAAkB,EAAA,OAAA,EAAA,CAJnB,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE,YAAY,CAAA,EAAA,OAAA,EAAA,CAChE,YAAY,CAAA,EAAA,CAAA,CAAA,EAAA;+GAGX,kBAAkB,EAAA,SAAA,EAFlB,CAAC,2BAA2B,CAAC,EAAA,OAAA,EAAA,CAF9B,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE,YAAY,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAI/D,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAL9B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE,YAAY,CAAC;oBAC3E,OAAO,EAAE,CAAC,YAAY,CAAC;oBACvB,SAAS,EAAE,CAAC,2BAA2B,CAAC;AACzC,iBAAA,CAAA;;;ACnBD;;AAEG;;;;"}