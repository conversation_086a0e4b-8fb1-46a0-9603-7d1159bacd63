{"ast": null, "code": "import { innerFrom } from '../observable/innerFrom';\nimport { observeOn } from '../operators/observeOn';\nimport { subscribeOn } from '../operators/subscribeOn';\nexport function scheduleObservable(input, scheduler) {\n  return innerFrom(input).pipe(subscribeOn(scheduler), observeOn(scheduler));\n}", "map": {"version": 3, "names": ["innerFrom", "observeOn", "subscribeOn", "scheduleObservable", "input", "scheduler", "pipe"], "sources": ["C:/Users/<USER>/Desktop/BookCart/bookcart-frontend/node_modules/rxjs/dist/esm/internal/scheduled/scheduleObservable.js"], "sourcesContent": ["import { innerFrom } from '../observable/innerFrom';\nimport { observeOn } from '../operators/observeOn';\nimport { subscribeOn } from '../operators/subscribeOn';\nexport function scheduleObservable(input, scheduler) {\n    return innerFrom(input).pipe(subscribeOn(scheduler), observeOn(scheduler));\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,yBAAyB;AACnD,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,WAAW,QAAQ,0BAA0B;AACtD,OAAO,SAASC,kBAAkBA,CAACC,KAAK,EAAEC,SAAS,EAAE;EACjD,OAAOL,SAAS,CAACI,KAAK,CAAC,CAACE,IAAI,CAACJ,WAAW,CAACG,SAAS,CAAC,EAAEJ,SAAS,CAACI,SAAS,CAAC,CAAC;AAC9E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}