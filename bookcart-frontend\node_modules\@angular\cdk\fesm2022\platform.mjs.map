{"version": 3, "file": "platform.mjs", "sources": ["../../../../../../src/cdk/platform/platform.ts", "../../../../../../src/cdk/platform/platform-module.ts", "../../../../../../src/cdk/platform/features/input-types.ts", "../../../../../../src/cdk/platform/features/passive-listeners.ts", "../../../../../../src/cdk/platform/features/scrolling.ts", "../../../../../../src/cdk/platform/features/shadow-dom.ts", "../../../../../../src/cdk/platform/features/test-environment.ts", "../../../../../../src/cdk/platform/platform_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Inject, Injectable, PLATFORM_ID} from '@angular/core';\nimport {isPlatformBrowser} from '@angular/common';\n\n// Whether the current platform supports the V8 Break Iterator. The V8 check\n// is necessary to detect all Blink based browsers.\nlet hasV8BreakIterator: boolean;\n\n// We need a try/catch around the reference to `Intl`, because accessing it in some cases can\n// cause IE to throw. These cases are tied to particular versions of Windows and can happen if\n// the consumer is providing a polyfilled `Map`. See:\n// https://github.com/Microsoft/ChakraCore/issues/3189\n// https://github.com/angular/components/issues/15687\ntry {\n  hasV8BreakIterator = typeof Intl !== 'undefined' && (Intl as any).v8BreakIterator;\n} catch {\n  hasV8BreakIterator = false;\n}\n\n/**\n * Service to detect the current platform by comparing the userAgent strings and\n * checking browser-specific global properties.\n */\n@Injectable({providedIn: 'root'})\nexport class Platform {\n  // We want to use the Angular platform check because if the Document is shimmed\n  // without the navigator, the following checks will fail. This is preferred because\n  // sometimes the Document may be shimmed without the user's knowledge or intention\n  /** Whether the Angular application is being rendered in the browser. */\n  isBrowser: boolean = this._platformId\n    ? isPlatformBrowser(this._platformId)\n    : typeof document === 'object' && !!document;\n\n  /** Whether the current browser is Microsoft Edge. */\n  EDGE: boolean = this.isBrowser && /(edge)/i.test(navigator.userAgent);\n\n  /** Whether the current rendering engine is Microsoft Trident. */\n  TRIDENT: boolean = this.isBrowser && /(msie|trident)/i.test(navigator.userAgent);\n\n  // EdgeHTML and Trident mock Blink specific things and need to be excluded from this check.\n  /** Whether the current rendering engine is Blink. */\n  BLINK: boolean =\n    this.isBrowser &&\n    !!((window as any).chrome || hasV8BreakIterator) &&\n    typeof CSS !== 'undefined' &&\n    !this.EDGE &&\n    !this.TRIDENT;\n\n  // Webkit is part of the userAgent in EdgeHTML, Blink and Trident. Therefore we need to\n  // ensure that Webkit runs standalone and is not used as another engine's base.\n  /** Whether the current rendering engine is WebKit. */\n  WEBKIT: boolean =\n    this.isBrowser &&\n    /AppleWebKit/i.test(navigator.userAgent) &&\n    !this.BLINK &&\n    !this.EDGE &&\n    !this.TRIDENT;\n\n  /** Whether the current platform is Apple iOS. */\n  IOS: boolean =\n    this.isBrowser && /iPad|iPhone|iPod/.test(navigator.userAgent) && !('MSStream' in window);\n\n  // It's difficult to detect the plain Gecko engine, because most of the browsers identify\n  // them self as Gecko-like browsers and modify the userAgent's according to that.\n  // Since we only cover one explicit Firefox case, we can simply check for Firefox\n  // instead of having an unstable check for Gecko.\n  /** Whether the current browser is Firefox. */\n  FIREFOX: boolean = this.isBrowser && /(firefox|minefield)/i.test(navigator.userAgent);\n\n  /** Whether the current platform is Android. */\n  // Trident on mobile adds the android platform to the userAgent to trick detections.\n  ANDROID: boolean = this.isBrowser && /android/i.test(navigator.userAgent) && !this.TRIDENT;\n\n  // Safari browsers will include the Safari keyword in their userAgent. Some browsers may fake\n  // this and just place the Safari keyword in the userAgent. To be more safe about Safari every\n  // Safari browser should also use Webkit as its layout engine.\n  /** Whether the current browser is Safari. */\n  SAFARI: boolean = this.isBrowser && /safari/i.test(navigator.userAgent) && this.WEBKIT;\n\n  constructor(@Inject(PLATFORM_ID) private _platformId: Object) {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\n\n@NgModule({})\nexport class PlatformModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Cached result Set of input types support by the current browser. */\nlet supportedInputTypes: Set<string>;\n\n/** Types of `<input>` that *might* be supported. */\nconst candidateInputTypes = [\n  // `color` must come first. Chrome 56 shows a warning if we change the type to `color` after\n  // first changing it to something else:\n  // The specified value \"\" does not conform to the required format.\n  // The format is \"#rrggbb\" where rr, gg, bb are two-digit hexadecimal numbers.\n  'color',\n  'button',\n  'checkbox',\n  'date',\n  'datetime-local',\n  'email',\n  'file',\n  'hidden',\n  'image',\n  'month',\n  'number',\n  'password',\n  'radio',\n  'range',\n  'reset',\n  'search',\n  'submit',\n  'tel',\n  'text',\n  'time',\n  'url',\n  'week',\n];\n\n/** @returns The input types supported by this browser. */\nexport function getSupportedInputTypes(): Set<string> {\n  // Result is cached.\n  if (supportedInputTypes) {\n    return supportedInputTypes;\n  }\n\n  // We can't check if an input type is not supported until we're on the browser, so say that\n  // everything is supported when not on the browser. We don't use `Platform` here since it's\n  // just a helper function and can't inject it.\n  if (typeof document !== 'object' || !document) {\n    supportedInputTypes = new Set(candidateInputTypes);\n    return supportedInputTypes;\n  }\n\n  let featureTestInput = document.createElement('input');\n  supportedInputTypes = new Set(\n    candidateInputTypes.filter(value => {\n      featureTestInput.setAttribute('type', value);\n      return featureTestInput.type === value;\n    }),\n  );\n\n  return supportedInputTypes;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Cached result of whether the user's browser supports passive event listeners. */\nlet supportsPassiveEvents: boolean;\n\n/**\n * Checks whether the user's browser supports passive event listeners.\n * See: https://github.com/WICG/EventListenerOptions/blob/gh-pages/explainer.md\n */\nexport function supportsPassiveEventListeners(): boolean {\n  if (supportsPassiveEvents == null && typeof window !== 'undefined') {\n    try {\n      window.addEventListener(\n        'test',\n        null!,\n        Object.defineProperty({}, 'passive', {\n          get: () => (supportsPassiveEvents = true),\n        }),\n      );\n    } finally {\n      supportsPassiveEvents = supportsPassiveEvents || false;\n    }\n  }\n\n  return supportsPassiveEvents;\n}\n\n/**\n * Normalizes an `AddEventListener` object to something that can be passed\n * to `addEventListener` on any browser, no matter whether it supports the\n * `options` parameter.\n * @param options Object to be normalized.\n */\nexport function normalizePassiveListenerOptions(\n  options: AddEventListenerOptions,\n): AddEventListenerOptions | boolean {\n  return supportsPassiveEventListeners() ? options : !!options.capture;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** The possible ways the browser may handle the horizontal scroll axis in RTL languages. */\nexport enum RtlScrollAxisType {\n  /**\n   * scrollLeft is 0 when scrolled all the way left and (scrollWidth - clientWidth) when scrolled\n   * all the way right.\n   */\n  NORMAL,\n  /**\n   * scrollLeft is -(scrollWidth - clientWidth) when scrolled all the way left and 0 when scrolled\n   * all the way right.\n   */\n  NEGATED,\n  /**\n   * scrollLeft is (scrollWidth - clientWidth) when scrolled all the way left and 0 when scrolled\n   * all the way right.\n   */\n  INVERTED,\n}\n\n/** Cached result of the way the browser handles the horizontal scroll axis in RTL mode. */\nlet rtlScrollAxisType: RtlScrollAxisType | undefined;\n\n/** Cached result of the check that indicates whether the browser supports scroll behaviors. */\nlet scrollBehaviorSupported: boolean | undefined;\n\n/** Check whether the browser supports scroll behaviors. */\nexport function supportsScrollBehavior(): boolean {\n  if (scrollBehaviorSupported == null) {\n    // If we're not in the browser, it can't be supported. Also check for `Element`, because\n    // some projects stub out the global `document` during SSR which can throw us off.\n    if (typeof document !== 'object' || !document || typeof Element !== 'function' || !Element) {\n      scrollBehaviorSupported = false;\n      return scrollBehaviorSupported;\n    }\n\n    // If the element can have a `scrollBehavior` style, we can be sure that it's supported.\n    if ('scrollBehavior' in document.documentElement!.style) {\n      scrollBehaviorSupported = true;\n    } else {\n      // At this point we have 3 possibilities: `scrollTo` isn't supported at all, it's\n      // supported but it doesn't handle scroll behavior, or it has been polyfilled.\n      const scrollToFunction: Function | undefined = Element.prototype.scrollTo;\n\n      if (scrollToFunction) {\n        // We can detect if the function has been polyfilled by calling `toString` on it. Native\n        // functions are obfuscated using `[native code]`, whereas if it was overwritten we'd get\n        // the actual function source. Via https://davidwalsh.name/detect-native-function. Consider\n        // polyfilled functions as supporting scroll behavior.\n        scrollBehaviorSupported = !/\\{\\s*\\[native code\\]\\s*\\}/.test(scrollToFunction.toString());\n      } else {\n        scrollBehaviorSupported = false;\n      }\n    }\n  }\n\n  return scrollBehaviorSupported;\n}\n\n/**\n * Checks the type of RTL scroll axis used by this browser. As of time of writing, Chrome is NORMAL,\n * Firefox & Safari are NEGATED, and IE & Edge are INVERTED.\n */\nexport function getRtlScrollAxisType(): RtlScrollAxisType {\n  // We can't check unless we're on the browser. Just assume 'normal' if we're not.\n  if (typeof document !== 'object' || !document) {\n    return RtlScrollAxisType.NORMAL;\n  }\n\n  if (rtlScrollAxisType == null) {\n    // Create a 1px wide scrolling container and a 2px wide content element.\n    const scrollContainer = document.createElement('div');\n    const containerStyle = scrollContainer.style;\n    scrollContainer.dir = 'rtl';\n    containerStyle.width = '1px';\n    containerStyle.overflow = 'auto';\n    containerStyle.visibility = 'hidden';\n    containerStyle.pointerEvents = 'none';\n    containerStyle.position = 'absolute';\n\n    const content = document.createElement('div');\n    const contentStyle = content.style;\n    contentStyle.width = '2px';\n    contentStyle.height = '1px';\n\n    scrollContainer.appendChild(content);\n    document.body.appendChild(scrollContainer);\n\n    rtlScrollAxisType = RtlScrollAxisType.NORMAL;\n\n    // The viewport starts scrolled all the way to the right in RTL mode. If we are in a NORMAL\n    // browser this would mean that the scrollLeft should be 1. If it's zero instead we know we're\n    // dealing with one of the other two types of browsers.\n    if (scrollContainer.scrollLeft === 0) {\n      // In a NEGATED browser the scrollLeft is always somewhere in [-maxScrollAmount, 0]. For an\n      // INVERTED browser it is always somewhere in [0, maxScrollAmount]. We can determine which by\n      // setting to the scrollLeft to 1. This is past the max for a NEGATED browser, so it will\n      // return 0 when we read it again.\n      scrollContainer.scrollLeft = 1;\n      rtlScrollAxisType =\n        scrollContainer.scrollLeft === 0 ? RtlScrollAxisType.NEGATED : RtlScrollAxisType.INVERTED;\n    }\n\n    scrollContainer.remove();\n  }\n  return rtlScrollAxisType;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nlet shadowDomIsSupported: boolean;\n\n/** Checks whether the user's browser support Shadow DOM. */\nexport function _supportsShadowDom(): boolean {\n  if (shadowDomIsSupported == null) {\n    const head = typeof document !== 'undefined' ? document.head : null;\n    shadowDomIsSupported = !!(head && ((head as any).createShadowRoot || head.attachShadow));\n  }\n\n  return shadowDomIsSupported;\n}\n\n/** Gets the shadow root of an element, if supported and the element is inside the Shadow DOM. */\nexport function _getShadowRoot(element: HTMLElement): ShadowRoot | null {\n  if (_supportsShadowDom()) {\n    const rootNode = element.getRootNode ? element.getRootNode() : null;\n\n    // Note that this should be caught by `_supportsShadowDom`, but some\n    // teams have been able to hit this code path on unsupported browsers.\n    if (typeof ShadowRoot !== 'undefined' && ShadowRoot && rootNode instanceof ShadowRoot) {\n      return rootNode;\n    }\n  }\n\n  return null;\n}\n\n/**\n * Gets the currently-focused element on the page while\n * also piercing through Shadow DOM boundaries.\n */\nexport function _getFocusedElementPierceShadowDom(): HTMLElement | null {\n  let activeElement =\n    typeof document !== 'undefined' && document\n      ? (document.activeElement as HTMLElement | null)\n      : null;\n\n  while (activeElement && activeElement.shadowRoot) {\n    const newActiveElement = activeElement.shadowRoot.activeElement as HTMLElement | null;\n    if (newActiveElement === activeElement) {\n      break;\n    } else {\n      activeElement = newActiveElement;\n    }\n  }\n\n  return activeElement;\n}\n\n/** Gets the target of an event while accounting for Shadow DOM. */\nexport function _getEventTarget<T extends EventTarget>(event: Event): T | null {\n  // If an event is bound outside the Shadow DOM, the `event.target` will\n  // point to the shadow root so we have to use `composedPath` instead.\n  return (event.composedPath ? event.composedPath()[0] : event.target) as T | null;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Gets whether the code is currently running in a test environment. */\nexport function _isTestEnvironment(): boolean {\n  // We can't use `declare const` because it causes conflicts inside Google with the real typings\n  // for these symbols and we can't read them off the global object, because they don't appear to\n  // be attached there for some runners like Jest.\n  // (see: https://github.com/angular/components/issues/23365#issuecomment-938146643)\n  return (\n    // @ts-ignore\n    (typeof __karma__ !== 'undefined' && !!__karma__) ||\n    // @ts-ignore\n    (typeof jasmine !== 'undefined' && !!jasmine) ||\n    // @ts-ignore\n    (typeof jest !== 'undefined' && !!jest) ||\n    // @ts-ignore\n    (typeof Mocha !== 'undefined' && !!Mocha)\n  );\n}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;AAWA;AACA;AACA,IAAI,kBAA2B,CAAC;AAEhC;AACA;AACA;AACA;AACA;AACA,IAAI;IACF,kBAAkB,GAAG,OAAO,IAAI,KAAK,WAAW,IAAK,IAAY,CAAC,eAAe,CAAC;AACpF,CAAC;AAAC,MAAM;IACN,kBAAkB,GAAG,KAAK,CAAC;AAC7B,CAAC;AAED;;;AAGG;MAEU,QAAQ,CAAA;AAuDnB,IAAA,WAAA,CAAyC,WAAmB,EAAA;QAAnB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAQ;;;;;QAlD5D,IAAS,CAAA,SAAA,GAAY,IAAI,CAAC,WAAW;AACnC,cAAE,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC;cACnC,OAAO,QAAQ,KAAK,QAAQ,IAAI,CAAC,CAAC,QAAQ,CAAC;;AAG/C,QAAA,IAAA,CAAA,IAAI,GAAY,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;;AAGtE,QAAA,IAAA,CAAA,OAAO,GAAY,IAAI,CAAC,SAAS,IAAI,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;;;QAIjF,IAAK,CAAA,KAAA,GACH,IAAI,CAAC,SAAS;AACd,YAAA,CAAC,EAAG,MAAc,CAAC,MAAM,IAAI,kBAAkB,CAAC;YAChD,OAAO,GAAG,KAAK,WAAW;YAC1B,CAAC,IAAI,CAAC,IAAI;YACV,CAAC,IAAI,CAAC,OAAO,CAAC;;;;QAKhB,IAAM,CAAA,MAAA,GACJ,IAAI,CAAC,SAAS;AACd,YAAA,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;YACxC,CAAC,IAAI,CAAC,KAAK;YACX,CAAC,IAAI,CAAC,IAAI;YACV,CAAC,IAAI,CAAC,OAAO,CAAC;;QAGhB,IAAG,CAAA,GAAA,GACD,IAAI,CAAC,SAAS,IAAI,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,UAAU,IAAI,MAAM,CAAC,CAAC;;;;;;AAO5F,QAAA,IAAA,CAAA,OAAO,GAAY,IAAI,CAAC,SAAS,IAAI,sBAAsB,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;;;AAItF,QAAA,IAAA,CAAA,OAAO,GAAY,IAAI,CAAC,SAAS,IAAI,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;;;;;AAM3F,QAAA,IAAA,CAAA,MAAM,GAAY,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC;KAEvB;AAvDrD,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,QAAQ,kBAuDC,WAAW,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAvDpB,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,QAAQ,cADI,MAAM,EAAA,CAAA,CAAA,EAAA;;2FAClB,QAAQ,EAAA,UAAA,EAAA,CAAA;kBADpB,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;0BAwDjB,MAAM;2BAAC,WAAW,CAAA;;;MC3EpB,cAAc,CAAA;8GAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;+GAAd,cAAc,EAAA,CAAA,CAAA,EAAA;+GAAd,cAAc,EAAA,CAAA,CAAA,EAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAD1B,QAAQ;mBAAC,EAAE,CAAA;;;ACFZ;AACA,IAAI,mBAAgC,CAAC;AAErC;AACA,MAAM,mBAAmB,GAAG;;;;;IAK1B,OAAO;IACP,QAAQ;IACR,UAAU;IACV,MAAM;IACN,gBAAgB;IAChB,OAAO;IACP,MAAM;IACN,QAAQ;IACR,OAAO;IACP,OAAO;IACP,QAAQ;IACR,UAAU;IACV,OAAO;IACP,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,MAAM;IACN,MAAM;IACN,KAAK;IACL,MAAM;CACP,CAAC;AAEF;SACgB,sBAAsB,GAAA;;IAEpC,IAAI,mBAAmB,EAAE;AACvB,QAAA,OAAO,mBAAmB,CAAC;KAC5B;;;;IAKD,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,CAAC,QAAQ,EAAE;AAC7C,QAAA,mBAAmB,GAAG,IAAI,GAAG,CAAC,mBAAmB,CAAC,CAAC;AACnD,QAAA,OAAO,mBAAmB,CAAC;KAC5B;IAED,IAAI,gBAAgB,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;IACvD,mBAAmB,GAAG,IAAI,GAAG,CAC3B,mBAAmB,CAAC,MAAM,CAAC,KAAK,IAAG;AACjC,QAAA,gBAAgB,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAC7C,QAAA,OAAO,gBAAgB,CAAC,IAAI,KAAK,KAAK,CAAC;KACxC,CAAC,CACH,CAAC;AAEF,IAAA,OAAO,mBAAmB,CAAC;AAC7B;;ACzDA;AACA,IAAI,qBAA8B,CAAC;AAEnC;;;AAGG;SACa,6BAA6B,GAAA;IAC3C,IAAI,qBAAqB,IAAI,IAAI,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAClE,QAAA,IAAI;AACF,YAAA,MAAM,CAAC,gBAAgB,CACrB,MAAM,EACN,IAAK,EACL,MAAM,CAAC,cAAc,CAAC,EAAE,EAAE,SAAS,EAAE;gBACnC,GAAG,EAAE,OAAO,qBAAqB,GAAG,IAAI,CAAC;AAC1C,aAAA,CAAC,CACH,CAAC;SACH;gBAAS;AACR,YAAA,qBAAqB,GAAG,qBAAqB,IAAI,KAAK,CAAC;SACxD;KACF;AAED,IAAA,OAAO,qBAAqB,CAAC;AAC/B,CAAC;AAED;;;;;AAKG;AACG,SAAU,+BAA+B,CAC7C,OAAgC,EAAA;AAEhC,IAAA,OAAO,6BAA6B,EAAE,GAAG,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;AACvE;;ACnCA;IACY,kBAgBX;AAhBD,CAAA,UAAY,iBAAiB,EAAA;AAC3B;;;AAGG;AACH,IAAA,iBAAA,CAAA,iBAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM,CAAA;AACN;;;AAGG;AACH,IAAA,iBAAA,CAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO,CAAA;AACP;;;AAGG;AACH,IAAA,iBAAA,CAAA,iBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ,CAAA;AACV,CAAC,EAhBW,iBAAiB,KAAjB,iBAAiB,GAgB5B,EAAA,CAAA,CAAA,CAAA;AAED;AACA,IAAI,iBAAgD,CAAC;AAErD;AACA,IAAI,uBAA4C,CAAC;AAEjD;SACgB,sBAAsB,GAAA;AACpC,IAAA,IAAI,uBAAuB,IAAI,IAAI,EAAE;;;AAGnC,QAAA,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,CAAC,QAAQ,IAAI,OAAO,OAAO,KAAK,UAAU,IAAI,CAAC,OAAO,EAAE;YAC1F,uBAAuB,GAAG,KAAK,CAAC;AAChC,YAAA,OAAO,uBAAuB,CAAC;SAChC;;QAGD,IAAI,gBAAgB,IAAI,QAAQ,CAAC,eAAgB,CAAC,KAAK,EAAE;YACvD,uBAAuB,GAAG,IAAI,CAAC;SAChC;aAAM;;;AAGL,YAAA,MAAM,gBAAgB,GAAyB,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC;YAE1E,IAAI,gBAAgB,EAAE;;;;;gBAKpB,uBAAuB,GAAG,CAAC,2BAA2B,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC;aAC1F;iBAAM;gBACL,uBAAuB,GAAG,KAAK,CAAC;aACjC;SACF;KACF;AAED,IAAA,OAAO,uBAAuB,CAAC;AACjC,CAAC;AAED;;;AAGG;SACa,oBAAoB,GAAA;;IAElC,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,CAAC,QAAQ,EAAE;QAC7C,OAAO,iBAAiB,CAAC,MAAM,CAAC;KACjC;AAED,IAAA,IAAI,iBAAiB,IAAI,IAAI,EAAE;;QAE7B,MAAM,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACtD,QAAA,MAAM,cAAc,GAAG,eAAe,CAAC,KAAK,CAAC;AAC7C,QAAA,eAAe,CAAC,GAAG,GAAG,KAAK,CAAC;AAC5B,QAAA,cAAc,CAAC,KAAK,GAAG,KAAK,CAAC;AAC7B,QAAA,cAAc,CAAC,QAAQ,GAAG,MAAM,CAAC;AACjC,QAAA,cAAc,CAAC,UAAU,GAAG,QAAQ,CAAC;AACrC,QAAA,cAAc,CAAC,aAAa,GAAG,MAAM,CAAC;AACtC,QAAA,cAAc,CAAC,QAAQ,GAAG,UAAU,CAAC;QAErC,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC9C,QAAA,MAAM,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC;AACnC,QAAA,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;AAC3B,QAAA,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;AAE5B,QAAA,eAAe,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AACrC,QAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;AAE3C,QAAA,iBAAiB,GAAG,iBAAiB,CAAC,MAAM,CAAC;;;;AAK7C,QAAA,IAAI,eAAe,CAAC,UAAU,KAAK,CAAC,EAAE;;;;;AAKpC,YAAA,eAAe,CAAC,UAAU,GAAG,CAAC,CAAC;YAC/B,iBAAiB;AACf,gBAAA,eAAe,CAAC,UAAU,KAAK,CAAC,GAAG,iBAAiB,CAAC,OAAO,GAAG,iBAAiB,CAAC,QAAQ,CAAC;SAC7F;QAED,eAAe,CAAC,MAAM,EAAE,CAAC;KAC1B;AACD,IAAA,OAAO,iBAAiB,CAAC;AAC3B;;ACzGA,IAAI,oBAA6B,CAAC;AAElC;SACgB,kBAAkB,GAAA;AAChC,IAAA,IAAI,oBAAoB,IAAI,IAAI,EAAE;AAChC,QAAA,MAAM,IAAI,GAAG,OAAO,QAAQ,KAAK,WAAW,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;AACpE,QAAA,oBAAoB,GAAG,CAAC,EAAE,IAAI,KAAM,IAAY,CAAC,gBAAgB,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;KAC1F;AAED,IAAA,OAAO,oBAAoB,CAAC;AAC9B,CAAC;AAED;AACM,SAAU,cAAc,CAAC,OAAoB,EAAA;IACjD,IAAI,kBAAkB,EAAE,EAAE;AACxB,QAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC;;;QAIpE,IAAI,OAAO,UAAU,KAAK,WAAW,IAAI,UAAU,IAAI,QAAQ,YAAY,UAAU,EAAE;AACrF,YAAA,OAAO,QAAQ,CAAC;SACjB;KACF;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;AAGG;SACa,iCAAiC,GAAA;AAC/C,IAAA,IAAI,aAAa,GACf,OAAO,QAAQ,KAAK,WAAW,IAAI,QAAQ;UACtC,QAAQ,CAAC,aAAoC;UAC9C,IAAI,CAAC;AAEX,IAAA,OAAO,aAAa,IAAI,aAAa,CAAC,UAAU,EAAE;AAChD,QAAA,MAAM,gBAAgB,GAAG,aAAa,CAAC,UAAU,CAAC,aAAmC,CAAC;AACtF,QAAA,IAAI,gBAAgB,KAAK,aAAa,EAAE;YACtC,MAAM;SACP;aAAM;YACL,aAAa,GAAG,gBAAgB,CAAC;SAClC;KACF;AAED,IAAA,OAAO,aAAa,CAAC;AACvB,CAAC;AAED;AACM,SAAU,eAAe,CAAwB,KAAY,EAAA;;;IAGjE,QAAQ,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,EAAc;AACnF;;ACtDA;SACgB,kBAAkB,GAAA;;;;;IAKhC;;IAEE,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,CAAC,CAAC,SAAS;;SAE/C,OAAO,OAAO,KAAK,WAAW,IAAI,CAAC,CAAC,OAAO,CAAC;;SAE5C,OAAO,IAAI,KAAK,WAAW,IAAI,CAAC,CAAC,IAAI,CAAC;;SAEtC,OAAO,KAAK,KAAK,WAAW,IAAI,CAAC,CAAC,KAAK,CAAC,EACzC;AACJ;;ACxBA;;AAEG;;;;"}