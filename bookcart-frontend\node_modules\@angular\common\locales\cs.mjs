/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\.?/, '').length;
    if (i === 1 && v === 0)
        return 1;
    if (i === Math.floor(i) && (i >= 2 && i <= 4) && v === 0)
        return 3;
    if (!(v === 0))
        return 4;
    return 5;
}
export default ["cs", [["dop.", "odp."], u, u], u, [["N", "P", "Ú", "S", "Č", "P", "S"], ["ne", "po", "út", "st", "čt", "pá", "so"], ["neděle", "ponděl<PERSON>", "úterý", "středa", "čtvrtek", "pátek", "sobota"], ["ne", "po", "út", "st", "čt", "pá", "so"]], u, [["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"], ["led", "úno", "bře", "dub", "kvě", "čvn", "čvc", "srp", "zář", "říj", "lis", "pro"], ["ledna", "února", "března", "dubna", "května", "června", "července", "srpna", "září", "října", "listopadu", "prosince"]], [["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"], ["led", "úno", "bře", "dub", "kvě", "čvn", "čvc", "srp", "zář", "říj", "lis", "pro"], ["leden", "únor", "březen", "duben", "květen", "červen", "červenec", "srpen", "září", "říjen", "listopad", "prosinec"]], [["př.n.l.", "n.l."], ["př. n. l.", "n. l."], ["před naším letopočtem", "našeho letopočtu"]], 1, [6, 0], ["dd.MM.yy", "d. M. y", "d. MMMM y", "EEEE d. MMMM y"], ["H:mm", "H:mm:ss", "H:mm:ss z", "H:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", " ", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0 %", "#,##0.00 ¤", "#E0"], "CZK", "Kč", "česká koruna", { "AUD": ["AU$", "$"], "BYN": [u, "р."], "CSK": ["Kčs"], "CZK": ["Kč"], "ILS": [u, "₪"], "INR": [u, "₹"], "JPY": ["JP¥", "¥"], "PHP": [u, "₱"], "RON": [u, "L"], "RUR": [u, "р."], "TWD": ["NT$"], "USD": ["US$", "$"], "VND": [u, "₫"], "XEU": ["ECU"], "XXX": [] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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