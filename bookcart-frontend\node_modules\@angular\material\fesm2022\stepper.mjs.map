{"version": 3, "file": "stepper.mjs", "sources": ["../../../../../../src/material/stepper/step-label.ts", "../../../../../../src/material/stepper/stepper-intl.ts", "../../../../../../src/material/stepper/step-header.ts", "../../../../../../src/material/stepper/step-header.html", "../../../../../../src/material/stepper/stepper-animations.ts", "../../../../../../src/material/stepper/stepper-icon.ts", "../../../../../../src/material/stepper/step-content.ts", "../../../../../../src/material/stepper/stepper.ts", "../../../../../../src/material/stepper/step.html", "../../../../../../src/material/stepper/stepper.html", "../../../../../../src/material/stepper/stepper-button.ts", "../../../../../../src/material/stepper/stepper-module.ts", "../../../../../../src/material/stepper/stepper_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive} from '@angular/core';\nimport {CdkStepLabel} from '@angular/cdk/stepper';\n\n@Directive({\n  selector: '[matStepLabel]',\n  standalone: true,\n})\nexport class MatStepLabel extends CdkStepLabel {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injectable, Optional, SkipSelf} from '@angular/core';\nimport {Subject} from 'rxjs';\n\n/** Stepper data that is required for internationalization. */\n@Injectable({providedIn: 'root'})\nexport class MatStepperIntl {\n  /**\n   * Stream that emits whenever the labels here are changed. Use this to notify\n   * components if the labels have changed after initialization.\n   */\n  readonly changes: Subject<void> = new Subject<void>();\n\n  /** Label that is rendered below optional steps. */\n  optionalLabel: string = 'Optional';\n\n  /** Label that is used to indicate step as completed to screen readers. */\n  completedLabel: string = 'Completed';\n\n  /** Label that is used to indicate step as editable to screen readers. */\n  editableLabel: string = 'Editable';\n}\n\n/** @docs-private */\nexport function MAT_STEPPER_INTL_PROVIDER_FACTORY(parentIntl: MatStepperIntl) {\n  return parentIntl || new MatStepperIntl();\n}\n\n/** @docs-private */\nexport const MAT_STEPPER_INTL_PROVIDER = {\n  provide: MatStepperIntl,\n  deps: [[new Optional(), new SkipSelf(), MatStepperIntl]],\n  useFactory: MAT_STEPPER_INTL_PROVIDER_FACTORY,\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {FocusMonitor, FocusOrigin} from '@angular/cdk/a11y';\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  Input,\n  OnDestroy,\n  ViewEncapsulation,\n  TemplateRef,\n  AfterViewInit,\n} from '@angular/core';\nimport {Subscription} from 'rxjs';\nimport {MatStepLabel} from './step-label';\nimport {MatStepperIntl} from './stepper-intl';\nimport {MatStepperIconContext} from './stepper-icon';\nimport {CdkStepHeader, StepState} from '@angular/cdk/stepper';\nimport {MatRipple, ThemePalette} from '@angular/material/core';\nimport {MatIcon} from '@angular/material/icon';\nimport {NgTemplateOutlet} from '@angular/common';\n\n@Component({\n  selector: 'mat-step-header',\n  templateUrl: 'step-header.html',\n  styleUrl: 'step-header.css',\n  host: {\n    'class': 'mat-step-header',\n    '[class]': '\"mat-\" + (color || \"primary\")',\n    'role': 'tab',\n  },\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: true,\n  imports: [MatRipple, NgTemplateOutlet, MatIcon],\n})\nexport class MatStepHeader extends CdkStepHeader implements AfterViewInit, OnDestroy {\n  private _intlSubscription: Subscription;\n\n  /** State of the given step. */\n  @Input() state: StepState;\n\n  /** Label of the given step. */\n  @Input() label: MatStepLabel | string;\n\n  /** Error message to display when there's an error. */\n  @Input() errorMessage: string;\n\n  /** Overrides for the header icons, passed in via the stepper. */\n  @Input() iconOverrides: {[key: string]: TemplateRef<MatStepperIconContext>};\n\n  /** Index of the given step. */\n  @Input() index: number;\n\n  /** Whether the given step is selected. */\n  @Input() selected: boolean;\n\n  /** Whether the given step label is active. */\n  @Input() active: boolean;\n\n  /** Whether the given step is optional. */\n  @Input() optional: boolean;\n\n  /** Whether the ripple should be disabled. */\n  @Input() disableRipple: boolean;\n\n  /** Theme palette color of the step header. */\n  @Input() color: ThemePalette;\n\n  constructor(\n    public _intl: MatStepperIntl,\n    private _focusMonitor: FocusMonitor,\n    _elementRef: ElementRef<HTMLElement>,\n    changeDetectorRef: ChangeDetectorRef,\n  ) {\n    super(_elementRef);\n    this._intlSubscription = _intl.changes.subscribe(() => changeDetectorRef.markForCheck());\n  }\n\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this._elementRef, true);\n  }\n\n  ngOnDestroy() {\n    this._intlSubscription.unsubscribe();\n    this._focusMonitor.stopMonitoring(this._elementRef);\n  }\n\n  /** Focuses the step header. */\n  override focus(origin?: FocusOrigin, options?: FocusOptions) {\n    if (origin) {\n      this._focusMonitor.focusVia(this._elementRef, origin, options);\n    } else {\n      this._elementRef.nativeElement.focus(options);\n    }\n  }\n\n  /** Returns string label of given step if it is a text label. */\n  _stringLabel(): string | null {\n    return this.label instanceof MatStepLabel ? null : this.label;\n  }\n\n  /** Returns MatStepLabel if the label of given step is a template label. */\n  _templateLabel(): MatStepLabel | null {\n    return this.label instanceof MatStepLabel ? this.label : null;\n  }\n\n  /** Returns the host HTML element. */\n  _getHostElement() {\n    return this._elementRef.nativeElement;\n  }\n\n  /** Template context variables that are exposed to the `matStepperIcon` instances. */\n  _getIconContext(): MatStepperIconContext {\n    return {\n      index: this.index,\n      active: this.active,\n      optional: this.optional,\n    };\n  }\n\n  _getDefaultTextForState(state: StepState): string {\n    if (state == 'number') {\n      return `${this.index + 1}`;\n    }\n    if (state == 'edit') {\n      return 'create';\n    }\n    if (state == 'error') {\n      return 'warning';\n    }\n    return state;\n  }\n}\n", "<div class=\"mat-step-header-ripple mat-focus-indicator\" matRipple\n     [matRippleTrigger]=\"_getHostElement()\"\n     [matRippleDisabled]=\"disableRipple\"></div>\n\n<div class=\"mat-step-icon-state-{{state}} mat-step-icon\" [class.mat-step-icon-selected]=\"selected\">\n  <div class=\"mat-step-icon-content\">\n    @if (iconOverrides && iconOverrides[state]) {\n      <ng-container\n        [ngTemplateOutlet]=\"iconOverrides[state]\"\n        [ngTemplateOutletContext]=\"_getIconContext()\"></ng-container>\n    } @else {\n      @switch (state) {\n        @case ('number') {\n          <span aria-hidden=\"true\">{{_getDefaultTextForState(state)}}</span>\n        }\n\n        @default {\n          @if (state === 'done') {\n            <span class=\"cdk-visually-hidden\">{{_intl.completedLabel}}</span>\n          } @else if (state === 'edit') {\n            <span class=\"cdk-visually-hidden\">{{_intl.editableLabel}}</span>\n          }\n\n          <mat-icon aria-hidden=\"true\">{{_getDefaultTextForState(state)}}</mat-icon>\n        }\n      }\n    }\n  </div>\n</div>\n<div class=\"mat-step-label\"\n     [class.mat-step-label-active]=\"active\"\n     [class.mat-step-label-selected]=\"selected\"\n     [class.mat-step-label-error]=\"state == 'error'\">\n  @if (_templateLabel(); as templateLabel) {\n    <!-- If there is a label template, use it. -->\n    <div class=\"mat-step-text-label\">\n      <ng-container [ngTemplateOutlet]=\"templateLabel.template\"></ng-container>\n    </div>\n  } @else if (_stringLabel()) {\n    <!-- If there is no label template, fall back to the text label. -->\n    <div class=\"mat-step-text-label\">{{label}}</div>\n  }\n\n  @if (optional && state != 'error') {\n    <div class=\"mat-step-optional\">{{_intl.optionalLabel}}</div>\n  }\n\n  @if (state === 'error') {\n    <div class=\"mat-step-sub-label-error\">{{errorMessage}}</div>\n  }\n</div>\n\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {\n  animate,\n  state,\n  style,\n  transition,\n  trigger,\n  AnimationTriggerMetadata,\n  group,\n  query,\n  animateChild,\n} from '@angular/animations';\n\nexport const DEFAULT_HORIZONTAL_ANIMATION_DURATION = '500ms';\nexport const DEFAULT_VERTICAL_ANIMATION_DURATION = '225ms';\n\n/**\n * Animations used by the Material steppers.\n * @docs-private\n */\nexport const matStepperAnimations: {\n  readonly horizontalStepTransition: AnimationTriggerMetadata;\n  readonly verticalStepTransition: AnimationTriggerMetadata;\n} = {\n  /** Animation that transitions the step along the X axis in a horizontal stepper. */\n  horizontalStepTransition: trigger('horizontalStepTransition', [\n    state('previous', style({transform: 'translate3d(-100%, 0, 0)', visibility: 'hidden'})),\n    // Transition to `inherit`, rather than `visible`,\n    // because visibility on a child element the one from the parent,\n    // making this element focusable inside of a `hidden` element.\n    state('current', style({transform: 'none', visibility: 'inherit'})),\n    state('next', style({transform: 'translate3d(100%, 0, 0)', visibility: 'hidden'})),\n    transition(\n      '* => *',\n      group([\n        animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n        query('@*', animateChild(), {optional: true}),\n      ]),\n      {\n        params: {'animationDuration': DEFAULT_HORIZONTAL_ANIMATION_DURATION},\n      },\n    ),\n  ]),\n\n  /** Animation that transitions the step along the Y axis in a vertical stepper. */\n  verticalStepTransition: trigger('verticalStepTransition', [\n    state('previous', style({height: '0px', visibility: 'hidden'})),\n    state('next', style({height: '0px', visibility: 'hidden'})),\n    // Transition to `inherit`, rather than `visible`,\n    // because visibility on a child element the one from the parent,\n    // making this element focusable inside of a `hidden` element.\n    state('current', style({height: '*', visibility: 'inherit'})),\n    transition(\n      '* <=> current',\n      group([\n        animate('{{animationDuration}} cubic-bezier(0.4, 0.0, 0.2, 1)'),\n        query('@*', animateChild(), {optional: true}),\n      ]),\n      {\n        params: {'animationDuration': DEFAULT_VERTICAL_ANIMATION_DURATION},\n      },\n    ),\n  ]),\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, Input, TemplateRef} from '@angular/core';\nimport {StepState} from '@angular/cdk/stepper';\n\n/** Template context available to an attached `matStepperIcon`. */\nexport interface MatStepperIconContext {\n  /** Index of the step. */\n  index: number;\n  /** Whether the step is currently active. */\n  active: boolean;\n  /** Whether the step is optional. */\n  optional: boolean;\n}\n\n/**\n * Template to be used to override the icons inside the step header.\n */\n@Directive({\n  selector: 'ng-template[matStepperIcon]',\n  standalone: true,\n})\nexport class MatStepperIcon {\n  /** Name of the icon to be overridden. */\n  @Input('matStepperIcon') name: StepState;\n\n  constructor(public templateRef: TemplateRef<MatStepperIconContext>) {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, TemplateRef} from '@angular/core';\n\n/**\n * Content for a `mat-step` that will be rendered lazily.\n */\n@Directive({\n  selector: 'ng-template[matStepContent]',\n  standalone: true,\n})\nexport class MatStepContent {\n  constructor(public _template: TemplateRef<any>) {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directionality} from '@angular/cdk/bidi';\nimport {\n  CdkStep,\n  CdkStepper,\n  StepContentPositionState,\n  STEPPER_GLOBAL_OPTIONS,\n  StepperOptions,\n} from '@angular/cdk/stepper';\nimport {AnimationEvent} from '@angular/animations';\nimport {\n  AfterContentInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChild,\n  ContentChildren,\n  ElementRef,\n  EventEmitter,\n  forwardRef,\n  inject,\n  Inject,\n  Input,\n  OnDestroy,\n  Optional,\n  Output,\n  QueryList,\n  SkipSelf,\n  TemplateRef,\n  ViewChildren,\n  ViewContainerRef,\n  ViewEncapsulation,\n} from '@angular/core';\nimport {AbstractControl, FormGroupDirective, NgForm} from '@angular/forms';\nimport {ErrorStateMatcher, ThemePalette} from '@angular/material/core';\nimport {CdkPortalOutlet, TemplatePortal} from '@angular/cdk/portal';\nimport {Subject, Subscription} from 'rxjs';\nimport {takeUntil, distinctUntilChanged, map, startWith, switchMap} from 'rxjs/operators';\n\nimport {MatStepHeader} from './step-header';\nimport {MatStepLabel} from './step-label';\nimport {\n  DEFAULT_HORIZONTAL_ANIMATION_DURATION,\n  DEFAULT_VERTICAL_ANIMATION_DURATION,\n  matStepperAnimations,\n} from './stepper-animations';\nimport {MatStepperIcon, MatStepperIconContext} from './stepper-icon';\nimport {MatStepContent} from './step-content';\nimport {NgTemplateOutlet} from '@angular/common';\nimport {Platform} from '@angular/cdk/platform';\n\n@Component({\n  selector: 'mat-step',\n  templateUrl: 'step.html',\n  providers: [\n    {provide: ErrorStateMatcher, useExisting: MatStep},\n    {provide: CdkStep, useExisting: MatStep},\n  ],\n  encapsulation: ViewEncapsulation.None,\n  exportAs: 'matStep',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: true,\n  imports: [CdkPortalOutlet],\n  host: {\n    'hidden': '', // Hide the steps so they don't affect the layout.\n  },\n})\nexport class MatStep extends CdkStep implements ErrorStateMatcher, AfterContentInit, OnDestroy {\n  private _isSelected = Subscription.EMPTY;\n\n  /** Content for step label given by `<ng-template matStepLabel>`. */\n  // We need an initializer here to avoid a TS error.\n  @ContentChild(MatStepLabel) override stepLabel: MatStepLabel = undefined!;\n\n  /** Theme color for the particular step. */\n  @Input() color: ThemePalette;\n\n  /** Content that will be rendered lazily. */\n  @ContentChild(MatStepContent, {static: false}) _lazyContent: MatStepContent;\n\n  /** Currently-attached portal containing the lazy content. */\n  _portal: TemplatePortal;\n\n  constructor(\n    @Inject(forwardRef(() => MatStepper)) stepper: MatStepper,\n    @SkipSelf() private _errorStateMatcher: ErrorStateMatcher,\n    private _viewContainerRef: ViewContainerRef,\n    @Optional() @Inject(STEPPER_GLOBAL_OPTIONS) stepperOptions?: StepperOptions,\n  ) {\n    super(stepper, stepperOptions);\n  }\n\n  ngAfterContentInit() {\n    this._isSelected = this._stepper.steps.changes\n      .pipe(\n        switchMap(() => {\n          return this._stepper.selectionChange.pipe(\n            map(event => event.selectedStep === this),\n            startWith(this._stepper.selected === this),\n          );\n        }),\n      )\n      .subscribe(isSelected => {\n        if (isSelected && this._lazyContent && !this._portal) {\n          this._portal = new TemplatePortal(this._lazyContent._template, this._viewContainerRef!);\n        }\n      });\n  }\n\n  ngOnDestroy() {\n    this._isSelected.unsubscribe();\n  }\n\n  /** Custom error state matcher that additionally checks for validity of interacted form. */\n  isErrorState(control: AbstractControl | null, form: FormGroupDirective | NgForm | null): boolean {\n    const originalErrorState = this._errorStateMatcher.isErrorState(control, form);\n\n    // Custom error state checks for the validity of form that is not submitted or touched\n    // since user can trigger a form change by calling for another step without directly\n    // interacting with the current form.\n    const customErrorState = !!(control && control.invalid && this.interacted);\n\n    return originalErrorState || customErrorState;\n  }\n}\n\n@Component({\n  selector: 'mat-stepper, mat-vertical-stepper, mat-horizontal-stepper, [matStepper]',\n  exportAs: 'matStepper, matVerticalStepper, matHorizontalStepper',\n  templateUrl: 'stepper.html',\n  styleUrl: 'stepper.css',\n  host: {\n    '[class.mat-stepper-horizontal]': 'orientation === \"horizontal\"',\n    '[class.mat-stepper-vertical]': 'orientation === \"vertical\"',\n    '[class.mat-stepper-label-position-end]':\n      'orientation === \"horizontal\" && labelPosition == \"end\"',\n    '[class.mat-stepper-label-position-bottom]':\n      'orientation === \"horizontal\" && labelPosition == \"bottom\"',\n    '[class.mat-stepper-header-position-bottom]': 'headerPosition === \"bottom\"',\n    '[attr.aria-orientation]': 'orientation',\n    'role': 'tablist',\n  },\n  animations: [\n    matStepperAnimations.horizontalStepTransition,\n    matStepperAnimations.verticalStepTransition,\n  ],\n  providers: [{provide: CdkStepper, useExisting: MatStepper}],\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: true,\n  imports: [NgTemplateOutlet, MatStepHeader],\n})\nexport class MatStepper extends CdkStepper implements AfterContentInit {\n  /** The list of step headers of the steps in the stepper. */\n  // We need an initializer here to avoid a TS error.\n  @ViewChildren(MatStepHeader) override _stepHeader: QueryList<MatStepHeader> =\n    undefined as unknown as QueryList<MatStepHeader>;\n\n  /** Full list of steps inside the stepper, including inside nested steppers. */\n  // We need an initializer here to avoid a TS error.\n  @ContentChildren(MatStep, {descendants: true}) override _steps: QueryList<MatStep> =\n    undefined as unknown as QueryList<MatStep>;\n\n  /** Steps that belong to the current stepper, excluding ones from nested steppers. */\n  override readonly steps: QueryList<MatStep> = new QueryList<MatStep>();\n\n  /** Custom icon overrides passed in by the consumer. */\n  @ContentChildren(MatStepperIcon, {descendants: true}) _icons: QueryList<MatStepperIcon>;\n\n  /** Event emitted when the current step is done transitioning in. */\n  @Output() readonly animationDone: EventEmitter<void> = new EventEmitter<void>();\n\n  /** Whether ripples should be disabled for the step headers. */\n  @Input() disableRipple: boolean;\n\n  /** Theme color for all of the steps in stepper. */\n  @Input() color: ThemePalette;\n\n  /**\n   * Whether the label should display in bottom or end position.\n   * Only applies in the `horizontal` orientation.\n   */\n  @Input()\n  labelPosition: 'bottom' | 'end' = 'end';\n\n  /**\n   * Position of the stepper's header.\n   * Only applies in the `horizontal` orientation.\n   */\n  @Input()\n  headerPosition: 'top' | 'bottom' = 'top';\n\n  /** Consumer-specified template-refs to be used to override the header icons. */\n  _iconOverrides: Record<string, TemplateRef<MatStepperIconContext>> = {};\n\n  /** Stream of animation `done` events when the body expands/collapses. */\n  readonly _animationDone = new Subject<AnimationEvent>();\n\n  /** Duration for the animation. Will be normalized to milliseconds if no units are set. */\n  @Input()\n  get animationDuration(): string {\n    return this._animationDuration;\n  }\n  set animationDuration(value: string) {\n    this._animationDuration = /^\\d+$/.test(value) ? value + 'ms' : value;\n  }\n  private _animationDuration = '';\n\n  /** Whether the stepper is rendering on the server. */\n  protected _isServer: boolean = !inject(Platform).isBrowser;\n\n  constructor(\n    @Optional() dir: Directionality,\n    changeDetectorRef: ChangeDetectorRef,\n    elementRef: ElementRef<HTMLElement>,\n  ) {\n    super(dir, changeDetectorRef, elementRef);\n    const nodeName = elementRef.nativeElement.nodeName.toLowerCase();\n    this.orientation = nodeName === 'mat-vertical-stepper' ? 'vertical' : 'horizontal';\n  }\n\n  override ngAfterContentInit() {\n    super.ngAfterContentInit();\n    this._icons.forEach(({name, templateRef}) => (this._iconOverrides[name] = templateRef));\n\n    // Mark the component for change detection whenever the content children query changes\n    this.steps.changes.pipe(takeUntil(this._destroyed)).subscribe(() => {\n      this._stateChanged();\n    });\n\n    this._animationDone\n      .pipe(\n        // This needs a `distinctUntilChanged` in order to avoid emitting the same event twice due\n        // to a bug in animations where the `.done` callback gets invoked twice on some browsers.\n        // See https://github.com/angular/angular/issues/24084\n        distinctUntilChanged((x, y) => x.fromState === y.fromState && x.toState === y.toState),\n        takeUntil(this._destroyed),\n      )\n      .subscribe(event => {\n        if ((event.toState as StepContentPositionState) === 'current') {\n          this.animationDone.emit();\n        }\n      });\n  }\n\n  _stepIsNavigable(index: number, step: MatStep): boolean {\n    return step.completed || this.selectedIndex === index || !this.linear;\n  }\n\n  _getAnimationDuration() {\n    if (this.animationDuration) {\n      return this.animationDuration;\n    }\n\n    return this.orientation === 'horizontal'\n      ? DEFAULT_HORIZONTAL_ANIMATION_DURATION\n      : DEFAULT_VERTICAL_ANIMATION_DURATION;\n  }\n}\n", "<ng-template>\n  <ng-content></ng-content>\n  <ng-template [cdkPortalOutlet]=\"_portal\"></ng-template>\n</ng-template>\n", "<!--\n  We need to project the content somewhere to avoid hydration errors. Some observations:\n  1. This is only necessary on the server.\n  2. We get a hydration error if there aren't any nodes after the `ng-content`.\n  3. We get a hydration error if `ng-content` is wrapped in another element.\n-->\n@if (_isServer) {\n  <ng-content/>\n}\n\n@switch (orientation) {\n  @case ('horizontal') {\n    <div class=\"mat-horizontal-stepper-wrapper\">\n      <div class=\"mat-horizontal-stepper-header-container\">\n        @for (step of steps; track step; let i = $index, isLast = $last) {\n          <ng-container\n            [ngTemplateOutlet]=\"stepTemplate\"\n            [ngTemplateOutletContext]=\"{step: step, i: i}\"></ng-container>\n          @if (!isLast) {\n            <div class=\"mat-stepper-horizontal-line\"></div>\n          }\n        }\n      </div>\n\n      <div class=\"mat-horizontal-content-container\">\n        @for (step of steps; track step; let i = $index) {\n          <div class=\"mat-horizontal-stepper-content\" role=\"tabpanel\"\n               [@horizontalStepTransition]=\"{\n                  'value': _getAnimationDirection(i),\n                  'params': {'animationDuration': _getAnimationDuration()}\n                }\"\n               (@horizontalStepTransition.done)=\"_animationDone.next($event)\"\n               [id]=\"_getStepContentId(i)\"\n               [attr.aria-labelledby]=\"_getStepLabelId(i)\"\n               [class.mat-horizontal-stepper-content-inactive]=\"selectedIndex !== i\">\n            <ng-container [ngTemplateOutlet]=\"step.content\"></ng-container>\n          </div>\n        }\n      </div>\n    </div>\n  }\n\n  @case ('vertical') {\n    @for (step of steps; track step; let i = $index, isLast = $last) {\n      <div class=\"mat-step\">\n        <ng-container\n          [ngTemplateOutlet]=\"stepTemplate\"\n          [ngTemplateOutletContext]=\"{step: step, i: i}\"></ng-container>\n        <div class=\"mat-vertical-content-container\" [class.mat-stepper-vertical-line]=\"!isLast\">\n          <div class=\"mat-vertical-stepper-content\" role=\"tabpanel\"\n               [@verticalStepTransition]=\"{\n                  'value': _getAnimationDirection(i),\n                  'params': {'animationDuration': _getAnimationDuration()}\n                }\"\n               (@verticalStepTransition.done)=\"_animationDone.next($event)\"\n               [id]=\"_getStepContentId(i)\"\n               [attr.aria-labelledby]=\"_getStepLabelId(i)\"\n               [class.mat-vertical-stepper-content-inactive]=\"selectedIndex !== i\">\n            <div class=\"mat-vertical-content\">\n              <ng-container [ngTemplateOutlet]=\"step.content\"></ng-container>\n            </div>\n          </div>\n        </div>\n      </div>\n    }\n  }\n}\n\n<!-- Common step templating -->\n<ng-template let-step=\"step\" let-i=\"i\" #stepTemplate>\n  <mat-step-header\n    [class.mat-horizontal-stepper-header]=\"orientation === 'horizontal'\"\n    [class.mat-vertical-stepper-header]=\"orientation === 'vertical'\"\n    (click)=\"step.select()\"\n    (keydown)=\"_onKeydown($event)\"\n    [tabIndex]=\"_getFocusIndex() === i ? 0 : -1\"\n    [id]=\"_getStepLabelId(i)\"\n    [attr.aria-posinset]=\"i + 1\"\n    [attr.aria-setsize]=\"steps.length\"\n    [attr.aria-controls]=\"_getStepContentId(i)\"\n    [attr.aria-selected]=\"selectedIndex == i\"\n    [attr.aria-label]=\"step.ariaLabel || null\"\n    [attr.aria-labelledby]=\"(!step.ariaLabel && step.ariaLabelledby) ? step.ariaLabelledby : null\"\n    [attr.aria-disabled]=\"_stepIsNavigable(i, step) ? null : true\"\n    [index]=\"i\"\n    [state]=\"_getIndicatorType(i, step.state)\"\n    [label]=\"step.stepLabel || step.label\"\n    [selected]=\"selectedIndex === i\"\n    [active]=\"_stepIsNavigable(i, step)\"\n    [optional]=\"step.optional\"\n    [errorMessage]=\"step.errorMessage\"\n    [iconOverrides]=\"_iconOverrides\"\n    [disableRipple]=\"disableRipple || !_stepIsNavigable(i, step)\"\n    [color]=\"step.color || color\"></mat-step-header>\n</ng-template>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {CdkStepperNext, CdkStepperPrevious} from '@angular/cdk/stepper';\nimport {Directive} from '@angular/core';\n\n/** <PERSON><PERSON> that moves to the next step in a stepper workflow. */\n@Directive({\n  selector: 'button[matStepperNext]',\n  host: {\n    'class': 'mat-stepper-next',\n    '[type]': 'type',\n  },\n  standalone: true,\n})\nexport class MatStepperNext extends CdkStepperNext {}\n\n/** But<PERSON> that moves to the previous step in a stepper workflow. */\n@Directive({\n  selector: 'button[matStepperPrevious]',\n  host: {\n    'class': 'mat-stepper-previous',\n    '[type]': 'type',\n  },\n  standalone: true,\n})\nexport class MatStepperPrevious extends CdkStepperPrevious {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {PortalModule} from '@angular/cdk/portal';\nimport {CdkStepperModule} from '@angular/cdk/stepper';\nimport {CommonModule} from '@angular/common';\nimport {NgModule} from '@angular/core';\nimport {ErrorStateMatcher, MatCommonModule, MatRippleModule} from '@angular/material/core';\nimport {MatIconModule} from '@angular/material/icon';\nimport {MatStepHeader} from './step-header';\nimport {MatStepLabel} from './step-label';\nimport {MatStep, MatStepper} from './stepper';\nimport {MatStepperNext, MatStepperPrevious} from './stepper-button';\nimport {MatStepperIcon} from './stepper-icon';\nimport {MAT_STEPPER_INTL_PROVIDER} from './stepper-intl';\nimport {MatStepContent} from './step-content';\n\n@NgModule({\n  imports: [\n    MatCommonModule,\n    CommonModule,\n    PortalModule,\n    CdkStepperModule,\n    MatIconModule,\n    MatRippleModule,\n    MatStep,\n    MatStepLabel,\n    MatStepper,\n    MatStepperNext,\n    MatStepperPrevious,\n    MatStepHeader,\n    MatStepperIcon,\n    MatStepContent,\n  ],\n  exports: [\n    MatCommonModule,\n    MatStep,\n    MatStepLabel,\n    MatStepper,\n    MatStepperNext,\n    MatStepperPrevious,\n    MatStepHeader,\n    MatStepperIcon,\n    MatStepContent,\n  ],\n  providers: [MAT_STEPPER_INTL_PROVIDER, ErrorStateMatcher],\n})\nexport class MatStepperModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["i1.MatStepperIntl", "i2"], "mappings": ";;;;;;;;;;;;;;;AAeM,MAAO,YAAa,SAAQ,YAAY,CAAA;8GAAjC,YAAY,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAZ,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBAJxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;;ACHD;MAEa,cAAc,CAAA;AAD3B,IAAA,WAAA,GAAA;AAEE;;;AAGG;AACM,QAAA,IAAA,CAAA,OAAO,GAAkB,IAAI,OAAO,EAAQ,CAAC;;QAGtD,IAAa,CAAA,aAAA,GAAW,UAAU,CAAC;;QAGnC,IAAc,CAAA,cAAA,GAAW,WAAW,CAAC;;QAGrC,IAAa,CAAA,aAAA,GAAW,UAAU,CAAC;AACpC,KAAA;8GAfY,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAAd,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,cADF,MAAM,EAAA,CAAA,CAAA,EAAA;;2FAClB,cAAc,EAAA,UAAA,EAAA,CAAA;kBAD1B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;AAkBhC;AACM,SAAU,iCAAiC,CAAC,UAA0B,EAAA;AAC1E,IAAA,OAAO,UAAU,IAAI,IAAI,cAAc,EAAE,CAAC;AAC5C,CAAC;AAED;AACa,MAAA,yBAAyB,GAAG;AACvC,IAAA,OAAO,EAAE,cAAc;AACvB,IAAA,IAAI,EAAE,CAAC,CAAC,IAAI,QAAQ,EAAE,EAAE,IAAI,QAAQ,EAAE,EAAE,cAAc,CAAC,CAAC;AACxD,IAAA,UAAU,EAAE,iCAAiC;;;ACIzC,MAAO,aAAc,SAAQ,aAAa,CAAA;AAiC9C,IAAA,WAAA,CACS,KAAqB,EACpB,aAA2B,EACnC,WAAoC,EACpC,iBAAoC,EAAA;QAEpC,KAAK,CAAC,WAAW,CAAC,CAAC;QALZ,IAAK,CAAA,KAAA,GAAL,KAAK,CAAgB;QACpB,IAAa,CAAA,aAAA,GAAb,aAAa,CAAc;AAKnC,QAAA,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,iBAAiB,CAAC,YAAY,EAAE,CAAC,CAAC;KAC1F;IAED,eAAe,GAAA;QACb,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;KACpD;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,CAAC;QACrC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;KACrD;;IAGQ,KAAK,CAAC,MAAoB,EAAE,OAAsB,EAAA;QACzD,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;SAChE;aAAM;YACL,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;SAC/C;KACF;;IAGD,YAAY,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,KAAK,YAAY,YAAY,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;KAC/D;;IAGD,cAAc,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,KAAK,YAAY,YAAY,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;KAC/D;;IAGD,eAAe,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;KACvC;;IAGD,eAAe,GAAA;QACb,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC;KACH;AAED,IAAA,uBAAuB,CAAC,KAAgB,EAAA;AACtC,QAAA,IAAI,KAAK,IAAI,QAAQ,EAAE;AACrB,YAAA,OAAO,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;SAC5B;AACD,QAAA,IAAI,KAAK,IAAI,MAAM,EAAE;AACnB,YAAA,OAAO,QAAQ,CAAC;SACjB;AACD,QAAA,IAAI,KAAK,IAAI,OAAO,EAAE;AACpB,YAAA,OAAO,SAAS,CAAC;SAClB;AACD,QAAA,OAAO,KAAK,CAAC;KACd;8GAhGU,aAAa,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAA,cAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,YAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAb,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,odC3C1B,62DAoDA,EAAA,MAAA,EAAA,CAAA,m6GAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EDXY,SAAS,EAAE,QAAA,EAAA,2BAAA,EAAA,MAAA,EAAA,CAAA,gBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,gBAAgB,oJAAE,OAAO,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,SAAA,EAAA,SAAA,EAAA,UAAA,CAAA,EAAA,QAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAEnC,aAAa,EAAA,UAAA,EAAA,CAAA;kBAdzB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,iBAAiB,EAGrB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,iBAAiB;AAC1B,wBAAA,SAAS,EAAE,+BAA+B;AAC1C,wBAAA,MAAM,EAAE,KAAK;AACd,qBAAA,EAAA,aAAA,EACc,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,UAAA,EACnC,IAAI,EAAA,OAAA,EACP,CAAC,SAAS,EAAE,gBAAgB,EAAE,OAAO,CAAC,EAAA,QAAA,EAAA,62DAAA,EAAA,MAAA,EAAA,CAAA,m6GAAA,CAAA,EAAA,CAAA;oKAMtC,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAGG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAGG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAGG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAGG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAGG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAGG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAGG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAGG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAGG,KAAK,EAAA,CAAA;sBAAb,KAAK;;;AEvDD,MAAM,qCAAqC,GAAG,OAAO,CAAC;AACtD,MAAM,mCAAmC,GAAG,OAAO,CAAC;AAE3D;;;AAGG;AACU,MAAA,oBAAoB,GAG7B;;AAEF,IAAA,wBAAwB,EAAE,OAAO,CAAC,0BAA0B,EAAE;AAC5D,QAAA,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,EAAC,SAAS,EAAE,0BAA0B,EAAE,UAAU,EAAE,QAAQ,EAAC,CAAC,CAAC;;;;AAIvF,QAAA,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,EAAC,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAC,CAAC,CAAC;AACnE,QAAA,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,EAAC,SAAS,EAAE,yBAAyB,EAAE,UAAU,EAAE,QAAQ,EAAC,CAAC,CAAC;AAClF,QAAA,UAAU,CACR,QAAQ,EACR,KAAK,CAAC;YACJ,OAAO,CAAC,sDAAsD,CAAC;YAC/D,KAAK,CAAC,IAAI,EAAE,YAAY,EAAE,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAC9C,SAAA,CAAC,EACF;AACE,YAAA,MAAM,EAAE,EAAC,mBAAmB,EAAE,qCAAqC,EAAC;SACrE,CACF;KACF,CAAC;;AAGF,IAAA,sBAAsB,EAAE,OAAO,CAAC,wBAAwB,EAAE;AACxD,QAAA,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,EAAC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAC,CAAC,CAAC;AAC/D,QAAA,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,EAAC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAC,CAAC,CAAC;;;;AAI3D,QAAA,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,EAAC,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,SAAS,EAAC,CAAC,CAAC;AAC7D,QAAA,UAAU,CACR,eAAe,EACf,KAAK,CAAC;YACJ,OAAO,CAAC,sDAAsD,CAAC;YAC/D,KAAK,CAAC,IAAI,EAAE,YAAY,EAAE,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAC9C,SAAA,CAAC,EACF;AACE,YAAA,MAAM,EAAE,EAAC,mBAAmB,EAAE,mCAAmC,EAAC;SACnE,CACF;KACF,CAAC;;;AC/CJ;;AAEG;MAKU,cAAc,CAAA;AAIzB,IAAA,WAAA,CAAmB,WAA+C,EAAA;QAA/C,IAAW,CAAA,WAAA,GAAX,WAAW,CAAoC;KAAI;8GAJ3D,cAAc,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,WAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,6BAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,CAAA,gBAAA,EAAA,MAAA,CAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAJ1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,6BAA6B;AACvC,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;gFAG0B,IAAI,EAAA,CAAA;sBAA5B,KAAK;uBAAC,gBAAgB,CAAA;;;ACpBzB;;AAEG;MAKU,cAAc,CAAA;AACzB,IAAA,WAAA,CAAmB,SAA2B,EAAA;QAA3B,IAAS,CAAA,SAAA,GAAT,SAAS,CAAkB;KAAI;8GADvC,cAAc,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,WAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,6BAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAJ1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,6BAA6B;AACvC,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;;AC0DK,MAAO,OAAQ,SAAQ,OAAO,CAAA;AAgBlC,IAAA,WAAA,CACwC,OAAmB,EACrC,kBAAqC,EACjD,iBAAmC,EACC,cAA+B,EAAA;AAE3E,QAAA,KAAK,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;QAJX,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAmB;QACjD,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAkB;AAlBrC,QAAA,IAAA,CAAA,WAAW,GAAG,YAAY,CAAC,KAAK,CAAC;;;QAIJ,IAAS,CAAA,SAAA,GAAiB,SAAU,CAAC;KAkBzE;IAED,kBAAkB,GAAA;QAChB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO;AAC3C,aAAA,IAAI,CACH,SAAS,CAAC,MAAK;AACb,YAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CACvC,GAAG,CAAC,KAAK,IAAI,KAAK,CAAC,YAAY,KAAK,IAAI,CAAC,EACzC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,KAAK,IAAI,CAAC,CAC3C,CAAC;AACJ,SAAC,CAAC,CACH;aACA,SAAS,CAAC,UAAU,IAAG;YACtB,IAAI,UAAU,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACpD,gBAAA,IAAI,CAAC,OAAO,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAkB,CAAC,CAAC;aACzF;AACH,SAAC,CAAC,CAAC;KACN;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;KAChC;;IAGD,YAAY,CAAC,OAA+B,EAAE,IAAwC,EAAA;AACpF,QAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;;;;AAK/E,QAAA,MAAM,gBAAgB,GAAG,CAAC,EAAE,OAAO,IAAI,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC;QAE3E,OAAO,kBAAkB,IAAI,gBAAgB,CAAC;KAC/C;8GAxDU,OAAO,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAiBR,UAAU,CAAC,MAAM,UAAU,CAAC,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAGhB,sBAAsB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AApBjC,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,OAAO,EAbP,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA,EAAC,OAAO,EAAE,iBAAiB,EAAE,WAAW,EAAE,OAAO,EAAC;AAClD,YAAA,EAAC,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAC;AACzC,SAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAea,YAAY,EAMZ,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,cAAc,ECrF9B,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,SAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,2HAIA,4CDiEY,eAAe,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,UAAA,CAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAKd,OAAO,EAAA,UAAA,EAAA,CAAA;kBAhBnB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,UAAU,EAET,SAAA,EAAA;AACT,wBAAA,EAAC,OAAO,EAAE,iBAAiB,EAAE,WAAW,SAAS,EAAC;AAClD,wBAAA,EAAC,OAAO,EAAE,OAAO,EAAE,WAAW,SAAS,EAAC;AACzC,qBAAA,EAAA,aAAA,EACc,iBAAiB,CAAC,IAAI,EAAA,QAAA,EAC3B,SAAS,EACF,eAAA,EAAA,uBAAuB,CAAC,MAAM,cACnC,IAAI,EAAA,OAAA,EACP,CAAC,eAAe,CAAC,EACpB,IAAA,EAAA;wBACJ,QAAQ,EAAE,EAAE;AACb,qBAAA,EAAA,QAAA,EAAA,2HAAA,EAAA,CAAA;;0BAmBE,MAAM;AAAC,oBAAA,IAAA,EAAA,CAAA,UAAU,CAAC,MAAM,UAAU,CAAC,CAAA;;0BACnC,QAAQ;;0BAER,QAAQ;;0BAAI,MAAM;2BAAC,sBAAsB,CAAA;yCAfP,SAAS,EAAA,CAAA;sBAA7C,YAAY;uBAAC,YAAY,CAAA;gBAGjB,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAGyC,YAAY,EAAA,CAAA;sBAA1D,YAAY;AAAC,gBAAA,IAAA,EAAA,CAAA,cAAc,EAAE,EAAC,MAAM,EAAE,KAAK,EAAC,CAAA;;AA0EzC,MAAO,UAAW,SAAQ,UAAU,CAAA;;AA+CxC,IAAA,IACI,iBAAiB,GAAA;QACnB,OAAO,IAAI,CAAC,kBAAkB,CAAC;KAChC;IACD,IAAI,iBAAiB,CAAC,KAAa,EAAA;AACjC,QAAA,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC;KACtE;AAMD,IAAA,WAAA,CACc,GAAmB,EAC/B,iBAAoC,EACpC,UAAmC,EAAA;AAEnC,QAAA,KAAK,CAAC,GAAG,EAAE,iBAAiB,EAAE,UAAU,CAAC,CAAC;;;QA7DN,IAAW,CAAA,WAAA,GAC/C,SAAgD,CAAC;;;QAIK,IAAM,CAAA,MAAA,GAC5D,SAA0C,CAAC;;AAG3B,QAAA,IAAA,CAAA,KAAK,GAAuB,IAAI,SAAS,EAAW,CAAC;;AAMpD,QAAA,IAAA,CAAA,aAAa,GAAuB,IAAI,YAAY,EAAQ,CAAC;AAQhF;;;AAGG;QAEH,IAAa,CAAA,aAAA,GAAqB,KAAK,CAAC;AAExC;;;AAGG;QAEH,IAAc,CAAA,cAAA,GAAqB,KAAK,CAAC;;QAGzC,IAAc,CAAA,cAAA,GAAuD,EAAE,CAAC;;AAG/D,QAAA,IAAA,CAAA,cAAc,GAAG,IAAI,OAAO,EAAkB,CAAC;QAUhD,IAAkB,CAAA,kBAAA,GAAG,EAAE,CAAC;;QAGtB,IAAS,CAAA,SAAA,GAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC;QAQzD,MAAM,QAAQ,GAAG,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;AACjE,QAAA,IAAI,CAAC,WAAW,GAAG,QAAQ,KAAK,sBAAsB,GAAG,UAAU,GAAG,YAAY,CAAC;KACpF;IAEQ,kBAAkB,GAAA;QACzB,KAAK,CAAC,kBAAkB,EAAE,CAAC;QAC3B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAC,IAAI,EAAE,WAAW,EAAC,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;;AAGxF,QAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;YACjE,IAAI,CAAC,aAAa,EAAE,CAAC;AACvB,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,cAAc;aAChB,IAAI;;;;AAIH,QAAA,oBAAoB,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,OAAO,CAAC,EACtF,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAC3B;aACA,SAAS,CAAC,KAAK,IAAG;AACjB,YAAA,IAAK,KAAK,CAAC,OAAoC,KAAK,SAAS,EAAE;AAC7D,gBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;aAC3B;AACH,SAAC,CAAC,CAAC;KACN;IAED,gBAAgB,CAAC,KAAa,EAAE,IAAa,EAAA;AAC3C,QAAA,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,aAAa,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;KACvE;IAED,qBAAqB,GAAA;AACnB,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,OAAO,IAAI,CAAC,iBAAiB,CAAC;SAC/B;AAED,QAAA,OAAO,IAAI,CAAC,WAAW,KAAK,YAAY;AACtC,cAAE,qCAAqC;cACrC,mCAAmC,CAAC;KACzC;8GAzGU,UAAU,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAC,IAAA,CAAA,cAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAV,UAAU,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,yEAAA,EAAA,MAAA,EAAA,EAAA,aAAA,EAAA,eAAA,EAAA,KAAA,EAAA,OAAA,EAAA,aAAA,EAAA,eAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,aAAA,EAAA,eAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,SAAA,EAAA,EAAA,UAAA,EAAA,EAAA,8BAAA,EAAA,gCAAA,EAAA,4BAAA,EAAA,8BAAA,EAAA,sCAAA,EAAA,4DAAA,EAAA,yCAAA,EAAA,+DAAA,EAAA,0CAAA,EAAA,+BAAA,EAAA,uBAAA,EAAA,aAAA,EAAA,EAAA,EAAA,SAAA,EANV,CAAC,EAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAC,CAAC,iDAc1C,OAAO,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,QAAA,EAAA,SAAA,EAOP,cAAc,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,aAAA,EAAA,SAAA,EAZjB,aAAa,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,YAAA,EAAA,oBAAA,EAAA,sBAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EElK7B,w/HA+FA,EF8DY,MAAA,EAAA,CAAA,woJAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,gBAAgB,EAAE,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,aAAa,EAR7B,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAAA,cAAA,EAAA,eAAA,EAAA,OAAA,EAAA,UAAA,EAAA,QAAA,EAAA,UAAA,EAAA,eAAA,EAAA,OAAA,CAAA,EAAA,CAAA,EAAA,UAAA,EAAA;AACV,YAAA,oBAAoB,CAAC,wBAAwB;AAC7C,YAAA,oBAAoB,CAAC,sBAAsB;AAC5C,SAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAOU,UAAU,EAAA,UAAA,EAAA,CAAA;kBA1BtB,SAAS;+BACE,yEAAyE,EAAA,QAAA,EACzE,sDAAsD,EAG1D,IAAA,EAAA;AACJ,wBAAA,gCAAgC,EAAE,8BAA8B;AAChE,wBAAA,8BAA8B,EAAE,4BAA4B;AAC5D,wBAAA,wCAAwC,EACtC,wDAAwD;AAC1D,wBAAA,2CAA2C,EACzC,2DAA2D;AAC7D,wBAAA,4CAA4C,EAAE,6BAA6B;AAC3E,wBAAA,yBAAyB,EAAE,aAAa;AACxC,wBAAA,MAAM,EAAE,SAAS;qBAClB,EACW,UAAA,EAAA;AACV,wBAAA,oBAAoB,CAAC,wBAAwB;AAC7C,wBAAA,oBAAoB,CAAC,sBAAsB;qBAC5C,EACU,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAA,UAAY,EAAC,CAAC,EAC5C,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,MAAM,EAAA,UAAA,EACnC,IAAI,EAAA,OAAA,EACP,CAAC,gBAAgB,EAAE,aAAa,CAAC,EAAA,QAAA,EAAA,w/HAAA,EAAA,MAAA,EAAA,CAAA,woJAAA,CAAA,EAAA,CAAA;;0BA8DvC,QAAQ;kGAzD2B,WAAW,EAAA,CAAA;sBAAhD,YAAY;uBAAC,aAAa,CAAA;gBAK6B,MAAM,EAAA,CAAA;sBAA7D,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,OAAO,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC,CAAA;gBAOS,MAAM,EAAA,CAAA;sBAA3D,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,cAAc,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC,CAAA;gBAGjC,aAAa,EAAA,CAAA;sBAA/B,MAAM;gBAGE,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAGG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAON,aAAa,EAAA,CAAA;sBADZ,KAAK;gBAQN,cAAc,EAAA,CAAA;sBADb,KAAK;gBAWF,iBAAiB,EAAA,CAAA;sBADpB,KAAK;;;AGnMR;AASM,MAAO,cAAe,SAAQ,cAAc,CAAA;8GAArC,cAAc,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,wBAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,MAAA,EAAA,EAAA,cAAA,EAAA,kBAAA,EAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAR1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,wBAAwB;AAClC,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,kBAAkB;AAC3B,wBAAA,QAAQ,EAAE,MAAM;AACjB,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;AAGD;AASM,MAAO,kBAAmB,SAAQ,kBAAkB,CAAA;8GAA7C,kBAAkB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAlB,kBAAkB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,4BAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,MAAA,EAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAlB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAR9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,4BAA4B;AACtC,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,sBAAsB;AAC/B,wBAAA,QAAQ,EAAE,MAAM;AACjB,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;;MCsBY,gBAAgB,CAAA;8GAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAhB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,YA5BzB,eAAe;YACf,YAAY;YACZ,YAAY;YACZ,gBAAgB;YAChB,aAAa;YACb,eAAe;YACf,OAAO;YACP,YAAY;YACZ,UAAU;YACV,cAAc;YACd,kBAAkB;YAClB,aAAa;YACb,cAAc;AACd,YAAA,cAAc,aAGd,eAAe;YACf,OAAO;YACP,YAAY;YACZ,UAAU;YACV,cAAc;YACd,kBAAkB;YAClB,aAAa;YACb,cAAc;YACd,cAAc,CAAA,EAAA,CAAA,CAAA,EAAA;AAIL,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,aAFhB,CAAC,yBAAyB,EAAE,iBAAiB,CAAC,YA1BvD,eAAe;YACf,YAAY;YACZ,YAAY;YACZ,gBAAgB;YAChB,aAAa;YACb,eAAe;YAGf,UAAU;AAGV,YAAA,aAAa,EAKb,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAYN,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBA9B5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE;wBACP,eAAe;wBACf,YAAY;wBACZ,YAAY;wBACZ,gBAAgB;wBAChB,aAAa;wBACb,eAAe;wBACf,OAAO;wBACP,YAAY;wBACZ,UAAU;wBACV,cAAc;wBACd,kBAAkB;wBAClB,aAAa;wBACb,cAAc;wBACd,cAAc;AACf,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,eAAe;wBACf,OAAO;wBACP,YAAY;wBACZ,UAAU;wBACV,cAAc;wBACd,kBAAkB;wBAClB,aAAa;wBACb,cAAc;wBACd,cAAc;AACf,qBAAA;AACD,oBAAA,SAAS,EAAE,CAAC,yBAAyB,EAAE,iBAAiB,CAAC;AAC1D,iBAAA,CAAA;;;ACnDD;;AAEG;;;;"}