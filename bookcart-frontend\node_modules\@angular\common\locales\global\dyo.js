/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['dyo'] = ["dyo",[["AM","PM"],u,u],u,[["D","T","T","A","A","A","S"],["Dim","Ten","Tal","Ala","Ara","Arj","Sib"],["Dimas","Teneŋ","Talata","<PERSON>arbay","<PERSON>misay","<PERSON><PERSON><PERSON><PERSON>","<PERSON>bit<PERSON>"],["Dim","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON>rj","Sib"]],u,[["S","F","M","A","M","S","S","U","S","O","N","D"],["Sa","<PERSON>","Ma","Ab","Me","Su","Sú","Ut","<PERSON>","Ok","<PERSON>","De"],["<PERSON>vie","<PERSON><PERSON>birie","<PERSON>","<PERSON>ril","<PERSON>e","<PERSON>ŋ","<PERSON><PERSON>uyee","Ut","<PERSON>tembar","Oktobar","<PERSON>embar","<PERSON><PERSON>mbar"]],u,[["ArY","AtY"],u,["Ariŋuu Yeesu","Atooŋe Yeesu"]],1,[6,0],["d/M/y","d MMM y","d MMMM y","EEEE d MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[","," ",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","#,##0.00 ¤","#E0"],"XOF","F CFA","seefa yati BCEAO",{"JPY":["JP¥","¥"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    