{"version": 3, "file": "form-field.mjs", "sources": ["../../../../../../src/material/form-field/directives/label.ts", "../../../../../../src/material/form-field/directives/error.ts", "../../../../../../src/material/form-field/directives/hint.ts", "../../../../../../src/material/form-field/directives/prefix.ts", "../../../../../../src/material/form-field/directives/suffix.ts", "../../../../../../src/material/form-field/directives/floating-label.ts", "../../../../../../src/material/form-field/directives/line-ripple.ts", "../../../../../../src/material/form-field/directives/notched-outline.ts", "../../../../../../src/material/form-field/directives/notched-outline.html", "../../../../../../src/material/form-field/form-field-animations.ts", "../../../../../../src/material/form-field/form-field-control.ts", "../../../../../../src/material/form-field/form-field-errors.ts", "../../../../../../src/material/form-field/form-field.ts", "../../../../../../src/material/form-field/form-field.html", "../../../../../../src/material/form-field/module.ts", "../../../../../../src/material/form-field/form-field_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive} from '@angular/core';\n\n/** The floating label for a `mat-form-field`. */\n@Directive({\n  selector: 'mat-label',\n  standalone: true,\n})\nexport class MatLabel {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Attribute, Directive, ElementRef, InjectionToken, Input} from '@angular/core';\n\nlet nextUniqueId = 0;\n\n/**\n * Injection token that can be used to reference instances of `MatError`. It serves as\n * alternative token to the actual `MatError` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nexport const MAT_ERROR = new InjectionToken<MatError>('MatError');\n\n/** Single error message to be shown underneath the form-field. */\n@Directive({\n  selector: 'mat-error, [matError]',\n  host: {\n    'class': 'mat-mdc-form-field-error mat-mdc-form-field-bottom-align',\n    'aria-atomic': 'true',\n    '[id]': 'id',\n  },\n  providers: [{provide: MAT_ERROR, useExisting: MatError}],\n  standalone: true,\n})\nexport class MatError {\n  @Input() id: string = `mat-mdc-error-${nextUniqueId++}`;\n\n  constructor(@Attribute('aria-live') ariaLive: string, elementRef: ElementRef) {\n    // If no aria-live value is set add 'polite' as a default. This is preferred over setting\n    // role='alert' so that screen readers do not interrupt the current task to read this aloud.\n    if (!ariaLive) {\n      elementRef.nativeElement.setAttribute('aria-live', 'polite');\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, Input} from '@angular/core';\n\nlet nextUniqueId = 0;\n\n/** Hint text to be shown underneath the form field control. */\n@Directive({\n  selector: 'mat-hint',\n  host: {\n    'class': 'mat-mdc-form-field-hint mat-mdc-form-field-bottom-align',\n    '[class.mat-mdc-form-field-hint-end]': 'align === \"end\"',\n    '[id]': 'id',\n    // Remove align attribute to prevent it from interfering with layout.\n    '[attr.align]': 'null',\n  },\n  standalone: true,\n})\nexport class MatHint {\n  /** Whether to align the hint label at the start or end of the line. */\n  @Input() align: 'start' | 'end' = 'start';\n\n  /** Unique ID for the hint. Used for the aria-describedby on the form field control. */\n  @Input() id: string = `mat-mdc-hint-${nextUniqueId++}`;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, InjectionToken, Input} from '@angular/core';\n\n/**\n * Injection token that can be used to reference instances of `MatPrefix`. It serves as\n * alternative token to the actual `MatPrefix` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nexport const MAT_PREFIX = new InjectionToken<MatPrefix>('MatPrefix');\n\n/** Prefix to be placed in front of the form field. */\n@Directive({\n  selector: '[matPrefix], [matIconPrefix], [matTextPrefix]',\n  providers: [{provide: MAT_PREFIX, useExisting: MatPrefix}],\n  standalone: true,\n})\nexport class MatPrefix {\n  @Input('matTextPrefix')\n  set _isTextSelector(value: '') {\n    this._isText = true;\n  }\n\n  _isText = false;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, InjectionToken, Input} from '@angular/core';\n\n/**\n * Injection token that can be used to reference instances of `MatSuffix`. It serves as\n * alternative token to the actual `MatSuffix` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nexport const MAT_SUFFIX = new InjectionToken<MatSuffix>('MatSuffix');\n\n/** Suffix to be placed at the end of the form field. */\n@Directive({\n  selector: '[matSuffix], [matIconSuffix], [matTextSuffix]',\n  providers: [{provide: MAT_SUFFIX, useExisting: MatSuffix}],\n  standalone: true,\n})\nexport class MatSuffix {\n  @Input('matTextSuffix')\n  set _isTextSelector(value: '') {\n    this._isText = true;\n  }\n\n  _isText = false;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Directive,\n  ElementRef,\n  inject,\n  Input,\n  NgZone,\n  OnDestroy,\n  InjectionToken,\n} from '@angular/core';\nimport {SharedResizeObserver} from '@angular/cdk/observers/private';\nimport {Subscription} from 'rxjs';\n\n/** An interface that the parent form-field should implement to receive resize events. */\nexport interface FloatingLabelParent {\n  _handleLabelResized(): void;\n}\n\n/** An injion token for the parent form-field. */\nexport const FLOATING_LABEL_PARENT = new InjectionToken<FloatingLabelParent>('FloatingLabelParent');\n\n/**\n * Internal directive that maintains a MDC floating label. This directive does not\n * use the `MDCFloatingLabelFoundation` class, as it is not worth the size cost of\n * including it just to measure the label width and toggle some classes.\n *\n * The use of a directive allows us to conditionally render a floating label in the\n * template without having to manually manage instantiation and destruction of the\n * floating label component based on.\n *\n * The component is responsible for setting up the floating label styles, measuring label\n * width for the outline notch, and providing inputs that can be used to toggle the\n * label's floating or required state.\n */\n@Directive({\n  selector: 'label[matFormFieldFloatingLabel]',\n  host: {\n    'class': 'mdc-floating-label mat-mdc-floating-label',\n    '[class.mdc-floating-label--float-above]': 'floating',\n  },\n  standalone: true,\n})\nexport class MatFormFieldFloatingLabel implements OnDestroy {\n  /** Whether the label is floating. */\n  @Input()\n  get floating() {\n    return this._floating;\n  }\n  set floating(value: boolean) {\n    this._floating = value;\n    if (this.monitorResize) {\n      this._handleResize();\n    }\n  }\n  private _floating = false;\n\n  /** Whether to monitor for resize events on the floating label. */\n  @Input()\n  get monitorResize() {\n    return this._monitorResize;\n  }\n  set monitorResize(value: boolean) {\n    this._monitorResize = value;\n    if (this._monitorResize) {\n      this._subscribeToResize();\n    } else {\n      this._resizeSubscription.unsubscribe();\n    }\n  }\n  private _monitorResize = false;\n\n  /** The shared ResizeObserver. */\n  private _resizeObserver = inject(SharedResizeObserver);\n\n  /** The Angular zone. */\n  private _ngZone = inject(NgZone);\n\n  /** The parent form-field. */\n  private _parent = inject(FLOATING_LABEL_PARENT);\n\n  /** The current resize event subscription. */\n  private _resizeSubscription = new Subscription();\n\n  constructor(private _elementRef: ElementRef<HTMLElement>) {}\n\n  ngOnDestroy() {\n    this._resizeSubscription.unsubscribe();\n  }\n\n  /** Gets the width of the label. Used for the outline notch. */\n  getWidth(): number {\n    return estimateScrollWidth(this._elementRef.nativeElement);\n  }\n\n  /** Gets the HTML element for the floating label. */\n  get element(): HTMLElement {\n    return this._elementRef.nativeElement;\n  }\n\n  /** Handles resize events from the ResizeObserver. */\n  private _handleResize() {\n    // In the case where the label grows in size, the following sequence of events occurs:\n    // 1. The label grows by 1px triggering the ResizeObserver\n    // 2. The notch is expanded to accommodate the entire label\n    // 3. The label expands to its full width, triggering the ResizeObserver again\n    //\n    // This is expected, but If we allow this to all happen within the same macro task it causes an\n    // error: `ResizeObserver loop limit exceeded`. Therefore we push the notch resize out until\n    // the next macro task.\n    setTimeout(() => this._parent._handleLabelResized());\n  }\n\n  /** Subscribes to resize events. */\n  private _subscribeToResize() {\n    this._resizeSubscription.unsubscribe();\n    this._ngZone.runOutsideAngular(() => {\n      this._resizeSubscription = this._resizeObserver\n        .observe(this._elementRef.nativeElement, {box: 'border-box'})\n        .subscribe(() => this._handleResize());\n    });\n  }\n}\n\n/**\n * Estimates the scroll width of an element.\n * via https://github.com/material-components/material-components-web/blob/c0a11ef0d000a098fd0c372be8f12d6a99302855/packages/mdc-dom/ponyfill.ts\n */\nfunction estimateScrollWidth(element: HTMLElement): number {\n  // Check the offsetParent. If the element inherits display: none from any\n  // parent, the offsetParent property will be null (see\n  // https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/offsetParent).\n  // This check ensures we only clone the node when necessary.\n  const htmlEl = element as HTMLElement;\n  if (htmlEl.offsetParent !== null) {\n    return htmlEl.scrollWidth;\n  }\n\n  const clone = htmlEl.cloneNode(true) as HTMLElement;\n  clone.style.setProperty('position', 'absolute');\n  clone.style.setProperty('transform', 'translate(-9999px, -9999px)');\n  document.documentElement.appendChild(clone);\n  const scrollWidth = clone.scrollWidth;\n  clone.remove();\n  return scrollWidth;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, ElementRef, NgZone, OnDestroy} from '@angular/core';\n\n/** Class added when the line ripple is active. */\nconst ACTIVATE_CLASS = 'mdc-line-ripple--active';\n\n/** Class added when the line ripple is being deactivated. */\nconst DEACTIVATING_CLASS = 'mdc-line-ripple--deactivating';\n\n/**\n * Internal directive that creates an instance of the MDC line-ripple component. Using a\n * directive allows us to conditionally render a line-ripple in the template without having\n * to manually create and destroy the `MDCLineRipple` component whenever the condition changes.\n *\n * The directive sets up the styles for the line-ripple and provides an API for activating\n * and deactivating the line-ripple.\n */\n@Directive({\n  selector: 'div[matFormFieldLineRipple]',\n  host: {\n    'class': 'mdc-line-ripple',\n  },\n  standalone: true,\n})\nexport class MatFormFieldLineRipple implements OnDestroy {\n  constructor(\n    private _elementRef: ElementRef<HTMLElement>,\n    ngZone: NgZone,\n  ) {\n    ngZone.runOutsideAngular(() => {\n      _elementRef.nativeElement.addEventListener('transitionend', this._handleTransitionEnd);\n    });\n  }\n\n  activate() {\n    const classList = this._elementRef.nativeElement.classList;\n    classList.remove(DEACTIVATING_CLASS);\n    classList.add(ACTIVATE_CLASS);\n  }\n\n  deactivate() {\n    this._elementRef.nativeElement.classList.add(DEACTIVATING_CLASS);\n  }\n\n  private _handleTransitionEnd = (event: TransitionEvent) => {\n    const classList = this._elementRef.nativeElement.classList;\n    const isDeactivating = classList.contains(DEACTIVATING_CLASS);\n\n    if (event.propertyName === 'opacity' && isDeactivating) {\n      classList.remove(ACTIVATE_CLASS, DEACTIVATING_CLASS);\n    }\n  };\n\n  ngOnDestroy() {\n    this._elementRef.nativeElement.removeEventListener('transitionend', this._handleTransitionEnd);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  Component,\n  ElementRef,\n  Input,\n  NgZone,\n  ViewChild,\n  ViewEncapsulation,\n} from '@angular/core';\n\n/**\n * Internal component that creates an instance of the MDC notched-outline component.\n *\n * The component sets up the HTML structure and styles for the notched-outline. It provides\n * inputs to toggle the notch state and width.\n */\n@Component({\n  selector: 'div[matFormFieldNotchedOutline]',\n  templateUrl: './notched-outline.html',\n  host: {\n    'class': 'mdc-notched-outline',\n    // Besides updating the notch state through the MDC component, we toggle this class through\n    // a host binding in order to ensure that the notched-outline renders correctly on the server.\n    '[class.mdc-notched-outline--notched]': 'open',\n  },\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  standalone: true,\n})\nexport class MatFormFieldNotchedOutline implements AfterViewInit {\n  /** Whether the notch should be opened. */\n  @Input('matFormFieldNotchedOutlineOpen') open: boolean = false;\n\n  @ViewChild('notch') _notch: ElementRef;\n\n  constructor(\n    private _elementRef: ElementRef<HTMLElement>,\n    private _ngZone: NgZone,\n  ) {}\n\n  ngAfterViewInit(): void {\n    const label = this._elementRef.nativeElement.querySelector<HTMLElement>('.mdc-floating-label');\n    if (label) {\n      this._elementRef.nativeElement.classList.add('mdc-notched-outline--upgraded');\n\n      if (typeof requestAnimationFrame === 'function') {\n        label.style.transitionDuration = '0s';\n        this._ngZone.runOutsideAngular(() => {\n          requestAnimationFrame(() => (label.style.transitionDuration = ''));\n        });\n      }\n    } else {\n      this._elementRef.nativeElement.classList.add('mdc-notched-outline--no-label');\n    }\n  }\n\n  _setNotchWidth(labelWidth: number) {\n    if (!this.open || !labelWidth) {\n      this._notch.nativeElement.style.width = '';\n    } else {\n      const NOTCH_ELEMENT_PADDING = 8;\n      const NOTCH_ELEMENT_BORDER = 1;\n      this._notch.nativeElement.style.width = `calc(${labelWidth}px * var(--mat-mdc-form-field-floating-label-scale, 0.75) + ${\n        NOTCH_ELEMENT_PADDING + NOTCH_ELEMENT_BORDER\n      }px)`;\n    }\n  }\n}\n", "<div class=\"mdc-notched-outline__leading\"></div>\n<div class=\"mdc-notched-outline__notch\" #notch>\n  <ng-content></ng-content>\n</div>\n<div class=\"mdc-notched-outline__trailing\"></div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {\n  animate,\n  state,\n  style,\n  transition,\n  trigger,\n  AnimationTriggerMetadata,\n} from '@angular/animations';\n\n/**\n * Animations used by the MatFormField.\n * @docs-private\n */\nexport const matFormFieldAnimations: {\n  readonly transitionMessages: AnimationTriggerMetadata;\n} = {\n  /** Animation that transitions the form field's error and hint messages. */\n  transitionMessages: trigger('transitionMessages', [\n    // TODO(mmalerba): Use angular animations for label animation as well.\n    state('enter', style({opacity: 1, transform: 'translateY(0%)'})),\n    transition('void => enter', [\n      style({opacity: 0, transform: 'translateY(-5px)'}),\n      animate('300ms cubic-bezier(0.55, 0, 0.55, 0.2)'),\n    ]),\n  ]),\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Observable} from 'rxjs';\nimport {AbstractControlDirective, NgControl} from '@angular/forms';\nimport {Directive} from '@angular/core';\n\n/** An interface which allows a control to work inside of a `MatFormField`. */\n@Directive()\nexport abstract class MatFormFieldControl<T> {\n  /** The value of the control. */\n  value: T | null;\n\n  /**\n   * Stream that emits whenever the state of the control changes such that the parent `MatFormField`\n   * needs to run change detection.\n   */\n  readonly stateChanges: Observable<void>;\n\n  /** The element ID for this control. */\n  readonly id: string;\n\n  /** The placeholder for this control. */\n  readonly placeholder: string;\n\n  /** Gets the AbstractControlDirective for this control. */\n  readonly ngControl: NgControl | AbstractControlDirective | null;\n\n  /** Whether the control is focused. */\n  readonly focused: boolean;\n\n  /** Whether the control is empty. */\n  readonly empty: boolean;\n\n  /** Whether the `MatForm<PERSON>ield` label should try to float. */\n  readonly shouldLabelFloat: boolean;\n\n  /** Whether the control is required. */\n  readonly required: boolean;\n\n  /** Whether the control is disabled. */\n  readonly disabled: boolean;\n\n  /** Whether the control is in an error state. */\n  readonly errorState: boolean;\n\n  /**\n   * An optional name for the control type that can be used to distinguish `mat-form-field` elements\n   * based on their control type. The form field will add a class,\n   * `mat-form-field-type-{{controlType}}` to its root element.\n   */\n  readonly controlType?: string;\n\n  /**\n   * Whether the input is currently in an autofilled state. If property is not present on the\n   * control it is assumed to be false.\n   */\n  readonly autofilled?: boolean;\n\n  /**\n   * Value of `aria-describedby` that should be merged with the described-by ids\n   * which are set by the form-field.\n   */\n  readonly userAriaDescribedBy?: string;\n\n  /**\n   * Whether to automatically assign the ID of the form field as the `for` attribute\n   * on the `<label>` inside the form field. Set this to true to prevent the form\n   * field from associating the label with non-native elements.\n   */\n  readonly disableAutomaticLabeling?: boolean;\n\n  /** Sets the list of element IDs that currently describe this control. */\n  abstract setDescribedByIds(ids: string[]): void;\n\n  /** Handles a click on the control's container. */\n  abstract onContainerClick(event: MouseEvent): void;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** @docs-private */\nexport function getMatFormFieldPlaceholderConflictError(): Error {\n  return Error('Placeholder attribute and child element were both specified.');\n}\n\n/** @docs-private */\nexport function getMatFormFieldDuplicatedHintError(align: string): Error {\n  return Error(`A hint was already declared for 'align=\"${align}\"'.`);\n}\n\n/** @docs-private */\nexport function getMatFormFieldMissingControlError(): Error {\n  return Error('mat-form-field must contain a MatFormFieldControl.');\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {Directionality} from '@angular/cdk/bidi';\nimport {Platform} from '@angular/cdk/platform';\nimport {\n  AfterContentChecked,\n  AfterContentInit,\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChild,\n  ContentChildren,\n  ElementRef,\n  Inject,\n  InjectionToken,\n  Input,\n  NgZone,\n  OnDestroy,\n  Optional,\n  QueryList,\n  ViewChild,\n  ViewEncapsulation,\n  ANIMATION_MODULE_TYPE,\n} from '@angular/core';\nimport {AbstractControlDirective} from '@angular/forms';\nimport {ThemePalette} from '@angular/material/core';\nimport {merge, Subject} from 'rxjs';\nimport {takeUntil} from 'rxjs/operators';\nimport {MAT_ERROR, MatError} from './directives/error';\nimport {\n  FLOATING_LABEL_PARENT,\n  Floating<PERSON>abelParent,\n  MatFormFieldFloatingLabel,\n} from './directives/floating-label';\nimport {MatHint} from './directives/hint';\nimport {MatLabel} from './directives/label';\nimport {MatFormFieldLineRipple} from './directives/line-ripple';\nimport {MatFormFieldNotchedOutline} from './directives/notched-outline';\nimport {MAT_PREFIX, MatPrefix} from './directives/prefix';\nimport {MAT_SUFFIX, MatSuffix} from './directives/suffix';\nimport {BooleanInput, coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {matFormFieldAnimations} from './form-field-animations';\nimport {MatFormFieldControl as _MatFormFieldControl} from './form-field-control';\nimport {\n  getMatFormFieldDuplicatedHintError,\n  getMatFormFieldMissingControlError,\n} from './form-field-errors';\nimport {DOCUMENT, NgTemplateOutlet} from '@angular/common';\n\n/** Type for the available floatLabel values. */\nexport type FloatLabelType = 'always' | 'auto';\n\n/** Possible appearance styles for the form field. */\nexport type MatFormFieldAppearance = 'fill' | 'outline';\n\n/** Behaviors for how the subscript height is set. */\nexport type SubscriptSizing = 'fixed' | 'dynamic';\n\n/**\n * Represents the default options for the form field that can be configured\n * using the `MAT_FORM_FIELD_DEFAULT_OPTIONS` injection token.\n */\nexport interface MatFormFieldDefaultOptions {\n  /** Default form field appearance style. */\n  appearance?: MatFormFieldAppearance;\n  /** Default color of the form field. */\n  color?: ThemePalette;\n  /** Whether the required marker should be hidden by default. */\n  hideRequiredMarker?: boolean;\n  /**\n   * Whether the label for form fields should by default float `always`,\n   * `never`, or `auto` (only when necessary).\n   */\n  floatLabel?: FloatLabelType;\n  /** Whether the form field should reserve space for one line by default. */\n  subscriptSizing?: SubscriptSizing;\n}\n\n/**\n * Injection token that can be used to inject an instances of `MatFormField`. It serves\n * as alternative token to the actual `MatFormField` class which would cause unnecessary\n * retention of the `MatFormField` class and its component metadata.\n */\nexport const MAT_FORM_FIELD = new InjectionToken<MatFormField>('MatFormField');\n\n/**\n * Injection token that can be used to configure the\n * default options for all form field within an app.\n */\nexport const MAT_FORM_FIELD_DEFAULT_OPTIONS = new InjectionToken<MatFormFieldDefaultOptions>(\n  'MAT_FORM_FIELD_DEFAULT_OPTIONS',\n);\n\nlet nextUniqueId = 0;\n\n/** Default appearance used by the form field. */\nconst DEFAULT_APPEARANCE: MatFormFieldAppearance = 'fill';\n\n/**\n * Whether the label for form fields should by default float `always`,\n * `never`, or `auto`.\n */\nconst DEFAULT_FLOAT_LABEL: FloatLabelType = 'auto';\n\n/** Default way that the subscript element height is set. */\nconst DEFAULT_SUBSCRIPT_SIZING: SubscriptSizing = 'fixed';\n\n/**\n * Default transform for docked floating labels in a MDC text-field. This value has been\n * extracted from the MDC text-field styles because we programmatically modify the docked\n * label transform, but do not want to accidentally discard the default label transform.\n */\nconst FLOATING_LABEL_DEFAULT_DOCKED_TRANSFORM = `translateY(-50%)`;\n\n/**\n * Despite `MatFormFieldControl` being an abstract class, most of our usages enforce its shape\n * using `implements` instead of `extends`. This appears to be problematic when Closure compiler\n * is configured to use type information to rename properties, because it can't figure out which\n * class properties are coming from. This interface seems to work around the issue while preserving\n * our type safety (alternative being using `any` everywhere).\n * @docs-private\n */\ninterface MatFormFieldControl<T> extends _MatFormFieldControl<T> {}\n\n/** Container for form controls that applies Material Design styling and behavior. */\n@Component({\n  selector: 'mat-form-field',\n  exportAs: 'matFormField',\n  templateUrl: './form-field.html',\n  styleUrl: './form-field.css',\n  animations: [matFormFieldAnimations.transitionMessages],\n  host: {\n    'class': 'mat-mdc-form-field',\n    '[class.mat-mdc-form-field-label-always-float]': '_shouldAlwaysFloat()',\n    '[class.mat-mdc-form-field-has-icon-prefix]': '_hasIconPrefix',\n    '[class.mat-mdc-form-field-has-icon-suffix]': '_hasIconSuffix',\n    // Note that these classes reuse the same names as the non-MDC version, because they can be\n    // considered a public API since custom form controls may use them to style themselves.\n    // See https://github.com/angular/components/pull/20502#discussion_r486124901.\n    '[class.mat-form-field-invalid]': '_control.errorState',\n    '[class.mat-form-field-disabled]': '_control.disabled',\n    '[class.mat-form-field-autofilled]': '_control.autofilled',\n    '[class.mat-form-field-no-animations]': '_animationMode === \"NoopAnimations\"',\n    '[class.mat-form-field-appearance-fill]': 'appearance == \"fill\"',\n    '[class.mat-form-field-appearance-outline]': 'appearance == \"outline\"',\n    '[class.mat-form-field-hide-placeholder]': '_hasFloatingLabel() && !_shouldLabelFloat()',\n    '[class.mat-focused]': '_control.focused',\n    '[class.mat-primary]': 'color !== \"accent\" && color !== \"warn\"',\n    '[class.mat-accent]': 'color === \"accent\"',\n    '[class.mat-warn]': 'color === \"warn\"',\n    '[class.ng-untouched]': '_shouldForward(\"untouched\")',\n    '[class.ng-touched]': '_shouldForward(\"touched\")',\n    '[class.ng-pristine]': '_shouldForward(\"pristine\")',\n    '[class.ng-dirty]': '_shouldForward(\"dirty\")',\n    '[class.ng-valid]': '_shouldForward(\"valid\")',\n    '[class.ng-invalid]': '_shouldForward(\"invalid\")',\n    '[class.ng-pending]': '_shouldForward(\"pending\")',\n  },\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  providers: [\n    {provide: MAT_FORM_FIELD, useExisting: MatFormField},\n    {provide: FLOATING_LABEL_PARENT, useExisting: MatFormField},\n  ],\n  standalone: true,\n  imports: [\n    MatFormFieldFloatingLabel,\n    MatFormFieldNotchedOutline,\n    NgTemplateOutlet,\n    MatFormFieldLineRipple,\n    MatHint,\n  ],\n})\nexport class MatFormField\n  implements FloatingLabelParent, AfterContentInit, AfterContentChecked, AfterViewInit, OnDestroy\n{\n  @ViewChild('textField') _textField: ElementRef<HTMLElement>;\n  @ViewChild('iconPrefixContainer') _iconPrefixContainer: ElementRef<HTMLElement>;\n  @ViewChild('textPrefixContainer') _textPrefixContainer: ElementRef<HTMLElement>;\n  @ViewChild(MatFormFieldFloatingLabel) _floatingLabel: MatFormFieldFloatingLabel | undefined;\n  @ViewChild(MatFormFieldNotchedOutline) _notchedOutline: MatFormFieldNotchedOutline | undefined;\n  @ViewChild(MatFormFieldLineRipple) _lineRipple: MatFormFieldLineRipple | undefined;\n\n  @ContentChild(MatLabel) _labelChildNonStatic: MatLabel | undefined;\n  @ContentChild(MatLabel, {static: true}) _labelChildStatic: MatLabel | undefined;\n  @ContentChild(_MatFormFieldControl) _formFieldControl: MatFormFieldControl<any>;\n  @ContentChildren(MAT_PREFIX, {descendants: true}) _prefixChildren: QueryList<MatPrefix>;\n  @ContentChildren(MAT_SUFFIX, {descendants: true}) _suffixChildren: QueryList<MatSuffix>;\n  @ContentChildren(MAT_ERROR, {descendants: true}) _errorChildren: QueryList<MatError>;\n  @ContentChildren(MatHint, {descendants: true}) _hintChildren: QueryList<MatHint>;\n\n  /** Whether the required marker should be hidden. */\n  @Input()\n  get hideRequiredMarker(): boolean {\n    return this._hideRequiredMarker;\n  }\n  set hideRequiredMarker(value: BooleanInput) {\n    this._hideRequiredMarker = coerceBooleanProperty(value);\n  }\n  private _hideRequiredMarker = false;\n\n  /** The color palette for the form field. */\n  @Input() color: ThemePalette = 'primary';\n\n  /** Whether the label should always float or float as the user types. */\n  @Input()\n  get floatLabel(): FloatLabelType {\n    return this._floatLabel || this._defaults?.floatLabel || DEFAULT_FLOAT_LABEL;\n  }\n  set floatLabel(value: FloatLabelType) {\n    if (value !== this._floatLabel) {\n      this._floatLabel = value;\n      // For backwards compatibility. Custom form field controls or directives might set\n      // the \"floatLabel\" input and expect the form field view to be updated automatically.\n      // e.g. autocomplete trigger. Ideally we'd get rid of this and the consumers would just\n      // emit the \"stateChanges\" observable. TODO(devversion): consider removing.\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  private _floatLabel: FloatLabelType;\n\n  /** The form field appearance style. */\n  @Input()\n  get appearance(): MatFormFieldAppearance {\n    return this._appearance;\n  }\n  set appearance(value: MatFormFieldAppearance) {\n    const oldValue = this._appearance;\n    const newAppearance = value || this._defaults?.appearance || DEFAULT_APPEARANCE;\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (newAppearance !== 'fill' && newAppearance !== 'outline') {\n        throw new Error(\n          `MatFormField: Invalid appearance \"${newAppearance}\", valid values are \"fill\" or \"outline\".`,\n        );\n      }\n    }\n    this._appearance = newAppearance;\n    if (this._appearance === 'outline' && this._appearance !== oldValue) {\n      // If the appearance has been switched to `outline`, the label offset needs to be updated.\n      // The update can happen once the view has been re-checked, but not immediately because\n      // the view has not been updated and the notched-outline floating label is not present.\n      this._needsOutlineLabelOffsetUpdateOnStable = true;\n    }\n  }\n  private _appearance: MatFormFieldAppearance = DEFAULT_APPEARANCE;\n\n  /**\n   * Whether the form field should reserve space for one line of hint/error text (default)\n   * or to have the spacing grow from 0px as needed based on the size of the hint/error content.\n   * Note that when using dynamic sizing, layout shifts will occur when hint/error text changes.\n   */\n  @Input()\n  get subscriptSizing(): SubscriptSizing {\n    return this._subscriptSizing || this._defaults?.subscriptSizing || DEFAULT_SUBSCRIPT_SIZING;\n  }\n  set subscriptSizing(value: SubscriptSizing) {\n    this._subscriptSizing = value || this._defaults?.subscriptSizing || DEFAULT_SUBSCRIPT_SIZING;\n  }\n  private _subscriptSizing: SubscriptSizing | null = null;\n\n  /** Text for the form field hint. */\n  @Input()\n  get hintLabel(): string {\n    return this._hintLabel;\n  }\n  set hintLabel(value: string) {\n    this._hintLabel = value;\n    this._processHints();\n  }\n  private _hintLabel = '';\n\n  _hasIconPrefix = false;\n  _hasTextPrefix = false;\n  _hasIconSuffix = false;\n  _hasTextSuffix = false;\n\n  // Unique id for the internal form field label.\n  readonly _labelId = `mat-mdc-form-field-label-${nextUniqueId++}`;\n\n  // Unique id for the hint label.\n  readonly _hintLabelId = `mat-mdc-hint-${nextUniqueId++}`;\n\n  /** State of the mat-hint and mat-error animations. */\n  _subscriptAnimationState = '';\n\n  /** Gets the current form field control */\n  get _control(): MatFormFieldControl<any> {\n    return this._explicitFormFieldControl || this._formFieldControl;\n  }\n  set _control(value) {\n    this._explicitFormFieldControl = value;\n  }\n\n  private _destroyed = new Subject<void>();\n  private _isFocused: boolean | null = null;\n  private _explicitFormFieldControl: MatFormFieldControl<any>;\n  private _needsOutlineLabelOffsetUpdateOnStable = false;\n\n  constructor(\n    public _elementRef: ElementRef,\n    private _changeDetectorRef: ChangeDetectorRef,\n    private _ngZone: NgZone,\n    private _dir: Directionality,\n    private _platform: Platform,\n    @Optional()\n    @Inject(MAT_FORM_FIELD_DEFAULT_OPTIONS)\n    private _defaults?: MatFormFieldDefaultOptions,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) public _animationMode?: string,\n    /**\n     * @deprecated not needed, to be removed.\n     * @breaking-change 17.0.0 remove this param\n     */\n    @Inject(DOCUMENT) _unusedDocument?: any,\n  ) {\n    if (_defaults) {\n      if (_defaults.appearance) {\n        this.appearance = _defaults.appearance;\n      }\n      this._hideRequiredMarker = Boolean(_defaults?.hideRequiredMarker);\n      if (_defaults.color) {\n        this.color = _defaults.color;\n      }\n    }\n  }\n\n  ngAfterViewInit() {\n    // Initial focus state sync. This happens rarely, but we want to account for\n    // it in case the form field control has \"focused\" set to true on init.\n    this._updateFocusState();\n    // Enable animations now. This ensures we don't animate on initial render.\n    this._subscriptAnimationState = 'enter';\n    // Because the above changes a value used in the template after it was checked, we need\n    // to trigger CD or the change might not be reflected if there is no other CD scheduled.\n    this._changeDetectorRef.detectChanges();\n  }\n\n  ngAfterContentInit() {\n    this._assertFormFieldControl();\n    this._initializeControl();\n    this._initializeSubscript();\n    this._initializePrefixAndSuffix();\n    this._initializeOutlineLabelOffsetSubscriptions();\n  }\n\n  ngAfterContentChecked() {\n    this._assertFormFieldControl();\n  }\n\n  ngOnDestroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n\n  /**\n   * Gets the id of the label element. If no label is present, returns `null`.\n   */\n  getLabelId(): string | null {\n    return this._hasFloatingLabel() ? this._labelId : null;\n  }\n\n  /**\n   * Gets an ElementRef for the element that a overlay attached to the form field\n   * should be positioned relative to.\n   */\n  getConnectedOverlayOrigin(): ElementRef {\n    return this._textField || this._elementRef;\n  }\n\n  /** Animates the placeholder up and locks it in position. */\n  _animateAndLockLabel(): void {\n    // This is for backwards compatibility only. Consumers of the form field might use\n    // this method. e.g. the autocomplete trigger. This method has been added to the non-MDC\n    // form field because setting \"floatLabel\" to \"always\" caused the label to float without\n    // animation. This is different in MDC where the label always animates, so this method\n    // is no longer necessary. There doesn't seem any benefit in adding logic to allow changing\n    // the floating label state without animations. The non-MDC implementation was inconsistent\n    // because it always animates if \"floatLabel\" is set away from \"always\".\n    // TODO(devversion): consider removing this method when releasing the MDC form field.\n    if (this._hasFloatingLabel()) {\n      this.floatLabel = 'always';\n    }\n  }\n\n  /** Initializes the registered form field control. */\n  private _initializeControl() {\n    const control = this._control;\n\n    if (control.controlType) {\n      this._elementRef.nativeElement.classList.add(\n        `mat-mdc-form-field-type-${control.controlType}`,\n      );\n    }\n\n    // Subscribe to changes in the child control state in order to update the form field UI.\n    control.stateChanges.subscribe(() => {\n      this._updateFocusState();\n      this._syncDescribedByIds();\n      this._changeDetectorRef.markForCheck();\n    });\n\n    // Run change detection if the value changes.\n    if (control.ngControl && control.ngControl.valueChanges) {\n      control.ngControl.valueChanges\n        .pipe(takeUntil(this._destroyed))\n        .subscribe(() => this._changeDetectorRef.markForCheck());\n    }\n  }\n\n  private _checkPrefixAndSuffixTypes() {\n    this._hasIconPrefix = !!this._prefixChildren.find(p => !p._isText);\n    this._hasTextPrefix = !!this._prefixChildren.find(p => p._isText);\n    this._hasIconSuffix = !!this._suffixChildren.find(s => !s._isText);\n    this._hasTextSuffix = !!this._suffixChildren.find(s => s._isText);\n  }\n\n  /** Initializes the prefix and suffix containers. */\n  private _initializePrefixAndSuffix() {\n    this._checkPrefixAndSuffixTypes();\n    // Mark the form field as dirty whenever the prefix or suffix children change. This\n    // is necessary because we conditionally display the prefix/suffix containers based\n    // on whether there is projected content.\n    merge(this._prefixChildren.changes, this._suffixChildren.changes).subscribe(() => {\n      this._checkPrefixAndSuffixTypes();\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n\n  /**\n   * Initializes the subscript by validating hints and synchronizing \"aria-describedby\" ids\n   * with the custom form field control. Also subscribes to hint and error changes in order\n   * to be able to validate and synchronize ids on change.\n   */\n  private _initializeSubscript() {\n    // Re-validate when the number of hints changes.\n    this._hintChildren.changes.subscribe(() => {\n      this._processHints();\n      this._changeDetectorRef.markForCheck();\n    });\n\n    // Update the aria-described by when the number of errors changes.\n    this._errorChildren.changes.subscribe(() => {\n      this._syncDescribedByIds();\n      this._changeDetectorRef.markForCheck();\n    });\n\n    // Initial mat-hint validation and subscript describedByIds sync.\n    this._validateHints();\n    this._syncDescribedByIds();\n  }\n\n  /** Throws an error if the form field's control is missing. */\n  private _assertFormFieldControl() {\n    if (!this._control && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatFormFieldMissingControlError();\n    }\n  }\n\n  private _updateFocusState() {\n    // Usually the MDC foundation would call \"activateFocus\" and \"deactivateFocus\" whenever\n    // certain DOM events are emitted. This is not possible in our implementation of the\n    // form field because we support abstract form field controls which are not necessarily\n    // of type input, nor do we have a reference to a native form field control element. Instead\n    // we handle the focus by checking if the abstract form field control focused state changes.\n    if (this._control.focused && !this._isFocused) {\n      this._isFocused = true;\n      this._lineRipple?.activate();\n    } else if (!this._control.focused && (this._isFocused || this._isFocused === null)) {\n      this._isFocused = false;\n      this._lineRipple?.deactivate();\n    }\n\n    this._textField?.nativeElement.classList.toggle(\n      'mdc-text-field--focused',\n      this._control.focused,\n    );\n  }\n\n  /**\n   * The floating label in the docked state needs to account for prefixes. The horizontal offset\n   * is calculated whenever the appearance changes to `outline`, the prefixes change, or when the\n   * form field is added to the DOM. This method sets up all subscriptions which are needed to\n   * trigger the label offset update. In general, we want to avoid performing measurements often,\n   * so we rely on the `NgZone` as indicator when the offset should be recalculated, instead of\n   * checking every change detection cycle.\n   */\n  private _initializeOutlineLabelOffsetSubscriptions() {\n    // Whenever the prefix changes, schedule an update of the label offset.\n    this._prefixChildren.changes.subscribe(\n      () => (this._needsOutlineLabelOffsetUpdateOnStable = true),\n    );\n\n    // Note that we have to run outside of the `NgZone` explicitly, in order to avoid\n    // throwing users into an infinite loop if `zone-patch-rxjs` is included.\n    this._ngZone.runOutsideAngular(() => {\n      this._ngZone.onStable.pipe(takeUntil(this._destroyed)).subscribe(() => {\n        if (this._needsOutlineLabelOffsetUpdateOnStable) {\n          this._needsOutlineLabelOffsetUpdateOnStable = false;\n          this._updateOutlineLabelOffset();\n        }\n      });\n    });\n\n    this._dir.change\n      .pipe(takeUntil(this._destroyed))\n      .subscribe(() => (this._needsOutlineLabelOffsetUpdateOnStable = true));\n  }\n\n  /** Whether the floating label should always float or not. */\n  _shouldAlwaysFloat() {\n    return this.floatLabel === 'always';\n  }\n\n  _hasOutline() {\n    return this.appearance === 'outline';\n  }\n\n  /**\n   * Whether the label should display in the infix. Labels in the outline appearance are\n   * displayed as part of the notched-outline and are horizontally offset to account for\n   * form field prefix content. This won't work in server side rendering since we cannot\n   * measure the width of the prefix container. To make the docked label appear as if the\n   * right offset has been calculated, we forcibly render the label inside the infix. Since\n   * the label is part of the infix, the label cannot overflow the prefix content.\n   */\n  _forceDisplayInfixLabel() {\n    return !this._platform.isBrowser && this._prefixChildren.length && !this._shouldLabelFloat();\n  }\n\n  _hasFloatingLabel() {\n    return !!this._labelChildNonStatic || !!this._labelChildStatic;\n  }\n\n  _shouldLabelFloat() {\n    return this._control.shouldLabelFloat || this._shouldAlwaysFloat();\n  }\n\n  /**\n   * Determines whether a class from the AbstractControlDirective\n   * should be forwarded to the host element.\n   */\n  _shouldForward(prop: keyof AbstractControlDirective): boolean {\n    const control = this._control ? this._control.ngControl : null;\n    return control && control[prop];\n  }\n\n  /** Determines whether to display hints or errors. */\n  _getDisplayedMessages(): 'error' | 'hint' {\n    return this._errorChildren && this._errorChildren.length > 0 && this._control.errorState\n      ? 'error'\n      : 'hint';\n  }\n\n  /** Handle label resize events. */\n  _handleLabelResized() {\n    this._refreshOutlineNotchWidth();\n  }\n\n  /** Refreshes the width of the outline-notch, if present. */\n  _refreshOutlineNotchWidth() {\n    if (!this._hasOutline() || !this._floatingLabel || !this._shouldLabelFloat()) {\n      this._notchedOutline?._setNotchWidth(0);\n    } else {\n      this._notchedOutline?._setNotchWidth(this._floatingLabel.getWidth());\n    }\n  }\n\n  /** Does any extra processing that is required when handling the hints. */\n  private _processHints() {\n    this._validateHints();\n    this._syncDescribedByIds();\n  }\n\n  /**\n   * Ensure that there is a maximum of one of each \"mat-hint\" alignment specified. The hint\n   * label specified set through the input is being considered as \"start\" aligned.\n   *\n   * This method is a noop if Angular runs in production mode.\n   */\n  private _validateHints() {\n    if (this._hintChildren && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      let startHint: MatHint;\n      let endHint: MatHint;\n      this._hintChildren.forEach((hint: MatHint) => {\n        if (hint.align === 'start') {\n          if (startHint || this.hintLabel) {\n            throw getMatFormFieldDuplicatedHintError('start');\n          }\n          startHint = hint;\n        } else if (hint.align === 'end') {\n          if (endHint) {\n            throw getMatFormFieldDuplicatedHintError('end');\n          }\n          endHint = hint;\n        }\n      });\n    }\n  }\n\n  /**\n   * Sets the list of element IDs that describe the child control. This allows the control to update\n   * its `aria-describedby` attribute accordingly.\n   */\n  private _syncDescribedByIds() {\n    if (this._control) {\n      let ids: string[] = [];\n\n      // TODO(wagnermaciel): Remove the type check when we find the root cause of this bug.\n      if (\n        this._control.userAriaDescribedBy &&\n        typeof this._control.userAriaDescribedBy === 'string'\n      ) {\n        ids.push(...this._control.userAriaDescribedBy.split(' '));\n      }\n\n      if (this._getDisplayedMessages() === 'hint') {\n        const startHint = this._hintChildren\n          ? this._hintChildren.find(hint => hint.align === 'start')\n          : null;\n        const endHint = this._hintChildren\n          ? this._hintChildren.find(hint => hint.align === 'end')\n          : null;\n\n        if (startHint) {\n          ids.push(startHint.id);\n        } else if (this._hintLabel) {\n          ids.push(this._hintLabelId);\n        }\n\n        if (endHint) {\n          ids.push(endHint.id);\n        }\n      } else if (this._errorChildren) {\n        ids.push(...this._errorChildren.map(error => error.id));\n      }\n\n      this._control.setDescribedByIds(ids);\n    }\n  }\n\n  /**\n   * Updates the horizontal offset of the label in the outline appearance. In the outline\n   * appearance, the notched-outline and label are not relative to the infix container because\n   * the outline intends to surround prefixes, suffixes and the infix. This means that the\n   * floating label by default overlaps prefixes in the docked state. To avoid this, we need to\n   * horizontally offset the label by the width of the prefix container. The MDC text-field does\n   * not need to do this because they use a fixed width for prefixes. Hence, they can simply\n   * incorporate the horizontal offset into their default text-field styles.\n   */\n  private _updateOutlineLabelOffset() {\n    if (!this._platform.isBrowser || !this._hasOutline() || !this._floatingLabel) {\n      return;\n    }\n    const floatingLabel = this._floatingLabel.element;\n    // If no prefix is displayed, reset the outline label offset from potential\n    // previous label offset updates.\n    if (!(this._iconPrefixContainer || this._textPrefixContainer)) {\n      floatingLabel.style.transform = '';\n      return;\n    }\n    // If the form field is not attached to the DOM yet (e.g. in a tab), we defer\n    // the label offset update until the zone stabilizes.\n    if (!this._isAttachedToDom()) {\n      this._needsOutlineLabelOffsetUpdateOnStable = true;\n      return;\n    }\n    const iconPrefixContainer = this._iconPrefixContainer?.nativeElement;\n    const textPrefixContainer = this._textPrefixContainer?.nativeElement;\n    const iconPrefixContainerWidth = iconPrefixContainer?.getBoundingClientRect().width ?? 0;\n    const textPrefixContainerWidth = textPrefixContainer?.getBoundingClientRect().width ?? 0;\n    // If the directionality is RTL, the x-axis transform needs to be inverted. This\n    // is because `transformX` does not change based on the page directionality.\n    const negate = this._dir.value === 'rtl' ? '-1' : '1';\n    const prefixWidth = `${iconPrefixContainerWidth + textPrefixContainerWidth}px`;\n    const labelOffset = `var(--mat-mdc-form-field-label-offset-x, 0px)`;\n    const labelHorizontalOffset = `calc(${negate} * (${prefixWidth} + ${labelOffset}))`;\n\n    // Update the translateX of the floating label to account for the prefix container,\n    // but allow the CSS to override this setting via a CSS variable when the label is\n    // floating.\n    floatingLabel.style.transform = `var(\n        --mat-mdc-form-field-label-transform,\n        ${FLOATING_LABEL_DEFAULT_DOCKED_TRANSFORM} translateX(${labelHorizontalOffset})\n    )`;\n  }\n\n  /** Checks whether the form field is attached to the DOM. */\n  private _isAttachedToDom(): boolean {\n    const element: HTMLElement = this._elementRef.nativeElement;\n    if (element.getRootNode) {\n      const rootNode = element.getRootNode();\n      // If the element is inside the DOM the root node will be either the document\n      // or the closest shadow root, otherwise it'll be the element itself.\n      return rootNode && rootNode !== element;\n    }\n    // Otherwise fall back to checking if it's in the document. This doesn't account for\n    // shadow DOM, however browser that support shadow DOM should support `getRootNode` as well.\n    return document.documentElement!.contains(element);\n  }\n}\n", "<ng-template #labelTemplate>\n  <!--\n    MDC recommends that the text-field is a `<label>` element. This rather complicates the\n    setup because it would require every form-field control to explicitly set `aria-labelledby`.\n    This is because the `<label>` itself contains more than the actual label (e.g. prefix, suffix\n    or other projected content), and screen readers could potentially read out undesired content.\n    Excluding elements from being printed out requires them to be marked with `aria-hidden`, or\n    the form control is set to a scoped element for the label (using `aria-labelledby`). Both of\n    these options seem to complicate the setup because we know exactly what content is rendered\n    as part of the label, and we don't want to spend resources on walking through projected content\n    to set `aria-hidden`. Nor do we want to set `aria-labelledby` on every form control if we could\n    simply link the label to the control using the label `for` attribute.\n  -->\n  @if (_hasFloatingLabel()) {\n    <label matFormFieldFloatingLabel\n           [floating]=\"_shouldLabelFloat()\"\n           [monitorResize]=\"_hasOutline()\"\n           [id]=\"_labelId\"\n           [attr.for]=\"_control.disableAutomaticLabeling ? null : _control.id\">\n      <ng-content select=\"mat-label\"></ng-content>\n      <!--\n        We set the required marker as a separate element, in order to make it easier to target if\n        apps want to override it and to be able to set `aria-hidden` so that screen readers don't\n        pick it up.\n       -->\n       @if (!hideRequiredMarker && _control.required) {\n         <span\n           aria-hidden=\"true\"\n           class=\"mat-mdc-form-field-required-marker mdc-floating-label--required\"></span>\n       }\n    </label>\n  }\n</ng-template>\n\n<div class=\"mat-mdc-text-field-wrapper mdc-text-field\" #textField\n     [class.mdc-text-field--filled]=\"!_hasOutline()\"\n     [class.mdc-text-field--outlined]=\"_hasOutline()\"\n     [class.mdc-text-field--no-label]=\"!_hasFloatingLabel()\"\n     [class.mdc-text-field--disabled]=\"_control.disabled\"\n     [class.mdc-text-field--invalid]=\"_control.errorState\"\n     (click)=\"_control.onContainerClick($event)\">\n  @if (!_hasOutline() && !_control.disabled) {\n    <div class=\"mat-mdc-form-field-focus-overlay\"></div>\n  }\n  <div class=\"mat-mdc-form-field-flex\">\n    @if (_hasOutline()) {\n      <div matFormFieldNotchedOutline [matFormFieldNotchedOutlineOpen]=\"_shouldLabelFloat()\">\n        @if (!_forceDisplayInfixLabel()) {\n          <ng-template [ngTemplateOutlet]=\"labelTemplate\"></ng-template>\n        }\n      </div>\n    }\n\n    @if (_hasIconPrefix) {\n      <div class=\"mat-mdc-form-field-icon-prefix\" #iconPrefixContainer>\n        <ng-content select=\"[matPrefix], [matIconPrefix]\"></ng-content>\n      </div>\n    }\n\n    @if (_hasTextPrefix) {\n      <div class=\"mat-mdc-form-field-text-prefix\" #textPrefixContainer>\n        <ng-content select=\"[matTextPrefix]\"></ng-content>\n      </div>\n    }\n\n    <div class=\"mat-mdc-form-field-infix\">\n      @if (!_hasOutline() || _forceDisplayInfixLabel()) {\n        <ng-template [ngTemplateOutlet]=\"labelTemplate\"></ng-template>\n      }\n\n      <ng-content></ng-content>\n    </div>\n\n    @if (_hasTextSuffix) {\n      <div class=\"mat-mdc-form-field-text-suffix\">\n        <ng-content select=\"[matTextSuffix]\"></ng-content>\n      </div>\n    }\n\n    @if (_hasIconSuffix) {\n      <div class=\"mat-mdc-form-field-icon-suffix\">\n        <ng-content select=\"[matSuffix], [matIconSuffix]\"></ng-content>\n      </div>\n    }\n  </div>\n\n  @if (!_hasOutline()) {\n    <div matFormFieldLineRipple></div>\n  }\n</div>\n\n<div class=\"mat-mdc-form-field-subscript-wrapper mat-mdc-form-field-bottom-align\"\n     [class.mat-mdc-form-field-subscript-dynamic-size]=\"subscriptSizing === 'dynamic'\">\n\n  @switch (_getDisplayedMessages()) {\n    @case ('error') {\n      <div class=\"mat-mdc-form-field-error-wrapper\"\n           [@transitionMessages]=\"_subscriptAnimationState\">\n        <ng-content select=\"mat-error, [matError]\"></ng-content>\n      </div>\n    }\n\n    @case ('hint') {\n      <div class=\"mat-mdc-form-field-hint-wrapper\" [@transitionMessages]=\"_subscriptAnimationState\">\n        @if (hintLabel) {\n          <mat-hint [id]=\"_hintLabelId\">{{hintLabel}}</mat-hint>\n        }\n        <ng-content select=\"mat-hint:not([align='end'])\"></ng-content>\n        <div class=\"mat-mdc-form-field-hint-spacer\"></div>\n        <ng-content select=\"mat-hint[align='end']\"></ng-content>\n      </div>\n    }\n  }\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ObserversModule} from '@angular/cdk/observers';\nimport {CommonModule} from '@angular/common';\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '@angular/material/core';\nimport {MatError} from './directives/error';\nimport {MatHint} from './directives/hint';\nimport {MatLabel} from './directives/label';\nimport {MatPrefix} from './directives/prefix';\nimport {MatSuffix} from './directives/suffix';\nimport {MatFormField} from './form-field';\n\n@NgModule({\n  imports: [\n    MatCommonModule,\n    CommonModule,\n    ObserversModule,\n    MatFormField,\n    MatLabel,\n    MatError,\n    MatHint,\n    MatPrefix,\n    MatSuffix,\n  ],\n  exports: [MatFormField, MatLabel, MatHint, MatError, MatPrefix, MatSuffix, MatCommonModule],\n})\nexport class MatFormFieldModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["nextUniqueId", "_MatFormFieldControl"], "mappings": ";;;;;;;;;;;;;AAUA;MAKa,QAAQ,CAAA;8GAAR,QAAQ,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAR,QAAQ,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,WAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAR,QAAQ,EAAA,UAAA,EAAA,CAAA;kBAJpB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,WAAW;AACrB,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;;ACJD,IAAIA,cAAY,GAAG,CAAC,CAAC;AAErB;;;;AAIG;MACU,SAAS,GAAG,IAAI,cAAc,CAAW,UAAU,EAAE;AAElE;MAWa,QAAQ,CAAA;IAGnB,WAAoC,CAAA,QAAgB,EAAE,UAAsB,EAAA;AAFnE,QAAA,IAAA,CAAA,EAAE,GAAW,CAAA,cAAA,EAAiBA,cAAY,EAAE,EAAE,CAAC;;;QAKtD,IAAI,CAAC,QAAQ,EAAE;YACb,UAAU,CAAC,aAAa,CAAC,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;SAC9D;KACF;AATU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,QAAQ,kBAGI,WAAW,EAAA,SAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAHvB,QAAQ,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,uBAAA,EAAA,MAAA,EAAA,EAAA,EAAA,EAAA,IAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,aAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,0DAAA,EAAA,EAAA,SAAA,EAHR,CAAC,EAAC,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAC,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAG7C,QAAQ,EAAA,UAAA,EAAA,CAAA;kBAVpB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,uBAAuB;AACjC,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,0DAA0D;AACnE,wBAAA,aAAa,EAAE,MAAM;AACrB,wBAAA,MAAM,EAAE,IAAI;AACb,qBAAA;oBACD,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,SAAS,EAAE,WAAW,EAAU,QAAA,EAAC,CAAC;AACxD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BAIc,SAAS;2BAAC,WAAW,CAAA;kEAFzB,EAAE,EAAA,CAAA;sBAAV,KAAK;;;ACrBR,IAAIA,cAAY,GAAG,CAAC,CAAC;AAErB;MAYa,OAAO,CAAA;AAXpB,IAAA,WAAA,GAAA;;QAaW,IAAK,CAAA,KAAA,GAAoB,OAAO,CAAC;;AAGjC,QAAA,IAAA,CAAA,EAAE,GAAW,CAAA,aAAA,EAAgBA,cAAY,EAAE,EAAE,CAAC;AACxD,KAAA;8GANY,OAAO,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAP,OAAO,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,EAAA,IAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,mCAAA,EAAA,mBAAA,EAAA,IAAA,EAAA,IAAA,EAAA,YAAA,EAAA,MAAA,EAAA,EAAA,cAAA,EAAA,yDAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAP,OAAO,EAAA,UAAA,EAAA,CAAA;kBAXnB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,UAAU;AACpB,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,yDAAyD;AAClE,wBAAA,qCAAqC,EAAE,iBAAiB;AACxD,wBAAA,MAAM,EAAE,IAAI;;AAEZ,wBAAA,cAAc,EAAE,MAAM;AACvB,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;8BAGU,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAGG,EAAE,EAAA,CAAA;sBAAV,KAAK;;;ACnBR;;;;AAIG;MACU,UAAU,GAAG,IAAI,cAAc,CAAY,WAAW,EAAE;AAErE;MAMa,SAAS,CAAA;AALtB,IAAA,WAAA,GAAA;QAWE,IAAO,CAAA,OAAA,GAAG,KAAK,CAAC;AACjB,KAAA;IANC,IACI,eAAe,CAAC,KAAS,EAAA;AAC3B,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;KACrB;8GAJU,SAAS,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAT,SAAS,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,+CAAA,EAAA,MAAA,EAAA,EAAA,eAAA,EAAA,CAAA,eAAA,EAAA,iBAAA,CAAA,EAAA,EAAA,SAAA,EAHT,CAAC,EAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAC,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAG/C,SAAS,EAAA,UAAA,EAAA,CAAA;kBALrB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,+CAA+C;oBACzD,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAW,SAAA,EAAC,CAAC;AAC1D,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;8BAGK,eAAe,EAAA,CAAA;sBADlB,KAAK;uBAAC,eAAe,CAAA;;;ACdxB;;;;AAIG;MACU,UAAU,GAAG,IAAI,cAAc,CAAY,WAAW,EAAE;AAErE;MAMa,SAAS,CAAA;AALtB,IAAA,WAAA,GAAA;QAWE,IAAO,CAAA,OAAA,GAAG,KAAK,CAAC;AACjB,KAAA;IANC,IACI,eAAe,CAAC,KAAS,EAAA;AAC3B,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;KACrB;8GAJU,SAAS,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAT,SAAS,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,+CAAA,EAAA,MAAA,EAAA,EAAA,eAAA,EAAA,CAAA,eAAA,EAAA,iBAAA,CAAA,EAAA,EAAA,SAAA,EAHT,CAAC,EAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAC,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAG/C,SAAS,EAAA,UAAA,EAAA,CAAA;kBALrB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,+CAA+C;oBACzD,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAW,SAAA,EAAC,CAAC;AAC1D,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;8BAGK,eAAe,EAAA,CAAA;sBADlB,KAAK;uBAAC,eAAe,CAAA;;;ACCxB;AACO,MAAM,qBAAqB,GAAG,IAAI,cAAc,CAAsB,qBAAqB,CAAC,CAAC;AAEpG;;;;;;;;;;;;AAYG;MASU,yBAAyB,CAAA;;AAEpC,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACvB,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,aAAa,EAAE,CAAC;SACtB;KACF;;AAID,IAAA,IACI,aAAa,GAAA;QACf,OAAO,IAAI,CAAC,cAAc,CAAC;KAC5B;IACD,IAAI,aAAa,CAAC,KAAc,EAAA;AAC9B,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;AAC5B,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,CAAC,kBAAkB,EAAE,CAAC;SAC3B;aAAM;AACL,YAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;SACxC;KACF;AAeD,IAAA,WAAA,CAAoB,WAAoC,EAAA;QAApC,IAAW,CAAA,WAAA,GAAX,WAAW,CAAyB;QA7BhD,IAAS,CAAA,SAAA,GAAG,KAAK,CAAC;QAelB,IAAc,CAAA,cAAA,GAAG,KAAK,CAAC;;AAGvB,QAAA,IAAA,CAAA,eAAe,GAAG,MAAM,CAAC,oBAAoB,CAAC,CAAC;;AAG/C,QAAA,IAAA,CAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;;AAGzB,QAAA,IAAA,CAAA,OAAO,GAAG,MAAM,CAAC,qBAAqB,CAAC,CAAC;;AAGxC,QAAA,IAAA,CAAA,mBAAmB,GAAG,IAAI,YAAY,EAAE,CAAC;KAEW;IAE5D,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;KACxC;;IAGD,QAAQ,GAAA;QACN,OAAO,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;KAC5D;;AAGD,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;KACvC;;IAGO,aAAa,GAAA;;;;;;;;;QASnB,UAAU,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC,CAAC;KACtD;;IAGO,kBAAkB,GAAA;AACxB,QAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;AACvC,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,eAAe;AAC5C,iBAAA,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,EAAC,GAAG,EAAE,YAAY,EAAC,CAAC;iBAC5D,SAAS,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;AAC3C,SAAC,CAAC,CAAC;KACJ;8GA9EU,yBAAyB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAzB,yBAAyB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kCAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,aAAA,EAAA,eAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,uCAAA,EAAA,UAAA,EAAA,EAAA,cAAA,EAAA,2CAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAzB,yBAAyB,EAAA,UAAA,EAAA,CAAA;kBARrC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,kCAAkC;AAC5C,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,2CAA2C;AACpD,wBAAA,yCAAyC,EAAE,UAAU;AACtD,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;+EAIK,QAAQ,EAAA,CAAA;sBADX,KAAK;gBAcF,aAAa,EAAA,CAAA;sBADhB,KAAK;;AAkER;;;AAGG;AACH,SAAS,mBAAmB,CAAC,OAAoB,EAAA;;;;;IAK/C,MAAM,MAAM,GAAG,OAAsB,CAAC;AACtC,IAAA,IAAI,MAAM,CAAC,YAAY,KAAK,IAAI,EAAE;QAChC,OAAO,MAAM,CAAC,WAAW,CAAC;KAC3B;IAED,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAgB,CAAC;IACpD,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IAChD,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,WAAW,EAAE,6BAA6B,CAAC,CAAC;AACpE,IAAA,QAAQ,CAAC,eAAe,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC5C,IAAA,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;IACtC,KAAK,CAAC,MAAM,EAAE,CAAC;AACf,IAAA,OAAO,WAAW,CAAC;AACrB;;AC7IA;AACA,MAAM,cAAc,GAAG,yBAAyB,CAAC;AAEjD;AACA,MAAM,kBAAkB,GAAG,+BAA+B,CAAC;AAE3D;;;;;;;AAOG;MAQU,sBAAsB,CAAA;IACjC,WACU,CAAA,WAAoC,EAC5C,MAAc,EAAA;QADN,IAAW,CAAA,WAAA,GAAX,WAAW,CAAyB;AAkBtC,QAAA,IAAA,CAAA,oBAAoB,GAAG,CAAC,KAAsB,KAAI;YACxD,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC;YAC3D,MAAM,cAAc,GAAG,SAAS,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;YAE9D,IAAI,KAAK,CAAC,YAAY,KAAK,SAAS,IAAI,cAAc,EAAE;AACtD,gBAAA,SAAS,CAAC,MAAM,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;aACtD;AACH,SAAC,CAAC;AAtBA,QAAA,MAAM,CAAC,iBAAiB,CAAC,MAAK;YAC5B,WAAW,CAAC,aAAa,CAAC,gBAAgB,CAAC,eAAe,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;AACzF,SAAC,CAAC,CAAC;KACJ;IAED,QAAQ,GAAA;QACN,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC;AAC3D,QAAA,SAAS,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;AACrC,QAAA,SAAS,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;KAC/B;IAED,UAAU,GAAA;QACR,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;KAClE;IAWD,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,mBAAmB,CAAC,eAAe,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;KAChG;8GA/BU,sBAAsB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAtB,sBAAsB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,6BAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,iBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAtB,sBAAsB,EAAA,UAAA,EAAA,CAAA;kBAPlC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,6BAA6B;AACvC,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,iBAAiB;AAC3B,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;;ACXD;;;;;AAKG;MAcU,0BAA0B,CAAA;IAMrC,WACU,CAAA,WAAoC,EACpC,OAAe,EAAA;QADf,IAAW,CAAA,WAAA,GAAX,WAAW,CAAyB;QACpC,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;;QANgB,IAAI,CAAA,IAAA,GAAY,KAAK,CAAC;KAO3D;IAEJ,eAAe,GAAA;AACb,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,aAAa,CAAc,qBAAqB,CAAC,CAAC;QAC/F,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;AAE9E,YAAA,IAAI,OAAO,qBAAqB,KAAK,UAAU,EAAE;AAC/C,gBAAA,KAAK,CAAC,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC;AACtC,gBAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,oBAAA,qBAAqB,CAAC,OAAO,KAAK,CAAC,KAAK,CAAC,kBAAkB,GAAG,EAAE,CAAC,CAAC,CAAC;AACrE,iBAAC,CAAC,CAAC;aACJ;SACF;aAAM;YACL,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;SAC/E;KACF;AAED,IAAA,cAAc,CAAC,UAAkB,EAAA;QAC/B,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE;YAC7B,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC;SAC5C;aAAM;YACL,MAAM,qBAAqB,GAAG,CAAC,CAAC;YAChC,MAAM,oBAAoB,GAAG,CAAC,CAAC;AAC/B,YAAA,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,CAAA,KAAA,EAAQ,UAAU,CACxD,4DAAA,EAAA,qBAAqB,GAAG,oBAC1B,KAAK,CAAC;SACP;KACF;8GArCU,0BAA0B,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAA1B,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,0BAA0B,yWCtCvC,mMAKA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FDiCa,0BAA0B,EAAA,UAAA,EAAA,CAAA;kBAbtC,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,iCAAiC,EAErC,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,qBAAqB;;;AAG9B,wBAAA,sCAAsC,EAAE,MAAM;qBAC/C,EACgB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,aAAA,EAChC,iBAAiB,CAAC,IAAI,cACzB,IAAI,EAAA,QAAA,EAAA,mMAAA,EAAA,CAAA;oGAIyB,IAAI,EAAA,CAAA;sBAA5C,KAAK;uBAAC,gCAAgC,CAAA;gBAEnB,MAAM,EAAA,CAAA;sBAAzB,SAAS;uBAAC,OAAO,CAAA;;;AE1BpB;;;AAGG;AACU,MAAA,sBAAsB,GAE/B;;AAEF,IAAA,kBAAkB,EAAE,OAAO,CAAC,oBAAoB,EAAE;;AAEhD,QAAA,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,EAAC,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,gBAAgB,EAAC,CAAC,CAAC;QAChE,UAAU,CAAC,eAAe,EAAE;YAC1B,KAAK,CAAC,EAAC,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,kBAAkB,EAAC,CAAC;YAClD,OAAO,CAAC,wCAAwC,CAAC;SAClD,CAAC;KACH,CAAC;;;ACnBJ;MAEsB,mBAAmB,CAAA;8GAAnB,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAnB,mBAAmB,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAnB,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBADxC,SAAS;;;ACLV;SACgB,uCAAuC,GAAA;AACrD,IAAA,OAAO,KAAK,CAAC,8DAA8D,CAAC,CAAC;AAC/E,CAAC;AAED;AACM,SAAU,kCAAkC,CAAC,KAAa,EAAA;AAC9D,IAAA,OAAO,KAAK,CAAC,CAAA,wCAAA,EAA2C,KAAK,CAAA,GAAA,CAAK,CAAC,CAAC;AACtE,CAAC;AAED;SACgB,kCAAkC,GAAA;AAChD,IAAA,OAAO,KAAK,CAAC,oDAAoD,CAAC,CAAC;AACrE;;AC+DA;;;;AAIG;MACU,cAAc,GAAG,IAAI,cAAc,CAAe,cAAc,EAAE;AAE/E;;;AAGG;MACU,8BAA8B,GAAG,IAAI,cAAc,CAC9D,gCAAgC,EAChC;AAEF,IAAI,YAAY,GAAG,CAAC,CAAC;AAErB;AACA,MAAM,kBAAkB,GAA2B,MAAM,CAAC;AAE1D;;;AAGG;AACH,MAAM,mBAAmB,GAAmB,MAAM,CAAC;AAEnD;AACA,MAAM,wBAAwB,GAAoB,OAAO,CAAC;AAE1D;;;;AAIG;AACH,MAAM,uCAAuC,GAAG,CAAA,gBAAA,CAAkB,CAAC;AAYnE;MAiDa,YAAY,CAAA;;AAmBvB,IAAA,IACI,kBAAkB,GAAA;QACpB,OAAO,IAAI,CAAC,mBAAmB,CAAC;KACjC;IACD,IAAI,kBAAkB,CAAC,KAAmB,EAAA;AACxC,QAAA,IAAI,CAAC,mBAAmB,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;KACzD;;AAOD,IAAA,IACI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,SAAS,EAAE,UAAU,IAAI,mBAAmB,CAAC;KAC9E;IACD,IAAI,UAAU,CAAC,KAAqB,EAAA;AAClC,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,WAAW,EAAE;AAC9B,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;;;;;AAKzB,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;SACxC;KACF;;AAID,IAAA,IACI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;KACzB;IACD,IAAI,UAAU,CAAC,KAA6B,EAAA;AAC1C,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC;QAClC,MAAM,aAAa,GAAG,KAAK,IAAI,IAAI,CAAC,SAAS,EAAE,UAAU,IAAI,kBAAkB,CAAC;AAChF,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;YACjD,IAAI,aAAa,KAAK,MAAM,IAAI,aAAa,KAAK,SAAS,EAAE;AAC3D,gBAAA,MAAM,IAAI,KAAK,CACb,qCAAqC,aAAa,CAAA,wCAAA,CAA0C,CAC7F,CAAC;aACH;SACF;AACD,QAAA,IAAI,CAAC,WAAW,GAAG,aAAa,CAAC;AACjC,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,IAAI,IAAI,CAAC,WAAW,KAAK,QAAQ,EAAE;;;;AAInE,YAAA,IAAI,CAAC,sCAAsC,GAAG,IAAI,CAAC;SACpD;KACF;AAGD;;;;AAIG;AACH,IAAA,IACI,eAAe,GAAA;QACjB,OAAO,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,SAAS,EAAE,eAAe,IAAI,wBAAwB,CAAC;KAC7F;IACD,IAAI,eAAe,CAAC,KAAsB,EAAA;AACxC,QAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK,IAAI,IAAI,CAAC,SAAS,EAAE,eAAe,IAAI,wBAAwB,CAAC;KAC9F;;AAID,IAAA,IACI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;KACxB;IACD,IAAI,SAAS,CAAC,KAAa,EAAA;AACzB,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,aAAa,EAAE,CAAC;KACtB;;AAkBD,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,yBAAyB,IAAI,IAAI,CAAC,iBAAiB,CAAC;KACjE;IACD,IAAI,QAAQ,CAAC,KAAK,EAAA;AAChB,QAAA,IAAI,CAAC,yBAAyB,GAAG,KAAK,CAAC;KACxC;AAOD,IAAA,WAAA,CACS,WAAuB,EACtB,kBAAqC,EACrC,OAAe,EACf,IAAoB,EACpB,SAAmB,EAGnB,SAAsC,EACI,cAAuB;AACzE;;;AAGG;IACe,eAAqB,EAAA;QAbhC,IAAW,CAAA,WAAA,GAAX,WAAW,CAAY;QACtB,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAmB;QACrC,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QACf,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAgB;QACpB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QAGnB,IAAS,CAAA,SAAA,GAAT,SAAS,CAA6B;QACI,IAAc,CAAA,cAAA,GAAd,cAAc,CAAS;QA5GnE,IAAmB,CAAA,mBAAA,GAAG,KAAK,CAAC;;QAG3B,IAAK,CAAA,KAAA,GAAiB,SAAS,CAAC;QA0CjC,IAAW,CAAA,WAAA,GAA2B,kBAAkB,CAAC;QAczD,IAAgB,CAAA,gBAAA,GAA2B,IAAI,CAAC;QAWhD,IAAU,CAAA,UAAA,GAAG,EAAE,CAAC;QAExB,IAAc,CAAA,cAAA,GAAG,KAAK,CAAC;QACvB,IAAc,CAAA,cAAA,GAAG,KAAK,CAAC;QACvB,IAAc,CAAA,cAAA,GAAG,KAAK,CAAC;QACvB,IAAc,CAAA,cAAA,GAAG,KAAK,CAAC;;AAGd,QAAA,IAAA,CAAA,QAAQ,GAAG,CAAA,yBAAA,EAA4B,YAAY,EAAE,EAAE,CAAC;;AAGxD,QAAA,IAAA,CAAA,YAAY,GAAG,CAAA,aAAA,EAAgB,YAAY,EAAE,EAAE,CAAC;;QAGzD,IAAwB,CAAA,wBAAA,GAAG,EAAE,CAAC;AAUtB,QAAA,IAAA,CAAA,UAAU,GAAG,IAAI,OAAO,EAAQ,CAAC;QACjC,IAAU,CAAA,UAAA,GAAmB,IAAI,CAAC;QAElC,IAAsC,CAAA,sCAAA,GAAG,KAAK,CAAC;QAkBrD,IAAI,SAAS,EAAE;AACb,YAAA,IAAI,SAAS,CAAC,UAAU,EAAE;AACxB,gBAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC;aACxC;YACD,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC;AAClE,YAAA,IAAI,SAAS,CAAC,KAAK,EAAE;AACnB,gBAAA,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;aAC9B;SACF;KACF;IAED,eAAe,GAAA;;;QAGb,IAAI,CAAC,iBAAiB,EAAE,CAAC;;AAEzB,QAAA,IAAI,CAAC,wBAAwB,GAAG,OAAO,CAAC;;;AAGxC,QAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC;KACzC;IAED,kBAAkB,GAAA;QAChB,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAClC,IAAI,CAAC,0CAA0C,EAAE,CAAC;KACnD;IAED,qBAAqB,GAAA;QACnB,IAAI,CAAC,uBAAuB,EAAE,CAAC;KAChC;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;KAC5B;AAED;;AAEG;IACH,UAAU,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,iBAAiB,EAAE,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;KACxD;AAED;;;AAGG;IACH,yBAAyB,GAAA;AACvB,QAAA,OAAO,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,WAAW,CAAC;KAC5C;;IAGD,oBAAoB,GAAA;;;;;;;;;AASlB,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;AAC5B,YAAA,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC;SAC5B;KACF;;IAGO,kBAAkB,GAAA;AACxB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;AAE9B,QAAA,IAAI,OAAO,CAAC,WAAW,EAAE;AACvB,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAC1C,2BAA2B,OAAO,CAAC,WAAW,CAAA,CAAE,CACjD,CAAC;SACH;;AAGD,QAAA,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,MAAK;YAClC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,CAAC,mBAAmB,EAAE,CAAC;AAC3B,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACzC,SAAC,CAAC,CAAC;;QAGH,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,YAAY,EAAE;YACvD,OAAO,CAAC,SAAS,CAAC,YAAY;AAC3B,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;iBAChC,SAAS,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC,CAAC;SAC5D;KACF;IAEO,0BAA0B,GAAA;QAChC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AACnE,QAAA,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC;QAClE,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AACnE,QAAA,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC;KACnE;;IAGO,0BAA0B,GAAA;QAChC,IAAI,CAAC,0BAA0B,EAAE,CAAC;;;;AAIlC,QAAA,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,MAAK;YAC/E,IAAI,CAAC,0BAA0B,EAAE,CAAC;AAClC,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACzC,SAAC,CAAC,CAAC;KACJ;AAED;;;;AAIG;IACK,oBAAoB,GAAA;;QAE1B,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,SAAS,CAAC,MAAK;YACxC,IAAI,CAAC,aAAa,EAAE,CAAC;AACrB,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACzC,SAAC,CAAC,CAAC;;QAGH,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,CAAC,MAAK;YACzC,IAAI,CAAC,mBAAmB,EAAE,CAAC;AAC3B,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACzC,SAAC,CAAC,CAAC;;QAGH,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,mBAAmB,EAAE,CAAC;KAC5B;;IAGO,uBAAuB,GAAA;AAC7B,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YACrE,MAAM,kCAAkC,EAAE,CAAC;SAC5C;KACF;IAEO,iBAAiB,GAAA;;;;;;QAMvB,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AAC7C,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,YAAA,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE,CAAC;SAC9B;AAAM,aAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,KAAK,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,EAAE;AAClF,YAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AACxB,YAAA,IAAI,CAAC,WAAW,EAAE,UAAU,EAAE,CAAC;SAChC;AAED,QAAA,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,SAAS,CAAC,MAAM,CAC7C,yBAAyB,EACzB,IAAI,CAAC,QAAQ,CAAC,OAAO,CACtB,CAAC;KACH;AAED;;;;;;;AAOG;IACK,0CAA0C,GAAA;;AAEhD,QAAA,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,SAAS,CACpC,OAAO,IAAI,CAAC,sCAAsC,GAAG,IAAI,CAAC,CAC3D,CAAC;;;AAIF,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;AACpE,gBAAA,IAAI,IAAI,CAAC,sCAAsC,EAAE;AAC/C,oBAAA,IAAI,CAAC,sCAAsC,GAAG,KAAK,CAAC;oBACpD,IAAI,CAAC,yBAAyB,EAAE,CAAC;iBAClC;AACH,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,MAAM;AACb,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAChC,aAAA,SAAS,CAAC,OAAO,IAAI,CAAC,sCAAsC,GAAG,IAAI,CAAC,CAAC,CAAC;KAC1E;;IAGD,kBAAkB,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,CAAC;KACrC;IAED,WAAW,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC;KACtC;AAED;;;;;;;AAOG;IACH,uBAAuB,GAAA;AACrB,QAAA,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;KAC9F;IAED,iBAAiB,GAAA;QACf,OAAO,CAAC,CAAC,IAAI,CAAC,oBAAoB,IAAI,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC;KAChE;IAED,iBAAiB,GAAA;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,gBAAgB,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;KACpE;AAED;;;AAGG;AACH,IAAA,cAAc,CAAC,IAAoC,EAAA;AACjD,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;AAC/D,QAAA,OAAO,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;KACjC;;IAGD,qBAAqB,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU;AACtF,cAAE,OAAO;cACP,MAAM,CAAC;KACZ;;IAGD,mBAAmB,GAAA;QACjB,IAAI,CAAC,yBAAyB,EAAE,CAAC;KAClC;;IAGD,yBAAyB,GAAA;AACvB,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE;AAC5E,YAAA,IAAI,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC;SACzC;aAAM;AACL,YAAA,IAAI,CAAC,eAAe,EAAE,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC;SACtE;KACF;;IAGO,aAAa,GAAA;QACnB,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,mBAAmB,EAAE,CAAC;KAC5B;AAED;;;;;AAKG;IACK,cAAc,GAAA;AACpB,QAAA,IAAI,IAAI,CAAC,aAAa,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AACzE,YAAA,IAAI,SAAkB,CAAC;AACvB,YAAA,IAAI,OAAgB,CAAC;YACrB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,IAAa,KAAI;AAC3C,gBAAA,IAAI,IAAI,CAAC,KAAK,KAAK,OAAO,EAAE;AAC1B,oBAAA,IAAI,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE;AAC/B,wBAAA,MAAM,kCAAkC,CAAC,OAAO,CAAC,CAAC;qBACnD;oBACD,SAAS,GAAG,IAAI,CAAC;iBAClB;AAAM,qBAAA,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,EAAE;oBAC/B,IAAI,OAAO,EAAE;AACX,wBAAA,MAAM,kCAAkC,CAAC,KAAK,CAAC,CAAC;qBACjD;oBACD,OAAO,GAAG,IAAI,CAAC;iBAChB;AACH,aAAC,CAAC,CAAC;SACJ;KACF;AAED;;;AAGG;IACK,mBAAmB,GAAA;AACzB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,GAAG,GAAa,EAAE,CAAC;;AAGvB,YAAA,IACE,IAAI,CAAC,QAAQ,CAAC,mBAAmB;gBACjC,OAAO,IAAI,CAAC,QAAQ,CAAC,mBAAmB,KAAK,QAAQ,EACrD;AACA,gBAAA,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;aAC3D;AAED,YAAA,IAAI,IAAI,CAAC,qBAAqB,EAAE,KAAK,MAAM,EAAE;AAC3C,gBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa;AAClC,sBAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,OAAO,CAAC;sBACvD,IAAI,CAAC;AACT,gBAAA,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa;AAChC,sBAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC;sBACrD,IAAI,CAAC;gBAET,IAAI,SAAS,EAAE;AACb,oBAAA,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;iBACxB;AAAM,qBAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AAC1B,oBAAA,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;iBAC7B;gBAED,IAAI,OAAO,EAAE;AACX,oBAAA,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;iBACtB;aACF;AAAM,iBAAA,IAAI,IAAI,CAAC,cAAc,EAAE;AAC9B,gBAAA,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;aACzD;AAED,YAAA,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;SACtC;KACF;AAED;;;;;;;;AAQG;IACK,yBAAyB,GAAA;AAC/B,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YAC5E,OAAO;SACR;AACD,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;;;QAGlD,IAAI,EAAE,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAC,EAAE;AAC7D,YAAA,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;YACnC,OAAO;SACR;;;AAGD,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE;AAC5B,YAAA,IAAI,CAAC,sCAAsC,GAAG,IAAI,CAAC;YACnD,OAAO;SACR;AACD,QAAA,MAAM,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,EAAE,aAAa,CAAC;AACrE,QAAA,MAAM,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,EAAE,aAAa,CAAC;QACrE,MAAM,wBAAwB,GAAG,mBAAmB,EAAE,qBAAqB,EAAE,CAAC,KAAK,IAAI,CAAC,CAAC;QACzF,MAAM,wBAAwB,GAAG,mBAAmB,EAAE,qBAAqB,EAAE,CAAC,KAAK,IAAI,CAAC,CAAC;;;AAGzF,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,GAAG,IAAI,GAAG,GAAG,CAAC;AACtD,QAAA,MAAM,WAAW,GAAG,CAAA,EAAG,wBAAwB,GAAG,wBAAwB,IAAI,CAAC;QAC/E,MAAM,WAAW,GAAG,CAAA,6CAAA,CAA+C,CAAC;QACpE,MAAM,qBAAqB,GAAG,CAAQ,KAAA,EAAA,MAAM,OAAO,WAAW,CAAA,GAAA,EAAM,WAAW,CAAA,EAAA,CAAI,CAAC;;;;AAKpF,QAAA,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG,CAAA;;AAE1B,QAAA,EAAA,uCAAuC,eAAe,qBAAqB,CAAA;MAC/E,CAAC;KACJ;;IAGO,gBAAgB,GAAA;AACtB,QAAA,MAAM,OAAO,GAAgB,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;AAC5D,QAAA,IAAI,OAAO,CAAC,WAAW,EAAE;AACvB,YAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;;;AAGvC,YAAA,OAAO,QAAQ,IAAI,QAAQ,KAAK,OAAO,CAAC;SACzC;;;QAGD,OAAO,QAAQ,CAAC,eAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;KACpD;AA5gBU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,EAoIb,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAA,8BAA8B,EAElB,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,qBAAqB,6BAKjC,QAAQ,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AA3IP,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAY,EAbZ,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,6CAAA,EAAA,sBAAA,EAAA,0CAAA,EAAA,gBAAA,EAAA,0CAAA,EAAA,gBAAA,EAAA,8BAAA,EAAA,qBAAA,EAAA,+BAAA,EAAA,mBAAA,EAAA,iCAAA,EAAA,qBAAA,EAAA,oCAAA,EAAA,uCAAA,EAAA,sCAAA,EAAA,wBAAA,EAAA,yCAAA,EAAA,2BAAA,EAAA,uCAAA,EAAA,6CAAA,EAAA,mBAAA,EAAA,kBAAA,EAAA,mBAAA,EAAA,4CAAA,EAAA,kBAAA,EAAA,sBAAA,EAAA,gBAAA,EAAA,oBAAA,EAAA,oBAAA,EAAA,+BAAA,EAAA,kBAAA,EAAA,6BAAA,EAAA,mBAAA,EAAA,8BAAA,EAAA,gBAAA,EAAA,2BAAA,EAAA,gBAAA,EAAA,2BAAA,EAAA,kBAAA,EAAA,6BAAA,EAAA,kBAAA,EAAA,6BAAA,EAAA,EAAA,cAAA,EAAA,oBAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA,EAAC,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,YAAY,EAAC;AACpD,YAAA,EAAC,OAAO,EAAE,qBAAqB,EAAE,WAAW,EAAE,YAAY,EAAC;AAC5D,SAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,sBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAoBa,QAAQ,EACR,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,mBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,QAAQ,EACR,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,mBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAAC,mBAAoB,qEACjB,UAAU,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,SAAA,EACV,UAAU,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,SAAA,EACV,SAAS,EACT,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,eAAA,EAAA,SAAA,EAAA,OAAO,EAVb,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,YAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,sBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,sBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,yBAAyB,kFACzB,0BAA0B,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAC1B,sBAAsB,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EC3LnC,yjJAkHA,ED0DI,MAAA,EAAA,CAAA,k4sDAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,yBAAyB,EACzB,QAAA,EAAA,kCAAA,EAAA,MAAA,EAAA,CAAA,UAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,0BAA0B,wHAC1B,gBAAgB,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAChB,sBAAsB,EAAA,QAAA,EAAA,6BAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EACtB,OAAO,EAxCG,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,IAAA,CAAA,EAAA,CAAA,EAAA,UAAA,EAAA,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FA2C5C,YAAY,EAAA,UAAA,EAAA,CAAA;kBAhDxB,SAAS;+BACE,gBAAgB,EAAA,QAAA,EAChB,cAAc,EAGZ,UAAA,EAAA,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,EACjD,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,oBAAoB;AAC7B,wBAAA,+CAA+C,EAAE,sBAAsB;AACvE,wBAAA,4CAA4C,EAAE,gBAAgB;AAC9D,wBAAA,4CAA4C,EAAE,gBAAgB;;;;AAI9D,wBAAA,gCAAgC,EAAE,qBAAqB;AACvD,wBAAA,iCAAiC,EAAE,mBAAmB;AACtD,wBAAA,mCAAmC,EAAE,qBAAqB;AAC1D,wBAAA,sCAAsC,EAAE,qCAAqC;AAC7E,wBAAA,wCAAwC,EAAE,sBAAsB;AAChE,wBAAA,2CAA2C,EAAE,yBAAyB;AACtE,wBAAA,yCAAyC,EAAE,6CAA6C;AACxF,wBAAA,qBAAqB,EAAE,kBAAkB;AACzC,wBAAA,qBAAqB,EAAE,wCAAwC;AAC/D,wBAAA,oBAAoB,EAAE,oBAAoB;AAC1C,wBAAA,kBAAkB,EAAE,kBAAkB;AACtC,wBAAA,sBAAsB,EAAE,6BAA6B;AACrD,wBAAA,oBAAoB,EAAE,2BAA2B;AACjD,wBAAA,qBAAqB,EAAE,4BAA4B;AACnD,wBAAA,kBAAkB,EAAE,yBAAyB;AAC7C,wBAAA,kBAAkB,EAAE,yBAAyB;AAC7C,wBAAA,oBAAoB,EAAE,2BAA2B;AACjD,wBAAA,oBAAoB,EAAE,2BAA2B;AAClD,qBAAA,EAAA,aAAA,EACc,iBAAiB,CAAC,IAAI,mBACpB,uBAAuB,CAAC,MAAM,EACpC,SAAA,EAAA;AACT,wBAAA,EAAC,OAAO,EAAE,cAAc,EAAE,WAAW,cAAc,EAAC;AACpD,wBAAA,EAAC,OAAO,EAAE,qBAAqB,EAAE,WAAW,cAAc,EAAC;AAC5D,qBAAA,EAAA,UAAA,EACW,IAAI,EACP,OAAA,EAAA;wBACP,yBAAyB;wBACzB,0BAA0B;wBAC1B,gBAAgB;wBAChB,sBAAsB;wBACtB,OAAO;AACR,qBAAA,EAAA,QAAA,EAAA,yjJAAA,EAAA,MAAA,EAAA,CAAA,k4sDAAA,CAAA,EAAA,CAAA;;0BAqIE,QAAQ;;0BACR,MAAM;2BAAC,8BAA8B,CAAA;;0BAErC,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;;0BAKxC,MAAM;2BAAC,QAAQ,CAAA;yCAxIM,UAAU,EAAA,CAAA;sBAAjC,SAAS;uBAAC,WAAW,CAAA;gBACY,oBAAoB,EAAA,CAAA;sBAArD,SAAS;uBAAC,qBAAqB,CAAA;gBACE,oBAAoB,EAAA,CAAA;sBAArD,SAAS;uBAAC,qBAAqB,CAAA;gBACM,cAAc,EAAA,CAAA;sBAAnD,SAAS;uBAAC,yBAAyB,CAAA;gBACG,eAAe,EAAA,CAAA;sBAArD,SAAS;uBAAC,0BAA0B,CAAA;gBACF,WAAW,EAAA,CAAA;sBAA7C,SAAS;uBAAC,sBAAsB,CAAA;gBAET,oBAAoB,EAAA,CAAA;sBAA3C,YAAY;uBAAC,QAAQ,CAAA;gBACkB,iBAAiB,EAAA,CAAA;sBAAxD,YAAY;AAAC,gBAAA,IAAA,EAAA,CAAA,QAAQ,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC,CAAA;gBACF,iBAAiB,EAAA,CAAA;sBAApD,YAAY;uBAACA,mBAAoB,CAAA;gBACgB,eAAe,EAAA,CAAA;sBAAhE,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,UAAU,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC,CAAA;gBACE,eAAe,EAAA,CAAA;sBAAhE,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,UAAU,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC,CAAA;gBACC,cAAc,EAAA,CAAA;sBAA9D,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,SAAS,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC,CAAA;gBACA,aAAa,EAAA,CAAA;sBAA3D,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,OAAO,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC,CAAA;gBAIzC,kBAAkB,EAAA,CAAA;sBADrB,KAAK;gBAUG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAIF,UAAU,EAAA,CAAA;sBADb,KAAK;gBAkBF,UAAU,EAAA,CAAA;sBADb,KAAK;gBA8BF,eAAe,EAAA,CAAA;sBADlB,KAAK;gBAWF,SAAS,EAAA,CAAA;sBADZ,KAAK;;;ME1OK,kBAAkB,CAAA;8GAAlB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAlB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,YAZ3B,eAAe;YACf,YAAY;YACZ,eAAe;YACf,YAAY;YACZ,QAAQ;YACR,QAAQ;YACR,OAAO;YACP,SAAS;AACT,YAAA,SAAS,CAED,EAAA,OAAA,EAAA,CAAA,YAAY,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;AAE/E,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,YAZ3B,eAAe;YACf,YAAY;AACZ,YAAA,eAAe,EAQ0D,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAE/E,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAd9B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE;wBACP,eAAe;wBACf,YAAY;wBACZ,eAAe;wBACf,YAAY;wBACZ,QAAQ;wBACR,QAAQ;wBACR,OAAO;wBACP,SAAS;wBACT,SAAS;AACV,qBAAA;AACD,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,eAAe,CAAC;AAC5F,iBAAA,CAAA;;;AChCD;;AAEG;;;;"}