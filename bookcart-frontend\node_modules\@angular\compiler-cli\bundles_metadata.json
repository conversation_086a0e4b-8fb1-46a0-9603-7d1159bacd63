{"inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/invalid_file_system.mjs": {"bytes": 8680, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/util.mjs": {"bytes": 3337, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/helpers.mjs": {"bytes": 10975, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/invalid_file_system.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/compiler_host.mjs": {"bytes": 8414, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/helpers.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/logical.mjs": {"bytes": 13147, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/helpers.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/node_js_file_system.mjs": {"bytes": 15783, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs": {"bytes": 2516, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/compiler_host.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/helpers.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/logical.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/node_js_file_system.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/version.mjs": {"bytes": 1413, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error_code.mjs": {"bytes": 44361, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/docs.mjs": {"bytes": 2589, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error_code.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/util.mjs": {"bytes": 2944, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error.mjs": {"bytes": 11385, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error_code.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error_details_base_url.mjs": {"bytes": 1623, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/extended_template_diagnostic_name.mjs": {"bytes": 4084, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs": {"bytes": 2473, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/docs.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error_code.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error_details_base_url.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/extended_template_diagnostic_name.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs": {"bytes": 22727, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/find_export.mjs": {"bytes": 3874, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/emitter.mjs": {"bytes": 55579, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/util.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/find_export.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/alias.mjs": {"bytes": 22746, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/emitter.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/path.mjs": {"bytes": 2654, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/core.mjs": {"bytes": 9451, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/path.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/patch_alias_reference_resolution.mjs": {"bytes": 18614, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/default.mjs": {"bytes": 12968, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/patch_alias_reference_resolution.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/host.mjs": {"bytes": 34720, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/type_to_value.mjs": {"bytes": 35004, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/util.mjs": {"bytes": 5022, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/host.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/typescript.mjs": {"bytes": 92956, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/host.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/type_to_value.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/deferred_symbol_tracker.mjs": {"bytes": 24746, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/typescript.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/imported_symbols_tracker.mjs": {"bytes": 13744, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/local_compilation_extra_imports_tracker.mjs": {"bytes": 12163, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/typescript.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs": {"bytes": 2107, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/host.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/type_to_value.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/references.mjs": {"bytes": 17417, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/resolver.mjs": {"bytes": 4023, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs": {"bytes": 4029, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/alias.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/core.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/default.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/deferred_symbol_tracker.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/emitter.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/imported_symbols_tracker.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/local_compilation_extra_imports_tracker.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/patch_alias_reference_resolution.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/references.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/resolver.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/downlevel_decorators_transform.mjs": {"bytes": 80028, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/api.mjs": {"bytes": 5207, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/util.mjs": {"bytes": 50078, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/default.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/di.mjs": {"bytes": 26722, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/api.mjs": {"bytes": 17228, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/property_mapping.mjs": {"bytes": 19489, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/util.mjs": {"bytes": 31505, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/dts.mjs": {"bytes": 39638, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/property_mapping.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/inheritance.mjs": {"bytes": 10437, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/property_mapping.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/registry.mjs": {"bytes": 9251, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/api.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/resource_registry.mjs": {"bytes": 11916, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/providers.mjs": {"bytes": 10355, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/host_directives_resolver.mjs": {"bytes": 13097, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/property_mapping.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/inheritance.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.mjs": {"bytes": 3024, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/dts.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/inheritance.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/registry.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/resource_registry.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/util.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/property_mapping.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/providers.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/host_directives_resolver.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/dynamic.mjs": {"bytes": 18642, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/result.mjs": {"bytes": 6383, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/diagnostics.mjs": {"bytes": 23464, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/dynamic.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/result.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/builtin.mjs": {"bytes": 7106, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/dynamic.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/result.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/synthetic.mjs": {"bytes": 1691, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/interpreter.mjs": {"bytes": 109263, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/builtin.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/dynamic.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/result.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/synthetic.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/interface.mjs": {"bytes": 4009, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/interpreter.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.mjs": {"bytes": 2055, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/diagnostics.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/dynamic.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/interface.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/interpreter.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/result.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/synthetic.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/api.mjs": {"bytes": 16469, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/alias.mjs": {"bytes": 4192, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/api.mjs": {"bytes": 27408, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/noop.mjs": {"bytes": 2000, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/api.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/clock.mjs": {"bytes": 1878, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/recorder.mjs": {"bytes": 15228, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/clock.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs": {"bytes": 1305, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/noop.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/recorder.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/trait.mjs": {"bytes": 17945, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/compilation.mjs": {"bytes": 88551, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/trait.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/context.mjs": {"bytes": 2352, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/check_unique_identifier_name.mjs": {"bytes": 4949, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/import_typescript_transform.mjs": {"bytes": 14225, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/reuse_generated_imports.mjs": {"bytes": 7548, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/reuse_source_file_imports.mjs": {"bytes": 15566, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/import_manager.mjs": {"bytes": 41999, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/check_unique_identifier_name.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/import_typescript_transform.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/reuse_generated_imports.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/reuse_source_file_imports.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/translator.mjs": {"bytes": 58629, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/type_emitter.mjs": {"bytes": 20952, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/ts_util.mjs": {"bytes": 2760, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/type_translator.mjs": {"bytes": 39836, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/context.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/ts_util.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/type_emitter.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/typescript_ast_factory.mjs": {"bytes": 39454, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/ts_util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/typescript_translator.mjs": {"bytes": 4197, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/context.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/translator.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/typescript_ast_factory.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.mjs": {"bytes": 3072, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/context.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/import_manager.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/translator.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/type_emitter.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/type_translator.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/typescript_ast_factory.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/typescript_translator.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/declaration.mjs": {"bytes": 26074, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/visitor.mjs": {"bytes": 13999, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/transform.mjs": {"bytes": 59764, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/default.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/visitor.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.mjs": {"bytes": 2066, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/alias.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/compilation.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/declaration.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/trait.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/transform.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/diagnostics.mjs": {"bytes": 55242, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/util.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/evaluation.mjs": {"bytes": 12076, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/diagnostics.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/factory.mjs": {"bytes": 3337, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/injectable_registry.mjs": {"bytes": 5263, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/di.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/metadata.mjs": {"bytes": 25712, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/debug_info.mjs": {"bytes": 4962, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/references_registry.mjs": {"bytes": 2030, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/schema.mjs": {"bytes": 6494, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/diagnostics.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/input_transforms.mjs": {"bytes": 3629, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.mjs": {"bytes": 2123, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/di.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/diagnostics.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/evaluation.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/factory.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/injectable_registry.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/metadata.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/debug_info.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/references_registry.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/schema.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/util.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/input_transforms.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/api.mjs": {"bytes": 9743, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/graph.mjs": {"bytes": 31967, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/api.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/util.mjs": {"bytes": 8315, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/type_parameters.mjs": {"bytes": 7068, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/index.mjs": {"bytes": 1876, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/graph.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/type_parameters.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/api.mjs": {"bytes": 4539, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/component_scope.mjs": {"bytes": 3722, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/dependency.mjs": {"bytes": 17471, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/util.mjs": {"bytes": 10711, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/api.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/local.mjs": {"bytes": 92538, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/typecheck.mjs": {"bytes": 16618, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/api.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/index.mjs": {"bytes": 2287, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/component_scope.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/dependency.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/local.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/typecheck.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_function_access.mjs": {"bytes": 4308, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_functions.mjs": {"bytes": 18809, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/input_output_parse_options.mjs": {"bytes": 4650, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/input_function.mjs": {"bytes": 7903, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_function_access.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_functions.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/input_output_parse_options.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/model_function.mjs": {"bytes": 7872, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_function_access.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_functions.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/input_output_parse_options.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/output_function.mjs": {"bytes": 9442, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_function_access.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_functions.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/input_output_parse_options.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/query_functions.mjs": {"bytes": 19203, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_function_access.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_functions.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/shared.mjs": {"bytes": 180966, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/input_function.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/model_function.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/output_function.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/query_functions.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/symbol.mjs": {"bytes": 18282, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/handler.mjs": {"bytes": 42268, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/shared.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/symbol.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/index.mjs": {"bytes": 1900, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/handler.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/symbol.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/shared.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/input_function.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/output_function.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/query_functions.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/model_function.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_functions.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/src/module_with_providers.mjs": {"bytes": 17653, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/src/handler.mjs": {"bytes": 133402, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/util.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/src/module_with_providers.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/index.mjs": {"bytes": 1434, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/src/handler.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/src/module_with_providers.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/diagnostics.mjs": {"bytes": 5999, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/resources.mjs": {"bytes": 73787, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/symbol.mjs": {"bytes": 12432, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/util.mjs": {"bytes": 17669, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/handler.mjs": {"bytes": 237844, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/util.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/diagnostics.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/resources.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/symbol.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/index.mjs": {"bytes": 1011, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/handler.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/src/injectable.mjs": {"bytes": 47050, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/src/pipe.mjs": {"bytes": 28140, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.mjs": {"bytes": 2837, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/src/injectable.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/src/pipe.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/initializer_api_transforms/transform_api.mjs": {"bytes": 5972, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/initializer_api_transforms/input_function.mjs": {"bytes": 9898, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/initializer_api_transforms/transform_api.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/initializer_api_transforms/model_function.mjs": {"bytes": 10259, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/initializer_api_transforms/transform_api.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/initializer_api_transforms/output_function.mjs": {"bytes": 6693, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/initializer_api_transforms/transform_api.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/initializer_api_transforms/query_functions.mjs": {"bytes": 10406, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/initializer_api_transforms/transform_api.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/initializer_api_transforms/transform.mjs": {"bytes": 11983, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/initializer_api_transforms/input_function.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/initializer_api_transforms/model_function.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/initializer_api_transforms/output_function.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/initializer_api_transforms/query_functions.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/index.mjs": {"bytes": 7394, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/downlevel_decorators_transform.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/initializer_api_transforms/transform.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/downlevel_decorators_transform.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/initializer_api_transforms/transform.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/api.mjs": {"bytes": 15141, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/compiler_host.mjs": {"bytes": 2444, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/i18n.mjs": {"bytes": 7547, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/version_helpers.mjs": {"bytes": 10364, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/typescript_support.mjs": {"bytes": 6644, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/version_helpers.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/cycles/src/analyzer.mjs": {"bytes": 15533, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/cycles/src/imports.mjs": {"bytes": 17491, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/cycles/index.mjs": {"bytes": 1180, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/cycles/src/analyzer.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/cycles/src/imports.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.mjs": {"bytes": 10349, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/filters.mjs": {"bytes": 1528, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/generics_extractor.mjs": {"bytes": 2528, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.mjs": {"bytes": 10690, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/type_extractor.mjs": {"bytes": 1548, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/function_extractor.mjs": {"bytes": 11813, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/generics_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/type_extractor.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/class_extractor.mjs": {"bytes": 50104, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/filters.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/function_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/generics_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/type_extractor.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/constant_extractor.mjs": {"bytes": 13886, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/decorator_extractor.mjs": {"bytes": 18518, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/class_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/enum_extractor.mjs": {"bytes": 7401, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/type_extractor.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/initializer_api_function_extractor.mjs": {"bytes": 27660, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/function_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/generics_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/type_alias_extractor.mjs": {"bytes": 3049, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/extractor.mjs": {"bytes": 21215, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/class_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/constant_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/decorator_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/enum_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/filters.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/function_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/initializer_api_function_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/type_alias_extractor.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/index.mjs": {"bytes": 1017, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/extractor.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/generator.mjs": {"bytes": 4457, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/path.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/logic.mjs": {"bytes": 4870, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/private_export_checker.mjs": {"bytes": 18746, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/reference_graph.mjs": {"bytes": 8851, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/index.mjs": {"bytes": 1580, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/generator.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/logic.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/private_export_checker.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/reference_graph.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/src/api.mjs": {"bytes": 4846, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/expando.mjs": {"bytes": 12801, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/util.mjs": {"bytes": 3021, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/adapter.mjs": {"bytes": 26483, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/expando.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/reference_tagger.mjs": {"bytes": 7867, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/expando.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/index.mjs": {"bytes": 1701, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/adapter.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/expando.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/reference_tagger.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/src/ts_create_program_driver.mjs": {"bytes": 28554, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/src/api.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/index.mjs": {"bytes": 1123, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/src/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/src/ts_create_program_driver.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/dependency_tracking.mjs": {"bytes": 14258, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/state.mjs": {"bytes": 6129, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/incremental.mjs": {"bytes": 46147, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/dependency_tracking.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/state.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/noop.mjs": {"bytes": 1492, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/strategy.mjs": {"bytes": 9481, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/index.mjs": {"bytes": 1585, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/incremental.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/noop.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/state.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/strategy.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/api.mjs": {"bytes": 7689, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/context.mjs": {"bytes": 3559, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/template.mjs": {"bytes": 53721, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/api.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/transform.mjs": {"bytes": 6564, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/template.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/index.mjs": {"bytes": 1232, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/context.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/transform.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/ng_module_index.mjs": {"bytes": 15145, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/api.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/resource/src/loader.mjs": {"bytes": 33931, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/resource/index.mjs": {"bytes": 978, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/resource/src/loader.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/standalone.mjs": {"bytes": 16903, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/api.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/api.mjs": {"bytes": 20535, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/checker.mjs": {"bytes": 16433, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/completion.mjs": {"bytes": 4275, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/context.mjs": {"bytes": 4027, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/scope.mjs": {"bytes": 5237, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/symbols.mjs": {"bytes": 14487, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs": {"bytes": 1351, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/checker.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/completion.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/context.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/scope.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/symbols.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/src/diagnostic.mjs": {"bytes": 21282, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/src/id.mjs": {"bytes": 3194, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/index.mjs": {"bytes": 1016, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/src/diagnostic.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/src/id.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/comments.mjs": {"bytes": 22066, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/completion.mjs": {"bytes": 26421, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/comments.mjs", "kind": "import-statement"}]}, "node_modules/magic-string/dist/magic-string.es.mjs": {"bytes": 37239, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/dom.mjs": {"bytes": 16194, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/reference_emit_environment.mjs": {"bytes": 10863, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/ts_util.mjs": {"bytes": 19507, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/comments.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_parameter_emitter.mjs": {"bytes": 14919, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/tcb_util.mjs": {"bytes": 24934, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/comments.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_parameter_emitter.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_constructor.mjs": {"bytes": 33887, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/tcb_util.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/ts_util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/environment.mjs": {"bytes": 17706, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/reference_emit_environment.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/ts_util.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_constructor.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_parameter_emitter.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/oob.mjs": {"bytes": 53942, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/shim.mjs": {"bytes": 5388, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/diagnostics.mjs": {"bytes": 13297, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/tcb_util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/expression.mjs": {"bytes": 67184, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/diagnostics.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/ts_util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_check_block.mjs": {"bytes": 373918, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/comments.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/diagnostics.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/expression.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/ts_util.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_constructor.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_parameter_emitter.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_check_file.mjs": {"bytes": 14612, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/environment.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/tcb_util.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_check_block.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/context.mjs": {"bytes": 64867, "imports": [{"path": "node_modules/magic-string/dist/magic-string.es.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/dom.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/environment.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/oob.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/reference_emit_environment.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/shim.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/tcb_util.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_check_block.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_check_file.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_constructor.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/line_mappings.mjs": {"bytes": 7287, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/source.mjs": {"bytes": 9661, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/line_mappings.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/template_symbol_builder.mjs": {"bytes": 93628, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/comments.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/ts_util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/checker.mjs": {"bytes": 125072, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/completion.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/context.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/diagnostics.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/shim.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/source.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/tcb_util.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/template_symbol_builder.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/index.mjs": {"bytes": 1575, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/checker.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/context.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/shim.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_check_file.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/symbol_util.mjs": {"bytes": 5215, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/api.mjs": {"bytes": 20719, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/extended_template_checker.mjs": {"bytes": 1431, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.mjs": {"bytes": 1040, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/extended_template_checker.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/interpolated_signal_not_invoked/index.mjs": {"bytes": 12679, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/symbol_util.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/invalid_banana_in_box/index.mjs": {"bytes": 5965, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/missing_control_flow_directive/index.mjs": {"bytes": 12484, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/missing_ngforof_let/index.mjs": {"bytes": 5953, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/nullish_coalescing_not_nullable/index.mjs": {"bytes": 10233, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/optional_chain_not_nullable/index.mjs": {"bytes": 10954, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/suffix_not_supported/index.mjs": {"bytes": 5749, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/text_attribute_not_binding/index.mjs": {"bytes": 8823, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/adapter.mjs": {"bytes": 6319, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/interfaces.mjs": {"bytes": 6207, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/options.mjs": {"bytes": 5279, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/public_options.mjs": {"bytes": 24802, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/index.mjs": {"bytes": 1234, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/adapter.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/interfaces.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/options.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/public_options.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/src/extended_template_checker.mjs": {"bytes": 11955, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/index.mjs": {"bytes": 5448, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/interpolated_signal_not_invoked/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/invalid_banana_in_box/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/missing_control_flow_directive/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/missing_ngforof_let/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/nullish_coalescing_not_nullable/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/optional_chain_not_nullable/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/suffix_not_supported/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/text_attribute_not_binding/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/src/extended_template_checker.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/template_semantics/src/template_semantics_checker.mjs": {"bytes": 16864, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/symbol_util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/core_version.mjs": {"bytes": 3638, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/feature_detection.mjs": {"bytes": 3193, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/compiler.mjs": {"bytes": 191210, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/cycles/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/ng_module_index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/resource/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/standalone.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/template_semantics/src/template_semantics_checker.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/core_version.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/feature_detection.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/host.mjs": {"bytes": 38850, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/path.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/index.mjs": {"bytes": 1053, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/compiler.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/host.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program.mjs": {"bytes": 46468, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/i18n.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/typescript_support.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/program.mjs": {"bytes": 1800, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/entry_points.mjs": {"bytes": 1145, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/compiler_host.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/program.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/util.mjs": {"bytes": 3287, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/api.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/perform_compile.mjs": {"bytes": 36749, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/entry_points.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/private/tooling.mjs": {"bytes": 3498, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/src/logger.mjs": {"bytes": 1761, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/src/console_logger.mjs": {"bytes": 4397, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/src/logger.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/index.mjs": {"bytes": 1131, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/src/console_logger.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/src/logger.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/tsc_plugin.mjs": {"bytes": 17476, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/index.mjs": {"bytes": 5606, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/version.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/entry_points.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/perform_compile.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/private/tooling.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/tsc_plugin.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/perform_watch.mjs": {"bytes": 38189, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/perform_compile.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/entry_points.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/main.mjs": {"bytes": 23169, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/perform_compile.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/perform_watch.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/api.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/bin/ngc.mjs": {"bytes": 3011, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/main.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/ngcc/index.mjs": {"bytes": 6442, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/extract_i18n.mjs": {"bytes": 5133, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/main.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/api.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/bin/ng_xi18n.mjs": {"bytes": 2373, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/extract_i18n.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs": {"bytes": 2520, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/ast/utils.mjs": {"bytes": 1969, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/ast/ast_value.mjs": {"bytes": 31283, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/linker_import_generator.mjs": {"bytes": 4122, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/emit_scopes/emit_scope.mjs": {"bytes": 10007, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/linker_import_generator.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/emit_scopes/local_emit_scope.mjs": {"bytes": 4602, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/emit_scopes/emit_scope.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/get_source_file.mjs": {"bytes": 3423, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_class_metadata_linker_1.mjs": {"bytes": 4340, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs": {"bytes": 12471, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_directive_linker_1.mjs": {"bytes": 31676, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_component_linker_1.mjs": {"bytes": 47646, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_directive_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_factory_linker_1.mjs": {"bytes": 6721, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_injectable_linker_1.mjs": {"bytes": 8118, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_injector_linker_1.mjs": {"bytes": 5267, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_ng_module_linker_1.mjs": {"bytes": 14692, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_pipe_linker_1.mjs": {"bytes": 5652, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_linker_selector.mjs": {"bytes": 26349, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/get_source_file.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_class_metadata_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_component_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_directive_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_factory_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_injectable_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_injector_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_ng_module_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_pipe_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/file_linker.mjs": {"bytes": 13324, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/ast/ast_value.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/emit_scopes/emit_scope.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/emit_scopes/local_emit_scope.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_linker_selector.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/content_origin.mjs": {"bytes": 3444, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/segment_marker.mjs": {"bytes": 5101, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/source_file.mjs": {"bytes": 60134, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/segment_marker.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/source_file_loader.mjs": {"bytes": 32541, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/content_origin.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/source_file.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/index.mjs": {"bytes": 1443, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/content_origin.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/source_file.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/source_file_loader.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/linker_options.mjs": {"bytes": 2991, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/translator.mjs": {"bytes": 3987, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/linker_environment.mjs": {"bytes": 5061, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/linker_options.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/translator.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/needs_linking.mjs": {"bytes": 3455, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_linker_selector.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/index.mjs": {"bytes": 2014, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/ast/utils.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/file_linker.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/linker_environment.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/linker_options.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/needs_linking.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/ast/babel_ast_factory.mjs": {"bytes": 23031, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/ast/babel_ast_host.mjs": {"bytes": 21783, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/babel_declaration_scope.mjs": {"bytes": 7835, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/es2015_linker_plugin.mjs": {"bytes": 20916, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/ast/babel_ast_factory.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/ast/babel_ast_host.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/babel_declaration_scope.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/babel_plugin.mjs": {"bytes": 4393, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/es2015_linker_plugin.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/index.mjs": {"bytes": 1317, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/babel_plugin.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/es2015_linker_plugin.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/private/bazel.mjs": {"bytes": 1229, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/private/localize.mjs": {"bytes": 1529, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/private/migrations.mjs": {"bytes": 2456, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs", "kind": "import-statement"}]}}, "outputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/private/migrations.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 69}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/private/migrations.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-YUMIYLNL.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-26Z5EPVF.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-NMMGOE7N.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-R4KQI5XI.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-75YFKYUJ.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-XI2RTGAL.js", "kind": "import-statement"}], "exports": ["DynamicValue", "PartialEvaluator", "PotentialImportKind", "PotentialImportMode", "Reference", "StaticInterpreter", "TypeScriptReflectionHost", "forwardRefResolver", "reflectObjectLiteral"], "entryPoint": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/private/migrations.mjs", "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/private/migrations.mjs": {"bytesInOutput": 0}}, "bytes": 973}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/private/tooling.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 69}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/private/tooling.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-NDT2FVCM.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-26Z5EPVF.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-NMMGOE7N.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-R4KQI5XI.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-75YFKYUJ.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-XI2RTGAL.js", "kind": "import-statement"}], "exports": ["GLOBAL_DEFS_FOR_TERSER", "GLOBAL_DEFS_FOR_TERSER_WITH_AOT", "constructorParametersDownlevelTransform"], "entryPoint": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/private/tooling.mjs", "inputs": {}, "bytes": 580}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/index.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 1903}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/index.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-NDT2FVCM.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-LV7FGTGX.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-YUMIYLNL.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-26Z5EPVF.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-NMMGOE7N.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-R4KQI5XI.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-SBDNBITT.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-75YFKYUJ.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-XI2RTGAL.js", "kind": "import-statement"}], "exports": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DEFAULT_ERROR_CODE", "DecoratorType", "DocsExtractor", "EmitFlags", "EntryType", "GLOBAL_DEFS_FOR_TERSER", "GLOBAL_DEFS_FOR_TERSER_WITH_AOT", "LogLevel", "LogicalFileSystem", "LogicalProjectPath", "MemberTags", "MemberType", "NgTscPlugin", "NgtscCompilerHost", "NgtscProgram", "NodeJSFileSystem", "OptimizeFor", "SOURCE", "UNKNOWN_ERROR_CODE", "VERSION", "absoluteFrom", "absoluteFromSourceFile", "angularJitApplicationTransform", "basename", "calcProjectFileAndBasePath", "constructorParametersDownlevelTransform", "createCompilerHost", "createProgram", "defaultGatherDiagnostics", "dirname", "exitCodeFromResult", "formatDiagnostics", "getDownlevelDecoratorsTransform", "getFileSystem", "getInitializerApiJitTransform", "getSourceFileOrError", "isLocalCompilationDiagnostics", "isLocalRelativePath", "isRoot", "isRooted", "isTsDiagnostic", "join", "performCompilation", "readConfiguration", "relative", "relativeFrom", "resolve", "setFileSystem", "toRelativeImport"], "entryPoint": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/index.mjs", "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/version.mjs": {"bytesInOutput": 93}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/index.mjs": {"bytesInOutput": 39}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/tsc_plugin.mjs": {"bytesInOutput": 2690}}, "bytes": 5923}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-NDT2FVCM.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 13985}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-NDT2FVCM.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-26Z5EPVF.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-NMMGOE7N.js", "kind": "import-statement"}], "exports": ["GLOBAL_DEFS_FOR_TERSER", "GLOBAL_DEFS_FOR_TERSER_WITH_AOT", "angularJitApplicationTransform", "constructorParametersDownlevelTransform", "getDownlevelDecoratorsTransform", "getInitializerApiJitTransform"], "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/downlevel_decorators_transform.mjs": {"bytesInOutput": 13441}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/initializer_api_transforms/transform.mjs": {"bytesInOutput": 1982}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/initializer_api_transforms/transform_api.mjs": {"bytesInOutput": 737}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/initializer_api_transforms/input_function.mjs": {"bytesInOutput": 1319}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/initializer_api_transforms/model_function.mjs": {"bytesInOutput": 1952}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/initializer_api_transforms/output_function.mjs": {"bytesInOutput": 952}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/initializer_api_transforms/query_functions.mjs": {"bytesInOutput": 1548}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/jit_transforms/index.mjs": {"bytesInOutput": 706}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/private/tooling.mjs": {"bytesInOutput": 310}}, "bytes": 25276}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/src/bin/ngc.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 387}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/src/bin/ngc.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-ZVICXMWS.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-LV7FGTGX.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-YUMIYLNL.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-26Z5EPVF.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-NMMGOE7N.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-R4KQI5XI.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-75YFKYUJ.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-XI2RTGAL.js", "kind": "import-statement"}], "exports": [], "entryPoint": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/bin/ngc.mjs", "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/bin/ngc.mjs": {"bytesInOutput": 337}}, "bytes": 1134}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/ngcc/index.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 696}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/ngcc/index.js": {"imports": [], "exports": [], "entryPoint": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/ngcc/index.mjs", "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/ngcc/index.mjs": {"bytesInOutput": 1397}}, "bytes": 1862}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/src/bin/ng_xi18n.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 892}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/src/bin/ng_xi18n.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-ZVICXMWS.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-LV7FGTGX.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-YUMIYLNL.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-26Z5EPVF.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-NMMGOE7N.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-R4KQI5XI.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-75YFKYUJ.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-XI2RTGAL.js", "kind": "import-statement"}], "exports": [], "entryPoint": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/bin/ng_xi18n.mjs", "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/bin/ng_xi18n.mjs": {"bytesInOutput": 197}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/extract_i18n.mjs": {"bytesInOutput": 859}}, "bytes": 2087}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-ZVICXMWS.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 6893}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-ZVICXMWS.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-LV7FGTGX.js", "kind": "import-statement"}], "exports": ["main", "readCommandLineAndConfiguration"], "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/main.mjs": {"bytesInOutput": 4094}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/perform_watch.mjs": {"bytesInOutput": 8014}}, "bytes": 12994}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-LV7FGTGX.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 235066}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-LV7FGTGX.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-YUMIYLNL.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-26Z5EPVF.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-NMMGOE7N.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-R4KQI5XI.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-75YFKYUJ.js", "kind": "import-statement"}], "exports": ["DEFAULT_ERROR_CODE", "DecoratorType", "DocsExtractor", "EmitFlags", "EntryType", "MemberTags", "MemberType", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgCompilerHost", "NgtscProgram", "PatchedProgramIncrementalBuildStrategy", "SOURCE", "TsCreateProgramDriver", "UNKNOWN_ERROR_CODE", "calcProjectFileAndBasePath", "createCompilerHost", "createMessageDiagnostic", "createProgram", "defaultGatherDiagnostics", "exitCodeFromResult", "formatDiagnostics", "freshCompilationTicket", "incrementalFromStateTicket", "isTsDiagnostic", "performCompilation", "readConfiguration", "untagAllTsFiles"], "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/api.mjs": {"bytesInOutput": 618}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/compiler_host.mjs": {"bytesInOutput": 242}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.mjs": {"bytesInOutput": 1520}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/extractor.mjs": {"bytesInOutput": 3131}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/class_extractor.mjs": {"bytesInOutput": 9471}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/filters.mjs": {"bytesInOutput": 158}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/function_extractor.mjs": {"bytesInOutput": 2350}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/generics_extractor.mjs": {"bytesInOutput": 404}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.mjs": {"bytesInOutput": 1781}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/type_extractor.mjs": {"bytesInOutput": 118}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/constant_extractor.mjs": {"bytesInOutput": 2409}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/decorator_extractor.mjs": {"bytesInOutput": 3205}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/enum_extractor.mjs": {"bytesInOutput": 1131}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/initializer_api_function_extractor.mjs": {"bytesInOutput": 5254}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/type_alias_extractor.mjs": {"bytesInOutput": 317}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program.mjs": {"bytesInOutput": 9008}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/i18n.mjs": {"bytesInOutput": 1466}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/typescript_support.mjs": {"bytesInOutput": 520}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/version_helpers.mjs": {"bytesInOutput": 929}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/compiler.mjs": {"bytesInOutput": 37578}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/cycles/src/analyzer.mjs": {"bytesInOutput": 1749}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/cycles/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/cycles/src/imports.mjs": {"bytesInOutput": 2619}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/generator.mjs": {"bytesInOutput": 794}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/logic.mjs": {"bytesInOutput": 495}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/private_export_checker.mjs": {"bytesInOutput": 2939}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/reference_graph.mjs": {"bytesInOutput": 1410}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/src/api.mjs": {"bytesInOutput": 245}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/src/ts_create_program_driver.mjs": {"bytesInOutput": 4843}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/adapter.mjs": {"bytesInOutput": 3214}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/expando.mjs": {"bytesInOutput": 1487}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/util.mjs": {"bytesInOutput": 144}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/reference_tagger.mjs": {"bytesInOutput": 991}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/dependency_tracking.mjs": {"bytesInOutput": 1974}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/state.mjs": {"bytesInOutput": 337}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/incremental.mjs": {"bytesInOutput": 7349}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/noop.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/strategy.mjs": {"bytesInOutput": 824}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/api.mjs": {"bytesInOutput": 659}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/context.mjs": {"bytesInOutput": 162}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/transform.mjs": {"bytesInOutput": 1129}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/template.mjs": {"bytesInOutput": 9567}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/ng_module_index.mjs": {"bytesInOutput": 3128}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/resource/src/loader.mjs": {"bytesInOutput": 5625}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/resource/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/standalone.mjs": {"bytesInOutput": 3601}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/checker.mjs": {"bytesInOutput": 25971}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/src/diagnostic.mjs": {"bytesInOutput": 3739}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/src/id.mjs": {"bytesInOutput": 437}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/completion.mjs": {"bytesInOutput": 5196}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/comments.mjs": {"bytesInOutput": 4563}, "node_modules/magic-string/dist/magic-string.es.mjs": {"bytesInOutput": 30832}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/context.mjs": {"bytesInOutput": 9555}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/dom.mjs": {"bytesInOutput": 2849}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/environment.mjs": {"bytesInOutput": 2694}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/reference_emit_environment.mjs": {"bytesInOutput": 1586}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/ts_util.mjs": {"bytesInOutput": 2718}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_constructor.mjs": {"bytesInOutput": 4761}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/tcb_util.mjs": {"bytesInOutput": 3488}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_parameter_emitter.mjs": {"bytesInOutput": 2747}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/oob.mjs": {"bytesInOutput": 11873}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/shim.mjs": {"bytesInOutput": 541}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_check_block.mjs": {"bytesInOutput": 61013}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/diagnostics.mjs": {"bytesInOutput": 1545}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/expression.mjs": {"bytesInOutput": 12803}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_check_file.mjs": {"bytesInOutput": 2034}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/source.mjs": {"bytesInOutput": 1551}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/line_mappings.mjs": {"bytesInOutput": 1140}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/template_symbol_builder.mjs": {"bytesInOutput": 20196}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/interpolated_signal_not_invoked/index.mjs": {"bytesInOutput": 2263}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/symbol_util.mjs": {"bytesInOutput": 881}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/api.mjs": {"bytesInOutput": 3423}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/extended_template_checker.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/invalid_banana_in_box/index.mjs": {"bytesInOutput": 1030}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/missing_control_flow_directive/index.mjs": {"bytesInOutput": 2318}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/missing_ngforof_let/index.mjs": {"bytesInOutput": 1021}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/nullish_coalescing_not_nullable/index.mjs": {"bytesInOutput": 1735}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/optional_chain_not_nullable/index.mjs": {"bytesInOutput": 1983}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/suffix_not_supported/index.mjs": {"bytesInOutput": 923}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/text_attribute_not_binding/index.mjs": {"bytesInOutput": 1481}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/src/extended_template_checker.mjs": {"bytesInOutput": 2044}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/adapter.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/interfaces.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/options.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/public_options.mjs": {"bytesInOutput": 281}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/index.mjs": {"bytesInOutput": 333}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/template_semantics/src/template_semantics_checker.mjs": {"bytesInOutput": 3981}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/core_version.mjs": {"bytesInOutput": 551}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/feature_detection.mjs": {"bytesInOutput": 217}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/host.mjs": {"bytesInOutput": 6374}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/program.mjs": {"bytesInOutput": 134}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/entry_points.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/perform_compile.mjs": {"bytesInOutput": 7369}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/util.mjs": {"bytesInOutput": 267}}, "bytes": 403887}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-YUMIYLNL.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 1417}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-YUMIYLNL.js": {"imports": [], "exports": ["CompletionKind", "OptimizeFor", "PotentialImportKind", "PotentialImportMode", "SymbolKind"], "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/checker.mjs": {"bytesInOutput": 212}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/scope.mjs": {"bytesInOutput": 534}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/api.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/completion.mjs": {"bytesInOutput": 226}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/context.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/symbols.mjs": {"bytesInOutput": 696}}, "bytes": 2546}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-26Z5EPVF.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 195171}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-26Z5EPVF.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-NMMGOE7N.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-R4KQI5XI.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-75YFKYUJ.js", "kind": "import-statement"}], "exports": ["CompilationMode", "ComponentDecoratorHandler", "ComponentScopeKind", "CompoundComponentScopeReader", "CompoundMetadataReader", "CompoundMetadataRegistry", "DirectiveDecoratorHandler", "DtsMetadataReader", "DtsTransformRegistry", "DynamicValue", "ExportedProviderStatusResolver", "HostDirectivesResolver", "InjectableClassRegistry", "InjectableDecoratorHandler", "LocalMetadataRegistry", "LocalModuleScopeRegistry", "<PERSON><PERSON><PERSON><PERSON>", "MetadataDtsModuleScopeResolver", "NgModuleDecoratorHandler", "NoopReferencesRegistry", "PartialEvaluator", "PipeDecoratorHandler", "ResourceRegistry", "SemanticDepGraphUpdater", "StaticInterpreter", "TraitCompiler", "TypeCheckScopeRegistry", "aliasTransformFactory", "declarationTransformFactory", "forwardRefResolver", "getAngularDecorators", "isAngularDecorator", "isHostDirectiveMetaForGlobalMode", "ivyTransformFactory", "queryDecoratorNames", "tryParseInitializerBasedOutput", "tryParseSignalInputMapping", "tryParseSignalModelMapping", "tryParseSignalQueryFromInitializer"], "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/util.mjs": {"bytesInOutput": 8715}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/dynamic.mjs": {"bytesInOutput": 2270}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/interpreter.mjs": {"bytesInOutput": 23197}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/result.mjs": {"bytesInOutput": 615}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/builtin.mjs": {"bytesInOutput": 1240}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/synthetic.mjs": {"bytesInOutput": 83}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/interface.mjs": {"bytesInOutput": 603}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/diagnostics.mjs": {"bytesInOutput": 4521}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/api.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/di.mjs": {"bytesInOutput": 5597}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/diagnostics.mjs": {"bytesInOutput": 11128}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/api.mjs": {"bytesInOutput": 433}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/dts.mjs": {"bytesInOutput": 7412}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/property_mapping.mjs": {"bytesInOutput": 2510}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/util.mjs": {"bytesInOutput": 6264}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/inheritance.mjs": {"bytesInOutput": 2094}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/registry.mjs": {"bytesInOutput": 1649}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/resource_registry.mjs": {"bytesInOutput": 2289}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/providers.mjs": {"bytesInOutput": 1345}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/host_directives_resolver.mjs": {"bytesInOutput": 2275}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/api.mjs": {"bytesInOutput": 588}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/alias.mjs": {"bytesInOutput": 745}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/compilation.mjs": {"bytesInOutput": 16820}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/trait.mjs": {"bytesInOutput": 1906}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/declaration.mjs": {"bytesInOutput": 4845}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/transform.mjs": {"bytesInOutput": 10611}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/visitor.mjs": {"bytesInOutput": 2068}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/evaluation.mjs": {"bytesInOutput": 1799}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/factory.mjs": {"bytesInOutput": 591}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/injectable_registry.mjs": {"bytesInOutput": 693}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/metadata.mjs": {"bytesInOutput": 4559}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/debug_info.mjs": {"bytesInOutput": 935}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/references_registry.mjs": {"bytesInOutput": 75}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/schema.mjs": {"bytesInOutput": 1168}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/input_transforms.mjs": {"bytesInOutput": 457}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/handler.mjs": {"bytesInOutput": 47992}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/api.mjs": {"bytesInOutput": 348}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/graph.mjs": {"bytesInOutput": 4422}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/type_parameters.mjs": {"bytesInOutput": 648}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/util.mjs": {"bytesInOutput": 1072}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/api.mjs": {"bytesInOutput": 260}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/component_scope.mjs": {"bytesInOutput": 534}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/dependency.mjs": {"bytesInOutput": 2461}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/local.mjs": {"bytesInOutput": 15462}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/util.mjs": {"bytesInOutput": 1711}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/typecheck.mjs": {"bytesInOutput": 3068}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/handler.mjs": {"bytesInOutput": 8841}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/shared.mjs": {"bytesInOutput": 40169}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_function_access.mjs": {"bytesInOutput": 535}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_functions.mjs": {"bytesInOutput": 2977}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/input_output_parse_options.mjs": {"bytesInOutput": 704}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/input_function.mjs": {"bytesInOutput": 1044}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/model_function.mjs": {"bytesInOutput": 1210}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/output_function.mjs": {"bytesInOutput": 1360}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/query_functions.mjs": {"bytesInOutput": 3371}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/symbol.mjs": {"bytesInOutput": 3033}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/src/handler.mjs": {"bytesInOutput": 27110}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/src/module_with_providers.mjs": {"bytesInOutput": 2987}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/diagnostics.mjs": {"bytesInOutput": 910}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/resources.mjs": {"bytesInOutput": 14554}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/symbol.mjs": {"bytesInOutput": 1625}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/util.mjs": {"bytesInOutput": 3159}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/src/injectable.mjs": {"bytesInOutput": 9479}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/src/pipe.mjs": {"bytesInOutput": 6184}}, "bytes": 349832}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/linker/index.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 69}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/linker/index.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-KAFBWQ67.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-NMMGOE7N.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-WCD6LVCP.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-75YFKYUJ.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-XI2RTGAL.js", "kind": "import-statement"}], "exports": ["DEFAULT_LINKER_OPTIONS", "FatalLinkerError", "FileLinker", "LinkerEnvironment", "assert", "isFatalLinkerError", "needsLinking"], "entryPoint": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/index.mjs", "inputs": {}, "bytes": 597}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/linker/babel/index.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 8991}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/linker/babel/index.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-KAFBWQ67.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-NMMGOE7N.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-SBDNBITT.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-WCD6LVCP.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-75YFKYUJ.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-XI2RTGAL.js", "kind": "import-statement"}], "exports": ["createEs2015LinkerPlugin", "default"], "entryPoint": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/index.mjs", "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/es2015_linker_plugin.mjs": {"bytesInOutput": 3570}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/ast/babel_ast_factory.mjs": {"bytesInOutput": 4636}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/ast/babel_ast_host.mjs": {"bytesInOutput": 4219}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/babel_declaration_scope.mjs": {"bytesInOutput": 776}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/babel_plugin.mjs": {"bytesInOutput": 214}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/index.mjs": {"bytesInOutput": 41}}, "bytes": 14964}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-KAFBWQ67.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 25643}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-KAFBWQ67.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-NMMGOE7N.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-WCD6LVCP.js", "kind": "import-statement"}], "exports": ["DEFAULT_LINKER_OPTIONS", "FatalLinkerError", "FileLinker", "LinkerEnvironment", "assert", "isFatalLinkerError", "needsLinking"], "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs": {"bytesInOutput": 241}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/ast/utils.mjs": {"bytesInOutput": 160}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/ast/ast_value.mjs": {"bytesInOutput": 3804}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/emit_scopes/emit_scope.mjs": {"bytesInOutput": 1360}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/linker_import_generator.mjs": {"bytesInOutput": 582}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/emit_scopes/local_emit_scope.mjs": {"bytesInOutput": 375}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_linker_selector.mjs": {"bytesInOutput": 4092}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/get_source_file.mjs": {"bytesInOutput": 305}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_class_metadata_linker_1.mjs": {"bytesInOutput": 629}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_component_linker_1.mjs": {"bytesInOutput": 9726}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_directive_linker_1.mjs": {"bytesInOutput": 6482}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs": {"bytesInOutput": 1994}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_factory_linker_1.mjs": {"bytesInOutput": 1054}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_injectable_linker_1.mjs": {"bytesInOutput": 1423}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_injector_linker_1.mjs": {"bytesInOutput": 760}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_ng_module_linker_1.mjs": {"bytesInOutput": 2400}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_pipe_linker_1.mjs": {"bytesInOutput": 887}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/file_linker.mjs": {"bytesInOutput": 2042}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/linker_options.mjs": {"bytesInOutput": 126}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/translator.mjs": {"bytesInOutput": 466}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/linker_environment.mjs": {"bytesInOutput": 941}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/needs_linking.mjs": {"bytesInOutput": 105}}, "bytes": 43659}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-NMMGOE7N.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 68639}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-NMMGOE7N.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-75YFKYUJ.js", "kind": "import-statement"}], "exports": ["AbsoluteModuleStrategy", "AliasStrategy", "AmbientImport", "COMPILER_ERRORS_WITH_GUIDES", "ClassMemberAccessLevel", "ClassMemberKind", "Context", "DefaultImportTracker", "DeferredSymbolTracker", "ERROR_DETAILS_PAGE_BASE_URL", "ErrorCode", "ExpressionTranslatorVisitor", "ExtendedTemplateDiagnosticName", "FatalDiagnosticError", "ImportFlags", "ImportManager", "ImportedSymbolsTracker", "LocalCompilationExtraImportsTracker", "LocalIdentifierStrategy", "LogicalProjectStrategy", "ModuleResolver", "NoopImportRewriter", "PrivateExportAliasingHost", "R3SymbolsImportRewriter", "Reference", "ReferenceEmitter", "RelativePathStrategy", "TypeEmitter", "TypeScriptReflectionHost", "UnifiedModulesAliasingHost", "UnifiedModulesStrategy", "addDiagnostic<PERSON><PERSON><PERSON>", "assertSuccessfulReferenceEmit", "attachDefaultImportDeclaration", "canEmitType", "classMemberAccessLevelToString", "entityNameToValue", "filterToMembersWithDecorator", "getDefaultImportDeclaration", "getRootDirs", "getSourceFile", "getSourceFileOrNull", "getTokenAtPosition", "identifierOfNode", "isAliasImportDeclaration", "isAssignment", "isDeclaration", "isDtsPath", "isFatalDiagnosticError", "isFromDtsFile", "isLocalCompilationDiagnostics", "isNamedClassDeclaration", "isNonDeclarationTsPath", "isSymbolWithValueDeclaration", "loadIsReferencedAliasDeclarationPatch", "makeDiagnostic", "makeDiagno<PERSON><PERSON><PERSON><PERSON>", "makeRelatedInformation", "ngErrorCode", "nodeDebugInfo", "nodeNameForError", "normalizeSeparators", "presetImportManagerForceNamespaceImports", "reflectClassMember", "reflectObjectLiteral", "reflectTypeEntityToDeclaration", "relativePathBetween", "replaceTsWithNgInErrors", "toUnredirectedSourceFile", "translateExpression", "translateStatement", "translateType", "typeNodeToValueExpr"], "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error.mjs": {"bytesInOutput": 2043}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error_code.mjs": {"bytesInOutput": 8458}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/util.mjs": {"bytesInOutput": 235}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/docs.mjs": {"bytesInOutput": 375}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error_details_base_url.mjs": {"bytesInOutput": 63}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/extended_template_diagnostic_name.mjs": {"bytesInOutput": 1113}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/typescript.mjs": {"bytesInOutput": 17368}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/host.mjs": {"bytesInOutput": 1231}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/type_to_value.mjs": {"bytesInOutput": 4964}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/util.mjs": {"bytesInOutput": 673}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs": {"bytesInOutput": 3362}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/references.mjs": {"bytesInOutput": 1949}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/alias.mjs": {"bytesInOutput": 1961}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/emitter.mjs": {"bytesInOutput": 7998}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/find_export.mjs": {"bytesInOutput": 517}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/path.mjs": {"bytesInOutput": 273}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/core.mjs": {"bytesInOutput": 1999}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/patch_alias_reference_resolution.mjs": {"bytesInOutput": 1721}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/default.mjs": {"bytesInOutput": 1334}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/deferred_symbol_tracker.mjs": {"bytesInOutput": 4191}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/imported_symbols_tracker.mjs": {"bytesInOutput": 2641}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/local_compilation_extra_imports_tracker.mjs": {"bytesInOutput": 1464}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/resolver.mjs": {"bytesInOutput": 579}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/import_manager.mjs": {"bytesInOutput": 7763}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/check_unique_identifier_name.mjs": {"bytesInOutput": 819}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/import_typescript_transform.mjs": {"bytesInOutput": 2656}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/reuse_generated_imports.mjs": {"bytesInOutput": 1085}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/reuse_source_file_imports.mjs": {"bytesInOutput": 2222}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/context.mjs": {"bytesInOutput": 273}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/translator.mjs": {"bytesInOutput": 11315}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/type_emitter.mjs": {"bytesInOutput": 2727}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/type_translator.mjs": {"bytesInOutput": 9272}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/ts_util.mjs": {"bytesInOutput": 307}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/typescript_ast_factory.mjs": {"bytesInOutput": 8809}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/typescript_translator.mjs": {"bytesInOutput": 541}}, "bytes": 121256}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/private/bazel.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 69}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/private/bazel.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-R4KQI5XI.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-XI2RTGAL.js", "kind": "import-statement"}], "exports": ["PerfPhase"], "entryPoint": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/private/bazel.mjs", "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/private/bazel.mjs": {"bytesInOutput": 0}}, "bytes": 474}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-R4KQI5XI.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 4593}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-R4KQI5XI.js": {"imports": [], "exports": ["ActivePerfRecorder", "DelegatingPerfRecorder", "PerfCheckpoint", "PerfEvent", "PerfPhase"], "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/api.mjs": {"bytesInOutput": 4130}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/noop.mjs": {"bytesInOutput": 231}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/clock.mjs": {"bytesInOutput": 178}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/recorder.mjs": {"bytesInOutput": 2452}}, "bytes": 7839}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/private/localize.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 69}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/private/localize.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-SBDNBITT.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-WCD6LVCP.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-75YFKYUJ.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-XI2RTGAL.js", "kind": "import-statement"}], "exports": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LogLevel", "LogicalFileSystem", "LogicalProjectPath", "NgtscCompilerHost", "NodeJSFileSystem", "SourceFile", "SourceFileLoader", "absoluteFrom", "absoluteFromSourceFile", "basename", "dirname", "getFileSystem", "getSourceFileOrError", "isLocalRelativePath", "isRoot", "isRooted", "join", "relative", "relativeFrom", "resolve", "setFileSystem", "toRelativeImport"], "entryPoint": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/private/localize.mjs", "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/private/localize.mjs": {"bytesInOutput": 0}}, "bytes": 1293}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-SBDNBITT.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 935}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-SBDNBITT.js": {"imports": [], "exports": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LogLevel"], "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/src/logger.mjs": {"bytesInOutput": 254}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/src/console_logger.mjs": {"bytesInOutput": 666}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/index.mjs": {"bytesInOutput": 0}}, "bytes": 1537}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-WCD6LVCP.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 8859}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-WCD6LVCP.js": {"imports": [], "exports": ["SourceFile", "SourceFileLoader"], "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/source_file.mjs": {"bytesInOutput": 8817}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/segment_marker.mjs": {"bytesInOutput": 548}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/source_file_loader.mjs": {"bytesInOutput": 4501}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/content_origin.mjs": {"bytesInOutput": 279}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/index.mjs": {"bytesInOutput": 0}}, "bytes": 15211}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-75YFKYUJ.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 7467}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-75YFKYUJ.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-XI2RTGAL.js", "kind": "import-statement"}], "exports": ["LogicalFileSystem", "LogicalProjectPath", "NgtscCompilerHost", "NodeJSFileSystem", "absoluteFrom", "absoluteFromSourceFile", "basename", "dirname", "getFileSystem", "getSourceFileOrError", "isLocalRelativePath", "isRoot", "isRooted", "join", "relative", "relativeFrom", "resolve", "setFileSystem", "stripExtension", "toRelativeImport"], "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/util.mjs": {"bytesInOutput": 495}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/invalid_file_system.mjs": {"bytesInOutput": 1486}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/helpers.mjs": {"bytesInOutput": 1506}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/compiler_host.mjs": {"bytesInOutput": 1646}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/logical.mjs": {"bytesInOutput": 1667}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/node_js_file_system.mjs": {"bytesInOutput": 2710}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs": {"bytesInOutput": 0}}, "bytes": 10919}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-XI2RTGAL.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 69}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-XI2RTGAL.js": {"imports": [], "exports": ["__require"], "inputs": {}, "bytes": 572}}}