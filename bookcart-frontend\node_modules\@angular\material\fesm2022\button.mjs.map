{"version": 3, "file": "button.mjs", "sources": ["../../../../../../src/material/button/button-base.ts", "../../../../../../src/material/button/button.ts", "../../../../../../src/material/button/button.html", "../../../../../../src/material/button/fab.ts", "../../../../../../src/material/button/icon-button.ts", "../../../../../../src/material/button/icon-button.html", "../../../../../../src/material/button/module.ts", "../../../../../../src/material/button/button_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {FocusMonitor, FocusOrigin} from '@angular/cdk/a11y';\nimport {Platform} from '@angular/cdk/platform';\nimport {\n  AfterViewInit,\n  booleanAttribute,\n  Directive,\n  ElementRef,\n  inject,\n  InjectionToken,\n  Input,\n  NgZone,\n  numberAttribute,\n  OnDestroy,\n  OnInit,\n} from '@angular/core';\nimport {MatRipple, MatRippleLoader} from '@angular/material/core';\n\n/** Object that can be used to configure the default options for the button component. */\nexport interface MatButtonConfig {\n  /** Whether disabled buttons should be interactive. */\n  disabledInteractive?: boolean;\n}\n\n/** Injection token that can be used to provide the default options the button component. */\nexport const MAT_BUTTON_CONFIG = new InjectionToken<MatButtonConfig>('MAT_BUTTON_CONFIG');\n\n/** Shared host configuration for all buttons */\nexport const MAT_BUTTON_HOST = {\n  '[attr.disabled]': '_getDisabledAttribute()',\n  '[attr.aria-disabled]': '_getAriaDisabled()',\n  '[class.mat-mdc-button-disabled]': 'disabled',\n  '[class.mat-mdc-button-disabled-interactive]': 'disabledInteractive',\n  '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n  // MDC automatically applies the primary theme color to the button, but we want to support\n  // an unthemed version. If color is undefined, apply a CSS class that makes it easy to\n  // select and style this \"theme\".\n  '[class.mat-unthemed]': '!color',\n  // Add a class that applies to all buttons. This makes it easier to target if somebody\n  // wants to target all Material buttons.\n  '[class.mat-mdc-button-base]': 'true',\n  '[class]': 'color ? \"mat-\" + color : \"\"',\n};\n\n/** List of classes to add to buttons instances based on host attribute selector. */\nconst HOST_SELECTOR_MDC_CLASS_PAIR: {attribute: string; mdcClasses: string[]}[] = [\n  {\n    attribute: 'mat-button',\n    mdcClasses: ['mdc-button', 'mat-mdc-button'],\n  },\n  {\n    attribute: 'mat-flat-button',\n    mdcClasses: ['mdc-button', 'mdc-button--unelevated', 'mat-mdc-unelevated-button'],\n  },\n  {\n    attribute: 'mat-raised-button',\n    mdcClasses: ['mdc-button', 'mdc-button--raised', 'mat-mdc-raised-button'],\n  },\n  {\n    attribute: 'mat-stroked-button',\n    mdcClasses: ['mdc-button', 'mdc-button--outlined', 'mat-mdc-outlined-button'],\n  },\n  {\n    attribute: 'mat-fab',\n    mdcClasses: ['mdc-fab', 'mat-mdc-fab'],\n  },\n  {\n    attribute: 'mat-mini-fab',\n    mdcClasses: ['mdc-fab', 'mdc-fab--mini', 'mat-mdc-mini-fab'],\n  },\n  {\n    attribute: 'mat-icon-button',\n    mdcClasses: ['mdc-icon-button', 'mat-mdc-icon-button'],\n  },\n];\n\n/** Base class for all buttons.  */\n@Directive()\nexport class MatButtonBase implements AfterViewInit, OnDestroy {\n  private readonly _focusMonitor = inject(FocusMonitor);\n\n  /**\n   * Handles the lazy creation of the MatButton ripple.\n   * Used to improve initial load time of large applications.\n   */\n  _rippleLoader: MatRippleLoader = inject(MatRippleLoader);\n\n  /** Whether this button is a FAB. Used to apply the correct class on the ripple. */\n  _isFab = false;\n\n  /**\n   * Reference to the MatRipple instance of the button.\n   * @deprecated Considered an implementation detail. To be removed.\n   * @breaking-change 17.0.0\n   */\n  get ripple(): MatRipple {\n    return this._rippleLoader?.getRipple(this._elementRef.nativeElement)!;\n  }\n  set ripple(v: MatRipple) {\n    this._rippleLoader?.attachRipple(this._elementRef.nativeElement, v);\n  }\n\n  /** Theme color palette of the button */\n  @Input() color?: string | null;\n\n  /** Whether the ripple effect is disabled or not. */\n  @Input({transform: booleanAttribute})\n  get disableRipple(): boolean {\n    return this._disableRipple;\n  }\n  set disableRipple(value: any) {\n    this._disableRipple = value;\n    this._updateRippleDisabled();\n  }\n  private _disableRipple: boolean = false;\n\n  /** Whether the button is disabled. */\n  @Input({transform: booleanAttribute})\n  get disabled(): boolean {\n    return this._disabled;\n  }\n  set disabled(value: any) {\n    this._disabled = value;\n    this._updateRippleDisabled();\n  }\n  private _disabled: boolean = false;\n\n  /** `aria-disabled` value of the button. */\n  @Input({transform: booleanAttribute, alias: 'aria-disabled'})\n  ariaDisabled: boolean | undefined;\n\n  /**\n   * Natively disabled buttons prevent focus and any pointer events from reaching the button.\n   * In some scenarios this might not be desirable, because it can prevent users from finding out\n   * why the button is disabled (e.g. via tooltip).\n   *\n   * Enabling this input will change the button so that it is styled to be disabled and will be\n   * marked as `aria-disabled`, but it will allow the button to receive events and focus.\n   *\n   * Note that by enabling this, you need to set the `tabindex` yourself if the button isn't\n   * meant to be tabbable and you have to prevent the button action (e.g. form submissions).\n   */\n  @Input({transform: booleanAttribute})\n  disabledInteractive: boolean;\n\n  constructor(\n    public _elementRef: ElementRef,\n    public _platform: Platform,\n    public _ngZone: NgZone,\n    public _animationMode?: string,\n  ) {\n    const config = inject(MAT_BUTTON_CONFIG, {optional: true});\n    const element = _elementRef.nativeElement;\n    const classList = (element as HTMLElement).classList;\n\n    this.disabledInteractive = config?.disabledInteractive ?? false;\n    this._rippleLoader?.configureRipple(element, {className: 'mat-mdc-button-ripple'});\n\n    // For each of the variant selectors that is present in the button's host\n    // attributes, add the correct corresponding MDC classes.\n    for (const {attribute, mdcClasses} of HOST_SELECTOR_MDC_CLASS_PAIR) {\n      if (element.hasAttribute(attribute)) {\n        classList.add(...mdcClasses);\n      }\n    }\n  }\n\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this._elementRef, true);\n  }\n\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    this._rippleLoader?.destroyRipple(this._elementRef.nativeElement);\n  }\n\n  /** Focuses the button. */\n  focus(origin: FocusOrigin = 'program', options?: FocusOptions): void {\n    if (origin) {\n      this._focusMonitor.focusVia(this._elementRef.nativeElement, origin, options);\n    } else {\n      this._elementRef.nativeElement.focus(options);\n    }\n  }\n\n  protected _getAriaDisabled() {\n    if (this.ariaDisabled != null) {\n      return this.ariaDisabled;\n    }\n\n    return this.disabled && this.disabledInteractive ? true : null;\n  }\n\n  protected _getDisabledAttribute() {\n    return this.disabledInteractive || !this.disabled ? null : true;\n  }\n\n  private _updateRippleDisabled(): void {\n    this._rippleLoader?.setDisabled(\n      this._elementRef.nativeElement,\n      this.disableRipple || this.disabled,\n    );\n  }\n}\n\n/** Shared host configuration for buttons using the `<a>` tag. */\nexport const MAT_ANCHOR_HOST = {\n  '[attr.disabled]': '_getDisabledAttribute()',\n  '[class.mat-mdc-button-disabled]': 'disabled',\n  '[class.mat-mdc-button-disabled-interactive]': 'disabledInteractive',\n  '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n\n  // Note that we ignore the user-specified tabindex when it's disabled for\n  // consistency with the `mat-button` applied on native buttons where even\n  // though they have an index, they're not tabbable.\n  '[attr.tabindex]': 'disabled && !disabledInteractive ? -1 : tabIndex',\n  '[attr.aria-disabled]': '_getDisabledAttribute()',\n  // MDC automatically applies the primary theme color to the button, but we want to support\n  // an unthemed version. If color is undefined, apply a CSS class that makes it easy to\n  // select and style this \"theme\".\n  '[class.mat-unthemed]': '!color',\n  // Add a class that applies to all buttons. This makes it easier to target if somebody\n  // wants to target all Material buttons.\n  '[class.mat-mdc-button-base]': 'true',\n  '[class]': 'color ? \"mat-\" + color : \"\"',\n};\n\n/**\n * Anchor button base.\n */\n@Directive()\nexport class MatAnchorBase extends MatButtonBase implements OnInit, OnDestroy {\n  @Input({\n    transform: (value: unknown) => {\n      return value == null ? undefined : numberAttribute(value);\n    },\n  })\n  tabIndex: number;\n\n  constructor(elementRef: ElementRef, platform: Platform, ngZone: NgZone, animationMode?: string) {\n    super(elementRef, platform, ngZone, animationMode);\n  }\n\n  ngOnInit(): void {\n    this._ngZone.runOutsideAngular(() => {\n      this._elementRef.nativeElement.addEventListener('click', this._haltDisabledEvents);\n    });\n  }\n\n  override ngOnDestroy(): void {\n    super.ngOnDestroy();\n    this._elementRef.nativeElement.removeEventListener('click', this._haltDisabledEvents);\n  }\n\n  _haltDisabledEvents = (event: Event): void => {\n    // A disabled button shouldn't apply any actions\n    if (this.disabled) {\n      event.preventDefault();\n      event.stopImmediatePropagation();\n    }\n  };\n\n  protected override _getAriaDisabled() {\n    return this.ariaDisabled == null ? this.disabled : this.ariaDisabled;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Platform} from '@angular/cdk/platform';\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  ElementRef,\n  Inject,\n  NgZone,\n  Optional,\n  ViewEncapsulation,\n  ANIMATION_MODULE_TYPE,\n} from '@angular/core';\n\nimport {MAT_ANCHOR_HOST, MAT_BUTTON_HOST, MatAnchorBase, MatButtonBase} from './button-base';\n\n/**\n * Material Design button component. Users interact with a button to perform an action.\n * See https://material.io/components/buttons\n *\n * The `MatButton` class applies to native button elements and captures the appearances for\n * \"text button\", \"outlined button\", and \"contained button\" per the Material Design\n * specification. `MatButton` additionally captures an additional \"flat\" appearance, which matches\n * \"contained\" but without elevation.\n */\n@Component({\n  selector: `\n    button[mat-button], button[mat-raised-button], button[mat-flat-button],\n    button[mat-stroked-button]\n  `,\n  templateUrl: 'button.html',\n  styleUrls: ['button.css', 'button-high-contrast.css'],\n  host: MAT_BUTTON_HOST,\n  exportAs: 'matButton',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: true,\n})\nexport class MatButton extends MatButtonBase {\n  constructor(\n    elementRef: ElementRef,\n    platform: Platform,\n    ngZone: NgZone,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) animationMode?: string,\n  ) {\n    super(elementRef, platform, ngZone, animationMode);\n  }\n}\n\n/**\n * Material Design button component for anchor elements. Anchor elements are used to provide\n * links for the user to navigate across different routes or pages.\n * See https://material.io/components/buttons\n *\n * The `MatAnchor` class applies to native anchor elements and captures the appearances for\n * \"text button\", \"outlined button\", and \"contained button\" per the Material Design\n * specification. `MatAnchor` additionally captures an additional \"flat\" appearance, which matches\n * \"contained\" but without elevation.\n */\n@Component({\n  selector: `a[mat-button], a[mat-raised-button], a[mat-flat-button], a[mat-stroked-button]`,\n  exportAs: 'matButton, matAnchor',\n  host: MAT_ANCHOR_HOST,\n  templateUrl: 'button.html',\n  styleUrls: ['button.css', 'button-high-contrast.css'],\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: true,\n})\nexport class MatAnchor extends MatAnchorBase {\n  constructor(\n    elementRef: ElementRef,\n    platform: Platform,\n    ngZone: NgZone,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) animationMode?: string,\n  ) {\n    super(elementRef, platform, ngZone, animationMode);\n  }\n}\n", "<span\n    class=\"mat-mdc-button-persistent-ripple\"\n    [class.mdc-button__ripple]=\"!_isFab\"\n    [class.mdc-fab__ripple]=\"_isFab\"></span>\n\n<ng-content select=\".material-icons:not([iconPositionEnd]), mat-icon:not([iconPositionEnd]), [matButtonIcon]:not([iconPositionEnd])\">\n</ng-content>\n\n<span class=\"mdc-button__label\"><ng-content></ng-content></span>\n\n<ng-content select=\".material-icons[iconPositionEnd], mat-icon[iconPositionEnd], [matButtonIcon][iconPositionEnd]\">\n</ng-content>\n\n<!--\n  The indicator can't be directly on the button, because MDC uses ::before for high contrast\n  indication and it can't be on the ripple, because it has a border radius and overflow: hidden.\n-->\n<span class=\"mat-mdc-focus-indicator\"></span>\n\n<span class=\"mat-mdc-button-touch-target\"></span>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Platform} from '@angular/cdk/platform';\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  ElementRef,\n  Inject,\n  InjectionToken,\n  Input,\n  NgZone,\n  Optional,\n  ViewEncapsulation,\n  booleanAttribute,\n  ANIMATION_MODULE_TYPE,\n} from '@angular/core';\n\nimport {MatAnchor} from './button';\nimport {MAT_ANCHOR_HOST, MAT_BUTTON_HOST, MatButtonBase} from './button-base';\nimport {ThemePalette} from '@angular/material/core';\n\n/** Default FAB options that can be overridden. */\nexport interface MatFabDefaultOptions {\n  color?: ThemePalette;\n}\n\n/** Injection token to be used to override the default options for FAB. */\nexport const MAT_FAB_DEFAULT_OPTIONS = new InjectionToken<MatFabDefaultOptions>(\n  'mat-mdc-fab-default-options',\n  {\n    providedIn: 'root',\n    factory: MAT_FAB_DEFAULT_OPTIONS_FACTORY,\n  },\n);\n\n/** @docs-private */\nexport function MAT_FAB_DEFAULT_OPTIONS_FACTORY(): MatFabDefaultOptions {\n  return {\n    // The FAB by default has its color set to accent.\n    color: 'accent',\n  };\n}\n\n// Default FAB configuration.\nconst defaults = MAT_FAB_DEFAULT_OPTIONS_FACTORY();\n\n/**\n * Material Design floating action button (FAB) component. These buttons represent the primary\n * or most common action for users to interact with.\n * See https://material.io/components/buttons-floating-action-button/\n *\n * The `MatFabButton` class has two appearances: normal and extended.\n */\n@Component({\n  selector: `button[mat-fab]`,\n  templateUrl: 'button.html',\n  styleUrl: 'fab.css',\n  host: {\n    ...MAT_BUTTON_HOST,\n    '[class.mdc-fab--extended]': 'extended',\n    '[class.mat-mdc-extended-fab]': 'extended',\n  },\n  exportAs: 'matButton',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: true,\n})\nexport class MatFabButton extends MatButtonBase {\n  override _isFab = true;\n\n  @Input({transform: booleanAttribute}) extended: boolean;\n\n  constructor(\n    elementRef: ElementRef,\n    platform: Platform,\n    ngZone: NgZone,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) animationMode?: string,\n    @Optional() @Inject(MAT_FAB_DEFAULT_OPTIONS) private _options?: MatFabDefaultOptions,\n  ) {\n    super(elementRef, platform, ngZone, animationMode);\n    this._options = this._options || defaults;\n    this.color = this._options!.color || defaults.color;\n  }\n}\n\n/**\n * Material Design mini floating action button (FAB) component. These buttons represent the primary\n * or most common action for users to interact with.\n * See https://material.io/components/buttons-floating-action-button/\n */\n@Component({\n  selector: `button[mat-mini-fab]`,\n  templateUrl: 'button.html',\n  styleUrl: 'fab.css',\n  host: MAT_BUTTON_HOST,\n  exportAs: 'matButton',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: true,\n})\nexport class MatMiniFabButton extends MatButtonBase {\n  override _isFab = true;\n\n  constructor(\n    elementRef: ElementRef,\n    platform: Platform,\n    ngZone: NgZone,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) animationMode?: string,\n    @Optional() @Inject(MAT_FAB_DEFAULT_OPTIONS) private _options?: MatFabDefaultOptions,\n  ) {\n    super(elementRef, platform, ngZone, animationMode);\n    this._options = this._options || defaults;\n    this.color = this._options!.color || defaults.color;\n  }\n}\n\n/**\n * Material Design floating action button (FAB) component for anchor elements. Anchor elements\n * are used to provide links for the user to navigate across different routes or pages.\n * See https://material.io/components/buttons-floating-action-button/\n *\n * The `MatFabAnchor` class has two appearances: normal and extended.\n */\n@Component({\n  selector: `a[mat-fab]`,\n  templateUrl: 'button.html',\n  styleUrl: 'fab.css',\n  host: {\n    ...MAT_ANCHOR_HOST,\n    '[class.mdc-fab--extended]': 'extended',\n    '[class.mat-mdc-extended-fab]': 'extended',\n  },\n  exportAs: 'matButton, matAnchor',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: true,\n})\nexport class MatFabAnchor extends MatAnchor {\n  override _isFab = true;\n\n  @Input({transform: booleanAttribute}) extended: boolean;\n\n  constructor(\n    elementRef: ElementRef,\n    platform: Platform,\n    ngZone: NgZone,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) animationMode?: string,\n    @Optional() @Inject(MAT_FAB_DEFAULT_OPTIONS) private _options?: MatFabDefaultOptions,\n  ) {\n    super(elementRef, platform, ngZone, animationMode);\n    this._options = this._options || defaults;\n    this.color = this._options!.color || defaults.color;\n  }\n}\n\n/**\n * Material Design mini floating action button (FAB) component for anchor elements. Anchor elements\n * are used to provide links for the user to navigate across different routes or pages.\n * See https://material.io/components/buttons-floating-action-button/\n */\n@Component({\n  selector: `a[mat-mini-fab]`,\n  templateUrl: 'button.html',\n  styleUrl: 'fab.css',\n  host: MAT_ANCHOR_HOST,\n  exportAs: 'matButton, matAnchor',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: true,\n})\nexport class MatMiniFabAnchor extends MatAnchor {\n  override _isFab = true;\n\n  constructor(\n    elementRef: ElementRef,\n    platform: Platform,\n    ngZone: NgZone,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) animationMode?: string,\n    @Optional() @Inject(MAT_FAB_DEFAULT_OPTIONS) private _options?: MatFabDefaultOptions,\n  ) {\n    super(elementRef, platform, ngZone, animationMode);\n    this._options = this._options || defaults;\n    this.color = this._options!.color || defaults.color;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Platform} from '@angular/cdk/platform';\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  ElementRef,\n  Inject,\n  NgZone,\n  Optional,\n  ViewEncapsulation,\n  ANIMATION_MODULE_TYPE,\n} from '@angular/core';\nimport {MAT_ANCHOR_HOST, MAT_BUTTON_HOST, MatAnchorBase, MatButtonBase} from './button-base';\n\n/**\n * Material Design icon button component. This type of button displays a single interactive icon for\n * users to perform an action.\n * See https://material.io/develop/web/components/buttons/icon-buttons/\n */\n@Component({\n  selector: `button[mat-icon-button]`,\n  templateUrl: 'icon-button.html',\n  styleUrls: ['icon-button.css', 'button-high-contrast.css'],\n  host: MAT_BUTTON_HOST,\n  exportAs: 'matButton',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: true,\n})\nexport class MatIcon<PERSON>utton extends MatButtonBase {\n  constructor(\n    elementRef: ElementRef,\n    platform: Platform,\n    ngZone: NgZone,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) animationMode?: string,\n  ) {\n    super(elementRef, platform, ngZone, animationMode);\n\n    this._rippleLoader.configureRipple(this._elementRef.nativeElement, {centered: true});\n  }\n}\n\n/**\n * Material Design icon button component for anchor elements. This button displays a single\n * interaction icon that allows users to navigate across different routes or pages.\n * See https://material.io/develop/web/components/buttons/icon-buttons/\n */\n@Component({\n  selector: `a[mat-icon-button]`,\n  templateUrl: 'icon-button.html',\n  styleUrls: ['icon-button.css', 'button-high-contrast.css'],\n  host: MAT_ANCHOR_HOST,\n  exportAs: 'matButton, matAnchor',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: true,\n})\nexport class MatIconAnchor extends MatAnchorBase {\n  constructor(\n    elementRef: ElementRef,\n    platform: Platform,\n    ngZone: NgZone,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) animationMode?: string,\n  ) {\n    super(elementRef, platform, ngZone, animationMode);\n  }\n}\n", "<span class=\"mat-mdc-button-persistent-ripple mdc-icon-button__ripple\"></span>\n\n<ng-content></ng-content>\n\n<!--\n  The indicator can't be directly on the button, because MDC uses ::before for high contrast\n  indication and it can't be on the ripple, because it has a border radius and overflow: hidden.\n-->\n<span class=\"mat-mdc-focus-indicator\"></span>\n\n<span class=\"mat-mdc-button-touch-target\"></span>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule, MatRippleModule} from '@angular/material/core';\nimport {<PERSON><PERSON><PERSON><PERSON>, MatButton} from './button';\nimport {MatFabAnchor, MatFabButton, MatMiniFabAnchor, MatMiniFabButton} from './fab';\nimport {MatIconAnchor, MatIconButton} from './icon-button';\n\n@NgModule({\n  imports: [\n    MatCommonModule,\n    MatRippleModule,\n    MatAnchor,\n    MatButton,\n    MatIconAnchor,\n    MatMiniFabAnchor,\n    MatMiniFabButton,\n    MatIconButton,\n    MatFabAnchor,\n    MatFabButton,\n  ],\n  exports: [\n    MatAnchor,\n    MatButton,\n    MatIconAnchor,\n    MatIconButton,\n    MatMiniFabAnchor,\n    MatMiniFabButton,\n    MatFabAnchor,\n    MatFabButton,\n    MatCommonModule,\n  ],\n})\nexport class MatButtonModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;;AA+BA;MACa,iBAAiB,GAAG,IAAI,cAAc,CAAkB,mBAAmB,EAAE;AAE1F;AACO,MAAM,eAAe,GAAG;AAC7B,IAAA,iBAAiB,EAAE,yBAAyB;AAC5C,IAAA,sBAAsB,EAAE,oBAAoB;AAC5C,IAAA,iCAAiC,EAAE,UAAU;AAC7C,IAAA,6CAA6C,EAAE,qBAAqB;AACpE,IAAA,iCAAiC,EAAE,qCAAqC;;;;AAIxE,IAAA,sBAAsB,EAAE,QAAQ;;;AAGhC,IAAA,6BAA6B,EAAE,MAAM;AACrC,IAAA,SAAS,EAAE,6BAA6B;CACzC,CAAC;AAEF;AACA,MAAM,4BAA4B,GAAgD;AAChF,IAAA;AACE,QAAA,SAAS,EAAE,YAAY;AACvB,QAAA,UAAU,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC;AAC7C,KAAA;AACD,IAAA;AACE,QAAA,SAAS,EAAE,iBAAiB;AAC5B,QAAA,UAAU,EAAE,CAAC,YAAY,EAAE,wBAAwB,EAAE,2BAA2B,CAAC;AAClF,KAAA;AACD,IAAA;AACE,QAAA,SAAS,EAAE,mBAAmB;AAC9B,QAAA,UAAU,EAAE,CAAC,YAAY,EAAE,oBAAoB,EAAE,uBAAuB,CAAC;AAC1E,KAAA;AACD,IAAA;AACE,QAAA,SAAS,EAAE,oBAAoB;AAC/B,QAAA,UAAU,EAAE,CAAC,YAAY,EAAE,sBAAsB,EAAE,yBAAyB,CAAC;AAC9E,KAAA;AACD,IAAA;AACE,QAAA,SAAS,EAAE,SAAS;AACpB,QAAA,UAAU,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC;AACvC,KAAA;AACD,IAAA;AACE,QAAA,SAAS,EAAE,cAAc;AACzB,QAAA,UAAU,EAAE,CAAC,SAAS,EAAE,eAAe,EAAE,kBAAkB,CAAC;AAC7D,KAAA;AACD,IAAA;AACE,QAAA,SAAS,EAAE,iBAAiB;AAC5B,QAAA,UAAU,EAAE,CAAC,iBAAiB,EAAE,qBAAqB,CAAC;AACvD,KAAA;CACF,CAAC;AAEF;MAEa,aAAa,CAAA;AAYxB;;;;AAIG;AACH,IAAA,IAAI,MAAM,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,aAAa,EAAE,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAE,CAAC;KACvE;IACD,IAAI,MAAM,CAAC,CAAY,EAAA;AACrB,QAAA,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;KACrE;;AAMD,IAAA,IACI,aAAa,GAAA;QACf,OAAO,IAAI,CAAC,cAAc,CAAC;KAC5B;IACD,IAAI,aAAa,CAAC,KAAU,EAAA;AAC1B,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,qBAAqB,EAAE,CAAC;KAC9B;;AAID,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,KAAU,EAAA;AACrB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,qBAAqB,EAAE,CAAC;KAC9B;AAqBD,IAAA,WAAA,CACS,WAAuB,EACvB,SAAmB,EACnB,OAAe,EACf,cAAuB,EAAA;QAHvB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAY;QACvB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QACnB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QACf,IAAc,CAAA,cAAA,GAAd,cAAc,CAAS;AAtEf,QAAA,IAAA,CAAA,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC;AAEtD;;;AAGG;AACH,QAAA,IAAA,CAAA,aAAa,GAAoB,MAAM,CAAC,eAAe,CAAC,CAAC;;QAGzD,IAAM,CAAA,MAAA,GAAG,KAAK,CAAC;QA0BP,IAAc,CAAA,cAAA,GAAY,KAAK,CAAC;QAWhC,IAAS,CAAA,SAAA,GAAY,KAAK,CAAC;AA0BjC,QAAA,MAAM,MAAM,GAAG,MAAM,CAAC,iBAAiB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;AAC3D,QAAA,MAAM,OAAO,GAAG,WAAW,CAAC,aAAa,CAAC;AAC1C,QAAA,MAAM,SAAS,GAAI,OAAuB,CAAC,SAAS,CAAC;QAErD,IAAI,CAAC,mBAAmB,GAAG,MAAM,EAAE,mBAAmB,IAAI,KAAK,CAAC;AAChE,QAAA,IAAI,CAAC,aAAa,EAAE,eAAe,CAAC,OAAO,EAAE,EAAC,SAAS,EAAE,uBAAuB,EAAC,CAAC,CAAC;;;QAInF,KAAK,MAAM,EAAC,SAAS,EAAE,UAAU,EAAC,IAAI,4BAA4B,EAAE;AAClE,YAAA,IAAI,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE;AACnC,gBAAA,SAAS,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC;aAC9B;SACF;KACF;IAED,eAAe,GAAA;QACb,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;KACpD;IAED,WAAW,GAAA;QACT,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACpD,IAAI,CAAC,aAAa,EAAE,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;KACnE;;AAGD,IAAA,KAAK,CAAC,MAAA,GAAsB,SAAS,EAAE,OAAsB,EAAA;QAC3D,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;SAC9E;aAAM;YACL,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;SAC/C;KACF;IAES,gBAAgB,GAAA;AACxB,QAAA,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,EAAE;YAC7B,OAAO,IAAI,CAAC,YAAY,CAAC;SAC1B;AAED,QAAA,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,mBAAmB,GAAG,IAAI,GAAG,IAAI,CAAC;KAChE;IAES,qBAAqB,GAAA;AAC7B,QAAA,OAAO,IAAI,CAAC,mBAAmB,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC;KACjE;IAEO,qBAAqB,GAAA;AAC3B,QAAA,IAAI,CAAC,aAAa,EAAE,WAAW,CAC7B,IAAI,CAAC,WAAW,CAAC,aAAa,EAC9B,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ,CACpC,CAAC;KACH;8GA5HU,aAAa,EAAA,IAAA,EAAA,SAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAb,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,8EA4BL,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAWhB,gBAAgB,CAWhB,EAAA,YAAA,EAAA,CAAA,eAAA,EAAA,cAAA,EAAA,gBAAgB,uEAchB,gBAAgB,CAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAhExB,aAAa,EAAA,UAAA,EAAA,CAAA;kBADzB,SAAS;gJA0BC,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAIF,aAAa,EAAA,CAAA;sBADhB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAYhC,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAYpC,YAAY,EAAA,CAAA;sBADX,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,SAAS,EAAE,gBAAgB,EAAE,KAAK,EAAE,eAAe,EAAC,CAAA;gBAe5D,mBAAmB,EAAA,CAAA;sBADlB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;;AA+DtC;AACO,MAAM,eAAe,GAAG;AAC7B,IAAA,iBAAiB,EAAE,yBAAyB;AAC5C,IAAA,iCAAiC,EAAE,UAAU;AAC7C,IAAA,6CAA6C,EAAE,qBAAqB;AACpE,IAAA,iCAAiC,EAAE,qCAAqC;;;;AAKxE,IAAA,iBAAiB,EAAE,kDAAkD;AACrE,IAAA,sBAAsB,EAAE,yBAAyB;;;;AAIjD,IAAA,sBAAsB,EAAE,QAAQ;;;AAGhC,IAAA,6BAA6B,EAAE,MAAM;AACrC,IAAA,SAAS,EAAE,6BAA6B;CACzC,CAAC;AAEF;;AAEG;AAEG,MAAO,aAAc,SAAQ,aAAa,CAAA;AAQ9C,IAAA,WAAA,CAAY,UAAsB,EAAE,QAAkB,EAAE,MAAc,EAAE,aAAsB,EAAA;QAC5F,KAAK,CAAC,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;AAcrD,QAAA,IAAA,CAAA,mBAAmB,GAAG,CAAC,KAAY,KAAU;;AAE3C,YAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,KAAK,CAAC,wBAAwB,EAAE,CAAC;aAClC;AACH,SAAC,CAAC;KAnBD;IAED,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;AACrF,SAAC,CAAC,CAAC;KACJ;IAEQ,WAAW,GAAA;QAClB,KAAK,CAAC,WAAW,EAAE,CAAC;AACpB,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;KACvF;IAUkB,gBAAgB,GAAA;AACjC,QAAA,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC;KACtE;8GAjCU,aAAa,EAAA,IAAA,EAAA,SAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAb,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,EAEX,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,CAAC,KAAc,KAAI;AAC5B,oBAAA,OAAO,KAAK,IAAI,IAAI,GAAG,SAAS,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;iBAC3D,CAAA,EAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAJQ,aAAa,EAAA,UAAA,EAAA,CAAA;kBADzB,SAAS;gJAOR,QAAQ,EAAA,CAAA;sBALP,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA;AACL,wBAAA,SAAS,EAAE,CAAC,KAAc,KAAI;AAC5B,4BAAA,OAAO,KAAK,IAAI,IAAI,GAAG,SAAS,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;yBAC3D;AACF,qBAAA,CAAA;;;AC7NH;;;;;;;;AAQG;AAcG,MAAO,SAAU,SAAQ,aAAa,CAAA;AAC1C,IAAA,WAAA,CACE,UAAsB,EACtB,QAAkB,EAClB,MAAc,EAC6B,aAAsB,EAAA;QAEjE,KAAK,CAAC,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;KACpD;AARU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,SAAS,0FAKE,qBAAqB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AALhC,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAS,2nBC5CtB,kzBAoBA,EAAA,MAAA,EAAA,CAAA,ikmBAAA,EAAA,kXAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FDwBa,SAAS,EAAA,UAAA,EAAA,CAAA;kBAbrB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA;;;AAGT,EAAA,CAAA,EAAA,IAAA,EAGK,eAAe,EAAA,QAAA,EACX,WAAW,EAAA,aAAA,EACN,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,MAAM,EAAA,UAAA,EACnC,IAAI,EAAA,QAAA,EAAA,kzBAAA,EAAA,MAAA,EAAA,CAAA,ikmBAAA,EAAA,kXAAA,CAAA,EAAA,CAAA;;0BAOb,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;;AAM7C;;;;;;;;;AASG;AAWG,MAAO,SAAU,SAAQ,aAAa,CAAA;AAC1C,IAAA,WAAA,CACE,UAAsB,EACtB,QAAkB,EAClB,MAAc,EAC6B,aAAsB,EAAA;QAEjE,KAAK,CAAC,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;KACpD;AARU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,SAAS,0FAKE,qBAAqB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AALhC,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAS,+qBC3EtB,kzBAoBA,EAAA,MAAA,EAAA,CAAA,ikmBAAA,EAAA,kXAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FDuDa,SAAS,EAAA,UAAA,EAAA,CAAA;kBAVrB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,8EAAA,CAAgF,EAChF,QAAA,EAAA,sBAAsB,EAC1B,IAAA,EAAA,eAAe,EAGN,aAAA,EAAA,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,cACnC,IAAI,EAAA,QAAA,EAAA,kzBAAA,EAAA,MAAA,EAAA,CAAA,ikmBAAA,EAAA,kXAAA,CAAA,EAAA,CAAA;;0BAOb,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;;;AEhD7C;MACa,uBAAuB,GAAG,IAAI,cAAc,CACvD,6BAA6B,EAC7B;AACE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,+BAA+B;AACzC,CAAA,EACD;AAEF;SACgB,+BAA+B,GAAA;IAC7C,OAAO;;AAEL,QAAA,KAAK,EAAE,QAAQ;KAChB,CAAC;AACJ,CAAC;AAED;AACA,MAAM,QAAQ,GAAG,+BAA+B,EAAE,CAAC;AAEnD;;;;;;AAMG;AAeG,MAAO,YAAa,SAAQ,aAAa,CAAA;IAK7C,WACE,CAAA,UAAsB,EACtB,QAAkB,EAClB,MAAc,EAC6B,aAAsB,EACZ,QAA+B,EAAA;QAEpF,KAAK,CAAC,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;QAFE,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAuB;QAT7E,IAAM,CAAA,MAAA,GAAG,IAAI,CAAC;QAYrB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC;AAC1C,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAS,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC;KACrD;8GAfU,YAAY,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EASD,qBAAqB,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EACrB,uBAAuB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAVlC,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAGJ,gBAAgB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,eAAA,EAAA,yBAAA,EAAA,oBAAA,EAAA,oBAAA,EAAA,+BAAA,EAAA,UAAA,EAAA,2CAAA,EAAA,qBAAA,EAAA,+BAAA,EAAA,uCAAA,EAAA,oBAAA,EAAA,QAAA,EAAA,2BAAA,EAAA,MAAA,EAAA,OAAA,EAAA,iCAAA,EAAA,yBAAA,EAAA,UAAA,EAAA,4BAAA,EAAA,UAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ED5ErC,kzBAoBA,EAAA,MAAA,EAAA,CAAA,+tYAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FCqDa,YAAY,EAAA,UAAA,EAAA,CAAA;kBAdxB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,eAAA,CAAiB,EAGrB,IAAA,EAAA;AACJ,wBAAA,GAAG,eAAe;AAClB,wBAAA,2BAA2B,EAAE,UAAU;AACvC,wBAAA,8BAA8B,EAAE,UAAU;qBAC3C,EACS,QAAA,EAAA,WAAW,EACN,aAAA,EAAA,iBAAiB,CAAC,IAAI,mBACpB,uBAAuB,CAAC,MAAM,EAAA,UAAA,EACnC,IAAI,EAAA,QAAA,EAAA,kzBAAA,EAAA,MAAA,EAAA,CAAA,+tYAAA,CAAA,EAAA,CAAA;;0BAWb,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;;0BACxC,QAAQ;;0BAAI,MAAM;2BAAC,uBAAuB,CAAA;yCAPP,QAAQ,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;;AAetC;;;;AAIG;AAWG,MAAO,gBAAiB,SAAQ,aAAa,CAAA;IAGjD,WACE,CAAA,UAAsB,EACtB,QAAkB,EAClB,MAAc,EAC6B,aAAsB,EACZ,QAA+B,EAAA;QAEpF,KAAK,CAAC,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;QAFE,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAuB;QAP7E,IAAM,CAAA,MAAA,GAAG,IAAI,CAAC;QAUrB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC;AAC1C,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAS,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC;KACrD;8GAbU,gBAAgB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAOL,qBAAqB,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EACrB,uBAAuB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AARlC,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,gBAAgB,8hBD1G7B,kzBAoBA,EAAA,MAAA,EAAA,CAAA,+tYAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FCsFa,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAV5B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,oBAAA,CAAsB,EAG1B,IAAA,EAAA,eAAe,EACX,QAAA,EAAA,WAAW,EACN,aAAA,EAAA,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,cACnC,IAAI,EAAA,QAAA,EAAA,kzBAAA,EAAA,MAAA,EAAA,CAAA,+tYAAA,CAAA,EAAA,CAAA;;0BASb,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;;0BACxC,QAAQ;;0BAAI,MAAM;2BAAC,uBAAuB,CAAA;;AAQ/C;;;;;;AAMG;AAeG,MAAO,YAAa,SAAQ,SAAS,CAAA;IAKzC,WACE,CAAA,UAAsB,EACtB,QAAkB,EAClB,MAAc,EAC6B,aAAsB,EACZ,QAA+B,EAAA;QAEpF,KAAK,CAAC,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;QAFE,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAuB;QAT7E,IAAM,CAAA,MAAA,GAAG,IAAI,CAAC;QAYrB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC;AAC1C,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAS,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC;KACrD;8GAfU,YAAY,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EASD,qBAAqB,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EACrB,uBAAuB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAVlC,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAGJ,gBAAgB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,eAAA,EAAA,yBAAA,EAAA,+BAAA,EAAA,UAAA,EAAA,2CAAA,EAAA,qBAAA,EAAA,+BAAA,EAAA,uCAAA,EAAA,eAAA,EAAA,kDAAA,EAAA,oBAAA,EAAA,yBAAA,EAAA,oBAAA,EAAA,QAAA,EAAA,2BAAA,EAAA,MAAA,EAAA,OAAA,EAAA,iCAAA,EAAA,yBAAA,EAAA,UAAA,EAAA,4BAAA,EAAA,UAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA,WAAA,EAAA,WAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EDlJrC,kzBAoBA,EAAA,MAAA,EAAA,CAAA,+tYAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FC2Ha,YAAY,EAAA,UAAA,EAAA,CAAA;kBAdxB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,CAAY,EAGhB,IAAA,EAAA;AACJ,wBAAA,GAAG,eAAe;AAClB,wBAAA,2BAA2B,EAAE,UAAU;AACvC,wBAAA,8BAA8B,EAAE,UAAU;qBAC3C,EACS,QAAA,EAAA,sBAAsB,EACjB,aAAA,EAAA,iBAAiB,CAAC,IAAI,mBACpB,uBAAuB,CAAC,MAAM,EAAA,UAAA,EACnC,IAAI,EAAA,QAAA,EAAA,kzBAAA,EAAA,MAAA,EAAA,CAAA,+tYAAA,CAAA,EAAA,CAAA;;0BAWb,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;;0BACxC,QAAQ;;0BAAI,MAAM;2BAAC,uBAAuB,CAAA;yCAPP,QAAQ,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;;AAetC;;;;AAIG;AAWG,MAAO,gBAAiB,SAAQ,SAAS,CAAA;IAG7C,WACE,CAAA,UAAsB,EACtB,QAAkB,EAClB,MAAc,EAC6B,aAAsB,EACZ,QAA+B,EAAA;QAEpF,KAAK,CAAC,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;QAFE,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAuB;QAP7E,IAAM,CAAA,MAAA,GAAG,IAAI,CAAC;QAUrB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC;AAC1C,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAS,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC;KACrD;8GAbU,gBAAgB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAOL,qBAAqB,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EACrB,uBAAuB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AARlC,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,gBAAgB,gnBDhL7B,kzBAoBA,EAAA,MAAA,EAAA,CAAA,+tYAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FC4Ja,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAV5B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,eAAA,CAAiB,EAGrB,IAAA,EAAA,eAAe,EACX,QAAA,EAAA,sBAAsB,EACjB,aAAA,EAAA,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,cACnC,IAAI,EAAA,QAAA,EAAA,kzBAAA,EAAA,MAAA,EAAA,CAAA,+tYAAA,CAAA,EAAA,CAAA;;0BASb,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;;0BACxC,QAAQ;;0BAAI,MAAM;2BAAC,uBAAuB,CAAA;;;ACnK/C;;;;AAIG;AAWG,MAAO,aAAc,SAAQ,aAAa,CAAA;AAC9C,IAAA,WAAA,CACE,UAAsB,EACtB,QAAkB,EAClB,MAAc,EAC6B,aAAsB,EAAA;QAEjE,KAAK,CAAC,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;AAEnD,QAAA,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;KACtF;AAVU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,0FAKF,qBAAqB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AALhC,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,iiBCpC1B,saAWA,EAAA,MAAA,EAAA,CAAA,y7JAAA,EAAA,kXAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FDyBa,aAAa,EAAA,UAAA,EAAA,CAAA;kBAVzB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,uBAAA,CAAyB,EAG7B,IAAA,EAAA,eAAe,EACX,QAAA,EAAA,WAAW,EACN,aAAA,EAAA,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,cACnC,IAAI,EAAA,QAAA,EAAA,saAAA,EAAA,MAAA,EAAA,CAAA,y7JAAA,EAAA,kXAAA,CAAA,EAAA,CAAA;;0BAOb,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;;AAQ7C;;;;AAIG;AAWG,MAAO,aAAc,SAAQ,aAAa,CAAA;AAC9C,IAAA,WAAA,CACE,UAAsB,EACtB,QAAkB,EAClB,MAAc,EAC6B,aAAsB,EAAA;QAEjE,KAAK,CAAC,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;KACpD;AARU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,0FAKF,qBAAqB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AALhC,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,mnBChE1B,saAWA,EAAA,MAAA,EAAA,CAAA,y7JAAA,EAAA,kXAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FDqDa,aAAa,EAAA,UAAA,EAAA,CAAA;kBAVzB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,kBAAA,CAAoB,EAGxB,IAAA,EAAA,eAAe,EACX,QAAA,EAAA,sBAAsB,EACjB,aAAA,EAAA,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,cACnC,IAAI,EAAA,QAAA,EAAA,saAAA,EAAA,MAAA,EAAA,CAAA,y7JAAA,EAAA,kXAAA,CAAA,EAAA,CAAA;;0BAOb,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;;;ME9BhC,eAAe,CAAA;8GAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAf,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YAvBxB,eAAe;YACf,eAAe;YACf,SAAS;YACT,SAAS;YACT,aAAa;YACb,gBAAgB;YAChB,gBAAgB;YAChB,aAAa;YACb,YAAY;AACZ,YAAA,YAAY,aAGZ,SAAS;YACT,SAAS;YACT,aAAa;YACb,aAAa;YACb,gBAAgB;YAChB,gBAAgB;YAChB,YAAY;YACZ,YAAY;YACZ,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;AAGN,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YAvBxB,eAAe;AACf,YAAA,eAAe,EAmBf,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAGN,eAAe,EAAA,UAAA,EAAA,CAAA;kBAzB3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE;wBACP,eAAe;wBACf,eAAe;wBACf,SAAS;wBACT,SAAS;wBACT,aAAa;wBACb,gBAAgB;wBAChB,gBAAgB;wBAChB,aAAa;wBACb,YAAY;wBACZ,YAAY;AACb,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,SAAS;wBACT,SAAS;wBACT,aAAa;wBACb,aAAa;wBACb,gBAAgB;wBAChB,gBAAgB;wBAChB,YAAY;wBACZ,YAAY;wBACZ,eAAe;AAChB,qBAAA;AACF,iBAAA,CAAA;;;ACtCD;;AAEG;;;;"}