@use '@material/slider/slider-theme' as mdc-slider-theme;
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/theming/validation';
@use '../core/typography/typography';
@use '../core/style/sass-utils';
@use '../core/tokens/token-utils';
@use '../core/tokens/m2/mat/slider' as tokens-mat-slider;
@use '../core/tokens/m2/mdc/slider' as tokens-mdc-slider;

/// Outputs base theme styles (styles not dependent on the color, typography, or density settings)
/// for the mat-slider.
/// @param {Map} $theme The theme to generate base styles for.
@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, base));
  }
  @else {
    // Add default values for tokens not related to color, typography, or density.
    @include sass-utils.current-selector-or-root() {
      $mat-tokens: tokens-mat-slider.get-unthemable-tokens();
      @include token-utils.create-token-values(tokens-mat-slider.$prefix, $mat-tokens);
      @include mdc-slider-theme.theme(tokens-mdc-slider.get-unthemable-tokens());
    }
  }
}

/// Outputs color theme styles for the mat-slider.
/// @param {Map} $theme The theme to generate color styles for.
/// @param {ArgList} Additional optional arguments (only supported for M3 themes):
///   $color-variant: The color variant to use for the slider: primary, secondary, tertiary,
///     or error (If not specified, default primary color will be used).
@mixin color($theme, $options...) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, color), $options...);
  }
  @else {
    $is-dark: inspection.get-theme-type($theme) == dark;

    $mdc-color-tokens: token-utils.resolve-elevation(
        tokens-mdc-slider.get-color-tokens($theme),
        handle-elevation,
        handle-shadow-color
    );

    $mat-slider-color-tokens: tokens-mat-slider.get-color-tokens($theme);

  // Add values for MDC slider tokens.
  @include sass-utils.current-selector-or-root() {
    @include mdc-slider-theme.theme($mdc-color-tokens);
    @include token-utils.create-token-values(
      tokens-mat-slider.$prefix,
      $mat-slider-color-tokens
    );

    .mat-accent {
      @include token-utils.create-token-values(
        tokens-mat-slider.$prefix,
        tokens-mat-slider.private-get-color-palette-color-tokens($theme, accent),
      );
      @include mdc-slider-theme.theme(
        tokens-mdc-slider.private-get-color-palette-color-tokens($theme, accent));
    }

      .mat-warn {
        @include token-utils.create-token-values(
          tokens-mat-slider.$prefix,
          tokens-mat-slider.private-get-color-palette-color-tokens($theme, warn),
        );
        @include mdc-slider-theme.theme(
            tokens-mdc-slider.private-get-color-palette-color-tokens($theme, warn));
      }
    }
  }
}


/// Outputs typography theme styles for the mat-slider.
/// @param {Map} $theme The theme to generate typography styles for.
@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, typography));
  }
  @else {
    // Add values for MDC slider tokens.
    @include sass-utils.current-selector-or-root() {
      @include mdc-slider-theme.theme(tokens-mdc-slider.get-typography-tokens($theme));
    }
  }
}


/// Outputs density theme styles for the mat-slider.
/// @param {Map} $theme The theme to generate density styles for.
@mixin density($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, density));
  }
  @else {
    // Add values for MDC slider tokens.
    @include sass-utils.current-selector-or-root() {
      @include mdc-slider-theme.theme(tokens-mdc-slider.get-density-tokens($theme));
    }
  }
}

/// Outputs all (base, color, typography, and density) theme styles for the mat-option.
/// @param {Map} $theme The theme to generate styles for.
/// @param {ArgList} Additional optional arguments (only supported for M3 themes):
///   $color-variant: The color variant to use for the slider: primary, secondary, tertiary,
///     or error (If not specified, default primary color will be used).
@mixin theme($theme, $options...) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-slider') {
    @if inspection.get-theme-version($theme) == 1 {
      @include _theme-from-tokens(inspection.get-theme-tokens($theme), $options...);
    }
    @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}

@mixin _theme-from-tokens($tokens, $options...) {
  @include validation.selector-defined(
      'Calls to Angular Material theme mixins with an M3 theme must be wrapped in a selector');
  $mdc-slider-tokens: token-utils.get-tokens-for($tokens, tokens-mdc-slider.$prefix, $options...);
  $mat-slider-tokens: token-utils.get-tokens-for($tokens, tokens-mat-slider.$prefix, $options...);
  @include mdc-slider-theme.theme($mdc-slider-tokens);
  @include token-utils.create-token-values(tokens-mat-slider.$prefix, $mat-slider-tokens);
}
