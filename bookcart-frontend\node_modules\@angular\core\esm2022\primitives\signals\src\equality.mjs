/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * The default equality function used for `signal` and `computed`, which uses referential equality.
 */
export function defaultEquals(a, b) {
    return Object.is(a, b);
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZXF1YWxpdHkuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9jb3JlL3ByaW1pdGl2ZXMvc2lnbmFscy9zcmMvZXF1YWxpdHkudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7OztHQU1HO0FBT0g7O0dBRUc7QUFDSCxNQUFNLFVBQVUsYUFBYSxDQUFJLENBQUksRUFBRSxDQUFJO0lBQ3pDLE9BQU8sTUFBTSxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7QUFDekIsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuXG4vKipcbiAqIEEgY29tcGFyaXNvbiBmdW5jdGlvbiB3aGljaCBjYW4gZGV0ZXJtaW5lIGlmIHR3byB2YWx1ZXMgYXJlIGVxdWFsLlxuICovXG5leHBvcnQgdHlwZSBWYWx1ZUVxdWFsaXR5Rm48VD4gPSAoYTogVCwgYjogVCkgPT4gYm9vbGVhbjtcblxuLyoqXG4gKiBUaGUgZGVmYXVsdCBlcXVhbGl0eSBmdW5jdGlvbiB1c2VkIGZvciBgc2lnbmFsYCBhbmQgYGNvbXB1dGVkYCwgd2hpY2ggdXNlcyByZWZlcmVudGlhbCBlcXVhbGl0eS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGRlZmF1bHRFcXVhbHM8VD4oYTogVCwgYjogVCkge1xuICByZXR1cm4gT2JqZWN0LmlzKGEsIGIpO1xufVxuIl19