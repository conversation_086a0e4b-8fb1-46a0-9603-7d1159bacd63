/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { ComponentHarness, parallel, } from '@angular/cdk/testing';
import { MatDividerHarness } from '@angular/material/divider/testing';
import { MatSubheaderHarness } from './list-item-harness-base';
/**
 * Shared behavior among the harnesses for the various `MatList` flavors.
 * @template T A constructor type for a list item harness type used by this list harness.
 * @template C The list item harness type that `T` constructs.
 * @template F The filter type used filter list item harness of type `C`.
 * @docs-private
 */
export class MatListHarnessBase extends ComponentHarness {
    /**
     * Gets a list of harnesses representing the items in this list.
     * @param filters Optional filters used to narrow which harnesses are included
     * @return The list of items matching the given filters.
     */
    async getItems(filters) {
        return this.locatorForAll(this._itemHarness.with(filters))();
    }
    /**
     * Gets a list of `ListSection` representing the list items grouped by subheaders. If the list has
     * no subheaders it is represented as a single `ListSection` with an undefined `heading` property.
     * @param filters Optional filters used to narrow which list item harnesses are included
     * @return The list of items matching the given filters, grouped into sections by subheader.
     */
    async getItemsGroupedBySubheader(filters) {
        const listSections = [];
        let currentSection = { items: [] };
        const itemsAndSubheaders = await this.getItemsWithSubheadersAndDividers({
            item: filters,
            divider: false,
        });
        for (const itemOrSubheader of itemsAndSubheaders) {
            if (itemOrSubheader instanceof MatSubheaderHarness) {
                if (currentSection.heading !== undefined || currentSection.items.length) {
                    listSections.push(currentSection);
                }
                currentSection = { heading: itemOrSubheader.getText(), items: [] };
            }
            else {
                currentSection.items.push(itemOrSubheader);
            }
        }
        if (currentSection.heading !== undefined ||
            currentSection.items.length ||
            !listSections.length) {
            listSections.push(currentSection);
        }
        // Concurrently wait for all sections to resolve their heading if present.
        return parallel(() => listSections.map(async (s) => ({ items: s.items, heading: await s.heading })));
    }
    /**
     * Gets a list of sub-lists representing the list items grouped by dividers. If the list has no
     * dividers it is represented as a list with a single sub-list.
     * @param filters Optional filters used to narrow which list item harnesses are included
     * @return The list of items matching the given filters, grouped into sub-lists by divider.
     */
    async getItemsGroupedByDividers(filters) {
        const listSections = [[]];
        const itemsAndDividers = await this.getItemsWithSubheadersAndDividers({
            item: filters,
            subheader: false,
        });
        for (const itemOrDivider of itemsAndDividers) {
            if (itemOrDivider instanceof MatDividerHarness) {
                listSections.push([]);
            }
            else {
                listSections[listSections.length - 1].push(itemOrDivider);
            }
        }
        return listSections;
    }
    async getItemsWithSubheadersAndDividers(filters = {}) {
        const query = [];
        if (filters.item !== false) {
            query.push(this._itemHarness.with(filters.item || {}));
        }
        if (filters.subheader !== false) {
            query.push(MatSubheaderHarness.with(filters.subheader));
        }
        if (filters.divider !== false) {
            query.push(MatDividerHarness.with(filters.divider));
        }
        return this.locatorForAll(...query)();
    }
}
//# sourceMappingURL=data:application/json;base64,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