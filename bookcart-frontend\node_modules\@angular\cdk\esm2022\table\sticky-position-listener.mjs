/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { InjectionToken } from '@angular/core';
/** The injection token used to specify the StickyPositioningListener. */
export const STICKY_POSITIONING_LISTENER = new InjectionToken('CDK_SPL');
//# sourceMappingURL=data:application/json;base64,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