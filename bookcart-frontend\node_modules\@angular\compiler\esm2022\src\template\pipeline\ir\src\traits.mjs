/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * Marker symbol for `ConsumesSlotOpTrait`.
 */
export const ConsumesSlot = Symbol('ConsumesSlot');
/**
 * Marker symbol for `DependsOnSlotContextOpTrait`.
 */
export const DependsOnSlotContext = Symbol('DependsOnSlotContext');
/**
 * Marker symbol for `ConsumesVars` trait.
 */
export const ConsumesVarsTrait = Symbol('ConsumesVars');
/**
 * Marker symbol for `UsesVarOffset` trait.
 */
export const UsesVarOffset = Symbol('UsesVarOffset');
/**
 * Default values for most `ConsumesSlotOpTrait` fields (used with the spread operator to initialize
 * implementors of the trait).
 */
export const TRAIT_CONSUMES_SLOT = {
    [ConsumesSlot]: true,
    numSlotsUsed: 1,
};
/**
 * Default values for most `DependsOnSlotContextOpTrait` fields (used with the spread operator to
 * initialize implementors of the trait).
 */
export const TRAIT_DEPENDS_ON_SLOT_CONTEXT = {
    [DependsOnSlotContext]: true,
};
/**
 * Default values for `UsesVars` fields (used with the spread operator to initialize
 * implementors of the trait).
 */
export const TRAIT_CONSUMES_VARS = {
    [ConsumesVarsTrait]: true,
};
/**
 * Test whether an operation implements `ConsumesSlotOpTrait`.
 */
export function hasConsumesSlotTrait(op) {
    return op[ConsumesSlot] === true;
}
/**
 * Test whether an operation implements `DependsOnSlotContextOpTrait`.
 */
export function hasDependsOnSlotContextTrait(op) {
    return op[DependsOnSlotContext] === true;
}
export function hasConsumesVarsTrait(value) {
    return value[ConsumesVarsTrait] === true;
}
/**
 * Test whether an expression implements `UsesVarOffsetTrait`.
 */
export function hasUsesVarOffsetTrait(expr) {
    return expr[UsesVarOffset] === true;
}
//# sourceMappingURL=data:application/json;base64,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