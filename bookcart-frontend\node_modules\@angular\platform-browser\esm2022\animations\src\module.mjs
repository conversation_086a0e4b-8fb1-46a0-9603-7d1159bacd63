/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { NgModule, ɵperformanceMarkFeature as performanceMarkFeature } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BROWSER_ANIMATIONS_PROVIDERS, BROWSER_NOOP_ANIMATIONS_PROVIDERS } from './providers';
import * as i0 from "@angular/core";
/**
 * Exports `BrowserModule` with additional [dependency-injection providers](guide/glossary#provider)
 * for use with animations. See [Animations](guide/animations).
 * @publicApi
 */
export class BrowserAnimationsModule {
    /**
     * Configures the module based on the specified object.
     *
     * @param config Object used to configure the behavior of the `BrowserAnimationsModule`.
     * @see {@link BrowserAnimationsModuleConfig}
     *
     * @usageNotes
     * When registering the `BrowserAnimationsModule`, you can use the `withConfig`
     * function as follows:
     * ```
     * @NgModule({
     *   imports: [BrowserAnimationsModule.withConfig(config)]
     * })
     * class MyNgModule {}
     * ```
     */
    static withConfig(config) {
        return {
            ngModule: BrowserAnimationsModule,
            providers: config.disableAnimations ? BROWSER_NOOP_ANIMATIONS_PROVIDERS :
                BROWSER_ANIMATIONS_PROVIDERS
        };
    }
    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "17.3.12", ngImport: i0, type: BrowserAnimationsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }
    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: "14.0.0", version: "17.3.12", ngImport: i0, type: BrowserAnimationsModule, exports: [BrowserModule] }); }
    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: "12.0.0", version: "17.3.12", ngImport: i0, type: BrowserAnimationsModule, providers: BROWSER_ANIMATIONS_PROVIDERS, imports: [BrowserModule] }); }
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "17.3.12", ngImport: i0, type: BrowserAnimationsModule, decorators: [{
            type: NgModule,
            args: [{
                    exports: [BrowserModule],
                    providers: BROWSER_ANIMATIONS_PROVIDERS,
                }]
        }] });
/**
 * Returns the set of [dependency-injection providers](guide/glossary#provider)
 * to enable animations in an application. See [animations guide](guide/animations)
 * to learn more about animations in Angular.
 *
 * @usageNotes
 *
 * The function is useful when you want to enable animations in an application
 * bootstrapped using the `bootstrapApplication` function. In this scenario there
 * is no need to import the `BrowserAnimationsModule` NgModule at all, just add
 * providers returned by this function to the `providers` list as show below.
 *
 * ```typescript
 * bootstrapApplication(RootComponent, {
 *   providers: [
 *     provideAnimations()
 *   ]
 * });
 * ```
 *
 * @publicApi
 */
export function provideAnimations() {
    performanceMarkFeature('NgEagerAnimations');
    // Return a copy to prevent changes to the original array in case any in-place
    // alterations are performed to the `provideAnimations` call results in app code.
    return [...BROWSER_ANIMATIONS_PROVIDERS];
}
/**
 * A null player that must be imported to allow disabling of animations.
 * @publicApi
 */
export class NoopAnimationsModule {
    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "17.3.12", ngImport: i0, type: NoopAnimationsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }
    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: "14.0.0", version: "17.3.12", ngImport: i0, type: NoopAnimationsModule, exports: [BrowserModule] }); }
    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: "12.0.0", version: "17.3.12", ngImport: i0, type: NoopAnimationsModule, providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS, imports: [BrowserModule] }); }
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "17.3.12", ngImport: i0, type: NoopAnimationsModule, decorators: [{
            type: NgModule,
            args: [{
                    exports: [BrowserModule],
                    providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS,
                }]
        }] });
/**
 * Returns the set of [dependency-injection providers](guide/glossary#provider)
 * to disable animations in an application. See [animations guide](guide/animations)
 * to learn more about animations in Angular.
 *
 * @usageNotes
 *
 * The function is useful when you want to bootstrap an application using
 * the `bootstrapApplication` function, but you need to disable animations
 * (for example, when running tests).
 *
 * ```typescript
 * bootstrapApplication(RootComponent, {
 *   providers: [
 *     provideNoopAnimations()
 *   ]
 * });
 * ```
 *
 * @publicApi
 */
export function provideNoopAnimations() {
    // Return a copy to prevent changes to the original array in case any in-place
    // alterations are performed to the `provideNoopAnimations` call results in app code.
    return [...BROWSER_NOOP_ANIMATIONS_PROVIDERS];
}
//# sourceMappingURL=data:application/json;base64,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