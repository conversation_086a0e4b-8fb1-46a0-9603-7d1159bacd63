/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { createComputed, SIGNAL } from '@angular/core/primitives/signals';
import { RuntimeError } from '../errors';
import { unwrapElementRef } from '../linker/element_ref';
import { EMPTY_ARRAY } from '../util/empty';
import { FLAGS } from './interfaces/view';
import { getQueryResults, loadQueryInternal } from './query';
import { signal } from './reactivity/signal';
import { getLView } from './state';
/**
 * A signal factory function in charge of creating a new computed signal capturing query
 * results. This centralized creation function is used by all types of queries (child / children,
 * required / optional).
 *
 * @param firstOnly indicates if all or only the first result should be returned
 * @param required indicates if at least one result is required
 * @returns a read-only signal with query results
 */
function createQuerySignalFn(firstOnly, required) {
    let node;
    const signalFn = createComputed(() => {
        // A dedicated signal that increments its value every time a query changes its dirty status. By
        // using this signal we can implement a query as computed and avoid creation of a specialized
        // reactive node type. Please note that a query gets marked dirty under the following
        // circumstances:
        // - a view (where a query is active) finished its first creation pass;
        // - a new view is inserted / deleted and it impacts query results.
        node._dirtyCounter();
        const value = refreshSignalQuery(node, firstOnly);
        if (required && value === undefined) {
            throw new RuntimeError(-951 /* RuntimeErrorCode.REQUIRED_QUERY_NO_VALUE */, ngDevMode && 'Child query result is required but no value is available.');
        }
        return value;
    });
    node = signalFn[SIGNAL];
    node._dirtyCounter = signal(0);
    node._flatValue = undefined;
    if (ngDevMode) {
        signalFn.toString = () => `[Query Signal]`;
    }
    return signalFn;
}
export function createSingleResultOptionalQuerySignalFn() {
    return createQuerySignalFn(/* firstOnly */ true, /* required */ false);
}
export function createSingleResultRequiredQuerySignalFn() {
    return createQuerySignalFn(/* firstOnly */ true, /* required */ true);
}
export function createMultiResultQuerySignalFn() {
    return createQuerySignalFn(/* firstOnly */ false, /* required */ false);
}
export function bindQueryToSignal(target, queryIndex) {
    const node = target[SIGNAL];
    node._lView = getLView();
    node._queryIndex = queryIndex;
    node._queryList = loadQueryInternal(node._lView, queryIndex);
    node._queryList.onDirty(() => node._dirtyCounter.update(v => v + 1));
}
function refreshSignalQuery(node, firstOnly) {
    const lView = node._lView;
    const queryIndex = node._queryIndex;
    // There are 2 conditions under which we want to return "empty" results instead of the ones
    // collected by a query:
    //
    // 1) a given query wasn't created yet (this is a period of time between the directive creation
    // and execution of the query creation function) - in this case a query doesn't exist yet and we
    // don't have any results to return.
    //
    // 2) we are in the process of constructing a view (the first
    // creation pass didn't finish) and a query might have partial results, but we don't want to
    // return those - instead we do delay results collection until all nodes had a chance of matching
    // and we can present consistent, "atomic" (on a view level) results.
    if (lView === undefined || queryIndex === undefined || (lView[FLAGS] & 4 /* LViewFlags.CreationMode */)) {
        return (firstOnly ? undefined : EMPTY_ARRAY);
    }
    const queryList = loadQueryInternal(lView, queryIndex);
    const results = getQueryResults(lView, queryIndex);
    queryList.reset(results, unwrapElementRef);
    if (firstOnly) {
        return queryList.first;
    }
    else {
        // TODO: remove access to the private _changesDetected field by abstracting / removing usage of
        // QueryList in the signal-based queries (perf follow-up)
        const resultChanged = queryList._changesDetected;
        if (resultChanged || node._flatValue === undefined) {
            return node._flatValue = queryList.toArray();
        }
        return node._flatValue;
    }
}
//# sourceMappingURL=data:application/json;base64,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