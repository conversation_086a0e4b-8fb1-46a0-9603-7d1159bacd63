/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val, i = Math.floor(Math.abs(val));
    if (i === 0 || n === 1)
        return 1;
    return 5;
}
export default ["bn-IN", [["AM", "PM"], u, u], u, [["র", "সো", "ম", "বু", "বৃ", "শু", "শ"], ["রবি", "সোম", "মঙ্গল", "বুধ", "বৃহস্পতি", "শুক্র", "শনি"], ["রবিবার", "সোমবার", "মঙ্গলবার", "বুধবার", "বৃহস্পতিবার", "শুক্রবার", "শনিবার"], ["রঃ", "সোঃ", "মঃ", "বুঃ", "বৃঃ", "শুঃ", "শনি"]], u, [["জা", "ফে", "মা", "এ", "মে", "জুন", "জু", "আ", "সে", "অ", "ন", "ডি"], ["জানু", "ফেব", "মার্চ", "এপ্রিল", "মে", "জুন", "জুলাই", "আগস্ট", "সেপ্টেম্বর", "অক্টোবর", "নভেম্বর", "ডিসেম্বর"], ["জানুয়ারী", "ফেব্রুয়ারী", "মার্চ", "এপ্রিল", "মে", "জুন", "জুলাই", "আগস্ট", "সেপ্টেম্বর", "অক্টোবর", "নভেম্বর", "ডিসেম্বর"]], [["জা", "ফে", "মা", "এ", "মে", "জুন", "জু", "আ", "সে", "অ", "ন", "ডি"], ["জানুয়ারী", "ফেব্রুয়ারী", "মার্চ", "এপ্রিল", "মে", "জুন", "জুলাই", "আগস্ট", "সেপ্টেম্বর", "অক্টোবর", "নভেম্বর", "ডিসেম্বর"], u], [["খ্রিস্টপূর্ব", "খৃষ্টাব্দ"], u, ["খ্রিস্টপূর্ব", "খ্রীষ্টাব্দ"]], 0, [0, 0], ["d/M/yy", "d MMM, y", "d MMMM, y", "EEEE, d MMMM, y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##,##0.###", "#,##,##0%", "#,##,##0.00¤", "#E0"], "INR", "₹", "ভারতীয় রুপি", { "BDT": ["৳"], "BYN": [u, "р."], "JPY": ["JP¥", "¥"], "PHP": [u, "₱"], "THB": ["฿"], "TWD": ["NT$"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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