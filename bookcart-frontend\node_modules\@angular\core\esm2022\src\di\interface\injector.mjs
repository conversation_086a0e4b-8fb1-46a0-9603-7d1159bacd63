/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * Injection flags for DI.
 *
 * @publicApi
 * @deprecated use an options object for [`inject`](api/core/inject) instead.
 */
export var InjectFlags;
(function (InjectFlags) {
    // TODO(alxhub): make this 'const' (and remove `InternalInjectFlags` enum) when ngc no longer
    // writes exports of it into ngfactory files.
    /** Check self and check parent injector if needed */
    InjectFlags[InjectFlags["Default"] = 0] = "Default";
    /**
     * Specifies that an injector should retrieve a dependency from any injector until reaching the
     * host element of the current component. (Only used with Element Injector)
     */
    InjectFlags[InjectFlags["Host"] = 1] = "Host";
    /** Don't ascend to ancestors of the node requesting injection. */
    InjectFlags[InjectFlags["Self"] = 2] = "Self";
    /** Skip the node that is requesting injection. */
    InjectFlags[InjectFlags["SkipSelf"] = 4] = "SkipSelf";
    /** Inject `defaultValue` instead if token not found. */
    InjectFlags[InjectFlags["Optional"] = 8] = "Optional";
})(InjectFlags || (InjectFlags = {}));
//# sourceMappingURL=data:application/json;base64,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