/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["sah", [["ЭИ", "ЭК"], u, u], u, [["Б", "Б", "О", "С", "Ч", "Б", "С"], ["бс", "бн", "оп", "сэ", "чп", "бэ", "сб"], ["баскыһыанньа", "бэнидиэнньик", "оптуорунньук", "сэрэдэ", "чэппиэр", "Бээтиҥсэ", "субуота"], ["бс", "бн", "оп", "сэ", "чп", "бэ", "сб"]], u, [["Т", "О", "К", "М", "Ы", "Б", "О", "А", "Б", "А", "С", "А"], ["Тохс", "Олун", "Клн", "Мсу", "Ыам", "Бэс", "Отй", "Атр", "Блҕ", "Алт", "Сэт", "Ахс"], ["Тохсунньу", "Олунньу", "Кулун тутар", "Муус устар", "Ыам ыйын", "Бэс ыйын", "От ыйын", "Атырдьых ыйын", "Балаҕан ыйын", "Алтынньы", "Сэтинньи", "ахсынньы"]], [["Т", "О", "К", "М", "Ы", "Б", "О", "А", "Б", "А", "С", "А"], ["Тохс", "Олун", "Клн", "Мсу", "Ыам", "Бэс", "Отй", "Атр", "Блҕ", "Алт", "Сэт", "Ахс"], ["тохсунньу", "олунньу", "кулун тутар", "муус устар", "ыам ыйа", "бэс ыйа", "от ыйа", "атырдьых ыйа", "балаҕан ыйа", "алтынньы", "сэтинньи", "ахсынньы"]], [["б. э. и.", "б. э"], u, u], 1, [6, 0], ["yy/M/d", "y, MMM d", "y, MMMM d", "y 'сыл' MMMM d 'күнэ', EEEE"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", " ", ";", "%", "+", "-", "E", "×", "‰", "∞", "чыыһыла буотах", ":"], ["#,##0.###", "#,##0%", "#,##0.00 ¤", "#E0"], "RUB", "₽", "Арассыыйа солкуобайа", { "JPY": ["JP¥", "¥"], "RUB": ["₽"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic2FoLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vLi4vcGFja2FnZXMvY29tbW9uL2xvY2FsZXMvc2FoLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7R0FNRztBQUVILDBDQUEwQztBQUMxQyxNQUFNLENBQUMsR0FBRyxTQUFTLENBQUM7QUFFcEIsU0FBUyxNQUFNLENBQUMsR0FBVztJQUMzQixNQUFNLENBQUMsR0FBRyxHQUFHLENBQUM7SUFFZCxPQUFPLENBQUMsQ0FBQztBQUNULENBQUM7QUFFRCxlQUFlLENBQUMsS0FBSyxFQUFDLENBQUMsQ0FBQyxJQUFJLEVBQUMsSUFBSSxDQUFDLEVBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxDQUFDLEVBQUMsQ0FBQyxJQUFJLEVBQUMsSUFBSSxFQUFDLElBQUksRUFBQyxJQUFJLEVBQUMsSUFBSSxFQUFDLElBQUksRUFBQyxJQUFJLENBQUMsRUFBQyxDQUFDLGNBQWMsRUFBQyxjQUFjLEVBQUMsY0FBYyxFQUFDLFFBQVEsRUFBQyxTQUFTLEVBQUMsVUFBVSxFQUFDLFNBQVMsQ0FBQyxFQUFDLENBQUMsSUFBSSxFQUFDLElBQUksRUFBQyxJQUFJLEVBQUMsSUFBSSxFQUFDLElBQUksRUFBQyxJQUFJLEVBQUMsSUFBSSxDQUFDLENBQUMsRUFBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxDQUFDLEVBQUMsQ0FBQyxNQUFNLEVBQUMsTUFBTSxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssQ0FBQyxFQUFDLENBQUMsV0FBVyxFQUFDLFNBQVMsRUFBQyxhQUFhLEVBQUMsWUFBWSxFQUFDLFVBQVUsRUFBQyxVQUFVLEVBQUMsU0FBUyxFQUFDLGVBQWUsRUFBQyxjQUFjLEVBQUMsVUFBVSxFQUFDLFVBQVUsRUFBQyxVQUFVLENBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsQ0FBQyxFQUFDLENBQUMsTUFBTSxFQUFDLE1BQU0sRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLENBQUMsRUFBQyxDQUFDLFdBQVcsRUFBQyxTQUFTLEVBQUMsYUFBYSxFQUFDLFlBQVksRUFBQyxTQUFTLEVBQUMsU0FBUyxFQUFDLFFBQVEsRUFBQyxjQUFjLEVBQUMsYUFBYSxFQUFDLFVBQVUsRUFBQyxVQUFVLEVBQUMsVUFBVSxDQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsVUFBVSxFQUFDLE1BQU0sQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsRUFBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDLEVBQUMsQ0FBQyxRQUFRLEVBQUMsVUFBVSxFQUFDLFdBQVcsRUFBQyw2QkFBNkIsQ0FBQyxFQUFDLENBQUMsT0FBTyxFQUFDLFVBQVUsRUFBQyxZQUFZLEVBQUMsZUFBZSxDQUFDLEVBQUMsQ0FBQyxTQUFTLEVBQUMsQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsRUFBQyxDQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxnQkFBZ0IsRUFBQyxHQUFHLENBQUMsRUFBQyxDQUFDLFdBQVcsRUFBQyxRQUFRLEVBQUMsWUFBWSxFQUFDLEtBQUssQ0FBQyxFQUFDLEtBQUssRUFBQyxHQUFHLEVBQUMsc0JBQXNCLEVBQUMsRUFBQyxLQUFLLEVBQUMsQ0FBQyxLQUFLLEVBQUMsR0FBRyxDQUFDLEVBQUMsS0FBSyxFQUFDLENBQUMsR0FBRyxDQUFDLEVBQUMsS0FBSyxFQUFDLENBQUMsS0FBSyxFQUFDLEdBQUcsQ0FBQyxFQUFDLEVBQUMsS0FBSyxFQUFFLE1BQU0sQ0FBQyxDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5cbi8vIFRISVMgQ09ERSBJUyBHRU5FUkFURUQgLSBETyBOT1QgTU9ESUZZLlxuY29uc3QgdSA9IHVuZGVmaW5lZDtcblxuZnVuY3Rpb24gcGx1cmFsKHZhbDogbnVtYmVyKTogbnVtYmVyIHtcbmNvbnN0IG4gPSB2YWw7XG5cbnJldHVybiA1O1xufVxuXG5leHBvcnQgZGVmYXVsdCBbXCJzYWhcIixbW1wi0K3QmFwiLFwi0K3QmlwiXSx1LHVdLHUsW1tcItCRXCIsXCLQkVwiLFwi0J5cIixcItChXCIsXCLQp1wiLFwi0JFcIixcItChXCJdLFtcItCx0YFcIixcItCx0L1cIixcItC+0L9cIixcItGB0Y1cIixcItGH0L9cIixcItCx0Y1cIixcItGB0LFcIl0sW1wi0LHQsNGB0LrRi9K70YvQsNC90L3RjNCwXCIsXCLQsdGN0L3QuNC00LjRjdC90L3RjNC40LpcIixcItC+0L/RgtGD0L7RgNGD0L3QvdGM0YPQulwiLFwi0YHRjdGA0Y3QtNGNXCIsXCLRh9GN0L/Qv9C40Y3RgFwiLFwi0JHRjdGN0YLQuNKl0YHRjVwiLFwi0YHRg9Cx0YPQvtGC0LBcIl0sW1wi0LHRgVwiLFwi0LHQvVwiLFwi0L7Qv1wiLFwi0YHRjVwiLFwi0YfQv1wiLFwi0LHRjVwiLFwi0YHQsVwiXV0sdSxbW1wi0KJcIixcItCeXCIsXCLQmlwiLFwi0JxcIixcItCrXCIsXCLQkVwiLFwi0J5cIixcItCQXCIsXCLQkVwiLFwi0JBcIixcItChXCIsXCLQkFwiXSxbXCLQotC+0YXRgVwiLFwi0J7Qu9GD0L1cIixcItCa0LvQvVwiLFwi0JzRgdGDXCIsXCLQq9Cw0LxcIixcItCR0Y3RgVwiLFwi0J7RgtC5XCIsXCLQkNGC0YBcIixcItCR0LvSlVwiLFwi0JDQu9GCXCIsXCLQodGN0YJcIixcItCQ0YXRgVwiXSxbXCLQotC+0YXRgdGD0L3QvdGM0YNcIixcItCe0LvRg9C90L3RjNGDXCIsXCLQmtGD0LvRg9C9INGC0YPRgtCw0YBcIixcItCc0YPRg9GBINGD0YHRgtCw0YBcIixcItCr0LDQvCDRi9C50YvQvVwiLFwi0JHRjdGBINGL0LnRi9C9XCIsXCLQntGCINGL0LnRi9C9XCIsXCLQkNGC0YvRgNC00YzRi9GFINGL0LnRi9C9XCIsXCLQkdCw0LvQsNKV0LDQvSDRi9C50YvQvVwiLFwi0JDQu9GC0YvQvdC90YzRi1wiLFwi0KHRjdGC0LjQvdC90YzQuFwiLFwi0LDRhdGB0YvQvdC90YzRi1wiXV0sW1tcItCiXCIsXCLQnlwiLFwi0JpcIixcItCcXCIsXCLQq1wiLFwi0JFcIixcItCeXCIsXCLQkFwiLFwi0JFcIixcItCQXCIsXCLQoVwiLFwi0JBcIl0sW1wi0KLQvtGF0YFcIixcItCe0LvRg9C9XCIsXCLQmtC70L1cIixcItCc0YHRg1wiLFwi0KvQsNC8XCIsXCLQkdGN0YFcIixcItCe0YLQuVwiLFwi0JDRgtGAXCIsXCLQkdC70pVcIixcItCQ0LvRglwiLFwi0KHRjdGCXCIsXCLQkNGF0YFcIl0sW1wi0YLQvtGF0YHRg9C90L3RjNGDXCIsXCLQvtC70YPQvdC90YzRg1wiLFwi0LrRg9C70YPQvSDRgtGD0YLQsNGAXCIsXCLQvNGD0YPRgSDRg9GB0YLQsNGAXCIsXCLRi9Cw0Lwg0YvQudCwXCIsXCLQsdGN0YEg0YvQudCwXCIsXCLQvtGCINGL0LnQsFwiLFwi0LDRgtGL0YDQtNGM0YvRhSDRi9C50LBcIixcItCx0LDQu9Cw0pXQsNC9INGL0LnQsFwiLFwi0LDQu9GC0YvQvdC90YzRi1wiLFwi0YHRjdGC0LjQvdC90YzQuFwiLFwi0LDRhdGB0YvQvdC90YzRi1wiXV0sW1tcItCxLiDRjS4g0LguXCIsXCLQsS4g0Y1cIl0sdSx1XSwxLFs2LDBdLFtcInl5L00vZFwiLFwieSwgTU1NIGRcIixcInksIE1NTU0gZFwiLFwieSAn0YHRi9C7JyBNTU1NIGQgJ9C60q/QvdGNJywgRUVFRVwiXSxbXCJISDptbVwiLFwiSEg6bW06c3NcIixcIkhIOm1tOnNzIHpcIixcIkhIOm1tOnNzIHp6enpcIl0sW1wiezF9IHswfVwiLHUsdSx1XSxbXCIsXCIsXCLCoFwiLFwiO1wiLFwiJVwiLFwiK1wiLFwiLVwiLFwiRVwiLFwiw5dcIixcIuKAsFwiLFwi4oieXCIsXCLRh9GL0YvSu9GL0LvQsMKg0LHRg9C+0YLQsNGFXCIsXCI6XCJdLFtcIiMsIyMwLiMjI1wiLFwiIywjIzAlXCIsXCIjLCMjMC4wMMKgwqRcIixcIiNFMFwiXSxcIlJVQlwiLFwi4oK9XCIsXCLQkNGA0LDRgdGB0YvRi9C50LAg0YHQvtC70LrRg9C+0LHQsNC50LBcIix7XCJKUFlcIjpbXCJKUMKlXCIsXCLCpVwiXSxcIlJVQlwiOltcIuKCvVwiXSxcIlVTRFwiOltcIlVTJFwiLFwiJFwiXX0sXCJsdHJcIiwgcGx1cmFsXTtcbiJdfQ==