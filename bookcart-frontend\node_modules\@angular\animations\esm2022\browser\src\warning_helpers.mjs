/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function createListOfWarnings(warnings) {
    const LINE_START = '\n - ';
    return `${LINE_START}${warnings
        .filter(Boolean)
        .map((warning) => warning)
        .join(LINE_START)}`;
}
export function warnValidation(warnings) {
    (typeof ngDevMode === 'undefined' || ngDevMode) &&
        console.warn(`animation validation warnings:${createListOfWarnings(warnings)}`);
}
export function warnTriggerBuild(name, warnings) {
    (typeof ngDevMode === 'undefined' || ngDevMode) &&
        console.warn(`The animation trigger "${name}" has built with the following warnings:${createListOfWarnings(warnings)}`);
}
export function warnRegister(warnings) {
    (typeof ngDevMode === 'undefined' || ngDevMode) &&
        console.warn(`Animation built with the following warnings:${createListOfWarnings(warnings)}`);
}
export function triggerParsingWarnings(name, warnings) {
    (typeof ngDevMode === 'undefined' || ngDevMode) &&
        console.warn(`Animation parsing for the ${name} trigger presents the following warnings:${createListOfWarnings(warnings)}`);
}
export function pushUnrecognizedPropertiesWarning(warnings, props) {
    if (props.length) {
        warnings.push(`The following provided properties are not recognized: ${props.join(', ')}`);
    }
}
//# sourceMappingURL=data:application/json;base64,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