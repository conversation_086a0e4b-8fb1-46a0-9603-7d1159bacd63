/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

if (n === 1)
    return 1;
return 5;
}
    global.ng.common.locales['wae'] = ["wae",[["AM","PM"],u,u],u,[["S","M","Z","M","F","F","S"],["<PERSON>","<PERSON>än","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>i","<PERSON>"],["Sunntag","<PERSON>äntag","<PERSON><PERSON><PERSON><PERSON>","<PERSON>tt<PERSON><PERSON>","Fróntag","Fritag","<PERSON><PERSON><PERSON>"],["<PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>"]],u,[["<PERSON>","H","M","A","<PERSON>","B","<PERSON>","<PERSON>","<PERSON>","W","<PERSON>","<PERSON>"],["<PERSON>","<PERSON>r","<PERSON><PERSON>r","<PERSON>br","<PERSON>","<PERSON>r<PERSON>","<PERSON>i","<PERSON>ig","<PERSON>","<PERSON><PERSON>m","<PERSON>","<PERSON>r"],["<PERSON><PERSON>","Hornig","<PERSON><PERSON>rze","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON>r<PERSON><PERSON>et","<PERSON>iwet","<PERSON>ig<PERSON>te","Herbštmánet","Wímánet","Wintermánet","Chrištmánet"]],u,[["v. Chr.","n. Chr"],u,u],1,[6,0],["y-MM-dd","d. MMM y","d. MMMM y","EEEE, d. MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[",","’",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤ #,##0.00","#E0"],"CHF","CHF","CHF",{},"ltr", plural, []];
  })(globalThis);
    