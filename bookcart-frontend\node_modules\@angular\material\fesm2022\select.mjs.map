{"version": 3, "file": "select.mjs", "sources": ["../../../../../../src/material/select/select-animations.ts", "../../../../../../src/material/select/select-errors.ts", "../../../../../../src/material/select/select.ts", "../../../../../../src/material/select/select.html", "../../../../../../src/material/select/module.ts", "../../../../../../src/material/select/select_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  animate,\n  animateChild,\n  AnimationTriggerMetadata,\n  query,\n  state,\n  style,\n  transition,\n  trigger,\n} from '@angular/animations';\n\n/**\n * The following are all the animations for the mat-select component, with each\n * const containing the metadata for one animation.\n *\n * The values below match the implementation of the AngularJS Material mat-select animation.\n * @docs-private\n */\nexport const matSelectAnimations: {\n  /**\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 12.0.0\n   */\n  readonly transformPanelWrap: AnimationTriggerMetadata;\n  readonly transformPanel: AnimationTriggerMetadata;\n} = {\n  /**\n   * This animation ensures the select's overlay panel animation (transformPanel) is called when\n   * closing the select.\n   * This is needed due to https://github.com/angular/angular/issues/23302\n   */\n  transformPanelWrap: trigger('transformPanelWrap', [\n    transition('* => void', query('@transformPanel', [animateChild()], {optional: true})),\n  ]),\n\n  /** This animation transforms the select's overlay panel on and off the page. */\n  transformPanel: trigger('transformPanel', [\n    state(\n      'void',\n      style({\n        opacity: 0,\n        transform: 'scale(1, 0.8)',\n      }),\n    ),\n    transition(\n      'void => showing',\n      animate(\n        '120ms cubic-bezier(0, 0, 0.2, 1)',\n        style({\n          opacity: 1,\n          transform: 'scale(1, 1)',\n        }),\n      ),\n    ),\n    transition('* => void', animate('100ms linear', style({opacity: 0}))),\n  ]),\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// Note that these have been copied over verbatim from\n// `material/select` so that we don't have to expose them publicly.\n\n/**\n * Returns an exception to be thrown when attempting to change a select's `multiple` option\n * after initialization.\n * @docs-private\n */\nexport function getMatSelectDynamicMultipleError(): Error {\n  return Error('Cannot change `multiple` mode of select after initialization.');\n}\n\n/**\n * Returns an exception to be thrown when attempting to assign a non-array value to a select\n * in `multiple` mode. Note that `undefined` and `null` are still valid values to allow for\n * resetting the value.\n * @docs-private\n */\nexport function getMatSelectNonArrayValueError(): Error {\n  return Error('Value must be an array in multiple-selection mode.');\n}\n\n/**\n * Returns an exception to be thrown when assigning a non-function value to the comparator\n * used to determine if a value corresponds to an option. Note that whether the function\n * actually takes two values and returns a boolean is not checked.\n */\nexport function getMatSelectNonFunctionValueError(): Error {\n  return Error('`compareWith` must be a function.');\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ActiveDescendantKeyManager,\n  addAriaReferencedId,\n  LiveAnnouncer,\n  removeAriaReferencedId,\n} from '@angular/cdk/a11y';\nimport {Directionality} from '@angular/cdk/bidi';\nimport {SelectionModel} from '@angular/cdk/collections';\nimport {\n  A,\n  DOWN_ARROW,\n  ENTER,\n  hasModifierKey,\n  LEFT_ARROW,\n  RIGHT_ARROW,\n  SPACE,\n  UP_ARROW,\n} from '@angular/cdk/keycodes';\nimport {\n  CdkConnectedOverlay,\n  CdkOverlayOrigin,\n  ConnectedPosition,\n  Overlay,\n  ScrollStrategy,\n} from '@angular/cdk/overlay';\nimport {ViewportRuler} from '@angular/cdk/scrolling';\nimport {\n  AfterContentInit,\n  Attribute,\n  booleanAttribute,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChild,\n  ContentChildren,\n  Directive,\n  Do<PERSON>he<PERSON>,\n  ElementRef,\n  EventEmitter,\n  Inject,\n  inject,\n  InjectionToken,\n  Input,\n  NgZone,\n  numberAttribute,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  Optional,\n  Output,\n  QueryList,\n  Self,\n  SimpleChanges,\n  ViewChild,\n  ViewEncapsulation,\n} from '@angular/core';\nimport {\n  AbstractControl,\n  ControlValueAccessor,\n  FormGroupDirective,\n  NgControl,\n  NgForm,\n  Validators,\n} from '@angular/forms';\nimport {\n  _countGroupLabelsBeforeOption,\n  _ErrorStateTracker,\n  _getOptionScrollPosition,\n  ErrorStateMatcher,\n  MAT_OPTGROUP,\n  MAT_OPTION_PARENT_COMPONENT,\n  MatOptgroup,\n  MatOption,\n  MatOptionSelectionChange,\n} from '@angular/material/core';\nimport {MAT_FORM_FIELD, MatFormField, MatFormFieldControl} from '@angular/material/form-field';\nimport {defer, merge, Observable, Subject} from 'rxjs';\nimport {\n  distinctUntilChanged,\n  filter,\n  map,\n  startWith,\n  switchMap,\n  take,\n  takeUntil,\n} from 'rxjs/operators';\nimport {matSelectAnimations} from './select-animations';\nimport {\n  getMatSelectDynamicMultipleError,\n  getMatSelectNonArrayValueError,\n  getMatSelectNonFunctionValueError,\n} from './select-errors';\nimport {NgClass} from '@angular/common';\n\nlet nextUniqueId = 0;\n\n/** Injection token that determines the scroll handling while a select is open. */\nexport const MAT_SELECT_SCROLL_STRATEGY = new InjectionToken<() => ScrollStrategy>(\n  'mat-select-scroll-strategy',\n  {\n    providedIn: 'root',\n    factory: () => {\n      const overlay = inject(Overlay);\n      return () => overlay.scrollStrategies.reposition();\n    },\n  },\n);\n\n/** @docs-private */\nexport function MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY(\n  overlay: Overlay,\n): () => ScrollStrategy {\n  return () => overlay.scrollStrategies.reposition();\n}\n\n/** Object that can be used to configure the default options for the select module. */\nexport interface MatSelectConfig {\n  /** Whether option centering should be disabled. */\n  disableOptionCentering?: boolean;\n\n  /** Time to wait in milliseconds after the last keystroke before moving focus to an item. */\n  typeaheadDebounceInterval?: number;\n\n  /** Class or list of classes to be applied to the menu's overlay panel. */\n  overlayPanelClass?: string | string[];\n\n  /** Wheter icon indicators should be hidden for single-selection. */\n  hideSingleSelectionIndicator?: boolean;\n\n  /**\n   * Width of the panel. If set to `auto`, the panel will match the trigger width.\n   * If set to null or an empty string, the panel will grow to match the longest option's text.\n   */\n  panelWidth?: string | number | null;\n}\n\n/** Injection token that can be used to provide the default options the select module. */\nexport const MAT_SELECT_CONFIG = new InjectionToken<MatSelectConfig>('MAT_SELECT_CONFIG');\n\n/** @docs-private */\nexport const MAT_SELECT_SCROLL_STRATEGY_PROVIDER = {\n  provide: MAT_SELECT_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n\n/**\n * Injection token that can be used to reference instances of `MatSelectTrigger`. It serves as\n * alternative token to the actual `MatSelectTrigger` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nexport const MAT_SELECT_TRIGGER = new InjectionToken<MatSelectTrigger>('MatSelectTrigger');\n\n/** Change event object that is emitted when the select value has changed. */\nexport class MatSelectChange {\n  constructor(\n    /** Reference to the select that emitted the change event. */\n    public source: MatSelect,\n    /** Current value of the select that emitted the event. */\n    public value: any,\n  ) {}\n}\n\n@Component({\n  selector: 'mat-select',\n  exportAs: 'matSelect',\n  templateUrl: 'select.html',\n  styleUrl: 'select.css',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  host: {\n    'role': 'combobox',\n    'aria-autocomplete': 'none',\n    'aria-haspopup': 'listbox',\n    'class': 'mat-mdc-select',\n    '[attr.id]': 'id',\n    '[attr.tabindex]': 'disabled ? -1 : tabIndex',\n    '[attr.aria-controls]': 'panelOpen ? id + \"-panel\" : null',\n    '[attr.aria-expanded]': 'panelOpen',\n    '[attr.aria-label]': 'ariaLabel || null',\n    '[attr.aria-required]': 'required.toString()',\n    '[attr.aria-disabled]': 'disabled.toString()',\n    '[attr.aria-invalid]': 'errorState',\n    '[attr.aria-activedescendant]': '_getAriaActiveDescendant()',\n    '[class.mat-mdc-select-disabled]': 'disabled',\n    '[class.mat-mdc-select-invalid]': 'errorState',\n    '[class.mat-mdc-select-required]': 'required',\n    '[class.mat-mdc-select-empty]': 'empty',\n    '[class.mat-mdc-select-multiple]': 'multiple',\n    '(keydown)': '_handleKeydown($event)',\n    '(focus)': '_onFocus()',\n    '(blur)': '_onBlur()',\n  },\n  animations: [matSelectAnimations.transformPanel],\n  providers: [\n    {provide: MatFormFieldControl, useExisting: MatSelect},\n    {provide: MAT_OPTION_PARENT_COMPONENT, useExisting: MatSelect},\n  ],\n  standalone: true,\n  imports: [CdkOverlayOrigin, CdkConnectedOverlay, NgClass],\n})\nexport class MatSelect\n  implements\n    AfterContentInit,\n    OnChanges,\n    OnDestroy,\n    OnInit,\n    DoCheck,\n    ControlValueAccessor,\n    MatFormFieldControl<any>\n{\n  /** All of the defined select options. */\n  @ContentChildren(MatOption, {descendants: true}) options: QueryList<MatOption>;\n\n  // TODO(crisbeto): this is only necessary for the non-MDC select, but it's technically a\n  // public API so we have to keep it. It should be deprecated and removed eventually.\n  /** All of the defined groups of options. */\n  @ContentChildren(MAT_OPTGROUP, {descendants: true}) optionGroups: QueryList<MatOptgroup>;\n\n  /** User-supplied override of the trigger element. */\n  @ContentChild(MAT_SELECT_TRIGGER) customTrigger: MatSelectTrigger;\n\n  /**\n   * This position config ensures that the top \"start\" corner of the overlay\n   * is aligned with with the top \"start\" of the origin by default (overlapping\n   * the trigger completely). If the panel cannot fit below the trigger, it\n   * will fall back to a position above the trigger.\n   */\n  _positions: ConnectedPosition[] = [\n    {\n      originX: 'start',\n      originY: 'bottom',\n      overlayX: 'start',\n      overlayY: 'top',\n    },\n    {\n      originX: 'end',\n      originY: 'bottom',\n      overlayX: 'end',\n      overlayY: 'top',\n    },\n    {\n      originX: 'start',\n      originY: 'top',\n      overlayX: 'start',\n      overlayY: 'bottom',\n      panelClass: 'mat-mdc-select-panel-above',\n    },\n    {\n      originX: 'end',\n      originY: 'top',\n      overlayX: 'end',\n      overlayY: 'bottom',\n      panelClass: 'mat-mdc-select-panel-above',\n    },\n  ];\n\n  /** Scrolls a particular option into the view. */\n  _scrollOptionIntoView(index: number): void {\n    const option = this.options.toArray()[index];\n\n    if (option) {\n      const panel: HTMLElement = this.panel.nativeElement;\n      const labelCount = _countGroupLabelsBeforeOption(index, this.options, this.optionGroups);\n      const element = option._getHostElement();\n\n      if (index === 0 && labelCount === 1) {\n        // If we've got one group label before the option and we're at the top option,\n        // scroll the list to the top. This is better UX than scrolling the list to the\n        // top of the option, because it allows the user to read the top group's label.\n        panel.scrollTop = 0;\n      } else {\n        panel.scrollTop = _getOptionScrollPosition(\n          element.offsetTop,\n          element.offsetHeight,\n          panel.scrollTop,\n          panel.offsetHeight,\n        );\n      }\n    }\n  }\n\n  /** Called when the panel has been opened and the overlay has settled on its final position. */\n  private _positioningSettled() {\n    this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n  }\n\n  /** Creates a change event object that should be emitted by the select. */\n  private _getChangeEvent(value: any) {\n    return new MatSelectChange(this, value);\n  }\n\n  /** Factory function used to create a scroll strategy for this select. */\n  private _scrollStrategyFactory: () => ScrollStrategy;\n\n  /** Whether or not the overlay panel is open. */\n  private _panelOpen = false;\n\n  /** Comparison function to specify which option is displayed. Defaults to object equality. */\n  private _compareWith = (o1: any, o2: any) => o1 === o2;\n\n  /** Unique id for this input. */\n  private _uid = `mat-select-${nextUniqueId++}`;\n\n  /** Current `aria-labelledby` value for the select trigger. */\n  private _triggerAriaLabelledBy: string | null = null;\n\n  /**\n   * Keeps track of the previous form control assigned to the select.\n   * Used to detect if it has changed.\n   */\n  private _previousControl: AbstractControl | null | undefined;\n\n  /** Emits whenever the component is destroyed. */\n  protected readonly _destroy = new Subject<void>();\n\n  /** Tracks the error state of the select. */\n  private _errorStateTracker: _ErrorStateTracker;\n\n  /**\n   * Emits whenever the component state changes and should cause the parent\n   * form-field to update. Implemented as part of `MatFormFieldControl`.\n   * @docs-private\n   */\n  readonly stateChanges = new Subject<void>();\n\n  /**\n   * Disable the automatic labeling to avoid issues like #27241.\n   * @docs-private\n   */\n  readonly disableAutomaticLabeling = true;\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  @Input('aria-describedby') userAriaDescribedBy: string;\n\n  /** Deals with the selection logic. */\n  _selectionModel: SelectionModel<MatOption>;\n\n  /** Manages keyboard events for options in the panel. */\n  _keyManager: ActiveDescendantKeyManager<MatOption>;\n\n  /** Ideal origin for the overlay panel. */\n  _preferredOverlayOrigin: CdkOverlayOrigin | ElementRef | undefined;\n\n  /** Width of the overlay panel. */\n  _overlayWidth: string | number;\n\n  /** `View -> model callback called when value changes` */\n  _onChange: (value: any) => void = () => {};\n\n  /** `View -> model callback called when select has been touched` */\n  _onTouched = () => {};\n\n  /** ID for the DOM node containing the select's value. */\n  _valueId = `mat-select-value-${nextUniqueId++}`;\n\n  /** Emits when the panel element is finished transforming in. */\n  readonly _panelDoneAnimatingStream = new Subject<string>();\n\n  /** Strategy that will be used to handle scrolling while the select panel is open. */\n  _scrollStrategy: ScrollStrategy;\n\n  _overlayPanelClass: string | string[] = this._defaultOptions?.overlayPanelClass || '';\n\n  /** Whether the select is focused. */\n  get focused(): boolean {\n    return this._focused || this._panelOpen;\n  }\n  private _focused = false;\n\n  /** A name for this control that can be used by `mat-form-field`. */\n  controlType = 'mat-select';\n\n  /** Trigger that opens the select. */\n  @ViewChild('trigger') trigger: ElementRef;\n\n  /** Panel containing the select options. */\n  @ViewChild('panel') panel: ElementRef;\n\n  /** Overlay pane containing the options. */\n  @ViewChild(CdkConnectedOverlay)\n  protected _overlayDir: CdkConnectedOverlay;\n\n  /** Classes to be passed to the select panel. Supports the same syntax as `ngClass`. */\n  @Input() panelClass: string | string[] | Set<string> | {[key: string]: any};\n\n  /** Whether the select is disabled. */\n  @Input({transform: booleanAttribute})\n  disabled: boolean = false;\n\n  /** Whether ripples in the select are disabled. */\n  @Input({transform: booleanAttribute})\n  disableRipple: boolean = false;\n\n  /** Tab index of the select. */\n  @Input({\n    transform: (value: unknown) => (value == null ? 0 : numberAttribute(value)),\n  })\n  tabIndex: number = 0;\n\n  /** Whether checkmark indicator for single-selection options is hidden. */\n  @Input({transform: booleanAttribute})\n  get hideSingleSelectionIndicator(): boolean {\n    return this._hideSingleSelectionIndicator;\n  }\n  set hideSingleSelectionIndicator(value: boolean) {\n    this._hideSingleSelectionIndicator = value;\n    this._syncParentProperties();\n  }\n  private _hideSingleSelectionIndicator: boolean =\n    this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n\n  /** Placeholder to be shown if no value has been selected. */\n  @Input()\n  get placeholder(): string {\n    return this._placeholder;\n  }\n  set placeholder(value: string) {\n    this._placeholder = value;\n    this.stateChanges.next();\n  }\n  private _placeholder: string;\n\n  /** Whether the component is required. */\n  @Input({transform: booleanAttribute})\n  get required(): boolean {\n    return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n  }\n  set required(value: boolean) {\n    this._required = value;\n    this.stateChanges.next();\n  }\n  private _required: boolean | undefined;\n\n  /** Whether the user should be allowed to select multiple options. */\n  @Input({transform: booleanAttribute})\n  get multiple(): boolean {\n    return this._multiple;\n  }\n  set multiple(value: boolean) {\n    if (this._selectionModel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatSelectDynamicMultipleError();\n    }\n\n    this._multiple = value;\n  }\n  private _multiple: boolean = false;\n\n  /** Whether to center the active option over the trigger. */\n  @Input({transform: booleanAttribute})\n  disableOptionCentering = this._defaultOptions?.disableOptionCentering ?? false;\n\n  /**\n   * Function to compare the option values with the selected values. The first argument\n   * is a value from an option. The second is a value from the selection. A boolean\n   * should be returned.\n   */\n  @Input()\n  get compareWith() {\n    return this._compareWith;\n  }\n  set compareWith(fn: (o1: any, o2: any) => boolean) {\n    if (typeof fn !== 'function' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatSelectNonFunctionValueError();\n    }\n    this._compareWith = fn;\n    if (this._selectionModel) {\n      // A different comparator means the selection could change.\n      this._initializeSelection();\n    }\n  }\n\n  /** Value of the select control. */\n  @Input()\n  get value(): any {\n    return this._value;\n  }\n  set value(newValue: any) {\n    const hasAssigned = this._assignValue(newValue);\n\n    if (hasAssigned) {\n      this._onChange(newValue);\n    }\n  }\n  private _value: any;\n\n  /** Aria label of the select. */\n  @Input('aria-label') ariaLabel: string = '';\n\n  /** Input that can be used to specify the `aria-labelledby` attribute. */\n  @Input('aria-labelledby') ariaLabelledby: string;\n\n  /** Object used to control when error messages are shown. */\n  @Input()\n  get errorStateMatcher() {\n    return this._errorStateTracker.matcher;\n  }\n  set errorStateMatcher(value: ErrorStateMatcher) {\n    this._errorStateTracker.matcher = value;\n  }\n\n  /** Time to wait in milliseconds after the last keystroke before moving focus to an item. */\n  @Input({transform: numberAttribute})\n  typeaheadDebounceInterval: number;\n\n  /**\n   * Function used to sort the values in a select in multiple mode.\n   * Follows the same logic as `Array.prototype.sort`.\n   */\n  @Input() sortComparator: (a: MatOption, b: MatOption, options: MatOption[]) => number;\n\n  /** Unique id of the element. */\n  @Input()\n  get id(): string {\n    return this._id;\n  }\n  set id(value: string) {\n    this._id = value || this._uid;\n    this.stateChanges.next();\n  }\n  private _id: string;\n\n  /** Whether the select is in an error state. */\n  get errorState() {\n    return this._errorStateTracker.errorState;\n  }\n  set errorState(value: boolean) {\n    this._errorStateTracker.errorState = value;\n  }\n\n  /**\n   * Width of the panel. If set to `auto`, the panel will match the trigger width.\n   * If set to null or an empty string, the panel will grow to match the longest option's text.\n   */\n  @Input() panelWidth: string | number | null =\n    this._defaultOptions && typeof this._defaultOptions.panelWidth !== 'undefined'\n      ? this._defaultOptions.panelWidth\n      : 'auto';\n\n  private _initialized = new Subject();\n\n  /** Combined stream of all of the child options' change events. */\n  readonly optionSelectionChanges: Observable<MatOptionSelectionChange> = defer(() => {\n    const options = this.options;\n\n    if (options) {\n      return options.changes.pipe(\n        startWith(options),\n        switchMap(() => merge(...options.map(option => option.onSelectionChange))),\n      );\n    }\n\n    return this._initialized.pipe(switchMap(() => this.optionSelectionChanges));\n  });\n\n  /** Event emitted when the select panel has been toggled. */\n  @Output() readonly openedChange: EventEmitter<boolean> = new EventEmitter<boolean>();\n\n  /** Event emitted when the select has been opened. */\n  @Output('opened') readonly _openedStream: Observable<void> = this.openedChange.pipe(\n    filter(o => o),\n    map(() => {}),\n  );\n\n  /** Event emitted when the select has been closed. */\n  @Output('closed') readonly _closedStream: Observable<void> = this.openedChange.pipe(\n    filter(o => !o),\n    map(() => {}),\n  );\n\n  /** Event emitted when the selected value has been changed by the user. */\n  @Output() readonly selectionChange = new EventEmitter<MatSelectChange>();\n\n  /**\n   * Event that emits whenever the raw value of the select changes. This is here primarily\n   * to facilitate the two-way binding for the `value` input.\n   * @docs-private\n   */\n  @Output() readonly valueChange: EventEmitter<any> = new EventEmitter<any>();\n\n  constructor(\n    protected _viewportRuler: ViewportRuler,\n    protected _changeDetectorRef: ChangeDetectorRef,\n    /**\n     * @deprecated Unused param, will be removed.\n     * @breaking-change 19.0.0\n     */\n    _unusedNgZone: NgZone,\n    defaultErrorStateMatcher: ErrorStateMatcher,\n    readonly _elementRef: ElementRef,\n    @Optional() private _dir: Directionality,\n    @Optional() parentForm: NgForm,\n    @Optional() parentFormGroup: FormGroupDirective,\n    @Optional() @Inject(MAT_FORM_FIELD) protected _parentFormField: MatFormField,\n    @Self() @Optional() public ngControl: NgControl,\n    @Attribute('tabindex') tabIndex: string,\n    @Inject(MAT_SELECT_SCROLL_STRATEGY) scrollStrategyFactory: any,\n    private _liveAnnouncer: LiveAnnouncer,\n    @Optional() @Inject(MAT_SELECT_CONFIG) protected _defaultOptions?: MatSelectConfig,\n  ) {\n    if (this.ngControl) {\n      // Note: we provide the value accessor through here, instead of\n      // the `providers` to avoid running into a circular import.\n      this.ngControl.valueAccessor = this;\n    }\n\n    // Note that we only want to set this when the defaults pass it in, otherwise it should\n    // stay as `undefined` so that it falls back to the default in the key manager.\n    if (_defaultOptions?.typeaheadDebounceInterval != null) {\n      this.typeaheadDebounceInterval = _defaultOptions.typeaheadDebounceInterval;\n    }\n\n    this._errorStateTracker = new _ErrorStateTracker(\n      defaultErrorStateMatcher,\n      ngControl,\n      parentFormGroup,\n      parentForm,\n      this.stateChanges,\n    );\n    this._scrollStrategyFactory = scrollStrategyFactory;\n    this._scrollStrategy = this._scrollStrategyFactory();\n    this.tabIndex = parseInt(tabIndex) || 0;\n\n    // Force setter to be called in case id was not specified.\n    this.id = this.id;\n  }\n\n  ngOnInit() {\n    this._selectionModel = new SelectionModel<MatOption>(this.multiple);\n    this.stateChanges.next();\n\n    // We need `distinctUntilChanged` here, because some browsers will\n    // fire the animation end event twice for the same animation. See:\n    // https://github.com/angular/angular/issues/24084\n    this._panelDoneAnimatingStream\n      .pipe(distinctUntilChanged(), takeUntil(this._destroy))\n      .subscribe(() => this._panelDoneAnimating(this.panelOpen));\n\n    this._viewportRuler\n      .change()\n      .pipe(takeUntil(this._destroy))\n      .subscribe(() => {\n        if (this.panelOpen) {\n          this._overlayWidth = this._getOverlayWidth(this._preferredOverlayOrigin);\n          this._changeDetectorRef.detectChanges();\n        }\n      });\n  }\n\n  ngAfterContentInit() {\n    this._initialized.next();\n    this._initialized.complete();\n\n    this._initKeyManager();\n\n    this._selectionModel.changed.pipe(takeUntil(this._destroy)).subscribe(event => {\n      event.added.forEach(option => option.select());\n      event.removed.forEach(option => option.deselect());\n    });\n\n    this.options.changes.pipe(startWith(null), takeUntil(this._destroy)).subscribe(() => {\n      this._resetOptions();\n      this._initializeSelection();\n    });\n  }\n\n  ngDoCheck() {\n    const newAriaLabelledby = this._getTriggerAriaLabelledby();\n    const ngControl = this.ngControl;\n\n    // We have to manage setting the `aria-labelledby` ourselves, because part of its value\n    // is computed as a result of a content query which can cause this binding to trigger a\n    // \"changed after checked\" error.\n    if (newAriaLabelledby !== this._triggerAriaLabelledBy) {\n      const element: HTMLElement = this._elementRef.nativeElement;\n      this._triggerAriaLabelledBy = newAriaLabelledby;\n      if (newAriaLabelledby) {\n        element.setAttribute('aria-labelledby', newAriaLabelledby);\n      } else {\n        element.removeAttribute('aria-labelledby');\n      }\n    }\n\n    if (ngControl) {\n      // The disabled state might go out of sync if the form group is swapped out. See #17860.\n      if (this._previousControl !== ngControl.control) {\n        if (\n          this._previousControl !== undefined &&\n          ngControl.disabled !== null &&\n          ngControl.disabled !== this.disabled\n        ) {\n          this.disabled = ngControl.disabled;\n        }\n\n        this._previousControl = ngControl.control;\n      }\n\n      this.updateErrorState();\n    }\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    // Updating the disabled state is handled by the input, but we need to additionally let\n    // the parent form field know to run change detection when the disabled state changes.\n    if (changes['disabled'] || changes['userAriaDescribedBy']) {\n      this.stateChanges.next();\n    }\n\n    if (changes['typeaheadDebounceInterval'] && this._keyManager) {\n      this._keyManager.withTypeAhead(this.typeaheadDebounceInterval);\n    }\n  }\n\n  ngOnDestroy() {\n    this._keyManager?.destroy();\n    this._destroy.next();\n    this._destroy.complete();\n    this.stateChanges.complete();\n    this._clearFromModal();\n  }\n\n  /** Toggles the overlay panel open or closed. */\n  toggle(): void {\n    this.panelOpen ? this.close() : this.open();\n  }\n\n  /** Opens the overlay panel. */\n  open(): void {\n    if (!this._canOpen()) {\n      return;\n    }\n\n    // It's important that we read this as late as possible, because doing so earlier will\n    // return a different element since it's based on queries in the form field which may\n    // not have run yet. Also this needs to be assigned before we measure the overlay width.\n    if (this._parentFormField) {\n      this._preferredOverlayOrigin = this._parentFormField.getConnectedOverlayOrigin();\n    }\n\n    this._overlayWidth = this._getOverlayWidth(this._preferredOverlayOrigin);\n    this._applyModalPanelOwnership();\n    this._panelOpen = true;\n    this._keyManager.withHorizontalOrientation(null);\n    this._highlightCorrectOption();\n    this._changeDetectorRef.markForCheck();\n\n    // Required for the MDC form field to pick up when the overlay has been opened.\n    this.stateChanges.next();\n  }\n\n  /**\n   * Track which modal we have modified the `aria-owns` attribute of. When the combobox trigger is\n   * inside an aria-modal, we apply aria-owns to the parent modal with the `id` of the options\n   * panel. Track the modal we have changed so we can undo the changes on destroy.\n   */\n  private _trackedModal: Element | null = null;\n\n  /**\n   * If the autocomplete trigger is inside of an `aria-modal` element, connect\n   * that modal to the options panel with `aria-owns`.\n   *\n   * For some browser + screen reader combinations, when navigation is inside\n   * of an `aria-modal` element, the screen reader treats everything outside\n   * of that modal as hidden or invisible.\n   *\n   * This causes a problem when the combobox trigger is _inside_ of a modal, because the\n   * options panel is rendered _outside_ of that modal, preventing screen reader navigation\n   * from reaching the panel.\n   *\n   * We can work around this issue by applying `aria-owns` to the modal with the `id` of\n   * the options panel. This effectively communicates to assistive technology that the\n   * options panel is part of the same interaction as the modal.\n   *\n   * At time of this writing, this issue is present in VoiceOver.\n   * See https://github.com/angular/components/issues/20694\n   */\n  private _applyModalPanelOwnership() {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n    // the `LiveAnnouncer` and any other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const modal = this._elementRef.nativeElement.closest(\n      'body > .cdk-overlay-container [aria-modal=\"true\"]',\n    );\n\n    if (!modal) {\n      // Most commonly, the autocomplete trigger is not inside a modal.\n      return;\n    }\n\n    const panelId = `${this.id}-panel`;\n\n    if (this._trackedModal) {\n      removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n    }\n\n    addAriaReferencedId(modal, 'aria-owns', panelId);\n    this._trackedModal = modal;\n  }\n\n  /** Clears the reference to the listbox overlay element from the modal it was added to. */\n  private _clearFromModal() {\n    if (!this._trackedModal) {\n      // Most commonly, the autocomplete trigger is not used inside a modal.\n      return;\n    }\n\n    const panelId = `${this.id}-panel`;\n\n    removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n    this._trackedModal = null;\n  }\n\n  /** Closes the overlay panel and focuses the host element. */\n  close(): void {\n    if (this._panelOpen) {\n      this._panelOpen = false;\n      this._keyManager.withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr');\n      this._changeDetectorRef.markForCheck();\n      this._onTouched();\n      // Required for the MDC form field to pick up when the overlay has been closed.\n      this.stateChanges.next();\n    }\n  }\n\n  /**\n   * Sets the select's value. Part of the ControlValueAccessor interface\n   * required to integrate with Angular's core forms API.\n   *\n   * @param value New value to be written to the model.\n   */\n  writeValue(value: any): void {\n    this._assignValue(value);\n  }\n\n  /**\n   * Saves a callback function to be invoked when the select's value\n   * changes from user input. Part of the ControlValueAccessor interface\n   * required to integrate with Angular's core forms API.\n   *\n   * @param fn Callback to be triggered when the value changes.\n   */\n  registerOnChange(fn: (value: any) => void): void {\n    this._onChange = fn;\n  }\n\n  /**\n   * Saves a callback function to be invoked when the select is blurred\n   * by the user. Part of the ControlValueAccessor interface required\n   * to integrate with Angular's core forms API.\n   *\n   * @param fn Callback to be triggered when the component has been touched.\n   */\n  registerOnTouched(fn: () => {}): void {\n    this._onTouched = fn;\n  }\n\n  /**\n   * Disables the select. Part of the ControlValueAccessor interface required\n   * to integrate with Angular's core forms API.\n   *\n   * @param isDisabled Sets whether the component is disabled.\n   */\n  setDisabledState(isDisabled: boolean): void {\n    this.disabled = isDisabled;\n    this._changeDetectorRef.markForCheck();\n    this.stateChanges.next();\n  }\n\n  /** Whether or not the overlay panel is open. */\n  get panelOpen(): boolean {\n    return this._panelOpen;\n  }\n\n  /** The currently selected option. */\n  get selected(): MatOption | MatOption[] {\n    return this.multiple ? this._selectionModel?.selected || [] : this._selectionModel?.selected[0];\n  }\n\n  /** The value displayed in the trigger. */\n  get triggerValue(): string {\n    if (this.empty) {\n      return '';\n    }\n\n    if (this._multiple) {\n      const selectedOptions = this._selectionModel.selected.map(option => option.viewValue);\n\n      if (this._isRtl()) {\n        selectedOptions.reverse();\n      }\n\n      // TODO(crisbeto): delimiter should be configurable for proper localization.\n      return selectedOptions.join(', ');\n    }\n\n    return this._selectionModel.selected[0].viewValue;\n  }\n\n  /** Refreshes the error state of the select. */\n  updateErrorState() {\n    this._errorStateTracker.updateErrorState();\n  }\n\n  /** Whether the element is in RTL mode. */\n  _isRtl(): boolean {\n    return this._dir ? this._dir.value === 'rtl' : false;\n  }\n\n  /** Handles all keydown events on the select. */\n  _handleKeydown(event: KeyboardEvent): void {\n    if (!this.disabled) {\n      this.panelOpen ? this._handleOpenKeydown(event) : this._handleClosedKeydown(event);\n    }\n  }\n\n  /** Handles keyboard events while the select is closed. */\n  private _handleClosedKeydown(event: KeyboardEvent): void {\n    const keyCode = event.keyCode;\n    const isArrowKey =\n      keyCode === DOWN_ARROW ||\n      keyCode === UP_ARROW ||\n      keyCode === LEFT_ARROW ||\n      keyCode === RIGHT_ARROW;\n    const isOpenKey = keyCode === ENTER || keyCode === SPACE;\n    const manager = this._keyManager;\n\n    // Open the select on ALT + arrow key to match the native <select>\n    if (\n      (!manager.isTyping() && isOpenKey && !hasModifierKey(event)) ||\n      ((this.multiple || event.altKey) && isArrowKey)\n    ) {\n      event.preventDefault(); // prevents the page from scrolling down when pressing space\n      this.open();\n    } else if (!this.multiple) {\n      const previouslySelectedOption = this.selected;\n      manager.onKeydown(event);\n      const selectedOption = this.selected;\n\n      // Since the value has changed, we need to announce it ourselves.\n      if (selectedOption && previouslySelectedOption !== selectedOption) {\n        // We set a duration on the live announcement, because we want the live element to be\n        // cleared after a while so that users can't navigate to it using the arrow keys.\n        this._liveAnnouncer.announce((selectedOption as MatOption).viewValue, 10000);\n      }\n    }\n  }\n\n  /** Handles keyboard events when the selected is open. */\n  private _handleOpenKeydown(event: KeyboardEvent): void {\n    const manager = this._keyManager;\n    const keyCode = event.keyCode;\n    const isArrowKey = keyCode === DOWN_ARROW || keyCode === UP_ARROW;\n    const isTyping = manager.isTyping();\n\n    if (isArrowKey && event.altKey) {\n      // Close the select on ALT + arrow key to match the native <select>\n      event.preventDefault();\n      this.close();\n      // Don't do anything in this case if the user is typing,\n      // because the typing sequence can include the space key.\n    } else if (\n      !isTyping &&\n      (keyCode === ENTER || keyCode === SPACE) &&\n      manager.activeItem &&\n      !hasModifierKey(event)\n    ) {\n      event.preventDefault();\n      manager.activeItem._selectViaInteraction();\n    } else if (!isTyping && this._multiple && keyCode === A && event.ctrlKey) {\n      event.preventDefault();\n      const hasDeselectedOptions = this.options.some(opt => !opt.disabled && !opt.selected);\n\n      this.options.forEach(option => {\n        if (!option.disabled) {\n          hasDeselectedOptions ? option.select() : option.deselect();\n        }\n      });\n    } else {\n      const previouslyFocusedIndex = manager.activeItemIndex;\n\n      manager.onKeydown(event);\n\n      if (\n        this._multiple &&\n        isArrowKey &&\n        event.shiftKey &&\n        manager.activeItem &&\n        manager.activeItemIndex !== previouslyFocusedIndex\n      ) {\n        manager.activeItem._selectViaInteraction();\n      }\n    }\n  }\n\n  _onFocus() {\n    if (!this.disabled) {\n      this._focused = true;\n      this.stateChanges.next();\n    }\n  }\n\n  /**\n   * Calls the touched callback only if the panel is closed. Otherwise, the trigger will\n   * \"blur\" to the panel when it opens, causing a false positive.\n   */\n  _onBlur() {\n    this._focused = false;\n    this._keyManager?.cancelTypeahead();\n\n    if (!this.disabled && !this.panelOpen) {\n      this._onTouched();\n      this._changeDetectorRef.markForCheck();\n      this.stateChanges.next();\n    }\n  }\n\n  /**\n   * Callback that is invoked when the overlay panel has been attached.\n   */\n  _onAttached(): void {\n    this._overlayDir.positionChange.pipe(take(1)).subscribe(() => {\n      this._changeDetectorRef.detectChanges();\n      this._positioningSettled();\n    });\n  }\n\n  /** Returns the theme to be used on the panel. */\n  _getPanelTheme(): string {\n    return this._parentFormField ? `mat-${this._parentFormField.color}` : '';\n  }\n\n  /** Whether the select has a value. */\n  get empty(): boolean {\n    return !this._selectionModel || this._selectionModel.isEmpty();\n  }\n\n  private _initializeSelection(): void {\n    // Defer setting the value in order to avoid the \"Expression\n    // has changed after it was checked\" errors from Angular.\n    Promise.resolve().then(() => {\n      if (this.ngControl) {\n        this._value = this.ngControl.value;\n      }\n\n      this._setSelectionByValue(this._value);\n      this.stateChanges.next();\n    });\n  }\n\n  /**\n   * Sets the selected option based on a value. If no option can be\n   * found with the designated value, the select trigger is cleared.\n   */\n  private _setSelectionByValue(value: any | any[]): void {\n    this.options.forEach(option => option.setInactiveStyles());\n    this._selectionModel.clear();\n\n    if (this.multiple && value) {\n      if (!Array.isArray(value) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getMatSelectNonArrayValueError();\n      }\n\n      value.forEach((currentValue: any) => this._selectOptionByValue(currentValue));\n      this._sortValues();\n    } else {\n      const correspondingOption = this._selectOptionByValue(value);\n\n      // Shift focus to the active item. Note that we shouldn't do this in multiple\n      // mode, because we don't know what option the user interacted with last.\n      if (correspondingOption) {\n        this._keyManager.updateActiveItem(correspondingOption);\n      } else if (!this.panelOpen) {\n        // Otherwise reset the highlighted option. Note that we only want to do this while\n        // closed, because doing it while open can shift the user's focus unnecessarily.\n        this._keyManager.updateActiveItem(-1);\n      }\n    }\n\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /**\n   * Finds and selects and option based on its value.\n   * @returns Option that has the corresponding value.\n   */\n  private _selectOptionByValue(value: any): MatOption | undefined {\n    const correspondingOption = this.options.find((option: MatOption) => {\n      // Skip options that are already in the model. This allows us to handle cases\n      // where the same primitive value is selected multiple times.\n      if (this._selectionModel.isSelected(option)) {\n        return false;\n      }\n\n      try {\n        // Treat null as a special reset value.\n        return option.value != null && this._compareWith(option.value, value);\n      } catch (error) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          // Notify developers of errors in their comparator.\n          console.warn(error);\n        }\n        return false;\n      }\n    });\n\n    if (correspondingOption) {\n      this._selectionModel.select(correspondingOption);\n    }\n\n    return correspondingOption;\n  }\n\n  /** Assigns a specific value to the select. Returns whether the value has changed. */\n  private _assignValue(newValue: any | any[]): boolean {\n    // Always re-assign an array, because it might have been mutated.\n    if (newValue !== this._value || (this._multiple && Array.isArray(newValue))) {\n      if (this.options) {\n        this._setSelectionByValue(newValue);\n      }\n\n      this._value = newValue;\n      return true;\n    }\n    return false;\n  }\n\n  // `skipPredicate` determines if key manager should avoid putting a given option in the tab\n  // order. Allow disabled list items to receive focus via keyboard to align with WAI ARIA\n  // recommendation.\n  //\n  // Normally WAI ARIA's instructions are to exclude disabled items from the tab order, but it\n  // makes a few exceptions for compound widgets.\n  //\n  // From [Developing a Keyboard Interface](\n  // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n  //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n  //   Listbox...\"\n  //\n  // The user can focus disabled options using the keyboard, but the user cannot click disabled\n  // options.\n  private _skipPredicate = (option: MatOption) => {\n    if (this.panelOpen) {\n      // Support keyboard focusing disabled options in an ARIA listbox.\n      return false;\n    }\n\n    // When the panel is closed, skip over disabled options. Support options via the UP/DOWN arrow\n    // keys on a closed select. ARIA listbox interaction pattern is less relevant when the panel is\n    // closed.\n    return option.disabled;\n  };\n\n  /** Gets how wide the overlay panel should be. */\n  private _getOverlayWidth(\n    preferredOrigin: ElementRef<ElementRef> | CdkOverlayOrigin | undefined,\n  ): string | number {\n    if (this.panelWidth === 'auto') {\n      const refToMeasure =\n        preferredOrigin instanceof CdkOverlayOrigin\n          ? preferredOrigin.elementRef\n          : preferredOrigin || this._elementRef;\n      return refToMeasure.nativeElement.getBoundingClientRect().width;\n    }\n\n    return this.panelWidth === null ? '' : this.panelWidth;\n  }\n  /** Syncs the parent state with the individual options. */\n  _syncParentProperties(): void {\n    if (this.options) {\n      for (const option of this.options) {\n        option._changeDetectorRef.markForCheck();\n      }\n    }\n  }\n\n  /** Sets up a key manager to listen to keyboard events on the overlay panel. */\n  private _initKeyManager() {\n    this._keyManager = new ActiveDescendantKeyManager<MatOption>(this.options)\n      .withTypeAhead(this.typeaheadDebounceInterval)\n      .withVerticalOrientation()\n      .withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr')\n      .withHomeAndEnd()\n      .withPageUpDown()\n      .withAllowedModifierKeys(['shiftKey'])\n      .skipPredicate(this._skipPredicate);\n\n    this._keyManager.tabOut.subscribe(() => {\n      if (this.panelOpen) {\n        // Select the active item when tabbing away. This is consistent with how the native\n        // select behaves. Note that we only want to do this in single selection mode.\n        if (!this.multiple && this._keyManager.activeItem) {\n          this._keyManager.activeItem._selectViaInteraction();\n        }\n\n        // Restore focus to the trigger before closing. Ensures that the focus\n        // position won't be lost if the user got focus into the overlay.\n        this.focus();\n        this.close();\n      }\n    });\n\n    this._keyManager.change.subscribe(() => {\n      if (this._panelOpen && this.panel) {\n        this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n      } else if (!this._panelOpen && !this.multiple && this._keyManager.activeItem) {\n        this._keyManager.activeItem._selectViaInteraction();\n      }\n    });\n  }\n\n  /** Drops current option subscriptions and IDs and resets from scratch. */\n  private _resetOptions(): void {\n    const changedOrDestroyed = merge(this.options.changes, this._destroy);\n\n    this.optionSelectionChanges.pipe(takeUntil(changedOrDestroyed)).subscribe(event => {\n      this._onSelect(event.source, event.isUserInput);\n\n      if (event.isUserInput && !this.multiple && this._panelOpen) {\n        this.close();\n        this.focus();\n      }\n    });\n\n    // Listen to changes in the internal state of the options and react accordingly.\n    // Handles cases like the labels of the selected options changing.\n    merge(...this.options.map(option => option._stateChanges))\n      .pipe(takeUntil(changedOrDestroyed))\n      .subscribe(() => {\n        // `_stateChanges` can fire as a result of a change in the label's DOM value which may\n        // be the result of an expression changing. We have to use `detectChanges` in order\n        // to avoid \"changed after checked\" errors (see #14793).\n        this._changeDetectorRef.detectChanges();\n        this.stateChanges.next();\n      });\n  }\n\n  /** Invoked when an option is clicked. */\n  private _onSelect(option: MatOption, isUserInput: boolean): void {\n    const wasSelected = this._selectionModel.isSelected(option);\n\n    if (option.value == null && !this._multiple) {\n      option.deselect();\n      this._selectionModel.clear();\n\n      if (this.value != null) {\n        this._propagateChanges(option.value);\n      }\n    } else {\n      if (wasSelected !== option.selected) {\n        option.selected\n          ? this._selectionModel.select(option)\n          : this._selectionModel.deselect(option);\n      }\n\n      if (isUserInput) {\n        this._keyManager.setActiveItem(option);\n      }\n\n      if (this.multiple) {\n        this._sortValues();\n\n        if (isUserInput) {\n          // In case the user selected the option with their mouse, we\n          // want to restore focus back to the trigger, in order to\n          // prevent the select keyboard controls from clashing with\n          // the ones from `mat-option`.\n          this.focus();\n        }\n      }\n    }\n\n    if (wasSelected !== this._selectionModel.isSelected(option)) {\n      this._propagateChanges();\n    }\n\n    this.stateChanges.next();\n  }\n\n  /** Sorts the selected values in the selected based on their order in the panel. */\n  private _sortValues() {\n    if (this.multiple) {\n      const options = this.options.toArray();\n\n      this._selectionModel.sort((a, b) => {\n        return this.sortComparator\n          ? this.sortComparator(a, b, options)\n          : options.indexOf(a) - options.indexOf(b);\n      });\n      this.stateChanges.next();\n    }\n  }\n\n  /** Emits change event to set the model value. */\n  private _propagateChanges(fallbackValue?: any): void {\n    let valueToEmit: any;\n\n    if (this.multiple) {\n      valueToEmit = (this.selected as MatOption[]).map(option => option.value);\n    } else {\n      valueToEmit = this.selected ? (this.selected as MatOption).value : fallbackValue;\n    }\n\n    this._value = valueToEmit;\n    this.valueChange.emit(valueToEmit);\n    this._onChange(valueToEmit);\n    this.selectionChange.emit(this._getChangeEvent(valueToEmit));\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /**\n   * Highlights the selected item. If no option is selected, it will highlight\n   * the first *enabled* option.\n   */\n  private _highlightCorrectOption(): void {\n    if (this._keyManager) {\n      if (this.empty) {\n        // Find the index of the first *enabled* option. Avoid calling `_keyManager.setActiveItem`\n        // because it activates the first option that passes the skip predicate, rather than the\n        // first *enabled* option.\n        let firstEnabledOptionIndex = -1;\n        for (let index = 0; index < this.options.length; index++) {\n          const option = this.options.get(index)!;\n          if (!option.disabled) {\n            firstEnabledOptionIndex = index;\n            break;\n          }\n        }\n\n        this._keyManager.setActiveItem(firstEnabledOptionIndex);\n      } else {\n        this._keyManager.setActiveItem(this._selectionModel.selected[0]);\n      }\n    }\n  }\n\n  /** Whether the panel is allowed to open. */\n  protected _canOpen(): boolean {\n    return !this._panelOpen && !this.disabled && this.options?.length > 0;\n  }\n\n  /** Focuses the select element. */\n  focus(options?: FocusOptions): void {\n    this._elementRef.nativeElement.focus(options);\n  }\n\n  /** Gets the aria-labelledby for the select panel. */\n  _getPanelAriaLabelledby(): string | null {\n    if (this.ariaLabel) {\n      return null;\n    }\n\n    const labelId = this._parentFormField?.getLabelId();\n    const labelExpression = labelId ? labelId + ' ' : '';\n    return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n  }\n\n  /** Determines the `aria-activedescendant` to be set on the host. */\n  _getAriaActiveDescendant(): string | null {\n    if (this.panelOpen && this._keyManager && this._keyManager.activeItem) {\n      return this._keyManager.activeItem.id;\n    }\n\n    return null;\n  }\n\n  /** Gets the aria-labelledby of the select component trigger. */\n  private _getTriggerAriaLabelledby(): string | null {\n    if (this.ariaLabel) {\n      return null;\n    }\n\n    const labelId = this._parentFormField?.getLabelId();\n    let value = (labelId ? labelId + ' ' : '') + this._valueId;\n\n    if (this.ariaLabelledby) {\n      value += ' ' + this.ariaLabelledby;\n    }\n\n    return value;\n  }\n\n  /** Called when the overlay panel is done animating. */\n  protected _panelDoneAnimating(isOpen: boolean) {\n    this.openedChange.emit(isOpen);\n  }\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  setDescribedByIds(ids: string[]) {\n    if (ids.length) {\n      this._elementRef.nativeElement.setAttribute('aria-describedby', ids.join(' '));\n    } else {\n      this._elementRef.nativeElement.removeAttribute('aria-describedby');\n    }\n  }\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  onContainerClick() {\n    this.focus();\n    this.open();\n  }\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get shouldLabelFloat(): boolean {\n    // Since the panel doesn't overlap the trigger, we\n    // want the label to only float when there's a value.\n    return this.panelOpen || !this.empty || (this.focused && !!this.placeholder);\n  }\n}\n\n/**\n * Allows the user to customize the trigger that is displayed when the select has a value.\n */\n@Directive({\n  selector: 'mat-select-trigger',\n  providers: [{provide: MAT_SELECT_TRIGGER, useExisting: MatSelectTrigger}],\n  standalone: true,\n})\nexport class MatSelectTrigger {}\n", "<div cdk-overlay-origin\n     class=\"mat-mdc-select-trigger\"\n     (click)=\"open()\"\n     #fallbackOverlayOrigin=\"cdkOverlayOrigin\"\n     #trigger>\n\n  <div class=\"mat-mdc-select-value\" [attr.id]=\"_valueId\">\n    @if (empty) {\n      <span class=\"mat-mdc-select-placeholder mat-mdc-select-min-line\">{{placeholder}}</span>\n    } @else {\n      <span class=\"mat-mdc-select-value-text\">\n        @if (customTrigger) {\n          <ng-content select=\"mat-select-trigger\"></ng-content>\n        } @else {\n          <span class=\"mat-mdc-select-min-line\">{{triggerValue}}</span>\n        }\n      </span>\n    }\n  </div>\n\n  <div class=\"mat-mdc-select-arrow-wrapper\">\n    <div class=\"mat-mdc-select-arrow\">\n      <!-- Use an inline SVG, because it works better than a CSS triangle in high contrast mode. -->\n      <svg viewBox=\"0 0 24 24\" width=\"24px\" height=\"24px\" focusable=\"false\" aria-hidden=\"true\">\n        <path d=\"M7 10l5 5 5-5z\"/>\n      </svg>\n    </div>\n  </div>\n</div>\n\n<ng-template\n  cdk-connected-overlay\n  cdkConnectedOverlayLockPosition\n  cdkConnectedOverlayHasBackdrop\n  cdkConnectedOverlayBackdropClass=\"cdk-overlay-transparent-backdrop\"\n  [cdkConnectedOverlayPanelClass]=\"_overlayPanelClass\"\n  [cdkConnectedOverlayScrollStrategy]=\"_scrollStrategy\"\n  [cdkConnectedOverlayOrigin]=\"_preferredOverlayOrigin || fallbackOverlayOrigin\"\n  [cdkConnectedOverlayOpen]=\"panelOpen\"\n  [cdkConnectedOverlayPositions]=\"_positions\"\n  [cdkConnectedOverlayWidth]=\"_overlayWidth\"\n  (backdropClick)=\"close()\"\n  (attach)=\"_onAttached()\"\n  (detach)=\"close()\">\n  <div\n    #panel\n    role=\"listbox\"\n    tabindex=\"-1\"\n    class=\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open {{ _getPanelTheme() }}\"\n    [attr.id]=\"id + '-panel'\"\n    [attr.aria-multiselectable]=\"multiple\"\n    [attr.aria-label]=\"ariaLabel || null\"\n    [attr.aria-labelledby]=\"_getPanelAriaLabelledby()\"\n    [ngClass]=\"panelClass\"\n    [@transformPanel]=\"'showing'\"\n    (@transformPanel.done)=\"_panelDoneAnimatingStream.next($event.toState)\"\n    (keydown)=\"_handleKeydown($event)\">\n    <ng-content></ng-content>\n  </div>\n</ng-template>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {OverlayModule} from '@angular/cdk/overlay';\nimport {CommonModule} from '@angular/common';\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule, MatOptionModule} from '@angular/material/core';\nimport {MatFormFieldModule} from '@angular/material/form-field';\nimport {CdkScrollableModule} from '@angular/cdk/scrolling';\nimport {MatSelect, MatSelectTrigger, MAT_SELECT_SCROLL_STRATEGY_PROVIDER} from './select';\n\n@NgModule({\n  imports: [\n    CommonModule,\n    OverlayModule,\n    MatOptionModule,\n    MatCommonModule,\n    MatSelect,\n    MatSelectTrigger,\n  ],\n  exports: [\n    CdkScrollableModule,\n    MatFormFieldModule,\n    MatSelect,\n    MatSelectTrigger,\n    MatOptionModule,\n    MatCommonModule,\n  ],\n  providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER],\n})\nexport class MatSelectModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAmBA;;;;;;AAMG;AACU,MAAA,mBAAmB,GAO5B;AACF;;;;AAIG;AACH,IAAA,kBAAkB,EAAE,OAAO,CAAC,oBAAoB,EAAE;AAChD,QAAA,UAAU,CAAC,WAAW,EAAE,KAAK,CAAC,iBAAiB,EAAE,CAAC,YAAY,EAAE,CAAC,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;KACtF,CAAC;;AAGF,IAAA,cAAc,EAAE,OAAO,CAAC,gBAAgB,EAAE;AACxC,QAAA,KAAK,CACH,MAAM,EACN,KAAK,CAAC;AACJ,YAAA,OAAO,EAAE,CAAC;AACV,YAAA,SAAS,EAAE,eAAe;AAC3B,SAAA,CAAC,CACH;QACD,UAAU,CACR,iBAAiB,EACjB,OAAO,CACL,kCAAkC,EAClC,KAAK,CAAC;AACJ,YAAA,OAAO,EAAE,CAAC;AACV,YAAA,SAAS,EAAE,aAAa;AACzB,SAAA,CAAC,CACH,CACF;AACD,QAAA,UAAU,CAAC,WAAW,EAAE,OAAO,CAAC,cAAc,EAAE,KAAK,CAAC,EAAC,OAAO,EAAE,CAAC,EAAC,CAAC,CAAC,CAAC;KACtE,CAAC;;;ACvDJ;AACA;AAEA;;;;AAIG;SACa,gCAAgC,GAAA;AAC9C,IAAA,OAAO,KAAK,CAAC,+DAA+D,CAAC,CAAC;AAChF,CAAC;AAED;;;;;AAKG;SACa,8BAA8B,GAAA;AAC5C,IAAA,OAAO,KAAK,CAAC,oDAAoD,CAAC,CAAC;AACrE,CAAC;AAED;;;;AAIG;SACa,iCAAiC,GAAA;AAC/C,IAAA,OAAO,KAAK,CAAC,mCAAmC,CAAC,CAAC;AACpD;;ACiEA,IAAI,YAAY,GAAG,CAAC,CAAC;AAErB;MACa,0BAA0B,GAAG,IAAI,cAAc,CAC1D,4BAA4B,EAC5B;AACE,IAAA,UAAU,EAAE,MAAM;IAClB,OAAO,EAAE,MAAK;AACZ,QAAA,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;QAChC,OAAO,MAAM,OAAO,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;KACpD;AACF,CAAA,EACD;AAEF;AACM,SAAU,2CAA2C,CACzD,OAAgB,EAAA;IAEhB,OAAO,MAAM,OAAO,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;AACrD,CAAC;AAuBD;MACa,iBAAiB,GAAG,IAAI,cAAc,CAAkB,mBAAmB,EAAE;AAE1F;AACa,MAAA,mCAAmC,GAAG;AACjD,IAAA,OAAO,EAAE,0BAA0B;IACnC,IAAI,EAAE,CAAC,OAAO,CAAC;AACf,IAAA,UAAU,EAAE,2CAA2C;EACvD;AAEF;;;;AAIG;MACU,kBAAkB,GAAG,IAAI,cAAc,CAAmB,kBAAkB,EAAE;AAE3F;MACa,eAAe,CAAA;AAC1B,IAAA,WAAA;;IAES,MAAiB;;IAEjB,KAAU,EAAA;QAFV,IAAM,CAAA,MAAA,GAAN,MAAM,CAAW;QAEjB,IAAK,CAAA,KAAA,GAAL,KAAK,CAAK;KACf;AACL,CAAA;MAwCY,SAAS,CAAA;;AAyDpB,IAAA,qBAAqB,CAAC,KAAa,EAAA;QACjC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC;QAE7C,IAAI,MAAM,EAAE;AACV,YAAA,MAAM,KAAK,GAAgB,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;AACpD,YAAA,MAAM,UAAU,GAAG,6BAA6B,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;AACzF,YAAA,MAAM,OAAO,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC;YAEzC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,KAAK,CAAC,EAAE;;;;AAInC,gBAAA,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC;aACrB;iBAAM;gBACL,KAAK,CAAC,SAAS,GAAG,wBAAwB,CACxC,OAAO,CAAC,SAAS,EACjB,OAAO,CAAC,YAAY,EACpB,KAAK,CAAC,SAAS,EACf,KAAK,CAAC,YAAY,CACnB,CAAC;aACH;SACF;KACF;;IAGO,mBAAmB,GAAA;QACzB,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,WAAW,CAAC,eAAe,IAAI,CAAC,CAAC,CAAC;KACnE;;AAGO,IAAA,eAAe,CAAC,KAAU,EAAA;AAChC,QAAA,OAAO,IAAI,eAAe,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KACzC;;AA8ED,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC;KACzC;;AAkCD,IAAA,IACI,4BAA4B,GAAA;QAC9B,OAAO,IAAI,CAAC,6BAA6B,CAAC;KAC3C;IACD,IAAI,4BAA4B,CAAC,KAAc,EAAA;AAC7C,QAAA,IAAI,CAAC,6BAA6B,GAAG,KAAK,CAAC;QAC3C,IAAI,CAAC,qBAAqB,EAAE,CAAC;KAC9B;;AAKD,IAAA,IACI,WAAW,GAAA;QACb,OAAO,IAAI,CAAC,YAAY,CAAC;KAC1B;IACD,IAAI,WAAW,CAAC,KAAa,EAAA;AAC3B,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;AAC1B,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;KAC1B;;AAID,IAAA,IACI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,YAAY,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC;KAC9F;IACD,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACvB,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;KAC1B;;AAID,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YAC3E,MAAM,gCAAgC,EAAE,CAAC;SAC1C;AAED,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;KACxB;AAOD;;;;AAIG;AACH,IAAA,IACI,WAAW,GAAA;QACb,OAAO,IAAI,CAAC,YAAY,CAAC;KAC1B;IACD,IAAI,WAAW,CAAC,EAAiC,EAAA;AAC/C,QAAA,IAAI,OAAO,EAAE,KAAK,UAAU,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YAC/E,MAAM,iCAAiC,EAAE,CAAC;SAC3C;AACD,QAAA,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;AACvB,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE;;YAExB,IAAI,CAAC,oBAAoB,EAAE,CAAC;SAC7B;KACF;;AAGD,IAAA,IACI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;KACpB;IACD,IAAI,KAAK,CAAC,QAAa,EAAA;QACrB,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAEhD,IAAI,WAAW,EAAE;AACf,YAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;SAC1B;KACF;;AAUD,IAAA,IACI,iBAAiB,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;KACxC;IACD,IAAI,iBAAiB,CAAC,KAAwB,EAAA;AAC5C,QAAA,IAAI,CAAC,kBAAkB,CAAC,OAAO,GAAG,KAAK,CAAC;KACzC;;AAaD,IAAA,IACI,EAAE,GAAA;QACJ,OAAO,IAAI,CAAC,GAAG,CAAC;KACjB;IACD,IAAI,EAAE,CAAC,KAAa,EAAA;QAClB,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC;AAC9B,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;KAC1B;;AAID,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC;KAC3C;IACD,IAAI,UAAU,CAAC,KAAc,EAAA;AAC3B,QAAA,IAAI,CAAC,kBAAkB,CAAC,UAAU,GAAG,KAAK,CAAC;KAC5C;IAoDD,WACY,CAAA,cAA6B,EAC7B,kBAAqC;AAC/C;;;AAGG;IACH,aAAqB,EACrB,wBAA2C,EAClC,WAAuB,EACZ,IAAoB,EAC5B,UAAkB,EAClB,eAAmC,EACD,gBAA8B,EACjD,SAAoB,EACxB,QAAgB,EACH,qBAA0B,EACtD,cAA6B,EACY,eAAiC,EAAA;QAjBxE,IAAc,CAAA,cAAA,GAAd,cAAc,CAAe;QAC7B,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAmB;QAOtC,IAAW,CAAA,WAAA,GAAX,WAAW,CAAY;QACZ,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAgB;QAGM,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAc;QACjD,IAAS,CAAA,SAAA,GAAT,SAAS,CAAW;QAGvC,IAAc,CAAA,cAAA,GAAd,cAAc,CAAe;QACY,IAAe,CAAA,eAAA,GAAf,eAAe,CAAkB;AA3XpF;;;;;AAKG;AACH,QAAA,IAAA,CAAA,UAAU,GAAwB;AAChC,YAAA;AACE,gBAAA,OAAO,EAAE,OAAO;AAChB,gBAAA,OAAO,EAAE,QAAQ;AACjB,gBAAA,QAAQ,EAAE,OAAO;AACjB,gBAAA,QAAQ,EAAE,KAAK;AAChB,aAAA;AACD,YAAA;AACE,gBAAA,OAAO,EAAE,KAAK;AACd,gBAAA,OAAO,EAAE,QAAQ;AACjB,gBAAA,QAAQ,EAAE,KAAK;AACf,gBAAA,QAAQ,EAAE,KAAK;AAChB,aAAA;AACD,YAAA;AACE,gBAAA,OAAO,EAAE,OAAO;AAChB,gBAAA,OAAO,EAAE,KAAK;AACd,gBAAA,QAAQ,EAAE,OAAO;AACjB,gBAAA,QAAQ,EAAE,QAAQ;AAClB,gBAAA,UAAU,EAAE,4BAA4B;AACzC,aAAA;AACD,YAAA;AACE,gBAAA,OAAO,EAAE,KAAK;AACd,gBAAA,OAAO,EAAE,KAAK;AACd,gBAAA,QAAQ,EAAE,KAAK;AACf,gBAAA,QAAQ,EAAE,QAAQ;AAClB,gBAAA,UAAU,EAAE,4BAA4B;AACzC,aAAA;SACF,CAAC;;QAyCM,IAAU,CAAA,UAAA,GAAG,KAAK,CAAC;;QAGnB,IAAY,CAAA,YAAA,GAAG,CAAC,EAAO,EAAE,EAAO,KAAK,EAAE,KAAK,EAAE,CAAC;;AAG/C,QAAA,IAAA,CAAA,IAAI,GAAG,CAAA,WAAA,EAAc,YAAY,EAAE,EAAE,CAAC;;QAGtC,IAAsB,CAAA,sBAAA,GAAkB,IAAI,CAAC;;AASlC,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,OAAO,EAAQ,CAAC;AAKlD;;;;AAIG;AACM,QAAA,IAAA,CAAA,YAAY,GAAG,IAAI,OAAO,EAAQ,CAAC;AAE5C;;;AAGG;QACM,IAAwB,CAAA,wBAAA,GAAG,IAAI,CAAC;;AAqBzC,QAAA,IAAA,CAAA,SAAS,GAAyB,MAAK,GAAG,CAAC;;AAG3C,QAAA,IAAA,CAAA,UAAU,GAAG,MAAK,GAAG,CAAC;;AAGtB,QAAA,IAAA,CAAA,QAAQ,GAAG,CAAA,iBAAA,EAAoB,YAAY,EAAE,EAAE,CAAC;;AAGvC,QAAA,IAAA,CAAA,yBAAyB,GAAG,IAAI,OAAO,EAAU,CAAC;QAK3D,IAAkB,CAAA,kBAAA,GAAsB,IAAI,CAAC,eAAe,EAAE,iBAAiB,IAAI,EAAE,CAAC;QAM9E,IAAQ,CAAA,QAAA,GAAG,KAAK,CAAC;;QAGzB,IAAW,CAAA,WAAA,GAAG,YAAY,CAAC;;QAiB3B,IAAQ,CAAA,QAAA,GAAY,KAAK,CAAC;;QAI1B,IAAa,CAAA,aAAA,GAAY,KAAK,CAAC;;QAM/B,IAAQ,CAAA,QAAA,GAAW,CAAC,CAAC;QAWb,IAA6B,CAAA,6BAAA,GACnC,IAAI,CAAC,eAAe,EAAE,4BAA4B,IAAI,KAAK,CAAC;QAoCtD,IAAS,CAAA,SAAA,GAAY,KAAK,CAAC;;QAInC,IAAsB,CAAA,sBAAA,GAAG,IAAI,CAAC,eAAe,EAAE,sBAAsB,IAAI,KAAK,CAAC;;QAqC1D,IAAS,CAAA,SAAA,GAAW,EAAE,CAAC;AA2C5C;;;AAGG;AACM,QAAA,IAAA,CAAA,UAAU,GACjB,IAAI,CAAC,eAAe,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,KAAK,WAAW;AAC5E,cAAE,IAAI,CAAC,eAAe,CAAC,UAAU;cAC/B,MAAM,CAAC;AAEL,QAAA,IAAA,CAAA,YAAY,GAAG,IAAI,OAAO,EAAE,CAAC;;AAG5B,QAAA,IAAA,CAAA,sBAAsB,GAAyC,KAAK,CAAC,MAAK;AACjF,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;YAE7B,IAAI,OAAO,EAAE;AACX,gBAAA,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CACzB,SAAS,CAAC,OAAO,CAAC,EAClB,SAAS,CAAC,MAAM,KAAK,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAC3E,CAAC;aACH;AAED,YAAA,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC;AAC9E,SAAC,CAAC,CAAC;;AAGgB,QAAA,IAAA,CAAA,YAAY,GAA0B,IAAI,YAAY,EAAW,CAAC;;QAG1D,IAAa,CAAA,aAAA,GAAqB,IAAI,CAAC,YAAY,CAAC,IAAI,CACjF,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,EACd,GAAG,CAAC,MAAO,GAAC,CAAC,CACd,CAAC;;QAGyB,IAAa,CAAA,aAAA,GAAqB,IAAI,CAAC,YAAY,CAAC,IAAI,CACjF,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EACf,GAAG,CAAC,MAAO,GAAC,CAAC,CACd,CAAC;;AAGiB,QAAA,IAAA,CAAA,eAAe,GAAG,IAAI,YAAY,EAAmB,CAAC;AAEzE;;;;AAIG;AACgB,QAAA,IAAA,CAAA,WAAW,GAAsB,IAAI,YAAY,EAAO,CAAC;AA4K5E;;;;AAIG;QACK,IAAa,CAAA,aAAA,GAAmB,IAAI,CAAC;;;;;;;;;;;;;;;AAoYrC,QAAA,IAAA,CAAA,cAAc,GAAG,CAAC,MAAiB,KAAI;AAC7C,YAAA,IAAI,IAAI,CAAC,SAAS,EAAE;;AAElB,gBAAA,OAAO,KAAK,CAAC;aACd;;;;YAKD,OAAO,MAAM,CAAC,QAAQ,CAAC;AACzB,SAAC,CAAC;AAziBA,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;;;AAGlB,YAAA,IAAI,CAAC,SAAS,CAAC,aAAa,GAAG,IAAI,CAAC;SACrC;;;AAID,QAAA,IAAI,eAAe,EAAE,yBAAyB,IAAI,IAAI,EAAE;AACtD,YAAA,IAAI,CAAC,yBAAyB,GAAG,eAAe,CAAC,yBAAyB,CAAC;SAC5E;AAED,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,kBAAkB,CAC9C,wBAAwB,EACxB,SAAS,EACT,eAAe,EACf,UAAU,EACV,IAAI,CAAC,YAAY,CAClB,CAAC;AACF,QAAA,IAAI,CAAC,sBAAsB,GAAG,qBAAqB,CAAC;AACpD,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QACrD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;;AAGxC,QAAA,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;KACnB;IAED,QAAQ,GAAA;QACN,IAAI,CAAC,eAAe,GAAG,IAAI,cAAc,CAAY,IAAI,CAAC,QAAQ,CAAC,CAAC;AACpE,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;;;;AAKzB,QAAA,IAAI,CAAC,yBAAyB;aAC3B,IAAI,CAAC,oBAAoB,EAAE,EAAE,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACtD,aAAA,SAAS,CAAC,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;AAE7D,QAAA,IAAI,CAAC,cAAc;AAChB,aAAA,MAAM,EAAE;AACR,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC9B,SAAS,CAAC,MAAK;AACd,YAAA,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;AACzE,gBAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC;aACzC;AACH,SAAC,CAAC,CAAC;KACN;IAED,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;AACzB,QAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;QAE7B,IAAI,CAAC,eAAe,EAAE,CAAC;AAEvB,QAAA,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,IAAG;AAC5E,YAAA,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;AAC/C,YAAA,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;AACrD,SAAC,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;YAClF,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC9B,SAAC,CAAC,CAAC;KACJ;IAED,SAAS,GAAA;AACP,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;AAC3D,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;;;;AAKjC,QAAA,IAAI,iBAAiB,KAAK,IAAI,CAAC,sBAAsB,EAAE;AACrD,YAAA,MAAM,OAAO,GAAgB,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;AAC5D,YAAA,IAAI,CAAC,sBAAsB,GAAG,iBAAiB,CAAC;YAChD,IAAI,iBAAiB,EAAE;AACrB,gBAAA,OAAO,CAAC,YAAY,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;aAC5D;iBAAM;AACL,gBAAA,OAAO,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;aAC5C;SACF;QAED,IAAI,SAAS,EAAE;;YAEb,IAAI,IAAI,CAAC,gBAAgB,KAAK,SAAS,CAAC,OAAO,EAAE;AAC/C,gBAAA,IACE,IAAI,CAAC,gBAAgB,KAAK,SAAS;oBACnC,SAAS,CAAC,QAAQ,KAAK,IAAI;AAC3B,oBAAA,SAAS,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,EACpC;AACA,oBAAA,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;iBACpC;AAED,gBAAA,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC,OAAO,CAAC;aAC3C;YAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;SACzB;KACF;AAED,IAAA,WAAW,CAAC,OAAsB,EAAA;;;QAGhC,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,qBAAqB,CAAC,EAAE;AACzD,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;SAC1B;QAED,IAAI,OAAO,CAAC,2BAA2B,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE;YAC5D,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;SAChE;KACF;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,CAAC;AAC5B,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;AACrB,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;AACzB,QAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;QAC7B,IAAI,CAAC,eAAe,EAAE,CAAC;KACxB;;IAGD,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;KAC7C;;IAGD,IAAI,GAAA;AACF,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;YACpB,OAAO;SACR;;;;AAKD,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,gBAAgB,CAAC,yBAAyB,EAAE,CAAC;SAClF;QAED,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACzE,IAAI,CAAC,yBAAyB,EAAE,CAAC;AACjC,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,QAAA,IAAI,CAAC,WAAW,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;QACjD,IAAI,CAAC,uBAAuB,EAAE,CAAC;AAC/B,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;;AAGvC,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;KAC1B;AASD;;;;;;;;;;;;;;;;;;AAkBG;IACK,yBAAyB,GAAA;;;;;;;AAO/B,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,OAAO,CAClD,mDAAmD,CACpD,CAAC;QAEF,IAAI,CAAC,KAAK,EAAE;;YAEV,OAAO;SACR;AAED,QAAA,MAAM,OAAO,GAAG,CAAA,EAAG,IAAI,CAAC,EAAE,QAAQ,CAAC;AAEnC,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,sBAAsB,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;SAClE;AAED,QAAA,mBAAmB,CAAC,KAAK,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;AACjD,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;KAC5B;;IAGO,eAAe,GAAA;AACrB,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;;YAEvB,OAAO;SACR;AAED,QAAA,MAAM,OAAO,GAAG,CAAA,EAAG,IAAI,CAAC,EAAE,QAAQ,CAAC;QAEnC,sBAAsB,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;AACjE,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;KAC3B;;IAGD,KAAK,GAAA;AACH,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AACxB,YAAA,IAAI,CAAC,WAAW,CAAC,yBAAyB,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,GAAG,KAAK,CAAC,CAAC;AAC1E,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;YACvC,IAAI,CAAC,UAAU,EAAE,CAAC;;AAElB,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;SAC1B;KACF;AAED;;;;;AAKG;AACH,IAAA,UAAU,CAAC,KAAU,EAAA;AACnB,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;KAC1B;AAED;;;;;;AAMG;AACH,IAAA,gBAAgB,CAAC,EAAwB,EAAA;AACvC,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;KACrB;AAED;;;;;;AAMG;AACH,IAAA,iBAAiB,CAAC,EAAY,EAAA;AAC5B,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;KACtB;AAED;;;;;AAKG;AACH,IAAA,gBAAgB,CAAC,UAAmB,EAAA;AAClC,QAAA,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC;AAC3B,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACvC,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;KAC1B;;AAGD,IAAA,IAAI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;KACxB;;AAGD,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,EAAE,QAAQ,IAAI,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;KACjG;;AAGD,IAAA,IAAI,YAAY,GAAA;AACd,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,YAAA,OAAO,EAAE,CAAC;SACX;AAED,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC;AAEtF,YAAA,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;gBACjB,eAAe,CAAC,OAAO,EAAE,CAAC;aAC3B;;AAGD,YAAA,OAAO,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACnC;QAED,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;KACnD;;IAGD,gBAAgB,GAAA;AACd,QAAA,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,CAAC;KAC5C;;IAGD,MAAM,GAAA;AACJ,QAAA,OAAO,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,GAAG,KAAK,CAAC;KACtD;;AAGD,IAAA,cAAc,CAAC,KAAoB,EAAA;AACjC,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;SACpF;KACF;;AAGO,IAAA,oBAAoB,CAAC,KAAoB,EAAA;AAC/C,QAAA,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;AAC9B,QAAA,MAAM,UAAU,GACd,OAAO,KAAK,UAAU;AACtB,YAAA,OAAO,KAAK,QAAQ;AACpB,YAAA,OAAO,KAAK,UAAU;YACtB,OAAO,KAAK,WAAW,CAAC;QAC1B,MAAM,SAAS,GAAG,OAAO,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK,CAAC;AACzD,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC;;AAGjC,QAAA,IACE,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,SAAS,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;AAC3D,aAAC,CAAC,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,MAAM,KAAK,UAAU,CAAC,EAC/C;AACA,YAAA,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,IAAI,CAAC,IAAI,EAAE,CAAC;SACb;AAAM,aAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AACzB,YAAA,MAAM,wBAAwB,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC/C,YAAA,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AACzB,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;;AAGrC,YAAA,IAAI,cAAc,IAAI,wBAAwB,KAAK,cAAc,EAAE;;;gBAGjE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAE,cAA4B,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;aAC9E;SACF;KACF;;AAGO,IAAA,kBAAkB,CAAC,KAAoB,EAAA;AAC7C,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC;AACjC,QAAA,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QAC9B,MAAM,UAAU,GAAG,OAAO,KAAK,UAAU,IAAI,OAAO,KAAK,QAAQ,CAAC;AAClE,QAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;AAEpC,QAAA,IAAI,UAAU,IAAI,KAAK,CAAC,MAAM,EAAE;;YAE9B,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,IAAI,CAAC,KAAK,EAAE,CAAC;;;SAGd;AAAM,aAAA,IACL,CAAC,QAAQ;AACT,aAAC,OAAO,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK,CAAC;AACxC,YAAA,OAAO,CAAC,UAAU;AAClB,YAAA,CAAC,cAAc,CAAC,KAAK,CAAC,EACtB;YACA,KAAK,CAAC,cAAc,EAAE,CAAC;AACvB,YAAA,OAAO,CAAC,UAAU,CAAC,qBAAqB,EAAE,CAAC;SAC5C;AAAM,aAAA,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,EAAE;YACxE,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,MAAM,oBAAoB,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AAEtF,YAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,IAAG;AAC5B,gBAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;AACpB,oBAAA,oBAAoB,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;iBAC5D;AACH,aAAC,CAAC,CAAC;SACJ;aAAM;AACL,YAAA,MAAM,sBAAsB,GAAG,OAAO,CAAC,eAAe,CAAC;AAEvD,YAAA,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAEzB,IACE,IAAI,CAAC,SAAS;gBACd,UAAU;AACV,gBAAA,KAAK,CAAC,QAAQ;AACd,gBAAA,OAAO,CAAC,UAAU;AAClB,gBAAA,OAAO,CAAC,eAAe,KAAK,sBAAsB,EAClD;AACA,gBAAA,OAAO,CAAC,UAAU,CAAC,qBAAqB,EAAE,CAAC;aAC5C;SACF;KACF;IAED,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrB,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;SAC1B;KACF;AAED;;;AAGG;IACH,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AACtB,QAAA,IAAI,CAAC,WAAW,EAAE,eAAe,EAAE,CAAC;QAEpC,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACrC,IAAI,CAAC,UAAU,EAAE,CAAC;AAClB,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACvC,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;SAC1B;KACF;AAED;;AAEG;IACH,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;AAC3D,YAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC;YACxC,IAAI,CAAC,mBAAmB,EAAE,CAAC;AAC7B,SAAC,CAAC,CAAC;KACJ;;IAGD,cAAc,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,gBAAgB,GAAG,CAAO,IAAA,EAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAA,CAAE,GAAG,EAAE,CAAC;KAC1E;;AAGD,IAAA,IAAI,KAAK,GAAA;QACP,OAAO,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;KAChE;IAEO,oBAAoB,GAAA;;;AAG1B,QAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;AAC1B,YAAA,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;aACpC;AAED,YAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACvC,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;AAC3B,SAAC,CAAC,CAAC;KACJ;AAED;;;AAGG;AACK,IAAA,oBAAoB,CAAC,KAAkB,EAAA;AAC7C,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,CAAC,CAAC;AAC3D,QAAA,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;AAE7B,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,EAAE;AAC1B,YAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;gBAC5E,MAAM,8BAA8B,EAAE,CAAC;aACxC;AAED,YAAA,KAAK,CAAC,OAAO,CAAC,CAAC,YAAiB,KAAK,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC,CAAC;YAC9E,IAAI,CAAC,WAAW,EAAE,CAAC;SACpB;aAAM;YACL,MAAM,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;;;YAI7D,IAAI,mBAAmB,EAAE;AACvB,gBAAA,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;aACxD;AAAM,iBAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;;;gBAG1B,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;aACvC;SACF;AAED,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;KACxC;AAED;;;AAGG;AACK,IAAA,oBAAoB,CAAC,KAAU,EAAA;QACrC,MAAM,mBAAmB,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAiB,KAAI;;;YAGlE,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;AAC3C,gBAAA,OAAO,KAAK,CAAC;aACd;AAED,YAAA,IAAI;;AAEF,gBAAA,OAAO,MAAM,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;aACvE;YAAC,OAAO,KAAK,EAAE;AACd,gBAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;;AAEjD,oBAAA,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBACrB;AACD,gBAAA,OAAO,KAAK,CAAC;aACd;AACH,SAAC,CAAC,CAAC;QAEH,IAAI,mBAAmB,EAAE;AACvB,YAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;SAClD;AAED,QAAA,OAAO,mBAAmB,CAAC;KAC5B;;AAGO,IAAA,YAAY,CAAC,QAAqB,EAAA;;AAExC,QAAA,IAAI,QAAQ,KAAK,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,SAAS,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE;AAC3E,YAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,gBAAA,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;aACrC;AAED,YAAA,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;AACvB,YAAA,OAAO,IAAI,CAAC;SACb;AACD,QAAA,OAAO,KAAK,CAAC;KACd;;AA6BO,IAAA,gBAAgB,CACtB,eAAsE,EAAA;AAEtE,QAAA,IAAI,IAAI,CAAC,UAAU,KAAK,MAAM,EAAE;AAC9B,YAAA,MAAM,YAAY,GAChB,eAAe,YAAY,gBAAgB;kBACvC,eAAe,CAAC,UAAU;AAC5B,kBAAE,eAAe,IAAI,IAAI,CAAC,WAAW,CAAC;YAC1C,OAAO,YAAY,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC,KAAK,CAAC;SACjE;AAED,QAAA,OAAO,IAAI,CAAC,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC;KACxD;;IAED,qBAAqB,GAAA;AACnB,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;AACjC,gBAAA,MAAM,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;aAC1C;SACF;KACF;;IAGO,eAAe,GAAA;QACrB,IAAI,CAAC,WAAW,GAAG,IAAI,0BAA0B,CAAY,IAAI,CAAC,OAAO,CAAC;AACvE,aAAA,aAAa,CAAC,IAAI,CAAC,yBAAyB,CAAC;AAC7C,aAAA,uBAAuB,EAAE;AACzB,aAAA,yBAAyB,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,GAAG,KAAK,CAAC;AACxD,aAAA,cAAc,EAAE;AAChB,aAAA,cAAc,EAAE;AAChB,aAAA,uBAAuB,CAAC,CAAC,UAAU,CAAC,CAAC;AACrC,aAAA,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEtC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,MAAK;AACrC,YAAA,IAAI,IAAI,CAAC,SAAS,EAAE;;;gBAGlB,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE;AACjD,oBAAA,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,qBAAqB,EAAE,CAAC;iBACrD;;;gBAID,IAAI,CAAC,KAAK,EAAE,CAAC;gBACb,IAAI,CAAC,KAAK,EAAE,CAAC;aACd;AACH,SAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,MAAK;YACrC,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,KAAK,EAAE;gBACjC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,WAAW,CAAC,eAAe,IAAI,CAAC,CAAC,CAAC;aACnE;AAAM,iBAAA,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE;AAC5E,gBAAA,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,qBAAqB,EAAE,CAAC;aACrD;AACH,SAAC,CAAC,CAAC;KACJ;;IAGO,aAAa,GAAA;AACnB,QAAA,MAAM,kBAAkB,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AAEtE,QAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,IAAG;YAChF,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC;AAEhD,YAAA,IAAI,KAAK,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,EAAE;gBAC1D,IAAI,CAAC,KAAK,EAAE,CAAC;gBACb,IAAI,CAAC,KAAK,EAAE,CAAC;aACd;AACH,SAAC,CAAC,CAAC;;;AAIH,QAAA,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,aAAa,CAAC,CAAC;AACvD,aAAA,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;aACnC,SAAS,CAAC,MAAK;;;;AAId,YAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC;AACxC,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;AAC3B,SAAC,CAAC,CAAC;KACN;;IAGO,SAAS,CAAC,MAAiB,EAAE,WAAoB,EAAA;QACvD,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAE5D,IAAI,MAAM,CAAC,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YAC3C,MAAM,CAAC,QAAQ,EAAE,CAAC;AAClB,YAAA,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;AAE7B,YAAA,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE;AACtB,gBAAA,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aACtC;SACF;aAAM;AACL,YAAA,IAAI,WAAW,KAAK,MAAM,CAAC,QAAQ,EAAE;AACnC,gBAAA,MAAM,CAAC,QAAQ;sBACX,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC;sBACnC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;aAC3C;YAED,IAAI,WAAW,EAAE;AACf,gBAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;aACxC;AAED,YAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,IAAI,CAAC,WAAW,EAAE,CAAC;gBAEnB,IAAI,WAAW,EAAE;;;;;oBAKf,IAAI,CAAC,KAAK,EAAE,CAAC;iBACd;aACF;SACF;QAED,IAAI,WAAW,KAAK,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;YAC3D,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAC1B;AAED,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;KAC1B;;IAGO,WAAW,GAAA;AACjB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YAEvC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAI;gBACjC,OAAO,IAAI,CAAC,cAAc;sBACtB,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC;AACpC,sBAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAC9C,aAAC,CAAC,CAAC;AACH,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;SAC1B;KACF;;AAGO,IAAA,iBAAiB,CAAC,aAAmB,EAAA;AAC3C,QAAA,IAAI,WAAgB,CAAC;AAErB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,WAAW,GAAI,IAAI,CAAC,QAAwB,CAAC,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC;SAC1E;aAAM;AACL,YAAA,WAAW,GAAG,IAAI,CAAC,QAAQ,GAAI,IAAI,CAAC,QAAsB,CAAC,KAAK,GAAG,aAAa,CAAC;SAClF;AAED,QAAA,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;AAC1B,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AACnC,QAAA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;AAC5B,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC;AAC7D,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;KACxC;AAED;;;AAGG;IACK,uBAAuB,GAAA;AAC7B,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,IAAI,CAAC,KAAK,EAAE;;;;AAId,gBAAA,IAAI,uBAAuB,GAAG,CAAC,CAAC,CAAC;AACjC,gBAAA,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;oBACxD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAE,CAAC;AACxC,oBAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;wBACpB,uBAAuB,GAAG,KAAK,CAAC;wBAChC,MAAM;qBACP;iBACF;AAED,gBAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC;aACzD;iBAAM;AACL,gBAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;aAClE;SACF;KACF;;IAGS,QAAQ,GAAA;AAChB,QAAA,OAAO,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,EAAE,MAAM,GAAG,CAAC,CAAC;KACvE;;AAGD,IAAA,KAAK,CAAC,OAAsB,EAAA;QAC1B,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;KAC/C;;IAGD,uBAAuB,GAAA;AACrB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,OAAO,IAAI,CAAC;SACb;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,EAAE,UAAU,EAAE,CAAC;AACpD,QAAA,MAAM,eAAe,GAAG,OAAO,GAAG,OAAO,GAAG,GAAG,GAAG,EAAE,CAAC;AACrD,QAAA,OAAO,IAAI,CAAC,cAAc,GAAG,eAAe,GAAG,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;KAC9E;;IAGD,wBAAwB,GAAA;AACtB,QAAA,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE;AACrE,YAAA,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,CAAC;SACvC;AAED,QAAA,OAAO,IAAI,CAAC;KACb;;IAGO,yBAAyB,GAAA;AAC/B,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,OAAO,IAAI,CAAC;SACb;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,EAAE,UAAU,EAAE,CAAC;AACpD,QAAA,IAAI,KAAK,GAAG,CAAC,OAAO,GAAG,OAAO,GAAG,GAAG,GAAG,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC;AAE3D,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE;AACvB,YAAA,KAAK,IAAI,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC;SACpC;AAED,QAAA,OAAO,KAAK,CAAC;KACd;;AAGS,IAAA,mBAAmB,CAAC,MAAe,EAAA;AAC3C,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KAChC;AAED;;;AAGG;AACH,IAAA,iBAAiB,CAAC,GAAa,EAAA;AAC7B,QAAA,IAAI,GAAG,CAAC,MAAM,EAAE;AACd,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,kBAAkB,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;SAChF;aAAM;YACL,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC;SACpE;KACF;AAED;;;AAGG;IACH,gBAAgB,GAAA;QACd,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,IAAI,CAAC,IAAI,EAAE,CAAC;KACb;AAED;;;AAGG;AACH,IAAA,IAAI,gBAAgB,GAAA;;;QAGlB,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;KAC9E;AApsCU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,SAAS,uSA2YE,cAAc,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAEvB,UAAU,EACb,SAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,0BAA0B,0CAEd,iBAAiB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAhZ5B,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAS,uLA6LD,gBAAgB,CAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAIhB,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAKtB,CAAC,KAAc,MAAM,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,CAK1D,EAAA,4BAAA,EAAA,CAAA,8BAAA,EAAA,8BAAA,EAAA,gBAAgB,CAuBhB,EAAA,WAAA,EAAA,aAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,sCAWhB,gBAAgB,CAAA,EAAA,sBAAA,EAAA,CAAA,wBAAA,EAAA,wBAAA,EAchB,gBAAgB,CAAA,EAAA,WAAA,EAAA,aAAA,EAAA,KAAA,EAAA,OAAA,EAAA,SAAA,EAAA,CAAA,YAAA,EAAA,WAAA,CAAA,EAAA,cAAA,EAAA,CAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,yBAAA,EAAA,CAAA,2BAAA,EAAA,2BAAA,EAqDhB,eAAe,CAvTvB,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,OAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,aAAA,EAAA,QAAA,EAAA,aAAA,EAAA,QAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,UAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,eAAA,EAAA,SAAA,EAAA,EAAA,SAAA,EAAA,EAAA,SAAA,EAAA,wBAAA,EAAA,OAAA,EAAA,YAAA,EAAA,MAAA,EAAA,WAAA,EAAA,EAAA,UAAA,EAAA,EAAA,SAAA,EAAA,IAAA,EAAA,eAAA,EAAA,0BAAA,EAAA,oBAAA,EAAA,oCAAA,EAAA,oBAAA,EAAA,WAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,oBAAA,EAAA,qBAAA,EAAA,oBAAA,EAAA,qBAAA,EAAA,mBAAA,EAAA,YAAA,EAAA,4BAAA,EAAA,4BAAA,EAAA,+BAAA,EAAA,UAAA,EAAA,8BAAA,EAAA,YAAA,EAAA,+BAAA,EAAA,UAAA,EAAA,4BAAA,EAAA,OAAA,EAAA,+BAAA,EAAA,UAAA,EAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA,EAAC,OAAO,EAAE,mBAAmB,EAAE,WAAW,EAAE,SAAS,EAAC;AACtD,YAAA,EAAC,OAAO,EAAE,2BAA2B,EAAE,WAAW,EAAE,SAAS,EAAC;SAC/D,EAuBa,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,kBAAkB,6DARf,SAAS,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,SAAA,EAKT,YAAY,EAsKlB,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,SAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,OAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,OAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,mBAAmB,8FCvYhC,6qEA4DA,EAAA,MAAA,EAAA,CAAA,s2HAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EDmJY,gBAAgB,EAAE,QAAA,EAAA,4DAAA,EAAA,QAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,mBAAmB,4+BAAE,OAAO,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,CAAA,EAAA,UAAA,EAN5C,CAAC,mBAAmB,CAAC,cAAc,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAQrC,SAAS,EAAA,UAAA,EAAA,CAAA;kBAtCrB,SAAS;+BACE,YAAY,EAAA,QAAA,EACZ,WAAW,EAAA,aAAA,EAGN,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EACzC,IAAA,EAAA;AACJ,wBAAA,MAAM,EAAE,UAAU;AAClB,wBAAA,mBAAmB,EAAE,MAAM;AAC3B,wBAAA,eAAe,EAAE,SAAS;AAC1B,wBAAA,OAAO,EAAE,gBAAgB;AACzB,wBAAA,WAAW,EAAE,IAAI;AACjB,wBAAA,iBAAiB,EAAE,0BAA0B;AAC7C,wBAAA,sBAAsB,EAAE,kCAAkC;AAC1D,wBAAA,sBAAsB,EAAE,WAAW;AACnC,wBAAA,mBAAmB,EAAE,mBAAmB;AACxC,wBAAA,sBAAsB,EAAE,qBAAqB;AAC7C,wBAAA,sBAAsB,EAAE,qBAAqB;AAC7C,wBAAA,qBAAqB,EAAE,YAAY;AACnC,wBAAA,8BAA8B,EAAE,4BAA4B;AAC5D,wBAAA,iCAAiC,EAAE,UAAU;AAC7C,wBAAA,gCAAgC,EAAE,YAAY;AAC9C,wBAAA,iCAAiC,EAAE,UAAU;AAC7C,wBAAA,8BAA8B,EAAE,OAAO;AACvC,wBAAA,iCAAiC,EAAE,UAAU;AAC7C,wBAAA,WAAW,EAAE,wBAAwB;AACrC,wBAAA,SAAS,EAAE,YAAY;AACvB,wBAAA,QAAQ,EAAE,WAAW;AACtB,qBAAA,EAAA,UAAA,EACW,CAAC,mBAAmB,CAAC,cAAc,CAAC,EACrC,SAAA,EAAA;AACT,wBAAA,EAAC,OAAO,EAAE,mBAAmB,EAAE,WAAW,WAAW,EAAC;AACtD,wBAAA,EAAC,OAAO,EAAE,2BAA2B,EAAE,WAAW,WAAW,EAAC;qBAC/D,EACW,UAAA,EAAA,IAAI,WACP,CAAC,gBAAgB,EAAE,mBAAmB,EAAE,OAAO,CAAC,EAAA,QAAA,EAAA,6qEAAA,EAAA,MAAA,EAAA,CAAA,s2HAAA,CAAA,EAAA,CAAA;;0BA0YtD,QAAQ;;0BACR,QAAQ;;0BACR,QAAQ;;0BACR,QAAQ;;0BAAI,MAAM;2BAAC,cAAc,CAAA;;0BACjC,IAAI;;0BAAI,QAAQ;;0BAChB,SAAS;2BAAC,UAAU,CAAA;;0BACpB,MAAM;2BAAC,0BAA0B,CAAA;;0BAEjC,QAAQ;;0BAAI,MAAM;2BAAC,iBAAiB,CAAA;yCArYU,OAAO,EAAA,CAAA;sBAAvD,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,SAAS,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC,CAAA;gBAKK,YAAY,EAAA,CAAA;sBAA/D,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,YAAY,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC,CAAA;gBAGhB,aAAa,EAAA,CAAA;sBAA9C,YAAY;uBAAC,kBAAkB,CAAA;gBAoHL,mBAAmB,EAAA,CAAA;sBAA7C,KAAK;uBAAC,kBAAkB,CAAA;gBAyCH,OAAO,EAAA,CAAA;sBAA5B,SAAS;uBAAC,SAAS,CAAA;gBAGA,KAAK,EAAA,CAAA;sBAAxB,SAAS;uBAAC,OAAO,CAAA;gBAIR,WAAW,EAAA,CAAA;sBADpB,SAAS;uBAAC,mBAAmB,CAAA;gBAIrB,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAIN,QAAQ,EAAA,CAAA;sBADP,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAKpC,aAAa,EAAA,CAAA;sBADZ,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAOpC,QAAQ,EAAA,CAAA;sBAHP,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA;wBACL,SAAS,EAAE,CAAC,KAAc,MAAM,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;AAC5E,qBAAA,CAAA;gBAKG,4BAA4B,EAAA,CAAA;sBAD/B,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAahC,WAAW,EAAA,CAAA;sBADd,KAAK;gBAYF,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAYhC,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAepC,sBAAsB,EAAA,CAAA;sBADrB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAShC,WAAW,EAAA,CAAA;sBADd,KAAK;gBAiBF,KAAK,EAAA,CAAA;sBADR,KAAK;gBAce,SAAS,EAAA,CAAA;sBAA7B,KAAK;uBAAC,YAAY,CAAA;gBAGO,cAAc,EAAA,CAAA;sBAAvC,KAAK;uBAAC,iBAAiB,CAAA;gBAIpB,iBAAiB,EAAA,CAAA;sBADpB,KAAK;gBAUN,yBAAyB,EAAA,CAAA;sBADxB,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAA;gBAO1B,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAIF,EAAE,EAAA,CAAA;sBADL,KAAK;gBAsBG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAsBa,YAAY,EAAA,CAAA;sBAA9B,MAAM;gBAGoB,aAAa,EAAA,CAAA;sBAAvC,MAAM;uBAAC,QAAQ,CAAA;gBAMW,aAAa,EAAA,CAAA;sBAAvC,MAAM;uBAAC,QAAQ,CAAA;gBAMG,eAAe,EAAA,CAAA;sBAAjC,MAAM;gBAOY,WAAW,EAAA,CAAA;sBAA7B,MAAM;;AA20BT;;AAEG;MAMU,gBAAgB,CAAA;8GAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAhB,gBAAgB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,SAAA,EAHhB,CAAC,EAAC,OAAO,EAAE,kBAAkB,EAAE,WAAW,EAAE,gBAAgB,EAAC,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAG9D,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAL5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oBAAoB;oBAC9B,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,kBAAkB,EAAE,WAAW,EAAkB,gBAAA,EAAC,CAAC;AACzE,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;;ME53CY,eAAe,CAAA;8GAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAf,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YAjBxB,YAAY;YACZ,aAAa;YACb,eAAe;YACf,eAAe;YACf,SAAS;AACT,YAAA,gBAAgB,aAGhB,mBAAmB;YACnB,kBAAkB;YAClB,SAAS;YACT,gBAAgB;YAChB,eAAe;YACf,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;AAIN,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,EAFf,SAAA,EAAA,CAAC,mCAAmC,CAAC,YAf9C,YAAY;YACZ,aAAa;YACb,eAAe;AACf,YAAA,eAAe,EAKf,mBAAmB;YACnB,kBAAkB;YAGlB,eAAe;YACf,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAIN,eAAe,EAAA,UAAA,EAAA,CAAA;kBAnB3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE;wBACP,YAAY;wBACZ,aAAa;wBACb,eAAe;wBACf,eAAe;wBACf,SAAS;wBACT,gBAAgB;AACjB,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,mBAAmB;wBACnB,kBAAkB;wBAClB,SAAS;wBACT,gBAAgB;wBAChB,eAAe;wBACf,eAAe;AAChB,qBAAA;oBACD,SAAS,EAAE,CAAC,mCAAmC,CAAC;AACjD,iBAAA,CAAA;;;AClCD;;AAEG;;;;"}