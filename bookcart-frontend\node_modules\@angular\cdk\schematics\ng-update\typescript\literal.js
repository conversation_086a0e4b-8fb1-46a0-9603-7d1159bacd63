"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.isStringLiteralLike = exports.findAllSubstringIndices = void 0;
const ts = require("typescript");
/** Finds all start indices of the given search string in the input string. */
function findAllSubstringIndices(input, search) {
    const result = [];
    let i = -1;
    while ((i = input.indexOf(search, i + 1)) !== -1) {
        result.push(i);
    }
    return result;
}
exports.findAllSubstringIndices = findAllSubstringIndices;
/**
 * Checks whether the given node is either a string literal or a no-substitution template
 * literal. Note that we cannot use `ts.isStringLiteralLike()` because if developers update
 * an outdated project, their TypeScript version is not automatically being updated
 * and therefore could throw because the function is not available yet.
 * https://github.com/Microsoft/TypeScript/commit/8518343dc8762475a5e92c9f80b5c5725bd81796
 */
function isStringLiteralLike(node) {
    return ts.isStringLiteral(node) || ts.isNoSubstitutionTemplateLiteral(node);
}
exports.isStringLiteralLike = isStringLiteralLike;
//# sourceMappingURL=data:application/json;base64,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