{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../../src/material/paginator/testing/paginator-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {MatSelectHarness} from '@angular/material/select/testing';\nimport {coerceNumberProperty} from '@angular/cdk/coercion';\nimport {PaginatorHarnessFilters} from './paginator-harness-filters';\n\n/** Harness for interacting with a mat-paginator in tests. */\nexport class MatPaginatorHarness extends ComponentHarness {\n  /** Selector used to find paginator instances. */\n  static hostSelector = '.mat-mdc-paginator';\n  private _nextButton = this.locatorFor('.mat-mdc-paginator-navigation-next');\n  private _previousButton = this.locatorFor('.mat-mdc-paginator-navigation-previous');\n  private _firstPageButton = this.locatorForOptional('.mat-mdc-paginator-navigation-first');\n  private _lastPageButton = this.locatorForOptional('.mat-mdc-paginator-navigation-last');\n  _select = this.locatorForOptional(\n    MatSelectHarness.with({\n      ancestor: '.mat-mdc-paginator-page-size',\n    }),\n  );\n  private _pageSizeFallback = this.locatorFor('.mat-mdc-paginator-page-size-value');\n  _rangeLabel = this.locatorFor('.mat-mdc-paginator-range-label');\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a paginator with specific attributes.\n   * @param options Options for filtering which paginator instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatPaginatorHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: PaginatorHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options);\n  }\n\n  /** Goes to the next page in the paginator. */\n  async goToNextPage(): Promise<void> {\n    return (await this._nextButton()).click();\n  }\n\n  /** Returns whether or not the next page button is disabled. */\n  async isNextPageDisabled(): Promise<boolean> {\n    const disabledValue = await (await this._nextButton()).getAttribute('disabled');\n    return disabledValue == 'true';\n  }\n\n  /* Returns whether or not the previous page button is disabled. */\n  async isPreviousPageDisabled(): Promise<boolean> {\n    const disabledValue = await (await this._previousButton()).getAttribute('disabled');\n    return disabledValue == 'true';\n  }\n\n  /** Goes to the previous page in the paginator. */\n  async goToPreviousPage(): Promise<void> {\n    return (await this._previousButton()).click();\n  }\n\n  /** Goes to the first page in the paginator. */\n  async goToFirstPage(): Promise<void> {\n    const button = await this._firstPageButton();\n\n    // The first page button isn't enabled by default so we need to check for it.\n    if (!button) {\n      throw Error(\n        'Could not find first page button inside paginator. ' +\n          'Make sure that `showFirstLastButtons` is enabled.',\n      );\n    }\n\n    return button.click();\n  }\n\n  /** Goes to the last page in the paginator. */\n  async goToLastPage(): Promise<void> {\n    const button = await this._lastPageButton();\n\n    // The last page button isn't enabled by default so we need to check for it.\n    if (!button) {\n      throw Error(\n        'Could not find last page button inside paginator. ' +\n          'Make sure that `showFirstLastButtons` is enabled.',\n      );\n    }\n\n    return button.click();\n  }\n\n  /**\n   * Sets the page size of the paginator.\n   * @param size Page size that should be select.\n   */\n  async setPageSize(size: number): Promise<void> {\n    const select = await this._select();\n\n    // The select is only available if the `pageSizeOptions` are\n    // set to an array with more than one item.\n    if (!select) {\n      throw Error(\n        'Cannot find page size selector in paginator. ' +\n          'Make sure that the `pageSizeOptions` have been configured.',\n      );\n    }\n\n    return select.clickOptions({text: `${size}`});\n  }\n\n  /** Gets the page size of the paginator. */\n  async getPageSize(): Promise<number> {\n    const select = await this._select();\n    const value = select ? select.getValueText() : (await this._pageSizeFallback()).text();\n    return coerceNumberProperty(await value);\n  }\n\n  /** Gets the text of the range label of the paginator. */\n  async getRangeLabel(): Promise<string> {\n    return (await this._rangeLabel()).text();\n  }\n}\n"], "names": [], "mappings": ";;;;AAiBA;AACM,MAAO,mBAAoB,SAAQ,gBAAgB,CAAA;AAAzD,IAAA,WAAA,GAAA;;AAGU,QAAA,IAAA,CAAA,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,oCAAoC,CAAC,CAAC;AACpE,QAAA,IAAA,CAAA,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,wCAAwC,CAAC,CAAC;AAC5E,QAAA,IAAA,CAAA,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,CAAC,qCAAqC,CAAC,CAAC;AAClF,QAAA,IAAA,CAAA,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,oCAAoC,CAAC,CAAC;QACxF,IAAO,CAAA,OAAA,GAAG,IAAI,CAAC,kBAAkB,CAC/B,gBAAgB,CAAC,IAAI,CAAC;AACpB,YAAA,QAAQ,EAAE,8BAA8B;AACzC,SAAA,CAAC,CACH,CAAC;AACM,QAAA,IAAA,CAAA,iBAAiB,GAAG,IAAI,CAAC,UAAU,CAAC,oCAAoC,CAAC,CAAC;AAClF,QAAA,IAAA,CAAA,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,gCAAgC,CAAC,CAAC;KAgGjE;;aA3GQ,IAAY,CAAA,YAAA,GAAG,oBAAH,CAAwB,EAAA;AAa3C;;;;AAIG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAmC,EAAE,EAAA;AAErC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAC5C;;AAGD,IAAA,MAAM,YAAY,GAAA;QAChB,OAAO,CAAC,MAAM,IAAI,CAAC,WAAW,EAAE,EAAE,KAAK,EAAE,CAAC;KAC3C;;AAGD,IAAA,MAAM,kBAAkB,GAAA;AACtB,QAAA,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,WAAW,EAAE,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC;QAChF,OAAO,aAAa,IAAI,MAAM,CAAC;KAChC;;AAGD,IAAA,MAAM,sBAAsB,GAAA;AAC1B,QAAA,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,eAAe,EAAE,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC;QACpF,OAAO,aAAa,IAAI,MAAM,CAAC;KAChC;;AAGD,IAAA,MAAM,gBAAgB,GAAA;QACpB,OAAO,CAAC,MAAM,IAAI,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,CAAC;KAC/C;;AAGD,IAAA,MAAM,aAAa,GAAA;AACjB,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;;QAG7C,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,KAAK,CACT,qDAAqD;AACnD,gBAAA,mDAAmD,CACtD,CAAC;SACH;AAED,QAAA,OAAO,MAAM,CAAC,KAAK,EAAE,CAAC;KACvB;;AAGD,IAAA,MAAM,YAAY,GAAA;AAChB,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;;QAG5C,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,KAAK,CACT,oDAAoD;AAClD,gBAAA,mDAAmD,CACtD,CAAC;SACH;AAED,QAAA,OAAO,MAAM,CAAC,KAAK,EAAE,CAAC;KACvB;AAED;;;AAGG;IACH,MAAM,WAAW,CAAC,IAAY,EAAA;AAC5B,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;;;QAIpC,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,KAAK,CACT,+CAA+C;AAC7C,gBAAA,4DAA4D,CAC/D,CAAC;SACH;AAED,QAAA,OAAO,MAAM,CAAC,YAAY,CAAC,EAAC,IAAI,EAAE,CAAA,EAAG,IAAI,CAAA,CAAE,EAAC,CAAC,CAAC;KAC/C;;AAGD,IAAA,MAAM,WAAW,GAAA;AACf,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;QACpC,MAAM,KAAK,GAAG,MAAM,GAAG,MAAM,CAAC,YAAY,EAAE,GAAG,CAAC,MAAM,IAAI,CAAC,iBAAiB,EAAE,EAAE,IAAI,EAAE,CAAC;AACvF,QAAA,OAAO,oBAAoB,CAAC,MAAM,KAAK,CAAC,CAAC;KAC1C;;AAGD,IAAA,MAAM,aAAa,GAAA;QACjB,OAAO,CAAC,MAAM,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,EAAE,CAAC;KAC1C;;;;;"}