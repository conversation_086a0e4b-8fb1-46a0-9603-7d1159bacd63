"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.MIGRATION_PATH = exports.COLLECTION_PATH = void 0;
const path_1 = require("path");
/** Path to the schematic collection for non-migration schematics. */
exports.COLLECTION_PATH = (0, path_1.join)(__dirname, 'collection.json');
/** Path to the schematic collection that includes the migrations. */
exports.MIGRATION_PATH = (0, path_1.join)(__dirname, 'migration.json');
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicGF0aHMuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi9zcmMvY2RrL3NjaGVtYXRpY3MvcGF0aHMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUFBOzs7Ozs7R0FNRzs7O0FBRUgsK0JBQTBCO0FBRTFCLHFFQUFxRTtBQUN4RCxRQUFBLGVBQWUsR0FBRyxJQUFBLFdBQUksRUFBQyxTQUFTLEVBQUUsaUJBQWlCLENBQUMsQ0FBQztBQUVsRSxxRUFBcUU7QUFDeEQsUUFBQSxjQUFjLEdBQUcsSUFBQSxXQUFJLEVBQUMsU0FBUyxFQUFFLGdCQUFnQixDQUFDLENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgR29vZ2xlIExMQyBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICpcbiAqIFVzZSBvZiB0aGlzIHNvdXJjZSBjb2RlIGlzIGdvdmVybmVkIGJ5IGFuIE1JVC1zdHlsZSBsaWNlbnNlIHRoYXQgY2FuIGJlXG4gKiBmb3VuZCBpbiB0aGUgTElDRU5TRSBmaWxlIGF0IGh0dHBzOi8vYW5ndWxhci5pby9saWNlbnNlXG4gKi9cblxuaW1wb3J0IHtqb2lufSBmcm9tICdwYXRoJztcblxuLyoqIFBhdGggdG8gdGhlIHNjaGVtYXRpYyBjb2xsZWN0aW9uIGZvciBub24tbWlncmF0aW9uIHNjaGVtYXRpY3MuICovXG5leHBvcnQgY29uc3QgQ09MTEVDVElPTl9QQVRIID0gam9pbihfX2Rpcm5hbWUsICdjb2xsZWN0aW9uLmpzb24nKTtcblxuLyoqIFBhdGggdG8gdGhlIHNjaGVtYXRpYyBjb2xsZWN0aW9uIHRoYXQgaW5jbHVkZXMgdGhlIG1pZ3JhdGlvbnMuICovXG5leHBvcnQgY29uc3QgTUlHUkFUSU9OX1BBVEggPSBqb2luKF9fZGlybmFtZSwgJ21pZ3JhdGlvbi5qc29uJyk7XG4iXX0=