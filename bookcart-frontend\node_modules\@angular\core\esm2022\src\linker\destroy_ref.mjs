/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { getLView } from '../render3/state';
import { removeLViewOnDestroy, storeLViewOnDestroy } from '../render3/util/view_utils';
/**
 * `DestroyRef` lets you set callbacks to run for any cleanup or destruction behavior.
 * The scope of this destruction depends on where `<PERSON>troyRef` is injected. If `DestroyRef`
 * is injected in a component or directive, the callbacks run when that component or
 * directive is destroyed. Otherwise the callbacks run when a corresponding injector is destroyed.
 *
 * @publicApi
 */
export class DestroyRef {
    /**
     * @internal
     * @nocollapse
     */
    static { this.__NG_ELEMENT_ID__ = injectDestroyRef; }
    /**
     * @internal
     * @nocollapse
     */
    static { this.__NG_ENV_ID__ = (injector) => injector; }
}
class NodeInjectorDestroyRef extends DestroyRef {
    constructor(_lView) {
        super();
        this._lView = _lView;
    }
    onDestroy(callback) {
        storeLViewOnDestroy(this._lView, callback);
        return () => removeLViewOnDestroy(this._lView, callback);
    }
}
function injectDestroyRef() {
    return new NodeInjectorDestroyRef(getLView());
}
//# sourceMappingURL=data:application/json;base64,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