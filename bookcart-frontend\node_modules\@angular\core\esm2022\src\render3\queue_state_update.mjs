/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { ApplicationRef } from '../application/application_ref';
import { assertInInjectionContext, Injector } from '../di';
import { inject } from '../di/injector_compatibility';
import { internalAfterNextRender } from './after_render_hooks';
/**
 * Queue a state update to be performed asynchronously.
 *
 * This is useful to safely update application state that is used in an expression that was already
 * checked during change detection. This defers the update until later and prevents
 * `ExpressionChangedAfterItHasBeenChecked` errors. Using signals for state is recommended instead,
 * but it's not always immediately possible to change the state to a signal because it would be a
 * breaking change. When the callback updates state used in an expression, this needs to be
 * accompanied by an explicit notification to the framework that something has changed (i.e.
 * updating a signal or calling `ChangeDetectorRef.markForCheck()`) or may still cause
 * `ExpressionChangedAfterItHasBeenChecked` in dev mode or fail to synchronize the state to the DOM
 * in production.
 */
export function queueStateUpdate(callback, options) {
    !options && assertInInjectionContext(queueStateUpdate);
    const injector = options?.injector ?? inject(Injector);
    const appRef = injector.get(ApplicationRef);
    let executed = false;
    const runCallbackOnce = () => {
        if (executed || appRef.destroyed)
            return;
        executed = true;
        callback();
    };
    internalAfterNextRender(runCallbackOnce, { injector, runOnServer: true });
    queueMicrotask(runCallbackOnce);
}
//# sourceMappingURL=data:application/json;base64,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