import { AnimationRenderer, BaseAnimationRenderer } from './renderer';
export class AnimationRendererFactory {
    constructor(delegate, engine, _zone) {
        this.delegate = delegate;
        this.engine = engine;
        this._zone = _zone;
        this._currentId = 0;
        this._microtaskId = 1;
        this._animationCallbacksBuffer = [];
        this._rendererCache = new Map();
        this._cdRecurDepth = 0;
        engine.onRemovalComplete = (element, delegate) => {
            // Note: if a component element has a leave animation, and a host leave animation,
            // the view engine will call `removeChild` for the parent
            // component renderer as well as for the child component renderer.
            // Therefore, we need to check if we already removed the element.
            const parentNode = delegate?.parentNode(element);
            if (parentNode) {
                delegate.removeChild(parentNode, element);
            }
        };
    }
    createRenderer(hostElement, type) {
        const EMPTY_NAMESPACE_ID = '';
        // cache the delegates to find out which cached delegate can
        // be used by which cached renderer
        const delegate = this.delegate.createRenderer(hostElement, type);
        if (!hostElement || !type?.data?.['animation']) {
            const cache = this._rendererCache;
            let renderer = cache.get(delegate);
            if (!renderer) {
                // Ensure that the renderer is removed from the cache on destroy
                // since it may contain references to detached DOM nodes.
                const onRendererDestroy = () => cache.delete(delegate);
                renderer = new BaseAnimationRenderer(EMPTY_NAMESPACE_ID, delegate, this.engine, onRendererDestroy);
                // only cache this result when the base renderer is used
                cache.set(delegate, renderer);
            }
            return renderer;
        }
        const componentId = type.id;
        const namespaceId = type.id + '-' + this._currentId;
        this._currentId++;
        this.engine.register(namespaceId, hostElement);
        const registerTrigger = (trigger) => {
            if (Array.isArray(trigger)) {
                trigger.forEach(registerTrigger);
            }
            else {
                this.engine.registerTrigger(componentId, namespaceId, hostElement, trigger.name, trigger);
            }
        };
        const animationTriggers = type.data['animation'];
        animationTriggers.forEach(registerTrigger);
        return new AnimationRenderer(this, namespaceId, delegate, this.engine);
    }
    begin() {
        this._cdRecurDepth++;
        if (this.delegate.begin) {
            this.delegate.begin();
        }
    }
    _scheduleCountTask() {
        queueMicrotask(() => {
            this._microtaskId++;
        });
    }
    /** @internal */
    scheduleListenerCallback(count, fn, data) {
        if (count >= 0 && count < this._microtaskId) {
            this._zone.run(() => fn(data));
            return;
        }
        const animationCallbacksBuffer = this._animationCallbacksBuffer;
        if (animationCallbacksBuffer.length == 0) {
            queueMicrotask(() => {
                this._zone.run(() => {
                    animationCallbacksBuffer.forEach((tuple) => {
                        const [fn, data] = tuple;
                        fn(data);
                    });
                    this._animationCallbacksBuffer = [];
                });
            });
        }
        animationCallbacksBuffer.push([fn, data]);
    }
    end() {
        this._cdRecurDepth--;
        // this is to prevent animations from running twice when an inner
        // component does CD when a parent component instead has inserted it
        if (this._cdRecurDepth == 0) {
            this._zone.runOutsideAngular(() => {
                this._scheduleCountTask();
                this.engine.flush(this._microtaskId);
            });
        }
        if (this.delegate.end) {
            this.delegate.end();
        }
    }
    whenRenderingDone() {
        return this.engine.whenRenderingDone();
    }
}
//# sourceMappingURL=data:application/json;base64,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