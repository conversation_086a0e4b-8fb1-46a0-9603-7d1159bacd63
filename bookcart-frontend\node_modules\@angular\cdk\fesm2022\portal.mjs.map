{"version": 3, "file": "portal.mjs", "sources": ["../../../../../../src/cdk/portal/portal-errors.ts", "../../../../../../src/cdk/portal/portal.ts", "../../../../../../src/cdk/portal/dom-portal-outlet.ts", "../../../../../../src/cdk/portal/portal-directives.ts", "../../../../../../src/cdk/portal/portal-injector.ts", "../../../../../../src/cdk/portal/portal_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Throws an exception when attempting to attach a null portal to a host.\n * @docs-private\n */\nexport function throwNullPortalError() {\n  throw Error('Must provide a portal to attach');\n}\n\n/**\n * Throws an exception when attempting to attach a portal to a host that is already attached.\n * @docs-private\n */\nexport function throwPortalAlreadyAttachedError() {\n  throw Error('Host already has a portal attached');\n}\n\n/**\n * Throws an exception when attempting to attach a portal to an already-disposed host.\n * @docs-private\n */\nexport function throwPortalOutletAlreadyDisposedError() {\n  throw Error('This PortalOutlet has already been disposed');\n}\n\n/**\n * Throws an exception when attempting to attach an unknown portal type.\n * @docs-private\n */\nexport function throwUnknownPortalTypeError() {\n  throw Error(\n    'Attempting to attach an unknown Portal type. BasePortalOutlet accepts either ' +\n      'a ComponentPortal or a TemplatePortal.',\n  );\n}\n\n/**\n * Throws an exception when attempting to attach a portal to a null host.\n * @docs-private\n */\nexport function throwNullPortalOutletError() {\n  throw Error('Attempting to attach a portal to a null PortalOutlet');\n}\n\n/**\n * Throws an exception when attempting to detach a portal that is not attached.\n * @docs-private\n */\nexport function throwNoPortalAttachedError() {\n  throw Error('Attempting to detach a portal that is not attached to a host');\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  TemplateRef,\n  ViewContainerRef,\n  ElementRef,\n  ComponentRef,\n  EmbeddedViewRef,\n  Injector,\n  ComponentFactoryResolver,\n} from '@angular/core';\nimport {\n  throwNullPortalOutletError,\n  throwPortalAlreadyAttachedError,\n  throwNoPortalAttachedError,\n  throwNullPortalError,\n  throwPortalOutletAlreadyDisposedError,\n  throwUnknownPortalTypeError,\n} from './portal-errors';\n\n/** Interface that can be used to generically type a class. */\nexport interface ComponentType<T> {\n  new (...args: any[]): T;\n}\n\n/**\n * A `Portal` is something that you want to render somewhere else.\n * It can be attach to / detached from a `PortalOutlet`.\n */\nexport abstract class Portal<T> {\n  private _attachedHost: PortalOutlet | null;\n\n  /** Attach this portal to a host. */\n  attach(host: PortalOutlet): T {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (host == null) {\n        throwNullPortalOutletError();\n      }\n\n      if (host.hasAttached()) {\n        throwPortalAlreadyAttachedError();\n      }\n    }\n\n    this._attachedHost = host;\n    return <T>host.attach(this);\n  }\n\n  /** Detach this portal from its host */\n  detach(): void {\n    let host = this._attachedHost;\n\n    if (host != null) {\n      this._attachedHost = null;\n      host.detach();\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throwNoPortalAttachedError();\n    }\n  }\n\n  /** Whether this portal is attached to a host. */\n  get isAttached(): boolean {\n    return this._attachedHost != null;\n  }\n\n  /**\n   * Sets the PortalOutlet reference without performing `attach()`. This is used directly by\n   * the PortalOutlet when it is performing an `attach()` or `detach()`.\n   */\n  setAttachedHost(host: PortalOutlet | null) {\n    this._attachedHost = host;\n  }\n}\n\n/**\n * A `ComponentPortal` is a portal that instantiates some Component upon attachment.\n */\nexport class ComponentPortal<T> extends Portal<ComponentRef<T>> {\n  /** The type of the component that will be instantiated for attachment. */\n  component: ComponentType<T>;\n\n  /**\n   * Where the attached component should live in Angular's *logical* component tree.\n   * This is different from where the component *renders*, which is determined by the PortalOutlet.\n   * The origin is necessary when the host is outside of the Angular application context.\n   */\n  viewContainerRef?: ViewContainerRef | null;\n\n  /** Injector used for the instantiation of the component. */\n  injector?: Injector | null;\n\n  /**\n   * Alternate `ComponentFactoryResolver` to use when resolving the associated component.\n   * Defaults to using the resolver from the outlet that the portal is attached to.\n   */\n  componentFactoryResolver?: ComponentFactoryResolver | null;\n\n  /**\n   * List of DOM nodes that should be projected through `<ng-content>` of the attached component.\n   */\n  projectableNodes?: Node[][] | null;\n\n  constructor(\n    component: ComponentType<T>,\n    viewContainerRef?: ViewContainerRef | null,\n    injector?: Injector | null,\n    componentFactoryResolver?: ComponentFactoryResolver | null,\n    projectableNodes?: Node[][] | null,\n  ) {\n    super();\n    this.component = component;\n    this.viewContainerRef = viewContainerRef;\n    this.injector = injector;\n    this.componentFactoryResolver = componentFactoryResolver;\n    this.projectableNodes = projectableNodes;\n  }\n}\n\n/**\n * A `TemplatePortal` is a portal that represents some embedded template (TemplateRef).\n */\nexport class TemplatePortal<C = any> extends Portal<EmbeddedViewRef<C>> {\n  constructor(\n    /** The embedded template that will be used to instantiate an embedded View in the host. */\n    public templateRef: TemplateRef<C>,\n    /** Reference to the ViewContainer into which the template will be stamped out. */\n    public viewContainerRef: ViewContainerRef,\n    /** Contextual data to be passed in to the embedded view. */\n    public context?: C,\n    /** The injector to use for the embedded view. */\n    public injector?: Injector,\n  ) {\n    super();\n  }\n\n  get origin(): ElementRef {\n    return this.templateRef.elementRef;\n  }\n\n  /**\n   * Attach the portal to the provided `PortalOutlet`.\n   * When a context is provided it will override the `context` property of the `TemplatePortal`\n   * instance.\n   */\n  override attach(host: PortalOutlet, context: C | undefined = this.context): EmbeddedViewRef<C> {\n    this.context = context;\n    return super.attach(host);\n  }\n\n  override detach(): void {\n    this.context = undefined;\n    return super.detach();\n  }\n}\n\n/**\n * A `DomPortal` is a portal whose DOM element will be taken from its current position\n * in the DOM and moved into a portal outlet, when it is attached. On detach, the content\n * will be restored to its original position.\n */\nexport class DomPortal<T = HTMLElement> extends Portal<T> {\n  /** DOM node hosting the portal's content. */\n  readonly element: T;\n\n  constructor(element: T | ElementRef<T>) {\n    super();\n    this.element = element instanceof ElementRef ? element.nativeElement : element;\n  }\n}\n\n/** A `PortalOutlet` is an space that can contain a single `Portal`. */\nexport interface PortalOutlet {\n  /** Attaches a portal to this outlet. */\n  attach(portal: Portal<any>): any;\n\n  /** Detaches the currently attached portal from this outlet. */\n  detach(): any;\n\n  /** Performs cleanup before the outlet is destroyed. */\n  dispose(): void;\n\n  /** Whether there is currently a portal attached to this outlet. */\n  hasAttached(): boolean;\n}\n\n/**\n * @deprecated Use `PortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nexport type PortalHost = PortalOutlet;\n\n/**\n * Partial implementation of PortalOutlet that handles attaching\n * ComponentPortal and TemplatePortal.\n */\nexport abstract class BasePortalOutlet implements PortalOutlet {\n  /** The portal currently attached to the host. */\n  protected _attachedPortal: Portal<any> | null;\n\n  /** A function that will permanently dispose this host. */\n  private _disposeFn: (() => void) | null;\n\n  /** Whether this host has already been permanently disposed. */\n  private _isDisposed: boolean = false;\n\n  /** Whether this host has an attached portal. */\n  hasAttached(): boolean {\n    return !!this._attachedPortal;\n  }\n\n  attach<T>(portal: ComponentPortal<T>): ComponentRef<T>;\n  attach<T>(portal: TemplatePortal<T>): EmbeddedViewRef<T>;\n  attach(portal: any): any;\n\n  /** Attaches a portal. */\n  attach(portal: Portal<any>): any {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!portal) {\n        throwNullPortalError();\n      }\n\n      if (this.hasAttached()) {\n        throwPortalAlreadyAttachedError();\n      }\n\n      if (this._isDisposed) {\n        throwPortalOutletAlreadyDisposedError();\n      }\n    }\n\n    if (portal instanceof ComponentPortal) {\n      this._attachedPortal = portal;\n      return this.attachComponentPortal(portal);\n    } else if (portal instanceof TemplatePortal) {\n      this._attachedPortal = portal;\n      return this.attachTemplatePortal(portal);\n      // @breaking-change 10.0.0 remove null check for `this.attachDomPortal`.\n    } else if (this.attachDomPortal && portal instanceof DomPortal) {\n      this._attachedPortal = portal;\n      return this.attachDomPortal(portal);\n    }\n\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throwUnknownPortalTypeError();\n    }\n  }\n\n  abstract attachComponentPortal<T>(portal: ComponentPortal<T>): ComponentRef<T>;\n\n  abstract attachTemplatePortal<C>(portal: TemplatePortal<C>): EmbeddedViewRef<C>;\n\n  // @breaking-change 10.0.0 `attachDomPortal` to become a required abstract method.\n  readonly attachDomPortal: null | ((portal: DomPortal) => any) = null;\n\n  /** Detaches a previously attached portal. */\n  detach(): void {\n    if (this._attachedPortal) {\n      this._attachedPortal.setAttachedHost(null);\n      this._attachedPortal = null;\n    }\n\n    this._invokeDisposeFn();\n  }\n\n  /** Permanently dispose of this portal host. */\n  dispose(): void {\n    if (this.hasAttached()) {\n      this.detach();\n    }\n\n    this._invokeDisposeFn();\n    this._isDisposed = true;\n  }\n\n  /** @docs-private */\n  setDisposeFn(fn: () => void) {\n    this._disposeFn = fn;\n  }\n\n  private _invokeDisposeFn() {\n    if (this._disposeFn) {\n      this._disposeFn();\n      this._disposeFn = null;\n    }\n  }\n}\n\n/**\n * @deprecated Use `BasePortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nexport abstract class BasePortalHost extends BasePortalOutlet {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ApplicationRef,\n  ComponentFactoryResolver,\n  ComponentRef,\n  EmbeddedViewRef,\n  Injector,\n} from '@angular/core';\nimport {BasePortalOutlet, ComponentPortal, DomPortal, TemplatePortal} from './portal';\n\n/**\n * A PortalOutlet for attaching portals to an arbitrary DOM element outside of the Angular\n * application context.\n */\nexport class DomPortalOutlet extends BasePortalOutlet {\n  private _document: Document;\n\n  /**\n   * @param outletElement Element into which the content is projected.\n   * @param _componentFactoryResolver Used to resolve the component factory.\n   *   Only required when attaching component portals.\n   * @param _appRef Reference to the application. Only used in component portals when there\n   *   is no `ViewContainerRef` available.\n   * @param _defaultInjector Injector to use as a fallback when the portal being attached doesn't\n   *   have one. Only used for component portals.\n   * @param _document Reference to the document. Used when attaching a DOM portal. Will eventually\n   *   become a required parameter.\n   */\n  constructor(\n    /** Element into which the content is projected. */\n    public outletElement: Element,\n    private _componentFactoryResolver?: ComponentFactoryResolver,\n    private _appRef?: ApplicationRef,\n    private _defaultInjector?: Injector,\n\n    /**\n     * @deprecated `_document` Parameter to be made required.\n     * @breaking-change 10.0.0\n     */\n    _document?: any,\n  ) {\n    super();\n    this._document = _document;\n  }\n\n  /**\n   * Attach the given ComponentPortal to DOM element using the ComponentFactoryResolver.\n   * @param portal Portal to be attached\n   * @returns Reference to the created component.\n   */\n  attachComponentPortal<T>(portal: ComponentPortal<T>): ComponentRef<T> {\n    const resolver = (portal.componentFactoryResolver || this._componentFactoryResolver)!;\n\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && !resolver) {\n      throw Error('Cannot attach component portal to outlet without a ComponentFactoryResolver.');\n    }\n\n    const componentFactory = resolver.resolveComponentFactory(portal.component);\n    let componentRef: ComponentRef<T>;\n\n    // If the portal specifies a ViewContainerRef, we will use that as the attachment point\n    // for the component (in terms of Angular's component tree, not rendering).\n    // When the ViewContainerRef is missing, we use the factory to create the component directly\n    // and then manually attach the view to the application.\n    if (portal.viewContainerRef) {\n      componentRef = portal.viewContainerRef.createComponent(\n        componentFactory,\n        portal.viewContainerRef.length,\n        portal.injector || portal.viewContainerRef.injector,\n        portal.projectableNodes || undefined,\n      );\n\n      this.setDisposeFn(() => componentRef.destroy());\n    } else {\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._appRef) {\n        throw Error('Cannot attach component portal to outlet without an ApplicationRef.');\n      }\n\n      componentRef = componentFactory.create(\n        portal.injector || this._defaultInjector || Injector.NULL,\n      );\n      this._appRef!.attachView(componentRef.hostView);\n      this.setDisposeFn(() => {\n        // Verify that the ApplicationRef has registered views before trying to detach a host view.\n        // This check also protects the `detachView` from being called on a destroyed ApplicationRef.\n        if (this._appRef!.viewCount > 0) {\n          this._appRef!.detachView(componentRef.hostView);\n        }\n        componentRef.destroy();\n      });\n    }\n    // At this point the component has been instantiated, so we move it to the location in the DOM\n    // where we want it to be rendered.\n    this.outletElement.appendChild(this._getComponentRootNode(componentRef));\n    this._attachedPortal = portal;\n\n    return componentRef;\n  }\n\n  /**\n   * Attaches a template portal to the DOM as an embedded view.\n   * @param portal Portal to be attached.\n   * @returns Reference to the created embedded view.\n   */\n  attachTemplatePortal<C>(portal: TemplatePortal<C>): EmbeddedViewRef<C> {\n    let viewContainer = portal.viewContainerRef;\n    let viewRef = viewContainer.createEmbeddedView(portal.templateRef, portal.context, {\n      injector: portal.injector,\n    });\n\n    // The method `createEmbeddedView` will add the view as a child of the viewContainer.\n    // But for the DomPortalOutlet the view can be added everywhere in the DOM\n    // (e.g Overlay Container) To move the view to the specified host element. We just\n    // re-append the existing root nodes.\n    viewRef.rootNodes.forEach(rootNode => this.outletElement.appendChild(rootNode));\n\n    // Note that we want to detect changes after the nodes have been moved so that\n    // any directives inside the portal that are looking at the DOM inside a lifecycle\n    // hook won't be invoked too early.\n    viewRef.detectChanges();\n\n    this.setDisposeFn(() => {\n      let index = viewContainer.indexOf(viewRef);\n      if (index !== -1) {\n        viewContainer.remove(index);\n      }\n    });\n\n    this._attachedPortal = portal;\n\n    // TODO(jelbourn): Return locals from view.\n    return viewRef;\n  }\n\n  /**\n   * Attaches a DOM portal by transferring its content into the outlet.\n   * @param portal Portal to be attached.\n   * @deprecated To be turned into a method.\n   * @breaking-change 10.0.0\n   */\n  override attachDomPortal = (portal: DomPortal) => {\n    // @breaking-change 10.0.0 Remove check and error once the\n    // `_document` constructor parameter is required.\n    if (!this._document && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Cannot attach DOM portal without _document constructor parameter');\n    }\n\n    const element = portal.element;\n    if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('DOM portal content must be attached to a parent node.');\n    }\n\n    // Anchor used to save the element's previous position so\n    // that we can restore it when the portal is detached.\n    const anchorNode = this._document.createComment('dom-portal');\n\n    element.parentNode!.insertBefore(anchorNode, element);\n    this.outletElement.appendChild(element);\n    this._attachedPortal = portal;\n\n    super.setDisposeFn(() => {\n      // We can't use `replaceWith` here because IE doesn't support it.\n      if (anchorNode.parentNode) {\n        anchorNode.parentNode.replaceChild(element, anchorNode);\n      }\n    });\n  };\n\n  /**\n   * Clears out a portal from the DOM.\n   */\n  override dispose(): void {\n    super.dispose();\n    this.outletElement.remove();\n  }\n\n  /** Gets the root HTMLElement for an instantiated component. */\n  private _getComponentRootNode(componentRef: ComponentRef<any>): HTMLElement {\n    return (componentRef.hostView as EmbeddedViewRef<any>).rootNodes[0] as HTMLElement;\n  }\n}\n\n/**\n * @deprecated Use `DomPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nexport class DomPortalHost extends DomPortalOutlet {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ComponentFactoryResolver,\n  ComponentRef,\n  Directive,\n  EmbeddedViewRef,\n  EventEmitter,\n  NgModule,\n  OnDestroy,\n  OnInit,\n  Output,\n  TemplateRef,\n  ViewContainerRef,\n  Inject,\n  Input,\n} from '@angular/core';\nimport {DOCUMENT} from '@angular/common';\nimport {BasePortalOutlet, ComponentPortal, Portal, TemplatePortal, DomPortal} from './portal';\n\n/**\n * Directive version of a `TemplatePortal`. Because the directive *is* a TemplatePortal,\n * the directive instance itself can be attached to a host, enabling declarative use of portals.\n */\n@Directive({\n  selector: '[cdkPortal]',\n  exportAs: 'cdkPortal',\n  standalone: true,\n})\nexport class CdkPortal extends TemplatePortal {\n  constructor(templateRef: TemplateRef<any>, viewContainerRef: ViewContainerRef) {\n    super(templateRef, viewContainerRef);\n  }\n}\n\n/**\n * @deprecated Use `CdkPortal` instead.\n * @breaking-change 9.0.0\n */\n@Directive({\n  selector: '[cdk-portal], [portal]',\n  exportAs: 'cdkPortal',\n  providers: [\n    {\n      provide: CdkPortal,\n      useExisting: TemplatePortalDirective,\n    },\n  ],\n  standalone: true,\n})\nexport class TemplatePortalDirective extends CdkPortal {}\n\n/**\n * Possible attached references to the CdkPortalOutlet.\n */\nexport type CdkPortalOutletAttachedRef = ComponentRef<any> | EmbeddedViewRef<any> | null;\n\n/**\n * Directive version of a PortalOutlet. Because the directive *is* a PortalOutlet, portals can be\n * directly attached to it, enabling declarative use.\n *\n * Usage:\n * `<ng-template [cdkPortalOutlet]=\"greeting\"></ng-template>`\n */\n@Directive({\n  selector: '[cdkPortalOutlet]',\n  exportAs: 'cdkPortalOutlet',\n  standalone: true,\n})\nexport class CdkPortalOutlet extends BasePortalOutlet implements OnInit, OnDestroy {\n  private _document: Document;\n\n  /** Whether the portal component is initialized. */\n  private _isInitialized = false;\n\n  /** Reference to the currently-attached component/view ref. */\n  private _attachedRef: CdkPortalOutletAttachedRef;\n\n  constructor(\n    private _componentFactoryResolver: ComponentFactoryResolver,\n    private _viewContainerRef: ViewContainerRef,\n\n    /**\n     * @deprecated `_document` parameter to be made required.\n     * @breaking-change 9.0.0\n     */\n    @Inject(DOCUMENT) _document?: any,\n  ) {\n    super();\n    this._document = _document;\n  }\n\n  /** Portal associated with the Portal outlet. */\n  @Input('cdkPortalOutlet')\n  get portal(): Portal<any> | null {\n    return this._attachedPortal;\n  }\n\n  set portal(portal: Portal<any> | null | undefined | '') {\n    // Ignore the cases where the `portal` is set to a falsy value before the lifecycle hooks have\n    // run. This handles the cases where the user might do something like `<div cdkPortalOutlet>`\n    // and attach a portal programmatically in the parent component. When Angular does the first CD\n    // round, it will fire the setter with empty string, causing the user's content to be cleared.\n    if (this.hasAttached() && !portal && !this._isInitialized) {\n      return;\n    }\n\n    if (this.hasAttached()) {\n      super.detach();\n    }\n\n    if (portal) {\n      super.attach(portal);\n    }\n\n    this._attachedPortal = portal || null;\n  }\n\n  /** Emits when a portal is attached to the outlet. */\n  @Output() readonly attached: EventEmitter<CdkPortalOutletAttachedRef> =\n    new EventEmitter<CdkPortalOutletAttachedRef>();\n\n  /** Component or view reference that is attached to the portal. */\n  get attachedRef(): CdkPortalOutletAttachedRef {\n    return this._attachedRef;\n  }\n\n  ngOnInit() {\n    this._isInitialized = true;\n  }\n\n  ngOnDestroy() {\n    super.dispose();\n    this._attachedRef = this._attachedPortal = null;\n  }\n\n  /**\n   * Attach the given ComponentPortal to this PortalOutlet using the ComponentFactoryResolver.\n   *\n   * @param portal Portal to be attached to the portal outlet.\n   * @returns Reference to the created component.\n   */\n  attachComponentPortal<T>(portal: ComponentPortal<T>): ComponentRef<T> {\n    portal.setAttachedHost(this);\n\n    // If the portal specifies an origin, use that as the logical location of the component\n    // in the application tree. Otherwise use the location of this PortalOutlet.\n    const viewContainerRef =\n      portal.viewContainerRef != null ? portal.viewContainerRef : this._viewContainerRef;\n\n    const resolver = portal.componentFactoryResolver || this._componentFactoryResolver;\n    const componentFactory = resolver.resolveComponentFactory(portal.component);\n    const ref = viewContainerRef.createComponent(\n      componentFactory,\n      viewContainerRef.length,\n      portal.injector || viewContainerRef.injector,\n      portal.projectableNodes || undefined,\n    );\n\n    // If we're using a view container that's different from the injected one (e.g. when the portal\n    // specifies its own) we need to move the component into the outlet, otherwise it'll be rendered\n    // inside of the alternate view container.\n    if (viewContainerRef !== this._viewContainerRef) {\n      this._getRootNode().appendChild((ref.hostView as EmbeddedViewRef<any>).rootNodes[0]);\n    }\n\n    super.setDisposeFn(() => ref.destroy());\n    this._attachedPortal = portal;\n    this._attachedRef = ref;\n    this.attached.emit(ref);\n\n    return ref;\n  }\n\n  /**\n   * Attach the given TemplatePortal to this PortalHost as an embedded View.\n   * @param portal Portal to be attached.\n   * @returns Reference to the created embedded view.\n   */\n  attachTemplatePortal<C>(portal: TemplatePortal<C>): EmbeddedViewRef<C> {\n    portal.setAttachedHost(this);\n    const viewRef = this._viewContainerRef.createEmbeddedView(portal.templateRef, portal.context, {\n      injector: portal.injector,\n    });\n    super.setDisposeFn(() => this._viewContainerRef.clear());\n\n    this._attachedPortal = portal;\n    this._attachedRef = viewRef;\n    this.attached.emit(viewRef);\n\n    return viewRef;\n  }\n\n  /**\n   * Attaches the given DomPortal to this PortalHost by moving all of the portal content into it.\n   * @param portal Portal to be attached.\n   * @deprecated To be turned into a method.\n   * @breaking-change 10.0.0\n   */\n  override attachDomPortal = (portal: DomPortal) => {\n    // @breaking-change 9.0.0 Remove check and error once the\n    // `_document` constructor parameter is required.\n    if (!this._document && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Cannot attach DOM portal without _document constructor parameter');\n    }\n\n    const element = portal.element;\n    if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('DOM portal content must be attached to a parent node.');\n    }\n\n    // Anchor used to save the element's previous position so\n    // that we can restore it when the portal is detached.\n    const anchorNode = this._document.createComment('dom-portal');\n\n    portal.setAttachedHost(this);\n    element.parentNode!.insertBefore(anchorNode, element);\n    this._getRootNode().appendChild(element);\n    this._attachedPortal = portal;\n\n    super.setDisposeFn(() => {\n      if (anchorNode.parentNode) {\n        anchorNode.parentNode!.replaceChild(element, anchorNode);\n      }\n    });\n  };\n\n  /** Gets the root node of the portal outlet. */\n  private _getRootNode(): HTMLElement {\n    const nativeElement: Node = this._viewContainerRef.element.nativeElement;\n\n    // The directive could be set on a template which will result in a comment\n    // node being the root. Use the comment's parent node if that is the case.\n    return (\n      nativeElement.nodeType === nativeElement.ELEMENT_NODE\n        ? nativeElement\n        : nativeElement.parentNode!\n    ) as HTMLElement;\n  }\n}\n\n/**\n * @deprecated Use `CdkPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\n@Directive({\n  selector: '[cdkPortalHost], [portalHost]',\n  exportAs: 'cdkPortalHost',\n  inputs: [{name: 'portal', alias: 'cdkPortalHost'}],\n  providers: [\n    {\n      provide: CdkPortalOutlet,\n      useExisting: PortalHostDirective,\n    },\n  ],\n  standalone: true,\n})\nexport class PortalHostDirective extends CdkPortalOutlet {}\n\n@NgModule({\n  imports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n  exports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n})\nexport class PortalModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injector} from '@angular/core';\n\n/**\n * Custom injector to be used when providing custom\n * injection tokens to components inside a portal.\n * @docs-private\n * @deprecated Use `Injector.create` instead.\n * @breaking-change 11.0.0\n */\nexport class PortalInjector implements Injector {\n  constructor(\n    private _parentInjector: Injector,\n    private _customTokens: WeakMap<any, any>,\n  ) {}\n\n  get(token: any, notFoundValue?: any): any {\n    const value = this._customTokens.get(token);\n\n    if (typeof value !== 'undefined') {\n      return value;\n    }\n\n    return this._parentInjector.get<any>(token, notFoundValue);\n  }\n}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;AAQA;;;AAGG;SACa,oBAAoB,GAAA;AAClC,IAAA,MAAM,KAAK,CAAC,iCAAiC,CAAC,CAAC;AACjD,CAAC;AAED;;;AAGG;SACa,+BAA+B,GAAA;AAC7C,IAAA,MAAM,KAAK,CAAC,oCAAoC,CAAC,CAAC;AACpD,CAAC;AAED;;;AAGG;SACa,qCAAqC,GAAA;AACnD,IAAA,MAAM,KAAK,CAAC,6CAA6C,CAAC,CAAC;AAC7D,CAAC;AAED;;;AAGG;SACa,2BAA2B,GAAA;IACzC,MAAM,KAAK,CACT,+EAA+E;AAC7E,QAAA,wCAAwC,CAC3C,CAAC;AACJ,CAAC;AAED;;;AAGG;SACa,0BAA0B,GAAA;AACxC,IAAA,MAAM,KAAK,CAAC,sDAAsD,CAAC,CAAC;AACtE,CAAC;AAED;;;AAGG;SACa,0BAA0B,GAAA;AACxC,IAAA,MAAM,KAAK,CAAC,8DAA8D,CAAC,CAAC;AAC9E;;AC1BA;;;AAGG;MACmB,MAAM,CAAA;;AAI1B,IAAA,MAAM,CAAC,IAAkB,EAAA;AACvB,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,IAAI,IAAI,IAAI,IAAI,EAAE;AAChB,gBAAA,0BAA0B,EAAE,CAAC;aAC9B;AAED,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;AACtB,gBAAA,+BAA+B,EAAE,CAAC;aACnC;SACF;AAED,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,QAAA,OAAU,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;KAC7B;;IAGD,MAAM,GAAA;AACJ,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC;AAE9B,QAAA,IAAI,IAAI,IAAI,IAAI,EAAE;AAChB,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,MAAM,EAAE,CAAC;SACf;AAAM,aAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACxD,YAAA,0BAA0B,EAAE,CAAC;SAC9B;KACF;;AAGD,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC;KACnC;AAED;;;AAGG;AACH,IAAA,eAAe,CAAC,IAAyB,EAAA;AACvC,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;KAC3B;AACF,CAAA;AAED;;AAEG;AACG,MAAO,eAAmB,SAAQ,MAAuB,CAAA;IAyB7D,WACE,CAAA,SAA2B,EAC3B,gBAA0C,EAC1C,QAA0B,EAC1B,wBAA0D,EAC1D,gBAAkC,EAAA;AAElC,QAAA,KAAK,EAAE,CAAC;AACR,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC3B,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AACzC,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,QAAA,IAAI,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;AACzD,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;KAC1C;AACF,CAAA;AAED;;AAEG;AACG,MAAO,cAAwB,SAAQ,MAA0B,CAAA;AACrE,IAAA,WAAA;;IAES,WAA2B;;IAE3B,gBAAkC;;IAElC,OAAW;;IAEX,QAAmB,EAAA;AAE1B,QAAA,KAAK,EAAE,CAAC;QARD,IAAW,CAAA,WAAA,GAAX,WAAW,CAAgB;QAE3B,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAkB;QAElC,IAAO,CAAA,OAAA,GAAP,OAAO,CAAI;QAEX,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;KAG3B;AAED,IAAA,IAAI,MAAM,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;KACpC;AAED;;;;AAIG;AACM,IAAA,MAAM,CAAC,IAAkB,EAAE,OAAyB,GAAA,IAAI,CAAC,OAAO,EAAA;AACvE,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AACvB,QAAA,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;KAC3B;IAEQ,MAAM,GAAA;AACb,QAAA,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;AACzB,QAAA,OAAO,KAAK,CAAC,MAAM,EAAE,CAAC;KACvB;AACF,CAAA;AAED;;;;AAIG;AACG,MAAO,SAA2B,SAAQ,MAAS,CAAA;AAIvD,IAAA,WAAA,CAAY,OAA0B,EAAA;AACpC,QAAA,KAAK,EAAE,CAAC;AACR,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,YAAY,UAAU,GAAG,OAAO,CAAC,aAAa,GAAG,OAAO,CAAC;KAChF;AACF,CAAA;AAuBD;;;AAGG;MACmB,gBAAgB,CAAA;AAAtC,IAAA,WAAA,GAAA;;QAQU,IAAW,CAAA,WAAA,GAAY,KAAK,CAAC;;QAiD5B,IAAe,CAAA,eAAA,GAAwC,IAAI,CAAC;KAiCtE;;IA/EC,WAAW,GAAA;AACT,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;KAC/B;;AAOD,IAAA,MAAM,CAAC,MAAmB,EAAA;AACxB,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;YACjD,IAAI,CAAC,MAAM,EAAE;AACX,gBAAA,oBAAoB,EAAE,CAAC;aACxB;AAED,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;AACtB,gBAAA,+BAA+B,EAAE,CAAC;aACnC;AAED,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,gBAAA,qCAAqC,EAAE,CAAC;aACzC;SACF;AAED,QAAA,IAAI,MAAM,YAAY,eAAe,EAAE;AACrC,YAAA,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;AAC9B,YAAA,OAAO,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;SAC3C;AAAM,aAAA,IAAI,MAAM,YAAY,cAAc,EAAE;AAC3C,YAAA,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;AAC9B,YAAA,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;;SAE1C;aAAM,IAAI,IAAI,CAAC,eAAe,IAAI,MAAM,YAAY,SAAS,EAAE;AAC9D,YAAA,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;AAC9B,YAAA,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;SACrC;AAED,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,2BAA2B,EAAE,CAAC;SAC/B;KACF;;IAUD,MAAM,GAAA;AACJ,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AACxB,YAAA,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;AAC3C,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;SAC7B;QAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;KACzB;;IAGD,OAAO,GAAA;AACL,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YACtB,IAAI,CAAC,MAAM,EAAE,CAAC;SACf;QAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;KACzB;;AAGD,IAAA,YAAY,CAAC,EAAc,EAAA;AACzB,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;KACtB;IAEO,gBAAgB,GAAA;AACtB,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,UAAU,EAAE,CAAC;AAClB,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;SACxB;KACF;AACF,CAAA;AAED;;;AAGG;AACG,MAAgB,cAAe,SAAQ,gBAAgB,CAAA;AAAG;;ACxRhE;;;AAGG;AACG,MAAO,eAAgB,SAAQ,gBAAgB,CAAA;AAGnD;;;;;;;;;;AAUG;AACH,IAAA,WAAA;;AAES,IAAA,aAAsB,EACrB,yBAAoD,EACpD,OAAwB,EACxB,gBAA2B;AAEnC;;;AAGG;IACH,SAAe,EAAA;AAEf,QAAA,KAAK,EAAE,CAAC;QAXD,IAAa,CAAA,aAAA,GAAb,aAAa,CAAS;QACrB,IAAyB,CAAA,yBAAA,GAAzB,yBAAyB,CAA2B;QACpD,IAAO,CAAA,OAAA,GAAP,OAAO,CAAiB;QACxB,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAW;AAqGrC;;;;;AAKG;AACM,QAAA,IAAA,CAAA,eAAe,GAAG,CAAC,MAAiB,KAAI;;;AAG/C,YAAA,IAAI,CAAC,IAAI,CAAC,SAAS,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AACtE,gBAAA,MAAM,KAAK,CAAC,kEAAkE,CAAC,CAAC;aACjF;AAED,YAAA,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;AAC/B,YAAA,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAC1E,gBAAA,MAAM,KAAK,CAAC,uDAAuD,CAAC,CAAC;aACtE;;;YAID,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;YAE9D,OAAO,CAAC,UAAW,CAAC,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;AACtD,YAAA,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AACxC,YAAA,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;AAE9B,YAAA,KAAK,CAAC,YAAY,CAAC,MAAK;;AAEtB,gBAAA,IAAI,UAAU,CAAC,UAAU,EAAE;oBACzB,UAAU,CAAC,UAAU,CAAC,YAAY,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;iBACzD;AACH,aAAC,CAAC,CAAC;AACL,SAAC,CAAC;AA5HA,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;KAC5B;AAED;;;;AAIG;AACH,IAAA,qBAAqB,CAAI,MAA0B,EAAA;QACjD,MAAM,QAAQ,IAAI,MAAM,CAAC,wBAAwB,IAAI,IAAI,CAAC,yBAAyB,CAAE,CAAC;AAEtF,QAAA,IAAI,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,CAAC,QAAQ,EAAE;AAChE,YAAA,MAAM,KAAK,CAAC,8EAA8E,CAAC,CAAC;SAC7F;QAED,MAAM,gBAAgB,GAAG,QAAQ,CAAC,uBAAuB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AAC5E,QAAA,IAAI,YAA6B,CAAC;;;;;AAMlC,QAAA,IAAI,MAAM,CAAC,gBAAgB,EAAE;AAC3B,YAAA,YAAY,GAAG,MAAM,CAAC,gBAAgB,CAAC,eAAe,CACpD,gBAAgB,EAChB,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAC9B,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EACnD,MAAM,CAAC,gBAAgB,IAAI,SAAS,CACrC,CAAC;YAEF,IAAI,CAAC,YAAY,CAAC,MAAM,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC;SACjD;aAAM;AACL,YAAA,IAAI,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE;AACpE,gBAAA,MAAM,KAAK,CAAC,qEAAqE,CAAC,CAAC;aACpF;AAED,YAAA,YAAY,GAAG,gBAAgB,CAAC,MAAM,CACpC,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,gBAAgB,IAAI,QAAQ,CAAC,IAAI,CAC1D,CAAC;YACF,IAAI,CAAC,OAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;AAChD,YAAA,IAAI,CAAC,YAAY,CAAC,MAAK;;;gBAGrB,IAAI,IAAI,CAAC,OAAQ,CAAC,SAAS,GAAG,CAAC,EAAE;oBAC/B,IAAI,CAAC,OAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;iBACjD;gBACD,YAAY,CAAC,OAAO,EAAE,CAAC;AACzB,aAAC,CAAC,CAAC;SACJ;;;AAGD,QAAA,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC,CAAC;AACzE,QAAA,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;AAE9B,QAAA,OAAO,YAAY,CAAC;KACrB;AAED;;;;AAIG;AACH,IAAA,oBAAoB,CAAI,MAAyB,EAAA;AAC/C,QAAA,IAAI,aAAa,GAAG,MAAM,CAAC,gBAAgB,CAAC;AAC5C,QAAA,IAAI,OAAO,GAAG,aAAa,CAAC,kBAAkB,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,OAAO,EAAE;YACjF,QAAQ,EAAE,MAAM,CAAC,QAAQ;AAC1B,SAAA,CAAC,CAAC;;;;;AAMH,QAAA,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;;;;QAKhF,OAAO,CAAC,aAAa,EAAE,CAAC;AAExB,QAAA,IAAI,CAAC,YAAY,CAAC,MAAK;YACrB,IAAI,KAAK,GAAG,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC3C,YAAA,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;AAChB,gBAAA,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAC7B;AACH,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;;AAG9B,QAAA,OAAO,OAAO,CAAC;KAChB;AAoCD;;AAEG;IACM,OAAO,GAAA;QACd,KAAK,CAAC,OAAO,EAAE,CAAC;AAChB,QAAA,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;KAC7B;;AAGO,IAAA,qBAAqB,CAAC,YAA+B,EAAA;QAC3D,OAAQ,YAAY,CAAC,QAAiC,CAAC,SAAS,CAAC,CAAC,CAAgB,CAAC;KACpF;AACF,CAAA;AAED;;;AAGG;AACG,MAAO,aAAc,SAAQ,eAAe,CAAA;AAAG;;ACvKrD;;;AAGG;AAMG,MAAO,SAAU,SAAQ,cAAc,CAAA;IAC3C,WAAY,CAAA,WAA6B,EAAE,gBAAkC,EAAA;AAC3E,QAAA,KAAK,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;KACtC;8GAHU,SAAS,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,WAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAT,SAAS,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,aAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAT,SAAS,EAAA,UAAA,EAAA,CAAA;kBALrB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,aAAa;AACvB,oBAAA,QAAQ,EAAE,WAAW;AACrB,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;AAOD;;;AAGG;AAYG,MAAO,uBAAwB,SAAQ,SAAS,CAAA;8GAAzC,uBAAuB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAvB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,uBAAuB,EARvB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,wBAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,SAAS;AAClB,gBAAA,WAAW,EAAE,uBAAuB;AACrC,aAAA;AACF,SAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAGU,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBAXnC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,wBAAwB;AAClC,oBAAA,QAAQ,EAAE,WAAW;AACrB,oBAAA,SAAS,EAAE;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,SAAS;AAClB,4BAAA,WAAW,EAAyB,uBAAA;AACrC,yBAAA;AACF,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;AAQD;;;;;;AAMG;AAMG,MAAO,eAAgB,SAAQ,gBAAgB,CAAA;IASnD,WACU,CAAA,yBAAmD,EACnD,iBAAmC;AAE3C;;;AAGG;IACe,SAAe,EAAA;AAEjC,QAAA,KAAK,EAAE,CAAC;QATA,IAAyB,CAAA,yBAAA,GAAzB,yBAAyB,CAA0B;QACnD,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAkB;;QAPrC,IAAc,CAAA,cAAA,GAAG,KAAK,CAAC;;AA8CZ,QAAA,IAAA,CAAA,QAAQ,GACzB,IAAI,YAAY,EAA8B,CAAC;AAyEjD;;;;;AAKG;AACM,QAAA,IAAA,CAAA,eAAe,GAAG,CAAC,MAAiB,KAAI;;;AAG/C,YAAA,IAAI,CAAC,IAAI,CAAC,SAAS,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AACtE,gBAAA,MAAM,KAAK,CAAC,kEAAkE,CAAC,CAAC;aACjF;AAED,YAAA,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;AAC/B,YAAA,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAC1E,gBAAA,MAAM,KAAK,CAAC,uDAAuD,CAAC,CAAC;aACtE;;;YAID,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;AAE9D,YAAA,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC7B,OAAO,CAAC,UAAW,CAAC,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YACtD,IAAI,CAAC,YAAY,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AACzC,YAAA,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;AAE9B,YAAA,KAAK,CAAC,YAAY,CAAC,MAAK;AACtB,gBAAA,IAAI,UAAU,CAAC,UAAU,EAAE;oBACzB,UAAU,CAAC,UAAW,CAAC,YAAY,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;iBAC1D;AACH,aAAC,CAAC,CAAC;AACL,SAAC,CAAC;AAxIA,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;KAC5B;;AAGD,IAAA,IACI,MAAM,GAAA;QACR,OAAO,IAAI,CAAC,eAAe,CAAC;KAC7B;IAED,IAAI,MAAM,CAAC,MAA2C,EAAA;;;;;AAKpD,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACzD,OAAO;SACR;AAED,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YACtB,KAAK,CAAC,MAAM,EAAE,CAAC;SAChB;QAED,IAAI,MAAM,EAAE;AACV,YAAA,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;SACtB;AAED,QAAA,IAAI,CAAC,eAAe,GAAG,MAAM,IAAI,IAAI,CAAC;KACvC;;AAOD,IAAA,IAAI,WAAW,GAAA;QACb,OAAO,IAAI,CAAC,YAAY,CAAC;KAC1B;IAED,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;KAC5B;IAED,WAAW,GAAA;QACT,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;KACjD;AAED;;;;;AAKG;AACH,IAAA,qBAAqB,CAAI,MAA0B,EAAA;AACjD,QAAA,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;;;AAI7B,QAAA,MAAM,gBAAgB,GACpB,MAAM,CAAC,gBAAgB,IAAI,IAAI,GAAG,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAErF,MAAM,QAAQ,GAAG,MAAM,CAAC,wBAAwB,IAAI,IAAI,CAAC,yBAAyB,CAAC;QACnF,MAAM,gBAAgB,GAAG,QAAQ,CAAC,uBAAuB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAC5E,MAAM,GAAG,GAAG,gBAAgB,CAAC,eAAe,CAC1C,gBAAgB,EAChB,gBAAgB,CAAC,MAAM,EACvB,MAAM,CAAC,QAAQ,IAAI,gBAAgB,CAAC,QAAQ,EAC5C,MAAM,CAAC,gBAAgB,IAAI,SAAS,CACrC,CAAC;;;;AAKF,QAAA,IAAI,gBAAgB,KAAK,IAAI,CAAC,iBAAiB,EAAE;AAC/C,YAAA,IAAI,CAAC,YAAY,EAAE,CAAC,WAAW,CAAE,GAAG,CAAC,QAAiC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;SACtF;QAED,KAAK,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;AACxC,QAAA,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;AAC9B,QAAA,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC;AACxB,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAExB,QAAA,OAAO,GAAG,CAAC;KACZ;AAED;;;;AAIG;AACH,IAAA,oBAAoB,CAAI,MAAyB,EAAA;AAC/C,QAAA,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,OAAO,EAAE;YAC5F,QAAQ,EAAE,MAAM,CAAC,QAAQ;AAC1B,SAAA,CAAC,CAAC;AACH,QAAA,KAAK,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC,CAAC;AAEzD,QAAA,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;AAC9B,QAAA,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC;AAC5B,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAE5B,QAAA,OAAO,OAAO,CAAC;KAChB;;IAqCO,YAAY,GAAA;QAClB,MAAM,aAAa,GAAS,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,aAAa,CAAC;;;AAIzE,QAAA,QACE,aAAa,CAAC,QAAQ,KAAK,aAAa,CAAC,YAAY;AACnD,cAAE,aAAa;AACf,cAAE,aAAa,CAAC,UAAW,EACd;KAClB;AAzKU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,0FAiBhB,QAAQ,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAjBP,eAAe,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,QAAA,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAL3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,mBAAmB;AAC7B,oBAAA,QAAQ,EAAE,iBAAiB;AAC3B,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BAkBI,MAAM;2BAAC,QAAQ,CAAA;yCAQd,MAAM,EAAA,CAAA;sBADT,KAAK;uBAAC,iBAAiB,CAAA;gBA0BL,QAAQ,EAAA,CAAA;sBAA1B,MAAM;;AA0HT;;;AAGG;AAaG,MAAO,mBAAoB,SAAQ,eAAe,CAAA;8GAA3C,mBAAmB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAnB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,mBAAmB,EARnB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,+BAAA,EAAA,MAAA,EAAA,EAAA,MAAA,EAAA,CAAA,eAAA,EAAA,QAAA,CAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,eAAe;AACxB,gBAAA,WAAW,EAAE,mBAAmB;AACjC,aAAA;AACF,SAAA,EAAA,QAAA,EAAA,CAAA,eAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAGU,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAZ/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,+BAA+B;AACzC,oBAAA,QAAQ,EAAE,eAAe;oBACzB,MAAM,EAAE,CAAC,EAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,eAAe,EAAC,CAAC;AAClD,oBAAA,SAAS,EAAE;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,eAAe;AACxB,4BAAA,WAAW,EAAqB,mBAAA;AACjC,yBAAA;AACF,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;MAOY,YAAY,CAAA;8GAAZ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAZ,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,EA1OZ,OAAA,EAAA,CAAA,SAAS,EAwCT,eAAe,EAnBf,uBAAuB,EA+MvB,mBAAmB,CAAA,EAAA,OAAA,EAAA,CApOnB,SAAS,EAwCT,eAAe,EAnBf,uBAAuB,EA+MvB,mBAAmB,CAAA,EAAA,CAAA,CAAA,EAAA;+GAMnB,YAAY,EAAA,CAAA,CAAA,EAAA;;2FAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBAJxB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,SAAS,EAAE,eAAe,EAAE,uBAAuB,EAAE,mBAAmB,CAAC;oBACnF,OAAO,EAAE,CAAC,SAAS,EAAE,eAAe,EAAE,uBAAuB,EAAE,mBAAmB,CAAC;AACpF,iBAAA,CAAA;;;AClQD;;;;;;AAMG;MACU,cAAc,CAAA;IACzB,WACU,CAAA,eAAyB,EACzB,aAAgC,EAAA;QADhC,IAAe,CAAA,eAAA,GAAf,eAAe,CAAU;QACzB,IAAa,CAAA,aAAA,GAAb,aAAa,CAAmB;KACtC;IAEJ,GAAG,CAAC,KAAU,EAAE,aAAmB,EAAA;QACjC,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAE5C,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAChC,YAAA,OAAO,KAAK,CAAC;SACd;QAED,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAM,KAAK,EAAE,aAAa,CAAC,CAAC;KAC5D;AACF;;AChCD;;AAEG;;;;"}