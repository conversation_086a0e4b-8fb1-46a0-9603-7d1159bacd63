/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { PLACEHOLDER_QUALITY } from './constants';
import { createImageLoader } from './image_loader';
/**
 * Name and URL tester for Imgix.
 */
export const imgixLoaderInfo = {
    name: 'Imgix',
    testUrl: isImgixUrl,
};
const IMGIX_LOADER_REGEX = /https?\:\/\/[^\/]+\.imgix\.net\/.+/;
/**
 * Tests whether a URL is from Imgix CDN.
 */
function isImgixUrl(url) {
    return IMGIX_LOADER_REGEX.test(url);
}
/**
 * Function that generates an ImageLoader for Imgix and turns it into an Angular provider.
 *
 * @param path path to the desired Imgix origin,
 * e.g. https://somepath.imgix.net or https://images.mysite.com
 * @returns Set of providers to configure the Imgix loader.
 *
 * @publicApi
 */
export const provideImgixLoader = createImageLoader(createImgixUrl, ngDevMode ? ['https://somepath.imgix.net/'] : undefined);
function createImgixUrl(path, config) {
    const url = new URL(`${path}/${config.src}`);
    // This setting ensures the smallest allowable format is set.
    url.searchParams.set('auto', 'format');
    if (config.width) {
        url.searchParams.set('w', config.width.toString());
    }
    // When requesting a placeholder image we ask a low quality image to reduce the load time.
    if (config.isPlaceholder) {
        url.searchParams.set('q', PLACEHOLDER_QUALITY);
    }
    return url.href;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW1naXhfbG9hZGVyLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vLi4vLi4vLi4vLi4vcGFja2FnZXMvY29tbW9uL3NyYy9kaXJlY3RpdmVzL25nX29wdGltaXplZF9pbWFnZS9pbWFnZV9sb2FkZXJzL2ltZ2l4X2xvYWRlci50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7O0dBTUc7QUFFSCxPQUFPLEVBQUMsbUJBQW1CLEVBQUMsTUFBTSxhQUFhLENBQUM7QUFDaEQsT0FBTyxFQUFDLGlCQUFpQixFQUFxQyxNQUFNLGdCQUFnQixDQUFDO0FBRXJGOztHQUVHO0FBQ0gsTUFBTSxDQUFDLE1BQU0sZUFBZSxHQUFvQjtJQUM5QyxJQUFJLEVBQUUsT0FBTztJQUNiLE9BQU8sRUFBRSxVQUFVO0NBQ3BCLENBQUM7QUFFRixNQUFNLGtCQUFrQixHQUFHLG9DQUFvQyxDQUFDO0FBQ2hFOztHQUVHO0FBQ0gsU0FBUyxVQUFVLENBQUMsR0FBVztJQUM3QixPQUFPLGtCQUFrQixDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQztBQUN0QyxDQUFDO0FBRUQ7Ozs7Ozs7O0dBUUc7QUFDSCxNQUFNLENBQUMsTUFBTSxrQkFBa0IsR0FBRyxpQkFBaUIsQ0FDakQsY0FBYyxFQUNkLFNBQVMsQ0FBQyxDQUFDLENBQUMsQ0FBQyw2QkFBNkIsQ0FBQyxDQUFDLENBQUMsQ0FBQyxTQUFTLENBQ3hELENBQUM7QUFFRixTQUFTLGNBQWMsQ0FBQyxJQUFZLEVBQUUsTUFBeUI7SUFDN0QsTUFBTSxHQUFHLEdBQUcsSUFBSSxHQUFHLENBQUMsR0FBRyxJQUFJLElBQUksTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLENBQUM7SUFDN0MsNkRBQTZEO0lBQzdELEdBQUcsQ0FBQyxZQUFZLENBQUMsR0FBRyxDQUFDLE1BQU0sRUFBRSxRQUFRLENBQUMsQ0FBQztJQUN2QyxJQUFJLE1BQU0sQ0FBQyxLQUFLLEVBQUUsQ0FBQztRQUNqQixHQUFHLENBQUMsWUFBWSxDQUFDLEdBQUcsQ0FBQyxHQUFHLEVBQUUsTUFBTSxDQUFDLEtBQUssQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDO0lBQ3JELENBQUM7SUFFRCwwRkFBMEY7SUFDMUYsSUFBSSxNQUFNLENBQUMsYUFBYSxFQUFFLENBQUM7UUFDekIsR0FBRyxDQUFDLFlBQVksQ0FBQyxHQUFHLENBQUMsR0FBRyxFQUFFLG1CQUFtQixDQUFDLENBQUM7SUFDakQsQ0FBQztJQUNELE9BQU8sR0FBRyxDQUFDLElBQUksQ0FBQztBQUNsQixDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5cbmltcG9ydCB7UExBQ0VIT0xERVJfUVVBTElUWX0gZnJvbSAnLi9jb25zdGFudHMnO1xuaW1wb3J0IHtjcmVhdGVJbWFnZUxvYWRlciwgSW1hZ2VMb2FkZXJDb25maWcsIEltYWdlTG9hZGVySW5mb30gZnJvbSAnLi9pbWFnZV9sb2FkZXInO1xuXG4vKipcbiAqIE5hbWUgYW5kIFVSTCB0ZXN0ZXIgZm9yIEltZ2l4LlxuICovXG5leHBvcnQgY29uc3QgaW1naXhMb2FkZXJJbmZvOiBJbWFnZUxvYWRlckluZm8gPSB7XG4gIG5hbWU6ICdJbWdpeCcsXG4gIHRlc3RVcmw6IGlzSW1naXhVcmwsXG59O1xuXG5jb25zdCBJTUdJWF9MT0FERVJfUkVHRVggPSAvaHR0cHM/XFw6XFwvXFwvW15cXC9dK1xcLmltZ2l4XFwubmV0XFwvLisvO1xuLyoqXG4gKiBUZXN0cyB3aGV0aGVyIGEgVVJMIGlzIGZyb20gSW1naXggQ0ROLlxuICovXG5mdW5jdGlvbiBpc0ltZ2l4VXJsKHVybDogc3RyaW5nKTogYm9vbGVhbiB7XG4gIHJldHVybiBJTUdJWF9MT0FERVJfUkVHRVgudGVzdCh1cmwpO1xufVxuXG4vKipcbiAqIEZ1bmN0aW9uIHRoYXQgZ2VuZXJhdGVzIGFuIEltYWdlTG9hZGVyIGZvciBJbWdpeCBhbmQgdHVybnMgaXQgaW50byBhbiBBbmd1bGFyIHByb3ZpZGVyLlxuICpcbiAqIEBwYXJhbSBwYXRoIHBhdGggdG8gdGhlIGRlc2lyZWQgSW1naXggb3JpZ2luLFxuICogZS5nLiBodHRwczovL3NvbWVwYXRoLmltZ2l4Lm5ldCBvciBodHRwczovL2ltYWdlcy5teXNpdGUuY29tXG4gKiBAcmV0dXJucyBTZXQgb2YgcHJvdmlkZXJzIHRvIGNvbmZpZ3VyZSB0aGUgSW1naXggbG9hZGVyLlxuICpcbiAqIEBwdWJsaWNBcGlcbiAqL1xuZXhwb3J0IGNvbnN0IHByb3ZpZGVJbWdpeExvYWRlciA9IGNyZWF0ZUltYWdlTG9hZGVyKFxuICBjcmVhdGVJbWdpeFVybCxcbiAgbmdEZXZNb2RlID8gWydodHRwczovL3NvbWVwYXRoLmltZ2l4Lm5ldC8nXSA6IHVuZGVmaW5lZCxcbik7XG5cbmZ1bmN0aW9uIGNyZWF0ZUltZ2l4VXJsKHBhdGg6IHN0cmluZywgY29uZmlnOiBJbWFnZUxvYWRlckNvbmZpZykge1xuICBjb25zdCB1cmwgPSBuZXcgVVJMKGAke3BhdGh9LyR7Y29uZmlnLnNyY31gKTtcbiAgLy8gVGhpcyBzZXR0aW5nIGVuc3VyZXMgdGhlIHNtYWxsZXN0IGFsbG93YWJsZSBmb3JtYXQgaXMgc2V0LlxuICB1cmwuc2VhcmNoUGFyYW1zLnNldCgnYXV0bycsICdmb3JtYXQnKTtcbiAgaWYgKGNvbmZpZy53aWR0aCkge1xuICAgIHVybC5zZWFyY2hQYXJhbXMuc2V0KCd3JywgY29uZmlnLndpZHRoLnRvU3RyaW5nKCkpO1xuICB9XG5cbiAgLy8gV2hlbiByZXF1ZXN0aW5nIGEgcGxhY2Vob2xkZXIgaW1hZ2Ugd2UgYXNrIGEgbG93IHF1YWxpdHkgaW1hZ2UgdG8gcmVkdWNlIHRoZSBsb2FkIHRpbWUuXG4gIGlmIChjb25maWcuaXNQbGFjZWhvbGRlcikge1xuICAgIHVybC5zZWFyY2hQYXJhbXMuc2V0KCdxJywgUExBQ0VIT0xERVJfUVVBTElUWSk7XG4gIH1cbiAgcmV0dXJuIHVybC5ocmVmO1xufVxuIl19