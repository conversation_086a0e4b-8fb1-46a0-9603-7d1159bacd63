{"version": 3, "names": ["Plug<PERSON><PERSON><PERSON>", "constructor", "file", "key", "options", "_map", "Map", "opts", "cwd", "filename", "set", "val", "get", "availableHelper", "name", "versionRange", "addHelper", "buildCodeFrameError", "node", "msg", "_Error", "exports", "default", "prototype", "getModuleName", "addImport"], "sources": ["../../src/transformation/plugin-pass.ts"], "sourcesContent": ["import type File from \"./file/file.ts\";\nimport type { NodeLocation } from \"./file/file.ts\";\n\nexport default class PluginPass<Options = {}> {\n  _map: Map<unknown, unknown> = new Map();\n  key: string | undefined | null;\n  file: File;\n  opts: Partial<Options>;\n\n  // The working directory that <PERSON><PERSON>'s programmatic options are loaded\n  // relative to.\n  cwd: string;\n\n  // The absolute path of the file being compiled.\n  filename: string | void;\n\n  constructor(file: File, key?: string | null, options?: Options) {\n    this.key = key;\n    this.file = file;\n    this.opts = options || {};\n    this.cwd = file.opts.cwd;\n    this.filename = file.opts.filename;\n  }\n\n  set(key: unknown, val: unknown) {\n    this._map.set(key, val);\n  }\n\n  get(key: unknown): any {\n    return this._map.get(key);\n  }\n\n  availableHelper(name: string, versionRange?: string | null) {\n    return this.file.availableHelper(name, versionRange);\n  }\n\n  addHelper(name: string) {\n    return this.file.addHelper(name);\n  }\n\n  buildCodeFrameError(\n    node: NodeLocation | undefined | null,\n    msg: string,\n    _Error?: typeof Error,\n  ) {\n    return this.file.buildCodeFrameError(node, msg, _Error);\n  }\n}\n\nif (!process.env.BABEL_8_BREAKING) {\n  (PluginPass as any).prototype.getModuleName = function getModuleName(\n    this: PluginPass,\n  ): string | undefined {\n    return this.file.getModuleName();\n  };\n  (PluginPass as any).prototype.addImport = function addImport(\n    this: PluginPass,\n  ): void {\n    this.file.addImport();\n  };\n}\n"], "mappings": ";;;;;;AAGe,MAAMA,UAAU,CAAe;EAa5CC,WAAWA,CAACC,IAAU,EAAEC,GAAmB,EAAEC,OAAiB,EAAE;IAAA,KAZhEC,IAAI,GAA0B,IAAIC,GAAG,CAAC,CAAC;IAAA,KACvCH,GAAG;IAAA,KACHD,IAAI;IAAA,KACJK,IAAI;IAAA,KAIJC,GAAG;IAAA,KAGHC,QAAQ;IAGN,IAAI,CAACN,GAAG,GAAGA,GAAG;IACd,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACK,IAAI,GAAGH,OAAO,IAAI,CAAC,CAAC;IACzB,IAAI,CAACI,GAAG,GAAGN,IAAI,CAACK,IAAI,CAACC,GAAG;IACxB,IAAI,CAACC,QAAQ,GAAGP,IAAI,CAACK,IAAI,CAACE,QAAQ;EACpC;EAEAC,GAAGA,CAACP,GAAY,EAAEQ,GAAY,EAAE;IAC9B,IAAI,CAACN,IAAI,CAACK,GAAG,CAACP,GAAG,EAAEQ,GAAG,CAAC;EACzB;EAEAC,GAAGA,CAACT,GAAY,EAAO;IACrB,OAAO,IAAI,CAACE,IAAI,CAACO,GAAG,CAACT,GAAG,CAAC;EAC3B;EAEAU,eAAeA,CAACC,IAAY,EAAEC,YAA4B,EAAE;IAC1D,OAAO,IAAI,CAACb,IAAI,CAACW,eAAe,CAACC,IAAI,EAAEC,YAAY,CAAC;EACtD;EAEAC,SAASA,CAACF,IAAY,EAAE;IACtB,OAAO,IAAI,CAACZ,IAAI,CAACc,SAAS,CAACF,IAAI,CAAC;EAClC;EAEAG,mBAAmBA,CACjBC,IAAqC,EACrCC,GAAW,EACXC,MAAqB,EACrB;IACA,OAAO,IAAI,CAAClB,IAAI,CAACe,mBAAmB,CAACC,IAAI,EAAEC,GAAG,EAAEC,MAAM,CAAC;EACzD;AACF;AAACC,OAAA,CAAAC,OAAA,GAAAtB,UAAA;AAEkC;EAChCA,UAAU,CAASuB,SAAS,CAACC,aAAa,GAAG,SAASA,aAAaA,CAAA,EAE9C;IACpB,OAAO,IAAI,CAACtB,IAAI,CAACsB,aAAa,CAAC,CAAC;EAClC,CAAC;EACAxB,UAAU,CAASuB,SAAS,CAACE,SAAS,GAAG,SAASA,SAASA,CAAA,EAEpD;IACN,IAAI,CAACvB,IAAI,CAACuB,SAAS,CAAC,CAAC;EACvB,CAAC;AACH;AAAC"}