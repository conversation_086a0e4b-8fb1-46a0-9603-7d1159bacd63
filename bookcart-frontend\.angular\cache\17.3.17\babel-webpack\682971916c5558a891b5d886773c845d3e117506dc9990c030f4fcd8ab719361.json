{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\n// Angular Material Modules\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatGridListModule } from '@angular/material/grid-list';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatDividerModule } from '@angular/material/divider';\n// JWT\nimport { JwtModule } from '@auth0/angular-jwt';\n// App Components\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\n// Auth Components\nimport { LoginComponent } from './auth/login/login.component';\nimport { RegisterComponent } from './auth/register/register.component';\n// Book Components\nimport { BookListComponent } from './books/book-list/book-list.component';\nimport { BookAddComponent } from './books/book-add/book-add.component';\n// Cart Components\nimport { CartComponent } from './cart/cart.component';\n// Order Components\nimport { OrderListComponent } from './orders/order-list/order-list.component';\nimport { OrderDetailComponent } from './orders/order-detail/order-detail.component';\n// Admin Components\nimport { UserManagementComponent } from './admin/user-management/user-management.component';\n// Shared Components\nimport { NavbarComponent } from './shared/components/navbar/navbar.component';\nimport { UnauthorizedComponent } from './shared/components/unauthorized/unauthorized.component';\n// Services & Guards\nimport { AuthInterceptor } from './shared/interceptors/auth.interceptor';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@auth0/angular-jwt\";\nexport function tokenGetter() {\n  return localStorage.getItem('token');\n}\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [{\n        provide: HTTP_INTERCEPTORS,\n        useClass: AuthInterceptor,\n        multi: true\n      }],\n      imports: [BrowserModule, AppRoutingModule, BrowserAnimationsModule, HttpClientModule, ReactiveFormsModule, FormsModule,\n      // Angular Material\n      MatToolbarModule, MatButtonModule, MatCardModule, MatFormFieldModule, MatInputModule, MatIconModule, MatMenuModule, MatBadgeModule, MatGridListModule, MatChipsModule, MatTableModule, MatPaginatorModule, MatSortModule, MatDialogModule, MatSnackBarModule, MatSelectModule, MatProgressSpinnerModule, MatDividerModule,\n      // JWT\n      JwtModule.forRoot({\n        config: {\n          tokenGetter: tokenGetter,\n          allowedDomains: ['localhost:5001'],\n          disallowedRoutes: []\n        }\n      })]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent, LoginComponent, RegisterComponent, BookListComponent, BookAddComponent, CartComponent, OrderListComponent, OrderDetailComponent, UserManagementComponent, NavbarComponent, UnauthorizedComponent],\n    imports: [BrowserModule, AppRoutingModule, BrowserAnimationsModule, HttpClientModule, ReactiveFormsModule, FormsModule,\n    // Angular Material\n    MatToolbarModule, MatButtonModule, MatCardModule, MatFormFieldModule, MatInputModule, MatIconModule, MatMenuModule, MatBadgeModule, MatGridListModule, MatChipsModule, MatTableModule, MatPaginatorModule, MatSortModule, MatDialogModule, MatSnackBarModule, MatSelectModule, MatProgressSpinnerModule, MatDividerModule, i1.JwtModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "BrowserAnimationsModule", "HttpClientModule", "HTTP_INTERCEPTORS", "ReactiveFormsModule", "FormsModule", "MatToolbarModule", "MatButtonModule", "MatCardModule", "MatFormFieldModule", "MatInputModule", "MatIconModule", "MatMenuModule", "MatBadgeModule", "MatGridListModule", "MatChipsModule", "MatTableModule", "MatPaginatorModule", "MatSortModule", "MatDialogModule", "MatSnackBarModule", "MatSelectModule", "MatProgressSpinnerModule", "MatDividerModule", "JwtModule", "AppRoutingModule", "AppComponent", "LoginComponent", "RegisterComponent", "BookListComponent", "BookAddComponent", "CartComponent", "OrderListComponent", "OrderDetailComponent", "UserManagementComponent", "NavbarComponent", "UnauthorizedComponent", "AuthInterceptor", "tokenGetter", "localStorage", "getItem", "AppModule", "bootstrap", "provide", "useClass", "multi", "imports", "forRoot", "config", "allowedDomains", "disallowedRoutes", "declarations", "i1"], "sources": ["C:\\Users\\<USER>\\Desktop\\BookCart\\bookcart-frontend\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\n\n// Angular Material Modules\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatGridListModule } from '@angular/material/grid-list';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatDividerModule } from '@angular/material/divider';\n\n// JWT\nimport { JwtModule } from '@auth0/angular-jwt';\n\n// App Components\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\n\n// Auth Components\nimport { LoginComponent } from './auth/login/login.component';\nimport { RegisterComponent } from './auth/register/register.component';\n\n// Book Components\nimport { BookListComponent } from './books/book-list/book-list.component';\nimport { BookAddComponent } from './books/book-add/book-add.component';\n\n// Cart Components\nimport { CartComponent } from './cart/cart.component';\n\n// Order Components\nimport { OrderListComponent } from './orders/order-list/order-list.component';\nimport { OrderDetailComponent } from './orders/order-detail/order-detail.component';\n\n// Admin Components\nimport { UserManagementComponent } from './admin/user-management/user-management.component';\n\n// Shared Components\nimport { NavbarComponent } from './shared/components/navbar/navbar.component';\nimport { UnauthorizedComponent } from './shared/components/unauthorized/unauthorized.component';\n\n// Services & Guards\nimport { AuthInterceptor } from './shared/interceptors/auth.interceptor';\n\nexport function tokenGetter() {\n  return localStorage.getItem('token');\n}\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    LoginComponent,\n    RegisterComponent,\n    BookListComponent,\n    BookAddComponent,\n    CartComponent,\n    OrderListComponent,\n    OrderDetailComponent,\n    UserManagementComponent,\n    NavbarComponent,\n    UnauthorizedComponent\n  ],\n  imports: [\n    BrowserModule,\n    AppRoutingModule,\n    BrowserAnimationsModule,\n    HttpClientModule,\n    ReactiveFormsModule,\n    FormsModule,\n    \n    // Angular Material\n    MatToolbarModule,\n    MatButtonModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatIconModule,\n    MatMenuModule,\n    MatBadgeModule,\n    MatGridListModule,\n    MatChipsModule,\n    MatTableModule,\n    MatPaginatorModule,\n    MatSortModule,\n    MatDialogModule,\n    MatSnackBarModule,\n    MatSelectModule,\n    MatProgressSpinnerModule,\n    MatDividerModule,\n    \n    // JWT\n    JwtModule.forRoot({\n      config: {\n        tokenGetter: tokenGetter,\n        allowedDomains: ['localhost:5001'],\n        disallowedRoutes: []\n      }\n    })\n  ],\n  providers: [\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: AuthInterceptor,\n      multi: true\n    }\n  ],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,sBAAsB;AAC1E,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AAEjE;AACA,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D;AACA,SAASC,SAAS,QAAQ,oBAAoB;AAE9C;AACA,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;AACA,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,iBAAiB,QAAQ,oCAAoC;AAEtE;AACA,SAASC,iBAAiB,QAAQ,uCAAuC;AACzE,SAASC,gBAAgB,QAAQ,qCAAqC;AAEtE;AACA,SAASC,aAAa,QAAQ,uBAAuB;AAErD;AACA,SAASC,kBAAkB,QAAQ,0CAA0C;AAC7E,SAASC,oBAAoB,QAAQ,8CAA8C;AAEnF;AACA,SAASC,uBAAuB,QAAQ,mDAAmD;AAE3F;AACA,SAASC,eAAe,QAAQ,6CAA6C;AAC7E,SAASC,qBAAqB,QAAQ,yDAAyD;AAE/F;AACA,SAASC,eAAe,QAAQ,wCAAwC;;;AAExE,OAAM,SAAUC,WAAWA,CAAA;EACzB,OAAOC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;AACtC;AA8DA,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFRhB,YAAY;IAAA;EAAA;;;iBAPb,CACT;QACEiB,OAAO,EAAExC,iBAAiB;QAC1ByC,QAAQ,EAAEP,eAAe;QACzBQ,KAAK,EAAE;OACR,CACF;MAAAC,OAAA,GA1CC9C,aAAa,EACbyB,gBAAgB,EAChBxB,uBAAuB,EACvBC,gBAAgB,EAChBE,mBAAmB,EACnBC,WAAW;MAEX;MACAC,gBAAgB,EAChBC,eAAe,EACfC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,iBAAiB,EACjBC,cAAc,EACdC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,eAAe,EACfC,iBAAiB,EACjBC,eAAe,EACfC,wBAAwB,EACxBC,gBAAgB;MAEhB;MACAC,SAAS,CAACuB,OAAO,CAAC;QAChBC,MAAM,EAAE;UACNV,WAAW,EAAEA,WAAW;UACxBW,cAAc,EAAE,CAAC,gBAAgB,CAAC;UAClCC,gBAAgB,EAAE;;OAErB,CAAC;IAAA;EAAA;;;2EAWOT,SAAS;IAAAU,YAAA,GA1DlBzB,YAAY,EACZC,cAAc,EACdC,iBAAiB,EACjBC,iBAAiB,EACjBC,gBAAgB,EAChBC,aAAa,EACbC,kBAAkB,EAClBC,oBAAoB,EACpBC,uBAAuB,EACvBC,eAAe,EACfC,qBAAqB;IAAAU,OAAA,GAGrB9C,aAAa,EACbyB,gBAAgB,EAChBxB,uBAAuB,EACvBC,gBAAgB,EAChBE,mBAAmB,EACnBC,WAAW;IAEX;IACAC,gBAAgB,EAChBC,eAAe,EACfC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,iBAAiB,EACjBC,cAAc,EACdC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,eAAe,EACfC,iBAAiB,EACjBC,eAAe,EACfC,wBAAwB,EACxBC,gBAAgB,EAAA6B,EAAA,CAAA5B,SAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}