/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export function coerceNumberProperty(value, fallbackValue = 0) {
    return _isNumberValue(value) ? Number(value) : fallbackValue;
}
/**
 * Whether the provided value is considered a number.
 * @docs-private
 */
export function _isNumberValue(value) {
    // parseFloat(value) handles most of the cases we're interested in (it treats null, empty string,
    // and other non-number values as NaN, where Number just uses 0) but it considers the string
    // '123hello' to be a valid number. Therefore we also check if Number(value) is NaN.
    return !isNaN(parseFloat(value)) && !isNaN(Number(value));
}
//# sourceMappingURL=data:application/json;base64,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