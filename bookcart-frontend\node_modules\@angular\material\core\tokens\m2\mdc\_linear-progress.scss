@use '../../../theming/inspection';
@use '../../../style/sass-utils';
@use '../../token-utils';
@use 'sass:color';
@use 'sass:meta';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mdc, linear-progress);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
//
// Tokens that are available in MDC, but not used in Angular Material should be mapped to `null`.
// `null` indicates that we are intentionally choosing not to emit a slot or value for the token in
// our CSS.
@function get-unthemable-tokens() {
  $height: 4px;

  @return (
    // The height of the progress bar. `active-indicator-height` applies to the
    // bar and `track-height` applies to the track.
    active-indicator-height: $height,
    track-height: $height,
    track-shape: 0,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme, $palette-name: primary) {
  $palette-color: inspection.get-theme-color($theme, $palette-name);
  @return (
    // The color of the progress bar's active section.
    active-indicator-color: $palette-color,
    track-color: if(
        meta.type-of($palette-color) == color,
        color.adjust($palette-color, $alpha: -0.75),
        $palette-color
    )
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return ();
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-utils.$placeholder-color-config),
      get-typography-tokens(token-utils.$placeholder-typography-config),
      get-density-tokens(token-utils.$placeholder-density-config)
  );
}
