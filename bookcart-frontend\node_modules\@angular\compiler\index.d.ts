/**
 * @license Angular v17.3.12
 * (c) 2010-2024 Google LLC. https://angular.io/
 * License: MIT
 */


/**
 * Records the absolute position of a text span in a source file, where `start` and `end` are the
 * starting and ending byte offsets, respectively, of the text span in a source file.
 */
export declare class AbsoluteSourceSpan {
    readonly start: number;
    readonly end: number;
    constructor(start: number, end: number);
}

/**
 * A data structure which captures the animation trigger names that are statically resolvable
 * and whether some names could not be statically evaluated.
 */
export declare interface AnimationTriggerNames {
    includesDynamicAnimations: boolean;
    staticTriggerNames: string[];
}

declare function areAllEquivalent<T extends {
    isEquivalent(other: T): boolean;
}>(base: T[], other: T[]): boolean;

export declare class ArrayType extends Type {
    of: Type;
    constructor(of: Type, modifiers?: TypeModifier);
    visitType(visitor: TypeVisitor, context: any): any;
}

declare function arrowFn(params: FnParam[], body: Expression | Statement[], type?: Type | null, sourceSpan?: ParseSourceSpan | null): ArrowFunctionExpr;

export declare class ArrowFunctionExpr extends Expression {
    params: FnParam[];
    body: Expression | Statement[];
    constructor(params: FnParam[], body: Expression | Statement[], type?: Type | null, sourceSpan?: ParseSourceSpan | null);
    isEquivalent(e: Expression): boolean;
    isConstant(): boolean;
    visitExpression(visitor: ExpressionVisitor, context: any): any;
    clone(): Expression;
    toDeclStmt(name: string, modifiers?: StmtModifier): DeclareVarStmt;
}

export declare abstract class AST {
    span: ParseSpan;
    /**
     * Absolute location of the expression AST in a source code file.
     */
    sourceSpan: AbsoluteSourceSpan;
    constructor(span: ParseSpan, 
    /**
     * Absolute location of the expression AST in a source code file.
     */
    sourceSpan: AbsoluteSourceSpan);
    abstract visit(visitor: AstVisitor, context?: any): any;
    toString(): string;
}

export declare class AstMemoryEfficientTransformer implements AstVisitor {
    visitImplicitReceiver(ast: ImplicitReceiver, context: any): AST;
    visitThisReceiver(ast: ThisReceiver, context: any): AST;
    visitInterpolation(ast: Interpolation, context: any): Interpolation;
    visitLiteralPrimitive(ast: LiteralPrimitive, context: any): AST;
    visitPropertyRead(ast: PropertyRead, context: any): AST;
    visitPropertyWrite(ast: PropertyWrite, context: any): AST;
    visitSafePropertyRead(ast: SafePropertyRead, context: any): AST;
    visitLiteralArray(ast: LiteralArray, context: any): AST;
    visitLiteralMap(ast: LiteralMap, context: any): AST;
    visitUnary(ast: Unary, context: any): AST;
    visitBinary(ast: Binary, context: any): AST;
    visitPrefixNot(ast: PrefixNot, context: any): AST;
    visitNonNullAssert(ast: NonNullAssert, context: any): AST;
    visitConditional(ast: Conditional, context: any): AST;
    visitPipe(ast: BindingPipe, context: any): AST;
    visitKeyedRead(ast: KeyedRead, context: any): AST;
    visitKeyedWrite(ast: KeyedWrite, context: any): AST;
    visitAll(asts: any[]): any[];
    visitChain(ast: Chain, context: any): AST;
    visitCall(ast: Call, context: any): AST;
    visitSafeCall(ast: SafeCall, context: any): AST;
    visitSafeKeyedRead(ast: SafeKeyedRead, context: any): AST;
}

export declare class AstTransformer implements AstVisitor {
    visitImplicitReceiver(ast: ImplicitReceiver, context: any): AST;
    visitThisReceiver(ast: ThisReceiver, context: any): AST;
    visitInterpolation(ast: Interpolation, context: any): AST;
    visitLiteralPrimitive(ast: LiteralPrimitive, context: any): AST;
    visitPropertyRead(ast: PropertyRead, context: any): AST;
    visitPropertyWrite(ast: PropertyWrite, context: any): AST;
    visitSafePropertyRead(ast: SafePropertyRead, context: any): AST;
    visitLiteralArray(ast: LiteralArray, context: any): AST;
    visitLiteralMap(ast: LiteralMap, context: any): AST;
    visitUnary(ast: Unary, context: any): AST;
    visitBinary(ast: Binary, context: any): AST;
    visitPrefixNot(ast: PrefixNot, context: any): AST;
    visitNonNullAssert(ast: NonNullAssert, context: any): AST;
    visitConditional(ast: Conditional, context: any): AST;
    visitPipe(ast: BindingPipe, context: any): AST;
    visitKeyedRead(ast: KeyedRead, context: any): AST;
    visitKeyedWrite(ast: KeyedWrite, context: any): AST;
    visitCall(ast: Call, context: any): AST;
    visitSafeCall(ast: SafeCall, context: any): AST;
    visitAll(asts: any[]): any[];
    visitChain(ast: Chain, context: any): AST;
    visitSafeKeyedRead(ast: SafeKeyedRead, context: any): AST;
}

export declare interface AstVisitor {
    /**
     * The `visitUnary` method is declared as optional for backwards compatibility. In an upcoming
     * major release, this method will be made required.
     */
    visitUnary?(ast: Unary, context: any): any;
    visitBinary(ast: Binary, context: any): any;
    visitChain(ast: Chain, context: any): any;
    visitConditional(ast: Conditional, context: any): any;
    /**
     * The `visitThisReceiver` method is declared as optional for backwards compatibility.
     * In an upcoming major release, this method will be made required.
     */
    visitThisReceiver?(ast: ThisReceiver, context: any): any;
    visitImplicitReceiver(ast: ImplicitReceiver, context: any): any;
    visitInterpolation(ast: Interpolation, context: any): any;
    visitKeyedRead(ast: KeyedRead, context: any): any;
    visitKeyedWrite(ast: KeyedWrite, context: any): any;
    visitLiteralArray(ast: LiteralArray, context: any): any;
    visitLiteralMap(ast: LiteralMap, context: any): any;
    visitLiteralPrimitive(ast: LiteralPrimitive, context: any): any;
    visitPipe(ast: BindingPipe, context: any): any;
    visitPrefixNot(ast: PrefixNot, context: any): any;
    visitNonNullAssert(ast: NonNullAssert, context: any): any;
    visitPropertyRead(ast: PropertyRead, context: any): any;
    visitPropertyWrite(ast: PropertyWrite, context: any): any;
    visitSafePropertyRead(ast: SafePropertyRead, context: any): any;
    visitSafeKeyedRead(ast: SafeKeyedRead, context: any): any;
    visitCall(ast: Call, context: any): any;
    visitSafeCall(ast: SafeCall, context: any): any;
    visitASTWithSource?(ast: ASTWithSource, context: any): any;
    /**
     * This function is optionally defined to allow classes that implement this
     * interface to selectively decide if the specified `ast` should be visited.
     * @param ast node to visit
     * @param context context that gets passed to the node and all its children
     */
    visit?(ast: AST, context?: any): any;
}

export declare abstract class ASTWithName extends AST {
    nameSpan: AbsoluteSourceSpan;
    constructor(span: ParseSpan, sourceSpan: AbsoluteSourceSpan, nameSpan: AbsoluteSourceSpan);
}

export declare class ASTWithSource<T extends AST = AST> extends AST {
    ast: T;
    source: string | null;
    location: string;
    errors: ParserError[];
    constructor(ast: T, source: string | null, location: string, absoluteOffset: number, errors: ParserError[]);
    visit(visitor: AstVisitor, context?: any): any;
    toString(): string;
}

export declare class Attribute extends NodeWithI18n {
    name: string;
    value: string;
    readonly keySpan: ParseSourceSpan | undefined;
    valueSpan: ParseSourceSpan | undefined;
    valueTokens: InterpolatedAttributeToken[] | undefined;
    constructor(name: string, value: string, sourceSpan: ParseSourceSpan, keySpan: ParseSourceSpan | undefined, valueSpan: ParseSourceSpan | undefined, valueTokens: InterpolatedAttributeToken[] | undefined, i18n: I18nMeta_2 | undefined);
    visit(visitor: Visitor, context: any): any;
}

/**
 * A set of marker values to be used in the attributes arrays. These markers indicate that some
 * items are not regular attributes and the processing should be adapted accordingly.
 */
declare const enum AttributeMarker {
    /**
     * Marker indicates that the following 3 values in the attributes array are:
     * namespaceUri, attributeName, attributeValue
     * in that order.
     */
    NamespaceURI = 0,
    /**
     * Signals class declaration.
     *
     * Each value following `Classes` designates a class name to include on the element.
     * ## Example:
     *
     * Given:
     * ```
     * <div class="foo bar baz">...<d/vi>
     * ```
     *
     * the generated code is:
     * ```
     * var _c1 = [AttributeMarker.Classes, 'foo', 'bar', 'baz'];
     * ```
     */
    Classes = 1,
    /**
     * Signals style declaration.
     *
     * Each pair of values following `Styles` designates a style name and value to include on the
     * element.
     * ## Example:
     *
     * Given:
     * ```
     * <div style="width:100px; height:200px; color:red">...</div>
     * ```
     *
     * the generated code is:
     * ```
     * var _c1 = [AttributeMarker.Styles, 'width', '100px', 'height'. '200px', 'color', 'red'];
     * ```
     */
    Styles = 2,
    /**
     * Signals that the following attribute names were extracted from input or output bindings.
     *
     * For example, given the following HTML:
     *
     * ```
     * <div moo="car" [foo]="exp" (bar)="doSth()">
     * ```
     *
     * the generated code is:
     *
     * ```
     * var _c1 = ['moo', 'car', AttributeMarker.Bindings, 'foo', 'bar'];
     * ```
     */
    Bindings = 3,
    /**
     * Signals that the following attribute names were hoisted from an inline-template declaration.
     *
     * For example, given the following HTML:
     *
     * ```
     * <div *ngFor="let value of values; trackBy:trackBy" dirA [dirB]="value">
     * ```
     *
     * the generated code for the `template()` instruction would include:
     *
     * ```
     * ['dirA', '', AttributeMarker.Bindings, 'dirB', AttributeMarker.Template, 'ngFor', 'ngForOf',
     * 'ngForTrackBy', 'let-value']
     * ```
     *
     * while the generated code for the `element()` instruction inside the template function would
     * include:
     *
     * ```
     * ['dirA', '', AttributeMarker.Bindings, 'dirB']
     * ```
     */
    Template = 4,
    /**
     * Signals that the following attribute is `ngProjectAs` and its value is a parsed `CssSelector`.
     *
     * For example, given the following HTML:
     *
     * ```
     * <h1 attr="value" ngProjectAs="[title]">
     * ```
     *
     * the generated code for the `element()` instruction would include:
     *
     * ```
     * ['attr', 'value', AttributeMarker.ProjectAs, ['', 'title', '']]
     * ```
     */
    ProjectAs = 5,
    /**
     * Signals that the following attribute will be translated by runtime i18n
     *
     * For example, given the following HTML:
     *
     * ```
     * <div moo="car" foo="value" i18n-foo [bar]="binding" i18n-bar>
     * ```
     *
     * the generated code is:
     *
     * ```
     * var _c1 = ['moo', 'car', AttributeMarker.I18n, 'foo', 'bar'];
     */
    I18n = 6
}

declare interface AttributeValueInterpolationToken extends TokenBase {
    type: LexerTokenType.ATTR_VALUE_INTERPOLATION;
    parts: [startMarker: string, expression: string, endMarker: string] | [
    startMarker: string,
    expression: string
    ];
}

declare interface AttributeValueTextToken extends TokenBase {
    type: LexerTokenType.ATTR_VALUE_TEXT;
    parts: [value: string];
}

declare interface BaseNode {
    sourceSpan: ParseSourceSpan;
    visit(visitor: Visitor, context: any): any;
}

export declare class Binary extends AST {
    operation: string;
    left: AST;
    right: AST;
    constructor(span: ParseSpan, sourceSpan: AbsoluteSourceSpan, operation: string, left: AST, right: AST);
    visit(visitor: AstVisitor, context?: any): any;
}

export declare enum BinaryOperator {
    Equals = 0,
    NotEquals = 1,
    Identical = 2,
    NotIdentical = 3,
    Minus = 4,
    Plus = 5,
    Divide = 6,
    Multiply = 7,
    Modulo = 8,
    And = 9,
    Or = 10,
    BitwiseOr = 11,
    BitwiseAnd = 12,
    Lower = 13,
    LowerEquals = 14,
    Bigger = 15,
    BiggerEquals = 16,
    NullishCoalesce = 17
}

export declare class BinaryOperatorExpr extends Expression {
    operator: BinaryOperator;
    rhs: Expression;
    parens: boolean;
    lhs: Expression;
    constructor(operator: BinaryOperator, lhs: Expression, rhs: Expression, type?: Type | null, sourceSpan?: ParseSourceSpan | null, parens?: boolean);
    isEquivalent(e: Expression): boolean;
    isConstant(): boolean;
    visitExpression(visitor: ExpressionVisitor, context: any): any;
    clone(): BinaryOperatorExpr;
}

/**
 * Parses bindings in templates and in the directive host area.
 */
declare class BindingParser {
    private _exprParser;
    private _interpolationConfig;
    private _schemaRegistry;
    errors: ParseError[];
    constructor(_exprParser: Parser, _interpolationConfig: InterpolationConfig, _schemaRegistry: ElementSchemaRegistry, errors: ParseError[]);
    get interpolationConfig(): InterpolationConfig;
    createBoundHostProperties(properties: HostProperties, sourceSpan: ParseSourceSpan): ParsedProperty[] | null;
    createDirectiveHostEventAsts(hostListeners: HostListeners, sourceSpan: ParseSourceSpan): ParsedEvent[] | null;
    parseInterpolation(value: string, sourceSpan: ParseSourceSpan, interpolatedTokens: InterpolatedAttributeToken[] | InterpolatedTextToken[] | null): ASTWithSource;
    /**
     * Similar to `parseInterpolation`, but treats the provided string as a single expression
     * element that would normally appear within the interpolation prefix and suffix (`{{` and `}}`).
     * This is used for parsing the switch expression in ICUs.
     */
    parseInterpolationExpression(expression: string, sourceSpan: ParseSourceSpan): ASTWithSource;
    /**
     * Parses the bindings in a microsyntax expression, and converts them to
     * `ParsedProperty` or `ParsedVariable`.
     *
     * @param tplKey template binding name
     * @param tplValue template binding value
     * @param sourceSpan span of template binding relative to entire the template
     * @param absoluteValueOffset start of the tplValue relative to the entire template
     * @param targetMatchableAttrs potential attributes to match in the template
     * @param targetProps target property bindings in the template
     * @param targetVars target variables in the template
     */
    parseInlineTemplateBinding(tplKey: string, tplValue: string, sourceSpan: ParseSourceSpan, absoluteValueOffset: number, targetMatchableAttrs: string[][], targetProps: ParsedProperty[], targetVars: ParsedVariable[], isIvyAst: boolean): void;
    /**
     * Parses the bindings in a microsyntax expression, e.g.
     * ```
     *    <tag *tplKey="let value1 = prop; let value2 = localVar">
     * ```
     *
     * @param tplKey template binding name
     * @param tplValue template binding value
     * @param sourceSpan span of template binding relative to entire the template
     * @param absoluteKeyOffset start of the `tplKey`
     * @param absoluteValueOffset start of the `tplValue`
     */
    private _parseTemplateBindings;
    parseLiteralAttr(name: string, value: string | null, sourceSpan: ParseSourceSpan, absoluteOffset: number, valueSpan: ParseSourceSpan | undefined, targetMatchableAttrs: string[][], targetProps: ParsedProperty[], keySpan: ParseSourceSpan): void;
    parsePropertyBinding(name: string, expression: string, isHost: boolean, isPartOfAssignmentBinding: boolean, sourceSpan: ParseSourceSpan, absoluteOffset: number, valueSpan: ParseSourceSpan | undefined, targetMatchableAttrs: string[][], targetProps: ParsedProperty[], keySpan: ParseSourceSpan): void;
    parsePropertyInterpolation(name: string, value: string, sourceSpan: ParseSourceSpan, valueSpan: ParseSourceSpan | undefined, targetMatchableAttrs: string[][], targetProps: ParsedProperty[], keySpan: ParseSourceSpan, interpolatedTokens: InterpolatedAttributeToken[] | InterpolatedTextToken[] | null): boolean;
    private _parsePropertyAst;
    private _parseAnimation;
    parseBinding(value: string, isHostBinding: boolean, sourceSpan: ParseSourceSpan, absoluteOffset: number): ASTWithSource;
    createBoundElementProperty(elementSelector: string, boundProp: ParsedProperty, skipValidation?: boolean, mapPropertyName?: boolean): BoundElementProperty;
    parseEvent(name: string, expression: string, isAssignmentEvent: boolean, sourceSpan: ParseSourceSpan, handlerSpan: ParseSourceSpan, targetMatchableAttrs: string[][], targetEvents: ParsedEvent[], keySpan: ParseSourceSpan): void;
    calcPossibleSecurityContexts(selector: string, propName: string, isAttribute: boolean): SecurityContext[];
    private _parseAnimationEvent;
    private _parseRegularEvent;
    private _parseAction;
    private _reportError;
    private _reportExpressionParserErrors;
    /**
     * @param propName the name of the property / attribute
     * @param sourceSpan
     * @param isAttr true when binding to an attribute
     */
    private _validatePropertyOrAttributeName;
    /**
     * Returns whether a parsed AST is allowed to be used within the event side of a two-way binding.
     * @param ast Parsed AST to be checked.
     */
    private _isAllowedAssignmentEvent;
}

export declare class BindingPipe extends ASTWithName {
    exp: AST;
    name: string;
    args: any[];
    constructor(span: ParseSpan, sourceSpan: AbsoluteSourceSpan, exp: AST, name: string, args: any[], nameSpan: AbsoluteSourceSpan);
    visit(visitor: AstVisitor, context?: any): any;
}

export declare const enum BindingType {
    Property = 0,
    Attribute = 1,
    Class = 2,
    Style = 3,
    Animation = 4,
    TwoWay = 5
}

export declare class Block extends NodeWithI18n {
    name: string;
    parameters: BlockParameter[];
    children: Node_2[];
    nameSpan: ParseSourceSpan;
    startSourceSpan: ParseSourceSpan;
    endSourceSpan: ParseSourceSpan | null;
    constructor(name: string, parameters: BlockParameter[], children: Node_2[], sourceSpan: ParseSourceSpan, nameSpan: ParseSourceSpan, startSourceSpan: ParseSourceSpan, endSourceSpan?: ParseSourceSpan | null, i18n?: I18nMeta_2);
    visit(visitor: Visitor, context: any): any;
}

export declare class BlockParameter implements BaseNode {
    expression: string;
    sourceSpan: ParseSourceSpan;
    constructor(expression: string, sourceSpan: ParseSourceSpan);
    visit(visitor: Visitor, context: any): any;
}

declare class BlockPlaceholder implements Node_3 {
    name: string;
    parameters: string[];
    startName: string;
    closeName: string;
    children: Node_3[];
    sourceSpan: ParseSourceSpan;
    startSourceSpan: ParseSourceSpan | null;
    endSourceSpan: ParseSourceSpan | null;
    constructor(name: string, parameters: string[], startName: string, closeName: string, children: Node_3[], sourceSpan: ParseSourceSpan, startSourceSpan: ParseSourceSpan | null, endSourceSpan: ParseSourceSpan | null);
    visit(visitor: Visitor_2, context?: any): any;
}

declare const BOOL_TYPE: BuiltinType;

export declare class BoundElementProperty {
    name: string;
    type: BindingType;
    securityContext: SecurityContext;
    value: ASTWithSource;
    unit: string | null;
    sourceSpan: ParseSourceSpan;
    readonly keySpan: ParseSourceSpan | undefined;
    valueSpan: ParseSourceSpan | undefined;
    constructor(name: string, type: BindingType, securityContext: SecurityContext, value: ASTWithSource, unit: string | null, sourceSpan: ParseSourceSpan, keySpan: ParseSourceSpan | undefined, valueSpan: ParseSourceSpan | undefined);
}

/**
 * Result of performing the binding operation against a `Target`.
 *
 * The original `Target` is accessible, as well as a suite of methods for extracting binding
 * information regarding the `Target`.
 *
 * @param DirectiveT directive metadata type
 */
export declare interface BoundTarget<DirectiveT extends DirectiveMeta> {
    /**
     * Get the original `Target` that was bound.
     */
    readonly target: Target;
    /**
     * For a given template node (either an `Element` or a `Template`), get the set of directives
     * which matched the node, if any.
     */
    getDirectivesOfNode(node: TmplAstElement | TmplAstTemplate): DirectiveT[] | null;
    /**
     * For a given `Reference`, get the reference's target - either an `Element`, a `Template`, or
     * a directive on a particular node.
     */
    getReferenceTarget(ref: TmplAstReference): ReferenceTarget<DirectiveT> | null;
    /**
     * For a given binding, get the entity to which the binding is being made.
     *
     * This will either be a directive or the node itself.
     */
    getConsumerOfBinding(binding: TmplAstBoundAttribute | TmplAstBoundEvent | TmplAstTextAttribute): DirectiveT | TmplAstElement | TmplAstTemplate | null;
    /**
     * If the given `AST` expression refers to a `Reference` or `Variable` within the `Target`, then
     * return that.
     *
     * Otherwise, returns `null`.
     *
     * This is only defined for `AST` expressions that read or write to a property of an
     * `ImplicitReceiver`.
     */
    getExpressionTarget(expr: AST): TmplAstReference | TmplAstVariable | null;
    /**
     * Given a particular `Reference` or `Variable`, get the `ScopedNode` which created it.
     *
     * All `Variable`s are defined on node, so this will always return a value for a `Variable`
     * from the `Target`. Returns `null` otherwise.
     */
    getDefinitionNodeOfSymbol(symbol: TmplAstReference | TmplAstVariable): ScopedNode | null;
    /**
     * Get the nesting level of a particular `ScopedNode`.
     *
     * This starts at 1 for top-level nodes within the `Target` and increases for nodes
     * nested at deeper levels.
     */
    getNestingLevel(node: ScopedNode): number;
    /**
     * Get all `Reference`s and `Variables` visible within the given `ScopedNode` (or at the top
     * level, if `null` is passed).
     */
    getEntitiesInScope(node: ScopedNode | null): ReadonlySet<TmplAstReference | TmplAstVariable>;
    /**
     * Get a list of all the directives used by the target,
     * including directives from `@defer` blocks.
     */
    getUsedDirectives(): DirectiveT[];
    /**
     * Get a list of eagerly used directives from the target.
     * Note: this list *excludes* directives from `@defer` blocks.
     */
    getEagerlyUsedDirectives(): DirectiveT[];
    /**
     * Get a list of all the pipes used by the target,
     * including pipes from `@defer` blocks.
     */
    getUsedPipes(): string[];
    /**
     * Get a list of eagerly used pipes from the target.
     * Note: this list *excludes* pipes from `@defer` blocks.
     */
    getEagerlyUsedPipes(): string[];
    /**
     * Get a list of all `@defer` blocks used by the target.
     */
    getDeferBlocks(): TmplAstDeferredBlock[];
    /**
     * Gets the element that a specific deferred block trigger is targeting.
     * @param block Block that the trigger belongs to.
     * @param trigger Trigger whose target is being looked up.
     */
    getDeferredTriggerTarget(block: TmplAstDeferredBlock, trigger: TmplAstDeferredTrigger): TmplAstElement | null;
    /**
     * Whether a given node is located in a `@defer` block.
     */
    isDeferred(node: TmplAstElement): boolean;
}

export declare class BuiltinType extends Type {
    name: BuiltinTypeName;
    constructor(name: BuiltinTypeName, modifiers?: TypeModifier);
    visitType(visitor: TypeVisitor, context: any): any;
}

export declare enum BuiltinTypeName {
    Dynamic = 0,
    Bool = 1,
    String = 2,
    Int = 3,
    Number = 4,
    Function = 5,
    Inferred = 6,
    None = 7
}

export declare class Call extends AST {
    receiver: AST;
    args: AST[];
    argumentSpan: AbsoluteSourceSpan;
    constructor(span: ParseSpan, sourceSpan: AbsoluteSourceSpan, receiver: AST, args: AST[], argumentSpan: AbsoluteSourceSpan);
    visit(visitor: AstVisitor, context?: any): any;
}

/**
 * Multiple expressions separated by a semicolon.
 */
export declare class Chain extends AST {
    expressions: any[];
    constructor(span: ParseSpan, sourceSpan: AbsoluteSourceSpan, expressions: any[]);
    visit(visitor: AstVisitor, context?: any): any;
}

export declare enum ChangeDetectionStrategy {
    OnPush = 0,
    Default = 1
}

declare class CloneVisitor implements Visitor_2 {
    visitText(text: Text_3, context?: any): Text_3;
    visitContainer(container: Container, context?: any): Container;
    visitIcu(icu: Icu, context?: any): Icu;
    visitTagPlaceholder(ph: TagPlaceholder, context?: any): TagPlaceholder;
    visitPlaceholder(ph: Placeholder, context?: any): Placeholder;
    visitIcuPlaceholder(ph: IcuPlaceholder, context?: any): IcuPlaceholder;
    visitBlockPlaceholder(ph: BlockPlaceholder, context?: any): BlockPlaceholder;
}

export declare class CommaExpr extends Expression {
    parts: Expression[];
    constructor(parts: Expression[], sourceSpan?: ParseSourceSpan | null);
    isEquivalent(e: Expression): boolean;
    isConstant(): boolean;
    visitExpression(visitor: ExpressionVisitor, context: any): any;
    clone(): CommaExpr;
}

declare class Comment_2 implements BaseNode {
    value: string | null;
    sourceSpan: ParseSourceSpan;
    constructor(value: string | null, sourceSpan: ParseSourceSpan);
    visit(visitor: Visitor, context: any): any;
}
export { Comment_2 as Comment }

/**
 * This is an R3 `Node`-like wrapper for a raw `html.Comment` node. We do not currently
 * require the implementation of a visitor for Comments as they are only collected at
 * the top-level of the R3 AST, and only if `Render3ParseOptions['collectCommentNodes']`
 * is true.
 */
declare class Comment_3 implements TmplAstNode {
    value: string;
    sourceSpan: ParseSourceSpan;
    constructor(value: string, sourceSpan: ParseSourceSpan);
    visit<Result>(_visitor: TmplAstVisitor<Result>): Result;
}

/**
 * Generate an ngDevMode guarded call to setClassDebugInfo with the debug info about the class
 * (e.g., the file name in which the class is defined)
 */
export declare function compileClassDebugInfo(debugInfo: R3ClassDebugInfo): outputAst.Expression;

export declare function compileClassMetadata(metadata: R3ClassMetadata): outputAst.Expression;

export declare type CompileClassMetadataFn = (metadata: R3ClassMetadata) => outputAst.Expression;

/**
 * Wraps the `setClassMetadata` function with extra logic that dynamically
 * loads dependencies from `@defer` blocks.
 *
 * Generates a call like this:
 * ```
 * setClassMetadataAsync(type, () => [
 *   import('./cmp-a').then(m => m.CmpA);
 *   import('./cmp-b').then(m => m.CmpB);
 * ], (CmpA, CmpB) => {
 *   setClassMetadata(type, decorators, ctorParameters, propParameters);
 * });
 * ```
 *
 * Similar to the `setClassMetadata` call, it's wrapped into the `ngDevMode`
 * check to tree-shake away this code in production mode.
 */
export declare function compileComponentClassMetadata(metadata: R3ClassMetadata, deferrableTypes: Map<string, {
    importPath: string;
    isDefaultImport: boolean;
}> | null): outputAst.Expression;

/**
 * Compile a component for the render3 runtime as defined by the `R3ComponentMetadata`.
 */
export declare function compileComponentFromMetadata(meta: R3ComponentMetadata<R3TemplateDependency>, constantPool: ConstantPool, bindingParser: BindingParser): R3CompiledExpression;

export declare function compileDeclareClassMetadata(metadata: R3ClassMetadata): outputAst.Expression;

/**
 * Compile a component declaration defined by the `R3ComponentMetadata`.
 */
export declare function compileDeclareComponentFromMetadata(meta: R3ComponentMetadata<R3TemplateDependencyMetadata>, template: ParsedTemplate, additionalTemplateInfo: DeclareComponentTemplateInfo): R3CompiledExpression;

/**
 * Compile a directive declaration defined by the `R3DirectiveMetadata`.
 */
export declare function compileDeclareDirectiveFromMetadata(meta: R3DirectiveMetadata): R3CompiledExpression;

export declare function compileDeclareFactoryFunction(meta: R3FactoryMetadata): R3CompiledExpression;

/**
 * Compile a Injectable declaration defined by the `R3InjectableMetadata`.
 */
export declare function compileDeclareInjectableFromMetadata(meta: R3InjectableMetadata): R3CompiledExpression;

export declare function compileDeclareInjectorFromMetadata(meta: R3InjectorMetadata): R3CompiledExpression;

export declare function compileDeclareNgModuleFromMetadata(meta: R3NgModuleMetadata): R3CompiledExpression;

/**
 * Compile a Pipe declaration defined by the `R3PipeMetadata`.
 */
export declare function compileDeclarePipeFromMetadata(meta: R3PipeMetadata): R3CompiledExpression;

/**
 * Compile a directive for the render3 runtime as defined by the `R3DirectiveMetadata`.
 */
export declare function compileDirectiveFromMetadata(meta: R3DirectiveMetadata, constantPool: ConstantPool, bindingParser: BindingParser): R3CompiledExpression;

/**
 * Construct a factory function expression for the given `R3FactoryMetadata`.
 */
export declare function compileFactoryFunction(meta: R3FactoryMetadata): R3CompiledExpression;

export declare interface CompileIdentifierMetadata {
    reference: any;
}

export declare function compileInjectable(meta: R3InjectableMetadata, resolveForwardRefs: boolean): R3CompiledExpression;

export declare function compileInjector(meta: R3InjectorMetadata): R3CompiledExpression;

/**
 * Construct an `R3NgModuleDef` for the given `R3NgModuleMetadata`.
 */
export declare function compileNgModule(meta: R3NgModuleMetadata): R3CompiledExpression;

export declare function compilePipeFromMetadata(metadata: R3PipeMetadata): R3CompiledExpression;

export declare class CompilerConfig {
    defaultEncapsulation: ViewEncapsulation | null;
    preserveWhitespaces: boolean;
    strictInjectionParameters: boolean;
    constructor({ defaultEncapsulation, preserveWhitespaces, strictInjectionParameters }?: {
        defaultEncapsulation?: ViewEncapsulation;
        preserveWhitespaces?: boolean;
        strictInjectionParameters?: boolean;
    });
}

export declare function computeMsgId(msg: string, meaning?: string): string;

export declare class Conditional extends AST {
    condition: AST;
    trueExp: AST;
    falseExp: AST;
    constructor(span: ParseSpan, sourceSpan: AbsoluteSourceSpan, condition: AST, trueExp: AST, falseExp: AST);
    visit(visitor: AstVisitor, context?: any): any;
}

export declare class ConditionalExpr extends Expression {
    condition: Expression;
    falseCase: Expression | null;
    trueCase: Expression;
    constructor(condition: Expression, trueCase: Expression, falseCase?: Expression | null, type?: Type | null, sourceSpan?: ParseSourceSpan | null);
    isEquivalent(e: Expression): boolean;
    isConstant(): boolean;
    visitExpression(visitor: ExpressionVisitor, context: any): any;
    clone(): ConditionalExpr;
}

declare interface Console_2 {
    log(message: string): void;
    warn(message: string): void;
}

/**
 * A constant pool allows a code emitter to share constant in an output context.
 *
 * The constant pool also supports sharing access to ivy definitions references.
 */
export declare class ConstantPool {
    private readonly isClosureCompilerEnabled;
    statements: outputAst.Statement[];
    private literals;
    private literalFactories;
    private sharedConstants;
    /**
     * Constant pool also tracks claimed names from {@link uniqueName}.
     * This is useful to avoid collisions if variables are intended to be
     * named a certain way- but may conflict. We wouldn't want to always suffix
     * them with unique numbers.
     */
    private _claimedNames;
    private nextNameIndex;
    constructor(isClosureCompilerEnabled?: boolean);
    getConstLiteral(literal: outputAst.Expression, forceShared?: boolean): outputAst.Expression;
    getSharedConstant(def: SharedConstantDefinition, expr: outputAst.Expression): outputAst.Expression;
    getLiteralFactory(literal: outputAst.LiteralArrayExpr | outputAst.LiteralMapExpr): {
        literalFactory: outputAst.Expression;
        literalFactoryArguments: outputAst.Expression[];
    };
    getSharedFunctionReference(fn: outputAst.FunctionExpr | outputAst.ArrowFunctionExpr, prefix: string, useUniqueName?: boolean): outputAst.Expression;
    private _getLiteralFactory;
    /**
     * Produce a unique name in the context of this pool.
     *
     * The name might be unique among different prefixes if any of the prefixes end in
     * a digit so the prefix should be a constant string (not based on user input) and
     * must not end in a digit.
     */
    uniqueName(name: string, alwaysIncludeSuffix?: boolean): string;
    private freshName;
}

declare class Container implements Node_3 {
    children: Node_3[];
    sourceSpan: ParseSourceSpan;
    constructor(children: Node_3[], sourceSpan: ParseSourceSpan);
    visit(visitor: Visitor_2, context?: any): any;
}

/**
 * A structure to hold the cooked and raw strings of a template literal element, along with its
 * source-span range.
 */
declare interface CookedRawString {
    cooked: string;
    raw: string;
    range: ParseSourceSpan | null;
}

declare namespace core {
    export {
        parseSelectorToR3Selector,
        emitDistinctChangesOnlyDefaultValue,
        ViewEncapsulation,
        ChangeDetectionStrategy,
        Input,
        InputFlags,
        Output,
        HostBinding,
        HostListener,
        SchemaMetadata,
        CUSTOM_ELEMENTS_SCHEMA,
        NO_ERRORS_SCHEMA,
        Type_2 as Type,
        SecurityContext,
        InjectFlags,
        MissingTranslationStrategy,
        SelectorFlags,
        R3CssSelector,
        R3CssSelectorList,
        RenderFlags,
        AttributeMarker
    }
}
export { core }

/**
 * Creates a `CssSelector` from an AST node.
 */
export declare function createCssSelectorFromNode(node: t.Element | t.Template): CssSelector;

export declare function createInjectableType(meta: R3InjectableMetadata): outputAst.ExpressionType;

export declare function createMayBeForwardRefExpression<T extends outputAst.Expression>(expression: T, forwardRef: ForwardRefHandling): MaybeForwardRefExpression<T>;


/**
 * A css selector contains an element name,
 * css classes and attribute/value pairs with the purpose
 * of selecting subsets out of them.
 */
export declare class CssSelector {
    element: string | null;
    classNames: string[];
    /**
     * The selectors are encoded in pairs where:
     * - even locations are attribute names
     * - odd locations are attribute values.
     *
     * Example:
     * Selector: `[key1=value1][key2]` would parse to:
     * ```
     * ['key1', 'value1', 'key2', '']
     * ```
     */
    attrs: string[];
    notSelectors: CssSelector[];
    static parse(selector: string): CssSelector[];
    /**
     * Unescape `\$` sequences from the CSS attribute selector.
     *
     * This is needed because `$` can have a special meaning in CSS selectors,
     * but we might want to match an attribute that contains `$`.
     * [MDN web link for more
     * info](https://developer.mozilla.org/en-US/docs/Web/CSS/Attribute_selectors).
     * @param attr the attribute to unescape.
     * @returns the unescaped string.
     */
    unescapeAttribute(attr: string): string;
    /**
     * Escape `$` sequences from the CSS attribute selector.
     *
     * This is needed because `$` can have a special meaning in CSS selectors,
     * with this method we are escaping `$` with `\$'.
     * [MDN web link for more
     * info](https://developer.mozilla.org/en-US/docs/Web/CSS/Attribute_selectors).
     * @param attr the attribute to escape.
     * @returns the escaped string.
     */
    escapeAttribute(attr: string): string;
    isElementSelector(): boolean;
    hasElementSelector(): boolean;
    setElement(element?: string | null): void;
    getAttrs(): string[];
    addAttribute(name: string, value?: string): void;
    addClassName(name: string): void;
    toString(): string;
}

export declare const CUSTOM_ELEMENTS_SCHEMA: SchemaMetadata;

/**
 * Specifies how a list of declaration type references should be emitted into the generated code.
 */
export declare const enum DeclarationListEmitMode {
    /**
     * The list of declarations is emitted into the generated code as is.
     *
     * ```
     * directives: [MyDir],
     * ```
     */
    Direct = 0,
    /**
     * The list of declarations is emitted into the generated code wrapped inside a closure, which
     * is needed when at least one declaration is a forward reference.
     *
     * ```
     * directives: function () { return [MyDir, ForwardDir]; },
     * ```
     */
    Closure = 1,
    /**
     * Similar to `Closure`, with the addition that the list of declarations can contain individual
     * items that are themselves forward references. This is relevant for JIT compilations, as
     * unwrapping the forwardRef cannot be done statically so must be deferred. This mode emits
     * the declaration list using a mapping transform through `resolveForwardRef` to ensure that
     * any forward references within the list are resolved when the outer closure is invoked.
     *
     * Consider the case where the runtime has captured two declarations in two distinct values:
     * ```
     * const dirA = MyDir;
     * const dirB = forwardRef(function() { return ForwardRef; });
     * ```
     *
     * This mode would emit the declarations captured in `dirA` and `dirB` as follows:
     * ```
     * directives: function () { return [dirA, dirB].map(ng.resolveForwardRef); },
     * ```
     */
    ClosureResolved = 2,
    RuntimeResolved = 3
}

export declare interface DeclareComponentTemplateInfo {
    /**
     * The string contents of the template.
     *
     * This is the "logical" template string, after expansion of any escaped characters (for inline
     * templates). This may differ from the actual template bytes as they appear in the .ts file.
     */
    content: string;
    /**
     * A full path to the file which contains the template.
     *
     * This can be either the original .ts file if the template is inline, or the .html file if an
     * external file was used.
     */
    sourceUrl: string;
    /**
     * Whether the template was inline (using `template`) or external (using `templateUrl`).
     */
    isInline: boolean;
    /**
     * If the template was defined inline by a direct string literal, then this is that literal
     * expression. Otherwise `null`, if the template was not defined inline or was not a literal.
     */
    inlineTemplateLiteralExpression: outputAst.Expression | null;
}

export declare class DeclareFunctionStmt extends Statement {
    name: string;
    params: FnParam[];
    statements: Statement[];
    type: Type | null;
    constructor(name: string, params: FnParam[], statements: Statement[], type?: Type | null, modifiers?: StmtModifier, sourceSpan?: ParseSourceSpan | null, leadingComments?: LeadingComment[]);
    isEquivalent(stmt: Statement): boolean;
    visitStatement(visitor: StatementVisitor, context: any): any;
}

export declare class DeclareVarStmt extends Statement {
    name: string;
    value?: Expression | undefined;
    type: Type | null;
    constructor(name: string, value?: Expression | undefined, type?: Type | null, modifiers?: StmtModifier, sourceSpan?: ParseSourceSpan | null, leadingComments?: LeadingComment[]);
    isEquivalent(stmt: Statement): boolean;
    visitStatement(visitor: StatementVisitor, context: any): any;
}

export declare const DEFAULT_INTERPOLATION_CONFIG: InterpolationConfig;

/**
 * Defines how dynamic imports for deferred dependencies should be emitted in the
 * generated output:
 *  - either in a function on per-component basis (in case of local compilation)
 *  - or in a function on per-block basis (in full compilation mode)
 */
export declare const enum DeferBlockDepsEmitMode {
    /**
     * Dynamic imports are grouped on per-block basis.
     *
     * This is used in full compilation mode, when compiler has more information
     * about particular dependencies that belong to this block.
     */
    PerBlock = 0,
    /**
     * Dynamic imports are grouped on per-component basis.
     *
     * In local compilation, compiler doesn't have enough information to determine
     * which deferred dependencies belong to which block. In this case we group all
     * dynamic imports into a single file on per-component basis.
     */
    PerComponent = 1
}

export declare function devOnlyGuardedExpression(expr: outputAst.Expression): outputAst.Expression;

/**
 * Metadata regarding a directive that's needed to match it against template elements. This is
 * provided by a consumer of the t2 APIs.
 */
export declare interface DirectiveMeta {
    /**
     * Name of the directive class (used for debugging).
     */
    name: string;
    /** The selector for the directive or `null` if there isn't one. */
    selector: string | null;
    /**
     * Whether the directive is a component.
     */
    isComponent: boolean;
    /**
     * Set of inputs which this directive claims.
     *
     * Goes from property names to field names.
     */
    inputs: InputOutputPropertySet;
    /**
     * Set of outputs which this directive claims.
     *
     * Goes from property names to field names.
     */
    outputs: InputOutputPropertySet;
    /**
     * Name under which the directive is exported, if any (exportAs in Angular).
     *
     * Null otherwise
     */
    exportAs: string[] | null;
    /**
     * Whether the directive is a structural directive (e.g. `<div *ngIf></div>`).
     */
    isStructural: boolean;
    /**
     * If the directive is a component, includes the selectors of its `ng-content` elements.
     */
    ngContentSelectors: string[] | null;
    /**
     * Whether the template of the component preserves whitespaces.
     */
    preserveWhitespaces: boolean;
    /**
     * The name of animations that the user defines in the component.
     * Only includes the animation names.
     */
    animationTriggerNames: AnimationTriggerNames | null;
}

export declare class DomElementSchemaRegistry extends ElementSchemaRegistry {
    private _schema;
    private _eventSchema;
    constructor();
    hasProperty(tagName: string, propName: string, schemaMetas: SchemaMetadata[]): boolean;
    hasElement(tagName: string, schemaMetas: SchemaMetadata[]): boolean;
    /**
     * securityContext returns the security context for the given property on the given DOM tag.
     *
     * Tag and property name are statically known and cannot change at runtime, i.e. it is not
     * possible to bind a value into a changing attribute or tag name.
     *
     * The filtering is based on a list of allowed tags|attributes. All attributes in the schema
     * above are assumed to have the 'NONE' security context, i.e. that they are safe inert
     * string values. Only specific well known attack vectors are assigned their appropriate context.
     */
    securityContext(tagName: string, propName: string, isAttribute: boolean): SecurityContext;
    getMappedPropName(propName: string): string;
    getDefaultComponentElementName(): string;
    validateProperty(name: string): {
        error: boolean;
        msg?: string;
    };
    validateAttribute(name: string): {
        error: boolean;
        msg?: string;
    };
    allKnownElementNames(): string[];
    allKnownAttributesOfElement(tagName: string): string[];
    allKnownEventsOfElement(tagName: string): string[];
    normalizeAnimationStyleProperty(propName: string): string;
    normalizeAnimationStyleValue(camelCaseProp: string, userProvidedProp: string, val: string | number): {
        error: string;
        value: string;
    };
}

export declare const DYNAMIC_TYPE: BuiltinType;

export declare class DynamicImportExpr extends Expression {
    url: string;
    constructor(url: string, sourceSpan?: ParseSourceSpan | null);
    isEquivalent(e: Expression): boolean;
    isConstant(): boolean;
    visitExpression(visitor: ExpressionVisitor, context: any): any;
    clone(): DynamicImportExpr;
}

declare class Element_2 extends NodeWithI18n {
    name: string;
    attrs: Attribute[];
    children: Node_2[];
    startSourceSpan: ParseSourceSpan;
    endSourceSpan: ParseSourceSpan | null;
    constructor(name: string, attrs: Attribute[], children: Node_2[], sourceSpan: ParseSourceSpan, startSourceSpan: ParseSourceSpan, endSourceSpan?: ParseSourceSpan | null, i18n?: I18nMeta_2);
    visit(visitor: Visitor, context: any): any;
}
export { Element_2 as Element }

export declare abstract class ElementSchemaRegistry {
    abstract hasProperty(tagName: string, propName: string, schemaMetas: SchemaMetadata[]): boolean;
    abstract hasElement(tagName: string, schemaMetas: SchemaMetadata[]): boolean;
    abstract securityContext(elementName: string, propName: string, isAttribute: boolean): SecurityContext;
    abstract allKnownElementNames(): string[];
    abstract getMappedPropName(propName: string): string;
    abstract getDefaultComponentElementName(): string;
    abstract validateProperty(name: string): {
        error: boolean;
        msg?: string;
    };
    abstract validateAttribute(name: string): {
        error: boolean;
        msg?: string;
    };
    abstract normalizeAnimationStyleProperty(propName: string): string;
    abstract normalizeAnimationStyleValue(camelCaseProp: string, userProvidedProp: string, val: string | number): {
        error: string;
        value: string;
    };
}


export declare const emitDistinctChangesOnlyDefaultValue = true;

export declare class EmitterVisitorContext {
    private _indent;
    static createRoot(): EmitterVisitorContext;
    private _lines;
    constructor(_indent: number);
    println(from?: {
        sourceSpan: ParseSourceSpan | null;
    } | null, lastPart?: string): void;
    lineIsEmpty(): boolean;
    lineLength(): number;
    print(from: {
        sourceSpan: ParseSourceSpan | null;
    } | null, part: string, newLine?: boolean): void;
    removeEmptyLastLine(): void;
    incIndent(): void;
    decIndent(): void;
    toSource(): string;
    toSourceMapGenerator(genFilePath: string, startsAtLine?: number): SourceMapGenerator;
    spanOf(line: number, column: number): ParseSourceSpan | null;
}

export declare class EmptyExpr extends AST {
    visit(visitor: AstVisitor, context?: any): void;
}

/**
 * Encapsulates a CSS stylesheet with emulated view encapsulation.
 * This allows a stylesheet to be used with an Angular component that
 * is using the `ViewEncapsulation.Emulated` mode.
 *
 * @param style The content of a CSS stylesheet.
 * @returns The encapsulated content for the style.
 */
export declare function encapsulateStyle(style: string): string;

declare interface EncodedEntityToken extends TokenBase {
    type: LexerTokenType.ENCODED_ENTITY;
    parts: [decoded: string, encoded: string];
}

export declare const EOF: Token;

export declare class Expansion extends NodeWithI18n {
    switchValue: string;
    type: string;
    cases: ExpansionCase[];
    switchValueSourceSpan: ParseSourceSpan;
    constructor(switchValue: string, type: string, cases: ExpansionCase[], sourceSpan: ParseSourceSpan, switchValueSourceSpan: ParseSourceSpan, i18n?: I18nMeta_2);
    visit(visitor: Visitor, context: any): any;
}

export declare class ExpansionCase implements BaseNode {
    value: string;
    expression: Node_2[];
    sourceSpan: ParseSourceSpan;
    valueSourceSpan: ParseSourceSpan;
    expSourceSpan: ParseSourceSpan;
    constructor(value: string, expression: Node_2[], sourceSpan: ParseSourceSpan, valueSourceSpan: ParseSourceSpan, expSourceSpan: ParseSourceSpan);
    visit(visitor: Visitor, context: any): any;
}

export declare abstract class Expression {
    type: Type | null;
    sourceSpan: ParseSourceSpan | null;
    constructor(type: Type | null | undefined, sourceSpan?: ParseSourceSpan | null);
    abstract visitExpression(visitor: ExpressionVisitor, context: any): any;
    /**
     * Calculates whether this expression produces the same value as the given expression.
     * Note: We don't check Types nor ParseSourceSpans nor function arguments.
     */
    abstract isEquivalent(e: Expression): boolean;
    /**
     * Return true if the expression is constant.
     */
    abstract isConstant(): boolean;
    abstract clone(): Expression;
    prop(name: string, sourceSpan?: ParseSourceSpan | null): ReadPropExpr;
    key(index: Expression, type?: Type | null, sourceSpan?: ParseSourceSpan | null): ReadKeyExpr;
    callFn(params: Expression[], sourceSpan?: ParseSourceSpan | null, pure?: boolean): InvokeFunctionExpr;
    instantiate(params: Expression[], type?: Type | null, sourceSpan?: ParseSourceSpan | null): InstantiateExpr;
    conditional(trueCase: Expression, falseCase?: Expression | null, sourceSpan?: ParseSourceSpan | null): ConditionalExpr;
    equals(rhs: Expression, sourceSpan?: ParseSourceSpan | null): BinaryOperatorExpr;
    notEquals(rhs: Expression, sourceSpan?: ParseSourceSpan | null): BinaryOperatorExpr;
    identical(rhs: Expression, sourceSpan?: ParseSourceSpan | null): BinaryOperatorExpr;
    notIdentical(rhs: Expression, sourceSpan?: ParseSourceSpan | null): BinaryOperatorExpr;
    minus(rhs: Expression, sourceSpan?: ParseSourceSpan | null): BinaryOperatorExpr;
    plus(rhs: Expression, sourceSpan?: ParseSourceSpan | null): BinaryOperatorExpr;
    divide(rhs: Expression, sourceSpan?: ParseSourceSpan | null): BinaryOperatorExpr;
    multiply(rhs: Expression, sourceSpan?: ParseSourceSpan | null): BinaryOperatorExpr;
    modulo(rhs: Expression, sourceSpan?: ParseSourceSpan | null): BinaryOperatorExpr;
    and(rhs: Expression, sourceSpan?: ParseSourceSpan | null): BinaryOperatorExpr;
    bitwiseOr(rhs: Expression, sourceSpan?: ParseSourceSpan | null, parens?: boolean): BinaryOperatorExpr;
    bitwiseAnd(rhs: Expression, sourceSpan?: ParseSourceSpan | null, parens?: boolean): BinaryOperatorExpr;
    or(rhs: Expression, sourceSpan?: ParseSourceSpan | null): BinaryOperatorExpr;
    lower(rhs: Expression, sourceSpan?: ParseSourceSpan | null): BinaryOperatorExpr;
    lowerEquals(rhs: Expression, sourceSpan?: ParseSourceSpan | null): BinaryOperatorExpr;
    bigger(rhs: Expression, sourceSpan?: ParseSourceSpan | null): BinaryOperatorExpr;
    biggerEquals(rhs: Expression, sourceSpan?: ParseSourceSpan | null): BinaryOperatorExpr;
    isBlank(sourceSpan?: ParseSourceSpan | null): Expression;
    nullishCoalesce(rhs: Expression, sourceSpan?: ParseSourceSpan | null): BinaryOperatorExpr;
    toStmt(): Statement;
}

export declare class ExpressionBinding {
    readonly sourceSpan: AbsoluteSourceSpan;
    readonly key: TemplateBindingIdentifier;
    readonly value: ASTWithSource | null;
    /**
     * @param sourceSpan entire span of the binding.
     * @param key binding name, like ngForOf, ngForTrackBy, ngIf, along with its
     * span. Note that the length of the span may not be the same as
     * `key.source.length`. For example,
     * 1. key.source = ngFor, key.span is for "ngFor"
     * 2. key.source = ngForOf, key.span is for "of"
     * 3. key.source = ngForTrackBy, key.span is for "trackBy"
     * @param value optional expression for the RHS.
     */
    constructor(sourceSpan: AbsoluteSourceSpan, key: TemplateBindingIdentifier, value: ASTWithSource | null);
}

declare interface ExpressionKeyFn {
    keyOf(expr: outputAst.Expression): string;
}

export declare class ExpressionStatement extends Statement {
    expr: Expression;
    constructor(expr: Expression, sourceSpan?: ParseSourceSpan | null, leadingComments?: LeadingComment[]);
    isEquivalent(stmt: Statement): boolean;
    visitStatement(visitor: StatementVisitor, context: any): any;
}

export declare class ExpressionType extends Type {
    value: Expression;
    typeParams: Type[] | null;
    constructor(value: Expression, modifiers?: TypeModifier, typeParams?: Type[] | null);
    visitType(visitor: TypeVisitor, context: any): any;
}

declare function expressionType(expr: Expression, typeModifiers?: TypeModifier, typeParams?: Type[] | null): ExpressionType;

export declare interface ExpressionVisitor {
    visitReadVarExpr(ast: ReadVarExpr, context: any): any;
    visitWriteVarExpr(expr: WriteVarExpr, context: any): any;
    visitWriteKeyExpr(expr: WriteKeyExpr, context: any): any;
    visitWritePropExpr(expr: WritePropExpr, context: any): any;
    visitInvokeFunctionExpr(ast: InvokeFunctionExpr, context: any): any;
    visitTaggedTemplateExpr(ast: TaggedTemplateExpr, context: any): any;
    visitInstantiateExpr(ast: InstantiateExpr, context: any): any;
    visitLiteralExpr(ast: LiteralExpr, context: any): any;
    visitLocalizedString(ast: LocalizedString, context: any): any;
    visitExternalExpr(ast: ExternalExpr, context: any): any;
    visitConditionalExpr(ast: ConditionalExpr, context: any): any;
    visitDynamicImportExpr(ast: DynamicImportExpr, context: any): any;
    visitNotExpr(ast: NotExpr, context: any): any;
    visitFunctionExpr(ast: FunctionExpr, context: any): any;
    visitUnaryOperatorExpr(ast: UnaryOperatorExpr, context: any): any;
    visitBinaryOperatorExpr(ast: BinaryOperatorExpr, context: any): any;
    visitReadPropExpr(ast: ReadPropExpr, context: any): any;
    visitReadKeyExpr(ast: ReadKeyExpr, context: any): any;
    visitLiteralArrayExpr(ast: LiteralArrayExpr, context: any): any;
    visitLiteralMapExpr(ast: LiteralMapExpr, context: any): any;
    visitCommaExpr(ast: CommaExpr, context: any): any;
    visitWrappedNodeExpr(ast: WrappedNodeExpr<any>, context: any): any;
    visitTypeofExpr(ast: TypeofExpr, context: any): any;
    visitArrowFunctionExpr(ast: ArrowFunctionExpr, context: any): any;
}

export declare class ExternalExpr extends Expression {
    value: ExternalReference;
    typeParams: Type[] | null;
    constructor(value: ExternalReference, type?: Type | null, typeParams?: Type[] | null, sourceSpan?: ParseSourceSpan | null);
    isEquivalent(e: Expression): boolean;
    isConstant(): boolean;
    visitExpression(visitor: ExpressionVisitor, context: any): any;
    clone(): ExternalExpr;
}

export declare class ExternalReference {
    moduleName: string | null;
    name: string | null;
    runtime?: any;
    constructor(moduleName: string | null, name: string | null, runtime?: any);
}

declare interface ExternalReferenceResolver {
    resolveExternalReference(ref: outputAst.ExternalReference): unknown;
}

export declare enum FactoryTarget {
    Directive = 0,
    Component = 1,
    Injectable = 2,
    Pipe = 3,
    NgModule = 4
}

declare enum FactoryTarget_2 {
    Directive = 0,
    Component = 1,
    Injectable = 2,
    Pipe = 3,
    NgModule = 4
}

declare function fn(params: FnParam[], body: Statement[], type?: Type | null, sourceSpan?: ParseSourceSpan | null, name?: string | null): FunctionExpr;

declare class FnParam {
    name: string;
    type: Type | null;
    constructor(name: string, type?: Type | null);
    isEquivalent(param: FnParam): boolean;
    clone(): FnParam;
}

/** Context variables that can be used inside a `ForLoopBlock`. */
declare type ForLoopBlockContext = Record<'$index' | '$first' | '$last' | '$even' | '$odd' | '$count', TmplAstVariable>;

/**
 * Specifies how a forward ref has been handled in a MaybeForwardRefExpression
 */
export declare const enum ForwardRefHandling {
    /** The expression was not wrapped in a `forwardRef()` call in the first place. */
    None = 0,
    /** The expression is still wrapped in a `forwardRef()` call. */
    Wrapped = 1,
    /** The expression was wrapped in a `forwardRef()` call but has since been unwrapped. */
    Unwrapped = 2
}

declare const FUNCTION_TYPE: BuiltinType;

export declare class FunctionExpr extends Expression {
    params: FnParam[];
    statements: Statement[];
    name?: string | null | undefined;
    constructor(params: FnParam[], statements: Statement[], type?: Type | null, sourceSpan?: ParseSourceSpan | null, name?: string | null | undefined);
    isEquivalent(e: Expression | Statement): boolean;
    isConstant(): boolean;
    visitExpression(visitor: ExpressionVisitor, context: any): any;
    toDeclStmt(name: string, modifiers?: StmtModifier): DeclareFunctionStmt;
    clone(): FunctionExpr;
}

export declare function getHtmlTagDefinition(tagName: string): HtmlTagDefinition;

export declare function getNsPrefix(fullName: string): string;

export declare function getNsPrefix(fullName: null): null;

export declare function getSafePropertyAccessString(accessor: string, name: string): string;

declare interface HostBinding {
    hostPropertyName?: string;
}

declare interface HostListener {
    eventName?: string;
    args?: string[];
}

declare interface HostListeners {
    [key: string]: string;
}

declare interface HostProperties {
    [key: string]: string;
}

declare namespace html {
    export {
        visitAll,
        Node_2 as Node,
        NodeWithI18n,
        Text_2 as Text,
        Expansion,
        ExpansionCase,
        Attribute,
        Element_2 as Element,
        Comment_2 as Comment,
        Block,
        BlockParameter,
        Visitor,
        RecursiveVisitor
    }
}

export declare class HtmlParser extends Parser_2 {
    constructor();
    parse(source: string, url: string, options?: TokenizeOptions): ParseTreeResult;
}

export declare class HtmlTagDefinition implements TagDefinition {
    private closedByChildren;
    private contentType;
    closedByParent: boolean;
    implicitNamespacePrefix: string | null;
    isVoid: boolean;
    ignoreFirstLf: boolean;
    canSelfClose: boolean;
    preventNamespaceInheritance: boolean;
    constructor({ closedByChildren, implicitNamespacePrefix, contentType, closedByParent, isVoid, ignoreFirstLf, preventNamespaceInheritance, canSelfClose, }?: {
        closedByChildren?: string[];
        closedByParent?: boolean;
        implicitNamespacePrefix?: string;
        contentType?: TagContentType | {
            default: TagContentType;
            [namespace: string]: TagContentType;
        };
        isVoid?: boolean;
        ignoreFirstLf?: boolean;
        preventNamespaceInheritance?: boolean;
        canSelfClose?: boolean;
    });
    isClosedByChild(name: string): boolean;
    getContentType(prefix?: string): TagContentType;
}

declare namespace i18n {
    export {
        MessagePlaceholder,
        Message,
        MessageSpan,
        Node_3 as Node,
        Text_3 as Text,
        Container,
        Icu,
        TagPlaceholder,
        Placeholder,
        IcuPlaceholder,
        BlockPlaceholder,
        I18nMeta_2 as I18nMeta,
        Visitor_2 as Visitor,
        CloneVisitor,
        RecurseVisitor
    }
}

export declare class I18NHtmlParser implements HtmlParser {
    private _htmlParser;
    getTagDefinition: any;
    private _translationBundle;
    constructor(_htmlParser: HtmlParser, translations?: string, translationsFormat?: string, missingTranslation?: MissingTranslationStrategy, console?: Console_2);
    parse(source: string, url: string, options?: TokenizeOptions): ParseTreeResult;
}

declare type I18nMeta = {
    id?: string;
    customId?: string;
    legacyIds?: string[];
    description?: string;
    meaning?: string;
};

/**
 * Each HTML node that is affect by an i18n tag will also have an `i18n` property that is of type
 * `I18nMeta`.
 * This information is either a `Message`, which indicates it is the root of an i18n message, or a
 * `Node`, which indicates is it part of a containing `Message`.
 */
declare type I18nMeta_2 = Message | Node_3;

declare class Icu implements Node_3 {
    expression: string;
    type: string;
    cases: {
        [k: string]: Node_3;
    };
    sourceSpan: ParseSourceSpan;
    expressionPlaceholder?: string | undefined;
    constructor(expression: string, type: string, cases: {
        [k: string]: Node_3;
    }, sourceSpan: ParseSourceSpan, expressionPlaceholder?: string | undefined);
    visit(visitor: Visitor_2, context?: any): any;
}

declare class IcuPlaceholder implements Node_3 {
    value: Icu;
    name: string;
    sourceSpan: ParseSourceSpan;
    /** Used to capture a message computed from a previous processing pass (see `setI18nRefs()`). */
    previousMessage?: Message;
    constructor(value: Icu, name: string, sourceSpan: ParseSourceSpan);
    visit(visitor: Visitor_2, context?: any): any;
}

export declare function identifierName(compileIdentifier: CompileIdentifierMetadata | null | undefined): string | null;

export declare class IfStmt extends Statement {
    condition: Expression;
    trueCase: Statement[];
    falseCase: Statement[];
    constructor(condition: Expression, trueCase: Statement[], falseCase?: Statement[], sourceSpan?: ParseSourceSpan | null, leadingComments?: LeadingComment[]);
    isEquivalent(stmt: Statement): boolean;
    visitStatement(visitor: StatementVisitor, context: any): any;
}

declare function ifStmt(condition: Expression, thenClause: Statement[], elseClause?: Statement[], sourceSpan?: ParseSourceSpan, leadingComments?: LeadingComment[]): IfStmt;

export declare class ImplicitReceiver extends AST {
    visit(visitor: AstVisitor, context?: any): any;
}

declare function importExpr(id: ExternalReference, typeParams?: Type[] | null, sourceSpan?: ParseSourceSpan | null): ExternalExpr;

declare function importType(id: ExternalReference, typeParams?: Type[] | null, typeModifiers?: TypeModifier): ExpressionType | null;

declare const INFERRED_TYPE: BuiltinType;

/**
 * Injection flags for DI.
 */
declare const enum InjectFlags {
    Default = 0,
    /**
     * Specifies that an injector should retrieve a dependency from any injector until reaching the
     * host element of the current component. (Only used with Element Injector)
     */
    Host = 1,
    /** Don't descend into ancestors of the node requesting injection. */
    Self = 2,
    /** Skip the node that is requesting injection. */
    SkipSelf = 4,
    /** Inject `defaultValue` instead if token not found. */
    Optional = 8
}

declare interface Input {
    alias?: string;
    required?: boolean;
    transform?: (value: any) => any;
    isSignal: boolean;
}

/** Flags describing an input for a directive. */
declare enum InputFlags {
    None = 0,
    SignalBased = 1,
    HasDecoratorInputTransform = 2
}

/**
 * A data structure which can indicate whether a given property name is present or not.
 *
 * This is used to represent the set of inputs or outputs present on a directive, and allows the
 * binder to query for the presence of a mapping for property names.
 */
export declare interface InputOutputPropertySet {
    hasBindingPropertyName(propertyName: string): boolean;
}

export declare class InstantiateExpr extends Expression {
    classExpr: Expression;
    args: Expression[];
    constructor(classExpr: Expression, args: Expression[], type?: Type | null, sourceSpan?: ParseSourceSpan | null);
    isEquivalent(e: Expression): boolean;
    isConstant(): boolean;
    visitExpression(visitor: ExpressionVisitor, context: any): any;
    clone(): InstantiateExpr;
}

declare const INT_TYPE: BuiltinType;

declare type InterpolatedAttributeToken = AttributeValueTextToken | AttributeValueInterpolationToken | EncodedEntityToken;

declare type InterpolatedTextToken = TextToken | InterpolationToken | EncodedEntityToken;

export declare class Interpolation extends AST {
    strings: string[];
    expressions: AST[];
    constructor(span: ParseSpan, sourceSpan: AbsoluteSourceSpan, strings: string[], expressions: AST[]);
    visit(visitor: AstVisitor, context?: any): any;
}


export declare class InterpolationConfig {
    start: string;
    end: string;
    static fromArray(markers: [string, string] | null): InterpolationConfig;
    constructor(start: string, end: string);
}

export declare interface InterpolationPiece {
    text: string;
    start: number;
    end: number;
}

declare interface InterpolationToken extends TokenBase {
    type: LexerTokenType.INTERPOLATION;
    parts: [startMarker: string, expression: string, endMarker: string] | [
    startMarker: string,
    expression: string
    ];
}

export declare class InvokeFunctionExpr extends Expression {
    fn: Expression;
    args: Expression[];
    pure: boolean;
    constructor(fn: Expression, args: Expression[], type?: Type | null, sourceSpan?: ParseSourceSpan | null, pure?: boolean);
    get receiver(): Expression;
    isEquivalent(e: Expression): boolean;
    isConstant(): boolean;
    visitExpression(visitor: ExpressionVisitor, context: any): any;
    clone(): InvokeFunctionExpr;
}

export declare function isIdentifier(input: string): boolean;

export declare function isNgContainer(tagName: string): boolean;

export declare function isNgContent(tagName: string): boolean;

export declare function isNgTemplate(tagName: string): boolean;

declare function isNull(exp: Expression): boolean;

/**
 * A helper class to manage the evaluation of JIT generated code.
 */
export declare class JitEvaluator {
    /**
     *
     * @param sourceUrl The URL of the generated code.
     * @param statements An array of Angular statement AST nodes to be evaluated.
     * @param refResolver Resolves `o.ExternalReference`s into values.
     * @param createSourceMaps If true then create a source-map for the generated code and include it
     * inline as a source-map comment.
     * @returns A map of all the variables in the generated code.
     */
    evaluateStatements(sourceUrl: string, statements: outputAst.Statement[], refResolver: ExternalReferenceResolver, createSourceMaps: boolean): {
        [key: string]: any;
    };
    /**
     * Evaluate a piece of JIT generated code.
     * @param sourceUrl The URL of this generated code.
     * @param ctx A context object that contains an AST of the code to be evaluated.
     * @param vars A map containing the names and values of variables that the evaluated code might
     * reference.
     * @param createSourceMap If true then create a source-map for the generated code and include it
     * inline as a source-map comment.
     * @returns The result of evaluating the code.
     */
    evaluateCode(sourceUrl: string, ctx: EmitterVisitorContext, vars: {
        [key: string]: any;
    }, createSourceMap: boolean): any;
    /**
     * Execute a JIT generated function by calling it.
     *
     * This method can be overridden in tests to capture the functions that are generated
     * by this `JitEvaluator` class.
     *
     * @param fn A function to execute.
     * @param args The arguments to pass to the function being executed.
     * @returns The return value of the executed function.
     */
    executeFunction(fn: Function, args: any[]): any;
}

export declare class JSDocComment extends LeadingComment {
    tags: JSDocTag[];
    constructor(tags: JSDocTag[]);
    toString(): string;
}

export declare function jsDocComment(tags?: JSDocTag[]): JSDocComment;

declare type JSDocTag = {
    tagName: JSDocTagName | string;
    text?: string;
} | {
    tagName?: undefined;
    text: string;
};

declare const enum JSDocTagName {
    Desc = "desc",
    Id = "id",
    Meaning = "meaning",
    Suppress = "suppress"
}

export declare class KeyedRead extends AST {
    receiver: AST;
    key: AST;
    constructor(span: ParseSpan, sourceSpan: AbsoluteSourceSpan, receiver: AST, key: AST);
    visit(visitor: AstVisitor, context?: any): any;
}

export declare class KeyedWrite extends AST {
    receiver: AST;
    key: AST;
    value: AST;
    constructor(span: ParseSpan, sourceSpan: AbsoluteSourceSpan, receiver: AST, key: AST, value: AST);
    visit(visitor: AstVisitor, context?: any): any;
}

export declare class LeadingComment {
    text: string;
    multiline: boolean;
    trailingNewline: boolean;
    constructor(text: string, multiline: boolean, trailingNewline: boolean);
    toString(): string;
}

export declare function leadingComment(text: string, multiline?: boolean, trailingNewline?: boolean): LeadingComment;

export declare type LegacyInputPartialMapping = string | [
bindingPropertyName: string,
classPropertyName: string,
transformFunction?: outputAst.Expression
];

export declare class Lexer {
    tokenize(text: string): Token[];
}

export declare interface LexerRange {
    startPos: number;
    startLine: number;
    startCol: number;
    endPos: number;
}

export declare const enum LexerTokenType {
    TAG_OPEN_START = 0,
    TAG_OPEN_END = 1,
    TAG_OPEN_END_VOID = 2,
    TAG_CLOSE = 3,
    INCOMPLETE_TAG_OPEN = 4,
    TEXT = 5,
    ESCAPABLE_RAW_TEXT = 6,
    RAW_TEXT = 7,
    INTERPOLATION = 8,
    ENCODED_ENTITY = 9,
    COMMENT_START = 10,
    COMMENT_END = 11,
    CDATA_START = 12,
    CDATA_END = 13,
    ATTR_NAME = 14,
    ATTR_QUOTE = 15,
    ATTR_VALUE_TEXT = 16,
    ATTR_VALUE_INTERPOLATION = 17,
    DOC_TYPE = 18,
    EXPANSION_FORM_START = 19,
    EXPANSION_CASE_VALUE = 20,
    EXPANSION_CASE_EXP_START = 21,
    EXPANSION_CASE_EXP_END = 22,
    EXPANSION_FORM_END = 23,
    BLOCK_OPEN_START = 24,
    BLOCK_OPEN_END = 25,
    BLOCK_CLOSE = 26,
    BLOCK_PARAMETER = 27,
    INCOMPLETE_BLOCK_OPEN = 28,
    EOF = 29
}

export declare function literal(value: any, type?: Type | null, sourceSpan?: ParseSourceSpan | null): LiteralExpr;

declare function literalArr(values: Expression[], type?: Type | null, sourceSpan?: ParseSourceSpan | null): LiteralArrayExpr;

export declare class LiteralArray extends AST {
    expressions: any[];
    constructor(span: ParseSpan, sourceSpan: AbsoluteSourceSpan, expressions: any[]);
    visit(visitor: AstVisitor, context?: any): any;
}

export declare class LiteralArrayExpr extends Expression {
    entries: Expression[];
    constructor(entries: Expression[], type?: Type | null, sourceSpan?: ParseSourceSpan | null);
    isConstant(): boolean;
    isEquivalent(e: Expression): boolean;
    visitExpression(visitor: ExpressionVisitor, context: any): any;
    clone(): LiteralArrayExpr;
}

export declare class LiteralExpr extends Expression {
    value: number | string | boolean | null | undefined;
    constructor(value: number | string | boolean | null | undefined, type?: Type | null, sourceSpan?: ParseSourceSpan | null);
    isEquivalent(e: Expression): boolean;
    isConstant(): boolean;
    visitExpression(visitor: ExpressionVisitor, context: any): any;
    clone(): LiteralExpr;
}

export declare class LiteralMap extends AST {
    keys: LiteralMapKey[];
    values: any[];
    constructor(span: ParseSpan, sourceSpan: AbsoluteSourceSpan, keys: LiteralMapKey[], values: any[]);
    visit(visitor: AstVisitor, context?: any): any;
}

export declare function literalMap(values: {
    key: string;
    quoted: boolean;
    value: Expression;
}[], type?: MapType | null): LiteralMapExpr;

declare class LiteralMapEntry {
    key: string;
    value: Expression;
    quoted: boolean;
    constructor(key: string, value: Expression, quoted: boolean);
    isEquivalent(e: LiteralMapEntry): boolean;
    clone(): LiteralMapEntry;
}

export declare class LiteralMapExpr extends Expression {
    entries: LiteralMapEntry[];
    valueType: Type | null;
    constructor(entries: LiteralMapEntry[], type?: MapType | null, sourceSpan?: ParseSourceSpan | null);
    isEquivalent(e: Expression): boolean;
    isConstant(): boolean;
    visitExpression(visitor: ExpressionVisitor, context: any): any;
    clone(): LiteralMapExpr;
}

export declare type LiteralMapKey = {
    key: string;
    quoted: boolean;
};

declare class LiteralPiece {
    text: string;
    sourceSpan: ParseSourceSpan;
    constructor(text: string, sourceSpan: ParseSourceSpan);
}

export declare class LiteralPrimitive extends AST {
    value: any;
    constructor(span: ParseSpan, sourceSpan: AbsoluteSourceSpan, value: any);
    visit(visitor: AstVisitor, context?: any): any;
}

export declare class LocalizedString extends Expression {
    readonly metaBlock: I18nMeta;
    readonly messageParts: LiteralPiece[];
    readonly placeHolderNames: PlaceholderPiece[];
    readonly expressions: Expression[];
    constructor(metaBlock: I18nMeta, messageParts: LiteralPiece[], placeHolderNames: PlaceholderPiece[], expressions: Expression[], sourceSpan?: ParseSourceSpan | null);
    isEquivalent(e: Expression): boolean;
    isConstant(): boolean;
    visitExpression(visitor: ExpressionVisitor, context: any): any;
    clone(): LocalizedString;
    /**
     * Serialize the given `meta` and `messagePart` into "cooked" and "raw" strings that can be used
     * in a `$localize` tagged string. The format of the metadata is the same as that parsed by
     * `parseI18nMeta()`.
     *
     * @param meta The metadata to serialize
     * @param messagePart The first part of the tagged string
     */
    serializeI18nHead(): CookedRawString;
    getMessagePartSourceSpan(i: number): ParseSourceSpan | null;
    getPlaceholderSourceSpan(i: number): ParseSourceSpan;
    /**
     * Serialize the given `placeholderName` and `messagePart` into "cooked" and "raw" strings that
     * can be used in a `$localize` tagged string.
     *
     * The format is `:<placeholder-name>[@@<associated-id>]:`.
     *
     * The `associated-id` is the message id of the (usually an ICU) message to which this placeholder
     * refers.
     *
     * @param partIndex The index of the message part to serialize.
     */
    serializeI18nTemplatePart(partIndex: number): CookedRawString;
}

declare function localizedString(metaBlock: I18nMeta, messageParts: LiteralPiece[], placeholderNames: PlaceholderPiece[], expressions: Expression[], sourceSpan?: ParseSourceSpan | null): LocalizedString;

/**
 * Construct a `BindingParser` with a default configuration.
 */
export declare function makeBindingParser(interpolationConfig?: InterpolationConfig): BindingParser;

export declare class MapType extends Type {
    valueType: Type | null;
    constructor(valueType: Type | null | undefined, modifiers?: TypeModifier);
    visitType(visitor: TypeVisitor, context: any): any;
}

/**
 * Describes an expression that may have been wrapped in a `forwardRef()` guard.
 *
 * This is used when describing expressions that can refer to types that may eagerly reference types
 * that have not yet been defined.
 */
export declare interface MaybeForwardRefExpression<T extends outputAst.Expression = outputAst.Expression> {
    /**
     * The unwrapped expression.
     */
    expression: T;
    /**
     * Specified whether the `expression` contains a reference to something that has not yet been
     * defined, and whether the expression is still wrapped in a `forwardRef()` call.
     *
     * If this value is `ForwardRefHandling.None` then the `expression` is safe to use as-is.
     *
     * Otherwise the `expression` was wrapped in a call to `forwardRef()` and must not be eagerly
     * evaluated. Instead it must be wrapped in a function closure that will be evaluated lazily to
     * allow the definition of the expression to be evaluated first.
     *
     * In full AOT compilation it can be safe to unwrap the `forwardRef()` call up front if the
     * expression will actually be evaluated lazily inside a function call after the value of
     * `expression` has been defined.
     *
     * But in other cases, such as partial AOT compilation or JIT compilation the expression will be
     * evaluated eagerly in top level code so will need to continue to be wrapped in a `forwardRef()`
     * call.
     *
     */
    forwardRef: ForwardRefHandling;
}

export declare function mergeNsAndName(prefix: string, localName: string): string;

declare class Message {
    nodes: Node_3[];
    placeholders: {
        [phName: string]: MessagePlaceholder;
    };
    placeholderToMessage: {
        [phName: string]: Message;
    };
    meaning: string;
    description: string;
    customId: string;
    sources: MessageSpan[];
    id: string;
    /** The ids to use if there are no custom id and if `i18nLegacyMessageIdFormat` is not empty */
    legacyIds: string[];
    messageString: string;
    /**
     * @param nodes message AST
     * @param placeholders maps placeholder names to static content and their source spans
     * @param placeholderToMessage maps placeholder names to messages (used for nested ICU messages)
     * @param meaning
     * @param description
     * @param customId
     */
    constructor(nodes: Node_3[], placeholders: {
        [phName: string]: MessagePlaceholder;
    }, placeholderToMessage: {
        [phName: string]: Message;
    }, meaning: string, description: string, customId: string);
}

/**
 * A container for message extracted from the templates.
 */
export declare class MessageBundle {
    private _htmlParser;
    private _implicitTags;
    private _implicitAttrs;
    private _locale;
    private _messages;
    constructor(_htmlParser: HtmlParser, _implicitTags: string[], _implicitAttrs: {
        [k: string]: string[];
    }, _locale?: string | null);
    updateFromTemplate(html: string, url: string, interpolationConfig: InterpolationConfig): ParseError[];
    getMessages(): i18n.Message[];
    write(serializer: Serializer, filterSources?: (path: string) => string): string;
}

declare type MessagePiece = LiteralPiece | PlaceholderPiece;

/**
 * Describes the text contents of a placeholder as it appears in an ICU expression, including its
 * source span information.
 */
declare interface MessagePlaceholder {
    /** The text contents of the placeholder */
    text: string;
    /** The source span of the placeholder */
    sourceSpan: ParseSourceSpan;
}

declare interface MessageSpan {
    filePath: string;
    startLine: number;
    startCol: number;
    endLine: number;
    endCol: number;
}

declare enum MissingTranslationStrategy {
    Error = 0,
    Warning = 1,
    Ignore = 2
}

export declare const NO_ERRORS_SCHEMA: SchemaMetadata;

declare type Node_2 = Attribute | Comment_2 | Element_2 | Expansion | ExpansionCase | Text_2 | Block | BlockParameter;
export { Node_2 as Node }

declare interface Node_3 {
    sourceSpan: ParseSourceSpan;
    visit(visitor: Visitor_2, context?: any): any;
}

export declare abstract class NodeWithI18n implements BaseNode {
    sourceSpan: ParseSourceSpan;
    i18n?: I18nMeta_2 | undefined;
    constructor(sourceSpan: ParseSourceSpan, i18n?: I18nMeta_2 | undefined);
    abstract visit(visitor: Visitor, context: any): any;
}

export declare const NONE_TYPE: BuiltinType;

export declare class NonNullAssert extends AST {
    expression: AST;
    constructor(span: ParseSpan, sourceSpan: AbsoluteSourceSpan, expression: AST);
    visit(visitor: AstVisitor, context?: any): any;
}

declare function not(expr: Expression, sourceSpan?: ParseSourceSpan | null): NotExpr;

export declare class NotExpr extends Expression {
    condition: Expression;
    constructor(condition: Expression, sourceSpan?: ParseSourceSpan | null);
    isEquivalent(e: Expression): boolean;
    isConstant(): boolean;
    visitExpression(visitor: ExpressionVisitor, context: any): any;
    clone(): NotExpr;
}

declare const NULL_EXPR: LiteralExpr;

declare function nullSafeIsEquivalent<T extends {
    isEquivalent(other: T): boolean;
}>(base: T | null, other: T | null): boolean;

declare const NUMBER_TYPE: BuiltinType;

declare interface Output {
    alias?: string;
}

declare namespace outputAst {
    export {
        nullSafeIsEquivalent,
        areAllEquivalent,
        leadingComment,
        jsDocComment,
        variable,
        importExpr,
        importType,
        expressionType,
        transplantedType,
        typeofExpr,
        literalArr,
        literalMap,
        unary,
        not,
        fn,
        arrowFn,
        ifStmt,
        taggedTemplate,
        literal,
        localizedString,
        isNull,
        TypeModifier,
        Type,
        BuiltinTypeName,
        BuiltinType,
        ExpressionType,
        ArrayType,
        MapType,
        TransplantedType,
        DYNAMIC_TYPE,
        INFERRED_TYPE,
        BOOL_TYPE,
        INT_TYPE,
        NUMBER_TYPE,
        STRING_TYPE,
        FUNCTION_TYPE,
        NONE_TYPE,
        TypeVisitor,
        UnaryOperator,
        BinaryOperator,
        Expression,
        ReadVarExpr,
        TypeofExpr,
        WrappedNodeExpr,
        WriteVarExpr,
        WriteKeyExpr,
        WritePropExpr,
        InvokeFunctionExpr,
        TaggedTemplateExpr,
        InstantiateExpr,
        LiteralExpr,
        TemplateLiteral,
        TemplateLiteralElement,
        LiteralPiece,
        PlaceholderPiece,
        MessagePiece,
        LocalizedString,
        CookedRawString,
        ExternalExpr,
        ExternalReference,
        ConditionalExpr,
        DynamicImportExpr,
        NotExpr,
        FnParam,
        FunctionExpr,
        ArrowFunctionExpr,
        UnaryOperatorExpr,
        BinaryOperatorExpr,
        ReadPropExpr,
        ReadKeyExpr,
        LiteralArrayExpr,
        LiteralMapEntry,
        LiteralMapExpr,
        CommaExpr,
        ExpressionVisitor,
        NULL_EXPR,
        TYPED_NULL_EXPR,
        StmtModifier,
        LeadingComment,
        JSDocComment,
        Statement,
        DeclareVarStmt,
        DeclareFunctionStmt,
        ExpressionStatement,
        ReturnStatement,
        IfStmt,
        StatementVisitor,
        RecursiveAstVisitor_2 as RecursiveAstVisitor,
        JSDocTagName,
        JSDocTag
    }
}
export { outputAst }

export declare class ParsedEvent {
    name: string;
    targetOrPhase: string;
    type: ParsedEventType;
    handler: ASTWithSource;
    sourceSpan: ParseSourceSpan;
    handlerSpan: ParseSourceSpan;
    readonly keySpan: ParseSourceSpan;
    constructor(name: string, targetOrPhase: string, type: ParsedEventType.TwoWay, handler: ASTWithSource<NonNullAssert | PropertyRead | KeyedRead>, sourceSpan: ParseSourceSpan, handlerSpan: ParseSourceSpan, keySpan: ParseSourceSpan);
    constructor(name: string, targetOrPhase: string, type: ParsedEventType, handler: ASTWithSource, sourceSpan: ParseSourceSpan, handlerSpan: ParseSourceSpan, keySpan: ParseSourceSpan);
}

export declare const enum ParsedEventType {
    Regular = 0,
    Animation = 1,
    TwoWay = 2
}

export declare interface ParsedHostBindings {
    attributes: {
        [key: string]: outputAst.Expression;
    };
    listeners: {
        [key: string]: string;
    };
    properties: {
        [key: string]: string;
    };
    specialAttributes: {
        styleAttr?: string;
        classAttr?: string;
    };
}

export declare class ParsedProperty {
    name: string;
    expression: ASTWithSource;
    type: ParsedPropertyType;
    sourceSpan: ParseSourceSpan;
    readonly keySpan: ParseSourceSpan;
    valueSpan: ParseSourceSpan | undefined;
    readonly isLiteral: boolean;
    readonly isAnimation: boolean;
    constructor(name: string, expression: ASTWithSource, type: ParsedPropertyType, sourceSpan: ParseSourceSpan, keySpan: ParseSourceSpan, valueSpan: ParseSourceSpan | undefined);
}

export declare enum ParsedPropertyType {
    DEFAULT = 0,
    LITERAL_ATTR = 1,
    ANIMATION = 2,
    TWO_WAY = 3
}

/**
 * Information about the template which was extracted during parsing.
 *
 * This contains the actual parsed template as well as any metadata collected during its parsing,
 * some of which might be useful for re-parsing the template with different options.
 */
export declare interface ParsedTemplate {
    /**
     * Include whitespace nodes in the parsed output.
     */
    preserveWhitespaces?: boolean;
    /**
     * How to parse interpolation markers.
     */
    interpolationConfig?: InterpolationConfig;
    /**
     * Any errors from parsing the template the first time.
     *
     * `null` if there are no errors. Otherwise, the array of errors is guaranteed to be non-empty.
     */
    errors: ParseError[] | null;
    /**
     * The template AST, parsed from the template.
     */
    nodes: t.Node[];
    /**
     * Any styleUrls extracted from the metadata.
     */
    styleUrls: string[];
    /**
     * Any inline styles extracted from the metadata.
     */
    styles: string[];
    /**
     * Any ng-content selectors extracted from the template.
     */
    ngContentSelectors: string[];
    /**
     * Any R3 Comment Nodes extracted from the template when the `collectCommentNodes` parse template
     * option is enabled.
     */
    commentNodes?: t.Comment[];
}

/**
 * ParsedVariable represents a variable declaration in a microsyntax expression.
 */
export declare class ParsedVariable {
    readonly name: string;
    readonly value: string;
    readonly sourceSpan: ParseSourceSpan;
    readonly keySpan: ParseSourceSpan;
    readonly valueSpan?: ParseSourceSpan | undefined;
    constructor(name: string, value: string, sourceSpan: ParseSourceSpan, keySpan: ParseSourceSpan, valueSpan?: ParseSourceSpan | undefined);
}

export declare class ParseError {
    span: ParseSourceSpan;
    msg: string;
    level: ParseErrorLevel;
    constructor(span: ParseSourceSpan, msg: string, level?: ParseErrorLevel);
    contextualMessage(): string;
    toString(): string;
}

export declare enum ParseErrorLevel {
    WARNING = 0,
    ERROR = 1
}

/**
 * Represents the possible parse modes to be used as a bitmask.
 */
export declare const enum ParseFlags {
    None = 0,
    /**
     * Whether an output binding is being parsed.
     */
    Action = 1
}

export declare function parseHostBindings(host: {
    [key: string]: string | outputAst.Expression;
}): ParsedHostBindings;

export declare class ParseLocation {
    file: ParseSourceFile;
    offset: number;
    line: number;
    col: number;
    constructor(file: ParseSourceFile, offset: number, line: number, col: number);
    toString(): string;
    moveBy(delta: number): ParseLocation;
    getContext(maxChars: number, maxLines: number): {
        before: string;
        after: string;
    } | null;
}

export declare class Parser {
    private _lexer;
    private errors;
    constructor(_lexer: Lexer);
    parseAction(input: string, location: string, absoluteOffset: number, interpolationConfig?: InterpolationConfig): ASTWithSource;
    parseBinding(input: string, location: string, absoluteOffset: number, interpolationConfig?: InterpolationConfig): ASTWithSource;
    private checkSimpleExpression;
    parseSimpleBinding(input: string, location: string, absoluteOffset: number, interpolationConfig?: InterpolationConfig): ASTWithSource;
    private _reportError;
    private _parseBindingAst;
    /**
     * Parse microsyntax template expression and return a list of bindings or
     * parsing errors in case the given expression is invalid.
     *
     * For example,
     * ```
     *   <div *ngFor="let item of items">
     *         ^      ^ absoluteValueOffset for `templateValue`
     *         absoluteKeyOffset for `templateKey`
     * ```
     * contains three bindings:
     * 1. ngFor -> null
     * 2. item -> NgForOfContext.$implicit
     * 3. ngForOf -> items
     *
     * This is apparent from the de-sugared template:
     * ```
     *   <ng-template ngFor let-item [ngForOf]="items">
     * ```
     *
     * @param templateKey name of directive, without the * prefix. For example: ngIf, ngFor
     * @param templateValue RHS of the microsyntax attribute
     * @param templateUrl template filename if it's external, component filename if it's inline
     * @param absoluteKeyOffset start of the `templateKey`
     * @param absoluteValueOffset start of the `templateValue`
     */
    parseTemplateBindings(templateKey: string, templateValue: string, templateUrl: string, absoluteKeyOffset: number, absoluteValueOffset: number): TemplateBindingParseResult;
    parseInterpolation(input: string, location: string, absoluteOffset: number, interpolatedTokens: InterpolatedAttributeToken[] | InterpolatedTextToken[] | null, interpolationConfig?: InterpolationConfig): ASTWithSource | null;
    /**
     * Similar to `parseInterpolation`, but treats the provided string as a single expression
     * element that would normally appear within the interpolation prefix and suffix (`{{` and `}}`).
     * This is used for parsing the switch expression in ICUs.
     */
    parseInterpolationExpression(expression: string, location: string, absoluteOffset: number): ASTWithSource;
    private createInterpolationAst;
    /**
     * Splits a string of text into "raw" text segments and expressions present in interpolations in
     * the string.
     * Returns `null` if there are no interpolations, otherwise a
     * `SplitInterpolation` with splits that look like
     *   <raw text> <expression> <raw text> ... <raw text> <expression> <raw text>
     */
    splitInterpolation(input: string, location: string, interpolatedTokens: InterpolatedAttributeToken[] | InterpolatedTextToken[] | null, interpolationConfig?: InterpolationConfig): SplitInterpolation;
    wrapLiteralPrimitive(input: string | null, location: string, absoluteOffset: number): ASTWithSource;
    private _stripComments;
    private _commentStart;
    private _checkNoInterpolation;
    /**
     * Finds the index of the end of an interpolation expression
     * while ignoring comments and quoted content.
     */
    private _getInterpolationEndIndex;
    /**
     * Generator used to iterate over the character indexes of a string that are outside of quotes.
     * @param input String to loop through.
     * @param start Index within the string at which to start.
     */
    private _forEachUnquotedChar;
}

declare class Parser_2 {
    getTagDefinition: (tagName: string) => TagDefinition;
    constructor(getTagDefinition: (tagName: string) => TagDefinition);
    parse(source: string, url: string, options?: TokenizeOptions): ParseTreeResult;
}

export declare class ParserError {
    input: string;
    errLocation: string;
    ctxLocation?: any;
    message: string;
    constructor(message: string, input: string, errLocation: string, ctxLocation?: any);
}

declare function parseSelectorToR3Selector(selector: string | null): R3CssSelectorList;

export declare class ParseSourceFile {
    content: string;
    url: string;
    constructor(content: string, url: string);
}

export declare class ParseSourceSpan {
    start: ParseLocation;
    end: ParseLocation;
    fullStart: ParseLocation;
    details: string | null;
    /**
     * Create an object that holds information about spans of tokens/nodes captured during
     * lexing/parsing of text.
     *
     * @param start
     * The location of the start of the span (having skipped leading trivia).
     * Skipping leading trivia makes source-spans more "user friendly", since things like HTML
     * elements will appear to begin at the start of the opening tag, rather than at the start of any
     * leading trivia, which could include newlines.
     *
     * @param end
     * The location of the end of the span.
     *
     * @param fullStart
     * The start of the token without skipping the leading trivia.
     * This is used by tooling that splits tokens further, such as extracting Angular interpolations
     * from text tokens. Such tooling creates new source-spans relative to the original token's
     * source-span. If leading trivia characters have been skipped then the new source-spans may be
     * incorrectly offset.
     *
     * @param details
     * Additional information (such as identifier names) that should be associated with the span.
     */
    constructor(start: ParseLocation, end: ParseLocation, fullStart?: ParseLocation, details?: string | null);
    toString(): string;
}

export declare class ParseSpan {
    start: number;
    end: number;
    constructor(start: number, end: number);
    toAbsolute(absoluteOffset: number): AbsoluteSourceSpan;
}

/**
 * Parse a template into render3 `Node`s and additional metadata, with no other dependencies.
 *
 * @param template text of the template to parse
 * @param templateUrl URL to use for source mapping of the parsed template
 * @param options options to modify how the template is parsed
 */
export declare function parseTemplate(template: string, templateUrl: string, options?: ParseTemplateOptions): ParsedTemplate;

/**
 * Options that can be used to modify how a template is parsed by `parseTemplate()`.
 */
export declare interface ParseTemplateOptions {
    /**
     * Include whitespace nodes in the parsed output.
     */
    preserveWhitespaces?: boolean;
    /**
     * Preserve original line endings instead of normalizing '\r\n' endings to '\n'.
     */
    preserveLineEndings?: boolean;
    /**
     * How to parse interpolation markers.
     */
    interpolationConfig?: InterpolationConfig;
    /**
     * The start and end point of the text to parse within the `source` string.
     * The entire `source` string is parsed if this is not provided.
     * */
    range?: LexerRange;
    /**
     * If this text is stored in a JavaScript string, then we have to deal with escape sequences.
     *
     * **Example 1:**
     *
     * ```
     * "abc\"def\nghi"
     * ```
     *
     * - The `\"` must be converted to `"`.
     * - The `\n` must be converted to a new line character in a token,
     *   but it should not increment the current line for source mapping.
     *
     * **Example 2:**
     *
     * ```
     * "abc\
     *  def"
     * ```
     *
     * The line continuation (`\` followed by a newline) should be removed from a token
     * but the new line should increment the current line for source mapping.
     */
    escapedString?: boolean;
    /**
     * An array of characters that should be considered as leading trivia.
     * Leading trivia are characters that are not important to the developer, and so should not be
     * included in source-map segments.  A common example is whitespace.
     */
    leadingTriviaChars?: string[];
    /**
     * Render `$localize` message ids with additional legacy message ids.
     *
     * This option defaults to `true` but in the future the default will be flipped.
     *
     * For now set this option to false if you have migrated the translation files to use the new
     * `$localize` message id format and you are not using compile time translation merging.
     */
    enableI18nLegacyMessageIdFormat?: boolean;
    /**
     * If this text is stored in an external template (e.g. via `templateUrl`) then we need to decide
     * whether or not to normalize the line-endings (from `\r\n` to `\n`) when processing ICU
     * expressions.
     *
     * If `true` then we will normalize ICU expression line endings.
     * The default is `false`, but this will be switched in a future major release.
     */
    i18nNormalizeLineEndingsInICUs?: boolean;
    /**
     * Whether to always attempt to convert the parsed HTML AST to an R3 AST, despite HTML or i18n
     * Meta parse errors.
     *
     *
     * This option is useful in the context of the language service, where we want to get as much
     * information as possible, despite any errors in the HTML. As an example, a user may be adding
     * a new tag and expecting autocomplete on that tag. In this scenario, the HTML is in an errored
     * state, as there is an incomplete open tag. However, we're still able to convert the HTML AST
     * nodes to R3 AST nodes in order to provide information for the language service.
     *
     * Note that even when `true` the HTML parse and i18n errors are still appended to the errors
     * output, but this is done after converting the HTML AST to R3 AST.
     */
    alwaysAttemptHtmlToR3AstConversion?: boolean;
    /**
     * Include HTML Comment nodes in a top-level comments array on the returned R3 AST.
     *
     * This option is required by tooling that needs to know the location of comment nodes within the
     * AST. A concrete example is @angular-eslint which requires this in order to enable
     * "eslint-disable" comments within HTML templates, which then allows users to turn off specific
     * rules on a case by case basis, instead of for their whole project within a configuration file.
     */
    collectCommentNodes?: boolean;
    /** Whether the @ block syntax is enabled. */
    enableBlockSyntax?: boolean;
}

export declare class ParseTreeResult {
    rootNodes: html.Node[];
    errors: ParseError[];
    constructor(rootNodes: html.Node[], errors: ParseError[]);
}

declare class Placeholder implements Node_3 {
    value: string;
    name: string;
    sourceSpan: ParseSourceSpan;
    constructor(value: string, name: string, sourceSpan: ParseSourceSpan);
    visit(visitor: Visitor_2, context?: any): any;
}

/**
 * A `PlaceholderMapper` converts placeholder names from internal to serialized representation and
 * back.
 *
 * It should be used for serialization format that put constraints on the placeholder names.
 */
declare interface PlaceholderMapper {
    toPublicName(internalName: string): string | null;
    toInternalName(publicName: string): string | null;
}

declare class PlaceholderPiece {
    text: string;
    sourceSpan: ParseSourceSpan;
    associatedMessage?: Message | undefined;
    /**
     * Create a new instance of a `PlaceholderPiece`.
     *
     * @param text the name of this placeholder (e.g. `PH_1`).
     * @param sourceSpan the location of this placeholder in its localized message the source code.
     * @param associatedMessage reference to another message that this placeholder is associated with.
     * The `associatedMessage` is mainly used to provide a relationship to an ICU message that has
     * been extracted out from the message containing the placeholder.
     */
    constructor(text: string, sourceSpan: ParseSourceSpan, associatedMessage?: Message | undefined);
}

export declare class PrefixNot extends AST {
    expression: AST;
    constructor(span: ParseSpan, sourceSpan: AbsoluteSourceSpan, expression: AST);
    visit(visitor: AstVisitor, context?: any): any;
}

export declare function preserveWhitespacesDefault(preserveWhitespacesOption: boolean | null, defaultSetting?: boolean): boolean;

export declare class PropertyRead extends ASTWithName {
    receiver: AST;
    name: string;
    constructor(span: ParseSpan, sourceSpan: AbsoluteSourceSpan, nameSpan: AbsoluteSourceSpan, receiver: AST, name: string);
    visit(visitor: AstVisitor, context?: any): any;
}

export declare class PropertyWrite extends ASTWithName {
    receiver: AST;
    name: string;
    value: AST;
    constructor(span: ParseSpan, sourceSpan: AbsoluteSourceSpan, nameSpan: AbsoluteSourceSpan, receiver: AST, name: string, value: AST);
    visit(visitor: AstVisitor, context?: any): any;
}

export declare function publishFacade(global: any): void;

/**
 * Metadata container for a `Target` that allows queries for specific bits of metadata.
 *
 * See `BoundTarget` for documentation on the individual methods.
 */
export declare class R3BoundTarget<DirectiveT extends DirectiveMeta> implements BoundTarget<DirectiveT> {
    readonly target: Target;
    private directives;
    private eagerDirectives;
    private bindings;
    private references;
    private exprTargets;
    private symbols;
    private nestingLevel;
    private scopedNodeEntities;
    private usedPipes;
    private eagerPipes;
    private deferBlocks;
    constructor(target: Target, directives: Map<TmplAstElement | TmplAstTemplate, DirectiveT[]>, eagerDirectives: DirectiveT[], bindings: Map<TmplAstBoundAttribute | TmplAstBoundEvent | TmplAstTextAttribute, DirectiveT | TmplAstElement | TmplAstTemplate>, references: Map<TmplAstBoundAttribute | TmplAstBoundEvent | TmplAstReference | TmplAstTextAttribute, {
        directive: DirectiveT;
        node: TmplAstElement | TmplAstTemplate;
    } | TmplAstElement | TmplAstTemplate>, exprTargets: Map<AST, TmplAstReference | TmplAstVariable>, symbols: Map<TmplAstReference | TmplAstVariable, TmplAstTemplate>, nestingLevel: Map<ScopedNode, number>, scopedNodeEntities: Map<ScopedNode | null, ReadonlySet<TmplAstReference | TmplAstVariable>>, usedPipes: Set<string>, eagerPipes: Set<string>, deferBlocks: Map<TmplAstDeferredBlock, Scope>);
    getEntitiesInScope(node: ScopedNode | null): ReadonlySet<TmplAstReference | TmplAstVariable>;
    getDirectivesOfNode(node: TmplAstElement | TmplAstTemplate): DirectiveT[] | null;
    getReferenceTarget(ref: TmplAstReference): ReferenceTarget<DirectiveT> | null;
    getConsumerOfBinding(binding: TmplAstBoundAttribute | TmplAstBoundEvent | TmplAstTextAttribute): DirectiveT | TmplAstElement | TmplAstTemplate | null;
    getExpressionTarget(expr: AST): TmplAstReference | TmplAstVariable | null;
    getDefinitionNodeOfSymbol(symbol: TmplAstReference | TmplAstVariable): ScopedNode | null;
    getNestingLevel(node: ScopedNode): number;
    getUsedDirectives(): DirectiveT[];
    getEagerlyUsedDirectives(): DirectiveT[];
    getUsedPipes(): string[];
    getEagerlyUsedPipes(): string[];
    getDeferBlocks(): TmplAstDeferredBlock[];
    getDeferredTriggerTarget(block: TmplAstDeferredBlock, trigger: TmplAstDeferredTrigger): TmplAstElement | null;
    isDeferred(element: TmplAstElement): boolean;
    /**
     * Finds an entity with a specific name in a scope.
     * @param rootNode Root node of the scope.
     * @param name Name of the entity.
     */
    private findEntityInScope;
    /** Coerces a `ReferenceTarget` to an `Element`, if possible. */
    private referenceTargetToElement;
}

/**
 * Info needed for runtime errors related to a class, such as the location in which the class is
 * defined.
 */
export declare interface R3ClassDebugInfo {
    /** The class identifier */
    type: outputAst.Expression;
    /**
     * A string literal containing the original class name as appears in its definition.
     */
    className: outputAst.Expression;
    /**
     * A string literal containing the relative path of the file in which the class is defined.
     *
     * The path is relative to the project root. The compiler does the best effort to find the project
     * root (e.g., using the rootDir of tsconfig), but if it fails this field is set to null,
     * indicating that the file path was failed to be computed. In this case, the downstream consumers
     * of the debug info will usually ignore the `lineNumber` field as well and just show the
     * `className`. For security reasons we never show the absolute file path and prefer to just
     * return null here.
     */
    filePath: outputAst.Expression | null;
    /**
     * A number literal number containing the line number in which this class is defined.
     */
    lineNumber: outputAst.Expression;
    /**
     * Whether to check if this component is being rendered without its NgModule being loaded into the
     * browser. Such checks is carried out only in dev mode.
     */
    forbidOrphanRendering: boolean;
}

/**
 * Metadata of a class which captures the original Angular decorators of a class. The original
 * decorators are preserved in the generated code to allow TestBed APIs to recompile the class
 * using the original decorator with a set of overrides applied.
 */
export declare interface R3ClassMetadata {
    /**
     * The class type for which the metadata is captured.
     */
    type: outputAst.Expression;
    /**
     * An expression representing the Angular decorators that were applied on the class.
     */
    decorators: outputAst.Expression;
    /**
     * An expression representing the Angular decorators applied to constructor parameters, or `null`
     * if there is no constructor.
     */
    ctorParameters: outputAst.Expression | null;
    /**
     * An expression representing the Angular decorators that were applied on the properties of the
     * class, or `null` if no properties have decorators.
     */
    propDecorators: outputAst.Expression | null;
}

/**
 * Result of compilation of a render3 code unit, e.g. component, directive, pipe, etc.
 */
export declare interface R3CompiledExpression {
    expression: outputAst.Expression;
    type: outputAst.Type;
    statements: outputAst.Statement[];
}

/**
 * Information needed to compile a component for the render3 runtime.
 */
export declare interface R3ComponentMetadata<DeclarationT extends R3TemplateDependency> extends R3DirectiveMetadata {
    /**
     * Information about the component's template.
     */
    template: {
        /**
         * Parsed nodes of the template.
         */
        nodes: t.Node[];
        /**
         * Any ng-content selectors extracted from the template. Contains `*` when an ng-content
         * element without selector is present.
         */
        ngContentSelectors: string[];
        /**
         * Whether the template preserves whitespaces from the user's code.
         */
        preserveWhitespaces?: boolean;
    };
    declarations: DeclarationT[];
    /**
     * Map of all types that can be defer loaded (ts.ClassDeclaration) ->
     * corresponding import declaration (ts.ImportDeclaration) within
     * the current source file.
     */
    deferrableDeclToImportDecl: Map<outputAst.Expression, outputAst.Expression>;
    /**
     * Map of `@defer` blocks -> their corresponding metadata.
     */
    deferBlocks: Map<t.DeferredBlock, R3DeferBlockMetadata>;
    /**
     * Defines how dynamic imports for deferred dependencies should be grouped:
     *  - either in a function on per-component basis (in case of local compilation)
     *  - or in a function on per-block basis (in full compilation mode)
     */
    deferBlockDepsEmitMode: DeferBlockDepsEmitMode;
    /**
     * Map of deferrable symbol names -> corresponding import paths.
     */
    deferrableTypes: Map<string, {
        importPath: string;
        isDefaultImport: boolean;
    }>;
    /**
     * Specifies how the 'directives' and/or `pipes` array, if generated, need to be emitted.
     */
    declarationListEmitMode: DeclarationListEmitMode;
    /**
     * A collection of styling data that will be applied and scoped to the component.
     */
    styles: string[];
    /**
     * An encapsulation policy for the component's styling.
     * Possible values:
     * - `ViewEncapsulation.Emulated`: Apply modified component styles in order to emulate
     *                                 a native Shadow DOM CSS encapsulation behavior.
     * - `ViewEncapsulation.None`: Apply component styles globally without any sort of encapsulation.
     * - `ViewEncapsulation.ShadowDom`: Use the browser's native Shadow DOM API to encapsulate styles.
     */
    encapsulation: ViewEncapsulation;
    /**
     * A collection of animation triggers that will be used in the component template.
     */
    animations: outputAst.Expression | null;
    /**
     * The list of view providers defined in the component.
     */
    viewProviders: outputAst.Expression | null;
    /**
     * Path to the .ts file in which this template's generated code will be included, relative to
     * the compilation root. This will be used to generate identifiers that need to be globally
     * unique in certain contexts (such as g3).
     */
    relativeContextFilePath: string;
    /**
     * Whether translation variable name should contain external message id
     * (used by Closure Compiler's output of `goog.getMsg` for transition period).
     */
    i18nUseExternalIds: boolean;
    /**
     * Overrides the default interpolation start and end delimiters ({{ and }}).
     */
    interpolation: InterpolationConfig;
    /**
     * Strategy used for detecting changes in the component.
     *
     * In global compilation mode the value is ChangeDetectionStrategy if available as it is
     * statically resolved during analysis phase. Whereas in local compilation mode the value is the
     * expression as appears in the decorator.
     */
    changeDetection: ChangeDetectionStrategy | outputAst.Expression | null;
    /**
     * The imports expression as appears on the component decorate for standalone component. This
     * field is currently needed only for local compilation, and so in other compilation modes it may
     * not be set. If component has empty array imports then this field is not set.
     */
    rawImports?: outputAst.Expression;
    useTemplatePipeline: boolean;
}

/**
 * Metadata required by the factory generator to generate a `factory` function for a type.
 */
declare interface R3ConstructorFactoryMetadata {
    /**
     * String name of the type being generated (used to name the factory function).
     */
    name: string;
    /**
     * An expression representing the interface type being constructed.
     */
    type: R3Reference;
    /** Number of arguments for the `type`. */
    typeArgumentCount: number;
    /**
     * Regardless of whether `fnOrClass` is a constructor function or a user-defined factory, it
     * may have 0 or more parameters, which will be injected according to the `R3DependencyMetadata`
     * for those parameters. If this is `null`, then the type's constructor is nonexistent and will
     * be inherited from `fnOrClass` which is interpreted as the current type. If this is `'invalid'`,
     * then one or more of the parameters wasn't resolvable and any attempt to use these deps will
     * result in a runtime error.
     */
    deps: R3DependencyMetadata[] | 'invalid' | null;
    /**
     * Type of the target being created by the factory.
     */
    target: FactoryTarget;
}

declare type R3CssSelector = (string | SelectorFlags)[];

declare type R3CssSelectorList = R3CssSelector[];

/**
 * Describes the shape of the object that the `ɵɵngDeclareClassMetadata()` function accepts.
 *
 * This interface serves primarily as documentation, as conformance to this interface is not
 * enforced during linking.
 */
export declare interface R3DeclareClassMetadata extends R3PartialDeclaration {
    /**
     * The Angular decorators of the class.
     */
    decorators: outputAst.Expression;
    /**
     * Optionally specifies the constructor parameters, their types and the Angular decorators of each
     * parameter. This property is omitted if the class does not have a constructor.
     */
    ctorParameters?: outputAst.Expression;
    /**
     * Optionally specifies the Angular decorators applied to the class properties. This property is
     * omitted if no properties have any decorators.
     */
    propDecorators?: outputAst.Expression;
}

/**
 * Describes the shape of the object that the `ɵɵngDeclareComponent()` function accepts.
 */
export declare interface R3DeclareComponentMetadata extends R3DeclareDirectiveMetadata {
    /**
     * The component's unparsed template string as opaque expression. The template is represented
     * using either a string literal or template literal without substitutions, but its value is
     * not read directly. Instead, the template parser is given the full source file's text and
     * the range of this expression to parse directly from source.
     */
    template: outputAst.Expression;
    /**
     * Whether the template was inline (using `template`) or external (using `templateUrl`).
     * Defaults to false.
     */
    isInline?: boolean;
    /**
     * CSS from inline styles and included styleUrls.
     */
    styles?: string[];
    /**
     * List of components which matched in the template, including sufficient
     * metadata for each directive to attribute bindings and references within
     * the template to each directive specifically, if the runtime instructions
     * support this.
     */
    components?: R3DeclareDirectiveDependencyMetadata[];
    /**
     * List of directives which matched in the template, including sufficient
     * metadata for each directive to attribute bindings and references within
     * the template to each directive specifically, if the runtime instructions
     * support this.
     */
    directives?: R3DeclareDirectiveDependencyMetadata[];
    /**
     * List of dependencies which matched in the template, including sufficient
     * metadata for each directive/pipe to attribute bindings and references within
     * the template to each directive specifically, if the runtime instructions
     * support this.
     */
    dependencies?: R3DeclareTemplateDependencyMetadata[];
    /**
     * A map of pipe names to an expression referencing the pipe type (possibly a forward reference
     * wrapped in a `forwardRef` invocation) which are used in the template.
     */
    pipes?: {
        [pipeName: string]: outputAst.Expression | (() => outputAst.Expression);
    };
    /**
     * The list of view providers defined in the component.
     */
    viewProviders?: outputAst.Expression;
    /**
     * A collection of animation triggers that will be used in the component template.
     */
    animations?: outputAst.Expression;
    /**
     * Strategy used for detecting changes in the component.
     * Defaults to `ChangeDetectionStrategy.Default`.
     */
    changeDetection?: ChangeDetectionStrategy;
    /**
     * An encapsulation policy for the component's styling.
     * Defaults to `ViewEncapsulation.Emulated`.
     */
    encapsulation?: ViewEncapsulation;
    /**
     * Overrides the default interpolation start and end delimiters. Defaults to {{ and }}.
     */
    interpolation?: [string, string];
    /**
     * Whether whitespace in the template should be preserved. Defaults to false.
     */
    preserveWhitespaces?: boolean;
}

/**
 * Metadata indicating how a dependency should be injected into a factory.
 */
export declare interface R3DeclareDependencyMetadata {
    /**
     * An expression representing the token or value to be injected, or `null` if the dependency is
     * not valid.
     *
     * If this dependency is due to the `@Attribute()` decorator, then this is an expression
     * evaluating to the name of the attribute.
     */
    token: outputAst.Expression | null;
    /**
     * Whether the dependency is injecting an attribute value.
     * Default: false.
     */
    attribute?: boolean;
    /**
     * Whether the dependency has an @Host qualifier.
     * Default: false,
     */
    host?: boolean;
    /**
     * Whether the dependency has an @Optional qualifier.
     * Default: false,
     */
    optional?: boolean;
    /**
     * Whether the dependency has an @Self qualifier.
     * Default: false,
     */
    self?: boolean;
    /**
     * Whether the dependency has an @SkipSelf qualifier.
     * Default: false,
     */
    skipSelf?: boolean;
}

export declare interface R3DeclareDirectiveDependencyMetadata {
    kind: 'directive' | 'component';
    /**
     * Selector of the directive.
     */
    selector: string;
    /**
     * Reference to the directive class (possibly a forward reference wrapped in a `forwardRef`
     * invocation).
     */
    type: outputAst.Expression | (() => outputAst.Expression);
    /**
     * Property names of the directive's inputs.
     */
    inputs?: string[];
    /**
     * Event names of the directive's outputs.
     */
    outputs?: string[];
    /**
     * Names by which this directive exports itself for references.
     */
    exportAs?: string[];
}

/**
 * Describes the shape of the object that the `ɵɵngDeclareDirective()` function accepts.
 */
export declare interface R3DeclareDirectiveMetadata extends R3PartialDeclaration {
    /**
     * Unparsed selector of the directive.
     */
    selector?: string;
    /**
     * A mapping of inputs from class property names to binding property names, or to a tuple of
     * binding property name and class property name if the names are different.
     */
    inputs?: {
        [fieldName: string]: {
            classPropertyName: string;
            publicName: string;
            isSignal: boolean;
            isRequired: boolean;
            transformFunction: outputAst.Expression | null;
        } | LegacyInputPartialMapping;
    };
    /**
     * A mapping of outputs from class property names to binding property names.
     */
    outputs?: {
        [classPropertyName: string]: string;
    };
    /**
     * Information about host bindings present on the component.
     */
    host?: {
        /**
         * A mapping of attribute names to their value expression.
         */
        attributes?: {
            [key: string]: outputAst.Expression;
        };
        /**
         * A mapping of event names to their unparsed event handler expression.
         */
        listeners: {
            [key: string]: string;
        };
        /**
         * A mapping of bound properties to their unparsed binding expression.
         */
        properties?: {
            [key: string]: string;
        };
        /**
         * The value of the class attribute, if present. This is stored outside of `attributes` as its
         * string value must be known statically.
         */
        classAttribute?: string;
        /**
         * The value of the style attribute, if present. This is stored outside of `attributes` as its
         * string value must be known statically.
         */
        styleAttribute?: string;
    };
    /**
     * Information about the content queries made by the directive.
     */
    queries?: R3DeclareQueryMetadata[];
    /**
     * Information about the view queries made by the directive.
     */
    viewQueries?: R3DeclareQueryMetadata[];
    /**
     * The list of providers provided by the directive.
     */
    providers?: outputAst.Expression;
    /**
     * The names by which the directive is exported.
     */
    exportAs?: string[];
    /**
     * Whether the directive has an inheritance clause. Defaults to false.
     */
    usesInheritance?: boolean;
    /**
     * Whether the directive implements the `ngOnChanges` hook. Defaults to false.
     */
    usesOnChanges?: boolean;
    /**
     * Whether the directive is standalone. Defaults to false.
     */
    isStandalone?: boolean;
    /**
     * Whether the directive is a signal-based directive. Defaults to false.
     */
    isSignal?: boolean;
    /**
     * Additional directives applied to the directive host.
     */
    hostDirectives?: R3DeclareHostDirectiveMetadata[];
}

/**
 * Describes the shape of the object that the `ɵɵngDeclareFactory()` function accepts.
 *
 * This interface serves primarily as documentation, as conformance to this interface is not
 * enforced during linking.
 */
export declare interface R3DeclareFactoryMetadata extends R3PartialDeclaration {
    /**
     * A collection of dependencies that this factory relies upon.
     *
     * If this is `null`, then the type's constructor is nonexistent and will be inherited from an
     * ancestor of the type.
     *
     * If this is `'invalid'`, then one or more of the parameters wasn't resolvable and any attempt to
     * use these deps will result in a runtime error.
     */
    deps: R3DeclareDependencyMetadata[] | 'invalid' | null;
    /**
     * Type of the target being created by the factory.
     */
    target: FactoryTarget_2;
}

/**
 * Describes the shape of the object literal that can be
 * passed in as a part of the `hostDirectives` array.
 */
export declare interface R3DeclareHostDirectiveMetadata {
    directive: outputAst.Expression;
    inputs?: string[];
    outputs?: string[];
}

/**
 * Describes the shape of the object that the `ɵɵngDeclareInjectable()` function accepts.
 *
 * This interface serves primarily as documentation, as conformance to this interface is not
 * enforced during linking.
 */
export declare interface R3DeclareInjectableMetadata extends R3PartialDeclaration {
    /**
     * If provided, specifies that the declared injectable belongs to a particular injector:
     * - `InjectorType` such as `NgModule`,
     * - `'root'` the root injector
     * - `'any'` all injectors.
     * If not provided, then it does not belong to any injector. Must be explicitly listed in the
     * providers of an injector.
     */
    providedIn?: outputAst.Expression;
    /**
     * If provided, an expression that evaluates to a class to use when creating an instance of this
     * injectable.
     */
    useClass?: outputAst.Expression;
    /**
     * If provided, an expression that evaluates to a function to use when creating an instance of
     * this injectable.
     */
    useFactory?: outputAst.Expression;
    /**
     * If provided, an expression that evaluates to a token of another injectable that this injectable
     * aliases.
     */
    useExisting?: outputAst.Expression;
    /**
     * If provided, an expression that evaluates to the value of the instance of this injectable.
     */
    useValue?: outputAst.Expression;
    /**
     * An array of dependencies to support instantiating this injectable via `useClass` or
     * `useFactory`.
     */
    deps?: R3DeclareDependencyMetadata[];
}

/**
 * Describes the shape of the objects that the `ɵɵngDeclareInjector()` accepts.
 */
export declare interface R3DeclareInjectorMetadata extends R3PartialDeclaration {
    /**
     * The list of providers provided by the injector.
     */
    providers?: outputAst.Expression;
    /**
     * The list of imports into the injector.
     */
    imports?: outputAst.Expression[];
}

export declare interface R3DeclareNgModuleDependencyMetadata {
    kind: 'ngmodule';
    type: outputAst.Expression | (() => outputAst.Expression);
}

/**
 * Describes the shape of the objects that the `ɵɵngDeclareNgModule()` accepts.
 */
export declare interface R3DeclareNgModuleMetadata extends R3PartialDeclaration {
    /**
     * An array of expressions representing the bootstrap components specified by the module.
     */
    bootstrap?: outputAst.Expression[];
    /**
     * An array of expressions representing the directives and pipes declared by the module.
     */
    declarations?: outputAst.Expression[];
    /**
     * An array of expressions representing the imports of the module.
     */
    imports?: outputAst.Expression[];
    /**
     * An array of expressions representing the exports of the module.
     */
    exports?: outputAst.Expression[];
    /**
     * The set of schemas that declare elements to be allowed in the NgModule.
     */
    schemas?: outputAst.Expression[];
    /** Unique ID or expression representing the unique ID of an NgModule. */
    id?: outputAst.Expression;
}

export declare interface R3DeclarePipeDependencyMetadata {
    kind: 'pipe';
    name: string;
    /**
     * Reference to the pipe class (possibly a forward reference wrapped in a `forwardRef`
     * invocation).
     */
    type: outputAst.Expression | (() => outputAst.Expression);
}

/**
 * Describes the shape of the object that the `ɵɵngDeclarePipe()` function accepts.
 *
 * This interface serves primarily as documentation, as conformance to this interface is not
 * enforced during linking.
 */
export declare interface R3DeclarePipeMetadata extends R3PartialDeclaration {
    /**
     * The name to use in templates to refer to this pipe.
     */
    name: string;
    /**
     * Whether this pipe is "pure".
     *
     * A pure pipe's `transform()` method is only invoked when its input arguments change.
     *
     * Default: true.
     */
    pure?: boolean;
    /**
     * Whether the pipe is standalone.
     *
     * Default: false.
     */
    isStandalone?: boolean;
}

export declare interface R3DeclareQueryMetadata {
    /**
     * Name of the property on the class to update with query results.
     */
    propertyName: string;
    /**
     * Whether to read only the first matching result, or an array of results. Defaults to false.
     */
    first?: boolean;
    /**
     * Either an expression representing a type (possibly wrapped in a `forwardRef()`) or
     * `InjectionToken` for the query predicate, or a set of string selectors.
     */
    predicate: outputAst.Expression | string[];
    /**
     * Whether to include only direct children or all descendants. Defaults to false.
     */
    descendants?: boolean;
    /**
     * True to only fire changes if there are underlying changes to the query.
     */
    emitDistinctChangesOnly?: boolean;
    /**
     * An expression representing a type to read from each matched node, or null if the default value
     * for a given node is to be returned.
     */
    read?: outputAst.Expression;
    /**
     * Whether or not this query should collect only static results. Defaults to false.
     *
     * If static is true, the query's results will be set on the component after nodes are created,
     * but before change detection runs. This means that any results that relied upon change detection
     * to run (e.g. results inside *ngIf or *ngFor views) will not be collected. Query results are
     * available in the ngOnInit hook.
     *
     * If static is false, the query's results will be set on the component after change detection
     * runs. This means that the query results can contain nodes inside *ngIf or *ngFor views, but
     * the results will not be available in the ngOnInit hook (only in the ngAfterContentInit for
     * content hooks and ngAfterViewInit for view hooks).
     */
    static?: boolean;
    /** Whether the query is signal-based. */
    isSignal: boolean;
}

export declare type R3DeclareTemplateDependencyMetadata = R3DeclareDirectiveDependencyMetadata | R3DeclarePipeDependencyMetadata | R3DeclareNgModuleDependencyMetadata;

/**
 * Information necessary to compile a `defer` block.
 */
export declare interface R3DeferBlockMetadata {
    /** Dependencies used within the block. */
    deps: R3DeferBlockTemplateDependency[];
    /** Mapping between triggers and the DOM nodes they refer to. */
    triggerElements: Map<t.DeferredTrigger, t.Element | null>;
}

/**
 * Describes a dependency used within a `@defer` block.
 */
export declare interface R3DeferBlockTemplateDependency {
    /**
     * Reference to a dependency.
     */
    type: outputAst.WrappedNodeExpr<unknown>;
    /**
     * Dependency class name.
     */
    symbolName: string;
    /**
     * Whether this dependency can be defer-loaded.
     */
    isDeferrable: boolean;
    /**
     * Import path where this dependency is located.
     */
    importPath: string | null;
    /**
     * Whether the symbol is the default export.
     */
    isDefaultImport: boolean;
}

declare interface R3DelegatedFnOrClassMetadata extends R3ConstructorFactoryMetadata {
    delegate: outputAst.Expression;
    delegateType: R3FactoryDelegateType;
    delegateDeps: R3DependencyMetadata[];
}

export declare interface R3DependencyMetadata {
    /**
     * An expression representing the token or value to be injected.
     * Or `null` if the dependency could not be resolved - making it invalid.
     */
    token: outputAst.Expression | null;
    /**
     * If an @Attribute decorator is present, this is the literal type of the attribute name, or
     * the unknown type if no literal type is available (e.g. the attribute name is an expression).
     * Otherwise it is null;
     */
    attributeNameType: outputAst.Expression | null;
    /**
     * Whether the dependency has an @Host qualifier.
     */
    host: boolean;
    /**
     * Whether the dependency has an @Optional qualifier.
     */
    optional: boolean;
    /**
     * Whether the dependency has an @Self qualifier.
     */
    self: boolean;
    /**
     * Whether the dependency has an @SkipSelf qualifier.
     */
    skipSelf: boolean;
}

/**
 * Information about a directive that is used in a component template. Only the stable, public
 * facing information of the directive is stored here.
 */
export declare interface R3DirectiveDependencyMetadata extends R3TemplateDependency {
    kind: R3TemplateDependencyKind.Directive;
    /**
     * The selector of the directive.
     */
    selector: string;
    /**
     * The binding property names of the inputs of the directive.
     */
    inputs: string[];
    /**
     * The binding property names of the outputs of the directive.
     */
    outputs: string[];
    /**
     * Name under which the directive is exported, if any (exportAs in Angular). Null otherwise.
     */
    exportAs: string[] | null;
    /**
     * If true then this directive is actually a component; otherwise it is not.
     */
    isComponent: boolean;
}

/**
 * Information needed to compile a directive for the render3 runtime.
 */
export declare interface R3DirectiveMetadata {
    /**
     * Name of the directive type.
     */
    name: string;
    /**
     * An expression representing a reference to the directive itself.
     */
    type: R3Reference;
    /**
     * Number of generic type parameters of the type itself.
     */
    typeArgumentCount: number;
    /**
     * A source span for the directive type.
     */
    typeSourceSpan: ParseSourceSpan;
    /**
     * Dependencies of the directive's constructor.
     */
    deps: R3DependencyMetadata[] | 'invalid' | null;
    /**
     * Unparsed selector of the directive, or `null` if there was no selector.
     */
    selector: string | null;
    /**
     * Information about the content queries made by the directive.
     */
    queries: R3QueryMetadata[];
    /**
     * Information about the view queries made by the directive.
     */
    viewQueries: R3QueryMetadata[];
    /**
     * Mappings indicating how the directive interacts with its host element (host bindings,
     * listeners, etc).
     */
    host: R3HostMetadata;
    /**
     * Information about usage of specific lifecycle events which require special treatment in the
     * code generator.
     */
    lifecycle: {
        /**
         * Whether the directive uses NgOnChanges.
         */
        usesOnChanges: boolean;
    };
    /**
     * A mapping of inputs from class property names to binding property names, or to a tuple of
     * binding property name and class property name if the names are different.
     */
    inputs: {
        [field: string]: R3InputMetadata;
    };
    /**
     * A mapping of outputs from class property names to binding property names, or to a tuple of
     * binding property name and class property name if the names are different.
     */
    outputs: {
        [field: string]: string;
    };
    /**
     * Whether or not the component or directive inherits from another class
     */
    usesInheritance: boolean;
    /**
     * Whether or not the component or directive inherits its entire decorator from its base class.
     */
    fullInheritance: boolean;
    /**
     * Reference name under which to export the directive's type in a template,
     * if any.
     */
    exportAs: string[] | null;
    /**
     * The list of providers defined in the directive.
     */
    providers: outputAst.Expression | null;
    /**
     * Whether or not the component or directive is standalone.
     */
    isStandalone: boolean;
    /**
     * Whether or not the component or directive is signal-based.
     */
    isSignal: boolean;
    /**
     * Additional directives applied to the directive host.
     */
    hostDirectives: R3HostDirectiveMetadata[] | null;
}

declare interface R3ExpressionFactoryMetadata extends R3ConstructorFactoryMetadata {
    expression: outputAst.Expression;
}

declare enum R3FactoryDelegateType {
    Class = 0,
    Function = 1
}

export declare type R3FactoryMetadata = R3ConstructorFactoryMetadata | R3DelegatedFnOrClassMetadata | R3ExpressionFactoryMetadata;

/**
 * Information needed to compile a host directive for the render3 runtime.
 */
export declare interface R3HostDirectiveMetadata {
    /** An expression representing the host directive class itself. */
    directive: R3Reference;
    /** Whether the expression referring to the host directive is a forward reference. */
    isForwardReference: boolean;
    /** Inputs from the host directive that will be exposed on the host. */
    inputs: {
        [publicName: string]: string;
    } | null;
    /** Outputs from the host directive that will be exposed on the host. */
    outputs: {
        [publicName: string]: string;
    } | null;
}

/**
 * Mappings indicating how the class interacts with its
 * host element (host bindings, listeners, etc).
 */
export declare interface R3HostMetadata {
    /**
     * A mapping of attribute binding keys to `o.Expression`s.
     */
    attributes: {
        [key: string]: outputAst.Expression;
    };
    /**
     * A mapping of event binding keys to unparsed expressions.
     */
    listeners: {
        [key: string]: string;
    };
    /**
     * A mapping of property binding keys to unparsed expressions.
     */
    properties: {
        [key: string]: string;
    };
    specialAttributes: {
        styleAttr?: string;
        classAttr?: string;
    };
    useTemplatePipeline: boolean;
}

export declare class R3Identifiers {
    static NEW_METHOD: string;
    static TRANSFORM_METHOD: string;
    static PATCH_DEPS: string;
    static core: outputAst.ExternalReference;
    static namespaceHTML: outputAst.ExternalReference;
    static namespaceMathML: outputAst.ExternalReference;
    static namespaceSVG: outputAst.ExternalReference;
    static element: outputAst.ExternalReference;
    static elementStart: outputAst.ExternalReference;
    static elementEnd: outputAst.ExternalReference;
    static advance: outputAst.ExternalReference;
    static syntheticHostProperty: outputAst.ExternalReference;
    static syntheticHostListener: outputAst.ExternalReference;
    static attribute: outputAst.ExternalReference;
    static attributeInterpolate1: outputAst.ExternalReference;
    static attributeInterpolate2: outputAst.ExternalReference;
    static attributeInterpolate3: outputAst.ExternalReference;
    static attributeInterpolate4: outputAst.ExternalReference;
    static attributeInterpolate5: outputAst.ExternalReference;
    static attributeInterpolate6: outputAst.ExternalReference;
    static attributeInterpolate7: outputAst.ExternalReference;
    static attributeInterpolate8: outputAst.ExternalReference;
    static attributeInterpolateV: outputAst.ExternalReference;
    static classProp: outputAst.ExternalReference;
    static elementContainerStart: outputAst.ExternalReference;
    static elementContainerEnd: outputAst.ExternalReference;
    static elementContainer: outputAst.ExternalReference;
    static styleMap: outputAst.ExternalReference;
    static styleMapInterpolate1: outputAst.ExternalReference;
    static styleMapInterpolate2: outputAst.ExternalReference;
    static styleMapInterpolate3: outputAst.ExternalReference;
    static styleMapInterpolate4: outputAst.ExternalReference;
    static styleMapInterpolate5: outputAst.ExternalReference;
    static styleMapInterpolate6: outputAst.ExternalReference;
    static styleMapInterpolate7: outputAst.ExternalReference;
    static styleMapInterpolate8: outputAst.ExternalReference;
    static styleMapInterpolateV: outputAst.ExternalReference;
    static classMap: outputAst.ExternalReference;
    static classMapInterpolate1: outputAst.ExternalReference;
    static classMapInterpolate2: outputAst.ExternalReference;
    static classMapInterpolate3: outputAst.ExternalReference;
    static classMapInterpolate4: outputAst.ExternalReference;
    static classMapInterpolate5: outputAst.ExternalReference;
    static classMapInterpolate6: outputAst.ExternalReference;
    static classMapInterpolate7: outputAst.ExternalReference;
    static classMapInterpolate8: outputAst.ExternalReference;
    static classMapInterpolateV: outputAst.ExternalReference;
    static styleProp: outputAst.ExternalReference;
    static stylePropInterpolate1: outputAst.ExternalReference;
    static stylePropInterpolate2: outputAst.ExternalReference;
    static stylePropInterpolate3: outputAst.ExternalReference;
    static stylePropInterpolate4: outputAst.ExternalReference;
    static stylePropInterpolate5: outputAst.ExternalReference;
    static stylePropInterpolate6: outputAst.ExternalReference;
    static stylePropInterpolate7: outputAst.ExternalReference;
    static stylePropInterpolate8: outputAst.ExternalReference;
    static stylePropInterpolateV: outputAst.ExternalReference;
    static nextContext: outputAst.ExternalReference;
    static resetView: outputAst.ExternalReference;
    static templateCreate: outputAst.ExternalReference;
    static defer: outputAst.ExternalReference;
    static deferWhen: outputAst.ExternalReference;
    static deferOnIdle: outputAst.ExternalReference;
    static deferOnImmediate: outputAst.ExternalReference;
    static deferOnTimer: outputAst.ExternalReference;
    static deferOnHover: outputAst.ExternalReference;
    static deferOnInteraction: outputAst.ExternalReference;
    static deferOnViewport: outputAst.ExternalReference;
    static deferPrefetchWhen: outputAst.ExternalReference;
    static deferPrefetchOnIdle: outputAst.ExternalReference;
    static deferPrefetchOnImmediate: outputAst.ExternalReference;
    static deferPrefetchOnTimer: outputAst.ExternalReference;
    static deferPrefetchOnHover: outputAst.ExternalReference;
    static deferPrefetchOnInteraction: outputAst.ExternalReference;
    static deferPrefetchOnViewport: outputAst.ExternalReference;
    static deferEnableTimerScheduling: outputAst.ExternalReference;
    static conditional: outputAst.ExternalReference;
    static repeater: outputAst.ExternalReference;
    static repeaterCreate: outputAst.ExternalReference;
    static repeaterTrackByIndex: outputAst.ExternalReference;
    static repeaterTrackByIdentity: outputAst.ExternalReference;
    static componentInstance: outputAst.ExternalReference;
    static text: outputAst.ExternalReference;
    static enableBindings: outputAst.ExternalReference;
    static disableBindings: outputAst.ExternalReference;
    static getCurrentView: outputAst.ExternalReference;
    static textInterpolate: outputAst.ExternalReference;
    static textInterpolate1: outputAst.ExternalReference;
    static textInterpolate2: outputAst.ExternalReference;
    static textInterpolate3: outputAst.ExternalReference;
    static textInterpolate4: outputAst.ExternalReference;
    static textInterpolate5: outputAst.ExternalReference;
    static textInterpolate6: outputAst.ExternalReference;
    static textInterpolate7: outputAst.ExternalReference;
    static textInterpolate8: outputAst.ExternalReference;
    static textInterpolateV: outputAst.ExternalReference;
    static restoreView: outputAst.ExternalReference;
    static pureFunction0: outputAst.ExternalReference;
    static pureFunction1: outputAst.ExternalReference;
    static pureFunction2: outputAst.ExternalReference;
    static pureFunction3: outputAst.ExternalReference;
    static pureFunction4: outputAst.ExternalReference;
    static pureFunction5: outputAst.ExternalReference;
    static pureFunction6: outputAst.ExternalReference;
    static pureFunction7: outputAst.ExternalReference;
    static pureFunction8: outputAst.ExternalReference;
    static pureFunctionV: outputAst.ExternalReference;
    static pipeBind1: outputAst.ExternalReference;
    static pipeBind2: outputAst.ExternalReference;
    static pipeBind3: outputAst.ExternalReference;
    static pipeBind4: outputAst.ExternalReference;
    static pipeBindV: outputAst.ExternalReference;
    static hostProperty: outputAst.ExternalReference;
    static property: outputAst.ExternalReference;
    static propertyInterpolate: outputAst.ExternalReference;
    static propertyInterpolate1: outputAst.ExternalReference;
    static propertyInterpolate2: outputAst.ExternalReference;
    static propertyInterpolate3: outputAst.ExternalReference;
    static propertyInterpolate4: outputAst.ExternalReference;
    static propertyInterpolate5: outputAst.ExternalReference;
    static propertyInterpolate6: outputAst.ExternalReference;
    static propertyInterpolate7: outputAst.ExternalReference;
    static propertyInterpolate8: outputAst.ExternalReference;
    static propertyInterpolateV: outputAst.ExternalReference;
    static i18n: outputAst.ExternalReference;
    static i18nAttributes: outputAst.ExternalReference;
    static i18nExp: outputAst.ExternalReference;
    static i18nStart: outputAst.ExternalReference;
    static i18nEnd: outputAst.ExternalReference;
    static i18nApply: outputAst.ExternalReference;
    static i18nPostprocess: outputAst.ExternalReference;
    static pipe: outputAst.ExternalReference;
    static projection: outputAst.ExternalReference;
    static projectionDef: outputAst.ExternalReference;
    static reference: outputAst.ExternalReference;
    static inject: outputAst.ExternalReference;
    static injectAttribute: outputAst.ExternalReference;
    static directiveInject: outputAst.ExternalReference;
    static invalidFactory: outputAst.ExternalReference;
    static invalidFactoryDep: outputAst.ExternalReference;
    static templateRefExtractor: outputAst.ExternalReference;
    static forwardRef: outputAst.ExternalReference;
    static resolveForwardRef: outputAst.ExternalReference;
    static ɵɵdefineInjectable: outputAst.ExternalReference;
    static declareInjectable: outputAst.ExternalReference;
    static InjectableDeclaration: outputAst.ExternalReference;
    static resolveWindow: outputAst.ExternalReference;
    static resolveDocument: outputAst.ExternalReference;
    static resolveBody: outputAst.ExternalReference;
    static getComponentDepsFactory: outputAst.ExternalReference;
    static defineComponent: outputAst.ExternalReference;
    static declareComponent: outputAst.ExternalReference;
    static setComponentScope: outputAst.ExternalReference;
    static ChangeDetectionStrategy: outputAst.ExternalReference;
    static ViewEncapsulation: outputAst.ExternalReference;
    static ComponentDeclaration: outputAst.ExternalReference;
    static FactoryDeclaration: outputAst.ExternalReference;
    static declareFactory: outputAst.ExternalReference;
    static FactoryTarget: outputAst.ExternalReference;
    static defineDirective: outputAst.ExternalReference;
    static declareDirective: outputAst.ExternalReference;
    static DirectiveDeclaration: outputAst.ExternalReference;
    static InjectorDef: outputAst.ExternalReference;
    static InjectorDeclaration: outputAst.ExternalReference;
    static defineInjector: outputAst.ExternalReference;
    static declareInjector: outputAst.ExternalReference;
    static NgModuleDeclaration: outputAst.ExternalReference;
    static ModuleWithProviders: outputAst.ExternalReference;
    static defineNgModule: outputAst.ExternalReference;
    static declareNgModule: outputAst.ExternalReference;
    static setNgModuleScope: outputAst.ExternalReference;
    static registerNgModuleType: outputAst.ExternalReference;
    static PipeDeclaration: outputAst.ExternalReference;
    static definePipe: outputAst.ExternalReference;
    static declarePipe: outputAst.ExternalReference;
    static declareClassMetadata: outputAst.ExternalReference;
    static setClassMetadata: outputAst.ExternalReference;
    static setClassMetadataAsync: outputAst.ExternalReference;
    static setClassDebugInfo: outputAst.ExternalReference;
    static queryRefresh: outputAst.ExternalReference;
    static viewQuery: outputAst.ExternalReference;
    static loadQuery: outputAst.ExternalReference;
    static contentQuery: outputAst.ExternalReference;
    static viewQuerySignal: outputAst.ExternalReference;
    static contentQuerySignal: outputAst.ExternalReference;
    static queryAdvance: outputAst.ExternalReference;
    static twoWayProperty: outputAst.ExternalReference;
    static twoWayBindingSet: outputAst.ExternalReference;
    static twoWayListener: outputAst.ExternalReference;
    static NgOnChangesFeature: outputAst.ExternalReference;
    static InheritDefinitionFeature: outputAst.ExternalReference;
    static CopyDefinitionFeature: outputAst.ExternalReference;
    static StandaloneFeature: outputAst.ExternalReference;
    static ProvidersFeature: outputAst.ExternalReference;
    static HostDirectivesFeature: outputAst.ExternalReference;
    static InputTransformsFeatureFeature: outputAst.ExternalReference;
    static listener: outputAst.ExternalReference;
    static getInheritedFactory: outputAst.ExternalReference;
    static InputFlags: outputAst.ExternalReference;
    static sanitizeHtml: outputAst.ExternalReference;
    static sanitizeStyle: outputAst.ExternalReference;
    static sanitizeResourceUrl: outputAst.ExternalReference;
    static sanitizeScript: outputAst.ExternalReference;
    static sanitizeUrl: outputAst.ExternalReference;
    static sanitizeUrlOrResourceUrl: outputAst.ExternalReference;
    static trustConstantHtml: outputAst.ExternalReference;
    static trustConstantResourceUrl: outputAst.ExternalReference;
    static validateIframeAttribute: outputAst.ExternalReference;
    static InputSignalBrandWriteType: {
        name: string;
        moduleName: string;
    };
    static UnwrapDirectiveSignalInputs: {
        name: string;
        moduleName: string;
    };
    static unwrapWritableSignal: {
        name: string;
        moduleName: string;
    };
}

export declare interface R3InjectableMetadata {
    name: string;
    type: R3Reference;
    typeArgumentCount: number;
    providedIn: MaybeForwardRefExpression;
    useClass?: MaybeForwardRefExpression;
    useFactory?: outputAst.Expression;
    useExisting?: MaybeForwardRefExpression;
    useValue?: MaybeForwardRefExpression;
    deps?: R3DependencyMetadata[];
}

export declare interface R3InjectorMetadata {
    name: string;
    type: R3Reference;
    providers: outputAst.Expression | null;
    imports: outputAst.Expression[];
}

/**
 * Metadata for an individual input on a directive.
 */
export declare interface R3InputMetadata {
    classPropertyName: string;
    bindingPropertyName: string;
    required: boolean;
    isSignal: boolean;
    /**
     * Transform function for the input.
     *
     * Null if there is no transform, or if this is a signal input.
     * Signal inputs capture their transform as part of the `InputSignal`.
     */
    transformFunction: outputAst.Expression | null;
}

/**
 * Generates Source Span object for a given R3 Type for JIT mode.
 *
 * @param kind Component or Directive.
 * @param typeName name of the Component or Directive.
 * @param sourceUrl reference to Component or Directive source.
 * @returns instance of ParseSourceSpan that represent a given Component or Directive.
 */
export declare function r3JitTypeSourceSpan(kind: string, typeName: string, sourceUrl: string): ParseSourceSpan;

export declare interface R3NgModuleDependencyMetadata extends R3TemplateDependency {
    kind: R3TemplateDependencyKind.NgModule;
}

/**
 * Metadata required by the module compiler to generate a module def (`ɵmod`) for a type.
 */
export declare type R3NgModuleMetadata = R3NgModuleMetadataGlobal | R3NgModuleMetadataLocal;

declare interface R3NgModuleMetadataCommon {
    kind: R3NgModuleMetadataKind;
    /**
     * An expression representing the module type being compiled.
     */
    type: R3Reference;
    /**
     * How to emit the selector scope values (declarations, imports, exports).
     */
    selectorScopeMode: R3SelectorScopeMode;
    /**
     * The set of schemas that declare elements to be allowed in the NgModule.
     */
    schemas: R3Reference[] | null;
    /** Unique ID or expression representing the unique ID of an NgModule. */
    id: outputAst.Expression | null;
}

/**
 * Metadata required by the module compiler in full/partial mode to generate a module def (`ɵmod`)
 * for a type.
 */
export declare interface R3NgModuleMetadataGlobal extends R3NgModuleMetadataCommon {
    kind: R3NgModuleMetadataKind.Global;
    /**
     * An array of expressions representing the bootstrap components specified by the module.
     */
    bootstrap: R3Reference[];
    /**
     * An array of expressions representing the directives and pipes declared by the module.
     */
    declarations: R3Reference[];
    /**
     * Those declarations which should be visible to downstream consumers. If not specified, all
     * declarations are made visible to downstream consumers.
     */
    publicDeclarationTypes: outputAst.Expression[] | null;
    /**
     * An array of expressions representing the imports of the module.
     */
    imports: R3Reference[];
    /**
     * Whether or not to include `imports` in generated type declarations.
     */
    includeImportTypes: boolean;
    /**
     * An array of expressions representing the exports of the module.
     */
    exports: R3Reference[];
    /**
     * Whether to generate closure wrappers for bootstrap, declarations, imports, and exports.
     */
    containsForwardDecls: boolean;
}

/**
 * The type of the NgModule meta data.
 * - Global: Used for full and partial compilation modes which mainly includes R3References.
 * - Local: Used for the local compilation mode which mainly includes the raw expressions as appears
 * in the NgModule decorator.
 */
export declare enum R3NgModuleMetadataKind {
    Global = 0,
    Local = 1
}

/**
 * Metadata required by the module compiler in local mode to generate a module def (`ɵmod`) for a
 * type.
 */
declare interface R3NgModuleMetadataLocal extends R3NgModuleMetadataCommon {
    kind: R3NgModuleMetadataKind.Local;
    /**
     * The output expression representing the bootstrap components specified by the module.
     */
    bootstrapExpression: outputAst.Expression | null;
    /**
     * The output expression representing the declarations of the module.
     */
    declarationsExpression: outputAst.Expression | null;
    /**
     * The output expression representing the imports of the module.
     */
    importsExpression: outputAst.Expression | null;
    /**
     * The output expression representing the exports of the module.
     */
    exportsExpression: outputAst.Expression | null;
    /**
     * Local compilation mode always requires scope to be handled using side effect function calls.
     */
    selectorScopeMode: R3SelectorScopeMode.SideEffect;
}

export declare interface R3PartialDeclaration {
    /**
     * The minimum version of the compiler that can process this partial declaration.
     */
    minVersion: string;
    /**
     * Version number of the Angular compiler that was used to compile this declaration. The linker
     * will be able to detect which version a library is using and interpret its metadata accordingly.
     */
    version: string;
    /**
     * A reference to the `@angular/core` ES module, which allows access
     * to all Angular exports, including Ivy instructions.
     */
    ngImport: outputAst.Expression;
    /**
     * Reference to the decorated class, which is subject to this partial declaration.
     */
    type: outputAst.Expression;
}

export declare interface R3PipeDependencyMetadata extends R3TemplateDependency {
    kind: R3TemplateDependencyKind.Pipe;
    name: string;
}

export declare interface R3PipeMetadata {
    /**
     * Name of the pipe type.
     */
    name: string;
    /**
     * An expression representing a reference to the pipe itself.
     */
    type: R3Reference;
    /**
     * Number of generic type parameters of the type itself.
     */
    typeArgumentCount: number;
    /**
     * Name of the pipe.
     */
    pipeName: string;
    /**
     * Dependencies of the pipe's constructor.
     */
    deps: R3DependencyMetadata[] | null;
    /**
     * Whether the pipe is marked as pure.
     */
    pure: boolean;
    /**
     * Whether the pipe is standalone.
     */
    isStandalone: boolean;
}

/**
 * Information needed to compile a query (view or content).
 */
export declare interface R3QueryMetadata {
    /**
     * Name of the property on the class to update with query results.
     */
    propertyName: string;
    /**
     * Whether to read only the first matching result, or an array of results.
     */
    first: boolean;
    /**
     * Either an expression representing a type or `InjectionToken` for the query
     * predicate, or a set of string selectors.
     *
     * Note: At compile time we split selectors as an optimization that avoids this
     * extra work at runtime creation phase.
     *
     * Notably, if the selector is not statically analyzable due to an expression,
     * the selectors may need to be split up at runtime.
     */
    predicate: MaybeForwardRefExpression | string[];
    /**
     * Whether to include only direct children or all descendants.
     */
    descendants: boolean;
    /**
     * If the `QueryList` should fire change event only if actual change to query was computed (vs old
     * behavior where the change was fired whenever the query was recomputed, even if the recomputed
     * query resulted in the same list.)
     */
    emitDistinctChangesOnly: boolean;
    /**
     * An expression representing a type to read from each matched node, or null if the default value
     * for a given node is to be returned.
     */
    read: outputAst.Expression | null;
    /**
     * Whether or not this query should collect only static results.
     *
     * If static is true, the query's results will be set on the component after nodes are created,
     * but before change detection runs. This means that any results that relied upon change detection
     * to run (e.g. results inside *ngIf or *ngFor views) will not be collected. Query results are
     * available in the ngOnInit hook.
     *
     * If static is false, the query's results will be set on the component after change detection
     * runs. This means that the query results can contain nodes inside *ngIf or *ngFor views, but
     * the results will not be available in the ngOnInit hook (only in the ngAfterContentInit for
     * content hooks and ngAfterViewInit for view hooks).
     *
     * Note: For signal-based queries, this option does not have any effect.
     */
    static: boolean;
    /** Whether the query is signal-based. */
    isSignal: boolean;
}

export declare interface R3Reference {
    value: outputAst.Expression;
    type: outputAst.Expression;
}

/**
 * How the selector scope of an NgModule (its declarations, imports, and exports) should be emitted
 * as a part of the NgModule definition.
 */
export declare enum R3SelectorScopeMode {
    /**
     * Emit the declarations inline into the module definition.
     *
     * This option is useful in certain contexts where it's known that JIT support is required. The
     * tradeoff here is that this emit style prevents directives and pipes from being tree-shaken if
     * they are unused, but the NgModule is used.
     */
    Inline = 0,
    /**
     * Emit the declarations using a side effectful function call, `ɵɵsetNgModuleScope`, that is
     * guarded with the `ngJitMode` flag.
     *
     * This form of emit supports JIT and can be optimized away if the `ngJitMode` flag is set to
     * false, which allows unused directives and pipes to be tree-shaken.
     */
    SideEffect = 1,
    /**
     * Don't generate selector scopes at all.
     *
     * This is useful for contexts where JIT support is known to be unnecessary.
     */
    Omit = 2
}

/**
 * Processes `Target`s with a given set of directives and performs a binding operation, which
 * returns an object similar to TypeScript's `ts.TypeChecker` that contains knowledge about the
 * target.
 */
export declare class R3TargetBinder<DirectiveT extends DirectiveMeta> implements TargetBinder<DirectiveT> {
    private directiveMatcher;
    constructor(directiveMatcher: SelectorMatcher<DirectiveT[]>);
    /**
     * Perform a binding operation on the given `Target` and return a `BoundTarget` which contains
     * metadata about the types referenced in the template.
     */
    bind(target: Target): BoundTarget<DirectiveT>;
}

/**
 * A dependency that's used within a component template.
 */
export declare interface R3TemplateDependency {
    kind: R3TemplateDependencyKind;
    /**
     * The type of the dependency as an expression.
     */
    type: outputAst.Expression;
}

export declare enum R3TemplateDependencyKind {
    Directive = 0,
    Pipe = 1,
    NgModule = 2
}

/**
 * A dependency that's used within a component template
 */
export declare type R3TemplateDependencyMetadata = R3DirectiveDependencyMetadata | R3PipeDependencyMetadata | R3NgModuleDependencyMetadata;

export declare class ReadKeyExpr extends Expression {
    receiver: Expression;
    index: Expression;
    constructor(receiver: Expression, index: Expression, type?: Type | null, sourceSpan?: ParseSourceSpan | null);
    isEquivalent(e: Expression): boolean;
    isConstant(): boolean;
    visitExpression(visitor: ExpressionVisitor, context: any): any;
    set(value: Expression): WriteKeyExpr;
    clone(): ReadKeyExpr;
}

export declare class ReadPropExpr extends Expression {
    receiver: Expression;
    name: string;
    constructor(receiver: Expression, name: string, type?: Type | null, sourceSpan?: ParseSourceSpan | null);
    get index(): string;
    isEquivalent(e: Expression): boolean;
    isConstant(): boolean;
    visitExpression(visitor: ExpressionVisitor, context: any): any;
    set(value: Expression): WritePropExpr;
    clone(): ReadPropExpr;
}

export declare class ReadVarExpr extends Expression {
    name: string;
    constructor(name: string, type?: Type | null, sourceSpan?: ParseSourceSpan | null);
    isEquivalent(e: Expression): boolean;
    isConstant(): boolean;
    visitExpression(visitor: ExpressionVisitor, context: any): any;
    clone(): ReadVarExpr;
    set(value: Expression): WriteVarExpr;
}

declare class RecurseVisitor implements Visitor_2 {
    visitText(text: Text_3, context?: any): any;
    visitContainer(container: Container, context?: any): any;
    visitIcu(icu: Icu, context?: any): any;
    visitTagPlaceholder(ph: TagPlaceholder, context?: any): any;
    visitPlaceholder(ph: Placeholder, context?: any): any;
    visitIcuPlaceholder(ph: IcuPlaceholder, context?: any): any;
    visitBlockPlaceholder(ph: BlockPlaceholder, context?: any): any;
}

export declare class RecursiveAstVisitor implements AstVisitor {
    visit(ast: AST, context?: any): any;
    visitUnary(ast: Unary, context: any): any;
    visitBinary(ast: Binary, context: any): any;
    visitChain(ast: Chain, context: any): any;
    visitConditional(ast: Conditional, context: any): any;
    visitPipe(ast: BindingPipe, context: any): any;
    visitImplicitReceiver(ast: ThisReceiver, context: any): any;
    visitThisReceiver(ast: ThisReceiver, context: any): any;
    visitInterpolation(ast: Interpolation, context: any): any;
    visitKeyedRead(ast: KeyedRead, context: any): any;
    visitKeyedWrite(ast: KeyedWrite, context: any): any;
    visitLiteralArray(ast: LiteralArray, context: any): any;
    visitLiteralMap(ast: LiteralMap, context: any): any;
    visitLiteralPrimitive(ast: LiteralPrimitive, context: any): any;
    visitPrefixNot(ast: PrefixNot, context: any): any;
    visitNonNullAssert(ast: NonNullAssert, context: any): any;
    visitPropertyRead(ast: PropertyRead, context: any): any;
    visitPropertyWrite(ast: PropertyWrite, context: any): any;
    visitSafePropertyRead(ast: SafePropertyRead, context: any): any;
    visitSafeKeyedRead(ast: SafeKeyedRead, context: any): any;
    visitCall(ast: Call, context: any): any;
    visitSafeCall(ast: SafeCall, context: any): any;
    visitAll(asts: AST[], context: any): any;
}

declare class RecursiveAstVisitor_2 implements StatementVisitor, ExpressionVisitor {
    visitType(ast: Type, context: any): any;
    visitExpression(ast: Expression, context: any): any;
    visitBuiltinType(type: BuiltinType, context: any): any;
    visitExpressionType(type: ExpressionType, context: any): any;
    visitArrayType(type: ArrayType, context: any): any;
    visitMapType(type: MapType, context: any): any;
    visitTransplantedType(type: TransplantedType<unknown>, context: any): any;
    visitWrappedNodeExpr(ast: WrappedNodeExpr<any>, context: any): any;
    visitTypeofExpr(ast: TypeofExpr, context: any): any;
    visitReadVarExpr(ast: ReadVarExpr, context: any): any;
    visitWriteVarExpr(ast: WriteVarExpr, context: any): any;
    visitWriteKeyExpr(ast: WriteKeyExpr, context: any): any;
    visitWritePropExpr(ast: WritePropExpr, context: any): any;
    visitDynamicImportExpr(ast: DynamicImportExpr, context: any): any;
    visitInvokeFunctionExpr(ast: InvokeFunctionExpr, context: any): any;
    visitTaggedTemplateExpr(ast: TaggedTemplateExpr, context: any): any;
    visitInstantiateExpr(ast: InstantiateExpr, context: any): any;
    visitLiteralExpr(ast: LiteralExpr, context: any): any;
    visitLocalizedString(ast: LocalizedString, context: any): any;
    visitExternalExpr(ast: ExternalExpr, context: any): any;
    visitConditionalExpr(ast: ConditionalExpr, context: any): any;
    visitNotExpr(ast: NotExpr, context: any): any;
    visitFunctionExpr(ast: FunctionExpr, context: any): any;
    visitArrowFunctionExpr(ast: ArrowFunctionExpr, context: any): any;
    visitUnaryOperatorExpr(ast: UnaryOperatorExpr, context: any): any;
    visitBinaryOperatorExpr(ast: BinaryOperatorExpr, context: any): any;
    visitReadPropExpr(ast: ReadPropExpr, context: any): any;
    visitReadKeyExpr(ast: ReadKeyExpr, context: any): any;
    visitLiteralArrayExpr(ast: LiteralArrayExpr, context: any): any;
    visitLiteralMapExpr(ast: LiteralMapExpr, context: any): any;
    visitCommaExpr(ast: CommaExpr, context: any): any;
    visitAllExpressions(exprs: Expression[], context: any): void;
    visitDeclareVarStmt(stmt: DeclareVarStmt, context: any): any;
    visitDeclareFunctionStmt(stmt: DeclareFunctionStmt, context: any): any;
    visitExpressionStmt(stmt: ExpressionStatement, context: any): any;
    visitReturnStmt(stmt: ReturnStatement, context: any): any;
    visitIfStmt(stmt: IfStmt, context: any): any;
    visitAllStatements(stmts: Statement[], context: any): void;
}

export declare class RecursiveVisitor implements Visitor {
    constructor();
    visitElement(ast: Element_2, context: any): any;
    visitAttribute(ast: Attribute, context: any): any;
    visitText(ast: Text_2, context: any): any;
    visitComment(ast: Comment_2, context: any): any;
    visitExpansion(ast: Expansion, context: any): any;
    visitExpansionCase(ast: ExpansionCase, context: any): any;
    visitBlock(block: Block, context: any): any;
    visitBlockParameter(ast: BlockParameter, context: any): any;
    private visitChildren;
}

/** Possible values that a reference can be resolved to. */
export declare type ReferenceTarget<DirectiveT> = {
    directive: DirectiveT;
    node: TmplAstElement | TmplAstTemplate;
} | TmplAstElement | TmplAstTemplate;

/**
 * Flags passed into template functions to determine which blocks (i.e. creation, update)
 * should be executed.
 *
 * Typically, a template runs both the creation block and the update block on initialization and
 * subsequent runs only execute the update block. However, dynamically created views require that
 * the creation block be executed separately from the update block (for backwards compat).
 */
declare const enum RenderFlags {
    Create = 1,
    Update = 2
}


/**
 * An interface for retrieving documents by URL that the compiler uses to
 * load templates.
 *
 * This is an abstract class, rather than an interface, so that it can be used
 * as injection token.
 */
export declare abstract class ResourceLoader {
    abstract get(url: string): Promise<string> | string;
}

export declare class ReturnStatement extends Statement {
    value: Expression;
    constructor(value: Expression, sourceSpan?: ParseSourceSpan | null, leadingComments?: LeadingComment[]);
    isEquivalent(stmt: Statement): boolean;
    visitStatement(visitor: StatementVisitor, context: any): any;
}

export declare class SafeCall extends AST {
    receiver: AST;
    args: AST[];
    argumentSpan: AbsoluteSourceSpan;
    constructor(span: ParseSpan, sourceSpan: AbsoluteSourceSpan, receiver: AST, args: AST[], argumentSpan: AbsoluteSourceSpan);
    visit(visitor: AstVisitor, context?: any): any;
}

export declare class SafeKeyedRead extends AST {
    receiver: AST;
    key: AST;
    constructor(span: ParseSpan, sourceSpan: AbsoluteSourceSpan, receiver: AST, key: AST);
    visit(visitor: AstVisitor, context?: any): any;
}

export declare class SafePropertyRead extends ASTWithName {
    receiver: AST;
    name: string;
    constructor(span: ParseSpan, sourceSpan: AbsoluteSourceSpan, nameSpan: AbsoluteSourceSpan, receiver: AST, name: string);
    visit(visitor: AstVisitor, context?: any): any;
}

export declare function sanitizeIdentifier(name: string): string;

export declare interface SchemaMetadata {
    name: string;
}

/**
 * Represents a binding scope within a template.
 *
 * Any variables, references, or other named entities declared within the template will
 * be captured and available by name in `namedEntities`. Additionally, child templates will
 * be analyzed and have their child `Scope`s available in `childScopes`.
 */
declare class Scope implements TmplAstVisitor {
    readonly parentScope: Scope | null;
    readonly rootNode: ScopedNode | null;
    /**
     * Named members of the `Scope`, such as `Reference`s or `Variable`s.
     */
    readonly namedEntities: Map<string, TmplAstVariable | TmplAstReference>;
    /**
     * Set of elements that belong to this scope.
     */
    readonly elementsInScope: Set<TmplAstElement>;
    /**
     * Child `Scope`s for immediately nested `ScopedNode`s.
     */
    readonly childScopes: Map<ScopedNode, Scope>;
    /** Whether this scope is deferred or if any of its ancestors are deferred. */
    readonly isDeferred: boolean;
    private constructor();
    static newRootScope(): Scope;
    /**
     * Process a template (either as a `Template` sub-template with variables, or a plain array of
     * template `Node`s) and construct its `Scope`.
     */
    static apply(template: TmplAstNode[]): Scope;
    /**
     * Internal method to process the scoped node and populate the `Scope`.
     */
    private ingest;
    visitElement(element: TmplAstElement): void;
    visitTemplate(template: TmplAstTemplate): void;
    visitVariable(variable: TmplAstVariable): void;
    visitReference(reference: TmplAstReference): void;
    visitDeferredBlock(deferred: TmplAstDeferredBlock): void;
    visitDeferredBlockPlaceholder(block: TmplAstDeferredBlockPlaceholder): void;
    visitDeferredBlockError(block: TmplAstDeferredBlockError): void;
    visitDeferredBlockLoading(block: TmplAstDeferredBlockLoading): void;
    visitSwitchBlock(block: TmplAstSwitchBlock): void;
    visitSwitchBlockCase(block: TmplAstSwitchBlockCase): void;
    visitForLoopBlock(block: TmplAstForLoopBlock): void;
    visitForLoopBlockEmpty(block: TmplAstForLoopBlockEmpty): void;
    visitIfBlock(block: TmplAstIfBlock): void;
    visitIfBlockBranch(block: TmplAstIfBlockBranch): void;
    visitContent(content: TmplAstContent): void;
    visitBoundAttribute(attr: TmplAstBoundAttribute): void;
    visitBoundEvent(event: TmplAstBoundEvent): void;
    visitBoundText(text: TmplAstBoundText): void;
    visitText(text: TmplAstText): void;
    visitTextAttribute(attr: TmplAstTextAttribute): void;
    visitIcu(icu: TmplAstIcu): void;
    visitDeferredTrigger(trigger: TmplAstDeferredTrigger): void;
    visitUnknownBlock(block: TmplAstUnknownBlock): void;
    private maybeDeclare;
    /**
     * Look up a variable within this `Scope`.
     *
     * This can recurse into a parent `Scope` if it's available.
     */
    lookup(name: string): TmplAstReference | TmplAstVariable | null;
    /**
     * Get the child scope for a `ScopedNode`.
     *
     * This should always be defined.
     */
    getChildScope(node: ScopedNode): Scope;
    private ingestScopedNode;
}

/** Node that has a `Scope` associated with it. */
export declare type ScopedNode = TmplAstTemplate | TmplAstSwitchBlockCase | TmplAstIfBlockBranch | TmplAstForLoopBlock | TmplAstForLoopBlockEmpty | TmplAstDeferredBlock | TmplAstDeferredBlockError | TmplAstDeferredBlockLoading | TmplAstDeferredBlockPlaceholder;

declare enum SecurityContext {
    NONE = 0,
    HTML = 1,
    STYLE = 2,
    SCRIPT = 3,
    URL = 4,
    RESOURCE_URL = 5
}

export declare class SelectorContext<T = any> {
    selector: CssSelector;
    cbContext: T;
    listContext: SelectorListContext;
    notSelectors: CssSelector[];
    constructor(selector: CssSelector, cbContext: T, listContext: SelectorListContext);
    finalize(cssSelector: CssSelector, callback: ((c: CssSelector, a: T) => void) | null): boolean;
}

/**
 * Flags used to generate R3-style CSS Selectors. They are pasted from
 * core/src/render3/projection.ts because they cannot be referenced directly.
 */
declare const enum SelectorFlags {
    /** Indicates this is the beginning of a new negative selector */
    NOT = 1,
    /** Mode for matching attributes */
    ATTRIBUTE = 2,
    /** Mode for matching tag names */
    ELEMENT = 4,
    /** Mode for matching class names */
    CLASS = 8
}

export declare class SelectorListContext {
    selectors: CssSelector[];
    alreadyMatched: boolean;
    constructor(selectors: CssSelector[]);
}

/**
 * Reads a list of CssSelectors and allows to calculate which ones
 * are contained in a given CssSelector.
 */
export declare class SelectorMatcher<T = any> {
    static createNotMatcher(notSelectors: CssSelector[]): SelectorMatcher<null>;
    private _elementMap;
    private _elementPartialMap;
    private _classMap;
    private _classPartialMap;
    private _attrValueMap;
    private _attrValuePartialMap;
    private _listContexts;
    addSelectables(cssSelectors: CssSelector[], callbackCtxt?: T): void;
    /**
     * Add an object that can be found later on by calling `match`.
     * @param cssSelector A css selector
     * @param callbackCtxt An opaque object that will be given to the callback of the `match` function
     */
    private _addSelectable;
    private _addTerminal;
    private _addPartial;
    /**
     * Find the objects that have been added via `addSelectable`
     * whose css selector is contained in the given css selector.
     * @param cssSelector A css selector
     * @param matchedCallback This callback will be called with the object handed into `addSelectable`
     * @return boolean true if a match was found
     */
    match(cssSelector: CssSelector, matchedCallback: ((c: CssSelector, a: T) => void) | null): boolean;
}

export declare abstract class Serializer {
    abstract write(messages: i18n.Message[], locale: string | null): string;
    abstract load(content: string, url: string): {
        locale: string | null;
        i18nNodesByMsgId: {
            [msgId: string]: i18n.Node[];
        };
    };
    abstract digest(message: i18n.Message): string;
    createNameMapper(message: i18n.Message): PlaceholderMapper | null;
}

declare interface SharedConstantDefinition extends ExpressionKeyFn {
    toSharedConstantDeclaration(declName: string, keyExpr: outputAst.Expression): outputAst.Statement;
}


export declare type SourceMap = {
    version: number;
    file?: string;
    sourceRoot: string;
    sources: string[];
    sourcesContent: (string | null)[];
    mappings: string;
};

declare class SourceMapGenerator {
    private file;
    private sourcesContent;
    private lines;
    private lastCol0;
    private hasMappings;
    constructor(file?: string | null);
    addSource(url: string, content?: string | null): this;
    addLine(): this;
    addMapping(col0: number, sourceUrl?: string, sourceLine0?: number, sourceCol0?: number): this;
    toJSON(): SourceMap | null;
    toJsComment(): string;
}

export declare class SplitInterpolation {
    strings: InterpolationPiece[];
    expressions: InterpolationPiece[];
    offsets: number[];
    constructor(strings: InterpolationPiece[], expressions: InterpolationPiece[], offsets: number[]);
}

export declare function splitNsName(elementName: string, fatal?: boolean): [string | null, string];

export declare abstract class Statement {
    modifiers: StmtModifier;
    sourceSpan: ParseSourceSpan | null;
    leadingComments?: LeadingComment[] | undefined;
    constructor(modifiers?: StmtModifier, sourceSpan?: ParseSourceSpan | null, leadingComments?: LeadingComment[] | undefined);
    /**
     * Calculates whether this statement produces the same value as the given statement.
     * Note: We don't check Types nor ParseSourceSpans nor function arguments.
     */
    abstract isEquivalent(stmt: Statement): boolean;
    abstract visitStatement(visitor: StatementVisitor, context: any): any;
    hasModifier(modifier: StmtModifier): boolean;
    addLeadingComment(leadingComment: LeadingComment): void;
}

export declare interface StatementVisitor {
    visitDeclareVarStmt(stmt: DeclareVarStmt, context: any): any;
    visitDeclareFunctionStmt(stmt: DeclareFunctionStmt, context: any): any;
    visitExpressionStmt(stmt: ExpressionStatement, context: any): any;
    visitReturnStmt(stmt: ReturnStatement, context: any): any;
    visitIfStmt(stmt: IfStmt, context: any): any;
}

export declare enum StmtModifier {
    None = 0,
    Final = 1,
    Private = 2,
    Exported = 4,
    Static = 8
}

export declare const STRING_TYPE: BuiltinType;

declare namespace t {
    export {
        tmplAstVisitAll as visitAll,
        TmplAstNode as Node,
        Comment_3 as Comment,
        TmplAstText as Text,
        TmplAstBoundText as BoundText,
        TmplAstTextAttribute as TextAttribute,
        TmplAstBoundAttribute as BoundAttribute,
        TmplAstBoundEvent as BoundEvent,
        TmplAstElement as Element,
        TmplAstDeferredTrigger as DeferredTrigger,
        TmplAstBoundDeferredTrigger as BoundDeferredTrigger,
        TmplAstIdleDeferredTrigger as IdleDeferredTrigger,
        TmplAstImmediateDeferredTrigger as ImmediateDeferredTrigger,
        TmplAstHoverDeferredTrigger as HoverDeferredTrigger,
        TmplAstTimerDeferredTrigger as TimerDeferredTrigger,
        TmplAstInteractionDeferredTrigger as InteractionDeferredTrigger,
        TmplAstViewportDeferredTrigger as ViewportDeferredTrigger,
        TmplAstBlockNode as BlockNode,
        TmplAstDeferredBlockPlaceholder as DeferredBlockPlaceholder,
        TmplAstDeferredBlockLoading as DeferredBlockLoading,
        TmplAstDeferredBlockError as DeferredBlockError,
        TmplAstDeferredBlockTriggers as DeferredBlockTriggers,
        TmplAstDeferredBlock as DeferredBlock,
        TmplAstSwitchBlock as SwitchBlock,
        TmplAstSwitchBlockCase as SwitchBlockCase,
        ForLoopBlockContext,
        TmplAstForLoopBlock as ForLoopBlock,
        TmplAstForLoopBlockEmpty as ForLoopBlockEmpty,
        TmplAstIfBlock as IfBlock,
        TmplAstIfBlockBranch as IfBlockBranch,
        TmplAstUnknownBlock as UnknownBlock,
        TmplAstTemplate as Template,
        TmplAstContent as Content,
        TmplAstVariable as Variable,
        TmplAstReference as Reference,
        TmplAstIcu as Icu,
        TmplAstVisitor as Visitor,
        TmplAstRecursiveVisitor as RecursiveVisitor
    }
}


export declare enum TagContentType {
    RAW_TEXT = 0,
    ESCAPABLE_RAW_TEXT = 1,
    PARSABLE_DATA = 2
}

export declare interface TagDefinition {
    closedByParent: boolean;
    implicitNamespacePrefix: string | null;
    isVoid: boolean;
    ignoreFirstLf: boolean;
    canSelfClose: boolean;
    preventNamespaceInheritance: boolean;
    isClosedByChild(name: string): boolean;
    getContentType(prefix?: string): TagContentType;
}

declare function taggedTemplate(tag: Expression, template: TemplateLiteral, type?: Type | null, sourceSpan?: ParseSourceSpan | null): TaggedTemplateExpr;

export declare class TaggedTemplateExpr extends Expression {
    tag: Expression;
    template: TemplateLiteral;
    constructor(tag: Expression, template: TemplateLiteral, type?: Type | null, sourceSpan?: ParseSourceSpan | null);
    isEquivalent(e: Expression): boolean;
    isConstant(): boolean;
    visitExpression(visitor: ExpressionVisitor, context: any): any;
    clone(): TaggedTemplateExpr;
}

declare class TagPlaceholder implements Node_3 {
    tag: string;
    attrs: {
        [k: string]: string;
    };
    startName: string;
    closeName: string;
    children: Node_3[];
    isVoid: boolean;
    sourceSpan: ParseSourceSpan;
    startSourceSpan: ParseSourceSpan | null;
    endSourceSpan: ParseSourceSpan | null;
    constructor(tag: string, attrs: {
        [k: string]: string;
    }, startName: string, closeName: string, children: Node_3[], isVoid: boolean, sourceSpan: ParseSourceSpan, startSourceSpan: ParseSourceSpan | null, endSourceSpan: ParseSourceSpan | null);
    visit(visitor: Visitor_2, context?: any): any;
}

/**
 * A logical target for analysis, which could contain a template or other types of bindings.
 */
export declare interface Target {
    template?: TmplAstNode[];
}

/**
 * Interface to the binding API, which processes a template and returns an object similar to the
 * `ts.TypeChecker`.
 *
 * The returned `BoundTarget` has an API for extracting information about the processed target.
 */
export declare interface TargetBinder<D extends DirectiveMeta> {
    bind(target: Target): BoundTarget<D>;
}

/**
 * TemplateBinding refers to a particular key-value pair in a microsyntax
 * expression. A few examples are:
 *
 *   |---------------------|--------------|---------|--------------|
 *   |     expression      |     key      |  value  | binding type |
 *   |---------------------|--------------|---------|--------------|
 *   | 1. let item         |    item      |  null   |   variable   |
 *   | 2. of items         |   ngForOf    |  items  |  expression  |
 *   | 3. let x = y        |      x       |    y    |   variable   |
 *   | 4. index as i       |      i       |  index  |   variable   |
 *   | 5. trackBy: func    | ngForTrackBy |   func  |  expression  |
 *   | 6. *ngIf="cond"     |     ngIf     |   cond  |  expression  |
 *   |---------------------|--------------|---------|--------------|
 *
 * (6) is a notable exception because it is a binding from the template key in
 * the LHS of a HTML attribute to the expression in the RHS. All other bindings
 * in the example above are derived solely from the RHS.
 */
export declare type TemplateBinding = VariableBinding | ExpressionBinding;

export declare interface TemplateBindingIdentifier {
    source: string;
    span: AbsoluteSourceSpan;
}

export declare class TemplateBindingParseResult {
    templateBindings: TemplateBinding[];
    warnings: string[];
    errors: ParserError[];
    constructor(templateBindings: TemplateBinding[], warnings: string[], errors: ParserError[]);
}

export declare class TemplateLiteral {
    elements: TemplateLiteralElement[];
    expressions: Expression[];
    constructor(elements: TemplateLiteralElement[], expressions: Expression[]);
    clone(): TemplateLiteral;
}

export declare class TemplateLiteralElement {
    text: string;
    sourceSpan?: ParseSourceSpan | undefined;
    rawText: string;
    constructor(text: string, sourceSpan?: ParseSourceSpan | undefined, rawText?: string);
    clone(): TemplateLiteralElement;
}

declare class Text_2 extends NodeWithI18n {
    value: string;
    tokens: InterpolatedTextToken[];
    constructor(value: string, sourceSpan: ParseSourceSpan, tokens: InterpolatedTextToken[], i18n?: I18nMeta_2);
    visit(visitor: Visitor, context: any): any;
}
export { Text_2 as Text }

declare class Text_3 implements Node_3 {
    value: string;
    sourceSpan: ParseSourceSpan;
    constructor(value: string, sourceSpan: ParseSourceSpan);
    visit(visitor: Visitor_2, context?: any): any;
}

declare interface TextToken extends TokenBase {
    type: LexerTokenType.TEXT | LexerTokenType.ESCAPABLE_RAW_TEXT | LexerTokenType.RAW_TEXT;
    parts: [text: string];
}

/**
 * Receiver when something is accessed through `this` (e.g. `this.foo`). Note that this class
 * inherits from `ImplicitReceiver`, because accessing something through `this` is treated the
 * same as accessing it implicitly inside of an Angular template (e.g. `[attr.title]="this.title"`
 * is the same as `[attr.title]="title"`.). Inheriting allows for the `this` accesses to be treated
 * the same as implicit ones, except for a couple of exceptions like `$event` and `$any`.
 * TODO: we should find a way for this class not to extend from `ImplicitReceiver` in the future.
 */
export declare class ThisReceiver extends ImplicitReceiver {
    visit(visitor: AstVisitor, context?: any): any;
}

export declare class TmplAstBlockNode {
    nameSpan: ParseSourceSpan;
    sourceSpan: ParseSourceSpan;
    startSourceSpan: ParseSourceSpan;
    endSourceSpan: ParseSourceSpan | null;
    constructor(nameSpan: ParseSourceSpan, sourceSpan: ParseSourceSpan, startSourceSpan: ParseSourceSpan, endSourceSpan: ParseSourceSpan | null);
}

export declare class TmplAstBoundAttribute implements TmplAstNode {
    name: string;
    type: BindingType;
    securityContext: SecurityContext;
    value: AST;
    unit: string | null;
    sourceSpan: ParseSourceSpan;
    readonly keySpan: ParseSourceSpan;
    valueSpan: ParseSourceSpan | undefined;
    i18n: I18nMeta_2 | undefined;
    constructor(name: string, type: BindingType, securityContext: SecurityContext, value: AST, unit: string | null, sourceSpan: ParseSourceSpan, keySpan: ParseSourceSpan, valueSpan: ParseSourceSpan | undefined, i18n: I18nMeta_2 | undefined);
    static fromBoundElementProperty(prop: BoundElementProperty, i18n?: I18nMeta_2): TmplAstBoundAttribute;
    visit<Result>(visitor: TmplAstVisitor<Result>): Result;
}

export declare class TmplAstBoundDeferredTrigger extends TmplAstDeferredTrigger {
    value: AST;
    constructor(value: AST, sourceSpan: ParseSourceSpan, prefetchSpan: ParseSourceSpan | null, whenSourceSpan: ParseSourceSpan);
}

export declare class TmplAstBoundEvent implements TmplAstNode {
    name: string;
    type: ParsedEventType;
    handler: AST;
    target: string | null;
    phase: string | null;
    sourceSpan: ParseSourceSpan;
    handlerSpan: ParseSourceSpan;
    readonly keySpan: ParseSourceSpan;
    constructor(name: string, type: ParsedEventType, handler: AST, target: string | null, phase: string | null, sourceSpan: ParseSourceSpan, handlerSpan: ParseSourceSpan, keySpan: ParseSourceSpan);
    static fromParsedEvent(event: ParsedEvent): TmplAstBoundEvent;
    visit<Result>(visitor: TmplAstVisitor<Result>): Result;
}

export declare class TmplAstBoundText implements TmplAstNode {
    value: AST;
    sourceSpan: ParseSourceSpan;
    i18n?: I18nMeta_2 | undefined;
    constructor(value: AST, sourceSpan: ParseSourceSpan, i18n?: I18nMeta_2 | undefined);
    visit<Result>(visitor: TmplAstVisitor<Result>): Result;
}

export declare class TmplAstContent implements TmplAstNode {
    selector: string;
    attributes: TmplAstTextAttribute[];
    sourceSpan: ParseSourceSpan;
    i18n?: I18nMeta_2 | undefined;
    readonly name = "ng-content";
    constructor(selector: string, attributes: TmplAstTextAttribute[], sourceSpan: ParseSourceSpan, i18n?: I18nMeta_2 | undefined);
    visit<Result>(visitor: TmplAstVisitor<Result>): Result;
}

export declare class TmplAstDeferredBlock extends TmplAstBlockNode implements TmplAstNode {
    children: TmplAstNode[];
    placeholder: TmplAstDeferredBlockPlaceholder | null;
    loading: TmplAstDeferredBlockLoading | null;
    error: TmplAstDeferredBlockError | null;
    mainBlockSpan: ParseSourceSpan;
    i18n?: I18nMeta_2 | undefined;
    readonly triggers: Readonly<TmplAstDeferredBlockTriggers>;
    readonly prefetchTriggers: Readonly<TmplAstDeferredBlockTriggers>;
    private readonly definedTriggers;
    private readonly definedPrefetchTriggers;
    constructor(children: TmplAstNode[], triggers: TmplAstDeferredBlockTriggers, prefetchTriggers: TmplAstDeferredBlockTriggers, placeholder: TmplAstDeferredBlockPlaceholder | null, loading: TmplAstDeferredBlockLoading | null, error: TmplAstDeferredBlockError | null, nameSpan: ParseSourceSpan, sourceSpan: ParseSourceSpan, mainBlockSpan: ParseSourceSpan, startSourceSpan: ParseSourceSpan, endSourceSpan: ParseSourceSpan | null, i18n?: I18nMeta_2 | undefined);
    visit<Result>(visitor: TmplAstVisitor<Result>): Result;
    visitAll(visitor: TmplAstVisitor<unknown>): void;
    private visitTriggers;
}

export declare class TmplAstDeferredBlockError extends TmplAstBlockNode implements TmplAstNode {
    children: TmplAstNode[];
    i18n?: I18nMeta_2 | undefined;
    constructor(children: TmplAstNode[], nameSpan: ParseSourceSpan, sourceSpan: ParseSourceSpan, startSourceSpan: ParseSourceSpan, endSourceSpan: ParseSourceSpan | null, i18n?: I18nMeta_2 | undefined);
    visit<Result>(visitor: TmplAstVisitor<Result>): Result;
}

export declare class TmplAstDeferredBlockLoading extends TmplAstBlockNode implements TmplAstNode {
    children: TmplAstNode[];
    afterTime: number | null;
    minimumTime: number | null;
    i18n?: I18nMeta_2 | undefined;
    constructor(children: TmplAstNode[], afterTime: number | null, minimumTime: number | null, nameSpan: ParseSourceSpan, sourceSpan: ParseSourceSpan, startSourceSpan: ParseSourceSpan, endSourceSpan: ParseSourceSpan | null, i18n?: I18nMeta_2 | undefined);
    visit<Result>(visitor: TmplAstVisitor<Result>): Result;
}

export declare class TmplAstDeferredBlockPlaceholder extends TmplAstBlockNode implements TmplAstNode {
    children: TmplAstNode[];
    minimumTime: number | null;
    i18n?: I18nMeta_2 | undefined;
    constructor(children: TmplAstNode[], minimumTime: number | null, nameSpan: ParseSourceSpan, sourceSpan: ParseSourceSpan, startSourceSpan: ParseSourceSpan, endSourceSpan: ParseSourceSpan | null, i18n?: I18nMeta_2 | undefined);
    visit<Result>(visitor: TmplAstVisitor<Result>): Result;
}

export declare interface TmplAstDeferredBlockTriggers {
    when?: TmplAstBoundDeferredTrigger;
    idle?: TmplAstIdleDeferredTrigger;
    immediate?: TmplAstImmediateDeferredTrigger;
    hover?: TmplAstHoverDeferredTrigger;
    timer?: TmplAstTimerDeferredTrigger;
    interaction?: TmplAstInteractionDeferredTrigger;
    viewport?: TmplAstViewportDeferredTrigger;
}

export declare abstract class TmplAstDeferredTrigger implements TmplAstNode {
    nameSpan: ParseSourceSpan | null;
    sourceSpan: ParseSourceSpan;
    prefetchSpan: ParseSourceSpan | null;
    whenOrOnSourceSpan: ParseSourceSpan | null;
    constructor(nameSpan: ParseSourceSpan | null, sourceSpan: ParseSourceSpan, prefetchSpan: ParseSourceSpan | null, whenOrOnSourceSpan: ParseSourceSpan | null);
    visit<Result>(visitor: TmplAstVisitor<Result>): Result;
}

export declare class TmplAstElement implements TmplAstNode {
    name: string;
    attributes: TmplAstTextAttribute[];
    inputs: TmplAstBoundAttribute[];
    outputs: TmplAstBoundEvent[];
    children: TmplAstNode[];
    references: TmplAstReference[];
    sourceSpan: ParseSourceSpan;
    startSourceSpan: ParseSourceSpan;
    endSourceSpan: ParseSourceSpan | null;
    i18n?: I18nMeta_2 | undefined;
    constructor(name: string, attributes: TmplAstTextAttribute[], inputs: TmplAstBoundAttribute[], outputs: TmplAstBoundEvent[], children: TmplAstNode[], references: TmplAstReference[], sourceSpan: ParseSourceSpan, startSourceSpan: ParseSourceSpan, endSourceSpan: ParseSourceSpan | null, i18n?: I18nMeta_2 | undefined);
    visit<Result>(visitor: TmplAstVisitor<Result>): Result;
}

export declare class TmplAstForLoopBlock extends TmplAstBlockNode implements TmplAstNode {
    item: TmplAstVariable;
    expression: ASTWithSource;
    trackBy: ASTWithSource;
    trackKeywordSpan: ParseSourceSpan;
    contextVariables: ForLoopBlockContext;
    children: TmplAstNode[];
    empty: TmplAstForLoopBlockEmpty | null;
    mainBlockSpan: ParseSourceSpan;
    i18n?: I18nMeta_2 | undefined;
    constructor(item: TmplAstVariable, expression: ASTWithSource, trackBy: ASTWithSource, trackKeywordSpan: ParseSourceSpan, contextVariables: ForLoopBlockContext, children: TmplAstNode[], empty: TmplAstForLoopBlockEmpty | null, sourceSpan: ParseSourceSpan, mainBlockSpan: ParseSourceSpan, startSourceSpan: ParseSourceSpan, endSourceSpan: ParseSourceSpan | null, nameSpan: ParseSourceSpan, i18n?: I18nMeta_2 | undefined);
    visit<Result>(visitor: TmplAstVisitor<Result>): Result;
}

export declare class TmplAstForLoopBlockEmpty extends TmplAstBlockNode implements TmplAstNode {
    children: TmplAstNode[];
    i18n?: I18nMeta_2 | undefined;
    constructor(children: TmplAstNode[], sourceSpan: ParseSourceSpan, startSourceSpan: ParseSourceSpan, endSourceSpan: ParseSourceSpan | null, nameSpan: ParseSourceSpan, i18n?: I18nMeta_2 | undefined);
    visit<Result>(visitor: TmplAstVisitor<Result>): Result;
}

export declare class TmplAstHoverDeferredTrigger extends TmplAstDeferredTrigger {
    reference: string | null;
    constructor(reference: string | null, nameSpan: ParseSourceSpan, sourceSpan: ParseSourceSpan, prefetchSpan: ParseSourceSpan | null, onSourceSpan: ParseSourceSpan | null);
}

export declare class TmplAstIcu implements TmplAstNode {
    vars: {
        [name: string]: TmplAstBoundText;
    };
    placeholders: {
        [name: string]: TmplAstText | TmplAstBoundText;
    };
    sourceSpan: ParseSourceSpan;
    i18n?: I18nMeta_2 | undefined;
    constructor(vars: {
        [name: string]: TmplAstBoundText;
    }, placeholders: {
        [name: string]: TmplAstText | TmplAstBoundText;
    }, sourceSpan: ParseSourceSpan, i18n?: I18nMeta_2 | undefined);
    visit<Result>(visitor: TmplAstVisitor<Result>): Result;
}

export declare class TmplAstIdleDeferredTrigger extends TmplAstDeferredTrigger {
}

export declare class TmplAstIfBlock extends TmplAstBlockNode implements TmplAstNode {
    branches: TmplAstIfBlockBranch[];
    constructor(branches: TmplAstIfBlockBranch[], sourceSpan: ParseSourceSpan, startSourceSpan: ParseSourceSpan, endSourceSpan: ParseSourceSpan | null, nameSpan: ParseSourceSpan);
    visit<Result>(visitor: TmplAstVisitor<Result>): Result;
}

export declare class TmplAstIfBlockBranch extends TmplAstBlockNode implements TmplAstNode {
    expression: AST | null;
    children: TmplAstNode[];
    expressionAlias: TmplAstVariable | null;
    i18n?: I18nMeta_2 | undefined;
    constructor(expression: AST | null, children: TmplAstNode[], expressionAlias: TmplAstVariable | null, sourceSpan: ParseSourceSpan, startSourceSpan: ParseSourceSpan, endSourceSpan: ParseSourceSpan | null, nameSpan: ParseSourceSpan, i18n?: I18nMeta_2 | undefined);
    visit<Result>(visitor: TmplAstVisitor<Result>): Result;
}

export declare class TmplAstImmediateDeferredTrigger extends TmplAstDeferredTrigger {
}

export declare class TmplAstInteractionDeferredTrigger extends TmplAstDeferredTrigger {
    reference: string | null;
    constructor(reference: string | null, nameSpan: ParseSourceSpan, sourceSpan: ParseSourceSpan, prefetchSpan: ParseSourceSpan | null, onSourceSpan: ParseSourceSpan | null);
}

export declare interface TmplAstNode {
    sourceSpan: ParseSourceSpan;
    visit<Result>(visitor: TmplAstVisitor<Result>): Result;
}

export declare class TmplAstRecursiveVisitor implements TmplAstVisitor<void> {
    visitElement(element: TmplAstElement): void;
    visitTemplate(template: TmplAstTemplate): void;
    visitDeferredBlock(deferred: TmplAstDeferredBlock): void;
    visitDeferredBlockPlaceholder(block: TmplAstDeferredBlockPlaceholder): void;
    visitDeferredBlockError(block: TmplAstDeferredBlockError): void;
    visitDeferredBlockLoading(block: TmplAstDeferredBlockLoading): void;
    visitSwitchBlock(block: TmplAstSwitchBlock): void;
    visitSwitchBlockCase(block: TmplAstSwitchBlockCase): void;
    visitForLoopBlock(block: TmplAstForLoopBlock): void;
    visitForLoopBlockEmpty(block: TmplAstForLoopBlockEmpty): void;
    visitIfBlock(block: TmplAstIfBlock): void;
    visitIfBlockBranch(block: TmplAstIfBlockBranch): void;
    visitContent(content: TmplAstContent): void;
    visitVariable(variable: TmplAstVariable): void;
    visitReference(reference: TmplAstReference): void;
    visitTextAttribute(attribute: TmplAstTextAttribute): void;
    visitBoundAttribute(attribute: TmplAstBoundAttribute): void;
    visitBoundEvent(attribute: TmplAstBoundEvent): void;
    visitText(text: TmplAstText): void;
    visitBoundText(text: TmplAstBoundText): void;
    visitIcu(icu: TmplAstIcu): void;
    visitDeferredTrigger(trigger: TmplAstDeferredTrigger): void;
    visitUnknownBlock(block: TmplAstUnknownBlock): void;
}

export declare class TmplAstReference implements TmplAstNode {
    name: string;
    value: string;
    sourceSpan: ParseSourceSpan;
    readonly keySpan: ParseSourceSpan;
    valueSpan?: ParseSourceSpan | undefined;
    constructor(name: string, value: string, sourceSpan: ParseSourceSpan, keySpan: ParseSourceSpan, valueSpan?: ParseSourceSpan | undefined);
    visit<Result>(visitor: TmplAstVisitor<Result>): Result;
}

export declare class TmplAstSwitchBlock extends TmplAstBlockNode implements TmplAstNode {
    expression: AST;
    cases: TmplAstSwitchBlockCase[];
    /**
     * These blocks are only captured to allow for autocompletion in the language service. They
     * aren't meant to be processed in any other way.
     */
    unknownBlocks: TmplAstUnknownBlock[];
    constructor(expression: AST, cases: TmplAstSwitchBlockCase[], 
    /**
     * These blocks are only captured to allow for autocompletion in the language service. They
     * aren't meant to be processed in any other way.
     */
    unknownBlocks: TmplAstUnknownBlock[], sourceSpan: ParseSourceSpan, startSourceSpan: ParseSourceSpan, endSourceSpan: ParseSourceSpan | null, nameSpan: ParseSourceSpan);
    visit<Result>(visitor: TmplAstVisitor<Result>): Result;
}

export declare class TmplAstSwitchBlockCase extends TmplAstBlockNode implements TmplAstNode {
    expression: AST | null;
    children: TmplAstNode[];
    i18n?: I18nMeta_2 | undefined;
    constructor(expression: AST | null, children: TmplAstNode[], sourceSpan: ParseSourceSpan, startSourceSpan: ParseSourceSpan, endSourceSpan: ParseSourceSpan | null, nameSpan: ParseSourceSpan, i18n?: I18nMeta_2 | undefined);
    visit<Result>(visitor: TmplAstVisitor<Result>): Result;
}

export declare class TmplAstTemplate implements TmplAstNode {
    tagName: string | null;
    attributes: TmplAstTextAttribute[];
    inputs: TmplAstBoundAttribute[];
    outputs: TmplAstBoundEvent[];
    templateAttrs: (TmplAstBoundAttribute | TmplAstTextAttribute)[];
    children: TmplAstNode[];
    references: TmplAstReference[];
    variables: TmplAstVariable[];
    sourceSpan: ParseSourceSpan;
    startSourceSpan: ParseSourceSpan;
    endSourceSpan: ParseSourceSpan | null;
    i18n?: I18nMeta_2 | undefined;
    constructor(tagName: string | null, attributes: TmplAstTextAttribute[], inputs: TmplAstBoundAttribute[], outputs: TmplAstBoundEvent[], templateAttrs: (TmplAstBoundAttribute | TmplAstTextAttribute)[], children: TmplAstNode[], references: TmplAstReference[], variables: TmplAstVariable[], sourceSpan: ParseSourceSpan, startSourceSpan: ParseSourceSpan, endSourceSpan: ParseSourceSpan | null, i18n?: I18nMeta_2 | undefined);
    visit<Result>(visitor: TmplAstVisitor<Result>): Result;
}

export declare class TmplAstText implements TmplAstNode {
    value: string;
    sourceSpan: ParseSourceSpan;
    constructor(value: string, sourceSpan: ParseSourceSpan);
    visit<Result>(visitor: TmplAstVisitor<Result>): Result;
}

/**
 * Represents a text attribute in the template.
 *
 * `valueSpan` may not be present in cases where there is no value `<div a></div>`.
 * `keySpan` may also not be present for synthetic attributes from ICU expansions.
 */
export declare class TmplAstTextAttribute implements TmplAstNode {
    name: string;
    value: string;
    sourceSpan: ParseSourceSpan;
    readonly keySpan: ParseSourceSpan | undefined;
    valueSpan?: ParseSourceSpan | undefined;
    i18n?: I18nMeta_2 | undefined;
    constructor(name: string, value: string, sourceSpan: ParseSourceSpan, keySpan: ParseSourceSpan | undefined, valueSpan?: ParseSourceSpan | undefined, i18n?: I18nMeta_2 | undefined);
    visit<Result>(visitor: TmplAstVisitor<Result>): Result;
}

export declare class TmplAstTimerDeferredTrigger extends TmplAstDeferredTrigger {
    delay: number;
    constructor(delay: number, nameSpan: ParseSourceSpan, sourceSpan: ParseSourceSpan, prefetchSpan: ParseSourceSpan | null, onSourceSpan: ParseSourceSpan | null);
}

export declare class TmplAstUnknownBlock implements TmplAstNode {
    name: string;
    sourceSpan: ParseSourceSpan;
    nameSpan: ParseSourceSpan;
    constructor(name: string, sourceSpan: ParseSourceSpan, nameSpan: ParseSourceSpan);
    visit<Result>(visitor: TmplAstVisitor<Result>): Result;
}

export declare class TmplAstVariable implements TmplAstNode {
    name: string;
    value: string;
    sourceSpan: ParseSourceSpan;
    readonly keySpan: ParseSourceSpan;
    valueSpan?: ParseSourceSpan | undefined;
    constructor(name: string, value: string, sourceSpan: ParseSourceSpan, keySpan: ParseSourceSpan, valueSpan?: ParseSourceSpan | undefined);
    visit<Result>(visitor: TmplAstVisitor<Result>): Result;
}

export declare class TmplAstViewportDeferredTrigger extends TmplAstDeferredTrigger {
    reference: string | null;
    constructor(reference: string | null, nameSpan: ParseSourceSpan, sourceSpan: ParseSourceSpan, prefetchSpan: ParseSourceSpan | null, onSourceSpan: ParseSourceSpan | null);
}

export declare function tmplAstVisitAll<Result>(visitor: TmplAstVisitor<Result>, nodes: TmplAstNode[]): Result[];

export declare interface TmplAstVisitor<Result = any> {
    visit?(node: TmplAstNode): Result;
    visitElement(element: TmplAstElement): Result;
    visitTemplate(template: TmplAstTemplate): Result;
    visitContent(content: TmplAstContent): Result;
    visitVariable(variable: TmplAstVariable): Result;
    visitReference(reference: TmplAstReference): Result;
    visitTextAttribute(attribute: TmplAstTextAttribute): Result;
    visitBoundAttribute(attribute: TmplAstBoundAttribute): Result;
    visitBoundEvent(attribute: TmplAstBoundEvent): Result;
    visitText(text: TmplAstText): Result;
    visitBoundText(text: TmplAstBoundText): Result;
    visitIcu(icu: TmplAstIcu): Result;
    visitDeferredBlock(deferred: TmplAstDeferredBlock): Result;
    visitDeferredBlockPlaceholder(block: TmplAstDeferredBlockPlaceholder): Result;
    visitDeferredBlockError(block: TmplAstDeferredBlockError): Result;
    visitDeferredBlockLoading(block: TmplAstDeferredBlockLoading): Result;
    visitDeferredTrigger(trigger: TmplAstDeferredTrigger): Result;
    visitSwitchBlock(block: TmplAstSwitchBlock): Result;
    visitSwitchBlockCase(block: TmplAstSwitchBlockCase): Result;
    visitForLoopBlock(block: TmplAstForLoopBlock): Result;
    visitForLoopBlockEmpty(block: TmplAstForLoopBlockEmpty): Result;
    visitIfBlock(block: TmplAstIfBlock): Result;
    visitIfBlockBranch(block: TmplAstIfBlockBranch): Result;
    visitUnknownBlock(block: TmplAstUnknownBlock): Result;
}

export declare class Token {
    index: number;
    end: number;
    type: TokenType;
    numValue: number;
    strValue: string;
    constructor(index: number, end: number, type: TokenType, numValue: number, strValue: string);
    isCharacter(code: number): boolean;
    isNumber(): boolean;
    isString(): boolean;
    isOperator(operator: string): boolean;
    isIdentifier(): boolean;
    isPrivateIdentifier(): boolean;
    isKeyword(): boolean;
    isKeywordLet(): boolean;
    isKeywordAs(): boolean;
    isKeywordNull(): boolean;
    isKeywordUndefined(): boolean;
    isKeywordTrue(): boolean;
    isKeywordFalse(): boolean;
    isKeywordThis(): boolean;
    isError(): boolean;
    toNumber(): number;
    toString(): string | null;
}

declare interface TokenBase {
    type: LexerTokenType;
    parts: string[];
    sourceSpan: ParseSourceSpan;
}

/**
 * Options that modify how the text is tokenized.
 */
declare interface TokenizeOptions {
    /** Whether to tokenize ICU messages (considered as text nodes when false). */
    tokenizeExpansionForms?: boolean;
    /** How to tokenize interpolation markers. */
    interpolationConfig?: InterpolationConfig;
    /**
     * The start and end point of the text to parse within the `source` string.
     * The entire `source` string is parsed if this is not provided.
     * */
    range?: LexerRange;
    /**
     * If this text is stored in a JavaScript string, then we have to deal with escape sequences.
     *
     * **Example 1:**
     *
     * ```
     * "abc\"def\nghi"
     * ```
     *
     * - The `\"` must be converted to `"`.
     * - The `\n` must be converted to a new line character in a token,
     *   but it should not increment the current line for source mapping.
     *
     * **Example 2:**
     *
     * ```
     * "abc\
     *  def"
     * ```
     *
     * The line continuation (`\` followed by a newline) should be removed from a token
     * but the new line should increment the current line for source mapping.
     */
    escapedString?: boolean;
    /**
     * If this text is stored in an external template (e.g. via `templateUrl`) then we need to decide
     * whether or not to normalize the line-endings (from `\r\n` to `\n`) when processing ICU
     * expressions.
     *
     * If `true` then we will normalize ICU expression line endings.
     * The default is `false`, but this will be switched in a future major release.
     */
    i18nNormalizeLineEndingsInICUs?: boolean;
    /**
     * An array of characters that should be considered as leading trivia.
     * Leading trivia are characters that are not important to the developer, and so should not be
     * included in source-map segments.  A common example is whitespace.
     */
    leadingTriviaChars?: string[];
    /**
     * If true, do not convert CRLF to LF.
     */
    preserveLineEndings?: boolean;
    /**
     * Whether to tokenize @ block syntax. Otherwise considered text,
     * or ICU tokens if `tokenizeExpansionForms` is enabled.
     */
    tokenizeBlocks?: boolean;
}


export declare enum TokenType {
    Character = 0,
    Identifier = 1,
    PrivateIdentifier = 2,
    Keyword = 3,
    String = 4,
    Operator = 5,
    Number = 6,
    Error = 7
}

export declare class TransplantedType<T> extends Type {
    readonly type: T;
    constructor(type: T, modifiers?: TypeModifier);
    visitType(visitor: TypeVisitor, context: any): any;
}

declare function transplantedType<T>(type: T, typeModifiers?: TypeModifier): TransplantedType<T>;

export declare class TreeError extends ParseError {
    elementName: string | null;
    static create(elementName: string | null, span: ParseSourceSpan, msg: string): TreeError;
    constructor(elementName: string | null, span: ParseSourceSpan, msg: string);
}

export declare abstract class Type {
    modifiers: TypeModifier;
    constructor(modifiers?: TypeModifier);
    abstract visitType(visitor: TypeVisitor, context: any): any;
    hasModifier(modifier: TypeModifier): boolean;
}

declare interface Type_2 extends Function {
    new (...args: any[]): any;
}

declare const Type_2: FunctionConstructor;

declare const TYPED_NULL_EXPR: LiteralExpr;

export declare enum TypeModifier {
    None = 0,
    Const = 1
}

export declare class TypeofExpr extends Expression {
    expr: Expression;
    constructor(expr: Expression, type?: Type | null, sourceSpan?: ParseSourceSpan | null);
    visitExpression(visitor: ExpressionVisitor, context: any): any;
    isEquivalent(e: Expression): boolean;
    isConstant(): boolean;
    clone(): TypeofExpr;
}

declare function typeofExpr(expr: Expression): TypeofExpr;

export declare interface TypeVisitor {
    visitBuiltinType(type: BuiltinType, context: any): any;
    visitExpressionType(type: ExpressionType, context: any): any;
    visitArrayType(type: ArrayType, context: any): any;
    visitMapType(type: MapType, context: any): any;
    visitTransplantedType(type: TransplantedType<unknown>, context: any): any;
}

/**
 * For backwards compatibility reasons, `Unary` inherits from `Binary` and mimics the binary AST
 * node that was originally used. This inheritance relation can be deleted in some future major,
 * after consumers have been given a chance to fully support Unary.
 */
export declare class Unary extends Binary {
    operator: string;
    expr: AST;
    left: never;
    right: never;
    operation: never;
    /**
     * Creates a unary minus expression "-x", represented as `Binary` using "0 - x".
     */
    static createMinus(span: ParseSpan, sourceSpan: AbsoluteSourceSpan, expr: AST): Unary;
    /**
     * Creates a unary plus expression "+x", represented as `Binary` using "x - 0".
     */
    static createPlus(span: ParseSpan, sourceSpan: AbsoluteSourceSpan, expr: AST): Unary;
    /**
     * During the deprecation period this constructor is private, to avoid consumers from creating
     * a `Unary` with the fallback properties for `Binary`.
     */
    private constructor();
    visit(visitor: AstVisitor, context?: any): any;
}

declare function unary(operator: UnaryOperator, expr: Expression, type?: Type, sourceSpan?: ParseSourceSpan | null): UnaryOperatorExpr;

export declare enum UnaryOperator {
    Minus = 0,
    Plus = 1
}

export declare class UnaryOperatorExpr extends Expression {
    operator: UnaryOperator;
    expr: Expression;
    parens: boolean;
    constructor(operator: UnaryOperator, expr: Expression, type?: Type | null, sourceSpan?: ParseSourceSpan | null, parens?: boolean);
    isEquivalent(e: Expression): boolean;
    isConstant(): boolean;
    visitExpression(visitor: ExpressionVisitor, context: any): any;
    clone(): UnaryOperatorExpr;
}

declare function variable(name: string, type?: Type | null, sourceSpan?: ParseSourceSpan | null): ReadVarExpr;

export declare class VariableBinding {
    readonly sourceSpan: AbsoluteSourceSpan;
    readonly key: TemplateBindingIdentifier;
    readonly value: TemplateBindingIdentifier | null;
    /**
     * @param sourceSpan entire span of the binding.
     * @param key name of the LHS along with its span.
     * @param value optional value for the RHS along with its span.
     */
    constructor(sourceSpan: AbsoluteSourceSpan, key: TemplateBindingIdentifier, value: TemplateBindingIdentifier | null);
}

/**
 * Verifies host bindings and returns the list of errors (if any). Empty array indicates that a
 * given set of host bindings has no errors.
 *
 * @param bindings set of host bindings to verify.
 * @param sourceSpan source span where host bindings were defined.
 * @returns array of errors associated with a given set of host bindings.
 */
export declare function verifyHostBindings(bindings: ParsedHostBindings, sourceSpan: ParseSourceSpan): ParseError[];

export declare const VERSION: Version;

export declare class Version {
    full: string;
    readonly major: string;
    readonly minor: string;
    readonly patch: string;
    constructor(full: string);
}

export declare enum ViewEncapsulation {
    Emulated = 0,
    None = 2,
    ShadowDom = 3
}

export declare function visitAll(visitor: Visitor, nodes: Node_2[], context?: any): any[];

export declare interface Visitor {
    visit?(node: Node_2, context: any): any;
    visitElement(element: Element_2, context: any): any;
    visitAttribute(attribute: Attribute, context: any): any;
    visitText(text: Text_2, context: any): any;
    visitComment(comment: Comment_2, context: any): any;
    visitExpansion(expansion: Expansion, context: any): any;
    visitExpansionCase(expansionCase: ExpansionCase, context: any): any;
    visitBlock(block: Block, context: any): any;
    visitBlockParameter(parameter: BlockParameter, context: any): any;
}

declare interface Visitor_2 {
    visitText(text: Text_3, context?: any): any;
    visitContainer(container: Container, context?: any): any;
    visitIcu(icu: Icu, context?: any): any;
    visitTagPlaceholder(ph: TagPlaceholder, context?: any): any;
    visitPlaceholder(ph: Placeholder, context?: any): any;
    visitIcuPlaceholder(ph: IcuPlaceholder, context?: any): any;
    visitBlockPlaceholder(ph: BlockPlaceholder, context?: any): any;
}

export declare class WrappedNodeExpr<T> extends Expression {
    node: T;
    constructor(node: T, type?: Type | null, sourceSpan?: ParseSourceSpan | null);
    isEquivalent(e: Expression): boolean;
    isConstant(): boolean;
    visitExpression(visitor: ExpressionVisitor, context: any): any;
    clone(): WrappedNodeExpr<T>;
}

export declare class WriteKeyExpr extends Expression {
    receiver: Expression;
    index: Expression;
    value: Expression;
    constructor(receiver: Expression, index: Expression, value: Expression, type?: Type | null, sourceSpan?: ParseSourceSpan | null);
    isEquivalent(e: Expression): boolean;
    isConstant(): boolean;
    visitExpression(visitor: ExpressionVisitor, context: any): any;
    clone(): WriteKeyExpr;
}

export declare class WritePropExpr extends Expression {
    receiver: Expression;
    name: string;
    value: Expression;
    constructor(receiver: Expression, name: string, value: Expression, type?: Type | null, sourceSpan?: ParseSourceSpan | null);
    isEquivalent(e: Expression): boolean;
    isConstant(): boolean;
    visitExpression(visitor: ExpressionVisitor, context: any): any;
    clone(): WritePropExpr;
}

export declare class WriteVarExpr extends Expression {
    name: string;
    value: Expression;
    constructor(name: string, value: Expression, type?: Type | null, sourceSpan?: ParseSourceSpan | null);
    isEquivalent(e: Expression): boolean;
    isConstant(): boolean;
    visitExpression(visitor: ExpressionVisitor, context: any): any;
    clone(): WriteVarExpr;
    toDeclStmt(type?: Type | null, modifiers?: StmtModifier): DeclareVarStmt;
    toConstDecl(): DeclareVarStmt;
}

export declare class Xliff extends Serializer {
    write(messages: i18n.Message[], locale: string | null): string;
    load(content: string, url: string): {
        locale: string;
        i18nNodesByMsgId: {
            [msgId: string]: i18n.Node[];
        };
    };
    digest(message: i18n.Message): string;
}

export declare class Xliff2 extends Serializer {
    write(messages: i18n.Message[], locale: string | null): string;
    load(content: string, url: string): {
        locale: string;
        i18nNodesByMsgId: {
            [msgId: string]: i18n.Node[];
        };
    };
    digest(message: i18n.Message): string;
}

export declare class Xmb extends Serializer {
    write(messages: i18n.Message[], locale: string | null): string;
    load(content: string, url: string): {
        locale: string;
        i18nNodesByMsgId: {
            [msgId: string]: i18n.Node[];
        };
    };
    digest(message: i18n.Message): string;
    createNameMapper(message: i18n.Message): PlaceholderMapper;
}

export declare class XmlParser extends Parser_2 {
    constructor();
    parse(source: string, url: string, options?: TokenizeOptions): ParseTreeResult;
}

export declare class Xtb extends Serializer {
    write(messages: i18n.Message[], locale: string | null): string;
    load(content: string, url: string): {
        locale: string;
        i18nNodesByMsgId: {
            [msgId: string]: i18n.Node[];
        };
    };
    digest(message: i18n.Message): string;
    createNameMapper(message: i18n.Message): PlaceholderMapper;
}

export { }
