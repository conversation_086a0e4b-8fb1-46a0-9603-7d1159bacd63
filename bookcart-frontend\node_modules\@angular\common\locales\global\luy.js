/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['luy'] = ["luy",[["a.m.","p.m."],u,u],u,[["S","M","T","W","T","F","S"],["J2","J3","J4","J5","<PERSON>","Ij","J1"],["<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON> wa <PERSON>nne","<PERSON><PERSON><PERSON> wa <PERSON>","<PERSON><PERSON><PERSON><PERSON>"],["J2","J3","J4","J5","<PERSON>","Ij","J1"]],u,[["J","F","M","A","M","J","J","A","<PERSON>","O","<PERSON>","<PERSON>"],["<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON>"],["<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","April<PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON>em<PERSON>","<PERSON><PERSON><PERSON>","<PERSON>em<PERSON>","Desemba"]],u,[["BC","AD"],u,["Imberi ya Kuuza Kwa","Muhiga Kuvita Kuuza"]],0,[6,0],["dd/MM/y","d MMM y","d MMMM y","EEEE, d MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤#,##0.00;¤- #,##0.00","#E0"],"KES","Ksh","Sirinji ya Kenya",{"JPY":["JP¥","¥"],"KES":["Ksh"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    