/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["yo-BJ", [["Àárɔ̀", "Ɔ̀sán"], u, u], u, [["À", "A", "Ì", "Ɔ", "Ɔ", "Ɛ", "À"], ["Àìk", "Aj", "Ìsɛ́g", "Ɔjɔ́r", "Ɔjɔ́b", "Ɛt", "Àbám"], ["Ɔjɔ́ Àìkú", "Ɔjɔ́ Ajé", "Ɔjɔ́ Ìsɛ́gun", "Ɔjɔ́rú", "Ɔjɔ́bɔ", "Ɔjɔ́ Ɛtì", "Ɔjɔ́ Àbámɛ́ta"], ["Àìk", "Aj", "Ìsɛ́g", "Ɔjɔ́r", "Ɔjɔ́b", "Ɛt", "Àbám"]], [["À", "A", "Ì", "Ɔ", "Ɔ", "Ɛ", "À"], ["Àìk", "Aj", "Ìsɛ́g", "Ɔjɔ́r", "Ɔjɔ́b", "Ɛt", "Àbám"], ["Àìkú", "Ajé", "Ìsɛ́gun", "Ɔjɔ́rú", "Ɔjɔ́bɔ", "Ɛtì", "Àbámɛ́ta"], ["Àìk", "Aj", "Ìsɛ́g", "Ɔjɔ́r", "Ɔjɔ́b", "Ɛt", "Àbám"]], [["S", "È", "Ɛ", "Ì", "Ɛ̀", "Ò", "A", "Ò", "O", "Ɔ̀", "B", "Ɔ̀"], ["Shɛ́r", "Èrèl", "Ɛrɛ̀n", "Ìgb", "Ɛ̀bi", "Òkú", "Agɛ", "Ògú", "Owe", "Ɔ̀wà", "Bél", "Ɔ̀pɛ"], ["Oshù Shɛ́rɛ́", "Oshù Èrèlè", "Oshù Ɛrɛ̀nà", "Oshù Ìgbé", "Oshù Ɛ̀bibi", "Oshù Òkúdu", "Oshù Agɛmɔ", "Oshù Ògún", "Oshù Owewe", "Oshù Ɔ̀wàrà", "Oshù Bélú", "Oshù Ɔ̀pɛ̀"]], [["S", "È", "Ɛ", "Ì", "Ɛ̀", "Ò", "A", "Ò", "O", "Ɔ̀", "B", "Ɔ̀"], ["Shɛ́", "Èr", "Ɛr", "Ìg", "Ɛ̀b", "Òk", "Ag", "Òg", "Ow", "Ɔ̀w", "Bé", "Ɔ̀p"], ["Shɛ́rɛ́", "Èrèlè", "Ɛrɛ̀nà", "Ìgbé", "Ɛ̀bibi", "Òkúdu", "Agɛmɔ", "Ògún", "Owewe", "Ɔ̀wàrà", "Bélú", "Ɔ̀pɛ̀"]], [["BCE", "AD"], u, ["Saju Kristi", "Lehin Kristi"]], 1, [6, 0], ["d/M/y", "d MM y", "d MMM y", "EEEE, d MMM y"], ["H:m", "H:m:s", "H:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "XOF", "F CFA", "Faransì ìwɔ̀-oorùn Afíríkà", { "JPY": ["JP¥", "¥"], "NGN": ["₦"], "RUB": ["₽"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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