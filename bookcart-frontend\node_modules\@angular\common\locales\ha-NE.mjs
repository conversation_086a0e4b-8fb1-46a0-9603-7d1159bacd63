/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["ha-NE", [["SF", "YM"], u, ["<PERSON><PERSON><PERSON>", "Yamma"]], [["SF", "YM"], u, u], [["L", "L", "T", "L", "A", "J", "A"], ["Lah", "Lit", "<PERSON>l", "<PERSON>r", "<PERSON>h", "Ju<PERSON>", "<PERSON>a"], ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>a", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>ʼa", "<PERSON><PERSON>"], ["Lh", "<PERSON>", "<PERSON>", "<PERSON>r", "<PERSON>", "<PERSON>", "<PERSON>"]], u, [["<PERSON>", "<PERSON>", "<PERSON>", "A", "M", "Y", "Y", "A", "S", "O", "N", "D"], ["<PERSON>", "Fab", "<PERSON>", "<PERSON><PERSON>", "May", "<PERSON>", "<PERSON>l", "<PERSON><PERSON>", "<PERSON>t", "<PERSON>t", "Nuw", "<PERSON>s"], ["<PERSON>iru", "<PERSON>ab<PERSON>ru", "<PERSON>", "<PERSON><PERSON>rilu", "<PERSON>u", "<PERSON>i", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>ba", "<PERSON>toba", "<PERSON>uwa<PERSON>", "<PERSON><PERSON><PERSON>"]], u, [["<PERSON>.<PERSON>", "<PERSON>HAI"], u, ["Kafin haihuwar annab", "Bayan haihuwar annab"]], 1, [6, 0], ["d/M/yy", "d MMM, y", "d MMMM, y", "EEEE d MMMM, y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1}, {0}", u, "{1} 'da' {0}", "{1} {0}"], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "XOF", "F CFA", "Kuɗin Sefa na Afirka Ta Yamma", { "NGN": ["₦"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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