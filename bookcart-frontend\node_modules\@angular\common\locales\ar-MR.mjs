/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 0)
        return 0;
    if (n === 1)
        return 1;
    if (n === 2)
        return 2;
    if (n % 100 === Math.floor(n % 100) && (n % 100 >= 3 && n % 100 <= 10))
        return 3;
    if (n % 100 === Math.floor(n % 100) && (n % 100 >= 11 && n % 100 <= 99))
        return 4;
    return 5;
}
export default ["ar-MR", [["ص", "م"], u, u], [["ص", "م"], u, ["صباحًا", "مساءً"]], [["ح", "ن", "ث", "ر", "خ", "ج", "س"], ["الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"], u, ["أحد", "إثنين", "ثلاثاء", "أربعاء", "خميس", "جمعة", "سبت"]], u, [["ي", "ف", "م", "إ", "و", "ن", "ل", "غ", "ش", "ك", "ب", "د"], ["يناير", "فبراير", "مارس", "إبريل", "مايو", "يونيو", "يوليو", "أغشت", "شتمبر", "أكتوبر", "نوفمبر", "دجمبر"], u], u, [["ق.م", "م"], u, ["قبل الميلاد", "ميلادي"]], 1, [6, 0], ["d‏/M‏/y", "dd‏/MM‏/y", "d MMMM y", "EEEE، d MMMM y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1}, {0}", u, "{1} في {0}", u], [",", ".", ";", "‎%‎", "‎+", "‎-", "E", "×", "‰", "∞", "ليس رقمًا", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "MRU", "أ.م.", "أوقية موريتانية", { "AED": ["د.إ.‏"], "ARS": [u, "AR$"], "AUD": ["AU$"], "BBD": [u, "BB$"], "BHD": ["د.ب.‏"], "BMD": [u, "BM$"], "BND": [u, "BN$"], "BSD": [u, "BS$"], "BYN": [u, "р."], "BZD": [u, "BZ$"], "CAD": ["CA$"], "CLP": [u, "CL$"], "CNY": ["CN¥"], "COP": [u, "CO$"], "CUP": [u, "CU$"], "DOP": [u, "DO$"], "DZD": ["د.ج.‏"], "EGP": ["ج.م.‏", "E£"], "FJD": [u, "FJ$"], "GBP": ["UK£"], "GYD": [u, "GY$"], "HKD": ["HK$"], "IQD": ["د.ع.‏"], "IRR": ["ر.إ."], "JMD": [u, "JM$"], "JOD": ["د.أ.‏"], "JPY": ["JP¥"], "KWD": ["د.ك.‏"], "KYD": [u, "KY$"], "LBP": ["ل.ل.‏", "L£"], "LRD": [u, "$LR"], "LYD": ["د.ل.‏"], "MAD": ["د.م.‏"], "MRU": ["أ.م."], "MXN": ["MX$"], "NZD": ["NZ$"], "OMR": ["ر.ع.‏"], "PHP": [u, "₱"], "QAR": ["ر.ق.‏"], "SAR": ["ر.س.‏"], "SBD": [u, "SB$"], "SDD": ["د.س.‏"], "SDG": ["ج.س."], "SRD": [u, "SR$"], "SYP": ["ل.س.‏", "£"], "THB": ["฿"], "TND": ["د.ت.‏"], "TTD": [u, "TT$"], "TWD": ["NT$"], "USD": ["US$"], "UYU": [u, "UY$"], "YER": ["ر.ي.‏"] }, "rtl", plural];
//# sourceMappingURL=data:application/json;base64,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