/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { InjectionToken } from '@angular/core';
/**
 * Thumb types: range slider has two thumbs (START, END) whereas single point
 * slider only has one thumb (END).
 */
export var _MatThumb;
(function (_MatThumb) {
    _MatThumb[_MatThumb["START"] = 1] = "START";
    _MatThumb[_MatThumb["END"] = 2] = "END";
})(_MatThumb || (_MatThumb = {}));
/** Tick mark enum, for discrete sliders. */
export var _MatTickMark;
(function (_MatTickMark) {
    _MatTickMark[_MatTickMark["ACTIVE"] = 0] = "ACTIVE";
    _MatTickMark[_MatTickMark["INACTIVE"] = 1] = "INACTIVE";
})(_MatTickMark || (_MatTickMark = {}));
/**
 * Injection token that can be used for a `MatSlider` to provide itself as a
 * parent to the `MatSliderThumb` and `MatSliderRangeThumb`.
 * Used primarily to avoid circular imports.
 * @docs-private
 */
export const MAT_SLIDER = new InjectionToken('_MatSlider');
/**
 * Injection token that can be used to query for a `MatSliderThumb`.
 * Used primarily to avoid circular imports.
 * @docs-private
 */
export const MAT_SLIDER_THUMB = new InjectionToken('_MatSliderThumb');
/**
 * Injection token that can be used to query for a `MatSliderRangeThumb`.
 * Used primarily to avoid circular imports.
 * @docs-private
 */
export const MAT_SLIDER_RANGE_THUMB = new InjectionToken('_MatSliderRangeThumb');
/**
 * Injection token that can be used to query for a `MatSliderVisualThumb`.
 * Used primarily to avoid circular imports.
 * @docs-private
 */
export const MAT_SLIDER_VISUAL_THUMB = new InjectionToken('_MatSliderVisualThumb');
/**
 * A simple change event emitted by the MatSlider component.
 * @deprecated Use event bindings directly on the MatSliderThumbs for `change` and `input` events. See https://material.angular.io/guide/mdc-migration for information about migrating.
 * @breaking-change 17.0.0
 */
export class MatSliderChange {
}
//# sourceMappingURL=data:application/json;base64,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