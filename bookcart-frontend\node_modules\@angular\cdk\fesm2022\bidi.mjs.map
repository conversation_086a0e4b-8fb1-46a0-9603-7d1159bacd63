{"version": 3, "file": "bidi.mjs", "sources": ["../../../../../../src/cdk/bidi/dir-document-token.ts", "../../../../../../src/cdk/bidi/directionality.ts", "../../../../../../src/cdk/bidi/dir.ts", "../../../../../../src/cdk/bidi/bidi-module.ts", "../../../../../../src/cdk/bidi/bidi_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {DOCUMENT} from '@angular/common';\nimport {inject, InjectionToken} from '@angular/core';\n\n/**\n * Injection token used to inject the document into Directionality.\n * This is used so that the value can be faked in tests.\n *\n * We can't use the real document in tests because changing the real `dir` causes geometry-based\n * tests in Safari to fail.\n *\n * We also can't re-provide the DOCUMENT token from platform-browser because the unit tests\n * themselves use things like `querySelector` in test code.\n *\n * This token is defined in a separate file from Directionality as a workaround for\n * https://github.com/angular/angular/issues/22559\n *\n * @docs-private\n */\nexport const DIR_DOCUMENT = new InjectionToken<Document>('cdk-dir-doc', {\n  providedIn: 'root',\n  factory: DIR_DOCUMENT_FACTORY,\n});\n\n/** @docs-private */\nexport function DIR_DOCUMENT_FACTORY(): Document {\n  return inject(DOCUMENT);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {EventEmitter, Inject, Injectable, Optional, OnDestroy} from '@angular/core';\nimport {DIR_DOCUMENT} from './dir-document-token';\n\nexport type Direction = 'ltr' | 'rtl';\n\n/** Regex that matches locales with an RTL script. Taken from `goog.i18n.bidi.isRtlLanguage`. */\nconst RTL_LOCALE_PATTERN =\n  /^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;\n\n/** Resolves a string value to a specific direction. */\nexport function _resolveDirectionality(rawValue: string): Direction {\n  const value = rawValue?.toLowerCase() || '';\n\n  if (value === 'auto' && typeof navigator !== 'undefined' && navigator?.language) {\n    return RTL_LOCALE_PATTERN.test(navigator.language) ? 'rtl' : 'ltr';\n  }\n\n  return value === 'rtl' ? 'rtl' : 'ltr';\n}\n\n/**\n * The directionality (LTR / RTL) context for the application (or a subtree of it).\n * Exposes the current direction and a stream of direction changes.\n */\n@Injectable({providedIn: 'root'})\nexport class Directionality implements OnDestroy {\n  /** The current 'ltr' or 'rtl' value. */\n  readonly value: Direction = 'ltr';\n\n  /** Stream that emits whenever the 'ltr' / 'rtl' state changes. */\n  readonly change = new EventEmitter<Direction>();\n\n  constructor(@Optional() @Inject(DIR_DOCUMENT) _document?: any) {\n    if (_document) {\n      const bodyDir = _document.body ? _document.body.dir : null;\n      const htmlDir = _document.documentElement ? _document.documentElement.dir : null;\n      this.value = _resolveDirectionality(bodyDir || htmlDir || 'ltr');\n    }\n  }\n\n  ngOnDestroy() {\n    this.change.complete();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, Output, Input, EventEmitter, AfterContentInit, OnDestroy} from '@angular/core';\n\nimport {Direction, Directionality, _resolveDirectionality} from './directionality';\n\n/**\n * Directive to listen for changes of direction of part of the DOM.\n *\n * Provides itself as Directionality such that descendant directives only need to ever inject\n * Directionality to get the closest direction.\n */\n@Directive({\n  selector: '[dir]',\n  providers: [{provide: Directionality, useExisting: Dir}],\n  host: {'[attr.dir]': '_rawDir'},\n  exportAs: 'dir',\n  standalone: true,\n})\nexport class Dir implements Directionality, AfterContentInit, OnDestroy {\n  /** Normalized direction that accounts for invalid/unsupported values. */\n  private _dir: Direction = 'ltr';\n\n  /** Whether the `value` has been set to its initial value. */\n  private _isInitialized: boolean = false;\n\n  /** Direction as passed in by the consumer. */\n  _rawDir: string;\n\n  /** Event emitted when the direction changes. */\n  @Output('dirChange') readonly change = new EventEmitter<Direction>();\n\n  /** @docs-private */\n  @Input()\n  get dir(): Direction {\n    return this._dir;\n  }\n  set dir(value: Direction | 'auto') {\n    const previousValue = this._dir;\n\n    // Note: `_resolveDirectionality` resolves the language based on the browser's language,\n    // whereas the browser does it based on the content of the element. Since doing so based\n    // on the content can be expensive, for now we're doing the simpler matching.\n    this._dir = _resolveDirectionality(value);\n    this._rawDir = value;\n\n    if (previousValue !== this._dir && this._isInitialized) {\n      this.change.emit(this._dir);\n    }\n  }\n\n  /** Current layout direction of the element. */\n  get value(): Direction {\n    return this.dir;\n  }\n\n  /** Initialize once default value has been set. */\n  ngAfterContentInit() {\n    this._isInitialized = true;\n  }\n\n  ngOnDestroy() {\n    this.change.complete();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {Dir} from './dir';\n\n@NgModule({\n  imports: [Dir],\n  exports: [Dir],\n})\nexport class BidiModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;AAWA;;;;;;;;;;;;;;AAcG;MACU,YAAY,GAAG,IAAI,cAAc,CAAW,aAAa,EAAE;AACtE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,oBAAoB;AAC9B,CAAA,EAAE;AAEH;SACgB,oBAAoB,GAAA;AAClC,IAAA,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC1B;;ACrBA;AACA,MAAM,kBAAkB,GACtB,oHAAoH,CAAC;AAEvH;AACM,SAAU,sBAAsB,CAAC,QAAgB,EAAA;IACrD,MAAM,KAAK,GAAG,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;AAE5C,IAAA,IAAI,KAAK,KAAK,MAAM,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE,QAAQ,EAAE;AAC/E,QAAA,OAAO,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC;KACpE;IAED,OAAO,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACzC,CAAC;AAED;;;AAGG;MAEU,cAAc,CAAA;AAOzB,IAAA,WAAA,CAA8C,SAAe,EAAA;;QALpD,IAAK,CAAA,KAAA,GAAc,KAAK,CAAC;;AAGzB,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,YAAY,EAAa,CAAC;QAG9C,IAAI,SAAS,EAAE;AACb,YAAA,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;AAC3D,YAAA,MAAM,OAAO,GAAG,SAAS,CAAC,eAAe,GAAG,SAAS,CAAC,eAAe,CAAC,GAAG,GAAG,IAAI,CAAC;YACjF,IAAI,CAAC,KAAK,GAAG,sBAAsB,CAAC,OAAO,IAAI,OAAO,IAAI,KAAK,CAAC,CAAC;SAClE;KACF;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;KACxB;AAjBU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,kBAOO,YAAY,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAPjC,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,cADF,MAAM,EAAA,CAAA,CAAA,EAAA;;2FAClB,cAAc,EAAA,UAAA,EAAA,CAAA;kBAD1B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;0BAQjB,QAAQ;;0BAAI,MAAM;2BAAC,YAAY,CAAA;;;AC5B9C;;;;;AAKG;MAQU,GAAG,CAAA;AAPhB,IAAA,WAAA,GAAA;;QASU,IAAI,CAAA,IAAA,GAAc,KAAK,CAAC;;QAGxB,IAAc,CAAA,cAAA,GAAY,KAAK,CAAC;;AAMV,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,YAAY,EAAa,CAAC;AAkCtE,KAAA;;AA/BC,IAAA,IACI,GAAG,GAAA;QACL,OAAO,IAAI,CAAC,IAAI,CAAC;KAClB;IACD,IAAI,GAAG,CAAC,KAAyB,EAAA;AAC/B,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC;;;;AAKhC,QAAA,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC,KAAK,CAAC,CAAC;AAC1C,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QAErB,IAAI,aAAa,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE;YACtD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC7B;KACF;;AAGD,IAAA,IAAI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,GAAG,CAAC;KACjB;;IAGD,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;KAC5B;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;KACxB;8GA5CU,GAAG,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAH,GAAG,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,OAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,KAAA,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,WAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,UAAA,EAAA,SAAA,EAAA,EAAA,EAAA,SAAA,EALH,CAAC,EAAC,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,GAAG,EAAC,CAAC,EAAA,QAAA,EAAA,CAAA,KAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAK7C,GAAG,EAAA,UAAA,EAAA,CAAA;kBAPf,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,OAAO;oBACjB,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,cAAc,EAAE,WAAW,EAAK,GAAA,EAAC,CAAC;AACxD,oBAAA,IAAI,EAAE,EAAC,YAAY,EAAE,SAAS,EAAC;AAC/B,oBAAA,QAAQ,EAAE,KAAK;AACf,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;8BAY+B,MAAM,EAAA,CAAA;sBAAnC,MAAM;uBAAC,WAAW,CAAA;gBAIf,GAAG,EAAA,CAAA;sBADN,KAAK;;;MCxBK,UAAU,CAAA;8GAAV,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;+GAAV,UAAU,EAAA,OAAA,EAAA,CAHX,GAAG,CAAA,EAAA,OAAA,EAAA,CACH,GAAG,CAAA,EAAA,CAAA,CAAA,EAAA;+GAEF,UAAU,EAAA,CAAA,CAAA,EAAA;;2FAAV,UAAU,EAAA,UAAA,EAAA,CAAA;kBAJtB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,GAAG,CAAC;oBACd,OAAO,EAAE,CAAC,GAAG,CAAC;AACf,iBAAA,CAAA;;;ACdD;;AAEG;;;;"}