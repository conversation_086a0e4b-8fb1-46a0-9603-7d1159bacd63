/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["ka", [["a", "p"], ["AM", "PM"], u], [["AM", "PM"], u, u], [["კ", "ო", "ს", "ო", "ხ", "პ", "შ"], ["კვი", "ორშ", "სამ", "ოთხ", "ხუთ", "პარ", "შაბ"], ["კვირა", "ორშაბათი", "სამშაბათი", "ოთხშაბათი", "ხუთშაბათი", "პარასკევი", "შაბათი"], ["კვ", "ორ", "სმ", "ოთ", "ხთ", "პრ", "შბ"]], u, [["ი", "თ", "მ", "ა", "მ", "ი", "ი", "ა", "ს", "ო", "ნ", "დ"], ["იან", "თებ", "მარ", "აპრ", "მაი", "ივნ", "ივლ", "აგვ", "სექ", "ოქტ", "ნოე", "დეკ"], ["იანვარი", "თებერვალი", "მარტი", "აპრილი", "მაისი", "ივნისი", "ივლისი", "აგვისტო", "სექტემბერი", "ოქტომბერი", "ნოემბერი", "დეკემბერი"]], u, [["ძვ. წ.", "ახ. წ."], u, ["ძველი წელთაღრიცხვით", "ახალი წელთაღრიცხვით"]], 1, [6, 0], ["dd.MM.yy", "d MMM. y", "d MMMM, y", "EEEE, dd MMMM, y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1}, {0}", u, u, u], [",", " ", ";", "%", "+", "-", "E", "×", "‰", "∞", "არ არის რიცხვი", ":"], ["#,##0.###", "#,##0%", "#,##0.00 ¤", "#E0"], "GEL", "₾", "ქართული ლარი", { "AUD": [u, "$"], "BYN": [u, "р."], "CNY": [u, "¥"], "GEL": ["₾"], "HKD": [u, "$"], "ILS": [u, "₪"], "INR": [u, "₹"], "JPY": [u, "¥"], "KRW": [u, "₩"], "NZD": [u, "$"], "PHP": [u, "₱"], "TWD": ["NT$"], "USD": ["US$", "$"], "VND": [u, "₫"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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