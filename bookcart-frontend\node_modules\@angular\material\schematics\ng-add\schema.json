{"$schema": "http://json-schema.org/draft-07/schema", "$id": "angular-material-ng-add", "title": "Angular Material ng-add schematic", "type": "object", "properties": {"project": {"type": "string", "description": "Name of the project.", "$default": {"$source": "projectName"}}, "theme": {"description": "The theme to apply", "type": "string", "default": "indigo-pink", "x-prompt": {"message": "Choose a prebuilt theme name, or \"custom\" for a custom theme:", "type": "list", "items": [{"value": "indigo-pink", "label": "Indigo/Pink        [ Preview: https://material.angular.io?theme=indigo-pink ]"}, {"value": "deeppurple-amber", "label": "Deep Purple/Amber  [ Preview: https://material.angular.io?theme=deeppurple-amber ]"}, {"value": "pink-bluegrey", "label": "Pink/Blue Grey     [ Preview: https://material.angular.io?theme=pink-bluegrey ]"}, {"value": "purple-green", "label": "Purple/Green       [ Preview: https://material.angular.io?theme=purple-green ]"}, {"value": "custom", "label": "Custom"}]}}, "typography": {"type": "boolean", "default": false, "description": "Whether to set up global typography styles.", "x-prompt": "Set up global Angular Material typography styles?"}, "animations": {"type": "string", "default": "enabled", "description": "Whether Angular browser animations should be included.", "x-prompt": {"message": "Include the Angular animations module?", "type": "list", "items": [{"value": "enabled", "label": "Include and enable animations"}, {"value": "disabled", "label": "Include, but disable animations"}, {"value": "excluded", "label": "Do not include"}]}}}, "required": []}