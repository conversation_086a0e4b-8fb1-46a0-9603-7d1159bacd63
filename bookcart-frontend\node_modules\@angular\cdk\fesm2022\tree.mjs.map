{"version": 3, "file": "tree.mjs", "sources": ["../../../../../../src/cdk/tree/control/base-tree-control.ts", "../../../../../../src/cdk/tree/control/flat-tree-control.ts", "../../../../../../src/cdk/tree/control/nested-tree-control.ts", "../../../../../../src/cdk/tree/outlet.ts", "../../../../../../src/cdk/tree/node.ts", "../../../../../../src/cdk/tree/tree-errors.ts", "../../../../../../src/cdk/tree/tree.ts", "../../../../../../src/cdk/tree/nested-node.ts", "../../../../../../src/cdk/tree/padding.ts", "../../../../../../src/cdk/tree/toggle.ts", "../../../../../../src/cdk/tree/tree-module.ts", "../../../../../../src/cdk/tree/tree_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {SelectionModel} from '@angular/cdk/collections';\nimport {Observable} from 'rxjs';\nimport {TreeControl} from './tree-control';\n\n/** Base tree control. It has basic toggle/expand/collapse operations on a single data node. */\nexport abstract class BaseTreeControl<T, K = T> implements TreeControl<T, K> {\n  /** Gets a list of descendent data nodes of a subtree rooted at given data node recursively. */\n  abstract getDescendants(dataNode: T): T[];\n\n  /** Expands all data nodes in the tree. */\n  abstract expandAll(): void;\n\n  /** Saved data node for `expandAll` action. */\n  dataNodes: T[];\n\n  /** A selection model with multi-selection to track expansion status. */\n  expansionModel: SelectionModel<K> = new SelectionModel<K>(true);\n\n  /**\n   * Returns the identifier by which a dataNode should be tracked, should its\n   * reference change.\n   *\n   * Similar to trackBy for *ngFor\n   */\n  trackBy?: (dataNode: T) => K;\n\n  /** Get depth of a given data node, return the level number. This is for flat tree node. */\n  getLevel: (dataNode: T) => number;\n\n  /**\n   * Whether the data node is expandable. Returns true if expandable.\n   * This is for flat tree node.\n   */\n  isExpandable: (dataNode: T) => boolean;\n\n  /** Gets a stream that emits whenever the given data node's children change. */\n  getChildren: (dataNode: T) => Observable<T[]> | T[] | undefined | null;\n\n  /** Toggles one single data node's expanded/collapsed state. */\n  toggle(dataNode: T): void {\n    this.expansionModel.toggle(this._trackByValue(dataNode));\n  }\n\n  /** Expands one single data node. */\n  expand(dataNode: T): void {\n    this.expansionModel.select(this._trackByValue(dataNode));\n  }\n\n  /** Collapses one single data node. */\n  collapse(dataNode: T): void {\n    this.expansionModel.deselect(this._trackByValue(dataNode));\n  }\n\n  /** Whether a given data node is expanded or not. Returns true if the data node is expanded. */\n  isExpanded(dataNode: T): boolean {\n    return this.expansionModel.isSelected(this._trackByValue(dataNode));\n  }\n\n  /** Toggles a subtree rooted at `node` recursively. */\n  toggleDescendants(dataNode: T): void {\n    this.expansionModel.isSelected(this._trackByValue(dataNode))\n      ? this.collapseDescendants(dataNode)\n      : this.expandDescendants(dataNode);\n  }\n\n  /** Collapse all dataNodes in the tree. */\n  collapseAll(): void {\n    this.expansionModel.clear();\n  }\n\n  /** Expands a subtree rooted at given data node recursively. */\n  expandDescendants(dataNode: T): void {\n    let toBeProcessed = [dataNode];\n    toBeProcessed.push(...this.getDescendants(dataNode));\n    this.expansionModel.select(...toBeProcessed.map(value => this._trackByValue(value)));\n  }\n\n  /** Collapses a subtree rooted at given data node recursively. */\n  collapseDescendants(dataNode: T): void {\n    let toBeProcessed = [dataNode];\n    toBeProcessed.push(...this.getDescendants(dataNode));\n    this.expansionModel.deselect(...toBeProcessed.map(value => this._trackByValue(value)));\n  }\n\n  protected _trackByValue(value: T | K): K {\n    return this.trackBy ? this.trackBy(value as T) : (value as K);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {BaseTreeControl} from './base-tree-control';\n\n/** Optional set of configuration that can be provided to the FlatTreeControl. */\nexport interface FlatTreeControlOptions<T, K> {\n  trackBy?: (dataNode: T) => K;\n}\n\n/** Flat tree control. Able to expand/collapse a subtree recursively for flattened tree. */\nexport class FlatTreeControl<T, K = T> extends BaseTreeControl<T, K> {\n  /** Construct with flat tree data node functions getLevel and isExpandable. */\n  constructor(\n    public override getLevel: (dataNode: T) => number,\n    public override isExpandable: (dataNode: T) => boolean,\n    public options?: FlatTreeControlOptions<T, K>,\n  ) {\n    super();\n\n    if (this.options) {\n      this.trackBy = this.options.trackBy;\n    }\n  }\n\n  /**\n   * Gets a list of the data node's subtree of descendent data nodes.\n   *\n   * To make this working, the `dataNodes` of the TreeControl must be flattened tree nodes\n   * with correct levels.\n   */\n  getDescendants(dataNode: T): T[] {\n    const startIndex = this.dataNodes.indexOf(dataNode);\n    const results: T[] = [];\n\n    // Goes through flattened tree nodes in the `dataNodes` array, and get all descendants.\n    // The level of descendants of a tree node must be greater than the level of the given\n    // tree node.\n    // If we reach a node whose level is equal to the level of the tree node, we hit a sibling.\n    // If we reach a node whose level is greater than the level of the tree node, we hit a\n    // sibling of an ancestor.\n    for (\n      let i = startIndex + 1;\n      i < this.dataNodes.length && this.getLevel(dataNode) < this.getLevel(this.dataNodes[i]);\n      i++\n    ) {\n      results.push(this.dataNodes[i]);\n    }\n    return results;\n  }\n\n  /**\n   * Expands all data nodes in the tree.\n   *\n   * To make this working, the `dataNodes` variable of the TreeControl must be set to all flattened\n   * data nodes of the tree.\n   */\n  expandAll(): void {\n    this.expansionModel.select(...this.dataNodes.map(node => this._trackByValue(node)));\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {Observable, isObservable} from 'rxjs';\nimport {take, filter} from 'rxjs/operators';\nimport {BaseTreeControl} from './base-tree-control';\n\n/** Optional set of configuration that can be provided to the NestedTreeControl. */\nexport interface NestedTreeControlOptions<T, K> {\n  trackBy?: (dataNode: T) => K;\n}\n\n/** Nested tree control. Able to expand/collapse a subtree recursively for NestedNode type. */\nexport class NestedTreeControl<T, K = T> extends BaseTreeControl<T, K> {\n  /** Construct with nested tree function getChildren. */\n  constructor(\n    public override getChildren: (dataNode: T) => Observable<T[]> | T[] | undefined | null,\n    public options?: NestedTreeControlOptions<T, K>,\n  ) {\n    super();\n\n    if (this.options) {\n      this.trackBy = this.options.trackBy;\n    }\n  }\n\n  /**\n   * Expands all dataNodes in the tree.\n   *\n   * To make this working, the `dataNodes` variable of the TreeControl must be set to all root level\n   * data nodes of the tree.\n   */\n  expandAll(): void {\n    this.expansionModel.clear();\n    const allNodes = this.dataNodes.reduce(\n      (accumulator: T[], dataNode) => [...accumulator, ...this.getDescendants(dataNode), dataNode],\n      [],\n    );\n    this.expansionModel.select(...allNodes.map(node => this._trackByValue(node)));\n  }\n\n  /** Gets a list of descendant dataNodes of a subtree rooted at given data node recursively. */\n  getDescendants(dataNode: T): T[] {\n    const descendants: T[] = [];\n\n    this._getDescendants(descendants, dataNode);\n    // Remove the node itself\n    return descendants.splice(1);\n  }\n\n  /** A helper function to get descendants recursively. */\n  protected _getDescendants(descendants: T[], dataNode: T): void {\n    descendants.push(dataNode);\n    const childrenNodes = this.getChildren(dataNode);\n    if (Array.isArray(childrenNodes)) {\n      childrenNodes.forEach((child: T) => this._getDescendants(descendants, child));\n    } else if (isObservable(childrenNodes)) {\n      // TypeScript as of version 3.5 doesn't seem to treat `Boolean` like a function that\n      // returns a `boolean` specifically in the context of `filter`, so we manually clarify that.\n      childrenNodes.pipe(take(1), filter(Boolean as () => boolean)).subscribe(children => {\n        for (const child of children) {\n          this._getDescendants(descendants, child);\n        }\n      });\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {Directive, Inject, InjectionToken, Optional, ViewContainerRef} from '@angular/core';\n\n/**\n * Injection token used to provide a `CdkTreeNode` to its outlet.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nexport const CDK_TREE_NODE_OUTLET_NODE = new InjectionToken<{}>('CDK_TREE_NODE_OUTLET_NODE');\n\n/**\n * Outlet for nested CdkNode. Put `[cdkTreeNodeOutlet]` on a tag to place children dataNodes\n * inside the outlet.\n */\n@Directive({\n  selector: '[cdkTreeNodeOutlet]',\n  standalone: true,\n})\nexport class CdkTreeNodeOutlet {\n  constructor(\n    public viewContainer: ViewContainerRef,\n    @Inject(CDK_TREE_NODE_OUTLET_NODE) @Optional() public _node?: any,\n  ) {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, TemplateRef} from '@angular/core';\n\n/** Context provided to the tree node component. */\nexport class CdkTreeNodeOutletContext<T> {\n  /** Data for the node. */\n  $implicit: T;\n\n  /** Depth of the node. */\n  level: number;\n\n  /** Index location of the node. */\n  index?: number;\n\n  /** Length of the number of total dataNodes. */\n  count?: number;\n\n  constructor(data: T) {\n    this.$implicit = data;\n  }\n}\n\n/**\n * Data node definition for the CdkTree.\n * Captures the node's template and a when predicate that describes when this node should be used.\n */\n@Directive({\n  selector: '[cdkTreeNodeDef]',\n  inputs: [{name: 'when', alias: 'cdkTreeNodeDefWhen'}],\n  standalone: true,\n})\nexport class CdkTreeNodeDef<T> {\n  /**\n   * Function that should return true if this node template should be used for the provided node\n   * data and index. If left undefined, this node will be considered the default node template to\n   * use when no other when functions return true for the data.\n   * For every node, there must be at least one when function that passes or an undefined to\n   * default.\n   */\n  when: (index: number, nodeData: T) => boolean;\n\n  /** @docs-private */\n  constructor(public template: TemplateRef<any>) {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Returns an error to be thrown when there is no usable data.\n * @docs-private\n */\nexport function getTreeNoValidDataSourceError() {\n  return Error(`A valid data source must be provided.`);\n}\n\n/**\n * Returns an error to be thrown when there are multiple nodes that are missing a when function.\n * @docs-private\n */\nexport function getTreeMultipleDefaultNodeDefsError() {\n  return Error(`There can only be one default row without a when predicate function.`);\n}\n\n/**\n * Returns an error to be thrown when there are no matching node defs for a particular set of data.\n * @docs-private\n */\nexport function getTreeMissingMatchingNodeDefError() {\n  return Error(`Could not find a matching node definition for the provided node data.`);\n}\n\n/**\n * Returns an error to be thrown when there are tree control.\n * @docs-private\n */\nexport function getTreeControlMissingError() {\n  return Error(`Could not find a tree control for the tree.`);\n}\n\n/**\n * Returns an error to be thrown when tree control did not implement functions for flat/nested node.\n * @docs-private\n */\nexport function getTreeControlFunctionsMissingError() {\n  return Error(`Could not find functions for nested/flat tree in tree control.`);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {FocusableOption} from '@angular/cdk/a11y';\nimport {CollectionViewer, DataSource, isDataSource} from '@angular/cdk/collections';\nimport {\n  AfterContentChecked,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChildren,\n  Directive,\n  ElementRef,\n  Input,\n  IterableChangeRecord,\n  IterableDiffer,\n  IterableDiffers,\n  OnDestroy,\n  OnInit,\n  QueryList,\n  TrackByFunction,\n  ViewChild,\n  ViewContainerRef,\n  ViewEncapsulation,\n  numberAttribute,\n} from '@angular/core';\nimport {\n  BehaviorSubject,\n  isObservable,\n  Observable,\n  of as observableOf,\n  Subject,\n  Subscription,\n} from 'rxjs';\nimport {takeUntil} from 'rxjs/operators';\nimport {TreeControl} from './control/tree-control';\nimport {CdkTreeNodeDef, CdkTreeNodeOutletContext} from './node';\nimport {CdkTreeNodeOutlet} from './outlet';\nimport {\n  getTreeControlFunctionsMissingError,\n  getTreeControlMissingError,\n  getTreeMissingMatchingNodeDefError,\n  getTreeMultipleDefaultNodeDefsError,\n  getTreeNoValidDataSourceError,\n} from './tree-errors';\n\n/**\n * CDK tree component that connects with a data source to retrieve data of type `T` and renders\n * dataNodes with hierarchy. Updates the dataNodes when new data is provided by the data source.\n */\n@Component({\n  selector: 'cdk-tree',\n  exportAs: 'cdkTree',\n  template: `<ng-container cdkTreeNodeOutlet></ng-container>`,\n  host: {\n    'class': 'cdk-tree',\n    'role': 'tree',\n  },\n  encapsulation: ViewEncapsulation.None,\n  // The \"OnPush\" status for the `CdkTree` component is effectively a noop, so we are removing it.\n  // The view for `CdkTree` consists entirely of templates declared in other views. As they are\n  // declared elsewhere, they are checked when their declaration points are checked.\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  standalone: true,\n  imports: [CdkTreeNodeOutlet],\n})\nexport class CdkTree<T, K = T> implements AfterContentChecked, CollectionViewer, OnDestroy, OnInit {\n  /** Subject that emits when the component has been destroyed. */\n  private readonly _onDestroy = new Subject<void>();\n\n  /** Differ used to find the changes in the data provided by the data source. */\n  private _dataDiffer: IterableDiffer<T>;\n\n  /** Stores the node definition that does not have a when predicate. */\n  private _defaultNodeDef: CdkTreeNodeDef<T> | null;\n\n  /** Data subscription */\n  private _dataSubscription: Subscription | null;\n\n  /** Level of nodes */\n  private _levels: Map<T, number> = new Map<T, number>();\n\n  /**\n   * Provides a stream containing the latest data array to render. Influenced by the tree's\n   * stream of view window (what dataNodes are currently on screen).\n   * Data source can be an observable of data array, or a data array to render.\n   */\n  @Input()\n  get dataSource(): DataSource<T> | Observable<T[]> | T[] {\n    return this._dataSource;\n  }\n  set dataSource(dataSource: DataSource<T> | Observable<T[]> | T[]) {\n    if (this._dataSource !== dataSource) {\n      this._switchDataSource(dataSource);\n    }\n  }\n  private _dataSource: DataSource<T> | Observable<T[]> | T[];\n\n  /** The tree controller */\n  @Input() treeControl: TreeControl<T, K>;\n\n  /**\n   * Tracking function that will be used to check the differences in data changes. Used similarly\n   * to `ngFor` `trackBy` function. Optimize node operations by identifying a node based on its data\n   * relative to the function to know if a node should be added/removed/moved.\n   * Accepts a function that takes two parameters, `index` and `item`.\n   */\n  @Input() trackBy: TrackByFunction<T>;\n\n  // Outlets within the tree's template where the dataNodes will be inserted.\n  @ViewChild(CdkTreeNodeOutlet, {static: true}) _nodeOutlet: CdkTreeNodeOutlet;\n\n  /** The tree node template for the tree */\n  @ContentChildren(CdkTreeNodeDef, {\n    // We need to use `descendants: true`, because Ivy will no longer match\n    // indirect descendants if it's left as false.\n    descendants: true,\n  })\n  _nodeDefs: QueryList<CdkTreeNodeDef<T>>;\n\n  // TODO(tinayuangao): Setup a listener for scrolling, emit the calculated view to viewChange.\n  //     Remove the MAX_VALUE in viewChange\n  /**\n   * Stream containing the latest information on what rows are being displayed on screen.\n   * Can be used by the data source to as a heuristic of what data should be provided.\n   */\n  readonly viewChange = new BehaviorSubject<{start: number; end: number}>({\n    start: 0,\n    end: Number.MAX_VALUE,\n  });\n\n  constructor(\n    private _differs: IterableDiffers,\n    private _changeDetectorRef: ChangeDetectorRef,\n  ) {}\n\n  ngOnInit() {\n    this._dataDiffer = this._differs.find([]).create(this.trackBy);\n    if (!this.treeControl && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTreeControlMissingError();\n    }\n  }\n\n  ngOnDestroy() {\n    this._nodeOutlet.viewContainer.clear();\n\n    this.viewChange.complete();\n    this._onDestroy.next();\n    this._onDestroy.complete();\n\n    if (this._dataSource && typeof (this._dataSource as DataSource<T>).disconnect === 'function') {\n      (this.dataSource as DataSource<T>).disconnect(this);\n    }\n\n    if (this._dataSubscription) {\n      this._dataSubscription.unsubscribe();\n      this._dataSubscription = null;\n    }\n  }\n\n  ngAfterContentChecked() {\n    const defaultNodeDefs = this._nodeDefs.filter(def => !def.when);\n    if (defaultNodeDefs.length > 1 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTreeMultipleDefaultNodeDefsError();\n    }\n    this._defaultNodeDef = defaultNodeDefs[0];\n\n    if (this.dataSource && this._nodeDefs && !this._dataSubscription) {\n      this._observeRenderChanges();\n    }\n  }\n\n  // TODO(tinayuangao): Work on keyboard traversal and actions, make sure it's working for RTL\n  //     and nested trees.\n\n  /**\n   * Switch to the provided data source by resetting the data and unsubscribing from the current\n   * render change subscription if one exists. If the data source is null, interpret this by\n   * clearing the node outlet. Otherwise start listening for new data.\n   */\n  private _switchDataSource(dataSource: DataSource<T> | Observable<T[]> | T[]) {\n    if (this._dataSource && typeof (this._dataSource as DataSource<T>).disconnect === 'function') {\n      (this.dataSource as DataSource<T>).disconnect(this);\n    }\n\n    if (this._dataSubscription) {\n      this._dataSubscription.unsubscribe();\n      this._dataSubscription = null;\n    }\n\n    // Remove the all dataNodes if there is now no data source\n    if (!dataSource) {\n      this._nodeOutlet.viewContainer.clear();\n    }\n\n    this._dataSource = dataSource;\n    if (this._nodeDefs) {\n      this._observeRenderChanges();\n    }\n  }\n\n  /** Set up a subscription for the data provided by the data source. */\n  private _observeRenderChanges() {\n    let dataStream: Observable<readonly T[]> | undefined;\n\n    if (isDataSource(this._dataSource)) {\n      dataStream = this._dataSource.connect(this);\n    } else if (isObservable(this._dataSource)) {\n      dataStream = this._dataSource;\n    } else if (Array.isArray(this._dataSource)) {\n      dataStream = observableOf(this._dataSource);\n    }\n\n    if (dataStream) {\n      this._dataSubscription = dataStream\n        .pipe(takeUntil(this._onDestroy))\n        .subscribe(data => this.renderNodeChanges(data));\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getTreeNoValidDataSourceError();\n    }\n  }\n\n  /** Check for changes made in the data and render each change (node added/removed/moved). */\n  renderNodeChanges(\n    data: readonly T[],\n    dataDiffer: IterableDiffer<T> = this._dataDiffer,\n    viewContainer: ViewContainerRef = this._nodeOutlet.viewContainer,\n    parentData?: T,\n  ) {\n    const changes = dataDiffer.diff(data);\n    if (!changes) {\n      return;\n    }\n\n    changes.forEachOperation(\n      (\n        item: IterableChangeRecord<T>,\n        adjustedPreviousIndex: number | null,\n        currentIndex: number | null,\n      ) => {\n        if (item.previousIndex == null) {\n          this.insertNode(data[currentIndex!], currentIndex!, viewContainer, parentData);\n        } else if (currentIndex == null) {\n          viewContainer.remove(adjustedPreviousIndex!);\n          this._levels.delete(item.item);\n        } else {\n          const view = viewContainer.get(adjustedPreviousIndex!);\n          viewContainer.move(view!, currentIndex);\n        }\n      },\n    );\n\n    this._changeDetectorRef.detectChanges();\n  }\n\n  /**\n   * Finds the matching node definition that should be used for this node data. If there is only\n   * one node definition, it is returned. Otherwise, find the node definition that has a when\n   * predicate that returns true with the data. If none return true, return the default node\n   * definition.\n   */\n  _getNodeDef(data: T, i: number): CdkTreeNodeDef<T> {\n    if (this._nodeDefs.length === 1) {\n      return this._nodeDefs.first!;\n    }\n\n    const nodeDef =\n      this._nodeDefs.find(def => def.when && def.when(i, data)) || this._defaultNodeDef;\n\n    if (!nodeDef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTreeMissingMatchingNodeDefError();\n    }\n\n    return nodeDef!;\n  }\n\n  /**\n   * Create the embedded view for the data node template and place it in the correct index location\n   * within the data node view container.\n   */\n  insertNode(nodeData: T, index: number, viewContainer?: ViewContainerRef, parentData?: T) {\n    const node = this._getNodeDef(nodeData, index);\n\n    // Node context that will be provided to created embedded view\n    const context = new CdkTreeNodeOutletContext<T>(nodeData);\n\n    // If the tree is flat tree, then use the `getLevel` function in flat tree control\n    // Otherwise, use the level of parent node.\n    if (this.treeControl.getLevel) {\n      context.level = this.treeControl.getLevel(nodeData);\n    } else if (typeof parentData !== 'undefined' && this._levels.has(parentData)) {\n      context.level = this._levels.get(parentData)! + 1;\n    } else {\n      context.level = 0;\n    }\n    this._levels.set(nodeData, context.level);\n\n    // Use default tree nodeOutlet, or nested node's nodeOutlet\n    const container = viewContainer ? viewContainer : this._nodeOutlet.viewContainer;\n    container.createEmbeddedView(node.template, context, index);\n\n    // Set the data to just created `CdkTreeNode`.\n    // The `CdkTreeNode` created from `createEmbeddedView` will be saved in static variable\n    //     `mostRecentTreeNode`. We get it from static variable and pass the node data to it.\n    if (CdkTreeNode.mostRecentTreeNode) {\n      CdkTreeNode.mostRecentTreeNode.data = nodeData;\n    }\n  }\n}\n\n/**\n * Tree node for CdkTree. It contains the data in the tree node.\n */\n@Directive({\n  selector: 'cdk-tree-node',\n  exportAs: 'cdkTreeNode',\n  host: {\n    'class': 'cdk-tree-node',\n    '[attr.aria-expanded]': 'isExpanded',\n  },\n  standalone: true,\n})\nexport class CdkTreeNode<T, K = T> implements FocusableOption, OnDestroy, OnInit {\n  /**\n   * The role of the tree node.\n   * @deprecated The correct role is 'treeitem', 'group' should not be used. This input will be\n   *   removed in a future version.\n   * @breaking-change 12.0.0 Remove this input\n   */\n  @Input() get role(): 'treeitem' | 'group' {\n    return 'treeitem';\n  }\n\n  set role(_role: 'treeitem' | 'group') {\n    // TODO: move to host after View Engine deprecation\n    this._elementRef.nativeElement.setAttribute('role', _role);\n  }\n\n  /**\n   * The most recently created `CdkTreeNode`. We save it in static variable so we can retrieve it\n   * in `CdkTree` and set the data to it.\n   */\n  static mostRecentTreeNode: CdkTreeNode<any> | null = null;\n\n  /** Subject that emits when the component has been destroyed. */\n  protected readonly _destroyed = new Subject<void>();\n\n  /** Emits when the node's data has changed. */\n  readonly _dataChanges = new Subject<void>();\n\n  private _parentNodeAriaLevel: number;\n\n  /** The tree node's data. */\n  get data(): T {\n    return this._data;\n  }\n  set data(value: T) {\n    if (value !== this._data) {\n      this._data = value;\n      this._setRoleFromData();\n      this._dataChanges.next();\n    }\n  }\n  protected _data: T;\n\n  get isExpanded(): boolean {\n    return this._tree.treeControl.isExpanded(this._data);\n  }\n\n  get level(): number {\n    // If the treeControl has a getLevel method, use it to get the level. Otherwise read the\n    // aria-level off the parent node and use it as the level for this node (note aria-level is\n    // 1-indexed, while this property is 0-indexed, so we don't need to increment).\n    return this._tree.treeControl.getLevel\n      ? this._tree.treeControl.getLevel(this._data)\n      : this._parentNodeAriaLevel;\n  }\n\n  constructor(\n    protected _elementRef: ElementRef<HTMLElement>,\n    protected _tree: CdkTree<T, K>,\n  ) {\n    CdkTreeNode.mostRecentTreeNode = this as CdkTreeNode<T, K>;\n    this.role = 'treeitem';\n  }\n\n  ngOnInit(): void {\n    this._parentNodeAriaLevel = getParentNodeAriaLevel(this._elementRef.nativeElement);\n    this._elementRef.nativeElement.setAttribute('aria-level', `${this.level + 1}`);\n  }\n\n  ngOnDestroy() {\n    // If this is the last tree node being destroyed,\n    // clear out the reference to avoid leaking memory.\n    if (CdkTreeNode.mostRecentTreeNode === this) {\n      CdkTreeNode.mostRecentTreeNode = null;\n    }\n\n    this._dataChanges.complete();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n\n  /** Focuses the menu item. Implements for FocusableOption. */\n  focus(): void {\n    this._elementRef.nativeElement.focus();\n  }\n\n  // TODO: role should eventually just be set in the component host\n  protected _setRoleFromData(): void {\n    if (\n      !this._tree.treeControl.isExpandable &&\n      !this._tree.treeControl.getChildren &&\n      (typeof ngDevMode === 'undefined' || ngDevMode)\n    ) {\n      throw getTreeControlFunctionsMissingError();\n    }\n    this.role = 'treeitem';\n  }\n}\n\nfunction getParentNodeAriaLevel(nodeElement: HTMLElement): number {\n  let parent = nodeElement.parentElement;\n  while (parent && !isNodeElement(parent)) {\n    parent = parent.parentElement;\n  }\n  if (!parent) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw Error('Incorrect tree structure containing detached node.');\n    } else {\n      return -1;\n    }\n  } else if (parent.classList.contains('cdk-nested-tree-node')) {\n    return numberAttribute(parent.getAttribute('aria-level')!);\n  } else {\n    // The ancestor element is the cdk-tree itself\n    return 0;\n  }\n}\n\nfunction isNodeElement(element: HTMLElement) {\n  const classList = element.classList;\n  return !!(classList?.contains('cdk-nested-tree-node') || classList?.contains('cdk-tree'));\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {\n  AfterContentInit,\n  ContentChildren,\n  Directive,\n  ElementRef,\n  IterableDiffer,\n  IterableDiffers,\n  OnDestroy,\n  OnInit,\n  QueryList,\n} from '@angular/core';\nimport {isObservable} from 'rxjs';\nimport {takeUntil} from 'rxjs/operators';\n\nimport {CDK_TREE_NODE_OUTLET_NODE, CdkTreeNodeOutlet} from './outlet';\nimport {CdkTree, CdkTreeNode} from './tree';\nimport {getTreeControlFunctionsMissingError} from './tree-errors';\n\n/**\n * Nested node is a child of `<cdk-tree>`. It works with nested tree.\n * By using `cdk-nested-tree-node` component in tree node template, children of the parent node will\n * be added in the `cdkTreeNodeOutlet` in tree node template.\n * The children of node will be automatically added to `cdkTreeNodeOutlet`.\n */\n@Directive({\n  selector: 'cdk-nested-tree-node',\n  exportAs: 'cdkNestedTreeNode',\n  providers: [\n    {provide: CdkTreeNode, useExisting: CdkNestedTreeNode},\n    {provide: CDK_TREE_NODE_OUTLET_NODE, useExisting: CdkNestedTreeNode},\n  ],\n  host: {\n    'class': 'cdk-nested-tree-node',\n  },\n  standalone: true,\n})\nexport class CdkNestedTreeNode<T, K = T>\n  extends CdkTreeNode<T, K>\n  implements AfterContentInit, OnDestroy, OnInit\n{\n  /** Differ used to find the changes in the data provided by the data source. */\n  private _dataDiffer: IterableDiffer<T>;\n\n  /** The children data dataNodes of current node. They will be placed in `CdkTreeNodeOutlet`. */\n  protected _children: T[];\n\n  /** The children node placeholder. */\n  @ContentChildren(CdkTreeNodeOutlet, {\n    // We need to use `descendants: true`, because Ivy will no longer match\n    // indirect descendants if it's left as false.\n    descendants: true,\n  })\n  nodeOutlet: QueryList<CdkTreeNodeOutlet>;\n\n  constructor(\n    elementRef: ElementRef<HTMLElement>,\n    tree: CdkTree<T, K>,\n    protected _differs: IterableDiffers,\n  ) {\n    super(elementRef, tree);\n  }\n\n  ngAfterContentInit() {\n    this._dataDiffer = this._differs.find([]).create(this._tree.trackBy);\n    if (!this._tree.treeControl.getChildren && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTreeControlFunctionsMissingError();\n    }\n    const childrenNodes = this._tree.treeControl.getChildren(this.data);\n    if (Array.isArray(childrenNodes)) {\n      this.updateChildrenNodes(childrenNodes as T[]);\n    } else if (isObservable(childrenNodes)) {\n      childrenNodes\n        .pipe(takeUntil(this._destroyed))\n        .subscribe(result => this.updateChildrenNodes(result));\n    }\n    this.nodeOutlet.changes\n      .pipe(takeUntil(this._destroyed))\n      .subscribe(() => this.updateChildrenNodes());\n  }\n\n  // This is a workaround for https://github.com/angular/angular/issues/23091\n  // In aot mode, the lifecycle hooks from parent class are not called.\n  override ngOnInit() {\n    super.ngOnInit();\n  }\n\n  override ngOnDestroy() {\n    this._clear();\n    super.ngOnDestroy();\n  }\n\n  /** Add children dataNodes to the NodeOutlet */\n  protected updateChildrenNodes(children?: T[]): void {\n    const outlet = this._getNodeOutlet();\n    if (children) {\n      this._children = children;\n    }\n    if (outlet && this._children) {\n      const viewContainer = outlet.viewContainer;\n      this._tree.renderNodeChanges(this._children, this._dataDiffer, viewContainer, this._data);\n    } else {\n      // Reset the data differ if there's no children nodes displayed\n      this._dataDiffer.diff([]);\n    }\n  }\n\n  /** Clear the children dataNodes. */\n  protected _clear(): void {\n    const outlet = this._getNodeOutlet();\n    if (outlet) {\n      outlet.viewContainer.clear();\n      this._dataDiffer.diff([]);\n    }\n  }\n\n  /** Gets the outlet for the current node. */\n  private _getNodeOutlet() {\n    const outlets = this.nodeOutlet;\n\n    // Note that since we use `descendants: true` on the query, we have to ensure\n    // that we don't pick up the outlet of a child node by accident.\n    return outlets && outlets.find(outlet => !outlet._node || outlet._node === this);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directionality} from '@angular/cdk/bidi';\nimport {Directive, ElementRef, Input, numberAttribute, OnDestroy, Optional} from '@angular/core';\nimport {takeUntil} from 'rxjs/operators';\nimport {Subject} from 'rxjs';\nimport {CdkTree, CdkTreeNode} from './tree';\n\n/** Regex used to split a string on its CSS units. */\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n\n/**\n * Indent for the children tree dataNodes.\n * This directive will add left-padding to the node to show hierarchy.\n */\n@Directive({\n  selector: '[cdkTreeNodePadding]',\n  standalone: true,\n})\nexport class CdkTreeNodePadding<T, K = T> implements OnDestroy {\n  /** Current padding value applied to the element. Used to avoid unnecessarily hitting the DOM. */\n  private _currentPadding: string | null;\n\n  /** Subject that emits when the component has been destroyed. */\n  private readonly _destroyed = new Subject<void>();\n\n  /** CSS units used for the indentation value. */\n  indentUnits = 'px';\n\n  /** The level of depth of the tree node. The padding will be `level * indent` pixels. */\n  @Input({alias: 'cdkTreeNodePadding', transform: numberAttribute})\n  get level(): number {\n    return this._level;\n  }\n  set level(value: number) {\n    this._setLevelInput(value);\n  }\n  _level: number;\n\n  /**\n   * The indent for each level. Can be a number or a CSS string.\n   * Default number 40px from material design menu sub-menu spec.\n   */\n  @Input('cdkTreeNodePaddingIndent')\n  get indent(): number | string {\n    return this._indent;\n  }\n  set indent(indent: number | string) {\n    this._setIndentInput(indent);\n  }\n  _indent: number = 40;\n\n  constructor(\n    private _treeNode: CdkTreeNode<T, K>,\n    private _tree: CdkTree<T, K>,\n    private _element: ElementRef<HTMLElement>,\n    @Optional() private _dir: Directionality,\n  ) {\n    this._setPadding();\n    if (_dir) {\n      _dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => this._setPadding(true));\n    }\n\n    // In Ivy the indentation binding might be set before the tree node's data has been added,\n    // which means that we'll miss the first render. We have to subscribe to changes in the\n    // data to ensure that everything is up to date.\n    _treeNode._dataChanges.subscribe(() => this._setPadding());\n  }\n\n  ngOnDestroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n\n  /** The padding indent value for the tree node. Returns a string with px numbers if not null. */\n  _paddingIndent(): string | null {\n    const nodeLevel =\n      this._treeNode.data && this._tree.treeControl.getLevel\n        ? this._tree.treeControl.getLevel(this._treeNode.data)\n        : null;\n    const level = this._level == null ? nodeLevel : this._level;\n    return typeof level === 'number' ? `${level * this._indent}${this.indentUnits}` : null;\n  }\n\n  _setPadding(forceChange = false) {\n    const padding = this._paddingIndent();\n\n    if (padding !== this._currentPadding || forceChange) {\n      const element = this._element.nativeElement;\n      const paddingProp = this._dir && this._dir.value === 'rtl' ? 'paddingRight' : 'paddingLeft';\n      const resetProp = paddingProp === 'paddingLeft' ? 'paddingRight' : 'paddingLeft';\n      element.style[paddingProp] = padding || '';\n      element.style[resetProp] = '';\n      this._currentPadding = padding;\n    }\n  }\n\n  /**\n   * This has been extracted to a util because of TS 4 and VE.\n   * View Engine doesn't support property rename inheritance.\n   * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n   * @docs-private\n   */\n  protected _setLevelInput(value: number) {\n    // Set to null as the fallback value so that _setPadding can fall back to the node level if the\n    // consumer set the directive as `cdkTreeNodePadding=\"\"`. We still want to take this value if\n    // they set 0 explicitly.\n    this._level = isNaN(value) ? null! : value;\n    this._setPadding();\n  }\n\n  /**\n   * This has been extracted to a util because of TS 4 and VE.\n   * View Engine doesn't support property rename inheritance.\n   * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n   * @docs-private\n   */\n  protected _setIndentInput(indent: number | string) {\n    let value = indent;\n    let units = 'px';\n\n    if (typeof indent === 'string') {\n      const parts = indent.split(cssUnitPattern);\n      value = parts[0];\n      units = parts[1] || units;\n    }\n\n    this.indentUnits = units;\n    this._indent = numberAttribute(value);\n    this._setPadding();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, Input, booleanAttribute} from '@angular/core';\n\nimport {CdkTree, CdkTreeNode} from './tree';\n\n/**\n * Node toggle to expand/collapse the node.\n */\n@Directive({\n  selector: '[cdkTreeNodeToggle]',\n  host: {\n    '(click)': '_toggle($event)',\n  },\n  standalone: true,\n})\nexport class CdkTreeNodeToggle<T, K = T> {\n  /** Whether expand/collapse the node recursively. */\n  @Input({alias: 'cdkTreeNodeToggleRecursive', transform: booleanAttribute})\n  recursive: boolean = false;\n\n  constructor(\n    protected _tree: CdkTree<T, K>,\n    protected _treeNode: CdkTreeNode<T, K>,\n  ) {}\n\n  _toggle(event: Event): void {\n    this.recursive\n      ? this._tree.treeControl.toggleDescendants(this._treeNode.data)\n      : this._tree.treeControl.toggle(this._treeNode.data);\n\n    event.stopPropagation();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {CdkTreeNodeOutlet} from './outlet';\nimport {CdkTreeNodePadding} from './padding';\nimport {CdkTreeNodeToggle} from './toggle';\nimport {CdkTree, CdkTreeNode} from './tree';\nimport {CdkTreeNodeDef} from './node';\nimport {CdkNestedTreeNode} from './nested-node';\n\nconst EXPORTED_DECLARATIONS = [\n  CdkNestedTreeNode,\n  CdkTreeNodeDef,\n  CdkTreeNodePadding,\n  CdkTreeNodeToggle,\n  CdkTree,\n  CdkTreeNode,\n  CdkTreeNodeOutlet,\n];\n\n@NgModule({\n  imports: EXPORTED_DECLARATIONS,\n  exports: EXPORTED_DECLARATIONS,\n})\nexport class CdkTreeModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["observableOf", "i1.CdkTree", "i1.CdkTreeNode"], "mappings": ";;;;;;;AAWA;MACsB,eAAe,CAAA;AAArC,IAAA,WAAA,GAAA;;AAWE,QAAA,IAAA,CAAA,cAAc,GAAsB,IAAI,cAAc,CAAI,IAAI,CAAC,CAAC;KAuEjE;;AAhDC,IAAA,MAAM,CAAC,QAAW,EAAA;AAChB,QAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC;KAC1D;;AAGD,IAAA,MAAM,CAAC,QAAW,EAAA;AAChB,QAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC;KAC1D;;AAGD,IAAA,QAAQ,CAAC,QAAW,EAAA;AAClB,QAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC;KAC5D;;AAGD,IAAA,UAAU,CAAC,QAAW,EAAA;AACpB,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC;KACrE;;AAGD,IAAA,iBAAiB,CAAC,QAAW,EAAA;QAC3B,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAC1D,cAAE,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC;AACpC,cAAE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;KACtC;;IAGD,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;KAC7B;;AAGD,IAAA,iBAAiB,CAAC,QAAW,EAAA;AAC3B,QAAA,IAAI,aAAa,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC/B,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC;QACrD,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KACtF;;AAGD,IAAA,mBAAmB,CAAC,QAAW,EAAA;AAC7B,QAAA,IAAI,aAAa,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC/B,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC;QACrD,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KACxF;AAES,IAAA,aAAa,CAAC,KAAY,EAAA;AAClC,QAAA,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAU,CAAC,GAAI,KAAW,CAAC;KAC/D;AACF;;AC/ED;AACM,MAAO,eAA0B,SAAQ,eAAqB,CAAA;;AAElE,IAAA,WAAA,CACkB,QAAiC,EACjC,YAAsC,EAC/C,OAAsC,EAAA;AAE7C,QAAA,KAAK,EAAE,CAAC;QAJQ,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAyB;QACjC,IAAY,CAAA,YAAA,GAAZ,YAAY,CAA0B;QAC/C,IAAO,CAAA,OAAA,GAAP,OAAO,CAA+B;AAI7C,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;SACrC;KACF;AAED;;;;;AAKG;AACH,IAAA,cAAc,CAAC,QAAW,EAAA;QACxB,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACpD,MAAM,OAAO,GAAQ,EAAE,CAAC;;;;;;;AAQxB,QAAA,KACE,IAAI,CAAC,GAAG,UAAU,GAAG,CAAC,EACtB,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EACvF,CAAC,EAAE,EACH;YACA,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;SACjC;AACD,QAAA,OAAO,OAAO,CAAC;KAChB;AAED;;;;;AAKG;IACH,SAAS,GAAA;QACP,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACrF;AACF;;ACjDD;AACM,MAAO,iBAA4B,SAAQ,eAAqB,CAAA;;IAEpE,WACkB,CAAA,WAAsE,EAC/E,OAAwC,EAAA;AAE/C,QAAA,KAAK,EAAE,CAAC;QAHQ,IAAW,CAAA,WAAA,GAAX,WAAW,CAA2D;QAC/E,IAAO,CAAA,OAAA,GAAP,OAAO,CAAiC;AAI/C,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;SACrC;KACF;AAED;;;;;AAKG;IACH,SAAS,GAAA;AACP,QAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;AAC5B,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CACpC,CAAC,WAAgB,EAAE,QAAQ,KAAK,CAAC,GAAG,WAAW,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAC5F,EAAE,CACH,CAAC;QACF,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KAC/E;;AAGD,IAAA,cAAc,CAAC,QAAW,EAAA;QACxB,MAAM,WAAW,GAAQ,EAAE,CAAC;AAE5B,QAAA,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;;AAE5C,QAAA,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;KAC9B;;IAGS,eAAe,CAAC,WAAgB,EAAE,QAAW,EAAA;AACrD,QAAA,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3B,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACjD,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;AAChC,YAAA,aAAa,CAAC,OAAO,CAAC,CAAC,KAAQ,KAAK,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC;SAC/E;AAAM,aAAA,IAAI,YAAY,CAAC,aAAa,CAAC,EAAE;;;AAGtC,YAAA,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,OAAwB,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,IAAG;AACjF,gBAAA,KAAK,MAAM,KAAK,IAAI,QAAQ,EAAE;AAC5B,oBAAA,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;iBAC1C;AACH,aAAC,CAAC,CAAC;SACJ;KACF;AACF;;AC7DD;;;;AAIG;MACU,yBAAyB,GAAG,IAAI,cAAc,CAAK,2BAA2B,EAAE;AAE7F;;;AAGG;MAKU,iBAAiB,CAAA;IAC5B,WACS,CAAA,aAA+B,EACgB,KAAW,EAAA;QAD1D,IAAa,CAAA,aAAA,GAAb,aAAa,CAAkB;QACgB,IAAK,CAAA,KAAA,GAAL,KAAK,CAAM;KAC/D;AAJO,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,kDAGlB,yBAAyB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAHxB,iBAAiB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAjB,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAJ7B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,qBAAqB;AAC/B,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BAII,MAAM;2BAAC,yBAAyB,CAAA;;0BAAG,QAAQ;;;ACjBhD;MACa,wBAAwB,CAAA;AAanC,IAAA,WAAA,CAAY,IAAO,EAAA;AACjB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;KACvB;AACF,CAAA;AAED;;;AAGG;MAMU,cAAc,CAAA;;AAWzB,IAAA,WAAA,CAAmB,QAA0B,EAAA;QAA1B,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAkB;KAAI;8GAXtC,cAAc,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,WAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,CAAA,oBAAA,EAAA,MAAA,CAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAL1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,kBAAkB;oBAC5B,MAAM,EAAE,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,oBAAoB,EAAC,CAAC;AACrD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;;AC7BD;;;AAGG;SACa,6BAA6B,GAAA;AAC3C,IAAA,OAAO,KAAK,CAAC,CAAuC,qCAAA,CAAA,CAAC,CAAC;AACxD,CAAC;AAED;;;AAGG;SACa,mCAAmC,GAAA;AACjD,IAAA,OAAO,KAAK,CAAC,CAAsE,oEAAA,CAAA,CAAC,CAAC;AACvF,CAAC;AAED;;;AAGG;SACa,kCAAkC,GAAA;AAChD,IAAA,OAAO,KAAK,CAAC,CAAuE,qEAAA,CAAA,CAAC,CAAC;AACxF,CAAC;AAED;;;AAGG;SACa,0BAA0B,GAAA;AACxC,IAAA,OAAO,KAAK,CAAC,CAA6C,2CAAA,CAAA,CAAC,CAAC;AAC9D,CAAC;AAED;;;AAGG;SACa,mCAAmC,GAAA;AACjD,IAAA,OAAO,KAAK,CAAC,CAAgE,8DAAA,CAAA,CAAC,CAAC;AACjF;;ACIA;;;AAGG;MAkBU,OAAO,CAAA;AAgBlB;;;;AAIG;AACH,IAAA,IACI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;KACzB;IACD,IAAI,UAAU,CAAC,UAAiD,EAAA;AAC9D,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,UAAU,EAAE;AACnC,YAAA,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;SACpC;KACF;IAoCD,WACU,CAAA,QAAyB,EACzB,kBAAqC,EAAA;QADrC,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;QACzB,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAmB;;AAjE9B,QAAA,IAAA,CAAA,UAAU,GAAG,IAAI,OAAO,EAAQ,CAAC;;AAY1C,QAAA,IAAA,CAAA,OAAO,GAAmB,IAAI,GAAG,EAAa,CAAC;;;AA0CvD;;;AAGG;QACM,IAAU,CAAA,UAAA,GAAG,IAAI,eAAe,CAA+B;AACtE,YAAA,KAAK,EAAE,CAAC;YACR,GAAG,EAAE,MAAM,CAAC,SAAS;AACtB,SAAA,CAAC,CAAC;KAKC;IAEJ,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC/D,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YACxE,MAAM,0BAA0B,EAAE,CAAC;SACpC;KACF;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AAEvC,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;AAC3B,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;AAE3B,QAAA,IAAI,IAAI,CAAC,WAAW,IAAI,OAAQ,IAAI,CAAC,WAA6B,CAAC,UAAU,KAAK,UAAU,EAAE;AAC3F,YAAA,IAAI,CAAC,UAA4B,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;SACrD;AAED,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAC1B,YAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,CAAC;AACrC,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;SAC/B;KACF;IAED,qBAAqB,GAAA;AACnB,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAChE,QAAA,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YACjF,MAAM,mCAAmC,EAAE,CAAC;SAC7C;AACD,QAAA,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;AAE1C,QAAA,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAChE,IAAI,CAAC,qBAAqB,EAAE,CAAC;SAC9B;KACF;;;AAKD;;;;AAIG;AACK,IAAA,iBAAiB,CAAC,UAAiD,EAAA;AACzE,QAAA,IAAI,IAAI,CAAC,WAAW,IAAI,OAAQ,IAAI,CAAC,WAA6B,CAAC,UAAU,KAAK,UAAU,EAAE;AAC3F,YAAA,IAAI,CAAC,UAA4B,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;SACrD;AAED,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAC1B,YAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,CAAC;AACrC,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;SAC/B;;QAGD,IAAI,CAAC,UAAU,EAAE;AACf,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;SACxC;AAED,QAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;AAC9B,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,qBAAqB,EAAE,CAAC;SAC9B;KACF;;IAGO,qBAAqB,GAAA;AAC3B,QAAA,IAAI,UAAgD,CAAC;AAErD,QAAA,IAAI,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;YAClC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SAC7C;AAAM,aAAA,IAAI,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;AACzC,YAAA,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;SAC/B;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;AAC1C,YAAA,UAAU,GAAGA,EAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SAC7C;QAED,IAAI,UAAU,EAAE;YACd,IAAI,CAAC,iBAAiB,GAAG,UAAU;AAChC,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAChC,iBAAA,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;SACpD;AAAM,aAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;YACxD,MAAM,6BAA6B,EAAE,CAAC;SACvC;KACF;;AAGD,IAAA,iBAAiB,CACf,IAAkB,EAClB,UAAA,GAAgC,IAAI,CAAC,WAAW,EAChD,aAAA,GAAkC,IAAI,CAAC,WAAW,CAAC,aAAa,EAChE,UAAc,EAAA;QAEd,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtC,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO;SACR;QAED,OAAO,CAAC,gBAAgB,CACtB,CACE,IAA6B,EAC7B,qBAAoC,EACpC,YAA2B,KACzB;AACF,YAAA,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,EAAE;AAC9B,gBAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAa,CAAC,EAAE,YAAa,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;aAChF;AAAM,iBAAA,IAAI,YAAY,IAAI,IAAI,EAAE;AAC/B,gBAAA,aAAa,CAAC,MAAM,CAAC,qBAAsB,CAAC,CAAC;gBAC7C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAChC;iBAAM;gBACL,MAAM,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC,qBAAsB,CAAC,CAAC;AACvD,gBAAA,aAAa,CAAC,IAAI,CAAC,IAAK,EAAE,YAAY,CAAC,CAAC;aACzC;AACH,SAAC,CACF,CAAC;AAEF,QAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC;KACzC;AAED;;;;;AAKG;IACH,WAAW,CAAC,IAAO,EAAE,CAAS,EAAA;QAC5B,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;AAC/B,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,KAAM,CAAC;SAC9B;AAED,QAAA,MAAM,OAAO,GACX,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC;AAEpF,QAAA,IAAI,CAAC,OAAO,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YAC/D,MAAM,kCAAkC,EAAE,CAAC;SAC5C;AAED,QAAA,OAAO,OAAQ,CAAC;KACjB;AAED;;;AAGG;AACH,IAAA,UAAU,CAAC,QAAW,EAAE,KAAa,EAAE,aAAgC,EAAE,UAAc,EAAA;QACrF,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;;AAG/C,QAAA,MAAM,OAAO,GAAG,IAAI,wBAAwB,CAAI,QAAQ,CAAC,CAAC;;;AAI1D,QAAA,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;YAC7B,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;SACrD;AAAM,aAAA,IAAI,OAAO,UAAU,KAAK,WAAW,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;AAC5E,YAAA,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAE,GAAG,CAAC,CAAC;SACnD;aAAM;AACL,YAAA,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC;SACnB;QACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;;AAG1C,QAAA,MAAM,SAAS,GAAG,aAAa,GAAG,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;QACjF,SAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;;;;AAK5D,QAAA,IAAI,WAAW,CAAC,kBAAkB,EAAE;AAClC,YAAA,WAAW,CAAC,kBAAkB,CAAC,IAAI,GAAG,QAAQ,CAAC;SAChD;KACF;8GAjPU,OAAO,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,eAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAP,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,OAAO,0PA+CD,cAAc,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAHpB,iBAAiB,EA1DlB,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,SAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA,+CAAA,CAAiD,4DAYjD,iBAAiB,EAAA,QAAA,EAAA,qBAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAEhB,OAAO,EAAA,UAAA,EAAA,CAAA;kBAjBnB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,UAAU;AACpB,oBAAA,QAAQ,EAAE,SAAS;AACnB,oBAAA,QAAQ,EAAE,CAAiD,+CAAA,CAAA;AAC3D,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,UAAU;AACnB,wBAAA,MAAM,EAAE,MAAM;AACf,qBAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;;;;;oBAKrC,eAAe,EAAE,uBAAuB,CAAC,OAAO;AAChD,oBAAA,UAAU,EAAE,IAAI;oBAChB,OAAO,EAAE,CAAC,iBAAiB,CAAC;AAC7B,iBAAA,CAAA;oHAuBK,UAAU,EAAA,CAAA;sBADb,KAAK;gBAYG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAQG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAGwC,WAAW,EAAA,CAAA;sBAAxD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,iBAAiB,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC,CAAA;gBAQ5C,SAAS,EAAA,CAAA;sBALR,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,cAAc,EAAE;;;AAG/B,wBAAA,WAAW,EAAE,IAAI;AAClB,qBAAA,CAAA;;AAiMH;;AAEG;MAUU,WAAW,CAAA;AACtB;;;;;AAKG;AACH,IAAA,IAAa,IAAI,GAAA;AACf,QAAA,OAAO,UAAU,CAAC;KACnB;IAED,IAAI,IAAI,CAAC,KAA2B,EAAA;;QAElC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC5D;AAED;;;AAGG;aACI,IAAkB,CAAA,kBAAA,GAA4B,IAA5B,CAAiC,EAAA;;AAW1D,IAAA,IAAI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;IACD,IAAI,IAAI,CAAC,KAAQ,EAAA;AACf,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE;AACxB,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;YACnB,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxB,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;SAC1B;KACF;AAGD,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACtD;AAED,IAAA,IAAI,KAAK,GAAA;;;;AAIP,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ;AACpC,cAAE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;AAC7C,cAAE,IAAI,CAAC,oBAAoB,CAAC;KAC/B;IAED,WACY,CAAA,WAAoC,EACpC,KAAoB,EAAA;QADpB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAyB;QACpC,IAAK,CAAA,KAAA,GAAL,KAAK,CAAe;;AAnCb,QAAA,IAAA,CAAA,UAAU,GAAG,IAAI,OAAO,EAAQ,CAAC;;AAG3C,QAAA,IAAA,CAAA,YAAY,GAAG,IAAI,OAAO,EAAQ,CAAC;AAkC1C,QAAA,WAAW,CAAC,kBAAkB,GAAG,IAAyB,CAAC;AAC3D,QAAA,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC;KACxB;IAED,QAAQ,GAAA;QACN,IAAI,CAAC,oBAAoB,GAAG,sBAAsB,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;AACnF,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,YAAY,EAAE,CAAA,EAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAA,CAAE,CAAC,CAAC;KAChF;IAED,WAAW,GAAA;;;AAGT,QAAA,IAAI,WAAW,CAAC,kBAAkB,KAAK,IAAI,EAAE;AAC3C,YAAA,WAAW,CAAC,kBAAkB,GAAG,IAAI,CAAC;SACvC;AAED,QAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;AAC7B,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;KAC5B;;IAGD,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;KACxC;;IAGS,gBAAgB,GAAA;AACxB,QAAA,IACE,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,YAAY;AACpC,YAAA,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,WAAW;aAClC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAC/C;YACA,MAAM,mCAAmC,EAAE,CAAC;SAC7C;AACD,QAAA,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC;KACxB;8GAhGU,WAAW,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAX,WAAW,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,oBAAA,EAAA,YAAA,EAAA,EAAA,cAAA,EAAA,eAAA,EAAA,EAAA,QAAA,EAAA,CAAA,aAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAX,WAAW,EAAA,UAAA,EAAA,CAAA;kBATvB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,eAAe;AACzB,oBAAA,QAAQ,EAAE,aAAa;AACvB,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,eAAe;AACxB,wBAAA,sBAAsB,EAAE,YAAY;AACrC,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;kGAQc,IAAI,EAAA,CAAA;sBAAhB,KAAK;;AA4FR,SAAS,sBAAsB,CAAC,WAAwB,EAAA;AACtD,IAAA,IAAI,MAAM,GAAG,WAAW,CAAC,aAAa,CAAC;IACvC,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE;AACvC,QAAA,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC;KAC/B;IACD,IAAI,CAAC,MAAM,EAAE;AACX,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,MAAM,KAAK,CAAC,oDAAoD,CAAC,CAAC;SACnE;aAAM;YACL,OAAO,CAAC,CAAC,CAAC;SACX;KACF;SAAM,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,sBAAsB,CAAC,EAAE;QAC5D,OAAO,eAAe,CAAC,MAAM,CAAC,YAAY,CAAC,YAAY,CAAE,CAAC,CAAC;KAC5D;SAAM;;AAEL,QAAA,OAAO,CAAC,CAAC;KACV;AACH,CAAC;AAED,SAAS,aAAa,CAAC,OAAoB,EAAA;AACzC,IAAA,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;AACpC,IAAA,OAAO,CAAC,EAAE,SAAS,EAAE,QAAQ,CAAC,sBAAsB,CAAC,IAAI,SAAS,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;AAC5F;;ACvaA;;;;;AAKG;AAaG,MAAO,iBACX,SAAQ,WAAiB,CAAA;AAiBzB,IAAA,WAAA,CACE,UAAmC,EACnC,IAAmB,EACT,QAAyB,EAAA;AAEnC,QAAA,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAFd,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;KAGpC;IAED,kBAAkB,GAAA;QAChB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACrE,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,WAAW,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YAC1F,MAAM,mCAAmC,EAAE,CAAC;SAC7C;AACD,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpE,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;AAChC,YAAA,IAAI,CAAC,mBAAmB,CAAC,aAAoB,CAAC,CAAC;SAChD;AAAM,aAAA,IAAI,YAAY,CAAC,aAAa,CAAC,EAAE;YACtC,aAAa;AACV,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAChC,iBAAA,SAAS,CAAC,MAAM,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC;SAC1D;QACD,IAAI,CAAC,UAAU,CAAC,OAAO;AACpB,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAChC,SAAS,CAAC,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC;KAChD;;;IAIQ,QAAQ,GAAA;QACf,KAAK,CAAC,QAAQ,EAAE,CAAC;KAClB;IAEQ,WAAW,GAAA;QAClB,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,KAAK,CAAC,WAAW,EAAE,CAAC;KACrB;;AAGS,IAAA,mBAAmB,CAAC,QAAc,EAAA;AAC1C,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACrC,IAAI,QAAQ,EAAE;AACZ,YAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;SAC3B;AACD,QAAA,IAAI,MAAM,IAAI,IAAI,CAAC,SAAS,EAAE;AAC5B,YAAA,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;AAC3C,YAAA,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,EAAE,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;SAC3F;aAAM;;AAEL,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SAC3B;KACF;;IAGS,MAAM,GAAA;AACd,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACrC,IAAI,MAAM,EAAE;AACV,YAAA,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AAC7B,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SAC3B;KACF;;IAGO,cAAc,GAAA;AACpB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC;;;QAIhC,OAAO,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC;KAClF;8GAtFU,iBAAiB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,OAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,eAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAjB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,iBAAiB,EATjB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,iBAAiB,EAAC;AACtD,YAAA,EAAC,OAAO,EAAE,yBAAyB,EAAE,WAAW,EAAE,iBAAiB,EAAC;AACrE,SAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,YAAA,EAAA,SAAA,EAiBgB,iBAAiB,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAXvB,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAZ7B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sBAAsB;AAChC,oBAAA,QAAQ,EAAE,mBAAmB;AAC7B,oBAAA,SAAS,EAAE;AACT,wBAAA,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,mBAAmB,EAAC;AACtD,wBAAA,EAAC,OAAO,EAAE,yBAAyB,EAAE,WAAW,mBAAmB,EAAC;AACrE,qBAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,sBAAsB;AAChC,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;gIAiBC,UAAU,EAAA,CAAA;sBALT,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,iBAAiB,EAAE;;;AAGlC,wBAAA,WAAW,EAAE,IAAI;AAClB,qBAAA,CAAA;;;AC5CH;AACA,MAAM,cAAc,GAAG,eAAe,CAAC;AAEvC;;;AAGG;MAKU,kBAAkB,CAAA;;AAW7B,IAAA,IACI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;KACpB;IACD,IAAI,KAAK,CAAC,KAAa,EAAA;AACrB,QAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;KAC5B;AAGD;;;AAGG;AACH,IAAA,IACI,MAAM,GAAA;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;IACD,IAAI,MAAM,CAAC,MAAuB,EAAA;AAChC,QAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;KAC9B;AAGD,IAAA,WAAA,CACU,SAA4B,EAC5B,KAAoB,EACpB,QAAiC,EACrB,IAAoB,EAAA;QAHhC,IAAS,CAAA,SAAA,GAAT,SAAS,CAAmB;QAC5B,IAAK,CAAA,KAAA,GAAL,KAAK,CAAe;QACpB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAyB;QACrB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAgB;;AAhCzB,QAAA,IAAA,CAAA,UAAU,GAAG,IAAI,OAAO,EAAQ,CAAC;;QAGlD,IAAW,CAAA,WAAA,GAAG,IAAI,CAAC;QAuBnB,IAAO,CAAA,OAAA,GAAW,EAAE,CAAC;QAQnB,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,IAAI,EAAE;YACR,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;SACtF;;;;AAKD,QAAA,SAAS,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;KAC5D;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;KAC5B;;IAGD,cAAc,GAAA;AACZ,QAAA,MAAM,SAAS,GACb,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ;AACpD,cAAE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;cACpD,IAAI,CAAC;AACX,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,GAAG,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;QAC5D,OAAO,OAAO,KAAK,KAAK,QAAQ,GAAG,CAAG,EAAA,KAAK,GAAG,IAAI,CAAC,OAAO,CAAA,EAAG,IAAI,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC;KACxF;IAED,WAAW,CAAC,WAAW,GAAG,KAAK,EAAA;AAC7B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtC,IAAI,OAAO,KAAK,IAAI,CAAC,eAAe,IAAI,WAAW,EAAE;AACnD,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;YAC5C,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,GAAG,cAAc,GAAG,aAAa,CAAC;AAC5F,YAAA,MAAM,SAAS,GAAG,WAAW,KAAK,aAAa,GAAG,cAAc,GAAG,aAAa,CAAC;YACjF,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,OAAO,IAAI,EAAE,CAAC;AAC3C,YAAA,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;AAC9B,YAAA,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC;SAChC;KACF;AAED;;;;;AAKG;AACO,IAAA,cAAc,CAAC,KAAa,EAAA;;;;AAIpC,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,IAAK,GAAG,KAAK,CAAC;QAC3C,IAAI,CAAC,WAAW,EAAE,CAAC;KACpB;AAED;;;;;AAKG;AACO,IAAA,eAAe,CAAC,MAAuB,EAAA;QAC/C,IAAI,KAAK,GAAG,MAAM,CAAC;QACnB,IAAI,KAAK,GAAG,IAAI,CAAC;AAEjB,QAAA,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;YAC9B,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AAC3C,YAAA,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACjB,YAAA,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC;SAC3B;AAED,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AACzB,QAAA,IAAI,CAAC,OAAO,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;QACtC,IAAI,CAAC,WAAW,EAAE,CAAC;KACpB;8GA/GU,kBAAkB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAC,WAAA,EAAA,EAAA,EAAA,KAAA,EAAAD,OAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAlB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,kBAAkB,yGAWmB,eAAe,CAAA,EAAA,MAAA,EAAA,CAAA,0BAAA,EAAA,QAAA,CAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAXpD,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAJ9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sBAAsB;AAChC,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BAsCI,QAAQ;yCAzBP,KAAK,EAAA,CAAA;sBADR,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,oBAAoB,EAAE,SAAS,EAAE,eAAe,EAAC,CAAA;gBAc5D,MAAM,EAAA,CAAA;sBADT,KAAK;uBAAC,0BAA0B,CAAA;;;ACrCnC;;AAEG;MAQU,iBAAiB,CAAA;IAK5B,WACY,CAAA,KAAoB,EACpB,SAA4B,EAAA;QAD5B,IAAK,CAAA,KAAA,GAAL,KAAK,CAAe;QACpB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAmB;;QAJxC,IAAS,CAAA,SAAA,GAAY,KAAK,CAAC;KAKvB;AAEJ,IAAA,OAAO,CAAC,KAAY,EAAA;AAClB,QAAA,IAAI,CAAC,SAAS;AACZ,cAAE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;AAC/D,cAAE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAEvD,KAAK,CAAC,eAAe,EAAE,CAAC;KACzB;8GAhBU,iBAAiB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAA,OAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,WAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAjB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,iBAAiB,wHAE4B,gBAAgB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,iBAAA,EAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAF7D,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAP7B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,qBAAqB;AAC/B,oBAAA,IAAI,EAAE;AACJ,wBAAA,SAAS,EAAE,iBAAiB;AAC7B,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;gGAIC,SAAS,EAAA,CAAA;sBADR,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,4BAA4B,EAAE,SAAS,EAAE,gBAAgB,EAAC,CAAA;;;ACR3E,MAAM,qBAAqB,GAAG;IAC5B,iBAAiB;IACjB,cAAc;IACd,kBAAkB;IAClB,iBAAiB;IACjB,OAAO;IACP,WAAW;IACX,iBAAiB;CAClB,CAAC;MAMW,aAAa,CAAA;8GAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAb,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,YAbxB,iBAAiB;YACjB,cAAc;YACd,kBAAkB;YAClB,iBAAiB;YACjB,OAAO;YACP,WAAW;AACX,YAAA,iBAAiB,aANjB,iBAAiB;YACjB,cAAc;YACd,kBAAkB;YAClB,iBAAiB;YACjB,OAAO;YACP,WAAW;YACX,iBAAiB,CAAA,EAAA,CAAA,CAAA,EAAA;+GAON,aAAa,EAAA,CAAA,CAAA,EAAA;;2FAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBAJzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,qBAAqB;AAC9B,oBAAA,OAAO,EAAE,qBAAqB;AAC/B,iBAAA,CAAA;;;AC7BD;;AAEG;;;;"}