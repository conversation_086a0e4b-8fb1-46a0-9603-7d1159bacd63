/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["so-KE", [["h", "d"], ["GH", "GD"], u], [["AM", "GD"], u, ["GH", "GD"]], [["A", "I", "T", "A", "Kh", "J", "S"], ["Axd", "Isn", "Tldo", "Arbc", "Khms", "Jmc", "Sbti"], ["Axad", "Isniin", "<PERSON>la<PERSON>", "<PERSON>rb<PERSON>", "Khamiis", "Jim<PERSON>", "Sabti"], ["Axd", "<PERSON>", "<PERSON><PERSON>", "<PERSON>rb<PERSON>", "<PERSON>hm<PERSON>", "<PERSON>mc", "Sbti"]], [["A", "I", "T", "A", "Kh", "J", "S"], ["Axd", "Isn", "Tldo", "Arbc", "Khms", "Jmc", "Sbti"], ["Axad", "Isniin", "Talaado", "Arbaco", "Khamiis", "Jimco", "Sabti"], ["Axd", "Isn", "Tldo", "Arbaco", "Khms", "Jmc", "Sbti"]], [["J", "F", "M", "A", "M", "J", "L", "O", "S", "O", "N", "D"], ["Jan", "Feb", "Mar", "Abr", "May", "Jun", "Lul", "Ogs", "Seb", "Okt", "Nof", "Dis"], ["Bisha Koobaad", "Bisha Labaad", "Bisha Saddexaad", "Bisha Afraad", "Bisha Shanaad", "Bisha Lixaad", "Bisha Todobaad", "Bisha Sideedaad", "Bisha Sagaalaad", "Bisha Tobnaad", "Bisha Kow iyo Tobnaad", "Bisha Laba iyo Tobnaad"]], [["J", "F", "M", "A", "M", "J", "L", "O", "S", "O", "N", "D"], ["Jan", "Feb", "Mar", "Abr", "May", "Jun", "Lul", "Ogs", "Seb", "Okt", "Nof", "Dis"], ["Jannaayo", "Febraayo", "Maarso", "Abriil", "May", "Juun", "Luuliyo", "Ogost", "Sebtembar", "Oktoobar", "Nofembar", "Desembar"]], [["B", "A"], ["BC", "AD"], ["Ciise Hortii", "Ciise Dabadii"]], 0, [6, 0], ["dd/MM/yy", "dd-MMM-y", "MMMM d, y", "EEEE, MMMM d, y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", "{1} 'ee' {0}", u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "MaL", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "KES", "Ksh", "Shilingka Kenya", { "BBD": ["DBB", "$"], "JPY": ["JP¥", "¥"], "KES": ["Ksh"], "SOS": ["S"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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