/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { setActiveConsumer } from '@angular/core/primitives/signals';
import { Subject, Subscription } from 'rxjs';
import { isInInjectionContext } from './di/contextual';
import { inject } from './di/injector_compatibility';
import { DestroyRef } from './linker/destroy_ref';
class EventEmitter_ extends Subject {
    constructor(isAsync = false) {
        super();
        this.destroyRef = undefined;
        this.__isAsync = isAsync;
        // Attempt to retrieve a `DestroyRef` optionally.
        // For backwards compatibility reasons, this cannot be required
        if (isInInjectionContext()) {
            this.destroyRef = inject(DestroyRef, { optional: true }) ?? undefined;
        }
    }
    emit(value) {
        const prevConsumer = setActiveConsumer(null);
        try {
            super.next(value);
        }
        finally {
            setActiveConsumer(prevConsumer);
        }
    }
    subscribe(observerOrNext, error, complete) {
        let nextFn = observerOrNext;
        let errorFn = error || (() => null);
        let completeFn = complete;
        if (observerOrNext && typeof observerOrNext === 'object') {
            const observer = observerOrNext;
            nextFn = observer.next?.bind(observer);
            errorFn = observer.error?.bind(observer);
            completeFn = observer.complete?.bind(observer);
        }
        if (this.__isAsync) {
            errorFn = _wrapInTimeout(errorFn);
            if (nextFn) {
                nextFn = _wrapInTimeout(nextFn);
            }
            if (completeFn) {
                completeFn = _wrapInTimeout(completeFn);
            }
        }
        const sink = super.subscribe({ next: nextFn, error: errorFn, complete: completeFn });
        if (observerOrNext instanceof Subscription) {
            observerOrNext.add(sink);
        }
        return sink;
    }
}
function _wrapInTimeout(fn) {
    return (value) => {
        setTimeout(fn, undefined, value);
    };
}
/**
 * @publicApi
 */
export const EventEmitter = EventEmitter_;
//# sourceMappingURL=data:application/json;base64,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