{"version": 3, "sources": ["../../../../../../packages/compiler-cli/src/ngtsc/typecheck/api/checker.ts", "../../../../../../packages/compiler-cli/src/ngtsc/typecheck/api/scope.ts", "../../../../../../packages/compiler-cli/src/ngtsc/typecheck/api/completion.ts", "../../../../../../packages/compiler-cli/src/ngtsc/typecheck/api/symbols.ts"], "mappings": ";;;;;;AA8OA,IAAY;CAAZ,SAAYA,cAAW;AAQrB,EAAAA,aAAAA,aAAA,gBAAA,KAAA;AAUA,EAAAA,aAAAA,aAAA,kBAAA,KAAA;AACF,GAnBY,gBAAA,cAAW,CAAA,EAAA;;;ACjNvB,IAAY;CAAZ,SAAYC,sBAAmB;AAC7B,EAAAA,qBAAAA,qBAAA,cAAA,KAAA;AACA,EAAAA,qBAAAA,qBAAA,gBAAA,KAAA;AACF,GAHY,wBAAA,sBAAmB,CAAA,EAAA;AAmE/B,IAAY;CAAZ,SAAYC,sBAAmB;AAE7B,EAAAA,qBAAAA,qBAAA,YAAA,KAAA;AAOA,EAAAA,qBAAAA,qBAAA,iBAAA,KAAA;AACF,GAVY,wBAAA,sBAAmB,CAAA,EAAA;;;AC3E/B,IAAY;CAAZ,SAAYC,iBAAc;AACxB,EAAAA,gBAAAA,gBAAA,eAAA,KAAA;AACA,EAAAA,gBAAAA,gBAAA,cAAA,KAAA;AACF,GAHY,mBAAA,iBAAc,CAAA,EAAA;;;ACL1B,IAAY;CAAZ,SAAYC,aAAU;AACpB,EAAAA,YAAAA,YAAA,WAAA,KAAA;AACA,EAAAA,YAAAA,YAAA,YAAA,KAAA;AACA,EAAAA,YAAAA,YAAA,aAAA,KAAA;AACA,EAAAA,YAAAA,YAAA,eAAA,KAAA;AACA,EAAAA,YAAAA,YAAA,cAAA,KAAA;AACA,EAAAA,YAAAA,YAAA,eAAA,KAAA;AACA,EAAAA,YAAAA,YAAA,aAAA,KAAA;AACA,EAAAA,YAAAA,YAAA,cAAA,KAAA;AACA,EAAAA,YAAAA,YAAA,gBAAA,KAAA;AACA,EAAAA,YAAAA,YAAA,gBAAA,KAAA;AACA,EAAAA,YAAAA,YAAA,UAAA,MAAA;AACF,GAZY,eAAA,aAAU,CAAA,EAAA;", "names": ["OptimizeFor", "PotentialImportKind", "PotentialImportMode", "CompletionKind", "SymbolKind"]}