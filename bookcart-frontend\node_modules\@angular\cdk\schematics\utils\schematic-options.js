"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.isStandaloneSchematic = exports.getDefaultComponentOptions = void 0;
const core_1 = require("@angular-devkit/core");
const schema_1 = require("@schematics/angular/component/schema");
const ng_ast_utils_1 = require("@schematics/angular/utility/ng-ast-utils");
const project_main_file_1 = require("./project-main-file");
const workspace_1 = require("@schematics/angular/utility/workspace");
const get_project_1 = require("./get-project");
/**
 * Returns the default options for the `@schematics/angular:component` schematic which would
 * have been specified at project initialization (ng new or ng init).
 *
 * This is necessary because the Angular CLI only exposes the default values for the "--style",
 * "--inlineStyle", "--skipTests" and "--inlineTemplate" options to the "component" schematic.
 */
function getDefaultComponentOptions(project) {
    // Note: Not all options which are available when running "ng new" will be stored in the
    // workspace config. List of options which will be available in the configuration:
    // angular/angular-cli/blob/main/packages/schematics/angular/application/index.ts#L109-L131
    let skipTests = getDefaultComponentOption(project, ['skipTests'], null);
    // In case "skipTests" is not set explicitly, also look for the "spec" option. The "spec"
    // option has been deprecated but can be still used in older Angular CLI projects.
    // See: https://github.com/angular/angular-cli/commit/a12a4e02a4689b5bdbc6e740c0d9865afb55671a
    if (skipTests === null) {
        skipTests = !getDefaultComponentOption(project, ['spec'], true);
    }
    return {
        style: getDefaultComponentOption(project, ['style', 'styleext'], schema_1.Style.Css),
        inlineStyle: getDefaultComponentOption(project, ['inlineStyle'], false),
        inlineTemplate: getDefaultComponentOption(project, ['inlineTemplate'], false),
        skipTests: skipTests,
    };
}
exports.getDefaultComponentOptions = getDefaultComponentOptions;
/** Determines whether the schematic is configured to be standalone. */
async function isStandaloneSchematic(host, options) {
    if (options.standalone != null) {
        return options.standalone;
    }
    // If the `--standalone` flag isn't passed and there isn't a default, infer based on the project.
    const workspace = await (0, workspace_1.getWorkspace)(host);
    const project = (0, get_project_1.getProjectFromWorkspace)(workspace, options.project);
    // Legacy projects might not have a `build` target, but they're likely
    // not on an Angular version that supports standalone either.
    if (!project.targets?.has('build')) {
        return false;
    }
    return (0, ng_ast_utils_1.isStandaloneApp)(host, (0, project_main_file_1.getProjectMainFile)(project));
}
exports.isStandaloneSchematic = isStandaloneSchematic;
/**
 * Gets the default value for the specified option. The default options will be determined
 * by looking at the stored schematic options for `@schematics/angular:component` in the
 * CLI workspace configuration.
 */
function getDefaultComponentOption(project, optionNames, fallbackValue) {
    const schematicOptions = (0, core_1.isJsonObject)(project.extensions['schematics'] || null)
        ? project.extensions['schematics']
        : null;
    const defaultSchematic = schematicOptions
        ? schematicOptions['@schematics/angular:component']
        : null;
    for (const optionName of optionNames) {
        if (defaultSchematic && defaultSchematic[optionName] != null) {
            return defaultSchematic[optionName];
        }
    }
    return fallbackValue;
}
//# sourceMappingURL=data:application/json;base64,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