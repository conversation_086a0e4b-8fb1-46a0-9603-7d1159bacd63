/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['jv'] = ["jv",[["<PERSON><PERSON>","<PERSON><PERSON>"],u,u],u,[["A","S","S","R","K","J","S"],["Ahad","<PERSON>","Sel","Rab","<PERSON><PERSON>","<PERSON><PERSON>","Sab"],["Ahad","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","Sabtu"],["<PERSON><PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>b"]],u,[["<PERSON>","<PERSON>","M","A","M","J","J","A","S","O","N","D"],["<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON>t","<PERSON>","<PERSON>"],["<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON>t","April","<PERSON>","<PERSON>i","<PERSON>i","<PERSON><PERSON><PERSON>","September","<PERSON>to<PERSON>","November","<PERSON>em<PERSON>"]],u,[["<PERSON>","<PERSON>"],u,["Sakdurunge Masehi","Masehi"]],0,[6,0],["dd-MM-y","d MMM y","d MMMM y","EEEE, d MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1}, {0}",u,"{1} {0}",u],[",",".",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤ #,##0.00","#E0"],"IDR","Rp","Rupiah Indonesia",{"IDR":["Rp"],"JPY":["JP¥","¥"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    