/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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