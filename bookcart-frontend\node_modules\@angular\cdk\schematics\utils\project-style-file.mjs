"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getProjectStyleFile = void 0;
const core_1 = require("@angular-devkit/core");
const project_targets_1 = require("./project-targets");
/** Regular expression that matches all possible Angular CLI default style files. */
const defaultStyleFileRegex = /styles\.(c|le|sc)ss/;
/** Regular expression that matches all files that have a proper stylesheet extension. */
const validStyleFileRegex = /\.(c|le|sc)ss/;
/**
 * Gets a style file with the given extension in a project and returns its path. If no
 * extension is specified, any style file with a valid extension will be returned.
 */
function getProjectStyleFile(project, extension) {
    const buildOptions = (0, project_targets_1.getProjectTargetOptions)(project, 'build');
    const buildStyles = buildOptions['styles'];
    if (buildStyles && (0, core_1.isJsonArray)(buildStyles) && buildStyles.length) {
        const styles = buildStyles.map(s => (typeof s === 'string' ? s : s.input));
        // Look for the default style file that is generated for new projects by the Angular CLI. This
        // default style file is usually called `styles.ext` unless it has been changed explicitly.
        const defaultMainStylePath = styles.find(file => extension ? file === `styles.${extension}` : defaultStyleFileRegex.test(file));
        if (defaultMainStylePath) {
            return (0, core_1.normalize)(defaultMainStylePath);
        }
        // If no default style file could be found, use the first style file that matches the given
        // extension. If no extension specified explicitly, we look for any file with a valid style
        // file extension.
        const fallbackStylePath = styles.find(file => extension ? file.endsWith(`.${extension}`) : validStyleFileRegex.test(file));
        if (fallbackStylePath) {
            return (0, core_1.normalize)(fallbackStylePath);
        }
    }
    return null;
}
exports.getProjectStyleFile = getProjectStyleFile;
//# sourceMappingURL=data:application/json;base64,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