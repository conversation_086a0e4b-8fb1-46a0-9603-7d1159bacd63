{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../../src/material/core/testing/option-harness.ts", "../../../../../../../src/material/core/testing/optgroup-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ComponentHarnessConstructor,\n  ContentContainerComponentHarness,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {OptionHarnessFilters} from './option-harness-filters';\n\n/** <PERSON><PERSON><PERSON> for interacting with an MDC-based `mat-option` in tests. */\nexport class MatOptionHarness extends ContentContainerComponentHarness {\n  /** Selector used to locate option instances. */\n  static hostSelector = '.mat-mdc-option';\n\n  /** Element containing the option's text. */\n  private _text = this.locatorFor('.mdc-list-item__primary-text');\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for an option with specific attributes.\n   * @param options Options for filtering which option instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatOptionHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: OptionHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options)\n      .addOption('text', options.text, async (harness, title) =>\n        HarnessPredicate.stringMatches(await harness.getText(), title),\n      )\n      .addOption(\n        'isSelected',\n        options.isSelected,\n        async (harness, isSelected) => (await harness.isSelected()) === isSelected,\n      );\n  }\n\n  /** Clicks the option. */\n  async click(): Promise<void> {\n    return (await this.host()).click();\n  }\n\n  /** Gets the option's label text. */\n  async getText(): Promise<string> {\n    return (await this._text()).text();\n  }\n\n  /** Gets whether the option is disabled. */\n  async isDisabled(): Promise<boolean> {\n    return (await this.host()).hasClass('mdc-list-item--disabled');\n  }\n\n  /** Gets whether the option is selected. */\n  async isSelected(): Promise<boolean> {\n    return (await this.host()).hasClass('mdc-list-item--selected');\n  }\n\n  /** Gets whether the option is active. */\n  async isActive(): Promise<boolean> {\n    return (await this.host()).hasClass('mat-mdc-option-active');\n  }\n\n  /** Gets whether the option is in multiple selection mode. */\n  async isMultiple(): Promise<boolean> {\n    return (await this.host()).hasClass('mat-mdc-option-multiple');\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {OptgroupHarnessFilters} from './optgroup-harness-filters';\nimport {MatOptionHarness} from './option-harness';\nimport {OptionHarnessFilters} from './option-harness-filters';\n\n/** Harness for interacting with an MDC-based `mat-optgroup` in tests. */\nexport class MatOptgroupHarness extends ComponentHarness {\n  /** Selector used to locate option group instances. */\n  static hostSelector = '.mat-mdc-optgroup';\n  private _label = this.locatorFor('.mat-mdc-optgroup-label');\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a option group with specific\n   * attributes.\n   * @param options Options for filtering which option instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatOptgroupHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: OptgroupHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options).addOption(\n      'labelText',\n      options.labelText,\n      async (harness, title) => HarnessPredicate.stringMatches(await harness.getLabelText(), title),\n    );\n  }\n\n  /** Gets the option group's label text. */\n  async getLabelText(): Promise<string> {\n    return (await this._label()).text();\n  }\n\n  /** Gets whether the option group is disabled. */\n  async isDisabled(): Promise<boolean> {\n    return (await (await this.host()).getAttribute('aria-disabled')) === 'true';\n  }\n\n  /**\n   * Gets the options that are inside the group.\n   * @param filter Optionally filters which options are included.\n   */\n  async getOptions(filter: OptionHarnessFilters = {}): Promise<MatOptionHarness[]> {\n    return this.locatorForAll(MatOptionHarness.with(filter))();\n  }\n}\n"], "names": [], "mappings": ";;AAeA;AACM,MAAO,gBAAiB,SAAQ,gCAAgC,CAAA;AAAtE,IAAA,WAAA,GAAA;;;AAKU,QAAA,IAAA,CAAA,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,8BAA8B,CAAC,CAAC;KAmDjE;;aAtDQ,IAAY,CAAA,YAAA,GAAG,iBAAH,CAAqB,EAAA;AAKxC;;;;AAIG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAgC,EAAE,EAAA;AAElC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC;aACvC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,OAAO,EAAE,KAAK,KACpD,gBAAgB,CAAC,aAAa,CAAC,MAAM,OAAO,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAC/D;aACA,SAAS,CACR,YAAY,EACZ,OAAO,CAAC,UAAU,EAClB,OAAO,OAAO,EAAE,UAAU,KAAK,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,UAAU,CAC3E,CAAC;KACL;;AAGD,IAAA,MAAM,KAAK,GAAA;QACT,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC;KACpC;;AAGD,IAAA,MAAM,OAAO,GAAA;QACX,OAAO,CAAC,MAAM,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC;KACpC;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,yBAAyB,CAAC,CAAC;KAChE;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,yBAAyB,CAAC,CAAC;KAChE;;AAGD,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,uBAAuB,CAAC,CAAC;KAC9D;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,yBAAyB,CAAC,CAAC;KAChE;;;ACtDH;AACM,MAAO,kBAAmB,SAAQ,gBAAgB,CAAA;AAAxD,IAAA,WAAA,GAAA;;AAGU,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,yBAAyB,CAAC,CAAC;KAoC7D;;aArCQ,IAAY,CAAA,YAAA,GAAG,mBAAH,CAAuB,EAAA;AAG1C;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAkC,EAAE,EAAA;AAEpC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,SAAS,CAClD,WAAW,EACX,OAAO,CAAC,SAAS,EACjB,OAAO,OAAO,EAAE,KAAK,KAAK,gBAAgB,CAAC,aAAa,CAAC,MAAM,OAAO,CAAC,YAAY,EAAE,EAAE,KAAK,CAAC,CAC9F,CAAC;KACH;;AAGD,IAAA,MAAM,YAAY,GAAA;QAChB,OAAO,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,CAAC;KACrC;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,eAAe,CAAC,MAAM,MAAM,CAAC;KAC7E;AAED;;;AAGG;AACH,IAAA,MAAM,UAAU,CAAC,MAAA,GAA+B,EAAE,EAAA;AAChD,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;KAC5D;;;;;"}