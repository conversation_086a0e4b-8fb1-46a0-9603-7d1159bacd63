/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['sah'] = ["sah",[["ЭИ","ЭК"],u,u],u,[["Б","Б","О","С","Ч","Б","С"],["бс","бн","оп","сэ","чп","бэ","сб"],["баскыһыанньа","бэнидиэнньик","оптуорунньук","сэрэдэ","чэппиэр","Бээтиҥсэ","субуота"],["бс","бн","оп","сэ","чп","бэ","сб"]],u,[["Т","О","К","М","Ы","Б","О","А","Б","А","С","А"],["Тохс","Олун","Клн","Мсу","Ыам","Бэс","Отй","Атр","Блҕ","Алт","Сэт","Ахс"],["Тохсунньу","Олунньу","Кулун тутар","Муус устар","Ыам ыйын","Бэс ыйын","От ыйын","Атырдьых ыйын","Балаҕан ыйын","Алтынньы","Сэтинньи","ахсынньы"]],[["Т","О","К","М","Ы","Б","О","А","Б","А","С","А"],["Тохс","Олун","Клн","Мсу","Ыам","Бэс","Отй","Атр","Блҕ","Алт","Сэт","Ахс"],["тохсунньу","олунньу","кулун тутар","муус устар","ыам ыйа","бэс ыйа","от ыйа","атырдьых ыйа","балаҕан ыйа","алтынньы","сэтинньи","ахсынньы"]],[["б. э. и.","б. э"],u,u],1,[6,0],["yy/M/d","y, MMM d","y, MMMM d","y 'сыл' MMMM d 'күнэ', EEEE"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[","," ",";","%","+","-","E","×","‰","∞","чыыһыла буотах",":"],["#,##0.###","#,##0%","#,##0.00 ¤","#E0"],"RUB","₽","Арассыыйа солкуобайа",{"JPY":["JP¥","¥"],"RUB":["₽"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    