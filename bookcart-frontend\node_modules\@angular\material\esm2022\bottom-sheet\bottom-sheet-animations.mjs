/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { animate, state, style, transition, trigger, group, query, animateChild, } from '@angular/animations';
import { AnimationCurves, AnimationDurations } from '@angular/material/core';
/** Animations used by the Material bottom sheet. */
export const matBottomSheetAnimations = {
    /** Animation that shows and hides a bottom sheet. */
    bottomSheetState: trigger('state', [
        state('void, hidden', style({ transform: 'translateY(100%)' })),
        state('visible', style({ transform: 'translateY(0%)' })),
        transition('visible => void, visible => hidden', group([
            animate(`${AnimationDurations.COMPLEX} ${AnimationCurves.ACCELERATION_CURVE}`),
            query('@*', animateChild(), { optional: true }),
        ])),
        transition('void => visible', group([
            animate(`${AnimationDurations.EXITING} ${AnimationCurves.DECELERATION_CURVE}`),
            query('@*', animateChild(), { optional: true }),
        ])),
    ]),
};
//# sourceMappingURL=data:application/json;base64,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