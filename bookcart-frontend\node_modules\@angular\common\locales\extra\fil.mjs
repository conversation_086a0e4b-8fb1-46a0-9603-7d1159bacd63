/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
export default [[["hatinggabi", "tanghaling-tapat", "umaga", "madaling-araw", "sa hapon", "sa gabi", "gabi"], ["hatinggabi", "tanghaling-tapat", "nang umaga", "madaling-araw", "tanghali", "ng hapon", "gabi"], ["hatinggabi", "tanghaling-tapat", "nang umaga", "madaling-araw", "tanghali", "ng hapon", "ng gabi"]], [["hatinggabi", "tanghaling-tapat", "umaga", "madaling-araw", "tanghali", "gabi", "gabi"], ["hatinggabi", "tanghaling-tapat", "umaga", "madaling-araw", "tanghali", "hapon", "gabi"], u], ["00:00", "12:00", ["00:00", "06:00"], ["06:00", "12:00"], ["12:00", "16:00"], ["16:00", "18:00"], ["18:00", "24:00"]]];
//# sourceMappingURL=data:application/json;base64,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