/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["ee-TG", [["ŋ", "ɣ"], ["ŋdi", "ɣetrɔ"], u], u, [["k", "d", "b", "k", "y", "f", "m"], ["kɔs", "dzo", "bla", "kuɖ", "yaw", "fiɖ", "mem"], ["kɔsiɖa", "dzoɖa", "blaɖa", "kuɖa", "yawoɖa", "fiɖa", "memleɖa"], ["kɔs", "dzo", "bla", "kuɖ", "yaw", "fiɖ", "mem"]], u, [["d", "d", "t", "a", "d", "m", "s", "d", "a", "k", "a", "d"], ["dzv", "dzd", "ted", "afɔ", "dam", "mas", "sia", "dea", "any", "kel", "ade", "dzm"], ["dzove", "dzodze", "tedoxe", "afɔfĩe", "dama", "masa", "siamlɔm", "deasiamime", "anyɔnyɔ", "kele", "adeɛmekpɔxe", "dzome"]], u, [["HYV", "Yŋ"], u, ["Hafi Yesu Va", "Yesu ŋɔli"]], 1, [6, 0], ["M/d/yy", "MMM d 'lia', y", "MMMM d 'lia' y", "EEEE, MMMM d 'lia' y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{0} {1}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "mnn", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "XOF", "F CFA", "ɣetoɖofe afrikaga CFA franc BCEAO", { "AUD": ["AU$", "$"], "BYN": [u, "р."], "GHS": ["GH₵"], "JPY": ["JP¥", "¥"], "THB": ["฿"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZWUtVEcuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9jb21tb24vbG9jYWxlcy9lZS1URy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7O0dBTUc7QUFFSCwwQ0FBMEM7QUFDMUMsTUFBTSxDQUFDLEdBQUcsU0FBUyxDQUFDO0FBRXBCLFNBQVMsTUFBTSxDQUFDLEdBQVc7SUFDM0IsTUFBTSxDQUFDLEdBQUcsR0FBRyxDQUFDO0lBRWQsSUFBSSxDQUFDLEtBQUssQ0FBQztRQUNQLE9BQU8sQ0FBQyxDQUFDO0lBQ2IsT0FBTyxDQUFDLENBQUM7QUFDVCxDQUFDO0FBRUQsZUFBZSxDQUFDLE9BQU8sRUFBQyxDQUFDLENBQUMsR0FBRyxFQUFDLEdBQUcsQ0FBQyxFQUFDLENBQUMsS0FBSyxFQUFDLE9BQU8sQ0FBQyxFQUFDLENBQUMsQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxDQUFDLEVBQUMsQ0FBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLENBQUMsRUFBQyxDQUFDLFFBQVEsRUFBQyxPQUFPLEVBQUMsT0FBTyxFQUFDLE1BQU0sRUFBQyxRQUFRLEVBQUMsTUFBTSxFQUFDLFNBQVMsQ0FBQyxFQUFDLENBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxDQUFDLENBQUMsRUFBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxDQUFDLEVBQUMsQ0FBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssQ0FBQyxFQUFDLENBQUMsT0FBTyxFQUFDLFFBQVEsRUFBQyxRQUFRLEVBQUMsUUFBUSxFQUFDLE1BQU0sRUFBQyxNQUFNLEVBQUMsU0FBUyxFQUFDLFlBQVksRUFBQyxTQUFTLEVBQUMsTUFBTSxFQUFDLGFBQWEsRUFBQyxPQUFPLENBQUMsQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsS0FBSyxFQUFDLElBQUksQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLGNBQWMsRUFBQyxXQUFXLENBQUMsQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsRUFBQyxDQUFDLFFBQVEsRUFBQyxnQkFBZ0IsRUFBQyxnQkFBZ0IsRUFBQyxzQkFBc0IsQ0FBQyxFQUFDLENBQUMsT0FBTyxFQUFDLFVBQVUsRUFBQyxZQUFZLEVBQUMsZUFBZSxDQUFDLEVBQUMsQ0FBQyxTQUFTLEVBQUMsQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsRUFBQyxDQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxLQUFLLEVBQUMsR0FBRyxDQUFDLEVBQUMsQ0FBQyxXQUFXLEVBQUMsUUFBUSxFQUFDLFdBQVcsRUFBQyxLQUFLLENBQUMsRUFBQyxLQUFLLEVBQUMsT0FBTyxFQUFDLG1DQUFtQyxFQUFDLEVBQUMsS0FBSyxFQUFDLENBQUMsS0FBSyxFQUFDLEdBQUcsQ0FBQyxFQUFDLEtBQUssRUFBQyxDQUFDLENBQUMsRUFBQyxJQUFJLENBQUMsRUFBQyxLQUFLLEVBQUMsQ0FBQyxLQUFLLENBQUMsRUFBQyxLQUFLLEVBQUMsQ0FBQyxLQUFLLEVBQUMsR0FBRyxDQUFDLEVBQUMsS0FBSyxFQUFDLENBQUMsR0FBRyxDQUFDLEVBQUMsS0FBSyxFQUFDLENBQUMsS0FBSyxFQUFDLEdBQUcsQ0FBQyxFQUFDLEVBQUMsS0FBSyxFQUFFLE1BQU0sQ0FBQyxDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5cbi8vIFRISVMgQ09ERSBJUyBHRU5FUkFURUQgLSBETyBOT1QgTU9ESUZZLlxuY29uc3QgdSA9IHVuZGVmaW5lZDtcblxuZnVuY3Rpb24gcGx1cmFsKHZhbDogbnVtYmVyKTogbnVtYmVyIHtcbmNvbnN0IG4gPSB2YWw7XG5cbmlmIChuID09PSAxKVxuICAgIHJldHVybiAxO1xucmV0dXJuIDU7XG59XG5cbmV4cG9ydCBkZWZhdWx0IFtcImVlLVRHXCIsW1tcIsWLXCIsXCLJo1wiXSxbXCLFi2RpXCIsXCLJo2V0csmUXCJdLHVdLHUsW1tcImtcIixcImRcIixcImJcIixcImtcIixcInlcIixcImZcIixcIm1cIl0sW1wia8mUc1wiLFwiZHpvXCIsXCJibGFcIixcImt1yZZcIixcInlhd1wiLFwiZmnJllwiLFwibWVtXCJdLFtcImvJlHNpyZZhXCIsXCJkem/JlmFcIixcImJsYcmWYVwiLFwia3XJlmFcIixcInlhd2/JlmFcIixcImZpyZZhXCIsXCJtZW1sZcmWYVwiXSxbXCJryZRzXCIsXCJkem9cIixcImJsYVwiLFwia3XJllwiLFwieWF3XCIsXCJmacmWXCIsXCJtZW1cIl1dLHUsW1tcImRcIixcImRcIixcInRcIixcImFcIixcImRcIixcIm1cIixcInNcIixcImRcIixcImFcIixcImtcIixcImFcIixcImRcIl0sW1wiZHp2XCIsXCJkemRcIixcInRlZFwiLFwiYWbJlFwiLFwiZGFtXCIsXCJtYXNcIixcInNpYVwiLFwiZGVhXCIsXCJhbnlcIixcImtlbFwiLFwiYWRlXCIsXCJkem1cIl0sW1wiZHpvdmVcIixcImR6b2R6ZVwiLFwidGVkb3hlXCIsXCJhZsmUZsSpZVwiLFwiZGFtYVwiLFwibWFzYVwiLFwic2lhbWzJlG1cIixcImRlYXNpYW1pbWVcIixcImFuecmUbnnJlFwiLFwia2VsZVwiLFwiYWRlyZttZWtwyZR4ZVwiLFwiZHpvbWVcIl1dLHUsW1tcIkhZVlwiLFwiWcWLXCJdLHUsW1wiSGFmaSBZZXN1IFZhXCIsXCJZZXN1IMWLyZRsaVwiXV0sMSxbNiwwXSxbXCJNL2QveXlcIixcIk1NTSBkICdsaWEnLCB5XCIsXCJNTU1NIGQgJ2xpYScgeVwiLFwiRUVFRSwgTU1NTSBkICdsaWEnIHlcIl0sW1wiSEg6bW1cIixcIkhIOm1tOnNzXCIsXCJISDptbTpzcyB6XCIsXCJISDptbTpzcyB6enp6XCJdLFtcInswfSB7MX1cIix1LHUsdV0sW1wiLlwiLFwiLFwiLFwiO1wiLFwiJVwiLFwiK1wiLFwiLVwiLFwiRVwiLFwiw5dcIixcIuKAsFwiLFwi4oieXCIsXCJtbm5cIixcIjpcIl0sW1wiIywjIzAuIyMjXCIsXCIjLCMjMCVcIixcIsKkIywjIzAuMDBcIixcIiNFMFwiXSxcIlhPRlwiLFwiRuKAr0NGQVwiLFwiyaNldG/Jlm9mZSBhZnJpa2FnYSBDRkEgZnJhbmMgQkNFQU9cIix7XCJBVURcIjpbXCJBVSRcIixcIiRcIl0sXCJCWU5cIjpbdSxcItGALlwiXSxcIkdIU1wiOltcIkdI4oK1XCJdLFwiSlBZXCI6W1wiSlDCpVwiLFwiwqVcIl0sXCJUSEJcIjpbXCLguL9cIl0sXCJVU0RcIjpbXCJVUyRcIixcIiRcIl19LFwibHRyXCIsIHBsdXJhbF07XG4iXX0=