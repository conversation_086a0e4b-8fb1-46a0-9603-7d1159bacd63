/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["sd-Deva", [["AM", "PM"], u, ["मंझंदि खां पहिंरियों", "मंझंदि खां पोइ"]], [["AM", "PM"], u, u], [["आ", "सू", "मं", "बु॒", "वि", "जु", "छं"], ["आर्त", "सू", "मंग", "बु॒ध", "विस", "जुम", "छंछ"], ["आर्तवार", "सूमर", "मंगलु", "बु॒धर", "विस्पत", "जुमो", "छंछर"], ["आर्त", "सू", "मंग", "बु॒ध", "विस", "जुम", "छंछ"]], [["आ", "सू", "मं", "बु॒", "वि", "जु", "छं"], ["आ", "सू", "मं", "बुध", "विस", "जु", "छंछ"], ["आर्त", "सू", "मं", "बु॒ध", "विस", "जुम", "छंछ"], ["आर्त", "सू", "मंग", "बु॒ध", "विस", "जुम", "छंछ"]], [["ज", "फ़", "मा", "अ", "मा", "जू", "जु", "अग", "स", "ऑ", "न", "डि"], ["जन", "फर", "मार्च", "अप्रै", "मई", "जून", "जु", "अग", "सप्टे", "ऑक्टो", "नवं", "डिसं"], ["जनवरी", "फरवरी", "मार्चु", "अप्रैल", "मई", "जून", "जुलाई", "अगस्ट", "सप्टेंबर", "ऑक्टोबर", "नवंबर", "डिसंबर"]], [["ज", "फ़", "म", "अ", "मा", "जू", "जु", "अग", "स", "ऑ", "न", "डि"], ["जन", "फर", "मार्च", "अप्रै", "मई", "जून", "जुला", "अग", "सप्टे", "ऑक्टो", "नवं", "डिसं"], ["जनवरी", "फरवरी", "मार्चु", "अप्रैल", "मई", "जून", "जुलाई", "अगस्ट", "सप्टेंबर", "ऑक्टोबर", "नवंबर", "डिसंबर"]], [["बीसी", "एडी"], u, u], 0, [0, 0], ["M/d/yy", "MMM d, y", "MMMM d, y", "EEEE, MMMM d, y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1}, {0}", u, "{1} ते {0}", u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "INR", "₹", "हिंदुस्तानी रुपयो", {}, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic2QtRGV2YS5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uL3BhY2thZ2VzL2NvbW1vbi9sb2NhbGVzL3NkLURldmEudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7OztHQU1HO0FBRUgsMENBQTBDO0FBQzFDLE1BQU0sQ0FBQyxHQUFHLFNBQVMsQ0FBQztBQUVwQixTQUFTLE1BQU0sQ0FBQyxHQUFXO0lBQzNCLE1BQU0sQ0FBQyxHQUFHLEdBQUcsQ0FBQztJQUVkLE9BQU8sQ0FBQyxDQUFDO0FBQ1QsQ0FBQztBQUVELGVBQWUsQ0FBQyxTQUFTLEVBQUMsQ0FBQyxDQUFDLElBQUksRUFBQyxJQUFJLENBQUMsRUFBQyxDQUFDLEVBQUMsQ0FBQyxzQkFBc0IsRUFBQyxnQkFBZ0IsQ0FBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDLElBQUksRUFBQyxJQUFJLENBQUMsRUFBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDLEdBQUcsRUFBQyxJQUFJLEVBQUMsSUFBSSxFQUFDLEtBQUssRUFBQyxJQUFJLEVBQUMsSUFBSSxFQUFDLElBQUksQ0FBQyxFQUFDLENBQUMsTUFBTSxFQUFDLElBQUksRUFBQyxLQUFLLEVBQUMsTUFBTSxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxDQUFDLEVBQUMsQ0FBQyxTQUFTLEVBQUMsTUFBTSxFQUFDLE9BQU8sRUFBQyxPQUFPLEVBQUMsUUFBUSxFQUFDLE1BQU0sRUFBQyxNQUFNLENBQUMsRUFBQyxDQUFDLE1BQU0sRUFBQyxJQUFJLEVBQUMsS0FBSyxFQUFDLE1BQU0sRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssQ0FBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDLEdBQUcsRUFBQyxJQUFJLEVBQUMsSUFBSSxFQUFDLEtBQUssRUFBQyxJQUFJLEVBQUMsSUFBSSxFQUFDLElBQUksQ0FBQyxFQUFDLENBQUMsR0FBRyxFQUFDLElBQUksRUFBQyxJQUFJLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxJQUFJLEVBQUMsS0FBSyxDQUFDLEVBQUMsQ0FBQyxNQUFNLEVBQUMsSUFBSSxFQUFDLElBQUksRUFBQyxNQUFNLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLENBQUMsRUFBQyxDQUFDLE1BQU0sRUFBQyxJQUFJLEVBQUMsS0FBSyxFQUFDLE1BQU0sRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssQ0FBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDLEdBQUcsRUFBQyxJQUFJLEVBQUMsSUFBSSxFQUFDLEdBQUcsRUFBQyxJQUFJLEVBQUMsSUFBSSxFQUFDLElBQUksRUFBQyxJQUFJLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsSUFBSSxDQUFDLEVBQUMsQ0FBQyxJQUFJLEVBQUMsSUFBSSxFQUFDLE9BQU8sRUFBQyxPQUFPLEVBQUMsSUFBSSxFQUFDLEtBQUssRUFBQyxJQUFJLEVBQUMsSUFBSSxFQUFDLE9BQU8sRUFBQyxPQUFPLEVBQUMsS0FBSyxFQUFDLE1BQU0sQ0FBQyxFQUFDLENBQUMsT0FBTyxFQUFDLE9BQU8sRUFBQyxRQUFRLEVBQUMsUUFBUSxFQUFDLElBQUksRUFBQyxLQUFLLEVBQUMsT0FBTyxFQUFDLE9BQU8sRUFBQyxVQUFVLEVBQUMsU0FBUyxFQUFDLE9BQU8sRUFBQyxRQUFRLENBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQyxHQUFHLEVBQUMsSUFBSSxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsSUFBSSxFQUFDLElBQUksRUFBQyxJQUFJLEVBQUMsSUFBSSxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLElBQUksQ0FBQyxFQUFDLENBQUMsSUFBSSxFQUFDLElBQUksRUFBQyxPQUFPLEVBQUMsT0FBTyxFQUFDLElBQUksRUFBQyxLQUFLLEVBQUMsTUFBTSxFQUFDLElBQUksRUFBQyxPQUFPLEVBQUMsT0FBTyxFQUFDLEtBQUssRUFBQyxNQUFNLENBQUMsRUFBQyxDQUFDLE9BQU8sRUFBQyxPQUFPLEVBQUMsUUFBUSxFQUFDLFFBQVEsRUFBQyxJQUFJLEVBQUMsS0FBSyxFQUFDLE9BQU8sRUFBQyxPQUFPLEVBQUMsVUFBVSxFQUFDLFNBQVMsRUFBQyxPQUFPLEVBQUMsUUFBUSxDQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsTUFBTSxFQUFDLEtBQUssQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsRUFBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDLEVBQUMsQ0FBQyxRQUFRLEVBQUMsVUFBVSxFQUFDLFdBQVcsRUFBQyxpQkFBaUIsQ0FBQyxFQUFDLENBQUMsUUFBUSxFQUFDLFdBQVcsRUFBQyxhQUFhLEVBQUMsZ0JBQWdCLENBQUMsRUFBQyxDQUFDLFVBQVUsRUFBQyxDQUFDLEVBQUMsWUFBWSxFQUFDLENBQUMsQ0FBQyxFQUFDLENBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEtBQUssRUFBQyxHQUFHLENBQUMsRUFBQyxDQUFDLFdBQVcsRUFBQyxRQUFRLEVBQUMsWUFBWSxFQUFDLEtBQUssQ0FBQyxFQUFDLEtBQUssRUFBQyxHQUFHLEVBQUMsbUJBQW1CLEVBQUMsRUFBRSxFQUFDLEtBQUssRUFBRSxNQUFNLENBQUMsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuXG4vLyBUSElTIENPREUgSVMgR0VORVJBVEVEIC0gRE8gTk9UIE1PRElGWS5cbmNvbnN0IHUgPSB1bmRlZmluZWQ7XG5cbmZ1bmN0aW9uIHBsdXJhbCh2YWw6IG51bWJlcik6IG51bWJlciB7XG5jb25zdCBuID0gdmFsO1xuXG5yZXR1cm4gNTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgW1wic2QtRGV2YVwiLFtbXCJBTVwiLFwiUE1cIl0sdSxbXCLgpK7gpILgpJ3gpILgpKbgpL8g4KSW4KS+4KSCIOCkquCkueCkv+CkguCksOCkv+Ckr+Cli+CkglwiLFwi4KSu4KSC4KSd4KSC4KSm4KS/IOCkluCkvuCkgiDgpKrgpYvgpIdcIl1dLFtbXCJBTVwiLFwiUE1cIl0sdSx1XSxbW1wi4KSGXCIsXCLgpLjgpYJcIixcIuCkruCkglwiLFwi4KSs4KWB4KWSXCIsXCLgpLXgpL9cIixcIuCknOClgVwiLFwi4KSb4KSCXCJdLFtcIuCkhuCksOCljeCkpFwiLFwi4KS44KWCXCIsXCLgpK7gpILgpJdcIixcIuCkrOClgeClkuCkp1wiLFwi4KS14KS/4KS4XCIsXCLgpJzgpYHgpK5cIixcIuCkm+CkguCkm1wiXSxbXCLgpIbgpLDgpY3gpKTgpLXgpL7gpLBcIixcIuCkuOClguCkruCksFwiLFwi4KSu4KSC4KSX4KSy4KWBXCIsXCLgpKzgpYHgpZLgpKfgpLBcIixcIuCkteCkv+CkuOCljeCkquCkpFwiLFwi4KSc4KWB4KSu4KWLXCIsXCLgpJvgpILgpJvgpLBcIl0sW1wi4KSG4KSw4KWN4KSkXCIsXCLgpLjgpYJcIixcIuCkruCkguCkl1wiLFwi4KSs4KWB4KWS4KSnXCIsXCLgpLXgpL/gpLhcIixcIuCknOClgeCkrlwiLFwi4KSb4KSC4KSbXCJdXSxbW1wi4KSGXCIsXCLgpLjgpYJcIixcIuCkruCkglwiLFwi4KSs4KWB4KWSXCIsXCLgpLXgpL9cIixcIuCknOClgVwiLFwi4KSb4KSCXCJdLFtcIuCkhlwiLFwi4KS44KWCXCIsXCLgpK7gpIJcIixcIuCkrOClgeCkp1wiLFwi4KS14KS/4KS4XCIsXCLgpJzgpYFcIixcIuCkm+CkguCkm1wiXSxbXCLgpIbgpLDgpY3gpKRcIixcIuCkuOClglwiLFwi4KSu4KSCXCIsXCLgpKzgpYHgpZLgpKdcIixcIuCkteCkv+CkuFwiLFwi4KSc4KWB4KSuXCIsXCLgpJvgpILgpJtcIl0sW1wi4KSG4KSw4KWN4KSkXCIsXCLgpLjgpYJcIixcIuCkruCkguCkl1wiLFwi4KSs4KWB4KWS4KSnXCIsXCLgpLXgpL/gpLhcIixcIuCknOClgeCkrlwiLFwi4KSb4KSC4KSbXCJdXSxbW1wi4KScXCIsXCLgpKvgpLxcIixcIuCkruCkvlwiLFwi4KSFXCIsXCLgpK7gpL5cIixcIuCknOClglwiLFwi4KSc4KWBXCIsXCLgpIXgpJdcIixcIuCkuFwiLFwi4KSRXCIsXCLgpKhcIixcIuCkoeCkv1wiXSxbXCLgpJzgpKhcIixcIuCkq+CksFwiLFwi4KSu4KS+4KSw4KWN4KSaXCIsXCLgpIXgpKrgpY3gpLDgpYhcIixcIuCkruCkiFwiLFwi4KSc4KWC4KSoXCIsXCLgpJzgpYFcIixcIuCkheCkl1wiLFwi4KS44KSq4KWN4KSf4KWHXCIsXCLgpJHgpJXgpY3gpJ/gpYtcIixcIuCkqOCkteCkglwiLFwi4KSh4KS/4KS44KSCXCJdLFtcIuCknOCkqOCkteCksOClgFwiLFwi4KSr4KSw4KS14KSw4KWAXCIsXCLgpK7gpL7gpLDgpY3gpJrgpYFcIixcIuCkheCkquCljeCksOCliOCkslwiLFwi4KSu4KSIXCIsXCLgpJzgpYLgpKhcIixcIuCknOClgeCksuCkvuCkiFwiLFwi4KSF4KSX4KS44KWN4KSfXCIsXCLgpLjgpKrgpY3gpJ/gpYfgpILgpKzgpLBcIixcIuCkkeCkleCljeCkn+Cli+CkrOCksFwiLFwi4KSo4KS14KSC4KSs4KSwXCIsXCLgpKHgpL/gpLjgpILgpKzgpLBcIl1dLFtbXCLgpJxcIixcIuCkq+CkvFwiLFwi4KSuXCIsXCLgpIVcIixcIuCkruCkvlwiLFwi4KSc4KWCXCIsXCLgpJzgpYFcIixcIuCkheCkl1wiLFwi4KS4XCIsXCLgpJFcIixcIuCkqFwiLFwi4KSh4KS/XCJdLFtcIuCknOCkqFwiLFwi4KSr4KSwXCIsXCLgpK7gpL7gpLDgpY3gpJpcIixcIuCkheCkquCljeCksOCliFwiLFwi4KSu4KSIXCIsXCLgpJzgpYLgpKhcIixcIuCknOClgeCksuCkvlwiLFwi4KSF4KSXXCIsXCLgpLjgpKrgpY3gpJ/gpYdcIixcIuCkkeCkleCljeCkn+Cli1wiLFwi4KSo4KS14KSCXCIsXCLgpKHgpL/gpLjgpIJcIl0sW1wi4KSc4KSo4KS14KSw4KWAXCIsXCLgpKvgpLDgpLXgpLDgpYBcIixcIuCkruCkvuCksOCljeCkmuClgVwiLFwi4KSF4KSq4KWN4KSw4KWI4KSyXCIsXCLgpK7gpIhcIixcIuCknOClguCkqFwiLFwi4KSc4KWB4KSy4KS+4KSIXCIsXCLgpIXgpJfgpLjgpY3gpJ9cIixcIuCkuOCkquCljeCkn+Clh+CkguCkrOCksFwiLFwi4KSR4KSV4KWN4KSf4KWL4KSs4KSwXCIsXCLgpKjgpLXgpILgpKzgpLBcIixcIuCkoeCkv+CkuOCkguCkrOCksFwiXV0sW1tcIuCkrOClgOCkuOClgFwiLFwi4KSP4KSh4KWAXCJdLHUsdV0sMCxbMCwwXSxbXCJNL2QveXlcIixcIk1NTSBkLCB5XCIsXCJNTU1NIGQsIHlcIixcIkVFRUUsIE1NTU0gZCwgeVwiXSxbXCJoOm1tIGFcIixcImg6bW06c3MgYVwiLFwiaDptbTpzcyBhIHpcIixcImg6bW06c3MgYSB6enp6XCJdLFtcInsxfSwgezB9XCIsdSxcInsxfSDgpKTgpYcgezB9XCIsdV0sW1wiLlwiLFwiLFwiLFwiO1wiLFwiJVwiLFwiK1wiLFwiLVwiLFwiRVwiLFwiw5dcIixcIuKAsFwiLFwi4oieXCIsXCJOYU5cIixcIjpcIl0sW1wiIywjIzAuIyMjXCIsXCIjLCMjMCVcIixcIsKkwqAjLCMjMC4wMFwiLFwiI0UwXCJdLFwiSU5SXCIsXCLigrlcIixcIuCkueCkv+CkguCkpuClgeCkuOCljeCkpOCkvuCkqOClgCDgpLDgpYHgpKrgpK/gpYtcIix7fSxcImx0clwiLCBwbHVyYWxdO1xuIl19