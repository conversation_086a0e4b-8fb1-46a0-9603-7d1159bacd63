/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["ig", [["A.M.", "P.M."], u, ["N’ụt<PERSON>tụ", "N’abali"]], [["A.M.", "P.M."], u, u], [["S", "M", "T", "W", "T", "F", "S"], ["<PERSON>ọn", "<PERSON>ọn", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Fra<PERSON>", "Sat"], ["<PERSON>ọndee", "<PERSON>ọ<PERSON>", "<PERSON><PERSON><PERSON><PERSON>e", "<PERSON>ez<PERSON><PERSON>", "<PERSON><PERSON><PERSON>z<PERSON>e", "<PERSON>aịdee", "<PERSON>tọdee"], ["<PERSON>ọ<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sat"]], u, [["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "M", "J", "J", "Ọ", "<PERSON>", "Ọ", "<PERSON>", "<PERSON>"], ["<PERSON>", "<PERSON>", "<PERSON>a", "<PERSON><PERSON>r", "<PERSON>e", "<PERSON>u", "<PERSON>", "Ọgọ", "<PERSON>", "Ọkt", "<PERSON>", "<PERSON><PERSON>"], ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>r<PERSON>war<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>l", "<PERSON>e", "<PERSON>un", "<PERSON>a<PERSON>", "Ọgọọst", "<PERSON>em<PERSON>", "Ọktoba", "<PERSON>emba", "Disemba"]], u, [["T.K.", "A.K."], u, ["Tupu Kraist", "Afọ Kraịst"]], 1, [6, 0], ["d/M/yy", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1}, {0}", u, "{1} 'na' {0}", u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "NGN", "₦", "Naịra", { "NGN": ["₦"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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