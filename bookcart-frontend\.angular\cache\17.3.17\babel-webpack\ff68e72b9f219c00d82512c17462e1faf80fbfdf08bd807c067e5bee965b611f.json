{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"./shared/components/navbar/navbar.component\";\nexport class AppComponent {\n  constructor() {\n    this.title = 'BookCart - Online Book Store';\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 3,\n      vars: 0,\n      consts: [[1, \"main-content\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"app-navbar\");\n          i0.ɵɵelementStart(1, \"main\", 0);\n          i0.ɵɵelement(2, \"router-outlet\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      dependencies: [i1.RouterOutlet, i2.NavbarComponent],\n      styles: [\".main-content[_ngcontent-%COMP%] {\\n      margin-top: 64px; \\n\\n      min-height: calc(100vh - 64px);\\n      background-color: #f5f5f5;\\n    }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0lBQ0k7TUFDRSxnQkFBZ0IsRUFBRSxxQkFBcUI7TUFDdkMsOEJBQThCO01BQzlCLHlCQUF5QjtJQUMzQiIsInNvdXJjZXNDb250ZW50IjpbIlxuICAgIC5tYWluLWNvbnRlbnQge1xuICAgICAgbWFyZ2luLXRvcDogNjRweDsgLyogSGVpZ2h0IG9mIG5hdmJhciAqL1xuICAgICAgbWluLWhlaWdodDogY2FsYygxMDB2aCAtIDY0cHgpO1xuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTtcbiAgICB9XG4gICJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "title", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\BookCart\\bookcart-frontend\\src\\app\\app.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-root',\n  template: `\n    <app-navbar></app-navbar>\n    <main class=\"main-content\">\n      <router-outlet></router-outlet>\n    </main>\n  `,\n  styles: [`\n    .main-content {\n      margin-top: 64px; /* Height of navbar */\n      min-height: calc(100vh - 64px);\n      background-color: #f5f5f5;\n    }\n  `]\n})\nexport class AppComponent {\n  title = 'BookCart - Online Book Store';\n}\n"], "mappings": ";;;AAkBA,OAAM,MAAOA,YAAY;EAhBzBC,YAAA;IAiBE,KAAAC,KAAK,GAAG,8BAA8B;;;;uBAD3BF,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAbrBE,EAAA,CAAAC,SAAA,iBAAyB;UACzBD,EAAA,CAAAE,cAAA,cAA2B;UACzBF,EAAA,CAAAC,SAAA,oBAA+B;UACjCD,EAAA,CAAAG,YAAA,EAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}