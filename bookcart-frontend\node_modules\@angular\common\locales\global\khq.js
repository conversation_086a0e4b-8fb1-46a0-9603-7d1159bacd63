/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['khq'] = ["khq",[["Addu<PERSON>","Al<PERSON><PERSON>"],u,u],u,[["H","T","T","L","L","L","S"],["Alh","Ati","Ata","Ala","Alm","Alj","Ass"],["Alhadi","<PERSON><PERSON>","<PERSON>alata","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>"],["<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>"]],u,[["Ž","F","M","A","M","<PERSON>","Ž","U","S","O","<PERSON>","<PERSON>"],["<PERSON>an","<PERSON>e","<PERSON>","Awi","Me","<PERSON>uw","<PERSON>uy","<PERSON>t","<PERSON>k","<PERSON>t","<PERSON>o","<PERSON>"],["<PERSON>anwiye","<PERSON>ewiriye","<PERSON>i","<PERSON>wiril","<PERSON>","<PERSON>uweŋ","<PERSON>uyye","<PERSON>t","<PERSON>ktanbur","<PERSON>toobur","Noowanbur","Deesanbur"]],u,[["IJ","IZ"],u,["Isaa jine","Isaa jamanoo"]],1,[6,0],["d/M/y","d MMM, y","d MMMM y","EEEE d MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],["."," ",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","#,##0.00¤","#E0"],"XOF","F CFA","CFA Fraŋ (BCEAO)",{"JPY":["JP¥","¥"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    