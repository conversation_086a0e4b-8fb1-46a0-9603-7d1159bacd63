"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.findOutputsOnElementWithAttr = exports.findOutputsOnElementWithTag = exports.findInputsOnElementWithAttr = exports.findInputsOnElementWithTag = void 0;
const elements_1 = require("./elements");
/** Finds the specified Angular @Input in the given elements with tag name. */
function findInputsOnElementWithTag(html, inputName, tagNames) {
    return [
        // Inputs can be also used without brackets (e.g. `<mat-toolbar color="primary">`)
        ...(0, elements_1.findAttributeOnElementWithTag)(html, inputName, tagNames),
        // Add one column to the mapped offset because the first bracket for the @Input
        // is part of the attribute and therefore also part of the offset. We only want to return
        // the offset for the inner name of the bracketed input.
        ...(0, elements_1.findAttributeOnElementWithTag)(html, `[${inputName}]`, tagNames).map(offset => offset + 1),
    ];
}
exports.findInputsOnElementWithTag = findInputsOnElementWithTag;
/** Finds the specified Angular @Input in elements that have one of the specified attributes. */
function findInputsOnElementWithAttr(html, inputName, attrs) {
    return [
        // Inputs can be also used without brackets (e.g. `<button mat-button color="primary">`)
        ...(0, elements_1.findAttributeOnElementWithAttrs)(html, inputName, attrs),
        // Add one column to the mapped offset because the first bracket for the @Input
        // is part of the attribute and therefore also part of the offset. We only want to return
        // the offset for the inner name of the bracketed input.
        ...(0, elements_1.findAttributeOnElementWithAttrs)(html, `[${inputName}]`, attrs).map(offset => offset + 1),
    ];
}
exports.findInputsOnElementWithAttr = findInputsOnElementWithAttr;
/** Finds the specified Angular @Output in the given elements with tag name. */
function findOutputsOnElementWithTag(html, outputName, tagNames) {
    // Add one column to the mapped offset because the first parenthesis for the @Output
    // is part of the attribute and therefore also part of the offset. We only want to return
    // the offset for the inner name of the output.
    return (0, elements_1.findAttributeOnElementWithTag)(html, `(${outputName})`, tagNames).map(offset => offset + 1);
}
exports.findOutputsOnElementWithTag = findOutputsOnElementWithTag;
/** Finds the specified Angular @Output in elements that have one of the specified attributes. */
function findOutputsOnElementWithAttr(html, outputName, attrs) {
    // Add one column to the mapped offset because the first bracket for the @Output
    // is part of the attribute and therefore also part of the offset. We only want to return
    // the offset for the inner name of the output.
    return (0, elements_1.findAttributeOnElementWithAttrs)(html, `(${outputName})`, attrs).map(offset => offset + 1);
}
exports.findOutputsOnElementWithAttr = findOutputsOnElementWithAttr;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiYW5ndWxhci5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uLy4uLy4uL3NyYy9jZGsvc2NoZW1hdGljcy9uZy11cGRhdGUvaHRtbC1wYXJzaW5nL2FuZ3VsYXIudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUFBOzs7Ozs7R0FNRzs7O0FBRUgseUNBQTBGO0FBRTFGLDhFQUE4RTtBQUM5RSxTQUFnQiwwQkFBMEIsQ0FBQyxJQUFZLEVBQUUsU0FBaUIsRUFBRSxRQUFrQjtJQUM1RixPQUFPO1FBQ0wsa0ZBQWtGO1FBQ2xGLEdBQUcsSUFBQSx3Q0FBNkIsRUFBQyxJQUFJLEVBQUUsU0FBUyxFQUFFLFFBQVEsQ0FBQztRQUMzRCwrRUFBK0U7UUFDL0UseUZBQXlGO1FBQ3pGLHdEQUF3RDtRQUN4RCxHQUFHLElBQUEsd0NBQTZCLEVBQUMsSUFBSSxFQUFFLElBQUksU0FBUyxHQUFHLEVBQUUsUUFBUSxDQUFDLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxFQUFFLENBQUMsTUFBTSxHQUFHLENBQUMsQ0FBQztLQUM3RixDQUFDO0FBQ0osQ0FBQztBQVRELGdFQVNDO0FBRUQsZ0dBQWdHO0FBQ2hHLFNBQWdCLDJCQUEyQixDQUFDLElBQVksRUFBRSxTQUFpQixFQUFFLEtBQWU7SUFDMUYsT0FBTztRQUNMLHdGQUF3RjtRQUN4RixHQUFHLElBQUEsMENBQStCLEVBQUMsSUFBSSxFQUFFLFNBQVMsRUFBRSxLQUFLLENBQUM7UUFDMUQsK0VBQStFO1FBQy9FLHlGQUF5RjtRQUN6Rix3REFBd0Q7UUFDeEQsR0FBRyxJQUFBLDBDQUErQixFQUFDLElBQUksRUFBRSxJQUFJLFNBQVMsR0FBRyxFQUFFLEtBQUssQ0FBQyxDQUFDLEdBQUcsQ0FBQyxNQUFNLENBQUMsRUFBRSxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUM7S0FDNUYsQ0FBQztBQUNKLENBQUM7QUFURCxrRUFTQztBQUVELCtFQUErRTtBQUMvRSxTQUFnQiwyQkFBMkIsQ0FBQyxJQUFZLEVBQUUsVUFBa0IsRUFBRSxRQUFrQjtJQUM5RixvRkFBb0Y7SUFDcEYseUZBQXlGO0lBQ3pGLCtDQUErQztJQUMvQyxPQUFPLElBQUEsd0NBQTZCLEVBQUMsSUFBSSxFQUFFLElBQUksVUFBVSxHQUFHLEVBQUUsUUFBUSxDQUFDLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxFQUFFLENBQUMsTUFBTSxHQUFHLENBQUMsQ0FBQyxDQUFDO0FBQ3BHLENBQUM7QUFMRCxrRUFLQztBQUVELGlHQUFpRztBQUNqRyxTQUFnQiw0QkFBNEIsQ0FBQyxJQUFZLEVBQUUsVUFBa0IsRUFBRSxLQUFlO0lBQzVGLGdGQUFnRjtJQUNoRix5RkFBeUY7SUFDekYsK0NBQStDO0lBQy9DLE9BQU8sSUFBQSwwQ0FBK0IsRUFBQyxJQUFJLEVBQUUsSUFBSSxVQUFVLEdBQUcsRUFBRSxLQUFLLENBQUMsQ0FBQyxHQUFHLENBQUMsTUFBTSxDQUFDLEVBQUUsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDLENBQUM7QUFDbkcsQ0FBQztBQUxELG9FQUtDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5cbmltcG9ydCB7ZmluZEF0dHJpYnV0ZU9uRWxlbWVudFdpdGhBdHRycywgZmluZEF0dHJpYnV0ZU9uRWxlbWVudFdpdGhUYWd9IGZyb20gJy4vZWxlbWVudHMnO1xuXG4vKiogRmluZHMgdGhlIHNwZWNpZmllZCBBbmd1bGFyIEBJbnB1dCBpbiB0aGUgZ2l2ZW4gZWxlbWVudHMgd2l0aCB0YWcgbmFtZS4gKi9cbmV4cG9ydCBmdW5jdGlvbiBmaW5kSW5wdXRzT25FbGVtZW50V2l0aFRhZyhodG1sOiBzdHJpbmcsIGlucHV0TmFtZTogc3RyaW5nLCB0YWdOYW1lczogc3RyaW5nW10pIHtcbiAgcmV0dXJuIFtcbiAgICAvLyBJbnB1dHMgY2FuIGJlIGFsc28gdXNlZCB3aXRob3V0IGJyYWNrZXRzIChlLmcuIGA8bWF0LXRvb2xiYXIgY29sb3I9XCJwcmltYXJ5XCI+YClcbiAgICAuLi5maW5kQXR0cmlidXRlT25FbGVtZW50V2l0aFRhZyhodG1sLCBpbnB1dE5hbWUsIHRhZ05hbWVzKSxcbiAgICAvLyBBZGQgb25lIGNvbHVtbiB0byB0aGUgbWFwcGVkIG9mZnNldCBiZWNhdXNlIHRoZSBmaXJzdCBicmFja2V0IGZvciB0aGUgQElucHV0XG4gICAgLy8gaXMgcGFydCBvZiB0aGUgYXR0cmlidXRlIGFuZCB0aGVyZWZvcmUgYWxzbyBwYXJ0IG9mIHRoZSBvZmZzZXQuIFdlIG9ubHkgd2FudCB0byByZXR1cm5cbiAgICAvLyB0aGUgb2Zmc2V0IGZvciB0aGUgaW5uZXIgbmFtZSBvZiB0aGUgYnJhY2tldGVkIGlucHV0LlxuICAgIC4uLmZpbmRBdHRyaWJ1dGVPbkVsZW1lbnRXaXRoVGFnKGh0bWwsIGBbJHtpbnB1dE5hbWV9XWAsIHRhZ05hbWVzKS5tYXAob2Zmc2V0ID0+IG9mZnNldCArIDEpLFxuICBdO1xufVxuXG4vKiogRmluZHMgdGhlIHNwZWNpZmllZCBBbmd1bGFyIEBJbnB1dCBpbiBlbGVtZW50cyB0aGF0IGhhdmUgb25lIG9mIHRoZSBzcGVjaWZpZWQgYXR0cmlidXRlcy4gKi9cbmV4cG9ydCBmdW5jdGlvbiBmaW5kSW5wdXRzT25FbGVtZW50V2l0aEF0dHIoaHRtbDogc3RyaW5nLCBpbnB1dE5hbWU6IHN0cmluZywgYXR0cnM6IHN0cmluZ1tdKSB7XG4gIHJldHVybiBbXG4gICAgLy8gSW5wdXRzIGNhbiBiZSBhbHNvIHVzZWQgd2l0aG91dCBicmFja2V0cyAoZS5nLiBgPGJ1dHRvbiBtYXQtYnV0dG9uIGNvbG9yPVwicHJpbWFyeVwiPmApXG4gICAgLi4uZmluZEF0dHJpYnV0ZU9uRWxlbWVudFdpdGhBdHRycyhodG1sLCBpbnB1dE5hbWUsIGF0dHJzKSxcbiAgICAvLyBBZGQgb25lIGNvbHVtbiB0byB0aGUgbWFwcGVkIG9mZnNldCBiZWNhdXNlIHRoZSBmaXJzdCBicmFja2V0IGZvciB0aGUgQElucHV0XG4gICAgLy8gaXMgcGFydCBvZiB0aGUgYXR0cmlidXRlIGFuZCB0aGVyZWZvcmUgYWxzbyBwYXJ0IG9mIHRoZSBvZmZzZXQuIFdlIG9ubHkgd2FudCB0byByZXR1cm5cbiAgICAvLyB0aGUgb2Zmc2V0IGZvciB0aGUgaW5uZXIgbmFtZSBvZiB0aGUgYnJhY2tldGVkIGlucHV0LlxuICAgIC4uLmZpbmRBdHRyaWJ1dGVPbkVsZW1lbnRXaXRoQXR0cnMoaHRtbCwgYFske2lucHV0TmFtZX1dYCwgYXR0cnMpLm1hcChvZmZzZXQgPT4gb2Zmc2V0ICsgMSksXG4gIF07XG59XG5cbi8qKiBGaW5kcyB0aGUgc3BlY2lmaWVkIEFuZ3VsYXIgQE91dHB1dCBpbiB0aGUgZ2l2ZW4gZWxlbWVudHMgd2l0aCB0YWcgbmFtZS4gKi9cbmV4cG9ydCBmdW5jdGlvbiBmaW5kT3V0cHV0c09uRWxlbWVudFdpdGhUYWcoaHRtbDogc3RyaW5nLCBvdXRwdXROYW1lOiBzdHJpbmcsIHRhZ05hbWVzOiBzdHJpbmdbXSkge1xuICAvLyBBZGQgb25lIGNvbHVtbiB0byB0aGUgbWFwcGVkIG9mZnNldCBiZWNhdXNlIHRoZSBmaXJzdCBwYXJlbnRoZXNpcyBmb3IgdGhlIEBPdXRwdXRcbiAgLy8gaXMgcGFydCBvZiB0aGUgYXR0cmlidXRlIGFuZCB0aGVyZWZvcmUgYWxzbyBwYXJ0IG9mIHRoZSBvZmZzZXQuIFdlIG9ubHkgd2FudCB0byByZXR1cm5cbiAgLy8gdGhlIG9mZnNldCBmb3IgdGhlIGlubmVyIG5hbWUgb2YgdGhlIG91dHB1dC5cbiAgcmV0dXJuIGZpbmRBdHRyaWJ1dGVPbkVsZW1lbnRXaXRoVGFnKGh0bWwsIGAoJHtvdXRwdXROYW1lfSlgLCB0YWdOYW1lcykubWFwKG9mZnNldCA9PiBvZmZzZXQgKyAxKTtcbn1cblxuLyoqIEZpbmRzIHRoZSBzcGVjaWZpZWQgQW5ndWxhciBAT3V0cHV0IGluIGVsZW1lbnRzIHRoYXQgaGF2ZSBvbmUgb2YgdGhlIHNwZWNpZmllZCBhdHRyaWJ1dGVzLiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGZpbmRPdXRwdXRzT25FbGVtZW50V2l0aEF0dHIoaHRtbDogc3RyaW5nLCBvdXRwdXROYW1lOiBzdHJpbmcsIGF0dHJzOiBzdHJpbmdbXSkge1xuICAvLyBBZGQgb25lIGNvbHVtbiB0byB0aGUgbWFwcGVkIG9mZnNldCBiZWNhdXNlIHRoZSBmaXJzdCBicmFja2V0IGZvciB0aGUgQE91dHB1dFxuICAvLyBpcyBwYXJ0IG9mIHRoZSBhdHRyaWJ1dGUgYW5kIHRoZXJlZm9yZSBhbHNvIHBhcnQgb2YgdGhlIG9mZnNldC4gV2Ugb25seSB3YW50IHRvIHJldHVyblxuICAvLyB0aGUgb2Zmc2V0IGZvciB0aGUgaW5uZXIgbmFtZSBvZiB0aGUgb3V0cHV0LlxuICByZXR1cm4gZmluZEF0dHJpYnV0ZU9uRWxlbWVudFdpdGhBdHRycyhodG1sLCBgKCR7b3V0cHV0TmFtZX0pYCwgYXR0cnMpLm1hcChvZmZzZXQgPT4gb2Zmc2V0ICsgMSk7XG59XG4iXX0=