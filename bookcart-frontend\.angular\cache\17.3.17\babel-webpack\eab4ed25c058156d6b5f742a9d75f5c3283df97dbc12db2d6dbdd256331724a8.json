{"ast": null, "code": "import * as i1 from '@angular/cdk/bidi';\nimport { _VIEW_REPEATER_STRATEGY, _RecycleViewRepeaterStrategy, isDataSource, _ViewRepeaterOperation, _DisposeViewRepeaterStrategy } from '@angular/cdk/collections';\nexport { DataSource } from '@angular/cdk/collections';\nimport * as i2 from '@angular/cdk/platform';\nimport * as i3 from '@angular/cdk/scrolling';\nimport { ScrollingModule } from '@angular/cdk/scrolling';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, booleanAttribute, Inject, Optional, Input, ContentChild, Injectable, Component, ChangeDetectionStrategy, ViewEncapsulation, inject, EmbeddedViewRef, EventEmitter, NgZone, Attribute, SkipSelf, Output, ContentChildren, ViewChild, NgModule } from '@angular/core';\nimport { Subject, from, BehaviorSubject, isObservable, of } from 'rxjs';\nimport { takeUntil, take } from 'rxjs/operators';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\n\n/**\n * Used to provide a table to some of the sub-components without causing a circular dependency.\n * @docs-private\n */\nconst _c0 = [[[\"caption\"]], [[\"colgroup\"], [\"col\"]], \"*\"];\nconst _c1 = [\"caption\", \"colgroup, col\", \"*\"];\nfunction CdkTable_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 2);\n  }\n}\nfunction CdkTable_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"thead\", 0);\n    i0.ɵɵelementContainer(1, 1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"tbody\", 0);\n    i0.ɵɵelementContainer(3, 2)(4, 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"tfoot\", 0);\n    i0.ɵɵelementContainer(6, 4);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CdkTable_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 1)(1, 2)(2, 3)(3, 4);\n  }\n}\nfunction CdkTextColumn_th_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 3);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"text-align\", ctx_r0.justify);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.headerText, \" \");\n  }\n}\nfunction CdkTextColumn_td_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const data_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"text-align\", ctx_r0.justify);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.dataAccessor(data_r2, ctx_r0.name), \" \");\n  }\n}\nconst CDK_TABLE = new InjectionToken('CDK_TABLE');\n/** Injection token that can be used to specify the text column options. */\nconst TEXT_COLUMN_OPTIONS = new InjectionToken('text-column-options');\n\n/**\n * Cell definition for a CDK table.\n * Captures the template of a column's data row cell as well as cell-specific properties.\n */\nclass CdkCellDef {\n  constructor(/** @docs-private */template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function CdkCellDef_Factory(t) {\n      return new (t || CdkCellDef)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkCellDef,\n      selectors: [[\"\", \"cdkCellDef\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkCellDef]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\n/**\n * Header cell definition for a CDK table.\n * Captures the template of a column's header cell and as well as cell-specific properties.\n */\nclass CdkHeaderCellDef {\n  constructor(/** @docs-private */template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function CdkHeaderCellDef_Factory(t) {\n      return new (t || CdkHeaderCellDef)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkHeaderCellDef,\n      selectors: [[\"\", \"cdkHeaderCellDef\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkHeaderCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkHeaderCellDef]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\n/**\n * Footer cell definition for a CDK table.\n * Captures the template of a column's footer cell and as well as cell-specific properties.\n */\nclass CdkFooterCellDef {\n  constructor(/** @docs-private */template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function CdkFooterCellDef_Factory(t) {\n      return new (t || CdkFooterCellDef)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkFooterCellDef,\n      selectors: [[\"\", \"cdkFooterCellDef\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkFooterCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkFooterCellDef]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\n/**\n * Column definition for the CDK table.\n * Defines a set of cells available for a table column.\n */\nclass CdkColumnDef {\n  /** Unique name for this column. */\n  get name() {\n    return this._name;\n  }\n  set name(name) {\n    this._setNameInput(name);\n  }\n  /** Whether the cell is sticky. */\n  get sticky() {\n    return this._sticky;\n  }\n  set sticky(value) {\n    if (value !== this._sticky) {\n      this._sticky = value;\n      this._hasStickyChanged = true;\n    }\n  }\n  /**\n   * Whether this column should be sticky positioned on the end of the row. Should make sure\n   * that it mimics the `CanStick` mixin such that `_hasStickyChanged` is set to true if the value\n   * has been changed.\n   */\n  get stickyEnd() {\n    return this._stickyEnd;\n  }\n  set stickyEnd(value) {\n    if (value !== this._stickyEnd) {\n      this._stickyEnd = value;\n      this._hasStickyChanged = true;\n    }\n  }\n  constructor(_table) {\n    this._table = _table;\n    this._hasStickyChanged = false;\n    this._sticky = false;\n    this._stickyEnd = false;\n  }\n  /** Whether the sticky state has changed. */\n  hasStickyChanged() {\n    const hasStickyChanged = this._hasStickyChanged;\n    this.resetStickyChanged();\n    return hasStickyChanged;\n  }\n  /** Resets the sticky changed state. */\n  resetStickyChanged() {\n    this._hasStickyChanged = false;\n  }\n  /**\n   * Overridable method that sets the css classes that will be added to every cell in this\n   * column.\n   * In the future, columnCssClassName will change from type string[] to string and this\n   * will set a single string value.\n   * @docs-private\n   */\n  _updateColumnCssClassName() {\n    this._columnCssClassName = [`cdk-column-${this.cssClassFriendlyName}`];\n  }\n  /**\n   * This has been extracted to a util because of TS 4 and VE.\n   * View Engine doesn't support property rename inheritance.\n   * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n   * @docs-private\n   */\n  _setNameInput(value) {\n    // If the directive is set without a name (updated programmatically), then this setter will\n    // trigger with an empty string and should not overwrite the programmatically set value.\n    if (value) {\n      this._name = value;\n      this.cssClassFriendlyName = value.replace(/[^a-z0-9_-]/gi, '-');\n      this._updateColumnCssClassName();\n    }\n  }\n  static {\n    this.ɵfac = function CdkColumnDef_Factory(t) {\n      return new (t || CdkColumnDef)(i0.ɵɵdirectiveInject(CDK_TABLE, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkColumnDef,\n      selectors: [[\"\", \"cdkColumnDef\", \"\"]],\n      contentQueries: function CdkColumnDef_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, CdkCellDef, 5);\n          i0.ɵɵcontentQuery(dirIndex, CdkHeaderCellDef, 5);\n          i0.ɵɵcontentQuery(dirIndex, CdkFooterCellDef, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.cell = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerCell = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footerCell = _t.first);\n        }\n      },\n      inputs: {\n        name: [i0.ɵɵInputFlags.None, \"cdkColumnDef\", \"name\"],\n        sticky: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"sticky\", \"sticky\", booleanAttribute],\n        stickyEnd: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"stickyEnd\", \"stickyEnd\", booleanAttribute]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: 'MAT_SORT_HEADER_COLUMN_DEF',\n        useExisting: CdkColumnDef\n      }]), i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkColumnDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkColumnDef]',\n      providers: [{\n        provide: 'MAT_SORT_HEADER_COLUMN_DEF',\n        useExisting: CdkColumnDef\n      }],\n      standalone: true\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CDK_TABLE]\n    }, {\n      type: Optional\n    }]\n  }], {\n    name: [{\n      type: Input,\n      args: ['cdkColumnDef']\n    }],\n    sticky: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    stickyEnd: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    cell: [{\n      type: ContentChild,\n      args: [CdkCellDef]\n    }],\n    headerCell: [{\n      type: ContentChild,\n      args: [CdkHeaderCellDef]\n    }],\n    footerCell: [{\n      type: ContentChild,\n      args: [CdkFooterCellDef]\n    }]\n  });\n})();\n/** Base class for the cells. Adds a CSS classname that identifies the column it renders in. */\nclass BaseCdkCell {\n  constructor(columnDef, elementRef) {\n    elementRef.nativeElement.classList.add(...columnDef._columnCssClassName);\n  }\n}\n/** Header cell template container that adds the right classes and role. */\nclass CdkHeaderCell extends BaseCdkCell {\n  constructor(columnDef, elementRef) {\n    super(columnDef, elementRef);\n  }\n  static {\n    this.ɵfac = function CdkHeaderCell_Factory(t) {\n      return new (t || CdkHeaderCell)(i0.ɵɵdirectiveInject(CdkColumnDef), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkHeaderCell,\n      selectors: [[\"cdk-header-cell\"], [\"th\", \"cdk-header-cell\", \"\"]],\n      hostAttrs: [\"role\", \"columnheader\", 1, \"cdk-header-cell\"],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkHeaderCell, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-header-cell, th[cdk-header-cell]',\n      host: {\n        'class': 'cdk-header-cell',\n        'role': 'columnheader'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: CdkColumnDef\n  }, {\n    type: i0.ElementRef\n  }], null);\n})();\n/** Footer cell template container that adds the right classes and role. */\nclass CdkFooterCell extends BaseCdkCell {\n  constructor(columnDef, elementRef) {\n    super(columnDef, elementRef);\n    const role = columnDef._table?._getCellRole();\n    if (role) {\n      elementRef.nativeElement.setAttribute('role', role);\n    }\n  }\n  static {\n    this.ɵfac = function CdkFooterCell_Factory(t) {\n      return new (t || CdkFooterCell)(i0.ɵɵdirectiveInject(CdkColumnDef), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkFooterCell,\n      selectors: [[\"cdk-footer-cell\"], [\"td\", \"cdk-footer-cell\", \"\"]],\n      hostAttrs: [1, \"cdk-footer-cell\"],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkFooterCell, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-footer-cell, td[cdk-footer-cell]',\n      host: {\n        'class': 'cdk-footer-cell'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: CdkColumnDef\n  }, {\n    type: i0.ElementRef\n  }], null);\n})();\n/** Cell template container that adds the right classes and role. */\nclass CdkCell extends BaseCdkCell {\n  constructor(columnDef, elementRef) {\n    super(columnDef, elementRef);\n    const role = columnDef._table?._getCellRole();\n    if (role) {\n      elementRef.nativeElement.setAttribute('role', role);\n    }\n  }\n  static {\n    this.ɵfac = function CdkCell_Factory(t) {\n      return new (t || CdkCell)(i0.ɵɵdirectiveInject(CdkColumnDef), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkCell,\n      selectors: [[\"cdk-cell\"], [\"td\", \"cdk-cell\", \"\"]],\n      hostAttrs: [1, \"cdk-cell\"],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkCell, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-cell, td[cdk-cell]',\n      host: {\n        'class': 'cdk-cell'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: CdkColumnDef\n  }, {\n    type: i0.ElementRef\n  }], null);\n})();\n\n/**\n * @docs-private\n */\nclass _Schedule {\n  constructor() {\n    this.tasks = [];\n    this.endTasks = [];\n  }\n}\n/** Injection token used to provide a coalesced style scheduler. */\nconst _COALESCED_STYLE_SCHEDULER = new InjectionToken('_COALESCED_STYLE_SCHEDULER');\n/**\n * Allows grouping up CSSDom mutations after the current execution context.\n * This can significantly improve performance when separate consecutive functions are\n * reading from the CSSDom and then mutating it.\n *\n * @docs-private\n */\nclass _CoalescedStyleScheduler {\n  constructor(_ngZone) {\n    this._ngZone = _ngZone;\n    this._currentSchedule = null;\n    this._destroyed = new Subject();\n  }\n  /**\n   * Schedules the specified task to run at the end of the current VM turn.\n   */\n  schedule(task) {\n    this._createScheduleIfNeeded();\n    this._currentSchedule.tasks.push(task);\n  }\n  /**\n   * Schedules the specified task to run after other scheduled tasks at the end of the current\n   * VM turn.\n   */\n  scheduleEnd(task) {\n    this._createScheduleIfNeeded();\n    this._currentSchedule.endTasks.push(task);\n  }\n  /** Prevent any further tasks from running. */\n  ngOnDestroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  _createScheduleIfNeeded() {\n    if (this._currentSchedule) {\n      return;\n    }\n    this._currentSchedule = new _Schedule();\n    this._getScheduleObservable().pipe(takeUntil(this._destroyed)).subscribe(() => {\n      while (this._currentSchedule.tasks.length || this._currentSchedule.endTasks.length) {\n        const schedule = this._currentSchedule;\n        // Capture new tasks scheduled by the current set of tasks.\n        this._currentSchedule = new _Schedule();\n        for (const task of schedule.tasks) {\n          task();\n        }\n        for (const task of schedule.endTasks) {\n          task();\n        }\n      }\n      this._currentSchedule = null;\n    });\n  }\n  _getScheduleObservable() {\n    // Use onStable when in the context of an ongoing change detection cycle so that we\n    // do not accidentally trigger additional cycles.\n    return this._ngZone.isStable ? from(Promise.resolve(undefined)) : this._ngZone.onStable.pipe(take(1));\n  }\n  static {\n    this.ɵfac = function _CoalescedStyleScheduler_Factory(t) {\n      return new (t || _CoalescedStyleScheduler)(i0.ɵɵinject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: _CoalescedStyleScheduler,\n      factory: _CoalescedStyleScheduler.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_CoalescedStyleScheduler, [{\n    type: Injectable\n  }], () => [{\n    type: i0.NgZone\n  }], null);\n})();\n\n/**\n * The row template that can be used by the mat-table. Should not be used outside of the\n * material library.\n */\nconst CDK_ROW_TEMPLATE = `<ng-container cdkCellOutlet></ng-container>`;\n/**\n * Base class for the CdkHeaderRowDef and CdkRowDef that handles checking their columns inputs\n * for changes and notifying the table.\n */\nclass BaseRowDef {\n  constructor(/** @docs-private */template, _differs) {\n    this.template = template;\n    this._differs = _differs;\n  }\n  ngOnChanges(changes) {\n    // Create a new columns differ if one does not yet exist. Initialize it based on initial value\n    // of the columns property or an empty array if none is provided.\n    if (!this._columnsDiffer) {\n      const columns = changes['columns'] && changes['columns'].currentValue || [];\n      this._columnsDiffer = this._differs.find(columns).create();\n      this._columnsDiffer.diff(columns);\n    }\n  }\n  /**\n   * Returns the difference between the current columns and the columns from the last diff, or null\n   * if there is no difference.\n   */\n  getColumnsDiff() {\n    return this._columnsDiffer.diff(this.columns);\n  }\n  /** Gets this row def's relevant cell template from the provided column def. */\n  extractCellTemplate(column) {\n    if (this instanceof CdkHeaderRowDef) {\n      return column.headerCell.template;\n    }\n    if (this instanceof CdkFooterRowDef) {\n      return column.footerCell.template;\n    } else {\n      return column.cell.template;\n    }\n  }\n  static {\n    this.ɵfac = function BaseRowDef_Factory(t) {\n      return new (t || BaseRowDef)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.IterableDiffers));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: BaseRowDef,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseRowDef, [{\n    type: Directive\n  }], () => [{\n    type: i0.TemplateRef\n  }, {\n    type: i0.IterableDiffers\n  }], null);\n})();\n/**\n * Header row definition for the CDK table.\n * Captures the header row's template and other header properties such as the columns to display.\n */\nclass CdkHeaderRowDef extends BaseRowDef {\n  /** Whether the row is sticky. */\n  get sticky() {\n    return this._sticky;\n  }\n  set sticky(value) {\n    if (value !== this._sticky) {\n      this._sticky = value;\n      this._hasStickyChanged = true;\n    }\n  }\n  constructor(template, _differs, _table) {\n    super(template, _differs);\n    this._table = _table;\n    this._hasStickyChanged = false;\n    this._sticky = false;\n  }\n  // Prerender fails to recognize that ngOnChanges in a part of this class through inheritance.\n  // Explicitly define it so that the method is called as part of the Angular lifecycle.\n  ngOnChanges(changes) {\n    super.ngOnChanges(changes);\n  }\n  /** Whether the sticky state has changed. */\n  hasStickyChanged() {\n    const hasStickyChanged = this._hasStickyChanged;\n    this.resetStickyChanged();\n    return hasStickyChanged;\n  }\n  /** Resets the sticky changed state. */\n  resetStickyChanged() {\n    this._hasStickyChanged = false;\n  }\n  static {\n    this.ɵfac = function CdkHeaderRowDef_Factory(t) {\n      return new (t || CdkHeaderRowDef)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵdirectiveInject(CDK_TABLE, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkHeaderRowDef,\n      selectors: [[\"\", \"cdkHeaderRowDef\", \"\"]],\n      inputs: {\n        columns: [i0.ɵɵInputFlags.None, \"cdkHeaderRowDef\", \"columns\"],\n        sticky: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"cdkHeaderRowDefSticky\", \"sticky\", booleanAttribute]\n      },\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkHeaderRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkHeaderRowDef]',\n      inputs: [{\n        name: 'columns',\n        alias: 'cdkHeaderRowDef'\n      }],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }, {\n    type: i0.IterableDiffers\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CDK_TABLE]\n    }, {\n      type: Optional\n    }]\n  }], {\n    sticky: [{\n      type: Input,\n      args: [{\n        alias: 'cdkHeaderRowDefSticky',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n/**\n * Footer row definition for the CDK table.\n * Captures the footer row's template and other footer properties such as the columns to display.\n */\nclass CdkFooterRowDef extends BaseRowDef {\n  /** Whether the row is sticky. */\n  get sticky() {\n    return this._sticky;\n  }\n  set sticky(value) {\n    if (value !== this._sticky) {\n      this._sticky = value;\n      this._hasStickyChanged = true;\n    }\n  }\n  constructor(template, _differs, _table) {\n    super(template, _differs);\n    this._table = _table;\n    this._hasStickyChanged = false;\n    this._sticky = false;\n  }\n  // Prerender fails to recognize that ngOnChanges in a part of this class through inheritance.\n  // Explicitly define it so that the method is called as part of the Angular lifecycle.\n  ngOnChanges(changes) {\n    super.ngOnChanges(changes);\n  }\n  /** Whether the sticky state has changed. */\n  hasStickyChanged() {\n    const hasStickyChanged = this._hasStickyChanged;\n    this.resetStickyChanged();\n    return hasStickyChanged;\n  }\n  /** Resets the sticky changed state. */\n  resetStickyChanged() {\n    this._hasStickyChanged = false;\n  }\n  static {\n    this.ɵfac = function CdkFooterRowDef_Factory(t) {\n      return new (t || CdkFooterRowDef)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵdirectiveInject(CDK_TABLE, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkFooterRowDef,\n      selectors: [[\"\", \"cdkFooterRowDef\", \"\"]],\n      inputs: {\n        columns: [i0.ɵɵInputFlags.None, \"cdkFooterRowDef\", \"columns\"],\n        sticky: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"cdkFooterRowDefSticky\", \"sticky\", booleanAttribute]\n      },\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkFooterRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkFooterRowDef]',\n      inputs: [{\n        name: 'columns',\n        alias: 'cdkFooterRowDef'\n      }],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }, {\n    type: i0.IterableDiffers\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CDK_TABLE]\n    }, {\n      type: Optional\n    }]\n  }], {\n    sticky: [{\n      type: Input,\n      args: [{\n        alias: 'cdkFooterRowDefSticky',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n/**\n * Data row definition for the CDK table.\n * Captures the header row's template and other row properties such as the columns to display and\n * a when predicate that describes when this row should be used.\n */\nclass CdkRowDef extends BaseRowDef {\n  // TODO(andrewseguin): Add an input for providing a switch function to determine\n  //   if this template should be used.\n  constructor(template, _differs, _table) {\n    super(template, _differs);\n    this._table = _table;\n  }\n  static {\n    this.ɵfac = function CdkRowDef_Factory(t) {\n      return new (t || CdkRowDef)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵdirectiveInject(CDK_TABLE, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkRowDef,\n      selectors: [[\"\", \"cdkRowDef\", \"\"]],\n      inputs: {\n        columns: [i0.ɵɵInputFlags.None, \"cdkRowDefColumns\", \"columns\"],\n        when: [i0.ɵɵInputFlags.None, \"cdkRowDefWhen\", \"when\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkRowDef]',\n      inputs: [{\n        name: 'columns',\n        alias: 'cdkRowDefColumns'\n      }, {\n        name: 'when',\n        alias: 'cdkRowDefWhen'\n      }],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }, {\n    type: i0.IterableDiffers\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CDK_TABLE]\n    }, {\n      type: Optional\n    }]\n  }], null);\n})();\n/**\n * Outlet for rendering cells inside of a row or header row.\n * @docs-private\n */\nclass CdkCellOutlet {\n  /**\n   * Static property containing the latest constructed instance of this class.\n   * Used by the CDK table when each CdkHeaderRow and CdkRow component is created using\n   * createEmbeddedView. After one of these components are created, this property will provide\n   * a handle to provide that component's cells and context. After init, the CdkCellOutlet will\n   * construct the cells with the provided context.\n   */\n  static {\n    this.mostRecentCellOutlet = null;\n  }\n  constructor(_viewContainer) {\n    this._viewContainer = _viewContainer;\n    CdkCellOutlet.mostRecentCellOutlet = this;\n  }\n  ngOnDestroy() {\n    // If this was the last outlet being rendered in the view, remove the reference\n    // from the static property after it has been destroyed to avoid leaking memory.\n    if (CdkCellOutlet.mostRecentCellOutlet === this) {\n      CdkCellOutlet.mostRecentCellOutlet = null;\n    }\n  }\n  static {\n    this.ɵfac = function CdkCellOutlet_Factory(t) {\n      return new (t || CdkCellOutlet)(i0.ɵɵdirectiveInject(i0.ViewContainerRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkCellOutlet,\n      selectors: [[\"\", \"cdkCellOutlet\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkCellOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkCellOutlet]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ViewContainerRef\n  }], null);\n})();\n/** Header template container that contains the cell outlet. Adds the right class and role. */\nclass CdkHeaderRow {\n  static {\n    this.ɵfac = function CdkHeaderRow_Factory(t) {\n      return new (t || CdkHeaderRow)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CdkHeaderRow,\n      selectors: [[\"cdk-header-row\"], [\"tr\", \"cdk-header-row\", \"\"]],\n      hostAttrs: [\"role\", \"row\", 1, \"cdk-header-row\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 0,\n      consts: [[\"cdkCellOutlet\", \"\"]],\n      template: function CdkHeaderRow_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainer(0, 0);\n        }\n      },\n      dependencies: [CdkCellOutlet],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkHeaderRow, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-header-row, tr[cdk-header-row]',\n      template: CDK_ROW_TEMPLATE,\n      host: {\n        'class': 'cdk-header-row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      standalone: true,\n      imports: [CdkCellOutlet]\n    }]\n  }], null, null);\n})();\n/** Footer template container that contains the cell outlet. Adds the right class and role. */\nclass CdkFooterRow {\n  static {\n    this.ɵfac = function CdkFooterRow_Factory(t) {\n      return new (t || CdkFooterRow)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CdkFooterRow,\n      selectors: [[\"cdk-footer-row\"], [\"tr\", \"cdk-footer-row\", \"\"]],\n      hostAttrs: [\"role\", \"row\", 1, \"cdk-footer-row\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 0,\n      consts: [[\"cdkCellOutlet\", \"\"]],\n      template: function CdkFooterRow_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainer(0, 0);\n        }\n      },\n      dependencies: [CdkCellOutlet],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkFooterRow, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-footer-row, tr[cdk-footer-row]',\n      template: CDK_ROW_TEMPLATE,\n      host: {\n        'class': 'cdk-footer-row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      standalone: true,\n      imports: [CdkCellOutlet]\n    }]\n  }], null, null);\n})();\n/** Data row template container that contains the cell outlet. Adds the right class and role. */\nclass CdkRow {\n  static {\n    this.ɵfac = function CdkRow_Factory(t) {\n      return new (t || CdkRow)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CdkRow,\n      selectors: [[\"cdk-row\"], [\"tr\", \"cdk-row\", \"\"]],\n      hostAttrs: [\"role\", \"row\", 1, \"cdk-row\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 0,\n      consts: [[\"cdkCellOutlet\", \"\"]],\n      template: function CdkRow_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainer(0, 0);\n        }\n      },\n      dependencies: [CdkCellOutlet],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkRow, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-row, tr[cdk-row]',\n      template: CDK_ROW_TEMPLATE,\n      host: {\n        'class': 'cdk-row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      standalone: true,\n      imports: [CdkCellOutlet]\n    }]\n  }], null, null);\n})();\n/** Row that can be used to display a message when no data is shown in the table. */\nclass CdkNoDataRow {\n  constructor(templateRef) {\n    this.templateRef = templateRef;\n    this._contentClassName = 'cdk-no-data-row';\n  }\n  static {\n    this.ɵfac = function CdkNoDataRow_Factory(t) {\n      return new (t || CdkNoDataRow)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkNoDataRow,\n      selectors: [[\"ng-template\", \"cdkNoDataRow\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkNoDataRow, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[cdkNoDataRow]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\n\n/**\n * List of all possible directions that can be used for sticky positioning.\n * @docs-private\n */\nconst STICKY_DIRECTIONS = ['top', 'bottom', 'left', 'right'];\n/**\n * Applies and removes sticky positioning styles to the `CdkTable` rows and columns cells.\n * @docs-private\n */\nclass StickyStyler {\n  /**\n   * @param _isNativeHtmlTable Whether the sticky logic should be based on a table\n   *     that uses the native `<table>` element.\n   * @param _stickCellCss The CSS class that will be applied to every row/cell that has\n   *     sticky positioning applied.\n   * @param direction The directionality context of the table (ltr/rtl); affects column positioning\n   *     by reversing left/right positions.\n   * @param _isBrowser Whether the table is currently being rendered on the server or the client.\n   * @param _needsPositionStickyOnElement Whether we need to specify position: sticky on cells\n   *     using inline styles. If false, it is assumed that position: sticky is included in\n   *     the component stylesheet for _stickCellCss.\n   * @param _positionListener A listener that is notified of changes to sticky rows/columns\n   *     and their dimensions.\n   */\n  constructor(_isNativeHtmlTable, _stickCellCss, direction, _coalescedStyleScheduler, _isBrowser = true, _needsPositionStickyOnElement = true, _positionListener) {\n    this._isNativeHtmlTable = _isNativeHtmlTable;\n    this._stickCellCss = _stickCellCss;\n    this.direction = direction;\n    this._coalescedStyleScheduler = _coalescedStyleScheduler;\n    this._isBrowser = _isBrowser;\n    this._needsPositionStickyOnElement = _needsPositionStickyOnElement;\n    this._positionListener = _positionListener;\n    this._cachedCellWidths = [];\n    this._borderCellCss = {\n      'top': `${_stickCellCss}-border-elem-top`,\n      'bottom': `${_stickCellCss}-border-elem-bottom`,\n      'left': `${_stickCellCss}-border-elem-left`,\n      'right': `${_stickCellCss}-border-elem-right`\n    };\n  }\n  /**\n   * Clears the sticky positioning styles from the row and its cells by resetting the `position`\n   * style, setting the zIndex to 0, and unsetting each provided sticky direction.\n   * @param rows The list of rows that should be cleared from sticking in the provided directions\n   * @param stickyDirections The directions that should no longer be set as sticky on the rows.\n   */\n  clearStickyPositioning(rows, stickyDirections) {\n    const elementsToClear = [];\n    for (const row of rows) {\n      // If the row isn't an element (e.g. if it's an `ng-container`),\n      // it won't have inline styles or `children` so we skip it.\n      if (row.nodeType !== row.ELEMENT_NODE) {\n        continue;\n      }\n      elementsToClear.push(row);\n      for (let i = 0; i < row.children.length; i++) {\n        elementsToClear.push(row.children[i]);\n      }\n    }\n    // Coalesce with sticky row/column updates (and potentially other changes like column resize).\n    this._coalescedStyleScheduler.schedule(() => {\n      for (const element of elementsToClear) {\n        this._removeStickyStyle(element, stickyDirections);\n      }\n    });\n  }\n  /**\n   * Applies sticky left and right positions to the cells of each row according to the sticky\n   * states of the rendered column definitions.\n   * @param rows The rows that should have its set of cells stuck according to the sticky states.\n   * @param stickyStartStates A list of boolean states where each state represents whether the cell\n   *     in this index position should be stuck to the start of the row.\n   * @param stickyEndStates A list of boolean states where each state represents whether the cell\n   *     in this index position should be stuck to the end of the row.\n   * @param recalculateCellWidths Whether the sticky styler should recalculate the width of each\n   *     column cell. If `false` cached widths will be used instead.\n   */\n  updateStickyColumns(rows, stickyStartStates, stickyEndStates, recalculateCellWidths = true) {\n    if (!rows.length || !this._isBrowser || !(stickyStartStates.some(state => state) || stickyEndStates.some(state => state))) {\n      if (this._positionListener) {\n        this._positionListener.stickyColumnsUpdated({\n          sizes: []\n        });\n        this._positionListener.stickyEndColumnsUpdated({\n          sizes: []\n        });\n      }\n      return;\n    }\n    // Coalesce with sticky row updates (and potentially other changes like column resize).\n    this._coalescedStyleScheduler.schedule(() => {\n      const firstRow = rows[0];\n      const numCells = firstRow.children.length;\n      const cellWidths = this._getCellWidths(firstRow, recalculateCellWidths);\n      const startPositions = this._getStickyStartColumnPositions(cellWidths, stickyStartStates);\n      const endPositions = this._getStickyEndColumnPositions(cellWidths, stickyEndStates);\n      const lastStickyStart = stickyStartStates.lastIndexOf(true);\n      const firstStickyEnd = stickyEndStates.indexOf(true);\n      const isRtl = this.direction === 'rtl';\n      const start = isRtl ? 'right' : 'left';\n      const end = isRtl ? 'left' : 'right';\n      for (const row of rows) {\n        for (let i = 0; i < numCells; i++) {\n          const cell = row.children[i];\n          if (stickyStartStates[i]) {\n            this._addStickyStyle(cell, start, startPositions[i], i === lastStickyStart);\n          }\n          if (stickyEndStates[i]) {\n            this._addStickyStyle(cell, end, endPositions[i], i === firstStickyEnd);\n          }\n        }\n      }\n      if (this._positionListener) {\n        this._positionListener.stickyColumnsUpdated({\n          sizes: lastStickyStart === -1 ? [] : cellWidths.slice(0, lastStickyStart + 1).map((width, index) => stickyStartStates[index] ? width : null)\n        });\n        this._positionListener.stickyEndColumnsUpdated({\n          sizes: firstStickyEnd === -1 ? [] : cellWidths.slice(firstStickyEnd).map((width, index) => stickyEndStates[index + firstStickyEnd] ? width : null).reverse()\n        });\n      }\n    });\n  }\n  /**\n   * Applies sticky positioning to the row's cells if using the native table layout, and to the\n   * row itself otherwise.\n   * @param rowsToStick The list of rows that should be stuck according to their corresponding\n   *     sticky state and to the provided top or bottom position.\n   * @param stickyStates A list of boolean states where each state represents whether the row\n   *     should be stuck in the particular top or bottom position.\n   * @param position The position direction in which the row should be stuck if that row should be\n   *     sticky.\n   *\n   */\n  stickRows(rowsToStick, stickyStates, position) {\n    // Since we can't measure the rows on the server, we can't stick the rows properly.\n    if (!this._isBrowser) {\n      return;\n    }\n    // Coalesce with other sticky row updates (top/bottom), sticky columns updates\n    // (and potentially other changes like column resize).\n    this._coalescedStyleScheduler.schedule(() => {\n      // If positioning the rows to the bottom, reverse their order when evaluating the sticky\n      // position such that the last row stuck will be \"bottom: 0px\" and so on. Note that the\n      // sticky states need to be reversed as well.\n      const rows = position === 'bottom' ? rowsToStick.slice().reverse() : rowsToStick;\n      const states = position === 'bottom' ? stickyStates.slice().reverse() : stickyStates;\n      // Measure row heights all at once before adding sticky styles to reduce layout thrashing.\n      const stickyOffsets = [];\n      const stickyCellHeights = [];\n      const elementsToStick = [];\n      for (let rowIndex = 0, stickyOffset = 0; rowIndex < rows.length; rowIndex++) {\n        if (!states[rowIndex]) {\n          continue;\n        }\n        stickyOffsets[rowIndex] = stickyOffset;\n        const row = rows[rowIndex];\n        elementsToStick[rowIndex] = this._isNativeHtmlTable ? Array.from(row.children) : [row];\n        const height = row.getBoundingClientRect().height;\n        stickyOffset += height;\n        stickyCellHeights[rowIndex] = height;\n      }\n      const borderedRowIndex = states.lastIndexOf(true);\n      for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {\n        if (!states[rowIndex]) {\n          continue;\n        }\n        const offset = stickyOffsets[rowIndex];\n        const isBorderedRowIndex = rowIndex === borderedRowIndex;\n        for (const element of elementsToStick[rowIndex]) {\n          this._addStickyStyle(element, position, offset, isBorderedRowIndex);\n        }\n      }\n      if (position === 'top') {\n        this._positionListener?.stickyHeaderRowsUpdated({\n          sizes: stickyCellHeights,\n          offsets: stickyOffsets,\n          elements: elementsToStick\n        });\n      } else {\n        this._positionListener?.stickyFooterRowsUpdated({\n          sizes: stickyCellHeights,\n          offsets: stickyOffsets,\n          elements: elementsToStick\n        });\n      }\n    });\n  }\n  /**\n   * When using the native table in Safari, sticky footer cells do not stick. The only way to stick\n   * footer rows is to apply sticky styling to the tfoot container. This should only be done if\n   * all footer rows are sticky. If not all footer rows are sticky, remove sticky positioning from\n   * the tfoot element.\n   */\n  updateStickyFooterContainer(tableElement, stickyStates) {\n    if (!this._isNativeHtmlTable) {\n      return;\n    }\n    // Coalesce with other sticky updates (and potentially other changes like column resize).\n    this._coalescedStyleScheduler.schedule(() => {\n      const tfoot = tableElement.querySelector('tfoot');\n      if (tfoot) {\n        if (stickyStates.some(state => !state)) {\n          this._removeStickyStyle(tfoot, ['bottom']);\n        } else {\n          this._addStickyStyle(tfoot, 'bottom', 0, false);\n        }\n      }\n    });\n  }\n  /**\n   * Removes the sticky style on the element by removing the sticky cell CSS class, re-evaluating\n   * the zIndex, removing each of the provided sticky directions, and removing the\n   * sticky position if there are no more directions.\n   */\n  _removeStickyStyle(element, stickyDirections) {\n    for (const dir of stickyDirections) {\n      element.style[dir] = '';\n      element.classList.remove(this._borderCellCss[dir]);\n    }\n    // If the element no longer has any more sticky directions, remove sticky positioning and\n    // the sticky CSS class.\n    // Short-circuit checking element.style[dir] for stickyDirections as they\n    // were already removed above.\n    const hasDirection = STICKY_DIRECTIONS.some(dir => stickyDirections.indexOf(dir) === -1 && element.style[dir]);\n    if (hasDirection) {\n      element.style.zIndex = this._getCalculatedZIndex(element);\n    } else {\n      // When not hasDirection, _getCalculatedZIndex will always return ''.\n      element.style.zIndex = '';\n      if (this._needsPositionStickyOnElement) {\n        element.style.position = '';\n      }\n      element.classList.remove(this._stickCellCss);\n    }\n  }\n  /**\n   * Adds the sticky styling to the element by adding the sticky style class, changing position\n   * to be sticky (and -webkit-sticky), setting the appropriate zIndex, and adding a sticky\n   * direction and value.\n   */\n  _addStickyStyle(element, dir, dirValue, isBorderElement) {\n    element.classList.add(this._stickCellCss);\n    if (isBorderElement) {\n      element.classList.add(this._borderCellCss[dir]);\n    }\n    element.style[dir] = `${dirValue}px`;\n    element.style.zIndex = this._getCalculatedZIndex(element);\n    if (this._needsPositionStickyOnElement) {\n      element.style.cssText += 'position: -webkit-sticky; position: sticky; ';\n    }\n  }\n  /**\n   * Calculate what the z-index should be for the element, depending on what directions (top,\n   * bottom, left, right) have been set. It should be true that elements with a top direction\n   * should have the highest index since these are elements like a table header. If any of those\n   * elements are also sticky in another direction, then they should appear above other elements\n   * that are only sticky top (e.g. a sticky column on a sticky header). Bottom-sticky elements\n   * (e.g. footer rows) should then be next in the ordering such that they are below the header\n   * but above any non-sticky elements. Finally, left/right sticky elements (e.g. sticky columns)\n   * should minimally increment so that they are above non-sticky elements but below top and bottom\n   * elements.\n   */\n  _getCalculatedZIndex(element) {\n    const zIndexIncrements = {\n      top: 100,\n      bottom: 10,\n      left: 1,\n      right: 1\n    };\n    let zIndex = 0;\n    // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n    // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n    // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n    for (const dir of STICKY_DIRECTIONS) {\n      if (element.style[dir]) {\n        zIndex += zIndexIncrements[dir];\n      }\n    }\n    return zIndex ? `${zIndex}` : '';\n  }\n  /** Gets the widths for each cell in the provided row. */\n  _getCellWidths(row, recalculateCellWidths = true) {\n    if (!recalculateCellWidths && this._cachedCellWidths.length) {\n      return this._cachedCellWidths;\n    }\n    const cellWidths = [];\n    const firstRowCells = row.children;\n    for (let i = 0; i < firstRowCells.length; i++) {\n      let cell = firstRowCells[i];\n      cellWidths.push(cell.getBoundingClientRect().width);\n    }\n    this._cachedCellWidths = cellWidths;\n    return cellWidths;\n  }\n  /**\n   * Determines the left and right positions of each sticky column cell, which will be the\n   * accumulation of all sticky column cell widths to the left and right, respectively.\n   * Non-sticky cells do not need to have a value set since their positions will not be applied.\n   */\n  _getStickyStartColumnPositions(widths, stickyStates) {\n    const positions = [];\n    let nextPosition = 0;\n    for (let i = 0; i < widths.length; i++) {\n      if (stickyStates[i]) {\n        positions[i] = nextPosition;\n        nextPosition += widths[i];\n      }\n    }\n    return positions;\n  }\n  /**\n   * Determines the left and right positions of each sticky column cell, which will be the\n   * accumulation of all sticky column cell widths to the left and right, respectively.\n   * Non-sticky cells do not need to have a value set since their positions will not be applied.\n   */\n  _getStickyEndColumnPositions(widths, stickyStates) {\n    const positions = [];\n    let nextPosition = 0;\n    for (let i = widths.length; i > 0; i--) {\n      if (stickyStates[i]) {\n        positions[i] = nextPosition;\n        nextPosition += widths[i];\n      }\n    }\n    return positions;\n  }\n}\n\n/**\n * Returns an error to be thrown when attempting to find an nonexistent column.\n * @param id Id whose lookup failed.\n * @docs-private\n */\nfunction getTableUnknownColumnError(id) {\n  return Error(`Could not find column with id \"${id}\".`);\n}\n/**\n * Returns an error to be thrown when two column definitions have the same name.\n * @docs-private\n */\nfunction getTableDuplicateColumnNameError(name) {\n  return Error(`Duplicate column definition name provided: \"${name}\".`);\n}\n/**\n * Returns an error to be thrown when there are multiple rows that are missing a when function.\n * @docs-private\n */\nfunction getTableMultipleDefaultRowDefsError() {\n  return Error(`There can only be one default row without a when predicate function.`);\n}\n/**\n * Returns an error to be thrown when there are no matching row defs for a particular set of data.\n * @docs-private\n */\nfunction getTableMissingMatchingRowDefError(data) {\n  return Error(`Could not find a matching row definition for the` + `provided row data: ${JSON.stringify(data)}`);\n}\n/**\n * Returns an error to be thrown when there is no row definitions present in the content.\n * @docs-private\n */\nfunction getTableMissingRowDefsError() {\n  return Error('Missing definitions for header, footer, and row; ' + 'cannot determine which columns should be rendered.');\n}\n/**\n * Returns an error to be thrown when the data source does not match the compatible types.\n * @docs-private\n */\nfunction getTableUnknownDataSourceError() {\n  return Error(`Provided data source did not match an array, Observable, or DataSource`);\n}\n/**\n * Returns an error to be thrown when the text column cannot find a parent table to inject.\n * @docs-private\n */\nfunction getTableTextColumnMissingParentTableError() {\n  return Error(`Text column could not find a parent table for registration.`);\n}\n/**\n * Returns an error to be thrown when a table text column doesn't have a name.\n * @docs-private\n */\nfunction getTableTextColumnMissingNameError() {\n  return Error(`Table text column must have a name.`);\n}\n\n/** The injection token used to specify the StickyPositioningListener. */\nconst STICKY_POSITIONING_LISTENER = new InjectionToken('CDK_SPL');\n\n/**\n * Enables the recycle view repeater strategy, which reduces rendering latency. Not compatible with\n * tables that animate rows.\n */\nclass CdkRecycleRows {\n  static {\n    this.ɵfac = function CdkRecycleRows_Factory(t) {\n      return new (t || CdkRecycleRows)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkRecycleRows,\n      selectors: [[\"cdk-table\", \"recycleRows\", \"\"], [\"table\", \"cdk-table\", \"\", \"recycleRows\", \"\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _RecycleViewRepeaterStrategy\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkRecycleRows, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-table[recycleRows], table[cdk-table][recycleRows]',\n      providers: [{\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _RecycleViewRepeaterStrategy\n      }],\n      standalone: true\n    }]\n  }], null, null);\n})();\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert data rows.\n * @docs-private\n */\nclass DataRowOutlet {\n  constructor(viewContainer, elementRef) {\n    this.viewContainer = viewContainer;\n    this.elementRef = elementRef;\n    const table = inject(CDK_TABLE);\n    table._rowOutlet = this;\n    table._outletAssigned();\n  }\n  static {\n    this.ɵfac = function DataRowOutlet_Factory(t) {\n      return new (t || DataRowOutlet)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: DataRowOutlet,\n      selectors: [[\"\", \"rowOutlet\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DataRowOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[rowOutlet]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.ElementRef\n  }], null);\n})();\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert the header.\n * @docs-private\n */\nclass HeaderRowOutlet {\n  constructor(viewContainer, elementRef) {\n    this.viewContainer = viewContainer;\n    this.elementRef = elementRef;\n    const table = inject(CDK_TABLE);\n    table._headerRowOutlet = this;\n    table._outletAssigned();\n  }\n  static {\n    this.ɵfac = function HeaderRowOutlet_Factory(t) {\n      return new (t || HeaderRowOutlet)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: HeaderRowOutlet,\n      selectors: [[\"\", \"headerRowOutlet\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HeaderRowOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[headerRowOutlet]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.ElementRef\n  }], null);\n})();\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert the footer.\n * @docs-private\n */\nclass FooterRowOutlet {\n  constructor(viewContainer, elementRef) {\n    this.viewContainer = viewContainer;\n    this.elementRef = elementRef;\n    const table = inject(CDK_TABLE);\n    table._footerRowOutlet = this;\n    table._outletAssigned();\n  }\n  static {\n    this.ɵfac = function FooterRowOutlet_Factory(t) {\n      return new (t || FooterRowOutlet)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: FooterRowOutlet,\n      selectors: [[\"\", \"footerRowOutlet\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FooterRowOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[footerRowOutlet]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.ElementRef\n  }], null);\n})();\n/**\n * Provides a handle for the table to grab the view\n * container's ng-container to insert the no data row.\n * @docs-private\n */\nclass NoDataRowOutlet {\n  constructor(viewContainer, elementRef) {\n    this.viewContainer = viewContainer;\n    this.elementRef = elementRef;\n    const table = inject(CDK_TABLE);\n    table._noDataRowOutlet = this;\n    table._outletAssigned();\n  }\n  static {\n    this.ɵfac = function NoDataRowOutlet_Factory(t) {\n      return new (t || NoDataRowOutlet)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NoDataRowOutlet,\n      selectors: [[\"\", \"noDataRowOutlet\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NoDataRowOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[noDataRowOutlet]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.ElementRef\n  }], null);\n})();\n/**\n * The table template that can be used by the mat-table. Should not be used outside of the\n * material library.\n * @docs-private\n */\nconst CDK_TABLE_TEMPLATE =\n// Note that according to MDN, the `caption` element has to be projected as the **first**\n// element in the table. See https://developer.mozilla.org/en-US/docs/Web/HTML/Element/caption\n`\n  <ng-content select=\"caption\"/>\n  <ng-content select=\"colgroup, col\"/>\n\n  <!--\n    Unprojected content throws a hydration error so we need this to capture it.\n    It gets removed on the client so it doesn't affect the layout.\n  -->\n  @if (_isServer) {\n    <ng-content/>\n  }\n\n  @if (_isNativeHtmlTable) {\n    <thead role=\"rowgroup\">\n      <ng-container headerRowOutlet/>\n    </thead>\n    <tbody role=\"rowgroup\">\n      <ng-container rowOutlet/>\n      <ng-container noDataRowOutlet/>\n    </tbody>\n    <tfoot role=\"rowgroup\">\n      <ng-container footerRowOutlet/>\n    </tfoot>\n  } @else {\n    <ng-container headerRowOutlet/>\n    <ng-container rowOutlet/>\n    <ng-container noDataRowOutlet/>\n    <ng-container footerRowOutlet/>\n  }\n`;\n/**\n * Class used to conveniently type the embedded view ref for rows with a context.\n * @docs-private\n */\nclass RowViewRef extends EmbeddedViewRef {}\n/**\n * A data table that can render a header row, data rows, and a footer row.\n * Uses the dataSource input to determine the data to be rendered. The data can be provided either\n * as a data array, an Observable stream that emits the data array to render, or a DataSource with a\n * connect function that will return an Observable stream that emits the data array to render.\n */\nclass CdkTable {\n  /** Aria role to apply to the table's cells based on the table's own role. */\n  _getCellRole() {\n    if (this._cellRoleInternal === undefined) {\n      // Perform this lazily in case the table's role was updated by a directive after construction.\n      const role = this._elementRef.nativeElement.getAttribute('role');\n      const cellRole = role === 'grid' || role === 'treegrid' ? 'gridcell' : 'cell';\n      this._cellRoleInternal = this._isNativeHtmlTable && cellRole === 'cell' ? null : cellRole;\n    }\n    return this._cellRoleInternal;\n  }\n  /**\n   * Tracking function that will be used to check the differences in data changes. Used similarly\n   * to `ngFor` `trackBy` function. Optimize row operations by identifying a row based on its data\n   * relative to the function to know if a row should be added/removed/moved.\n   * Accepts a function that takes two parameters, `index` and `item`.\n   */\n  get trackBy() {\n    return this._trackByFn;\n  }\n  set trackBy(fn) {\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && fn != null && typeof fn !== 'function') {\n      console.warn(`trackBy must be a function, but received ${JSON.stringify(fn)}.`);\n    }\n    this._trackByFn = fn;\n  }\n  /**\n   * The table's source of data, which can be provided in three ways (in order of complexity):\n   *   - Simple data array (each object represents one table row)\n   *   - Stream that emits a data array each time the array changes\n   *   - `DataSource` object that implements the connect/disconnect interface.\n   *\n   * If a data array is provided, the table must be notified when the array's objects are\n   * added, removed, or moved. This can be done by calling the `renderRows()` function which will\n   * render the diff since the last table render. If the data array reference is changed, the table\n   * will automatically trigger an update to the rows.\n   *\n   * When providing an Observable stream, the table will trigger an update automatically when the\n   * stream emits a new array of data.\n   *\n   * Finally, when providing a `DataSource` object, the table will use the Observable stream\n   * provided by the connect function and trigger updates when that stream emits new data array\n   * values. During the table's ngOnDestroy or when the data source is removed from the table, the\n   * table will call the DataSource's `disconnect` function (may be useful for cleaning up any\n   * subscriptions registered during the connect process).\n   */\n  get dataSource() {\n    return this._dataSource;\n  }\n  set dataSource(dataSource) {\n    if (this._dataSource !== dataSource) {\n      this._switchDataSource(dataSource);\n    }\n  }\n  /**\n   * Whether to allow multiple rows per data object by evaluating which rows evaluate their 'when'\n   * predicate to true. If `multiTemplateDataRows` is false, which is the default value, then each\n   * dataobject will render the first row that evaluates its when predicate to true, in the order\n   * defined in the table, or otherwise the default row which does not have a when predicate.\n   */\n  get multiTemplateDataRows() {\n    return this._multiTemplateDataRows;\n  }\n  set multiTemplateDataRows(value) {\n    this._multiTemplateDataRows = value;\n    // In Ivy if this value is set via a static attribute (e.g. <table multiTemplateDataRows>),\n    // this setter will be invoked before the row outlet has been defined hence the null check.\n    if (this._rowOutlet && this._rowOutlet.viewContainer.length) {\n      this._forceRenderDataRows();\n      this.updateStickyColumnStyles();\n    }\n  }\n  /**\n   * Whether to use a fixed table layout. Enabling this option will enforce consistent column widths\n   * and optimize rendering sticky styles for native tables. No-op for flex tables.\n   */\n  get fixedLayout() {\n    return this._fixedLayout;\n  }\n  set fixedLayout(value) {\n    this._fixedLayout = value;\n    // Toggling `fixedLayout` may change column widths. Sticky column styles should be recalculated.\n    this._forceRecalculateCellWidths = true;\n    this._stickyColumnStylesNeedReset = true;\n  }\n  constructor(_differs, _changeDetectorRef, _elementRef, role, _dir, _document, _platform, _viewRepeater, _coalescedStyleScheduler, _viewportRuler,\n  /**\n   * @deprecated `_stickyPositioningListener` parameter to become required.\n   * @breaking-change 13.0.0\n   */\n  _stickyPositioningListener,\n  /**\n   * @deprecated `_ngZone` parameter to become required.\n   * @breaking-change 14.0.0\n   */\n  _ngZone) {\n    this._differs = _differs;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._elementRef = _elementRef;\n    this._dir = _dir;\n    this._platform = _platform;\n    this._viewRepeater = _viewRepeater;\n    this._coalescedStyleScheduler = _coalescedStyleScheduler;\n    this._viewportRuler = _viewportRuler;\n    this._stickyPositioningListener = _stickyPositioningListener;\n    this._ngZone = _ngZone;\n    /** Subject that emits when the component has been destroyed. */\n    this._onDestroy = new Subject();\n    /**\n     * Map of all the user's defined columns (header, data, and footer cell template) identified by\n     * name. Collection populated by the column definitions gathered by `ContentChildren` as well as\n     * any custom column definitions added to `_customColumnDefs`.\n     */\n    this._columnDefsByName = new Map();\n    /**\n     * Column definitions that were defined outside of the direct content children of the table.\n     * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n     * column definitions as *its* content child.\n     */\n    this._customColumnDefs = new Set();\n    /**\n     * Data row definitions that were defined outside of the direct content children of the table.\n     * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n     * built-in data rows as *its* content child.\n     */\n    this._customRowDefs = new Set();\n    /**\n     * Header row definitions that were defined outside of the direct content children of the table.\n     * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n     * built-in header rows as *its* content child.\n     */\n    this._customHeaderRowDefs = new Set();\n    /**\n     * Footer row definitions that were defined outside of the direct content children of the table.\n     * These will be defined when, e.g., creating a wrapper around the cdkTable that has a\n     * built-in footer row as *its* content child.\n     */\n    this._customFooterRowDefs = new Set();\n    /**\n     * Whether the header row definition has been changed. Triggers an update to the header row after\n     * content is checked. Initialized as true so that the table renders the initial set of rows.\n     */\n    this._headerRowDefChanged = true;\n    /**\n     * Whether the footer row definition has been changed. Triggers an update to the footer row after\n     * content is checked. Initialized as true so that the table renders the initial set of rows.\n     */\n    this._footerRowDefChanged = true;\n    /**\n     * Whether the sticky column styles need to be updated. Set to `true` when the visible columns\n     * change.\n     */\n    this._stickyColumnStylesNeedReset = true;\n    /**\n     * Whether the sticky styler should recalculate cell widths when applying sticky styles. If\n     * `false`, cached values will be used instead. This is only applicable to tables with\n     * {@link fixedLayout} enabled. For other tables, cell widths will always be recalculated.\n     */\n    this._forceRecalculateCellWidths = true;\n    /**\n     * Cache of the latest rendered `RenderRow` objects as a map for easy retrieval when constructing\n     * a new list of `RenderRow` objects for rendering rows. Since the new list is constructed with\n     * the cached `RenderRow` objects when possible, the row identity is preserved when the data\n     * and row template matches, which allows the `IterableDiffer` to check rows by reference\n     * and understand which rows are added/moved/removed.\n     *\n     * Implemented as a map of maps where the first key is the `data: T` object and the second is the\n     * `CdkRowDef<T>` object. With the two keys, the cache points to a `RenderRow<T>` object that\n     * contains an array of created pairs. The array is necessary to handle cases where the data\n     * array contains multiple duplicate data objects and each instantiated `RenderRow` must be\n     * stored.\n     */\n    this._cachedRenderRowsMap = new Map();\n    /**\n     * CSS class added to any row or cell that has sticky positioning applied. May be overridden by\n     * table subclasses.\n     */\n    this.stickyCssClass = 'cdk-table-sticky';\n    /**\n     * Whether to manually add position: sticky to all sticky cell elements. Not needed if\n     * the position is set in a selector associated with the value of stickyCssClass. May be\n     * overridden by table subclasses\n     */\n    this.needsPositionStickyOnElement = true;\n    /** Whether the no data row is currently showing anything. */\n    this._isShowingNoDataRow = false;\n    /** Whether the table has rendered out all the outlets for the first time. */\n    this._hasAllOutlets = false;\n    /** Whether the table is done initializing. */\n    this._hasInitialized = false;\n    this._cellRoleInternal = undefined;\n    this._multiTemplateDataRows = false;\n    this._fixedLayout = false;\n    /**\n     * Emits when the table completes rendering a set of data rows based on the latest data from the\n     * data source, even if the set of rows is empty.\n     */\n    this.contentChanged = new EventEmitter();\n    // TODO(andrewseguin): Remove max value as the end index\n    //   and instead calculate the view on init and scroll.\n    /**\n     * Stream containing the latest information on what rows are being displayed on screen.\n     * Can be used by the data source to as a heuristic of what data should be provided.\n     *\n     * @docs-private\n     */\n    this.viewChange = new BehaviorSubject({\n      start: 0,\n      end: Number.MAX_VALUE\n    });\n    if (!role) {\n      _elementRef.nativeElement.setAttribute('role', 'table');\n    }\n    this._document = _document;\n    this._isServer = !_platform.isBrowser;\n    this._isNativeHtmlTable = _elementRef.nativeElement.nodeName === 'TABLE';\n  }\n  ngOnInit() {\n    this._setupStickyStyler();\n    // Set up the trackBy function so that it uses the `RenderRow` as its identity by default. If\n    // the user has provided a custom trackBy, return the result of that function as evaluated\n    // with the values of the `RenderRow`'s data and index.\n    this._dataDiffer = this._differs.find([]).create((_i, dataRow) => {\n      return this.trackBy ? this.trackBy(dataRow.dataIndex, dataRow.data) : dataRow;\n    });\n    this._viewportRuler.change().pipe(takeUntil(this._onDestroy)).subscribe(() => {\n      this._forceRecalculateCellWidths = true;\n    });\n  }\n  ngAfterContentInit() {\n    this._hasInitialized = true;\n  }\n  ngAfterContentChecked() {\n    // Only start re-rendering in `ngAfterContentChecked` after the first render.\n    if (this._canRender()) {\n      this._render();\n    }\n  }\n  ngOnDestroy() {\n    [this._rowOutlet?.viewContainer, this._headerRowOutlet?.viewContainer, this._footerRowOutlet?.viewContainer, this._cachedRenderRowsMap, this._customColumnDefs, this._customRowDefs, this._customHeaderRowDefs, this._customFooterRowDefs, this._columnDefsByName].forEach(def => {\n      def?.clear();\n    });\n    this._headerRowDefs = [];\n    this._footerRowDefs = [];\n    this._defaultRowDef = null;\n    this._onDestroy.next();\n    this._onDestroy.complete();\n    if (isDataSource(this.dataSource)) {\n      this.dataSource.disconnect(this);\n    }\n  }\n  /**\n   * Renders rows based on the table's latest set of data, which was either provided directly as an\n   * input or retrieved through an Observable stream (directly or from a DataSource).\n   * Checks for differences in the data since the last diff to perform only the necessary\n   * changes (add/remove/move rows).\n   *\n   * If the table's data source is a DataSource or Observable, this will be invoked automatically\n   * each time the provided Observable stream emits a new data array. Otherwise if your data is\n   * an array, this function will need to be called to render any changes.\n   */\n  renderRows() {\n    this._renderRows = this._getAllRenderRows();\n    const changes = this._dataDiffer.diff(this._renderRows);\n    if (!changes) {\n      this._updateNoDataRow();\n      this.contentChanged.next();\n      return;\n    }\n    const viewContainer = this._rowOutlet.viewContainer;\n    this._viewRepeater.applyChanges(changes, viewContainer, (record, _adjustedPreviousIndex, currentIndex) => this._getEmbeddedViewArgs(record.item, currentIndex), record => record.item.data, change => {\n      if (change.operation === _ViewRepeaterOperation.INSERTED && change.context) {\n        this._renderCellTemplateForItem(change.record.item.rowDef, change.context);\n      }\n    });\n    // Update the meta context of a row's context data (index, count, first, last, ...)\n    this._updateRowIndexContext();\n    // Update rows that did not get added/removed/moved but may have had their identity changed,\n    // e.g. if trackBy matched data on some property but the actual data reference changed.\n    changes.forEachIdentityChange(record => {\n      const rowView = viewContainer.get(record.currentIndex);\n      rowView.context.$implicit = record.item.data;\n    });\n    this._updateNoDataRow();\n    // Allow the new row data to render before measuring it.\n    // @breaking-change 14.0.0 Remove undefined check once _ngZone is required.\n    if (this._ngZone && NgZone.isInAngularZone()) {\n      this._ngZone.onStable.pipe(take(1), takeUntil(this._onDestroy)).subscribe(() => {\n        this.updateStickyColumnStyles();\n      });\n    } else {\n      this.updateStickyColumnStyles();\n    }\n    this.contentChanged.next();\n  }\n  /** Adds a column definition that was not included as part of the content children. */\n  addColumnDef(columnDef) {\n    this._customColumnDefs.add(columnDef);\n  }\n  /** Removes a column definition that was not included as part of the content children. */\n  removeColumnDef(columnDef) {\n    this._customColumnDefs.delete(columnDef);\n  }\n  /** Adds a row definition that was not included as part of the content children. */\n  addRowDef(rowDef) {\n    this._customRowDefs.add(rowDef);\n  }\n  /** Removes a row definition that was not included as part of the content children. */\n  removeRowDef(rowDef) {\n    this._customRowDefs.delete(rowDef);\n  }\n  /** Adds a header row definition that was not included as part of the content children. */\n  addHeaderRowDef(headerRowDef) {\n    this._customHeaderRowDefs.add(headerRowDef);\n    this._headerRowDefChanged = true;\n  }\n  /** Removes a header row definition that was not included as part of the content children. */\n  removeHeaderRowDef(headerRowDef) {\n    this._customHeaderRowDefs.delete(headerRowDef);\n    this._headerRowDefChanged = true;\n  }\n  /** Adds a footer row definition that was not included as part of the content children. */\n  addFooterRowDef(footerRowDef) {\n    this._customFooterRowDefs.add(footerRowDef);\n    this._footerRowDefChanged = true;\n  }\n  /** Removes a footer row definition that was not included as part of the content children. */\n  removeFooterRowDef(footerRowDef) {\n    this._customFooterRowDefs.delete(footerRowDef);\n    this._footerRowDefChanged = true;\n  }\n  /** Sets a no data row definition that was not included as a part of the content children. */\n  setNoDataRow(noDataRow) {\n    this._customNoDataRow = noDataRow;\n  }\n  /**\n   * Updates the header sticky styles. First resets all applied styles with respect to the cells\n   * sticking to the top. Then, evaluating which cells need to be stuck to the top. This is\n   * automatically called when the header row changes its displayed set of columns, or if its\n   * sticky input changes. May be called manually for cases where the cell content changes outside\n   * of these events.\n   */\n  updateStickyHeaderRowStyles() {\n    const headerRows = this._getRenderedRows(this._headerRowOutlet);\n    // Hide the thead element if there are no header rows. This is necessary to satisfy\n    // overzealous a11y checkers that fail because the `rowgroup` element does not contain\n    // required child `row`.\n    if (this._isNativeHtmlTable) {\n      const thead = closestTableSection(this._headerRowOutlet, 'thead');\n      if (thead) {\n        thead.style.display = headerRows.length ? '' : 'none';\n      }\n    }\n    const stickyStates = this._headerRowDefs.map(def => def.sticky);\n    this._stickyStyler.clearStickyPositioning(headerRows, ['top']);\n    this._stickyStyler.stickRows(headerRows, stickyStates, 'top');\n    // Reset the dirty state of the sticky input change since it has been used.\n    this._headerRowDefs.forEach(def => def.resetStickyChanged());\n  }\n  /**\n   * Updates the footer sticky styles. First resets all applied styles with respect to the cells\n   * sticking to the bottom. Then, evaluating which cells need to be stuck to the bottom. This is\n   * automatically called when the footer row changes its displayed set of columns, or if its\n   * sticky input changes. May be called manually for cases where the cell content changes outside\n   * of these events.\n   */\n  updateStickyFooterRowStyles() {\n    const footerRows = this._getRenderedRows(this._footerRowOutlet);\n    // Hide the tfoot element if there are no footer rows. This is necessary to satisfy\n    // overzealous a11y checkers that fail because the `rowgroup` element does not contain\n    // required child `row`.\n    if (this._isNativeHtmlTable) {\n      const tfoot = closestTableSection(this._footerRowOutlet, 'tfoot');\n      if (tfoot) {\n        tfoot.style.display = footerRows.length ? '' : 'none';\n      }\n    }\n    const stickyStates = this._footerRowDefs.map(def => def.sticky);\n    this._stickyStyler.clearStickyPositioning(footerRows, ['bottom']);\n    this._stickyStyler.stickRows(footerRows, stickyStates, 'bottom');\n    this._stickyStyler.updateStickyFooterContainer(this._elementRef.nativeElement, stickyStates);\n    // Reset the dirty state of the sticky input change since it has been used.\n    this._footerRowDefs.forEach(def => def.resetStickyChanged());\n  }\n  /**\n   * Updates the column sticky styles. First resets all applied styles with respect to the cells\n   * sticking to the left and right. Then sticky styles are added for the left and right according\n   * to the column definitions for each cell in each row. This is automatically called when\n   * the data source provides a new set of data or when a column definition changes its sticky\n   * input. May be called manually for cases where the cell content changes outside of these events.\n   */\n  updateStickyColumnStyles() {\n    const headerRows = this._getRenderedRows(this._headerRowOutlet);\n    const dataRows = this._getRenderedRows(this._rowOutlet);\n    const footerRows = this._getRenderedRows(this._footerRowOutlet);\n    // For tables not using a fixed layout, the column widths may change when new rows are rendered.\n    // In a table using a fixed layout, row content won't affect column width, so sticky styles\n    // don't need to be cleared unless either the sticky column config changes or one of the row\n    // defs change.\n    if (this._isNativeHtmlTable && !this._fixedLayout || this._stickyColumnStylesNeedReset) {\n      // Clear the left and right positioning from all columns in the table across all rows since\n      // sticky columns span across all table sections (header, data, footer)\n      this._stickyStyler.clearStickyPositioning([...headerRows, ...dataRows, ...footerRows], ['left', 'right']);\n      this._stickyColumnStylesNeedReset = false;\n    }\n    // Update the sticky styles for each header row depending on the def's sticky state\n    headerRows.forEach((headerRow, i) => {\n      this._addStickyColumnStyles([headerRow], this._headerRowDefs[i]);\n    });\n    // Update the sticky styles for each data row depending on its def's sticky state\n    this._rowDefs.forEach(rowDef => {\n      // Collect all the rows rendered with this row definition.\n      const rows = [];\n      for (let i = 0; i < dataRows.length; i++) {\n        if (this._renderRows[i].rowDef === rowDef) {\n          rows.push(dataRows[i]);\n        }\n      }\n      this._addStickyColumnStyles(rows, rowDef);\n    });\n    // Update the sticky styles for each footer row depending on the def's sticky state\n    footerRows.forEach((footerRow, i) => {\n      this._addStickyColumnStyles([footerRow], this._footerRowDefs[i]);\n    });\n    // Reset the dirty state of the sticky input change since it has been used.\n    Array.from(this._columnDefsByName.values()).forEach(def => def.resetStickyChanged());\n  }\n  /** Invoked whenever an outlet is created and has been assigned to the table. */\n  _outletAssigned() {\n    // Trigger the first render once all outlets have been assigned. We do it this way, as\n    // opposed to waiting for the next `ngAfterContentChecked`, because we don't know when\n    // the next change detection will happen.\n    // Also we can't use queries to resolve the outlets, because they're wrapped in a\n    // conditional, so we have to rely on them being assigned via DI.\n    if (!this._hasAllOutlets && this._rowOutlet && this._headerRowOutlet && this._footerRowOutlet && this._noDataRowOutlet) {\n      this._hasAllOutlets = true;\n      // In some setups this may fire before `ngAfterContentInit`\n      // so we need a check here. See #28538.\n      if (this._canRender()) {\n        this._render();\n      }\n    }\n  }\n  /** Whether the table has all the information to start rendering. */\n  _canRender() {\n    return this._hasAllOutlets && this._hasInitialized;\n  }\n  /** Renders the table if its state has changed. */\n  _render() {\n    // Cache the row and column definitions gathered by ContentChildren and programmatic injection.\n    this._cacheRowDefs();\n    this._cacheColumnDefs();\n    // Make sure that the user has at least added header, footer, or data row def.\n    if (!this._headerRowDefs.length && !this._footerRowDefs.length && !this._rowDefs.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTableMissingRowDefsError();\n    }\n    // Render updates if the list of columns have been changed for the header, row, or footer defs.\n    const columnsChanged = this._renderUpdatedColumns();\n    const rowDefsChanged = columnsChanged || this._headerRowDefChanged || this._footerRowDefChanged;\n    // Ensure sticky column styles are reset if set to `true` elsewhere.\n    this._stickyColumnStylesNeedReset = this._stickyColumnStylesNeedReset || rowDefsChanged;\n    this._forceRecalculateCellWidths = rowDefsChanged;\n    // If the header row definition has been changed, trigger a render to the header row.\n    if (this._headerRowDefChanged) {\n      this._forceRenderHeaderRows();\n      this._headerRowDefChanged = false;\n    }\n    // If the footer row definition has been changed, trigger a render to the footer row.\n    if (this._footerRowDefChanged) {\n      this._forceRenderFooterRows();\n      this._footerRowDefChanged = false;\n    }\n    // If there is a data source and row definitions, connect to the data source unless a\n    // connection has already been made.\n    if (this.dataSource && this._rowDefs.length > 0 && !this._renderChangeSubscription) {\n      this._observeRenderChanges();\n    } else if (this._stickyColumnStylesNeedReset) {\n      // In the above case, _observeRenderChanges will result in updateStickyColumnStyles being\n      // called when it row data arrives. Otherwise, we need to call it proactively.\n      this.updateStickyColumnStyles();\n    }\n    this._checkStickyStates();\n  }\n  /**\n   * Get the list of RenderRow objects to render according to the current list of data and defined\n   * row definitions. If the previous list already contained a particular pair, it should be reused\n   * so that the differ equates their references.\n   */\n  _getAllRenderRows() {\n    const renderRows = [];\n    // Store the cache and create a new one. Any re-used RenderRow objects will be moved into the\n    // new cache while unused ones can be picked up by garbage collection.\n    const prevCachedRenderRows = this._cachedRenderRowsMap;\n    this._cachedRenderRowsMap = new Map();\n    // For each data object, get the list of rows that should be rendered, represented by the\n    // respective `RenderRow` object which is the pair of `data` and `CdkRowDef`.\n    for (let i = 0; i < this._data.length; i++) {\n      let data = this._data[i];\n      const renderRowsForData = this._getRenderRowsForData(data, i, prevCachedRenderRows.get(data));\n      if (!this._cachedRenderRowsMap.has(data)) {\n        this._cachedRenderRowsMap.set(data, new WeakMap());\n      }\n      for (let j = 0; j < renderRowsForData.length; j++) {\n        let renderRow = renderRowsForData[j];\n        const cache = this._cachedRenderRowsMap.get(renderRow.data);\n        if (cache.has(renderRow.rowDef)) {\n          cache.get(renderRow.rowDef).push(renderRow);\n        } else {\n          cache.set(renderRow.rowDef, [renderRow]);\n        }\n        renderRows.push(renderRow);\n      }\n    }\n    return renderRows;\n  }\n  /**\n   * Gets a list of `RenderRow<T>` for the provided data object and any `CdkRowDef` objects that\n   * should be rendered for this data. Reuses the cached RenderRow objects if they match the same\n   * `(T, CdkRowDef)` pair.\n   */\n  _getRenderRowsForData(data, dataIndex, cache) {\n    const rowDefs = this._getRowDefs(data, dataIndex);\n    return rowDefs.map(rowDef => {\n      const cachedRenderRows = cache && cache.has(rowDef) ? cache.get(rowDef) : [];\n      if (cachedRenderRows.length) {\n        const dataRow = cachedRenderRows.shift();\n        dataRow.dataIndex = dataIndex;\n        return dataRow;\n      } else {\n        return {\n          data,\n          rowDef,\n          dataIndex\n        };\n      }\n    });\n  }\n  /** Update the map containing the content's column definitions. */\n  _cacheColumnDefs() {\n    this._columnDefsByName.clear();\n    const columnDefs = mergeArrayAndSet(this._getOwnDefs(this._contentColumnDefs), this._customColumnDefs);\n    columnDefs.forEach(columnDef => {\n      if (this._columnDefsByName.has(columnDef.name) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getTableDuplicateColumnNameError(columnDef.name);\n      }\n      this._columnDefsByName.set(columnDef.name, columnDef);\n    });\n  }\n  /** Update the list of all available row definitions that can be used. */\n  _cacheRowDefs() {\n    this._headerRowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentHeaderRowDefs), this._customHeaderRowDefs);\n    this._footerRowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentFooterRowDefs), this._customFooterRowDefs);\n    this._rowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentRowDefs), this._customRowDefs);\n    // After all row definitions are determined, find the row definition to be considered default.\n    const defaultRowDefs = this._rowDefs.filter(def => !def.when);\n    if (!this.multiTemplateDataRows && defaultRowDefs.length > 1 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTableMultipleDefaultRowDefsError();\n    }\n    this._defaultRowDef = defaultRowDefs[0];\n  }\n  /**\n   * Check if the header, data, or footer rows have changed what columns they want to display or\n   * whether the sticky states have changed for the header or footer. If there is a diff, then\n   * re-render that section.\n   */\n  _renderUpdatedColumns() {\n    const columnsDiffReducer = (acc, def) => acc || !!def.getColumnsDiff();\n    // Force re-render data rows if the list of column definitions have changed.\n    const dataColumnsChanged = this._rowDefs.reduce(columnsDiffReducer, false);\n    if (dataColumnsChanged) {\n      this._forceRenderDataRows();\n    }\n    // Force re-render header/footer rows if the list of column definitions have changed.\n    const headerColumnsChanged = this._headerRowDefs.reduce(columnsDiffReducer, false);\n    if (headerColumnsChanged) {\n      this._forceRenderHeaderRows();\n    }\n    const footerColumnsChanged = this._footerRowDefs.reduce(columnsDiffReducer, false);\n    if (footerColumnsChanged) {\n      this._forceRenderFooterRows();\n    }\n    return dataColumnsChanged || headerColumnsChanged || footerColumnsChanged;\n  }\n  /**\n   * Switch to the provided data source by resetting the data and unsubscribing from the current\n   * render change subscription if one exists. If the data source is null, interpret this by\n   * clearing the row outlet. Otherwise start listening for new data.\n   */\n  _switchDataSource(dataSource) {\n    this._data = [];\n    if (isDataSource(this.dataSource)) {\n      this.dataSource.disconnect(this);\n    }\n    // Stop listening for data from the previous data source.\n    if (this._renderChangeSubscription) {\n      this._renderChangeSubscription.unsubscribe();\n      this._renderChangeSubscription = null;\n    }\n    if (!dataSource) {\n      if (this._dataDiffer) {\n        this._dataDiffer.diff([]);\n      }\n      if (this._rowOutlet) {\n        this._rowOutlet.viewContainer.clear();\n      }\n    }\n    this._dataSource = dataSource;\n  }\n  /** Set up a subscription for the data provided by the data source. */\n  _observeRenderChanges() {\n    // If no data source has been set, there is nothing to observe for changes.\n    if (!this.dataSource) {\n      return;\n    }\n    let dataStream;\n    if (isDataSource(this.dataSource)) {\n      dataStream = this.dataSource.connect(this);\n    } else if (isObservable(this.dataSource)) {\n      dataStream = this.dataSource;\n    } else if (Array.isArray(this.dataSource)) {\n      dataStream = of(this.dataSource);\n    }\n    if (dataStream === undefined && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTableUnknownDataSourceError();\n    }\n    this._renderChangeSubscription = dataStream.pipe(takeUntil(this._onDestroy)).subscribe(data => {\n      this._data = data || [];\n      this.renderRows();\n    });\n  }\n  /**\n   * Clears any existing content in the header row outlet and creates a new embedded view\n   * in the outlet using the header row definition.\n   */\n  _forceRenderHeaderRows() {\n    // Clear the header row outlet if any content exists.\n    if (this._headerRowOutlet.viewContainer.length > 0) {\n      this._headerRowOutlet.viewContainer.clear();\n    }\n    this._headerRowDefs.forEach((def, i) => this._renderRow(this._headerRowOutlet, def, i));\n    this.updateStickyHeaderRowStyles();\n  }\n  /**\n   * Clears any existing content in the footer row outlet and creates a new embedded view\n   * in the outlet using the footer row definition.\n   */\n  _forceRenderFooterRows() {\n    // Clear the footer row outlet if any content exists.\n    if (this._footerRowOutlet.viewContainer.length > 0) {\n      this._footerRowOutlet.viewContainer.clear();\n    }\n    this._footerRowDefs.forEach((def, i) => this._renderRow(this._footerRowOutlet, def, i));\n    this.updateStickyFooterRowStyles();\n  }\n  /** Adds the sticky column styles for the rows according to the columns' stick states. */\n  _addStickyColumnStyles(rows, rowDef) {\n    const columnDefs = Array.from(rowDef.columns || []).map(columnName => {\n      const columnDef = this._columnDefsByName.get(columnName);\n      if (!columnDef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getTableUnknownColumnError(columnName);\n      }\n      return columnDef;\n    });\n    const stickyStartStates = columnDefs.map(columnDef => columnDef.sticky);\n    const stickyEndStates = columnDefs.map(columnDef => columnDef.stickyEnd);\n    this._stickyStyler.updateStickyColumns(rows, stickyStartStates, stickyEndStates, !this._fixedLayout || this._forceRecalculateCellWidths);\n  }\n  /** Gets the list of rows that have been rendered in the row outlet. */\n  _getRenderedRows(rowOutlet) {\n    const renderedRows = [];\n    for (let i = 0; i < rowOutlet.viewContainer.length; i++) {\n      const viewRef = rowOutlet.viewContainer.get(i);\n      renderedRows.push(viewRef.rootNodes[0]);\n    }\n    return renderedRows;\n  }\n  /**\n   * Get the matching row definitions that should be used for this row data. If there is only\n   * one row definition, it is returned. Otherwise, find the row definitions that has a when\n   * predicate that returns true with the data. If none return true, return the default row\n   * definition.\n   */\n  _getRowDefs(data, dataIndex) {\n    if (this._rowDefs.length == 1) {\n      return [this._rowDefs[0]];\n    }\n    let rowDefs = [];\n    if (this.multiTemplateDataRows) {\n      rowDefs = this._rowDefs.filter(def => !def.when || def.when(dataIndex, data));\n    } else {\n      let rowDef = this._rowDefs.find(def => def.when && def.when(dataIndex, data)) || this._defaultRowDef;\n      if (rowDef) {\n        rowDefs.push(rowDef);\n      }\n    }\n    if (!rowDefs.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTableMissingMatchingRowDefError(data);\n    }\n    return rowDefs;\n  }\n  _getEmbeddedViewArgs(renderRow, index) {\n    const rowDef = renderRow.rowDef;\n    const context = {\n      $implicit: renderRow.data\n    };\n    return {\n      templateRef: rowDef.template,\n      context,\n      index\n    };\n  }\n  /**\n   * Creates a new row template in the outlet and fills it with the set of cell templates.\n   * Optionally takes a context to provide to the row and cells, as well as an optional index\n   * of where to place the new row template in the outlet.\n   */\n  _renderRow(outlet, rowDef, index, context = {}) {\n    // TODO(andrewseguin): enforce that one outlet was instantiated from createEmbeddedView\n    const view = outlet.viewContainer.createEmbeddedView(rowDef.template, context, index);\n    this._renderCellTemplateForItem(rowDef, context);\n    return view;\n  }\n  _renderCellTemplateForItem(rowDef, context) {\n    for (let cellTemplate of this._getCellTemplates(rowDef)) {\n      if (CdkCellOutlet.mostRecentCellOutlet) {\n        CdkCellOutlet.mostRecentCellOutlet._viewContainer.createEmbeddedView(cellTemplate, context);\n      }\n    }\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Updates the index-related context for each row to reflect any changes in the index of the rows,\n   * e.g. first/last/even/odd.\n   */\n  _updateRowIndexContext() {\n    const viewContainer = this._rowOutlet.viewContainer;\n    for (let renderIndex = 0, count = viewContainer.length; renderIndex < count; renderIndex++) {\n      const viewRef = viewContainer.get(renderIndex);\n      const context = viewRef.context;\n      context.count = count;\n      context.first = renderIndex === 0;\n      context.last = renderIndex === count - 1;\n      context.even = renderIndex % 2 === 0;\n      context.odd = !context.even;\n      if (this.multiTemplateDataRows) {\n        context.dataIndex = this._renderRows[renderIndex].dataIndex;\n        context.renderIndex = renderIndex;\n      } else {\n        context.index = this._renderRows[renderIndex].dataIndex;\n      }\n    }\n  }\n  /** Gets the column definitions for the provided row def. */\n  _getCellTemplates(rowDef) {\n    if (!rowDef || !rowDef.columns) {\n      return [];\n    }\n    return Array.from(rowDef.columns, columnId => {\n      const column = this._columnDefsByName.get(columnId);\n      if (!column && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getTableUnknownColumnError(columnId);\n      }\n      return rowDef.extractCellTemplate(column);\n    });\n  }\n  /**\n   * Forces a re-render of the data rows. Should be called in cases where there has been an input\n   * change that affects the evaluation of which rows should be rendered, e.g. toggling\n   * `multiTemplateDataRows` or adding/removing row definitions.\n   */\n  _forceRenderDataRows() {\n    this._dataDiffer.diff([]);\n    this._rowOutlet.viewContainer.clear();\n    this.renderRows();\n  }\n  /**\n   * Checks if there has been a change in sticky states since last check and applies the correct\n   * sticky styles. Since checking resets the \"dirty\" state, this should only be performed once\n   * during a change detection and after the inputs are settled (after content check).\n   */\n  _checkStickyStates() {\n    const stickyCheckReducer = (acc, d) => {\n      return acc || d.hasStickyChanged();\n    };\n    // Note that the check needs to occur for every definition since it notifies the definition\n    // that it can reset its dirty state. Using another operator like `some` may short-circuit\n    // remaining definitions and leave them in an unchecked state.\n    if (this._headerRowDefs.reduce(stickyCheckReducer, false)) {\n      this.updateStickyHeaderRowStyles();\n    }\n    if (this._footerRowDefs.reduce(stickyCheckReducer, false)) {\n      this.updateStickyFooterRowStyles();\n    }\n    if (Array.from(this._columnDefsByName.values()).reduce(stickyCheckReducer, false)) {\n      this._stickyColumnStylesNeedReset = true;\n      this.updateStickyColumnStyles();\n    }\n  }\n  /**\n   * Creates the sticky styler that will be used for sticky rows and columns. Listens\n   * for directionality changes and provides the latest direction to the styler. Re-applies column\n   * stickiness when directionality changes.\n   */\n  _setupStickyStyler() {\n    const direction = this._dir ? this._dir.value : 'ltr';\n    this._stickyStyler = new StickyStyler(this._isNativeHtmlTable, this.stickyCssClass, direction, this._coalescedStyleScheduler, this._platform.isBrowser, this.needsPositionStickyOnElement, this._stickyPositioningListener);\n    (this._dir ? this._dir.change : of()).pipe(takeUntil(this._onDestroy)).subscribe(value => {\n      this._stickyStyler.direction = value;\n      this.updateStickyColumnStyles();\n    });\n  }\n  /** Filters definitions that belong to this table from a QueryList. */\n  _getOwnDefs(items) {\n    return items.filter(item => !item._table || item._table === this);\n  }\n  /** Creates or removes the no data row, depending on whether any data is being shown. */\n  _updateNoDataRow() {\n    const noDataRow = this._customNoDataRow || this._noDataRow;\n    if (!noDataRow) {\n      return;\n    }\n    const shouldShow = this._rowOutlet.viewContainer.length === 0;\n    if (shouldShow === this._isShowingNoDataRow) {\n      return;\n    }\n    const container = this._noDataRowOutlet.viewContainer;\n    if (shouldShow) {\n      const view = container.createEmbeddedView(noDataRow.templateRef);\n      const rootNode = view.rootNodes[0];\n      // Only add the attributes if we have a single root node since it's hard\n      // to figure out which one to add it to when there are multiple.\n      if (view.rootNodes.length === 1 && rootNode?.nodeType === this._document.ELEMENT_NODE) {\n        rootNode.setAttribute('role', 'row');\n        rootNode.classList.add(noDataRow._contentClassName);\n      }\n    } else {\n      container.clear();\n    }\n    this._isShowingNoDataRow = shouldShow;\n    this._changeDetectorRef.markForCheck();\n  }\n  static {\n    this.ɵfac = function CdkTable_Factory(t) {\n      return new (t || CdkTable)(i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵinjectAttribute('role'), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i2.Platform), i0.ɵɵdirectiveInject(_VIEW_REPEATER_STRATEGY), i0.ɵɵdirectiveInject(_COALESCED_STYLE_SCHEDULER), i0.ɵɵdirectiveInject(i3.ViewportRuler), i0.ɵɵdirectiveInject(STICKY_POSITIONING_LISTENER, 12), i0.ɵɵdirectiveInject(i0.NgZone, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CdkTable,\n      selectors: [[\"cdk-table\"], [\"table\", \"cdk-table\", \"\"]],\n      contentQueries: function CdkTable_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, CdkNoDataRow, 5);\n          i0.ɵɵcontentQuery(dirIndex, CdkColumnDef, 5);\n          i0.ɵɵcontentQuery(dirIndex, CdkRowDef, 5);\n          i0.ɵɵcontentQuery(dirIndex, CdkHeaderRowDef, 5);\n          i0.ɵɵcontentQuery(dirIndex, CdkFooterRowDef, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._noDataRow = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentColumnDefs = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentRowDefs = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentHeaderRowDefs = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentFooterRowDefs = _t);\n        }\n      },\n      hostAttrs: [1, \"cdk-table\"],\n      hostVars: 2,\n      hostBindings: function CdkTable_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"cdk-table-fixed-layout\", ctx.fixedLayout);\n        }\n      },\n      inputs: {\n        trackBy: \"trackBy\",\n        dataSource: \"dataSource\",\n        multiTemplateDataRows: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"multiTemplateDataRows\", \"multiTemplateDataRows\", booleanAttribute],\n        fixedLayout: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"fixedLayout\", \"fixedLayout\", booleanAttribute]\n      },\n      outputs: {\n        contentChanged: \"contentChanged\"\n      },\n      exportAs: [\"cdkTable\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CDK_TABLE,\n        useExisting: CdkTable\n      }, {\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _DisposeViewRepeaterStrategy\n      }, {\n        provide: _COALESCED_STYLE_SCHEDULER,\n        useClass: _CoalescedStyleScheduler\n      },\n      // Prevent nested tables from seeing this table's StickyPositioningListener.\n      {\n        provide: STICKY_POSITIONING_LISTENER,\n        useValue: null\n      }]), i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c1,\n      decls: 5,\n      vars: 2,\n      consts: [[\"role\", \"rowgroup\"], [\"headerRowOutlet\", \"\"], [\"rowOutlet\", \"\"], [\"noDataRowOutlet\", \"\"], [\"footerRowOutlet\", \"\"]],\n      template: function CdkTable_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵprojection(0);\n          i0.ɵɵprojection(1, 1);\n          i0.ɵɵtemplate(2, CdkTable_Conditional_2_Template, 1, 0)(3, CdkTable_Conditional_3_Template, 7, 0)(4, CdkTable_Conditional_4_Template, 4, 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(2, ctx._isServer ? 2 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(3, ctx._isNativeHtmlTable ? 3 : 4);\n        }\n      },\n      dependencies: [HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet],\n      styles: [\".cdk-table-fixed-layout{table-layout:fixed}\"],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTable, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-table, table[cdk-table]',\n      exportAs: 'cdkTable',\n      template: CDK_TABLE_TEMPLATE,\n      host: {\n        'class': 'cdk-table',\n        '[class.cdk-table-fixed-layout]': 'fixedLayout'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      providers: [{\n        provide: CDK_TABLE,\n        useExisting: CdkTable\n      }, {\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _DisposeViewRepeaterStrategy\n      }, {\n        provide: _COALESCED_STYLE_SCHEDULER,\n        useClass: _CoalescedStyleScheduler\n      },\n      // Prevent nested tables from seeing this table's StickyPositioningListener.\n      {\n        provide: STICKY_POSITIONING_LISTENER,\n        useValue: null\n      }],\n      standalone: true,\n      imports: [HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet],\n      styles: [\".cdk-table-fixed-layout{table-layout:fixed}\"]\n    }]\n  }], () => [{\n    type: i0.IterableDiffers\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Attribute,\n      args: ['role']\n    }]\n  }, {\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i2.Platform\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [_VIEW_REPEATER_STRATEGY]\n    }]\n  }, {\n    type: _CoalescedStyleScheduler,\n    decorators: [{\n      type: Inject,\n      args: [_COALESCED_STYLE_SCHEDULER]\n    }]\n  }, {\n    type: i3.ViewportRuler\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: SkipSelf\n    }, {\n      type: Inject,\n      args: [STICKY_POSITIONING_LISTENER]\n    }]\n  }, {\n    type: i0.NgZone,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    trackBy: [{\n      type: Input\n    }],\n    dataSource: [{\n      type: Input\n    }],\n    multiTemplateDataRows: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    fixedLayout: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    contentChanged: [{\n      type: Output\n    }],\n    _contentColumnDefs: [{\n      type: ContentChildren,\n      args: [CdkColumnDef, {\n        descendants: true\n      }]\n    }],\n    _contentRowDefs: [{\n      type: ContentChildren,\n      args: [CdkRowDef, {\n        descendants: true\n      }]\n    }],\n    _contentHeaderRowDefs: [{\n      type: ContentChildren,\n      args: [CdkHeaderRowDef, {\n        descendants: true\n      }]\n    }],\n    _contentFooterRowDefs: [{\n      type: ContentChildren,\n      args: [CdkFooterRowDef, {\n        descendants: true\n      }]\n    }],\n    _noDataRow: [{\n      type: ContentChild,\n      args: [CdkNoDataRow]\n    }]\n  });\n})();\n/** Utility function that gets a merged list of the entries in an array and values of a Set. */\nfunction mergeArrayAndSet(array, set) {\n  return array.concat(Array.from(set));\n}\n/**\n * Finds the closest table section to an outlet. We can't use `HTMLElement.closest` for this,\n * because the node representing the outlet is a comment.\n */\nfunction closestTableSection(outlet, section) {\n  const uppercaseSection = section.toUpperCase();\n  let current = outlet.viewContainer.element.nativeElement;\n  while (current) {\n    // 1 is an element node.\n    const nodeName = current.nodeType === 1 ? current.nodeName : null;\n    if (nodeName === uppercaseSection) {\n      return current;\n    } else if (nodeName === 'TABLE') {\n      // Stop traversing past the `table` node.\n      break;\n    }\n    current = current.parentNode;\n  }\n  return null;\n}\n\n/**\n * Column that simply shows text content for the header and row cells. Assumes that the table\n * is using the native table implementation (`<table>`).\n *\n * By default, the name of this column will be the header text and data property accessor.\n * The header text can be overridden with the `headerText` input. Cell values can be overridden with\n * the `dataAccessor` input. Change the text justification to the start or end using the `justify`\n * input.\n */\nclass CdkTextColumn {\n  /** Column name that should be used to reference this column. */\n  get name() {\n    return this._name;\n  }\n  set name(name) {\n    this._name = name;\n    // With Ivy, inputs can be initialized before static query results are\n    // available. In that case, we defer the synchronization until \"ngOnInit\" fires.\n    this._syncColumnDefName();\n  }\n  constructor(\n  // `CdkTextColumn` is always requiring a table, but we just assert it manually\n  // for better error reporting.\n  // tslint:disable-next-line: lightweight-tokens\n  _table, _options) {\n    this._table = _table;\n    this._options = _options;\n    /** Alignment of the cell values. */\n    this.justify = 'start';\n    this._options = _options || {};\n  }\n  ngOnInit() {\n    this._syncColumnDefName();\n    if (this.headerText === undefined) {\n      this.headerText = this._createDefaultHeaderText();\n    }\n    if (!this.dataAccessor) {\n      this.dataAccessor = this._options.defaultDataAccessor || ((data, name) => data[name]);\n    }\n    if (this._table) {\n      // Provide the cell and headerCell directly to the table with the static `ViewChild` query,\n      // since the columnDef will not pick up its content by the time the table finishes checking\n      // its content and initializing the rows.\n      this.columnDef.cell = this.cell;\n      this.columnDef.headerCell = this.headerCell;\n      this._table.addColumnDef(this.columnDef);\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getTableTextColumnMissingParentTableError();\n    }\n  }\n  ngOnDestroy() {\n    if (this._table) {\n      this._table.removeColumnDef(this.columnDef);\n    }\n  }\n  /**\n   * Creates a default header text. Use the options' header text transformation function if one\n   * has been provided. Otherwise simply capitalize the column name.\n   */\n  _createDefaultHeaderText() {\n    const name = this.name;\n    if (!name && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTableTextColumnMissingNameError();\n    }\n    if (this._options && this._options.defaultHeaderTextTransform) {\n      return this._options.defaultHeaderTextTransform(name);\n    }\n    return name[0].toUpperCase() + name.slice(1);\n  }\n  /** Synchronizes the column definition name with the text column name. */\n  _syncColumnDefName() {\n    if (this.columnDef) {\n      this.columnDef.name = this.name;\n    }\n  }\n  static {\n    this.ɵfac = function CdkTextColumn_Factory(t) {\n      return new (t || CdkTextColumn)(i0.ɵɵdirectiveInject(CdkTable, 8), i0.ɵɵdirectiveInject(TEXT_COLUMN_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CdkTextColumn,\n      selectors: [[\"cdk-text-column\"]],\n      viewQuery: function CdkTextColumn_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(CdkColumnDef, 7);\n          i0.ɵɵviewQuery(CdkCellDef, 7);\n          i0.ɵɵviewQuery(CdkHeaderCellDef, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.columnDef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.cell = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerCell = _t.first);\n        }\n      },\n      inputs: {\n        name: \"name\",\n        headerText: \"headerText\",\n        dataAccessor: \"dataAccessor\",\n        justify: \"justify\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 0,\n      consts: [[\"cdkColumnDef\", \"\"], [\"cdk-header-cell\", \"\", 3, \"text-align\", 4, \"cdkHeaderCellDef\"], [\"cdk-cell\", \"\", 3, \"text-align\", 4, \"cdkCellDef\"], [\"cdk-header-cell\", \"\"], [\"cdk-cell\", \"\"]],\n      template: function CdkTextColumn_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainerStart(0, 0);\n          i0.ɵɵtemplate(1, CdkTextColumn_th_1_Template, 2, 3, \"th\", 1)(2, CdkTextColumn_td_2_Template, 2, 3, \"td\", 2);\n          i0.ɵɵelementContainerEnd();\n        }\n      },\n      dependencies: [CdkColumnDef, CdkHeaderCellDef, CdkHeaderCell, CdkCellDef, CdkCell],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTextColumn, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-text-column',\n      template: `\n    <ng-container cdkColumnDef>\n      <th cdk-header-cell *cdkHeaderCellDef [style.text-align]=\"justify\">\n        {{headerText}}\n      </th>\n      <td cdk-cell *cdkCellDef=\"let data\" [style.text-align]=\"justify\">\n        {{dataAccessor(data, name)}}\n      </td>\n    </ng-container>\n  `,\n      encapsulation: ViewEncapsulation.None,\n      // Change detection is intentionally not set to OnPush. This component's template will be provided\n      // to the table to be inserted into its view. This is problematic when change detection runs since\n      // the bindings in this template will be evaluated _after_ the table's view is evaluated, which\n      // mean's the template in the table's view will not have the updated value (and in fact will cause\n      // an ExpressionChangedAfterItHasBeenCheckedError).\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      standalone: true,\n      imports: [CdkColumnDef, CdkHeaderCellDef, CdkHeaderCell, CdkCellDef, CdkCell]\n    }]\n  }], () => [{\n    type: CdkTable,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [TEXT_COLUMN_OPTIONS]\n    }]\n  }], {\n    name: [{\n      type: Input\n    }],\n    headerText: [{\n      type: Input\n    }],\n    dataAccessor: [{\n      type: Input\n    }],\n    justify: [{\n      type: Input\n    }],\n    columnDef: [{\n      type: ViewChild,\n      args: [CdkColumnDef, {\n        static: true\n      }]\n    }],\n    cell: [{\n      type: ViewChild,\n      args: [CdkCellDef, {\n        static: true\n      }]\n    }],\n    headerCell: [{\n      type: ViewChild,\n      args: [CdkHeaderCellDef, {\n        static: true\n      }]\n    }]\n  });\n})();\nconst EXPORTED_DECLARATIONS = [CdkTable, CdkRowDef, CdkCellDef, CdkCellOutlet, CdkHeaderCellDef, CdkFooterCellDef, CdkColumnDef, CdkCell, CdkRow, CdkHeaderCell, CdkFooterCell, CdkHeaderRow, CdkHeaderRowDef, CdkFooterRow, CdkFooterRowDef, DataRowOutlet, HeaderRowOutlet, FooterRowOutlet, CdkTextColumn, CdkNoDataRow, CdkRecycleRows, NoDataRowOutlet];\nclass CdkTableModule {\n  static {\n    this.ɵfac = function CdkTableModule_Factory(t) {\n      return new (t || CdkTableModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: CdkTableModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [ScrollingModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTableModule, [{\n    type: NgModule,\n    args: [{\n      exports: EXPORTED_DECLARATIONS,\n      imports: [ScrollingModule, ...EXPORTED_DECLARATIONS]\n    }]\n  }], null, null);\n})();\n\n/**\n * Mixin to provide a directive with a function that checks if the sticky input has been\n * changed since the last time the function was called. Essentially adds a dirty-check to the\n * sticky value.\n * @docs-private\n * @deprecated Implement the `CanStick` interface instead.\n * @breaking-change 19.0.0\n */\nfunction mixinHasStickyInput(base) {\n  return class extends base {\n    /** Whether sticky positioning should be applied. */\n    get sticky() {\n      return this._sticky;\n    }\n    set sticky(v) {\n      const prevValue = this._sticky;\n      this._sticky = coerceBooleanProperty(v);\n      this._hasStickyChanged = prevValue !== this._sticky;\n    }\n    /** Whether the sticky value has changed since this was last called. */\n    hasStickyChanged() {\n      const hasStickyChanged = this._hasStickyChanged;\n      this._hasStickyChanged = false;\n      return hasStickyChanged;\n    }\n    /** Resets the dirty check for cases where the sticky state has been used without checking. */\n    resetStickyChanged() {\n      this._hasStickyChanged = false;\n    }\n    constructor(...args) {\n      super(...args);\n      this._sticky = false;\n      /** Whether the sticky input has changed since it was last checked. */\n      this._hasStickyChanged = false;\n    }\n  };\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BaseCdkCell, BaseRowDef, CDK_ROW_TEMPLATE, CDK_TABLE, CDK_TABLE_TEMPLATE, CdkCell, CdkCellDef, CdkCellOutlet, CdkColumnDef, CdkFooterCell, CdkFooterCellDef, CdkFooterRow, CdkFooterRowDef, CdkHeaderCell, CdkHeaderCellDef, CdkHeaderRow, CdkHeaderRowDef, CdkNoDataRow, CdkRecycleRows, CdkRow, CdkRowDef, CdkTable, CdkTableModule, CdkTextColumn, DataRowOutlet, FooterRowOutlet, HeaderRowOutlet, NoDataRowOutlet, STICKY_DIRECTIONS, STICKY_POSITIONING_LISTENER, StickyStyler, TEXT_COLUMN_OPTIONS, _COALESCED_STYLE_SCHEDULER, _CoalescedStyleScheduler, _Schedule, mixinHasStickyInput };", "map": {"version": 3, "names": ["i1", "_VIEW_REPEATER_STRATEGY", "_RecycleViewRepeaterStrategy", "isDataSource", "_ViewRepeaterOperation", "_DisposeViewRepeaterStrategy", "DataSource", "i2", "i3", "ScrollingModule", "DOCUMENT", "i0", "InjectionToken", "Directive", "booleanAttribute", "Inject", "Optional", "Input", "ContentChild", "Injectable", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "inject", "EmbeddedViewRef", "EventEmitter", "NgZone", "Attribute", "SkipSelf", "Output", "ContentChildren", "ViewChild", "NgModule", "Subject", "from", "BehaviorSubject", "isObservable", "of", "takeUntil", "take", "coerceBooleanProperty", "_c0", "_c1", "CdkTable_Conditional_2_Template", "rf", "ctx", "ɵɵprojection", "CdkTable_Conditional_3_Template", "ɵɵelementStart", "ɵɵelementContainer", "ɵɵelementEnd", "CdkTable_Conditional_4_Template", "CdkTextColumn_th_1_Template", "ɵɵtext", "ctx_r0", "ɵɵnextContext", "ɵɵstyleProp", "justify", "ɵɵadvance", "ɵɵtextInterpolate1", "headerText", "CdkTextColumn_td_2_Template", "data_r2", "$implicit", "dataAccessor", "name", "CDK_TABLE", "TEXT_COLUMN_OPTIONS", "CdkCellDef", "constructor", "template", "ɵfac", "CdkCellDef_Factory", "t", "ɵɵdirectiveInject", "TemplateRef", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "standalone", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "CdkHeaderCellDef", "CdkHeaderCellDef_Factory", "CdkFooterCellDef", "CdkFooterCellDef_Factory", "CdkColumnDef", "_name", "_setNameInput", "sticky", "_sticky", "value", "_hasStickyChanged", "stickyEnd", "_stickyEnd", "_table", "hasStickyChanged", "resetStickyChanged", "_updateColumnCssClassName", "_columnCssClassName", "cssClassFriendlyName", "replace", "CdkColumnDef_Factory", "contentQueries", "CdkColumnDef_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "cell", "first", "headerCell", "<PERSON><PERSON><PERSON><PERSON>", "inputs", "ɵɵInputFlags", "None", "HasDecoratorInputTransform", "features", "ɵɵProvidersFeature", "provide", "useExisting", "ɵɵInputTransformsFeature", "providers", "undefined", "decorators", "transform", "BaseCdkCell", "columnDef", "elementRef", "nativeElement", "classList", "add", "CdkHeaderCell", "CdkHeaderCell_Factory", "ElementRef", "hostAttrs", "ɵɵInheritDefinitionFeature", "host", "CdkFooterCell", "role", "_getCellRole", "setAttribute", "CdkFooterCell_Factory", "CdkCell", "CdkCell_Factory", "_Schedule", "tasks", "endTasks", "_COALESCED_STYLE_SCHEDULER", "_CoalescedStyleScheduler", "_ngZone", "_currentSchedule", "_destroyed", "schedule", "task", "_createScheduleIfNeeded", "push", "scheduleEnd", "ngOnDestroy", "next", "complete", "_getScheduleObservable", "pipe", "subscribe", "length", "isStable", "Promise", "resolve", "onStable", "_CoalescedStyleScheduler_Factory", "ɵɵinject", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "CDK_ROW_TEMPLATE", "BaseRowDef", "_differs", "ngOnChanges", "changes", "_<PERSON><PERSON><PERSON><PERSON>", "columns", "currentValue", "find", "create", "diff", "getColumnsDiff", "extractCellTemplate", "column", "CdkHeaderRowDef", "CdkFooterRowDef", "BaseRowDef_Factory", "Iterable<PERSON><PERSON><PERSON>", "ɵɵNgOnChangesFeature", "CdkHeaderRowDef_Factory", "alias", "CdkFooterRowDef_Factory", "CdkRowDef", "CdkRowDef_Factory", "when", "CdkCellOutlet", "mostRecentCellOutlet", "_viewContainer", "CdkCellOutlet_Factory", "ViewContainerRef", "CdkHeaderRow", "CdkHeaderRow_Factory", "ɵcmp", "ɵɵdefineComponent", "ɵɵStandaloneFeature", "decls", "vars", "consts", "CdkHeaderRow_Template", "dependencies", "encapsulation", "changeDetection", "<PERSON><PERSON><PERSON>", "imports", "CdkFooterRow", "CdkFooterRow_Factory", "CdkFooterRow_Template", "CdkRow", "CdkRow_Factory", "CdkRow_Template", "CdkNoDataRow", "templateRef", "_contentClassName", "CdkNoDataRow_Factory", "STICKY_DIRECTIONS", "<PERSON>y<PERSON><PERSON><PERSON>", "_isNativeHtmlTable", "_stickCellCss", "direction", "_coalescedStyleScheduler", "_isBrowser", "_needsPositionStickyOnElement", "_positionListener", "_cachedCellWidths", "_borderCellCss", "clearStickyPositioning", "rows", "stickyDirections", "elementsToClear", "row", "nodeType", "ELEMENT_NODE", "i", "children", "element", "_removeStickyStyle", "updateStickyColumns", "stickyStartStates", "stickyEndStates", "recalculateCellWidths", "some", "state", "stickyColumnsUpdated", "sizes", "stickyEndColumnsUpdated", "firstRow", "num<PERSON>ells", "cellWidths", "_get<PERSON><PERSON><PERSON><PERSON><PERSON>", "startPositions", "_getStickyStartColumnPositions", "endPositions", "_getStickyEndColumnPositions", "lastStickyStart", "lastIndexOf", "firstStickyEnd", "indexOf", "isRtl", "start", "end", "_addStickyStyle", "slice", "map", "width", "index", "reverse", "stickRows", "rowsToStick", "stickyStates", "position", "states", "stickyOffsets", "stickyCellHeights", "elementsToStick", "rowIndex", "stickyOffset", "Array", "height", "getBoundingClientRect", "borderedRowIndex", "offset", "isBorderedRowIndex", "stickyHeaderRowsUpdated", "offsets", "elements", "stickyFooterRowsUpdated", "updateStickyFooterContainer", "tableElement", "tfoot", "querySelector", "dir", "style", "remove", "hasDirection", "zIndex", "_getCalculatedZIndex", "dir<PERSON><PERSON><PERSON>", "isBorderElement", "cssText", "zIndexIncrements", "top", "bottom", "left", "right", "firstRowCells", "widths", "positions", "nextPosition", "getTableUnknownColumnError", "id", "Error", "getTableDuplicateColumnNameError", "getTableMultipleDefaultRowDefsError", "getTableMissingMatchingRowDefError", "data", "JSON", "stringify", "getTableMissingRowDefsError", "getTableUnknownDataSourceError", "getTableTextColumnMissingParentTableError", "getTableTextColumnMissingNameError", "STICKY_POSITIONING_LISTENER", "CdkRecycleRows", "CdkRecycleRows_Factory", "useClass", "DataRowOutlet", "viewContainer", "table", "_rowOutlet", "_outletAssigned", "DataRowOutlet_Factory", "HeaderRowOutlet", "_headerRowOutlet", "HeaderRowOutlet_Factory", "FooterRowOutlet", "_footerRowOutlet", "FooterRowOutlet_Factory", "NoDataRowOutlet", "_noDataRowOutlet", "NoDataRowOutlet_Factory", "CDK_TABLE_TEMPLATE", "RowViewRef", "CdkTable", "_cellRoleInternal", "_elementRef", "getAttribute", "cellRole", "trackBy", "_trackByFn", "fn", "console", "warn", "dataSource", "_dataSource", "_switchDataSource", "multiTemplateDataRows", "_multiTemplateDataRows", "_forceRenderDataRows", "updateStickyColumnStyles", "fixedLayout", "_fixedLayout", "_forceRecalculateCellWidths", "_stickyColumnStylesNeedReset", "_changeDetectorRef", "_dir", "_document", "_platform", "_view<PERSON><PERSON><PERSON>er", "_viewportRuler", "_stickyPositioningListener", "_onD<PERSON>roy", "_columnDefsByName", "Map", "_customColumnDefs", "Set", "_customRowDefs", "_customHeaderRowDefs", "_customFooterRowDefs", "_headerRowDefChanged", "_footerRowDefChanged", "_cachedRenderRowsMap", "stickyCssClass", "needsPositionStickyOnElement", "_isShowingNoDataRow", "_hasAllOutlets", "_hasInitialized", "contentChanged", "viewChange", "Number", "MAX_VALUE", "_isServer", "<PERSON><PERSON><PERSON><PERSON>", "nodeName", "ngOnInit", "_setupStickyStyler", "_data<PERSON><PERSON>er", "_i", "dataRow", "dataIndex", "change", "ngAfterContentInit", "ngAfterContentChecked", "_canRender", "_render", "for<PERSON>ach", "def", "clear", "_headerRowDefs", "_footerRowDefs", "_defaultRowDef", "disconnect", "renderRows", "_renderRows", "_getAllRenderRows", "_updateNoDataRow", "applyChanges", "record", "_adjustedPreviousIndex", "currentIndex", "_getEmbeddedViewArgs", "item", "operation", "INSERTED", "context", "_renderCellTemplateForItem", "rowDef", "_updateRowIndexContext", "forEachIdentityChange", "row<PERSON>iew", "get", "isInAngularZone", "addColumnDef", "removeColumnDef", "delete", "addRowDef", "removeRowDef", "addHeaderRowDef", "headerRowDef", "removeHeaderRowDef", "addFooterRowDef", "footerRowDef", "removeFooterRowDef", "setNoDataRow", "noDataRow", "_customNoDataRow", "updateStickyHeaderRowStyles", "headerRows", "_getRenderedRows", "thead", "closestTableSection", "display", "_sticky<PERSON><PERSON><PERSON>", "updateStickyFooterRowStyles", "footerRows", "dataRows", "headerRow", "_addStickyColumnStyles", "_rowDefs", "footerRow", "values", "_cacheRowDefs", "_cacheColumnDefs", "columnsChanged", "_renderUpdatedColumns", "rowDefsChanged", "_forceRenderHeaderRows", "_forceRenderFooterRows", "_renderChangeSubscription", "_observe<PERSON><PERSON><PERSON><PERSON><PERSON>", "_checkStickyStates", "prevCachedRenderRows", "_data", "renderRowsForData", "_getRenderRowsForData", "has", "set", "WeakMap", "j", "renderRow", "cache", "rowDefs", "_getRowDefs", "cachedRenderRows", "shift", "columnDefs", "mergeArrayAndSet", "_getOwnDefs", "_contentColumnDefs", "_contentHeaderRowDefs", "_contentFooterRowDefs", "_contentRowDefs", "defaultRowDefs", "filter", "columnsDiffReducer", "acc", "dataColumnsChanged", "reduce", "headerColumnsChanged", "footerColumnsChanged", "unsubscribe", "dataStream", "connect", "isArray", "_renderRow", "columnName", "rowOutlet", "renderedRows", "viewRef", "rootNodes", "outlet", "view", "createEmbeddedView", "cellTemplate", "_getCellTemplates", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderIndex", "count", "last", "even", "odd", "columnId", "stickyCheckReducer", "d", "items", "_noDataRow", "shouldShow", "container", "rootNode", "CdkTable_Factory", "ChangeDetectorRef", "ɵɵinjectAttribute", "Directionality", "Platform", "ViewportRuler", "CdkTable_ContentQueries", "hostVars", "hostBindings", "CdkTable_HostBindings", "ɵɵclassProp", "outputs", "exportAs", "useValue", "ngContentSelectors", "CdkTable_Template", "ɵɵprojectionDef", "ɵɵtemplate", "ɵɵconditional", "styles", "descendants", "array", "concat", "section", "uppercaseSection", "toUpperCase", "current", "parentNode", "CdkTextColumn", "_syncColumnDefName", "_options", "_createDefaultHeaderText", "defaultDataAccessor", "defaultHeaderTextTransform", "CdkTextColumn_Factory", "viewQuery", "CdkTextColumn_Query", "ɵɵviewQuery", "CdkTextColumn_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "static", "EXPORTED_DECLARATIONS", "CdkTableModule", "CdkTableModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports", "mixinHasStickyInput", "base", "v", "prevValue"], "sources": ["C:/Users/<USER>/Desktop/BookCart/bookcart-frontend/node_modules/@angular/cdk/fesm2022/table.mjs"], "sourcesContent": ["import * as i1 from '@angular/cdk/bidi';\nimport { _VIEW_REPEATER_STRATEGY, _RecycleViewRepeaterStrategy, isDataSource, _ViewRepeaterOperation, _DisposeViewRepeaterStrategy } from '@angular/cdk/collections';\nexport { DataSource } from '@angular/cdk/collections';\nimport * as i2 from '@angular/cdk/platform';\nimport * as i3 from '@angular/cdk/scrolling';\nimport { ScrollingModule } from '@angular/cdk/scrolling';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, booleanAttribute, Inject, Optional, Input, ContentChild, Injectable, Component, ChangeDetectionStrategy, ViewEncapsulation, inject, EmbeddedViewRef, EventEmitter, NgZone, Attribute, SkipSelf, Output, ContentChildren, ViewChild, NgModule } from '@angular/core';\nimport { Subject, from, BehaviorSubject, isObservable, of } from 'rxjs';\nimport { takeUntil, take } from 'rxjs/operators';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\n\n/**\n * Used to provide a table to some of the sub-components without causing a circular dependency.\n * @docs-private\n */\nconst CDK_TABLE = new InjectionToken('CDK_TABLE');\n/** Injection token that can be used to specify the text column options. */\nconst TEXT_COLUMN_OPTIONS = new InjectionToken('text-column-options');\n\n/**\n * Cell definition for a CDK table.\n * Captures the template of a column's data row cell as well as cell-specific properties.\n */\nclass CdkCellDef {\n    constructor(/** @docs-private */ template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkCellDef, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkCellDef, isStandalone: true, selector: \"[cdkCellDef]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkCellDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkCellDef]',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n/**\n * Header cell definition for a CDK table.\n * Captures the template of a column's header cell and as well as cell-specific properties.\n */\nclass CdkHeaderCellDef {\n    constructor(/** @docs-private */ template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkHeaderCellDef, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkHeaderCellDef, isStandalone: true, selector: \"[cdkHeaderCellDef]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkHeaderCellDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkHeaderCellDef]',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n/**\n * Footer cell definition for a CDK table.\n * Captures the template of a column's footer cell and as well as cell-specific properties.\n */\nclass CdkFooterCellDef {\n    constructor(/** @docs-private */ template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkFooterCellDef, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkFooterCellDef, isStandalone: true, selector: \"[cdkFooterCellDef]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkFooterCellDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkFooterCellDef]',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n/**\n * Column definition for the CDK table.\n * Defines a set of cells available for a table column.\n */\nclass CdkColumnDef {\n    /** Unique name for this column. */\n    get name() {\n        return this._name;\n    }\n    set name(name) {\n        this._setNameInput(name);\n    }\n    /** Whether the cell is sticky. */\n    get sticky() {\n        return this._sticky;\n    }\n    set sticky(value) {\n        if (value !== this._sticky) {\n            this._sticky = value;\n            this._hasStickyChanged = true;\n        }\n    }\n    /**\n     * Whether this column should be sticky positioned on the end of the row. Should make sure\n     * that it mimics the `CanStick` mixin such that `_hasStickyChanged` is set to true if the value\n     * has been changed.\n     */\n    get stickyEnd() {\n        return this._stickyEnd;\n    }\n    set stickyEnd(value) {\n        if (value !== this._stickyEnd) {\n            this._stickyEnd = value;\n            this._hasStickyChanged = true;\n        }\n    }\n    constructor(_table) {\n        this._table = _table;\n        this._hasStickyChanged = false;\n        this._sticky = false;\n        this._stickyEnd = false;\n    }\n    /** Whether the sticky state has changed. */\n    hasStickyChanged() {\n        const hasStickyChanged = this._hasStickyChanged;\n        this.resetStickyChanged();\n        return hasStickyChanged;\n    }\n    /** Resets the sticky changed state. */\n    resetStickyChanged() {\n        this._hasStickyChanged = false;\n    }\n    /**\n     * Overridable method that sets the css classes that will be added to every cell in this\n     * column.\n     * In the future, columnCssClassName will change from type string[] to string and this\n     * will set a single string value.\n     * @docs-private\n     */\n    _updateColumnCssClassName() {\n        this._columnCssClassName = [`cdk-column-${this.cssClassFriendlyName}`];\n    }\n    /**\n     * This has been extracted to a util because of TS 4 and VE.\n     * View Engine doesn't support property rename inheritance.\n     * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n     * @docs-private\n     */\n    _setNameInput(value) {\n        // If the directive is set without a name (updated programmatically), then this setter will\n        // trigger with an empty string and should not overwrite the programmatically set value.\n        if (value) {\n            this._name = value;\n            this.cssClassFriendlyName = value.replace(/[^a-z0-9_-]/gi, '-');\n            this._updateColumnCssClassName();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkColumnDef, deps: [{ token: CDK_TABLE, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: CdkColumnDef, isStandalone: true, selector: \"[cdkColumnDef]\", inputs: { name: [\"cdkColumnDef\", \"name\"], sticky: [\"sticky\", \"sticky\", booleanAttribute], stickyEnd: [\"stickyEnd\", \"stickyEnd\", booleanAttribute] }, providers: [{ provide: 'MAT_SORT_HEADER_COLUMN_DEF', useExisting: CdkColumnDef }], queries: [{ propertyName: \"cell\", first: true, predicate: CdkCellDef, descendants: true }, { propertyName: \"headerCell\", first: true, predicate: CdkHeaderCellDef, descendants: true }, { propertyName: \"footerCell\", first: true, predicate: CdkFooterCellDef, descendants: true }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkColumnDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkColumnDef]',\n                    providers: [{ provide: 'MAT_SORT_HEADER_COLUMN_DEF', useExisting: CdkColumnDef }],\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CDK_TABLE]\n                }, {\n                    type: Optional\n                }] }], propDecorators: { name: [{\n                type: Input,\n                args: ['cdkColumnDef']\n            }], sticky: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], stickyEnd: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], cell: [{\n                type: ContentChild,\n                args: [CdkCellDef]\n            }], headerCell: [{\n                type: ContentChild,\n                args: [CdkHeaderCellDef]\n            }], footerCell: [{\n                type: ContentChild,\n                args: [CdkFooterCellDef]\n            }] } });\n/** Base class for the cells. Adds a CSS classname that identifies the column it renders in. */\nclass BaseCdkCell {\n    constructor(columnDef, elementRef) {\n        elementRef.nativeElement.classList.add(...columnDef._columnCssClassName);\n    }\n}\n/** Header cell template container that adds the right classes and role. */\nclass CdkHeaderCell extends BaseCdkCell {\n    constructor(columnDef, elementRef) {\n        super(columnDef, elementRef);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkHeaderCell, deps: [{ token: CdkColumnDef }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkHeaderCell, isStandalone: true, selector: \"cdk-header-cell, th[cdk-header-cell]\", host: { attributes: { \"role\": \"columnheader\" }, classAttribute: \"cdk-header-cell\" }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkHeaderCell, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-header-cell, th[cdk-header-cell]',\n                    host: {\n                        'class': 'cdk-header-cell',\n                        'role': 'columnheader',\n                    },\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: CdkColumnDef }, { type: i0.ElementRef }] });\n/** Footer cell template container that adds the right classes and role. */\nclass CdkFooterCell extends BaseCdkCell {\n    constructor(columnDef, elementRef) {\n        super(columnDef, elementRef);\n        const role = columnDef._table?._getCellRole();\n        if (role) {\n            elementRef.nativeElement.setAttribute('role', role);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkFooterCell, deps: [{ token: CdkColumnDef }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkFooterCell, isStandalone: true, selector: \"cdk-footer-cell, td[cdk-footer-cell]\", host: { classAttribute: \"cdk-footer-cell\" }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkFooterCell, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-footer-cell, td[cdk-footer-cell]',\n                    host: {\n                        'class': 'cdk-footer-cell',\n                    },\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: CdkColumnDef }, { type: i0.ElementRef }] });\n/** Cell template container that adds the right classes and role. */\nclass CdkCell extends BaseCdkCell {\n    constructor(columnDef, elementRef) {\n        super(columnDef, elementRef);\n        const role = columnDef._table?._getCellRole();\n        if (role) {\n            elementRef.nativeElement.setAttribute('role', role);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkCell, deps: [{ token: CdkColumnDef }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkCell, isStandalone: true, selector: \"cdk-cell, td[cdk-cell]\", host: { classAttribute: \"cdk-cell\" }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkCell, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-cell, td[cdk-cell]',\n                    host: {\n                        'class': 'cdk-cell',\n                    },\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: CdkColumnDef }, { type: i0.ElementRef }] });\n\n/**\n * @docs-private\n */\nclass _Schedule {\n    constructor() {\n        this.tasks = [];\n        this.endTasks = [];\n    }\n}\n/** Injection token used to provide a coalesced style scheduler. */\nconst _COALESCED_STYLE_SCHEDULER = new InjectionToken('_COALESCED_STYLE_SCHEDULER');\n/**\n * Allows grouping up CSSDom mutations after the current execution context.\n * This can significantly improve performance when separate consecutive functions are\n * reading from the CSSDom and then mutating it.\n *\n * @docs-private\n */\nclass _CoalescedStyleScheduler {\n    constructor(_ngZone) {\n        this._ngZone = _ngZone;\n        this._currentSchedule = null;\n        this._destroyed = new Subject();\n    }\n    /**\n     * Schedules the specified task to run at the end of the current VM turn.\n     */\n    schedule(task) {\n        this._createScheduleIfNeeded();\n        this._currentSchedule.tasks.push(task);\n    }\n    /**\n     * Schedules the specified task to run after other scheduled tasks at the end of the current\n     * VM turn.\n     */\n    scheduleEnd(task) {\n        this._createScheduleIfNeeded();\n        this._currentSchedule.endTasks.push(task);\n    }\n    /** Prevent any further tasks from running. */\n    ngOnDestroy() {\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    _createScheduleIfNeeded() {\n        if (this._currentSchedule) {\n            return;\n        }\n        this._currentSchedule = new _Schedule();\n        this._getScheduleObservable()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => {\n            while (this._currentSchedule.tasks.length || this._currentSchedule.endTasks.length) {\n                const schedule = this._currentSchedule;\n                // Capture new tasks scheduled by the current set of tasks.\n                this._currentSchedule = new _Schedule();\n                for (const task of schedule.tasks) {\n                    task();\n                }\n                for (const task of schedule.endTasks) {\n                    task();\n                }\n            }\n            this._currentSchedule = null;\n        });\n    }\n    _getScheduleObservable() {\n        // Use onStable when in the context of an ongoing change detection cycle so that we\n        // do not accidentally trigger additional cycles.\n        return this._ngZone.isStable\n            ? from(Promise.resolve(undefined))\n            : this._ngZone.onStable.pipe(take(1));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: _CoalescedStyleScheduler, deps: [{ token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: _CoalescedStyleScheduler }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: _CoalescedStyleScheduler, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: i0.NgZone }] });\n\n/**\n * The row template that can be used by the mat-table. Should not be used outside of the\n * material library.\n */\nconst CDK_ROW_TEMPLATE = `<ng-container cdkCellOutlet></ng-container>`;\n/**\n * Base class for the CdkHeaderRowDef and CdkRowDef that handles checking their columns inputs\n * for changes and notifying the table.\n */\nclass BaseRowDef {\n    constructor(\n    /** @docs-private */ template, _differs) {\n        this.template = template;\n        this._differs = _differs;\n    }\n    ngOnChanges(changes) {\n        // Create a new columns differ if one does not yet exist. Initialize it based on initial value\n        // of the columns property or an empty array if none is provided.\n        if (!this._columnsDiffer) {\n            const columns = (changes['columns'] && changes['columns'].currentValue) || [];\n            this._columnsDiffer = this._differs.find(columns).create();\n            this._columnsDiffer.diff(columns);\n        }\n    }\n    /**\n     * Returns the difference between the current columns and the columns from the last diff, or null\n     * if there is no difference.\n     */\n    getColumnsDiff() {\n        return this._columnsDiffer.diff(this.columns);\n    }\n    /** Gets this row def's relevant cell template from the provided column def. */\n    extractCellTemplate(column) {\n        if (this instanceof CdkHeaderRowDef) {\n            return column.headerCell.template;\n        }\n        if (this instanceof CdkFooterRowDef) {\n            return column.footerCell.template;\n        }\n        else {\n            return column.cell.template;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: BaseRowDef, deps: [{ token: i0.TemplateRef }, { token: i0.IterableDiffers }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: BaseRowDef, usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: BaseRowDef, decorators: [{\n            type: Directive\n        }], ctorParameters: () => [{ type: i0.TemplateRef }, { type: i0.IterableDiffers }] });\n/**\n * Header row definition for the CDK table.\n * Captures the header row's template and other header properties such as the columns to display.\n */\nclass CdkHeaderRowDef extends BaseRowDef {\n    /** Whether the row is sticky. */\n    get sticky() {\n        return this._sticky;\n    }\n    set sticky(value) {\n        if (value !== this._sticky) {\n            this._sticky = value;\n            this._hasStickyChanged = true;\n        }\n    }\n    constructor(template, _differs, _table) {\n        super(template, _differs);\n        this._table = _table;\n        this._hasStickyChanged = false;\n        this._sticky = false;\n    }\n    // Prerender fails to recognize that ngOnChanges in a part of this class through inheritance.\n    // Explicitly define it so that the method is called as part of the Angular lifecycle.\n    ngOnChanges(changes) {\n        super.ngOnChanges(changes);\n    }\n    /** Whether the sticky state has changed. */\n    hasStickyChanged() {\n        const hasStickyChanged = this._hasStickyChanged;\n        this.resetStickyChanged();\n        return hasStickyChanged;\n    }\n    /** Resets the sticky changed state. */\n    resetStickyChanged() {\n        this._hasStickyChanged = false;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkHeaderRowDef, deps: [{ token: i0.TemplateRef }, { token: i0.IterableDiffers }, { token: CDK_TABLE, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: CdkHeaderRowDef, isStandalone: true, selector: \"[cdkHeaderRowDef]\", inputs: { columns: [\"cdkHeaderRowDef\", \"columns\"], sticky: [\"cdkHeaderRowDefSticky\", \"sticky\", booleanAttribute] }, usesInheritance: true, usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkHeaderRowDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkHeaderRowDef]',\n                    inputs: [{ name: 'columns', alias: 'cdkHeaderRowDef' }],\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }, { type: i0.IterableDiffers }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CDK_TABLE]\n                }, {\n                    type: Optional\n                }] }], propDecorators: { sticky: [{\n                type: Input,\n                args: [{ alias: 'cdkHeaderRowDefSticky', transform: booleanAttribute }]\n            }] } });\n/**\n * Footer row definition for the CDK table.\n * Captures the footer row's template and other footer properties such as the columns to display.\n */\nclass CdkFooterRowDef extends BaseRowDef {\n    /** Whether the row is sticky. */\n    get sticky() {\n        return this._sticky;\n    }\n    set sticky(value) {\n        if (value !== this._sticky) {\n            this._sticky = value;\n            this._hasStickyChanged = true;\n        }\n    }\n    constructor(template, _differs, _table) {\n        super(template, _differs);\n        this._table = _table;\n        this._hasStickyChanged = false;\n        this._sticky = false;\n    }\n    // Prerender fails to recognize that ngOnChanges in a part of this class through inheritance.\n    // Explicitly define it so that the method is called as part of the Angular lifecycle.\n    ngOnChanges(changes) {\n        super.ngOnChanges(changes);\n    }\n    /** Whether the sticky state has changed. */\n    hasStickyChanged() {\n        const hasStickyChanged = this._hasStickyChanged;\n        this.resetStickyChanged();\n        return hasStickyChanged;\n    }\n    /** Resets the sticky changed state. */\n    resetStickyChanged() {\n        this._hasStickyChanged = false;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkFooterRowDef, deps: [{ token: i0.TemplateRef }, { token: i0.IterableDiffers }, { token: CDK_TABLE, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: CdkFooterRowDef, isStandalone: true, selector: \"[cdkFooterRowDef]\", inputs: { columns: [\"cdkFooterRowDef\", \"columns\"], sticky: [\"cdkFooterRowDefSticky\", \"sticky\", booleanAttribute] }, usesInheritance: true, usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkFooterRowDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkFooterRowDef]',\n                    inputs: [{ name: 'columns', alias: 'cdkFooterRowDef' }],\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }, { type: i0.IterableDiffers }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CDK_TABLE]\n                }, {\n                    type: Optional\n                }] }], propDecorators: { sticky: [{\n                type: Input,\n                args: [{ alias: 'cdkFooterRowDefSticky', transform: booleanAttribute }]\n            }] } });\n/**\n * Data row definition for the CDK table.\n * Captures the header row's template and other row properties such as the columns to display and\n * a when predicate that describes when this row should be used.\n */\nclass CdkRowDef extends BaseRowDef {\n    // TODO(andrewseguin): Add an input for providing a switch function to determine\n    //   if this template should be used.\n    constructor(template, _differs, _table) {\n        super(template, _differs);\n        this._table = _table;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkRowDef, deps: [{ token: i0.TemplateRef }, { token: i0.IterableDiffers }, { token: CDK_TABLE, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkRowDef, isStandalone: true, selector: \"[cdkRowDef]\", inputs: { columns: [\"cdkRowDefColumns\", \"columns\"], when: [\"cdkRowDefWhen\", \"when\"] }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkRowDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkRowDef]',\n                    inputs: [\n                        { name: 'columns', alias: 'cdkRowDefColumns' },\n                        { name: 'when', alias: 'cdkRowDefWhen' },\n                    ],\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }, { type: i0.IterableDiffers }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CDK_TABLE]\n                }, {\n                    type: Optional\n                }] }] });\n/**\n * Outlet for rendering cells inside of a row or header row.\n * @docs-private\n */\nclass CdkCellOutlet {\n    /**\n     * Static property containing the latest constructed instance of this class.\n     * Used by the CDK table when each CdkHeaderRow and CdkRow component is created using\n     * createEmbeddedView. After one of these components are created, this property will provide\n     * a handle to provide that component's cells and context. After init, the CdkCellOutlet will\n     * construct the cells with the provided context.\n     */\n    static { this.mostRecentCellOutlet = null; }\n    constructor(_viewContainer) {\n        this._viewContainer = _viewContainer;\n        CdkCellOutlet.mostRecentCellOutlet = this;\n    }\n    ngOnDestroy() {\n        // If this was the last outlet being rendered in the view, remove the reference\n        // from the static property after it has been destroyed to avoid leaking memory.\n        if (CdkCellOutlet.mostRecentCellOutlet === this) {\n            CdkCellOutlet.mostRecentCellOutlet = null;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkCellOutlet, deps: [{ token: i0.ViewContainerRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkCellOutlet, isStandalone: true, selector: \"[cdkCellOutlet]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkCellOutlet, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkCellOutlet]',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ViewContainerRef }] });\n/** Header template container that contains the cell outlet. Adds the right class and role. */\nclass CdkHeaderRow {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkHeaderRow, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkHeaderRow, isStandalone: true, selector: \"cdk-header-row, tr[cdk-header-row]\", host: { attributes: { \"role\": \"row\" }, classAttribute: \"cdk-header-row\" }, ngImport: i0, template: \"<ng-container cdkCellOutlet></ng-container>\", isInline: true, dependencies: [{ kind: \"directive\", type: CdkCellOutlet, selector: \"[cdkCellOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkHeaderRow, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'cdk-header-row, tr[cdk-header-row]',\n                    template: CDK_ROW_TEMPLATE,\n                    host: {\n                        'class': 'cdk-header-row',\n                        'role': 'row',\n                    },\n                    // See note on CdkTable for explanation on why this uses the default change detection strategy.\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    encapsulation: ViewEncapsulation.None,\n                    standalone: true,\n                    imports: [CdkCellOutlet],\n                }]\n        }] });\n/** Footer template container that contains the cell outlet. Adds the right class and role. */\nclass CdkFooterRow {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkFooterRow, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkFooterRow, isStandalone: true, selector: \"cdk-footer-row, tr[cdk-footer-row]\", host: { attributes: { \"role\": \"row\" }, classAttribute: \"cdk-footer-row\" }, ngImport: i0, template: \"<ng-container cdkCellOutlet></ng-container>\", isInline: true, dependencies: [{ kind: \"directive\", type: CdkCellOutlet, selector: \"[cdkCellOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkFooterRow, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'cdk-footer-row, tr[cdk-footer-row]',\n                    template: CDK_ROW_TEMPLATE,\n                    host: {\n                        'class': 'cdk-footer-row',\n                        'role': 'row',\n                    },\n                    // See note on CdkTable for explanation on why this uses the default change detection strategy.\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    encapsulation: ViewEncapsulation.None,\n                    standalone: true,\n                    imports: [CdkCellOutlet],\n                }]\n        }] });\n/** Data row template container that contains the cell outlet. Adds the right class and role. */\nclass CdkRow {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkRow, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkRow, isStandalone: true, selector: \"cdk-row, tr[cdk-row]\", host: { attributes: { \"role\": \"row\" }, classAttribute: \"cdk-row\" }, ngImport: i0, template: \"<ng-container cdkCellOutlet></ng-container>\", isInline: true, dependencies: [{ kind: \"directive\", type: CdkCellOutlet, selector: \"[cdkCellOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkRow, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'cdk-row, tr[cdk-row]',\n                    template: CDK_ROW_TEMPLATE,\n                    host: {\n                        'class': 'cdk-row',\n                        'role': 'row',\n                    },\n                    // See note on CdkTable for explanation on why this uses the default change detection strategy.\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    encapsulation: ViewEncapsulation.None,\n                    standalone: true,\n                    imports: [CdkCellOutlet],\n                }]\n        }] });\n/** Row that can be used to display a message when no data is shown in the table. */\nclass CdkNoDataRow {\n    constructor(templateRef) {\n        this.templateRef = templateRef;\n        this._contentClassName = 'cdk-no-data-row';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkNoDataRow, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkNoDataRow, isStandalone: true, selector: \"ng-template[cdkNoDataRow]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkNoDataRow, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[cdkNoDataRow]',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n\n/**\n * List of all possible directions that can be used for sticky positioning.\n * @docs-private\n */\nconst STICKY_DIRECTIONS = ['top', 'bottom', 'left', 'right'];\n/**\n * Applies and removes sticky positioning styles to the `CdkTable` rows and columns cells.\n * @docs-private\n */\nclass StickyStyler {\n    /**\n     * @param _isNativeHtmlTable Whether the sticky logic should be based on a table\n     *     that uses the native `<table>` element.\n     * @param _stickCellCss The CSS class that will be applied to every row/cell that has\n     *     sticky positioning applied.\n     * @param direction The directionality context of the table (ltr/rtl); affects column positioning\n     *     by reversing left/right positions.\n     * @param _isBrowser Whether the table is currently being rendered on the server or the client.\n     * @param _needsPositionStickyOnElement Whether we need to specify position: sticky on cells\n     *     using inline styles. If false, it is assumed that position: sticky is included in\n     *     the component stylesheet for _stickCellCss.\n     * @param _positionListener A listener that is notified of changes to sticky rows/columns\n     *     and their dimensions.\n     */\n    constructor(_isNativeHtmlTable, _stickCellCss, direction, _coalescedStyleScheduler, _isBrowser = true, _needsPositionStickyOnElement = true, _positionListener) {\n        this._isNativeHtmlTable = _isNativeHtmlTable;\n        this._stickCellCss = _stickCellCss;\n        this.direction = direction;\n        this._coalescedStyleScheduler = _coalescedStyleScheduler;\n        this._isBrowser = _isBrowser;\n        this._needsPositionStickyOnElement = _needsPositionStickyOnElement;\n        this._positionListener = _positionListener;\n        this._cachedCellWidths = [];\n        this._borderCellCss = {\n            'top': `${_stickCellCss}-border-elem-top`,\n            'bottom': `${_stickCellCss}-border-elem-bottom`,\n            'left': `${_stickCellCss}-border-elem-left`,\n            'right': `${_stickCellCss}-border-elem-right`,\n        };\n    }\n    /**\n     * Clears the sticky positioning styles from the row and its cells by resetting the `position`\n     * style, setting the zIndex to 0, and unsetting each provided sticky direction.\n     * @param rows The list of rows that should be cleared from sticking in the provided directions\n     * @param stickyDirections The directions that should no longer be set as sticky on the rows.\n     */\n    clearStickyPositioning(rows, stickyDirections) {\n        const elementsToClear = [];\n        for (const row of rows) {\n            // If the row isn't an element (e.g. if it's an `ng-container`),\n            // it won't have inline styles or `children` so we skip it.\n            if (row.nodeType !== row.ELEMENT_NODE) {\n                continue;\n            }\n            elementsToClear.push(row);\n            for (let i = 0; i < row.children.length; i++) {\n                elementsToClear.push(row.children[i]);\n            }\n        }\n        // Coalesce with sticky row/column updates (and potentially other changes like column resize).\n        this._coalescedStyleScheduler.schedule(() => {\n            for (const element of elementsToClear) {\n                this._removeStickyStyle(element, stickyDirections);\n            }\n        });\n    }\n    /**\n     * Applies sticky left and right positions to the cells of each row according to the sticky\n     * states of the rendered column definitions.\n     * @param rows The rows that should have its set of cells stuck according to the sticky states.\n     * @param stickyStartStates A list of boolean states where each state represents whether the cell\n     *     in this index position should be stuck to the start of the row.\n     * @param stickyEndStates A list of boolean states where each state represents whether the cell\n     *     in this index position should be stuck to the end of the row.\n     * @param recalculateCellWidths Whether the sticky styler should recalculate the width of each\n     *     column cell. If `false` cached widths will be used instead.\n     */\n    updateStickyColumns(rows, stickyStartStates, stickyEndStates, recalculateCellWidths = true) {\n        if (!rows.length ||\n            !this._isBrowser ||\n            !(stickyStartStates.some(state => state) || stickyEndStates.some(state => state))) {\n            if (this._positionListener) {\n                this._positionListener.stickyColumnsUpdated({ sizes: [] });\n                this._positionListener.stickyEndColumnsUpdated({ sizes: [] });\n            }\n            return;\n        }\n        // Coalesce with sticky row updates (and potentially other changes like column resize).\n        this._coalescedStyleScheduler.schedule(() => {\n            const firstRow = rows[0];\n            const numCells = firstRow.children.length;\n            const cellWidths = this._getCellWidths(firstRow, recalculateCellWidths);\n            const startPositions = this._getStickyStartColumnPositions(cellWidths, stickyStartStates);\n            const endPositions = this._getStickyEndColumnPositions(cellWidths, stickyEndStates);\n            const lastStickyStart = stickyStartStates.lastIndexOf(true);\n            const firstStickyEnd = stickyEndStates.indexOf(true);\n            const isRtl = this.direction === 'rtl';\n            const start = isRtl ? 'right' : 'left';\n            const end = isRtl ? 'left' : 'right';\n            for (const row of rows) {\n                for (let i = 0; i < numCells; i++) {\n                    const cell = row.children[i];\n                    if (stickyStartStates[i]) {\n                        this._addStickyStyle(cell, start, startPositions[i], i === lastStickyStart);\n                    }\n                    if (stickyEndStates[i]) {\n                        this._addStickyStyle(cell, end, endPositions[i], i === firstStickyEnd);\n                    }\n                }\n            }\n            if (this._positionListener) {\n                this._positionListener.stickyColumnsUpdated({\n                    sizes: lastStickyStart === -1\n                        ? []\n                        : cellWidths\n                            .slice(0, lastStickyStart + 1)\n                            .map((width, index) => (stickyStartStates[index] ? width : null)),\n                });\n                this._positionListener.stickyEndColumnsUpdated({\n                    sizes: firstStickyEnd === -1\n                        ? []\n                        : cellWidths\n                            .slice(firstStickyEnd)\n                            .map((width, index) => (stickyEndStates[index + firstStickyEnd] ? width : null))\n                            .reverse(),\n                });\n            }\n        });\n    }\n    /**\n     * Applies sticky positioning to the row's cells if using the native table layout, and to the\n     * row itself otherwise.\n     * @param rowsToStick The list of rows that should be stuck according to their corresponding\n     *     sticky state and to the provided top or bottom position.\n     * @param stickyStates A list of boolean states where each state represents whether the row\n     *     should be stuck in the particular top or bottom position.\n     * @param position The position direction in which the row should be stuck if that row should be\n     *     sticky.\n     *\n     */\n    stickRows(rowsToStick, stickyStates, position) {\n        // Since we can't measure the rows on the server, we can't stick the rows properly.\n        if (!this._isBrowser) {\n            return;\n        }\n        // Coalesce with other sticky row updates (top/bottom), sticky columns updates\n        // (and potentially other changes like column resize).\n        this._coalescedStyleScheduler.schedule(() => {\n            // If positioning the rows to the bottom, reverse their order when evaluating the sticky\n            // position such that the last row stuck will be \"bottom: 0px\" and so on. Note that the\n            // sticky states need to be reversed as well.\n            const rows = position === 'bottom' ? rowsToStick.slice().reverse() : rowsToStick;\n            const states = position === 'bottom' ? stickyStates.slice().reverse() : stickyStates;\n            // Measure row heights all at once before adding sticky styles to reduce layout thrashing.\n            const stickyOffsets = [];\n            const stickyCellHeights = [];\n            const elementsToStick = [];\n            for (let rowIndex = 0, stickyOffset = 0; rowIndex < rows.length; rowIndex++) {\n                if (!states[rowIndex]) {\n                    continue;\n                }\n                stickyOffsets[rowIndex] = stickyOffset;\n                const row = rows[rowIndex];\n                elementsToStick[rowIndex] = this._isNativeHtmlTable\n                    ? Array.from(row.children)\n                    : [row];\n                const height = row.getBoundingClientRect().height;\n                stickyOffset += height;\n                stickyCellHeights[rowIndex] = height;\n            }\n            const borderedRowIndex = states.lastIndexOf(true);\n            for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {\n                if (!states[rowIndex]) {\n                    continue;\n                }\n                const offset = stickyOffsets[rowIndex];\n                const isBorderedRowIndex = rowIndex === borderedRowIndex;\n                for (const element of elementsToStick[rowIndex]) {\n                    this._addStickyStyle(element, position, offset, isBorderedRowIndex);\n                }\n            }\n            if (position === 'top') {\n                this._positionListener?.stickyHeaderRowsUpdated({\n                    sizes: stickyCellHeights,\n                    offsets: stickyOffsets,\n                    elements: elementsToStick,\n                });\n            }\n            else {\n                this._positionListener?.stickyFooterRowsUpdated({\n                    sizes: stickyCellHeights,\n                    offsets: stickyOffsets,\n                    elements: elementsToStick,\n                });\n            }\n        });\n    }\n    /**\n     * When using the native table in Safari, sticky footer cells do not stick. The only way to stick\n     * footer rows is to apply sticky styling to the tfoot container. This should only be done if\n     * all footer rows are sticky. If not all footer rows are sticky, remove sticky positioning from\n     * the tfoot element.\n     */\n    updateStickyFooterContainer(tableElement, stickyStates) {\n        if (!this._isNativeHtmlTable) {\n            return;\n        }\n        // Coalesce with other sticky updates (and potentially other changes like column resize).\n        this._coalescedStyleScheduler.schedule(() => {\n            const tfoot = tableElement.querySelector('tfoot');\n            if (tfoot) {\n                if (stickyStates.some(state => !state)) {\n                    this._removeStickyStyle(tfoot, ['bottom']);\n                }\n                else {\n                    this._addStickyStyle(tfoot, 'bottom', 0, false);\n                }\n            }\n        });\n    }\n    /**\n     * Removes the sticky style on the element by removing the sticky cell CSS class, re-evaluating\n     * the zIndex, removing each of the provided sticky directions, and removing the\n     * sticky position if there are no more directions.\n     */\n    _removeStickyStyle(element, stickyDirections) {\n        for (const dir of stickyDirections) {\n            element.style[dir] = '';\n            element.classList.remove(this._borderCellCss[dir]);\n        }\n        // If the element no longer has any more sticky directions, remove sticky positioning and\n        // the sticky CSS class.\n        // Short-circuit checking element.style[dir] for stickyDirections as they\n        // were already removed above.\n        const hasDirection = STICKY_DIRECTIONS.some(dir => stickyDirections.indexOf(dir) === -1 && element.style[dir]);\n        if (hasDirection) {\n            element.style.zIndex = this._getCalculatedZIndex(element);\n        }\n        else {\n            // When not hasDirection, _getCalculatedZIndex will always return ''.\n            element.style.zIndex = '';\n            if (this._needsPositionStickyOnElement) {\n                element.style.position = '';\n            }\n            element.classList.remove(this._stickCellCss);\n        }\n    }\n    /**\n     * Adds the sticky styling to the element by adding the sticky style class, changing position\n     * to be sticky (and -webkit-sticky), setting the appropriate zIndex, and adding a sticky\n     * direction and value.\n     */\n    _addStickyStyle(element, dir, dirValue, isBorderElement) {\n        element.classList.add(this._stickCellCss);\n        if (isBorderElement) {\n            element.classList.add(this._borderCellCss[dir]);\n        }\n        element.style[dir] = `${dirValue}px`;\n        element.style.zIndex = this._getCalculatedZIndex(element);\n        if (this._needsPositionStickyOnElement) {\n            element.style.cssText += 'position: -webkit-sticky; position: sticky; ';\n        }\n    }\n    /**\n     * Calculate what the z-index should be for the element, depending on what directions (top,\n     * bottom, left, right) have been set. It should be true that elements with a top direction\n     * should have the highest index since these are elements like a table header. If any of those\n     * elements are also sticky in another direction, then they should appear above other elements\n     * that are only sticky top (e.g. a sticky column on a sticky header). Bottom-sticky elements\n     * (e.g. footer rows) should then be next in the ordering such that they are below the header\n     * but above any non-sticky elements. Finally, left/right sticky elements (e.g. sticky columns)\n     * should minimally increment so that they are above non-sticky elements but below top and bottom\n     * elements.\n     */\n    _getCalculatedZIndex(element) {\n        const zIndexIncrements = {\n            top: 100,\n            bottom: 10,\n            left: 1,\n            right: 1,\n        };\n        let zIndex = 0;\n        // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n        // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n        // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n        for (const dir of STICKY_DIRECTIONS) {\n            if (element.style[dir]) {\n                zIndex += zIndexIncrements[dir];\n            }\n        }\n        return zIndex ? `${zIndex}` : '';\n    }\n    /** Gets the widths for each cell in the provided row. */\n    _getCellWidths(row, recalculateCellWidths = true) {\n        if (!recalculateCellWidths && this._cachedCellWidths.length) {\n            return this._cachedCellWidths;\n        }\n        const cellWidths = [];\n        const firstRowCells = row.children;\n        for (let i = 0; i < firstRowCells.length; i++) {\n            let cell = firstRowCells[i];\n            cellWidths.push(cell.getBoundingClientRect().width);\n        }\n        this._cachedCellWidths = cellWidths;\n        return cellWidths;\n    }\n    /**\n     * Determines the left and right positions of each sticky column cell, which will be the\n     * accumulation of all sticky column cell widths to the left and right, respectively.\n     * Non-sticky cells do not need to have a value set since their positions will not be applied.\n     */\n    _getStickyStartColumnPositions(widths, stickyStates) {\n        const positions = [];\n        let nextPosition = 0;\n        for (let i = 0; i < widths.length; i++) {\n            if (stickyStates[i]) {\n                positions[i] = nextPosition;\n                nextPosition += widths[i];\n            }\n        }\n        return positions;\n    }\n    /**\n     * Determines the left and right positions of each sticky column cell, which will be the\n     * accumulation of all sticky column cell widths to the left and right, respectively.\n     * Non-sticky cells do not need to have a value set since their positions will not be applied.\n     */\n    _getStickyEndColumnPositions(widths, stickyStates) {\n        const positions = [];\n        let nextPosition = 0;\n        for (let i = widths.length; i > 0; i--) {\n            if (stickyStates[i]) {\n                positions[i] = nextPosition;\n                nextPosition += widths[i];\n            }\n        }\n        return positions;\n    }\n}\n\n/**\n * Returns an error to be thrown when attempting to find an nonexistent column.\n * @param id Id whose lookup failed.\n * @docs-private\n */\nfunction getTableUnknownColumnError(id) {\n    return Error(`Could not find column with id \"${id}\".`);\n}\n/**\n * Returns an error to be thrown when two column definitions have the same name.\n * @docs-private\n */\nfunction getTableDuplicateColumnNameError(name) {\n    return Error(`Duplicate column definition name provided: \"${name}\".`);\n}\n/**\n * Returns an error to be thrown when there are multiple rows that are missing a when function.\n * @docs-private\n */\nfunction getTableMultipleDefaultRowDefsError() {\n    return Error(`There can only be one default row without a when predicate function.`);\n}\n/**\n * Returns an error to be thrown when there are no matching row defs for a particular set of data.\n * @docs-private\n */\nfunction getTableMissingMatchingRowDefError(data) {\n    return Error(`Could not find a matching row definition for the` +\n        `provided row data: ${JSON.stringify(data)}`);\n}\n/**\n * Returns an error to be thrown when there is no row definitions present in the content.\n * @docs-private\n */\nfunction getTableMissingRowDefsError() {\n    return Error('Missing definitions for header, footer, and row; ' +\n        'cannot determine which columns should be rendered.');\n}\n/**\n * Returns an error to be thrown when the data source does not match the compatible types.\n * @docs-private\n */\nfunction getTableUnknownDataSourceError() {\n    return Error(`Provided data source did not match an array, Observable, or DataSource`);\n}\n/**\n * Returns an error to be thrown when the text column cannot find a parent table to inject.\n * @docs-private\n */\nfunction getTableTextColumnMissingParentTableError() {\n    return Error(`Text column could not find a parent table for registration.`);\n}\n/**\n * Returns an error to be thrown when a table text column doesn't have a name.\n * @docs-private\n */\nfunction getTableTextColumnMissingNameError() {\n    return Error(`Table text column must have a name.`);\n}\n\n/** The injection token used to specify the StickyPositioningListener. */\nconst STICKY_POSITIONING_LISTENER = new InjectionToken('CDK_SPL');\n\n/**\n * Enables the recycle view repeater strategy, which reduces rendering latency. Not compatible with\n * tables that animate rows.\n */\nclass CdkRecycleRows {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkRecycleRows, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkRecycleRows, isStandalone: true, selector: \"cdk-table[recycleRows], table[cdk-table][recycleRows]\", providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkRecycleRows, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-table[recycleRows], table[cdk-table][recycleRows]',\n                    providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }],\n                    standalone: true,\n                }]\n        }] });\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert data rows.\n * @docs-private\n */\nclass DataRowOutlet {\n    constructor(viewContainer, elementRef) {\n        this.viewContainer = viewContainer;\n        this.elementRef = elementRef;\n        const table = inject(CDK_TABLE);\n        table._rowOutlet = this;\n        table._outletAssigned();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: DataRowOutlet, deps: [{ token: i0.ViewContainerRef }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: DataRowOutlet, isStandalone: true, selector: \"[rowOutlet]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: DataRowOutlet, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[rowOutlet]',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ViewContainerRef }, { type: i0.ElementRef }] });\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert the header.\n * @docs-private\n */\nclass HeaderRowOutlet {\n    constructor(viewContainer, elementRef) {\n        this.viewContainer = viewContainer;\n        this.elementRef = elementRef;\n        const table = inject(CDK_TABLE);\n        table._headerRowOutlet = this;\n        table._outletAssigned();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: HeaderRowOutlet, deps: [{ token: i0.ViewContainerRef }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: HeaderRowOutlet, isStandalone: true, selector: \"[headerRowOutlet]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: HeaderRowOutlet, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[headerRowOutlet]',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ViewContainerRef }, { type: i0.ElementRef }] });\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert the footer.\n * @docs-private\n */\nclass FooterRowOutlet {\n    constructor(viewContainer, elementRef) {\n        this.viewContainer = viewContainer;\n        this.elementRef = elementRef;\n        const table = inject(CDK_TABLE);\n        table._footerRowOutlet = this;\n        table._outletAssigned();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: FooterRowOutlet, deps: [{ token: i0.ViewContainerRef }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: FooterRowOutlet, isStandalone: true, selector: \"[footerRowOutlet]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: FooterRowOutlet, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[footerRowOutlet]',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ViewContainerRef }, { type: i0.ElementRef }] });\n/**\n * Provides a handle for the table to grab the view\n * container's ng-container to insert the no data row.\n * @docs-private\n */\nclass NoDataRowOutlet {\n    constructor(viewContainer, elementRef) {\n        this.viewContainer = viewContainer;\n        this.elementRef = elementRef;\n        const table = inject(CDK_TABLE);\n        table._noDataRowOutlet = this;\n        table._outletAssigned();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: NoDataRowOutlet, deps: [{ token: i0.ViewContainerRef }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: NoDataRowOutlet, isStandalone: true, selector: \"[noDataRowOutlet]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: NoDataRowOutlet, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[noDataRowOutlet]',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ViewContainerRef }, { type: i0.ElementRef }] });\n/**\n * The table template that can be used by the mat-table. Should not be used outside of the\n * material library.\n * @docs-private\n */\nconst CDK_TABLE_TEMPLATE = \n// Note that according to MDN, the `caption` element has to be projected as the **first**\n// element in the table. See https://developer.mozilla.org/en-US/docs/Web/HTML/Element/caption\n`\n  <ng-content select=\"caption\"/>\n  <ng-content select=\"colgroup, col\"/>\n\n  <!--\n    Unprojected content throws a hydration error so we need this to capture it.\n    It gets removed on the client so it doesn't affect the layout.\n  -->\n  @if (_isServer) {\n    <ng-content/>\n  }\n\n  @if (_isNativeHtmlTable) {\n    <thead role=\"rowgroup\">\n      <ng-container headerRowOutlet/>\n    </thead>\n    <tbody role=\"rowgroup\">\n      <ng-container rowOutlet/>\n      <ng-container noDataRowOutlet/>\n    </tbody>\n    <tfoot role=\"rowgroup\">\n      <ng-container footerRowOutlet/>\n    </tfoot>\n  } @else {\n    <ng-container headerRowOutlet/>\n    <ng-container rowOutlet/>\n    <ng-container noDataRowOutlet/>\n    <ng-container footerRowOutlet/>\n  }\n`;\n/**\n * Class used to conveniently type the embedded view ref for rows with a context.\n * @docs-private\n */\nclass RowViewRef extends EmbeddedViewRef {\n}\n/**\n * A data table that can render a header row, data rows, and a footer row.\n * Uses the dataSource input to determine the data to be rendered. The data can be provided either\n * as a data array, an Observable stream that emits the data array to render, or a DataSource with a\n * connect function that will return an Observable stream that emits the data array to render.\n */\nclass CdkTable {\n    /** Aria role to apply to the table's cells based on the table's own role. */\n    _getCellRole() {\n        if (this._cellRoleInternal === undefined) {\n            // Perform this lazily in case the table's role was updated by a directive after construction.\n            const role = this._elementRef.nativeElement.getAttribute('role');\n            const cellRole = role === 'grid' || role === 'treegrid' ? 'gridcell' : 'cell';\n            this._cellRoleInternal = this._isNativeHtmlTable && cellRole === 'cell' ? null : cellRole;\n        }\n        return this._cellRoleInternal;\n    }\n    /**\n     * Tracking function that will be used to check the differences in data changes. Used similarly\n     * to `ngFor` `trackBy` function. Optimize row operations by identifying a row based on its data\n     * relative to the function to know if a row should be added/removed/moved.\n     * Accepts a function that takes two parameters, `index` and `item`.\n     */\n    get trackBy() {\n        return this._trackByFn;\n    }\n    set trackBy(fn) {\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) && fn != null && typeof fn !== 'function') {\n            console.warn(`trackBy must be a function, but received ${JSON.stringify(fn)}.`);\n        }\n        this._trackByFn = fn;\n    }\n    /**\n     * The table's source of data, which can be provided in three ways (in order of complexity):\n     *   - Simple data array (each object represents one table row)\n     *   - Stream that emits a data array each time the array changes\n     *   - `DataSource` object that implements the connect/disconnect interface.\n     *\n     * If a data array is provided, the table must be notified when the array's objects are\n     * added, removed, or moved. This can be done by calling the `renderRows()` function which will\n     * render the diff since the last table render. If the data array reference is changed, the table\n     * will automatically trigger an update to the rows.\n     *\n     * When providing an Observable stream, the table will trigger an update automatically when the\n     * stream emits a new array of data.\n     *\n     * Finally, when providing a `DataSource` object, the table will use the Observable stream\n     * provided by the connect function and trigger updates when that stream emits new data array\n     * values. During the table's ngOnDestroy or when the data source is removed from the table, the\n     * table will call the DataSource's `disconnect` function (may be useful for cleaning up any\n     * subscriptions registered during the connect process).\n     */\n    get dataSource() {\n        return this._dataSource;\n    }\n    set dataSource(dataSource) {\n        if (this._dataSource !== dataSource) {\n            this._switchDataSource(dataSource);\n        }\n    }\n    /**\n     * Whether to allow multiple rows per data object by evaluating which rows evaluate their 'when'\n     * predicate to true. If `multiTemplateDataRows` is false, which is the default value, then each\n     * dataobject will render the first row that evaluates its when predicate to true, in the order\n     * defined in the table, or otherwise the default row which does not have a when predicate.\n     */\n    get multiTemplateDataRows() {\n        return this._multiTemplateDataRows;\n    }\n    set multiTemplateDataRows(value) {\n        this._multiTemplateDataRows = value;\n        // In Ivy if this value is set via a static attribute (e.g. <table multiTemplateDataRows>),\n        // this setter will be invoked before the row outlet has been defined hence the null check.\n        if (this._rowOutlet && this._rowOutlet.viewContainer.length) {\n            this._forceRenderDataRows();\n            this.updateStickyColumnStyles();\n        }\n    }\n    /**\n     * Whether to use a fixed table layout. Enabling this option will enforce consistent column widths\n     * and optimize rendering sticky styles for native tables. No-op for flex tables.\n     */\n    get fixedLayout() {\n        return this._fixedLayout;\n    }\n    set fixedLayout(value) {\n        this._fixedLayout = value;\n        // Toggling `fixedLayout` may change column widths. Sticky column styles should be recalculated.\n        this._forceRecalculateCellWidths = true;\n        this._stickyColumnStylesNeedReset = true;\n    }\n    constructor(_differs, _changeDetectorRef, _elementRef, role, _dir, _document, _platform, _viewRepeater, _coalescedStyleScheduler, _viewportRuler, \n    /**\n     * @deprecated `_stickyPositioningListener` parameter to become required.\n     * @breaking-change 13.0.0\n     */\n    _stickyPositioningListener, \n    /**\n     * @deprecated `_ngZone` parameter to become required.\n     * @breaking-change 14.0.0\n     */\n    _ngZone) {\n        this._differs = _differs;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._elementRef = _elementRef;\n        this._dir = _dir;\n        this._platform = _platform;\n        this._viewRepeater = _viewRepeater;\n        this._coalescedStyleScheduler = _coalescedStyleScheduler;\n        this._viewportRuler = _viewportRuler;\n        this._stickyPositioningListener = _stickyPositioningListener;\n        this._ngZone = _ngZone;\n        /** Subject that emits when the component has been destroyed. */\n        this._onDestroy = new Subject();\n        /**\n         * Map of all the user's defined columns (header, data, and footer cell template) identified by\n         * name. Collection populated by the column definitions gathered by `ContentChildren` as well as\n         * any custom column definitions added to `_customColumnDefs`.\n         */\n        this._columnDefsByName = new Map();\n        /**\n         * Column definitions that were defined outside of the direct content children of the table.\n         * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n         * column definitions as *its* content child.\n         */\n        this._customColumnDefs = new Set();\n        /**\n         * Data row definitions that were defined outside of the direct content children of the table.\n         * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n         * built-in data rows as *its* content child.\n         */\n        this._customRowDefs = new Set();\n        /**\n         * Header row definitions that were defined outside of the direct content children of the table.\n         * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n         * built-in header rows as *its* content child.\n         */\n        this._customHeaderRowDefs = new Set();\n        /**\n         * Footer row definitions that were defined outside of the direct content children of the table.\n         * These will be defined when, e.g., creating a wrapper around the cdkTable that has a\n         * built-in footer row as *its* content child.\n         */\n        this._customFooterRowDefs = new Set();\n        /**\n         * Whether the header row definition has been changed. Triggers an update to the header row after\n         * content is checked. Initialized as true so that the table renders the initial set of rows.\n         */\n        this._headerRowDefChanged = true;\n        /**\n         * Whether the footer row definition has been changed. Triggers an update to the footer row after\n         * content is checked. Initialized as true so that the table renders the initial set of rows.\n         */\n        this._footerRowDefChanged = true;\n        /**\n         * Whether the sticky column styles need to be updated. Set to `true` when the visible columns\n         * change.\n         */\n        this._stickyColumnStylesNeedReset = true;\n        /**\n         * Whether the sticky styler should recalculate cell widths when applying sticky styles. If\n         * `false`, cached values will be used instead. This is only applicable to tables with\n         * {@link fixedLayout} enabled. For other tables, cell widths will always be recalculated.\n         */\n        this._forceRecalculateCellWidths = true;\n        /**\n         * Cache of the latest rendered `RenderRow` objects as a map for easy retrieval when constructing\n         * a new list of `RenderRow` objects for rendering rows. Since the new list is constructed with\n         * the cached `RenderRow` objects when possible, the row identity is preserved when the data\n         * and row template matches, which allows the `IterableDiffer` to check rows by reference\n         * and understand which rows are added/moved/removed.\n         *\n         * Implemented as a map of maps where the first key is the `data: T` object and the second is the\n         * `CdkRowDef<T>` object. With the two keys, the cache points to a `RenderRow<T>` object that\n         * contains an array of created pairs. The array is necessary to handle cases where the data\n         * array contains multiple duplicate data objects and each instantiated `RenderRow` must be\n         * stored.\n         */\n        this._cachedRenderRowsMap = new Map();\n        /**\n         * CSS class added to any row or cell that has sticky positioning applied. May be overridden by\n         * table subclasses.\n         */\n        this.stickyCssClass = 'cdk-table-sticky';\n        /**\n         * Whether to manually add position: sticky to all sticky cell elements. Not needed if\n         * the position is set in a selector associated with the value of stickyCssClass. May be\n         * overridden by table subclasses\n         */\n        this.needsPositionStickyOnElement = true;\n        /** Whether the no data row is currently showing anything. */\n        this._isShowingNoDataRow = false;\n        /** Whether the table has rendered out all the outlets for the first time. */\n        this._hasAllOutlets = false;\n        /** Whether the table is done initializing. */\n        this._hasInitialized = false;\n        this._cellRoleInternal = undefined;\n        this._multiTemplateDataRows = false;\n        this._fixedLayout = false;\n        /**\n         * Emits when the table completes rendering a set of data rows based on the latest data from the\n         * data source, even if the set of rows is empty.\n         */\n        this.contentChanged = new EventEmitter();\n        // TODO(andrewseguin): Remove max value as the end index\n        //   and instead calculate the view on init and scroll.\n        /**\n         * Stream containing the latest information on what rows are being displayed on screen.\n         * Can be used by the data source to as a heuristic of what data should be provided.\n         *\n         * @docs-private\n         */\n        this.viewChange = new BehaviorSubject({\n            start: 0,\n            end: Number.MAX_VALUE,\n        });\n        if (!role) {\n            _elementRef.nativeElement.setAttribute('role', 'table');\n        }\n        this._document = _document;\n        this._isServer = !_platform.isBrowser;\n        this._isNativeHtmlTable = _elementRef.nativeElement.nodeName === 'TABLE';\n    }\n    ngOnInit() {\n        this._setupStickyStyler();\n        // Set up the trackBy function so that it uses the `RenderRow` as its identity by default. If\n        // the user has provided a custom trackBy, return the result of that function as evaluated\n        // with the values of the `RenderRow`'s data and index.\n        this._dataDiffer = this._differs.find([]).create((_i, dataRow) => {\n            return this.trackBy ? this.trackBy(dataRow.dataIndex, dataRow.data) : dataRow;\n        });\n        this._viewportRuler\n            .change()\n            .pipe(takeUntil(this._onDestroy))\n            .subscribe(() => {\n            this._forceRecalculateCellWidths = true;\n        });\n    }\n    ngAfterContentInit() {\n        this._hasInitialized = true;\n    }\n    ngAfterContentChecked() {\n        // Only start re-rendering in `ngAfterContentChecked` after the first render.\n        if (this._canRender()) {\n            this._render();\n        }\n    }\n    ngOnDestroy() {\n        [\n            this._rowOutlet?.viewContainer,\n            this._headerRowOutlet?.viewContainer,\n            this._footerRowOutlet?.viewContainer,\n            this._cachedRenderRowsMap,\n            this._customColumnDefs,\n            this._customRowDefs,\n            this._customHeaderRowDefs,\n            this._customFooterRowDefs,\n            this._columnDefsByName,\n        ].forEach((def) => {\n            def?.clear();\n        });\n        this._headerRowDefs = [];\n        this._footerRowDefs = [];\n        this._defaultRowDef = null;\n        this._onDestroy.next();\n        this._onDestroy.complete();\n        if (isDataSource(this.dataSource)) {\n            this.dataSource.disconnect(this);\n        }\n    }\n    /**\n     * Renders rows based on the table's latest set of data, which was either provided directly as an\n     * input or retrieved through an Observable stream (directly or from a DataSource).\n     * Checks for differences in the data since the last diff to perform only the necessary\n     * changes (add/remove/move rows).\n     *\n     * If the table's data source is a DataSource or Observable, this will be invoked automatically\n     * each time the provided Observable stream emits a new data array. Otherwise if your data is\n     * an array, this function will need to be called to render any changes.\n     */\n    renderRows() {\n        this._renderRows = this._getAllRenderRows();\n        const changes = this._dataDiffer.diff(this._renderRows);\n        if (!changes) {\n            this._updateNoDataRow();\n            this.contentChanged.next();\n            return;\n        }\n        const viewContainer = this._rowOutlet.viewContainer;\n        this._viewRepeater.applyChanges(changes, viewContainer, (record, _adjustedPreviousIndex, currentIndex) => this._getEmbeddedViewArgs(record.item, currentIndex), record => record.item.data, (change) => {\n            if (change.operation === _ViewRepeaterOperation.INSERTED && change.context) {\n                this._renderCellTemplateForItem(change.record.item.rowDef, change.context);\n            }\n        });\n        // Update the meta context of a row's context data (index, count, first, last, ...)\n        this._updateRowIndexContext();\n        // Update rows that did not get added/removed/moved but may have had their identity changed,\n        // e.g. if trackBy matched data on some property but the actual data reference changed.\n        changes.forEachIdentityChange((record) => {\n            const rowView = viewContainer.get(record.currentIndex);\n            rowView.context.$implicit = record.item.data;\n        });\n        this._updateNoDataRow();\n        // Allow the new row data to render before measuring it.\n        // @breaking-change 14.0.0 Remove undefined check once _ngZone is required.\n        if (this._ngZone && NgZone.isInAngularZone()) {\n            this._ngZone.onStable.pipe(take(1), takeUntil(this._onDestroy)).subscribe(() => {\n                this.updateStickyColumnStyles();\n            });\n        }\n        else {\n            this.updateStickyColumnStyles();\n        }\n        this.contentChanged.next();\n    }\n    /** Adds a column definition that was not included as part of the content children. */\n    addColumnDef(columnDef) {\n        this._customColumnDefs.add(columnDef);\n    }\n    /** Removes a column definition that was not included as part of the content children. */\n    removeColumnDef(columnDef) {\n        this._customColumnDefs.delete(columnDef);\n    }\n    /** Adds a row definition that was not included as part of the content children. */\n    addRowDef(rowDef) {\n        this._customRowDefs.add(rowDef);\n    }\n    /** Removes a row definition that was not included as part of the content children. */\n    removeRowDef(rowDef) {\n        this._customRowDefs.delete(rowDef);\n    }\n    /** Adds a header row definition that was not included as part of the content children. */\n    addHeaderRowDef(headerRowDef) {\n        this._customHeaderRowDefs.add(headerRowDef);\n        this._headerRowDefChanged = true;\n    }\n    /** Removes a header row definition that was not included as part of the content children. */\n    removeHeaderRowDef(headerRowDef) {\n        this._customHeaderRowDefs.delete(headerRowDef);\n        this._headerRowDefChanged = true;\n    }\n    /** Adds a footer row definition that was not included as part of the content children. */\n    addFooterRowDef(footerRowDef) {\n        this._customFooterRowDefs.add(footerRowDef);\n        this._footerRowDefChanged = true;\n    }\n    /** Removes a footer row definition that was not included as part of the content children. */\n    removeFooterRowDef(footerRowDef) {\n        this._customFooterRowDefs.delete(footerRowDef);\n        this._footerRowDefChanged = true;\n    }\n    /** Sets a no data row definition that was not included as a part of the content children. */\n    setNoDataRow(noDataRow) {\n        this._customNoDataRow = noDataRow;\n    }\n    /**\n     * Updates the header sticky styles. First resets all applied styles with respect to the cells\n     * sticking to the top. Then, evaluating which cells need to be stuck to the top. This is\n     * automatically called when the header row changes its displayed set of columns, or if its\n     * sticky input changes. May be called manually for cases where the cell content changes outside\n     * of these events.\n     */\n    updateStickyHeaderRowStyles() {\n        const headerRows = this._getRenderedRows(this._headerRowOutlet);\n        // Hide the thead element if there are no header rows. This is necessary to satisfy\n        // overzealous a11y checkers that fail because the `rowgroup` element does not contain\n        // required child `row`.\n        if (this._isNativeHtmlTable) {\n            const thead = closestTableSection(this._headerRowOutlet, 'thead');\n            if (thead) {\n                thead.style.display = headerRows.length ? '' : 'none';\n            }\n        }\n        const stickyStates = this._headerRowDefs.map(def => def.sticky);\n        this._stickyStyler.clearStickyPositioning(headerRows, ['top']);\n        this._stickyStyler.stickRows(headerRows, stickyStates, 'top');\n        // Reset the dirty state of the sticky input change since it has been used.\n        this._headerRowDefs.forEach(def => def.resetStickyChanged());\n    }\n    /**\n     * Updates the footer sticky styles. First resets all applied styles with respect to the cells\n     * sticking to the bottom. Then, evaluating which cells need to be stuck to the bottom. This is\n     * automatically called when the footer row changes its displayed set of columns, or if its\n     * sticky input changes. May be called manually for cases where the cell content changes outside\n     * of these events.\n     */\n    updateStickyFooterRowStyles() {\n        const footerRows = this._getRenderedRows(this._footerRowOutlet);\n        // Hide the tfoot element if there are no footer rows. This is necessary to satisfy\n        // overzealous a11y checkers that fail because the `rowgroup` element does not contain\n        // required child `row`.\n        if (this._isNativeHtmlTable) {\n            const tfoot = closestTableSection(this._footerRowOutlet, 'tfoot');\n            if (tfoot) {\n                tfoot.style.display = footerRows.length ? '' : 'none';\n            }\n        }\n        const stickyStates = this._footerRowDefs.map(def => def.sticky);\n        this._stickyStyler.clearStickyPositioning(footerRows, ['bottom']);\n        this._stickyStyler.stickRows(footerRows, stickyStates, 'bottom');\n        this._stickyStyler.updateStickyFooterContainer(this._elementRef.nativeElement, stickyStates);\n        // Reset the dirty state of the sticky input change since it has been used.\n        this._footerRowDefs.forEach(def => def.resetStickyChanged());\n    }\n    /**\n     * Updates the column sticky styles. First resets all applied styles with respect to the cells\n     * sticking to the left and right. Then sticky styles are added for the left and right according\n     * to the column definitions for each cell in each row. This is automatically called when\n     * the data source provides a new set of data or when a column definition changes its sticky\n     * input. May be called manually for cases where the cell content changes outside of these events.\n     */\n    updateStickyColumnStyles() {\n        const headerRows = this._getRenderedRows(this._headerRowOutlet);\n        const dataRows = this._getRenderedRows(this._rowOutlet);\n        const footerRows = this._getRenderedRows(this._footerRowOutlet);\n        // For tables not using a fixed layout, the column widths may change when new rows are rendered.\n        // In a table using a fixed layout, row content won't affect column width, so sticky styles\n        // don't need to be cleared unless either the sticky column config changes or one of the row\n        // defs change.\n        if ((this._isNativeHtmlTable && !this._fixedLayout) || this._stickyColumnStylesNeedReset) {\n            // Clear the left and right positioning from all columns in the table across all rows since\n            // sticky columns span across all table sections (header, data, footer)\n            this._stickyStyler.clearStickyPositioning([...headerRows, ...dataRows, ...footerRows], ['left', 'right']);\n            this._stickyColumnStylesNeedReset = false;\n        }\n        // Update the sticky styles for each header row depending on the def's sticky state\n        headerRows.forEach((headerRow, i) => {\n            this._addStickyColumnStyles([headerRow], this._headerRowDefs[i]);\n        });\n        // Update the sticky styles for each data row depending on its def's sticky state\n        this._rowDefs.forEach(rowDef => {\n            // Collect all the rows rendered with this row definition.\n            const rows = [];\n            for (let i = 0; i < dataRows.length; i++) {\n                if (this._renderRows[i].rowDef === rowDef) {\n                    rows.push(dataRows[i]);\n                }\n            }\n            this._addStickyColumnStyles(rows, rowDef);\n        });\n        // Update the sticky styles for each footer row depending on the def's sticky state\n        footerRows.forEach((footerRow, i) => {\n            this._addStickyColumnStyles([footerRow], this._footerRowDefs[i]);\n        });\n        // Reset the dirty state of the sticky input change since it has been used.\n        Array.from(this._columnDefsByName.values()).forEach(def => def.resetStickyChanged());\n    }\n    /** Invoked whenever an outlet is created and has been assigned to the table. */\n    _outletAssigned() {\n        // Trigger the first render once all outlets have been assigned. We do it this way, as\n        // opposed to waiting for the next `ngAfterContentChecked`, because we don't know when\n        // the next change detection will happen.\n        // Also we can't use queries to resolve the outlets, because they're wrapped in a\n        // conditional, so we have to rely on them being assigned via DI.\n        if (!this._hasAllOutlets &&\n            this._rowOutlet &&\n            this._headerRowOutlet &&\n            this._footerRowOutlet &&\n            this._noDataRowOutlet) {\n            this._hasAllOutlets = true;\n            // In some setups this may fire before `ngAfterContentInit`\n            // so we need a check here. See #28538.\n            if (this._canRender()) {\n                this._render();\n            }\n        }\n    }\n    /** Whether the table has all the information to start rendering. */\n    _canRender() {\n        return this._hasAllOutlets && this._hasInitialized;\n    }\n    /** Renders the table if its state has changed. */\n    _render() {\n        // Cache the row and column definitions gathered by ContentChildren and programmatic injection.\n        this._cacheRowDefs();\n        this._cacheColumnDefs();\n        // Make sure that the user has at least added header, footer, or data row def.\n        if (!this._headerRowDefs.length &&\n            !this._footerRowDefs.length &&\n            !this._rowDefs.length &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getTableMissingRowDefsError();\n        }\n        // Render updates if the list of columns have been changed for the header, row, or footer defs.\n        const columnsChanged = this._renderUpdatedColumns();\n        const rowDefsChanged = columnsChanged || this._headerRowDefChanged || this._footerRowDefChanged;\n        // Ensure sticky column styles are reset if set to `true` elsewhere.\n        this._stickyColumnStylesNeedReset = this._stickyColumnStylesNeedReset || rowDefsChanged;\n        this._forceRecalculateCellWidths = rowDefsChanged;\n        // If the header row definition has been changed, trigger a render to the header row.\n        if (this._headerRowDefChanged) {\n            this._forceRenderHeaderRows();\n            this._headerRowDefChanged = false;\n        }\n        // If the footer row definition has been changed, trigger a render to the footer row.\n        if (this._footerRowDefChanged) {\n            this._forceRenderFooterRows();\n            this._footerRowDefChanged = false;\n        }\n        // If there is a data source and row definitions, connect to the data source unless a\n        // connection has already been made.\n        if (this.dataSource && this._rowDefs.length > 0 && !this._renderChangeSubscription) {\n            this._observeRenderChanges();\n        }\n        else if (this._stickyColumnStylesNeedReset) {\n            // In the above case, _observeRenderChanges will result in updateStickyColumnStyles being\n            // called when it row data arrives. Otherwise, we need to call it proactively.\n            this.updateStickyColumnStyles();\n        }\n        this._checkStickyStates();\n    }\n    /**\n     * Get the list of RenderRow objects to render according to the current list of data and defined\n     * row definitions. If the previous list already contained a particular pair, it should be reused\n     * so that the differ equates their references.\n     */\n    _getAllRenderRows() {\n        const renderRows = [];\n        // Store the cache and create a new one. Any re-used RenderRow objects will be moved into the\n        // new cache while unused ones can be picked up by garbage collection.\n        const prevCachedRenderRows = this._cachedRenderRowsMap;\n        this._cachedRenderRowsMap = new Map();\n        // For each data object, get the list of rows that should be rendered, represented by the\n        // respective `RenderRow` object which is the pair of `data` and `CdkRowDef`.\n        for (let i = 0; i < this._data.length; i++) {\n            let data = this._data[i];\n            const renderRowsForData = this._getRenderRowsForData(data, i, prevCachedRenderRows.get(data));\n            if (!this._cachedRenderRowsMap.has(data)) {\n                this._cachedRenderRowsMap.set(data, new WeakMap());\n            }\n            for (let j = 0; j < renderRowsForData.length; j++) {\n                let renderRow = renderRowsForData[j];\n                const cache = this._cachedRenderRowsMap.get(renderRow.data);\n                if (cache.has(renderRow.rowDef)) {\n                    cache.get(renderRow.rowDef).push(renderRow);\n                }\n                else {\n                    cache.set(renderRow.rowDef, [renderRow]);\n                }\n                renderRows.push(renderRow);\n            }\n        }\n        return renderRows;\n    }\n    /**\n     * Gets a list of `RenderRow<T>` for the provided data object and any `CdkRowDef` objects that\n     * should be rendered for this data. Reuses the cached RenderRow objects if they match the same\n     * `(T, CdkRowDef)` pair.\n     */\n    _getRenderRowsForData(data, dataIndex, cache) {\n        const rowDefs = this._getRowDefs(data, dataIndex);\n        return rowDefs.map(rowDef => {\n            const cachedRenderRows = cache && cache.has(rowDef) ? cache.get(rowDef) : [];\n            if (cachedRenderRows.length) {\n                const dataRow = cachedRenderRows.shift();\n                dataRow.dataIndex = dataIndex;\n                return dataRow;\n            }\n            else {\n                return { data, rowDef, dataIndex };\n            }\n        });\n    }\n    /** Update the map containing the content's column definitions. */\n    _cacheColumnDefs() {\n        this._columnDefsByName.clear();\n        const columnDefs = mergeArrayAndSet(this._getOwnDefs(this._contentColumnDefs), this._customColumnDefs);\n        columnDefs.forEach(columnDef => {\n            if (this._columnDefsByName.has(columnDef.name) &&\n                (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw getTableDuplicateColumnNameError(columnDef.name);\n            }\n            this._columnDefsByName.set(columnDef.name, columnDef);\n        });\n    }\n    /** Update the list of all available row definitions that can be used. */\n    _cacheRowDefs() {\n        this._headerRowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentHeaderRowDefs), this._customHeaderRowDefs);\n        this._footerRowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentFooterRowDefs), this._customFooterRowDefs);\n        this._rowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentRowDefs), this._customRowDefs);\n        // After all row definitions are determined, find the row definition to be considered default.\n        const defaultRowDefs = this._rowDefs.filter(def => !def.when);\n        if (!this.multiTemplateDataRows &&\n            defaultRowDefs.length > 1 &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getTableMultipleDefaultRowDefsError();\n        }\n        this._defaultRowDef = defaultRowDefs[0];\n    }\n    /**\n     * Check if the header, data, or footer rows have changed what columns they want to display or\n     * whether the sticky states have changed for the header or footer. If there is a diff, then\n     * re-render that section.\n     */\n    _renderUpdatedColumns() {\n        const columnsDiffReducer = (acc, def) => acc || !!def.getColumnsDiff();\n        // Force re-render data rows if the list of column definitions have changed.\n        const dataColumnsChanged = this._rowDefs.reduce(columnsDiffReducer, false);\n        if (dataColumnsChanged) {\n            this._forceRenderDataRows();\n        }\n        // Force re-render header/footer rows if the list of column definitions have changed.\n        const headerColumnsChanged = this._headerRowDefs.reduce(columnsDiffReducer, false);\n        if (headerColumnsChanged) {\n            this._forceRenderHeaderRows();\n        }\n        const footerColumnsChanged = this._footerRowDefs.reduce(columnsDiffReducer, false);\n        if (footerColumnsChanged) {\n            this._forceRenderFooterRows();\n        }\n        return dataColumnsChanged || headerColumnsChanged || footerColumnsChanged;\n    }\n    /**\n     * Switch to the provided data source by resetting the data and unsubscribing from the current\n     * render change subscription if one exists. If the data source is null, interpret this by\n     * clearing the row outlet. Otherwise start listening for new data.\n     */\n    _switchDataSource(dataSource) {\n        this._data = [];\n        if (isDataSource(this.dataSource)) {\n            this.dataSource.disconnect(this);\n        }\n        // Stop listening for data from the previous data source.\n        if (this._renderChangeSubscription) {\n            this._renderChangeSubscription.unsubscribe();\n            this._renderChangeSubscription = null;\n        }\n        if (!dataSource) {\n            if (this._dataDiffer) {\n                this._dataDiffer.diff([]);\n            }\n            if (this._rowOutlet) {\n                this._rowOutlet.viewContainer.clear();\n            }\n        }\n        this._dataSource = dataSource;\n    }\n    /** Set up a subscription for the data provided by the data source. */\n    _observeRenderChanges() {\n        // If no data source has been set, there is nothing to observe for changes.\n        if (!this.dataSource) {\n            return;\n        }\n        let dataStream;\n        if (isDataSource(this.dataSource)) {\n            dataStream = this.dataSource.connect(this);\n        }\n        else if (isObservable(this.dataSource)) {\n            dataStream = this.dataSource;\n        }\n        else if (Array.isArray(this.dataSource)) {\n            dataStream = of(this.dataSource);\n        }\n        if (dataStream === undefined && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getTableUnknownDataSourceError();\n        }\n        this._renderChangeSubscription = dataStream\n            .pipe(takeUntil(this._onDestroy))\n            .subscribe(data => {\n            this._data = data || [];\n            this.renderRows();\n        });\n    }\n    /**\n     * Clears any existing content in the header row outlet and creates a new embedded view\n     * in the outlet using the header row definition.\n     */\n    _forceRenderHeaderRows() {\n        // Clear the header row outlet if any content exists.\n        if (this._headerRowOutlet.viewContainer.length > 0) {\n            this._headerRowOutlet.viewContainer.clear();\n        }\n        this._headerRowDefs.forEach((def, i) => this._renderRow(this._headerRowOutlet, def, i));\n        this.updateStickyHeaderRowStyles();\n    }\n    /**\n     * Clears any existing content in the footer row outlet and creates a new embedded view\n     * in the outlet using the footer row definition.\n     */\n    _forceRenderFooterRows() {\n        // Clear the footer row outlet if any content exists.\n        if (this._footerRowOutlet.viewContainer.length > 0) {\n            this._footerRowOutlet.viewContainer.clear();\n        }\n        this._footerRowDefs.forEach((def, i) => this._renderRow(this._footerRowOutlet, def, i));\n        this.updateStickyFooterRowStyles();\n    }\n    /** Adds the sticky column styles for the rows according to the columns' stick states. */\n    _addStickyColumnStyles(rows, rowDef) {\n        const columnDefs = Array.from(rowDef.columns || []).map(columnName => {\n            const columnDef = this._columnDefsByName.get(columnName);\n            if (!columnDef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw getTableUnknownColumnError(columnName);\n            }\n            return columnDef;\n        });\n        const stickyStartStates = columnDefs.map(columnDef => columnDef.sticky);\n        const stickyEndStates = columnDefs.map(columnDef => columnDef.stickyEnd);\n        this._stickyStyler.updateStickyColumns(rows, stickyStartStates, stickyEndStates, !this._fixedLayout || this._forceRecalculateCellWidths);\n    }\n    /** Gets the list of rows that have been rendered in the row outlet. */\n    _getRenderedRows(rowOutlet) {\n        const renderedRows = [];\n        for (let i = 0; i < rowOutlet.viewContainer.length; i++) {\n            const viewRef = rowOutlet.viewContainer.get(i);\n            renderedRows.push(viewRef.rootNodes[0]);\n        }\n        return renderedRows;\n    }\n    /**\n     * Get the matching row definitions that should be used for this row data. If there is only\n     * one row definition, it is returned. Otherwise, find the row definitions that has a when\n     * predicate that returns true with the data. If none return true, return the default row\n     * definition.\n     */\n    _getRowDefs(data, dataIndex) {\n        if (this._rowDefs.length == 1) {\n            return [this._rowDefs[0]];\n        }\n        let rowDefs = [];\n        if (this.multiTemplateDataRows) {\n            rowDefs = this._rowDefs.filter(def => !def.when || def.when(dataIndex, data));\n        }\n        else {\n            let rowDef = this._rowDefs.find(def => def.when && def.when(dataIndex, data)) || this._defaultRowDef;\n            if (rowDef) {\n                rowDefs.push(rowDef);\n            }\n        }\n        if (!rowDefs.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getTableMissingMatchingRowDefError(data);\n        }\n        return rowDefs;\n    }\n    _getEmbeddedViewArgs(renderRow, index) {\n        const rowDef = renderRow.rowDef;\n        const context = { $implicit: renderRow.data };\n        return {\n            templateRef: rowDef.template,\n            context,\n            index,\n        };\n    }\n    /**\n     * Creates a new row template in the outlet and fills it with the set of cell templates.\n     * Optionally takes a context to provide to the row and cells, as well as an optional index\n     * of where to place the new row template in the outlet.\n     */\n    _renderRow(outlet, rowDef, index, context = {}) {\n        // TODO(andrewseguin): enforce that one outlet was instantiated from createEmbeddedView\n        const view = outlet.viewContainer.createEmbeddedView(rowDef.template, context, index);\n        this._renderCellTemplateForItem(rowDef, context);\n        return view;\n    }\n    _renderCellTemplateForItem(rowDef, context) {\n        for (let cellTemplate of this._getCellTemplates(rowDef)) {\n            if (CdkCellOutlet.mostRecentCellOutlet) {\n                CdkCellOutlet.mostRecentCellOutlet._viewContainer.createEmbeddedView(cellTemplate, context);\n            }\n        }\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Updates the index-related context for each row to reflect any changes in the index of the rows,\n     * e.g. first/last/even/odd.\n     */\n    _updateRowIndexContext() {\n        const viewContainer = this._rowOutlet.viewContainer;\n        for (let renderIndex = 0, count = viewContainer.length; renderIndex < count; renderIndex++) {\n            const viewRef = viewContainer.get(renderIndex);\n            const context = viewRef.context;\n            context.count = count;\n            context.first = renderIndex === 0;\n            context.last = renderIndex === count - 1;\n            context.even = renderIndex % 2 === 0;\n            context.odd = !context.even;\n            if (this.multiTemplateDataRows) {\n                context.dataIndex = this._renderRows[renderIndex].dataIndex;\n                context.renderIndex = renderIndex;\n            }\n            else {\n                context.index = this._renderRows[renderIndex].dataIndex;\n            }\n        }\n    }\n    /** Gets the column definitions for the provided row def. */\n    _getCellTemplates(rowDef) {\n        if (!rowDef || !rowDef.columns) {\n            return [];\n        }\n        return Array.from(rowDef.columns, columnId => {\n            const column = this._columnDefsByName.get(columnId);\n            if (!column && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw getTableUnknownColumnError(columnId);\n            }\n            return rowDef.extractCellTemplate(column);\n        });\n    }\n    /**\n     * Forces a re-render of the data rows. Should be called in cases where there has been an input\n     * change that affects the evaluation of which rows should be rendered, e.g. toggling\n     * `multiTemplateDataRows` or adding/removing row definitions.\n     */\n    _forceRenderDataRows() {\n        this._dataDiffer.diff([]);\n        this._rowOutlet.viewContainer.clear();\n        this.renderRows();\n    }\n    /**\n     * Checks if there has been a change in sticky states since last check and applies the correct\n     * sticky styles. Since checking resets the \"dirty\" state, this should only be performed once\n     * during a change detection and after the inputs are settled (after content check).\n     */\n    _checkStickyStates() {\n        const stickyCheckReducer = (acc, d) => {\n            return acc || d.hasStickyChanged();\n        };\n        // Note that the check needs to occur for every definition since it notifies the definition\n        // that it can reset its dirty state. Using another operator like `some` may short-circuit\n        // remaining definitions and leave them in an unchecked state.\n        if (this._headerRowDefs.reduce(stickyCheckReducer, false)) {\n            this.updateStickyHeaderRowStyles();\n        }\n        if (this._footerRowDefs.reduce(stickyCheckReducer, false)) {\n            this.updateStickyFooterRowStyles();\n        }\n        if (Array.from(this._columnDefsByName.values()).reduce(stickyCheckReducer, false)) {\n            this._stickyColumnStylesNeedReset = true;\n            this.updateStickyColumnStyles();\n        }\n    }\n    /**\n     * Creates the sticky styler that will be used for sticky rows and columns. Listens\n     * for directionality changes and provides the latest direction to the styler. Re-applies column\n     * stickiness when directionality changes.\n     */\n    _setupStickyStyler() {\n        const direction = this._dir ? this._dir.value : 'ltr';\n        this._stickyStyler = new StickyStyler(this._isNativeHtmlTable, this.stickyCssClass, direction, this._coalescedStyleScheduler, this._platform.isBrowser, this.needsPositionStickyOnElement, this._stickyPositioningListener);\n        (this._dir ? this._dir.change : of())\n            .pipe(takeUntil(this._onDestroy))\n            .subscribe(value => {\n            this._stickyStyler.direction = value;\n            this.updateStickyColumnStyles();\n        });\n    }\n    /** Filters definitions that belong to this table from a QueryList. */\n    _getOwnDefs(items) {\n        return items.filter(item => !item._table || item._table === this);\n    }\n    /** Creates or removes the no data row, depending on whether any data is being shown. */\n    _updateNoDataRow() {\n        const noDataRow = this._customNoDataRow || this._noDataRow;\n        if (!noDataRow) {\n            return;\n        }\n        const shouldShow = this._rowOutlet.viewContainer.length === 0;\n        if (shouldShow === this._isShowingNoDataRow) {\n            return;\n        }\n        const container = this._noDataRowOutlet.viewContainer;\n        if (shouldShow) {\n            const view = container.createEmbeddedView(noDataRow.templateRef);\n            const rootNode = view.rootNodes[0];\n            // Only add the attributes if we have a single root node since it's hard\n            // to figure out which one to add it to when there are multiple.\n            if (view.rootNodes.length === 1 && rootNode?.nodeType === this._document.ELEMENT_NODE) {\n                rootNode.setAttribute('role', 'row');\n                rootNode.classList.add(noDataRow._contentClassName);\n            }\n        }\n        else {\n            container.clear();\n        }\n        this._isShowingNoDataRow = shouldShow;\n        this._changeDetectorRef.markForCheck();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkTable, deps: [{ token: i0.IterableDiffers }, { token: i0.ChangeDetectorRef }, { token: i0.ElementRef }, { token: 'role', attribute: true }, { token: i1.Directionality, optional: true }, { token: DOCUMENT }, { token: i2.Platform }, { token: _VIEW_REPEATER_STRATEGY }, { token: _COALESCED_STYLE_SCHEDULER }, { token: i3.ViewportRuler }, { token: STICKY_POSITIONING_LISTENER, optional: true, skipSelf: true }, { token: i0.NgZone, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.2.0\", type: CdkTable, isStandalone: true, selector: \"cdk-table, table[cdk-table]\", inputs: { trackBy: \"trackBy\", dataSource: \"dataSource\", multiTemplateDataRows: [\"multiTemplateDataRows\", \"multiTemplateDataRows\", booleanAttribute], fixedLayout: [\"fixedLayout\", \"fixedLayout\", booleanAttribute] }, outputs: { contentChanged: \"contentChanged\" }, host: { properties: { \"class.cdk-table-fixed-layout\": \"fixedLayout\" }, classAttribute: \"cdk-table\" }, providers: [\n            { provide: CDK_TABLE, useExisting: CdkTable },\n            { provide: _VIEW_REPEATER_STRATEGY, useClass: _DisposeViewRepeaterStrategy },\n            { provide: _COALESCED_STYLE_SCHEDULER, useClass: _CoalescedStyleScheduler },\n            // Prevent nested tables from seeing this table's StickyPositioningListener.\n            { provide: STICKY_POSITIONING_LISTENER, useValue: null },\n        ], queries: [{ propertyName: \"_noDataRow\", first: true, predicate: CdkNoDataRow, descendants: true }, { propertyName: \"_contentColumnDefs\", predicate: CdkColumnDef, descendants: true }, { propertyName: \"_contentRowDefs\", predicate: CdkRowDef, descendants: true }, { propertyName: \"_contentHeaderRowDefs\", predicate: CdkHeaderRowDef, descendants: true }, { propertyName: \"_contentFooterRowDefs\", predicate: CdkFooterRowDef, descendants: true }], exportAs: [\"cdkTable\"], ngImport: i0, template: \"\\n  <ng-content select=\\\"caption\\\"/>\\n  <ng-content select=\\\"colgroup, col\\\"/>\\n\\n  <!--\\n    Unprojected content throws a hydration error so we need this to capture it.\\n    It gets removed on the client so it doesn't affect the layout.\\n  -->\\n  @if (_isServer) {\\n    <ng-content/>\\n  }\\n\\n  @if (_isNativeHtmlTable) {\\n    <thead role=\\\"rowgroup\\\">\\n      <ng-container headerRowOutlet/>\\n    </thead>\\n    <tbody role=\\\"rowgroup\\\">\\n      <ng-container rowOutlet/>\\n      <ng-container noDataRowOutlet/>\\n    </tbody>\\n    <tfoot role=\\\"rowgroup\\\">\\n      <ng-container footerRowOutlet/>\\n    </tfoot>\\n  } @else {\\n    <ng-container headerRowOutlet/>\\n    <ng-container rowOutlet/>\\n    <ng-container noDataRowOutlet/>\\n    <ng-container footerRowOutlet/>\\n  }\\n\", isInline: true, styles: [\".cdk-table-fixed-layout{table-layout:fixed}\"], dependencies: [{ kind: \"directive\", type: HeaderRowOutlet, selector: \"[headerRowOutlet]\" }, { kind: \"directive\", type: DataRowOutlet, selector: \"[rowOutlet]\" }, { kind: \"directive\", type: NoDataRowOutlet, selector: \"[noDataRowOutlet]\" }, { kind: \"directive\", type: FooterRowOutlet, selector: \"[footerRowOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkTable, decorators: [{\n            type: Component,\n            args: [{ selector: 'cdk-table, table[cdk-table]', exportAs: 'cdkTable', template: CDK_TABLE_TEMPLATE, host: {\n                        'class': 'cdk-table',\n                        '[class.cdk-table-fixed-layout]': 'fixedLayout',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, providers: [\n                        { provide: CDK_TABLE, useExisting: CdkTable },\n                        { provide: _VIEW_REPEATER_STRATEGY, useClass: _DisposeViewRepeaterStrategy },\n                        { provide: _COALESCED_STYLE_SCHEDULER, useClass: _CoalescedStyleScheduler },\n                        // Prevent nested tables from seeing this table's StickyPositioningListener.\n                        { provide: STICKY_POSITIONING_LISTENER, useValue: null },\n                    ], standalone: true, imports: [HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet], styles: [\".cdk-table-fixed-layout{table-layout:fixed}\"] }]\n        }], ctorParameters: () => [{ type: i0.IterableDiffers }, { type: i0.ChangeDetectorRef }, { type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['role']\n                }] }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i2.Platform }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [_VIEW_REPEATER_STRATEGY]\n                }] }, { type: _CoalescedStyleScheduler, decorators: [{\n                    type: Inject,\n                    args: [_COALESCED_STYLE_SCHEDULER]\n                }] }, { type: i3.ViewportRuler }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }, {\n                    type: Inject,\n                    args: [STICKY_POSITIONING_LISTENER]\n                }] }, { type: i0.NgZone, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { trackBy: [{\n                type: Input\n            }], dataSource: [{\n                type: Input\n            }], multiTemplateDataRows: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], fixedLayout: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], contentChanged: [{\n                type: Output\n            }], _contentColumnDefs: [{\n                type: ContentChildren,\n                args: [CdkColumnDef, { descendants: true }]\n            }], _contentRowDefs: [{\n                type: ContentChildren,\n                args: [CdkRowDef, { descendants: true }]\n            }], _contentHeaderRowDefs: [{\n                type: ContentChildren,\n                args: [CdkHeaderRowDef, {\n                        descendants: true,\n                    }]\n            }], _contentFooterRowDefs: [{\n                type: ContentChildren,\n                args: [CdkFooterRowDef, {\n                        descendants: true,\n                    }]\n            }], _noDataRow: [{\n                type: ContentChild,\n                args: [CdkNoDataRow]\n            }] } });\n/** Utility function that gets a merged list of the entries in an array and values of a Set. */\nfunction mergeArrayAndSet(array, set) {\n    return array.concat(Array.from(set));\n}\n/**\n * Finds the closest table section to an outlet. We can't use `HTMLElement.closest` for this,\n * because the node representing the outlet is a comment.\n */\nfunction closestTableSection(outlet, section) {\n    const uppercaseSection = section.toUpperCase();\n    let current = outlet.viewContainer.element.nativeElement;\n    while (current) {\n        // 1 is an element node.\n        const nodeName = current.nodeType === 1 ? current.nodeName : null;\n        if (nodeName === uppercaseSection) {\n            return current;\n        }\n        else if (nodeName === 'TABLE') {\n            // Stop traversing past the `table` node.\n            break;\n        }\n        current = current.parentNode;\n    }\n    return null;\n}\n\n/**\n * Column that simply shows text content for the header and row cells. Assumes that the table\n * is using the native table implementation (`<table>`).\n *\n * By default, the name of this column will be the header text and data property accessor.\n * The header text can be overridden with the `headerText` input. Cell values can be overridden with\n * the `dataAccessor` input. Change the text justification to the start or end using the `justify`\n * input.\n */\nclass CdkTextColumn {\n    /** Column name that should be used to reference this column. */\n    get name() {\n        return this._name;\n    }\n    set name(name) {\n        this._name = name;\n        // With Ivy, inputs can be initialized before static query results are\n        // available. In that case, we defer the synchronization until \"ngOnInit\" fires.\n        this._syncColumnDefName();\n    }\n    constructor(\n    // `CdkTextColumn` is always requiring a table, but we just assert it manually\n    // for better error reporting.\n    // tslint:disable-next-line: lightweight-tokens\n    _table, _options) {\n        this._table = _table;\n        this._options = _options;\n        /** Alignment of the cell values. */\n        this.justify = 'start';\n        this._options = _options || {};\n    }\n    ngOnInit() {\n        this._syncColumnDefName();\n        if (this.headerText === undefined) {\n            this.headerText = this._createDefaultHeaderText();\n        }\n        if (!this.dataAccessor) {\n            this.dataAccessor =\n                this._options.defaultDataAccessor || ((data, name) => data[name]);\n        }\n        if (this._table) {\n            // Provide the cell and headerCell directly to the table with the static `ViewChild` query,\n            // since the columnDef will not pick up its content by the time the table finishes checking\n            // its content and initializing the rows.\n            this.columnDef.cell = this.cell;\n            this.columnDef.headerCell = this.headerCell;\n            this._table.addColumnDef(this.columnDef);\n        }\n        else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throw getTableTextColumnMissingParentTableError();\n        }\n    }\n    ngOnDestroy() {\n        if (this._table) {\n            this._table.removeColumnDef(this.columnDef);\n        }\n    }\n    /**\n     * Creates a default header text. Use the options' header text transformation function if one\n     * has been provided. Otherwise simply capitalize the column name.\n     */\n    _createDefaultHeaderText() {\n        const name = this.name;\n        if (!name && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getTableTextColumnMissingNameError();\n        }\n        if (this._options && this._options.defaultHeaderTextTransform) {\n            return this._options.defaultHeaderTextTransform(name);\n        }\n        return name[0].toUpperCase() + name.slice(1);\n    }\n    /** Synchronizes the column definition name with the text column name. */\n    _syncColumnDefName() {\n        if (this.columnDef) {\n            this.columnDef.name = this.name;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkTextColumn, deps: [{ token: CdkTable, optional: true }, { token: TEXT_COLUMN_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkTextColumn, isStandalone: true, selector: \"cdk-text-column\", inputs: { name: \"name\", headerText: \"headerText\", dataAccessor: \"dataAccessor\", justify: \"justify\" }, viewQueries: [{ propertyName: \"columnDef\", first: true, predicate: CdkColumnDef, descendants: true, static: true }, { propertyName: \"cell\", first: true, predicate: CdkCellDef, descendants: true, static: true }, { propertyName: \"headerCell\", first: true, predicate: CdkHeaderCellDef, descendants: true, static: true }], ngImport: i0, template: `\n    <ng-container cdkColumnDef>\n      <th cdk-header-cell *cdkHeaderCellDef [style.text-align]=\"justify\">\n        {{headerText}}\n      </th>\n      <td cdk-cell *cdkCellDef=\"let data\" [style.text-align]=\"justify\">\n        {{dataAccessor(data, name)}}\n      </td>\n    </ng-container>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: CdkColumnDef, selector: \"[cdkColumnDef]\", inputs: [\"cdkColumnDef\", \"sticky\", \"stickyEnd\"] }, { kind: \"directive\", type: CdkHeaderCellDef, selector: \"[cdkHeaderCellDef]\" }, { kind: \"directive\", type: CdkHeaderCell, selector: \"cdk-header-cell, th[cdk-header-cell]\" }, { kind: \"directive\", type: CdkCellDef, selector: \"[cdkCellDef]\" }, { kind: \"directive\", type: CdkCell, selector: \"cdk-cell, td[cdk-cell]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkTextColumn, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'cdk-text-column',\n                    template: `\n    <ng-container cdkColumnDef>\n      <th cdk-header-cell *cdkHeaderCellDef [style.text-align]=\"justify\">\n        {{headerText}}\n      </th>\n      <td cdk-cell *cdkCellDef=\"let data\" [style.text-align]=\"justify\">\n        {{dataAccessor(data, name)}}\n      </td>\n    </ng-container>\n  `,\n                    encapsulation: ViewEncapsulation.None,\n                    // Change detection is intentionally not set to OnPush. This component's template will be provided\n                    // to the table to be inserted into its view. This is problematic when change detection runs since\n                    // the bindings in this template will be evaluated _after_ the table's view is evaluated, which\n                    // mean's the template in the table's view will not have the updated value (and in fact will cause\n                    // an ExpressionChangedAfterItHasBeenCheckedError).\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    standalone: true,\n                    imports: [CdkColumnDef, CdkHeaderCellDef, CdkHeaderCell, CdkCellDef, CdkCell],\n                }]\n        }], ctorParameters: () => [{ type: CdkTable, decorators: [{\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [TEXT_COLUMN_OPTIONS]\n                }] }], propDecorators: { name: [{\n                type: Input\n            }], headerText: [{\n                type: Input\n            }], dataAccessor: [{\n                type: Input\n            }], justify: [{\n                type: Input\n            }], columnDef: [{\n                type: ViewChild,\n                args: [CdkColumnDef, { static: true }]\n            }], cell: [{\n                type: ViewChild,\n                args: [CdkCellDef, { static: true }]\n            }], headerCell: [{\n                type: ViewChild,\n                args: [CdkHeaderCellDef, { static: true }]\n            }] } });\n\nconst EXPORTED_DECLARATIONS = [\n    CdkTable,\n    CdkRowDef,\n    CdkCellDef,\n    CdkCellOutlet,\n    CdkHeaderCellDef,\n    CdkFooterCellDef,\n    CdkColumnDef,\n    CdkCell,\n    CdkRow,\n    CdkHeaderCell,\n    CdkFooterCell,\n    CdkHeaderRow,\n    CdkHeaderRowDef,\n    CdkFooterRow,\n    CdkFooterRowDef,\n    DataRowOutlet,\n    HeaderRowOutlet,\n    FooterRowOutlet,\n    CdkTextColumn,\n    CdkNoDataRow,\n    CdkRecycleRows,\n    NoDataRowOutlet,\n];\nclass CdkTableModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkTableModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkTableModule, imports: [ScrollingModule, CdkTable,\n            CdkRowDef,\n            CdkCellDef,\n            CdkCellOutlet,\n            CdkHeaderCellDef,\n            CdkFooterCellDef,\n            CdkColumnDef,\n            CdkCell,\n            CdkRow,\n            CdkHeaderCell,\n            CdkFooterCell,\n            CdkHeaderRow,\n            CdkHeaderRowDef,\n            CdkFooterRow,\n            CdkFooterRowDef,\n            DataRowOutlet,\n            HeaderRowOutlet,\n            FooterRowOutlet,\n            CdkTextColumn,\n            CdkNoDataRow,\n            CdkRecycleRows,\n            NoDataRowOutlet], exports: [CdkTable,\n            CdkRowDef,\n            CdkCellDef,\n            CdkCellOutlet,\n            CdkHeaderCellDef,\n            CdkFooterCellDef,\n            CdkColumnDef,\n            CdkCell,\n            CdkRow,\n            CdkHeaderCell,\n            CdkFooterCell,\n            CdkHeaderRow,\n            CdkHeaderRowDef,\n            CdkFooterRow,\n            CdkFooterRowDef,\n            DataRowOutlet,\n            HeaderRowOutlet,\n            FooterRowOutlet,\n            CdkTextColumn,\n            CdkNoDataRow,\n            CdkRecycleRows,\n            NoDataRowOutlet] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkTableModule, imports: [ScrollingModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkTableModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: EXPORTED_DECLARATIONS,\n                    imports: [ScrollingModule, ...EXPORTED_DECLARATIONS],\n                }]\n        }] });\n\n/**\n * Mixin to provide a directive with a function that checks if the sticky input has been\n * changed since the last time the function was called. Essentially adds a dirty-check to the\n * sticky value.\n * @docs-private\n * @deprecated Implement the `CanStick` interface instead.\n * @breaking-change 19.0.0\n */\nfunction mixinHasStickyInput(base) {\n    return class extends base {\n        /** Whether sticky positioning should be applied. */\n        get sticky() {\n            return this._sticky;\n        }\n        set sticky(v) {\n            const prevValue = this._sticky;\n            this._sticky = coerceBooleanProperty(v);\n            this._hasStickyChanged = prevValue !== this._sticky;\n        }\n        /** Whether the sticky value has changed since this was last called. */\n        hasStickyChanged() {\n            const hasStickyChanged = this._hasStickyChanged;\n            this._hasStickyChanged = false;\n            return hasStickyChanged;\n        }\n        /** Resets the dirty check for cases where the sticky state has been used without checking. */\n        resetStickyChanged() {\n            this._hasStickyChanged = false;\n        }\n        constructor(...args) {\n            super(...args);\n            this._sticky = false;\n            /** Whether the sticky input has changed since it was last checked. */\n            this._hasStickyChanged = false;\n        }\n    };\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BaseCdkCell, BaseRowDef, CDK_ROW_TEMPLATE, CDK_TABLE, CDK_TABLE_TEMPLATE, CdkCell, CdkCellDef, CdkCellOutlet, CdkColumnDef, CdkFooterCell, CdkFooterCellDef, CdkFooterRow, CdkFooterRowDef, CdkHeaderCell, CdkHeaderCellDef, CdkHeaderRow, CdkHeaderRowDef, CdkNoDataRow, CdkRecycleRows, CdkRow, CdkRowDef, CdkTable, CdkTableModule, CdkTextColumn, DataRowOutlet, FooterRowOutlet, HeaderRowOutlet, NoDataRowOutlet, STICKY_DIRECTIONS, STICKY_POSITIONING_LISTENER, StickyStyler, TEXT_COLUMN_OPTIONS, _COALESCED_STYLE_SCHEDULER, _CoalescedStyleScheduler, _Schedule, mixinHasStickyInput };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,mBAAmB;AACvC,SAASC,uBAAuB,EAAEC,4BAA4B,EAAEC,YAAY,EAAEC,sBAAsB,EAAEC,4BAA4B,QAAQ,0BAA0B;AACpK,SAASC,UAAU,QAAQ,0BAA0B;AACrD,OAAO,KAAKC,EAAE,MAAM,uBAAuB;AAC3C,OAAO,KAAKC,EAAE,MAAM,wBAAwB;AAC5C,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,YAAY,EAAEC,UAAU,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,eAAe,EAAEC,YAAY,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,eAAe,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACvS,SAASC,OAAO,EAAEC,IAAI,EAAEC,eAAe,EAAEC,YAAY,EAAEC,EAAE,QAAQ,MAAM;AACvE,SAASC,SAAS,EAAEC,IAAI,QAAQ,gBAAgB;AAChD,SAASC,qBAAqB,QAAQ,uBAAuB;;AAE7D;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,gCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAgBoGjC,EAAE,CAAAmC,YAAA,KAkgE2qB,CAAC;EAAA;AAAA;AAAA,SAAAC,gCAAAH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlgE9qBjC,EAAE,CAAAqC,cAAA,cAkgE+uB,CAAC;IAlgElvBrC,EAAE,CAAAsC,kBAAA,KAkgEsxB,CAAC;IAlgEzxBtC,EAAE,CAAAuC,YAAA,CAkgEoyB,CAAC;IAlgEvyBvC,EAAE,CAAAqC,cAAA,cAkgEm0B,CAAC;IAlgEt0BrC,EAAE,CAAAsC,kBAAA,KAkgEo2B,CAAC,KAAsC,CAAC;IAlgE94BtC,EAAE,CAAAuC,YAAA,CAkgEy5B,CAAC;IAlgE55BvC,EAAE,CAAAqC,cAAA,cAkgEw7B,CAAC;IAlgE37BrC,EAAE,CAAAsC,kBAAA,KAkgE+9B,CAAC;IAlgEl+BtC,EAAE,CAAAuC,YAAA,CAkgE6+B,CAAC;EAAA;AAAA;AAAA,SAAAC,gCAAAP,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlgEh/BjC,EAAE,CAAAsC,kBAAA,KAkgE+hC,CAAC,KAA8B,CAAC,KAAoC,CAAC,KAAoC,CAAC;EAAA;AAAA;AAAA,SAAAG,4BAAAR,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlgE3oCjC,EAAE,CAAAqC,cAAA,WAirE9B,CAAC;IAjrE2BrC,EAAE,CAAA0C,MAAA,EAmrEjG,CAAC;IAnrE8F1C,EAAE,CAAAuC,YAAA,CAmrE5F,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAU,MAAA,GAnrEyF3C,EAAE,CAAA4C,aAAA;IAAF5C,EAAE,CAAA6C,WAAA,eAAAF,MAAA,CAAAG,OAirE/B,CAAC;IAjrE4B9C,EAAE,CAAA+C,SAAA,CAmrEjG,CAAC;IAnrE8F/C,EAAE,CAAAgD,kBAAA,MAAAL,MAAA,CAAAM,UAAA,KAmrEjG,CAAC;EAAA;AAAA;AAAA,SAAAC,4BAAAjB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnrE8FjC,EAAE,CAAAqC,cAAA,WAorEhC,CAAC;IAprE6BrC,EAAE,CAAA0C,MAAA,EAsrEjG,CAAC;IAtrE8F1C,EAAE,CAAAuC,YAAA,CAsrE5F,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAkB,OAAA,GAAAjB,GAAA,CAAAkB,SAAA;IAAA,MAAAT,MAAA,GAtrEyF3C,EAAE,CAAA4C,aAAA;IAAF5C,EAAE,CAAA6C,WAAA,eAAAF,MAAA,CAAAG,OAorEjC,CAAC;IAprE8B9C,EAAE,CAAA+C,SAAA,CAsrEjG,CAAC;IAtrE8F/C,EAAE,CAAAgD,kBAAA,MAAAL,MAAA,CAAAU,YAAA,CAAAF,OAAA,EAAAR,MAAA,CAAAW,IAAA,MAsrEjG,CAAC;EAAA;AAAA;AAlsEN,MAAMC,SAAS,GAAG,IAAItD,cAAc,CAAC,WAAW,CAAC;AACjD;AACA,MAAMuD,mBAAmB,GAAG,IAAIvD,cAAc,CAAC,qBAAqB,CAAC;;AAErE;AACA;AACA;AACA;AACA,MAAMwD,UAAU,CAAC;EACbC,WAAWA,CAAC,oBAAqBC,QAAQ,EAAE;IACvC,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,mBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFL,UAAU,EAApBzD,EAAE,CAAA+D,iBAAA,CAAoC/D,EAAE,CAACgE,WAAW;IAAA,CAA4C;EAAE;EAClM;IAAS,IAAI,CAACC,IAAI,kBAD8EjE,EAAE,CAAAkE,iBAAA;MAAAC,IAAA,EACJV,UAAU;MAAAW,SAAA;MAAAC,UAAA;IAAA,EAA+D;EAAE;AAC7K;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGtE,EAAE,CAAAuE,iBAAA,CAGXd,UAAU,EAAc,CAAC;IACxGU,IAAI,EAAEjE,SAAS;IACfsE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,cAAc;MACxBJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAEnE,EAAE,CAACgE;EAAY,CAAC,CAAC;AAAA;AAC5D;AACA;AACA;AACA;AACA,MAAMU,gBAAgB,CAAC;EACnBhB,WAAWA,CAAC,oBAAqBC,QAAQ,EAAE;IACvC,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAACC,IAAI,YAAAe,yBAAAb,CAAA;MAAA,YAAAA,CAAA,IAAwFY,gBAAgB,EAlB1B1E,EAAE,CAAA+D,iBAAA,CAkB0C/D,EAAE,CAACgE,WAAW;IAAA,CAA4C;EAAE;EACxM;IAAS,IAAI,CAACC,IAAI,kBAnB8EjE,EAAE,CAAAkE,iBAAA;MAAAC,IAAA,EAmBJO,gBAAgB;MAAAN,SAAA;MAAAC,UAAA;IAAA,EAAqE;EAAE;AACzL;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KArBoGtE,EAAE,CAAAuE,iBAAA,CAqBXG,gBAAgB,EAAc,CAAC;IAC9GP,IAAI,EAAEjE,SAAS;IACfsE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBAAoB;MAC9BJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAEnE,EAAE,CAACgE;EAAY,CAAC,CAAC;AAAA;AAC5D;AACA;AACA;AACA;AACA,MAAMY,gBAAgB,CAAC;EACnBlB,WAAWA,CAAC,oBAAqBC,QAAQ,EAAE;IACvC,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAACC,IAAI,YAAAiB,yBAAAf,CAAA;MAAA,YAAAA,CAAA,IAAwFc,gBAAgB,EApC1B5E,EAAE,CAAA+D,iBAAA,CAoC0C/D,EAAE,CAACgE,WAAW;IAAA,CAA4C;EAAE;EACxM;IAAS,IAAI,CAACC,IAAI,kBArC8EjE,EAAE,CAAAkE,iBAAA;MAAAC,IAAA,EAqCJS,gBAAgB;MAAAR,SAAA;MAAAC,UAAA;IAAA,EAAqE;EAAE;AACzL;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAvCoGtE,EAAE,CAAAuE,iBAAA,CAuCXK,gBAAgB,EAAc,CAAC;IAC9GT,IAAI,EAAEjE,SAAS;IACfsE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBAAoB;MAC9BJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAEnE,EAAE,CAACgE;EAAY,CAAC,CAAC;AAAA;AAC5D;AACA;AACA;AACA;AACA,MAAMc,YAAY,CAAC;EACf;EACA,IAAIxB,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACyB,KAAK;EACrB;EACA,IAAIzB,IAAIA,CAACA,IAAI,EAAE;IACX,IAAI,CAAC0B,aAAa,CAAC1B,IAAI,CAAC;EAC5B;EACA;EACA,IAAI2B,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,OAAO;EACvB;EACA,IAAID,MAAMA,CAACE,KAAK,EAAE;IACd,IAAIA,KAAK,KAAK,IAAI,CAACD,OAAO,EAAE;MACxB,IAAI,CAACA,OAAO,GAAGC,KAAK;MACpB,IAAI,CAACC,iBAAiB,GAAG,IAAI;IACjC;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,UAAU;EAC1B;EACA,IAAID,SAASA,CAACF,KAAK,EAAE;IACjB,IAAIA,KAAK,KAAK,IAAI,CAACG,UAAU,EAAE;MAC3B,IAAI,CAACA,UAAU,GAAGH,KAAK;MACvB,IAAI,CAACC,iBAAiB,GAAG,IAAI;IACjC;EACJ;EACA1B,WAAWA,CAAC6B,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACH,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACF,OAAO,GAAG,KAAK;IACpB,IAAI,CAACI,UAAU,GAAG,KAAK;EAC3B;EACA;EACAE,gBAAgBA,CAAA,EAAG;IACf,MAAMA,gBAAgB,GAAG,IAAI,CAACJ,iBAAiB;IAC/C,IAAI,CAACK,kBAAkB,CAAC,CAAC;IACzB,OAAOD,gBAAgB;EAC3B;EACA;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACL,iBAAiB,GAAG,KAAK;EAClC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIM,yBAAyBA,CAAA,EAAG;IACxB,IAAI,CAACC,mBAAmB,GAAG,CAAC,cAAc,IAAI,CAACC,oBAAoB,EAAE,CAAC;EAC1E;EACA;AACJ;AACA;AACA;AACA;AACA;EACIZ,aAAaA,CAACG,KAAK,EAAE;IACjB;IACA;IACA,IAAIA,KAAK,EAAE;MACP,IAAI,CAACJ,KAAK,GAAGI,KAAK;MAClB,IAAI,CAACS,oBAAoB,GAAGT,KAAK,CAACU,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC;MAC/D,IAAI,CAACH,yBAAyB,CAAC,CAAC;IACpC;EACJ;EACA;IAAS,IAAI,CAAC9B,IAAI,YAAAkC,qBAAAhC,CAAA;MAAA,YAAAA,CAAA,IAAwFgB,YAAY,EA3HtB9E,EAAE,CAAA+D,iBAAA,CA2HsCR,SAAS;IAAA,CAA4D;EAAE;EAC/M;IAAS,IAAI,CAACU,IAAI,kBA5H8EjE,EAAE,CAAAkE,iBAAA;MAAAC,IAAA,EA4HJW,YAAY;MAAAV,SAAA;MAAA2B,cAAA,WAAAC,4BAAA/D,EAAA,EAAAC,GAAA,EAAA+D,QAAA;QAAA,IAAAhE,EAAA;UA5HVjC,EAAE,CAAAkG,cAAA,CAAAD,QAAA,EA4H4VxC,UAAU;UA5HxWzD,EAAE,CAAAkG,cAAA,CAAAD,QAAA,EA4HmbvB,gBAAgB;UA5Hrc1E,EAAE,CAAAkG,cAAA,CAAAD,QAAA,EA4HghBrB,gBAAgB;QAAA;QAAA,IAAA3C,EAAA;UAAA,IAAAkE,EAAA;UA5HliBnG,EAAE,CAAAoG,cAAA,CAAAD,EAAA,GAAFnG,EAAE,CAAAqG,WAAA,QAAAnE,GAAA,CAAAoE,IAAA,GAAAH,EAAA,CAAAI,KAAA;UAAFvG,EAAE,CAAAoG,cAAA,CAAAD,EAAA,GAAFnG,EAAE,CAAAqG,WAAA,QAAAnE,GAAA,CAAAsE,UAAA,GAAAL,EAAA,CAAAI,KAAA;UAAFvG,EAAE,CAAAoG,cAAA,CAAAD,EAAA,GAAFnG,EAAE,CAAAqG,WAAA,QAAAnE,GAAA,CAAAuE,UAAA,GAAAN,EAAA,CAAAI,KAAA;QAAA;MAAA;MAAAG,MAAA;QAAApD,IAAA,GAAFtD,EAAE,CAAA2G,YAAA,CAAAC,IAAA;QAAA3B,MAAA,GAAFjF,EAAE,CAAA2G,YAAA,CAAAE,0BAAA,sBA4HiI1G,gBAAgB;QAAAkF,SAAA,GA5HnJrF,EAAE,CAAA2G,YAAA,CAAAE,0BAAA,4BA4H0L1G,gBAAgB;MAAA;MAAAkE,UAAA;MAAAyC,QAAA,GA5H5M9G,EAAE,CAAA+G,kBAAA,CA4H0N,CAAC;QAAEC,OAAO,EAAE,4BAA4B;QAAEC,WAAW,EAAEnC;MAAa,CAAC,CAAC,GA5HlS9E,EAAE,CAAAkH,wBAAA;IAAA,EA4HukB;EAAE;AAC/qB;AACA;EAAA,QAAA5C,SAAA,oBAAAA,SAAA,KA9HoGtE,EAAE,CAAAuE,iBAAA,CA8HXO,YAAY,EAAc,CAAC;IAC1GX,IAAI,EAAEjE,SAAS;IACfsE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gBAAgB;MAC1B0C,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAE,4BAA4B;QAAEC,WAAW,EAAEnC;MAAa,CAAC,CAAC;MACjFT,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAEiD,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/ClD,IAAI,EAAE/D,MAAM;MACZoE,IAAI,EAAE,CAACjB,SAAS;IACpB,CAAC,EAAE;MACCY,IAAI,EAAE9D;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEiD,IAAI,EAAE,CAAC;MAChCa,IAAI,EAAE7D,KAAK;MACXkE,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC,CAAC;IAAES,MAAM,EAAE,CAAC;MACTd,IAAI,EAAE7D,KAAK;MACXkE,IAAI,EAAE,CAAC;QAAE8C,SAAS,EAAEnH;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkF,SAAS,EAAE,CAAC;MACZlB,IAAI,EAAE7D,KAAK;MACXkE,IAAI,EAAE,CAAC;QAAE8C,SAAS,EAAEnH;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEmG,IAAI,EAAE,CAAC;MACPnC,IAAI,EAAE5D,YAAY;MAClBiE,IAAI,EAAE,CAACf,UAAU;IACrB,CAAC,CAAC;IAAE+C,UAAU,EAAE,CAAC;MACbrC,IAAI,EAAE5D,YAAY;MAClBiE,IAAI,EAAE,CAACE,gBAAgB;IAC3B,CAAC,CAAC;IAAE+B,UAAU,EAAE,CAAC;MACbtC,IAAI,EAAE5D,YAAY;MAClBiE,IAAI,EAAE,CAACI,gBAAgB;IAC3B,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,MAAM2C,WAAW,CAAC;EACd7D,WAAWA,CAAC8D,SAAS,EAAEC,UAAU,EAAE;IAC/BA,UAAU,CAACC,aAAa,CAACC,SAAS,CAACC,GAAG,CAAC,GAAGJ,SAAS,CAAC7B,mBAAmB,CAAC;EAC5E;AACJ;AACA;AACA,MAAMkC,aAAa,SAASN,WAAW,CAAC;EACpC7D,WAAWA,CAAC8D,SAAS,EAAEC,UAAU,EAAE;IAC/B,KAAK,CAACD,SAAS,EAAEC,UAAU,CAAC;EAChC;EACA;IAAS,IAAI,CAAC7D,IAAI,YAAAkE,sBAAAhE,CAAA;MAAA,YAAAA,CAAA,IAAwF+D,aAAa,EAxKvB7H,EAAE,CAAA+D,iBAAA,CAwKuCe,YAAY,GAxKrD9E,EAAE,CAAA+D,iBAAA,CAwKgE/D,EAAE,CAAC+H,UAAU;IAAA,CAA4C;EAAE;EAC7N;IAAS,IAAI,CAAC9D,IAAI,kBAzK8EjE,EAAE,CAAAkE,iBAAA;MAAAC,IAAA,EAyKJ0D,aAAa;MAAAzD,SAAA;MAAA4D,SAAA,WAAsG,cAAc;MAAA3D,UAAA;MAAAyC,QAAA,GAzK/H9G,EAAE,CAAAiI,0BAAA;IAAA,EAyK4M;EAAE;AACpT;AACA;EAAA,QAAA3D,SAAA,oBAAAA,SAAA,KA3KoGtE,EAAE,CAAAuE,iBAAA,CA2KXsD,aAAa,EAAc,CAAC;IAC3G1D,IAAI,EAAEjE,SAAS;IACfsE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sCAAsC;MAChDyD,IAAI,EAAE;QACF,OAAO,EAAE,iBAAiB;QAC1B,MAAM,EAAE;MACZ,CAAC;MACD7D,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAEW;EAAa,CAAC,EAAE;IAAEX,IAAI,EAAEnE,EAAE,CAAC+H;EAAW,CAAC,CAAC;AAAA;AACnF;AACA,MAAMI,aAAa,SAASZ,WAAW,CAAC;EACpC7D,WAAWA,CAAC8D,SAAS,EAAEC,UAAU,EAAE;IAC/B,KAAK,CAACD,SAAS,EAAEC,UAAU,CAAC;IAC5B,MAAMW,IAAI,GAAGZ,SAAS,CAACjC,MAAM,EAAE8C,YAAY,CAAC,CAAC;IAC7C,IAAID,IAAI,EAAE;MACNX,UAAU,CAACC,aAAa,CAACY,YAAY,CAAC,MAAM,EAAEF,IAAI,CAAC;IACvD;EACJ;EACA;IAAS,IAAI,CAACxE,IAAI,YAAA2E,sBAAAzE,CAAA;MAAA,YAAAA,CAAA,IAAwFqE,aAAa,EA/LvBnI,EAAE,CAAA+D,iBAAA,CA+LuCe,YAAY,GA/LrD9E,EAAE,CAAA+D,iBAAA,CA+LgE/D,EAAE,CAAC+H,UAAU;IAAA,CAA4C;EAAE;EAC7N;IAAS,IAAI,CAAC9D,IAAI,kBAhM8EjE,EAAE,CAAAkE,iBAAA;MAAAC,IAAA,EAgMJgE,aAAa;MAAA/D,SAAA;MAAA4D,SAAA;MAAA3D,UAAA;MAAAyC,QAAA,GAhMX9G,EAAE,CAAAiI,0BAAA;IAAA,EAgMoK;EAAE;AAC5Q;AACA;EAAA,QAAA3D,SAAA,oBAAAA,SAAA,KAlMoGtE,EAAE,CAAAuE,iBAAA,CAkMX4D,aAAa,EAAc,CAAC;IAC3GhE,IAAI,EAAEjE,SAAS;IACfsE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sCAAsC;MAChDyD,IAAI,EAAE;QACF,OAAO,EAAE;MACb,CAAC;MACD7D,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAEW;EAAa,CAAC,EAAE;IAAEX,IAAI,EAAEnE,EAAE,CAAC+H;EAAW,CAAC,CAAC;AAAA;AACnF;AACA,MAAMS,OAAO,SAASjB,WAAW,CAAC;EAC9B7D,WAAWA,CAAC8D,SAAS,EAAEC,UAAU,EAAE;IAC/B,KAAK,CAACD,SAAS,EAAEC,UAAU,CAAC;IAC5B,MAAMW,IAAI,GAAGZ,SAAS,CAACjC,MAAM,EAAE8C,YAAY,CAAC,CAAC;IAC7C,IAAID,IAAI,EAAE;MACNX,UAAU,CAACC,aAAa,CAACY,YAAY,CAAC,MAAM,EAAEF,IAAI,CAAC;IACvD;EACJ;EACA;IAAS,IAAI,CAACxE,IAAI,YAAA6E,gBAAA3E,CAAA;MAAA,YAAAA,CAAA,IAAwF0E,OAAO,EArNjBxI,EAAE,CAAA+D,iBAAA,CAqNiCe,YAAY,GArN/C9E,EAAE,CAAA+D,iBAAA,CAqN0D/D,EAAE,CAAC+H,UAAU;IAAA,CAA4C;EAAE;EACvN;IAAS,IAAI,CAAC9D,IAAI,kBAtN8EjE,EAAE,CAAAkE,iBAAA;MAAAC,IAAA,EAsNJqE,OAAO;MAAApE,SAAA;MAAA4D,SAAA;MAAA3D,UAAA;MAAAyC,QAAA,GAtNL9G,EAAE,CAAAiI,0BAAA;IAAA,EAsNyI;EAAE;AACjP;AACA;EAAA,QAAA3D,SAAA,oBAAAA,SAAA,KAxNoGtE,EAAE,CAAAuE,iBAAA,CAwNXiE,OAAO,EAAc,CAAC;IACrGrE,IAAI,EAAEjE,SAAS;IACfsE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,wBAAwB;MAClCyD,IAAI,EAAE;QACF,OAAO,EAAE;MACb,CAAC;MACD7D,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAEW;EAAa,CAAC,EAAE;IAAEX,IAAI,EAAEnE,EAAE,CAAC+H;EAAW,CAAC,CAAC;AAAA;;AAEnF;AACA;AACA;AACA,MAAMW,SAAS,CAAC;EACZhF,WAAWA,CAAA,EAAG;IACV,IAAI,CAACiF,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,QAAQ,GAAG,EAAE;EACtB;AACJ;AACA;AACA,MAAMC,0BAA0B,GAAG,IAAI5I,cAAc,CAAC,4BAA4B,CAAC;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6I,wBAAwB,CAAC;EAC3BpF,WAAWA,CAACqF,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,UAAU,GAAG,IAAI3H,OAAO,CAAC,CAAC;EACnC;EACA;AACJ;AACA;EACI4H,QAAQA,CAACC,IAAI,EAAE;IACX,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACJ,gBAAgB,CAACL,KAAK,CAACU,IAAI,CAACF,IAAI,CAAC;EAC1C;EACA;AACJ;AACA;AACA;EACIG,WAAWA,CAACH,IAAI,EAAE;IACd,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACJ,gBAAgB,CAACJ,QAAQ,CAACS,IAAI,CAACF,IAAI,CAAC;EAC7C;EACA;EACAI,WAAWA,CAAA,EAAG;IACV,IAAI,CAACN,UAAU,CAACO,IAAI,CAAC,CAAC;IACtB,IAAI,CAACP,UAAU,CAACQ,QAAQ,CAAC,CAAC;EAC9B;EACAL,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACJ,gBAAgB,EAAE;MACvB;IACJ;IACA,IAAI,CAACA,gBAAgB,GAAG,IAAIN,SAAS,CAAC,CAAC;IACvC,IAAI,CAACgB,sBAAsB,CAAC,CAAC,CACxBC,IAAI,CAAChI,SAAS,CAAC,IAAI,CAACsH,UAAU,CAAC,CAAC,CAChCW,SAAS,CAAC,MAAM;MACjB,OAAO,IAAI,CAACZ,gBAAgB,CAACL,KAAK,CAACkB,MAAM,IAAI,IAAI,CAACb,gBAAgB,CAACJ,QAAQ,CAACiB,MAAM,EAAE;QAChF,MAAMX,QAAQ,GAAG,IAAI,CAACF,gBAAgB;QACtC;QACA,IAAI,CAACA,gBAAgB,GAAG,IAAIN,SAAS,CAAC,CAAC;QACvC,KAAK,MAAMS,IAAI,IAAID,QAAQ,CAACP,KAAK,EAAE;UAC/BQ,IAAI,CAAC,CAAC;QACV;QACA,KAAK,MAAMA,IAAI,IAAID,QAAQ,CAACN,QAAQ,EAAE;UAClCO,IAAI,CAAC,CAAC;QACV;MACJ;MACA,IAAI,CAACH,gBAAgB,GAAG,IAAI;IAChC,CAAC,CAAC;EACN;EACAU,sBAAsBA,CAAA,EAAG;IACrB;IACA;IACA,OAAO,IAAI,CAACX,OAAO,CAACe,QAAQ,GACtBvI,IAAI,CAACwI,OAAO,CAACC,OAAO,CAAC5C,SAAS,CAAC,CAAC,GAChC,IAAI,CAAC2B,OAAO,CAACkB,QAAQ,CAACN,IAAI,CAAC/H,IAAI,CAAC,CAAC,CAAC,CAAC;EAC7C;EACA;IAAS,IAAI,CAACgC,IAAI,YAAAsG,iCAAApG,CAAA;MAAA,YAAAA,CAAA,IAAwFgF,wBAAwB,EA5SlC9I,EAAE,CAAAmK,QAAA,CA4SkDnK,EAAE,CAACe,MAAM;IAAA,CAA6C;EAAE;EAC5M;IAAS,IAAI,CAACqJ,KAAK,kBA7S6EpK,EAAE,CAAAqK,kBAAA;MAAAC,KAAA,EA6SYxB,wBAAwB;MAAAyB,OAAA,EAAxBzB,wBAAwB,CAAAlF;IAAA,EAAG;EAAE;AAC/I;AACA;EAAA,QAAAU,SAAA,oBAAAA,SAAA,KA/SoGtE,EAAE,CAAAuE,iBAAA,CA+SXuE,wBAAwB,EAAc,CAAC;IACtH3E,IAAI,EAAE3D;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE2D,IAAI,EAAEnE,EAAE,CAACe;EAAO,CAAC,CAAC;AAAA;;AAEvD;AACA;AACA;AACA;AACA,MAAMyJ,gBAAgB,GAAG,6CAA6C;AACtE;AACA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACb/G,WAAWA,CACX,oBAAqBC,QAAQ,EAAE+G,QAAQ,EAAE;IACrC,IAAI,CAAC/G,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC+G,QAAQ,GAAGA,QAAQ;EAC5B;EACAC,WAAWA,CAACC,OAAO,EAAE;IACjB;IACA;IACA,IAAI,CAAC,IAAI,CAACC,cAAc,EAAE;MACtB,MAAMC,OAAO,GAAIF,OAAO,CAAC,SAAS,CAAC,IAAIA,OAAO,CAAC,SAAS,CAAC,CAACG,YAAY,IAAK,EAAE;MAC7E,IAAI,CAACF,cAAc,GAAG,IAAI,CAACH,QAAQ,CAACM,IAAI,CAACF,OAAO,CAAC,CAACG,MAAM,CAAC,CAAC;MAC1D,IAAI,CAACJ,cAAc,CAACK,IAAI,CAACJ,OAAO,CAAC;IACrC;EACJ;EACA;AACJ;AACA;AACA;EACIK,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACN,cAAc,CAACK,IAAI,CAAC,IAAI,CAACJ,OAAO,CAAC;EACjD;EACA;EACAM,mBAAmBA,CAACC,MAAM,EAAE;IACxB,IAAI,IAAI,YAAYC,eAAe,EAAE;MACjC,OAAOD,MAAM,CAAC7E,UAAU,CAAC7C,QAAQ;IACrC;IACA,IAAI,IAAI,YAAY4H,eAAe,EAAE;MACjC,OAAOF,MAAM,CAAC5E,UAAU,CAAC9C,QAAQ;IACrC,CAAC,MACI;MACD,OAAO0H,MAAM,CAAC/E,IAAI,CAAC3C,QAAQ;IAC/B;EACJ;EACA;IAAS,IAAI,CAACC,IAAI,YAAA4H,mBAAA1H,CAAA;MAAA,YAAAA,CAAA,IAAwF2G,UAAU,EA9VpBzK,EAAE,CAAA+D,iBAAA,CA8VoC/D,EAAE,CAACgE,WAAW,GA9VpDhE,EAAE,CAAA+D,iBAAA,CA8V+D/D,EAAE,CAACyL,eAAe;IAAA,CAA4C;EAAE;EACjO;IAAS,IAAI,CAACxH,IAAI,kBA/V8EjE,EAAE,CAAAkE,iBAAA;MAAAC,IAAA,EA+VJsG,UAAU;MAAA3D,QAAA,GA/VR9G,EAAE,CAAA0L,oBAAA;IAAA,EA+V4C;EAAE;AACpJ;AACA;EAAA,QAAApH,SAAA,oBAAAA,SAAA,KAjWoGtE,EAAE,CAAAuE,iBAAA,CAiWXkG,UAAU,EAAc,CAAC;IACxGtG,IAAI,EAAEjE;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEiE,IAAI,EAAEnE,EAAE,CAACgE;EAAY,CAAC,EAAE;IAAEG,IAAI,EAAEnE,EAAE,CAACyL;EAAgB,CAAC,CAAC;AAAA;AAC1F;AACA;AACA;AACA;AACA,MAAMH,eAAe,SAASb,UAAU,CAAC;EACrC;EACA,IAAIxF,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,OAAO;EACvB;EACA,IAAID,MAAMA,CAACE,KAAK,EAAE;IACd,IAAIA,KAAK,KAAK,IAAI,CAACD,OAAO,EAAE;MACxB,IAAI,CAACA,OAAO,GAAGC,KAAK;MACpB,IAAI,CAACC,iBAAiB,GAAG,IAAI;IACjC;EACJ;EACA1B,WAAWA,CAACC,QAAQ,EAAE+G,QAAQ,EAAEnF,MAAM,EAAE;IACpC,KAAK,CAAC5B,QAAQ,EAAE+G,QAAQ,CAAC;IACzB,IAAI,CAACnF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACH,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACF,OAAO,GAAG,KAAK;EACxB;EACA;EACA;EACAyF,WAAWA,CAACC,OAAO,EAAE;IACjB,KAAK,CAACD,WAAW,CAACC,OAAO,CAAC;EAC9B;EACA;EACApF,gBAAgBA,CAAA,EAAG;IACf,MAAMA,gBAAgB,GAAG,IAAI,CAACJ,iBAAiB;IAC/C,IAAI,CAACK,kBAAkB,CAAC,CAAC;IACzB,OAAOD,gBAAgB;EAC3B;EACA;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACL,iBAAiB,GAAG,KAAK;EAClC;EACA;IAAS,IAAI,CAACxB,IAAI,YAAA+H,wBAAA7H,CAAA;MAAA,YAAAA,CAAA,IAAwFwH,eAAe,EAxYzBtL,EAAE,CAAA+D,iBAAA,CAwYyC/D,EAAE,CAACgE,WAAW,GAxYzDhE,EAAE,CAAA+D,iBAAA,CAwYoE/D,EAAE,CAACyL,eAAe,GAxYxFzL,EAAE,CAAA+D,iBAAA,CAwYmGR,SAAS;IAAA,CAA4D;EAAE;EAC5Q;IAAS,IAAI,CAACU,IAAI,kBAzY8EjE,EAAE,CAAAkE,iBAAA;MAAAC,IAAA,EAyYJmH,eAAe;MAAAlH,SAAA;MAAAsC,MAAA;QAAAoE,OAAA,GAzYb9K,EAAE,CAAA2G,YAAA,CAAAC,IAAA;QAAA3B,MAAA,GAAFjF,EAAE,CAAA2G,YAAA,CAAAE,0BAAA,qCAyY+J1G,gBAAgB;MAAA;MAAAkE,UAAA;MAAAyC,QAAA,GAzYjL9G,EAAE,CAAAkH,wBAAA,EAAFlH,EAAE,CAAAiI,0BAAA,EAAFjI,EAAE,CAAA0L,oBAAA;IAAA,EAyY+O;EAAE;AACvV;AACA;EAAA,QAAApH,SAAA,oBAAAA,SAAA,KA3YoGtE,EAAE,CAAAuE,iBAAA,CA2YX+G,eAAe,EAAc,CAAC;IAC7GnH,IAAI,EAAEjE,SAAS;IACfsE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BiC,MAAM,EAAE,CAAC;QAAEpD,IAAI,EAAE,SAAS;QAAEsI,KAAK,EAAE;MAAkB,CAAC,CAAC;MACvDvH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAEnE,EAAE,CAACgE;EAAY,CAAC,EAAE;IAAEG,IAAI,EAAEnE,EAAE,CAACyL;EAAgB,CAAC,EAAE;IAAEtH,IAAI,EAAEiD,SAAS;IAAEC,UAAU,EAAE,CAAC;MACvGlD,IAAI,EAAE/D,MAAM;MACZoE,IAAI,EAAE,CAACjB,SAAS;IACpB,CAAC,EAAE;MACCY,IAAI,EAAE9D;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE4E,MAAM,EAAE,CAAC;MAClCd,IAAI,EAAE7D,KAAK;MACXkE,IAAI,EAAE,CAAC;QAAEoH,KAAK,EAAE,uBAAuB;QAAEtE,SAAS,EAAEnH;MAAiB,CAAC;IAC1E,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,MAAMoL,eAAe,SAASd,UAAU,CAAC;EACrC;EACA,IAAIxF,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,OAAO;EACvB;EACA,IAAID,MAAMA,CAACE,KAAK,EAAE;IACd,IAAIA,KAAK,KAAK,IAAI,CAACD,OAAO,EAAE;MACxB,IAAI,CAACA,OAAO,GAAGC,KAAK;MACpB,IAAI,CAACC,iBAAiB,GAAG,IAAI;IACjC;EACJ;EACA1B,WAAWA,CAACC,QAAQ,EAAE+G,QAAQ,EAAEnF,MAAM,EAAE;IACpC,KAAK,CAAC5B,QAAQ,EAAE+G,QAAQ,CAAC;IACzB,IAAI,CAACnF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACH,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACF,OAAO,GAAG,KAAK;EACxB;EACA;EACA;EACAyF,WAAWA,CAACC,OAAO,EAAE;IACjB,KAAK,CAACD,WAAW,CAACC,OAAO,CAAC;EAC9B;EACA;EACApF,gBAAgBA,CAAA,EAAG;IACf,MAAMA,gBAAgB,GAAG,IAAI,CAACJ,iBAAiB;IAC/C,IAAI,CAACK,kBAAkB,CAAC,CAAC;IACzB,OAAOD,gBAAgB;EAC3B;EACA;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACL,iBAAiB,GAAG,KAAK;EAClC;EACA;IAAS,IAAI,CAACxB,IAAI,YAAAiI,wBAAA/H,CAAA;MAAA,YAAAA,CAAA,IAAwFyH,eAAe,EA/bzBvL,EAAE,CAAA+D,iBAAA,CA+byC/D,EAAE,CAACgE,WAAW,GA/bzDhE,EAAE,CAAA+D,iBAAA,CA+boE/D,EAAE,CAACyL,eAAe,GA/bxFzL,EAAE,CAAA+D,iBAAA,CA+bmGR,SAAS;IAAA,CAA4D;EAAE;EAC5Q;IAAS,IAAI,CAACU,IAAI,kBAhc8EjE,EAAE,CAAAkE,iBAAA;MAAAC,IAAA,EAgcJoH,eAAe;MAAAnH,SAAA;MAAAsC,MAAA;QAAAoE,OAAA,GAhcb9K,EAAE,CAAA2G,YAAA,CAAAC,IAAA;QAAA3B,MAAA,GAAFjF,EAAE,CAAA2G,YAAA,CAAAE,0BAAA,qCAgc+J1G,gBAAgB;MAAA;MAAAkE,UAAA;MAAAyC,QAAA,GAhcjL9G,EAAE,CAAAkH,wBAAA,EAAFlH,EAAE,CAAAiI,0BAAA,EAAFjI,EAAE,CAAA0L,oBAAA;IAAA,EAgc+O;EAAE;AACvV;AACA;EAAA,QAAApH,SAAA,oBAAAA,SAAA,KAlcoGtE,EAAE,CAAAuE,iBAAA,CAkcXgH,eAAe,EAAc,CAAC;IAC7GpH,IAAI,EAAEjE,SAAS;IACfsE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BiC,MAAM,EAAE,CAAC;QAAEpD,IAAI,EAAE,SAAS;QAAEsI,KAAK,EAAE;MAAkB,CAAC,CAAC;MACvDvH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAEnE,EAAE,CAACgE;EAAY,CAAC,EAAE;IAAEG,IAAI,EAAEnE,EAAE,CAACyL;EAAgB,CAAC,EAAE;IAAEtH,IAAI,EAAEiD,SAAS;IAAEC,UAAU,EAAE,CAAC;MACvGlD,IAAI,EAAE/D,MAAM;MACZoE,IAAI,EAAE,CAACjB,SAAS;IACpB,CAAC,EAAE;MACCY,IAAI,EAAE9D;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE4E,MAAM,EAAE,CAAC;MAClCd,IAAI,EAAE7D,KAAK;MACXkE,IAAI,EAAE,CAAC;QAAEoH,KAAK,EAAE,uBAAuB;QAAEtE,SAAS,EAAEnH;MAAiB,CAAC;IAC1E,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA,MAAM2L,SAAS,SAASrB,UAAU,CAAC;EAC/B;EACA;EACA/G,WAAWA,CAACC,QAAQ,EAAE+G,QAAQ,EAAEnF,MAAM,EAAE;IACpC,KAAK,CAAC5B,QAAQ,EAAE+G,QAAQ,CAAC;IACzB,IAAI,CAACnF,MAAM,GAAGA,MAAM;EACxB;EACA;IAAS,IAAI,CAAC3B,IAAI,YAAAmI,kBAAAjI,CAAA;MAAA,YAAAA,CAAA,IAAwFgI,SAAS,EA9dnB9L,EAAE,CAAA+D,iBAAA,CA8dmC/D,EAAE,CAACgE,WAAW,GA9dnDhE,EAAE,CAAA+D,iBAAA,CA8d8D/D,EAAE,CAACyL,eAAe,GA9dlFzL,EAAE,CAAA+D,iBAAA,CA8d6FR,SAAS;IAAA,CAA4D;EAAE;EACtQ;IAAS,IAAI,CAACU,IAAI,kBA/d8EjE,EAAE,CAAAkE,iBAAA;MAAAC,IAAA,EA+dJ2H,SAAS;MAAA1H,SAAA;MAAAsC,MAAA;QAAAoE,OAAA,GA/dP9K,EAAE,CAAA2G,YAAA,CAAAC,IAAA;QAAAoF,IAAA,GAAFhM,EAAE,CAAA2G,YAAA,CAAAC,IAAA;MAAA;MAAAvC,UAAA;MAAAyC,QAAA,GAAF9G,EAAE,CAAAiI,0BAAA;IAAA,EA+diL;EAAE;AACzR;AACA;EAAA,QAAA3D,SAAA,oBAAAA,SAAA,KAjeoGtE,EAAE,CAAAuE,iBAAA,CAieXuH,SAAS,EAAc,CAAC;IACvG3H,IAAI,EAAEjE,SAAS;IACfsE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,aAAa;MACvBiC,MAAM,EAAE,CACJ;QAAEpD,IAAI,EAAE,SAAS;QAAEsI,KAAK,EAAE;MAAmB,CAAC,EAC9C;QAAEtI,IAAI,EAAE,MAAM;QAAEsI,KAAK,EAAE;MAAgB,CAAC,CAC3C;MACDvH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAEnE,EAAE,CAACgE;EAAY,CAAC,EAAE;IAAEG,IAAI,EAAEnE,EAAE,CAACyL;EAAgB,CAAC,EAAE;IAAEtH,IAAI,EAAEiD,SAAS;IAAEC,UAAU,EAAE,CAAC;MACvGlD,IAAI,EAAE/D,MAAM;MACZoE,IAAI,EAAE,CAACjB,SAAS;IACpB,CAAC,EAAE;MACCY,IAAI,EAAE9D;IACV,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB;AACA;AACA;AACA;AACA,MAAM4L,aAAa,CAAC;EAChB;AACJ;AACA;AACA;AACA;AACA;AACA;EACI;IAAS,IAAI,CAACC,oBAAoB,GAAG,IAAI;EAAE;EAC3CxI,WAAWA,CAACyI,cAAc,EAAE;IACxB,IAAI,CAACA,cAAc,GAAGA,cAAc;IACpCF,aAAa,CAACC,oBAAoB,GAAG,IAAI;EAC7C;EACA3C,WAAWA,CAAA,EAAG;IACV;IACA;IACA,IAAI0C,aAAa,CAACC,oBAAoB,KAAK,IAAI,EAAE;MAC7CD,aAAa,CAACC,oBAAoB,GAAG,IAAI;IAC7C;EACJ;EACA;IAAS,IAAI,CAACtI,IAAI,YAAAwI,sBAAAtI,CAAA;MAAA,YAAAA,CAAA,IAAwFmI,aAAa,EAzgBvBjM,EAAE,CAAA+D,iBAAA,CAygBuC/D,EAAE,CAACqM,gBAAgB;IAAA,CAA4C;EAAE;EAC1M;IAAS,IAAI,CAACpI,IAAI,kBA1gB8EjE,EAAE,CAAAkE,iBAAA;MAAAC,IAAA,EA0gBJ8H,aAAa;MAAA7H,SAAA;MAAAC,UAAA;IAAA,EAAkE;EAAE;AACnL;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA5gBoGtE,EAAE,CAAAuE,iBAAA,CA4gBX0H,aAAa,EAAc,CAAC;IAC3G9H,IAAI,EAAEjE,SAAS;IACfsE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAEnE,EAAE,CAACqM;EAAiB,CAAC,CAAC;AAAA;AACjE;AACA,MAAMC,YAAY,CAAC;EACf;IAAS,IAAI,CAAC1I,IAAI,YAAA2I,qBAAAzI,CAAA;MAAA,YAAAA,CAAA,IAAwFwI,YAAY;IAAA,CAAmD;EAAE;EAC3K;IAAS,IAAI,CAACE,IAAI,kBAthB8ExM,EAAE,CAAAyM,iBAAA;MAAAtI,IAAA,EAshBJmI,YAAY;MAAAlI,SAAA;MAAA4D,SAAA,WAAoG,KAAK;MAAA3D,UAAA;MAAAyC,QAAA,GAthBnH9G,EAAE,CAAA0M,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAlJ,QAAA,WAAAmJ,sBAAA7K,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFjC,EAAE,CAAAsC,kBAAA,KAshB4N,CAAC;QAAA;MAAA;MAAAyK,YAAA,GAA6Dd,aAAa;MAAAe,aAAA;IAAA,EAAkI;EAAE;AACjhB;AACA;EAAA,QAAA1I,SAAA,oBAAAA,SAAA,KAxhBoGtE,EAAE,CAAAuE,iBAAA,CAwhBX+H,YAAY,EAAc,CAAC;IAC1GnI,IAAI,EAAE1D,SAAS;IACf+D,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oCAAoC;MAC9Cd,QAAQ,EAAE6G,gBAAgB;MAC1BtC,IAAI,EAAE;QACF,OAAO,EAAE,gBAAgB;QACzB,MAAM,EAAE;MACZ,CAAC;MACD;MACA;MACA+E,eAAe,EAAEvM,uBAAuB,CAACwM,OAAO;MAChDF,aAAa,EAAErM,iBAAiB,CAACiG,IAAI;MACrCvC,UAAU,EAAE,IAAI;MAChB8I,OAAO,EAAE,CAAClB,aAAa;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMmB,YAAY,CAAC;EACf;IAAS,IAAI,CAACxJ,IAAI,YAAAyJ,qBAAAvJ,CAAA;MAAA,YAAAA,CAAA,IAAwFsJ,YAAY;IAAA,CAAmD;EAAE;EAC3K;IAAS,IAAI,CAACZ,IAAI,kBA5iB8ExM,EAAE,CAAAyM,iBAAA;MAAAtI,IAAA,EA4iBJiJ,YAAY;MAAAhJ,SAAA;MAAA4D,SAAA,WAAoG,KAAK;MAAA3D,UAAA;MAAAyC,QAAA,GA5iBnH9G,EAAE,CAAA0M,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAlJ,QAAA,WAAA2J,sBAAArL,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFjC,EAAE,CAAAsC,kBAAA,KA4iB4N,CAAC;QAAA;MAAA;MAAAyK,YAAA,GAA6Dd,aAAa;MAAAe,aAAA;IAAA,EAAkI;EAAE;AACjhB;AACA;EAAA,QAAA1I,SAAA,oBAAAA,SAAA,KA9iBoGtE,EAAE,CAAAuE,iBAAA,CA8iBX6I,YAAY,EAAc,CAAC;IAC1GjJ,IAAI,EAAE1D,SAAS;IACf+D,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oCAAoC;MAC9Cd,QAAQ,EAAE6G,gBAAgB;MAC1BtC,IAAI,EAAE;QACF,OAAO,EAAE,gBAAgB;QACzB,MAAM,EAAE;MACZ,CAAC;MACD;MACA;MACA+E,eAAe,EAAEvM,uBAAuB,CAACwM,OAAO;MAChDF,aAAa,EAAErM,iBAAiB,CAACiG,IAAI;MACrCvC,UAAU,EAAE,IAAI;MAChB8I,OAAO,EAAE,CAAClB,aAAa;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMsB,MAAM,CAAC;EACT;IAAS,IAAI,CAAC3J,IAAI,YAAA4J,eAAA1J,CAAA;MAAA,YAAAA,CAAA,IAAwFyJ,MAAM;IAAA,CAAmD;EAAE;EACrK;IAAS,IAAI,CAACf,IAAI,kBAlkB8ExM,EAAE,CAAAyM,iBAAA;MAAAtI,IAAA,EAkkBJoJ,MAAM;MAAAnJ,SAAA;MAAA4D,SAAA,WAAsF,KAAK;MAAA3D,UAAA;MAAAyC,QAAA,GAlkB/F9G,EAAE,CAAA0M,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAlJ,QAAA,WAAA8J,gBAAAxL,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFjC,EAAE,CAAAsC,kBAAA,KAkkBiM,CAAC;QAAA;MAAA;MAAAyK,YAAA,GAA6Dd,aAAa;MAAAe,aAAA;IAAA,EAAkI;EAAE;AACtf;AACA;EAAA,QAAA1I,SAAA,oBAAAA,SAAA,KApkBoGtE,EAAE,CAAAuE,iBAAA,CAokBXgJ,MAAM,EAAc,CAAC;IACpGpJ,IAAI,EAAE1D,SAAS;IACf+D,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChCd,QAAQ,EAAE6G,gBAAgB;MAC1BtC,IAAI,EAAE;QACF,OAAO,EAAE,SAAS;QAClB,MAAM,EAAE;MACZ,CAAC;MACD;MACA;MACA+E,eAAe,EAAEvM,uBAAuB,CAACwM,OAAO;MAChDF,aAAa,EAAErM,iBAAiB,CAACiG,IAAI;MACrCvC,UAAU,EAAE,IAAI;MAChB8I,OAAO,EAAE,CAAClB,aAAa;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMyB,YAAY,CAAC;EACfhK,WAAWA,CAACiK,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,iBAAiB,GAAG,iBAAiB;EAC9C;EACA;IAAS,IAAI,CAAChK,IAAI,YAAAiK,qBAAA/J,CAAA;MAAA,YAAAA,CAAA,IAAwF4J,YAAY,EA3lBtB1N,EAAE,CAAA+D,iBAAA,CA2lBsC/D,EAAE,CAACgE,WAAW;IAAA,CAA4C;EAAE;EACpM;IAAS,IAAI,CAACC,IAAI,kBA5lB8EjE,EAAE,CAAAkE,iBAAA;MAAAC,IAAA,EA4lBJuJ,YAAY;MAAAtJ,SAAA;MAAAC,UAAA;IAAA,EAA4E;EAAE;AAC5L;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA9lBoGtE,EAAE,CAAAuE,iBAAA,CA8lBXmJ,YAAY,EAAc,CAAC;IAC1GvJ,IAAI,EAAEjE,SAAS;IACfsE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2BAA2B;MACrCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAEnE,EAAE,CAACgE;EAAY,CAAC,CAAC;AAAA;;AAE5D;AACA;AACA;AACA;AACA,MAAM8J,iBAAiB,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC;AAC5D;AACA;AACA;AACA;AACA,MAAMC,YAAY,CAAC;EACf;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIrK,WAAWA,CAACsK,kBAAkB,EAAEC,aAAa,EAAEC,SAAS,EAAEC,wBAAwB,EAAEC,UAAU,GAAG,IAAI,EAAEC,6BAA6B,GAAG,IAAI,EAAEC,iBAAiB,EAAE;IAC5J,IAAI,CAACN,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,wBAAwB,GAAGA,wBAAwB;IACxD,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,6BAA6B,GAAGA,6BAA6B;IAClE,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,cAAc,GAAG;MAClB,KAAK,EAAE,GAAGP,aAAa,kBAAkB;MACzC,QAAQ,EAAE,GAAGA,aAAa,qBAAqB;MAC/C,MAAM,EAAE,GAAGA,aAAa,mBAAmB;MAC3C,OAAO,EAAE,GAAGA,aAAa;IAC7B,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;EACIQ,sBAAsBA,CAACC,IAAI,EAAEC,gBAAgB,EAAE;IAC3C,MAAMC,eAAe,GAAG,EAAE;IAC1B,KAAK,MAAMC,GAAG,IAAIH,IAAI,EAAE;MACpB;MACA;MACA,IAAIG,GAAG,CAACC,QAAQ,KAAKD,GAAG,CAACE,YAAY,EAAE;QACnC;MACJ;MACAH,eAAe,CAACvF,IAAI,CAACwF,GAAG,CAAC;MACzB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAACI,QAAQ,CAACpF,MAAM,EAAEmF,CAAC,EAAE,EAAE;QAC1CJ,eAAe,CAACvF,IAAI,CAACwF,GAAG,CAACI,QAAQ,CAACD,CAAC,CAAC,CAAC;MACzC;IACJ;IACA;IACA,IAAI,CAACb,wBAAwB,CAACjF,QAAQ,CAAC,MAAM;MACzC,KAAK,MAAMgG,OAAO,IAAIN,eAAe,EAAE;QACnC,IAAI,CAACO,kBAAkB,CAACD,OAAO,EAAEP,gBAAgB,CAAC;MACtD;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIS,mBAAmBA,CAACV,IAAI,EAAEW,iBAAiB,EAAEC,eAAe,EAAEC,qBAAqB,GAAG,IAAI,EAAE;IACxF,IAAI,CAACb,IAAI,CAAC7E,MAAM,IACZ,CAAC,IAAI,CAACuE,UAAU,IAChB,EAAEiB,iBAAiB,CAACG,IAAI,CAACC,KAAK,IAAIA,KAAK,CAAC,IAAIH,eAAe,CAACE,IAAI,CAACC,KAAK,IAAIA,KAAK,CAAC,CAAC,EAAE;MACnF,IAAI,IAAI,CAACnB,iBAAiB,EAAE;QACxB,IAAI,CAACA,iBAAiB,CAACoB,oBAAoB,CAAC;UAAEC,KAAK,EAAE;QAAG,CAAC,CAAC;QAC1D,IAAI,CAACrB,iBAAiB,CAACsB,uBAAuB,CAAC;UAAED,KAAK,EAAE;QAAG,CAAC,CAAC;MACjE;MACA;IACJ;IACA;IACA,IAAI,CAACxB,wBAAwB,CAACjF,QAAQ,CAAC,MAAM;MACzC,MAAM2G,QAAQ,GAAGnB,IAAI,CAAC,CAAC,CAAC;MACxB,MAAMoB,QAAQ,GAAGD,QAAQ,CAACZ,QAAQ,CAACpF,MAAM;MACzC,MAAMkG,UAAU,GAAG,IAAI,CAACC,cAAc,CAACH,QAAQ,EAAEN,qBAAqB,CAAC;MACvE,MAAMU,cAAc,GAAG,IAAI,CAACC,8BAA8B,CAACH,UAAU,EAAEV,iBAAiB,CAAC;MACzF,MAAMc,YAAY,GAAG,IAAI,CAACC,4BAA4B,CAACL,UAAU,EAAET,eAAe,CAAC;MACnF,MAAMe,eAAe,GAAGhB,iBAAiB,CAACiB,WAAW,CAAC,IAAI,CAAC;MAC3D,MAAMC,cAAc,GAAGjB,eAAe,CAACkB,OAAO,CAAC,IAAI,CAAC;MACpD,MAAMC,KAAK,GAAG,IAAI,CAACvC,SAAS,KAAK,KAAK;MACtC,MAAMwC,KAAK,GAAGD,KAAK,GAAG,OAAO,GAAG,MAAM;MACtC,MAAME,GAAG,GAAGF,KAAK,GAAG,MAAM,GAAG,OAAO;MACpC,KAAK,MAAM5B,GAAG,IAAIH,IAAI,EAAE;QACpB,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,QAAQ,EAAEd,CAAC,EAAE,EAAE;UAC/B,MAAM1I,IAAI,GAAGuI,GAAG,CAACI,QAAQ,CAACD,CAAC,CAAC;UAC5B,IAAIK,iBAAiB,CAACL,CAAC,CAAC,EAAE;YACtB,IAAI,CAAC4B,eAAe,CAACtK,IAAI,EAAEoK,KAAK,EAAET,cAAc,CAACjB,CAAC,CAAC,EAAEA,CAAC,KAAKqB,eAAe,CAAC;UAC/E;UACA,IAAIf,eAAe,CAACN,CAAC,CAAC,EAAE;YACpB,IAAI,CAAC4B,eAAe,CAACtK,IAAI,EAAEqK,GAAG,EAAER,YAAY,CAACnB,CAAC,CAAC,EAAEA,CAAC,KAAKuB,cAAc,CAAC;UAC1E;QACJ;MACJ;MACA,IAAI,IAAI,CAACjC,iBAAiB,EAAE;QACxB,IAAI,CAACA,iBAAiB,CAACoB,oBAAoB,CAAC;UACxCC,KAAK,EAAEU,eAAe,KAAK,CAAC,CAAC,GACvB,EAAE,GACFN,UAAU,CACPc,KAAK,CAAC,CAAC,EAAER,eAAe,GAAG,CAAC,CAAC,CAC7BS,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAM3B,iBAAiB,CAAC2B,KAAK,CAAC,GAAGD,KAAK,GAAG,IAAK;QAC5E,CAAC,CAAC;QACF,IAAI,CAACzC,iBAAiB,CAACsB,uBAAuB,CAAC;UAC3CD,KAAK,EAAEY,cAAc,KAAK,CAAC,CAAC,GACtB,EAAE,GACFR,UAAU,CACPc,KAAK,CAACN,cAAc,CAAC,CACrBO,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAM1B,eAAe,CAAC0B,KAAK,GAAGT,cAAc,CAAC,GAAGQ,KAAK,GAAG,IAAK,CAAC,CAC/EE,OAAO,CAAC;QACrB,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,SAASA,CAACC,WAAW,EAAEC,YAAY,EAAEC,QAAQ,EAAE;IAC3C;IACA,IAAI,CAAC,IAAI,CAACjD,UAAU,EAAE;MAClB;IACJ;IACA;IACA;IACA,IAAI,CAACD,wBAAwB,CAACjF,QAAQ,CAAC,MAAM;MACzC;MACA;MACA;MACA,MAAMwF,IAAI,GAAG2C,QAAQ,KAAK,QAAQ,GAAGF,WAAW,CAACN,KAAK,CAAC,CAAC,CAACI,OAAO,CAAC,CAAC,GAAGE,WAAW;MAChF,MAAMG,MAAM,GAAGD,QAAQ,KAAK,QAAQ,GAAGD,YAAY,CAACP,KAAK,CAAC,CAAC,CAACI,OAAO,CAAC,CAAC,GAAGG,YAAY;MACpF;MACA,MAAMG,aAAa,GAAG,EAAE;MACxB,MAAMC,iBAAiB,GAAG,EAAE;MAC5B,MAAMC,eAAe,GAAG,EAAE;MAC1B,KAAK,IAAIC,QAAQ,GAAG,CAAC,EAAEC,YAAY,GAAG,CAAC,EAAED,QAAQ,GAAGhD,IAAI,CAAC7E,MAAM,EAAE6H,QAAQ,EAAE,EAAE;QACzE,IAAI,CAACJ,MAAM,CAACI,QAAQ,CAAC,EAAE;UACnB;QACJ;QACAH,aAAa,CAACG,QAAQ,CAAC,GAAGC,YAAY;QACtC,MAAM9C,GAAG,GAAGH,IAAI,CAACgD,QAAQ,CAAC;QAC1BD,eAAe,CAACC,QAAQ,CAAC,GAAG,IAAI,CAAC1D,kBAAkB,GAC7C4D,KAAK,CAACrQ,IAAI,CAACsN,GAAG,CAACI,QAAQ,CAAC,GACxB,CAACJ,GAAG,CAAC;QACX,MAAMgD,MAAM,GAAGhD,GAAG,CAACiD,qBAAqB,CAAC,CAAC,CAACD,MAAM;QACjDF,YAAY,IAAIE,MAAM;QACtBL,iBAAiB,CAACE,QAAQ,CAAC,GAAGG,MAAM;MACxC;MACA,MAAME,gBAAgB,GAAGT,MAAM,CAAChB,WAAW,CAAC,IAAI,CAAC;MACjD,KAAK,IAAIoB,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAGhD,IAAI,CAAC7E,MAAM,EAAE6H,QAAQ,EAAE,EAAE;QACvD,IAAI,CAACJ,MAAM,CAACI,QAAQ,CAAC,EAAE;UACnB;QACJ;QACA,MAAMM,MAAM,GAAGT,aAAa,CAACG,QAAQ,CAAC;QACtC,MAAMO,kBAAkB,GAAGP,QAAQ,KAAKK,gBAAgB;QACxD,KAAK,MAAM7C,OAAO,IAAIuC,eAAe,CAACC,QAAQ,CAAC,EAAE;UAC7C,IAAI,CAACd,eAAe,CAAC1B,OAAO,EAAEmC,QAAQ,EAAEW,MAAM,EAAEC,kBAAkB,CAAC;QACvE;MACJ;MACA,IAAIZ,QAAQ,KAAK,KAAK,EAAE;QACpB,IAAI,CAAC/C,iBAAiB,EAAE4D,uBAAuB,CAAC;UAC5CvC,KAAK,EAAE6B,iBAAiB;UACxBW,OAAO,EAAEZ,aAAa;UACtBa,QAAQ,EAAEX;QACd,CAAC,CAAC;MACN,CAAC,MACI;QACD,IAAI,CAACnD,iBAAiB,EAAE+D,uBAAuB,CAAC;UAC5C1C,KAAK,EAAE6B,iBAAiB;UACxBW,OAAO,EAAEZ,aAAa;UACtBa,QAAQ,EAAEX;QACd,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIa,2BAA2BA,CAACC,YAAY,EAAEnB,YAAY,EAAE;IACpD,IAAI,CAAC,IAAI,CAACpD,kBAAkB,EAAE;MAC1B;IACJ;IACA;IACA,IAAI,CAACG,wBAAwB,CAACjF,QAAQ,CAAC,MAAM;MACzC,MAAMsJ,KAAK,GAAGD,YAAY,CAACE,aAAa,CAAC,OAAO,CAAC;MACjD,IAAID,KAAK,EAAE;QACP,IAAIpB,YAAY,CAAC5B,IAAI,CAACC,KAAK,IAAI,CAACA,KAAK,CAAC,EAAE;UACpC,IAAI,CAACN,kBAAkB,CAACqD,KAAK,EAAE,CAAC,QAAQ,CAAC,CAAC;QAC9C,CAAC,MACI;UACD,IAAI,CAAC5B,eAAe,CAAC4B,KAAK,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,CAAC;QACnD;MACJ;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIrD,kBAAkBA,CAACD,OAAO,EAAEP,gBAAgB,EAAE;IAC1C,KAAK,MAAM+D,GAAG,IAAI/D,gBAAgB,EAAE;MAChCO,OAAO,CAACyD,KAAK,CAACD,GAAG,CAAC,GAAG,EAAE;MACvBxD,OAAO,CAACvH,SAAS,CAACiL,MAAM,CAAC,IAAI,CAACpE,cAAc,CAACkE,GAAG,CAAC,CAAC;IACtD;IACA;IACA;IACA;IACA;IACA,MAAMG,YAAY,GAAG/E,iBAAiB,CAAC0B,IAAI,CAACkD,GAAG,IAAI/D,gBAAgB,CAAC6B,OAAO,CAACkC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAIxD,OAAO,CAACyD,KAAK,CAACD,GAAG,CAAC,CAAC;IAC9G,IAAIG,YAAY,EAAE;MACd3D,OAAO,CAACyD,KAAK,CAACG,MAAM,GAAG,IAAI,CAACC,oBAAoB,CAAC7D,OAAO,CAAC;IAC7D,CAAC,MACI;MACD;MACAA,OAAO,CAACyD,KAAK,CAACG,MAAM,GAAG,EAAE;MACzB,IAAI,IAAI,CAACzE,6BAA6B,EAAE;QACpCa,OAAO,CAACyD,KAAK,CAACtB,QAAQ,GAAG,EAAE;MAC/B;MACAnC,OAAO,CAACvH,SAAS,CAACiL,MAAM,CAAC,IAAI,CAAC3E,aAAa,CAAC;IAChD;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI2C,eAAeA,CAAC1B,OAAO,EAAEwD,GAAG,EAAEM,QAAQ,EAAEC,eAAe,EAAE;IACrD/D,OAAO,CAACvH,SAAS,CAACC,GAAG,CAAC,IAAI,CAACqG,aAAa,CAAC;IACzC,IAAIgF,eAAe,EAAE;MACjB/D,OAAO,CAACvH,SAAS,CAACC,GAAG,CAAC,IAAI,CAAC4G,cAAc,CAACkE,GAAG,CAAC,CAAC;IACnD;IACAxD,OAAO,CAACyD,KAAK,CAACD,GAAG,CAAC,GAAG,GAAGM,QAAQ,IAAI;IACpC9D,OAAO,CAACyD,KAAK,CAACG,MAAM,GAAG,IAAI,CAACC,oBAAoB,CAAC7D,OAAO,CAAC;IACzD,IAAI,IAAI,CAACb,6BAA6B,EAAE;MACpCa,OAAO,CAACyD,KAAK,CAACO,OAAO,IAAI,8CAA8C;IAC3E;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIH,oBAAoBA,CAAC7D,OAAO,EAAE;IAC1B,MAAMiE,gBAAgB,GAAG;MACrBC,GAAG,EAAE,GAAG;MACRC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE;IACX,CAAC;IACD,IAAIT,MAAM,GAAG,CAAC;IACd;IACA;IACA;IACA,KAAK,MAAMJ,GAAG,IAAI5E,iBAAiB,EAAE;MACjC,IAAIoB,OAAO,CAACyD,KAAK,CAACD,GAAG,CAAC,EAAE;QACpBI,MAAM,IAAIK,gBAAgB,CAACT,GAAG,CAAC;MACnC;IACJ;IACA,OAAOI,MAAM,GAAG,GAAGA,MAAM,EAAE,GAAG,EAAE;EACpC;EACA;EACA9C,cAAcA,CAACnB,GAAG,EAAEU,qBAAqB,GAAG,IAAI,EAAE;IAC9C,IAAI,CAACA,qBAAqB,IAAI,IAAI,CAAChB,iBAAiB,CAAC1E,MAAM,EAAE;MACzD,OAAO,IAAI,CAAC0E,iBAAiB;IACjC;IACA,MAAMwB,UAAU,GAAG,EAAE;IACrB,MAAMyD,aAAa,GAAG3E,GAAG,CAACI,QAAQ;IAClC,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwE,aAAa,CAAC3J,MAAM,EAAEmF,CAAC,EAAE,EAAE;MAC3C,IAAI1I,IAAI,GAAGkN,aAAa,CAACxE,CAAC,CAAC;MAC3Be,UAAU,CAAC1G,IAAI,CAAC/C,IAAI,CAACwL,qBAAqB,CAAC,CAAC,CAACf,KAAK,CAAC;IACvD;IACA,IAAI,CAACxC,iBAAiB,GAAGwB,UAAU;IACnC,OAAOA,UAAU;EACrB;EACA;AACJ;AACA;AACA;AACA;EACIG,8BAA8BA,CAACuD,MAAM,EAAErC,YAAY,EAAE;IACjD,MAAMsC,SAAS,GAAG,EAAE;IACpB,IAAIC,YAAY,GAAG,CAAC;IACpB,KAAK,IAAI3E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyE,MAAM,CAAC5J,MAAM,EAAEmF,CAAC,EAAE,EAAE;MACpC,IAAIoC,YAAY,CAACpC,CAAC,CAAC,EAAE;QACjB0E,SAAS,CAAC1E,CAAC,CAAC,GAAG2E,YAAY;QAC3BA,YAAY,IAAIF,MAAM,CAACzE,CAAC,CAAC;MAC7B;IACJ;IACA,OAAO0E,SAAS;EACpB;EACA;AACJ;AACA;AACA;AACA;EACItD,4BAA4BA,CAACqD,MAAM,EAAErC,YAAY,EAAE;IAC/C,MAAMsC,SAAS,GAAG,EAAE;IACpB,IAAIC,YAAY,GAAG,CAAC;IACpB,KAAK,IAAI3E,CAAC,GAAGyE,MAAM,CAAC5J,MAAM,EAAEmF,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACpC,IAAIoC,YAAY,CAACpC,CAAC,CAAC,EAAE;QACjB0E,SAAS,CAAC1E,CAAC,CAAC,GAAG2E,YAAY;QAC3BA,YAAY,IAAIF,MAAM,CAACzE,CAAC,CAAC;MAC7B;IACJ;IACA,OAAO0E,SAAS;EACpB;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASE,0BAA0BA,CAACC,EAAE,EAAE;EACpC,OAAOC,KAAK,CAAC,kCAAkCD,EAAE,IAAI,CAAC;AAC1D;AACA;AACA;AACA;AACA;AACA,SAASE,gCAAgCA,CAACzQ,IAAI,EAAE;EAC5C,OAAOwQ,KAAK,CAAC,+CAA+CxQ,IAAI,IAAI,CAAC;AACzE;AACA;AACA;AACA;AACA;AACA,SAAS0Q,mCAAmCA,CAAA,EAAG;EAC3C,OAAOF,KAAK,CAAC,sEAAsE,CAAC;AACxF;AACA;AACA;AACA;AACA;AACA,SAASG,kCAAkCA,CAACC,IAAI,EAAE;EAC9C,OAAOJ,KAAK,CAAC,kDAAkD,GAC3D,sBAAsBK,IAAI,CAACC,SAAS,CAACF,IAAI,CAAC,EAAE,CAAC;AACrD;AACA;AACA;AACA;AACA;AACA,SAASG,2BAA2BA,CAAA,EAAG;EACnC,OAAOP,KAAK,CAAC,mDAAmD,GAC5D,oDAAoD,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA,SAASQ,8BAA8BA,CAAA,EAAG;EACtC,OAAOR,KAAK,CAAC,wEAAwE,CAAC;AAC1F;AACA;AACA;AACA;AACA;AACA,SAASS,yCAAyCA,CAAA,EAAG;EACjD,OAAOT,KAAK,CAAC,6DAA6D,CAAC;AAC/E;AACA;AACA;AACA;AACA;AACA,SAASU,kCAAkCA,CAAA,EAAG;EAC1C,OAAOV,KAAK,CAAC,qCAAqC,CAAC;AACvD;;AAEA;AACA,MAAMW,2BAA2B,GAAG,IAAIxU,cAAc,CAAC,SAAS,CAAC;;AAEjE;AACA;AACA;AACA;AACA,MAAMyU,cAAc,CAAC;EACjB;IAAS,IAAI,CAAC9Q,IAAI,YAAA+Q,uBAAA7Q,CAAA;MAAA,YAAAA,CAAA,IAAwF4Q,cAAc;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAACzQ,IAAI,kBA//B8EjE,EAAE,CAAAkE,iBAAA;MAAAC,IAAA,EA+/BJuQ,cAAc;MAAAtQ,SAAA;MAAAC,UAAA;MAAAyC,QAAA,GA//BZ9G,EAAE,CAAA+G,kBAAA,CA+/B8G,CAAC;QAAEC,OAAO,EAAE1H,uBAAuB;QAAEsV,QAAQ,EAAErV;MAA6B,CAAC,CAAC;IAAA,EAAiB;EAAE;AACrT;AACA;EAAA,QAAA+E,SAAA,oBAAAA,SAAA,KAjgCoGtE,EAAE,CAAAuE,iBAAA,CAigCXmQ,cAAc,EAAc,CAAC;IAC5GvQ,IAAI,EAAEjE,SAAS;IACfsE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,uDAAuD;MACjE0C,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAE1H,uBAAuB;QAAEsV,QAAQ,EAAErV;MAA6B,CAAC,CAAC;MACzF8E,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAMwQ,aAAa,CAAC;EAChBnR,WAAWA,CAACoR,aAAa,EAAErN,UAAU,EAAE;IACnC,IAAI,CAACqN,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACrN,UAAU,GAAGA,UAAU;IAC5B,MAAMsN,KAAK,GAAGnU,MAAM,CAAC2C,SAAS,CAAC;IAC/BwR,KAAK,CAACC,UAAU,GAAG,IAAI;IACvBD,KAAK,CAACE,eAAe,CAAC,CAAC;EAC3B;EACA;IAAS,IAAI,CAACrR,IAAI,YAAAsR,sBAAApR,CAAA;MAAA,YAAAA,CAAA,IAAwF+Q,aAAa,EArhCvB7U,EAAE,CAAA+D,iBAAA,CAqhCuC/D,EAAE,CAACqM,gBAAgB,GArhC5DrM,EAAE,CAAA+D,iBAAA,CAqhCuE/D,EAAE,CAAC+H,UAAU;IAAA,CAA4C;EAAE;EACpO;IAAS,IAAI,CAAC9D,IAAI,kBAthC8EjE,EAAE,CAAAkE,iBAAA;MAAAC,IAAA,EAshCJ0Q,aAAa;MAAAzQ,SAAA;MAAAC,UAAA;IAAA,EAA8D;EAAE;AAC/K;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAxhCoGtE,EAAE,CAAAuE,iBAAA,CAwhCXsQ,aAAa,EAAc,CAAC;IAC3G1Q,IAAI,EAAEjE,SAAS;IACfsE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,aAAa;MACvBJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAEnE,EAAE,CAACqM;EAAiB,CAAC,EAAE;IAAElI,IAAI,EAAEnE,EAAE,CAAC+H;EAAW,CAAC,CAAC;AAAA;AAC1F;AACA;AACA;AACA;AACA,MAAMoN,eAAe,CAAC;EAClBzR,WAAWA,CAACoR,aAAa,EAAErN,UAAU,EAAE;IACnC,IAAI,CAACqN,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACrN,UAAU,GAAGA,UAAU;IAC5B,MAAMsN,KAAK,GAAGnU,MAAM,CAAC2C,SAAS,CAAC;IAC/BwR,KAAK,CAACK,gBAAgB,GAAG,IAAI;IAC7BL,KAAK,CAACE,eAAe,CAAC,CAAC;EAC3B;EACA;IAAS,IAAI,CAACrR,IAAI,YAAAyR,wBAAAvR,CAAA;MAAA,YAAAA,CAAA,IAAwFqR,eAAe,EA3iCzBnV,EAAE,CAAA+D,iBAAA,CA2iCyC/D,EAAE,CAACqM,gBAAgB,GA3iC9DrM,EAAE,CAAA+D,iBAAA,CA2iCyE/D,EAAE,CAAC+H,UAAU;IAAA,CAA4C;EAAE;EACtO;IAAS,IAAI,CAAC9D,IAAI,kBA5iC8EjE,EAAE,CAAAkE,iBAAA;MAAAC,IAAA,EA4iCJgR,eAAe;MAAA/Q,SAAA;MAAAC,UAAA;IAAA,EAAoE;EAAE;AACvL;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA9iCoGtE,EAAE,CAAAuE,iBAAA,CA8iCX4Q,eAAe,EAAc,CAAC;IAC7GhR,IAAI,EAAEjE,SAAS;IACfsE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAEnE,EAAE,CAACqM;EAAiB,CAAC,EAAE;IAAElI,IAAI,EAAEnE,EAAE,CAAC+H;EAAW,CAAC,CAAC;AAAA;AAC1F;AACA;AACA;AACA;AACA,MAAMuN,eAAe,CAAC;EAClB5R,WAAWA,CAACoR,aAAa,EAAErN,UAAU,EAAE;IACnC,IAAI,CAACqN,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACrN,UAAU,GAAGA,UAAU;IAC5B,MAAMsN,KAAK,GAAGnU,MAAM,CAAC2C,SAAS,CAAC;IAC/BwR,KAAK,CAACQ,gBAAgB,GAAG,IAAI;IAC7BR,KAAK,CAACE,eAAe,CAAC,CAAC;EAC3B;EACA;IAAS,IAAI,CAACrR,IAAI,YAAA4R,wBAAA1R,CAAA;MAAA,YAAAA,CAAA,IAAwFwR,eAAe,EAjkCzBtV,EAAE,CAAA+D,iBAAA,CAikCyC/D,EAAE,CAACqM,gBAAgB,GAjkC9DrM,EAAE,CAAA+D,iBAAA,CAikCyE/D,EAAE,CAAC+H,UAAU;IAAA,CAA4C;EAAE;EACtO;IAAS,IAAI,CAAC9D,IAAI,kBAlkC8EjE,EAAE,CAAAkE,iBAAA;MAAAC,IAAA,EAkkCJmR,eAAe;MAAAlR,SAAA;MAAAC,UAAA;IAAA,EAAoE;EAAE;AACvL;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KApkCoGtE,EAAE,CAAAuE,iBAAA,CAokCX+Q,eAAe,EAAc,CAAC;IAC7GnR,IAAI,EAAEjE,SAAS;IACfsE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAEnE,EAAE,CAACqM;EAAiB,CAAC,EAAE;IAAElI,IAAI,EAAEnE,EAAE,CAAC+H;EAAW,CAAC,CAAC;AAAA;AAC1F;AACA;AACA;AACA;AACA;AACA,MAAM0N,eAAe,CAAC;EAClB/R,WAAWA,CAACoR,aAAa,EAAErN,UAAU,EAAE;IACnC,IAAI,CAACqN,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACrN,UAAU,GAAGA,UAAU;IAC5B,MAAMsN,KAAK,GAAGnU,MAAM,CAAC2C,SAAS,CAAC;IAC/BwR,KAAK,CAACW,gBAAgB,GAAG,IAAI;IAC7BX,KAAK,CAACE,eAAe,CAAC,CAAC;EAC3B;EACA;IAAS,IAAI,CAACrR,IAAI,YAAA+R,wBAAA7R,CAAA;MAAA,YAAAA,CAAA,IAAwF2R,eAAe,EAxlCzBzV,EAAE,CAAA+D,iBAAA,CAwlCyC/D,EAAE,CAACqM,gBAAgB,GAxlC9DrM,EAAE,CAAA+D,iBAAA,CAwlCyE/D,EAAE,CAAC+H,UAAU;IAAA,CAA4C;EAAE;EACtO;IAAS,IAAI,CAAC9D,IAAI,kBAzlC8EjE,EAAE,CAAAkE,iBAAA;MAAAC,IAAA,EAylCJsR,eAAe;MAAArR,SAAA;MAAAC,UAAA;IAAA,EAAoE;EAAE;AACvL;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA3lCoGtE,EAAE,CAAAuE,iBAAA,CA2lCXkR,eAAe,EAAc,CAAC;IAC7GtR,IAAI,EAAEjE,SAAS;IACfsE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAEnE,EAAE,CAACqM;EAAiB,CAAC,EAAE;IAAElI,IAAI,EAAEnE,EAAE,CAAC+H;EAAW,CAAC,CAAC;AAAA;AAC1F;AACA;AACA;AACA;AACA;AACA,MAAM6N,kBAAkB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,UAAU,SAAShV,eAAe,CAAC;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiV,QAAQ,CAAC;EACX;EACAzN,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAAC0N,iBAAiB,KAAK3O,SAAS,EAAE;MACtC;MACA,MAAMgB,IAAI,GAAG,IAAI,CAAC4N,WAAW,CAACtO,aAAa,CAACuO,YAAY,CAAC,MAAM,CAAC;MAChE,MAAMC,QAAQ,GAAG9N,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,UAAU,GAAG,UAAU,GAAG,MAAM;MAC7E,IAAI,CAAC2N,iBAAiB,GAAG,IAAI,CAAC/H,kBAAkB,IAAIkI,QAAQ,KAAK,MAAM,GAAG,IAAI,GAAGA,QAAQ;IAC7F;IACA,OAAO,IAAI,CAACH,iBAAiB;EACjC;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAII,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,UAAU;EAC1B;EACA,IAAID,OAAOA,CAACE,EAAE,EAAE;IACZ,IAAI,CAAC,OAAO/R,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAK+R,EAAE,IAAI,IAAI,IAAI,OAAOA,EAAE,KAAK,UAAU,EAAE;MAC3FC,OAAO,CAACC,IAAI,CAAC,4CAA4CpC,IAAI,CAACC,SAAS,CAACiC,EAAE,CAAC,GAAG,CAAC;IACnF;IACA,IAAI,CAACD,UAAU,GAAGC,EAAE;EACxB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIG,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW;EAC3B;EACA,IAAID,UAAUA,CAACA,UAAU,EAAE;IACvB,IAAI,IAAI,CAACC,WAAW,KAAKD,UAAU,EAAE;MACjC,IAAI,CAACE,iBAAiB,CAACF,UAAU,CAAC;IACtC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIG,qBAAqBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACC,sBAAsB;EACtC;EACA,IAAID,qBAAqBA,CAACxR,KAAK,EAAE;IAC7B,IAAI,CAACyR,sBAAsB,GAAGzR,KAAK;IACnC;IACA;IACA,IAAI,IAAI,CAAC6P,UAAU,IAAI,IAAI,CAACA,UAAU,CAACF,aAAa,CAACjL,MAAM,EAAE;MACzD,IAAI,CAACgN,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACC,wBAAwB,CAAC,CAAC;IACnC;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAAC5R,KAAK,EAAE;IACnB,IAAI,CAAC6R,YAAY,GAAG7R,KAAK;IACzB;IACA,IAAI,CAAC8R,2BAA2B,GAAG,IAAI;IACvC,IAAI,CAACC,4BAA4B,GAAG,IAAI;EAC5C;EACAxT,WAAWA,CAACgH,QAAQ,EAAEyM,kBAAkB,EAAEnB,WAAW,EAAE5N,IAAI,EAAEgP,IAAI,EAAEC,SAAS,EAAEC,SAAS,EAAEC,aAAa,EAAEpJ,wBAAwB,EAAEqJ,cAAc;EAChJ;AACJ;AACA;AACA;EACIC,0BAA0B;EAC1B;AACJ;AACA;AACA;EACI1O,OAAO,EAAE;IACL,IAAI,CAAC2B,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACyM,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACnB,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACoB,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACE,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACpJ,wBAAwB,GAAGA,wBAAwB;IACxD,IAAI,CAACqJ,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,0BAA0B,GAAGA,0BAA0B;IAC5D,IAAI,CAAC1O,OAAO,GAAGA,OAAO;IACtB;IACA,IAAI,CAAC2O,UAAU,GAAG,IAAIpW,OAAO,CAAC,CAAC;IAC/B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACqW,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC;IAClC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC;IAClC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,IAAID,GAAG,CAAC,CAAC;IAC/B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACE,oBAAoB,GAAG,IAAIF,GAAG,CAAC,CAAC;IACrC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACG,oBAAoB,GAAG,IAAIH,GAAG,CAAC,CAAC;IACrC;AACR;AACA;AACA;IACQ,IAAI,CAACI,oBAAoB,GAAG,IAAI;IAChC;AACR;AACA;AACA;IACQ,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAChC;AACR;AACA;AACA;IACQ,IAAI,CAACjB,4BAA4B,GAAG,IAAI;IACxC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACD,2BAA2B,GAAG,IAAI;IACvC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACmB,oBAAoB,GAAG,IAAIR,GAAG,CAAC,CAAC;IACrC;AACR;AACA;AACA;IACQ,IAAI,CAACS,cAAc,GAAG,kBAAkB;IACxC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,4BAA4B,GAAG,IAAI;IACxC;IACA,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC;IACA,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B;IACA,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAAC1C,iBAAiB,GAAG3O,SAAS;IAClC,IAAI,CAACwP,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACI,YAAY,GAAG,KAAK;IACzB;AACR;AACA;AACA;IACQ,IAAI,CAAC0B,cAAc,GAAG,IAAI5X,YAAY,CAAC,CAAC;IACxC;IACA;IACA;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC6X,UAAU,GAAG,IAAInX,eAAe,CAAC;MAClCkP,KAAK,EAAE,CAAC;MACRC,GAAG,EAAEiI,MAAM,CAACC;IAChB,CAAC,CAAC;IACF,IAAI,CAACzQ,IAAI,EAAE;MACP4N,WAAW,CAACtO,aAAa,CAACY,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC;IAC3D;IACA,IAAI,CAAC+O,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACyB,SAAS,GAAG,CAACxB,SAAS,CAACyB,SAAS;IACrC,IAAI,CAAC/K,kBAAkB,GAAGgI,WAAW,CAACtO,aAAa,CAACsR,QAAQ,KAAK,OAAO;EAC5E;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB;IACA;IACA;IACA,IAAI,CAACC,WAAW,GAAG,IAAI,CAACzO,QAAQ,CAACM,IAAI,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAACmO,EAAE,EAAEC,OAAO,KAAK;MAC9D,OAAO,IAAI,CAAClD,OAAO,GAAG,IAAI,CAACA,OAAO,CAACkD,OAAO,CAACC,SAAS,EAAED,OAAO,CAACnF,IAAI,CAAC,GAAGmF,OAAO;IACjF,CAAC,CAAC;IACF,IAAI,CAAC7B,cAAc,CACd+B,MAAM,CAAC,CAAC,CACR5P,IAAI,CAAChI,SAAS,CAAC,IAAI,CAAC+V,UAAU,CAAC,CAAC,CAChC9N,SAAS,CAAC,MAAM;MACjB,IAAI,CAACqN,2BAA2B,GAAG,IAAI;IAC3C,CAAC,CAAC;EACN;EACAuC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACf,eAAe,GAAG,IAAI;EAC/B;EACAgB,qBAAqBA,CAAA,EAAG;IACpB;IACA,IAAI,IAAI,CAACC,UAAU,CAAC,CAAC,EAAE;MACnB,IAAI,CAACC,OAAO,CAAC,CAAC;IAClB;EACJ;EACApQ,WAAWA,CAAA,EAAG;IACV,CACI,IAAI,CAACyL,UAAU,EAAEF,aAAa,EAC9B,IAAI,CAACM,gBAAgB,EAAEN,aAAa,EACpC,IAAI,CAACS,gBAAgB,EAAET,aAAa,EACpC,IAAI,CAACsD,oBAAoB,EACzB,IAAI,CAACP,iBAAiB,EACtB,IAAI,CAACE,cAAc,EACnB,IAAI,CAACC,oBAAoB,EACzB,IAAI,CAACC,oBAAoB,EACzB,IAAI,CAACN,iBAAiB,CACzB,CAACiC,OAAO,CAAEC,GAAG,IAAK;MACfA,GAAG,EAAEC,KAAK,CAAC,CAAC;IAChB,CAAC,CAAC;IACF,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACvC,UAAU,CAAClO,IAAI,CAAC,CAAC;IACtB,IAAI,CAACkO,UAAU,CAACjO,QAAQ,CAAC,CAAC;IAC1B,IAAIjK,YAAY,CAAC,IAAI,CAACgX,UAAU,CAAC,EAAE;MAC/B,IAAI,CAACA,UAAU,CAAC0D,UAAU,CAAC,IAAI,CAAC;IACpC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,UAAUA,CAAA,EAAG;IACT,IAAI,CAACC,WAAW,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAC3C,MAAMzP,OAAO,GAAG,IAAI,CAACuO,WAAW,CAACjO,IAAI,CAAC,IAAI,CAACkP,WAAW,CAAC;IACvD,IAAI,CAACxP,OAAO,EAAE;MACV,IAAI,CAAC0P,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAAC5B,cAAc,CAAClP,IAAI,CAAC,CAAC;MAC1B;IACJ;IACA,MAAMsL,aAAa,GAAG,IAAI,CAACE,UAAU,CAACF,aAAa;IACnD,IAAI,CAACyC,aAAa,CAACgD,YAAY,CAAC3P,OAAO,EAAEkK,aAAa,EAAE,CAAC0F,MAAM,EAAEC,sBAAsB,EAAEC,YAAY,KAAK,IAAI,CAACC,oBAAoB,CAACH,MAAM,CAACI,IAAI,EAAEF,YAAY,CAAC,EAAEF,MAAM,IAAIA,MAAM,CAACI,IAAI,CAAC1G,IAAI,EAAGqF,MAAM,IAAK;MACpM,IAAIA,MAAM,CAACsB,SAAS,KAAKpb,sBAAsB,CAACqb,QAAQ,IAAIvB,MAAM,CAACwB,OAAO,EAAE;QACxE,IAAI,CAACC,0BAA0B,CAACzB,MAAM,CAACiB,MAAM,CAACI,IAAI,CAACK,MAAM,EAAE1B,MAAM,CAACwB,OAAO,CAAC;MAC9E;IACJ,CAAC,CAAC;IACF;IACA,IAAI,CAACG,sBAAsB,CAAC,CAAC;IAC7B;IACA;IACAtQ,OAAO,CAACuQ,qBAAqB,CAAEX,MAAM,IAAK;MACtC,MAAMY,OAAO,GAAGtG,aAAa,CAACuG,GAAG,CAACb,MAAM,CAACE,YAAY,CAAC;MACtDU,OAAO,CAACL,OAAO,CAAC3X,SAAS,GAAGoX,MAAM,CAACI,IAAI,CAAC1G,IAAI;IAChD,CAAC,CAAC;IACF,IAAI,CAACoG,gBAAgB,CAAC,CAAC;IACvB;IACA;IACA,IAAI,IAAI,CAACvR,OAAO,IAAIhI,MAAM,CAACua,eAAe,CAAC,CAAC,EAAE;MAC1C,IAAI,CAACvS,OAAO,CAACkB,QAAQ,CAACN,IAAI,CAAC/H,IAAI,CAAC,CAAC,CAAC,EAAED,SAAS,CAAC,IAAI,CAAC+V,UAAU,CAAC,CAAC,CAAC9N,SAAS,CAAC,MAAM;QAC5E,IAAI,CAACkN,wBAAwB,CAAC,CAAC;MACnC,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACA,wBAAwB,CAAC,CAAC;IACnC;IACA,IAAI,CAAC4B,cAAc,CAAClP,IAAI,CAAC,CAAC;EAC9B;EACA;EACA+R,YAAYA,CAAC/T,SAAS,EAAE;IACpB,IAAI,CAACqQ,iBAAiB,CAACjQ,GAAG,CAACJ,SAAS,CAAC;EACzC;EACA;EACAgU,eAAeA,CAAChU,SAAS,EAAE;IACvB,IAAI,CAACqQ,iBAAiB,CAAC4D,MAAM,CAACjU,SAAS,CAAC;EAC5C;EACA;EACAkU,SAASA,CAACT,MAAM,EAAE;IACd,IAAI,CAAClD,cAAc,CAACnQ,GAAG,CAACqT,MAAM,CAAC;EACnC;EACA;EACAU,YAAYA,CAACV,MAAM,EAAE;IACjB,IAAI,CAAClD,cAAc,CAAC0D,MAAM,CAACR,MAAM,CAAC;EACtC;EACA;EACAW,eAAeA,CAACC,YAAY,EAAE;IAC1B,IAAI,CAAC7D,oBAAoB,CAACpQ,GAAG,CAACiU,YAAY,CAAC;IAC3C,IAAI,CAAC3D,oBAAoB,GAAG,IAAI;EACpC;EACA;EACA4D,kBAAkBA,CAACD,YAAY,EAAE;IAC7B,IAAI,CAAC7D,oBAAoB,CAACyD,MAAM,CAACI,YAAY,CAAC;IAC9C,IAAI,CAAC3D,oBAAoB,GAAG,IAAI;EACpC;EACA;EACA6D,eAAeA,CAACC,YAAY,EAAE;IAC1B,IAAI,CAAC/D,oBAAoB,CAACrQ,GAAG,CAACoU,YAAY,CAAC;IAC3C,IAAI,CAAC7D,oBAAoB,GAAG,IAAI;EACpC;EACA;EACA8D,kBAAkBA,CAACD,YAAY,EAAE;IAC7B,IAAI,CAAC/D,oBAAoB,CAACwD,MAAM,CAACO,YAAY,CAAC;IAC9C,IAAI,CAAC7D,oBAAoB,GAAG,IAAI;EACpC;EACA;EACA+D,YAAYA,CAACC,SAAS,EAAE;IACpB,IAAI,CAACC,gBAAgB,GAAGD,SAAS;EACrC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIE,2BAA2BA,CAAA,EAAG;IAC1B,MAAMC,UAAU,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACnH,gBAAgB,CAAC;IAC/D;IACA;IACA;IACA,IAAI,IAAI,CAACpH,kBAAkB,EAAE;MACzB,MAAMwO,KAAK,GAAGC,mBAAmB,CAAC,IAAI,CAACrH,gBAAgB,EAAE,OAAO,CAAC;MACjE,IAAIoH,KAAK,EAAE;QACPA,KAAK,CAAC7J,KAAK,CAAC+J,OAAO,GAAGJ,UAAU,CAACzS,MAAM,GAAG,EAAE,GAAG,MAAM;MACzD;IACJ;IACA,MAAMuH,YAAY,GAAG,IAAI,CAAC2I,cAAc,CAACjJ,GAAG,CAAC+I,GAAG,IAAIA,GAAG,CAAC5U,MAAM,CAAC;IAC/D,IAAI,CAAC0X,aAAa,CAAClO,sBAAsB,CAAC6N,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC;IAC9D,IAAI,CAACK,aAAa,CAACzL,SAAS,CAACoL,UAAU,EAAElL,YAAY,EAAE,KAAK,CAAC;IAC7D;IACA,IAAI,CAAC2I,cAAc,CAACH,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACpU,kBAAkB,CAAC,CAAC,CAAC;EAChE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACImX,2BAA2BA,CAAA,EAAG;IAC1B,MAAMC,UAAU,GAAG,IAAI,CAACN,gBAAgB,CAAC,IAAI,CAAChH,gBAAgB,CAAC;IAC/D;IACA;IACA;IACA,IAAI,IAAI,CAACvH,kBAAkB,EAAE;MACzB,MAAMwE,KAAK,GAAGiK,mBAAmB,CAAC,IAAI,CAAClH,gBAAgB,EAAE,OAAO,CAAC;MACjE,IAAI/C,KAAK,EAAE;QACPA,KAAK,CAACG,KAAK,CAAC+J,OAAO,GAAGG,UAAU,CAAChT,MAAM,GAAG,EAAE,GAAG,MAAM;MACzD;IACJ;IACA,MAAMuH,YAAY,GAAG,IAAI,CAAC4I,cAAc,CAAClJ,GAAG,CAAC+I,GAAG,IAAIA,GAAG,CAAC5U,MAAM,CAAC;IAC/D,IAAI,CAAC0X,aAAa,CAAClO,sBAAsB,CAACoO,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC;IACjE,IAAI,CAACF,aAAa,CAACzL,SAAS,CAAC2L,UAAU,EAAEzL,YAAY,EAAE,QAAQ,CAAC;IAChE,IAAI,CAACuL,aAAa,CAACrK,2BAA2B,CAAC,IAAI,CAAC0D,WAAW,CAACtO,aAAa,EAAE0J,YAAY,CAAC;IAC5F;IACA,IAAI,CAAC4I,cAAc,CAACJ,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACpU,kBAAkB,CAAC,CAAC,CAAC;EAChE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIqR,wBAAwBA,CAAA,EAAG;IACvB,MAAMwF,UAAU,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACnH,gBAAgB,CAAC;IAC/D,MAAM0H,QAAQ,GAAG,IAAI,CAACP,gBAAgB,CAAC,IAAI,CAACvH,UAAU,CAAC;IACvD,MAAM6H,UAAU,GAAG,IAAI,CAACN,gBAAgB,CAAC,IAAI,CAAChH,gBAAgB,CAAC;IAC/D;IACA;IACA;IACA;IACA,IAAK,IAAI,CAACvH,kBAAkB,IAAI,CAAC,IAAI,CAACgJ,YAAY,IAAK,IAAI,CAACE,4BAA4B,EAAE;MACtF;MACA;MACA,IAAI,CAACyF,aAAa,CAAClO,sBAAsB,CAAC,CAAC,GAAG6N,UAAU,EAAE,GAAGQ,QAAQ,EAAE,GAAGD,UAAU,CAAC,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;MACzG,IAAI,CAAC3F,4BAA4B,GAAG,KAAK;IAC7C;IACA;IACAoF,UAAU,CAAC1C,OAAO,CAAC,CAACmD,SAAS,EAAE/N,CAAC,KAAK;MACjC,IAAI,CAACgO,sBAAsB,CAAC,CAACD,SAAS,CAAC,EAAE,IAAI,CAAChD,cAAc,CAAC/K,CAAC,CAAC,CAAC;IACpE,CAAC,CAAC;IACF;IACA,IAAI,CAACiO,QAAQ,CAACrD,OAAO,CAACqB,MAAM,IAAI;MAC5B;MACA,MAAMvM,IAAI,GAAG,EAAE;MACf,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8N,QAAQ,CAACjT,MAAM,EAAEmF,CAAC,EAAE,EAAE;QACtC,IAAI,IAAI,CAACoL,WAAW,CAACpL,CAAC,CAAC,CAACiM,MAAM,KAAKA,MAAM,EAAE;UACvCvM,IAAI,CAACrF,IAAI,CAACyT,QAAQ,CAAC9N,CAAC,CAAC,CAAC;QAC1B;MACJ;MACA,IAAI,CAACgO,sBAAsB,CAACtO,IAAI,EAAEuM,MAAM,CAAC;IAC7C,CAAC,CAAC;IACF;IACA4B,UAAU,CAACjD,OAAO,CAAC,CAACsD,SAAS,EAAElO,CAAC,KAAK;MACjC,IAAI,CAACgO,sBAAsB,CAAC,CAACE,SAAS,CAAC,EAAE,IAAI,CAAClD,cAAc,CAAChL,CAAC,CAAC,CAAC;IACpE,CAAC,CAAC;IACF;IACA4C,KAAK,CAACrQ,IAAI,CAAC,IAAI,CAACoW,iBAAiB,CAACwF,MAAM,CAAC,CAAC,CAAC,CAACvD,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACpU,kBAAkB,CAAC,CAAC,CAAC;EACxF;EACA;EACAwP,eAAeA,CAAA,EAAG;IACd;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACuD,cAAc,IACpB,IAAI,CAACxD,UAAU,IACf,IAAI,CAACI,gBAAgB,IACrB,IAAI,CAACG,gBAAgB,IACrB,IAAI,CAACG,gBAAgB,EAAE;MACvB,IAAI,CAAC8C,cAAc,GAAG,IAAI;MAC1B;MACA;MACA,IAAI,IAAI,CAACkB,UAAU,CAAC,CAAC,EAAE;QACnB,IAAI,CAACC,OAAO,CAAC,CAAC;MAClB;IACJ;EACJ;EACA;EACAD,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAAClB,cAAc,IAAI,IAAI,CAACC,eAAe;EACtD;EACA;EACAkB,OAAOA,CAAA,EAAG;IACN;IACA,IAAI,CAACyD,aAAa,CAAC,CAAC;IACpB,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB;IACA,IAAI,CAAC,IAAI,CAACtD,cAAc,CAAClQ,MAAM,IAC3B,CAAC,IAAI,CAACmQ,cAAc,CAACnQ,MAAM,IAC3B,CAAC,IAAI,CAACoT,QAAQ,CAACpT,MAAM,KACpB,OAAOvF,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAM+P,2BAA2B,CAAC,CAAC;IACvC;IACA;IACA,MAAMiJ,cAAc,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACnD,MAAMC,cAAc,GAAGF,cAAc,IAAI,IAAI,CAACpF,oBAAoB,IAAI,IAAI,CAACC,oBAAoB;IAC/F;IACA,IAAI,CAACjB,4BAA4B,GAAG,IAAI,CAACA,4BAA4B,IAAIsG,cAAc;IACvF,IAAI,CAACvG,2BAA2B,GAAGuG,cAAc;IACjD;IACA,IAAI,IAAI,CAACtF,oBAAoB,EAAE;MAC3B,IAAI,CAACuF,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAACvF,oBAAoB,GAAG,KAAK;IACrC;IACA;IACA,IAAI,IAAI,CAACC,oBAAoB,EAAE;MAC3B,IAAI,CAACuF,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAACvF,oBAAoB,GAAG,KAAK;IACrC;IACA;IACA;IACA,IAAI,IAAI,CAAC3B,UAAU,IAAI,IAAI,CAACyG,QAAQ,CAACpT,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC8T,yBAAyB,EAAE;MAChF,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAChC,CAAC,MACI,IAAI,IAAI,CAAC1G,4BAA4B,EAAE;MACxC;MACA;MACA,IAAI,CAACJ,wBAAwB,CAAC,CAAC;IACnC;IACA,IAAI,CAAC+G,kBAAkB,CAAC,CAAC;EAC7B;EACA;AACJ;AACA;AACA;AACA;EACIxD,iBAAiBA,CAAA,EAAG;IAChB,MAAMF,UAAU,GAAG,EAAE;IACrB;IACA;IACA,MAAM2D,oBAAoB,GAAG,IAAI,CAAC1F,oBAAoB;IACtD,IAAI,CAACA,oBAAoB,GAAG,IAAIR,GAAG,CAAC,CAAC;IACrC;IACA;IACA,KAAK,IAAI5I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC+O,KAAK,CAAClU,MAAM,EAAEmF,CAAC,EAAE,EAAE;MACxC,IAAIkF,IAAI,GAAG,IAAI,CAAC6J,KAAK,CAAC/O,CAAC,CAAC;MACxB,MAAMgP,iBAAiB,GAAG,IAAI,CAACC,qBAAqB,CAAC/J,IAAI,EAAElF,CAAC,EAAE8O,oBAAoB,CAACzC,GAAG,CAACnH,IAAI,CAAC,CAAC;MAC7F,IAAI,CAAC,IAAI,CAACkE,oBAAoB,CAAC8F,GAAG,CAAChK,IAAI,CAAC,EAAE;QACtC,IAAI,CAACkE,oBAAoB,CAAC+F,GAAG,CAACjK,IAAI,EAAE,IAAIkK,OAAO,CAAC,CAAC,CAAC;MACtD;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,iBAAiB,CAACnU,MAAM,EAAEwU,CAAC,EAAE,EAAE;QAC/C,IAAIC,SAAS,GAAGN,iBAAiB,CAACK,CAAC,CAAC;QACpC,MAAME,KAAK,GAAG,IAAI,CAACnG,oBAAoB,CAACiD,GAAG,CAACiD,SAAS,CAACpK,IAAI,CAAC;QAC3D,IAAIqK,KAAK,CAACL,GAAG,CAACI,SAAS,CAACrD,MAAM,CAAC,EAAE;UAC7BsD,KAAK,CAAClD,GAAG,CAACiD,SAAS,CAACrD,MAAM,CAAC,CAAC5R,IAAI,CAACiV,SAAS,CAAC;QAC/C,CAAC,MACI;UACDC,KAAK,CAACJ,GAAG,CAACG,SAAS,CAACrD,MAAM,EAAE,CAACqD,SAAS,CAAC,CAAC;QAC5C;QACAnE,UAAU,CAAC9Q,IAAI,CAACiV,SAAS,CAAC;MAC9B;IACJ;IACA,OAAOnE,UAAU;EACrB;EACA;AACJ;AACA;AACA;AACA;EACI8D,qBAAqBA,CAAC/J,IAAI,EAAEoF,SAAS,EAAEiF,KAAK,EAAE;IAC1C,MAAMC,OAAO,GAAG,IAAI,CAACC,WAAW,CAACvK,IAAI,EAAEoF,SAAS,CAAC;IACjD,OAAOkF,OAAO,CAAC1N,GAAG,CAACmK,MAAM,IAAI;MACzB,MAAMyD,gBAAgB,GAAGH,KAAK,IAAIA,KAAK,CAACL,GAAG,CAACjD,MAAM,CAAC,GAAGsD,KAAK,CAAClD,GAAG,CAACJ,MAAM,CAAC,GAAG,EAAE;MAC5E,IAAIyD,gBAAgB,CAAC7U,MAAM,EAAE;QACzB,MAAMwP,OAAO,GAAGqF,gBAAgB,CAACC,KAAK,CAAC,CAAC;QACxCtF,OAAO,CAACC,SAAS,GAAGA,SAAS;QAC7B,OAAOD,OAAO;MAClB,CAAC,MACI;QACD,OAAO;UAAEnF,IAAI;UAAE+G,MAAM;UAAE3B;QAAU,CAAC;MACtC;IACJ,CAAC,CAAC;EACN;EACA;EACA+D,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC1F,iBAAiB,CAACmC,KAAK,CAAC,CAAC;IAC9B,MAAM8E,UAAU,GAAGC,gBAAgB,CAAC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACC,kBAAkB,CAAC,EAAE,IAAI,CAAClH,iBAAiB,CAAC;IACtG+G,UAAU,CAAChF,OAAO,CAACpS,SAAS,IAAI;MAC5B,IAAI,IAAI,CAACmQ,iBAAiB,CAACuG,GAAG,CAAC1W,SAAS,CAAClE,IAAI,CAAC,KACzC,OAAOgB,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACjD,MAAMyP,gCAAgC,CAACvM,SAAS,CAAClE,IAAI,CAAC;MAC1D;MACA,IAAI,CAACqU,iBAAiB,CAACwG,GAAG,CAAC3W,SAAS,CAAClE,IAAI,EAAEkE,SAAS,CAAC;IACzD,CAAC,CAAC;EACN;EACA;EACA4V,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACrD,cAAc,GAAG8E,gBAAgB,CAAC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACE,qBAAqB,CAAC,EAAE,IAAI,CAAChH,oBAAoB,CAAC;IAC/G,IAAI,CAACgC,cAAc,GAAG6E,gBAAgB,CAAC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACG,qBAAqB,CAAC,EAAE,IAAI,CAAChH,oBAAoB,CAAC;IAC/G,IAAI,CAACgF,QAAQ,GAAG4B,gBAAgB,CAAC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACI,eAAe,CAAC,EAAE,IAAI,CAACnH,cAAc,CAAC;IAC7F;IACA,MAAMoH,cAAc,GAAG,IAAI,CAAClC,QAAQ,CAACmC,MAAM,CAACvF,GAAG,IAAI,CAACA,GAAG,CAAC7N,IAAI,CAAC;IAC7D,IAAI,CAAC,IAAI,CAAC2K,qBAAqB,IAC3BwI,cAAc,CAACtV,MAAM,GAAG,CAAC,KACxB,OAAOvF,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAM0P,mCAAmC,CAAC,CAAC;IAC/C;IACA,IAAI,CAACiG,cAAc,GAAGkF,cAAc,CAAC,CAAC,CAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;EACI5B,qBAAqBA,CAAA,EAAG;IACpB,MAAM8B,kBAAkB,GAAGA,CAACC,GAAG,EAAEzF,GAAG,KAAKyF,GAAG,IAAI,CAAC,CAACzF,GAAG,CAAC1O,cAAc,CAAC,CAAC;IACtE;IACA,MAAMoU,kBAAkB,GAAG,IAAI,CAACtC,QAAQ,CAACuC,MAAM,CAACH,kBAAkB,EAAE,KAAK,CAAC;IAC1E,IAAIE,kBAAkB,EAAE;MACpB,IAAI,CAAC1I,oBAAoB,CAAC,CAAC;IAC/B;IACA;IACA,MAAM4I,oBAAoB,GAAG,IAAI,CAAC1F,cAAc,CAACyF,MAAM,CAACH,kBAAkB,EAAE,KAAK,CAAC;IAClF,IAAII,oBAAoB,EAAE;MACtB,IAAI,CAAChC,sBAAsB,CAAC,CAAC;IACjC;IACA,MAAMiC,oBAAoB,GAAG,IAAI,CAAC1F,cAAc,CAACwF,MAAM,CAACH,kBAAkB,EAAE,KAAK,CAAC;IAClF,IAAIK,oBAAoB,EAAE;MACtB,IAAI,CAAChC,sBAAsB,CAAC,CAAC;IACjC;IACA,OAAO6B,kBAAkB,IAAIE,oBAAoB,IAAIC,oBAAoB;EAC7E;EACA;AACJ;AACA;AACA;AACA;EACIhJ,iBAAiBA,CAACF,UAAU,EAAE;IAC1B,IAAI,CAACuH,KAAK,GAAG,EAAE;IACf,IAAIve,YAAY,CAAC,IAAI,CAACgX,UAAU,CAAC,EAAE;MAC/B,IAAI,CAACA,UAAU,CAAC0D,UAAU,CAAC,IAAI,CAAC;IACpC;IACA;IACA,IAAI,IAAI,CAACyD,yBAAyB,EAAE;MAChC,IAAI,CAACA,yBAAyB,CAACgC,WAAW,CAAC,CAAC;MAC5C,IAAI,CAAChC,yBAAyB,GAAG,IAAI;IACzC;IACA,IAAI,CAACnH,UAAU,EAAE;MACb,IAAI,IAAI,CAAC2C,WAAW,EAAE;QAClB,IAAI,CAACA,WAAW,CAACjO,IAAI,CAAC,EAAE,CAAC;MAC7B;MACA,IAAI,IAAI,CAAC8J,UAAU,EAAE;QACjB,IAAI,CAACA,UAAU,CAACF,aAAa,CAACgF,KAAK,CAAC,CAAC;MACzC;IACJ;IACA,IAAI,CAACrD,WAAW,GAAGD,UAAU;EACjC;EACA;EACAoH,qBAAqBA,CAAA,EAAG;IACpB;IACA,IAAI,CAAC,IAAI,CAACpH,UAAU,EAAE;MAClB;IACJ;IACA,IAAIoJ,UAAU;IACd,IAAIpgB,YAAY,CAAC,IAAI,CAACgX,UAAU,CAAC,EAAE;MAC/BoJ,UAAU,GAAG,IAAI,CAACpJ,UAAU,CAACqJ,OAAO,CAAC,IAAI,CAAC;IAC9C,CAAC,MACI,IAAIpe,YAAY,CAAC,IAAI,CAAC+U,UAAU,CAAC,EAAE;MACpCoJ,UAAU,GAAG,IAAI,CAACpJ,UAAU;IAChC,CAAC,MACI,IAAI5E,KAAK,CAACkO,OAAO,CAAC,IAAI,CAACtJ,UAAU,CAAC,EAAE;MACrCoJ,UAAU,GAAGle,EAAE,CAAC,IAAI,CAAC8U,UAAU,CAAC;IACpC;IACA,IAAIoJ,UAAU,KAAKxY,SAAS,KAAK,OAAO9C,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC7E,MAAMgQ,8BAA8B,CAAC,CAAC;IAC1C;IACA,IAAI,CAACqJ,yBAAyB,GAAGiC,UAAU,CACtCjW,IAAI,CAAChI,SAAS,CAAC,IAAI,CAAC+V,UAAU,CAAC,CAAC,CAChC9N,SAAS,CAACsK,IAAI,IAAI;MACnB,IAAI,CAAC6J,KAAK,GAAG7J,IAAI,IAAI,EAAE;MACvB,IAAI,CAACiG,UAAU,CAAC,CAAC;IACrB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIsD,sBAAsBA,CAAA,EAAG;IACrB;IACA,IAAI,IAAI,CAACrI,gBAAgB,CAACN,aAAa,CAACjL,MAAM,GAAG,CAAC,EAAE;MAChD,IAAI,CAACuL,gBAAgB,CAACN,aAAa,CAACgF,KAAK,CAAC,CAAC;IAC/C;IACA,IAAI,CAACC,cAAc,CAACH,OAAO,CAAC,CAACC,GAAG,EAAE7K,CAAC,KAAK,IAAI,CAAC+Q,UAAU,CAAC,IAAI,CAAC3K,gBAAgB,EAAEyE,GAAG,EAAE7K,CAAC,CAAC,CAAC;IACvF,IAAI,CAACqN,2BAA2B,CAAC,CAAC;EACtC;EACA;AACJ;AACA;AACA;EACIqB,sBAAsBA,CAAA,EAAG;IACrB;IACA,IAAI,IAAI,CAACnI,gBAAgB,CAACT,aAAa,CAACjL,MAAM,GAAG,CAAC,EAAE;MAChD,IAAI,CAAC0L,gBAAgB,CAACT,aAAa,CAACgF,KAAK,CAAC,CAAC;IAC/C;IACA,IAAI,CAACE,cAAc,CAACJ,OAAO,CAAC,CAACC,GAAG,EAAE7K,CAAC,KAAK,IAAI,CAAC+Q,UAAU,CAAC,IAAI,CAACxK,gBAAgB,EAAEsE,GAAG,EAAE7K,CAAC,CAAC,CAAC;IACvF,IAAI,CAAC4N,2BAA2B,CAAC,CAAC;EACtC;EACA;EACAI,sBAAsBA,CAACtO,IAAI,EAAEuM,MAAM,EAAE;IACjC,MAAM2D,UAAU,GAAGhN,KAAK,CAACrQ,IAAI,CAAC0Z,MAAM,CAACnQ,OAAO,IAAI,EAAE,CAAC,CAACgG,GAAG,CAACkP,UAAU,IAAI;MAClE,MAAMxY,SAAS,GAAG,IAAI,CAACmQ,iBAAiB,CAAC0D,GAAG,CAAC2E,UAAU,CAAC;MACxD,IAAI,CAACxY,SAAS,KAAK,OAAOlD,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAC/D,MAAMsP,0BAA0B,CAACoM,UAAU,CAAC;MAChD;MACA,OAAOxY,SAAS;IACpB,CAAC,CAAC;IACF,MAAM6H,iBAAiB,GAAGuP,UAAU,CAAC9N,GAAG,CAACtJ,SAAS,IAAIA,SAAS,CAACvC,MAAM,CAAC;IACvE,MAAMqK,eAAe,GAAGsP,UAAU,CAAC9N,GAAG,CAACtJ,SAAS,IAAIA,SAAS,CAACnC,SAAS,CAAC;IACxE,IAAI,CAACsX,aAAa,CAACvN,mBAAmB,CAACV,IAAI,EAAEW,iBAAiB,EAAEC,eAAe,EAAE,CAAC,IAAI,CAAC0H,YAAY,IAAI,IAAI,CAACC,2BAA2B,CAAC;EAC5I;EACA;EACAsF,gBAAgBA,CAAC0D,SAAS,EAAE;IACxB,MAAMC,YAAY,GAAG,EAAE;IACvB,KAAK,IAAIlR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiR,SAAS,CAACnL,aAAa,CAACjL,MAAM,EAAEmF,CAAC,EAAE,EAAE;MACrD,MAAMmR,OAAO,GAAGF,SAAS,CAACnL,aAAa,CAACuG,GAAG,CAACrM,CAAC,CAAC;MAC9CkR,YAAY,CAAC7W,IAAI,CAAC8W,OAAO,CAACC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC3C;IACA,OAAOF,YAAY;EACvB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIzB,WAAWA,CAACvK,IAAI,EAAEoF,SAAS,EAAE;IACzB,IAAI,IAAI,CAAC2D,QAAQ,CAACpT,MAAM,IAAI,CAAC,EAAE;MAC3B,OAAO,CAAC,IAAI,CAACoT,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC7B;IACA,IAAIuB,OAAO,GAAG,EAAE;IAChB,IAAI,IAAI,CAAC7H,qBAAqB,EAAE;MAC5B6H,OAAO,GAAG,IAAI,CAACvB,QAAQ,CAACmC,MAAM,CAACvF,GAAG,IAAI,CAACA,GAAG,CAAC7N,IAAI,IAAI6N,GAAG,CAAC7N,IAAI,CAACsN,SAAS,EAAEpF,IAAI,CAAC,CAAC;IACjF,CAAC,MACI;MACD,IAAI+G,MAAM,GAAG,IAAI,CAACgC,QAAQ,CAACjS,IAAI,CAAC6O,GAAG,IAAIA,GAAG,CAAC7N,IAAI,IAAI6N,GAAG,CAAC7N,IAAI,CAACsN,SAAS,EAAEpF,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC+F,cAAc;MACpG,IAAIgB,MAAM,EAAE;QACRuD,OAAO,CAACnV,IAAI,CAAC4R,MAAM,CAAC;MACxB;IACJ;IACA,IAAI,CAACuD,OAAO,CAAC3U,MAAM,KAAK,OAAOvF,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACpE,MAAM2P,kCAAkC,CAACC,IAAI,CAAC;IAClD;IACA,OAAOsK,OAAO;EAClB;EACA7D,oBAAoBA,CAAC2D,SAAS,EAAEtN,KAAK,EAAE;IACnC,MAAMiK,MAAM,GAAGqD,SAAS,CAACrD,MAAM;IAC/B,MAAMF,OAAO,GAAG;MAAE3X,SAAS,EAAEkb,SAAS,CAACpK;IAAK,CAAC;IAC7C,OAAO;MACHvG,WAAW,EAAEsN,MAAM,CAACtX,QAAQ;MAC5BoX,OAAO;MACP/J;IACJ,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;EACI+O,UAAUA,CAACM,MAAM,EAAEpF,MAAM,EAAEjK,KAAK,EAAE+J,OAAO,GAAG,CAAC,CAAC,EAAE;IAC5C;IACA,MAAMuF,IAAI,GAAGD,MAAM,CAACvL,aAAa,CAACyL,kBAAkB,CAACtF,MAAM,CAACtX,QAAQ,EAAEoX,OAAO,EAAE/J,KAAK,CAAC;IACrF,IAAI,CAACgK,0BAA0B,CAACC,MAAM,EAAEF,OAAO,CAAC;IAChD,OAAOuF,IAAI;EACf;EACAtF,0BAA0BA,CAACC,MAAM,EAAEF,OAAO,EAAE;IACxC,KAAK,IAAIyF,YAAY,IAAI,IAAI,CAACC,iBAAiB,CAACxF,MAAM,CAAC,EAAE;MACrD,IAAIhP,aAAa,CAACC,oBAAoB,EAAE;QACpCD,aAAa,CAACC,oBAAoB,CAACC,cAAc,CAACoU,kBAAkB,CAACC,YAAY,EAAEzF,OAAO,CAAC;MAC/F;IACJ;IACA,IAAI,CAAC5D,kBAAkB,CAACuJ,YAAY,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;AACA;EACIxF,sBAAsBA,CAAA,EAAG;IACrB,MAAMpG,aAAa,GAAG,IAAI,CAACE,UAAU,CAACF,aAAa;IACnD,KAAK,IAAI6L,WAAW,GAAG,CAAC,EAAEC,KAAK,GAAG9L,aAAa,CAACjL,MAAM,EAAE8W,WAAW,GAAGC,KAAK,EAAED,WAAW,EAAE,EAAE;MACxF,MAAMR,OAAO,GAAGrL,aAAa,CAACuG,GAAG,CAACsF,WAAW,CAAC;MAC9C,MAAM5F,OAAO,GAAGoF,OAAO,CAACpF,OAAO;MAC/BA,OAAO,CAAC6F,KAAK,GAAGA,KAAK;MACrB7F,OAAO,CAACxU,KAAK,GAAGoa,WAAW,KAAK,CAAC;MACjC5F,OAAO,CAAC8F,IAAI,GAAGF,WAAW,KAAKC,KAAK,GAAG,CAAC;MACxC7F,OAAO,CAAC+F,IAAI,GAAGH,WAAW,GAAG,CAAC,KAAK,CAAC;MACpC5F,OAAO,CAACgG,GAAG,GAAG,CAAChG,OAAO,CAAC+F,IAAI;MAC3B,IAAI,IAAI,CAACnK,qBAAqB,EAAE;QAC5BoE,OAAO,CAACzB,SAAS,GAAG,IAAI,CAACc,WAAW,CAACuG,WAAW,CAAC,CAACrH,SAAS;QAC3DyB,OAAO,CAAC4F,WAAW,GAAGA,WAAW;MACrC,CAAC,MACI;QACD5F,OAAO,CAAC/J,KAAK,GAAG,IAAI,CAACoJ,WAAW,CAACuG,WAAW,CAAC,CAACrH,SAAS;MAC3D;IACJ;EACJ;EACA;EACAmH,iBAAiBA,CAACxF,MAAM,EAAE;IACtB,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAACnQ,OAAO,EAAE;MAC5B,OAAO,EAAE;IACb;IACA,OAAO8G,KAAK,CAACrQ,IAAI,CAAC0Z,MAAM,CAACnQ,OAAO,EAAEkW,QAAQ,IAAI;MAC1C,MAAM3V,MAAM,GAAG,IAAI,CAACsM,iBAAiB,CAAC0D,GAAG,CAAC2F,QAAQ,CAAC;MACnD,IAAI,CAAC3V,MAAM,KAAK,OAAO/G,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAC5D,MAAMsP,0BAA0B,CAACoN,QAAQ,CAAC;MAC9C;MACA,OAAO/F,MAAM,CAAC7P,mBAAmB,CAACC,MAAM,CAAC;IAC7C,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIwL,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACsC,WAAW,CAACjO,IAAI,CAAC,EAAE,CAAC;IACzB,IAAI,CAAC8J,UAAU,CAACF,aAAa,CAACgF,KAAK,CAAC,CAAC;IACrC,IAAI,CAACK,UAAU,CAAC,CAAC;EACrB;EACA;AACJ;AACA;AACA;AACA;EACI0D,kBAAkBA,CAAA,EAAG;IACjB,MAAMoD,kBAAkB,GAAGA,CAAC3B,GAAG,EAAE4B,CAAC,KAAK;MACnC,OAAO5B,GAAG,IAAI4B,CAAC,CAAC1b,gBAAgB,CAAC,CAAC;IACtC,CAAC;IACD;IACA;IACA;IACA,IAAI,IAAI,CAACuU,cAAc,CAACyF,MAAM,CAACyB,kBAAkB,EAAE,KAAK,CAAC,EAAE;MACvD,IAAI,CAAC5E,2BAA2B,CAAC,CAAC;IACtC;IACA,IAAI,IAAI,CAACrC,cAAc,CAACwF,MAAM,CAACyB,kBAAkB,EAAE,KAAK,CAAC,EAAE;MACvD,IAAI,CAACrE,2BAA2B,CAAC,CAAC;IACtC;IACA,IAAIhL,KAAK,CAACrQ,IAAI,CAAC,IAAI,CAACoW,iBAAiB,CAACwF,MAAM,CAAC,CAAC,CAAC,CAACqC,MAAM,CAACyB,kBAAkB,EAAE,KAAK,CAAC,EAAE;MAC/E,IAAI,CAAC/J,4BAA4B,GAAG,IAAI;MACxC,IAAI,CAACJ,wBAAwB,CAAC,CAAC;IACnC;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIoC,kBAAkBA,CAAA,EAAG;IACjB,MAAMhL,SAAS,GAAG,IAAI,CAACkJ,IAAI,GAAG,IAAI,CAACA,IAAI,CAACjS,KAAK,GAAG,KAAK;IACrD,IAAI,CAACwX,aAAa,GAAG,IAAI5O,YAAY,CAAC,IAAI,CAACC,kBAAkB,EAAE,IAAI,CAACqK,cAAc,EAAEnK,SAAS,EAAE,IAAI,CAACC,wBAAwB,EAAE,IAAI,CAACmJ,SAAS,CAACyB,SAAS,EAAE,IAAI,CAACT,4BAA4B,EAAE,IAAI,CAACb,0BAA0B,CAAC;IAC3N,CAAC,IAAI,CAACL,IAAI,GAAG,IAAI,CAACA,IAAI,CAACmC,MAAM,GAAG7X,EAAE,CAAC,CAAC,EAC/BiI,IAAI,CAAChI,SAAS,CAAC,IAAI,CAAC+V,UAAU,CAAC,CAAC,CAChC9N,SAAS,CAACzE,KAAK,IAAI;MACpB,IAAI,CAACwX,aAAa,CAACzO,SAAS,GAAG/I,KAAK;MACpC,IAAI,CAAC2R,wBAAwB,CAAC,CAAC;IACnC,CAAC,CAAC;EACN;EACA;EACAgI,WAAWA,CAACqC,KAAK,EAAE;IACf,OAAOA,KAAK,CAAC/B,MAAM,CAACxE,IAAI,IAAI,CAACA,IAAI,CAACrV,MAAM,IAAIqV,IAAI,CAACrV,MAAM,KAAK,IAAI,CAAC;EACrE;EACA;EACA+U,gBAAgBA,CAAA,EAAG;IACf,MAAM6B,SAAS,GAAG,IAAI,CAACC,gBAAgB,IAAI,IAAI,CAACgF,UAAU;IAC1D,IAAI,CAACjF,SAAS,EAAE;MACZ;IACJ;IACA,MAAMkF,UAAU,GAAG,IAAI,CAACrM,UAAU,CAACF,aAAa,CAACjL,MAAM,KAAK,CAAC;IAC7D,IAAIwX,UAAU,KAAK,IAAI,CAAC9I,mBAAmB,EAAE;MACzC;IACJ;IACA,MAAM+I,SAAS,GAAG,IAAI,CAAC5L,gBAAgB,CAACZ,aAAa;IACrD,IAAIuM,UAAU,EAAE;MACZ,MAAMf,IAAI,GAAGgB,SAAS,CAACf,kBAAkB,CAACpE,SAAS,CAACxO,WAAW,CAAC;MAChE,MAAM4T,QAAQ,GAAGjB,IAAI,CAACF,SAAS,CAAC,CAAC,CAAC;MAClC;MACA;MACA,IAAIE,IAAI,CAACF,SAAS,CAACvW,MAAM,KAAK,CAAC,IAAI0X,QAAQ,EAAEzS,QAAQ,KAAK,IAAI,CAACuI,SAAS,CAACtI,YAAY,EAAE;QACnFwS,QAAQ,CAACjZ,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC;QACpCiZ,QAAQ,CAAC5Z,SAAS,CAACC,GAAG,CAACuU,SAAS,CAACvO,iBAAiB,CAAC;MACvD;IACJ,CAAC,MACI;MACD0T,SAAS,CAACxH,KAAK,CAAC,CAAC;IACrB;IACA,IAAI,CAACvB,mBAAmB,GAAG8I,UAAU;IACrC,IAAI,CAAClK,kBAAkB,CAACuJ,YAAY,CAAC,CAAC;EAC1C;EACA;IAAS,IAAI,CAAC9c,IAAI,YAAA4d,iBAAA1d,CAAA;MAAA,YAAAA,CAAA,IAAwFgS,QAAQ,EA3/DlB9V,EAAE,CAAA+D,iBAAA,CA2/DkC/D,EAAE,CAACyL,eAAe,GA3/DtDzL,EAAE,CAAA+D,iBAAA,CA2/DiE/D,EAAE,CAACyhB,iBAAiB,GA3/DvFzhB,EAAE,CAAA+D,iBAAA,CA2/DkG/D,EAAE,CAAC+H,UAAU,GA3/DjH/H,EAAE,CAAA0hB,iBAAA,CA2/D4H,MAAM,GA3/DpI1hB,EAAE,CAAA+D,iBAAA,CA2/DgK1E,EAAE,CAACsiB,cAAc,MA3/DnL3hB,EAAE,CAAA+D,iBAAA,CA2/D8MhE,QAAQ,GA3/DxNC,EAAE,CAAA+D,iBAAA,CA2/DmOnE,EAAE,CAACgiB,QAAQ,GA3/DhP5hB,EAAE,CAAA+D,iBAAA,CA2/D2PzE,uBAAuB,GA3/DpRU,EAAE,CAAA+D,iBAAA,CA2/D+R8E,0BAA0B,GA3/D3T7I,EAAE,CAAA+D,iBAAA,CA2/DsUlE,EAAE,CAACgiB,aAAa,GA3/DxV7hB,EAAE,CAAA+D,iBAAA,CA2/DmW0Q,2BAA2B,OA3/DhYzU,EAAE,CAAA+D,iBAAA,CA2/D2a/D,EAAE,CAACe,MAAM;IAAA,CAA4D;EAAE;EACplB;IAAS,IAAI,CAACyL,IAAI,kBA5/D8ExM,EAAE,CAAAyM,iBAAA;MAAAtI,IAAA,EA4/DJ2R,QAAQ;MAAA1R,SAAA;MAAA2B,cAAA,WAAA+b,wBAAA7f,EAAA,EAAAC,GAAA,EAAA+D,QAAA;QAAA,IAAAhE,EAAA;UA5/DNjC,EAAE,CAAAkG,cAAA,CAAAD,QAAA,EAkgE3ByH,YAAY;UAlgEa1N,EAAE,CAAAkG,cAAA,CAAAD,QAAA,EAkgEyDnB,YAAY;UAlgEvE9E,EAAE,CAAAkG,cAAA,CAAAD,QAAA,EAkgE0I6F,SAAS;UAlgErJ9L,EAAE,CAAAkG,cAAA,CAAAD,QAAA,EAkgE8NqF,eAAe;UAlgE/OtL,EAAE,CAAAkG,cAAA,CAAAD,QAAA,EAkgEwTsF,eAAe;QAAA;QAAA,IAAAtJ,EAAA;UAAA,IAAAkE,EAAA;UAlgEzUnG,EAAE,CAAAoG,cAAA,CAAAD,EAAA,GAAFnG,EAAE,CAAAqG,WAAA,QAAAnE,GAAA,CAAAkf,UAAA,GAAAjb,EAAA,CAAAI,KAAA;UAAFvG,EAAE,CAAAoG,cAAA,CAAAD,EAAA,GAAFnG,EAAE,CAAAqG,WAAA,QAAAnE,GAAA,CAAA6c,kBAAA,GAAA5Y,EAAA;UAAFnG,EAAE,CAAAoG,cAAA,CAAAD,EAAA,GAAFnG,EAAE,CAAAqG,WAAA,QAAAnE,GAAA,CAAAgd,eAAA,GAAA/Y,EAAA;UAAFnG,EAAE,CAAAoG,cAAA,CAAAD,EAAA,GAAFnG,EAAE,CAAAqG,WAAA,QAAAnE,GAAA,CAAA8c,qBAAA,GAAA7Y,EAAA;UAAFnG,EAAE,CAAAoG,cAAA,CAAAD,EAAA,GAAFnG,EAAE,CAAAqG,WAAA,QAAAnE,GAAA,CAAA+c,qBAAA,GAAA9Y,EAAA;QAAA;MAAA;MAAA6B,SAAA;MAAA+Z,QAAA;MAAAC,YAAA,WAAAC,sBAAAhgB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFjC,EAAE,CAAAkiB,WAAA,2BAAAhgB,GAAA,CAAA6U,WA4/DG,CAAC;QAAA;MAAA;MAAArQ,MAAA;QAAAyP,OAAA;QAAAK,UAAA;QAAAG,qBAAA,GA5/DN3W,EAAE,CAAA2G,YAAA,CAAAE,0BAAA,oDA4/DqM1G,gBAAgB;QAAA4W,WAAA,GA5/DvN/W,EAAE,CAAA2G,YAAA,CAAAE,0BAAA,gCA4/DoQ1G,gBAAgB;MAAA;MAAAgiB,OAAA;QAAAzJ,cAAA;MAAA;MAAA0J,QAAA;MAAA/d,UAAA;MAAAyC,QAAA,GA5/DtR9G,EAAE,CAAA+G,kBAAA,CA4/Dyb,CACnhB;QAAEC,OAAO,EAAEzD,SAAS;QAAE0D,WAAW,EAAE6O;MAAS,CAAC,EAC7C;QAAE9O,OAAO,EAAE1H,uBAAuB;QAAEsV,QAAQ,EAAElV;MAA6B,CAAC,EAC5E;QAAEsH,OAAO,EAAE6B,0BAA0B;QAAE+L,QAAQ,EAAE9L;MAAyB,CAAC;MAC3E;MACA;QAAE9B,OAAO,EAAEyN,2BAA2B;QAAE4N,QAAQ,EAAE;MAAK,CAAC,CAC3D,GAlgE2FriB,EAAE,CAAAkH,wBAAA,EAAFlH,EAAE,CAAA0M,mBAAA;MAAA4V,kBAAA,EAAAvgB,GAAA;MAAA4K,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAlJ,QAAA,WAAA4e,kBAAAtgB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFjC,EAAE,CAAAwiB,eAAA,CAAA1gB,GAAA;UAAF9B,EAAE,CAAAmC,YAAA,EAkgEmb,CAAC;UAlgEtbnC,EAAE,CAAAmC,YAAA,KAkgE6d,CAAC;UAlgEhenC,EAAE,CAAAyiB,UAAA,IAAAzgB,+BAAA,MAkgEwpB,CAAC,IAAAI,+BAAA,MAAuD,CAAC,IAAAI,+BAAA,MAAyS,CAAC;QAAA;QAAA,IAAAP,EAAA;UAlgE7/BjC,EAAE,CAAA+C,SAAA,EAkgEgrB,CAAC;UAlgEnrB/C,EAAE,CAAA0iB,aAAA,IAAAxgB,GAAA,CAAA4W,SAAA,SAkgEgrB,CAAC;UAlgEnrB9Y,EAAE,CAAA+C,SAAA,CAkgE6oC,CAAC;UAlgEhpC/C,EAAE,CAAA0iB,aAAA,IAAAxgB,GAAA,CAAA8L,kBAAA,QAkgE6oC,CAAC;QAAA;MAAA;MAAAjB,YAAA,GAAwHoI,eAAe,EAA8DN,aAAa,EAAwDY,eAAe,EAA8DH,eAAe;MAAAqN,MAAA;MAAA3V,aAAA;IAAA,EAAoI;EAAE;AAChuD;AACA;EAAA,QAAA1I,SAAA,oBAAAA,SAAA,KApgEoGtE,EAAE,CAAAuE,iBAAA,CAogEXuR,QAAQ,EAAc,CAAC;IACtG3R,IAAI,EAAE1D,SAAS;IACf+D,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,6BAA6B;MAAE2d,QAAQ,EAAE,UAAU;MAAEze,QAAQ,EAAEiS,kBAAkB;MAAE1N,IAAI,EAAE;QAChG,OAAO,EAAE,WAAW;QACpB,gCAAgC,EAAE;MACtC,CAAC;MAAE8E,aAAa,EAAErM,iBAAiB,CAACiG,IAAI;MAAEqG,eAAe,EAAEvM,uBAAuB,CAACwM,OAAO;MAAE/F,SAAS,EAAE,CACnG;QAAEH,OAAO,EAAEzD,SAAS;QAAE0D,WAAW,EAAE6O;MAAS,CAAC,EAC7C;QAAE9O,OAAO,EAAE1H,uBAAuB;QAAEsV,QAAQ,EAAElV;MAA6B,CAAC,EAC5E;QAAEsH,OAAO,EAAE6B,0BAA0B;QAAE+L,QAAQ,EAAE9L;MAAyB,CAAC;MAC3E;MACA;QAAE9B,OAAO,EAAEyN,2BAA2B;QAAE4N,QAAQ,EAAE;MAAK,CAAC,CAC3D;MAAEhe,UAAU,EAAE,IAAI;MAAE8I,OAAO,EAAE,CAACgI,eAAe,EAAEN,aAAa,EAAEY,eAAe,EAAEH,eAAe,CAAC;MAAEqN,MAAM,EAAE,CAAC,6CAA6C;IAAE,CAAC;EACvK,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAExe,IAAI,EAAEnE,EAAE,CAACyL;EAAgB,CAAC,EAAE;IAAEtH,IAAI,EAAEnE,EAAE,CAACyhB;EAAkB,CAAC,EAAE;IAAEtd,IAAI,EAAEnE,EAAE,CAAC+H;EAAW,CAAC,EAAE;IAAE5D,IAAI,EAAEiD,SAAS;IAAEC,UAAU,EAAE,CAAC;MACtIlD,IAAI,EAAEnD,SAAS;MACfwD,IAAI,EAAE,CAAC,MAAM;IACjB,CAAC;EAAE,CAAC,EAAE;IAAEL,IAAI,EAAE9E,EAAE,CAACsiB,cAAc;IAAEta,UAAU,EAAE,CAAC;MAC1ClD,IAAI,EAAE9D;IACV,CAAC;EAAE,CAAC,EAAE;IAAE8D,IAAI,EAAEiD,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClClD,IAAI,EAAE/D,MAAM;MACZoE,IAAI,EAAE,CAACzE,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEoE,IAAI,EAAEvE,EAAE,CAACgiB;EAAS,CAAC,EAAE;IAAEzd,IAAI,EAAEiD,SAAS;IAAEC,UAAU,EAAE,CAAC;MACzDlD,IAAI,EAAE/D,MAAM;MACZoE,IAAI,EAAE,CAAClF,uBAAuB;IAClC,CAAC;EAAE,CAAC,EAAE;IAAE6E,IAAI,EAAE2E,wBAAwB;IAAEzB,UAAU,EAAE,CAAC;MACjDlD,IAAI,EAAE/D,MAAM;MACZoE,IAAI,EAAE,CAACqE,0BAA0B;IACrC,CAAC;EAAE,CAAC,EAAE;IAAE1E,IAAI,EAAEtE,EAAE,CAACgiB;EAAc,CAAC,EAAE;IAAE1d,IAAI,EAAEiD,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC9DlD,IAAI,EAAE9D;IACV,CAAC,EAAE;MACC8D,IAAI,EAAElD;IACV,CAAC,EAAE;MACCkD,IAAI,EAAE/D,MAAM;MACZoE,IAAI,EAAE,CAACiQ,2BAA2B;IACtC,CAAC;EAAE,CAAC,EAAE;IAAEtQ,IAAI,EAAEnE,EAAE,CAACe,MAAM;IAAEsG,UAAU,EAAE,CAAC;MAClClD,IAAI,EAAE9D;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE8V,OAAO,EAAE,CAAC;MACnChS,IAAI,EAAE7D;IACV,CAAC,CAAC;IAAEkW,UAAU,EAAE,CAAC;MACbrS,IAAI,EAAE7D;IACV,CAAC,CAAC;IAAEqW,qBAAqB,EAAE,CAAC;MACxBxS,IAAI,EAAE7D,KAAK;MACXkE,IAAI,EAAE,CAAC;QAAE8C,SAAS,EAAEnH;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE4W,WAAW,EAAE,CAAC;MACd5S,IAAI,EAAE7D,KAAK;MACXkE,IAAI,EAAE,CAAC;QAAE8C,SAAS,EAAEnH;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEuY,cAAc,EAAE,CAAC;MACjBvU,IAAI,EAAEjD;IACV,CAAC,CAAC;IAAE6d,kBAAkB,EAAE,CAAC;MACrB5a,IAAI,EAAEhD,eAAe;MACrBqD,IAAI,EAAE,CAACM,YAAY,EAAE;QAAE8d,WAAW,EAAE;MAAK,CAAC;IAC9C,CAAC,CAAC;IAAE1D,eAAe,EAAE,CAAC;MAClB/a,IAAI,EAAEhD,eAAe;MACrBqD,IAAI,EAAE,CAACsH,SAAS,EAAE;QAAE8W,WAAW,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAE5D,qBAAqB,EAAE,CAAC;MACxB7a,IAAI,EAAEhD,eAAe;MACrBqD,IAAI,EAAE,CAAC8G,eAAe,EAAE;QAChBsX,WAAW,EAAE;MACjB,CAAC;IACT,CAAC,CAAC;IAAE3D,qBAAqB,EAAE,CAAC;MACxB9a,IAAI,EAAEhD,eAAe;MACrBqD,IAAI,EAAE,CAAC+G,eAAe,EAAE;QAChBqX,WAAW,EAAE;MACjB,CAAC;IACT,CAAC,CAAC;IAAExB,UAAU,EAAE,CAAC;MACbjd,IAAI,EAAE5D,YAAY;MAClBiE,IAAI,EAAE,CAACkJ,YAAY;IACvB,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,SAASmR,gBAAgBA,CAACgE,KAAK,EAAE1E,GAAG,EAAE;EAClC,OAAO0E,KAAK,CAACC,MAAM,CAAClR,KAAK,CAACrQ,IAAI,CAAC4c,GAAG,CAAC,CAAC;AACxC;AACA;AACA;AACA;AACA;AACA,SAAS1B,mBAAmBA,CAAC4D,MAAM,EAAE0C,OAAO,EAAE;EAC1C,MAAMC,gBAAgB,GAAGD,OAAO,CAACE,WAAW,CAAC,CAAC;EAC9C,IAAIC,OAAO,GAAG7C,MAAM,CAACvL,aAAa,CAAC5F,OAAO,CAACxH,aAAa;EACxD,OAAOwb,OAAO,EAAE;IACZ;IACA,MAAMlK,QAAQ,GAAGkK,OAAO,CAACpU,QAAQ,KAAK,CAAC,GAAGoU,OAAO,CAAClK,QAAQ,GAAG,IAAI;IACjE,IAAIA,QAAQ,KAAKgK,gBAAgB,EAAE;MAC/B,OAAOE,OAAO;IAClB,CAAC,MACI,IAAIlK,QAAQ,KAAK,OAAO,EAAE;MAC3B;MACA;IACJ;IACAkK,OAAO,GAAGA,OAAO,CAACC,UAAU;EAChC;EACA,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChB;EACA,IAAI9f,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACyB,KAAK;EACrB;EACA,IAAIzB,IAAIA,CAACA,IAAI,EAAE;IACX,IAAI,CAACyB,KAAK,GAAGzB,IAAI;IACjB;IACA;IACA,IAAI,CAAC+f,kBAAkB,CAAC,CAAC;EAC7B;EACA3f,WAAWA;EACX;EACA;EACA;EACA6B,MAAM,EAAE+d,QAAQ,EAAE;IACd,IAAI,CAAC/d,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC+d,QAAQ,GAAGA,QAAQ;IACxB;IACA,IAAI,CAACxgB,OAAO,GAAG,OAAO;IACtB,IAAI,CAACwgB,QAAQ,GAAGA,QAAQ,IAAI,CAAC,CAAC;EAClC;EACArK,QAAQA,CAAA,EAAG;IACP,IAAI,CAACoK,kBAAkB,CAAC,CAAC;IACzB,IAAI,IAAI,CAACpgB,UAAU,KAAKmE,SAAS,EAAE;MAC/B,IAAI,CAACnE,UAAU,GAAG,IAAI,CAACsgB,wBAAwB,CAAC,CAAC;IACrD;IACA,IAAI,CAAC,IAAI,CAAClgB,YAAY,EAAE;MACpB,IAAI,CAACA,YAAY,GACb,IAAI,CAACigB,QAAQ,CAACE,mBAAmB,KAAK,CAACtP,IAAI,EAAE5Q,IAAI,KAAK4Q,IAAI,CAAC5Q,IAAI,CAAC,CAAC;IACzE;IACA,IAAI,IAAI,CAACiC,MAAM,EAAE;MACb;MACA;MACA;MACA,IAAI,CAACiC,SAAS,CAAClB,IAAI,GAAG,IAAI,CAACA,IAAI;MAC/B,IAAI,CAACkB,SAAS,CAAChB,UAAU,GAAG,IAAI,CAACA,UAAU;MAC3C,IAAI,CAACjB,MAAM,CAACgW,YAAY,CAAC,IAAI,CAAC/T,SAAS,CAAC;IAC5C,CAAC,MACI,IAAI,OAAOlD,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MACpD,MAAMiQ,yCAAyC,CAAC,CAAC;IACrD;EACJ;EACAhL,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAChE,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAACiW,eAAe,CAAC,IAAI,CAAChU,SAAS,CAAC;IAC/C;EACJ;EACA;AACJ;AACA;AACA;EACI+b,wBAAwBA,CAAA,EAAG;IACvB,MAAMjgB,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,IAAI,CAACA,IAAI,KAAK,OAAOgB,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC1D,MAAMkQ,kCAAkC,CAAC,CAAC;IAC9C;IACA,IAAI,IAAI,CAAC8O,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACG,0BAA0B,EAAE;MAC3D,OAAO,IAAI,CAACH,QAAQ,CAACG,0BAA0B,CAACngB,IAAI,CAAC;IACzD;IACA,OAAOA,IAAI,CAAC,CAAC,CAAC,CAAC2f,WAAW,CAAC,CAAC,GAAG3f,IAAI,CAACuN,KAAK,CAAC,CAAC,CAAC;EAChD;EACA;EACAwS,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAAC7b,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAAClE,IAAI,GAAG,IAAI,CAACA,IAAI;IACnC;EACJ;EACA;IAAS,IAAI,CAACM,IAAI,YAAA8f,sBAAA5f,CAAA;MAAA,YAAAA,CAAA,IAAwFsf,aAAa,EA9qEvBpjB,EAAE,CAAA+D,iBAAA,CA8qEuC+R,QAAQ,MA9qEjD9V,EAAE,CAAA+D,iBAAA,CA8qE4EP,mBAAmB;IAAA,CAA4D;EAAE;EAC/P;IAAS,IAAI,CAACgJ,IAAI,kBA/qE8ExM,EAAE,CAAAyM,iBAAA;MAAAtI,IAAA,EA+qEJif,aAAa;MAAAhf,SAAA;MAAAuf,SAAA,WAAAC,oBAAA3hB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA/qEXjC,EAAE,CAAA6jB,WAAA,CA+qEqO/e,YAAY;UA/qEnP9E,EAAE,CAAA6jB,WAAA,CA+qEsUpgB,UAAU;UA/qElVzD,EAAE,CAAA6jB,WAAA,CA+qE2anf,gBAAgB;QAAA;QAAA,IAAAzC,EAAA;UAAA,IAAAkE,EAAA;UA/qE7bnG,EAAE,CAAAoG,cAAA,CAAAD,EAAA,GAAFnG,EAAE,CAAAqG,WAAA,QAAAnE,GAAA,CAAAsF,SAAA,GAAArB,EAAA,CAAAI,KAAA;UAAFvG,EAAE,CAAAoG,cAAA,CAAAD,EAAA,GAAFnG,EAAE,CAAAqG,WAAA,QAAAnE,GAAA,CAAAoE,IAAA,GAAAH,EAAA,CAAAI,KAAA;UAAFvG,EAAE,CAAAoG,cAAA,CAAAD,EAAA,GAAFnG,EAAE,CAAAqG,WAAA,QAAAnE,GAAA,CAAAsE,UAAA,GAAAL,EAAA,CAAAI,KAAA;QAAA;MAAA;MAAAG,MAAA;QAAApD,IAAA;QAAAL,UAAA;QAAAI,YAAA;QAAAP,OAAA;MAAA;MAAAuB,UAAA;MAAAyC,QAAA,GAAF9G,EAAE,CAAA0M,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAlJ,QAAA,WAAAmgB,uBAAA7hB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFjC,EAAE,CAAA+jB,uBAAA,KAgrExE,CAAC;UAhrEqE/jB,EAAE,CAAAyiB,UAAA,IAAAhgB,2BAAA,eAirE9B,CAAC,IAAAS,2BAAA,eAGH,CAAC;UAprE6BlD,EAAE,CAAAgkB,qBAAA;QAAA;MAAA;MAAAjX,YAAA,GAwrEvCjI,YAAY,EAA4GJ,gBAAgB,EAA+DmD,aAAa,EAAiFpE,UAAU,EAAyD+E,OAAO;MAAAwE,aAAA;IAAA,EAAyI;EAAE;AACzjB;AACA;EAAA,QAAA1I,SAAA,oBAAAA,SAAA,KA1rEoGtE,EAAE,CAAAuE,iBAAA,CA0rEX6e,aAAa,EAAc,CAAC;IAC3Gjf,IAAI,EAAE1D,SAAS;IACf+D,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3Bd,QAAQ,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBqJ,aAAa,EAAErM,iBAAiB,CAACiG,IAAI;MACrC;MACA;MACA;MACA;MACA;MACA;MACAqG,eAAe,EAAEvM,uBAAuB,CAACwM,OAAO;MAChD7I,UAAU,EAAE,IAAI;MAChB8I,OAAO,EAAE,CAACrI,YAAY,EAAEJ,gBAAgB,EAAEmD,aAAa,EAAEpE,UAAU,EAAE+E,OAAO;IAChF,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAErE,IAAI,EAAE2R,QAAQ;IAAEzO,UAAU,EAAE,CAAC;MAC9ClD,IAAI,EAAE9D;IACV,CAAC;EAAE,CAAC,EAAE;IAAE8D,IAAI,EAAEiD,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClClD,IAAI,EAAE9D;IACV,CAAC,EAAE;MACC8D,IAAI,EAAE/D,MAAM;MACZoE,IAAI,EAAE,CAAChB,mBAAmB;IAC9B,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEF,IAAI,EAAE,CAAC;MAChCa,IAAI,EAAE7D;IACV,CAAC,CAAC;IAAE2C,UAAU,EAAE,CAAC;MACbkB,IAAI,EAAE7D;IACV,CAAC,CAAC;IAAE+C,YAAY,EAAE,CAAC;MACfc,IAAI,EAAE7D;IACV,CAAC,CAAC;IAAEwC,OAAO,EAAE,CAAC;MACVqB,IAAI,EAAE7D;IACV,CAAC,CAAC;IAAEkH,SAAS,EAAE,CAAC;MACZrD,IAAI,EAAE/C,SAAS;MACfoD,IAAI,EAAE,CAACM,YAAY,EAAE;QAAEmf,MAAM,EAAE;MAAK,CAAC;IACzC,CAAC,CAAC;IAAE3d,IAAI,EAAE,CAAC;MACPnC,IAAI,EAAE/C,SAAS;MACfoD,IAAI,EAAE,CAACf,UAAU,EAAE;QAAEwgB,MAAM,EAAE;MAAK,CAAC;IACvC,CAAC,CAAC;IAAEzd,UAAU,EAAE,CAAC;MACbrC,IAAI,EAAE/C,SAAS;MACfoD,IAAI,EAAE,CAACE,gBAAgB,EAAE;QAAEuf,MAAM,EAAE;MAAK,CAAC;IAC7C,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMC,qBAAqB,GAAG,CAC1BpO,QAAQ,EACRhK,SAAS,EACTrI,UAAU,EACVwI,aAAa,EACbvH,gBAAgB,EAChBE,gBAAgB,EAChBE,YAAY,EACZ0D,OAAO,EACP+E,MAAM,EACN1F,aAAa,EACbM,aAAa,EACbmE,YAAY,EACZhB,eAAe,EACf8B,YAAY,EACZ7B,eAAe,EACfsJ,aAAa,EACbM,eAAe,EACfG,eAAe,EACf8N,aAAa,EACb1V,YAAY,EACZgH,cAAc,EACde,eAAe,CAClB;AACD,MAAM0O,cAAc,CAAC;EACjB;IAAS,IAAI,CAACvgB,IAAI,YAAAwgB,uBAAAtgB,CAAA;MAAA,YAAAA,CAAA,IAAwFqgB,cAAc;IAAA,CAAkD;EAAE;EAC5K;IAAS,IAAI,CAACE,IAAI,kBAvwE8ErkB,EAAE,CAAAskB,gBAAA;MAAAngB,IAAA,EAuwESggB;IAAc,EA0C9F;EAAE;EAC7B;IAAS,IAAI,CAACI,IAAI,kBAlzE8EvkB,EAAE,CAAAwkB,gBAAA;MAAArX,OAAA,GAkzEmCrN,eAAe;IAAA,EAAI;EAAE;AAC9J;AACA;EAAA,QAAAwE,SAAA,oBAAAA,SAAA,KApzEoGtE,EAAE,CAAAuE,iBAAA,CAozEX4f,cAAc,EAAc,CAAC;IAC5GhgB,IAAI,EAAE9C,QAAQ;IACdmD,IAAI,EAAE,CAAC;MACCigB,OAAO,EAAEP,qBAAqB;MAC9B/W,OAAO,EAAE,CAACrN,eAAe,EAAE,GAAGokB,qBAAqB;IACvD,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,mBAAmBA,CAACC,IAAI,EAAE;EAC/B,OAAO,cAAcA,IAAI,CAAC;IACtB;IACA,IAAI1f,MAAMA,CAAA,EAAG;MACT,OAAO,IAAI,CAACC,OAAO;IACvB;IACA,IAAID,MAAMA,CAAC2f,CAAC,EAAE;MACV,MAAMC,SAAS,GAAG,IAAI,CAAC3f,OAAO;MAC9B,IAAI,CAACA,OAAO,GAAGrD,qBAAqB,CAAC+iB,CAAC,CAAC;MACvC,IAAI,CAACxf,iBAAiB,GAAGyf,SAAS,KAAK,IAAI,CAAC3f,OAAO;IACvD;IACA;IACAM,gBAAgBA,CAAA,EAAG;MACf,MAAMA,gBAAgB,GAAG,IAAI,CAACJ,iBAAiB;MAC/C,IAAI,CAACA,iBAAiB,GAAG,KAAK;MAC9B,OAAOI,gBAAgB;IAC3B;IACA;IACAC,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAACL,iBAAiB,GAAG,KAAK;IAClC;IACA1B,WAAWA,CAAC,GAAGc,IAAI,EAAE;MACjB,KAAK,CAAC,GAAGA,IAAI,CAAC;MACd,IAAI,CAACU,OAAO,GAAG,KAAK;MACpB;MACA,IAAI,CAACE,iBAAiB,GAAG,KAAK;IAClC;EACJ,CAAC;AACL;;AAEA;AACA;AACA;;AAEA,SAASmC,WAAW,EAAEkD,UAAU,EAAED,gBAAgB,EAAEjH,SAAS,EAAEqS,kBAAkB,EAAEpN,OAAO,EAAE/E,UAAU,EAAEwI,aAAa,EAAEnH,YAAY,EAAEqD,aAAa,EAAEvD,gBAAgB,EAAEwI,YAAY,EAAE7B,eAAe,EAAE1D,aAAa,EAAEnD,gBAAgB,EAAE4H,YAAY,EAAEhB,eAAe,EAAEoC,YAAY,EAAEgH,cAAc,EAAEnH,MAAM,EAAEzB,SAAS,EAAEgK,QAAQ,EAAEqO,cAAc,EAAEf,aAAa,EAAEvO,aAAa,EAAES,eAAe,EAAEH,eAAe,EAAEM,eAAe,EAAE3H,iBAAiB,EAAE2G,2BAA2B,EAAE1G,YAAY,EAAEvK,mBAAmB,EAAEqF,0BAA0B,EAAEC,wBAAwB,EAAEJ,SAAS,EAAEgc,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}