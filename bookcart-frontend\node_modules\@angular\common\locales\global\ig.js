/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['ig'] = ["ig",[["A.M.","P.M."],u,["<PERSON>’<PERSON>t<PERSON>tụ","<PERSON>’abali"]],[["A.M.","P.M."],u,u],[["S","M","T","W","T","F","S"],["Sọn","<PERSON>ọn","<PERSON><PERSON>","<PERSON>","<PERSON><PERSON><PERSON>","<PERSON>a<PERSON>","<PERSON>t"],["<PERSON>ọnde<PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>zdee","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>"],["<PERSON>ọ<PERSON>","<PERSON>ọ<PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON><PERSON><PERSON>","<PERSON>a<PERSON>","<PERSON>t"]],u,[["<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","Ọ","<PERSON>","Ọ","<PERSON>","<PERSON>"],["<PERSON>","<PERSON>","<PERSON>a","<PERSON>pr","<PERSON><PERSON>","<PERSON>u","<PERSON>","Ọgọ","<PERSON>","Ọkt","<PERSON>","<PERSON>s"],["<PERSON><PERSON><PERSON><PERSON>","Febrụwarị","Maachị","Epreel","Mee","Juun","Julaị","Ọgọọst","Septemba","Ọktoba","Novemba","Disemba"]],u,[["T.K.","A.K."],u,["Tupu Kraist","Afọ Kraịst"]],1,[6,0],["d/M/yy","d MMM y","d MMMM y","EEEE, d MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1}, {0}",u,"{1} 'na' {0}",u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤#,##0.00","#E0"],"NGN","₦","Naịra",{"NGN":["₦"]},"ltr", plural, []];
  })(globalThis);
    