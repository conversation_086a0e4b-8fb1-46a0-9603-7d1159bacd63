/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["ee", [["ŋ", "ɣ"], ["ŋdi", "ɣetrɔ"], u], u, [["k", "d", "b", "k", "y", "f", "m"], ["kɔs", "dzo", "bla", "kuɖ", "yaw", "fiɖ", "mem"], ["kɔsiɖa", "dzoɖa", "blaɖa", "kuɖa", "yawoɖa", "fiɖa", "memleɖa"], ["kɔs", "dzo", "bla", "kuɖ", "yaw", "fiɖ", "mem"]], u, [["d", "d", "t", "a", "d", "m", "s", "d", "a", "k", "a", "d"], ["dzv", "dzd", "ted", "afɔ", "dam", "mas", "sia", "dea", "any", "kel", "ade", "dzm"], ["dzove", "dzodze", "tedoxe", "afɔfĩe", "dama", "masa", "siamlɔm", "deasiamime", "anyɔnyɔ", "kele", "adeɛmekpɔxe", "dzome"]], u, [["HYV", "Yŋ"], u, ["Hafi Yesu Va", "Yesu ŋɔli"]], 1, [6, 0], ["M/d/yy", "MMM d 'lia', y", "MMMM d 'lia' y", "EEEE, MMMM d 'lia' y"], ["a 'ga' h:mm", "a 'ga' h:mm:ss", "a 'ga' h:mm:ss z", "a 'ga' h:mm:ss zzzz"], ["{0} {1}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "mnn", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "GHS", "GH₵", "ghana siɖi", { "AUD": ["AU$", "$"], "BYN": [u, "р."], "GHS": ["GH₵"], "JPY": ["JP¥", "¥"], "THB": ["฿"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZWUuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9jb21tb24vbG9jYWxlcy9lZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7O0dBTUc7QUFFSCwwQ0FBMEM7QUFDMUMsTUFBTSxDQUFDLEdBQUcsU0FBUyxDQUFDO0FBRXBCLFNBQVMsTUFBTSxDQUFDLEdBQVc7SUFDM0IsTUFBTSxDQUFDLEdBQUcsR0FBRyxDQUFDO0lBRWQsSUFBSSxDQUFDLEtBQUssQ0FBQztRQUNQLE9BQU8sQ0FBQyxDQUFDO0lBQ2IsT0FBTyxDQUFDLENBQUM7QUFDVCxDQUFDO0FBRUQsZUFBZSxDQUFDLElBQUksRUFBQyxDQUFDLENBQUMsR0FBRyxFQUFDLEdBQUcsQ0FBQyxFQUFDLENBQUMsS0FBSyxFQUFDLE9BQU8sQ0FBQyxFQUFDLENBQUMsQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxDQUFDLEVBQUMsQ0FBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLENBQUMsRUFBQyxDQUFDLFFBQVEsRUFBQyxPQUFPLEVBQUMsT0FBTyxFQUFDLE1BQU0sRUFBQyxRQUFRLEVBQUMsTUFBTSxFQUFDLFNBQVMsQ0FBQyxFQUFDLENBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxDQUFDLENBQUMsRUFBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxDQUFDLEVBQUMsQ0FBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssQ0FBQyxFQUFDLENBQUMsT0FBTyxFQUFDLFFBQVEsRUFBQyxRQUFRLEVBQUMsUUFBUSxFQUFDLE1BQU0sRUFBQyxNQUFNLEVBQUMsU0FBUyxFQUFDLFlBQVksRUFBQyxTQUFTLEVBQUMsTUFBTSxFQUFDLGFBQWEsRUFBQyxPQUFPLENBQUMsQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsS0FBSyxFQUFDLElBQUksQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLGNBQWMsRUFBQyxXQUFXLENBQUMsQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsRUFBQyxDQUFDLFFBQVEsRUFBQyxnQkFBZ0IsRUFBQyxnQkFBZ0IsRUFBQyxzQkFBc0IsQ0FBQyxFQUFDLENBQUMsYUFBYSxFQUFDLGdCQUFnQixFQUFDLGtCQUFrQixFQUFDLHFCQUFxQixDQUFDLEVBQUMsQ0FBQyxTQUFTLEVBQUMsQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsRUFBQyxDQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxLQUFLLEVBQUMsR0FBRyxDQUFDLEVBQUMsQ0FBQyxXQUFXLEVBQUMsUUFBUSxFQUFDLFdBQVcsRUFBQyxLQUFLLENBQUMsRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLFlBQVksRUFBQyxFQUFDLEtBQUssRUFBQyxDQUFDLEtBQUssRUFBQyxHQUFHLENBQUMsRUFBQyxLQUFLLEVBQUMsQ0FBQyxDQUFDLEVBQUMsSUFBSSxDQUFDLEVBQUMsS0FBSyxFQUFDLENBQUMsS0FBSyxDQUFDLEVBQUMsS0FBSyxFQUFDLENBQUMsS0FBSyxFQUFDLEdBQUcsQ0FBQyxFQUFDLEtBQUssRUFBQyxDQUFDLEdBQUcsQ0FBQyxFQUFDLEtBQUssRUFBQyxDQUFDLEtBQUssRUFBQyxHQUFHLENBQUMsRUFBQyxFQUFDLEtBQUssRUFBRSxNQUFNLENBQUMsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuXG4vLyBUSElTIENPREUgSVMgR0VORVJBVEVEIC0gRE8gTk9UIE1PRElGWS5cbmNvbnN0IHUgPSB1bmRlZmluZWQ7XG5cbmZ1bmN0aW9uIHBsdXJhbCh2YWw6IG51bWJlcik6IG51bWJlciB7XG5jb25zdCBuID0gdmFsO1xuXG5pZiAobiA9PT0gMSlcbiAgICByZXR1cm4gMTtcbnJldHVybiA1O1xufVxuXG5leHBvcnQgZGVmYXVsdCBbXCJlZVwiLFtbXCLFi1wiLFwiyaNcIl0sW1wixYtkaVwiLFwiyaNldHLJlFwiXSx1XSx1LFtbXCJrXCIsXCJkXCIsXCJiXCIsXCJrXCIsXCJ5XCIsXCJmXCIsXCJtXCJdLFtcImvJlHNcIixcImR6b1wiLFwiYmxhXCIsXCJrdcmWXCIsXCJ5YXdcIixcImZpyZZcIixcIm1lbVwiXSxbXCJryZRzacmWYVwiLFwiZHpvyZZhXCIsXCJibGHJlmFcIixcImt1yZZhXCIsXCJ5YXdvyZZhXCIsXCJmacmWYVwiLFwibWVtbGXJlmFcIl0sW1wia8mUc1wiLFwiZHpvXCIsXCJibGFcIixcImt1yZZcIixcInlhd1wiLFwiZmnJllwiLFwibWVtXCJdXSx1LFtbXCJkXCIsXCJkXCIsXCJ0XCIsXCJhXCIsXCJkXCIsXCJtXCIsXCJzXCIsXCJkXCIsXCJhXCIsXCJrXCIsXCJhXCIsXCJkXCJdLFtcImR6dlwiLFwiZHpkXCIsXCJ0ZWRcIixcImFmyZRcIixcImRhbVwiLFwibWFzXCIsXCJzaWFcIixcImRlYVwiLFwiYW55XCIsXCJrZWxcIixcImFkZVwiLFwiZHptXCJdLFtcImR6b3ZlXCIsXCJkem9kemVcIixcInRlZG94ZVwiLFwiYWbJlGbEqWVcIixcImRhbWFcIixcIm1hc2FcIixcInNpYW1syZRtXCIsXCJkZWFzaWFtaW1lXCIsXCJhbnnJlG55yZRcIixcImtlbGVcIixcImFkZcmbbWVrcMmUeGVcIixcImR6b21lXCJdXSx1LFtbXCJIWVZcIixcIlnFi1wiXSx1LFtcIkhhZmkgWWVzdSBWYVwiLFwiWWVzdSDFi8mUbGlcIl1dLDEsWzYsMF0sW1wiTS9kL3l5XCIsXCJNTU0gZCAnbGlhJywgeVwiLFwiTU1NTSBkICdsaWEnIHlcIixcIkVFRUUsIE1NTU0gZCAnbGlhJyB5XCJdLFtcImEgJ2dhJyBoOm1tXCIsXCJhICdnYScgaDptbTpzc1wiLFwiYSAnZ2EnIGg6bW06c3MgelwiLFwiYSAnZ2EnIGg6bW06c3Mgenp6elwiXSxbXCJ7MH0gezF9XCIsdSx1LHVdLFtcIi5cIixcIixcIixcIjtcIixcIiVcIixcIitcIixcIi1cIixcIkVcIixcIsOXXCIsXCLigLBcIixcIuKInlwiLFwibW5uXCIsXCI6XCJdLFtcIiMsIyMwLiMjI1wiLFwiIywjIzAlXCIsXCLCpCMsIyMwLjAwXCIsXCIjRTBcIl0sXCJHSFNcIixcIkdI4oK1XCIsXCJnaGFuYSBzacmWaVwiLHtcIkFVRFwiOltcIkFVJFwiLFwiJFwiXSxcIkJZTlwiOlt1LFwi0YAuXCJdLFwiR0hTXCI6W1wiR0jigrVcIl0sXCJKUFlcIjpbXCJKUMKlXCIsXCLCpVwiXSxcIlRIQlwiOltcIuC4v1wiXSxcIlVTRFwiOltcIlVTJFwiLFwiJFwiXX0sXCJsdHJcIiwgcGx1cmFsXTtcbiJdfQ==