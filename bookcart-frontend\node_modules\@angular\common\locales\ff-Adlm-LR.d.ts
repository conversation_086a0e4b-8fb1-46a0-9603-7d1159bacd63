/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    BYN: (string | undefined)[];
    GNF: (string | undefined)[];
    JPY: string[];
    LRD: string[];
    NGN: string[];
    PGK: string[];
    PHP: string[];
    USD: string[];
    XAF: string[];
    XOF: string[];
} | undefined)[];
export default _default;
