@use 'sass:map';
@use '../../../theming/theming';
@use '../../../theming/inspection';
@use '../../token-utils';
@use '../../../style/sass-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, table);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    row-item-outline-width: 1px,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  @return (
    background-color: inspection.get-theme-color($theme, background, 'card'),

    header-headline-color: inspection.get-theme-color($theme, foreground, text),
    row-item-label-text-color: inspection.get-theme-color($theme, foreground, text),
    row-item-outline-color: inspection.get-theme-color($theme, foreground, divider),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  $cell-font-family: inspection.get-theme-typography($theme, body-2, font-family);
  $cell-line-height: inspection.get-theme-typography($theme, body-2, line-height);
  $cell-font-size: inspection.get-theme-typography($theme, body-2, font-size);
  $cell-font-weight: inspection.get-theme-typography($theme, body-2, font-weight);
  $cell-letter-spacing: inspection.get-theme-typography($theme, body-2, letter-spacing);

  @return (
    header-headline-font: inspection.get-theme-typography($theme, subtitle-2, font-family),
    header-headline-line-height: inspection.get-theme-typography($theme, subtitle-2, line-height),
    header-headline-size: inspection.get-theme-typography($theme, subtitle-2, font-size),
    header-headline-weight: inspection.get-theme-typography($theme, subtitle-2, font-weight),
    header-headline-tracking: inspection.get-theme-typography($theme, subtitle-2, letter-spacing),

    // Plain cells and footer cells have the same typography.
    row-item-label-text-font: $cell-font-family,
    row-item-label-text-line-height: $cell-line-height,
    row-item-label-text-size: $cell-font-size,
    row-item-label-text-weight: $cell-font-weight,
    row-item-label-text-tracking: $cell-letter-spacing,

    footer-supporting-text-font: $cell-font-family,
    footer-supporting-text-line-height: $cell-line-height,
    footer-supporting-text-size: $cell-font-size,
    footer-supporting-text-weight: $cell-font-weight,
    footer-supporting-text-tracking: $cell-letter-spacing,
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $scale: theming.clamp-density(inspection.get-theme-density($theme), -4);
  $header-scale: (
    0: 56px,
    -1: 52px,
    -2: 48px,
    -3: 44px,
    -4: 40px
  );
  $cell-scale: (
    0: 52px,
    -1: 48px,
    -2: 44px,
    -3: 40px,
    -4: 36px
  );

  @return (
    header-container-height: map.get($header-scale, $scale),
    footer-container-height: map.get($cell-scale, $scale),
    row-item-container-height: map.get($cell-scale, $scale),
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
          get-unthemable-tokens(),
          get-color-tokens(token-utils.$placeholder-color-config),
          get-typography-tokens(token-utils.$placeholder-typography-config),
          get-density-tokens(token-utils.$placeholder-density-config)
  );
}
