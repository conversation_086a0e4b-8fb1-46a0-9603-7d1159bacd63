{"version": 3, "file": "expansion.mjs", "sources": ["../../../../../../src/material/expansion/accordion-base.ts", "../../../../../../src/material/expansion/expansion-animations.ts", "../../../../../../src/material/expansion/expansion-panel-base.ts", "../../../../../../src/material/expansion/expansion-panel-content.ts", "../../../../../../src/material/expansion/expansion-panel.ts", "../../../../../../src/material/expansion/expansion-panel.html", "../../../../../../src/material/expansion/expansion-panel-header.ts", "../../../../../../src/material/expansion/expansion-panel-header.html", "../../../../../../src/material/expansion/accordion.ts", "../../../../../../src/material/expansion/expansion-module.ts", "../../../../../../src/material/expansion/expansion_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {InjectionToken} from '@angular/core';\nimport {CdkAccordion} from '@angular/cdk/accordion';\n\n/** MatAccordion's display modes. */\nexport type MatAccordionDisplayMode = 'default' | 'flat';\n\n/** MatAccordion's toggle positions. */\nexport type MatAccordionTogglePosition = 'before' | 'after';\n\n/**\n * Base interface for a `MatAccordion`.\n * @docs-private\n */\nexport interface MatAccordionBase extends CdkAccordion {\n  /** Whether the expansion indicator should be hidden. */\n  hideToggle: boolean;\n\n  /** Display mode used for all expansion panels in the accordion. */\n  displayMode: MatAccordionDisplayMode;\n\n  /** The position of the expansion indicator. */\n  togglePosition: MatAccordionTogglePosition;\n\n  /** Handles keyboard events coming in from the panel headers. */\n  _handleHeaderKeydown: (event: KeyboardEvent) => void;\n\n  /** Handles focus events on the panel headers. */\n  _handleHeaderFocus: (header: any) => void;\n}\n\n/**\n * <PERSON>ken used to provide a `MatAccordion` to `MatExpansionPanel`.\n * Used primarily to avoid circular imports between `MatAccordion` and `MatExpansionPanel`.\n */\nexport const MAT_ACCORDION = new InjectionToken<MatAccordionBase>('MAT_ACCORDION');\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {\n  animate,\n  AnimationTriggerMetadata,\n  state,\n  style,\n  transition,\n  trigger,\n} from '@angular/animations';\n\n/** Time and timing curve for expansion panel animations. */\n// Note: Keep this in sync with the Sass variable for the panel header animation.\nexport const EXPANSION_PANEL_ANIMATION_TIMING = '225ms cubic-bezier(0.4,0.0,0.2,1)';\n\n/**\n * Animations used by the Material expansion panel.\n *\n * A bug in angular animation's `state` when ViewContainers are moved using ViewContainerRef.move()\n * causes the animation state of moved components to become `void` upon exit, and not update again\n * upon reentry into the DOM.  This can lead a to situation for the expansion panel where the state\n * of the panel is `expanded` or `collapsed` but the animation state is `void`.\n *\n * To correctly handle animating to the next state, we animate between `void` and `collapsed` which\n * are defined to have the same styles. Since angular animates from the current styles to the\n * destination state's style definition, in situations where we are moving from `void`'s styles to\n * `collapsed` this acts a noop since no style values change.\n *\n * In the case where angular's animation state is out of sync with the expansion panel's state, the\n * expansion panel being `expanded` and angular animations being `void`, the animation from the\n * `expanded`'s effective styles (though in a `void` animation state) to the collapsed state will\n * occur as expected.\n *\n * Angular Bug: https://github.com/angular/angular/issues/18847\n *\n * @docs-private\n */\nexport const matExpansionAnimations: {\n  readonly indicatorRotate: AnimationTriggerMetadata;\n  readonly bodyExpansion: AnimationTriggerMetadata;\n} = {\n  /** Animation that rotates the indicator arrow. */\n  indicatorRotate: trigger('indicatorRotate', [\n    state('collapsed, void', style({transform: 'rotate(0deg)'})),\n    state('expanded', style({transform: 'rotate(180deg)'})),\n    transition(\n      'expanded <=> collapsed, void => collapsed',\n      animate(EXPANSION_PANEL_ANIMATION_TIMING),\n    ),\n  ]),\n  /** Animation that expands and collapses the panel content. */\n  bodyExpansion: trigger('bodyExpansion', [\n    state('collapsed, void', style({height: '0px', visibility: 'hidden'})),\n    // Clear the `visibility` while open, otherwise the content will be visible when placed in\n    // a parent that's `visibility: hidden`, because `visibility` doesn't apply to descendants\n    // that have a `visibility` of their own (see #27436).\n    state('expanded', style({height: '*', visibility: ''})),\n    transition(\n      'expanded <=> collapsed, void => collapsed',\n      animate(EXPANSION_PANEL_ANIMATION_TIMING),\n    ),\n  ]),\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {InjectionToken} from '@angular/core';\nimport {CdkAccordionItem} from '@angular/cdk/accordion';\n\n/**\n * Base interface for a `MatExpansionPanel`.\n * @docs-private\n */\nexport interface MatExpansionPanelBase extends CdkAccordionItem {\n  /** Whether the toggle indicator should be hidden. */\n  hideToggle: boolean;\n}\n\n/**\n * Token used to provide a `MatExpansionPanel` to `MatExpansionPanelContent`.\n * Used to avoid circular imports between `MatExpansionPanel` and `MatExpansionPanelContent`.\n */\nexport const MAT_EXPANSION_PANEL = new InjectionToken<MatExpansionPanelBase>('MAT_EXPANSION_PANEL');\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, TemplateRef, Inject, Optional} from '@angular/core';\nimport {MAT_EXPANSION_PANEL, MatExpansionPanelBase} from './expansion-panel-base';\n\n/**\n * Expansion panel content that will be rendered lazily\n * after the panel is opened for the first time.\n */\n@Directive({\n  selector: 'ng-template[matExpansionPanelContent]',\n  standalone: true,\n})\nexport class MatExpansionPanelContent {\n  constructor(\n    public _template: TemplateRef<any>,\n    @Inject(MAT_EXPANSION_PANEL) @Optional() public _expansionPanel?: MatExpansionPanelBase,\n  ) {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationEvent} from '@angular/animations';\nimport {CdkAccordionItem} from '@angular/cdk/accordion';\nimport {UniqueSelectionDispatcher} from '@angular/cdk/collections';\nimport {CdkPortalOutlet, TemplatePortal} from '@angular/cdk/portal';\nimport {DOCUMENT} from '@angular/common';\nimport {\n  AfterContentInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChild,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  Inject,\n  InjectionToken,\n  Input,\n  OnChanges,\n  OnDestroy,\n  Optional,\n  Output,\n  SimpleChanges,\n  SkipSelf,\n  ViewChild,\n  ViewContainerRef,\n  ViewEncapsulation,\n  booleanAttribute,\n  ANIMATION_MODULE_TYPE,\n} from '@angular/core';\nimport {Subject} from 'rxjs';\nimport {filter, startWith, take} from 'rxjs/operators';\nimport {MatAccordionBase, MatAccordionTogglePosition, MAT_ACCORDION} from './accordion-base';\nimport {matExpansionAnimations} from './expansion-animations';\nimport {MAT_EXPANSION_PANEL} from './expansion-panel-base';\nimport {MatExpansionPanelContent} from './expansion-panel-content';\n\n/** MatExpansionPanel's states. */\nexport type MatExpansionPanelState = 'expanded' | 'collapsed';\n\n/** Counter for generating unique element ids. */\nlet uniqueId = 0;\n\n/**\n * Object that can be used to override the default options\n * for all of the expansion panels in a module.\n */\nexport interface MatExpansionPanelDefaultOptions {\n  /** Height of the header while the panel is expanded. */\n  expandedHeight: string;\n\n  /** Height of the header while the panel is collapsed. */\n  collapsedHeight: string;\n\n  /** Whether the toggle indicator should be hidden. */\n  hideToggle: boolean;\n}\n\n/**\n * Injection token that can be used to configure the default\n * options for the expansion panel component.\n */\nexport const MAT_EXPANSION_PANEL_DEFAULT_OPTIONS =\n  new InjectionToken<MatExpansionPanelDefaultOptions>('MAT_EXPANSION_PANEL_DEFAULT_OPTIONS');\n\n/**\n * This component can be used as a single element to show expandable content, or as one of\n * multiple children of an element with the MatAccordion directive attached.\n */\n@Component({\n  styleUrl: 'expansion-panel.css',\n  selector: 'mat-expansion-panel',\n  exportAs: 'matExpansionPanel',\n  templateUrl: 'expansion-panel.html',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  animations: [matExpansionAnimations.bodyExpansion],\n  providers: [\n    // Provide MatAccordion as undefined to prevent nested expansion panels from registering\n    // to the same accordion.\n    {provide: MAT_ACCORDION, useValue: undefined},\n    {provide: MAT_EXPANSION_PANEL, useExisting: MatExpansionPanel},\n  ],\n  host: {\n    'class': 'mat-expansion-panel',\n    '[class.mat-expanded]': 'expanded',\n    '[class._mat-animation-noopable]': '_animationsDisabled',\n    '[class.mat-expansion-panel-spacing]': '_hasSpacing()',\n  },\n  standalone: true,\n  imports: [CdkPortalOutlet],\n})\nexport class MatExpansionPanel\n  extends CdkAccordionItem\n  implements AfterContentInit, OnChanges, OnDestroy\n{\n  protected _animationsDisabled: boolean;\n  private _document: Document;\n\n  /** Whether the toggle indicator should be hidden. */\n  @Input({transform: booleanAttribute})\n  get hideToggle(): boolean {\n    return this._hideToggle || (this.accordion && this.accordion.hideToggle);\n  }\n  set hideToggle(value: boolean) {\n    this._hideToggle = value;\n  }\n  private _hideToggle = false;\n\n  /** The position of the expansion indicator. */\n  @Input()\n  get togglePosition(): MatAccordionTogglePosition {\n    return this._togglePosition || (this.accordion && this.accordion.togglePosition);\n  }\n  set togglePosition(value: MatAccordionTogglePosition) {\n    this._togglePosition = value;\n  }\n  private _togglePosition: MatAccordionTogglePosition;\n\n  /** An event emitted after the body's expansion animation happens. */\n  @Output() readonly afterExpand = new EventEmitter<void>();\n\n  /** An event emitted after the body's collapse animation happens. */\n  @Output() readonly afterCollapse = new EventEmitter<void>();\n\n  /** Stream that emits for changes in `@Input` properties. */\n  readonly _inputChanges = new Subject<SimpleChanges>();\n\n  /** Optionally defined accordion the expansion panel belongs to. */\n  override accordion: MatAccordionBase;\n\n  /** Content that will be rendered lazily. */\n  @ContentChild(MatExpansionPanelContent) _lazyContent: MatExpansionPanelContent;\n\n  /** Element containing the panel's user-provided content. */\n  @ViewChild('body') _body: ElementRef<HTMLElement>;\n\n  /** Portal holding the user's content. */\n  _portal: TemplatePortal;\n\n  /** ID for the associated header element. Used for a11y labelling. */\n  _headerId = `mat-expansion-panel-header-${uniqueId++}`;\n\n  constructor(\n    @Optional() @SkipSelf() @Inject(MAT_ACCORDION) accordion: MatAccordionBase,\n    _changeDetectorRef: ChangeDetectorRef,\n    _uniqueSelectionDispatcher: UniqueSelectionDispatcher,\n    private _viewContainerRef: ViewContainerRef,\n    @Inject(DOCUMENT) _document: any,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) public _animationMode: string,\n    @Inject(MAT_EXPANSION_PANEL_DEFAULT_OPTIONS)\n    @Optional()\n    defaultOptions?: MatExpansionPanelDefaultOptions,\n  ) {\n    super(accordion, _changeDetectorRef, _uniqueSelectionDispatcher);\n    this.accordion = accordion;\n    this._document = _document;\n    this._animationsDisabled = _animationMode === 'NoopAnimations';\n\n    if (defaultOptions) {\n      this.hideToggle = defaultOptions.hideToggle;\n    }\n  }\n\n  /** Determines whether the expansion panel should have spacing between it and its siblings. */\n  _hasSpacing(): boolean {\n    if (this.accordion) {\n      return this.expanded && this.accordion.displayMode === 'default';\n    }\n    return false;\n  }\n\n  /** Gets the expanded state string. */\n  _getExpandedState(): MatExpansionPanelState {\n    return this.expanded ? 'expanded' : 'collapsed';\n  }\n\n  /** Toggles the expanded state of the expansion panel. */\n  override toggle(): void {\n    this.expanded = !this.expanded;\n  }\n\n  /** Sets the expanded state of the expansion panel to false. */\n  override close(): void {\n    this.expanded = false;\n  }\n\n  /** Sets the expanded state of the expansion panel to true. */\n  override open(): void {\n    this.expanded = true;\n  }\n\n  ngAfterContentInit() {\n    if (this._lazyContent && this._lazyContent._expansionPanel === this) {\n      // Render the content as soon as the panel becomes open.\n      this.opened\n        .pipe(\n          startWith(null),\n          filter(() => this.expanded && !this._portal),\n          take(1),\n        )\n        .subscribe(() => {\n          this._portal = new TemplatePortal(this._lazyContent._template, this._viewContainerRef);\n        });\n    }\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    this._inputChanges.next(changes);\n  }\n\n  override ngOnDestroy() {\n    super.ngOnDestroy();\n    this._inputChanges.complete();\n  }\n\n  /** Checks whether the expansion panel's content contains the currently-focused element. */\n  _containsFocus(): boolean {\n    if (this._body) {\n      const focusedElement = this._document.activeElement;\n      const bodyElement = this._body.nativeElement;\n      return focusedElement === bodyElement || bodyElement.contains(focusedElement);\n    }\n\n    return false;\n  }\n\n  /** Called when the expansion animation has started. */\n  protected _animationStarted(event: AnimationEvent) {\n    if (!isInitialAnimation(event) && !this._animationsDisabled && this._body) {\n      // Prevent the user from tabbing into the content while it's animating.\n      // TODO(crisbeto): maybe use `inert` to prevent focus from entering while closed as well\n      // instead of `visibility`? Will allow us to clean up some code but needs more testing.\n      this._body?.nativeElement.setAttribute('inert', '');\n    }\n  }\n\n  /** Called when the expansion animation has finished. */\n  protected _animationDone(event: AnimationEvent) {\n    if (!isInitialAnimation(event)) {\n      if (event.toState === 'expanded') {\n        this.afterExpand.emit();\n      } else if (event.toState === 'collapsed') {\n        this.afterCollapse.emit();\n      }\n\n      // Re-enable tabbing once the animation is finished.\n      if (!this._animationsDisabled && this._body) {\n        this._body.nativeElement.removeAttribute('inert');\n      }\n    }\n  }\n}\n\n/** Checks whether an animation is the initial setup animation. */\nfunction isInitialAnimation(event: AnimationEvent): boolean {\n  return event.fromState === 'void';\n}\n\n/**\n * Actions of a `<mat-expansion-panel>`.\n */\n@Directive({\n  selector: 'mat-action-row',\n  host: {\n    class: 'mat-action-row',\n  },\n  standalone: true,\n})\nexport class MatExpansionPanelActionRow {}\n", "<ng-content select=\"mat-expansion-panel-header\"></ng-content>\n<div class=\"mat-expansion-panel-content\"\n     role=\"region\"\n     [@bodyExpansion]=\"_getExpandedState()\"\n     (@bodyExpansion.start)=\"_animationStarted($event)\"\n     (@bodyExpansion.done)=\"_animationDone($event)\"\n     [attr.aria-labelledby]=\"_headerId\"\n     [id]=\"id\"\n     #body>\n  <div class=\"mat-expansion-panel-body\">\n    <ng-content></ng-content>\n    <ng-template [cdkPortalOutlet]=\"_portal\"></ng-template>\n  </div>\n  <ng-content select=\"mat-action-row\"></ng-content>\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {FocusableOption, FocusMonitor, FocusOrigin} from '@angular/cdk/a11y';\nimport {ENTER, hasModifier<PERSON>ey, SPACE} from '@angular/cdk/keycodes';\nimport {\n  AfterViewInit,\n  Attribute,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  Directive,\n  ElementRef,\n  Host,\n  Inject,\n  Input,\n  numberAttribute,\n  OnDestroy,\n  Optional,\n  ViewEncapsulation,\n  ANIMATION_MODULE_TYPE,\n} from '@angular/core';\nimport {EMPTY, merge, Subscription} from 'rxjs';\nimport {filter} from 'rxjs/operators';\nimport {MatAccordionTogglePosition} from './accordion-base';\nimport {matExpansionAnimations} from './expansion-animations';\nimport {\n  MatExpansionPanel,\n  MatExpansionPanelDefaultOptions,\n  MAT_EXPANSION_PANEL_DEFAULT_OPTIONS,\n} from './expansion-panel';\n\n/**\n * Header element of a `<mat-expansion-panel>`.\n */\n@Component({\n  selector: 'mat-expansion-panel-header',\n  styleUrl: 'expansion-panel-header.css',\n  templateUrl: 'expansion-panel-header.html',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  animations: [matExpansionAnimations.indicatorRotate],\n  host: {\n    'class': 'mat-expansion-panel-header mat-focus-indicator',\n    'role': 'button',\n    '[attr.id]': 'panel._headerId',\n    '[attr.tabindex]': 'disabled ? -1 : tabIndex',\n    '[attr.aria-controls]': '_getPanelId()',\n    '[attr.aria-expanded]': '_isExpanded()',\n    '[attr.aria-disabled]': 'panel.disabled',\n    '[class.mat-expanded]': '_isExpanded()',\n    '[class.mat-expansion-toggle-indicator-after]': `_getTogglePosition() === 'after'`,\n    '[class.mat-expansion-toggle-indicator-before]': `_getTogglePosition() === 'before'`,\n    '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n    '[style.height]': '_getHeaderHeight()',\n    '(click)': '_toggle()',\n    '(keydown)': '_keydown($event)',\n  },\n  standalone: true,\n})\nexport class MatExpansionPanelHeader implements AfterViewInit, OnDestroy, FocusableOption {\n  private _parentChangeSubscription = Subscription.EMPTY;\n\n  constructor(\n    @Host() public panel: MatExpansionPanel,\n    private _element: ElementRef,\n    private _focusMonitor: FocusMonitor,\n    private _changeDetectorRef: ChangeDetectorRef,\n    @Inject(MAT_EXPANSION_PANEL_DEFAULT_OPTIONS)\n    @Optional()\n    defaultOptions?: MatExpansionPanelDefaultOptions,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) public _animationMode?: string,\n    @Attribute('tabindex') tabIndex?: string,\n  ) {\n    const accordionHideToggleChange = panel.accordion\n      ? panel.accordion._stateChanges.pipe(\n          filter(changes => !!(changes['hideToggle'] || changes['togglePosition'])),\n        )\n      : EMPTY;\n    this.tabIndex = parseInt(tabIndex || '') || 0;\n\n    // Since the toggle state depends on an @Input on the panel, we\n    // need to subscribe and trigger change detection manually.\n    this._parentChangeSubscription = merge(\n      panel.opened,\n      panel.closed,\n      accordionHideToggleChange,\n      panel._inputChanges.pipe(\n        filter(changes => {\n          return !!(changes['hideToggle'] || changes['disabled'] || changes['togglePosition']);\n        }),\n      ),\n    ).subscribe(() => this._changeDetectorRef.markForCheck());\n\n    // Avoids focus being lost if the panel contained the focused element and was closed.\n    panel.closed\n      .pipe(filter(() => panel._containsFocus()))\n      .subscribe(() => _focusMonitor.focusVia(_element, 'program'));\n\n    if (defaultOptions) {\n      this.expandedHeight = defaultOptions.expandedHeight;\n      this.collapsedHeight = defaultOptions.collapsedHeight;\n    }\n  }\n\n  /** Height of the header while the panel is expanded. */\n  @Input() expandedHeight: string;\n\n  /** Height of the header while the panel is collapsed. */\n  @Input() collapsedHeight: string;\n\n  /** Tab index of the header. */\n  @Input({\n    transform: (value: unknown) => (value == null ? 0 : numberAttribute(value)),\n  })\n  tabIndex: number = 0;\n\n  /**\n   * Whether the associated panel is disabled. Implemented as a part of `FocusableOption`.\n   * @docs-private\n   */\n  get disabled(): boolean {\n    return this.panel.disabled;\n  }\n\n  /** Toggles the expanded state of the panel. */\n  _toggle(): void {\n    if (!this.disabled) {\n      this.panel.toggle();\n    }\n  }\n\n  /** Gets whether the panel is expanded. */\n  _isExpanded(): boolean {\n    return this.panel.expanded;\n  }\n\n  /** Gets the expanded state string of the panel. */\n  _getExpandedState(): string {\n    return this.panel._getExpandedState();\n  }\n\n  /** Gets the panel id. */\n  _getPanelId(): string {\n    return this.panel.id;\n  }\n\n  /** Gets the toggle position for the header. */\n  _getTogglePosition(): MatAccordionTogglePosition {\n    return this.panel.togglePosition;\n  }\n\n  /** Gets whether the expand indicator should be shown. */\n  _showToggle(): boolean {\n    return !this.panel.hideToggle && !this.panel.disabled;\n  }\n\n  /**\n   * Gets the current height of the header. Null if no custom height has been\n   * specified, and if the default height from the stylesheet should be used.\n   */\n  _getHeaderHeight(): string | null {\n    const isExpanded = this._isExpanded();\n    if (isExpanded && this.expandedHeight) {\n      return this.expandedHeight;\n    } else if (!isExpanded && this.collapsedHeight) {\n      return this.collapsedHeight;\n    }\n    return null;\n  }\n\n  /** Handle keydown event calling to toggle() if appropriate. */\n  _keydown(event: KeyboardEvent) {\n    switch (event.keyCode) {\n      // Toggle for space and enter keys.\n      case SPACE:\n      case ENTER:\n        if (!hasModifierKey(event)) {\n          event.preventDefault();\n          this._toggle();\n        }\n\n        break;\n      default:\n        if (this.panel.accordion) {\n          this.panel.accordion._handleHeaderKeydown(event);\n        }\n\n        return;\n    }\n  }\n\n  /**\n   * Focuses the panel header. Implemented as a part of `FocusableOption`.\n   * @param origin Origin of the action that triggered the focus.\n   * @docs-private\n   */\n  focus(origin?: FocusOrigin, options?: FocusOptions) {\n    if (origin) {\n      this._focusMonitor.focusVia(this._element, origin, options);\n    } else {\n      this._element.nativeElement.focus(options);\n    }\n  }\n\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this._element).subscribe(origin => {\n      if (origin && this.panel.accordion) {\n        this.panel.accordion._handleHeaderFocus(this);\n      }\n    });\n  }\n\n  ngOnDestroy() {\n    this._parentChangeSubscription.unsubscribe();\n    this._focusMonitor.stopMonitoring(this._element);\n  }\n}\n\n/**\n * Description element of a `<mat-expansion-panel-header>`.\n */\n@Directive({\n  selector: 'mat-panel-description',\n  host: {\n    class: 'mat-expansion-panel-header-description',\n  },\n  standalone: true,\n})\nexport class MatExpansionPanelDescription {}\n\n/**\n * Title element of a `<mat-expansion-panel-header>`.\n */\n@Directive({\n  selector: 'mat-panel-title',\n  host: {\n    class: 'mat-expansion-panel-header-title',\n  },\n  standalone: true,\n})\nexport class MatExpansionPanelTitle {}\n", "<span class=\"mat-content\" [class.mat-content-hide-toggle]=\"!_showToggle()\">\n  <ng-content select=\"mat-panel-title\"></ng-content>\n  <ng-content select=\"mat-panel-description\"></ng-content>\n  <ng-content></ng-content>\n</span>\n\n@if (_showToggle()) {\n  <span [@indicatorRotate]=\"_getExpandedState()\" class=\"mat-expansion-indicator\">\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      viewBox=\"0 -960 960 960\"\n      aria-hidden=\"true\"\n      focusable=\"false\">\n      <path d=\"M480-345 240-585l56-56 184 184 184-184 56 56-240 240Z\"/>\n    </svg>\n  </span>\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Directive,\n  Input,\n  ContentChildren,\n  QueryList,\n  AfterContentInit,\n  OnDestroy,\n  booleanAttribute,\n} from '@angular/core';\nimport {CdkAccordion} from '@angular/cdk/accordion';\nimport {FocusKeyManager} from '@angular/cdk/a11y';\nimport {startWith} from 'rxjs/operators';\nimport {\n  MAT_ACCORDION,\n  MatAccordionBase,\n  MatAccordionDisplayMode,\n  MatAccordionTogglePosition,\n} from './accordion-base';\nimport {MatExpansionPanelHeader} from './expansion-panel-header';\n\n/**\n * Directive for a Material Design Accordion.\n */\n@Directive({\n  selector: 'mat-accordion',\n  exportAs: 'matAccordion',\n  providers: [\n    {\n      provide: MAT_ACCORDION,\n      useExisting: MatAccordion,\n    },\n  ],\n  host: {\n    class: 'mat-accordion',\n    // Class binding which is only used by the test harness as there is no other\n    // way for the harness to detect if multiple panel support is enabled.\n    '[class.mat-accordion-multi]': 'this.multi',\n  },\n  standalone: true,\n})\nexport class MatAccordion\n  extends CdkAccordion\n  implements MatAccordionBase, AfterContentInit, OnDestroy\n{\n  private _keyManager: FocusKeyManager<MatExpansionPanelHeader>;\n\n  /** Headers belonging to this accordion. */\n  private _ownHeaders = new QueryList<MatExpansionPanelHeader>();\n\n  /** All headers inside the accordion. Includes headers inside nested accordions. */\n  @ContentChildren(MatExpansionPanelHeader, {descendants: true})\n  _headers: QueryList<MatExpansionPanelHeader>;\n\n  /** Whether the expansion indicator should be hidden. */\n  @Input({transform: booleanAttribute})\n  hideToggle: boolean = false;\n\n  /**\n   * Display mode used for all expansion panels in the accordion. Currently two display\n   * modes exist:\n   *  default - a gutter-like spacing is placed around any expanded panel, placing the expanded\n   *     panel at a different elevation from the rest of the accordion.\n   *  flat - no spacing is placed around expanded panels, showing all panels at the same\n   *     elevation.\n   */\n  @Input() displayMode: MatAccordionDisplayMode = 'default';\n\n  /** The position of the expansion indicator. */\n  @Input() togglePosition: MatAccordionTogglePosition = 'after';\n\n  ngAfterContentInit() {\n    this._headers.changes\n      .pipe(startWith(this._headers))\n      .subscribe((headers: QueryList<MatExpansionPanelHeader>) => {\n        this._ownHeaders.reset(headers.filter(header => header.panel.accordion === this));\n        this._ownHeaders.notifyOnChanges();\n      });\n\n    this._keyManager = new FocusKeyManager(this._ownHeaders).withWrap().withHomeAndEnd();\n  }\n\n  /** Handles keyboard events coming in from the panel headers. */\n  _handleHeaderKeydown(event: KeyboardEvent) {\n    this._keyManager.onKeydown(event);\n  }\n\n  _handleHeaderFocus(header: MatExpansionPanelHeader) {\n    this._keyManager.updateActiveItem(header);\n  }\n\n  override ngOnDestroy() {\n    super.ngOnDestroy();\n    this._keyManager?.destroy();\n    this._ownHeaders.destroy();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {CdkAccordionModule} from '@angular/cdk/accordion';\nimport {PortalModule} from '@angular/cdk/portal';\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '@angular/material/core';\nimport {MatAccordion} from './accordion';\nimport {MatExpansionPanel, MatExpansionPanelActionRow} from './expansion-panel';\nimport {MatExpansionPanelContent} from './expansion-panel-content';\nimport {\n  MatExpansionPanelDescription,\n  MatExpansionPanelHeader,\n  MatExpansionPanelTitle,\n} from './expansion-panel-header';\n\n@NgModule({\n  imports: [\n    MatCommonModule,\n    CdkAccordionModule,\n    PortalModule,\n    MatAccordion,\n    MatExpansionPanel,\n    MatExpansionPanelActionRow,\n    MatExpansionPanelHeader,\n    MatExpansionPanelTitle,\n    MatExpansionPanelDescription,\n    MatExpansionPanelContent,\n  ],\n  exports: [\n    MatAccordion,\n    MatExpansionPanel,\n    MatExpansionPanelActionRow,\n    MatExpansionPanelHeader,\n    MatExpansionPanelTitle,\n    MatExpansionPanelDescription,\n    MatExpansionPanelContent,\n  ],\n})\nexport class MatExpansionModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["i1.MatExpansionPanel"], "mappings": ";;;;;;;;;;;;;;AAsCA;;;AAGG;MACU,aAAa,GAAG,IAAI,cAAc,CAAmB,eAAe;;AC1BjF;AACA;AACO,MAAM,gCAAgC,GAAG,oCAAoC;AAEpF;;;;;;;;;;;;;;;;;;;;;AAqBG;AACU,MAAA,sBAAsB,GAG/B;;AAEF,IAAA,eAAe,EAAE,OAAO,CAAC,iBAAiB,EAAE;QAC1C,KAAK,CAAC,iBAAiB,EAAE,KAAK,CAAC,EAAC,SAAS,EAAE,cAAc,EAAC,CAAC,CAAC;QAC5D,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAC,CAAC;AACvD,QAAA,UAAU,CACR,2CAA2C,EAC3C,OAAO,CAAC,gCAAgC,CAAC,CAC1C;KACF,CAAC;;AAEF,IAAA,aAAa,EAAE,OAAO,CAAC,eAAe,EAAE;AACtC,QAAA,KAAK,CAAC,iBAAiB,EAAE,KAAK,CAAC,EAAC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAC,CAAC,CAAC;;;;AAItE,QAAA,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,EAAC,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,EAAE,EAAC,CAAC,CAAC;AACvD,QAAA,UAAU,CACR,2CAA2C,EAC3C,OAAO,CAAC,gCAAgC,CAAC,CAC1C;KACF,CAAC;;;AC9CJ;;;AAGG;MACU,mBAAmB,GAAG,IAAI,cAAc,CAAwB,qBAAqB;;ACblG;;;AAGG;MAKU,wBAAwB,CAAA;IACnC,WACS,CAAA,SAA2B,EACc,eAAuC,EAAA;QADhF,IAAS,CAAA,SAAA,GAAT,SAAS,CAAkB;QACc,IAAe,CAAA,eAAA,GAAf,eAAe,CAAwB;KACrF;AAJO,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,wBAAwB,6CAGzB,mBAAmB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAHlB,wBAAwB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,uCAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAxB,wBAAwB,EAAA,UAAA,EAAA,CAAA;kBAJpC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,uCAAuC;AACjD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BAII,MAAM;2BAAC,mBAAmB,CAAA;;0BAAG,QAAQ;;;ACyB1C;AACA,IAAI,QAAQ,GAAG,CAAC,CAAC;AAiBjB;;;AAGG;MACU,mCAAmC,GAC9C,IAAI,cAAc,CAAkC,qCAAqC,EAAE;AAE7F;;;AAGG;AAwBG,MAAO,iBACX,SAAQ,gBAAgB,CAAA;;AAOxB,IAAA,IACI,UAAU,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;KAC1E;IACD,IAAI,UAAU,CAAC,KAAc,EAAA;AAC3B,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;KAC1B;;AAID,IAAA,IACI,cAAc,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,eAAe,KAAK,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;KAClF;IACD,IAAI,cAAc,CAAC,KAAiC,EAAA;AAClD,QAAA,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;KAC9B;AA2BD,IAAA,WAAA,CACiD,SAA2B,EAC1E,kBAAqC,EACrC,0BAAqD,EAC7C,iBAAmC,EACzB,SAAc,EACkB,cAAsB,EAGxE,cAAgD,EAAA;AAEhD,QAAA,KAAK,CAAC,SAAS,EAAE,kBAAkB,EAAE,0BAA0B,CAAC,CAAC;QAPzD,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAkB;QAEO,IAAc,CAAA,cAAA,GAAd,cAAc,CAAQ;QA1ClE,IAAW,CAAA,WAAA,GAAG,KAAK,CAAC;;AAaT,QAAA,IAAA,CAAA,WAAW,GAAG,IAAI,YAAY,EAAQ,CAAC;;AAGvC,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,YAAY,EAAQ,CAAC;;AAGnD,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,OAAO,EAAiB,CAAC;;AAetD,QAAA,IAAA,CAAA,SAAS,GAAG,CAAA,2BAAA,EAA8B,QAAQ,EAAE,EAAE,CAAC;AAcrD,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC3B,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC3B,QAAA,IAAI,CAAC,mBAAmB,GAAG,cAAc,KAAK,gBAAgB,CAAC;QAE/D,IAAI,cAAc,EAAE;AAClB,YAAA,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;SAC7C;KACF;;IAGD,WAAW,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,KAAK,SAAS,CAAC;SAClE;AACD,QAAA,OAAO,KAAK,CAAC;KACd;;IAGD,iBAAiB,GAAA;QACf,OAAO,IAAI,CAAC,QAAQ,GAAG,UAAU,GAAG,WAAW,CAAC;KACjD;;IAGQ,MAAM,GAAA;AACb,QAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC;KAChC;;IAGQ,KAAK,GAAA;AACZ,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;KACvB;;IAGQ,IAAI,GAAA;AACX,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;KACtB;IAED,kBAAkB,GAAA;AAChB,QAAA,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,eAAe,KAAK,IAAI,EAAE;;AAEnE,YAAA,IAAI,CAAC,MAAM;iBACR,IAAI,CACH,SAAS,CAAC,IAAI,CAAC,EACf,MAAM,CAAC,MAAM,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAC5C,IAAI,CAAC,CAAC,CAAC,CACR;iBACA,SAAS,CAAC,MAAK;AACd,gBAAA,IAAI,CAAC,OAAO,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;AACzF,aAAC,CAAC,CAAC;SACN;KACF;AAED,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KAClC;IAEQ,WAAW,GAAA;QAClB,KAAK,CAAC,WAAW,EAAE,CAAC;AACpB,QAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;KAC/B;;IAGD,cAAc,GAAA;AACZ,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;AACpD,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;YAC7C,OAAO,cAAc,KAAK,WAAW,IAAI,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;SAC/E;AAED,QAAA,OAAO,KAAK,CAAC;KACd;;AAGS,IAAA,iBAAiB,CAAC,KAAqB,EAAA;AAC/C,QAAA,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,KAAK,EAAE;;;;YAIzE,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;SACrD;KACF;;AAGS,IAAA,cAAc,CAAC,KAAqB,EAAA;AAC5C,QAAA,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE;AAC9B,YAAA,IAAI,KAAK,CAAC,OAAO,KAAK,UAAU,EAAE;AAChC,gBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;aACzB;AAAM,iBAAA,IAAI,KAAK,CAAC,OAAO,KAAK,WAAW,EAAE;AACxC,gBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;aAC3B;;YAGD,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,KAAK,EAAE;gBAC3C,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;aACnD;SACF;KACF;AA/JU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,kBAoDM,aAAa,EAAA,QAAA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,yBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAIrC,QAAQ,EACI,EAAA,EAAA,KAAA,EAAA,qBAAqB,6BACjC,mCAAmC,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGA1DlC,iBAAiB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAQT,gBAAgB,CAvBxB,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,aAAA,EAAA,eAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,oBAAA,EAAA,UAAA,EAAA,+BAAA,EAAA,qBAAA,EAAA,mCAAA,EAAA,eAAA,EAAA,EAAA,cAAA,EAAA,qBAAA,EAAA,EAAA,SAAA,EAAA;;;AAGT,YAAA,EAAC,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,SAAS,EAAC;AAC7C,YAAA,EAAC,OAAO,EAAE,mBAAmB,EAAE,WAAW,EAAE,iBAAiB,EAAC;SAC/D,EAkDa,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,cAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,wBAAwB,EC3IxC,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,OAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,MAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,mkBAeA,EDkFY,MAAA,EAAA,CAAA,u7EAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,eAAe,mIAdb,CAAC,sBAAsB,CAAC,aAAa,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAgBvC,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAvB7B,SAAS;AAEE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,qBAAqB,YACrB,mBAAmB,EAAA,aAAA,EAEd,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,cACnC,CAAC,sBAAsB,CAAC,aAAa,CAAC,EACvC,SAAA,EAAA;;;AAGT,wBAAA,EAAC,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,SAAS,EAAC;AAC7C,wBAAA,EAAC,OAAO,EAAE,mBAAmB,EAAE,WAAW,mBAAmB,EAAC;qBAC/D,EACK,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,qBAAqB;AAC9B,wBAAA,sBAAsB,EAAE,UAAU;AAClC,wBAAA,iCAAiC,EAAE,qBAAqB;AACxD,wBAAA,qCAAqC,EAAE,eAAe;AACvD,qBAAA,EAAA,UAAA,EACW,IAAI,EAAA,OAAA,EACP,CAAC,eAAe,CAAC,EAAA,QAAA,EAAA,mkBAAA,EAAA,MAAA,EAAA,CAAA,u7EAAA,CAAA,EAAA,CAAA;;0BAsDvB,QAAQ;;0BAAI,QAAQ;;0BAAI,MAAM;2BAAC,aAAa,CAAA;;0BAI5C,MAAM;2BAAC,QAAQ,CAAA;;0BACf,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;;0BACxC,MAAM;2BAAC,mCAAmC,CAAA;;0BAC1C,QAAQ;yCAlDP,UAAU,EAAA,CAAA;sBADb,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAWhC,cAAc,EAAA,CAAA;sBADjB,KAAK;gBAUa,WAAW,EAAA,CAAA;sBAA7B,MAAM;gBAGY,aAAa,EAAA,CAAA;sBAA/B,MAAM;gBASiC,YAAY,EAAA,CAAA;sBAAnD,YAAY;uBAAC,wBAAwB,CAAA;gBAGnB,KAAK,EAAA,CAAA;sBAAvB,SAAS;uBAAC,MAAM,CAAA;;AAuHnB;AACA,SAAS,kBAAkB,CAAC,KAAqB,EAAA;AAC/C,IAAA,OAAO,KAAK,CAAC,SAAS,KAAK,MAAM,CAAC;AACpC,CAAC;AAED;;AAEG;MAQU,0BAA0B,CAAA;8GAA1B,0BAA0B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAA1B,0BAA0B,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAA1B,0BAA0B,EAAA,UAAA,EAAA,CAAA;kBAPtC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,gBAAgB;AACxB,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;;AE9OD;;AAEG;MA0BU,uBAAuB,CAAA;AAGlC,IAAA,WAAA,CACiB,KAAwB,EAC/B,QAAoB,EACpB,aAA2B,EAC3B,kBAAqC,EAG7C,cAAgD,EACE,cAAuB,EAClD,QAAiB,EAAA;QARzB,IAAK,CAAA,KAAA,GAAL,KAAK,CAAmB;QAC/B,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAY;QACpB,IAAa,CAAA,aAAA,GAAb,aAAa,CAAc;QAC3B,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAmB;QAIK,IAAc,CAAA,cAAA,GAAd,cAAc,CAAS;AAVnE,QAAA,IAAA,CAAA,yBAAyB,GAAG,YAAY,CAAC,KAAK,CAAC;;QAsDvD,IAAQ,CAAA,QAAA,GAAW,CAAC,CAAC;AAzCnB,QAAA,MAAM,yBAAyB,GAAG,KAAK,CAAC,SAAS;AAC/C,cAAE,KAAK,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAChC,MAAM,CAAC,OAAO,IAAI,CAAC,EAAE,OAAO,CAAC,YAAY,CAAC,IAAI,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAC1E;cACD,KAAK,CAAC;QACV,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC;;;QAI9C,IAAI,CAAC,yBAAyB,GAAG,KAAK,CACpC,KAAK,CAAC,MAAM,EACZ,KAAK,CAAC,MAAM,EACZ,yBAAyB,EACzB,KAAK,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,OAAO,IAAG;AACf,YAAA,OAAO,CAAC,EAAE,OAAO,CAAC,YAAY,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC;AACvF,SAAC,CAAC,CACH,CACF,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC,CAAC;;AAG1D,QAAA,KAAK,CAAC,MAAM;aACT,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC;AAC1C,aAAA,SAAS,CAAC,MAAM,aAAa,CAAC,QAAQ,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC;QAEhE,IAAI,cAAc,EAAE;AAClB,YAAA,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC,cAAc,CAAC;AACpD,YAAA,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC,eAAe,CAAC;SACvD;KACF;AAcD;;;AAGG;AACH,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;KAC5B;;IAGD,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;SACrB;KACF;;IAGD,WAAW,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;KAC5B;;IAGD,iBAAiB,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;KACvC;;IAGD,WAAW,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;KACtB;;IAGD,kBAAkB,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;KAClC;;IAGD,WAAW,GAAA;AACT,QAAA,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;KACvD;AAED;;;AAGG;IACH,gBAAgB,GAAA;AACd,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;AACtC,QAAA,IAAI,UAAU,IAAI,IAAI,CAAC,cAAc,EAAE;YACrC,OAAO,IAAI,CAAC,cAAc,CAAC;SAC5B;AAAM,aAAA,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,eAAe,EAAE;YAC9C,OAAO,IAAI,CAAC,eAAe,CAAC;SAC7B;AACD,QAAA,OAAO,IAAI,CAAC;KACb;;AAGD,IAAA,QAAQ,CAAC,KAAoB,EAAA;AAC3B,QAAA,QAAQ,KAAK,CAAC,OAAO;;AAEnB,YAAA,KAAK,KAAK,CAAC;AACX,YAAA,KAAK,KAAK;AACR,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;oBAC1B,KAAK,CAAC,cAAc,EAAE,CAAC;oBACvB,IAAI,CAAC,OAAO,EAAE,CAAC;iBAChB;gBAED,MAAM;AACR,YAAA;AACE,gBAAA,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;oBACxB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;iBAClD;gBAED,OAAO;SACV;KACF;AAED;;;;AAIG;IACH,KAAK,CAAC,MAAoB,EAAE,OAAsB,EAAA;QAChD,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;SAC7D;aAAM;YACL,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;SAC5C;KACF;IAED,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,MAAM,IAAG;YAC3D,IAAI,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;gBAClC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;aAC/C;AACH,SAAC,CAAC,CAAC;KACJ;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,yBAAyB,CAAC,WAAW,EAAE,CAAC;QAC7C,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KAClD;AA5JU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,uBAAuB,EAQxB,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAA,iBAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,YAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,mCAAmC,EAGvB,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,qBAAqB,6BAC9B,UAAU,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAZZ,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,uBAAuB,EAqDrB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,4BAAA,EAAA,MAAA,EAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,CAAC,KAAc,MAAM,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,CCtH/E,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,WAAA,EAAA,SAAA,EAAA,kBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,SAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,0BAAA,EAAA,oBAAA,EAAA,eAAA,EAAA,oBAAA,EAAA,eAAA,EAAA,oBAAA,EAAA,gBAAA,EAAA,oBAAA,EAAA,eAAA,EAAA,4CAAA,EAAA,kCAAA,EAAA,6CAAA,EAAA,mCAAA,EAAA,+BAAA,EAAA,uCAAA,EAAA,cAAA,EAAA,oBAAA,EAAA,EAAA,cAAA,EAAA,gDAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,olBAiBA,ED6Bc,MAAA,EAAA,CAAA,mlHAAA,CAAA,EAAA,UAAA,EAAA,CAAC,sBAAsB,CAAC,eAAe,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAmBzC,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBAzBnC,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,4BAA4B,EAGvB,aAAA,EAAA,iBAAiB,CAAC,IAAI,mBACpB,uBAAuB,CAAC,MAAM,EAAA,UAAA,EACnC,CAAC,sBAAsB,CAAC,eAAe,CAAC,EAC9C,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,gDAAgD;AACzD,wBAAA,MAAM,EAAE,QAAQ;AAChB,wBAAA,WAAW,EAAE,iBAAiB;AAC9B,wBAAA,iBAAiB,EAAE,0BAA0B;AAC7C,wBAAA,sBAAsB,EAAE,eAAe;AACvC,wBAAA,sBAAsB,EAAE,eAAe;AACvC,wBAAA,sBAAsB,EAAE,gBAAgB;AACxC,wBAAA,sBAAsB,EAAE,eAAe;AACvC,wBAAA,8CAA8C,EAAE,CAAkC,gCAAA,CAAA;AAClF,wBAAA,+CAA+C,EAAE,CAAmC,iCAAA,CAAA;AACpF,wBAAA,iCAAiC,EAAE,qCAAqC;AACxE,wBAAA,gBAAgB,EAAE,oBAAoB;AACtC,wBAAA,SAAS,EAAE,WAAW;AACtB,wBAAA,WAAW,EAAE,kBAAkB;AAChC,qBAAA,EAAA,UAAA,EACW,IAAI,EAAA,QAAA,EAAA,olBAAA,EAAA,MAAA,EAAA,CAAA,mlHAAA,CAAA,EAAA,CAAA;;0BAMb,IAAI;;0BAIJ,MAAM;2BAAC,mCAAmC,CAAA;;0BAC1C,QAAQ;;0BAER,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;;0BACxC,SAAS;2BAAC,UAAU,CAAA;yCAkCd,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAGG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAMN,QAAQ,EAAA,CAAA;sBAHP,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA;wBACL,SAAS,EAAE,CAAC,KAAc,MAAM,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;AAC5E,qBAAA,CAAA;;AAyGH;;AAEG;MAQU,4BAA4B,CAAA;8GAA5B,4BAA4B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAA5B,4BAA4B,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,uBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,wCAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAA5B,4BAA4B,EAAA,UAAA,EAAA,CAAA;kBAPxC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,uBAAuB;AACjC,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,wCAAwC;AAChD,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;AAGD;;AAEG;MAQU,sBAAsB,CAAA;8GAAtB,sBAAsB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAtB,sBAAsB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,kCAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAtB,sBAAsB,EAAA,UAAA,EAAA,CAAA;kBAPlC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iBAAiB;AAC3B,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,kCAAkC;AAC1C,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;;AEzND;;AAEG;AAkBG,MAAO,YACX,SAAQ,YAAY,CAAA;AAlBtB,IAAA,WAAA,GAAA;;;AAwBU,QAAA,IAAA,CAAA,WAAW,GAAG,IAAI,SAAS,EAA2B,CAAC;;QAQ/D,IAAU,CAAA,UAAA,GAAY,KAAK,CAAC;AAE5B;;;;;;;AAOG;QACM,IAAW,CAAA,WAAA,GAA4B,SAAS,CAAC;;QAGjD,IAAc,CAAA,cAAA,GAA+B,OAAO,CAAC;AA2B/D,KAAA;IAzBC,kBAAkB,GAAA;QAChB,IAAI,CAAC,QAAQ,CAAC,OAAO;AAClB,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC9B,aAAA,SAAS,CAAC,CAAC,OAA2C,KAAI;YACzD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,SAAS,KAAK,IAAI,CAAC,CAAC,CAAC;AAClF,YAAA,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,CAAC;AACrC,SAAC,CAAC,CAAC;AAEL,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,CAAC,cAAc,EAAE,CAAC;KACtF;;AAGD,IAAA,oBAAoB,CAAC,KAAoB,EAAA;AACvC,QAAA,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;KACnC;AAED,IAAA,kBAAkB,CAAC,MAA+B,EAAA;AAChD,QAAA,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;KAC3C;IAEQ,WAAW,GAAA;QAClB,KAAK,CAAC,WAAW,EAAE,CAAC;AACpB,QAAA,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,CAAC;AAC5B,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;KAC5B;8GAtDU,YAAY,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAZ,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAcJ,gBAAgB,CA5BxB,EAAA,WAAA,EAAA,aAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,2BAAA,EAAA,YAAA,EAAA,EAAA,cAAA,EAAA,eAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,aAAa;AACtB,gBAAA,WAAW,EAAE,YAAY;AAC1B,aAAA;AACF,SAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,UAAA,EAAA,SAAA,EAmBgB,uBAAuB,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAV7B,YAAY,EAAA,UAAA,EAAA,CAAA;kBAjBxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,eAAe;AACzB,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,SAAS,EAAE;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,aAAa;AACtB,4BAAA,WAAW,EAAc,YAAA;AAC1B,yBAAA;AACF,qBAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,eAAe;;;AAGtB,wBAAA,6BAA6B,EAAE,YAAY;AAC5C,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;8BAYC,QAAQ,EAAA,CAAA;sBADP,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,uBAAuB,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC,CAAA;gBAK7D,UAAU,EAAA,CAAA;sBADT,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAW3B,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAGG,cAAc,EAAA,CAAA;sBAAtB,KAAK;;;MChCK,kBAAkB,CAAA;8GAAlB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAlB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,YArB3B,eAAe;YACf,kBAAkB;YAClB,YAAY;YACZ,YAAY;YACZ,iBAAiB;YACjB,0BAA0B;YAC1B,uBAAuB;YACvB,sBAAsB;YACtB,4BAA4B;AAC5B,YAAA,wBAAwB,aAGxB,YAAY;YACZ,iBAAiB;YACjB,0BAA0B;YAC1B,uBAAuB;YACvB,sBAAsB;YACtB,4BAA4B;YAC5B,wBAAwB,CAAA,EAAA,CAAA,CAAA,EAAA;AAGf,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,YArB3B,eAAe;YACf,kBAAkB;YAClB,YAAY,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAmBH,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAvB9B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE;wBACP,eAAe;wBACf,kBAAkB;wBAClB,YAAY;wBACZ,YAAY;wBACZ,iBAAiB;wBACjB,0BAA0B;wBAC1B,uBAAuB;wBACvB,sBAAsB;wBACtB,4BAA4B;wBAC5B,wBAAwB;AACzB,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,YAAY;wBACZ,iBAAiB;wBACjB,0BAA0B;wBAC1B,uBAAuB;wBACvB,sBAAsB;wBACtB,4BAA4B;wBAC5B,wBAAwB;AACzB,qBAAA;AACF,iBAAA,CAAA;;;AC3CD;;AAEG;;;;"}