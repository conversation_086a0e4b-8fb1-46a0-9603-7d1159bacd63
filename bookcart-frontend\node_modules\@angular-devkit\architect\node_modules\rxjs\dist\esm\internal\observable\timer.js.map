{"version": 3, "file": "timer.js", "sourceRoot": "", "sources": ["../../../../src/internal/observable/timer.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAE3C,OAAO,EAAE,KAAK,IAAI,cAAc,EAAE,MAAM,oBAAoB,CAAC;AAC7D,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AAClD,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAgI7C,MAAM,UAAU,KAAK,CACnB,UAAyB,CAAC,EAC1B,mBAA4C,EAC5C,YAA2B,cAAc;IAIzC,IAAI,gBAAgB,GAAG,CAAC,CAAC,CAAC;IAE1B,IAAI,mBAAmB,IAAI,IAAI,EAAE;QAI/B,IAAI,WAAW,CAAC,mBAAmB,CAAC,EAAE;YACpC,SAAS,GAAG,mBAAmB,CAAC;SACjC;aAAM;YAGL,gBAAgB,GAAG,mBAAmB,CAAC;SACxC;KACF;IAED,OAAO,IAAI,UAAU,CAAC,CAAC,UAAU,EAAE,EAAE;QAInC,IAAI,GAAG,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,SAAU,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;QAEvE,IAAI,GAAG,GAAG,CAAC,EAAE;YAEX,GAAG,GAAG,CAAC,CAAC;SACT;QAGD,IAAI,CAAC,GAAG,CAAC,CAAC;QAGV,OAAO,SAAS,CAAC,QAAQ,CAAC;YACxB,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;gBAEtB,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;gBAErB,IAAI,CAAC,IAAI,gBAAgB,EAAE;oBAGzB,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC;iBAC5C;qBAAM;oBAEL,UAAU,CAAC,QAAQ,EAAE,CAAC;iBACvB;aACF;QACH,CAAC,EAAE,GAAG,CAAC,CAAC;IACV,CAAC,CAAC,CAAC;AACL,CAAC"}