{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../../src/material/divider/testing/divider-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ComponentHarness, HarnessPredicate} from '@angular/cdk/testing';\nimport {DividerHarnessFilters} from './divider-harness-filters';\n\n/** <PERSON><PERSON>ss for interacting with a `mat-divider`. */\nexport class MatDividerHarness extends ComponentHarness {\n  static hostSelector = '.mat-divider';\n\n  static with(options: DividerHarnessFilters = {}) {\n    return new HarnessPredicate(MatDividerHarness, options);\n  }\n\n  async getOrientation(): Promise<'horizontal' | 'vertical'> {\n    return (await this.host()).getAttribute('aria-orientation') as Promise<\n      'horizontal' | 'vertical'\n    >;\n  }\n\n  async isInset(): Promise<boolean> {\n    return (await this.host()).hasClass('mat-divider-inset');\n  }\n}\n"], "names": [], "mappings": ";;AAWA;AACM,MAAO,iBAAkB,SAAQ,gBAAgB,CAAA;aAC9C,IAAY,CAAA,YAAA,GAAG,cAAc,CAAC,EAAA;AAErC,IAAA,OAAO,IAAI,CAAC,OAAA,GAAiC,EAAE,EAAA;AAC7C,QAAA,OAAO,IAAI,gBAAgB,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;KACzD;AAED,IAAA,MAAM,cAAc,GAAA;AAClB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,kBAAkB,CAEzD,CAAC;KACH;AAED,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,mBAAmB,CAAC,CAAC;KAC1D;;;;;"}