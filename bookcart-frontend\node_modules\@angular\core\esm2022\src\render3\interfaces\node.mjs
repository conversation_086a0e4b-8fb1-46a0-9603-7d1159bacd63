/**
 * Converts `TNodeType` into human readable text.
 * Make sure this matches with `TNodeType`
 */
export function toTNodeTypeAsString(tNodeType) {
    let text = '';
    (tNodeType & 1 /* TNodeType.Text */) && (text += '|Text');
    (tNodeType & 2 /* TNodeType.Element */) && (text += '|Element');
    (tNodeType & 4 /* TNodeType.Container */) && (text += '|Container');
    (tNodeType & 8 /* TNodeType.ElementContainer */) && (text += '|ElementContainer');
    (tNodeType & 16 /* TNodeType.Projection */) && (text += '|Projection');
    (tNodeType & 32 /* TNodeType.Icu */) && (text += '|IcuContainer');
    (tNodeType & 64 /* TNodeType.Placeholder */) && (text += '|Placeholder');
    return text.length > 0 ? text.substring(1) : text;
}
/**
 * Helper function to detect if a given value matches a `TNode` shape.
 *
 * The logic uses the `insertBeforeIndex` and its possible values as
 * a way to differentiate a TNode shape from other types of objects
 * within the `TView.data`. This is not a perfect check, but it can
 * be a reasonable differentiator, since we control the shapes of objects
 * within `TView.data`.
 */
export function isTNodeShape(value) {
    return value != null && typeof value === 'object' &&
        (value.insertBeforeIndex === null ||
            typeof value.insertBeforeIndex === 'number' ||
            Array.isArray(value.insertBeforeIndex));
}
/**
 * Returns `true` if the `TNode` has a directive which has `@Input()` for `class` binding.
 *
 * ```
 * <div my-dir [class]="exp"></div>
 * ```
 * and
 * ```
 * @Directive({
 * })
 * class MyDirective {
 *   @Input()
 *   class: string;
 * }
 * ```
 *
 * In the above case it is necessary to write the reconciled styling information into the
 * directive's input.
 *
 * @param tNode
 */
export function hasClassInput(tNode) {
    return (tNode.flags & 8 /* TNodeFlags.hasClassInput */) !== 0;
}
/**
 * Returns `true` if the `TNode` has a directive which has `@Input()` for `style` binding.
 *
 * ```
 * <div my-dir [style]="exp"></div>
 * ```
 * and
 * ```
 * @Directive({
 * })
 * class MyDirective {
 *   @Input()
 *   class: string;
 * }
 * ```
 *
 * In the above case it is necessary to write the reconciled styling information into the
 * directive's input.
 *
 * @param tNode
 */
export function hasStyleInput(tNode) {
    return (tNode.flags & 16 /* TNodeFlags.hasStyleInput */) !== 0;
}
//# sourceMappingURL=data:application/json;base64,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