@use '../../token-utils';
@use '../../../theming/inspection';
@use '../../../style/sass-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, menu);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    container-shape: 4px,
    divider-bottom-spacing: 0,
    divider-top-spacing: 0,
    item-spacing: 16px,
    item-icon-size: 24px,
    item-leading-spacing: 16px,
    item-trailing-spacing: 16px,
    item-with-icon-leading-spacing: 16px,
    item-with-icon-trailing-spacing: 16px,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  $is-dark: inspection.get-theme-type($theme) == dark;
  $active-state-layer-color: inspection.get-theme-color($theme, foreground, base,
    if($is-dark, 0.08, 0.04));
  $text-color: inspection.get-theme-color($theme, foreground, text);

  @return (
    item-label-text-color: $text-color,
    item-icon-color: $text-color,
    item-hover-state-layer-color: $active-state-layer-color,
    item-focus-state-layer-color: $active-state-layer-color,
    container-color: inspection.get-theme-color($theme, background, card),
    divider-color: inspection.get-theme-color($theme, foreground, divider),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    item-label-text-font: inspection.get-theme-typography($theme, body-1, font-family),
    item-label-text-size: inspection.get-theme-typography($theme, body-1, font-size),
    item-label-text-tracking: inspection.get-theme-typography($theme, body-1, letter-spacing),
    item-label-text-line-height: inspection.get-theme-typography($theme, body-1, line-height),
    item-label-text-weight: inspection.get-theme-typography($theme, body-1, font-weight),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-utils.$placeholder-color-config),
      get-typography-tokens(token-utils.$placeholder-typography-config),
      get-density-tokens(token-utils.$placeholder-density-config)
  );
}
