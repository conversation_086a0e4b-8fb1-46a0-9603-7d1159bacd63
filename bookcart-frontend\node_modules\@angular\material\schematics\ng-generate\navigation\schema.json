{"$schema": "http://json-schema.org/draft-07/schema", "$id": "SchematicsMaterialNavigation", "title": "Material Navigation Options Schema", "type": "object", "properties": {"path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path to create the component.", "visible": false}, "project": {"type": "string", "description": "The name of the project.", "$default": {"$source": "projectName"}}, "name": {"type": "string", "description": "The name of the component.", "$default": {"$source": "argv", "index": 0}, "x-prompt": "What should be the name of the component?"}, "inlineStyle": {"description": "Specifies if the style will be in the ts file.", "type": "boolean", "alias": "s"}, "inlineTemplate": {"description": "Specifies if the template will be in the ts file.", "type": "boolean", "alias": "t"}, "standalone": {"description": "Whether the generated component is standalone.", "type": "boolean"}, "viewEncapsulation": {"description": "Specifies the view encapsulation strategy.", "enum": ["Emulated", "None"], "type": "string", "alias": "v"}, "changeDetection": {"description": "Specifies the change detection strategy.", "enum": ["<PERSON><PERSON><PERSON>", "OnPush"], "type": "string", "default": "<PERSON><PERSON><PERSON>", "alias": "c"}, "prefix": {"type": "string", "format": "html-selector", "description": "The prefix to apply to generated selectors.", "alias": "p"}, "style": {"description": "The file extension to be used for style files.", "type": "string"}, "skipTests": {"type": "boolean", "description": "When true, does not generate a test file."}, "flat": {"type": "boolean", "description": "Flag to indicate if a dir is created.", "default": false}, "skipImport": {"type": "boolean", "description": "Flag to skip the module import.", "default": false}, "selector": {"type": "string", "format": "html-selector", "description": "The selector to use for the component."}, "module": {"type": "string", "description": "Allows specification of the declaring module.", "alias": "m"}, "export": {"type": "boolean", "default": false, "description": "Specifies if declaring module exports the component."}, "routing": {"type": "boolean", "default": false, "description": "Specifies whether Angular routing is enabled. This controls whether anchor elements use href or routerLink"}}, "required": ["name"]}