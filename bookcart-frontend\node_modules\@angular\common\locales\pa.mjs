/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === Math.floor(n) && (n >= 0 && n <= 1))
        return 1;
    return 5;
}
export default ["pa", [["ਸ.", "ਸ਼."], ["ਪੂ.ਦੁ.", "ਬਾ.ਦੁ."], u], [["ਪੂ.ਦੁ.", "ਬਾ.ਦੁ."], u, u], [["ਐ", "ਸੋ", "ਮੰ", "ਬੁੱ", "ਵੀ", "ਸ਼ੁੱ", "ਸ਼"], ["ਐਤ", "ਸੋਮ", "ਮੰਗਲ", "ਬੁੱਧ", "ਵੀਰ", "ਸ਼ੁੱਕਰ", "ਸ਼ਨਿੱਚਰ"], ["ਐਤਵਾਰ", "ਸੋਮਵਾਰ", "ਮੰਗਲਵਾਰ", "ਬੁੱਧਵਾਰ", "ਵੀਰਵਾਰ", "ਸ਼ੁੱਕਰਵਾਰ", "ਸ਼ਨਿੱਚਰਵਾਰ"], ["ਐਤ", "ਸੋਮ", "ਮੰਗ", "ਬੁੱਧ", "ਵੀਰ", "ਸ਼ੁੱਕ", "ਸ਼ਨਿੱ"]], u, [["ਜ", "ਫ਼", "ਮਾ", "ਅ", "ਮ", "ਜੂ", "ਜੁ", "ਅ", "ਸ", "ਅ", "ਨ", "ਦ"], ["ਜਨ", "ਫ਼ਰ", "ਮਾਰਚ", "ਅਪ੍ਰੈ", "ਮਈ", "ਜੂਨ", "ਜੁਲਾ", "ਅਗ", "ਸਤੰ", "ਅਕਤੂ", "ਨਵੰ", "ਦਸੰ"], ["ਜਨਵਰੀ", "ਫ਼ਰਵਰੀ", "ਮਾਰਚ", "ਅਪ੍ਰੈਲ", "ਮਈ", "ਜੂਨ", "ਜੁਲਾਈ", "ਅਗਸਤ", "ਸਤੰਬਰ", "ਅਕਤੂਬਰ", "ਨਵੰਬਰ", "ਦਸੰਬਰ"]], u, [["ਈ.ਪੂ.", "ਸੰਨ"], ["ਈ. ਪੂ.", "ਸੰਨ"], ["ਈਸਵੀ ਪੂਰਵ", "ਈਸਵੀ ਸੰਨ"]], 0, [0, 0], ["d/M/yy", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1}, {0}", u, "{1} {0}", u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##,##0.###", "#,##,##0%", "¤ #,##,##0.00", "[#E0]"], "INR", "₹", "ਭਾਰਤੀ ਰੁਪਇਆ", { "BYN": [u, "р."], "JPY": ["JP¥", "¥"], "PHP": [u, "₱"], "THB": ["฿"], "TWD": ["NT$"], "USD": ["US$", "$"], "XXX": [] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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