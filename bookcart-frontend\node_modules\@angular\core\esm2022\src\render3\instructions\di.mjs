/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { InjectFlags, resolveForwardRef } from '../../di';
import { assertInjectImplementationNotEqual } from '../../di/inject_switch';
import { ɵɵinject } from '../../di/injector_compatibility';
import { emitInjectEvent } from '../debug/injector_profiler';
import { getOrCreateInjectable } from '../di';
import { getCurrentTNode, getLView } from '../state';
export function ɵɵdirectiveInject(token, flags = InjectFlags.Default) {
    const lView = getLView();
    // Fall back to inject() if view hasn't been created. This situation can happen in tests
    // if inject utilities are used before bootstrapping.
    if (lView === null) {
        // Verify that we will not get into infinite loop.
        ngDevMode && assertInjectImplementationNotEqual(ɵɵdirectiveInject);
        return ɵɵinject(token, flags);
    }
    const tNode = getCurrentTNode();
    const value = getOrCreateInjectable(tNode, lView, resolveForwardRef(token), flags);
    ngDevMode && emitInjectEvent(token, value, flags);
    return value;
}
/**
 * Throws an error indicating that a factory function could not be generated by the compiler for a
 * particular class.
 *
 * This instruction allows the actual error message to be optimized away when ngDevMode is turned
 * off, saving bytes of generated code while still providing a good experience in dev mode.
 *
 * The name of the class is not mentioned here, but will be in the generated factory function name
 * and thus in the stack trace.
 *
 * @codeGenApi
 */
export function ɵɵinvalidFactory() {
    const msg = ngDevMode ? `This constructor was not compatible with Dependency Injection.` : 'invalid';
    throw new Error(msg);
}
//# sourceMappingURL=data:application/json;base64,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