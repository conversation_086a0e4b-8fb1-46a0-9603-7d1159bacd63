@use 'sass:map';
@use '../../../style/sass-utils';
@use '../../../theming/theming';
@use '../../../theming/inspection';
@use '../../token-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mdc, icon-button);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
//
// Tokens that are available in MDC, but not used in Angular Material should be mapped to `null`.
// `null` indicates that we are intentionally choosing not to emit a slot or value for the token in
// our CSS.
@function get-unthemable-tokens() {
  @return (
    // MDC's icon size applied to svg and img elements inside the component
    icon-size: 24px,

    // =============================================================================================
    // = TOKENS NOT USED IN ANGULAR MATERIAL                                                       =
    // =============================================================================================
    // State layer is unused
    focus-icon-color: null,
    focus-state-layer-color: null,
    focus-state-layer-opacity: null,
    hover-icon-color: null,
    hover-state-layer-color: null,
    hover-state-layer-opacity: null,
    pressed-icon-color: null,
    pressed-state-layer-color: null,
    pressed-state-layer-opacity: null,
    focus-ring-color: null,
    focus-ring-offset: null,

    // We use a color with an opacity to show the disabled state,
    // instead of applying it to the entire button.
    disabled-icon-opacity: null,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  $is-dark: inspection.get-theme-type($theme) == dark;

  @return (
    icon-color: inherit,
    disabled-icon-color: if($is-dark, rgba(#fff, 0.5), rgba(#000, 0.38)),
  );
}

// Generates the mapping for the properties that change based on the button palette color.
@function private-get-color-palette-color-tokens($theme, $palette-name) {
  @return (
    icon-color: inspection.get-theme-color($theme, $palette-name)
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return ();
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $scale: theming.clamp-density(inspection.get-theme-density($theme), -5);

  @return (
    // The diameter of the checkbox's ripple.
    state-layer-size: map.get((
      0: 48px,
      -1: 44px,
      -2: 40px,
      -3: 36px,
      -4: 32px,
      -5: 28px,
    ), $scale)
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-utils.$placeholder-color-config),
      get-typography-tokens(token-utils.$placeholder-typography-config),
      get-density-tokens(token-utils.$placeholder-density-config)
  );
}
