/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["os-RU", [["AM", "PM"], u, ["ӕмбисбоны размӕ", "ӕмбисбоны фӕстӕ"]], [["AM", "PM"], u, u], [["Х", "К", "Д", "Ӕ", "Ц", "М", "С"], ["хцб", "крс", "дцг", "ӕрт", "цпр", "мрб", "сбт"], ["хуыцаубон", "къуырисӕр", "дыццӕг", "ӕртыццӕг", "цыппӕрӕм", "майрӕмбон", "сабат"], ["хцб", "крс", "дцг", "ӕрт", "цпр", "мрб", "сбт"]], [["Х", "К", "Д", "Ӕ", "Ц", "М", "С"], ["Хцб", "Крс", "Дцг", "Ӕрт", "Цпр", "Мрб", "Сбт"], ["Хуыцаубон", "Къуырисӕр", "Дыццӕг", "Ӕртыццӕг", "Цыппӕрӕм", "Майрӕмбон", "Сабат"], ["хцб", "крс", "дцг", "ӕрт", "цпр", "мрб", "сбт"]], [["Я", "Ф", "М", "А", "М", "И", "И", "А", "С", "О", "Н", "Д"], ["янв.", "фев.", "мар.", "апр.", "майы", "июны", "июлы", "авг.", "сен.", "окт.", "ноя.", "дек."], ["январы", "февралы", "мартъийы", "апрелы", "майы", "июны", "июлы", "августы", "сентябры", "октябры", "ноябры", "декабры"]], [["Я", "Ф", "М", "А", "М", "И", "И", "А", "С", "О", "Н", "Д"], ["Янв.", "Февр.", "Март.", "Апр.", "Май", "Июнь", "Июль", "Авг.", "Сент.", "Окт.", "Нояб.", "Дек."], ["Январь", "Февраль", "Мартъи", "Апрель", "Май", "Июнь", "Июль", "Август", "Сентябрь", "Октябрь", "Ноябрь", "Декабрь"]], [["н.д.а.", "н.д."], u, u], 1, [6, 0], ["dd.MM.yy", "dd MMM y 'аз'", "d MMMM, y 'аз'", "EEEE, d MMMM, y 'аз'"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1}, {0}", u, u, u], [",", " ", ";", "%", "+", "-", "E", "×", "‰", "∞", "НН", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "RUB", "₽", "Сом", { "JPY": ["JP¥", "¥"], "RUB": ["₽"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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