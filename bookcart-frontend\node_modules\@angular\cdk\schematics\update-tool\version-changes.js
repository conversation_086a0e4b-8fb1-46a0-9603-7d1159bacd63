"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAllChanges = exports.getChangesForTarget = void 0;
const target_version_1 = require("./target-version");
/**
 * Gets the changes for a given target version from the specified version changes object.
 *
 * For readability and a good overview of breaking changes, the version change data always
 * includes the related Pull Request link. Since this data is not needed when performing the
 * upgrade, this unused data can be removed and the changes data can be flattened into an
 * easy iterable array.
 */
function getChangesForTarget(target, data) {
    if (!data) {
        const version = target_version_1.TargetVersion[target];
        throw new Error(`No data could be found for target version: ${version}`);
    }
    return (data[target] || []).reduce((result, prData) => result.concat(prData.changes), []);
}
exports.getChangesForTarget = getChangesForTarget;
/**
 * Gets all changes from the specified version changes object. This is helpful in case a migration
 * rule does not distinguish data based on the target version, but for readability the
 * upgrade data is separated for each target version.
 */
function getAllChanges(data) {
    return Object.keys(data)
        .map(targetVersion => getChangesForTarget(targetVersion, data))
        .reduce((result, versionData) => result.concat(versionData), []);
}
exports.getAllChanges = getAllChanges;
//# sourceMappingURL=data:application/json;base64,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