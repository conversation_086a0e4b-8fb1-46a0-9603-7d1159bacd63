{"version": 3, "file": "sidenav.mjs", "sources": ["../../../../../../src/material/sidenav/drawer-animations.ts", "../../../../../../src/material/sidenav/drawer.ts", "../../../../../../src/material/sidenav/drawer.html", "../../../../../../src/material/sidenav/drawer-container.html", "../../../../../../src/material/sidenav/sidenav.ts", "../../../../../../src/material/sidenav/sidenav-container.html", "../../../../../../src/material/sidenav/sidenav-module.ts", "../../../../../../src/material/sidenav/sidenav_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {\n  animate,\n  state,\n  style,\n  transition,\n  trigger,\n  AnimationTriggerMetadata,\n} from '@angular/animations';\n\n/**\n * Animations used by the Material drawers.\n * @docs-private\n */\nexport const matDrawerAnimations: {\n  readonly transformDrawer: AnimationTriggerMetadata;\n} = {\n  /** Animation that slides a drawer in and out. */\n  transformDrawer: trigger('transform', [\n    // We remove the `transform` here completely, rather than setting it to zero, because:\n    // 1. Having a transform can cause elements with ripples or an animated\n    //    transform to shift around in Chrome with an RTL layout (see #10023).\n    // 2. 3d transforms causes text to appear blurry on IE and Edge.\n    state(\n      'open, open-instant',\n      style({\n        'transform': 'none',\n        'visibility': 'visible',\n      }),\n    ),\n    state(\n      'void',\n      style({\n        // Avoids the shadow showing up when closed in SSR.\n        'box-shadow': 'none',\n        'visibility': 'hidden',\n      }),\n    ),\n    transition('void => open-instant', animate('0ms')),\n    transition(\n      'void <=> open, open-instant => void',\n      animate('400ms cubic-bezier(0.25, 0.8, 0.25, 1)'),\n    ),\n  ]),\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AnimationEvent} from '@angular/animations';\nimport {\n  FocusMonitor,\n  FocusOrigin,\n  FocusTrap,\n  FocusTrapFactory,\n  InteractivityChecker,\n} from '@angular/cdk/a11y';\nimport {Directionality} from '@angular/cdk/bidi';\nimport {BooleanInput, coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {ESCAPE, hasModifierKey} from '@angular/cdk/keycodes';\nimport {Platform} from '@angular/cdk/platform';\nimport {CdkScrollable, ScrollDispatcher, ViewportRuler} from '@angular/cdk/scrolling';\nimport {DOCUMENT} from '@angular/common';\nimport {\n  AfterContentChecked,\n  AfterContentInit,\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChild,\n  ContentChildren,\n  <PERSON><PERSON><PERSON><PERSON>,\n  ElementRef,\n  EventEmitter,\n  forwardRef,\n  Inject,\n  InjectionToken,\n  Input,\n  NgZone,\n  OnDestroy,\n  Optional,\n  Output,\n  QueryList,\n  ViewChild,\n  ViewEncapsulation,\n  ANIMATION_MODULE_TYPE,\n} from '@angular/core';\nimport {fromEvent, merge, Observable, Subject} from 'rxjs';\nimport {\n  debounceTime,\n  filter,\n  map,\n  startWith,\n  take,\n  takeUntil,\n  distinctUntilChanged,\n  mapTo,\n} from 'rxjs/operators';\nimport {matDrawerAnimations} from './drawer-animations';\n\n/**\n * Throws an exception when two MatDrawer are matching the same position.\n * @docs-private\n */\nexport function throwMatDuplicatedDrawerError(position: string) {\n  throw Error(`A drawer was already declared for 'position=\"${position}\"'`);\n}\n\n/** Options for where to set focus to automatically on dialog open */\nexport type AutoFocusTarget = 'dialog' | 'first-tabbable' | 'first-heading';\n\n/** Result of the toggle promise that indicates the state of the drawer. */\nexport type MatDrawerToggleResult = 'open' | 'close';\n\n/** Drawer and SideNav display modes. */\nexport type MatDrawerMode = 'over' | 'push' | 'side';\n\n/** Configures whether drawers should use auto sizing by default. */\nexport const MAT_DRAWER_DEFAULT_AUTOSIZE = new InjectionToken<boolean>(\n  'MAT_DRAWER_DEFAULT_AUTOSIZE',\n  {\n    providedIn: 'root',\n    factory: MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY,\n  },\n);\n\n/**\n * Used to provide a drawer container to a drawer while avoiding circular references.\n * @docs-private\n */\nexport const MAT_DRAWER_CONTAINER = new InjectionToken('MAT_DRAWER_CONTAINER');\n\n/** @docs-private */\nexport function MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY(): boolean {\n  return false;\n}\n\n@Component({\n  selector: 'mat-drawer-content',\n  template: '<ng-content></ng-content>',\n  host: {\n    'class': 'mat-drawer-content',\n    '[style.margin-left.px]': '_container._contentMargins.left',\n    '[style.margin-right.px]': '_container._contentMargins.right',\n  },\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  providers: [\n    {\n      provide: CdkScrollable,\n      useExisting: MatDrawerContent,\n    },\n  ],\n  standalone: true,\n})\nexport class MatDrawerContent extends CdkScrollable implements AfterContentInit {\n  constructor(\n    private _changeDetectorRef: ChangeDetectorRef,\n    @Inject(forwardRef(() => MatDrawerContainer)) public _container: MatDrawerContainer,\n    elementRef: ElementRef<HTMLElement>,\n    scrollDispatcher: ScrollDispatcher,\n    ngZone: NgZone,\n  ) {\n    super(elementRef, scrollDispatcher, ngZone);\n  }\n\n  ngAfterContentInit() {\n    this._container._contentMarginChanges.subscribe(() => {\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n}\n\n/**\n * This component corresponds to a drawer that can be opened on the drawer container.\n */\n@Component({\n  selector: 'mat-drawer',\n  exportAs: 'matDrawer',\n  templateUrl: 'drawer.html',\n  animations: [matDrawerAnimations.transformDrawer],\n  host: {\n    'class': 'mat-drawer',\n    // must prevent the browser from aligning text based on value\n    '[attr.align]': 'null',\n    '[class.mat-drawer-end]': 'position === \"end\"',\n    '[class.mat-drawer-over]': 'mode === \"over\"',\n    '[class.mat-drawer-push]': 'mode === \"push\"',\n    '[class.mat-drawer-side]': 'mode === \"side\"',\n    '[class.mat-drawer-opened]': 'opened',\n    'tabIndex': '-1',\n    '[@transform]': '_animationState',\n    '(@transform.start)': '_animationStarted.next($event)',\n    '(@transform.done)': '_animationEnd.next($event)',\n  },\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  standalone: true,\n  imports: [CdkScrollable],\n})\nexport class MatDrawer implements AfterViewInit, AfterContentChecked, OnDestroy {\n  private _focusTrap: FocusTrap | null = null;\n  private _elementFocusedBeforeDrawerWasOpened: HTMLElement | null = null;\n\n  /** Whether the drawer is initialized. Used for disabling the initial animation. */\n  private _enableAnimations = false;\n\n  /** Whether the view of the component has been attached. */\n  private _isAttached: boolean;\n\n  /** Anchor node used to restore the drawer to its initial position. */\n  private _anchor: Comment | null;\n\n  /** The side that the drawer is attached to. */\n  @Input()\n  get position(): 'start' | 'end' {\n    return this._position;\n  }\n  set position(value: 'start' | 'end') {\n    // Make sure we have a valid value.\n    value = value === 'end' ? 'end' : 'start';\n    if (value !== this._position) {\n      // Static inputs in Ivy are set before the element is in the DOM.\n      if (this._isAttached) {\n        this._updatePositionInParent(value);\n      }\n\n      this._position = value;\n      this.onPositionChanged.emit();\n    }\n  }\n  private _position: 'start' | 'end' = 'start';\n\n  /** Mode of the drawer; one of 'over', 'push' or 'side'. */\n  @Input()\n  get mode(): MatDrawerMode {\n    return this._mode;\n  }\n  set mode(value: MatDrawerMode) {\n    this._mode = value;\n    this._updateFocusTrapState();\n    this._modeChanged.next();\n  }\n  private _mode: MatDrawerMode = 'over';\n\n  /** Whether the drawer can be closed with the escape key or by clicking on the backdrop. */\n  @Input()\n  get disableClose(): boolean {\n    return this._disableClose;\n  }\n  set disableClose(value: BooleanInput) {\n    this._disableClose = coerceBooleanProperty(value);\n  }\n  private _disableClose: boolean = false;\n\n  /**\n   * Whether the drawer should focus the first focusable element automatically when opened.\n   * Defaults to false in when `mode` is set to `side`, otherwise defaults to `true`. If explicitly\n   * enabled, focus will be moved into the sidenav in `side` mode as well.\n   * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or AutoFocusTarget\n   * instead.\n   */\n  @Input()\n  get autoFocus(): AutoFocusTarget | string | boolean {\n    const value = this._autoFocus;\n\n    // Note that usually we don't allow autoFocus to be set to `first-tabbable` in `side` mode,\n    // because we don't know how the sidenav is being used, but in some cases it still makes\n    // sense to do it. The consumer can explicitly set `autoFocus`.\n    if (value == null) {\n      if (this.mode === 'side') {\n        return 'dialog';\n      } else {\n        return 'first-tabbable';\n      }\n    }\n    return value;\n  }\n  set autoFocus(value: AutoFocusTarget | string | BooleanInput) {\n    if (value === 'true' || value === 'false' || value == null) {\n      value = coerceBooleanProperty(value);\n    }\n    this._autoFocus = value;\n  }\n  private _autoFocus: AutoFocusTarget | string | boolean | undefined;\n\n  /**\n   * Whether the drawer is opened. We overload this because we trigger an event when it\n   * starts or end.\n   */\n  @Input()\n  get opened(): boolean {\n    return this._opened;\n  }\n  set opened(value: BooleanInput) {\n    this.toggle(coerceBooleanProperty(value));\n  }\n  private _opened: boolean = false;\n\n  /** How the sidenav was opened (keypress, mouse click etc.) */\n  private _openedVia: FocusOrigin | null;\n\n  /** Emits whenever the drawer has started animating. */\n  readonly _animationStarted = new Subject<AnimationEvent>();\n\n  /** Emits whenever the drawer is done animating. */\n  readonly _animationEnd = new Subject<AnimationEvent>();\n\n  /** Current state of the sidenav animation. */\n  _animationState: 'open-instant' | 'open' | 'void' = 'void';\n\n  /** Event emitted when the drawer open state is changed. */\n  @Output() readonly openedChange: EventEmitter<boolean> =\n    // Note this has to be async in order to avoid some issues with two-bindings (see #8872).\n    new EventEmitter<boolean>(/* isAsync */ true);\n\n  /** Event emitted when the drawer has been opened. */\n  @Output('opened')\n  readonly _openedStream = this.openedChange.pipe(\n    filter(o => o),\n    map(() => {}),\n  );\n\n  /** Event emitted when the drawer has started opening. */\n  @Output()\n  readonly openedStart: Observable<void> = this._animationStarted.pipe(\n    filter(e => e.fromState !== e.toState && e.toState.indexOf('open') === 0),\n    mapTo(undefined),\n  );\n\n  /** Event emitted when the drawer has been closed. */\n  @Output('closed')\n  readonly _closedStream = this.openedChange.pipe(\n    filter(o => !o),\n    map(() => {}),\n  );\n\n  /** Event emitted when the drawer has started closing. */\n  @Output()\n  readonly closedStart: Observable<void> = this._animationStarted.pipe(\n    filter(e => e.fromState !== e.toState && e.toState === 'void'),\n    mapTo(undefined),\n  );\n\n  /** Emits when the component is destroyed. */\n  private readonly _destroyed = new Subject<void>();\n\n  /** Event emitted when the drawer's position changes. */\n  // tslint:disable-next-line:no-output-on-prefix\n  @Output('positionChanged') readonly onPositionChanged = new EventEmitter<void>();\n\n  /** Reference to the inner element that contains all the content. */\n  @ViewChild('content') _content: ElementRef<HTMLElement>;\n\n  /**\n   * An observable that emits when the drawer mode changes. This is used by the drawer container to\n   * to know when to when the mode changes so it can adapt the margins on the content.\n   */\n  readonly _modeChanged = new Subject<void>();\n\n  constructor(\n    private _elementRef: ElementRef<HTMLElement>,\n    private _focusTrapFactory: FocusTrapFactory,\n    private _focusMonitor: FocusMonitor,\n    private _platform: Platform,\n    private _ngZone: NgZone,\n    private readonly _interactivityChecker: InteractivityChecker,\n    @Optional() @Inject(DOCUMENT) private _doc: any,\n    @Optional() @Inject(MAT_DRAWER_CONTAINER) public _container?: MatDrawerContainer,\n  ) {\n    this.openedChange.subscribe((opened: boolean) => {\n      if (opened) {\n        if (this._doc) {\n          this._elementFocusedBeforeDrawerWasOpened = this._doc.activeElement as HTMLElement;\n        }\n\n        this._takeFocus();\n      } else if (this._isFocusWithinDrawer()) {\n        this._restoreFocus(this._openedVia || 'program');\n      }\n    });\n\n    /**\n     * Listen to `keydown` events outside the zone so that change detection is not run every\n     * time a key is pressed. Instead we re-enter the zone only if the `ESC` key is pressed\n     * and we don't have close disabled.\n     */\n    this._ngZone.runOutsideAngular(() => {\n      (fromEvent(this._elementRef.nativeElement, 'keydown') as Observable<KeyboardEvent>)\n        .pipe(\n          filter(event => {\n            return event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event);\n          }),\n          takeUntil(this._destroyed),\n        )\n        .subscribe(event =>\n          this._ngZone.run(() => {\n            this.close();\n            event.stopPropagation();\n            event.preventDefault();\n          }),\n        );\n    });\n\n    // We need a Subject with distinctUntilChanged, because the `done` event\n    // fires twice on some browsers. See https://github.com/angular/angular/issues/24084\n    this._animationEnd\n      .pipe(\n        distinctUntilChanged((x, y) => {\n          return x.fromState === y.fromState && x.toState === y.toState;\n        }),\n      )\n      .subscribe((event: AnimationEvent) => {\n        const {fromState, toState} = event;\n\n        if (\n          (toState.indexOf('open') === 0 && fromState === 'void') ||\n          (toState === 'void' && fromState.indexOf('open') === 0)\n        ) {\n          this.openedChange.emit(this._opened);\n        }\n      });\n  }\n\n  /**\n   * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n   * attribute to forcefully focus it. The attribute is removed after focus is moved.\n   * @param element The element to focus.\n   */\n  private _forceFocus(element: HTMLElement, options?: FocusOptions) {\n    if (!this._interactivityChecker.isFocusable(element)) {\n      element.tabIndex = -1;\n      // The tabindex attribute should be removed to avoid navigating to that element again\n      this._ngZone.runOutsideAngular(() => {\n        const callback = () => {\n          element.removeEventListener('blur', callback);\n          element.removeEventListener('mousedown', callback);\n          element.removeAttribute('tabindex');\n        };\n\n        element.addEventListener('blur', callback);\n        element.addEventListener('mousedown', callback);\n      });\n    }\n    element.focus(options);\n  }\n\n  /**\n   * Focuses the first element that matches the given selector within the focus trap.\n   * @param selector The CSS selector for the element to set focus to.\n   */\n  private _focusByCssSelector(selector: string, options?: FocusOptions) {\n    let elementToFocus = this._elementRef.nativeElement.querySelector(\n      selector,\n    ) as HTMLElement | null;\n    if (elementToFocus) {\n      this._forceFocus(elementToFocus, options);\n    }\n  }\n\n  /**\n   * Moves focus into the drawer. Note that this works even if\n   * the focus trap is disabled in `side` mode.\n   */\n  private _takeFocus() {\n    if (!this._focusTrap) {\n      return;\n    }\n\n    const element = this._elementRef.nativeElement;\n\n    // When autoFocus is not on the sidenav, if the element cannot be focused or does\n    // not exist, focus the sidenav itself so the keyboard navigation still works.\n    // We need to check that `focus` is a function due to Universal.\n    switch (this.autoFocus) {\n      case false:\n      case 'dialog':\n        return;\n      case true:\n      case 'first-tabbable':\n        this._focusTrap.focusInitialElementWhenReady().then(hasMovedFocus => {\n          if (!hasMovedFocus && typeof this._elementRef.nativeElement.focus === 'function') {\n            element.focus();\n          }\n        });\n        break;\n      case 'first-heading':\n        this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]');\n        break;\n      default:\n        this._focusByCssSelector(this.autoFocus!);\n        break;\n    }\n  }\n\n  /**\n   * Restores focus to the element that was originally focused when the drawer opened.\n   * If no element was focused at that time, the focus will be restored to the drawer.\n   */\n  private _restoreFocus(focusOrigin: Exclude<FocusOrigin, null>) {\n    if (this.autoFocus === 'dialog') {\n      return;\n    }\n\n    if (this._elementFocusedBeforeDrawerWasOpened) {\n      this._focusMonitor.focusVia(this._elementFocusedBeforeDrawerWasOpened, focusOrigin);\n    } else {\n      this._elementRef.nativeElement.blur();\n    }\n\n    this._elementFocusedBeforeDrawerWasOpened = null;\n  }\n\n  /** Whether focus is currently within the drawer. */\n  private _isFocusWithinDrawer(): boolean {\n    const activeEl = this._doc.activeElement;\n    return !!activeEl && this._elementRef.nativeElement.contains(activeEl);\n  }\n\n  ngAfterViewInit() {\n    this._isAttached = true;\n\n    // Only update the DOM position when the sidenav is positioned at\n    // the end since we project the sidenav before the content by default.\n    if (this._position === 'end') {\n      this._updatePositionInParent('end');\n    }\n\n    // Needs to happen after the position is updated\n    // so the focus trap anchors are in the right place.\n    if (this._platform.isBrowser) {\n      this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n      this._updateFocusTrapState();\n    }\n  }\n\n  ngAfterContentChecked() {\n    // Enable the animations after the lifecycle hooks have run, in order to avoid animating\n    // drawers that are open by default. When we're on the server, we shouldn't enable the\n    // animations, because we don't want the drawer to animate the first time the user sees\n    // the page.\n    if (this._platform.isBrowser) {\n      this._enableAnimations = true;\n    }\n  }\n\n  ngOnDestroy() {\n    this._focusTrap?.destroy();\n    this._anchor?.remove();\n    this._anchor = null;\n    this._animationStarted.complete();\n    this._animationEnd.complete();\n    this._modeChanged.complete();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n\n  /**\n   * Open the drawer.\n   * @param openedVia Whether the drawer was opened by a key press, mouse click or programmatically.\n   * Used for focus management after the sidenav is closed.\n   */\n  open(openedVia?: FocusOrigin): Promise<MatDrawerToggleResult> {\n    return this.toggle(true, openedVia);\n  }\n\n  /** Close the drawer. */\n  close(): Promise<MatDrawerToggleResult> {\n    return this.toggle(false);\n  }\n\n  /** Closes the drawer with context that the backdrop was clicked. */\n  _closeViaBackdropClick(): Promise<MatDrawerToggleResult> {\n    // If the drawer is closed upon a backdrop click, we always want to restore focus. We\n    // don't need to check whether focus is currently in the drawer, as clicking on the\n    // backdrop causes blurs the active element.\n    return this._setOpen(/* isOpen */ false, /* restoreFocus */ true, 'mouse');\n  }\n\n  /**\n   * Toggle this drawer.\n   * @param isOpen Whether the drawer should be open.\n   * @param openedVia Whether the drawer was opened by a key press, mouse click or programmatically.\n   * Used for focus management after the sidenav is closed.\n   */\n  toggle(isOpen: boolean = !this.opened, openedVia?: FocusOrigin): Promise<MatDrawerToggleResult> {\n    // If the focus is currently inside the drawer content and we are closing the drawer,\n    // restore the focus to the initially focused element (when the drawer opened).\n    if (isOpen && openedVia) {\n      this._openedVia = openedVia;\n    }\n\n    const result = this._setOpen(\n      isOpen,\n      /* restoreFocus */ !isOpen && this._isFocusWithinDrawer(),\n      this._openedVia || 'program',\n    );\n\n    if (!isOpen) {\n      this._openedVia = null;\n    }\n\n    return result;\n  }\n\n  /**\n   * Toggles the opened state of the drawer.\n   * @param isOpen Whether the drawer should open or close.\n   * @param restoreFocus Whether focus should be restored on close.\n   * @param focusOrigin Origin to use when restoring focus.\n   */\n  private _setOpen(\n    isOpen: boolean,\n    restoreFocus: boolean,\n    focusOrigin: Exclude<FocusOrigin, null>,\n  ): Promise<MatDrawerToggleResult> {\n    this._opened = isOpen;\n\n    if (isOpen) {\n      this._animationState = this._enableAnimations ? 'open' : 'open-instant';\n    } else {\n      this._animationState = 'void';\n      if (restoreFocus) {\n        this._restoreFocus(focusOrigin);\n      }\n    }\n\n    this._updateFocusTrapState();\n\n    return new Promise<MatDrawerToggleResult>(resolve => {\n      this.openedChange.pipe(take(1)).subscribe(open => resolve(open ? 'open' : 'close'));\n    });\n  }\n\n  _getWidth(): number {\n    return this._elementRef.nativeElement ? this._elementRef.nativeElement.offsetWidth || 0 : 0;\n  }\n\n  /** Updates the enabled state of the focus trap. */\n  private _updateFocusTrapState() {\n    if (this._focusTrap) {\n      // Trap focus only if the backdrop is enabled. Otherwise, allow end user to interact with the\n      // sidenav content.\n      this._focusTrap.enabled = !!this._container?.hasBackdrop;\n    }\n  }\n\n  /**\n   * Updates the position of the drawer in the DOM. We need to move the element around ourselves\n   * when it's in the `end` position so that it comes after the content and the visual order\n   * matches the tab order. We also need to be able to move it back to `start` if the sidenav\n   * started off as `end` and was changed to `start`.\n   */\n  private _updatePositionInParent(newPosition: 'start' | 'end'): void {\n    // Don't move the DOM node around on the server, because it can throw off hydration.\n    if (!this._platform.isBrowser) {\n      return;\n    }\n\n    const element = this._elementRef.nativeElement;\n    const parent = element.parentNode!;\n\n    if (newPosition === 'end') {\n      if (!this._anchor) {\n        this._anchor = this._doc.createComment('mat-drawer-anchor')!;\n        parent.insertBefore(this._anchor!, element);\n      }\n\n      parent.appendChild(element);\n    } else if (this._anchor) {\n      this._anchor.parentNode!.insertBefore(element, this._anchor);\n    }\n  }\n}\n\n/**\n * `<mat-drawer-container>` component.\n *\n * This is the parent component to one or two `<mat-drawer>`s that validates the state internally\n * and coordinates the backdrop and content styling.\n */\n@Component({\n  selector: 'mat-drawer-container',\n  exportAs: 'matDrawerContainer',\n  templateUrl: 'drawer-container.html',\n  styleUrl: 'drawer.css',\n  host: {\n    'class': 'mat-drawer-container',\n    '[class.mat-drawer-container-explicit-backdrop]': '_backdropOverride',\n  },\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  providers: [\n    {\n      provide: MAT_DRAWER_CONTAINER,\n      useExisting: MatDrawerContainer,\n    },\n  ],\n  standalone: true,\n  imports: [MatDrawerContent],\n})\nexport class MatDrawerContainer implements AfterContentInit, DoCheck, OnDestroy {\n  /** All drawers in the container. Includes drawers from inside nested containers. */\n  @ContentChildren(MatDrawer, {\n    // We need to use `descendants: true`, because Ivy will no longer match\n    // indirect descendants if it's left as false.\n    descendants: true,\n  })\n  _allDrawers: QueryList<MatDrawer>;\n\n  /** Drawers that belong to this container. */\n  _drawers = new QueryList<MatDrawer>();\n\n  @ContentChild(MatDrawerContent) _content: MatDrawerContent;\n  @ViewChild(MatDrawerContent) _userContent: MatDrawerContent;\n\n  /** The drawer child with the `start` position. */\n  get start(): MatDrawer | null {\n    return this._start;\n  }\n\n  /** The drawer child with the `end` position. */\n  get end(): MatDrawer | null {\n    return this._end;\n  }\n\n  /**\n   * Whether to automatically resize the container whenever\n   * the size of any of its drawers changes.\n   *\n   * **Use at your own risk!** Enabling this option can cause layout thrashing by measuring\n   * the drawers on every change detection cycle. Can be configured globally via the\n   * `MAT_DRAWER_DEFAULT_AUTOSIZE` token.\n   */\n  @Input()\n  get autosize(): boolean {\n    return this._autosize;\n  }\n  set autosize(value: BooleanInput) {\n    this._autosize = coerceBooleanProperty(value);\n  }\n  private _autosize: boolean;\n\n  /**\n   * Whether the drawer container should have a backdrop while one of the sidenavs is open.\n   * If explicitly set to `true`, the backdrop will be enabled for drawers in the `side`\n   * mode as well.\n   */\n  @Input()\n  get hasBackdrop(): boolean {\n    return this._drawerHasBackdrop(this._start) || this._drawerHasBackdrop(this._end);\n  }\n  set hasBackdrop(value: BooleanInput) {\n    this._backdropOverride = value == null ? null : coerceBooleanProperty(value);\n  }\n  _backdropOverride: boolean | null;\n\n  /** Event emitted when the drawer backdrop is clicked. */\n  @Output() readonly backdropClick: EventEmitter<void> = new EventEmitter<void>();\n\n  /** The drawer at the start/end position, independent of direction. */\n  private _start: MatDrawer | null;\n  private _end: MatDrawer | null;\n\n  /**\n   * The drawer at the left/right. When direction changes, these will change as well.\n   * They're used as aliases for the above to set the left/right style properly.\n   * In LTR, _left == _start and _right == _end.\n   * In RTL, _left == _end and _right == _start.\n   */\n  private _left: MatDrawer | null;\n  private _right: MatDrawer | null;\n\n  /** Emits when the component is destroyed. */\n  private readonly _destroyed = new Subject<void>();\n\n  /** Emits on every ngDoCheck. Used for debouncing reflows. */\n  private readonly _doCheckSubject = new Subject<void>();\n\n  /**\n   * Margins to be applied to the content. These are used to push / shrink the drawer content when a\n   * drawer is open. We use margin rather than transform even for push mode because transform breaks\n   * fixed position elements inside of the transformed element.\n   */\n  _contentMargins: {left: number | null; right: number | null} = {left: null, right: null};\n\n  readonly _contentMarginChanges = new Subject<{left: number | null; right: number | null}>();\n\n  /** Reference to the CdkScrollable instance that wraps the scrollable content. */\n  get scrollable(): CdkScrollable {\n    return this._userContent || this._content;\n  }\n\n  constructor(\n    @Optional() private _dir: Directionality,\n    private _element: ElementRef<HTMLElement>,\n    private _ngZone: NgZone,\n    private _changeDetectorRef: ChangeDetectorRef,\n    viewportRuler: ViewportRuler,\n    @Inject(MAT_DRAWER_DEFAULT_AUTOSIZE) defaultAutosize = false,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) private _animationMode?: string,\n  ) {\n    // If a `Dir` directive exists up the tree, listen direction changes\n    // and update the left/right properties to point to the proper start/end.\n    if (_dir) {\n      _dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n        this._validateDrawers();\n        this.updateContentMargins();\n      });\n    }\n\n    // Since the minimum width of the sidenav depends on the viewport width,\n    // we need to recompute the margins if the viewport changes.\n    viewportRuler\n      .change()\n      .pipe(takeUntil(this._destroyed))\n      .subscribe(() => this.updateContentMargins());\n\n    this._autosize = defaultAutosize;\n  }\n\n  ngAfterContentInit() {\n    this._allDrawers.changes\n      .pipe(startWith(this._allDrawers), takeUntil(this._destroyed))\n      .subscribe((drawer: QueryList<MatDrawer>) => {\n        this._drawers.reset(drawer.filter(item => !item._container || item._container === this));\n        this._drawers.notifyOnChanges();\n      });\n\n    this._drawers.changes.pipe(startWith(null)).subscribe(() => {\n      this._validateDrawers();\n\n      this._drawers.forEach((drawer: MatDrawer) => {\n        this._watchDrawerToggle(drawer);\n        this._watchDrawerPosition(drawer);\n        this._watchDrawerMode(drawer);\n      });\n\n      if (\n        !this._drawers.length ||\n        this._isDrawerOpen(this._start) ||\n        this._isDrawerOpen(this._end)\n      ) {\n        this.updateContentMargins();\n      }\n\n      this._changeDetectorRef.markForCheck();\n    });\n\n    // Avoid hitting the NgZone through the debounce timeout.\n    this._ngZone.runOutsideAngular(() => {\n      this._doCheckSubject\n        .pipe(\n          debounceTime(10), // Arbitrary debounce time, less than a frame at 60fps\n          takeUntil(this._destroyed),\n        )\n        .subscribe(() => this.updateContentMargins());\n    });\n  }\n\n  ngOnDestroy() {\n    this._contentMarginChanges.complete();\n    this._doCheckSubject.complete();\n    this._drawers.destroy();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n\n  /** Calls `open` of both start and end drawers */\n  open(): void {\n    this._drawers.forEach(drawer => drawer.open());\n  }\n\n  /** Calls `close` of both start and end drawers */\n  close(): void {\n    this._drawers.forEach(drawer => drawer.close());\n  }\n\n  /**\n   * Recalculates and updates the inline styles for the content. Note that this should be used\n   * sparingly, because it causes a reflow.\n   */\n  updateContentMargins() {\n    // 1. For drawers in `over` mode, they don't affect the content.\n    // 2. For drawers in `side` mode they should shrink the content. We do this by adding to the\n    //    left margin (for left drawer) or right margin (for right the drawer).\n    // 3. For drawers in `push` mode the should shift the content without resizing it. We do this by\n    //    adding to the left or right margin and simultaneously subtracting the same amount of\n    //    margin from the other side.\n    let left = 0;\n    let right = 0;\n\n    if (this._left && this._left.opened) {\n      if (this._left.mode == 'side') {\n        left += this._left._getWidth();\n      } else if (this._left.mode == 'push') {\n        const width = this._left._getWidth();\n        left += width;\n        right -= width;\n      }\n    }\n\n    if (this._right && this._right.opened) {\n      if (this._right.mode == 'side') {\n        right += this._right._getWidth();\n      } else if (this._right.mode == 'push') {\n        const width = this._right._getWidth();\n        right += width;\n        left -= width;\n      }\n    }\n\n    // If either `right` or `left` is zero, don't set a style to the element. This\n    // allows users to specify a custom size via CSS class in SSR scenarios where the\n    // measured widths will always be zero. Note that we reset to `null` here, rather\n    // than below, in order to ensure that the types in the `if` below are consistent.\n    left = left || null!;\n    right = right || null!;\n\n    if (left !== this._contentMargins.left || right !== this._contentMargins.right) {\n      this._contentMargins = {left, right};\n\n      // Pull back into the NgZone since in some cases we could be outside. We need to be careful\n      // to do it only when something changed, otherwise we can end up hitting the zone too often.\n      this._ngZone.run(() => this._contentMarginChanges.next(this._contentMargins));\n    }\n  }\n\n  ngDoCheck() {\n    // If users opted into autosizing, do a check every change detection cycle.\n    if (this._autosize && this._isPushed()) {\n      // Run outside the NgZone, otherwise the debouncer will throw us into an infinite loop.\n      this._ngZone.runOutsideAngular(() => this._doCheckSubject.next());\n    }\n  }\n\n  /**\n   * Subscribes to drawer events in order to set a class on the main container element when the\n   * drawer is open and the backdrop is visible. This ensures any overflow on the container element\n   * is properly hidden.\n   */\n  private _watchDrawerToggle(drawer: MatDrawer): void {\n    drawer._animationStarted\n      .pipe(\n        filter((event: AnimationEvent) => event.fromState !== event.toState),\n        takeUntil(this._drawers.changes),\n      )\n      .subscribe((event: AnimationEvent) => {\n        // Set the transition class on the container so that the animations occur. This should not\n        // be set initially because animations should only be triggered via a change in state.\n        if (event.toState !== 'open-instant' && this._animationMode !== 'NoopAnimations') {\n          this._element.nativeElement.classList.add('mat-drawer-transition');\n        }\n\n        this.updateContentMargins();\n        this._changeDetectorRef.markForCheck();\n      });\n\n    if (drawer.mode !== 'side') {\n      drawer.openedChange\n        .pipe(takeUntil(this._drawers.changes))\n        .subscribe(() => this._setContainerClass(drawer.opened));\n    }\n  }\n\n  /**\n   * Subscribes to drawer onPositionChanged event in order to\n   * re-validate drawers when the position changes.\n   */\n  private _watchDrawerPosition(drawer: MatDrawer): void {\n    if (!drawer) {\n      return;\n    }\n    // NOTE: We need to wait for the microtask queue to be empty before validating,\n    // since both drawers may be swapping positions at the same time.\n    drawer.onPositionChanged.pipe(takeUntil(this._drawers.changes)).subscribe(() => {\n      this._ngZone.onMicrotaskEmpty.pipe(take(1)).subscribe(() => {\n        this._validateDrawers();\n      });\n    });\n  }\n\n  /** Subscribes to changes in drawer mode so we can run change detection. */\n  private _watchDrawerMode(drawer: MatDrawer): void {\n    if (drawer) {\n      drawer._modeChanged\n        .pipe(takeUntil(merge(this._drawers.changes, this._destroyed)))\n        .subscribe(() => {\n          this.updateContentMargins();\n          this._changeDetectorRef.markForCheck();\n        });\n    }\n  }\n\n  /** Toggles the 'mat-drawer-opened' class on the main 'mat-drawer-container' element. */\n  private _setContainerClass(isAdd: boolean): void {\n    const classList = this._element.nativeElement.classList;\n    const className = 'mat-drawer-container-has-open';\n\n    if (isAdd) {\n      classList.add(className);\n    } else {\n      classList.remove(className);\n    }\n  }\n\n  /** Validate the state of the drawer children components. */\n  private _validateDrawers() {\n    this._start = this._end = null;\n\n    // Ensure that we have at most one start and one end drawer.\n    this._drawers.forEach(drawer => {\n      if (drawer.position == 'end') {\n        if (this._end != null && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throwMatDuplicatedDrawerError('end');\n        }\n        this._end = drawer;\n      } else {\n        if (this._start != null && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throwMatDuplicatedDrawerError('start');\n        }\n        this._start = drawer;\n      }\n    });\n\n    this._right = this._left = null;\n\n    // Detect if we're LTR or RTL.\n    if (this._dir && this._dir.value === 'rtl') {\n      this._left = this._end;\n      this._right = this._start;\n    } else {\n      this._left = this._start;\n      this._right = this._end;\n    }\n  }\n\n  /** Whether the container is being pushed to the side by one of the drawers. */\n  private _isPushed() {\n    return (\n      (this._isDrawerOpen(this._start) && this._start.mode != 'over') ||\n      (this._isDrawerOpen(this._end) && this._end.mode != 'over')\n    );\n  }\n\n  _onBackdropClicked() {\n    this.backdropClick.emit();\n    this._closeModalDrawersViaBackdrop();\n  }\n\n  _closeModalDrawersViaBackdrop() {\n    // Close all open drawers where closing is not disabled and the mode is not `side`.\n    [this._start, this._end]\n      .filter(drawer => drawer && !drawer.disableClose && this._drawerHasBackdrop(drawer))\n      .forEach(drawer => drawer!._closeViaBackdropClick());\n  }\n\n  _isShowingBackdrop(): boolean {\n    return (\n      (this._isDrawerOpen(this._start) && this._drawerHasBackdrop(this._start)) ||\n      (this._isDrawerOpen(this._end) && this._drawerHasBackdrop(this._end))\n    );\n  }\n\n  private _isDrawerOpen(drawer: MatDrawer | null): drawer is MatDrawer {\n    return drawer != null && drawer.opened;\n  }\n\n  // Whether argument drawer should have a backdrop when it opens\n  private _drawerHasBackdrop(drawer: MatDrawer | null) {\n    if (this._backdropOverride == null) {\n      return !!drawer && drawer.mode !== 'side';\n    }\n\n    return this._backdropOverride;\n  }\n}\n", "<div class=\"mat-drawer-inner-container\" cdkScrollable #content>\r\n  <ng-content></ng-content>\r\n</div>\r\n", "@if (hasBackdrop) {\n  <div class=\"mat-drawer-backdrop\" (click)=\"_onBackdropClicked()\"\n       [class.mat-drawer-shown]=\"_isShowingBackdrop()\"></div>\n}\n\n<ng-content select=\"mat-drawer\"></ng-content>\n\n<ng-content select=\"mat-drawer-content\">\n</ng-content>\n\n@if (!_content) {\n  <mat-drawer-content>\n    <ng-content></ng-content>\n  </mat-drawer-content>\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChild,\n  ContentChildren,\n  forwardRef,\n  Inject,\n  Input,\n  ViewEncapsulation,\n  QueryList,\n  ElementRef,\n  NgZone,\n} from '@angular/core';\nimport {MatDrawer, MatDrawerContainer, MatDrawerContent, MAT_DRAWER_CONTAINER} from './drawer';\nimport {matDrawerAnimations} from './drawer-animations';\nimport {\n  BooleanInput,\n  coerceBooleanProperty,\n  coerceNumberProperty,\n  NumberInput,\n} from '@angular/cdk/coercion';\nimport {ScrollDispatcher, CdkScrollable} from '@angular/cdk/scrolling';\n\n@Component({\n  selector: 'mat-sidenav-content',\n  template: '<ng-content></ng-content>',\n  host: {\n    'class': 'mat-drawer-content mat-sidenav-content',\n    '[style.margin-left.px]': '_container._contentMargins.left',\n    '[style.margin-right.px]': '_container._contentMargins.right',\n  },\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  providers: [\n    {\n      provide: CdkScrollable,\n      useExisting: MatSidenavContent,\n    },\n  ],\n  standalone: true,\n})\nexport class MatSidenavContent extends MatDrawerContent {\n  constructor(\n    changeDetectorRef: ChangeDetectorRef,\n    @Inject(forwardRef(() => MatSidenavContainer)) container: MatSidenavContainer,\n    elementRef: ElementRef<HTMLElement>,\n    scrollDispatcher: ScrollDispatcher,\n    ngZone: NgZone,\n  ) {\n    super(changeDetectorRef, container, elementRef, scrollDispatcher, ngZone);\n  }\n}\n\n@Component({\n  selector: 'mat-sidenav',\n  exportAs: 'matSidenav',\n  templateUrl: 'drawer.html',\n  animations: [matDrawerAnimations.transformDrawer],\n  host: {\n    'class': 'mat-drawer mat-sidenav',\n    'tabIndex': '-1',\n    // must prevent the browser from aligning text based on value\n    '[attr.align]': 'null',\n    '[class.mat-drawer-end]': 'position === \"end\"',\n    '[class.mat-drawer-over]': 'mode === \"over\"',\n    '[class.mat-drawer-push]': 'mode === \"push\"',\n    '[class.mat-drawer-side]': 'mode === \"side\"',\n    '[class.mat-drawer-opened]': 'opened',\n    '[class.mat-sidenav-fixed]': 'fixedInViewport',\n    '[style.top.px]': 'fixedInViewport ? fixedTopGap : null',\n    '[style.bottom.px]': 'fixedInViewport ? fixedBottomGap : null',\n  },\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  standalone: true,\n  imports: [CdkScrollable],\n})\nexport class MatSidenav extends MatDrawer {\n  /** Whether the sidenav is fixed in the viewport. */\n  @Input()\n  get fixedInViewport(): boolean {\n    return this._fixedInViewport;\n  }\n  set fixedInViewport(value: BooleanInput) {\n    this._fixedInViewport = coerceBooleanProperty(value);\n  }\n  private _fixedInViewport = false;\n\n  /**\n   * The gap between the top of the sidenav and the top of the viewport when the sidenav is in fixed\n   * mode.\n   */\n  @Input()\n  get fixedTopGap(): number {\n    return this._fixedTopGap;\n  }\n  set fixedTopGap(value: NumberInput) {\n    this._fixedTopGap = coerceNumberProperty(value);\n  }\n  private _fixedTopGap = 0;\n\n  /**\n   * The gap between the bottom of the sidenav and the bottom of the viewport when the sidenav is in\n   * fixed mode.\n   */\n  @Input()\n  get fixedBottomGap(): number {\n    return this._fixedBottomGap;\n  }\n  set fixedBottomGap(value: NumberInput) {\n    this._fixedBottomGap = coerceNumberProperty(value);\n  }\n  private _fixedBottomGap = 0;\n}\n\n@Component({\n  selector: 'mat-sidenav-container',\n  exportAs: 'matSidenavContainer',\n  templateUrl: 'sidenav-container.html',\n  styleUrl: 'drawer.css',\n  host: {\n    'class': 'mat-drawer-container mat-sidenav-container',\n    '[class.mat-drawer-container-explicit-backdrop]': '_backdropOverride',\n  },\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  providers: [\n    {\n      provide: MAT_DRAWER_CONTAINER,\n      useExisting: MatSidenavContainer,\n    },\n  ],\n  standalone: true,\n  imports: [MatSidenavContent],\n})\nexport class MatSidenavContainer extends MatDrawerContainer {\n  @ContentChildren(MatSidenav, {\n    // We need to use `descendants: true`, because Ivy will no longer match\n    // indirect descendants if it's left as false.\n    descendants: true,\n  })\n  // We need an initializer here to avoid a TS error.\n  override _allDrawers: QueryList<MatSidenav> = undefined!;\n\n  // We need an initializer here to avoid a TS error.\n  @ContentChild(MatSidenavContent) override _content: MatSidenavContent = undefined!;\n}\n", "@if (hasBackdrop) {\n  <div class=\"mat-drawer-backdrop\" (click)=\"_onBackdropClicked()\"\n       [class.mat-drawer-shown]=\"_isShowingBackdrop()\"></div>\n}\n\n<ng-content select=\"mat-sidenav\"></ng-content>\n\n<ng-content select=\"mat-sidenav-content\">\n</ng-content>\n\n@if (!_content) {\n  <mat-sidenav-content>\n    <ng-content></ng-content>\n  </mat-sidenav-content>\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {CdkScrollableModule} from '@angular/cdk/scrolling';\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '@angular/material/core';\nimport {<PERSON><PERSON><PERSON><PERSON>, Mat<PERSON>rawerContainer, MatDrawerContent} from './drawer';\nimport {Mat<PERSON><PERSON>av, MatSidenavContainer, MatSidenavContent} from './sidenav';\n\n@NgModule({\n  imports: [\n    MatCommonModule,\n    CdkScrollableModule,\n    MatDrawer,\n    MatDrawerContainer,\n    MatDrawerContent,\n    MatSidenav,\n    MatSidenavContainer,\n    MatSidenavContent,\n  ],\n  exports: [\n    CdkScrollableModule,\n    MatCommonModule,\n    MatDrawer,\n    MatDrawerContainer,\n    MatDrawerContent,\n    MatSidenav,\n    MatSidenavContainer,\n    MatSidenavContent,\n  ],\n})\nexport class MatSidenavModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAgBA;;;AAGG;AACU,MAAA,mBAAmB,GAE5B;;AAEF,IAAA,eAAe,EAAE,OAAO,CAAC,WAAW,EAAE;;;;;AAKpC,QAAA,KAAK,CACH,oBAAoB,EACpB,KAAK,CAAC;AACJ,YAAA,WAAW,EAAE,MAAM;AACnB,YAAA,YAAY,EAAE,SAAS;AACxB,SAAA,CAAC,CACH;AACD,QAAA,KAAK,CACH,MAAM,EACN,KAAK,CAAC;;AAEJ,YAAA,YAAY,EAAE,MAAM;AACpB,YAAA,YAAY,EAAE,QAAQ;AACvB,SAAA,CAAC,CACH;AACD,QAAA,UAAU,CAAC,sBAAsB,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;AAClD,QAAA,UAAU,CACR,qCAAqC,EACrC,OAAO,CAAC,wCAAwC,CAAC,CAClD;KACF,CAAC;;;ACUJ;;;AAGG;AACG,SAAU,6BAA6B,CAAC,QAAgB,EAAA;AAC5D,IAAA,MAAM,KAAK,CAAC,CAAA,6CAAA,EAAgD,QAAQ,CAAA,EAAA,CAAI,CAAC,CAAC;AAC5E,CAAC;AAWD;MACa,2BAA2B,GAAG,IAAI,cAAc,CAC3D,6BAA6B,EAC7B;AACE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,mCAAmC;AAC7C,CAAA,EACD;AAEF;;;AAGG;AACI,MAAM,oBAAoB,GAAG,IAAI,cAAc,CAAC,sBAAsB,CAAC,CAAC;AAE/E;SACgB,mCAAmC,GAAA;AACjD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAoBK,MAAO,gBAAiB,SAAQ,aAAa,CAAA;IACjD,WACU,CAAA,kBAAqC,EACQ,UAA8B,EACnF,UAAmC,EACnC,gBAAkC,EAClC,MAAc,EAAA;AAEd,QAAA,KAAK,CAAC,UAAU,EAAE,gBAAgB,EAAE,MAAM,CAAC,CAAC;QANpC,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAmB;QACQ,IAAU,CAAA,UAAA,GAAV,UAAU,CAAoB;KAMpF;IAED,kBAAkB,GAAA;QAChB,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,SAAS,CAAC,MAAK;AACnD,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACzC,SAAC,CAAC,CAAC;KACJ;AAfU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,mDAGjB,UAAU,CAAC,MAAM,kBAAkB,CAAC,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAHnC,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,gBAAgB,EARhB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,sBAAA,EAAA,iCAAA,EAAA,uBAAA,EAAA,kCAAA,EAAA,EAAA,cAAA,EAAA,oBAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,aAAa;AACtB,gBAAA,WAAW,EAAE,gBAAgB;AAC9B,aAAA;AACF,SAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAbS,2BAA2B,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAgB1B,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAlB5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oBAAoB;AAC9B,oBAAA,QAAQ,EAAE,2BAA2B;AACrC,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,oBAAoB;AAC7B,wBAAA,wBAAwB,EAAE,iCAAiC;AAC3D,wBAAA,yBAAyB,EAAE,kCAAkC;AAC9D,qBAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,SAAS,EAAE;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,aAAa;AACtB,4BAAA,WAAW,EAAkB,gBAAA;AAC9B,yBAAA;AACF,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BAII,MAAM;AAAC,oBAAA,IAAA,EAAA,CAAA,UAAU,CAAC,MAAM,kBAAkB,CAAC,CAAA;;AAehD;;AAEG;MAyBU,SAAS,CAAA;;AAcpB,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,KAAsB,EAAA;;AAEjC,QAAA,KAAK,GAAG,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,OAAO,CAAC;AAC1C,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,EAAE;;AAE5B,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,gBAAA,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;aACrC;AAED,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACvB,YAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;SAC/B;KACF;;AAID,IAAA,IACI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;IACD,IAAI,IAAI,CAAC,KAAoB,EAAA;AAC3B,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;KAC1B;;AAID,IAAA,IACI,YAAY,GAAA;QACd,OAAO,IAAI,CAAC,aAAa,CAAC;KAC3B;IACD,IAAI,YAAY,CAAC,KAAmB,EAAA;AAClC,QAAA,IAAI,CAAC,aAAa,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;KACnD;AAGD;;;;;;AAMG;AACH,IAAA,IACI,SAAS,GAAA;AACX,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;;;;AAK9B,QAAA,IAAI,KAAK,IAAI,IAAI,EAAE;AACjB,YAAA,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE;AACxB,gBAAA,OAAO,QAAQ,CAAC;aACjB;iBAAM;AACL,gBAAA,OAAO,gBAAgB,CAAC;aACzB;SACF;AACD,QAAA,OAAO,KAAK,CAAC;KACd;IACD,IAAI,SAAS,CAAC,KAA8C,EAAA;AAC1D,QAAA,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,OAAO,IAAI,KAAK,IAAI,IAAI,EAAE;AAC1D,YAAA,KAAK,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;SACtC;AACD,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;KACzB;AAGD;;;AAGG;AACH,IAAA,IACI,MAAM,GAAA;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;IACD,IAAI,MAAM,CAAC,KAAmB,EAAA;QAC5B,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC,CAAC;KAC3C;AAgED,IAAA,WAAA,CACU,WAAoC,EACpC,iBAAmC,EACnC,aAA2B,EAC3B,SAAmB,EACnB,OAAe,EACN,qBAA2C,EACtB,IAAS,EACE,UAA+B,EAAA;QAPxE,IAAW,CAAA,WAAA,GAAX,WAAW,CAAyB;QACpC,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAkB;QACnC,IAAa,CAAA,aAAA,GAAb,aAAa,CAAc;QAC3B,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QACnB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QACN,IAAqB,CAAA,qBAAA,GAArB,qBAAqB,CAAsB;QACtB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAK;QACE,IAAU,CAAA,UAAA,GAAV,UAAU,CAAqB;QAvK1E,IAAU,CAAA,UAAA,GAAqB,IAAI,CAAC;QACpC,IAAoC,CAAA,oCAAA,GAAuB,IAAI,CAAC;;QAGhE,IAAiB,CAAA,iBAAA,GAAG,KAAK,CAAC;QA0B1B,IAAS,CAAA,SAAA,GAAoB,OAAO,CAAC;QAYrC,IAAK,CAAA,KAAA,GAAkB,MAAM,CAAC;QAU9B,IAAa,CAAA,aAAA,GAAY,KAAK,CAAC;QA4C/B,IAAO,CAAA,OAAA,GAAY,KAAK,CAAC;;AAMxB,QAAA,IAAA,CAAA,iBAAiB,GAAG,IAAI,OAAO,EAAkB,CAAC;;AAGlD,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,OAAO,EAAkB,CAAC;;QAGvD,IAAe,CAAA,eAAA,GAAqC,MAAM,CAAC;;QAGxC,IAAY,CAAA,YAAA;;AAE7B,QAAA,IAAI,YAAY,eAAwB,IAAI,CAAC,CAAC;;QAIvC,IAAa,CAAA,aAAA,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAC7C,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,EACd,GAAG,CAAC,MAAO,GAAC,CAAC,CACd,CAAC;;AAIO,QAAA,IAAA,CAAA,WAAW,GAAqB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAClE,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EACzE,KAAK,CAAC,SAAS,CAAC,CACjB,CAAC;;QAIO,IAAa,CAAA,aAAA,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAC7C,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EACf,GAAG,CAAC,MAAO,GAAC,CAAC,CACd,CAAC;;AAIO,QAAA,IAAA,CAAA,WAAW,GAAqB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAClE,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,KAAK,MAAM,CAAC,EAC9D,KAAK,CAAC,SAAS,CAAC,CACjB,CAAC;;AAGe,QAAA,IAAA,CAAA,UAAU,GAAG,IAAI,OAAO,EAAQ,CAAC;;;AAId,QAAA,IAAA,CAAA,iBAAiB,GAAG,IAAI,YAAY,EAAQ,CAAC;AAKjF;;;AAGG;AACM,QAAA,IAAA,CAAA,YAAY,GAAG,IAAI,OAAO,EAAQ,CAAC;QAY1C,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,MAAe,KAAI;YAC9C,IAAI,MAAM,EAAE;AACV,gBAAA,IAAI,IAAI,CAAC,IAAI,EAAE;oBACb,IAAI,CAAC,oCAAoC,GAAG,IAAI,CAAC,IAAI,CAAC,aAA4B,CAAC;iBACpF;gBAED,IAAI,CAAC,UAAU,EAAE,CAAC;aACnB;AAAM,iBAAA,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE;gBACtC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,IAAI,SAAS,CAAC,CAAC;aAClD;AACH,SAAC,CAAC,CAAC;AAEH;;;;AAIG;AACH,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;YACjC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,SAAS,CAA+B;AAChF,iBAAA,IAAI,CACH,MAAM,CAAC,KAAK,IAAG;AACb,gBAAA,OAAO,KAAK,CAAC,OAAO,KAAK,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;aACjF,CAAC,EACF,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAC3B;AACA,iBAAA,SAAS,CAAC,KAAK,IACd,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAK;gBACpB,IAAI,CAAC,KAAK,EAAE,CAAC;gBACb,KAAK,CAAC,eAAe,EAAE,CAAC;gBACxB,KAAK,CAAC,cAAc,EAAE,CAAC;aACxB,CAAC,CACH,CAAC;AACN,SAAC,CAAC,CAAC;;;AAIH,QAAA,IAAI,CAAC,aAAa;aACf,IAAI,CACH,oBAAoB,CAAC,CAAC,CAAC,EAAE,CAAC,KAAI;AAC5B,YAAA,OAAO,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,OAAO,CAAC;AAChE,SAAC,CAAC,CACH;AACA,aAAA,SAAS,CAAC,CAAC,KAAqB,KAAI;AACnC,YAAA,MAAM,EAAC,SAAS,EAAE,OAAO,EAAC,GAAG,KAAK,CAAC;AAEnC,YAAA,IACE,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,SAAS,KAAK,MAAM;AACtD,iBAAC,OAAO,KAAK,MAAM,IAAI,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EACvD;gBACA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aACtC;AACH,SAAC,CAAC,CAAC;KACN;AAED;;;;AAIG;IACK,WAAW,CAAC,OAAoB,EAAE,OAAsB,EAAA;QAC9D,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE;AACpD,YAAA,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;;AAEtB,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;gBAClC,MAAM,QAAQ,GAAG,MAAK;AACpB,oBAAA,OAAO,CAAC,mBAAmB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC9C,oBAAA,OAAO,CAAC,mBAAmB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;AACnD,oBAAA,OAAO,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;AACtC,iBAAC,CAAC;AAEF,gBAAA,OAAO,CAAC,gBAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC3C,gBAAA,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;AAClD,aAAC,CAAC,CAAC;SACJ;AACD,QAAA,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;KACxB;AAED;;;AAGG;IACK,mBAAmB,CAAC,QAAgB,EAAE,OAAsB,EAAA;AAClE,QAAA,IAAI,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,aAAa,CAC/D,QAAQ,CACa,CAAC;QACxB,IAAI,cAAc,EAAE;AAClB,YAAA,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;SAC3C;KACF;AAED;;;AAGG;IACK,UAAU,GAAA;AAChB,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB,OAAO;SACR;AAED,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;;;;AAK/C,QAAA,QAAQ,IAAI,CAAC,SAAS;AACpB,YAAA,KAAK,KAAK,CAAC;AACX,YAAA,KAAK,QAAQ;gBACX,OAAO;AACT,YAAA,KAAK,IAAI,CAAC;AACV,YAAA,KAAK,gBAAgB;gBACnB,IAAI,CAAC,UAAU,CAAC,4BAA4B,EAAE,CAAC,IAAI,CAAC,aAAa,IAAG;AAClE,oBAAA,IAAI,CAAC,aAAa,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,KAAK,UAAU,EAAE;wBAChF,OAAO,CAAC,KAAK,EAAE,CAAC;qBACjB;AACH,iBAAC,CAAC,CAAC;gBACH,MAAM;AACR,YAAA,KAAK,eAAe;AAClB,gBAAA,IAAI,CAAC,mBAAmB,CAAC,0CAA0C,CAAC,CAAC;gBACrE,MAAM;AACR,YAAA;AACE,gBAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAU,CAAC,CAAC;gBAC1C,MAAM;SACT;KACF;AAED;;;AAGG;AACK,IAAA,aAAa,CAAC,WAAuC,EAAA;AAC3D,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,EAAE;YAC/B,OAAO;SACR;AAED,QAAA,IAAI,IAAI,CAAC,oCAAoC,EAAE;YAC7C,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,oCAAoC,EAAE,WAAW,CAAC,CAAC;SACrF;aAAM;AACL,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;SACvC;AAED,QAAA,IAAI,CAAC,oCAAoC,GAAG,IAAI,CAAC;KAClD;;IAGO,oBAAoB,GAAA;AAC1B,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;AACzC,QAAA,OAAO,CAAC,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;KACxE;IAED,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;;;AAIxB,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;AAC5B,YAAA,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;SACrC;;;AAID,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;AAC5B,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;YAChF,IAAI,CAAC,qBAAqB,EAAE,CAAC;SAC9B;KACF;IAED,qBAAqB,GAAA;;;;;AAKnB,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;AAC5B,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;SAC/B;KACF;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,CAAC;AAC3B,QAAA,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,CAAC;AAClC,QAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;AAC9B,QAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;AAC7B,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;KAC5B;AAED;;;;AAIG;AACH,IAAA,IAAI,CAAC,SAAuB,EAAA;QAC1B,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;KACrC;;IAGD,KAAK,GAAA;AACH,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KAC3B;;IAGD,sBAAsB,GAAA;;;;AAIpB,QAAA,OAAO,IAAI,CAAC,QAAQ,cAAc,KAAK,qBAAqB,IAAI,EAAE,OAAO,CAAC,CAAC;KAC5E;AAED;;;;;AAKG;AACH,IAAA,MAAM,CAAC,MAAkB,GAAA,CAAC,IAAI,CAAC,MAAM,EAAE,SAAuB,EAAA;;;AAG5D,QAAA,IAAI,MAAM,IAAI,SAAS,EAAE;AACvB,YAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;SAC7B;AAED,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAC1B,MAAM;AACN,2BAAmB,CAAC,MAAM,IAAI,IAAI,CAAC,oBAAoB,EAAE,EACzD,IAAI,CAAC,UAAU,IAAI,SAAS,CAC7B,CAAC;QAEF,IAAI,CAAC,MAAM,EAAE;AACX,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;SACxB;AAED,QAAA,OAAO,MAAM,CAAC;KACf;AAED;;;;;AAKG;AACK,IAAA,QAAQ,CACd,MAAe,EACf,YAAqB,EACrB,WAAuC,EAAA;AAEvC,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,iBAAiB,GAAG,MAAM,GAAG,cAAc,CAAC;SACzE;aAAM;AACL,YAAA,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;YAC9B,IAAI,YAAY,EAAE;AAChB,gBAAA,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;aACjC;SACF;QAED,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAE7B,QAAA,OAAO,IAAI,OAAO,CAAwB,OAAO,IAAG;AAClD,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,GAAG,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC;AACtF,SAAC,CAAC,CAAC;KACJ;IAED,SAAS,GAAA;QACP,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,WAAW,IAAI,CAAC,GAAG,CAAC,CAAC;KAC7F;;IAGO,qBAAqB,GAAA;AAC3B,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;;;AAGnB,YAAA,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,CAAC;SAC1D;KACF;AAED;;;;;AAKG;AACK,IAAA,uBAAuB,CAAC,WAA4B,EAAA;;AAE1D,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAC7B,OAAO;SACR;AAED,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;AAC/C,QAAA,MAAM,MAAM,GAAG,OAAO,CAAC,UAAW,CAAC;AAEnC,QAAA,IAAI,WAAW,KAAK,KAAK,EAAE;AACzB,YAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACjB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAE,CAAC;gBAC7D,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,OAAQ,EAAE,OAAO,CAAC,CAAC;aAC7C;AAED,YAAA,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;SAC7B;AAAM,aAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AACvB,YAAA,IAAI,CAAC,OAAO,CAAC,UAAW,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;SAC9D;KACF;8GAxdU,SAAS,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,YAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,oBAAA,EAAA,EAAA,EAAA,KAAA,EAuKE,QAAQ,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EACR,oBAAoB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAxK/B,SAAS,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,IAAA,EAAA,MAAA,EAAA,YAAA,EAAA,cAAA,EAAA,SAAA,EAAA,WAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,OAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,aAAA,EAAA,QAAA,EAAA,WAAA,EAAA,aAAA,EAAA,aAAA,EAAA,QAAA,EAAA,WAAA,EAAA,aAAA,EAAA,iBAAA,EAAA,iBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,UAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,kBAAA,EAAA,gCAAA,EAAA,iBAAA,EAAA,4BAAA,EAAA,EAAA,UAAA,EAAA,EAAA,YAAA,EAAA,MAAA,EAAA,sBAAA,EAAA,sBAAA,EAAA,uBAAA,EAAA,mBAAA,EAAA,uBAAA,EAAA,mBAAA,EAAA,uBAAA,EAAA,mBAAA,EAAA,yBAAA,EAAA,QAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,EAAA,cAAA,EAAA,YAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EC/JtB,gHAGA,ED0JY,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,aAAa,gEAlBX,CAAC,mBAAmB,CAAC,eAAe,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAoBtC,SAAS,EAAA,UAAA,EAAA,CAAA;kBAxBrB,SAAS;+BACE,YAAY,EAAA,QAAA,EACZ,WAAW,EAET,UAAA,EAAA,CAAC,mBAAmB,CAAC,eAAe,CAAC,EAC3C,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,YAAY;;AAErB,wBAAA,cAAc,EAAE,MAAM;AACtB,wBAAA,wBAAwB,EAAE,oBAAoB;AAC9C,wBAAA,yBAAyB,EAAE,iBAAiB;AAC5C,wBAAA,yBAAyB,EAAE,iBAAiB;AAC5C,wBAAA,yBAAyB,EAAE,iBAAiB;AAC5C,wBAAA,2BAA2B,EAAE,QAAQ;AACrC,wBAAA,UAAU,EAAE,IAAI;AAChB,wBAAA,cAAc,EAAE,iBAAiB;AACjC,wBAAA,oBAAoB,EAAE,gCAAgC;AACtD,wBAAA,mBAAmB,EAAE,4BAA4B;AAClD,qBAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,EAAA,aAAA,EAChC,iBAAiB,CAAC,IAAI,EAAA,UAAA,EACzB,IAAI,EAAA,OAAA,EACP,CAAC,aAAa,CAAC,EAAA,QAAA,EAAA,gHAAA,EAAA,CAAA;;0BAyKrB,QAAQ;;0BAAI,MAAM;2BAAC,QAAQ,CAAA;;0BAC3B,QAAQ;;0BAAI,MAAM;2BAAC,oBAAoB,CAAA;yCAzJtC,QAAQ,EAAA,CAAA;sBADX,KAAK;gBAqBF,IAAI,EAAA,CAAA;sBADP,KAAK;gBAaF,YAAY,EAAA,CAAA;sBADf,KAAK;gBAiBF,SAAS,EAAA,CAAA;sBADZ,KAAK;gBA6BF,MAAM,EAAA,CAAA;sBADT,KAAK;gBAsBa,YAAY,EAAA,CAAA;sBAA9B,MAAM;gBAME,aAAa,EAAA,CAAA;sBADrB,MAAM;uBAAC,QAAQ,CAAA;gBAQP,WAAW,EAAA,CAAA;sBADnB,MAAM;gBAQE,aAAa,EAAA,CAAA;sBADrB,MAAM;uBAAC,QAAQ,CAAA;gBAQP,WAAW,EAAA,CAAA;sBADnB,MAAM;gBAW6B,iBAAiB,EAAA,CAAA;sBAApD,MAAM;uBAAC,iBAAiB,CAAA;gBAGH,QAAQ,EAAA,CAAA;sBAA7B,SAAS;uBAAC,SAAS,CAAA;;AAmUtB;;;;;AAKG;MAqBU,kBAAkB,CAAA;;AAgB7B,IAAA,IAAI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;KACpB;;AAGD,IAAA,IAAI,GAAG,GAAA;QACL,OAAO,IAAI,CAAC,IAAI,CAAC;KAClB;AAED;;;;;;;AAOG;AACH,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,KAAmB,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;KAC/C;AAGD;;;;AAIG;AACH,IAAA,IACI,WAAW,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACnF;IACD,IAAI,WAAW,CAAC,KAAmB,EAAA;AACjC,QAAA,IAAI,CAAC,iBAAiB,GAAG,KAAK,IAAI,IAAI,GAAG,IAAI,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;KAC9E;;AAmCD,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,QAAQ,CAAC;KAC3C;AAED,IAAA,WAAA,CACsB,IAAoB,EAChC,QAAiC,EACjC,OAAe,EACf,kBAAqC,EAC7C,aAA4B,EACS,eAAe,GAAG,KAAK,EACT,cAAuB,EAAA;QANtD,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAgB;QAChC,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAyB;QACjC,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QACf,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAmB;QAGM,IAAc,CAAA,cAAA,GAAd,cAAc,CAAS;;AAzF5E,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,SAAS,EAAa,CAAC;;AA+CnB,QAAA,IAAA,CAAA,aAAa,GAAuB,IAAI,YAAY,EAAQ,CAAC;;AAgB/D,QAAA,IAAA,CAAA,UAAU,GAAG,IAAI,OAAO,EAAQ,CAAC;;AAGjC,QAAA,IAAA,CAAA,eAAe,GAAG,IAAI,OAAO,EAAQ,CAAC;AAEvD;;;;AAIG;QACH,IAAe,CAAA,eAAA,GAAgD,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAC,CAAC;AAEhF,QAAA,IAAA,CAAA,qBAAqB,GAAG,IAAI,OAAO,EAA+C,CAAC;;;QAkB1F,IAAI,IAAI,EAAE;AACR,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;gBAC1D,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC9B,aAAC,CAAC,CAAC;SACJ;;;QAID,aAAa;AACV,aAAA,MAAM,EAAE;AACR,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAChC,SAAS,CAAC,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC;AAEhD,QAAA,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;KAClC;IAED,kBAAkB,GAAA;QAChB,IAAI,CAAC,WAAW,CAAC,OAAO;AACrB,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC7D,aAAA,SAAS,CAAC,CAAC,MAA4B,KAAI;YAC1C,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,CAAC,CAAC;AACzF,YAAA,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC;AAClC,SAAC,CAAC,CAAC;AAEL,QAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;YACzD,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAExB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,MAAiB,KAAI;AAC1C,gBAAA,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;AAChC,gBAAA,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;AAClC,gBAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;AAChC,aAAC,CAAC,CAAC;AAEH,YAAA,IACE,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM;AACrB,gBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC;gBAC/B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAC7B;gBACA,IAAI,CAAC,oBAAoB,EAAE,CAAC;aAC7B;AAED,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACzC,SAAC,CAAC,CAAC;;AAGH,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,IAAI,CAAC,eAAe;AACjB,iBAAA,IAAI,CACH,YAAY,CAAC,EAAE,CAAC;AAChB,YAAA,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAC3B;iBACA,SAAS,CAAC,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC;AAClD,SAAC,CAAC,CAAC;KACJ;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,CAAC;AACtC,QAAA,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;AAChC,QAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;AACxB,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;KAC5B;;IAGD,IAAI,GAAA;AACF,QAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;KAChD;;IAGD,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;KACjD;AAED;;;AAGG;IACH,oBAAoB,GAAA;;;;;;;QAOlB,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACnC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,MAAM,EAAE;AAC7B,gBAAA,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;aAChC;iBAAM,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,MAAM,EAAE;gBACpC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;gBACrC,IAAI,IAAI,KAAK,CAAC;gBACd,KAAK,IAAI,KAAK,CAAC;aAChB;SACF;QAED,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YACrC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,MAAM,EAAE;AAC9B,gBAAA,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;aAClC;iBAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,MAAM,EAAE;gBACrC,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBACtC,KAAK,IAAI,KAAK,CAAC;gBACf,IAAI,IAAI,KAAK,CAAC;aACf;SACF;;;;;AAMD,QAAA,IAAI,GAAG,IAAI,IAAI,IAAK,CAAC;AACrB,QAAA,KAAK,GAAG,KAAK,IAAI,IAAK,CAAC;AAEvB,QAAA,IAAI,IAAI,KAAK,IAAI,CAAC,eAAe,CAAC,IAAI,IAAI,KAAK,KAAK,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE;YAC9E,IAAI,CAAC,eAAe,GAAG,EAAC,IAAI,EAAE,KAAK,EAAC,CAAC;;;AAIrC,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;SAC/E;KACF;IAED,SAAS,GAAA;;QAEP,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;;AAEtC,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC;SACnE;KACF;AAED;;;;AAIG;AACK,IAAA,kBAAkB,CAAC,MAAiB,EAAA;AAC1C,QAAA,MAAM,CAAC,iBAAiB;aACrB,IAAI,CACH,MAAM,CAAC,CAAC,KAAqB,KAAK,KAAK,CAAC,SAAS,KAAK,KAAK,CAAC,OAAO,CAAC,EACpE,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CACjC;AACA,aAAA,SAAS,CAAC,CAAC,KAAqB,KAAI;;;AAGnC,YAAA,IAAI,KAAK,CAAC,OAAO,KAAK,cAAc,IAAI,IAAI,CAAC,cAAc,KAAK,gBAAgB,EAAE;gBAChF,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;aACpE;YAED,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACzC,SAAC,CAAC,CAAC;AAEL,QAAA,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE;AAC1B,YAAA,MAAM,CAAC,YAAY;iBAChB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AACtC,iBAAA,SAAS,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;SAC5D;KACF;AAED;;;AAGG;AACK,IAAA,oBAAoB,CAAC,MAAiB,EAAA;QAC5C,IAAI,CAAC,MAAM,EAAE;YACX,OAAO;SACR;;;AAGD,QAAA,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;AAC7E,YAAA,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;gBACzD,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAC1B,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;KACJ;;AAGO,IAAA,gBAAgB,CAAC,MAAiB,EAAA;QACxC,IAAI,MAAM,EAAE;AACV,YAAA,MAAM,CAAC,YAAY;AAChB,iBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;iBAC9D,SAAS,CAAC,MAAK;gBACd,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,gBAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACzC,aAAC,CAAC,CAAC;SACN;KACF;;AAGO,IAAA,kBAAkB,CAAC,KAAc,EAAA;QACvC,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC;QACxD,MAAM,SAAS,GAAG,+BAA+B,CAAC;QAElD,IAAI,KAAK,EAAE;AACT,YAAA,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;SAC1B;aAAM;AACL,YAAA,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;SAC7B;KACF;;IAGO,gBAAgB,GAAA;QACtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;;AAG/B,QAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,IAAG;AAC7B,YAAA,IAAI,MAAM,CAAC,QAAQ,IAAI,KAAK,EAAE;AAC5B,gBAAA,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;oBACxE,6BAA6B,CAAC,KAAK,CAAC,CAAC;iBACtC;AACD,gBAAA,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC;aACpB;iBAAM;AACL,gBAAA,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;oBAC1E,6BAA6B,CAAC,OAAO,CAAC,CAAC;iBACxC;AACD,gBAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;aACtB;AACH,SAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;;AAGhC,QAAA,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,EAAE;AAC1C,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC;AACvB,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;SAC3B;aAAM;AACL,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;AACzB,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;SACzB;KACF;;IAGO,SAAS,GAAA;AACf,QAAA,QACE,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,MAAM;AAC9D,aAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,MAAM,CAAC,EAC3D;KACH;IAED,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;QAC1B,IAAI,CAAC,6BAA6B,EAAE,CAAC;KACtC;IAED,6BAA6B,GAAA;;AAE3B,QAAA,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC;AACrB,aAAA,MAAM,CAAC,MAAM,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;aACnF,OAAO,CAAC,MAAM,IAAI,MAAO,CAAC,sBAAsB,EAAE,CAAC,CAAC;KACxD;IAED,kBAAkB,GAAA;AAChB,QAAA,QACE,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC;AACxE,aAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EACrE;KACH;AAEO,IAAA,aAAa,CAAC,MAAwB,EAAA;AAC5C,QAAA,OAAO,MAAM,IAAI,IAAI,IAAI,MAAM,CAAC,MAAM,CAAC;KACxC;;AAGO,IAAA,kBAAkB,CAAC,MAAwB,EAAA;AACjD,QAAA,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,EAAE;YAClC,OAAO,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC;SAC3C;QAED,OAAO,IAAI,CAAC,iBAAiB,CAAC;KAC/B;8GAtXU,kBAAkB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAkGnB,2BAA2B,EAAA,EAAA,EAAA,KAAA,EACf,qBAAqB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAnGhC,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,kBAAkB,EATlB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,OAAA,EAAA,EAAA,aAAA,EAAA,eAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,8CAAA,EAAA,mBAAA,EAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,oBAAoB;AAC7B,gBAAA,WAAW,EAAE,kBAAkB;AAChC,aAAA;SACF,EAgBa,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,gBAAgB,iEAVb,SAAS,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,cAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAWf,gBAAgB,EEjqB7B,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,0XAeA,m1HFmGa,gBAAgB,EAAA,QAAA,EAAA,oBAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAkiBhB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBApB9B,SAAS;+BACE,sBAAsB,EAAA,QAAA,EACtB,oBAAoB,EAGxB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,sBAAsB;AAC/B,wBAAA,gDAAgD,EAAE,mBAAmB;AACtE,qBAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAC1B,SAAA,EAAA;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,oBAAoB;AAC7B,4BAAA,WAAW,EAAoB,kBAAA;AAChC,yBAAA;AACF,qBAAA,EAAA,UAAA,EACW,IAAI,EAAA,OAAA,EACP,CAAC,gBAAgB,CAAC,EAAA,QAAA,EAAA,0XAAA,EAAA,MAAA,EAAA,CAAA,2xHAAA,CAAA,EAAA,CAAA;;0BA+FxB,QAAQ;;0BAKR,MAAM;2BAAC,2BAA2B,CAAA;;0BAClC,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;yCA5F3C,WAAW,EAAA,CAAA;sBALV,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,SAAS,EAAE;;;AAG1B,wBAAA,WAAW,EAAE,IAAI;AAClB,qBAAA,CAAA;gBAM+B,QAAQ,EAAA,CAAA;sBAAvC,YAAY;uBAAC,gBAAgB,CAAA;gBACD,YAAY,EAAA,CAAA;sBAAxC,SAAS;uBAAC,gBAAgB,CAAA;gBAqBvB,QAAQ,EAAA,CAAA;sBADX,KAAK;gBAeF,WAAW,EAAA,CAAA;sBADd,KAAK;gBAUa,aAAa,EAAA,CAAA;sBAA/B,MAAM;;;AG3pBH,MAAO,iBAAkB,SAAQ,gBAAgB,CAAA;IACrD,WACE,CAAA,iBAAoC,EACW,SAA8B,EAC7E,UAAmC,EACnC,gBAAkC,EAClC,MAAc,EAAA;QAEd,KAAK,CAAC,iBAAiB,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,EAAE,MAAM,CAAC,CAAC;KAC3E;AATU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,mDAGlB,UAAU,CAAC,MAAM,mBAAmB,CAAC,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAHpC,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,iBAAiB,EARjB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,sBAAA,EAAA,iCAAA,EAAA,uBAAA,EAAA,kCAAA,EAAA,EAAA,cAAA,EAAA,wCAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,aAAa;AACtB,gBAAA,WAAW,EAAE,iBAAiB;AAC/B,aAAA;AACF,SAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAbS,2BAA2B,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAgB1B,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAlB7B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,qBAAqB;AAC/B,oBAAA,QAAQ,EAAE,2BAA2B;AACrC,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,wCAAwC;AACjD,wBAAA,wBAAwB,EAAE,iCAAiC;AAC3D,wBAAA,yBAAyB,EAAE,kCAAkC;AAC9D,qBAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,SAAS,EAAE;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,aAAa;AACtB,4BAAA,WAAW,EAAmB,iBAAA;AAC/B,yBAAA;AACF,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BAII,MAAM;AAAC,oBAAA,IAAA,EAAA,CAAA,UAAU,CAAC,MAAM,mBAAmB,CAAC,CAAA;;AAiC3C,MAAO,UAAW,SAAQ,SAAS,CAAA;AAxBzC,IAAA,WAAA,GAAA;;QAiCU,IAAgB,CAAA,gBAAA,GAAG,KAAK,CAAC;QAazB,IAAY,CAAA,YAAA,GAAG,CAAC,CAAC;QAajB,IAAe,CAAA,eAAA,GAAG,CAAC,CAAC;AAC7B,KAAA;;AAlCC,IAAA,IACI,eAAe,GAAA;QACjB,OAAO,IAAI,CAAC,gBAAgB,CAAC;KAC9B;IACD,IAAI,eAAe,CAAC,KAAmB,EAAA;AACrC,QAAA,IAAI,CAAC,gBAAgB,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;KACtD;AAGD;;;AAGG;AACH,IAAA,IACI,WAAW,GAAA;QACb,OAAO,IAAI,CAAC,YAAY,CAAC;KAC1B;IACD,IAAI,WAAW,CAAC,KAAkB,EAAA;AAChC,QAAA,IAAI,CAAC,YAAY,GAAG,oBAAoB,CAAC,KAAK,CAAC,CAAC;KACjD;AAGD;;;AAGG;AACH,IAAA,IACI,cAAc,GAAA;QAChB,OAAO,IAAI,CAAC,eAAe,CAAC;KAC7B;IACD,IAAI,cAAc,CAAC,KAAkB,EAAA;AACnC,QAAA,IAAI,CAAC,eAAe,GAAG,oBAAoB,CAAC,KAAK,CAAC,CAAC;KACpD;8GAlCU,UAAU,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAV,UAAU,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,WAAA,EAAA,aAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,UAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,YAAA,EAAA,MAAA,EAAA,sBAAA,EAAA,sBAAA,EAAA,uBAAA,EAAA,mBAAA,EAAA,uBAAA,EAAA,mBAAA,EAAA,uBAAA,EAAA,mBAAA,EAAA,yBAAA,EAAA,QAAA,EAAA,yBAAA,EAAA,iBAAA,EAAA,cAAA,EAAA,sCAAA,EAAA,iBAAA,EAAA,yCAAA,EAAA,EAAA,cAAA,EAAA,wBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EFtFvB,gHAGA,EEiFY,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,aAAa,gEAlBX,CAAC,mBAAmB,CAAC,eAAe,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAoBtC,UAAU,EAAA,UAAA,EAAA,CAAA;kBAxBtB,SAAS;+BACE,aAAa,EAAA,QAAA,EACb,YAAY,EAEV,UAAA,EAAA,CAAC,mBAAmB,CAAC,eAAe,CAAC,EAC3C,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,wBAAwB;AACjC,wBAAA,UAAU,EAAE,IAAI;;AAEhB,wBAAA,cAAc,EAAE,MAAM;AACtB,wBAAA,wBAAwB,EAAE,oBAAoB;AAC9C,wBAAA,yBAAyB,EAAE,iBAAiB;AAC5C,wBAAA,yBAAyB,EAAE,iBAAiB;AAC5C,wBAAA,yBAAyB,EAAE,iBAAiB;AAC5C,wBAAA,2BAA2B,EAAE,QAAQ;AACrC,wBAAA,2BAA2B,EAAE,iBAAiB;AAC9C,wBAAA,gBAAgB,EAAE,sCAAsC;AACxD,wBAAA,mBAAmB,EAAE,yCAAyC;AAC/D,qBAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,EAAA,aAAA,EAChC,iBAAiB,CAAC,IAAI,EAAA,UAAA,EACzB,IAAI,EAAA,OAAA,EACP,CAAC,aAAa,CAAC,EAAA,QAAA,EAAA,gHAAA,EAAA,CAAA;8BAKpB,eAAe,EAAA,CAAA;sBADlB,KAAK;gBAcF,WAAW,EAAA,CAAA;sBADd,KAAK;gBAcF,cAAc,EAAA,CAAA;sBADjB,KAAK;;AA8BF,MAAO,mBAAoB,SAAQ,kBAAkB,CAAA;AApB3D,IAAA,WAAA,GAAA;;QA2BW,IAAW,CAAA,WAAA,GAA0B,SAAU,CAAC;;QAGf,IAAQ,CAAA,QAAA,GAAsB,SAAU,CAAC;AACpF,KAAA;8GAXY,mBAAmB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAnB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,mBAAmB,EATnB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,uBAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,8CAAA,EAAA,mBAAA,EAAA,EAAA,cAAA,EAAA,4CAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,oBAAoB;AAC7B,gBAAA,WAAW,EAAE,mBAAmB;AACjC,aAAA;AACF,SAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAca,iBAAiB,EATd,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,aAAA,EAAA,SAAA,EAAA,UAAU,ECjJ7B,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,8XAeA,m1HDmCa,iBAAiB,EAAA,QAAA,EAAA,qBAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FA8FjB,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBApB/B,SAAS;+BACE,uBAAuB,EAAA,QAAA,EACvB,qBAAqB,EAGzB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,4CAA4C;AACrD,wBAAA,gDAAgD,EAAE,mBAAmB;AACtE,qBAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAC1B,SAAA,EAAA;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,oBAAoB;AAC7B,4BAAA,WAAW,EAAqB,mBAAA;AACjC,yBAAA;AACF,qBAAA,EAAA,UAAA,EACW,IAAI,EAAA,OAAA,EACP,CAAC,iBAAiB,CAAC,EAAA,QAAA,EAAA,8XAAA,EAAA,MAAA,EAAA,CAAA,2xHAAA,CAAA,EAAA,CAAA;8BASnB,WAAW,EAAA,CAAA;sBANnB,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,UAAU,EAAE;;;AAG3B,wBAAA,WAAW,EAAE,IAAI;AAClB,qBAAA,CAAA;gBAKyC,QAAQ,EAAA,CAAA;sBAAjD,YAAY;uBAAC,iBAAiB,CAAA;;;MEvHpB,gBAAgB,CAAA;8GAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAhB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,YApBzB,eAAe;YACf,mBAAmB;YACnB,SAAS;YACT,kBAAkB;YAClB,gBAAgB;YAChB,UAAU;YACV,mBAAmB;AACnB,YAAA,iBAAiB,aAGjB,mBAAmB;YACnB,eAAe;YACf,SAAS;YACT,kBAAkB;YAClB,gBAAgB;YAChB,UAAU;YACV,mBAAmB;YACnB,iBAAiB,CAAA,EAAA,CAAA,CAAA,EAAA;AAGR,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,YApBzB,eAAe;AACf,YAAA,mBAAmB,EASnB,mBAAmB;YACnB,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FASN,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAtB5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE;wBACP,eAAe;wBACf,mBAAmB;wBACnB,SAAS;wBACT,kBAAkB;wBAClB,gBAAgB;wBAChB,UAAU;wBACV,mBAAmB;wBACnB,iBAAiB;AAClB,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,mBAAmB;wBACnB,eAAe;wBACf,SAAS;wBACT,kBAAkB;wBAClB,gBAAgB;wBAChB,UAAU;wBACV,mBAAmB;wBACnB,iBAAiB;AAClB,qBAAA;AACF,iBAAA,CAAA;;;AClCD;;AAEG;;;;"}