{"version": 3, "file": "accordion.mjs", "sources": ["../../../../../../src/cdk/accordion/accordion.ts", "../../../../../../src/cdk/accordion/accordion-item.ts", "../../../../../../src/cdk/accordion/accordion-module.ts", "../../../../../../src/cdk/accordion/accordion_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Directive,\n  InjectionToken,\n  Input,\n  OnChanges,\n  OnDestroy,\n  SimpleChanges,\n  booleanAttribute,\n} from '@angular/core';\nimport {Subject} from 'rxjs';\n\n/** Used to generate unique ID for each accordion. */\nlet nextId = 0;\n\n/**\n * Injection token that can be used to reference instances of `CdkAccordion`. It serves\n * as alternative token to the actual `CdkAccordion` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nexport const CDK_ACCORDION = new InjectionToken<CdkAccordion>('CdkAccordion');\n\n/**\n * Directive whose purpose is to manage the expanded state of CdkAccordionItem children.\n */\n@Directive({\n  selector: 'cdk-accordion, [cdkAccordion]',\n  exportAs: 'cdkAccordion',\n  providers: [{provide: CDK_ACCORDION, useExisting: CdkAccordion}],\n  standalone: true,\n})\nexport class CdkAccordion implements OnDestroy, OnChanges {\n  /** Emits when the state of the accordion changes */\n  readonly _stateChanges = new Subject<SimpleChanges>();\n\n  /** Stream that emits true/false when openAll/closeAll is triggered. */\n  readonly _openCloseAllActions: Subject<boolean> = new Subject<boolean>();\n\n  /** A readonly id value to use for unique selection coordination. */\n  readonly id: string = `cdk-accordion-${nextId++}`;\n\n  /** Whether the accordion should allow multiple expanded accordion items simultaneously. */\n  @Input({transform: booleanAttribute}) multi: boolean = false;\n\n  /** Opens all enabled accordion items in an accordion where multi is enabled. */\n  openAll(): void {\n    if (this.multi) {\n      this._openCloseAllActions.next(true);\n    }\n  }\n\n  /** Closes all enabled accordion items. */\n  closeAll(): void {\n    this._openCloseAllActions.next(false);\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    this._stateChanges.next(changes);\n  }\n\n  ngOnDestroy() {\n    this._stateChanges.complete();\n    this._openCloseAllActions.complete();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Output,\n  Directive,\n  EventEmitter,\n  Input,\n  OnDestroy,\n  Optional,\n  ChangeDetectorRef,\n  SkipSelf,\n  Inject,\n  booleanAttribute,\n} from '@angular/core';\nimport {UniqueSelectionDispatcher} from '@angular/cdk/collections';\nimport {CDK_ACCORDION, CdkAccordion} from './accordion';\nimport {Subscription} from 'rxjs';\n\n/** Used to generate unique ID for each accordion item. */\nlet nextId = 0;\n\n/**\n * An basic directive expected to be extended and decorated as a component.  Sets up all\n * events and attributes needed to be managed by a CdkAccordion parent.\n */\n@Directive({\n  selector: 'cdk-accordion-item, [cdkAccordionItem]',\n  exportAs: 'cdkAccordionItem',\n  providers: [\n    // Provide `CDK_ACCORDION` as undefined to prevent nested accordion items from\n    // registering to the same accordion.\n    {provide: CDK_ACCORDION, useValue: undefined},\n  ],\n  standalone: true,\n})\nexport class CdkAccordionItem implements OnDestroy {\n  /** Subscription to openAll/closeAll events. */\n  private _openCloseAllSubscription = Subscription.EMPTY;\n  /** Event emitted every time the AccordionItem is closed. */\n  @Output() readonly closed: EventEmitter<void> = new EventEmitter<void>();\n  /** Event emitted every time the AccordionItem is opened. */\n  @Output() readonly opened: EventEmitter<void> = new EventEmitter<void>();\n  /** Event emitted when the AccordionItem is destroyed. */\n  @Output() readonly destroyed: EventEmitter<void> = new EventEmitter<void>();\n\n  /**\n   * Emits whenever the expanded state of the accordion changes.\n   * Primarily used to facilitate two-way binding.\n   * @docs-private\n   */\n  @Output() readonly expandedChange: EventEmitter<boolean> = new EventEmitter<boolean>();\n\n  /** The unique AccordionItem id. */\n  readonly id: string = `cdk-accordion-child-${nextId++}`;\n\n  /** Whether the AccordionItem is expanded. */\n  @Input({transform: booleanAttribute})\n  get expanded(): boolean {\n    return this._expanded;\n  }\n  set expanded(expanded: boolean) {\n    // Only emit events and update the internal value if the value changes.\n    if (this._expanded !== expanded) {\n      this._expanded = expanded;\n      this.expandedChange.emit(expanded);\n\n      if (expanded) {\n        this.opened.emit();\n        /**\n         * In the unique selection dispatcher, the id parameter is the id of the CdkAccordionItem,\n         * the name value is the id of the accordion.\n         */\n        const accordionId = this.accordion ? this.accordion.id : this.id;\n        this._expansionDispatcher.notify(this.id, accordionId);\n      } else {\n        this.closed.emit();\n      }\n\n      // Ensures that the animation will run when the value is set outside of an `@Input`.\n      // This includes cases like the open, close and toggle methods.\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  private _expanded = false;\n\n  /** Whether the AccordionItem is disabled. */\n  @Input({transform: booleanAttribute}) disabled: boolean = false;\n\n  /** Unregister function for _expansionDispatcher. */\n  private _removeUniqueSelectionListener: () => void = () => {};\n\n  constructor(\n    @Optional() @Inject(CDK_ACCORDION) @SkipSelf() public accordion: CdkAccordion,\n    private _changeDetectorRef: ChangeDetectorRef,\n    protected _expansionDispatcher: UniqueSelectionDispatcher,\n  ) {\n    this._removeUniqueSelectionListener = _expansionDispatcher.listen(\n      (id: string, accordionId: string) => {\n        if (\n          this.accordion &&\n          !this.accordion.multi &&\n          this.accordion.id === accordionId &&\n          this.id !== id\n        ) {\n          this.expanded = false;\n        }\n      },\n    );\n\n    // When an accordion item is hosted in an accordion, subscribe to open/close events.\n    if (this.accordion) {\n      this._openCloseAllSubscription = this._subscribeToOpenCloseAllActions();\n    }\n  }\n\n  /** Emits an event for the accordion item being destroyed. */\n  ngOnDestroy() {\n    this.opened.complete();\n    this.closed.complete();\n    this.destroyed.emit();\n    this.destroyed.complete();\n    this._removeUniqueSelectionListener();\n    this._openCloseAllSubscription.unsubscribe();\n  }\n\n  /** Toggles the expanded state of the accordion item. */\n  toggle(): void {\n    if (!this.disabled) {\n      this.expanded = !this.expanded;\n    }\n  }\n\n  /** Sets the expanded state of the accordion item to false. */\n  close(): void {\n    if (!this.disabled) {\n      this.expanded = false;\n    }\n  }\n\n  /** Sets the expanded state of the accordion item to true. */\n  open(): void {\n    if (!this.disabled) {\n      this.expanded = true;\n    }\n  }\n\n  private _subscribeToOpenCloseAllActions(): Subscription {\n    return this.accordion._openCloseAllActions.subscribe(expanded => {\n      // Only change expanded state if item is enabled\n      if (!this.disabled) {\n        this.expanded = expanded;\n      }\n    });\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {CdkAccordion} from './accordion';\nimport {CdkAccordionItem} from './accordion-item';\n\n@NgModule({\n  imports: [CdkAccordion, CdkAccordionItem],\n  exports: [CdkAccordion, CdkAccordionItem],\n})\nexport class CdkAccordionModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["nextId"], "mappings": ";;;;;AAmBA;AACA,IAAIA,QAAM,GAAG,CAAC,CAAC;AAEf;;;;AAIG;MACU,aAAa,GAAG,IAAI,cAAc,CAAe,cAAc,EAAE;AAE9E;;AAEG;MAOU,YAAY,CAAA;AANzB,IAAA,WAAA,GAAA;;AAQW,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,OAAO,EAAiB,CAAC;;AAG7C,QAAA,IAAA,CAAA,oBAAoB,GAAqB,IAAI,OAAO,EAAW,CAAC;;AAGhE,QAAA,IAAA,CAAA,EAAE,GAAW,CAAA,cAAA,EAAiBA,QAAM,EAAE,EAAE,CAAC;;QAGZ,IAAK,CAAA,KAAA,GAAY,KAAK,CAAC;AAsB9D,KAAA;;IAnBC,OAAO,GAAA;AACL,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,YAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACtC;KACF;;IAGD,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACvC;AAED,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KAClC;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;AAC9B,QAAA,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,CAAC;KACtC;8GAhCU,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAZ,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAY,EAWJ,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,+BAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAAA,gBAAgB,CAdxB,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,YAAY,EAAC,CAAC,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAGrD,YAAY,EAAA,UAAA,EAAA,CAAA;kBANxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,+BAA+B;AACzC,oBAAA,QAAQ,EAAE,cAAc;oBACxB,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,aAAa,EAAE,WAAW,EAAc,YAAA,EAAC,CAAC;AAChE,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;8BAYuC,KAAK,EAAA,CAAA;sBAA1C,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;;;ACzBtC;AACA,IAAI,MAAM,GAAG,CAAC,CAAC;AAEf;;;AAGG;MAWU,gBAAgB,CAAA;;AAqB3B,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,QAAiB,EAAA;;AAE5B,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,EAAE;AAC/B,YAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC1B,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEnC,IAAI,QAAQ,EAAE;AACZ,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;AACnB;;;AAGG;AACH,gBAAA,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;gBACjE,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;aACxD;iBAAM;AACL,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;aACpB;;;AAID,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;SACxC;KACF;AASD,IAAA,WAAA,CACwD,SAAuB,EACrE,kBAAqC,EACnC,oBAA+C,EAAA;QAFH,IAAS,CAAA,SAAA,GAAT,SAAS,CAAc;QACrE,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAmB;QACnC,IAAoB,CAAA,oBAAA,GAApB,oBAAoB,CAA2B;;AAzDnD,QAAA,IAAA,CAAA,yBAAyB,GAAG,YAAY,CAAC,KAAK,CAAC;;AAEpC,QAAA,IAAA,CAAA,MAAM,GAAuB,IAAI,YAAY,EAAQ,CAAC;;AAEtD,QAAA,IAAA,CAAA,MAAM,GAAuB,IAAI,YAAY,EAAQ,CAAC;;AAEtD,QAAA,IAAA,CAAA,SAAS,GAAuB,IAAI,YAAY,EAAQ,CAAC;AAE5E;;;;AAIG;AACgB,QAAA,IAAA,CAAA,cAAc,GAA0B,IAAI,YAAY,EAAW,CAAC;;AAG9E,QAAA,IAAA,CAAA,EAAE,GAAW,CAAA,oBAAA,EAAuB,MAAM,EAAE,EAAE,CAAC;QA8BhD,IAAS,CAAA,SAAA,GAAG,KAAK,CAAC;;QAGY,IAAQ,CAAA,QAAA,GAAY,KAAK,CAAC;;AAGxD,QAAA,IAAA,CAAA,8BAA8B,GAAe,MAAK,GAAG,CAAC;AAO5D,QAAA,IAAI,CAAC,8BAA8B,GAAG,oBAAoB,CAAC,MAAM,CAC/D,CAAC,EAAU,EAAE,WAAmB,KAAI;YAClC,IACE,IAAI,CAAC,SAAS;AACd,gBAAA,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK;AACrB,gBAAA,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,WAAW;AACjC,gBAAA,IAAI,CAAC,EAAE,KAAK,EAAE,EACd;AACA,gBAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;aACvB;AACH,SAAC,CACF,CAAC;;AAGF,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,+BAA+B,EAAE,CAAC;SACzE;KACF;;IAGD,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;QAC1B,IAAI,CAAC,8BAA8B,EAAE,CAAC;AACtC,QAAA,IAAI,CAAC,yBAAyB,CAAC,WAAW,EAAE,CAAC;KAC9C;;IAGD,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC;SAChC;KACF;;IAGD,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;SACvB;KACF;;IAGD,IAAI,GAAA;AACF,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACtB;KACF;IAEO,+BAA+B,GAAA;QACrC,OAAO,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,SAAS,CAAC,QAAQ,IAAG;;AAE9D,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,gBAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;aAC1B;AACH,SAAC,CAAC,CAAC;KACJ;AAtHU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,kBAyDL,aAAa,EAAA,QAAA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,yBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAzDxB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,gBAAgB,EAqBR,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,wCAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CA8BhB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CA1DxB,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,SAAA,EAAA;;;AAGT,YAAA,EAAC,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,SAAS,EAAC;AAC9C,SAAA,EAAA,QAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAGU,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAV5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,wCAAwC;AAClD,oBAAA,QAAQ,EAAE,kBAAkB;AAC5B,oBAAA,SAAS,EAAE;;;AAGT,wBAAA,EAAC,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,SAAS,EAAC;AAC9C,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BA0DI,QAAQ;;0BAAI,MAAM;2BAAC,aAAa,CAAA;;0BAAG,QAAQ;iHArD3B,MAAM,EAAA,CAAA;sBAAxB,MAAM;gBAEY,MAAM,EAAA,CAAA;sBAAxB,MAAM;gBAEY,SAAS,EAAA,CAAA;sBAA3B,MAAM;gBAOY,cAAc,EAAA,CAAA;sBAAhC,MAAM;gBAOH,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBA8BE,QAAQ,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;;;MC5EzB,kBAAkB,CAAA;8GAAlB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAlB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,YAHnB,YAAY,EAAE,gBAAgB,CAC9B,EAAA,OAAA,EAAA,CAAA,YAAY,EAAE,gBAAgB,CAAA,EAAA,CAAA,CAAA,EAAA;+GAE7B,kBAAkB,EAAA,CAAA,CAAA,EAAA;;2FAAlB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAJ9B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC;AACzC,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC;AAC1C,iBAAA,CAAA;;;ACfD;;AAEG;;;;"}